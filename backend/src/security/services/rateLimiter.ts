/**
 * 速率限制服务
 * 
 * 负责限制请求频率，防止滥用
 */

import { Context } from 'hono';
import { RateLimitConfig, ValidationResult } from '../types';

/**
 * 速率限制服务类
 */
export class RateLimiter {
  private config: RateLimitConfig;

  /**
   * 构造函数
   */
  constructor() {
    this.config = {
      enabled: false,
      ipLimit: 0,
      fingerprintLimit: 0,
      cooldownPeriod: 0
    };
  }

  /**
   * 初始化服务
   * @param config 速率限制配置
   */
  public initialize(config: RateLimitConfig): void {
    this.config = { ...config };
  }

  /**
   * 更新配置
   * @param config 速率限制配置
   */
  public updateConfig(config: RateLimitConfig): void {
    this.config = { ...this.config, ...config };
  }

  /**
   * 检查速率限制
   * @param c 上下文
   * @param clientIp 客户端 IP
   * @param fingerprint 浏览器指纹
   */
  public async checkRateLimit(
    c: Context,
    clientIp: string,
    fingerprint?: string
  ): Promise<ValidationResult> {
    // 如果未启用速率限制，直接返回有效
    if (!this.config.enabled) {
      return { valid: true };
    }

    try {
      // 检查 IP 速率限制
      if (this.config.ipLimit > 0) {
        const ipLimitResult = await this.checkIpRateLimit(c, clientIp);
        if (!ipLimitResult.valid) {
          return ipLimitResult;
        }
      }

      // 检查指纹速率限制
      if (this.config.fingerprintLimit > 0 && fingerprint) {
        const fingerprintLimitResult = await this.checkFingerprintRateLimit(c, fingerprint);
        if (!fingerprintLimitResult.valid) {
          return fingerprintLimitResult;
        }
      }

      return { valid: true };
    } catch (error) {
      console.error('Error checking rate limit:', error);
      // 出错时允许请求通过，避免阻止合法请求
      return { valid: true };
    }
  }

  /**
   * 检查 IP 速率限制
   * @param c 上下文
   * @param clientIp 客户端 IP
   */
  private async checkIpRateLimit(c: Context, clientIp: string): Promise<ValidationResult> {
    const key = `ratelimit:ip:${clientIp}`;
    
    // 从 KV 存储获取当前计数
    let count = 0;
    
    try {
      // 尝试从 KV 存储获取计数
      if (c.env && c.env.SURVEY_SECURITY) {
        const storedCount = await c.env.SURVEY_SECURITY.get(key);
        count = storedCount ? parseInt(storedCount, 10) : 0;
      } else {
        // 如果没有 KV 存储，使用内存存储（仅用于开发环境）
        const memoryStorage = global.__rateLimit = global.__rateLimit || {};
        count = memoryStorage[key] || 0;
      }
      
      // 检查是否超过限制
      if (count >= this.config.ipLimit) {
        return {
          valid: false,
          error: `请求频率过高，请在 ${Math.ceil(this.config.cooldownPeriod / 60)} 分钟后重试`,
          status: 429
        };
      }
      
      // 增加计数
      count++;
      
      // 存储计数
      if (c.env && c.env.SURVEY_SECURITY) {
        await c.env.SURVEY_SECURITY.put(key, count.toString(), {
          expirationTtl: this.config.cooldownPeriod
        });
      } else {
        // 内存存储
        const memoryStorage = global.__rateLimit = global.__rateLimit || {};
        memoryStorage[key] = count;
        
        // 设置过期时间
        setTimeout(() => {
          if (memoryStorage[key] === count) {
            delete memoryStorage[key];
          }
        }, this.config.cooldownPeriod * 1000);
      }
      
      return { valid: true };
    } catch (error) {
      console.error('Error checking IP rate limit:', error);
      // 出错时允许请求通过
      return { valid: true };
    }
  }

  /**
   * 检查指纹速率限制
   * @param c 上下文
   * @param fingerprint 浏览器指纹
   */
  private async checkFingerprintRateLimit(c: Context, fingerprint: string): Promise<ValidationResult> {
    const key = `ratelimit:fp:${fingerprint}`;
    
    // 从 KV 存储获取当前计数
    let count = 0;
    
    try {
      // 尝试从 KV 存储获取计数
      if (c.env && c.env.SURVEY_SECURITY) {
        const storedCount = await c.env.SURVEY_SECURITY.get(key);
        count = storedCount ? parseInt(storedCount, 10) : 0;
      } else {
        // 如果没有 KV 存储，使用内存存储（仅用于开发环境）
        const memoryStorage = global.__rateLimit = global.__rateLimit || {};
        count = memoryStorage[key] || 0;
      }
      
      // 检查是否超过限制
      if (count >= this.config.fingerprintLimit) {
        return {
          valid: false,
          error: `请求频率过高，请在 ${Math.ceil(this.config.cooldownPeriod / 60)} 分钟后重试`,
          status: 429
        };
      }
      
      // 增加计数
      count++;
      
      // 存储计数
      if (c.env && c.env.SURVEY_SECURITY) {
        await c.env.SURVEY_SECURITY.put(key, count.toString(), {
          expirationTtl: this.config.cooldownPeriod
        });
      } else {
        // 内存存储
        const memoryStorage = global.__rateLimit = global.__rateLimit || {};
        memoryStorage[key] = count;
        
        // 设置过期时间
        setTimeout(() => {
          if (memoryStorage[key] === count) {
            delete memoryStorage[key];
          }
        }, this.config.cooldownPeriod * 1000);
      }
      
      return { valid: true };
    } catch (error) {
      console.error('Error checking fingerprint rate limit:', error);
      // 出错时允许请求通过
      return { valid: true };
    }
  }
}

// 为内存存储声明全局类型
declare global {
  var __rateLimit: Record<string, number>;
}
