import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { Separator } from '@/components/ui/separator';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import { 
  Database, 
  Trash2, 
  RefreshCw, 
  BarChart3, 
  Clock, 
  HardDrive,
  Zap,
  AlertTriangle,
  CheckCircle,
  TrendingUp,
  Activity
} from 'lucide-react';
import { useToast } from '@/components/ui/use-toast';
import { 
  globalCache, 
  contentCache, 
  searchCache, 
  userCache,
  type CacheStats 
} from '@/utils/cache';

/**
 * 缓存管理面板组件
 * 
 * 功能：
 * - 缓存统计监控
 * - 缓存清理管理
 * - 性能分析
 * - 内存使用监控
 */
const CacheManagementPanel: React.FC = () => {
  const { toast } = useToast();
  
  // 缓存统计状态
  const [cacheStats, setCacheStats] = useState<{
    global: CacheStats;
    content: CacheStats;
    search: CacheStats;
    user: CacheStats;
  }>({
    global: { hits: 0, misses: 0, hitRate: 0, totalSize: 0, itemCount: 0, memoryUsage: 0 },
    content: { hits: 0, misses: 0, hitRate: 0, totalSize: 0, itemCount: 0, memoryUsage: 0 },
    search: { hits: 0, misses: 0, hitRate: 0, totalSize: 0, itemCount: 0, memoryUsage: 0 },
    user: { hits: 0, misses: 0, hitRate: 0, totalSize: 0, itemCount: 0, memoryUsage: 0 }
  });

  // 性能指标
  const [performanceMetrics, setPerformanceMetrics] = useState({
    memoryUsage: 0,
    memoryLimit: 0,
    memoryUsagePercent: 0,
    cacheEfficiency: 0,
    averageResponseTime: 0
  });

  // 缓存健康状态
  const [cacheHealth, setCacheHealth] = useState<{
    status: 'healthy' | 'warning' | 'critical';
    issues: string[];
    recommendations: string[];
  }>({
    status: 'healthy',
    issues: [],
    recommendations: []
  });

  // 更新缓存统计
  const updateCacheStats = () => {
    setCacheStats({
      global: globalCache.getStats(),
      content: contentCache.getStats(),
      search: searchCache.getStats(),
      user: userCache.getStats()
    });
  };

  // 更新性能指标
  const updatePerformanceMetrics = () => {
    if (typeof window !== 'undefined' && 'memory' in performance) {
      const memory = (performance as any).memory;
      const memoryUsage = memory.usedJSHeapSize;
      const memoryLimit = memory.jsHeapSizeLimit;
      const memoryUsagePercent = (memoryUsage / memoryLimit) * 100;

      // 计算缓存效率
      const totalHits = cacheStats.global.hits + cacheStats.content.hits + 
                       cacheStats.search.hits + cacheStats.user.hits;
      const totalRequests = totalHits + cacheStats.global.misses + 
                           cacheStats.content.misses + cacheStats.search.misses + 
                           cacheStats.user.misses;
      const cacheEfficiency = totalRequests > 0 ? (totalHits / totalRequests) * 100 : 0;

      setPerformanceMetrics({
        memoryUsage: memoryUsage / 1024 / 1024, // MB
        memoryLimit: memoryLimit / 1024 / 1024, // MB
        memoryUsagePercent,
        cacheEfficiency,
        averageResponseTime: 0 // 这里可以集成实际的响应时间统计
      });
    }
  };

  // 分析缓存健康状态
  const analyzeCacheHealth = () => {
    const issues: string[] = [];
    const recommendations: string[] = [];
    let status: 'healthy' | 'warning' | 'critical' = 'healthy';

    // 检查内存使用
    if (performanceMetrics.memoryUsagePercent > 80) {
      status = 'critical';
      issues.push('内存使用率过高');
      recommendations.push('清理缓存或增加内存限制');
    } else if (performanceMetrics.memoryUsagePercent > 60) {
      status = 'warning';
      issues.push('内存使用率较高');
      recommendations.push('考虑清理部分缓存');
    }

    // 检查缓存效率
    if (performanceMetrics.cacheEfficiency < 30) {
      if (status !== 'critical') status = 'warning';
      issues.push('缓存命中率过低');
      recommendations.push('优化缓存策略或增加缓存时间');
    }

    // 检查缓存大小
    const totalCacheSize = cacheStats.global.totalSize + cacheStats.content.totalSize + 
                          cacheStats.search.totalSize + cacheStats.user.totalSize;
    if (totalCacheSize > 50 * 1024 * 1024) { // 50MB
      if (status !== 'critical') status = 'warning';
      issues.push('缓存占用空间过大');
      recommendations.push('清理过期缓存或减少缓存项目');
    }

    setCacheHealth({ status, issues, recommendations });
  };

  // 清理指定缓存
  const clearCache = (cacheType: 'global' | 'content' | 'search' | 'user' | 'all') => {
    try {
      switch (cacheType) {
        case 'global':
          globalCache.clear();
          break;
        case 'content':
          contentCache.clear();
          break;
        case 'search':
          searchCache.clear();
          break;
        case 'user':
          userCache.clear();
          break;
        case 'all':
          globalCache.clear();
          contentCache.clear();
          searchCache.clear();
          userCache.clear();
          break;
      }

      updateCacheStats();
      updatePerformanceMetrics();
      
      toast({
        title: '缓存清理成功',
        description: `${cacheType === 'all' ? '所有' : cacheType}缓存已清理`,
      });
    } catch (error) {
      toast({
        title: '缓存清理失败',
        description: '清理缓存时发生错误',
        variant: 'destructive',
      });
    }
  };

  // 格式化字节大小
  const formatBytes = (bytes: number): string => {
    if (bytes === 0) return '0 B';
    const k = 1024;
    const sizes = ['B', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  // 获取状态颜色
  const getStatusColor = (status: string) => {
    switch (status) {
      case 'healthy':
        return 'text-green-600';
      case 'warning':
        return 'text-yellow-600';
      case 'critical':
        return 'text-red-600';
      default:
        return 'text-gray-600';
    }
  };

  // 获取状态图标
  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'healthy':
        return <CheckCircle className="h-5 w-5 text-green-600" />;
      case 'warning':
        return <AlertTriangle className="h-5 w-5 text-yellow-600" />;
      case 'critical':
        return <AlertTriangle className="h-5 w-5 text-red-600" />;
      default:
        return <Activity className="h-5 w-5 text-gray-600" />;
    }
  };

  // 定期更新统计
  useEffect(() => {
    const interval = setInterval(() => {
      updateCacheStats();
      updatePerformanceMetrics();
      analyzeCacheHealth();
    }, 5000);

    // 初始更新
    updateCacheStats();
    updatePerformanceMetrics();
    analyzeCacheHealth();

    return () => clearInterval(interval);
  }, [cacheStats]);

  return (
    <div className="space-y-6">
      {/* 缓存健康状态 */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            {getStatusIcon(cacheHealth.status)}
            缓存健康状态
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <span className="font-medium">整体状态</span>
              <Badge 
                variant={cacheHealth.status === 'healthy' ? 'default' : 'destructive'}
                className={getStatusColor(cacheHealth.status)}
              >
                {cacheHealth.status === 'healthy' ? '健康' : 
                 cacheHealth.status === 'warning' ? '警告' : '严重'}
              </Badge>
            </div>

            {cacheHealth.issues.length > 0 && (
              <Alert variant={cacheHealth.status === 'critical' ? 'destructive' : 'default'}>
                <AlertTriangle className="h-4 w-4" />
                <AlertTitle>发现问题</AlertTitle>
                <AlertDescription>
                  <ul className="list-disc list-inside space-y-1">
                    {cacheHealth.issues.map((issue, index) => (
                      <li key={index}>{issue}</li>
                    ))}
                  </ul>
                </AlertDescription>
              </Alert>
            )}

            {cacheHealth.recommendations.length > 0 && (
              <Alert>
                <CheckCircle className="h-4 w-4" />
                <AlertTitle>优化建议</AlertTitle>
                <AlertDescription>
                  <ul className="list-disc list-inside space-y-1">
                    {cacheHealth.recommendations.map((rec, index) => (
                      <li key={index}>{rec}</li>
                    ))}
                  </ul>
                </AlertDescription>
              </Alert>
            )}
          </div>
        </CardContent>
      </Card>

      {/* 性能概览 */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-500">内存使用</p>
                <p className="text-2xl font-bold">
                  {performanceMetrics.memoryUsage.toFixed(1)}MB
                </p>
              </div>
              <HardDrive className="h-8 w-8 text-blue-500" />
            </div>
            <Progress 
              value={performanceMetrics.memoryUsagePercent} 
              className="mt-2"
            />
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-500">缓存效率</p>
                <p className="text-2xl font-bold">
                  {performanceMetrics.cacheEfficiency.toFixed(1)}%
                </p>
              </div>
              <TrendingUp className="h-8 w-8 text-green-500" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-500">总缓存项</p>
                <p className="text-2xl font-bold">
                  {cacheStats.global.itemCount + cacheStats.content.itemCount + 
                   cacheStats.search.itemCount + cacheStats.user.itemCount}
                </p>
              </div>
              <Database className="h-8 w-8 text-purple-500" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-500">响应时间</p>
                <p className="text-2xl font-bold">
                  {performanceMetrics.averageResponseTime.toFixed(0)}ms
                </p>
              </div>
              <Zap className="h-8 w-8 text-yellow-500" />
            </div>
          </CardContent>
        </Card>
      </div>

      {/* 详细缓存统计 */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <CardTitle className="flex items-center gap-2">
              <BarChart3 className="h-5 w-5" />
              缓存详细统计
            </CardTitle>
            <div className="flex gap-2">
              <Button
                variant="outline"
                size="sm"
                onClick={() => {
                  updateCacheStats();
                  updatePerformanceMetrics();
                }}
              >
                <RefreshCw className="h-4 w-4 mr-2" />
                刷新
              </Button>
              <Button
                variant="destructive"
                size="sm"
                onClick={() => clearCache('all')}
              >
                <Trash2 className="h-4 w-4 mr-2" />
                清空所有缓存
              </Button>
            </div>
          </div>
        </CardHeader>
        <CardContent>
          <div className="space-y-6">
            {/* 全局缓存 */}
            <div>
              <div className="flex items-center justify-between mb-2">
                <h4 className="font-medium">全局缓存</h4>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => clearCache('global')}
                >
                  清理
                </Button>
              </div>
              <div className="grid grid-cols-2 md:grid-cols-5 gap-4 text-sm">
                <div>
                  <span className="text-gray-500">命中次数</span>
                  <p className="font-medium">{cacheStats.global.hits}</p>
                </div>
                <div>
                  <span className="text-gray-500">未命中次数</span>
                  <p className="font-medium">{cacheStats.global.misses}</p>
                </div>
                <div>
                  <span className="text-gray-500">命中率</span>
                  <p className="font-medium">{(cacheStats.global.hitRate * 100).toFixed(1)}%</p>
                </div>
                <div>
                  <span className="text-gray-500">项目数量</span>
                  <p className="font-medium">{cacheStats.global.itemCount}</p>
                </div>
                <div>
                  <span className="text-gray-500">占用空间</span>
                  <p className="font-medium">{formatBytes(cacheStats.global.totalSize)}</p>
                </div>
              </div>
            </div>

            <Separator />

            {/* 内容缓存 */}
            <div>
              <div className="flex items-center justify-between mb-2">
                <h4 className="font-medium">内容缓存</h4>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => clearCache('content')}
                >
                  清理
                </Button>
              </div>
              <div className="grid grid-cols-2 md:grid-cols-5 gap-4 text-sm">
                <div>
                  <span className="text-gray-500">命中次数</span>
                  <p className="font-medium">{cacheStats.content.hits}</p>
                </div>
                <div>
                  <span className="text-gray-500">未命中次数</span>
                  <p className="font-medium">{cacheStats.content.misses}</p>
                </div>
                <div>
                  <span className="text-gray-500">命中率</span>
                  <p className="font-medium">{(cacheStats.content.hitRate * 100).toFixed(1)}%</p>
                </div>
                <div>
                  <span className="text-gray-500">项目数量</span>
                  <p className="font-medium">{cacheStats.content.itemCount}</p>
                </div>
                <div>
                  <span className="text-gray-500">占用空间</span>
                  <p className="font-medium">{formatBytes(cacheStats.content.totalSize)}</p>
                </div>
              </div>
            </div>

            <Separator />

            {/* 搜索缓存 */}
            <div>
              <div className="flex items-center justify-between mb-2">
                <h4 className="font-medium">搜索缓存</h4>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => clearCache('search')}
                >
                  清理
                </Button>
              </div>
              <div className="grid grid-cols-2 md:grid-cols-5 gap-4 text-sm">
                <div>
                  <span className="text-gray-500">命中次数</span>
                  <p className="font-medium">{cacheStats.search.hits}</p>
                </div>
                <div>
                  <span className="text-gray-500">未命中次数</span>
                  <p className="font-medium">{cacheStats.search.misses}</p>
                </div>
                <div>
                  <span className="text-gray-500">命中率</span>
                  <p className="font-medium">{(cacheStats.search.hitRate * 100).toFixed(1)}%</p>
                </div>
                <div>
                  <span className="text-gray-500">项目数量</span>
                  <p className="font-medium">{cacheStats.search.itemCount}</p>
                </div>
                <div>
                  <span className="text-gray-500">占用空间</span>
                  <p className="font-medium">{formatBytes(cacheStats.search.totalSize)}</p>
                </div>
              </div>
            </div>

            <Separator />

            {/* 用户缓存 */}
            <div>
              <div className="flex items-center justify-between mb-2">
                <h4 className="font-medium">用户缓存</h4>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => clearCache('user')}
                >
                  清理
                </Button>
              </div>
              <div className="grid grid-cols-2 md:grid-cols-5 gap-4 text-sm">
                <div>
                  <span className="text-gray-500">命中次数</span>
                  <p className="font-medium">{cacheStats.user.hits}</p>
                </div>
                <div>
                  <span className="text-gray-500">未命中次数</span>
                  <p className="font-medium">{cacheStats.user.misses}</p>
                </div>
                <div>
                  <span className="text-gray-500">命中率</span>
                  <p className="font-medium">{(cacheStats.user.hitRate * 100).toFixed(1)}%</p>
                </div>
                <div>
                  <span className="text-gray-500">项目数量</span>
                  <p className="font-medium">{cacheStats.user.itemCount}</p>
                </div>
                <div>
                  <span className="text-gray-500">占用空间</span>
                  <p className="font-medium">{formatBytes(cacheStats.user.totalSize)}</p>
                </div>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default CacheManagementPanel;
