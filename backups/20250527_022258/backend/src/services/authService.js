/**
 * 用户认证服务 - 支持A+B匿名身份验证
 */

const crypto = require('crypto');
const jwt = require('jsonwebtoken');

class AuthService {
  constructor(dbService) {
    this.db = dbService;
    this.jwtSecret = process.env.JWT_SECRET || 'your-secret-key';
    this.saltRounds = 12;
  }

  /**
   * 生成A+B组合的UUID
   * @param {string} identityA - A值 (11位数字)
   * @param {string} identityB - B值 (4位或6位密码)
   * @returns {string} 生成的UUID
   */
  generateAnonymousUUID(identityA, identityB) {
    // 服务器端加盐
    const salt = process.env.ANONYMOUS_SALT || 'default-salt-2025';
    const combined = `${identityA}_${identityB}_${salt}`;
    
    // 使用SHA-256生成哈希
    const hash = crypto.createHash('sha256').update(combined).digest('hex');
    
    // 生成UUID格式
    return `uuid_${hash.substring(0, 16)}`;
  }

  /**
   * 生成身份哈希
   * @param {string} value - 原始值
   * @returns {string} 哈希值
   */
  generateIdentityHash(value) {
    const salt = process.env.IDENTITY_SALT || 'identity-salt-2025';
    return crypto.createHash('sha256').update(`${value}_${salt}`).digest('hex');
  }

  /**
   * A+B匿名身份注册
   * @param {Object} userData - 用户数据
   * @returns {Object} 注册结果
   */
  async registerAnonymous(userData) {
    const { identity_a, identity_b, display_name, education_level, industry_code, graduation_year, location } = userData;

    // 验证A+B格式
    if (!this.validateIdentityA(identity_a) || !this.validateIdentityB(identity_b)) {
      throw new Error('身份验证格式不正确');
    }

    // 生成UUID和哈希
    const anonymousUUID = this.generateAnonymousUUID(identity_a, identity_b);
    const identityAHash = this.generateIdentityHash(identity_a);
    const identityBHash = this.generateIdentityHash(identity_b);
    const userUUID = `user_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;

    // 检查是否已存在
    const existingUser = await this.db.query(
      'SELECT uuid FROM users WHERE anonymous_uuid = ?',
      [anonymousUUID]
    );

    if (existingUser.length > 0) {
      throw new Error('该身份组合已注册，请直接登录');
    }

    // 创建用户记录
    const userRecord = {
      id: userUUID,
      uuid: userUUID,
      auth_type: 'anonymous',
      identity_a_hash: identityAHash,
      identity_b_hash: identityBHash,
      anonymous_uuid: anonymousUUID,
      display_name: display_name || '匿名用户',
      education_level,
      education_level_display: this.getEducationDisplay(education_level),
      industry_code,
      industry_display: this.getIndustryDisplay(industry_code),
      graduation_year,
      location,
      is_anonymous: true,
      status: 'active',
      created_at: new Date().toISOString()
    };

    await this.db.insert('users', userRecord);

    // 生成JWT令牌
    const token = this.generateJWT({
      user_uuid: userUUID,
      anonymous_uuid: anonymousUUID,
      auth_type: 'anonymous'
    });

    return {
      user_uuid: userUUID,
      anonymous_uuid: anonymousUUID,
      display_name: userRecord.display_name,
      auth_type: 'anonymous',
      token,
      expires_at: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000).toISOString() // 7天
    };
  }

  /**
   * A+B匿名身份登录
   * @param {string} identityA - A值
   * @param {string} identityB - B值
   * @returns {Object} 登录结果
   */
  async loginAnonymous(identityA, identityB) {
    // 验证格式
    if (!this.validateIdentityA(identityA) || !this.validateIdentityB(identityB)) {
      throw new Error('身份验证格式不正确');
    }

    // 生成UUID查找用户
    const anonymousUUID = this.generateAnonymousUUID(identityA, identityB);
    
    const users = await this.db.query(
      'SELECT * FROM users WHERE anonymous_uuid = ? AND auth_type = "anonymous"',
      [anonymousUUID]
    );

    if (users.length === 0) {
      throw new Error('身份验证失败，请检查A+B组合或先注册');
    }

    const user = users[0];

    // 更新最后登录时间
    await this.db.update('users', 
      { last_login_at: new Date().toISOString() },
      { uuid: user.uuid }
    );

    // 生成JWT令牌
    const token = this.generateJWT({
      user_uuid: user.uuid,
      anonymous_uuid: user.anonymous_uuid,
      auth_type: 'anonymous'
    });

    return {
      user_uuid: user.uuid,
      anonymous_uuid: user.anonymous_uuid,
      display_name: user.display_name,
      auth_type: 'anonymous',
      token,
      expires_at: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000).toISOString(),
      stories_count: user.stories_count || 0,
      questionnaire_count: user.questionnaire_count || 0
    };
  }

  /**
   * 查询用户内容
   * @param {string} identityA - A值
   * @param {string} identityB - B值
   * @returns {Object} 用户内容
   */
  async getUserContent(identityA, identityB) {
    const anonymousUUID = this.generateAnonymousUUID(identityA, identityB);
    
    const users = await this.db.query(
      'SELECT * FROM users WHERE anonymous_uuid = ?',
      [anonymousUUID]
    );

    if (users.length === 0) {
      throw new Error('未找到相关内容');
    }

    const user = users[0];

    // 查询用户故事
    const stories = await this.db.query(`
      SELECT id, title, status, views, likes, created_at 
      FROM stories 
      WHERE user_uuid = ? 
      ORDER BY created_at DESC
    `, [user.uuid]);

    // 查询问卷提交
    const questionnaires = await this.db.query(`
      SELECT id, status, created_at as submitted_at 
      FROM questionnaire_responses 
      WHERE user_uuid = ? 
      ORDER BY created_at DESC
    `, [user.uuid]);

    // 查询待审核内容
    const pendingReviews = await this.db.query(`
      SELECT id, 'story' as type, title, status, created_at as submitted_at 
      FROM stories 
      WHERE user_uuid = ? AND status = 'pending'
      ORDER BY created_at DESC
    `, [user.uuid]);

    return {
      user_info: {
        uuid: user.uuid,
        anonymous_uuid: user.anonymous_uuid,
        display_name: user.display_name,
        stories_count: stories.length,
        questionnaire_count: questionnaires.length
      },
      stories,
      questionnaires,
      pending_reviews: pendingReviews
    };
  }

  /**
   * 验证JWT令牌
   * @param {string} token - JWT令牌
   * @returns {Object} 用户信息
   */
  async verifyToken(token) {
    try {
      const decoded = jwt.verify(token, this.jwtSecret);
      
      const users = await this.db.query(
        'SELECT * FROM users WHERE uuid = ?',
        [decoded.user_uuid]
      );

      if (users.length === 0) {
        throw new Error('用户不存在');
      }

      return users[0];
    } catch (error) {
      throw new Error('令牌验证失败');
    }
  }

  /**
   * 生成JWT令牌
   * @param {Object} payload - 载荷
   * @returns {string} JWT令牌
   */
  generateJWT(payload) {
    return jwt.sign(payload, this.jwtSecret, { expiresIn: '7d' });
  }

  /**
   * 验证A值格式 (11位数字)
   * @param {string} identityA - A值
   * @returns {boolean} 是否有效
   */
  validateIdentityA(identityA) {
    return /^\d{11}$/.test(identityA);
  }

  /**
   * 验证B值格式 (4位或6位数字)
   * @param {string} identityB - B值
   * @returns {boolean} 是否有效
   */
  validateIdentityB(identityB) {
    return /^\d{4}$/.test(identityB) || /^\d{6}$/.test(identityB);
  }

  /**
   * 获取学历显示名称
   * @param {string} level - 学历代码
   * @returns {string} 显示名称
   */
  getEducationDisplay(level) {
    const mapping = {
      'high_school': '高中',
      'college': '大专',
      'bachelor': '本科',
      'master': '硕士',
      'doctor': '博士'
    };
    return mapping[level] || level;
  }

  /**
   * 获取行业显示名称
   * @param {string} code - 行业代码
   * @returns {string} 显示名称
   */
  getIndustryDisplay(code) {
    const mapping = {
      'technology': '互联网/IT',
      'finance': '金融',
      'education': '教育',
      'healthcare': '医疗',
      'manufacturing': '制造业',
      'retail': '零售',
      'consulting': '咨询'
    };
    return mapping[code] || code;
  }
}

module.exports = AuthService;
