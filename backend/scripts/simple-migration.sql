-- 简化版数据库优化迁移脚本
-- 直接在现有数据库上添加优化表结构

BEGIN TRANSACTION;

-- 1. 创建用户优化表
CREATE TABLE IF NOT EXISTS users_optimized (
  id TEXT PRIMARY KEY,
  email TEXT UNIQUE,
  email_verified BOOLEA<PERSON> DEFAULT false,
  user_type TEXT DEFAULT 'anonymous',
  role TEXT DEFAULT 'user',
  username TEXT,
  display_name TEXT,
  password_hash TEXT,
  anonymous_id TEXT UNIQUE,
  is_anonymous_registered BOOLEAN DEFAULT false,
  ip_address TEXT,
  user_agent TEXT,
  last_login_at DATETIME,
  is_active BOOLEAN DEFAULT true,
  created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
  updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
  created_by TEXT,
  metadata TEXT,
  tags TEXT,
  extra_data TEXT,
  version INTEGER DEFAULT 1,
  schema_version TEXT DEFAULT '1.0',
  is_deleted BOOLEAN DEFAULT false,
  is_test_data BOOLEAN DEFAULT false,
  test_data_version TEXT,
  test_data_set TEXT
);

-- 2. 创建内容元数据表
CREATE TABLE IF NOT EXISTS content_metadata (
  id TEXT PRIMARY KEY,
  user_id TEXT,
  content_type TEXT NOT NULL,
  title TEXT,
  category TEXT,
  tags TEXT,
  status TEXT DEFAULT 'pending',
  likes INTEGER DEFAULT 0,
  dislikes INTEGER DEFAULT 0,
  views INTEGER DEFAULT 0,
  word_count INTEGER DEFAULT 0,
  r2_path TEXT,
  created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
  updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
  metadata TEXT,
  extra_data TEXT,
  version INTEGER DEFAULT 1,
  schema_version TEXT DEFAULT '1.0',
  is_deleted BOOLEAN DEFAULT false,
  is_public BOOLEAN DEFAULT true,
  is_test_data BOOLEAN DEFAULT false,
  FOREIGN KEY (user_id) REFERENCES users_optimized(id)
);

-- 3. 创建问卷模板表
CREATE TABLE IF NOT EXISTS questionnaire_templates (
  id TEXT PRIMARY KEY,
  title TEXT NOT NULL,
  description TEXT,
  version TEXT DEFAULT '1.0',
  is_active BOOLEAN DEFAULT true,
  created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
  updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
  metadata TEXT,
  schema_version TEXT DEFAULT '1.0'
);

-- 4. 创建题目表
CREATE TABLE IF NOT EXISTS questions (
  id TEXT PRIMARY KEY,
  questionnaire_id TEXT NOT NULL,
  question_number INTEGER NOT NULL,
  question_text TEXT NOT NULL,
  question_type TEXT NOT NULL,
  options TEXT,
  validation TEXT,
  is_required BOOLEAN DEFAULT true,
  created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
  updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
  FOREIGN KEY (questionnaire_id) REFERENCES questionnaire_templates(id)
);

-- 5. 创建用户答案表
CREATE TABLE IF NOT EXISTS question_answers (
  id TEXT PRIMARY KEY,
  user_id TEXT NOT NULL,
  question_id TEXT NOT NULL,
  response_id TEXT NOT NULL,
  answer_type TEXT NOT NULL,
  answer_value TEXT NOT NULL,
  answer_text TEXT,
  answer_number REAL,
  created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
  FOREIGN KEY (user_id) REFERENCES users_optimized(id),
  FOREIGN KEY (question_id) REFERENCES questions(id)
);

-- 6. 创建问卷回复优化表
CREATE TABLE IF NOT EXISTS questionnaire_responses_optimized (
  id TEXT PRIMARY KEY,
  user_id TEXT,
  questionnaire_id TEXT NOT NULL,
  sequence_number TEXT UNIQUE,
  submission_session_id TEXT,
  ip_address TEXT,
  user_agent TEXT,
  is_anonymous BOOLEAN DEFAULT true,
  status TEXT DEFAULT 'submitted',
  completion_rate REAL DEFAULT 0,
  created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
  updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
  metadata TEXT,
  version INTEGER DEFAULT 1,
  schema_version TEXT DEFAULT '1.0',
  is_deleted BOOLEAN DEFAULT false,
  is_test_data BOOLEAN DEFAULT false,
  FOREIGN KEY (user_id) REFERENCES users_optimized(id),
  FOREIGN KEY (questionnaire_id) REFERENCES questionnaire_templates(id)
);

-- 7. 创建问卷心声表
CREATE TABLE IF NOT EXISTS questionnaire_voices (
  id TEXT PRIMARY KEY,
  response_id TEXT NOT NULL,
  user_id TEXT,
  submission_session_id TEXT,
  voice_type TEXT NOT NULL,
  title TEXT NOT NULL,
  content TEXT NOT NULL,
  content_id TEXT,
  status TEXT DEFAULT 'pending',
  moderation_notes TEXT,
  reviewed_by TEXT,
  reviewed_at DATETIME,
  is_public BOOLEAN DEFAULT true,
  is_anonymous BOOLEAN DEFAULT true,
  ip_address TEXT,
  user_agent TEXT,
  created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
  updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
  metadata TEXT,
  version INTEGER DEFAULT 1,
  schema_version TEXT DEFAULT '1.0',
  is_deleted BOOLEAN DEFAULT false,
  is_test_data BOOLEAN DEFAULT false,
  FOREIGN KEY (response_id) REFERENCES questionnaire_responses_optimized(id),
  FOREIGN KEY (user_id) REFERENCES users_optimized(id)
);

-- 8. 创建标准索引
-- 用户表索引
CREATE INDEX IF NOT EXISTS idx_users_optimized_role ON users_optimized(role);
CREATE INDEX IF NOT EXISTS idx_users_optimized_anonymous_id ON users_optimized(anonymous_id);
CREATE INDEX IF NOT EXISTS idx_users_optimized_active ON users_optimized(is_active);
CREATE INDEX IF NOT EXISTS idx_users_optimized_created_at ON users_optimized(created_at);
CREATE INDEX IF NOT EXISTS idx_users_optimized_email ON users_optimized(email);

-- 内容元数据表索引
CREATE INDEX IF NOT EXISTS idx_content_metadata_user_id ON content_metadata(user_id);
CREATE INDEX IF NOT EXISTS idx_content_metadata_type ON content_metadata(content_type);
CREATE INDEX IF NOT EXISTS idx_content_metadata_status ON content_metadata(status);
CREATE INDEX IF NOT EXISTS idx_content_metadata_created_at ON content_metadata(created_at);
CREATE INDEX IF NOT EXISTS idx_content_metadata_user_type ON content_metadata(user_id, content_type);
CREATE INDEX IF NOT EXISTS idx_content_metadata_public ON content_metadata(is_public);
CREATE INDEX IF NOT EXISTS idx_content_metadata_deleted ON content_metadata(is_deleted);

-- 问卷相关索引
CREATE INDEX IF NOT EXISTS idx_questionnaire_templates_active ON questionnaire_templates(is_active);
CREATE INDEX IF NOT EXISTS idx_questions_questionnaire ON questions(questionnaire_id);
CREATE INDEX IF NOT EXISTS idx_questions_type ON questions(question_type);
CREATE INDEX IF NOT EXISTS idx_questions_number ON questions(question_number);
CREATE INDEX IF NOT EXISTS idx_question_answers_question ON question_answers(question_id);
CREATE INDEX IF NOT EXISTS idx_question_answers_user ON question_answers(user_id);
CREATE INDEX IF NOT EXISTS idx_question_answers_response ON question_answers(response_id);
CREATE INDEX IF NOT EXISTS idx_questionnaire_responses_user ON questionnaire_responses_optimized(user_id);
CREATE INDEX IF NOT EXISTS idx_questionnaire_responses_status ON questionnaire_responses_optimized(status);
CREATE INDEX IF NOT EXISTS idx_questionnaire_responses_session ON questionnaire_responses_optimized(submission_session_id);

-- 心声表索引
CREATE INDEX IF NOT EXISTS idx_questionnaire_voices_response ON questionnaire_voices(response_id);
CREATE INDEX IF NOT EXISTS idx_questionnaire_voices_user ON questionnaire_voices(user_id);
CREATE INDEX IF NOT EXISTS idx_questionnaire_voices_status ON questionnaire_voices(status);
CREATE INDEX IF NOT EXISTS idx_questionnaire_voices_type ON questionnaire_voices(voice_type);
CREATE INDEX IF NOT EXISTS idx_questionnaire_voices_public ON questionnaire_voices(is_public);
CREATE INDEX IF NOT EXISTS idx_questionnaire_voices_session ON questionnaire_voices(submission_session_id);

-- 9. 插入默认问卷模板
INSERT OR IGNORE INTO questionnaire_templates (
  id, title, description, version, is_active, created_at, metadata, schema_version
) VALUES (
  'quest_default_employment_survey',
  '大学生就业调研问卷',
  '针对大学生就业情况的调研问卷',
  '1.0',
  1,
  datetime('now'),
  '{"isDefault": true, "category": "employment"}',
  '1.0'
);

-- 10. 插入默认问卷题目
INSERT OR IGNORE INTO questions (id, questionnaire_id, question_number, question_text, question_type, options, is_required, created_at) VALUES
('qitem_education_level', 'quest_default_employment_survey', 1, '教育水平', 'single', '["本科", "硕士", "博士", "其他"]', 1, datetime('now')),
('qitem_major', 'quest_default_employment_survey', 2, '专业', 'text', null, 1, datetime('now')),
('qitem_graduation_year', 'quest_default_employment_survey', 3, '毕业年份', 'number', null, 1, datetime('now')),
('qitem_region', 'quest_default_employment_survey', 4, '所在地区', 'text', null, 1, datetime('now')),
('qitem_employment_status', 'quest_default_employment_survey', 5, '就业状态', 'single', '["已就业", "未就业", "继续深造", "创业", "其他"]', 1, datetime('now')),
('qitem_current_industry', 'quest_default_employment_survey', 6, '当前行业', 'text', null, 0, datetime('now')),
('qitem_current_position', 'quest_default_employment_survey', 7, '当前职位', 'text', null, 0, datetime('now')),
('qitem_monthly_salary', 'quest_default_employment_survey', 8, '月薪（元）', 'number', null, 0, datetime('now')),
('qitem_job_satisfaction', 'quest_default_employment_survey', 9, '工作满意度', 'single', '["很满意", "满意", "一般", "不满意", "很不满意"]', 0, datetime('now')),
('qitem_unemployment_duration', 'quest_default_employment_survey', 10, '失业时长', 'single', '["1个月以内", "1-3个月", "3-6个月", "6个月以上"]', 0, datetime('now')),
('qitem_job_hunting_difficulty', 'quest_default_employment_survey', 11, '求职难度（1-5分）', 'number', null, 0, datetime('now')),
('qitem_advice_for_students', 'quest_default_employment_survey', 12, '给学弟学妹的建议', 'text', null, 0, datetime('now')),
('qitem_observation_on_employment', 'quest_default_employment_survey', 13, '对就业形势的观察', 'text', null, 0, datetime('now'));

-- 11. 记录迁移信息
INSERT OR REPLACE INTO SystemConfig (id, key, value, description, category, createdAt, updatedAt) VALUES
('migration_v1_0', 'database_migration_v1_0', '{"version": "1.0", "completed_at": "' || datetime('now') || '", "status": "completed", "tables_created": ["users_optimized", "content_metadata", "questionnaire_templates", "questions", "question_answers", "questionnaire_responses_optimized", "questionnaire_voices"]}', '数据库优化迁移v1.0', 'migration', datetime('now'), datetime('now'));

COMMIT;

-- 验证迁移结果
SELECT 'Migration completed successfully. New tables created:' as message;
SELECT name FROM sqlite_master WHERE type='table' AND name LIKE '%optimized%' OR name IN ('content_metadata', 'questionnaire_templates', 'questions', 'question_answers', 'questionnaire_voices');
