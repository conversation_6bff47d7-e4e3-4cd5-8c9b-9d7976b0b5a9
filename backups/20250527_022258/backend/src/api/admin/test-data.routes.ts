/**
 * 测试数据管理路由
 */

import { Hono } from 'hono';
import { Env } from '../../types';
import * as testDataController from './test-data.controller';

// 创建路由
const app = new Hono<{ Bindings: Env }>();

// 获取测试数据状态
app.get('/status', ...testDataController.getStatus);

// 导入测试数据
app.post('/import', ...testDataController.importData);

// 清除测试数据
app.post('/clear', ...testDataController.clearData);

export default app;
