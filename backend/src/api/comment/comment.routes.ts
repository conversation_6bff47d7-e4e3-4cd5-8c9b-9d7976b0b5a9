/**
 * 评论路由
 * 
 * 处理评论相关的路由
 */

import { Hono } from 'hono';
import { Env } from '../../types';
import * as commentController from './comment.controller';
import { rateLimit } from '../../middlewares/rateLimit';
import { commentModeration } from '../../middlewares/contentModeration.middleware';
import { authMiddleware } from '../../middlewares/auth.middleware';

// 创建路由
const commentRouter = new Hono<{ Bindings: Env }>();

// 提交评论
commentRouter.post('/submit', 
  authMiddleware,
  // 使用同步审核中间件
  commentModeration({ async: false }),
  ...commentController.submitComment
);

// 获取评论列表
commentRouter.get('/', commentController.getComments);

// 获取评论详情
commentRouter.get('/:id', commentController.getCommentById);

// 点赞评论
commentRouter.post('/:id/like', authMiddleware, commentController.likeComment);

// 回复评论
commentRouter.post('/:id/reply', 
  authMiddleware,
  // 使用同步审核中间件
  commentModeration({ async: false }),
  commentController.replyComment
);

// 删除评论
commentRouter.delete('/:id', authMiddleware, commentController.deleteComment);

export default commentRouter;
