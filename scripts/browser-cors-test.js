// browser-cors-test.js - 在浏览器控制台运行的CORS测试工具
// 用法: 复制此代码到浏览器控制台运行

(function() {
  // 配置 - 根据实际情况修改
  const API_BASE = 'https://college-employment-survey.aibook2099.workers.dev';
  
  console.log('🔍 浏览器CORS测试开始...');
  console.log(`🌐 当前域名: ${window.location.origin}`);
  console.log(`🎯 目标API: ${API_BASE}`);
  
  async function testCORSEndpoint(endpoint, method = 'GET') {
    try {
      console.log(`\n📡 测试 ${method} ${endpoint}`);
      
      const startTime = Date.now();
      const response = await fetch(`${API_BASE}${endpoint}`, {
        method,
        mode: 'cors',
        cache: 'no-cache',
        credentials: 'omit',
        headers: {
          'Content-Type': 'application/json',
        },
      });
      
      const responseTime = Date.now() - startTime;
      
      console.log(`   状态: ${response.status} ${response.statusText}`);
      console.log(`   响应时间: ${responseTime}ms`);
      console.log(`   响应类型: ${response.type}`);
      console.log(`   响应URL: ${response.url}`);
      
      // 检查CORS头
      const corsHeaders = {
        'access-control-allow-origin': response.headers.get('access-control-allow-origin'),
        'access-control-allow-methods': response.headers.get('access-control-allow-methods'),
        'access-control-allow-headers': response.headers.get('access-control-allow-headers'),
      };
      console.log('   CORS头:', corsHeaders);
      
      // 分析CORS头
      const allowOrigin = corsHeaders['access-control-allow-origin'];
      if (allowOrigin === 'true') {
        console.log('   ⚠️  警告: CORS返回布尔值，应该返回具体域名');
      } else if (allowOrigin === window.location.origin) {
        console.log('   ✅ CORS头正确');
      } else if (allowOrigin === null) {
        console.log('   ❌ 缺少CORS头');
      } else {
        console.log(`   ❌ CORS头不匹配: 期望 ${window.location.origin}, 实际 ${allowOrigin}`);
      }
      
      if (response.ok) {
        const data = await response.json();
        console.log('   ✅ 请求成功');
        return { success: true, data, responseTime, corsHeaders };
      } else {
        console.log('   ❌ HTTP错误');
        return { success: false, status: response.status, corsHeaders };
      }
      
    } catch (error) {
      console.log(`   ❌ 请求失败: ${error.name} - ${error.message}`);
      
      // 详细错误分析
      if (error.name === 'TypeError' && error.message.includes('Failed to fetch')) {
        console.log('   🔍 可能原因:');
        console.log('     - CORS预检失败');
        console.log('     - 网络连接问题');
        console.log('     - API服务不可达');
        console.log('     - CORS配置返回布尔值而非域名');
      }
      
      return { success: false, error: error.message, errorType: error.name };
    }
  }
  
  // 测试多个端点
  const endpoints = [
    '/api/system/monitor',
    '/api/questionnaire/stats',
    '/api/questionnaire-voices',
    '/api/visualization/data',
    '/api/story/list'
  ];
  
  async function runAllTests() {
    const results = {};
    
    for (const endpoint of endpoints) {
      results[endpoint] = await testCORSEndpoint(endpoint);
      await new Promise(resolve => setTimeout(resolve, 500)); // 避免请求过快
    }
    
    // 生成测试报告
    console.log('\n📊 测试报告:');
    const successful = Object.values(results).filter(r => r.success).length;
    const total = Object.keys(results).length;
    
    console.log(`成功: ${successful}/${total}`);
    
    // 分析失败原因
    const failures = Object.entries(results).filter(([_, result]) => !result.success);
    
    if (successful === total) {
      console.log('✅ 所有CORS测试通过');
    } else {
      console.log('❌ 发现CORS问题');
      
      // 分析错误类型
      const errorTypes = failures.map(([_, result]) => result.errorType || 'HTTP_ERROR');
      const typeofErrors = errorTypes.filter(type => type === 'TypeError').length;
      
      if (typeofErrors > 0) {
        console.log('🔧 主要问题: CORS配置错误');
        console.log('   建议: 运行服务器端CORS诊断脚本');
        console.log('   命令: ./scripts/cors-diagnose.sh API_URL FRONTEND_URL');
      }
      
      console.log('\n❌ 失败的端点:');
      failures.forEach(([endpoint, result]) => {
        console.log(`   ${endpoint}: ${result.error || result.status}`);
      });
    }
    
    // 生成修复建议
    console.log('\n🔧 修复建议:');
    if (typeofErrors > 0) {
      console.log('1. 检查后端CORS配置');
      console.log('2. 确保origin函数返回具体域名而不是布尔值');
      console.log('3. 验证当前域名在CORS允许列表中');
    } else {
      console.log('1. 检查API服务是否正常运行');
      console.log('2. 验证网络连接');
      console.log('3. 检查API端点是否存在');
    }
    
    return results;
  }
  
  // 自动运行测试
  runAllTests().then(results => {
    window.corsTestResults = results;
    console.log('\n💾 测试结果已保存到 window.corsTestResults');
    console.log('📋 可以通过 console.table(window.corsTestResults) 查看详细结果');
  });
  
  // 提供手动测试函数
  window.testCORS = testCORSEndpoint;
  console.log('\n🛠️  可用函数:');
  console.log('   testCORS(endpoint) - 测试单个端点');
  console.log('   例如: testCORS("/api/system/monitor")');
})();
