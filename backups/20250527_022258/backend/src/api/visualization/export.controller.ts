/**
 * 数据导出控制器
 */

import { Context } from 'hono';
import { PrismaClient } from '@prisma/client';
import { zValidator } from '@hono/zod-validator';
import { z } from 'zod';
import config from '../../config';
import { KVStorageService } from '../../services/kvStorageService';
import type { Env } from '../../types';

// 导出参数验证schema
const exportParamsSchema = z.object({
  type: z.enum(['data', 'stats', 'charts', 'report']),
  format: z.string(),
  fields: z.array(z.string()),
  filters: z.record(z.string()),
  dateRange: z.object({
    startDate: z.string().optional(),
    endDate: z.string().optional(),
  }).optional(),
});

/**
 * 导出数据
 */
export const exportData = [
  zValidator('json', exportParamsSchema),
  async (c: Context<{ Bindings: Env }>) => {
    try {
      // 获取请求参数
      const params = c.req.valid('json');

      // 检查是否使用模拟数据
      if (config.mockData.enabled) {
        console.log('Mock data enabled: Returning mock export URL');

        // 生成模拟下载URL
        const mockFileName = `mock-export-${params.type}-${Date.now()}.${params.format}`;
        const mockUrl = `${c.env.BASE_URL || 'http://localhost:8787'}/mock-exports/${mockFileName}`;

        // 使用KV存储服务保存导出记录
        const kvStorage = new KVStorageService(c.env);
        await kvStorage.saveExportRecord({
          id: Date.now().toString(),
          fileName: mockFileName,
          type: params.type,
          format: params.format,
          fields: params.fields,
          filters: params.filters,
          dateRange: params.dateRange,
          url: mockUrl,
          createdAt: new Date().toISOString(),
          expiresAt: new Date(Date.now() + 24 * 60 * 60 * 1000).toISOString(),
        });

        return c.json({
          success: true,
          downloadUrl: mockUrl,
          expiresAt: new Date(Date.now() + 24 * 60 * 60 * 1000).toISOString(),
        });
      }

      // 实际导出逻辑
      // 1. 根据参数查询数据
      const prisma = new PrismaClient({
        datasources: {
          db: {
            url: c.env.DATABASE_URL,
          },
        },
      });

      // 构建查询条件
      const where: any = {};

      // 应用过滤条件
      if (params.filters) {
        if (params.filters.educationLevel && params.filters.educationLevel !== 'all') {
          where.educationLevel = params.filters.educationLevel;
        }

        if (params.filters.employmentStatus && params.filters.employmentStatus !== 'all') {
          where.employmentStatus = params.filters.employmentStatus === 'employed'
            ? '已就业'
            : params.filters.employmentStatus === 'unemployed'
              ? '待业中'
              : params.filters.employmentStatus === 'studying'
                ? '继续深造'
                : params.filters.employmentStatus;
        }

        if (params.filters.region && params.filters.region !== 'all') {
          // 处理地区筛选
          if (params.filters.region === 'north') {
            where.region = { in: ['北京', '天津', '河北', '山西', '内蒙古'] };
          } else if (params.filters.region === 'east') {
            where.region = { in: ['上海', '江苏', '浙江', '安徽', '福建', '江西', '山东'] };
          } else if (params.filters.region === 'south') {
            where.region = { in: ['广东', '广西', '海南'] };
          } else if (params.filters.region === 'central') {
            where.region = { in: ['河南', '湖北', '湖南'] };
          } else if (params.filters.region === 'southwest') {
            where.region = { in: ['重庆', '四川', '贵州', '云南', '西藏'] };
          } else if (params.filters.region === 'northwest') {
            where.region = { in: ['陕西', '甘肃', '青海', '宁夏', '新疆'] };
          } else if (params.filters.region === 'northeast') {
            where.region = { in: ['辽宁', '吉林', '黑龙江'] };
          } else {
            where.region = params.filters.region;
          }
        }
      }

      // 应用日期范围
      if (params.dateRange) {
        where.createdAt = {};

        if (params.dateRange.startDate) {
          where.createdAt.gte = new Date(params.dateRange.startDate);
        }

        if (params.dateRange.endDate) {
          where.createdAt.lte = new Date(params.dateRange.endDate);
        }
      }

      // 查询数据
      let data: any;

      if (params.type === 'data') {
        // 查询原始数据
        data = await prisma.questionnaireResponse.findMany({
          where,
          select: params.fields.reduce((acc, field) => {
            acc[field] = true;
            return acc;
          }, {} as Record<string, boolean>),
          orderBy: {
            createdAt: 'desc',
          },
        });
      } else if (params.type === 'stats') {
        // 查询统计数据
        const totalCount = await prisma.questionnaireResponse.count({ where });
        const verifiedCount = await prisma.questionnaireResponse.count({
          where: { ...where, isAnonymous: false }
        });
        const anonymousCount = await prisma.questionnaireResponse.count({
          where: { ...where, isAnonymous: true }
        });

        // 获取各学历层次数据量
        const educationLevels = await prisma.questionnaireResponse.groupBy({
          by: ['educationLevel'],
          _count: true,
          where: {
            ...where,
            educationLevel: {
              not: null,
            },
          },
        });

        // 获取各地区数据量
        const regions = await prisma.questionnaireResponse.groupBy({
          by: ['region'],
          _count: true,
          where: {
            ...where,
            region: {
              not: null,
            },
          },
        });

        // 获取就业状态分布
        const employmentStatus = await prisma.questionnaireResponse.groupBy({
          by: ['employmentStatus'],
          _count: true,
          where: {
            ...where,
            employmentStatus: {
              not: null,
            },
          },
        });

        data = {
          totalCount,
          verifiedCount,
          anonymousCount,
          educationLevels: educationLevels.map(level => ({
            level: level.educationLevel,
            count: level._count,
          })),
          regions: regions.map(region => ({
            region: region.region,
            count: region._count,
          })),
          employmentStatus: employmentStatus.map(status => ({
            status: status.employmentStatus,
            count: status._count,
          })),
        };
      } else {
        // 图表和报告类型
        data = {
          message: `${params.type} export is not fully implemented yet`,
        };
      }

      // 关闭Prisma连接
      await prisma.$disconnect();

      // 2. 生成导出文件
      const fileName = `export-${params.type}-${Date.now()}.${params.format}`;
      const fileUrl = `${c.env.BASE_URL || 'http://localhost:8787'}/exports/${fileName}`;

      // 3. 保存导出记录
      const kvStorage = new KVStorageService(c.env);
      await kvStorage.saveExportRecord({
        id: Date.now().toString(),
        fileName,
        type: params.type,
        format: params.format,
        fields: params.fields,
        filters: params.filters,
        dateRange: params.dateRange,
        url: fileUrl,
        data,
        createdAt: new Date().toISOString(),
        expiresAt: new Date(Date.now() + 24 * 60 * 60 * 1000).toISOString(),
      });

      // 4. 返回下载链接
      return c.json({
        success: true,
        downloadUrl: fileUrl,
        expiresAt: new Date(Date.now() + 24 * 60 * 60 * 1000).toISOString(),
      });
    } catch (error) {
      console.error('Error exporting data:', error);
      return c.json({
        success: false,
        error: error instanceof Error ? error.message : 'Failed to export data'
      }, 500);
    }
  }
];
