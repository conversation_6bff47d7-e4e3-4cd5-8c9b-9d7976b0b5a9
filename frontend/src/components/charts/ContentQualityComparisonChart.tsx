import React, { useState, useEffect } from 'react';
import { <PERSON><PERSON><PERSON>, <PERSON>, <PERSON>Axis, <PERSON>Axis, CartesianGrid, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> } from 'recharts';
import { Button } from '@/components/ui/button';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Loader2, RefreshCw, Star, ThumbsUp, Eye, MessageSquare } from 'lucide-react';

interface ContentQualityData {
  contentType: string;
  totalCount: number;
  approvedCount: number;
  rejectedCount: number;
  approvalRate: number;
  avgLikes: number;
  avgViews: number;
  avgComments: number;
  qualityScore: number;
  engagementRate: number;
}

interface ContentQualityComparisonChartProps {
  height?: number;
  showControls?: boolean;
  chartType?: 'bar' | 'scatter';
}

const ContentQualityComparisonChart: React.FC<ContentQualityComparisonChartProps> = ({ 
  height = 300,
  showControls = true,
  chartType = 'bar'
}) => {
  const [data, setData] = useState<ContentQualityData[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [selectedChartType, setSelectedChartType] = useState<'bar' | 'scatter'>(chartType);
  const [selectedMetric, setSelectedMetric] = useState<'approvalRate' | 'qualityScore' | 'engagement' | 'interaction'>('qualityScore');

  // 内容类型映射
  const contentTypeMap: { [key: string]: string } = {
    'story': '故事墙',
    'questionnaire_voice': '问卷心声',
    'questionnaire_response': '问卷回复',
    'comment': '评论内容'
  };

  // 获取内容质量对比数据
  const fetchContentQualityData = async () => {
    setLoading(true);
    setError(null);
    
    try {
      const response = await fetch('/api/admin/dashboard/content-quality-comparison');
      
      if (!response.ok) {
        throw new Error('获取内容质量对比数据失败');
      }
      
      const result = await response.json();
      
      if (result.success) {
        // 转换数据格式
        const formattedData = result.data.map((item: any) => ({
          contentType: contentTypeMap[item.content_type] || item.content_type,
          totalCount: item.total_count || 0,
          approvedCount: item.approved_count || 0,
          rejectedCount: item.rejected_count || 0,
          approvalRate: item.approval_rate || 0,
          avgLikes: item.avg_likes || 0,
          avgViews: item.avg_views || 0,
          avgComments: item.avg_comments || 0,
          qualityScore: item.quality_score || 0,
          engagementRate: item.engagement_rate || 0
        }));
        setData(formattedData);
      } else {
        throw new Error(result.message || '数据格式错误');
      }
    } catch (err) {
      console.error('内容质量对比数据获取错误:', err);
      setError(err instanceof Error ? err.message : '未知错误');
      
      // 使用模拟数据作为降级
      generateMockData();
    } finally {
      setLoading(false);
    }
  };

  // 生成模拟数据
  const generateMockData = () => {
    const mockData: ContentQualityData[] = [
      {
        contentType: '故事墙',
        totalCount: 165,
        approvedCount: 142,
        rejectedCount: 23,
        approvalRate: 86.1,
        avgLikes: 15.3,
        avgViews: 234.7,
        avgComments: 8.9,
        qualityScore: 82.5,
        engagementRate: 6.5
      },
      {
        contentType: '问卷心声',
        totalCount: 89,
        approvedCount: 81,
        rejectedCount: 8,
        approvalRate: 91.0,
        avgLikes: 12.7,
        avgViews: 189.3,
        avgComments: 5.4,
        qualityScore: 87.2,
        engagementRate: 6.7
      },
      {
        contentType: '问卷回复',
        totalCount: 342,
        approvedCount: 342,
        rejectedCount: 0,
        approvalRate: 100.0,
        avgLikes: 3.2,
        avgViews: 45.8,
        avgComments: 1.1,
        qualityScore: 75.8,
        engagementRate: 7.0
      },
      {
        contentType: '评论内容',
        totalCount: 456,
        approvedCount: 398,
        rejectedCount: 58,
        approvalRate: 87.3,
        avgLikes: 2.8,
        avgViews: 0,
        avgComments: 0.3,
        qualityScore: 71.4,
        engagementRate: 0.6
      }
    ];
    
    setData(mockData);
  };

  // 初始化数据
  useEffect(() => {
    fetchContentQualityData();
  }, []);

  // 获取显示数据
  const getDisplayData = () => {
    if (selectedChartType === 'scatter') {
      return data.map(item => ({
        contentType: item.contentType,
        x: item.qualityScore,
        y: item.engagementRate,
        z: item.totalCount
      }));
    }
    
    return data.map(item => ({
      ...item,
      value: selectedMetric === 'approvalRate' ? item.approvalRate :
             selectedMetric === 'qualityScore' ? item.qualityScore :
             selectedMetric === 'engagement' ? item.engagementRate :
             (item.avgLikes + item.avgViews + item.avgComments) / 3
    }));
  };

  // 自定义Tooltip
  const CustomTooltip = ({ active, payload, label }: any) => {
    if (active && payload && payload.length) {
      const data = payload[0].payload;
      return (
        <div className="bg-white p-3 border rounded-lg shadow-lg">
          <p className="font-medium mb-2">{selectedChartType === 'scatter' ? data.contentType : label}</p>
          {selectedChartType === 'scatter' ? (
            <>
              <p className="text-sm text-gray-600">质量分数: {data.x}分</p>
              <p className="text-sm text-gray-600">参与率: {data.y}%</p>
              <p className="text-sm text-gray-600">内容数量: {data.z}</p>
            </>
          ) : (
            <>
              <p className="text-sm text-gray-600">总数量: {data.totalCount}</p>
              <p className="text-sm text-gray-600">通过率: {data.approvalRate}%</p>
              <div className="flex items-center text-sm text-gray-600">
                <ThumbsUp className="h-3 w-3 mr-1" />
                平均点赞: {data.avgLikes}
              </div>
              <div className="flex items-center text-sm text-gray-600">
                <Eye className="h-3 w-3 mr-1" />
                平均浏览: {data.avgViews}
              </div>
              <div className="flex items-center text-sm text-gray-600">
                <Star className="h-3 w-3 mr-1" />
                质量分数: {data.qualityScore}分
              </div>
            </>
          )}
        </div>
      );
    }
    return null;
  };

  // 处理刷新
  const handleRefresh = () => {
    fetchContentQualityData();
  };

  // 渲染加载状态
  if (loading) {
    return (
      <div className="flex items-center justify-center" style={{ height: `${height}px` }}>
        <Loader2 className="h-8 w-8 animate-spin text-muted-foreground" />
        <span className="ml-2 text-muted-foreground">加载内容质量对比数据...</span>
      </div>
    );
  }

  // 渲染错误状态
  if (error) {
    return (
      <div className="flex flex-col items-center justify-center" style={{ height: `${height}px` }}>
        <p className="text-red-500 mb-2">加载失败: {error}</p>
        <Button variant="outline" size="sm" onClick={handleRefresh}>
          <RefreshCw className="h-4 w-4 mr-2" />
          重试
        </Button>
      </div>
    );
  }

  const displayData = getDisplayData();

  return (
    <div className="w-full">
      {showControls && (
        <div className="flex justify-between items-center mb-4">
          <div className="flex space-x-2">
            <Select value={selectedChartType} onValueChange={(value: any) => setSelectedChartType(value)}>
              <SelectTrigger className="w-24">
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="bar">柱图</SelectItem>
                <SelectItem value="scatter">散点图</SelectItem>
              </SelectContent>
            </Select>
            
            {selectedChartType === 'bar' && (
              <Select value={selectedMetric} onValueChange={(value: any) => setSelectedMetric(value)}>
                <SelectTrigger className="w-32">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="approvalRate">通过率</SelectItem>
                  <SelectItem value="qualityScore">质量分数</SelectItem>
                  <SelectItem value="engagement">参与率</SelectItem>
                  <SelectItem value="interaction">互动指标</SelectItem>
                </SelectContent>
              </Select>
            )}
          </div>
          
          <Button variant="outline" size="sm" onClick={handleRefresh}>
            <RefreshCw className="h-4 w-4 mr-2" />
            刷新
          </Button>
        </div>
      )}
      
      <div style={{ height: `${height}px` }}>
        <ResponsiveContainer width="100%" height="100%">
          {selectedChartType === 'scatter' ? (
            <ScatterChart data={displayData} margin={{ top: 20, right: 20, bottom: 20, left: 20 }}>
              <CartesianGrid />
              <XAxis 
                type="number" 
                dataKey="x" 
                name="质量分数" 
                unit="分"
                tick={{ fontSize: 12 }}
              />
              <YAxis 
                type="number" 
                dataKey="y" 
                name="参与率" 
                unit="%"
                tick={{ fontSize: 12 }}
              />
              <Tooltip content={<CustomTooltip />} cursor={{ strokeDasharray: '3 3' }} />
              <Scatter name="内容类型" dataKey="z" fill="#8884d8" />
            </ScatterChart>
          ) : (
            <BarChart data={displayData} margin={{ top: 20, right: 30, left: 20, bottom: 5 }}>
              <CartesianGrid strokeDasharray="3 3" />
              <XAxis 
                dataKey="contentType" 
                tick={{ fontSize: 12 }}
                angle={-45}
                textAnchor="end"
                height={80}
              />
              <YAxis tick={{ fontSize: 12 }} />
              <Tooltip content={<CustomTooltip />} />
              <Bar dataKey="value" fill="#8884d8" />
            </BarChart>
          )}
        </ResponsiveContainer>
      </div>
      
      {data.length === 0 && (
        <div className="text-center text-muted-foreground mt-4">
          暂无内容质量对比数据
        </div>
      )}
      
      {/* 质量摘要 */}
      {data.length > 0 && (
        <div className="mt-4 grid grid-cols-4 gap-4 text-sm">
          <div className="text-center">
            <p className="text-muted-foreground">总内容数</p>
            <p className="font-semibold text-lg">
              {data.reduce((sum, item) => sum + item.totalCount, 0).toLocaleString()}
            </p>
          </div>
          <div className="text-center">
            <p className="text-muted-foreground">平均通过率</p>
            <p className="font-semibold text-lg">
              {(data.reduce((sum, item) => sum + item.approvalRate, 0) / data.length).toFixed(1)}%
            </p>
          </div>
          <div className="text-center">
            <p className="text-muted-foreground">平均质量分</p>
            <p className="font-semibold text-lg">
              {(data.reduce((sum, item) => sum + item.qualityScore, 0) / data.length).toFixed(1)}
            </p>
          </div>
          <div className="text-center">
            <p className="text-muted-foreground">内容类型</p>
            <p className="font-semibold text-lg">{data.length}</p>
          </div>
        </div>
      )}
    </div>
  );
};

export default ContentQualityComparisonChart;
