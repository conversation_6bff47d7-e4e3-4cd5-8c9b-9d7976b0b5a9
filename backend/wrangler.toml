name = "college-employment-survey"
main = "./index.js"
compatibility_date = "2024-05-15"
compatibility_flags = ["nodejs_compat"]

# KV Namespace for storing data
kv_namespaces = [
  { binding = "SURVEY_KV", id = "d9d122b8ad0e4a4aa6685f843394cb57" }
]

# D1 Database
# 使用命令: wrangler d1 create college-employment-survey
# 然后将生成的 ID 填入下方
[[d1_databases]]
binding = "DB"
database_name = "college-employment-survey-realapi"
database_id = "b5487ab1-87ca-4b84-8351-671c2b5974f9"
migrations_dir = "migrations"

# R2 Bucket
[[r2_buckets]]
binding = "R2_BUCKET"
bucket_name = "college-employment-survey-data"

# Environment variables
[vars]
ENVIRONMENT = "production"

# Development environment variables
[env.dev]
name = "college-employment-survey-api-dev"
ENVIRONMENT = "development"
JWT_SECRET = "college-employment-survey-jwt-secret-key-2024"
kv_namespaces = [
  { binding = "SURVEY_KV", id = "d9d122b8ad0e4a4aa6685f843394cb57" }
]

# Staging environment variables
[env.staging]
name = "college-employment-survey-api-staging"
ENVIRONMENT = "staging"
JWT_SECRET = "college-employment-survey-jwt-secret-key-2024-staging"
compatibility_flags = ["nodejs_compat"]

# KV Namespace for staging
kv_namespaces = [
  { binding = "SURVEY_KV", id = "d9d122b8ad0e4a4aa6685f843394cb57" }
]

# D1 Database for staging
[[env.staging.d1_databases]]
binding = "DB"
database_name = "college-employment-survey-staging"
database_id = "1271d759-b44a-4209-8008-f7e00353b5a3"
migrations_dir = "prisma/migrations"

# R2 Bucket for staging - 暂时注释掉，因为存储桶不存在
# [[env.staging.r2_buckets]]
# binding = "R2_BUCKET"
# bucket_name = "college-employment-survey-data"

# Secrets (to be set using wrangler secret put)
# - RESEND_API_KEY
# - DATABASE_URL
# - ENCRYPTION_KEY
# - ENCRYPTION_IV
# - JWT_SECRET
# - OPENAI_API_KEY
# - GROK_API_KEY
# - AES_SECRET_KEY
# - AES_IV
# - UUID_SALT

# Cron Triggers for scheduled tasks
[triggers]
crons = ["0 0 * * *"] # Run daily at midnight
