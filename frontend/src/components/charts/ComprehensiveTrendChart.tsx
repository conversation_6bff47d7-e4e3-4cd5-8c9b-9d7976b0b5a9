import React, { useState, useEffect } from 'react';
import { Line<PERSON>hart, Line, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer, Legend, AreaChart, Area } from 'recharts';
import { Button } from '@/components/ui/button';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Loader2, RefreshCw, TrendingUp, TrendingDown } from 'lucide-react';

interface TrendData {
  date: string;
  users: number;
  stories: number;
  voices: number;
  responses: number;
  userGrowthRate: number;
  contentGrowthRate: number;
  engagementRate: number;
}

interface ComprehensiveTrendChartProps {
  height?: number;
  timeRange?: 'week' | 'month' | 'quarter' | 'year';
  showControls?: boolean;
}

const ComprehensiveTrendChart: React.FC<ComprehensiveTrendChartProps> = ({ 
  height = 300, 
  timeRange = 'month',
  showControls = true 
}) => {
  const [data, setData] = useState<TrendData[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [selectedTimeRange, setSelectedTimeRange] = useState(timeRange);
  const [chartType, setChartType] = useState<'line' | 'area'>('line');
  const [selectedMetrics, setSelectedMetrics] = useState<string[]>(['users', 'stories', 'voices']);

  // 指标配置
  const metricsConfig = {
    users: { name: '用户数', color: '#8884d8', key: 'users' },
    stories: { name: '故事数', color: '#82ca9d', key: 'stories' },
    voices: { name: '心声数', color: '#ffc658', key: 'voices' },
    responses: { name: '问卷数', color: '#ff7c7c', key: 'responses' },
    userGrowthRate: { name: '用户增长率', color: '#8dd1e1', key: 'userGrowthRate' },
    contentGrowthRate: { name: '内容增长率', color: '#d084d0', key: 'contentGrowthRate' },
    engagementRate: { name: '参与度', color: '#ffb347', key: 'engagementRate' }
  };

  // 获取综合趋势数据
  const fetchTrendData = async () => {
    setLoading(true);
    setError(null);
    
    try {
      const response = await fetch(`/api/admin/dashboard/comprehensive-trend?range=${selectedTimeRange}`);
      
      if (!response.ok) {
        throw new Error('获取综合趋势数据失败');
      }
      
      const result = await response.json();
      
      if (result.success) {
        setData(result.data);
      } else {
        throw new Error(result.message || '数据格式错误');
      }
    } catch (err) {
      console.error('综合趋势数据获取错误:', err);
      setError(err instanceof Error ? err.message : '未知错误');
      
      // 使用模拟数据作为降级
      generateMockData();
    } finally {
      setLoading(false);
    }
  };

  // 生成模拟数据
  const generateMockData = () => {
    const now = new Date();
    const days = selectedTimeRange === 'week' ? 7 : 
                 selectedTimeRange === 'month' ? 30 : 
                 selectedTimeRange === 'quarter' ? 90 : 365;
    
    const mockData: TrendData[] = [];
    let baseUsers = 1000;
    let baseStories = 100;
    let baseVoices = 80;
    let baseResponses = 200;
    
    for (let i = days - 1; i >= 0; i--) {
      const date = new Date(now);
      date.setDate(date.getDate() - i);
      
      // 模拟增长趋势
      const userGrowth = Math.random() * 20 + 5;
      const storyGrowth = Math.random() * 8 + 2;
      const voiceGrowth = Math.random() * 6 + 1;
      const responseGrowth = Math.random() * 15 + 5;
      
      baseUsers += userGrowth;
      baseStories += storyGrowth;
      baseVoices += voiceGrowth;
      baseResponses += responseGrowth;
      
      // 计算增长率和参与度
      const userGrowthRate = (userGrowth / baseUsers) * 100;
      const contentGrowthRate = ((storyGrowth + voiceGrowth) / (baseStories + baseVoices)) * 100;
      const engagementRate = ((baseStories + baseVoices + baseResponses) / baseUsers) * 100;
      
      mockData.push({
        date: date.toISOString().split('T')[0],
        users: Math.round(baseUsers),
        stories: Math.round(baseStories),
        voices: Math.round(baseVoices),
        responses: Math.round(baseResponses),
        userGrowthRate: Math.round(userGrowthRate * 100) / 100,
        contentGrowthRate: Math.round(contentGrowthRate * 100) / 100,
        engagementRate: Math.round(engagementRate * 100) / 100
      });
    }
    
    setData(mockData);
  };

  // 初始化和刷新数据
  useEffect(() => {
    fetchTrendData();
  }, [selectedTimeRange]);

  // 自定义Tooltip
  const CustomTooltip = ({ active, payload, label }: any) => {
    if (active && payload && payload.length) {
      return (
        <div className="bg-white p-3 border rounded-lg shadow-lg">
          <p className="font-medium mb-2">{`日期: ${label}`}</p>
          {payload.map((entry: any, index: number) => (
            <p key={index} style={{ color: entry.color }} className="text-sm">
              {`${entry.name}: ${entry.value.toLocaleString()}${entry.dataKey.includes('Rate') ? '%' : ''}`}
            </p>
          ))}
        </div>
      );
    }
    return null;
  };

  // 处理指标选择
  const handleMetricToggle = (metric: string) => {
    setSelectedMetrics(prev => 
      prev.includes(metric) 
        ? prev.filter(m => m !== metric)
        : [...prev, metric]
    );
  };

  // 处理刷新
  const handleRefresh = () => {
    fetchTrendData();
  };

  // 渲染加载状态
  if (loading) {
    return (
      <div className="flex items-center justify-center" style={{ height: `${height}px` }}>
        <Loader2 className="h-8 w-8 animate-spin text-muted-foreground" />
        <span className="ml-2 text-muted-foreground">加载综合趋势数据...</span>
      </div>
    );
  }

  // 渲染错误状态
  if (error) {
    return (
      <div className="flex flex-col items-center justify-center" style={{ height: `${height}px` }}>
        <p className="text-red-500 mb-2">加载失败: {error}</p>
        <Button variant="outline" size="sm" onClick={handleRefresh}>
          <RefreshCw className="h-4 w-4 mr-2" />
          重试
        </Button>
      </div>
    );
  }

  // 计算趋势指标
  const latestData = data[data.length - 1];
  const previousData = data[data.length - 2];
  const userTrend = latestData && previousData ? 
    ((latestData.users - previousData.users) / previousData.users) * 100 : 0;
  const contentTrend = latestData && previousData ? 
    (((latestData.stories + latestData.voices) - (previousData.stories + previousData.voices)) / 
     (previousData.stories + previousData.voices)) * 100 : 0;

  return (
    <div className="w-full">
      {showControls && (
        <div className="space-y-4 mb-4">
          <div className="flex justify-between items-center">
            <div className="flex space-x-2">
              <Select value={selectedTimeRange} onValueChange={(value: any) => setSelectedTimeRange(value)}>
                <SelectTrigger className="w-32">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="week">最近一周</SelectItem>
                  <SelectItem value="month">最近一月</SelectItem>
                  <SelectItem value="quarter">最近三月</SelectItem>
                  <SelectItem value="year">最近一年</SelectItem>
                </SelectContent>
              </Select>
              
              <Select value={chartType} onValueChange={(value: any) => setChartType(value)}>
                <SelectTrigger className="w-24">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="line">线图</SelectItem>
                  <SelectItem value="area">面积图</SelectItem>
                </SelectContent>
              </Select>
            </div>
            
            <Button variant="outline" size="sm" onClick={handleRefresh}>
              <RefreshCw className="h-4 w-4 mr-2" />
              刷新
            </Button>
          </div>
          
          {/* 指标选择器 */}
          <div className="flex flex-wrap gap-2">
            {Object.entries(metricsConfig).map(([key, config]) => (
              <Button
                key={key}
                variant={selectedMetrics.includes(key) ? "default" : "outline"}
                size="sm"
                onClick={() => handleMetricToggle(key)}
                className="text-xs"
              >
                <div 
                  className="w-3 h-3 rounded-full mr-2" 
                  style={{ backgroundColor: config.color }}
                />
                {config.name}
              </Button>
            ))}
          </div>
          
          {/* 趋势指标 */}
          <div className="grid grid-cols-2 gap-4 text-sm">
            <div className="flex items-center">
              {userTrend >= 0 ? (
                <TrendingUp className="h-4 w-4 text-green-500 mr-1" />
              ) : (
                <TrendingDown className="h-4 w-4 text-red-500 mr-1" />
              )}
              <span>用户趋势: {userTrend.toFixed(1)}%</span>
            </div>
            <div className="flex items-center">
              {contentTrend >= 0 ? (
                <TrendingUp className="h-4 w-4 text-green-500 mr-1" />
              ) : (
                <TrendingDown className="h-4 w-4 text-red-500 mr-1" />
              )}
              <span>内容趋势: {contentTrend.toFixed(1)}%</span>
            </div>
          </div>
        </div>
      )}
      
      <div style={{ height: `${height}px` }}>
        <ResponsiveContainer width="100%" height="100%">
          {chartType === 'area' ? (
            <AreaChart data={data} margin={{ top: 5, right: 30, left: 20, bottom: 5 }}>
              <CartesianGrid strokeDasharray="3 3" />
              <XAxis 
                dataKey="date" 
                tick={{ fontSize: 12 }}
                tickFormatter={(value) => {
                  const date = new Date(value);
                  return `${date.getMonth() + 1}/${date.getDate()}`;
                }}
              />
              <YAxis tick={{ fontSize: 12 }} />
              <Tooltip content={<CustomTooltip />} />
              <Legend />
              
              {selectedMetrics.map(metric => (
                <Area
                  key={metric}
                  type="monotone"
                  dataKey={metric}
                  stackId="1"
                  stroke={metricsConfig[metric as keyof typeof metricsConfig].color}
                  fill={metricsConfig[metric as keyof typeof metricsConfig].color}
                  fillOpacity={0.6}
                  name={metricsConfig[metric as keyof typeof metricsConfig].name}
                />
              ))}
            </AreaChart>
          ) : (
            <LineChart data={data} margin={{ top: 5, right: 30, left: 20, bottom: 5 }}>
              <CartesianGrid strokeDasharray="3 3" />
              <XAxis 
                dataKey="date" 
                tick={{ fontSize: 12 }}
                tickFormatter={(value) => {
                  const date = new Date(value);
                  return `${date.getMonth() + 1}/${date.getDate()}`;
                }}
              />
              <YAxis tick={{ fontSize: 12 }} />
              <Tooltip content={<CustomTooltip />} />
              <Legend />
              
              {selectedMetrics.map(metric => (
                <Line
                  key={metric}
                  type="monotone"
                  dataKey={metric}
                  stroke={metricsConfig[metric as keyof typeof metricsConfig].color}
                  strokeWidth={2}
                  name={metricsConfig[metric as keyof typeof metricsConfig].name}
                  dot={{ r: 3 }}
                />
              ))}
            </LineChart>
          )}
        </ResponsiveContainer>
      </div>
      
      {data.length === 0 && (
        <div className="text-center text-muted-foreground mt-4">
          暂无综合趋势数据
        </div>
      )}
    </div>
  );
};

export default ComprehensiveTrendChart;
