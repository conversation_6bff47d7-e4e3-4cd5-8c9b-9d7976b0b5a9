import { Env } from '../types';

/**
 * 获取标签统计数据
 * @param request 请求对象
 * @param env 环境对象
 * @returns 响应对象
 */
export async function handleGetTagStats(request: Request, env: Env): Promise<Response> {
  try {
    console.log('Getting tag statistics...');

    // 获取查询参数
    const url = new URL(request.url);
    const category = url.searchParams.get('category');
    const limit = parseInt(url.searchParams.get('limit') || '20');
    const sortBy = url.searchParams.get('sortBy') || 'count'; // count, name, priority

    // 构建查询
    let query = `
      SELECT 
        t.id,
        t.name,
        t.color,
        t.priority,
        t.category,
        t.count,
        t.created_at,
        t.updated_at
      FROM tags t
    `;

    const params = [];

    // 添加分类过滤
    if (category && category !== 'all') {
      query += ` WHERE t.category = ?`;
      params.push(category);
    }

    // 添加排序
    switch (sortBy) {
      case 'count':
        query += ` ORDER BY t.count DESC, t.priority DESC`;
        break;
      case 'name':
        query += ` ORDER BY t.name ASC`;
        break;
      case 'priority':
        query += ` ORDER BY t.priority DESC, t.count DESC`;
        break;
      default:
        query += ` ORDER BY t.count DESC`;
    }

    // 添加限制
    query += ` LIMIT ?`;
    params.push(limit);

    console.log('Executing query:', query);
    console.log('With params:', params);

    // 执行查询
    const result = await env.DB.prepare(query).bind(...params).all();

    if (!result.success) {
      throw new Error('Database query failed');
    }

    // 格式化标签数据
    const tags = result.results.map((row: any) => ({
      id: row.id,
      name: row.name,
      color: row.color,
      priority: row.priority,
      category: row.category,
      count: row.count,
      createdAt: row.created_at,
      updatedAt: row.updated_at,
    }));

    // 获取分类统计
    const categoryStatsQuery = `
      SELECT 
        category,
        COUNT(*) as tag_count,
        SUM(count) as total_usage
      FROM tags
      GROUP BY category
      ORDER BY total_usage DESC
    `;

    const categoryStatsResult = await env.DB.prepare(categoryStatsQuery).all();
    
    const categoryStats = categoryStatsResult.success ? categoryStatsResult.results.map((row: any) => ({
      category: row.category,
      tagCount: row.tag_count,
      totalUsage: row.total_usage,
    })) : [];

    // 获取总体统计
    const totalStatsQuery = `
      SELECT 
        COUNT(*) as total_tags,
        SUM(count) as total_usages,
        MAX(count) as max_usage,
        AVG(count) as avg_usage
      FROM tags
    `;

    const totalStatsResult = await env.DB.prepare(totalStatsQuery).first();
    
    const totalStats = totalStatsResult ? {
      totalTags: totalStatsResult.total_tags,
      totalUsages: totalStatsResult.total_usages,
      maxUsage: totalStatsResult.max_usage,
      avgUsage: Math.round(totalStatsResult.avg_usage * 100) / 100,
    } : {
      totalTags: 0,
      totalUsages: 0,
      maxUsage: 0,
      avgUsage: 0,
    };

    console.log(`Found ${tags.length} tags`);

    return new Response(JSON.stringify({
      success: true,
      tags,
      categoryStats,
      totalStats,
      meta: {
        category: category || 'all',
        limit,
        sortBy,
        count: tags.length,
      },
    }), {
      status: 200,
      headers: {
        'Content-Type': 'application/json',
        'Access-Control-Allow-Origin': '*',
        'Access-Control-Allow-Methods': 'GET, OPTIONS',
        'Access-Control-Allow-Headers': 'Content-Type',
      },
    });

  } catch (error) {
    console.error('Error getting tag stats:', error);

    return new Response(JSON.stringify({
      success: false,
      error: 'Failed to get tag statistics',
      details: error instanceof Error ? error.message : 'Unknown error',
    }), {
      status: 500,
      headers: {
        'Content-Type': 'application/json',
        'Access-Control-Allow-Origin': '*',
      },
    });
  }
}

/**
 * 获取热门标签
 * @param request 请求对象
 * @param env 环境对象
 * @returns 响应对象
 */
export async function handleGetHotTags(request: Request, env: Env): Promise<Response> {
  try {
    console.log('Getting hot tags...');

    // 获取查询参数
    const url = new URL(request.url);
    const limit = parseInt(url.searchParams.get('limit') || '10');
    const minUsage = parseInt(url.searchParams.get('minUsage') || '1');

    // 查询热门标签
    const query = `
      SELECT 
        t.id,
        t.name,
        t.color,
        t.category,
        t.count
      FROM tags t
      WHERE t.count >= ?
      ORDER BY t.count DESC, t.priority DESC
      LIMIT ?
    `;

    const result = await env.DB.prepare(query).bind(minUsage, limit).all();

    if (!result.success) {
      throw new Error('Database query failed');
    }

    const hotTags = result.results.map((row: any) => ({
      id: row.id,
      name: row.name,
      color: row.color,
      category: row.category,
      count: row.count,
    }));

    console.log(`Found ${hotTags.length} hot tags`);

    return new Response(JSON.stringify({
      success: true,
      tags: hotTags,
      meta: {
        limit,
        minUsage,
        count: hotTags.length,
      },
    }), {
      status: 200,
      headers: {
        'Content-Type': 'application/json',
        'Access-Control-Allow-Origin': '*',
        'Access-Control-Allow-Methods': 'GET, OPTIONS',
        'Access-Control-Allow-Headers': 'Content-Type',
      },
    });

  } catch (error) {
    console.error('Error getting hot tags:', error);

    return new Response(JSON.stringify({
      success: false,
      error: 'Failed to get hot tags',
      details: error instanceof Error ? error.message : 'Unknown error',
    }), {
      status: 500,
      headers: {
        'Content-Type': 'application/json',
        'Access-Control-Allow-Origin': '*',
      },
    });
  }
}

/**
 * 更新标签统计
 * @param request 请求对象
 * @param env 环境对象
 * @returns 响应对象
 */
export async function handleUpdateTagStats(request: Request, env: Env): Promise<Response> {
  try {
    console.log('Updating tag statistics...');

    // 重新计算所有标签的使用统计
    const updateQuery = `
      UPDATE tags SET count = (
        SELECT COUNT(*)
        FROM story_tags st
        WHERE st.tag_id = tags.id
      )
    `;

    const result = await env.DB.prepare(updateQuery).run();

    if (!result.success) {
      throw new Error('Failed to update tag statistics');
    }

    // 获取更新后的统计
    const statsQuery = `
      SELECT 
        COUNT(*) as total_tags,
        SUM(count) as total_usages,
        MAX(count) as max_usage
      FROM tags
    `;

    const statsResult = await env.DB.prepare(statsQuery).first();

    console.log('Tag statistics updated successfully');

    return new Response(JSON.stringify({
      success: true,
      message: 'Tag statistics updated successfully',
      stats: statsResult ? {
        totalTags: statsResult.total_tags,
        totalUsages: statsResult.total_usages,
        maxUsage: statsResult.max_usage,
      } : null,
    }), {
      status: 200,
      headers: {
        'Content-Type': 'application/json',
        'Access-Control-Allow-Origin': '*',
        'Access-Control-Allow-Methods': 'POST, OPTIONS',
        'Access-Control-Allow-Headers': 'Content-Type',
      },
    });

  } catch (error) {
    console.error('Error updating tag stats:', error);

    return new Response(JSON.stringify({
      success: false,
      error: 'Failed to update tag statistics',
      details: error instanceof Error ? error.message : 'Unknown error',
    }), {
      status: 500,
      headers: {
        'Content-Type': 'application/json',
        'Access-Control-Allow-Origin': '*',
      },
    });
  }
}
