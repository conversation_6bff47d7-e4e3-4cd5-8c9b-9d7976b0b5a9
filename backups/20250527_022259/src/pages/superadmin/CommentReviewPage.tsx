import React from 'react';
import SuperAdminLayout from '@/components/layouts/SuperAdminLayout';
import CommentReviewPanel from '@/components/admin/CommentReviewPanel';

/**
 * 超级管理员评论审核页面
 * 
 * 专门用于审核用户提交的评论内容
 * 包括故事评论、问卷评论等各类用户互动内容
 */
const SuperAdminCommentReviewPage: React.FC = () => {
  return (
    <SuperAdminLayout>
      <div className="container mx-auto py-6">
        <div className="flex justify-between items-center mb-6">
          <div>
            <h1 className="text-2xl font-bold">评论审核</h1>
            <p className="text-gray-500 mt-1">审核用户提交的评论内容，确保社区环境健康</p>
          </div>
        </div>
        
        <CommentReviewPanel />
      </div>
    </SuperAdminLayout>
  );
};

export default SuperAdminCommentReviewPage;
