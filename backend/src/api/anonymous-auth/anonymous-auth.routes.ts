/**
 * 轻量级匿名身份验证路由
 */

import { Hono } from 'hono';
import { Env } from '../../types';
import { getMyContent, deleteStory, deleteQuestionnaireResponse } from './anonymous-auth.controller';

// 创建路由
const anonymousAuthRouter = new Hono<{ Bindings: Env }>();

// 获取我的内容
anonymousAuthRouter.get('/my-content', ...getMyContent);

// 删除故事
anonymousAuthRouter.delete('/story/:id', ...deleteStory);

// 删除问卷回复
anonymousAuthRouter.delete('/questionnaire/:id', ...deleteQuestionnaireResponse);

export { app as anonymousAuthRoutes };
