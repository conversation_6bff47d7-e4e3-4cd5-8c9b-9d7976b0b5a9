/**
 * 🚀 优化版问卷路由
 * 集成性能优化的问卷API路由
 */

import { Hono } from 'hono';
import { Env } from '../../types';
import * as optimizedController from './questionnaire.optimized.controller';
import * as originalController from './questionnaire.controller';

// 创建优化版路由
const optimizedQuestionnaireRouter = new Hono<{ Bindings: Env }>();

// 性能优化的统计端点
optimizedQuestionnaireRouter.get('/stats/optimized', optimizedController.getQuestionnaireStatsOptimized);
optimizedQuestionnaireRouter.get('/realtime-stats/optimized', optimizedController.getQuestionnaireRealtimeStatsOptimized);

// 缓存管理端点
optimizedQuestionnaireRouter.post('/cache/clear', async (c) => {
  try {
    optimizedController.clearStatsCache();
    return c.json({
      success: true,
      message: '缓存已清理',
      timestamp: new Date().toISOString()
    });
  } catch (error) {
    return c.json({
      success: false,
      error: '清理缓存失败',
      details: error.message
    }, 500);
  }
});

optimizedQuestionnaireRouter.get('/cache/status', async (c) => {
  try {
    const status = optimizedController.getCacheStatus();
    return c.json({
      success: true,
      data: status,
      timestamp: new Date().toISOString()
    });
  } catch (error) {
    return c.json({
      success: false,
      error: '获取缓存状态失败',
      details: error.message
    }, 500);
  }
});

// A/B测试端点 - 比较优化前后的性能
optimizedQuestionnaireRouter.get('/stats/compare', async (c) => {
  const startTime = Date.now();
  
  try {
    console.log('🔄 开始A/B性能测试...');

    // 并行执行优化版和原版
    const [optimizedResult, originalResult] = await Promise.allSettled([
      optimizedController.getQuestionnaireStatsOptimized(c),
      originalController.getQuestionnaireStats(c)
    ]);

    const totalTime = Date.now() - startTime;

    // 提取响应时间
    const optimizedTime = optimizedResult.status === 'fulfilled' ? 
      JSON.parse(await optimizedResult.value.text()).meta?.responseTime : null;
    
    const originalTime = originalResult.status === 'fulfilled' ? 
      JSON.parse(await originalResult.value.text()).meta?.responseTime : null;

    const comparison = {
      success: true,
      comparison: {
        optimized: {
          status: optimizedResult.status,
          responseTime: optimizedTime,
          cached: optimizedTime ? JSON.parse(await optimizedResult.value.text()).meta?.cached : false
        },
        original: {
          status: originalResult.status,
          responseTime: originalTime
        },
        improvement: optimizedTime && originalTime ? {
          absolute: originalTime - optimizedTime,
          percentage: Math.round(((originalTime - optimizedTime) / originalTime) * 100)
        } : null,
        totalTestTime: totalTime
      },
      timestamp: new Date().toISOString()
    };

    console.log(`✅ A/B测试完成，总耗时: ${totalTime}ms`);
    if (comparison.comparison.improvement) {
      console.log(`⚡ 性能提升: ${comparison.comparison.improvement.percentage}% (${comparison.comparison.improvement.absolute}ms)`);
    }

    return c.json(comparison);

  } catch (error) {
    console.error('❌ A/B测试失败:', error);
    return c.json({
      success: false,
      error: 'A/B测试失败',
      details: error.message,
      totalTestTime: Date.now() - startTime
    }, 500);
  }
});

// 性能基准测试端点
optimizedQuestionnaireRouter.get('/benchmark', async (c) => {
  const iterations = parseInt(c.req.query('iterations') || '10');
  const endpoint = c.req.query('endpoint') || 'optimized';
  
  const startTime = Date.now();
  
  try {
    console.log(`🏃 开始性能基准测试 (${iterations}次迭代, 端点: ${endpoint})`);

    const results = [];
    const controller = endpoint === 'optimized' ? 
      optimizedController.getQuestionnaireStatsOptimized :
      originalController.getQuestionnaireStats;

    // 预热
    await controller(c);
    console.log('🔥 预热完成');

    // 基准测试
    for (let i = 0; i < iterations; i++) {
      const iterationStart = Date.now();
      
      try {
        const result = await controller(c);
        const iterationTime = Date.now() - iterationStart;
        
        results.push({
          iteration: i + 1,
          responseTime: iterationTime,
          success: true
        });
        
        console.log(`  测试 ${i + 1}/${iterations}: ${iterationTime}ms`);
      } catch (error) {
        const iterationTime = Date.now() - iterationStart;
        
        results.push({
          iteration: i + 1,
          responseTime: iterationTime,
          success: false,
          error: error.message
        });
        
        console.log(`  测试 ${i + 1}/${iterations}: 失败 (${iterationTime}ms)`);
      }
    }

    // 计算统计数据
    const successfulResults = results.filter(r => r.success);
    const times = successfulResults.map(r => r.responseTime);
    
    const stats = times.length > 0 ? {
      min: Math.min(...times),
      max: Math.max(...times),
      avg: Math.round((times.reduce((a, b) => a + b, 0) / times.length) * 100) / 100,
      median: times.sort((a, b) => a - b)[Math.floor(times.length / 2)],
      p95: times.sort((a, b) => a - b)[Math.floor(times.length * 0.95)],
      successRate: Math.round((successfulResults.length / results.length) * 100)
    } : null;

    const totalTime = Date.now() - startTime;

    const benchmark = {
      success: true,
      benchmark: {
        endpoint,
        iterations,
        totalTime,
        results,
        statistics: stats
      },
      timestamp: new Date().toISOString()
    };

    console.log(`✅ 基准测试完成，总耗时: ${totalTime}ms`);
    if (stats) {
      console.log(`📊 平均响应时间: ${stats.avg}ms (成功率: ${stats.successRate}%)`);
    }

    return c.json(benchmark);

  } catch (error) {
    console.error('❌ 基准测试失败:', error);
    return c.json({
      success: false,
      error: '基准测试失败',
      details: error.message,
      totalTime: Date.now() - startTime
    }, 500);
  }
});

// 健康检查端点
optimizedQuestionnaireRouter.get('/health', async (c) => {
  const startTime = Date.now();
  
  try {
    // 快速健康检查
    const healthCheck = {
      status: 'healthy',
      version: 'optimized-v1.0',
      responseTime: Date.now() - startTime,
      cache: optimizedController.getCacheStatus(),
      timestamp: new Date().toISOString()
    };

    return c.json({
      success: true,
      health: healthCheck
    });

  } catch (error) {
    return c.json({
      success: false,
      status: 'unhealthy',
      error: error.message,
      responseTime: Date.now() - startTime,
      timestamp: new Date().toISOString()
    }, 500);
  }
});

export default optimizedQuestionnaireRouter;
