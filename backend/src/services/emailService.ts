import CryptoJS from 'crypto-js';

// Mock Resend for development environment
class MockResend {
  constructor(apiKey: string) {
    console.log('Using MockResend for development');
  }

  emails = {
    send: async (options: any) => {
      console.log('Mock email sent:', options);
      return { data: { id: 'mock-email-id' }, error: null };
    }
  };
}

// Try to import Resend, but fall back to mock if it fails
let Resend: any;
try {
  Resend = require('resend').Resend;
} catch (error) {
  console.log('Resend import failed, using mock implementation');
  Resend = MockResend;
}

interface EmailServiceConfig {
  resendApiKey: string;
  encryptionKey: string;
  encryptionIv: string;
  fromEmail: string;
  verificationExpiryMinutes: number;
}

export class EmailService {
  private resend: any;
  private config: EmailServiceConfig;

  constructor(config: EmailServiceConfig) {
    this.resend = new Resend(config.resendApiKey);
    this.config = config;
  }

  /**
   * Generate a random 6-digit verification code
   */
  generateVerificationCode(): string {
    return Math.floor(100000 + Math.random() * 900000).toString();
  }

  /**
   * Send verification email with code
   */
  async sendVerificationEmail(email: string, code: string): Promise<boolean> {
    try {
      const { data, error } = await this.resend.emails.send({
        from: this.config.fromEmail,
        to: email,
        subject: '大学生就业问卷调查 - 邮箱验证码',
        html: `
          <div style="font-family: sans-serif; max-width: 600px; margin: 0 auto;">
            <h2>邮箱验证码</h2>
            <p>您好，感谢参与大学生就业问卷调查。</p>
            <p>您的验证码是：</p>
            <div style="background-color: #f4f4f4; padding: 10px; font-size: 24px; font-weight: bold; text-align: center; letter-spacing: 5px;">
              ${code}
            </div>
            <p>验证码有效期为 ${this.config.verificationExpiryMinutes} 分钟，请尽快完成验证。</p>
            <p>如果您没有请求此验证码，请忽略此邮件。</p>
            <p>谢谢！</p>
          </div>
        `,
      });

      if (error) {
        console.error('Error sending email:', error);
        return false;
      }

      return true;
    } catch (error) {
      console.error('Error sending email:', error);
      return false;
    }
  }

  /**
   * Encrypt email using AES-256-GCM
   */
  encryptEmail(email: string): { encrypted: string; authTag: string } {
    // Convert string key and iv to WordArray
    const key = CryptoJS.enc.Utf8.parse(this.config.encryptionKey);
    const iv = CryptoJS.enc.Utf8.parse(this.config.encryptionIv);

    // Encrypt
    const encrypted = CryptoJS.AES.encrypt(email, key, {
      iv: iv,
      mode: CryptoJS.mode.GCM,
      padding: CryptoJS.pad.Pkcs7,
    });

    return {
      encrypted: encrypted.ciphertext.toString(CryptoJS.enc.Hex),
      authTag: encrypted.tag.toString(CryptoJS.enc.Hex),
    };
  }

  /**
   * Decrypt email using AES-256-GCM
   */
  decryptEmail(encrypted: string, authTag: string): string {
    // Convert string key and iv to WordArray
    const key = CryptoJS.enc.Utf8.parse(this.config.encryptionKey);
    const iv = CryptoJS.enc.Utf8.parse(this.config.encryptionIv);

    // Create CipherParams object
    const cipherParams = CryptoJS.lib.CipherParams.create({
      ciphertext: CryptoJS.enc.Hex.parse(encrypted),
      salt: null,
      iv: iv,
      algorithm: CryptoJS.algo.AES,
      mode: CryptoJS.mode.GCM,
      padding: CryptoJS.pad.Pkcs7,
      blockSize: 4,
      formatter: CryptoJS.format.OpenSSL,
      tag: CryptoJS.enc.Hex.parse(authTag),
    });

    // Decrypt
    const decrypted = CryptoJS.AES.decrypt(cipherParams, key, {
      iv: iv,
      mode: CryptoJS.mode.GCM,
      padding: CryptoJS.pad.Pkcs7,
    });

    return decrypted.toString(CryptoJS.enc.Utf8);
  }

  /**
   * Check if verification code is expired
   */
  isVerificationExpired(expiresAt: Date): boolean {
    return new Date() > expiresAt;
  }

  /**
   * Calculate verification expiry time
   */
  getVerificationExpiryTime(): Date {
    const expiryTime = new Date();
    expiryTime.setMinutes(expiryTime.getMinutes() + this.config.verificationExpiryMinutes);
    return expiryTime;
  }
}
