import { Hono } from 'hono';
import { Env } from '../../types';
import { adminAuthMiddleware } from '../../middlewares/auth.middleware';
import { reviewerAuthMiddleware } from '../../middlewares/reviewerAuth.middleware';
import {
  getTags,
  createTag,
  updateTag,
  deleteTag,
  getTagById,
  getTagStats,
  batchOperateTags,
  getTagCategories,
  createTagCategory,
  updateTagCategory,
  deleteTagCategory,
  getTagUsageAnalytics,
  getTagTrends,
  getTagCorrelations
} from './tag-management.controller';

// 创建路由
const tagRouter = new Hono<{ Bindings: Env }>();

// ==================== 标签查询 ====================

// 获取标签列表（审核员和管理员都可以查看）
tagRouter.get('/', reviewerAuthMiddleware, getTags);

// 获取单个标签详情（审核员和管理员都可以查看）
tagRouter.get('/:id', reviewerAuthMiddleware, getTagById);

// 获取标签使用统计（审核员和管理员都可以查看）
tagRouter.get('/:id/stats', reviewerAuthMiddleware, getTagStats);

// ==================== 标签管理 ====================

// 创建标签（需要管理员权限）
tagRouter.post('/', adminAuthMiddleware, createTag);

// 更新标签（需要管理员权限）
tagRouter.put('/:id', adminAuthMiddleware, updateTag);

// 删除标签（需要管理员权限）
tagRouter.delete('/:id', adminAuthMiddleware, deleteTag);

// 批量操作标签（需要管理员权限）
tagRouter.post('/batch', adminAuthMiddleware, batchOperateTags);

// ==================== 标签分类管理 ====================

// 获取标签分类列表
tagRouter.get('/categories/list', reviewerAuthMiddleware, getTagCategories);

// 创建标签分类（需要管理员权限）
tagRouter.post('/categories', adminAuthMiddleware, createTagCategory);

// 更新标签分类（需要管理员权限）
tagRouter.put('/categories/:category', adminAuthMiddleware, updateTagCategory);

// 删除标签分类（需要管理员权限）
tagRouter.delete('/categories/:category', adminAuthMiddleware, deleteTagCategory);

// ==================== 标签统计和分析 ====================

// 获取标签使用统计
tagRouter.get('/analytics/usage', adminAuthMiddleware, getTagUsageAnalytics);

// 获取标签趋势分析
tagRouter.get('/analytics/trends', adminAuthMiddleware, getTagTrends);

// 获取标签关联分析
tagRouter.get('/analytics/correlations', adminAuthMiddleware, getTagCorrelations);

export default tagRouter;
