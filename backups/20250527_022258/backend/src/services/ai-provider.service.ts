/**
 * Cloudflare AI Provider Manager
 * 支持故障转移和冗余机制的AI脱敏服务
 */

import { Env } from '../types';

interface AIProvider {
  id: string;
  name: string;
  apiKey: string;
  endpoint: string;
  model: string;
  priority: number;
  status: 'active' | 'unavailable';
  failureCount: number;
  lastFailure: number | null;
  avgResponseTime: number;
}

interface FilterOptions {
  personalInfo: boolean;
  contactInfo: boolean;
  inappropriateContent: boolean;
  sensitiveData: boolean;
}

interface DeidentificationResult {
  success: boolean;
  sanitized: string;
  changes: any[];
  provider: string;
  responseTime?: number;
  failoverUsed?: boolean;
  attemptedProviders?: string[];
  model?: string;
}

export class CloudflareAIProviderManager {
  private providers: AIProvider[];
  private env: Env;
  private failureThreshold = 3;
  private recoveryTime = 300000; // 5分钟

  constructor(env: Env) {
    this.env = env;
    this.providers = [
      {
        id: 'openai',
        name: 'OpenAI GPT',
        apiKey: env.OPENAI_API_KEY || '',
        endpoint: 'api.openai.com',
        model: 'gpt-4',
        priority: 1,
        status: 'active',
        failureCount: 0,
        lastFailure: null,
        avgResponseTime: 500
      },
      {
        id: 'grok',
        name: 'Grok (X.AI)',
        apiKey: env.GROK_API_KEY || '',
        endpoint: 'api.x.ai',
        model: 'grok-3-latest',
        priority: 2,
        status: 'active',
        failureCount: 0,
        lastFailure: null,
        avgResponseTime: 1000
      }
    ];
  }

  /**
   * 智能故障转移AI脱敏调用
   */
  async callAIDeidentificationWithFailover(
    content: string, 
    filterOptions: FilterOptions, 
    preferredProvider?: string
  ): Promise<DeidentificationResult> {
    const availableProviders = this.getAvailableProviders();
    
    if (availableProviders.length === 0) {
      throw new Error('没有可用的AI提供商');
    }

    // 如果指定了首选提供商，优先使用
    if (preferredProvider) {
      const preferred = availableProviders.find(p => p.id === preferredProvider);
      if (preferred) {
        availableProviders.unshift(preferred);
        // 移除重复项
        const uniqueProviders = availableProviders.filter((provider, index, self) => 
          index === 0 || self.findIndex(p => p.id === provider.id) === index
        );
        availableProviders.splice(0, availableProviders.length, ...uniqueProviders);
      }
    }

    console.log(`🔄 开始AI脱敏，可用提供商: ${availableProviders.map(p => p.id).join(', ')}`);

    // 依次尝试每个可用的提供商
    for (let i = 0; i < availableProviders.length; i++) {
      const provider = availableProviders[i];
      
      try {
        console.log(`🤖 尝试使用 ${provider.id} (优先级: ${provider.priority})`);
        const startTime = Date.now();
        
        const result = await this.callAIDeidentificationAPI(content, provider, filterOptions);
        const responseTime = Date.now() - startTime;
        
        // 记录成功
        this.recordSuccess(provider.id, responseTime);
        
        return {
          ...result,
          provider: provider.id,
          responseTime: responseTime,
          failoverUsed: i > 0,
          attemptedProviders: availableProviders.slice(0, i + 1).map(p => p.id)
        };
        
      } catch (error) {
        console.log(`❌ ${provider.id} 失败: ${error.message}`);
        
        // 记录失败
        this.recordFailure(provider.id, error.message);
        
        // 如果不是最后一个提供商，继续尝试下一个
        if (i < availableProviders.length - 1) {
          console.log(`🔄 切换到下一个提供商...`);
          continue;
        } else {
          // 所有提供商都失败了
          throw new Error(`所有AI提供商都不可用。最后错误: ${error.message}`);
        }
      }
    }

    throw new Error('未知错误：无法完成AI脱敏');
  }

  /**
   * 获取可用的提供商列表
   */
  private getAvailableProviders(): AIProvider[] {
    const now = Date.now();
    return this.providers
      .filter(provider => {
        // 如果提供商被标记为不可用，检查是否到了恢复时间
        if (provider.status === 'unavailable' && provider.lastFailure) {
          if (now - provider.lastFailure > this.recoveryTime) {
            provider.status = 'active';
            provider.failureCount = 0;
            console.log(`🔄 ${provider.id} 提供商恢复可用状态`);
          }
        }
        return provider.status === 'active' && provider.apiKey;
      })
      .sort((a, b) => a.priority - b.priority);
  }

  /**
   * 记录提供商调用成功
   */
  private recordSuccess(providerId: string, responseTime: number): void {
    const provider = this.providers.find(p => p.id === providerId);
    if (provider) {
      provider.failureCount = 0;
      provider.status = 'active';
      provider.avgResponseTime = Math.round((provider.avgResponseTime + responseTime) / 2);
      console.log(`✅ ${providerId} 调用成功 (${responseTime}ms)`);
    }
  }

  /**
   * 记录提供商调用失败
   */
  private recordFailure(providerId: string, error: string): void {
    const provider = this.providers.find(p => p.id === providerId);
    if (provider) {
      provider.failureCount++;
      provider.lastFailure = Date.now();
      
      if (provider.failureCount >= this.failureThreshold) {
        provider.status = 'unavailable';
        console.log(`❌ ${providerId} 连续失败${provider.failureCount}次，标记为不可用`);
      } else {
        console.log(`⚠️ ${providerId} 失败 (${provider.failureCount}/${this.failureThreshold}): ${error}`);
      }
    }
  }

  /**
   * 调用AI脱敏API
   */
  private async callAIDeidentificationAPI(
    content: string, 
    provider: AIProvider, 
    filterOptions: FilterOptions
  ): Promise<DeidentificationResult> {
    const deidentificationPrompt = `请对以下文本进行脱敏处理，根据以下规则：
${filterOptions.personalInfo ? '- 替换个人信息（姓名、身份证号等）为***' : ''}
${filterOptions.contactInfo ? '- 替换联系方式（手机号、邮箱等）为***' : ''}
${filterOptions.inappropriateContent ? '- 替换不良信息（违规言论、敏感词汇）为***' : ''}
${filterOptions.sensitiveData ? '- 替换敏感数据（地址、财务信息）为***' : ''}

请直接返回脱敏后的文本，不要添加任何解释。

原文本：
${content}`;

    let requestBody: any;
    let endpoint: string;

    if (provider.id === 'grok') {
      endpoint = `https://${provider.endpoint}/v1/chat/completions`;
      requestBody = {
        messages: [
          {
            role: 'system',
            content: '你是一个专业的内容脱敏助手，负责保护用户隐私信息。'
          },
          {
            role: 'user',
            content: deidentificationPrompt
          }
        ],
        model: provider.model,
        stream: false,
        temperature: 0.1
      };
    } else if (provider.id === 'openai') {
      endpoint = `https://${provider.endpoint}/v1/chat/completions`;
      requestBody = {
        model: provider.model,
        messages: [
          {
            role: 'system',
            content: '你是一个专业的内容脱敏助手，负责保护用户隐私信息。'
          },
          {
            role: 'user',
            content: deidentificationPrompt
          }
        ],
        temperature: 0.1,
        max_tokens: 1000
      };
    } else {
      throw new Error(`不支持的AI提供商: ${provider.id}`);
    }

    console.log(`🔍 ${provider.id} API请求开始: ${endpoint}`);

    const response = await fetch(endpoint, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${provider.apiKey}`,
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(requestBody)
    });

    console.log(`🔍 ${provider.id} API响应状态: ${response.status}`);

    if (!response.ok) {
      const errorData = await response.text();
      console.log(`❌ ${provider.id} API错误: ${response.status} - ${errorData}`);
      throw new Error(`AI API错误: ${response.status} - ${errorData}`);
    }

    const data = await response.json();
    
    if (data.choices && data.choices[0]) {
      const sanitized = data.choices[0].message.content.trim();
      
      console.log(`✅ ${provider.id} API调用成功`);
      return {
        success: true,
        sanitized: sanitized,
        changes: this.analyzeChanges(content, sanitized),
        provider: provider.id,
        model: data.model || provider.model
      };
    } else {
      console.log(`❌ ${provider.id} API返回格式错误`);
      throw new Error('AI API返回格式错误');
    }
  }

  /**
   * 分析内容变更
   */
  private analyzeChanges(original: string, sanitized: string): any[] {
    const changes = [];
    
    if (original !== sanitized) {
      changes.push({
        type: 'ai_processed',
        original: '原始内容',
        sanitized: '已脱敏',
        description: 'AI处理后的内容'
      });
    }
    
    return changes;
  }

  /**
   * 获取提供商状态统计
   */
  getProviderStats() {
    return this.providers.map(provider => ({
      id: provider.id,
      name: provider.name,
      status: provider.status,
      failureCount: provider.failureCount,
      avgResponseTime: provider.avgResponseTime,
      priority: provider.priority
    }));
  }

  /**
   * 本地脱敏处理函数（备用方案）
   */
  performLocalDeidentification(content: string, filterOptions: FilterOptions) {
    let sanitized = content;
    let changes = [];

    if (filterOptions.personalInfo) {
      // 替换中文姓名（2-4个汉字）
      sanitized = sanitized.replace(/(?:我叫|姓名[是：]?|名字[是：]?)[\s]*([一-龯]{2,4})/g, (match, name) => {
        changes.push({ type: 'name', original: name, sanitized: '***' });
        return match.replace(name, '***');
      });
      
      // 替换身份证号
      sanitized = sanitized.replace(/\d{15}|\d{18}/g, (match) => {
        changes.push({ type: 'idcard', original: match, sanitized: '***************' });
        return '***************';
      });
    }

    if (filterOptions.contactInfo) {
      // 替换手机号
      sanitized = sanitized.replace(/1[3-9]\d{9}/g, (match) => {
        changes.push({ type: 'phone', original: match, sanitized: '1****5678' });
        return '1****5678';
      });
      
      // 替换邮箱
      sanitized = sanitized.replace(/\b[\w.-]+@[\w.-]+\.\w+\b/g, (match) => {
        changes.push({ type: 'email', original: match, sanitized: '***@***.com' });
        return '***@***.com';
      });
    }

    if (filterOptions.inappropriateContent) {
      // 替换不良信息关键词
      const badWords = ['违规', '敏感', '不良', '抵制', '反对', '政治', '暴力', '色情'];
      badWords.forEach(word => {
        const regex = new RegExp(word, 'g');
        if (regex.test(sanitized)) {
          changes.push({ type: 'inappropriate', original: word, sanitized: '***' });
          sanitized = sanitized.replace(regex, '***');
        }
      });
    }

    if (filterOptions.sensitiveData) {
      // 替换地址信息
      sanitized = sanitized.replace(/[一-龯]{2,}[省市区县][一-龯]{2,}[路街道巷弄][一-龯\d]{1,}/g, (match) => {
        changes.push({ type: 'address', original: match, sanitized: '***地址***' });
        return '***地址***';
      });
      
      // 替换金额信息
      sanitized = sanitized.replace(/\d+[万千百十]?元/g, (match) => {
        changes.push({ type: 'money', original: match, sanitized: '***元' });
        return '***元';
      });
    }

    return {
      sanitized: sanitized,
      changes: changes
    };
  }
}
