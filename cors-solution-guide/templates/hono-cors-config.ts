/**
 * Hono CORS配置模板
 * 适用于: Cloudflare Workers + Hono框架
 * 
 * 使用方法:
 * 1. 复制此文件到你的项目中
 * 2. 根据实际需求修改 allowedOrigins 数组
 * 3. 在主应用文件中导入并使用
 */

import { cors } from 'hono/cors';

// 环境配置接口
interface CORSEnvironmentConfig {
  origins: string[];
  credentials: boolean;
  maxAge: number;
  debug: boolean;
}

// 不同环境的CORS配置
const corsEnvironments: Record<string, CORSEnvironmentConfig> = {
  development: {
    origins: [
      'http://localhost:3000',
      'http://localhost:5173',
      'http://localhost:5174',
      'http://localhost:8080'
    ],
    credentials: true,
    maxAge: 0,  // 开发时不缓存预检结果
    debug: true
  },
  
  staging: {
    origins: [
      'https://staging.yourdomain.com',
      'https://test.yourdomain.com'
    ],
    credentials: true,
    maxAge: 3600,  // 1小时缓存
    debug: true
  },
  
  production: {
    origins: [
      'https://app.yourdomain.com',
      'https://admin.yourdomain.com'
    ],
    credentials: true,
    maxAge: 86400,  // 24小时缓存
    debug: false
  }
};

/**
 * 获取当前环境的CORS配置
 */
const getCurrentEnvironmentConfig = (): CORSEnvironmentConfig => {
  const env = process.env.NODE_ENV || 'production';
  return corsEnvironments[env] || corsEnvironments.production;
};

/**
 * 从环境变量获取允许的域名
 * 格式: ALLOWED_ORIGINS=https://app.domain.com,https://admin.domain.com
 */
const getOriginsFromEnv = (): string[] => {
  const origins = process.env.ALLOWED_ORIGINS;
  if (!origins) return [];
  
  return origins.split(',').map(origin => origin.trim()).filter(Boolean);
};

/**
 * 检查域名是否为Cloudflare Pages临时域名
 * 格式: https://[8位16进制].project-name.pages.dev
 */
const isCloudflarePagesDomain = (origin: string): boolean => {
  // 修改这里的项目名称为你的实际项目名
  const projectName = 'your-project-name';
  const regex = new RegExp(`^https://[a-f0-9]{8}\\.${projectName}\\.pages\\.dev$`);
  return regex.test(origin);
};

/**
 * 主要的CORS配置函数
 */
export const createCORSConfig = () => {
  const envConfig = getCurrentEnvironmentConfig();
  const envOrigins = getOriginsFromEnv();
  
  // 合并配置中的域名和环境变量中的域名
  const allAllowedOrigins = [
    ...envConfig.origins,
    ...envOrigins
  ];
  
  // 去重
  const uniqueOrigins = [...new Set(allAllowedOrigins)];
  
  if (envConfig.debug) {
    console.log('CORS配置初始化:', {
      environment: process.env.NODE_ENV || 'production',
      allowedOrigins: uniqueOrigins,
      credentials: envConfig.credentials,
      maxAge: envConfig.maxAge
    });
  }
  
  return cors({
    origin: (origin) => {
      // 处理无origin的请求 (如Postman, curl, 服务器到服务器请求)
      if (!origin) {
        if (envConfig.debug) {
          console.log('CORS: 允许无origin请求');
        }
        return origin;
      }
      
      // 检查是否在明确允许的域名列表中
      if (uniqueOrigins.includes(origin)) {
        if (envConfig.debug) {
          console.log(`CORS: 允许域名 ${origin} (在允许列表中)`);
        }
        return origin;
      }
      
      // 检查是否为Cloudflare Pages临时域名 (可选)
      if (isCloudflarePagesDomain(origin)) {
        if (envConfig.debug) {
          console.log(`CORS: 允许域名 ${origin} (Cloudflare Pages临时域名)`);
        }
        return origin;
      }
      
      // 拒绝其他域名
      if (envConfig.debug) {
        console.warn(`CORS: 拒绝域名 ${origin}`);
      }
      
      return false;
    },
    
    // 允许的HTTP方法
    allowMethods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
    
    // 允许的请求头
    allowHeaders: [
      'Content-Type',
      'Authorization',
      'X-Requested-With',
      'Accept',
      'Origin',
      'X-API-Key'  // 如果使用API密钥
    ],
    
    // 暴露给前端的响应头
    exposeHeaders: [
      'X-Total-Count',
      'X-Page-Count',
      'X-Rate-Limit-Remaining'
    ],
    
    // 是否允许发送凭证 (cookies, authorization headers)
    credentials: envConfig.credentials,
    
    // 预检请求缓存时间 (秒)
    maxAge: envConfig.maxAge
  });
};

/**
 * 默认导出 - 可以直接在app.use()中使用
 */
export default createCORSConfig();

/**
 * 使用示例:
 * 
 * // 方式1: 使用默认配置
 * import corsConfig from './cors-config';
 * app.use('*', corsConfig);
 * 
 * // 方式2: 自定义配置
 * import { createCORSConfig } from './cors-config';
 * const customCORS = createCORSConfig();
 * app.use('*', customCORS);
 * 
 * // 方式3: 运行时配置
 * import { createCORSConfig } from './cors-config';
 * app.use('*', createCORSConfig());
 */

/**
 * 环境变量配置示例:
 * 
 * # .env.development
 * NODE_ENV=development
 * ALLOWED_ORIGINS=http://localhost:3000,http://localhost:5173
 * 
 * # .env.production
 * NODE_ENV=production
 * ALLOWED_ORIGINS=https://app.yourdomain.com,https://admin.yourdomain.com
 * 
 * # wrangler.toml
 * [env.production.vars]
 * ALLOWED_ORIGINS = "https://app.yourdomain.com"
 */

/**
 * 故障排除:
 * 
 * 1. 如果遇到 "access-control-allow-origin: true" 错误:
 *    - 检查origin函数是否返回具体域名而不是布尔值
 * 
 * 2. 如果某个域名被拒绝:
 *    - 检查域名是否在allowedOrigins数组中
 *    - 检查域名格式是否正确 (包括协议和端口)
 * 
 * 3. 如果预检请求失败:
 *    - 检查allowMethods是否包含实际使用的HTTP方法
 *    - 检查allowHeaders是否包含实际发送的请求头
 * 
 * 4. 调试技巧:
 *    - 设置 NODE_ENV=development 启用调试日志
 *    - 使用浏览器开发者工具查看网络请求
 *    - 运行 cors-diagnose.sh 脚本进行诊断
 */
