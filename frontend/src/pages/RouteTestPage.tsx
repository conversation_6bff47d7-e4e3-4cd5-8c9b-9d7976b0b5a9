import React from 'react';
import { useNavigate } from 'react-router-dom';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { CheckCircle, XCircle, ExternalLink } from 'lucide-react';

const RouteTestPage: React.FC = () => {
  const navigate = useNavigate();

  // 数据分析相关路由测试
  const dataAnalysisRoutes = [
    {
      name: '问卷回复',
      path: '/superadmin/questionnaire-responses',
      description: '查看和管理问卷回复数据'
    },
    {
      name: '数据分析',
      path: '/superadmin/data-analysis',
      description: '基础数据分析和图表展示'
    },
    {
      name: '高级数据分析',
      path: '/superadmin/enhanced-data-analysis',
      description: '高级数据分析功能'
    },
    {
      name: '数据管理中心',
      path: '/superadmin/data-management',
      description: '数据管理和维护功能'
    },
    {
      name: '数据测试中心',
      path: '/superadmin/data-testing',
      description: '数据测试和验证功能'
    }
  ];

  const testRoute = (path: string) => {
    try {
      navigate(path);
      return true;
    } catch (error) {
      console.error(`路由测试失败: ${path}`, error);
      return false;
    }
  };

  const openInNewTab = (path: string) => {
    window.open(path, '_blank');
  };

  return (
    <div className="container mx-auto py-6">
      <div className="mb-6">
        <h1 className="text-2xl font-bold mb-2">数据分析路由测试</h1>
        <p className="text-gray-600">测试数据分析栏目下各个子项的路由是否正常工作</p>
      </div>

      <div className="grid gap-4">
        {dataAnalysisRoutes.map((route, index) => (
          <Card key={index} className="hover:shadow-md transition-shadow">
            <CardHeader>
              <div className="flex items-center justify-between">
                <div>
                  <CardTitle className="text-lg">{route.name}</CardTitle>
                  <CardDescription>{route.description}</CardDescription>
                </div>
                <Badge variant="outline" className="font-mono text-xs">
                  {route.path}
                </Badge>
              </div>
            </CardHeader>
            <CardContent>
              <div className="flex gap-2">
                <Button
                  onClick={() => testRoute(route.path)}
                  className="flex items-center gap-2"
                >
                  <CheckCircle className="h-4 w-4" />
                  当前窗口测试
                </Button>
                <Button
                  variant="outline"
                  onClick={() => openInNewTab(route.path)}
                  className="flex items-center gap-2"
                >
                  <ExternalLink className="h-4 w-4" />
                  新窗口打开
                </Button>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      <Card className="mt-6">
        <CardHeader>
          <CardTitle>测试说明</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-2 text-sm text-gray-600">
            <p>• <strong>当前窗口测试</strong>：在当前窗口中导航到目标页面</p>
            <p>• <strong>新窗口打开</strong>：在新标签页中打开目标页面</p>
            <p>• 如果页面能正常加载，说明路由配置正确</p>
            <p>• 如果出现404或加载错误，说明路由配置有问题</p>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default RouteTestPage;
