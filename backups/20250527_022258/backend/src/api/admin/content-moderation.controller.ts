/**
 * 内容审核控制器
 *
 * 处理内容审核相关的API请求
 */

import { Context } from 'hono';
import { Env } from '../../types';
import { AIService, ContentModerationResult } from '../../services/ai/ai-service';
import { zValidator } from '@hono/zod-validator';
import { z } from 'zod';
import { PrismaClient } from '@prisma/client';
import { getPrismaClient } from '../../utils/prisma';

// 内容审核请求验证模式
const contentModerationSchema = z.object({
  content: z.string().min(1, '内容不能为空').max(10000, '内容过长'),
  contentType: z.enum(['story', 'questionnaire', 'comment', 'profile', 'feedback']).optional(),
  contentId: z.string().optional(),
  contextData: z.record(z.any()).optional() // 额外的上下文数据，用于更精确的审核
});

/**
 * 审核内容
 *
 * 使用AI服务审核内容是否包含敏感或不适当的信息
 */
export const moderateContent = [
  zValidator('json', contentModerationSchema),
  async (c: Context<{ Bindings: Env }>) => {
    try {
      const { content, contentType, contentId, contextData } = c.req.valid('json');

      // 创建AI服务实例
      const aiService = new AIService(c.env);

      // 根据内容类型选择适当的审核提示词
      let result: ContentModerationResult;

      if (contentType === 'questionnaire') {
        result = await aiService.moderateQuestionnaire(content, contextData);
      } else if (contentType === 'comment') {
        result = await aiService.moderateComment(content, contextData);
      } else if (contentType === 'profile') {
        result = await aiService.moderateProfile(content, contextData);
      } else if (contentType === 'feedback') {
        result = await aiService.moderateFeedback(content, contextData);
      } else if (contentType === 'story') {
        result = await aiService.moderateStory(content, contextData);
      } else {
        // 默认使用通用审核
        result = await aiService.moderateContent(content);
      }

      // 记录审核结果
      console.log(`内容审核结果 [${contentType || 'unknown'}:${contentId || 'new'}]:`, {
        isSafe: result.isSafe,
        suggestedAction: result.suggestedAction,
        issues: result.issues
      });

      // 记录审核历史
      try {
        await recordModerationHistory(c.env, {
          contentType: contentType || 'unknown',
          contentId: contentId || 'new',
          reviewerId: c.get('user')?.id || 'system',
          result,
          ipAddress: c.req.header('CF-Connecting-IP') || c.req.header('X-Forwarded-For') || 'unknown',
          userAgent: c.req.header('User-Agent') || 'unknown'
        });
      } catch (recordError) {
        console.error('记录审核历史失败:', recordError);
        // 不影响主流程，继续返回结果
      }

      // 返回结果
      return c.json({
        success: true,
        result
      });
    } catch (error) {
      console.error('内容审核失败:', error);

      return c.json({
        success: false,
        error: error instanceof Error ? error.message : '内容审核失败'
      }, 500);
    }
  }
];

/**
 * 批量审核内容
 *
 * 批量审核多条内容
 */
export const moderateContentBatch = async (c: Context<{ Bindings: Env }>) => {
  try {
    const { contents } = await c.req.json<{ contents: { id: string; content: string; type: string }[] }>();

    if (!Array.isArray(contents) || contents.length === 0) {
      return c.json({
        success: false,
        error: '无效的内容列表'
      }, 400);
    }

    // 创建AI服务实例
    const aiService = new AIService(c.env);

    // 批量审核内容
    const results: { id: string; result: ContentModerationResult }[] = [];

    for (const item of contents) {
      try {
        const result = await aiService.moderateContent(item.content);
        results.push({ id: item.id, result });
      } catch (error) {
        console.error(`审核内容 ${item.id} 失败:`, error);
        results.push({
          id: item.id,
          result: {
            isSafe: false,
            issues: ['内容审核失败'],
            confidence: 0,
            explanation: '内容审核服务暂时不可用',
            suggestedAction: 'review'
          }
        });
      }
    }

    // 返回结果
    return c.json({
      success: true,
      results
    });
  } catch (error) {
    console.error('批量内容审核失败:', error);

    return c.json({
      success: false,
      error: error instanceof Error ? error.message : '批量内容审核失败'
    }, 500);
  }
};

/**
 * 记录内容审核历史
 *
 * @param env 环境变量
 * @param data 审核数据
 */
interface ModerationHistoryData {
  contentType: string;
  contentId: string;
  reviewerId: string;
  result: ContentModerationResult;
  ipAddress: string;
  userAgent: string;
}

async function recordModerationHistory(env: Env, data: ModerationHistoryData): Promise<void> {
  try {
    const prisma = getPrismaClient(env);

    // 创建审核历史记录
    await prisma.moderationHistory.create({
      data: {
        contentType: data.contentType,
        contentId: data.contentId,
        reviewerId: data.reviewerId,
        isSafe: data.result.isSafe,
        issues: data.result.issues.join(','),
        confidence: data.result.confidence,
        explanation: data.result.explanation,
        suggestedAction: data.result.suggestedAction,
        severity: data.result.severity,
        dataQuality: data.result.dataQuality,
        constructiveValue: data.result.constructiveValue,
        storyValue: data.result.storyValue,
        ipAddress: data.ipAddress,
        userAgent: data.userAgent
      }
    });

    console.log(`已记录内容审核历史 [${data.contentType}:${data.contentId}]`);
  } catch (error) {
    console.error('记录内容审核历史失败:', error);
    throw error;
  }
}

/**
 * 获取AI服务状态
 *
 * 检查AI服务是否可用
 */
export const getAIServiceStatus = async (c: Context<{ Bindings: Env }>) => {
  try {
    // 创建AI服务实例
    const aiService = new AIService(c.env);

    // 审核一个简单的测试内容
    const result = await aiService.moderateContent('这是一个测试内容，用于检查AI服务是否正常工作。');

    // 返回状态
    return c.json({
      success: true,
      status: 'available',
      provider: c.env.AI_PROVIDER || 'grok',
      model: c.env.GROK_MODEL || 'grok-3-latest'
    });
  } catch (error) {
    console.error('AI服务状态检查失败:', error);

    return c.json({
      success: false,
      status: 'unavailable',
      error: error instanceof Error ? error.message : 'AI服务不可用'
    });
  }
};

/**
 * 获取内容质量趋势分析
 */
export const getContentQualityTrends = async (c: Context<{ Bindings: Env }>) => {
  try {
    const prisma = getPrismaClient(c.env);

    // 获取参数
    const { period = 'month', contentType, startDate, endDate } = c.req.query();

    // 确定时间格式
    const timeFormat = period === 'day' ? '%Y-%m-%d' :
                       period === 'week' ? '%Y-%W' :
                       period === 'year' ? '%Y' : '%Y-%m';

    // 确定时间范围
    let timeRange = new Date();
    if (period === 'day') {
      timeRange.setDate(timeRange.getDate() - 30); // 最近30天
    } else if (period === 'week') {
      timeRange.setDate(timeRange.getDate() - 90); // 最近90天（约13周）
    } else if (period === 'year') {
      timeRange.setFullYear(timeRange.getFullYear() - 3); // 最近3年
    } else {
      timeRange.setMonth(timeRange.getMonth() - 12); // 最近12个月
    }

    // 如果提供了自定义时间范围，则使用自定义范围
    const startDateTime = startDate ? new Date(startDate) : timeRange;
    const endDateTime = endDate ? new Date(endDate) : new Date();

    // 构建内容类型条件
    const contentTypeCondition = contentType ? `AND contentType = '${contentType}'` : '';

    // 获取内容质量趋势数据
    const qualityTrends = await prisma.$queryRaw`
      SELECT
        strftime(${timeFormat}, createdAt) as timePeriod,
        contentType,
        AVG(CASE
          WHEN dataQuality = 'high' THEN 3
          WHEN dataQuality = 'medium' THEN 2
          WHEN dataQuality = 'low' THEN 1
          ELSE NULL
        END) as avgDataQuality,
        AVG(CASE
          WHEN constructiveValue = 'high' THEN 3
          WHEN constructiveValue = 'medium' THEN 2
          WHEN constructiveValue = 'low' THEN 1
          WHEN constructiveValue = 'none' THEN 0
          ELSE NULL
        END) as avgConstructiveValue,
        AVG(CASE
          WHEN storyValue = 'high' THEN 3
          WHEN storyValue = 'medium' THEN 2
          WHEN storyValue = 'low' THEN 1
          WHEN storyValue = 'none' THEN 0
          ELSE NULL
        END) as avgStoryValue,
        AVG(confidence) as avgConfidence,
        COUNT(*) as count,
        SUM(CASE WHEN isSafe = 1 THEN 1 ELSE 0 END) as safeCount,
        SUM(CASE WHEN isSafe = 0 THEN 1 ELSE 0 END) as unsafeCount
      FROM ModerationHistory
      WHERE createdAt BETWEEN ${startDateTime} AND ${endDateTime}
      ${contentTypeCondition}
      GROUP BY timePeriod, contentType
      ORDER BY timePeriod ASC, contentType
    `;

    // 获取严重程度趋势
    const severityTrends = await prisma.$queryRaw`
      SELECT
        strftime(${timeFormat}, createdAt) as timePeriod,
        severity,
        COUNT(*) as count
      FROM ModerationHistory
      WHERE createdAt BETWEEN ${startDateTime} AND ${endDateTime}
      AND severity IS NOT NULL
      ${contentTypeCondition}
      GROUP BY timePeriod, severity
      ORDER BY timePeriod ASC, severity
    `;

    // 获取建议操作趋势
    const actionTrends = await prisma.$queryRaw`
      SELECT
        strftime(${timeFormat}, createdAt) as timePeriod,
        suggestedAction,
        COUNT(*) as count
      FROM ModerationHistory
      WHERE createdAt BETWEEN ${startDateTime} AND ${endDateTime}
      ${contentTypeCondition}
      GROUP BY timePeriod, suggestedAction
      ORDER BY timePeriod ASC, suggestedAction
    `;

    // 返回结果
    return c.json({
      success: true,
      trends: {
        qualityTrends,
        severityTrends,
        actionTrends,
        period,
        startDate: startDateTime.toISOString(),
        endDate: endDateTime.toISOString()
      }
    });
  } catch (error) {
    console.error('获取内容质量趋势分析失败:', error);

    return c.json({
      success: false,
      error: error instanceof Error ? error.message : '获取内容质量趋势分析失败'
    }, 500);
  }
};

/**
 * 获取内容审核历史
 */
export const getReviewerPerformance = async (c: Context<{ Bindings: Env }>) => {
  try {
    const prisma = getPrismaClient(c.env);

    // 获取时间范围参数
    const { startDate, endDate } = c.req.query();

    // 构建查询条件
    const whereClause: any = {};

    if (startDate) {
      whereClause.createdAt = {
        ...whereClause.createdAt,
        gte: new Date(startDate)
      };
    }

    if (endDate) {
      whereClause.createdAt = {
        ...whereClause.createdAt,
        lte: new Date(endDate)
      };
    }

    // 获取所有审核员ID
    const reviewers = await prisma.moderationHistory.findMany({
      select: {
        reviewerId: true
      },
      distinct: ['reviewerId'],
      where: whereClause
    });

    // 获取每个审核员的绩效数据
    const performanceData = await Promise.all(
      reviewers.map(async (reviewer) => {
        // 总审核数
        const totalCount = await prisma.moderationHistory.count({
          where: {
            reviewerId: reviewer.reviewerId,
            ...whereClause
          }
        });

        // 按内容类型分组的审核数量
        const typeStats = await prisma.$queryRaw`
          SELECT contentType, COUNT(*) as count
          FROM ModerationHistory
          WHERE reviewerId = ${reviewer.reviewerId}
          ${startDate ? `AND createdAt >= ${new Date(startDate)}` : ''}
          ${endDate ? `AND createdAt <= ${new Date(endDate)}` : ''}
          GROUP BY contentType
        `;

        // 按建议操作分组的审核数量
        const actionStats = await prisma.$queryRaw`
          SELECT suggestedAction, COUNT(*) as count
          FROM ModerationHistory
          WHERE reviewerId = ${reviewer.reviewerId}
          ${startDate ? `AND createdAt >= ${new Date(startDate)}` : ''}
          ${endDate ? `AND createdAt <= ${new Date(endDate)}` : ''}
          GROUP BY suggestedAction
        `;

        // 按严重程度分组的审核数量
        const severityStats = await prisma.$queryRaw`
          SELECT severity, COUNT(*) as count
          FROM ModerationHistory
          WHERE reviewerId = ${reviewer.reviewerId}
          AND severity IS NOT NULL
          ${startDate ? `AND createdAt >= ${new Date(startDate)}` : ''}
          ${endDate ? `AND createdAt <= ${new Date(endDate)}` : ''}
          GROUP BY severity
        `;

        // 每日审核数量
        const dailyStats = await prisma.$queryRaw`
          SELECT DATE(createdAt) as date, COUNT(*) as count
          FROM ModerationHistory
          WHERE reviewerId = ${reviewer.reviewerId}
          ${startDate ? `AND createdAt >= ${new Date(startDate)}` : ''}
          ${endDate ? `AND createdAt <= ${new Date(endDate)}` : ''}
          GROUP BY DATE(createdAt)
          ORDER BY date ASC
        `;

        // 计算平均质量评分
        const qualityStats = await prisma.$queryRaw`
          SELECT
            AVG(CASE
              WHEN dataQuality = 'high' THEN 3
              WHEN dataQuality = 'medium' THEN 2
              WHEN dataQuality = 'low' THEN 1
              ELSE NULL
            END) as avgDataQuality,
            AVG(CASE
              WHEN constructiveValue = 'high' THEN 3
              WHEN constructiveValue = 'medium' THEN 2
              WHEN constructiveValue = 'low' THEN 1
              WHEN constructiveValue = 'none' THEN 0
              ELSE NULL
            END) as avgConstructiveValue,
            AVG(CASE
              WHEN storyValue = 'high' THEN 3
              WHEN storyValue = 'medium' THEN 2
              WHEN storyValue = 'low' THEN 1
              WHEN storyValue = 'none' THEN 0
              ELSE NULL
            END) as avgStoryValue
          FROM ModerationHistory
          WHERE reviewerId = ${reviewer.reviewerId}
          ${startDate ? `AND createdAt >= ${new Date(startDate)}` : ''}
          ${endDate ? `AND createdAt <= ${new Date(endDate)}` : ''}
        `;

        // 计算平均处理时间（如果有相关数据）
        // 注意：这里假设有一个关联的审核日志表记录了处理时间
        // 如果没有这样的表，可以省略这部分

        return {
          reviewerId: reviewer.reviewerId,
          totalCount,
          byType: typeStats,
          byAction: actionStats,
          bySeverity: severityStats,
          daily: dailyStats,
          qualityStats: qualityStats[0]
        };
      })
    );

    // 返回结果
    return c.json({
      success: true,
      performance: performanceData
    });
  } catch (error) {
    console.error('获取审核员绩效统计失败:', error);

    return c.json({
      success: false,
      error: error instanceof Error ? error.message : '获取审核员绩效统计失败'
    }, 500);
  }
};

export const getModerationHistory = async (c: Context<{ Bindings: Env }>) => {
  try {
    // 获取查询参数
    const contentType = c.req.query('contentType');
    const contentId = c.req.query('contentId');
    const reviewerId = c.req.query('reviewerId');
    const suggestedAction = c.req.query('suggestedAction');
    const startDate = c.req.query('startDate') ? new Date(c.req.query('startDate')) : undefined;
    const endDate = c.req.query('endDate') ? new Date(c.req.query('endDate')) : undefined;
    const page = parseInt(c.req.query('page') || '1', 10);
    const pageSize = parseInt(c.req.query('pageSize') || '20', 10);

    // 计算分页
    const skip = (page - 1) * pageSize;

    // 构建查询条件
    const where: any = {};

    if (contentType) {
      where.contentType = contentType;
    }

    if (contentId) {
      where.contentId = contentId;
    }

    if (reviewerId) {
      where.reviewerId = reviewerId;
    }

    if (suggestedAction) {
      where.suggestedAction = suggestedAction;
    }

    if (startDate) {
      where.createdAt = {
        ...(where.createdAt || {}),
        gte: startDate
      };
    }

    if (endDate) {
      where.createdAt = {
        ...(where.createdAt || {}),
        lte: endDate
      };
    }

    // 获取审核历史
    const prisma = getPrismaClient(c.env);

    // 获取总数
    const total = await prisma.moderationHistory.count({
      where
    });

    // 获取分页数据
    const history = await prisma.moderationHistory.findMany({
      where,
      orderBy: {
        createdAt: 'desc'
      },
      skip,
      take: pageSize
    });

    // 返回结果
    return c.json({
      success: true,
      history,
      pagination: {
        page,
        pageSize,
        total,
        totalPages: Math.ceil(total / pageSize)
      }
    });
  } catch (error) {
    console.error('获取内容审核历史失败:', error);

    return c.json({
      success: false,
      error: error instanceof Error ? error.message : '获取内容审核历史失败'
    }, 500);
  }
};

/**
 * 获取内容审核统计
 */
export const getModerationStats = async (c: Context<{ Bindings: Env }>) => {
  try {
    const prisma = getPrismaClient(c.env);

    // 获取总审核数
    const totalCount = await prisma.moderationHistory.count();

    // 获取安全内容数
    const safeCount = await prisma.moderationHistory.count({
      where: {
        isSafe: true
      }
    });

    // 获取不安全内容数
    const unsafeCount = await prisma.moderationHistory.count({
      where: {
        isSafe: false
      }
    });

    // 获取按建议操作分组的数量
    const actionStats = await prisma.$queryRaw`
      SELECT suggestedAction, COUNT(*) as count
      FROM ModerationHistory
      GROUP BY suggestedAction
    `;

    // 获取按内容类型分组的数量
    const typeStats = await prisma.$queryRaw`
      SELECT contentType, COUNT(*) as count
      FROM ModerationHistory
      GROUP BY contentType
    `;

    // 获取最近7天的审核数量
    const last7Days = new Date();
    last7Days.setDate(last7Days.getDate() - 7);

    const dailyStats = await prisma.$queryRaw`
      SELECT DATE(createdAt) as date, COUNT(*) as count
      FROM ModerationHistory
      WHERE createdAt >= ${last7Days}
      GROUP BY DATE(createdAt)
      ORDER BY date ASC
    `;

    // 获取按严重程度分组的数量
    const severityStats = await prisma.$queryRaw`
      SELECT severity, COUNT(*) as count
      FROM ModerationHistory
      WHERE severity IS NOT NULL
      GROUP BY severity
    `;

    // 获取内容质量趋势（按月统计）
    const last6Months = new Date();
    last6Months.setMonth(last6Months.getMonth() - 6);

    const qualityTrends = await prisma.$queryRaw`
      SELECT
        strftime('%Y-%m', createdAt) as month,
        contentType,
        AVG(CASE
          WHEN dataQuality = 'high' THEN 3
          WHEN dataQuality = 'medium' THEN 2
          WHEN dataQuality = 'low' THEN 1
          ELSE NULL
        END) as avgDataQuality,
        AVG(CASE
          WHEN constructiveValue = 'high' THEN 3
          WHEN constructiveValue = 'medium' THEN 2
          WHEN constructiveValue = 'low' THEN 1
          WHEN constructiveValue = 'none' THEN 0
          ELSE NULL
        END) as avgConstructiveValue,
        AVG(CASE
          WHEN storyValue = 'high' THEN 3
          WHEN storyValue = 'medium' THEN 2
          WHEN storyValue = 'low' THEN 1
          WHEN storyValue = 'none' THEN 0
          ELSE NULL
        END) as avgStoryValue,
        COUNT(*) as count
      FROM ModerationHistory
      WHERE createdAt >= ${last6Months}
      GROUP BY month, contentType
      ORDER BY month ASC, contentType
    `;

    // 返回结果
    return c.json({
      success: true,
      stats: {
        total: totalCount,
        safe: safeCount,
        unsafe: unsafeCount,
        byAction: actionStats,
        byType: typeStats,
        bySeverity: severityStats,
        daily: dailyStats,
        qualityTrends
      }
    });
  } catch (error) {
    console.error('获取内容审核统计失败:', error);

    return c.json({
      success: false,
      error: error instanceof Error ? error.message : '获取内容审核统计失败'
    }, 500);
  }
};

// ==================== 新增CRUD操作 ====================

/**
 * 获取待审核内容列表
 */
export const getPendingContents = async (c: Context<{ Bindings: Env }>) => {
  try {
    const prisma = getPrismaClient(c.env);

    // 获取查询参数
    const page = parseInt(c.req.query('page') || '1');
    const pageSize = parseInt(c.req.query('pageSize') || '10');
    const type = c.req.query('type');
    const status = c.req.query('status') || 'pending';
    const priority = c.req.query('priority');
    const reviewerId = c.req.query('reviewerId');
    const search = c.req.query('search');

    // 构建查询条件
    const whereClause: any = { status };

    if (type) whereClause.type = type;
    if (priority) whereClause.priority = parseInt(priority);
    if (reviewerId) whereClause.reviewerId = reviewerId;
    if (search) {
      whereClause.OR = [
        { originalContent: { contains: search } },
        { sanitizedContent: { contains: search } },
        { sequenceNumber: { contains: search } }
      ];
    }

    // 获取总数
    const total = await prisma.pendingContent.count({ where: whereClause });

    // 获取分页数据
    const contents = await prisma.pendingContent.findMany({
      where: whereClause,
      orderBy: [
        { priority: 'desc' },
        { createdAt: 'asc' }
      ],
      skip: (page - 1) * pageSize,
      take: pageSize
    });

    return c.json({
      success: true,
      pendingContents: contents,
      pagination: {
        page,
        pageSize,
        total,
        totalPages: Math.ceil(total / pageSize)
      }
    });
  } catch (error) {
    console.error('获取待审核内容列表失败:', error);
    return c.json({
      success: false,
      error: error instanceof Error ? error.message : '获取待审核内容列表失败'
    }, 500);
  }
};

/**
 * 获取单个待审核内容详情
 */
export const getPendingContentById = async (c: Context<{ Bindings: Env }>) => {
  try {
    const prisma = getPrismaClient(c.env);
    const id = c.req.param('id');

    const content = await prisma.pendingContent.findUnique({
      where: { id }
    });

    if (!content) {
      return c.json({
        success: false,
        error: '内容不存在'
      }, 404);
    }

    return c.json({
      success: true,
      pendingContent: content
    });
  } catch (error) {
    console.error('获取待审核内容详情失败:', error);
    return c.json({
      success: false,
      error: error instanceof Error ? error.message : '获取待审核内容详情失败'
    }, 500);
  }
};

/**
 * 审核通过内容
 */
export const approveContent = async (c: Context<{ Bindings: Env }>) => {
  try {
    const prisma = getPrismaClient(c.env);
    const id = c.req.param('id');
    const { reviewNotes } = await c.req.json();
    const reviewerId = c.get('user')?.id || 'system';

    // 检查内容是否存在
    const content = await prisma.pendingContent.findUnique({
      where: { id }
    });

    if (!content) {
      return c.json({
        success: false,
        error: '内容不存在'
      }, 404);
    }

    // 更新内容状态
    const updatedContent = await prisma.pendingContent.update({
      where: { id },
      data: {
        status: 'approved',
        reviewerId,
        reviewedAt: new Date(),
        reviewNotes
      }
    });

    // 记录审核历史
    await prisma.reviewLog.create({
      data: {
        contentId: id,
        reviewerId,
        action: 'approve',
        reviewNotes,
        ipAddress: c.req.header('CF-Connecting-IP') || 'unknown',
        userAgent: c.req.header('User-Agent') || 'unknown'
      }
    });

    return c.json({
      success: true,
      message: '审核通过',
      contentId: id
    });
  } catch (error) {
    console.error('审核通过内容失败:', error);
    return c.json({
      success: false,
      error: error instanceof Error ? error.message : '审核通过内容失败'
    }, 500);
  }
};

/**
 * 编辑并通过内容
 */
export const editAndApproveContent = async (c: Context<{ Bindings: Env }>) => {
  try {
    const prisma = getPrismaClient(c.env);
    const id = c.req.param('id');
    const { content, reviewNotes } = await c.req.json();
    const reviewerId = c.get('user')?.id || 'system';

    // 检查内容是否存在
    const existingContent = await prisma.pendingContent.findUnique({
      where: { id }
    });

    if (!existingContent) {
      return c.json({
        success: false,
        error: '内容不存在'
      }, 404);
    }

    // 更新内容状态和编辑后的内容
    const updatedContent = await prisma.pendingContent.update({
      where: { id },
      data: {
        status: 'approved',
        sanitizedContent: JSON.stringify(content),
        reviewerId,
        reviewedAt: new Date(),
        reviewNotes
      }
    });

    // 记录审核历史，包含编辑差异
    const diff = JSON.stringify({
      original: existingContent.sanitizedContent || existingContent.originalContent,
      edited: content
    });

    await prisma.reviewLog.create({
      data: {
        contentId: id,
        reviewerId,
        action: 'edit',
        diff,
        reviewNotes,
        ipAddress: c.req.header('CF-Connecting-IP') || 'unknown',
        userAgent: c.req.header('User-Agent') || 'unknown'
      }
    });

    return c.json({
      success: true,
      message: '编辑并通过',
      contentId: id
    });
  } catch (error) {
    console.error('编辑并通过内容失败:', error);
    return c.json({
      success: false,
      error: error instanceof Error ? error.message : '编辑并通过内容失败'
    }, 500);
  }
};

/**
 * 拒绝内容
 */
export const rejectContent = async (c: Context<{ Bindings: Env }>) => {
  try {
    const prisma = getPrismaClient(c.env);
    const id = c.req.param('id');
    const { reason, reviewNotes } = await c.req.json();
    const reviewerId = c.get('user')?.id || 'system';

    // 检查内容是否存在
    const content = await prisma.pendingContent.findUnique({
      where: { id }
    });

    if (!content) {
      return c.json({
        success: false,
        error: '内容不存在'
      }, 404);
    }

    // 更新内容状态
    await prisma.pendingContent.update({
      where: { id },
      data: {
        status: 'rejected',
        reviewerId,
        reviewedAt: new Date(),
        reviewNotes: `拒绝原因: ${reason}${reviewNotes ? `\n审核备注: ${reviewNotes}` : ''}`
      }
    });

    // 记录审核历史
    await prisma.reviewLog.create({
      data: {
        contentId: id,
        reviewerId,
        action: 'reject',
        reviewNotes: `拒绝原因: ${reason}${reviewNotes ? `\n审核备注: ${reviewNotes}` : ''}`,
        ipAddress: c.req.header('CF-Connecting-IP') || 'unknown',
        userAgent: c.req.header('User-Agent') || 'unknown'
      }
    });

    // 将内容移动到拒绝内容表
    await prisma.rejectedContent.create({
      data: {
        userId: content.reviewerId,
        contentType: content.type,
        originalContent: content.originalContent,
        reason,
        issues: reason,
        ipAddress: c.req.header('CF-Connecting-IP') || 'unknown',
        userAgent: c.req.header('User-Agent') || 'unknown'
      }
    });

    return c.json({
      success: true,
      message: '已拒绝内容'
    });
  } catch (error) {
    console.error('拒绝内容失败:', error);
    return c.json({
      success: false,
      error: error instanceof Error ? error.message : '拒绝内容失败'
    }, 500);
  }
};

/**
 * 批量审核内容
 */
export const batchModerateContent = async (c: Context<{ Bindings: Env }>) => {
  try {
    const prisma = getPrismaClient(c.env);
    const { contentIds, action, reason, reviewNotes } = await c.req.json();
    const reviewerId = c.get('user')?.id || 'system';

    if (!Array.isArray(contentIds) || contentIds.length === 0) {
      return c.json({
        success: false,
        error: '内容ID列表不能为空'
      }, 400);
    }

    if (!['approve', 'reject'].includes(action)) {
      return c.json({
        success: false,
        error: '操作类型必须是approve或reject'
      }, 400);
    }

    let processed = 0;
    let failed = 0;

    // 批量处理内容
    for (const contentId of contentIds) {
      try {
        // 检查内容是否存在
        const content = await prisma.pendingContent.findUnique({
          where: { id: contentId }
        });

        if (!content) {
          failed++;
          continue;
        }

        if (action === 'approve') {
          // 审核通过
          await prisma.pendingContent.update({
            where: { id: contentId },
            data: {
              status: 'approved',
              reviewerId,
              reviewedAt: new Date(),
              reviewNotes
            }
          });

          // 记录审核历史
          await prisma.reviewLog.create({
            data: {
              contentId,
              reviewerId,
              action: 'approve',
              reviewNotes,
              ipAddress: c.req.header('CF-Connecting-IP') || 'unknown',
              userAgent: c.req.header('User-Agent') || 'unknown'
            }
          });
        } else {
          // 拒绝
          await prisma.pendingContent.update({
            where: { id: contentId },
            data: {
              status: 'rejected',
              reviewerId,
              reviewedAt: new Date(),
              reviewNotes: `拒绝原因: ${reason}${reviewNotes ? `\n审核备注: ${reviewNotes}` : ''}`
            }
          });

          // 记录审核历史
          await prisma.reviewLog.create({
            data: {
              contentId,
              reviewerId,
              action: 'reject',
              reviewNotes: `拒绝原因: ${reason}${reviewNotes ? `\n审核备注: ${reviewNotes}` : ''}`,
              ipAddress: c.req.header('CF-Connecting-IP') || 'unknown',
              userAgent: c.req.header('User-Agent') || 'unknown'
            }
          });

          // 将内容移动到拒绝内容表
          await prisma.rejectedContent.create({
            data: {
              userId: content.reviewerId,
              contentType: content.type,
              originalContent: content.originalContent,
              reason: reason || '批量拒绝',
              issues: reason || '批量拒绝',
              ipAddress: c.req.header('CF-Connecting-IP') || 'unknown',
              userAgent: c.req.header('User-Agent') || 'unknown'
            }
          });
        }

        processed++;
      } catch (error) {
        console.error(`处理内容 ${contentId} 失败:`, error);
        failed++;
      }
    }

    return c.json({
      success: true,
      processed,
      failed,
      message: `批量${action === 'approve' ? '通过' : '拒绝'}完成，成功处理 ${processed} 个，失败 ${failed} 个`
    });
  } catch (error) {
    console.error('批量审核内容失败:', error);
    return c.json({
      success: false,
      error: error instanceof Error ? error.message : '批量审核内容失败'
    }, 500);
  }
};