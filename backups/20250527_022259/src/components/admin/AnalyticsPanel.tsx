import { useState, useEffect } from 'react';
import { useToast } from '@/components/ui/use-toast';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Tabs, TabsContent, Ta<PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import { Button } from '@/components/ui/button';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { DatePicker } from '@/components/ui/date-picker';
import { Skeleton } from '@/components/ui/skeleton';
import LoadingSpinner from '@/components/ui/loading-spinner';
import ErrorDisplay from '@/components/ui/error-display';

// 导入数据服务
import { getAnalyticsData, exportAnalyticsData } from '@/services/dataService';
import {
  BarChart,
  Bar,
  PieChart,
  Pie,
  LineChart,
  Line,
  XAxis,
  YAxis,
  CartesianGrid,
  <PERSON><PERSON><PERSON>,
  <PERSON>,
  ResponsiveContainer,
  Cell
} from 'recharts';

// 定义数据类型
interface AnalyticsData {
  totalResponses: number;
  verifiedResponses: number;
  anonymousResponses: number;
  responsesByDate: Array<{ date: string; count: number }>;
  educationLevels: Array<{ level: string; count: number }>;
  regions: Array<{ region: string; count: number }>;
  employmentStatus: Array<{ status: string; count: number }>;
  industries: Array<{ industry: string; count: number }>;
  salaryRanges: Array<{ range: string; count: number }>;
  jobSatisfaction: Array<{ level: string; count: number }>;
  careerChangeIntention: Array<{ intention: string; count: number }>;
}

// 定义过滤器类型
interface AnalyticsFilters {
  startDate?: string;
  endDate?: string;
  educationLevel?: string;
  region?: string;
  employmentStatus?: string;
  graduationYear?: number;
}

// 颜色配置
const COLORS = [
  '#0088FE', '#00C49F', '#FFBB28', '#FF8042', '#8884D8',
  '#82CA9D', '#FFC658', '#8DD1E1', '#A4DE6C', '#D0ED57'
];

export default function AnalyticsPanel() {
  const { toast } = useToast();
  const [activeTab, setActiveTab] = useState('overview');
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [analyticsData, setAnalyticsData] = useState<AnalyticsData | null>(null);
  const [filters, setFilters] = useState<AnalyticsFilters>({});
  const [exportFormat, setExportFormat] = useState<'csv' | 'excel'>('csv');

  // 获取分析数据
  const fetchAnalyticsData = async () => {
    try {
      setIsLoading(true);
      setError(null);

      const data = await getAnalyticsData(filters);

      if (data.success) {
        setAnalyticsData(data.stats);
      } else {
        setError(data.error || '获取分析数据失败');
      }
    } catch (error) {
      console.error('获取分析数据错误:', error);
      setError('服务器错误，请稍后再试');
    } finally {
      setIsLoading(false);
    }
  };

  // 导出数据
  const exportData = async () => {
    try {
      const data = await exportAnalyticsData(exportFormat, filters);

      if (data.success) {
        // 打开下载链接
        window.open(data.exportUrl, '_blank');

        toast({
          title: '导出成功',
          description: `数据已导出为${exportFormat.toUpperCase()}格式，链接有效期24小时`,
        });
      } else {
        toast({
          variant: 'destructive',
          title: '导出失败',
          description: data.error || '导出数据失败',
        });
      }
    } catch (error) {
      console.error('导出数据错误:', error);
      toast({
        variant: 'destructive',
        title: '导出失败',
        description: '服务器错误，请稍后再试',
      });
    }
  };

  // 组件加载时获取分析数据
  useEffect(() => {
    fetchAnalyticsData();
  }, [filters]);

  // 处理过滤器变化
  const handleFilterChange = (key: keyof AnalyticsFilters, value: any) => {
    setFilters(prev => ({
      ...prev,
      [key]: value,
    }));
  };

  if (isLoading) {
    return (
      <div className="flex justify-center items-center py-12">
        <LoadingSpinner size="lg" text="加载分析数据中..." />
      </div>
    );
  }

  if (error) {
    return (
      <ErrorDisplay
        title="加载数据失败"
        message={error}
        onRetry={fetchAnalyticsData}
      />
    );
  }

  if (!analyticsData) {
    return (
      <div className="text-center p-8">
        <p className="text-gray-500">暂无分析数据</p>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex flex-col md:flex-row justify-between items-start md:items-center gap-4">
        <div>
          <h3 className="text-lg font-medium">数据分析</h3>
          <p className="text-sm text-gray-500">
            总计 {analyticsData.totalResponses} 条问卷回复，
            其中已验证 {analyticsData.verifiedResponses} 条，
            匿名 {analyticsData.anonymousResponses} 条
          </p>
        </div>

        <div className="flex flex-wrap gap-2">
          <Select
            value={exportFormat}
            onValueChange={(value) => setExportFormat(value as 'csv' | 'excel')}
          >
            <SelectTrigger className="w-[120px]">
              <SelectValue placeholder="导出格式" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="csv">CSV格式</SelectItem>
              <SelectItem value="excel">Excel格式</SelectItem>
            </SelectContent>
          </Select>

          <Button variant="outline" onClick={exportData}>
            导出数据
          </Button>

          <Button variant="outline" onClick={fetchAnalyticsData}>
            刷新
          </Button>
        </div>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium">总回复数</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{analyticsData.totalResponses}</div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium">已验证回复</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{analyticsData.verifiedResponses}</div>
            <p className="text-xs text-gray-500">
              {Math.round((analyticsData.verifiedResponses / analyticsData.totalResponses) * 100)}%
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium">匿名回复</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{analyticsData.anonymousResponses}</div>
            <p className="text-xs text-gray-500">
              {Math.round((analyticsData.anonymousResponses / analyticsData.totalResponses) * 100)}%
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium">筛选</CardTitle>
          </CardHeader>
          <CardContent>
            <Select
              value={filters.educationLevel}
              onValueChange={(value) => handleFilterChange('educationLevel', value)}
            >
              <SelectTrigger className="w-full mb-2">
                <SelectValue placeholder="学历筛选" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="">全部学历</SelectItem>
                {analyticsData.educationLevels.map((item) => (
                  <SelectItem key={item.level} value={item.level}>
                    {item.level}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </CardContent>
        </Card>
      </div>

      <Tabs defaultValue="overview" value={activeTab} onValueChange={setActiveTab}>
        <TabsList className="grid grid-cols-3 mb-4">
          <TabsTrigger value="overview">概览</TabsTrigger>
          <TabsTrigger value="demographics">人口统计</TabsTrigger>
          <TabsTrigger value="employment">就业情况</TabsTrigger>
        </TabsList>

        <TabsContent value="overview" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>问卷提交趋势</CardTitle>
              <CardDescription>按月份统计问卷提交数量</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="h-80">
                <ResponsiveContainer width="100%" height="100%">
                  <LineChart
                    data={analyticsData.responsesByDate}
                    margin={{ top: 5, right: 30, left: 20, bottom: 5 }}
                  >
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis dataKey="date" />
                    <YAxis />
                    <Tooltip />
                    <Legend />
                    <Line
                      type="monotone"
                      dataKey="count"
                      name="提交数量"
                      stroke="#8884d8"
                      activeDot={{ r: 8 }}
                    />
                  </LineChart>
                </ResponsiveContainer>
              </div>
            </CardContent>
          </Card>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <Card>
              <CardHeader>
                <CardTitle>数据验证分布</CardTitle>
                <CardDescription>已验证与匿名数据占比</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="h-80">
                  <ResponsiveContainer width="100%" height="100%">
                    <PieChart>
                      <Pie
                        data={[
                          { name: '已验证', value: analyticsData.verifiedResponses },
                          { name: '匿名', value: analyticsData.anonymousResponses },
                        ]}
                        cx="50%"
                        cy="50%"
                        labelLine={false}
                        label={({ name, percent }) => `${name}: ${(percent * 100).toFixed(0)}%`}
                        outerRadius={80}
                        fill="#8884d8"
                        dataKey="value"
                      >
                        {[
                          { name: '已验证', value: analyticsData.verifiedResponses },
                          { name: '匿名', value: analyticsData.anonymousResponses },
                        ].map((entry, index) => (
                          <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                        ))}
                      </Pie>
                      <Tooltip />
                    </PieChart>
                  </ResponsiveContainer>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>转行意向分布</CardTitle>
                <CardDescription>是否有转行意向的占比</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="h-80">
                  <ResponsiveContainer width="100%" height="100%">
                    <PieChart>
                      <Pie
                        data={analyticsData.careerChangeIntention}
                        cx="50%"
                        cy="50%"
                        labelLine={false}
                        label={({ name, percent }) => `${name}: ${(percent * 100).toFixed(0)}%`}
                        outerRadius={80}
                        fill="#8884d8"
                        dataKey="count"
                      >
                        {analyticsData.careerChangeIntention.map((entry, index) => (
                          <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                        ))}
                      </Pie>
                      <Tooltip />
                    </PieChart>
                  </ResponsiveContainer>
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="demographics" className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <Card>
              <CardHeader>
                <CardTitle>学历分布</CardTitle>
                <CardDescription>各学历层次人数占比</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="h-80">
                  <ResponsiveContainer width="100%" height="100%">
                    <PieChart>
                      <Pie
                        data={analyticsData.educationLevels}
                        cx="50%"
                        cy="50%"
                        labelLine={false}
                        label={({ level, percent }) => `${level}: ${(percent * 100).toFixed(0)}%`}
                        outerRadius={80}
                        fill="#8884d8"
                        nameKey="level"
                        dataKey="count"
                      >
                        {analyticsData.educationLevels.map((entry, index) => (
                          <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                        ))}
                      </Pie>
                      <Tooltip />
                    </PieChart>
                  </ResponsiveContainer>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>地区分布</CardTitle>
                <CardDescription>各地区人数占比</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="h-80">
                  <ResponsiveContainer width="100%" height="100%">
                    <BarChart
                      data={analyticsData.regions.slice(0, 10)} // 只显示前10个地区
                      layout="vertical"
                      margin={{ top: 5, right: 30, left: 20, bottom: 5 }}
                    >
                      <CartesianGrid strokeDasharray="3 3" />
                      <XAxis type="number" />
                      <YAxis type="category" dataKey="region" width={80} />
                      <Tooltip />
                      <Legend />
                      <Bar dataKey="count" name="人数" fill="#8884d8" />
                    </BarChart>
                  </ResponsiveContainer>
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="employment" className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <Card>
              <CardHeader>
                <CardTitle>就业状态分布</CardTitle>
                <CardDescription>各就业状态人数占比</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="h-80">
                  <ResponsiveContainer width="100%" height="100%">
                    <PieChart>
                      <Pie
                        data={analyticsData.employmentStatus}
                        cx="50%"
                        cy="50%"
                        labelLine={false}
                        label={({ status, percent }) => `${status}: ${(percent * 100).toFixed(0)}%`}
                        outerRadius={80}
                        fill="#8884d8"
                        nameKey="status"
                        dataKey="count"
                      >
                        {analyticsData.employmentStatus.map((entry, index) => (
                          <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                        ))}
                      </Pie>
                      <Tooltip />
                    </PieChart>
                  </ResponsiveContainer>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>行业分布</CardTitle>
                <CardDescription>各行业人数占比</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="h-80">
                  <ResponsiveContainer width="100%" height="100%">
                    <BarChart
                      data={analyticsData.industries.slice(0, 10)} // 只显示前10个行业
                      layout="vertical"
                      margin={{ top: 5, right: 30, left: 20, bottom: 5 }}
                    >
                      <CartesianGrid strokeDasharray="3 3" />
                      <XAxis type="number" />
                      <YAxis type="category" dataKey="industry" width={80} />
                      <Tooltip />
                      <Legend />
                      <Bar dataKey="count" name="人数" fill="#8884d8" />
                    </BarChart>
                  </ResponsiveContainer>
                </div>
              </CardContent>
            </Card>
          </div>

          <Card>
            <CardHeader>
              <CardTitle>薪资分布</CardTitle>
              <CardDescription>各薪资范围人数分布</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="h-80">
                <ResponsiveContainer width="100%" height="100%">
                  <BarChart
                    data={analyticsData.salaryRanges}
                    margin={{ top: 5, right: 30, left: 20, bottom: 5 }}
                  >
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis dataKey="range" />
                    <YAxis />
                    <Tooltip />
                    <Legend />
                    <Bar dataKey="count" name="人数" fill="#8884d8" />
                  </BarChart>
                </ResponsiveContainer>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}

// 骨架屏组件
function AnalyticsSkeleton() {
  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <div>
          <Skeleton className="h-6 w-32 mb-2" />
          <Skeleton className="h-4 w-64" />
        </div>
        <div className="flex gap-2">
          <Skeleton className="h-10 w-24" />
          <Skeleton className="h-10 w-24" />
        </div>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        {[1, 2, 3, 4].map((i) => (
          <Card key={i}>
            <CardHeader className="pb-2">
              <Skeleton className="h-4 w-20" />
            </CardHeader>
            <CardContent>
              <Skeleton className="h-8 w-16 mb-1" />
              <Skeleton className="h-3 w-8" />
            </CardContent>
          </Card>
        ))}
      </div>

      <div>
        <Skeleton className="h-10 w-full mb-4" />

        <Card>
          <CardHeader>
            <Skeleton className="h-6 w-40 mb-2" />
            <Skeleton className="h-4 w-60" />
          </CardHeader>
          <CardContent>
            <Skeleton className="h-80 w-full" />
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
