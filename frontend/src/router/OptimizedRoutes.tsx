import React, { lazy, Suspense } from 'react';
import { Routes, Route, Navigate } from 'react-router-dom';
import { Spin } from 'antd';
import PermissionGuard from '../components/auth/PermissionGuard';
import { measureComponentRender } from '../utils/performance';
import Layout from '../components/Layout';

// 路由加载状态组件
const RouteLoading: React.FC = () => (
  <div style={{
    display: 'flex',
    justifyContent: 'center',
    alignItems: 'center',
    height: '100vh'
  }}>
    <Spin size="large" tip="页面加载中..." />
  </div>
);

// 预加载组件
const preloadComponent = (factory: () => Promise<any>) => {
  const Component = lazy(factory);
  Component.preload = factory;
  return Component;
};

// 懒加载组件
const withLazy = (componentName: string, factory: () => Promise<any>) => {
  const LazyComponent = lazy(() => {
    try {
      return factory().catch(error => {
        console.error(`Failed to load component ${componentName}:`, error);
        // 返回一个错误组件
        return Promise.resolve({
          default: () => (
            <div style={{ padding: '20px', textAlign: 'center' }}>
              <h2>组件加载失败</h2>
              <p>组件 {componentName} 加载失败，请刷新页面重试。</p>
              <button onClick={() => window.location.reload()}>刷新页面</button>
            </div>
          )
        });
      });
    } catch (error) {
      console.error(`Error in withLazy for ${componentName}:`, error);
      return Promise.resolve({
        default: () => (
          <div style={{ padding: '20px', textAlign: 'center' }}>
            <h2>组件加载失败</h2>
            <p>组件 {componentName} 加载失败，请刷新页面重试。</p>
            <button onClick={() => window.location.reload()}>刷新页面</button>
          </div>
        )
      });
    }
  });

  return (props: any) => (
    <Suspense fallback={<RouteLoading />}>
      <LazyComponent {...props} />
    </Suspense>
  );
};

// 预加载路由
export const preloadRoute = (path: string) => {
  const component = routeComponents[path];
  if (component && 'preload' in component) {
    component.preload();
  }
};

// 懒加载路由组件
const HomePage = withLazy('HomePage', () => import('../pages/HomePage'));
const QuestionnairePage = withLazy('QuestionnairePage', () => import('../pages/QuestionnairePage'));
const VisualizationPage = withLazy('VisualizationPage', () => import('../pages/VisualizationPage'));
const AdvancedAnalysisPage = withLazy('AdvancedAnalysisPage', () => import('../pages/AdvancedAnalysisPage'));
const StoryWallPage = withLazy('StoryWallPage', () => import('../pages/StoryWallPage'));
const SimpleStoryWall = withLazy('SimpleStoryWall', () => import('../pages/SimpleStoryWall'));
const MobileStoryWallDemo = withLazy('MobileStoryWallDemo', () => import('../pages/MobileStoryWallDemo'));
const QuestionnaireVoicesPage = withLazy('QuestionnaireVoicesPage', () => import('../pages/QuestionnaireVoicesPage'));
const QuestionnaireVoices2Page = withLazy('QuestionnaireVoices2Page', () => import('../pages/QuestionnaireVoices2Page'));
const StoryDetailPage = withLazy('StoryDetailPage', () => import('../pages/StoryDetailPage'));
const SubmitSuccessPage = withLazy('SubmitSuccessPage', () => import('../pages/SubmitSuccessPage'));
const MyContentPage = withLazy('MyContentPage', () => import('../pages/MyContentPage'));
const NotFoundPage = withLazy('NotFoundPage', () => import('../pages/NotFoundPage'));
const TempLoginPage = withLazy('TempLoginPage', () => import('../pages/TempLoginPage'));
const TestPage = withLazy('TestPage', () => import('../pages/TestPage'));
const TestSimplePage = withLazy('TestSimplePage', () => import('../pages/TestSimplePage'));
const ReviewerDashboard = withLazy('ReviewerDashboard', () => import('../pages/ReviewerDashboard'));

// 管理员页面
const AdminLoginPage = withLazy('AdminLoginPage', () => import('../pages/admin/AdminLoginPage'));
const AdminDashboardHomePage = withLazy('AdminDashboardHomePage', () => import('../pages/admin/AdminDashboardHomePage'));
const StoryReviewPage = withLazy('StoryReviewPage', () => import('../pages/admin/StoryReviewPage'));
const TagManagementPage = withLazy('TagManagementPage', () => import('../pages/admin/TagManagementPage'));
const QuestionnaireResponsesPage = withLazy('QuestionnaireResponsesPage', () => import('../pages/admin/QuestionnaireResponsesPage'));
const DataAnalysisPage = withLazy('DataAnalysisPage', () => import('../pages/admin/DataAnalysisPage'));
const UserManagementPage = withLazy('UserManagementPage', () => import('../pages/admin/UserManagementPage'));
const ReviewerManagementPage = withLazy('ReviewerManagementPage', () => import('../pages/admin/ReviewerManagementPage'));
const SecuritySettingsPage = withLazy('SecuritySettingsPage', () => import('../pages/admin/SecuritySettingsPage'));
const DeidentificationSettingsPage = withLazy('DeidentificationSettingsPage', () => import('../pages/admin/DeidentificationSettingsPage'));
const SuperAdminDeidentificationSettingsPage = withLazy('SuperAdminDeidentificationSettingsPage', () => import('../pages/superadmin/DeidentificationSettingsPage'));
const SecurityLogsPage = withLazy('SecurityLogsPage', () => import('../pages/admin/SecurityLogsPage'));
const ContentReviewPage = withLazy('ContentReviewPage', () => import('../pages/admin/ContentReviewPage'));
const CommentReviewPage = withLazy('CommentReviewPage', () => import('../pages/admin/CommentReviewPage'));
const AdminSettingsPage = withLazy('AdminSettingsPage', () => import('../pages/admin/AdminSettingsPage'));
const QuickReviewPage = withLazy('QuickReviewPage', () => import('../pages/admin/QuickReviewPage'));

// 审核员页面
const ReviewerLoginPage = withLazy('ReviewerLoginPage', () => import('../pages/reviewer/ReviewerLoginPage'));
const ReviewerDashboardPage = withLazy('ReviewerDashboardPage', () => import('../pages/reviewer/ReviewerDashboardPage'));
const ReviewerQuestionnaireVoicesPage = withLazy('ReviewerQuestionnaireVoicesPage', () => import('../pages/reviewer/ReviewerQuestionnaireVoicesPage'));
const ReviewerStoryReviewPage = withLazy('ReviewerStoryReviewPage', () => import('../pages/reviewer/ReviewerStoryReviewPage'));
const ReviewerContentReviewPage = withLazy('ReviewerContentReviewPage', () => import('../pages/reviewer/ReviewerContentReviewPage'));
const ReviewerQuickReviewPage = withLazy('ReviewerQuickReviewPage', () => import('../pages/reviewer/ReviewerQuickReviewPage'));
const ReviewerSettingsPage = withLazy('ReviewerSettingsPage', () => import('../pages/reviewer/ReviewerSettingsPage'));
const BatchStoryReviewPage = withLazy('BatchStoryReviewPage', () => import('../pages/reviewer/BatchStoryReviewPage'));
const BatchVoiceReviewPage = withLazy('BatchVoiceReviewPage', () => import('../pages/reviewer/BatchVoiceReviewPage'));
const TestBatchPage = withLazy('TestBatchPage', () => import('../pages/reviewer/TestBatchPage'));

// 超级管理员页面
const SuperAdminDashboardPage = withLazy('SuperAdminDashboardPage', () => import('../pages/superadmin/SuperAdminDashboardPage'));
const PlatformOverviewPage = withLazy('PlatformOverviewPage', () => import('../pages/superadmin/PlatformOverviewPage'));
const EnhancedDataAnalysisPage = withLazy('EnhancedDataAnalysisPage', () => import('../pages/superadmin/EnhancedDataAnalysisPage'));
const EnhancedSecurityMonitorPage = withLazy('EnhancedSecurityMonitorPage', () => import('../pages/superadmin/EnhancedSecurityMonitorPage'));
const SystemBackupPage = withLazy('SystemBackupPage', () => import('../pages/superadmin/SystemBackupPage'));
const UserBatchPage = withLazy('UserBatchPage', () => import('../pages/superadmin/UserBatchPage'));
const AdminAuditPage = withLazy('AdminAuditPage', () => import('../pages/superadmin/AdminAuditPage'));
const SecurityMonitorPage = withLazy('SecurityMonitorPage', () => import('../pages/superadmin/SecurityMonitorPage'));
const SystemConfigPage = withLazy('SystemConfigPage', () => import('../pages/superadmin/SystemConfigPage'));
const SystemManagementPage = withLazy('SystemManagementPage', () => import('../pages/superadmin/SystemManagementPage'));
const TestDataManagementPage = withLazy('TestDataManagementPage', () => import('../pages/superadmin/TestDataManagementPage'));
const RouteTestPage = withLazy('RouteTestPage', () => import('../pages/superadmin/RouteTestPage'));
const RoleManagementPage = withLazy('RoleManagementPage', () => import('../pages/superadmin/RoleManagementPage'));
const SuperAdminSettingsPage = withLazy('SuperAdminSettingsPage', () => import('../pages/superadmin/SuperAdminSettingsPage'));
const SystemMonitorPage = withLazy('SystemMonitorPage', () => import('../pages/superadmin/SystemMonitorPage'));
const DataRestorePage = withLazy('DataRestorePage', () => import('../pages/superadmin/DataRestorePage'));
const DocumentationPage = withLazy('DocumentationPage', () => import('../pages/superadmin/DocumentationPage'));
const SecurityPage = withLazy('SecurityPage', () => import('../pages/superadmin/SecurityPage'));
const SecurityAuditPage = withLazy('SecurityAuditPage', () => import('../pages/superadmin/SecurityAuditPage'));
const DataManagementPage = withLazy('DataManagementPage', () => import('../pages/superadmin/DataManagementPage'));
const DataTestingPage = withLazy('DataTestingPage', () => import('../pages/superadmin/DataTestingPage'));
const SuperAdminReviewerManagementPage = withLazy('SuperAdminReviewerManagementPage', () => import('../pages/superadmin/ReviewerManagementPage'));
const SuperAdminUserManagementPage = withLazy('SuperAdminUserManagementPage', () => import('../pages/superadmin/UserManagementPage'));
const SuperAdminAdminManagementPage = withLazy('SuperAdminAdminManagementPage', () => import('../pages/superadmin/AdminManagementPage'));
const SuperAdminContentReviewPage = withLazy('SuperAdminContentReviewPage', () => import('../pages/superadmin/ContentReviewPage'));
const SuperAdminStoryReviewPage = withLazy('SuperAdminStoryReviewPage', () => import('../pages/superadmin/StoryReviewPage'));
const SuperAdminCommentReviewPage = withLazy('SuperAdminCommentReviewPage', () => import('../pages/superadmin/CommentReviewPage'));
const SuperAdminQuickReviewPage = withLazy('SuperAdminQuickReviewPage', () => import('../pages/superadmin/QuickReviewPage'));
const SuperAdminTagManagementPage = withLazy('SuperAdminTagManagementPage', () => import('../pages/superadmin/TagManagementPage'));
const TestServicesPage = withLazy('TestServicesPage', () => import('../pages/TestServicesPage'));
const EnhancedContentManagementPage = withLazy('EnhancedContentManagementPage', () => import('../pages/superadmin/EnhancedContentManagementPage'));
const PerformanceOptimizationPage = withLazy('PerformanceOptimizationPage', () => import('../pages/superadmin/PerformanceOptimizationPage'));
const DiagnosticsPage = withLazy('DiagnosticsPage', () => import('../pages/DiagnosticsPage'));
const DataMonitorPage = withLazy('DataMonitorPage', () => import('../pages/DataMonitorPage'));
const TestDataGeneratorPage = withLazy('TestDataGeneratorPage', () => import('../pages/TestDataGeneratorPage'));

// 路由组件映射表（用于预加载）
export const routeComponents: Record<string, any> = {
  '/': HomePage,
  '/questionnaire': QuestionnairePage,
  '/visualization': VisualizationPage,
  '/advanced-analysis': AdvancedAnalysisPage,
  '/story-wall': StoryWallPage,
  '/questionnaire-voices': QuestionnaireVoicesPage,
  '/questionnaire-voices-2': QuestionnaireVoices2Page,
  '/test-data-generator': TestDataGeneratorPage,
  '/story/:storyId': StoryDetailPage,
  '/submit-success': SubmitSuccessPage,
  '/my-content': MyContentPage,
  '/admin': AdminLoginPage,
  '/admin/login': AdminLoginPage,
  '/temp-login': TempLoginPage,
  '/reviewer/login': ReviewerLoginPage,
  '/reviewer/dashboard': ReviewerDashboardPage,
  '/reviewer/story-review': ReviewerStoryReviewPage,
  '/reviewer/content-review': ReviewerContentReviewPage,
  '/reviewer/quick-review': ReviewerQuickReviewPage,
  '/reviewer/settings': ReviewerSettingsPage,
  '/reviewer/batch/stories': BatchStoryReviewPage,
  '/reviewer/batch/voices': BatchVoiceReviewPage,
  '/admin/dashboard': AdminDashboardHomePage,
  '/admin/story-review': StoryReviewPage,
  '/admin/content-review': ContentReviewPage,
  '/admin/comment-review': CommentReviewPage,
  '/admin/quick-review': QuickReviewPage,
  '/admin/tag-management': TagManagementPage,
  '/admin/questionnaire-responses': QuestionnaireResponsesPage,
  '/admin/data-analysis': DataAnalysisPage,
  '/admin/user-management': UserManagementPage,
  '/admin/reviewer-management': ReviewerManagementPage,
  '/admin/deidentification-settings': DeidentificationSettingsPage,
  '/admin/settings': AdminSettingsPage,
  '/admin/security-settings': SecuritySettingsPage,
  '/admin/security-logs': SecurityLogsPage,
  '/superadmin/dashboard': SuperAdminDashboardPage,
  '/superadmin/story-review': SuperAdminStoryReviewPage,
  '/superadmin/content-review': SuperAdminContentReviewPage,
  '/superadmin/role-management': RoleManagementPage,
  '/superadmin/comment-review': SuperAdminCommentReviewPage,
  '/superadmin/quick-review': SuperAdminQuickReviewPage,
  '/superadmin/tag-management': SuperAdminTagManagementPage,
  '/superadmin/questionnaire-responses': QuestionnaireResponsesPage,
  '/superadmin/data-analysis': DataAnalysisPage,
  '/superadmin/user-management': SuperAdminUserManagementPage,
  '/superadmin/reviewer-management': SuperAdminReviewerManagementPage,
  '/superadmin/admin-management': SuperAdminAdminManagementPage,
  '/superadmin/deidentification-settings': SuperAdminDeidentificationSettingsPage,
  '/superadmin/security-settings': SecuritySettingsPage,
  '/superadmin/security-logs': SecurityLogsPage,
  '/superadmin/settings': SuperAdminSettingsPage,
  '/superadmin/system-monitor': SystemMonitorPage,
  '/superadmin/data-restore': DataRestorePage,
  '/superadmin/documentation': DocumentationPage,
  '/superadmin/security': SecurityPage,
  '/superadmin/admin-audit': AdminAuditPage,
  '/superadmin/security-monitor': SecurityMonitorPage,
  '/superadmin/platform-overview': PlatformOverviewPage,
  '/superadmin/system-config': SystemConfigPage,
  '/superadmin/enhanced-data-analysis': EnhancedDataAnalysisPage,
  '/superadmin/enhanced-security-monitor': EnhancedSecurityMonitorPage,
  '/superadmin/system-backup': SystemBackupPage,
  '/superadmin/user-batch': UserBatchPage,
  '/superadmin/system-management': SystemManagementPage,
  '/superadmin/test-data-management': TestDataManagementPage,
  '/superadmin/route-test': RouteTestPage,
  '/superadmin/security-audit': SecurityAuditPage,
  '/superadmin/data-management': DataManagementPage,
  '/superadmin/data-testing': DataTestingPage,
  '/superadmin/operation-logs': lazy(() => import('@/pages/superadmin/OperationLogsPage')),
  '/superadmin/enhanced-content-management': EnhancedContentManagementPage,
  '/superadmin/performance-optimization': PerformanceOptimizationPage,
};

// 优化的路由组件
const OptimizedRoutes: React.FC = () => {
  return (
    <Routes>
      {/* 主站路由 */}
      <Route path="/" element={<Layout />}>
        {/* 确保首页正确加载 */}
        <Route index element={<HomePage />} />
        <Route path="questionnaire" element={<QuestionnairePage />} />
        <Route path="visualization" element={<VisualizationPage />} />
        <Route path="advanced-analysis" element={<AdvancedAnalysisPage />} />
        <Route path="story-wall" element={<StoryWallPage />} />
        <Route path="simple-story-wall" element={<SimpleStoryWall />} />
        <Route path="mobile-story-wall-demo" element={<MobileStoryWallDemo />} />
        <Route path="questionnaire-voices" element={<QuestionnaireVoicesPage />} />
        <Route path="questionnaire-voices-2" element={<QuestionnaireVoices2Page />} />
        <Route path="story/:storyId" element={<StoryDetailPage />} />
        <Route path="submit-success" element={<SubmitSuccessPage />} />
        <Route path="my-content" element={<MyContentPage />} />
        <Route path="test" element={<TestPage />} />
        <Route path="test-simple" element={<TestSimplePage />} />
        <Route path="test-services" element={<TestServicesPage />} />
        <Route path="diagnostics" element={<DiagnosticsPage />} />
        <Route path="data-monitor" element={<DataMonitorPage />} />
        <Route path="test-data-generator" element={<TestDataGeneratorPage />} />
        <Route path="reviewer-dashboard" element={<ReviewerDashboard />} />
      </Route>

      {/* 公共登录页面 - 直接加载而不是懒加载，确保快速访问 */}
      <Route path="/admin" element={<AdminLoginPage />} />
      <Route path="/admin/login" element={<AdminLoginPage />} />
      <Route path="/temp-login" element={<TempLoginPage />} />
      <Route path="/reviewer/login" element={<ReviewerLoginPage />} />

      {/* 添加临时登录页面的别名路由，确保多种URL都能访问 */}
      <Route path="/login" element={<TempLoginPage />} />

      {/* 审核员路由 */}
      <Route path="/reviewer/dashboard" element={
        <PermissionGuard allowedRoles={['reviewer', 'admin', 'superadmin']}>
          <ReviewerDashboardPage />
        </PermissionGuard>
      } />
      {/* 内容审核的三个子页面 */}
      <Route path="/reviewer/questionnaire-voices" element={
        <PermissionGuard allowedRoles={['reviewer', 'admin', 'superadmin']}>
          <ReviewerQuestionnaireVoicesPage />
        </PermissionGuard>
      } />
      <Route path="/reviewer/story-review" element={
        <PermissionGuard allowedRoles={['reviewer', 'admin', 'superadmin']}>
          <ReviewerStoryReviewPage />
        </PermissionGuard>
      } />
      <Route path="/reviewer/quick-review" element={
        <PermissionGuard allowedRoles={['reviewer', 'admin', 'superadmin']}>
          <ReviewerQuickReviewPage />
        </PermissionGuard>
      } />
      {/* 保留原来的content-review路由作为重定向或兼容 */}
      <Route path="/reviewer/content-review" element={
        <PermissionGuard allowedRoles={['reviewer', 'admin', 'superadmin']}>
          <ReviewerQuestionnaireVoicesPage />
        </PermissionGuard>
      } />
      <Route path="/reviewer/settings" element={
        <PermissionGuard allowedRoles={['reviewer', 'admin', 'superadmin']}>
          <ReviewerSettingsPage />
        </PermissionGuard>
      } />
      {/* 批量审核路由 */}
      <Route path="/reviewer/batch/stories" element={
        <PermissionGuard allowedRoles={['reviewer', 'admin', 'superadmin']}>
          <BatchStoryReviewPage />
        </PermissionGuard>
      } />
      <Route path="/reviewer/batch/voices" element={
        <PermissionGuard allowedRoles={['reviewer', 'admin', 'superadmin']}>
          <BatchVoiceReviewPage />
        </PermissionGuard>
      } />
      <Route path="/reviewer/test-batch" element={
        <PermissionGuard allowedRoles={['reviewer', 'admin', 'superadmin']}>
          <TestBatchPage />
        </PermissionGuard>
      } />

      {/* 管理员路由 */}
      <Route path="/admin/dashboard" element={
        <PermissionGuard allowedRoles={['admin', 'superadmin']}>
          <AdminDashboardHomePage />
        </PermissionGuard>
      } />
      <Route path="/admin/story-review" element={
        <PermissionGuard allowedRoles={['admin', 'superadmin']}>
          <StoryReviewPage />
        </PermissionGuard>
      } />
      <Route path="/admin/content-review" element={
        <PermissionGuard allowedRoles={['admin', 'superadmin']}>
          <ContentReviewPage />
        </PermissionGuard>
      } />
      <Route path="/admin/comment-review" element={
        <PermissionGuard allowedRoles={['admin', 'superadmin']}>
          <CommentReviewPage />
        </PermissionGuard>
      } />
      <Route path="/admin/quick-review" element={
        <PermissionGuard allowedRoles={['admin', 'superadmin']}>
          <QuickReviewPage />
        </PermissionGuard>
      } />
      <Route path="/admin/tag-management" element={
        <PermissionGuard allowedRoles={['admin', 'superadmin']}>
          <TagManagementPage />
        </PermissionGuard>
      } />
      <Route path="/admin/questionnaire-responses" element={
        <PermissionGuard allowedRoles={['admin', 'superadmin']}>
          <QuestionnaireResponsesPage />
        </PermissionGuard>
      } />
      <Route path="/admin/data-analysis" element={
        <PermissionGuard allowedRoles={['admin', 'superadmin']}>
          <DataAnalysisPage />
        </PermissionGuard>
      } />
      <Route path="/admin/user-management" element={
        <PermissionGuard allowedRoles={['admin', 'superadmin']}>
          <UserManagementPage />
        </PermissionGuard>
      } />
      <Route path="/admin/reviewer-management" element={
        <PermissionGuard allowedRoles={['admin', 'superadmin']}>
          <ReviewerManagementPage />
        </PermissionGuard>
      } />
      <Route path="/admin/deidentification-settings" element={
        <PermissionGuard allowedRoles={['admin', 'superadmin']}>
          <DeidentificationSettingsPage />
        </PermissionGuard>
      } />
      <Route path="/admin/settings" element={
        <PermissionGuard allowedRoles={['admin', 'superadmin']}>
          <AdminSettingsPage />
        </PermissionGuard>
      } />
      <Route path="/admin/security-settings" element={
        <PermissionGuard allowedRoles={['admin', 'superadmin']}>
          <SecuritySettingsPage />
        </PermissionGuard>
      } />
      <Route path="/admin/security-logs" element={
        <PermissionGuard allowedRoles={['admin', 'superadmin']}>
          <SecurityLogsPage />
        </PermissionGuard>
      } />

      {/* 超级管理员路由 */}
      <Route path="/superadmin/dashboard" element={
        <PermissionGuard allowedRoles={['superadmin']}>
          <SuperAdminDashboardPage />
        </PermissionGuard>
      } />
      <Route path="/superadmin/story-review" element={
        <PermissionGuard allowedRoles={['superadmin']}>
          <SuperAdminStoryReviewPage />
        </PermissionGuard>
      } />
      <Route path="/superadmin/content-review" element={
        <PermissionGuard allowedRoles={['superadmin']}>
          <SuperAdminContentReviewPage />
        </PermissionGuard>
      } />
      <Route path="/superadmin/comment-review" element={
        <PermissionGuard allowedRoles={['superadmin']}>
          <SuperAdminCommentReviewPage />
        </PermissionGuard>
      } />
      <Route path="/superadmin/quick-review" element={
        <PermissionGuard allowedRoles={['superadmin']}>
          <SuperAdminQuickReviewPage />
        </PermissionGuard>
      } />
      <Route path="/superadmin/tag-management" element={
        <PermissionGuard allowedRoles={['superadmin']}>
          <SuperAdminTagManagementPage />
        </PermissionGuard>
      } />
      <Route path="/superadmin/questionnaire-responses" element={
        <PermissionGuard allowedRoles={['superadmin']}>
          <QuestionnaireResponsesPage />
        </PermissionGuard>
      } />
      <Route path="/superadmin/data-analysis" element={
        <PermissionGuard allowedRoles={['superadmin']}>
          <DataAnalysisPage />
        </PermissionGuard>
      } />
      <Route path="/superadmin/user-management" element={
        <PermissionGuard allowedRoles={['superadmin']}>
          <SuperAdminUserManagementPage />
        </PermissionGuard>
      } />
      <Route path="/superadmin/reviewer-management" element={
        <PermissionGuard allowedRoles={['superadmin']}>
          <SuperAdminReviewerManagementPage />
        </PermissionGuard>
      } />
      <Route path="/superadmin/admin-management" element={
        <PermissionGuard allowedRoles={['superadmin']}>
          <SuperAdminAdminManagementPage />
        </PermissionGuard>
      } />
      <Route path="/superadmin/deidentification-settings" element={
        <PermissionGuard allowedRoles={['superadmin']}>
          <SuperAdminDeidentificationSettingsPage />
        </PermissionGuard>
      } />
      <Route path="/superadmin/security-settings" element={
        <PermissionGuard allowedRoles={['superadmin']}>
          <SecuritySettingsPage />
        </PermissionGuard>
      } />
      <Route path="/superadmin/security-logs" element={
        <PermissionGuard allowedRoles={['superadmin']}>
          <SecurityLogsPage />
        </PermissionGuard>
      } />
      <Route path="/superadmin/settings" element={
        <PermissionGuard allowedRoles={['superadmin']}>
          <SuperAdminSettingsPage />
        </PermissionGuard>
      } />
      <Route path="/superadmin/system-monitor" element={
        <PermissionGuard allowedRoles={['superadmin']}>
          <SystemMonitorPage />
        </PermissionGuard>
      } />
      <Route path="/superadmin/data-restore" element={
        <PermissionGuard allowedRoles={['superadmin']}>
          <DataRestorePage />
        </PermissionGuard>
      } />
      <Route path="/superadmin/documentation" element={
        <PermissionGuard allowedRoles={['superadmin']}>
          <DocumentationPage />
        </PermissionGuard>
      } />
      <Route path="/superadmin/security" element={
        <PermissionGuard allowedRoles={['superadmin']}>
          <SecurityPage />
        </PermissionGuard>
      } />
      <Route path="/superadmin/admin-audit" element={
        <PermissionGuard allowedRoles={['superadmin']}>
          <AdminAuditPage />
        </PermissionGuard>
      } />
      <Route path="/superadmin/role-management" element={
        <PermissionGuard allowedRoles={['superadmin']}>
          <RoleManagementPage />
        </PermissionGuard>
      } />
      <Route path="/superadmin/security-monitor" element={
        <PermissionGuard allowedRoles={['superadmin']}>
          <SecurityMonitorPage />
        </PermissionGuard>
      } />
      <Route path="/superadmin/platform-overview" element={
        <PermissionGuard allowedRoles={['superadmin']}>
          <PlatformOverviewPage />
        </PermissionGuard>
      } />
      <Route path="/superadmin/system-config" element={
        <PermissionGuard allowedRoles={['superadmin']}>
          <SystemConfigPage />
        </PermissionGuard>
      } />
      <Route path="/superadmin/enhanced-data-analysis" element={
        <PermissionGuard allowedRoles={['superadmin']}>
          <EnhancedDataAnalysisPage />
        </PermissionGuard>
      } />
      <Route path="/superadmin/enhanced-security-monitor" element={
        <PermissionGuard allowedRoles={['superadmin']}>
          <EnhancedSecurityMonitorPage />
        </PermissionGuard>
      } />
      <Route path="/superadmin/system-backup" element={
        <PermissionGuard allowedRoles={['superadmin']}>
          <SystemBackupPage />
        </PermissionGuard>
      } />
      <Route path="/superadmin/user-batch" element={
        <PermissionGuard allowedRoles={['superadmin']}>
          <UserBatchPage />
        </PermissionGuard>
      } />
      <Route path="/superadmin/system-management" element={
        <PermissionGuard allowedRoles={['superadmin']}>
          <SystemManagementPage />
        </PermissionGuard>
      } />
      <Route path="/superadmin/test-data-management" element={
        <PermissionGuard allowedRoles={['superadmin']}>
          <TestDataManagementPage />
        </PermissionGuard>
      } />
      <Route path="/superadmin/route-test" element={
        <PermissionGuard allowedRoles={['superadmin']}>
          <RouteTestPage />
        </PermissionGuard>
      } />
      <Route path="/superadmin/security-audit" element={
        <PermissionGuard allowedRoles={['superadmin']}>
          <SecurityAuditPage />
        </PermissionGuard>
      } />
      <Route path="/superadmin/data-management" element={
        <PermissionGuard allowedRoles={['superadmin']}>
          <DataManagementPage />
        </PermissionGuard>
      } />
      <Route path="/superadmin/data-testing" element={
        <PermissionGuard allowedRoles={['superadmin']}>
          <DataTestingPage />
        </PermissionGuard>
      } />
      <Route path="/superadmin/operation-logs" element={
        <PermissionGuard allowedRoles={['superadmin']}>
          <Suspense fallback={<div>加载中...</div>}>
            {React.createElement(routeComponents['/superadmin/operation-logs'])}
          </Suspense>
        </PermissionGuard>
      } />
      <Route path="/superadmin/enhanced-content-management" element={
        <PermissionGuard allowedRoles={['superadmin']}>
          <EnhancedContentManagementPage />
        </PermissionGuard>
      } />
      <Route path="/superadmin/performance-optimization" element={
        <PermissionGuard allowedRoles={['superadmin']}>
          <PerformanceOptimizationPage />
        </PermissionGuard>
      } />

      {/* 404 页面 - 放在最后 */}
      <Route path="*" element={<NotFoundPage />} />
    </Routes>
  );
};

export default OptimizedRoutes;
