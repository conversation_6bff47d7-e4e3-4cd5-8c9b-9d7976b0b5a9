name: 🔍 Code Quality & Testing

on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main ]

env:
  NODE_VERSION: '18'

jobs:
  # 代码质量检查
  code-quality:
    name: 📋 Code Quality
    runs-on: ubuntu-latest

    steps:
    - name: 📥 Checkout code
      uses: actions/checkout@v4

    - name: 📦 Setup Node.js
      uses: actions/setup-node@v4
      with:
        node-version: ${{ env.NODE_VERSION }}
        cache: 'npm'
        cache-dependency-path: |
          frontend/package-lock.json
          backend/package-lock.json

    - name: 📥 Install backend dependencies
      run: |
        cd backend
        npm ci

    - name: 🔍 ESLint check
      run: |
        cd backend
        npm run lint

    - name: 🎨 Prettier check
      run: |
        cd backend
        npm run format:check

    - name: 🔍 Run duplicate detection
      run: |
        cd backend
        if [ -f "scripts/detect-duplicates.js" ]; then
          node scripts/detect-duplicates.js || echo "Duplicate detection completed with warnings"
        fi

    - name: 📊 Upload code quality artifacts
      uses: actions/upload-artifact@v4
      if: always()
      with:
        name: code-quality-reports
        path: |
          backend/duplicate-report.json
          backend/eslint-report.json
        retention-days: 7

  # 单元测试
  unit-tests:
    name: 🧪 Unit Tests
    runs-on: ubuntu-latest
    needs: code-quality

    steps:
    - name: 📥 Checkout code
      uses: actions/checkout@v4

    - name: 📦 Setup Node.js
      uses: actions/setup-node@v4
      with:
        node-version: ${{ env.NODE_VERSION }}
        cache: 'npm'
        cache-dependency-path: backend/package-lock.json

    - name: 📥 Install dependencies
      run: |
        cd backend
        npm ci

    - name: 🧪 Run unit tests
      run: |
        cd backend
        npm run test:unit

    - name: 📊 Generate coverage report
      run: |
        cd backend
        npm run test:coverage

    - name: 📈 Upload coverage to Codecov
      uses: codecov/codecov-action@v4
      with:
        file: ./backend/coverage/lcov.info
        directory: ./backend/coverage/
        flags: backend
        name: backend-coverage

    - name: 📊 Upload test artifacts
      uses: actions/upload-artifact@v4
      if: always()
      with:
        name: test-reports
        path: |
          backend/coverage/
          backend/test-results.xml
        retention-days: 7

  # 集成测试
  integration-tests:
    name: 🔗 Integration Tests
    runs-on: ubuntu-latest
    needs: unit-tests

    steps:
    - name: 📥 Checkout code
      uses: actions/checkout@v4

    - name: 📦 Setup Node.js
      uses: actions/setup-node@v4
      with:
        node-version: ${{ env.NODE_VERSION }}
        cache: 'npm'
        cache-dependency-path: backend/package-lock.json

    - name: 📥 Install dependencies
      run: |
        cd backend
        npm ci

    - name: 🔗 Run integration tests
      run: |
        cd backend
        npm run test:integration

  # 端到端测试
  e2e-tests:
    name: 🌐 E2E Tests
    runs-on: ubuntu-latest
    needs: integration-tests
    if: github.event_name == 'push' || github.event.pull_request.draft == false

    steps:
    - name: 📥 Checkout code
      uses: actions/checkout@v4

    - name: 📦 Setup Node.js
      uses: actions/setup-node@v4
      with:
        node-version: ${{ env.NODE_VERSION }}
        cache: 'npm'
        cache-dependency-path: backend/package-lock.json

    - name: 📥 Install dependencies
      run: |
        cd backend
        npm ci

    - name: 🌐 Run E2E tests
      run: |
        cd backend
        API_BASE_URL=https://college-employment-survey.aibook2099.workers.dev npm run test:e2e
      env:
        API_BASE_URL: https://college-employment-survey.aibook2099.workers.dev

  # PR评论和质量报告
  quality-report:
    name: 📊 Quality Report
    runs-on: ubuntu-latest
    needs: [code-quality, unit-tests, integration-tests]
    if: github.event_name == 'pull_request'

    steps:
    - name: 📥 Download artifacts
      uses: actions/download-artifact@v4
      with:
        name: code-quality-reports
        path: ./reports/

    - name: 📥 Download test reports
      uses: actions/download-artifact@v4
      with:
        name: test-reports
        path: ./test-reports/

    - name: 💬 Comment PR with quality report
      uses: actions/github-script@v7
      with:
        script: |
          const fs = require('fs');

          let comment = '## 🔍 Code Quality & Test Report\n\n';

          // 读取重复检测报告
          try {
            if (fs.existsSync('./reports/duplicate-report.json')) {
              const report = JSON.parse(fs.readFileSync('./reports/duplicate-report.json', 'utf8'));
              comment += `### 📊 代码重复检测\n`;
              comment += `- 总文件数: ${report.summary?.totalFiles || 'N/A'}\n`;
              comment += `- 重复项: ${report.duplicates?.length || 0}\n`;
              comment += `- 警告数: ${report.warnings?.length || 0}\n\n`;

              if (report.duplicates && report.duplicates.length > 0) {
                comment += `⚠️ **发现 ${report.duplicates.length} 个重复项**\n\n`;
              } else {
                comment += `✅ **未发现严重重复问题**\n\n`;
              }
            }
          } catch (e) {
            comment += `### 📊 代码重复检测\n❌ 无法读取重复检测报告\n\n`;
          }

          // 测试结果
          comment += `### 🧪 测试结果\n`;
          comment += `- ✅ 代码质量检查: 通过\n`;
          comment += `- ✅ 单元测试: 通过\n`;
          comment += `- ✅ 集成测试: 通过\n`;

          // 添加改进建议
          comment += `\n### 💡 质量保证建议\n`;
          comment += `1. 🔍 定期运行 \`npm run quality:check\` 检查代码质量\n`;
          comment += `2. 🧪 确保测试覆盖率保持在80%以上\n`;
          comment += `3. 🎨 使用 \`npm run format\` 保持代码格式一致\n`;
          comment += `4. 📋 遵循ESLint规则，避免代码质量问题\n`;

          github.rest.issues.createComment({
            issue_number: context.issue.number,
            owner: context.repo.owner,
            repo: context.repo.repo,
            body: comment
          });

  # 质量门禁
  quality-gate:
    name: 🚪 Quality Gate
    runs-on: ubuntu-latest
    needs: [code-quality, unit-tests, integration-tests]
    if: always()

    steps:
    - name: 📥 Download artifacts
      uses: actions/download-artifact@v4
      with:
        name: code-quality-reports
        path: ./reports/
      continue-on-error: true

    - name: 🚪 Check quality gate
      run: |
        echo "🔍 Checking quality gate criteria..."

        # 检查是否有严重的代码重复
        if [ -f "./reports/duplicate-report.json" ]; then
          DUPLICATES=$(jq '.duplicates | length' ./reports/duplicate-report.json 2>/dev/null || echo "0")
          echo "发现重复项: $DUPLICATES"

          if [ "$DUPLICATES" -gt 20 ]; then
            echo "❌ 质量门禁失败: 发现过多重复代码 ($DUPLICATES 个)"
            echo "请先清理重复代码再提交"
            exit 1
          fi
        fi

        # 检查必要的作业是否成功
        if [ "${{ needs.code-quality.result }}" != "success" ]; then
          echo "❌ 质量门禁失败: 代码质量检查未通过"
          exit 1
        fi

        if [ "${{ needs.unit-tests.result }}" != "success" ]; then
          echo "❌ 质量门禁失败: 单元测试未通过"
          exit 1
        fi

        if [ "${{ needs.integration-tests.result }}" != "success" ]; then
          echo "❌ 质量门禁失败: 集成测试未通过"
          exit 1
        fi

        echo "✅ 质量门禁通过!"
