import React, { useState } from 'react';
import {
  <PERSON>,
  Card<PERSON><PERSON>nt,
  Card<PERSON><PERSON>er,
  CardTitle,
  CardDescription,
  CardFooter
} from '@/components/ui/card';
import {
  Ta<PERSON>,
  TabsContent,
  <PERSON><PERSON>List,
  TabsTrigger
} from '@/components/ui/tabs';
import {
  Alert,
  AlertTitle,
  AlertDescription
} from '@/components/ui/alert';
import { Button } from '@/components/ui/button';
import { Textarea } from '@/components/ui/textarea';
import { Badge } from '@/components/ui/badge';
import { Skeleton } from '@/components/ui/skeleton';
import { useToast } from '@/components/ui/use-toast';
import { Shield, AlertTriangle, CheckCircle, XCircle, Loader2 } from 'lucide-react';
import { useApi } from '@/hooks/useApi';

import { ContentModerationResult } from '@/hooks/useContentModeration';

interface ContentModerationPanelProps {
  onResult?: (result: ContentModerationResult) => void;
  initialContent?: string;
  contentType?: 'story' | 'questionnaire' | 'comment';
  contentId?: string;
  autoModerate?: boolean;
}

/**
 * 内容审核面板
 *
 * 用于审核内容是否包含敏感或不适当的信息
 */
const ContentModerationPanel: React.FC<ContentModerationPanelProps> = ({
  onResult,
  initialContent = '',
  contentType,
  contentId,
  autoModerate = false
}) => {
  const { toast } = useToast();
  const api = useApi();
  const [content, setContent] = useState<string>(initialContent);
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [result, setResult] = useState<ContentModerationResult | null>(null);
  const [error, setError] = useState<string | null>(null);

  // 自动审核
  React.useEffect(() => {
    if (autoModerate && initialContent && initialContent.trim().length > 0) {
      moderateContent();
    }
  }, [autoModerate, initialContent]);

  // 审核内容
  const moderateContent = async () => {
    if (!content || content.trim().length === 0) {
      toast({
        title: '内容为空',
        description: '请输入要审核的内容',
        variant: 'destructive'
      });
      return;
    }

    setIsLoading(true);
    setError(null);

    try {
      const response = await api.post('/admin/content-moderation/moderate', {
        content,
        contentType,
        contentId
      });

      if (response.success && response.result) {
        setResult(response.result);

        if (onResult) {
          onResult(response.result);
        }
      } else {
        setError(response.error || '内容审核失败');
      }
    } catch (error) {
      console.error('内容审核失败:', error);
      setError('内容审核服务暂时不可用');
    } finally {
      setIsLoading(false);
    }
  };

  // 渲染结果标签
  const renderResultBadge = () => {
    if (!result) return null;

    if (result.suggestedAction === 'approve') {
      return (
        <Badge variant="outline" className="bg-green-50 text-green-700 border-green-200">
          <CheckCircle className="w-4 h-4 mr-1" />
          通过
        </Badge>
      );
    } else if (result.suggestedAction === 'reject') {
      return (
        <Badge variant="outline" className="bg-red-50 text-red-700 border-red-200">
          <XCircle className="w-4 h-4 mr-1" />
          拒绝
        </Badge>
      );
    } else {
      return (
        <Badge variant="outline" className="bg-yellow-50 text-yellow-700 border-yellow-200">
          <AlertTriangle className="w-4 h-4 mr-1" />
          人工审核
        </Badge>
      );
    }
  };

  return (
    <Card>
      <CardHeader>
        <div className="flex items-center justify-between">
          <div>
            <CardTitle className="flex items-center">
              <Shield className="w-5 h-5 mr-2" />
              内容审核
            </CardTitle>
            <CardDescription>
              使用AI检测内容是否包含敏感或不适当的信息
            </CardDescription>
          </div>
          {renderResultBadge()}
        </div>
      </CardHeader>
      <CardContent>
        {error && (
          <Alert variant="destructive" className="mb-4">
            <AlertTriangle className="h-4 w-4" />
            <AlertTitle>审核失败</AlertTitle>
            <AlertDescription>{error}</AlertDescription>
          </Alert>
        )}

        <Tabs defaultValue="input" className="w-full">
          <TabsList className="grid w-full grid-cols-2">
            <TabsTrigger value="input">内容输入</TabsTrigger>
            <TabsTrigger value="result" disabled={!result}>审核结果</TabsTrigger>
          </TabsList>

          <TabsContent value="input" className="space-y-4">
            <Textarea
              placeholder="输入要审核的内容..."
              className="min-h-[200px]"
              value={content}
              onChange={(e) => setContent(e.target.value)}
              disabled={isLoading}
            />
          </TabsContent>

          <TabsContent value="result" className="space-y-4">
            {isLoading ? (
              <div className="space-y-2">
                <Skeleton className="h-4 w-full" />
                <Skeleton className="h-4 w-full" />
                <Skeleton className="h-4 w-3/4" />
              </div>
            ) : result ? (
              <div className="space-y-4">
                <div>
                  <h4 className="text-sm font-medium mb-1">审核结果</h4>
                  <p className="text-sm">
                    {result.isSafe ? (
                      <span className="text-green-600">内容安全，可以发布</span>
                    ) : (
                      <span className="text-red-600">内容可能不适合发布</span>
                    )}
                  </p>
                </div>

                {result.issues.length > 0 && (
                  <div>
                    <h4 className="text-sm font-medium mb-1">发现的问题</h4>
                    <ul className="list-disc list-inside text-sm">
                      {result.issues.map((issue, index) => (
                        <li key={index} className="text-red-600">{issue}</li>
                      ))}
                    </ul>
                  </div>
                )}

                <div>
                  <h4 className="text-sm font-medium mb-1">解释</h4>
                  <p className="text-sm">{result.explanation}</p>
                </div>

                <div>
                  <h4 className="text-sm font-medium mb-1">置信度</h4>
                  <div className="w-full bg-gray-200 rounded-full h-2.5">
                    <div
                      className={`h-2.5 rounded-full ${
                        result.confidence > 0.7 ? 'bg-green-600' :
                        result.confidence > 0.4 ? 'bg-yellow-400' : 'bg-red-600'
                      }`}
                      style={{ width: `${result.confidence * 100}%` }}
                    ></div>
                  </div>
                  <p className="text-xs text-right mt-1">{Math.round(result.confidence * 100)}%</p>
                </div>

                <div>
                  <h4 className="text-sm font-medium mb-1">建议操作</h4>
                  <p className="text-sm">
                    {result.suggestedAction === 'approve' && '通过内容'}
                    {result.suggestedAction === 'reject' && '拒绝内容'}
                    {result.suggestedAction === 'review' && '人工审核'}
                  </p>
                </div>

                {result.severity && (
                  <div>
                    <h4 className="text-sm font-medium mb-1">问题严重程度</h4>
                    <Badge variant="outline" className={`
                      ${result.severity === 'low' ? 'bg-yellow-50 text-yellow-700 border-yellow-200' :
                        result.severity === 'medium' ? 'bg-orange-50 text-orange-700 border-orange-200' :
                        'bg-red-50 text-red-700 border-red-200'}
                    `}>
                      {result.severity === 'low' && '低'}
                      {result.severity === 'medium' && '中'}
                      {result.severity === 'high' && '高'}
                    </Badge>
                  </div>
                )}

                {result.dataQuality && (
                  <div>
                    <h4 className="text-sm font-medium mb-1">数据质量</h4>
                    <Badge variant="outline" className={`
                      ${result.dataQuality === 'high' ? 'bg-green-50 text-green-700 border-green-200' :
                        result.dataQuality === 'medium' ? 'bg-blue-50 text-blue-700 border-blue-200' :
                        'bg-gray-50 text-gray-700 border-gray-200'}
                    `}>
                      {result.dataQuality === 'low' && '低'}
                      {result.dataQuality === 'medium' && '中'}
                      {result.dataQuality === 'high' && '高'}
                    </Badge>
                  </div>
                )}

                {result.constructiveValue && (
                  <div>
                    <h4 className="text-sm font-medium mb-1">建设性价值</h4>
                    <Badge variant="outline" className={`
                      ${result.constructiveValue === 'high' ? 'bg-green-50 text-green-700 border-green-200' :
                        result.constructiveValue === 'medium' ? 'bg-blue-50 text-blue-700 border-blue-200' :
                        result.constructiveValue === 'low' ? 'bg-gray-50 text-gray-700 border-gray-200' :
                        'bg-red-50 text-red-700 border-red-200'}
                    `}>
                      {result.constructiveValue === 'none' && '无'}
                      {result.constructiveValue === 'low' && '低'}
                      {result.constructiveValue === 'medium' && '中'}
                      {result.constructiveValue === 'high' && '高'}
                    </Badge>
                  </div>
                )}

                {result.storyValue && (
                  <div>
                    <h4 className="text-sm font-medium mb-1">故事价值</h4>
                    <Badge variant="outline" className={`
                      ${result.storyValue === 'high' ? 'bg-green-50 text-green-700 border-green-200' :
                        result.storyValue === 'medium' ? 'bg-blue-50 text-blue-700 border-blue-200' :
                        result.storyValue === 'low' ? 'bg-gray-50 text-gray-700 border-gray-200' :
                        'bg-red-50 text-red-700 border-red-200'}
                    `}>
                      {result.storyValue === 'none' && '无'}
                      {result.storyValue === 'low' && '低'}
                      {result.storyValue === 'medium' && '中'}
                      {result.storyValue === 'high' && '高'}
                    </Badge>
                  </div>
                )}
              </div>
            ) : (
              <p className="text-sm text-gray-500">点击"审核内容"按钮开始审核</p>
            )}
          </TabsContent>
        </Tabs>
      </CardContent>
      <CardFooter className="flex justify-end">
        <Button
          onClick={moderateContent}
          disabled={isLoading || !content}
        >
          {isLoading ? (
            <>
              <Loader2 className="mr-2 h-4 w-4 animate-spin" />
              审核中...
            </>
          ) : (
            <>
              <Shield className="mr-2 h-4 w-4" />
              审核内容
            </>
          )}
        </Button>
      </CardFooter>
    </Card>
  );
};

export default ContentModerationPanel;
