import { Context, <PERSON>wareH<PERSON><PERSON>, Next } from 'hono';

interface RateLimitOptions {
  limit: number;      // Maximum number of requests
  window: number;     // Time window in seconds
  keyGenerator?: (c: Context) => string; // Function to generate a unique key for the request
}

export const rateLimit = (options: RateLimitOptions): MiddlewareHandler => {
  const { limit, window } = options;
  
  return async (c: Context, next: Next) => {
    const ip = c.req.header('CF-Connecting-IP') || c.req.header('X-Forwarded-For') || 'unknown';
    const path = new URL(c.req.url).pathname;
    
    // Generate a unique key for this request
    const key = options.keyGenerator 
      ? options.keyGenerator(c) 
      : `rate-limit:${ip}:${path}`;
    
    // Get the KV namespace
    const kv = c.env.SURVEY_KV;
    
    // Get current count and timestamp
    const rateData = await kv.get(key, 'json') as { count: number, timestamp: number } | null;
    
    const now = Math.floor(Date.now() / 1000);
    
    if (rateData) {
      // Check if the window has expired
      if (now - rateData.timestamp > window) {
        // Reset the counter
        await kv.put(key, JSON.stringify({ count: 1, timestamp: now }), { expirationTtl: window });
      } else if (rateData.count >= limit) {
        // Rate limit exceeded
        return c.json({
          error: 'Rate limit exceeded',
          retryAfter: window - (now - rateData.timestamp)
        }, 429);
      } else {
        // Increment the counter
        await kv.put(key, JSON.stringify({ 
          count: rateData.count + 1, 
          timestamp: rateData.timestamp 
        }), { expirationTtl: window });
      }
    } else {
      // First request in this window
      await kv.put(key, JSON.stringify({ count: 1, timestamp: now }), { expirationTtl: window });
    }
    
    // Add rate limit headers
    if (rateData) {
      c.header('X-RateLimit-Limit', limit.toString());
      c.header('X-RateLimit-Remaining', Math.max(0, limit - (rateData.count + 1)).toString());
      c.header('X-RateLimit-Reset', (rateData.timestamp + window).toString());
    }
    
    await next();
  };
};
