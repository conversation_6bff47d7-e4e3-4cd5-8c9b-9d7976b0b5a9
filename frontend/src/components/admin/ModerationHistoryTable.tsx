import React, { useState, useEffect } from 'react';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Pagination } from '@/components/ui/pagination';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Skeleton } from '@/components/ui/skeleton';
import { useToast } from '@/components/ui/use-toast';
import {
  CheckCircle,
  XCircle,
  AlertTriangle,
  Search,
  Calendar,
  RefreshCw,
  FileText,
  User,
  Tag
} from 'lucide-react';
import { format } from 'date-fns';
import { useApi } from '@/hooks/useApi';

interface ModerationHistoryItem {
  id: string;
  contentType: string;
  contentId: string;
  reviewerId: string;
  isSafe: boolean;
  issues: string;
  confidence: number;
  explanation: string;
  suggestedAction: string;
  ipAddress?: string;
  userAgent?: string;
  createdAt: string;
}

interface ModerationHistoryTableProps {
  initialContentType?: string;
  initialContentId?: string;
  initialReviewerId?: string;
}

/**
 * 内容审核历史表格组件
 *
 * 用于显示内容审核历史记录
 */
const ModerationHistoryTable: React.FC<ModerationHistoryTableProps> = ({
  initialContentType,
  initialContentId,
  initialReviewerId
}) => {
  const { toast } = useToast();
  const api = useApi();
  const [isLoading, setIsLoading] = useState<boolean>(true);
  const [history, setHistory] = useState<ModerationHistoryItem[]>([]);
  const [pagination, setPagination] = useState({
    page: 1,
    pageSize: 10,
    total: 0,
    totalPages: 0
  });

  // 筛选条件
  const [contentType, setContentType] = useState<string>(initialContentType || '');
  const [contentId, setContentId] = useState<string>(initialContentId || '');
  const [reviewerId, setReviewerId] = useState<string>(initialReviewerId || '');
  const [suggestedAction, setSuggestedAction] = useState<string>('');
  const [startDate, setStartDate] = useState<string>('');
  const [endDate, setEndDate] = useState<string>('');

  // 加载审核历史
  const loadHistory = async (page = 1) => {
    setIsLoading(true);

    try {
      // 构建查询参数
      const params = new URLSearchParams();
      params.append('page', page.toString());
      params.append('pageSize', pagination.pageSize.toString());

      if (contentType) {
        params.append('contentType', contentType);
      }

      if (contentId) {
        params.append('contentId', contentId);
      }

      if (reviewerId) {
        params.append('reviewerId', reviewerId);
      }

      if (suggestedAction) {
        params.append('suggestedAction', suggestedAction);
      }

      if (startDate) {
        params.append('startDate', startDate);
      }

      if (endDate) {
        params.append('endDate', endDate);
      }

      // 发送请求
      const response = await api.get(`/admin/content-moderation/history?${params.toString()}`);

      if (response.success) {
        setHistory(response.history);
        setPagination({
          page: response.pagination.page,
          pageSize: response.pagination.pageSize,
          total: response.pagination.total,
          totalPages: response.pagination.totalPages
        });
      } else {
        toast({
          title: '加载失败',
          description: response.error || '无法加载审核历史',
          variant: 'destructive'
        });
      }
    } catch (error) {
      console.error('加载审核历史失败:', error);
      toast({
        title: '加载失败',
        description: '服务器错误，请稍后再试',
        variant: 'destructive'
      });
    } finally {
      setIsLoading(false);
    }
  };

  // 处理筛选
  const handleFilter = () => {
    loadHistory(1);
  };

  // 处理重置筛选
  const handleResetFilter = () => {
    setContentType('');
    setContentId('');
    setReviewerId('');
    setSuggestedAction('');
    setStartDate('');
    setEndDate('');

    // 重新加载数据
    setTimeout(() => {
      loadHistory(1);
    }, 0);
  };

  // 处理分页
  const handlePageChange = (page: number) => {
    loadHistory(page);
  };

  // 组件加载时加载数据
  useEffect(() => {
    loadHistory();
  }, []);

  // 渲染操作建议标签
  const renderActionBadge = (action: string) => {
    switch (action) {
      case 'approve':
        return (
          <Badge variant="outline" className="bg-green-50 text-green-700 border-green-200">
            <CheckCircle className="w-3 h-3 mr-1" />
            通过
          </Badge>
        );
      case 'reject':
        return (
          <Badge variant="outline" className="bg-red-50 text-red-700 border-red-200">
            <XCircle className="w-3 h-3 mr-1" />
            拒绝
          </Badge>
        );
      case 'review':
        return (
          <Badge variant="outline" className="bg-yellow-50 text-yellow-700 border-yellow-200">
            <AlertTriangle className="w-3 h-3 mr-1" />
            人工审核
          </Badge>
        );
      default:
        return (
          <Badge variant="outline">
            {action}
          </Badge>
        );
    }
  };

  // 渲染内容类型标签
  const renderContentTypeBadge = (type: string) => {
    switch (type) {
      case 'story':
        return (
          <Badge variant="outline" className="bg-blue-50 text-blue-700 border-blue-200">
            <FileText className="w-3 h-3 mr-1" />
            故事
          </Badge>
        );
      case 'questionnaire':
        return (
          <Badge variant="outline" className="bg-purple-50 text-purple-700 border-purple-200">
            <FileText className="w-3 h-3 mr-1" />
            问卷
          </Badge>
        );
      case 'comment':
        return (
          <Badge variant="outline" className="bg-gray-50 text-gray-700 border-gray-200">
            <FileText className="w-3 h-3 mr-1" />
            评论
          </Badge>
        );
      case 'profile':
        return (
          <Badge variant="outline" className="bg-indigo-50 text-indigo-700 border-indigo-200">
            <User className="w-3 h-3 mr-1" />
            个人资料
          </Badge>
        );
      case 'feedback':
        return (
          <Badge variant="outline" className="bg-teal-50 text-teal-700 border-teal-200">
            <Tag className="w-3 h-3 mr-1" />
            反馈
          </Badge>
        );
      default:
        return (
          <Badge variant="outline">
            {type}
          </Badge>
        );
    }
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle>内容审核历史</CardTitle>
        <CardDescription>
          查看AI内容审核的历史记录和结果
        </CardDescription>
      </CardHeader>
      <CardContent>
        {/* 筛选条件 */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
          <div>
            <label className="text-sm font-medium mb-1 block">内容类型</label>
            <Select value={contentType} onValueChange={setContentType}>
              <SelectTrigger>
                <SelectValue placeholder="所有类型" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="">所有类型</SelectItem>
                <SelectItem value="story">故事</SelectItem>
                <SelectItem value="questionnaire">问卷</SelectItem>
                <SelectItem value="comment">评论</SelectItem>
                <SelectItem value="profile">个人资料</SelectItem>
                <SelectItem value="feedback">反馈</SelectItem>
              </SelectContent>
            </Select>
          </div>

          <div>
            <label className="text-sm font-medium mb-1 block">内容ID</label>
            <Input
              placeholder="输入内容ID"
              value={contentId}
              onChange={(e) => setContentId(e.target.value)}
            />
          </div>

          <div>
            <label className="text-sm font-medium mb-1 block">审核员ID</label>
            <Input
              placeholder="输入审核员ID"
              value={reviewerId}
              onChange={(e) => setReviewerId(e.target.value)}
            />
          </div>

          <div>
            <label className="text-sm font-medium mb-1 block">建议操作</label>
            <Select value={suggestedAction} onValueChange={setSuggestedAction}>
              <SelectTrigger>
                <SelectValue placeholder="所有操作" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="">所有操作</SelectItem>
                <SelectItem value="approve">通过</SelectItem>
                <SelectItem value="reject">拒绝</SelectItem>
                <SelectItem value="review">人工审核</SelectItem>
              </SelectContent>
            </Select>
          </div>

          <div>
            <label className="text-sm font-medium mb-1 block">开始日期</label>
            <div className="flex items-center">
              <Calendar className="w-4 h-4 mr-2 text-muted-foreground" />
              <Input
                type="date"
                value={startDate}
                onChange={(e) => setStartDate(e.target.value)}
              />
            </div>
          </div>

          <div>
            <label className="text-sm font-medium mb-1 block">结束日期</label>
            <div className="flex items-center">
              <Calendar className="w-4 h-4 mr-2 text-muted-foreground" />
              <Input
                type="date"
                value={endDate}
                onChange={(e) => setEndDate(e.target.value)}
              />
            </div>
          </div>
        </div>

        <div className="flex justify-between mb-6">
          <Button variant="outline" onClick={handleResetFilter}>
            重置筛选
          </Button>
          <Button onClick={handleFilter}>
            <Search className="w-4 h-4 mr-2" />
            筛选
          </Button>
        </div>

        {/* 表格 */}
        {isLoading ? (
          <div className="space-y-2">
            <Skeleton className="h-8 w-full" />
            <Skeleton className="h-8 w-full" />
            <Skeleton className="h-8 w-full" />
            <Skeleton className="h-8 w-full" />
            <Skeleton className="h-8 w-full" />
          </div>
        ) : history.length === 0 ? (
          <div className="text-center py-8">
            <p className="text-muted-foreground">暂无审核历史记录</p>
          </div>
        ) : (
          <div className="border rounded-md">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>内容类型</TableHead>
                  <TableHead>内容ID</TableHead>
                  <TableHead>审核结果</TableHead>
                  <TableHead>建议操作</TableHead>
                  <TableHead>置信度</TableHead>
                  <TableHead>审核时间</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {history.map((item) => (
                  <TableRow key={item.id}>
                    <TableCell>{renderContentTypeBadge(item.contentType)}</TableCell>
                    <TableCell className="font-mono text-xs">{item.contentId}</TableCell>
                    <TableCell>
                      {item.isSafe ? (
                        <Badge variant="outline" className="bg-green-50 text-green-700">
                          <CheckCircle className="w-3 h-3 mr-1" />
                          安全
                        </Badge>
                      ) : (
                        <Badge variant="outline" className="bg-red-50 text-red-700">
                          <XCircle className="w-3 h-3 mr-1" />
                          不安全
                        </Badge>
                      )}
                    </TableCell>
                    <TableCell>{renderActionBadge(item.suggestedAction)}</TableCell>
                    <TableCell>{Math.round(item.confidence * 100)}%</TableCell>
                    <TableCell>{format(new Date(item.createdAt), 'yyyy-MM-dd HH:mm:ss')}</TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </div>
        )}

        {/* 分页 */}
        {!isLoading && history.length > 0 && (
          <div className="mt-4">
            <Pagination
              currentPage={pagination.page}
              totalPages={pagination.totalPages}
              onPageChange={handlePageChange}
            />
          </div>
        )}
      </CardContent>
    </Card>
  );
};

export default ModerationHistoryTable;
