import { Card, CardContent } from '@/components/ui/card';
import { Users, Briefcase, Clock, School } from 'lucide-react';

interface DashboardSummaryProps {
  data: any;
  dataType: string;
}

export default function DashboardSummary({ data, dataType }: DashboardSummaryProps) {
  if (!data || !data.success || !data.data) {
    return null;
  }

  const stats = data.data;

  // 适配新的API数据结构
  let totalCount = 0;
  let verifiedCount = 0;
  let anonymousCount = 0;
  let employedCount = 0;
  let unemployedCount = 0;
  let employmentRate = 0;

  // 如果是新的数据结构 (有summary和charts)
  if (stats.summary && stats.charts) {
    totalCount = stats.summary.totalResponses || 0;
    verifiedCount = totalCount; // 暂时假设所有都是已验证的
    anonymousCount = 0;

    // 从就业状态图表数据中提取就业信息
    const employmentData = stats.charts.employmentStatus?.data || [];
    employedCount = employmentData.find((item: any) =>
      item.name === '已就业' || item.name === 'employed'
    )?.value || 0;
    unemployedCount = employmentData.find((item: any) =>
      item.name === '求职中' || item.name === 'unemployed'
    )?.value || 0;

    employmentRate = stats.summary.offerRate || 0;
  } else {
    // 兼容旧的数据结构
    verifiedCount = stats.verifiedCount || 0;
    anonymousCount = stats.anonymousCount || 0;
    employedCount = stats.employedCount || 0;
    unemployedCount = stats.unemployedCount || 0;
    totalCount = verifiedCount + anonymousCount;

    const totalEmploymentRelated = employedCount + unemployedCount;
    employmentRate = totalEmploymentRelated > 0
      ? Math.round((employedCount / totalEmploymentRelated) * 100)
      : 0;
  }

  // 获取教育水平和行业数据
  let educationLevels = [];
  let industries = [];
  let unemploymentDurations = [];

  if (stats.summary && stats.charts) {
    educationLevels = stats.charts.educationDistribution?.data || [];
    industries = stats.charts.majorDistribution?.data || []; // 使用专业分布作为行业数据
    unemploymentDurations = stats.charts.jobSearchDuration?.data || [];
  } else {
    educationLevels = stats.educationLevels || [];
    industries = stats.industries || [];
    unemploymentDurations = stats.unemploymentDurations || [];
  }

  // Calculate most common education
  const mostCommonEducation = educationLevels.length > 0
    ? educationLevels.reduce((prev, current) => ((prev.count || prev.value) > (current.count || current.value)) ? prev : current).name
    : '暂无数据';

  // Calculate most common industry
  const mostCommonIndustry = industries.length > 0
    ? industries.reduce((prev, current) => ((prev.count || prev.value) > (current.count || current.value)) ? prev : current).name
    : '暂无数据';

  // Calculate average unemployment duration
  const averageUnemploymentDuration = unemploymentDurations.length > 0
    ? unemploymentDurations.reduce((sum, item) => sum + ((item.count || item.value) * parseFloat((item.duration || item.name || '').replace(/[^0-9.]/g, '') || '0')), 0) / unemploymentDurations.reduce((sum, item) => sum + (item.count || item.value), 0)
    : 0;

  const formattedUnemploymentDuration = averageUnemploymentDuration > 0
    ? `${averageUnemploymentDuration.toFixed(1)}个月`
    : '暂无数据';

  // Summary cards data
  const summaryCards = [
    {
      title: '总样本数',
      value: totalCount,
      description: `${verifiedCount} 已验证 / ${anonymousCount} 匿名`,
      icon: <Users className="h-5 w-5 text-blue-500" />,
      color: 'bg-blue-50 border-blue-200',
    },
    {
      title: '就业率',
      value: `${employmentRate}%`,
      description: `${employedCount} 已就业 / ${unemployedCount} 待业`,
      icon: <Briefcase className="h-5 w-5 text-green-500" />,
      color: 'bg-green-50 border-green-200',
    },
    {
      title: '平均失业时长',
      value: formattedUnemploymentDuration,
      description: '待业人群平均失业时间',
      icon: <Clock className="h-5 w-5 text-orange-500" />,
      color: 'bg-orange-50 border-orange-200',
    },
    {
      title: '最多学历/行业',
      value: mostCommonEducation || '暂无数据',
      description: `最热门行业: ${mostCommonIndustry || '暂无数据'}`,
      icon: <School className="h-5 w-5 text-purple-500" />,
      color: 'bg-purple-50 border-purple-200',
    },
  ];

  return (
    <div className="mb-8">
      <h2 className="text-xl font-semibold mb-4">{dataType}概览</h2>
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        {summaryCards.map((card, index) => (
          <Card key={index} className={`border ${card.color}`}>
            <CardContent className="p-4">
              <div className="flex justify-between items-start">
                <div>
                  <p className="text-sm font-medium text-gray-500">{card.title}</p>
                  <p className="text-2xl font-bold mt-1">{card.value}</p>
                  <p className="text-xs text-gray-500 mt-1">{card.description}</p>
                </div>
                <div className="p-2 rounded-full bg-white border">
                  {card.icon}
                </div>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>
    </div>
  );
}
