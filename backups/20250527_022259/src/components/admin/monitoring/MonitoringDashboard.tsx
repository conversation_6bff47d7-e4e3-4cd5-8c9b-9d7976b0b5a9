import React, { useState, useEffect } from 'react';
import { <PERSON>, Row, Col, Statistic, Tabs, Spin, Alert, Button, DatePicker, Space, Typography } from 'antd';
import { ReloadOutlined, WarningOutlined, CheckCircleOutlined, ClockCircleOutlined } from '@ant-design/icons';
import { fetchData } from '../../../services/unifiedDataService';
import { Line, Pie } from '@ant-design/charts';
import dayjs from 'dayjs';

const { TabPane } = Tabs;
const { RangePicker } = DatePicker;
const { Title, Text } = Typography;

// 系统状态类型
type SystemStatus = 'healthy' | 'warning' | 'critical' | 'unknown';

// 系统状态组件
const SystemStatusIndicator: React.FC<{ status: SystemStatus }> = ({ status }) => {
  const statusConfig = {
    healthy: { icon: <CheckCircleOutlined />, color: '#52c41a', text: '健康' },
    warning: { icon: <WarningOutlined />, color: '#faad14', text: '警告' },
    critical: { icon: <WarningOutlined />, color: '#f5222d', text: '严重' },
    unknown: { icon: <ClockCircleOutlined />, color: '#8c8c8c', text: '未知' },
  };

  const config = statusConfig[status];

  return (
    <div style={{ display: 'flex', alignItems: 'center' }}>
      <span style={{ color: config.color, marginRight: 8, fontSize: 20 }}>{config.icon}</span>
      <span style={{ color: config.color, fontWeight: 'bold' }}>{config.text}</span>
    </div>
  );
};

// 监控仪表板组件
const MonitoringDashboard: React.FC = () => {
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);
  const [systemStatus, setSystemStatus] = useState<SystemStatus>('unknown');
  const [stats, setStats] = useState<any>({});
  const [timeRange, setTimeRange] = useState<[dayjs.Dayjs, dayjs.Dayjs]>([
    dayjs().subtract(7, 'day'),
    dayjs(),
  ]);
  const [requestsData, setRequestsData] = useState<any[]>([]);
  const [errorsData, setErrorsData] = useState<any[]>([]);
  const [statusCodesData, setStatusCodesData] = useState<any[]>([]);
  const [performanceData, setPerformanceData] = useState<any[]>([]);

  // 加载监控数据
  const loadMonitoringData = async () => {
    setLoading(true);
    setError(null);

    try {
      // 获取系统状态和统计数据
      const response = await fetchData(
        'admin/monitoring/stats',
        'getMonitoringStats',
        {
          startTime: timeRange[0].toISOString(),
          endTime: timeRange[1].toISOString(),
        },
        {
          method: 'GET',
          headers: {
            Authorization: `Bearer ${localStorage.getItem('authToken')}`,
          },
        }
      );

      if (response.success) {
        setStats(response.stats);
        setSystemStatus(response.systemStatus);
        setRequestsData(response.requestsData || []);
        setErrorsData(response.errorsData || []);
        setStatusCodesData(response.statusCodesData || []);
        setPerformanceData(response.performanceData || []);
      } else {
        setError(response.error || '获取监控数据失败');
      }
    } catch (err) {
      setError('获取监控数据时发生错误');
      console.error('获取监控数据错误:', err);
    } finally {
      setLoading(false);
    }
  };

  // 首次加载和时间范围变化时加载数据
  useEffect(() => {
    loadMonitoringData();
  }, [timeRange]);

  // 请求数图表配置
  const requestsConfig = {
    data: requestsData,
    xField: 'date',
    yField: 'value',
    seriesField: 'type',
    yAxis: {
      title: {
        text: '请求数',
      },
    },
    legend: {
      position: 'top',
    },
    smooth: true,
    animation: {
      appear: {
        animation: 'path-in',
        duration: 1000,
      },
    },
  };

  // 错误率图表配置
  const errorsConfig = {
    data: errorsData,
    xField: 'date',
    yField: 'value',
    seriesField: 'type',
    yAxis: {
      title: {
        text: '错误率 (%)',
      },
      min: 0,
      max: 100,
    },
    legend: {
      position: 'top',
    },
    smooth: true,
    animation: {
      appear: {
        animation: 'path-in',
        duration: 1000,
      },
    },
  };

  // 状态码分布图表配置
  const statusCodesConfig = {
    data: statusCodesData,
    angleField: 'value',
    colorField: 'type',
    radius: 0.8,
    label: {
      type: 'outer',
      content: '{name}: {percentage}',
    },
    interactions: [
      {
        type: 'element-active',
      },
    ],
  };

  // 性能指标图表配置
  const performanceConfig = {
    data: performanceData,
    xField: 'date',
    yField: 'value',
    seriesField: 'type',
    yAxis: {
      title: {
        text: '响应时间 (ms)',
      },
    },
    legend: {
      position: 'top',
    },
    smooth: true,
    animation: {
      appear: {
        animation: 'path-in',
        duration: 1000,
      },
    },
  };

  return (
    <div className="monitoring-dashboard">
      <div style={{ marginBottom: 16, display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
        <Title level={4}>系统监控仪表板</Title>
        <Space>
          <RangePicker
            value={timeRange}
            onChange={(dates) => {
              if (dates) {
                setTimeRange([dates[0]!, dates[1]!]);
              }
            }}
            allowClear={false}
          />
          <Button
            type="primary"
            icon={<ReloadOutlined />}
            onClick={loadMonitoringData}
            loading={loading}
          >
            刷新
          </Button>
        </Space>
      </div>

      {error && (
        <Alert
          message="错误"
          description={error}
          type="error"
          showIcon
          style={{ marginBottom: 16 }}
        />
      )}

      <Spin spinning={loading}>
        {/* 系统状态卡片 */}
        <Card style={{ marginBottom: 16 }}>
          <Row gutter={16}>
            <Col span={6}>
              <Card bordered={false}>
                <Statistic
                  title="系统状态"
                  value={<SystemStatusIndicator status={systemStatus} />}
                />
              </Card>
            </Col>
            <Col span={6}>
              <Card bordered={false}>
                <Statistic
                  title="API请求总数"
                  value={stats.totalRequests || 0}
                  suffix="次"
                />
              </Card>
            </Col>
            <Col span={6}>
              <Card bordered={false}>
                <Statistic
                  title="平均响应时间"
                  value={stats.avgResponseTime || 0}
                  suffix="ms"
                  precision={2}
                />
              </Card>
            </Col>
            <Col span={6}>
              <Card bordered={false}>
                <Statistic
                  title="错误率"
                  value={stats.errorRate || 0}
                  suffix="%"
                  precision={2}
                  valueStyle={{ color: stats.errorRate > 5 ? '#cf1322' : '#3f8600' }}
                />
              </Card>
            </Col>
          </Row>
        </Card>

        {/* 详细监控数据 */}
        <Tabs defaultActiveKey="requests">
          <TabPane tab="请求统计" key="requests">
            <Card>
              <Line {...requestsConfig} />
            </Card>
          </TabPane>
          <TabPane tab="错误统计" key="errors">
            <Card>
              <Line {...errorsConfig} />
            </Card>
          </TabPane>
          <TabPane tab="状态码分布" key="statusCodes">
            <Card>
              <Pie {...statusCodesConfig} />
            </Card>
          </TabPane>
          <TabPane tab="性能指标" key="performance">
            <Card>
              <Line {...performanceConfig} />
            </Card>
          </TabPane>
        </Tabs>

        {/* 系统资源使用情况 */}
        <Card title="系统资源使用情况" style={{ marginTop: 16 }}>
          <Row gutter={16}>
            <Col span={8}>
              <Statistic
                title="CPU使用率"
                value={stats.cpuUsage || 0}
                suffix="%"
                precision={2}
              />
              <div style={{ height: 8 }}></div>
              <Progress percent={stats.cpuUsage || 0} status={stats.cpuUsage > 80 ? 'exception' : 'normal'} />
            </Col>
            <Col span={8}>
              <Statistic
                title="内存使用率"
                value={stats.memoryUsage || 0}
                suffix="%"
                precision={2}
              />
              <div style={{ height: 8 }}></div>
              <Progress percent={stats.memoryUsage || 0} status={stats.memoryUsage > 80 ? 'exception' : 'normal'} />
            </Col>
            <Col span={8}>
              <Statistic
                title="数据库大小"
                value={stats.dbSize ? (stats.dbSize / (1024 * 1024)).toFixed(2) : 0}
                suffix="MB"
              />
              <div style={{ height: 8 }}></div>
              <Progress 
                percent={stats.dbSize ? (stats.dbSize / (1024 * 1024 * 1000) * 100) : 0} 
                status={(stats.dbSize || 0) > 1024 * 1024 * 900 ? 'exception' : 'normal'} 
              />
            </Col>
          </Row>
        </Card>
      </Spin>
    </div>
  );
};

export default MonitoringDashboard;

// 进度条组件
const Progress: React.FC<{ percent: number; status: 'normal' | 'exception' | 'active' | 'success' }> = ({ percent, status }) => {
  const bgColor = status === 'exception' ? '#ffccc7' : '#e6f7ff';
  const fgColor = status === 'exception' ? '#f5222d' : '#1890ff';
  
  return (
    <div style={{ height: 8, backgroundColor: bgColor, borderRadius: 4, overflow: 'hidden' }}>
      <div 
        style={{ 
          width: `${Math.min(percent, 100)}%`, 
          height: '100%', 
          backgroundColor: fgColor,
          transition: 'width 0.3s ease'
        }} 
      />
    </div>
  );
};
