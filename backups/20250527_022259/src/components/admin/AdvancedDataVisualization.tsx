import React, { useState, useEffect, useRef } from 'react';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { LoadingSpinner } from '@/components/ui/loading-spinner';
import { useToast } from '@/components/ui/use-toast';
import { 
  BarChart, 
  Bar, 
  LineChart, 
  Line, 
  PieChart, 
  Pie, 
  Cell, 
  AreaChart, 
  Area,
  XAxis, 
  YAxis, 
  CartesianGrid, 
  Tooltip, 
  Legend, 
  ResponsiveContainer,
  Radar,
  RadarChart,
  PolarGrid,
  PolarAngleAxis,
  PolarRadiusAxis,
  Treemap
} from 'recharts';
import { 
  Download, 
  Share2, 
  Filter, 
  RefreshCw, 
  Calendar, 
  BarChart2, 
  <PERSON><PERSON><PERSON> as PieChartIcon,
  <PERSON><PERSON><PERSON> as LineChartIcon,
  Activity
} from 'lucide-react';

// 导入数据服务
import { getReviewStats, getReviewerPerformance } from '@/services/dataService';

// 图表类型
enum ChartType {
  BAR = 'bar',
  LINE = 'line',
  PIE = 'pie',
  AREA = 'area',
  RADAR = 'radar',
  TREEMAP = 'treemap'
}

// 时间范围
enum TimeRange {
  DAY = 'day',
  WEEK = 'week',
  MONTH = 'month',
  QUARTER = 'quarter',
  YEAR = 'year',
  ALL = 'all'
}

// 数据类型
enum DataType {
  REVIEW_STATS = 'review_stats',
  REVIEWER_PERFORMANCE = 'reviewer_performance',
  CONTENT_DISTRIBUTION = 'content_distribution',
  TAG_USAGE = 'tag_usage',
  USER_ACTIVITY = 'user_activity'
}

// 图表配置
interface ChartConfig {
  type: ChartType;
  dataType: DataType;
  timeRange: TimeRange;
  title: string;
  description: string;
}

// 颜色配置
const COLORS = [
  '#8884d8', '#82ca9d', '#ffc658', '#ff8042', '#0088fe', 
  '#00c49f', '#ffbb28', '#ff8042', '#a4de6c', '#d0ed57'
];

/**
 * 高级数据可视化组件
 */
export default function AdvancedDataVisualization() {
  const { toast } = useToast();
  const [activeTab, setActiveTab] = useState<string>('charts');
  const [chartType, setChartType] = useState<ChartType>(ChartType.BAR);
  const [dataType, setDataType] = useState<DataType>(DataType.REVIEW_STATS);
  const [timeRange, setTimeRange] = useState<TimeRange>(TimeRange.MONTH);
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [data, setData] = useState<any>(null);
  const [error, setError] = useState<string | null>(null);
  
  // 加载数据
  useEffect(() => {
    fetchData();
  }, [chartType, dataType, timeRange]);
  
  // 获取数据
  const fetchData = async () => {
    setIsLoading(true);
    setError(null);
    
    try {
      let result;
      
      switch (dataType) {
        case DataType.REVIEW_STATS:
          result = await getReviewStats();
          if (result.success) {
            // 处理数据
            setData(processReviewStatsData(result.stats, timeRange));
          } else {
            setError(result.error || '获取审核统计数据失败');
          }
          break;
          
        case DataType.REVIEWER_PERFORMANCE:
          result = await getReviewerPerformance();
          if (result.success) {
            // 处理数据
            setData(processReviewerPerformanceData(result.performance, timeRange));
          } else {
            setError(result.error || '获取审核员绩效数据失败');
          }
          break;
          
        case DataType.CONTENT_DISTRIBUTION:
          // 模拟数据
          setData(getContentDistributionData());
          break;
          
        case DataType.TAG_USAGE:
          // 模拟数据
          setData(getTagUsageData());
          break;
          
        case DataType.USER_ACTIVITY:
          // 模拟数据
          setData(getUserActivityData());
          break;
          
        default:
          setError('未知数据类型');
      }
    } catch (error) {
      console.error('获取数据失败:', error);
      setError('获取数据失败，请稍后再试');
    } finally {
      setIsLoading(false);
    }
  };
  
  // 处理审核统计数据
  const processReviewStatsData = (stats: any, timeRange: TimeRange) => {
    // 模拟不同时间范围的数据
    switch (timeRange) {
      case TimeRange.DAY:
        return [
          { name: '00:00', 审核量: 5, 通过: 4, 拒绝: 1 },
          { name: '04:00', 审核量: 8, 通过: 6, 拒绝: 2 },
          { name: '08:00', 审核量: 15, 通过: 12, 拒绝: 3 },
          { name: '12:00', 审核量: 25, 通过: 20, 拒绝: 5 },
          { name: '16:00', 审核量: 30, 通过: 22, 拒绝: 8 },
          { name: '20:00', 审核量: 20, 通过: 15, 拒绝: 5 }
        ];
        
      case TimeRange.WEEK:
        return [
          { name: '周一', 审核量: 40, 通过: 30, 拒绝: 10 },
          { name: '周二', 审核量: 35, 通过: 28, 拒绝: 7 },
          { name: '周三', 审核量: 50, 通过: 40, 拒绝: 10 },
          { name: '周四', 审核量: 45, 通过: 35, 拒绝: 10 },
          { name: '周五', 审核量: 60, 通过: 48, 拒绝: 12 },
          { name: '周六', 审核量: 30, 通过: 25, 拒绝: 5 },
          { name: '周日', 审核量: 20, 通过: 18, 拒绝: 2 }
        ];
        
      case TimeRange.MONTH:
        return [
          { name: '第1周', 审核量: 280, 通过: 224, 拒绝: 56 },
          { name: '第2周', 审核量: 250, 通过: 200, 拒绝: 50 },
          { name: '第3周', 审核量: 300, 通过: 240, 拒绝: 60 },
          { name: '第4周', 审核量: 320, 通过: 256, 拒绝: 64 }
        ];
        
      default:
        return [
          { name: '1月', 审核量: 1200, 通过: 960, 拒绝: 240 },
          { name: '2月', 审核量: 1100, 通过: 880, 拒绝: 220 },
          { name: '3月', 审核量: 1300, 通过: 1040, 拒绝: 260 },
          { name: '4月', 审核量: 1400, 通过: 1120, 拒绝: 280 },
          { name: '5月', 审核量: 1500, 通过: 1200, 拒绝: 300 },
          { name: '6月', 审核量: 1600, 通过: 1280, 拒绝: 320 }
        ];
    }
  };
  
  // 处理审核员绩效数据
  const processReviewerPerformanceData = (performance: any, timeRange: TimeRange) => {
    // 模拟数据
    return [
      { name: '张三', 审核量: 120, 通过率: 85, 拒绝率: 15, 平均耗时: 45 },
      { name: '李四', 审核量: 150, 通过率: 80, 拒绝率: 20, 平均耗时: 40 },
      { name: '王五', 审核量: 100, 通过率: 90, 拒绝率: 10, 平均耗时: 50 },
      { name: '赵六', 审核量: 130, 通过率: 75, 拒绝率: 25, 平均耗时: 35 },
      { name: '钱七', 审核量: 110, 通过率: 82, 拒绝率: 18, 平均耗时: 42 }
    ];
  };
  
  // 获取内容分布数据
  const getContentDistributionData = () => {
    return [
      { name: '故事', value: 400 },
      { name: '问卷', value: 300 },
      { name: '评论', value: 200 },
      { name: '反馈', value: 100 }
    ];
  };
  
  // 获取标签使用数据
  const getTagUsageData = () => {
    return [
      { name: '职业规划', value: 100 },
      { name: '就业经验', value: 80 },
      { name: '求职技巧', value: 70 },
      { name: '面试经验', value: 60 },
      { name: '简历制作', value: 50 },
      { name: '行业分析', value: 40 },
      { name: '薪资待遇', value: 30 },
      { name: '职场文化', value: 20 }
    ];
  };
  
  // 获取用户活动数据
  const getUserActivityData = () => {
    return [
      { name: '浏览', 今日: 4000, 昨日: 2400, 上周同期: 2400 },
      { name: '提交', 今日: 3000, 昨日: 1398, 上周同期: 2210 },
      { name: '分享', 今日: 2000, 昨日: 9800, 上周同期: 2290 },
      { name: '评论', 今日: 2780, 昨日: 3908, 上周同期: 2000 },
      { name: '点赞', 今日: 1890, 昨日: 4800, 上周同期: 2181 }
    ];
  };
  
  // 导出数据
  const exportData = () => {
    if (!data) return;
    
    try {
      // 转换为CSV
      const headers = Object.keys(data[0]).join(',');
      const rows = data.map((item: any) => Object.values(item).join(','));
      const csv = [headers, ...rows].join('\n');
      
      // 创建下载链接
      const blob = new Blob([csv], { type: 'text/csv;charset=utf-8;' });
      const url = URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.setAttribute('href', url);
      link.setAttribute('download', `${dataType}_${timeRange}_${new Date().toISOString().split('T')[0]}.csv`);
      link.style.visibility = 'hidden';
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      
      toast({
        title: '导出成功',
        description: '数据已成功导出为CSV文件',
        variant: 'default'
      });
    } catch (error) {
      console.error('导出数据失败:', error);
      toast({
        title: '导出失败',
        description: '导出数据时发生错误',
        variant: 'destructive'
      });
    }
  };
  
  // 渲染图表
  const renderChart = () => {
    if (isLoading) {
      return (
        <div className="flex items-center justify-center p-12">
          <LoadingSpinner size="lg" />
        </div>
      );
    }
    
    if (error) {
      return (
        <div className="flex flex-col items-center justify-center p-12 text-destructive">
          <p>{error}</p>
          <Button 
            variant="outline" 
            onClick={fetchData} 
            className="mt-4"
          >
            <RefreshCw className="h-4 w-4 mr-2" />
            重试
          </Button>
        </div>
      );
    }
    
    if (!data) {
      return (
        <div className="flex items-center justify-center p-12 text-muted-foreground">
          <p>暂无数据</p>
        </div>
      );
    }
    
    switch (chartType) {
      case ChartType.BAR:
        return (
          <ResponsiveContainer width="100%" height={400}>
            <BarChart data={data}>
              <CartesianGrid strokeDasharray="3 3" />
              <XAxis dataKey="name" />
              <YAxis />
              <Tooltip />
              <Legend />
              {Object.keys(data[0])
                .filter(key => key !== 'name')
                .map((key, index) => (
                  <Bar key={key} dataKey={key} fill={COLORS[index % COLORS.length]} />
                ))}
            </BarChart>
          </ResponsiveContainer>
        );
        
      case ChartType.LINE:
        return (
          <ResponsiveContainer width="100%" height={400}>
            <LineChart data={data}>
              <CartesianGrid strokeDasharray="3 3" />
              <XAxis dataKey="name" />
              <YAxis />
              <Tooltip />
              <Legend />
              {Object.keys(data[0])
                .filter(key => key !== 'name')
                .map((key, index) => (
                  <Line 
                    key={key} 
                    type="monotone" 
                    dataKey={key} 
                    stroke={COLORS[index % COLORS.length]} 
                    activeDot={{ r: 8 }} 
                  />
                ))}
            </LineChart>
          </ResponsiveContainer>
        );
        
      case ChartType.PIE:
        return (
          <ResponsiveContainer width="100%" height={400}>
            <PieChart>
              <Pie
                data={data}
                cx="50%"
                cy="50%"
                labelLine={true}
                label={({ name, percent }) => `${name}: ${(percent * 100).toFixed(0)}%`}
                outerRadius={150}
                fill="#8884d8"
                dataKey="value"
              >
                {data.map((entry: any, index: number) => (
                  <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                ))}
              </Pie>
              <Tooltip />
              <Legend />
            </PieChart>
          </ResponsiveContainer>
        );
        
      case ChartType.AREA:
        return (
          <ResponsiveContainer width="100%" height={400}>
            <AreaChart data={data}>
              <CartesianGrid strokeDasharray="3 3" />
              <XAxis dataKey="name" />
              <YAxis />
              <Tooltip />
              <Legend />
              {Object.keys(data[0])
                .filter(key => key !== 'name')
                .map((key, index) => (
                  <Area 
                    key={key} 
                    type="monotone" 
                    dataKey={key} 
                    stackId="1"
                    stroke={COLORS[index % COLORS.length]}
                    fill={COLORS[index % COLORS.length]} 
                  />
                ))}
            </AreaChart>
          </ResponsiveContainer>
        );
        
      case ChartType.RADAR:
        return (
          <ResponsiveContainer width="100%" height={400}>
            <RadarChart cx="50%" cy="50%" outerRadius="80%" data={data}>
              <PolarGrid />
              <PolarAngleAxis dataKey="name" />
              <PolarRadiusAxis />
              {Object.keys(data[0])
                .filter(key => key !== 'name')
                .map((key, index) => (
                  <Radar 
                    key={key} 
                    name={key} 
                    dataKey={key} 
                    stroke={COLORS[index % COLORS.length]} 
                    fill={COLORS[index % COLORS.length]} 
                    fillOpacity={0.6} 
                  />
                ))}
              <Legend />
              <Tooltip />
            </RadarChart>
          </ResponsiveContainer>
        );
        
      case ChartType.TREEMAP:
        return (
          <ResponsiveContainer width="100%" height={400}>
            <Treemap
              data={data}
              dataKey="value"
              aspectRatio={4 / 3}
              stroke="#fff"
              fill="#8884d8"
            >
              {data.map((entry: any, index: number) => (
                <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
              ))}
            </Treemap>
          </ResponsiveContainer>
        );
        
      default:
        return null;
    }
  };
  
  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <div className="flex justify-between items-center">
            <div>
              <CardTitle>高级数据可视化</CardTitle>
              <CardDescription>查看和分析系统数据</CardDescription>
            </div>
            
            <div className="flex items-center space-x-2">
              <Button 
                variant="outline" 
                size="sm"
                onClick={exportData}
                disabled={!data}
              >
                <Download className="h-4 w-4 mr-2" />
                导出
              </Button>
              
              <Button 
                variant="outline" 
                size="sm"
                onClick={fetchData}
              >
                <RefreshCw className="h-4 w-4 mr-2" />
                刷新
              </Button>
            </div>
          </div>
        </CardHeader>
        
        <CardContent className="space-y-6">
          <div className="flex flex-wrap gap-4">
            <div className="w-full md:w-auto">
              <label className="block text-sm font-medium mb-1">数据类型</label>
              <Select
                value={dataType}
                onValueChange={(value) => setDataType(value as DataType)}
              >
                <SelectTrigger className="w-[200px]">
                  <SelectValue placeholder="选择数据类型" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value={DataType.REVIEW_STATS}>审核统计</SelectItem>
                  <SelectItem value={DataType.REVIEWER_PERFORMANCE}>审核员绩效</SelectItem>
                  <SelectItem value={DataType.CONTENT_DISTRIBUTION}>内容分布</SelectItem>
                  <SelectItem value={DataType.TAG_USAGE}>标签使用</SelectItem>
                  <SelectItem value={DataType.USER_ACTIVITY}>用户活动</SelectItem>
                </SelectContent>
              </Select>
            </div>
            
            <div className="w-full md:w-auto">
              <label className="block text-sm font-medium mb-1">图表类型</label>
              <Select
                value={chartType}
                onValueChange={(value) => setChartType(value as ChartType)}
              >
                <SelectTrigger className="w-[200px]">
                  <SelectValue placeholder="选择图表类型" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value={ChartType.BAR}>柱状图</SelectItem>
                  <SelectItem value={ChartType.LINE}>折线图</SelectItem>
                  <SelectItem value={ChartType.PIE}>饼图</SelectItem>
                  <SelectItem value={ChartType.AREA}>面积图</SelectItem>
                  <SelectItem value={ChartType.RADAR}>雷达图</SelectItem>
                  <SelectItem value={ChartType.TREEMAP}>矩形树图</SelectItem>
                </SelectContent>
              </Select>
            </div>
            
            <div className="w-full md:w-auto">
              <label className="block text-sm font-medium mb-1">时间范围</label>
              <Select
                value={timeRange}
                onValueChange={(value) => setTimeRange(value as TimeRange)}
              >
                <SelectTrigger className="w-[200px]">
                  <SelectValue placeholder="选择时间范围" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value={TimeRange.DAY}>今日</SelectItem>
                  <SelectItem value={TimeRange.WEEK}>本周</SelectItem>
                  <SelectItem value={TimeRange.MONTH}>本月</SelectItem>
                  <SelectItem value={TimeRange.QUARTER}>本季度</SelectItem>
                  <SelectItem value={TimeRange.YEAR}>本年</SelectItem>
                  <SelectItem value={TimeRange.ALL}>全部</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>
          
          <div className="border rounded-lg p-4">
            {renderChart()}
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
