<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>API连接调试测试</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            margin: 0;
            padding: 20px;
        }

        .container {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            padding: 40px;
            text-align: center;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.2);
            max-width: 800px;
            width: 100%;
        }

        h1 {
            font-size: 2rem;
            margin-bottom: 20px;
            background: linear-gradient(45deg, #fff, #f0f0f0);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .test-item {
            background: rgba(255, 255, 255, 0.05);
            border-radius: 8px;
            padding: 15px;
            margin: 10px 0;
            display: flex;
            align-items: center;
            justify-content: space-between;
        }

        .status {
            padding: 5px 15px;
            border-radius: 20px;
            font-size: 0.9rem;
            font-weight: bold;
        }

        .status.success { background: #4CAF50; color: white; }
        .status.error { background: #f44336; color: white; }
        .status.testing { background: #ff9800; color: white; }
        .status.pending { background: #9E9E9E; color: white; }

        button {
            background: linear-gradient(45deg, #4CAF50, #45a049);
            color: white;
            border: none;
            padding: 15px 30px;
            border-radius: 25px;
            font-size: 1.1rem;
            cursor: pointer;
            margin: 10px;
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(76, 175, 80, 0.3);
        }

        button:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(76, 175, 80, 0.4);
        }

        .response-box {
            background: rgba(0, 0, 0, 0.3);
            border-radius: 8px;
            padding: 15px;
            margin: 10px 0;
            text-align: left;
            font-family: monospace;
            font-size: 0.9rem;
            white-space: pre-wrap;
            max-height: 200px;
            overflow-y: auto;
        }

        .url-input {
            width: 100%;
            padding: 10px;
            border-radius: 8px;
            border: 1px solid rgba(255, 255, 255, 0.3);
            background: rgba(255, 255, 255, 0.1);
            color: white;
            margin: 10px 0;
        }

        .url-input::placeholder {
            color: rgba(255, 255, 255, 0.7);
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔍 API连接调试测试</h1>

        <div style="margin: 20px 0;">
            <label>API基础URL:</label>
            <input type="text" id="api-url" class="url-input"
                   value="https://college-employment-survey.aibook2099.workers.dev"
                   placeholder="输入API基础URL">
        </div>

        <div class="test-item">
            <span>基础连接测试</span>
            <span class="status pending" id="basic-status">⏳ 待测试</span>
        </div>

        <div class="test-item">
            <span>CORS配置测试</span>
            <span class="status pending" id="cors-status">⏳ 待测试</span>
        </div>

        <div class="test-item">
            <span>问卷统计API</span>
            <span class="status pending" id="stats-status">⏳ 待测试</span>
        </div>

        <div class="test-item">
            <span>系统监控API</span>
            <span class="status pending" id="monitor-status">⏳ 待测试</span>
        </div>

        <button onclick="runAllTests()">🚀 运行所有测试</button>
        <button onclick="copyResults()">📋 复制结果</button>

        <div class="response-box" id="response-box">点击"运行所有测试"开始...</div>

        <div style="margin-top: 20px; text-align: left; font-size: 0.9rem;">
            <strong>当前环境信息:</strong><br>
            域名: <span id="current-domain"></span><br>
            时间: <span id="current-time"></span><br>
            浏览器: <span id="browser-info"></span>
        </div>
    </div>

    <script>
        let testResults = {};

        // 更新环境信息
        document.getElementById('current-domain').textContent = window.location.hostname;
        document.getElementById('current-time').textContent = new Date().toLocaleString();
        document.getElementById('browser-info').textContent = navigator.userAgent.split(' ').slice(-2).join(' ');

        function updateStatus(elementId, status, text) {
            const element = document.getElementById(elementId);
            element.className = `status ${status}`;
            element.textContent = text;
        }

        function logResponse(message) {
            const box = document.getElementById('response-box');
            box.textContent += message + '\n';
            box.scrollTop = box.scrollHeight;
        }

        async function testBasicConnection() {
            const apiUrl = document.getElementById('api-url').value;
            updateStatus('basic-status', 'testing', '⏳ 测试中');
            logResponse('=== 基础连接测试 ===');

            try {
                const startTime = Date.now();

                // 添加详细的fetch配置
                const response = await fetch(`${apiUrl}/api/system/monitor`, {
                    method: 'GET',
                    mode: 'cors',
                    cache: 'no-cache',
                    credentials: 'omit',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                });

                const responseTime = Date.now() - startTime;

                logResponse(`请求URL: ${apiUrl}/api/system/monitor`);
                logResponse(`请求模式: cors`);
                logResponse(`响应状态: ${response.status} ${response.statusText}`);
                logResponse(`响应时间: ${responseTime}ms`);
                logResponse(`响应类型: ${response.type}`);
                logResponse(`响应URL: ${response.url}`);

                if (response.ok) {
                    const data = await response.json();
                    updateStatus('basic-status', 'success', '✅ 成功');
                    logResponse(`响应数据: ${JSON.stringify(data, null, 2)}`);
                    testResults.basic = { success: true, responseTime, data };
                } else {
                    updateStatus('basic-status', 'error', '❌ 失败');
                    logResponse(`错误: HTTP ${response.status}`);
                    testResults.basic = { success: false, status: response.status };
                }
            } catch (error) {
                updateStatus('basic-status', 'error', '❌ 失败');
                logResponse(`错误类型: ${error.name}`);
                logResponse(`错误信息: ${error.message}`);
                logResponse(`错误堆栈: ${error.stack}`);

                // 添加网络诊断
                logResponse(`\n=== 网络诊断 ===`);
                logResponse(`在线状态: ${navigator.onLine}`);
                logResponse(`用户代理: ${navigator.userAgent}`);
                logResponse(`当前域名: ${window.location.origin}`);

                testResults.basic = { success: false, error: error.message, errorType: error.name };
            }
        }

        async function testCORS() {
            const apiUrl = document.getElementById('api-url').value;
            updateStatus('cors-status', 'testing', '⏳ 测试中');
            logResponse('\n=== CORS配置测试 ===');

            try {
                const response = await fetch(`${apiUrl}/api/questionnaire/stats`, {
                    method: 'GET',
                    headers: {
                        'Content-Type': 'application/json',
                        'Origin': window.location.origin
                    }
                });

                logResponse(`请求URL: ${apiUrl}/api/questionnaire/stats`);
                logResponse(`Origin: ${window.location.origin}`);
                logResponse(`响应状态: ${response.status}`);

                // 检查CORS头
                const corsHeaders = {
                    'access-control-allow-origin': response.headers.get('access-control-allow-origin'),
                    'access-control-allow-methods': response.headers.get('access-control-allow-methods'),
                    'access-control-allow-headers': response.headers.get('access-control-allow-headers')
                };

                logResponse(`CORS头: ${JSON.stringify(corsHeaders, null, 2)}`);

                if (response.ok) {
                    updateStatus('cors-status', 'success', '✅ 成功');
                    testResults.cors = { success: true, headers: corsHeaders };
                } else {
                    updateStatus('cors-status', 'error', '❌ 失败');
                    testResults.cors = { success: false, status: response.status };
                }
            } catch (error) {
                updateStatus('cors-status', 'error', '❌ 失败');
                logResponse(`CORS错误: ${error.message}`);
                testResults.cors = { success: false, error: error.message };
            }
        }

        async function testStatsAPI() {
            const apiUrl = document.getElementById('api-url').value;
            updateStatus('stats-status', 'testing', '⏳ 测试中');
            logResponse('\n=== 问卷统计API测试 ===');

            try {
                const response = await fetch(`${apiUrl}/api/questionnaire/stats`);
                logResponse(`请求URL: ${apiUrl}/api/questionnaire/stats`);
                logResponse(`响应状态: ${response.status}`);

                if (response.ok) {
                    const data = await response.json();
                    updateStatus('stats-status', 'success', '✅ 成功');
                    logResponse(`数据: ${JSON.stringify(data, null, 2)}`);
                    testResults.stats = { success: true, data };
                } else {
                    updateStatus('stats-status', 'error', '❌ 失败');
                    testResults.stats = { success: false, status: response.status };
                }
            } catch (error) {
                updateStatus('stats-status', 'error', '❌ 失败');
                logResponse(`错误: ${error.message}`);
                testResults.stats = { success: false, error: error.message };
            }
        }

        async function testMonitorAPI() {
            const apiUrl = document.getElementById('api-url').value;
            updateStatus('monitor-status', 'testing', '⏳ 测试中');
            logResponse('\n=== 系统监控API测试 ===');

            try {
                const response = await fetch(`${apiUrl}/api/system/monitor`);
                logResponse(`请求URL: ${apiUrl}/api/system/monitor`);
                logResponse(`响应状态: ${response.status}`);

                if (response.ok) {
                    const data = await response.json();
                    updateStatus('monitor-status', 'success', '✅ 成功');
                    logResponse(`数据: ${JSON.stringify(data, null, 2)}`);
                    testResults.monitor = { success: true, data };
                } else {
                    updateStatus('monitor-status', 'error', '❌ 失败');
                    testResults.monitor = { success: false, status: response.status };
                }
            } catch (error) {
                updateStatus('monitor-status', 'error', '❌ 失败');
                logResponse(`错误: ${error.message}`);
                testResults.monitor = { success: false, error: error.message };
            }
        }

        async function runAllTests() {
            // 清空结果
            document.getElementById('response-box').textContent = '';
            testResults = {};

            // 重置状态
            ['basic-status', 'cors-status', 'stats-status', 'monitor-status'].forEach(id => {
                updateStatus(id, 'pending', '⏳ 待测试');
            });

            logResponse(`开始测试 - ${new Date().toLocaleString()}`);
            logResponse(`前端域名: ${window.location.origin}`);
            logResponse(`API基础URL: ${document.getElementById('api-url').value}`);

            await testBasicConnection();
            await new Promise(resolve => setTimeout(resolve, 500));
            await testCORS();
            await new Promise(resolve => setTimeout(resolve, 500));
            await testStatsAPI();
            await new Promise(resolve => setTimeout(resolve, 500));
            await testMonitorAPI();

            logResponse('\n=== 测试完成 ===');
        }

        function copyResults() {
            const report = `# API连接调试测试报告
生成时间: ${new Date().toLocaleString()}
前端域名: ${window.location.origin}
API基础URL: ${document.getElementById('api-url').value}

## 测试结果
${JSON.stringify(testResults, null, 2)}

## 详细日志
${document.getElementById('response-box').textContent}

---
报告生成完成`;

            navigator.clipboard.writeText(report).then(() => {
                alert('测试结果已复制到剪贴板！');
            }).catch(() => {
                const textarea = document.createElement('textarea');
                textarea.value = report;
                document.body.appendChild(textarea);
                textarea.select();
                document.execCommand('copy');
                document.body.removeChild(textarea);
                alert('测试结果已复制到剪贴板！');
            });
        }
    </script>
</body>
</html>
