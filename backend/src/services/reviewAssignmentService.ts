/**
 * 审核分配服务
 * 负责将待审核内容分配给审核员
 */

import { PrismaClient } from '@prisma/client';
import { ReviewLevel, ContentType } from './reviewService';

// 分配策略
export enum AssignmentStrategy {
  ROUND_ROBIN = 'round_robin', // 轮询分配
  WORKLOAD_BALANCED = 'workload_balanced', // 工作量均衡
  EXPERTISE_BASED = 'expertise_based', // 基于专长
  PRIORITY_BASED = 'priority_based' // 基于优先级
}

// 审核员信息
interface Reviewer {
  id: string;
  username: string;
  role: string;
  expertise?: string[];
  currentWorkload?: number;
  lastAssignedAt?: Date;
}

// 待分配内容
interface PendingContent {
  id: string;
  type: ContentType;
  flags: string[];
  priority?: number;
}

/**
 * 审核分配服务
 */
export class ReviewAssignmentService {
  private static instance: ReviewAssignmentService;
  private prisma: PrismaClient;
  private strategy: AssignmentStrategy = AssignmentStrategy.WORKLOAD_BALANCED;
  private isInitialized: boolean = false;
  
  private constructor() {
    this.prisma = new PrismaClient();
  }
  
  /**
   * 获取单例实例
   */
  public static getInstance(): ReviewAssignmentService {
    if (!ReviewAssignmentService.instance) {
      ReviewAssignmentService.instance = new ReviewAssignmentService();
    }
    return ReviewAssignmentService.instance;
  }
  
  /**
   * 初始化服务
   */
  public async initialize(): Promise<void> {
    if (this.isInitialized) {
      return;
    }
    
    try {
      // 从数据库加载配置
      const setting = await this.prisma.systemSetting.findUnique({
        where: {
          key: 'review_assignment_strategy'
        }
      });
      
      if (setting) {
        this.strategy = setting.value as AssignmentStrategy;
      }
      
      this.isInitialized = true;
    } catch (error) {
      console.error('初始化审核分配服务失败:', error);
      throw error;
    }
  }
  
  /**
   * 设置分配策略
   */
  public async setStrategy(strategy: AssignmentStrategy, updatedBy: string): Promise<void> {
    try {
      // 更新数据库配置
      await this.prisma.systemSetting.upsert({
        where: {
          key: 'review_assignment_strategy'
        },
        update: {
          value: strategy,
          updatedBy,
          updatedAt: new Date()
        },
        create: {
          key: 'review_assignment_strategy',
          value: strategy,
          description: '审核分配策略',
          updatedBy
        }
      });
      
      this.strategy = strategy;
    } catch (error) {
      console.error('设置审核分配策略失败:', error);
      throw error;
    }
  }
  
  /**
   * 获取当前分配策略
   */
  public getStrategy(): AssignmentStrategy {
    return this.strategy;
  }
  
  /**
   * 分配待审核内容
   */
  public async assignContent(contentId: string): Promise<string | null> {
    try {
      // 获取待审核内容
      const content = await this.prisma.pendingContent.findUnique({
        where: {
          id: contentId
        },
        select: {
          id: true,
          type: true,
          flags: true,
          priority: true,
          assignedTo: true
        }
      });
      
      if (!content) {
        throw new Error(`待审核内容不存在: ${contentId}`);
      }
      
      // 如果已分配，则返回已分配的审核员
      if (content.assignedTo) {
        return content.assignedTo;
      }
      
      // 获取所有审核员
      const reviewers = await this.getAvailableReviewers();
      
      if (reviewers.length === 0) {
        return null;
      }
      
      // 根据策略选择审核员
      let selectedReviewer: Reviewer;
      
      switch (this.strategy) {
        case AssignmentStrategy.ROUND_ROBIN:
          selectedReviewer = this.assignRoundRobin(reviewers);
          break;
        case AssignmentStrategy.WORKLOAD_BALANCED:
          selectedReviewer = this.assignWorkloadBalanced(reviewers);
          break;
        case AssignmentStrategy.EXPERTISE_BASED:
          selectedReviewer = this.assignExpertiseBased(reviewers, content);
          break;
        case AssignmentStrategy.PRIORITY_BASED:
          selectedReviewer = this.assignPriorityBased(reviewers, content);
          break;
        default:
          selectedReviewer = this.assignWorkloadBalanced(reviewers);
      }
      
      // 更新待审核内容
      await this.prisma.pendingContent.update({
        where: {
          id: contentId
        },
        data: {
          assignedTo: selectedReviewer.id,
          assignedAt: new Date()
        }
      });
      
      // 更新审核员工作量
      await this.updateReviewerWorkload(selectedReviewer.id);
      
      return selectedReviewer.id;
    } catch (error) {
      console.error('分配待审核内容失败:', error);
      throw error;
    }
  }
  
  /**
   * 获取可用审核员
   */
  private async getAvailableReviewers(): Promise<Reviewer[]> {
    try {
      // 获取所有审核员和管理员
      const users = await this.prisma.user.findMany({
        where: {
          role: {
            in: ['reviewer', 'admin', 'superadmin']
          },
          isActive: true
        },
        select: {
          id: true,
          username: true,
          role: true,
          expertise: true
        }
      });
      
      // 获取每个审核员的当前工作量
      const workloads = await this.prisma.pendingContent.groupBy({
        by: ['assignedTo'],
        where: {
          status: 'pending',
          assignedTo: {
            not: null
          }
        },
        _count: {
          id: true
        }
      });
      
      // 获取每个审核员的最后分配时间
      const lastAssignments = await this.prisma.pendingContent.groupBy({
        by: ['assignedTo'],
        where: {
          assignedTo: {
            not: null
          }
        },
        _max: {
          assignedAt: true
        }
      });
      
      // 合并数据
      const reviewers = users.map(user => {
        const workload = workloads.find(w => w.assignedTo === user.id);
        const lastAssignment = lastAssignments.find(la => la.assignedTo === user.id);
        
        return {
          id: user.id,
          username: user.username,
          role: user.role,
          expertise: user.expertise as string[] || [],
          currentWorkload: workload ? workload._count.id : 0,
          lastAssignedAt: lastAssignment ? lastAssignment._max.assignedAt : null
        };
      });
      
      return reviewers;
    } catch (error) {
      console.error('获取可用审核员失败:', error);
      throw error;
    }
  }
  
  /**
   * 轮询分配
   */
  private assignRoundRobin(reviewers: Reviewer[]): Reviewer {
    // 按最后分配时间排序
    reviewers.sort((a, b) => {
      if (!a.lastAssignedAt) return -1;
      if (!b.lastAssignedAt) return 1;
      return a.lastAssignedAt.getTime() - b.lastAssignedAt.getTime();
    });
    
    return reviewers[0];
  }
  
  /**
   * 工作量均衡分配
   */
  private assignWorkloadBalanced(reviewers: Reviewer[]): Reviewer {
    // 按当前工作量排序
    reviewers.sort((a, b) => (a.currentWorkload || 0) - (b.currentWorkload || 0));
    
    return reviewers[0];
  }
  
  /**
   * 基于专长分配
   */
  private assignExpertiseBased(reviewers: Reviewer[], content: PendingContent): Reviewer {
    // 计算每个审核员的匹配度
    const scoredReviewers = reviewers.map(reviewer => {
      let score = 0;
      
      // 根据内容类型匹配
      if (reviewer.expertise?.includes(content.type)) {
        score += 5;
      }
      
      // 根据标记匹配
      content.flags.forEach(flag => {
        if (reviewer.expertise?.includes(flag)) {
          score += 3;
        }
      });
      
      // 考虑工作量
      score -= (reviewer.currentWorkload || 0) * 0.5;
      
      return { reviewer, score };
    });
    
    // 按匹配度排序
    scoredReviewers.sort((a, b) => b.score - a.score);
    
    return scoredReviewers[0].reviewer;
  }
  
  /**
   * 基于优先级分配
   */
  private assignPriorityBased(reviewers: Reviewer[], content: PendingContent): Reviewer {
    // 高优先级内容分配给管理员
    if (content.priority && content.priority > 7) {
      const admins = reviewers.filter(r => r.role === 'admin' || r.role === 'superadmin');
      if (admins.length > 0) {
        // 按工作量排序
        admins.sort((a, b) => (a.currentWorkload || 0) - (b.currentWorkload || 0));
        return admins[0];
      }
    }
    
    // 中优先级内容分配给有经验的审核员
    if (content.priority && content.priority > 4) {
      const experienced = reviewers.filter(r => 
        r.expertise?.length > 0 || r.role === 'admin' || r.role === 'superadmin'
      );
      if (experienced.length > 0) {
        // 按工作量排序
        experienced.sort((a, b) => (a.currentWorkload || 0) - (b.currentWorkload || 0));
        return experienced[0];
      }
    }
    
    // 其他情况按工作量均衡分配
    return this.assignWorkloadBalanced(reviewers);
  }
  
  /**
   * 更新审核员工作量
   */
  private async updateReviewerWorkload(reviewerId: string): Promise<void> {
    try {
      // 更新最后分配时间
      await this.prisma.user.update({
        where: {
          id: reviewerId
        },
        data: {
          lastAssignedAt: new Date()
        }
      });
    } catch (error) {
      console.error('更新审核员工作量失败:', error);
      throw error;
    }
  }
}
