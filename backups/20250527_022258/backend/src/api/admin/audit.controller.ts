/**
 * 审计日志控制器
 */

import { Context } from 'hono';
import { AuditLogService, AuditAction, AuditSeverity } from '../../services/auditLogService';
import { Env } from '../../types';

/**
 * 获取审计日志列表
 */
export const getAuditLogs = async (c: Context<{ Bindings: Env }>) => {
  try {
    // 获取查询参数
    const userId = c.req.query('userId');
    const action = c.req.query('action') as AuditAction;
    const resourceType = c.req.query('resourceType');
    const resourceId = c.req.query('resourceId');
    const severity = c.req.query('severity') as AuditSeverity;
    const startDate = c.req.query('startDate') ? new Date(c.req.query('startDate')) : undefined;
    const endDate = c.req.query('endDate') ? new Date(c.req.query('endDate')) : undefined;
    const page = parseInt(c.req.query('page') || '1', 10);
    const pageSize = parseInt(c.req.query('pageSize') || '20', 10);
    
    // 获取审计日志服务实例
    const auditLogService = AuditLogService.getInstance();
    
    // 确保审计日志服务已初始化
    if (!auditLogService.isInitialized) {
      await auditLogService.initialize();
    }
    
    // 获取审计日志
    const result = await auditLogService.getLogs(
      {
        userId,
        action,
        resourceType,
        resourceId,
        severity,
        startDate,
        endDate
      },
      {
        page,
        pageSize
      },
      c.env
    );
    
    // 返回结果
    return c.json({
      success: true,
      logs: result.logs,
      pagination: {
        page: result.page,
        pageSize: result.pageSize,
        total: result.total,
        totalPages: result.totalPages
      }
    });
  } catch (error) {
    console.error('Error getting audit logs:', error);
    return c.json({
      success: false,
      error: '获取审计日志失败'
    }, 500);
  }
};

/**
 * 获取单条审计日志详情
 */
export const getAuditLog = async (c: Context<{ Bindings: Env }>) => {
  try {
    // 获取日志ID
    const id = c.req.param('id');
    
    // 获取审计日志服务实例
    const auditLogService = AuditLogService.getInstance();
    
    // 确保审计日志服务已初始化
    if (!auditLogService.isInitialized) {
      await auditLogService.initialize();
    }
    
    // 获取审计日志
    const log = await auditLogService.getLog(id, c.env);
    
    // 检查是否存在
    if (!log) {
      return c.json({
        success: false,
        error: '审计日志不存在'
      }, 404);
    }
    
    // 返回结果
    return c.json({
      success: true,
      log
    });
  } catch (error) {
    console.error('Error getting audit log:', error);
    return c.json({
      success: false,
      error: '获取审计日志详情失败'
    }, 500);
  }
};

/**
 * 删除审计日志
 */
export const deleteAuditLog = async (c: Context<{ Bindings: Env }>) => {
  try {
    // 获取日志ID
    const id = c.req.param('id');
    
    // 获取审计日志服务实例
    const auditLogService = AuditLogService.getInstance();
    
    // 确保审计日志服务已初始化
    if (!auditLogService.isInitialized) {
      await auditLogService.initialize();
    }
    
    // 删除审计日志
    await auditLogService.deleteLog(id, c.env);
    
    // 记录删除操作
    const user = c.get('user');
    const userId = user.username || user.id || 'unknown';
    const ipAddress = c.req.headers.get('CF-Connecting-IP') || c.req.headers.get('X-Forwarded-For') || 'unknown';
    const userAgent = c.req.headers.get('User-Agent') || 'unknown';
    
    await auditLogService.log({
      userId,
      action: AuditAction.DATA_DELETE,
      resourceType: 'auditLog',
      resourceId: id,
      details: {
        message: '删除审计日志'
      },
      ipAddress,
      userAgent,
      severity: AuditSeverity.WARNING
    }, c.env);
    
    // 返回结果
    return c.json({
      success: true,
      message: '审计日志已删除'
    });
  } catch (error) {
    console.error('Error deleting audit log:', error);
    return c.json({
      success: false,
      error: '删除审计日志失败'
    }, 500);
  }
};

/**
 * 清理过期审计日志
 */
export const cleanupAuditLogs = async (c: Context<{ Bindings: Env }>) => {
  try {
    // 获取请求数据
    const data = await c.req.json();
    const { days } = data;
    
    // 验证参数
    if (!days || days < 30) {
      return c.json({
        success: false,
        error: '保留天数不能少于30天'
      }, 400);
    }
    
    // 获取审计日志服务实例
    const auditLogService = AuditLogService.getInstance();
    
    // 确保审计日志服务已初始化
    if (!auditLogService.isInitialized) {
      await auditLogService.initialize();
    }
    
    // 清理过期日志
    const count = await auditLogService.cleanupLogs(days, c.env);
    
    // 记录清理操作
    const user = c.get('user');
    const userId = user.username || user.id || 'unknown';
    const ipAddress = c.req.headers.get('CF-Connecting-IP') || c.req.headers.get('X-Forwarded-For') || 'unknown';
    const userAgent = c.req.headers.get('User-Agent') || 'unknown';
    
    await auditLogService.log({
      userId,
      action: AuditAction.DATA_DELETE,
      resourceType: 'auditLog',
      details: {
        message: `清理${days}天前的审计日志`,
        count
      },
      ipAddress,
      userAgent,
      severity: AuditSeverity.WARNING
    }, c.env);
    
    // 返回结果
    return c.json({
      success: true,
      message: `已清理${count}条过期审计日志`,
      count
    });
  } catch (error) {
    console.error('Error cleaning up audit logs:', error);
    return c.json({
      success: false,
      error: '清理审计日志失败'
    }, 500);
  }
};

/**
 * 生成审核员操作审计报告
 */
export const generateReviewerAuditReport = async (c: Context<{ Bindings: Env }>) => {
  try {
    // 获取查询参数
    const reviewerId = c.req.query('reviewerId');
    const startDate = c.req.query('startDate') ? new Date(c.req.query('startDate')) : undefined;
    const endDate = c.req.query('endDate') ? new Date(c.req.query('endDate')) : undefined;
    
    // 验证参数
    if (!startDate || !endDate) {
      return c.json({
        success: false,
        error: '开始日期和结束日期是必需的'
      }, 400);
    }
    
    // 获取审计日志服务实例
    const auditLogService = AuditLogService.getInstance();
    
    // 确保审计日志服务已初始化
    if (!auditLogService.isInitialized) {
      await auditLogService.initialize();
    }
    
    // 获取审核员操作日志
    const result = await auditLogService.getLogs(
      {
        userId: reviewerId,
        action: undefined, // 获取所有操作
        startDate,
        endDate
      },
      {
        page: 1,
        pageSize: 1000 // 获取足够多的日志以生成报告
      },
      c.env
    );
    
    // 分析日志
    const logs = result.logs;
    
    // 按操作类型统计
    const actionStats: Record<string, number> = {};
    logs.forEach(log => {
      if (!actionStats[log.action]) {
        actionStats[log.action] = 0;
      }
      actionStats[log.action]++;
    });
    
    // 按资源类型统计
    const resourceStats: Record<string, number> = {};
    logs.forEach(log => {
      if (!resourceStats[log.resourceType]) {
        resourceStats[log.resourceType] = 0;
      }
      resourceStats[log.resourceType]++;
    });
    
    // 按严重性统计
    const severityStats: Record<string, number> = {};
    logs.forEach(log => {
      if (!severityStats[log.severity]) {
        severityStats[log.severity] = 0;
      }
      severityStats[log.severity]++;
    });
    
    // 按日期统计
    const dateStats: Record<string, number> = {};
    logs.forEach(log => {
      const date = new Date(log.createdAt).toISOString().split('T')[0];
      if (!dateStats[date]) {
        dateStats[date] = 0;
      }
      dateStats[date]++;
    });
    
    // 生成报告
    const report = {
      reviewerId,
      period: {
        startDate: startDate.toISOString(),
        endDate: endDate.toISOString()
      },
      summary: {
        totalOperations: logs.length,
        actionStats,
        resourceStats,
        severityStats,
        dateStats
      },
      details: logs.slice(0, 100) // 只包含前100条日志
    };
    
    // 记录报告生成操作
    const user = c.get('user');
    const userId = user.username || user.id || 'unknown';
    const ipAddress = c.req.headers.get('CF-Connecting-IP') || c.req.headers.get('X-Forwarded-For') || 'unknown';
    const userAgent = c.req.headers.get('User-Agent') || 'unknown';
    
    await auditLogService.log({
      userId,
      action: AuditAction.DATA_EXPORT,
      resourceType: 'auditReport',
      details: {
        message: '生成审核员操作审计报告',
        reviewerId,
        period: {
          startDate: startDate.toISOString(),
          endDate: endDate.toISOString()
        }
      },
      ipAddress,
      userAgent,
      severity: AuditSeverity.INFO
    }, c.env);
    
    // 返回结果
    return c.json({
      success: true,
      report
    });
  } catch (error) {
    console.error('Error generating reviewer audit report:', error);
    return c.json({
      success: false,
      error: '生成审核员操作审计报告失败'
    }, 500);
  }
};
