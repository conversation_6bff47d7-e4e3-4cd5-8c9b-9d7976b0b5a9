/**
 * 加密工具函数
 */

/**
 * 生成哈希值
 * @param value 要哈希的值
 * @returns 哈希值
 */
export function generateHash(value: string): string {
  // 在实际应用中，应该使用更安全的方法，如SHA-256
  // 这里使用简化的方法，仅用于演示
  const encoder = new TextEncoder();
  const data = encoder.encode(value);
  
  // 简单的哈希算法
  let hash = 0;
  for (let i = 0; i < data.length; i++) {
    hash = ((hash << 5) - hash) + data[i];
    hash |= 0; // 转换为32位整数
  }
  
  // 转换为16进制字符串
  return (hash >>> 0).toString(16).padStart(8, '0');
}
