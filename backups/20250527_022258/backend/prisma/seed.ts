import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

async function main() {
  console.log('Seeding database...');

  // Seed sensitive words
  const sensitiveWords = [
    { word: 'sensitive1', level: 3 },
    { word: 'sensitive2', level: 3 },
    { word: 'sensitive3', level: 2 },
    { word: 'sensitive4', level: 2 },
    { word: 'sensitive5', level: 1 },
  ];

  for (const word of sensitiveWords) {
    await prisma.sensitiveWord.upsert({
      where: { word: word.word },
      update: {},
      create: {
        word: word.word,
        level: word.level,
      },
    });
  }

  console.log('Seeded sensitive words');

  // Seed mock users
  const users = [
    { email: '<EMAIL>', emailVerified: true },
    { email: '<EMAIL>', emailVerified: true },
    { email: '<EMAIL>', emailVerified: false },
  ];

  for (const user of users) {
    await prisma.user.upsert({
      where: { email: user.email },
      update: {},
      create: {
        email: user.email,
        emailVerified: user.emailVerified,
        ipAddress: '127.0.0.1',
        userAgent: 'Mozilla/5.0',
      },
    });
  }

  console.log('Seeded users');

  // Seed mock questionnaire responses
  const educationLevels = ['高中/中专', '大专', '本科', '硕士', '博士'];
  const majors = ['理学', '工学', '文学', '经济学', '管理学', '法学', '教育学', '医学', '农学', '艺术学'];
  const regions = ['北上广深', '省会城市', '二线城市', '三四线城市', '县城或乡镇', '海外'];
  const positions = ['技术研发', '产品运营', '市场营销', '人力资源', '财务会计', '行政管理', '教育培训', '医疗卫生'];
  const salaryRanges = ['3000元以下', '3000-5000元', '5000-8000元', '8000-12000元', '12000-20000元', '20000元以上'];
  const employmentStatuses = ['已就业', '待业中', '自由职业', '创业', '其他'];
  const industries = ['互联网/IT', '金融', '教育', '医疗健康', '制造业', '零售/快消', '文化/传媒', '房地产', '政府/事业单位'];
  const unemploymentDurations = ['3个月以内', '3-6个月', '6-12个月', '1年以上', '应届生尚未就业'];

  // Generate 50 mock responses
  for (let i = 0; i < 50; i++) {
    const isAnonymous = Math.random() > 0.5;
    const userId = isAnonymous ? null : Math.floor(Math.random() * 3) + 1;
    const employmentStatus = employmentStatuses[Math.floor(Math.random() * employmentStatuses.length)];
    const isEmployed = employmentStatus === '已就业';
    const isUnemployed = employmentStatus === '待业中';
    const regretMajor = Math.random() > 0.5;
    const careerChangeIntention = Math.random() > 0.4;

    await prisma.questionnaireResponse.create({
      data: {
        sequenceNumber: `QR${Date.now()}-${i.toString().padStart(3, '0')}`,
        userId,
        isAnonymous,
        ipAddress: '127.0.0.1',

        // 1. Personal information
        educationLevel: educationLevels[Math.floor(Math.random() * educationLevels.length)],
        major: majors[Math.floor(Math.random() * majors.length)],
        graduationYear: 2018 + Math.floor(Math.random() * 6),
        region: regions[Math.floor(Math.random() * regions.length)],

        // 2. Employment expectations
        expectedPosition: positions[Math.floor(Math.random() * positions.length)],
        expectedSalaryRange: salaryRanges[Math.floor(Math.random() * salaryRanges.length)],
        expectedWorkHours: 30 + Math.floor(Math.random() * 40),
        expectedVacationDays: 5 + Math.floor(Math.random() * 25),

        // 3. Work experience
        employmentStatus,
        currentIndustry: isEmployed ? industries[Math.floor(Math.random() * industries.length)] : null,
        currentPosition: isEmployed ? positions[Math.floor(Math.random() * positions.length)] : null,
        jobSatisfaction: isEmployed ? (Math.floor(Math.random() * 5) + 1).toString() : null,

        // 4. Unemployment status
        unemploymentDuration: isUnemployed ? unemploymentDurations[Math.floor(Math.random() * unemploymentDurations.length)] : null,
        unemploymentReason: isUnemployed ? '行业不景气，竞争激烈，找不到合适的工作' : null,
        jobHuntingDifficulty: isUnemployed ? Math.floor(Math.random() * 5) + 1 : null,

        // 5. Career change and reflection
        regretMajor,
        preferredMajor: regretMajor ? majors[Math.floor(Math.random() * majors.length)] : null,
        careerChangeIntention,
        careerChangeTarget: careerChangeIntention ? industries[Math.floor(Math.random() * industries.length)] : null,

        // 6. Advice and feedback
        adviceForStudents: '多参加实习，提前了解行业情况，培养实际技能，不要只关注理论知识。',
        observationOnEmployment: '就业压力大，竞争激烈，需要不断学习和提升自己的能力。',
      },
    });
  }

  console.log('Seeded questionnaire responses');

  // Seed mock stories
  const storyTitles = [
    '我的求职经历与感悟',
    '从校园到职场的转变',
    '如何在竞争激烈的环境中脱颖而出',
    '转行经验分享',
    '面试技巧总结',
    '给应届生的几点建议',
    '工作与生活的平衡',
    '职场新人必看指南',
    '我的跳槽经历',
    '如何处理职场人际关系',
  ];

  const storyContents = [
    '毕业后我经历了三个月的求职期，投了上百份简历，参加了二十多场面试，最终找到了一份满意的工作。期间有很多挫折和困难，但也学到了很多东西。',
    '从学生到职场人的转变并不容易，需要适应新的环境和工作方式。最重要的是保持学习的态度，不断提升自己的能力。',
    '在竞争激烈的就业市场中，除了专业知识外，软技能也非常重要。良好的沟通能力、团队协作能力和解决问题的能力都是雇主看重的。',
    '我从传统行业转到了互联网行业，过程中遇到了很多挑战，但也获得了更好的发展机会。转行需要勇气和决心，也需要付出更多的努力。',
    '面试前的准备非常重要，包括了解公司背景、梳理自己的经历、准备常见问题的回答等。面试中要保持自信，展示自己的优势和特长。',
    '给应届生的建议：多参加实习，了解行业情况；培养实际技能，不要只关注理论知识；建立人脉网络，多参加行业活动；保持积极心态，不要害怕失败。',
    '工作固然重要，但也要注意生活的平衡。合理安排时间，保持健康的生活方式，培养兴趣爱好，都有助于提高工作效率和生活质量。',
    '职场新人要注意的几点：虚心学习，不要怕问问题；主动承担责任，展示自己的能力；建立良好的人际关系；保持积极的工作态度；不断学习和提升自己。',
    '我在第一份工作干了两年后选择了跳槽，主要是因为发展空间有限。跳槽前要慎重考虑，评估利弊，做好充分准备。',
    '职场人际关系很重要，要学会与同事、上级和下级相处。保持真诚、尊重他人、积极沟通、适当表达自己的想法，都有助于建立良好的职场关系。',
  ];

  const tags = [
    ['job-hunting', 'advice'],
    ['career-change', 'work-life'],
    ['job-hunting', 'interview'],
    ['career-change', 'advice'],
    ['interview', 'job-hunting'],
    ['advice', 'job-hunting'],
    ['work-life', 'advice'],
    ['advice', 'work-life'],
    ['career-change', 'salary'],
    ['work-life', 'advice'],
  ];

  for (let i = 0; i < 10; i++) {
    const isAnonymous = Math.random() > 0.5;
    const userId = isAnonymous ? null : Math.floor(Math.random() * 3) + 1;
    const likes = Math.floor(Math.random() * 50);
    const dislikes = Math.floor(Math.random() * 10);

    await prisma.story.create({
      data: {
        userId,
        isAnonymous,
        title: storyTitles[i],
        content: `${storyContents[i]}\n\n标签: ${tags[i].join(', ')}`,
        ipAddress: '127.0.0.1',
        likes,
        dislikes,
        status: 'approved',
      },
    });
  }

  console.log('Seeded stories');

  console.log('Database seeding completed');
}

main()
  .catch((e) => {
    console.error(e);
    process.exit(1);
  })
  .finally(async () => {
    await prisma.$disconnect();
  });
