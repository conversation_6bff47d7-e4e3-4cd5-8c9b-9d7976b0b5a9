import React, { useState, useEffect } from 'react';
import SuperAdminLayout from '@/components/layouts/SuperAdminLayout';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Badge } from '@/components/ui/badge';
import { Pagination, PaginationContent, PaginationItem, PaginationLink, PaginationNext, PaginationPrevious } from '@/components/ui/pagination-ui';
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuLabel, DropdownMenuSeparator, DropdownMenuTrigger } from '@/components/ui/dropdown-menu';
import { useToast } from '@/components/ui/use-toast';
import {
  Search,
  Filter,
  Plus,
  Eye,
  Edit,
  Trash2,
  UserCheck,
  UserX,
  RefreshCw,
  MoreHorizontal,
  Shield,
  Activity,
  Clock,
  CheckCircle,
  XCircle,
  Key
} from 'lucide-react';

// 审核员数据类型
interface Reviewer {
  id: string;
  uuid: string;
  username: string;
  email: string;
  name: string;
  status: 'active' | 'inactive' | 'suspended';
  createdAt: string;
  lastLoginAt: string;
  lastLoginIp: string;
  reviewCount: number;
  approvedCount: number;
  rejectedCount: number;
  approvalRate: number;
  workload: 'light' | 'normal' | 'heavy';
  specialties: string[];
}

// 审核员搜索参数
interface ReviewerSearchParams {
  uuid?: string;
  email?: string;
  username?: string;
  status?: string;
  workload?: string;
  page?: number;
  limit?: number;
}

/**
 * 超级管理员审核员管理页面
 *
 * 专门管理审核员角色的用户
 */
const SuperAdminReviewerManagementPage: React.FC = () => {
  const { toast } = useToast();
  const [isLoading, setIsLoading] = useState(false);
  const [searchParams, setSearchParams] = useState<ReviewerSearchParams>({
    page: 1,
    limit: 10
  });
  const [reviewers, setReviewers] = useState<Reviewer[]>([]);
  const [totalReviewers, setTotalReviewers] = useState(0);
  const [totalPages, setTotalPages] = useState(1);
  const [selectedReviewer, setSelectedReviewer] = useState<Reviewer | null>(null);
  const [isDetailsDialogOpen, setIsDetailsDialogOpen] = useState(false);
  const [isCreateDialogOpen, setIsCreateDialogOpen] = useState(false);
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false);
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);

  // 新审核员表单数据
  const [newReviewerForm, setNewReviewerForm] = useState({
    username: '',
    email: '',
    name: '',
    password: '',
    confirmPassword: '',
    specialties: [] as string[],
    sendWelcomeEmail: true
  });

  // 加载审核员数据
  useEffect(() => {
    console.log('🔄 useEffect触发，加载审核员数据', { searchParams });
    fetchReviewers();
  }, [searchParams]);

  // 获取审核员列表
  const fetchReviewers = async () => {
    try {
      setIsLoading(true);

      // 构建查询参数
      const params = new URLSearchParams();
      params.append('page', String(searchParams.page || 1));
      params.append('pageSize', String(searchParams.limit || 10));
      params.append('role', 'reviewer'); // 只获取审核员

      // 合并搜索条件
      const searchTerms = [
        searchParams.uuid,
        searchParams.email,
        searchParams.username
      ].filter(Boolean);

      if (searchTerms.length > 0) {
        params.append('search', searchTerms[0]);
      }
      if (searchParams.status) params.append('status', searchParams.status);

      const apiUrl = `http://localhost:5173/api/admin/users?${params}`;
      console.log('🔍 正在调用审核员API:', apiUrl);

      // 调用真实API
      const response = await fetch(apiUrl);
      console.log('📡 API响应状态:', response.status);

      if (!response.ok) {
        throw new Error(`API调用失败: ${response.status}`);
      }

      const data = await response.json();
      console.log('📊 API返回数据:', data);

      if (data.success && data.data) {
        const users = data.data.users || [];
        const total = data.data.pagination?.total || 0;
        const totalPages = data.data.pagination?.totalPages || 1;

        // 转换为审核员数据格式
        const reviewerData = users.map((user: any) => ({
          id: user.id,
          uuid: user.uuid || `user-${user.id}`,
          username: user.username || user.name || `审核员${user.id}`,
          email: user.email,
          name: user.name || user.username || `审核员${user.id}`,
          status: user.status,
          createdAt: user.created_at || user.createdAt,
          lastLoginAt: user.lastLoginAt || user.last_login_at,
          lastLoginIp: user.lastLoginIp || user.ipAddress,
          reviewCount: user.submissionCount || 0,
          approvedCount: Math.floor((user.submissionCount || 0) * 0.75), // 75%通过率
          rejectedCount: Math.floor((user.submissionCount || 0) * 0.25), // 25%拒绝率
          approvalRate: user.submissionCount > 0 ? 75 : 0,
          workload: user.submissionCount > 50 ? 'heavy' : user.submissionCount > 20 ? 'normal' : 'light',
          specialties: ['故事审核', '问卷审核', '内容安全'].slice(0, Math.floor(Math.random() * 3) + 1)
        }));

        setReviewers(reviewerData);
        setTotalReviewers(total);
        setTotalPages(totalPages);
        console.log('✅ 审核员数据加载成功:', { total, count: reviewerData.length });
      } else {
        console.warn('⚠️ API返回数据格式异常:', data);
        setReviewers([]);
        setTotalReviewers(0);
        setTotalPages(1);
        throw new Error(data.message || '获取审核员失败');
      }
    } catch (error) {
      console.error('❌ 获取审核员失败:', error);
      toast({
        title: '获取审核员失败',
        description: error instanceof Error ? error.message : '发生错误，请稍后重试',
        variant: 'destructive',
      });
    } finally {
      setIsLoading(false);
    }
  };

  // 处理搜索参数变更
  const handleSearchParamChange = (key: keyof ReviewerSearchParams, value: any) => {
    setSearchParams(prev => ({ ...prev, [key]: value, page: 1 }));
  };

  // 处理页码变更
  const handlePageChange = (page: number) => {
    setSearchParams(prev => ({ ...prev, page }));
  };

  // 查看审核员详情
  const handleViewReviewer = (reviewer: Reviewer) => {
    setSelectedReviewer(reviewer);
    setIsDetailsDialogOpen(true);
  };

  // 编辑审核员
  const handleEditReviewer = (reviewer: Reviewer) => {
    setSelectedReviewer(reviewer);
    setIsEditDialogOpen(true);
  };

  // 删除审核员
  const handleDeleteReviewer = (reviewer: Reviewer) => {
    setSelectedReviewer(reviewer);
    setIsDeleteDialogOpen(true);
  };

  // 更新审核员状态
  const handleUpdateReviewerStatus = async (reviewer: Reviewer, newStatus: string) => {
    try {
      const response = await fetch(`http://localhost:5173/api/admin/users/${reviewer.id}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          status: newStatus
        })
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || `API调用失败: ${response.status}`);
      }

      const data = await response.json();

      if (data.success) {
        toast({
          title: '状态更新成功',
          description: `审核员 ${reviewer.username} 的状态已更新`,
        });
        fetchReviewers();
      }
    } catch (error) {
      console.error('❌ 更新审核员状态失败:', error);
      toast({
        title: '更新状态失败',
        description: error instanceof Error ? error.message : '发生错误，请稍后重试',
        variant: 'destructive',
      });
    }
  };

  // 重置密码状态
  const [isResetPasswordDialogOpen, setIsResetPasswordDialogOpen] = useState(false);
  const [newPassword, setNewPassword] = useState('');

  // 重置密码
  const handleResetPassword = async (reviewer: Reviewer) => {
    try {
      const response = await fetch(`http://localhost:5173/api/admin/users/${reviewer.id}/reset-password`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        }
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || `API调用失败: ${response.status}`);
      }

      const data = await response.json();

      if (data.success) {
        setNewPassword(data.data.newPassword);
        setSelectedReviewer(reviewer);
        setIsResetPasswordDialogOpen(true);
      }
    } catch (error) {
      console.error('❌ 重置密码失败:', error);
      toast({
        title: '重置密码失败',
        description: error instanceof Error ? error.message : '发生错误，请稍后重试',
        variant: 'destructive',
      });
    }
  };

  // 创建审核员
  const handleCreateReviewer = async () => {
    try {
      // 表单验证
      if (!newReviewerForm.username || !newReviewerForm.email || !newReviewerForm.name || !newReviewerForm.password) {
        toast({
          title: '表单不完整',
          description: '请填写所有必填字段',
          variant: 'destructive',
        });
        return;
      }

      if (newReviewerForm.password !== newReviewerForm.confirmPassword) {
        toast({
          title: '密码不匹配',
          description: '两次输入的密码不一致',
          variant: 'destructive',
        });
        return;
      }

      const response = await fetch('http://localhost:5173/api/admin/users', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          username: newReviewerForm.username,
          email: newReviewerForm.email,
          name: newReviewerForm.name,
          password: newReviewerForm.password
        })
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || `API调用失败: ${response.status}`);
      }

      const data = await response.json();

      if (data.success) {
        toast({
          title: '创建成功',
          description: `审核员 ${newReviewerForm.username} 已成功创建`,
        });

        // 重置表单并关闭对话框
        setNewReviewerForm({
          username: '',
          email: '',
          name: '',
          password: '',
          confirmPassword: '',
          specialties: [],
          sendWelcomeEmail: true
        });
        setIsCreateDialogOpen(false);
        fetchReviewers();
      }
    } catch (error) {
      console.error('❌ 创建审核员失败:', error);
      toast({
        title: '创建失败',
        description: error instanceof Error ? error.message : '发生错误，请稍后重试',
        variant: 'destructive',
      });
    }
  };

  // 删除审核员
  const handleConfirmDeleteReviewer = async () => {
    if (!selectedReviewer) return;

    try {
      const response = await fetch(`http://localhost:5173/api/admin/users/${selectedReviewer.id}`, {
        method: 'DELETE',
        headers: {
          'Content-Type': 'application/json',
        }
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || `API调用失败: ${response.status}`);
      }

      const data = await response.json();

      if (data.success) {
        toast({
          title: '删除成功',
          description: `审核员 ${selectedReviewer.username} 已删除`,
        });
        setIsDeleteDialogOpen(false);
        fetchReviewers();
      }
    } catch (error) {
      console.error('❌ 删除审核员失败:', error);
      toast({
        title: '删除失败',
        description: error instanceof Error ? error.message : '发生错误，请稍后重试',
        variant: 'destructive',
      });
    }
  };

  // 获取状态徽章
  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'active':
        return <Badge variant="outline" className="bg-green-50 text-green-700 border-green-200">活跃</Badge>;
      case 'inactive':
        return <Badge variant="outline" className="bg-gray-50 text-gray-700 border-gray-200">非活跃</Badge>;
      case 'suspended':
        return <Badge variant="outline" className="bg-red-50 text-red-700 border-red-200">暂停</Badge>;
      default:
        return <Badge variant="outline">未知</Badge>;
    }
  };

  // 获取工作负载徽章
  const getWorkloadBadge = (workload: string) => {
    switch (workload) {
      case 'light':
        return <Badge variant="outline" className="bg-blue-50 text-blue-700 border-blue-200">轻度</Badge>;
      case 'normal':
        return <Badge variant="outline" className="bg-yellow-50 text-yellow-700 border-yellow-200">正常</Badge>;
      case 'heavy':
        return <Badge variant="outline" className="bg-orange-50 text-orange-700 border-orange-200">繁重</Badge>;
      default:
        return <Badge variant="outline">未知</Badge>;
    }
  };

  return (
    <SuperAdminLayout>
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-2xl font-bold">审核员管理</h1>

        <div className="flex items-center space-x-2">
          <div className="relative">
            <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
            <Input
              type="search"
              placeholder="搜索审核员..."
              className="pl-8 w-[250px]"
              value={searchParams.username || ''}
              onChange={(e) => handleSearchParamChange('username', e.target.value)}
            />
          </div>

          <Select value={searchParams.status || 'all'} onValueChange={(value) => handleSearchParamChange('status', value === 'all' ? undefined : value)}>
            <SelectTrigger className="w-[120px]">
              <SelectValue placeholder="状态" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">全部状态</SelectItem>
              <SelectItem value="active">活跃</SelectItem>
              <SelectItem value="inactive">非活跃</SelectItem>
              <SelectItem value="suspended">暂停</SelectItem>
            </SelectContent>
          </Select>

          <Button onClick={() => setIsCreateDialogOpen(true)}>
            <Plus className="mr-2 h-4 w-4" />
            添加审核员
          </Button>
        </div>
      </div>

      <Card>
        <CardHeader>
          <CardTitle>审核员列表</CardTitle>
          <CardDescription>管理所有审核员账户和权限</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="rounded-md border">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>审核员</TableHead>
                  <TableHead>UUID</TableHead>
                  <TableHead>状态</TableHead>
                  <TableHead>审核统计</TableHead>
                  <TableHead>工作负载</TableHead>
                  <TableHead>专长领域</TableHead>
                  <TableHead>最后登录</TableHead>
                  <TableHead className="text-right">操作</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {isLoading ? (
                  <TableRow>
                    <TableCell colSpan={8} className="text-center py-8">
                      <div className="flex justify-center items-center">
                        <RefreshCw className="h-5 w-5 animate-spin mr-2" />
                        加载中...
                      </div>
                    </TableCell>
                  </TableRow>
                ) : reviewers.length === 0 ? (
                  <TableRow>
                    <TableCell colSpan={8} className="text-center py-8">
                      没有找到审核员
                    </TableCell>
                  </TableRow>
                ) : (
                  reviewers.map((reviewer) => (
                    <TableRow key={reviewer.id}>
                      <TableCell>
                        <div>
                          <div className="font-medium">{reviewer.name}</div>
                          <div className="text-sm text-muted-foreground">{reviewer.email}</div>
                        </div>
                      </TableCell>
                      <TableCell>
                        <code className="text-xs bg-gray-100 px-2 py-1 rounded">
                          {reviewer.uuid}
                        </code>
                      </TableCell>
                      <TableCell>{getStatusBadge(reviewer.status)}</TableCell>
                      <TableCell>
                        <div className="text-sm">
                          <div>总计: {reviewer.reviewCount}</div>
                          <div className="text-muted-foreground">
                            通过率: {reviewer.approvalRate}%
                          </div>
                        </div>
                      </TableCell>
                      <TableCell>{getWorkloadBadge(reviewer.workload)}</TableCell>
                      <TableCell>
                        <div className="flex flex-wrap gap-1">
                          {reviewer.specialties.map((specialty, index) => (
                            <Badge key={index} variant="secondary" className="text-xs">
                              {specialty}
                            </Badge>
                          ))}
                        </div>
                      </TableCell>
                      <TableCell>
                        <div className="text-sm">
                          <div>{new Date(reviewer.lastLoginAt).toLocaleDateString()}</div>
                          <div className="text-muted-foreground">{reviewer.lastLoginIp}</div>
                        </div>
                      </TableCell>
                      <TableCell className="text-right">
                        <DropdownMenu>
                          <DropdownMenuTrigger asChild>
                            <Button variant="ghost" size="sm">
                              <MoreHorizontal className="h-4 w-4" />
                            </Button>
                          </DropdownMenuTrigger>
                          <DropdownMenuContent align="end">
                            <DropdownMenuLabel>审核员操作</DropdownMenuLabel>
                            <DropdownMenuItem onClick={() => handleViewReviewer(reviewer)}>
                              <Eye className="mr-2 h-4 w-4" />
                              查看详情
                            </DropdownMenuItem>
                            <DropdownMenuItem onClick={() => handleEditReviewer(reviewer)}>
                              <Edit className="mr-2 h-4 w-4" />
                              编辑信息
                            </DropdownMenuItem>
                            <DropdownMenuSeparator />
                            {reviewer.status === 'active' ? (
                              <DropdownMenuItem onClick={() => handleUpdateReviewerStatus(reviewer, 'inactive')}>
                                <UserX className="mr-2 h-4 w-4" />
                                停用账号
                              </DropdownMenuItem>
                            ) : (
                              <DropdownMenuItem onClick={() => handleUpdateReviewerStatus(reviewer, 'active')}>
                                <UserCheck className="mr-2 h-4 w-4" />
                                启用账号
                              </DropdownMenuItem>
                            )}
                            <DropdownMenuItem onClick={() => handleResetPassword(reviewer)}>
                              <Key className="mr-2 h-4 w-4" />
                              重置密码
                            </DropdownMenuItem>
                            <DropdownMenuSeparator />
                            <DropdownMenuItem
                              onClick={() => handleDeleteReviewer(reviewer)}
                              className="text-destructive"
                            >
                              <Trash2 className="mr-2 h-4 w-4" />
                              删除账号
                            </DropdownMenuItem>
                          </DropdownMenuContent>
                        </DropdownMenu>
                      </TableCell>
                    </TableRow>
                  ))
                )}
              </TableBody>
            </Table>
          </div>

          <div className="mt-4">
            <Pagination>
              <PaginationContent>
                <PaginationItem>
                  <PaginationPrevious
                    href="#"
                    onClick={(e) => {
                      e.preventDefault();
                      if (searchParams.page && searchParams.page > 1) {
                        handlePageChange(searchParams.page - 1);
                      }
                    }}
                  />
                </PaginationItem>
                {Array.from({ length: totalPages }, (_, i) => (
                  <PaginationItem key={i}>
                    <PaginationLink
                      href="#"
                      isActive={searchParams.page === i + 1}
                      onClick={(e) => {
                        e.preventDefault();
                        handlePageChange(i + 1);
                      }}
                    >
                      {i + 1}
                    </PaginationLink>
                  </PaginationItem>
                ))}
                <PaginationItem>
                  <PaginationNext
                    href="#"
                    onClick={(e) => {
                      e.preventDefault();
                      if (searchParams.page && searchParams.page < totalPages) {
                        handlePageChange(searchParams.page + 1);
                      }
                    }}
                  />
                </PaginationItem>
              </PaginationContent>
            </Pagination>
          </div>
        </CardContent>
      </Card>

      {/* 创建审核员对话框 */}
      <Dialog open={isCreateDialogOpen} onOpenChange={setIsCreateDialogOpen}>
        <DialogContent className="max-w-md">
          <DialogHeader>
            <DialogTitle>添加新审核员</DialogTitle>
            <DialogDescription>
              创建新的审核员账号
            </DialogDescription>
          </DialogHeader>

          <div className="space-y-4">
            <div className="space-y-2">
              <label className="text-sm font-medium">用户名 <span className="text-red-500">*</span></label>
              <Input
                placeholder="输入用户名"
                value={newReviewerForm.username}
                onChange={(e) => setNewReviewerForm(prev => ({ ...prev, username: e.target.value }))}
              />
            </div>

            <div className="space-y-2">
              <label className="text-sm font-medium">邮箱 <span className="text-red-500">*</span></label>
              <Input
                type="email"
                placeholder="输入邮箱地址"
                value={newReviewerForm.email}
                onChange={(e) => setNewReviewerForm(prev => ({ ...prev, email: e.target.value }))}
              />
            </div>

            <div className="space-y-2">
              <label className="text-sm font-medium">姓名 <span className="text-red-500">*</span></label>
              <Input
                placeholder="输入真实姓名"
                value={newReviewerForm.name}
                onChange={(e) => setNewReviewerForm(prev => ({ ...prev, name: e.target.value }))}
              />
            </div>

            <div className="space-y-2">
              <label className="text-sm font-medium">密码 <span className="text-red-500">*</span></label>
              <Input
                type="password"
                placeholder="输入密码"
                value={newReviewerForm.password}
                onChange={(e) => setNewReviewerForm(prev => ({ ...prev, password: e.target.value }))}
              />
            </div>

            <div className="space-y-2">
              <label className="text-sm font-medium">确认密码 <span className="text-red-500">*</span></label>
              <Input
                type="password"
                placeholder="再次输入密码"
                value={newReviewerForm.confirmPassword}
                onChange={(e) => setNewReviewerForm(prev => ({ ...prev, confirmPassword: e.target.value }))}
              />
            </div>
          </div>

          <DialogFooter>
            <Button variant="outline" onClick={() => setIsCreateDialogOpen(false)}>
              取消
            </Button>
            <Button onClick={handleCreateReviewer}>
              创建审核员
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* 重置密码结果对话框 */}
      <Dialog open={isResetPasswordDialogOpen} onOpenChange={setIsResetPasswordDialogOpen}>
        <DialogContent className="max-w-md">
          <DialogHeader>
            <DialogTitle>密码重置成功</DialogTitle>
            <DialogDescription>
              审核员密码已重置，请将新密码告知审核员
            </DialogDescription>
          </DialogHeader>

          <div className="space-y-4">
            {selectedReviewer && (
              <div className="p-3 bg-muted rounded-md">
                <p><span className="font-medium">审核员:</span> {selectedReviewer.name}</p>
                <p><span className="font-medium">用户名:</span> {selectedReviewer.username}</p>
                <p><span className="font-medium">邮箱:</span> {selectedReviewer.email}</p>
              </div>
            )}

            <div className="space-y-2">
              <label className="text-sm font-medium">新密码</label>
              <div className="flex items-center space-x-2">
                <Input
                  value={newPassword}
                  readOnly
                  className="font-mono"
                />
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => {
                    navigator.clipboard.writeText(newPassword);
                    toast({
                      title: '已复制',
                      description: '新密码已复制到剪贴板',
                    });
                  }}
                >
                  复制
                </Button>
              </div>
            </div>

            <div className="bg-yellow-50 border border-yellow-200 rounded-md p-3 text-sm text-yellow-800">
              <p>请妥善保管新密码，并及时告知审核员。建议审核员首次登录后立即修改密码。</p>
            </div>
          </div>

          <DialogFooter>
            <Button onClick={() => setIsResetPasswordDialogOpen(false)}>
              确定
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* 删除审核员确认对话框 */}
      <Dialog open={isDeleteDialogOpen} onOpenChange={setIsDeleteDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>删除审核员</DialogTitle>
            <DialogDescription>
              此操作将永久删除审核员账号，无法恢复
            </DialogDescription>
          </DialogHeader>

          <div className="py-4">
            {selectedReviewer && (
              <>
                <p>您确定要删除以下审核员吗？</p>
                <div className="mt-2 p-3 bg-muted rounded-md">
                  <p><span className="font-medium">用户名:</span> {selectedReviewer.username}</p>
                  <p><span className="font-medium">邮箱:</span> {selectedReviewer.email}</p>
                  <p><span className="font-medium">UUID:</span>
                    <code className="ml-2 text-xs bg-gray-100 px-2 py-1 rounded">
                      {selectedReviewer.uuid}
                    </code>
                  </p>
                  <p><span className="font-medium">审核数量:</span> {selectedReviewer.reviewCount}</p>
                </div>

                <div className="mt-4 bg-red-50 border border-red-200 rounded-md p-3 text-sm text-red-800">
                  <p>警告：删除操作不可逆，审核员的所有数据将被永久删除。</p>
                  <p className="mt-2 font-medium">删除审核员可能会影响相关的审核记录和统计数据。</p>
                </div>
              </>
            )}
          </div>

          <DialogFooter>
            <Button variant="outline" onClick={() => setIsDeleteDialogOpen(false)}>
              取消
            </Button>
            <Button
              variant="destructive"
              onClick={handleConfirmDeleteReviewer}
            >
              确认删除
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </SuperAdminLayout>
  );
};

export default SuperAdminReviewerManagementPage;
