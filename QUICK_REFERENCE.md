# 快速修复参考卡片

## 🚨 紧急修复 - 3步解决

### 1️⃣ 运行诊断
```bash
./scripts/quick-health-check.sh
```

### 2️⃣ 常见问题快速修复

#### API 404错误
```bash
# 检查路由注册
grep -r "deidentification" backend/src/api/routes.ts
# 如果没有，添加：
# import deidentificationRoutes from './admin/deidentification.routes';
# api.route('/admin/deidentification', deidentificationRoutes);
```

#### API 401错误
```bash
# 检查认证中间件
grep -A 3 "payload.role" backend/src/middlewares/adminAuth.ts
# 修改为支持多角色：
# (payload.role !== 'admin' && payload.role !== 'superadmin')
```

#### 前端认证失败
```javascript
// 在浏览器控制台运行
localStorage.clear(); location.reload();
```

### 3️⃣ 重新部署
```bash
# 后端
cd backend && npm run deploy

# 前端  
cd frontend && npm run build && npx wrangler pages deploy dist --project-name college-employment-survey
```

---

## 🔧 管理员账户

| 角色 | 用户名 | 密码 | 权限 |
|------|--------|------|------|
| 管理员 | admin1 | admin123 | 基础管理 |
| 审核员 | reviewer1 | admin123 | 内容审核 |
| 超级管理员 | superadmin | admin123 | 全部权限 |

---

## 📋 检查清单

### 每次修复后验证
- [ ] 运行 `./scripts/quick-health-check.sh`
- [ ] 测试管理员登录
- [ ] 检查主要功能页面
- [ ] 查看浏览器控制台无错误

### 关键文件位置
- 路由注册：`backend/src/api/routes.ts`
- 认证中间件：`backend/src/middlewares/adminAuth.ts`
- 管理员配置：`backend/src/controllers/admin.ts`

---

## 🆘 紧急联系

**如果脚本修复失败**：
1. 查看 `TROUBLESHOOTING_GUIDE.md` 详细指南
2. 运行前端认证检查：复制 `scripts/frontend-auth-check.js` 到浏览器控制台
3. 回滚部署：`npx wrangler rollback [deployment-id]`

**系统状态页面**：
- 前端：https://college-employment-survey.pages.dev
- 后端：https://college-employment-survey.aibook2099.workers.dev/health

---

*保存此文件到桌面，紧急时快速参考*
