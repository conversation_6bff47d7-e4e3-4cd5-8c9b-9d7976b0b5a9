
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>代码质量报告 - v3.0-modular</title>
    <style>
        body { font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif; margin: 0; padding: 20px; background: #f5f5f5; }
        .container { max-width: 1200px; margin: 0 auto; background: white; border-radius: 8px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        .header { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 30px; border-radius: 8px 8px 0 0; }
        .content { padding: 30px; }
        .score-card { display: inline-block; background: #f8f9fa; border-radius: 8px; padding: 20px; margin: 10px; text-align: center; min-width: 120px; }
        .score { font-size: 2em; font-weight: bold; margin-bottom: 5px; }
        .excellent { color: #28a745; }
        .good { color: #17a2b8; }
        .fair { color: #ffc107; }
        .poor { color: #dc3545; }
        .section { margin: 30px 0; }
        .section h2 { color: #333; border-bottom: 2px solid #eee; padding-bottom: 10px; }
        .recommendation { background: #f8f9fa; border-left: 4px solid #007bff; padding: 15px; margin: 10px 0; border-radius: 4px; }
        .high { border-left-color: #dc3545; }
        .medium { border-left-color: #ffc107; }
        .low { border-left-color: #28a745; }
        .achievement { color: #28a745; margin: 5px 0; }
        .issue { color: #dc3545; margin: 5px 0; }
        pre { background: #f8f9fa; padding: 15px; border-radius: 4px; overflow-x: auto; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>📊 代码质量报告</h1>
            <p>版本: v3.0-modular | 生成时间: 2025/5/27 03:21:10</p>
        </div>
        
        <div class="content">
            <div class="section">
                <h2>📈 总体评分</h2>
                <div class="score-card">
                    <div class="score excellent">98</div>
                    <div>总体评分</div>
                </div>
                
                    <div class="score-card">
                        <div class="score">98</div>
                        <div>codeQuality</div>
                    </div>
                
            </div>

            <div class="section">
                <h2>✅ 成就</h2>
                <p>暂无成就</p>
            </div>

            <div class="section">
                <h2>⚠️ 问题</h2>
                <div class="issue">❌ ESLint发现 1 个问题</div>
            </div>

            <div class="section">
                <h2>💡 改进建议</h2>
                
                    <div class="recommendation high">
                        <h4>修复ESLint问题</h4>
                        <p>运行 `npm run lint:fix` 自动修复可修复的问题</p>
                        <code>npm run lint:fix</code>
                    </div>
                
                    <div class="recommendation low">
                        <h4>定期运行质量检查</h4>
                        <p>建议每周运行一次完整的质量检查</p>
                        <code>node scripts/generate-quality-report.js</code>
                    </div>
                
            </div>
        </div>
    </div>
</body>
</html>