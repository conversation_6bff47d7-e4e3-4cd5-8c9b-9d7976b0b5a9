import { Badge } from '@/components/ui/badge';

interface StoryTag {
  id: string;
  label: string;
}

interface StoryTagFilterProps {
  tags: StoryTag[];
  selectedTag: string;
  onSelect: (tagId: string) => void;
}

export default function StoryTagFilter({ 
  tags, 
  selectedTag, 
  onSelect 
}: StoryTagFilterProps) {
  return (
    <div className="flex flex-wrap gap-2">
      {tags.map((tag) => (
        <Badge
          key={tag.id}
          variant={selectedTag === tag.id ? 'default' : 'outline'}
          className="cursor-pointer hover:bg-primary/90 hover:text-primary-foreground transition-colors"
          onClick={() => onSelect(tag.id)}
        >
          {tag.label}
        </Badge>
      ))}
    </div>
  );
}
