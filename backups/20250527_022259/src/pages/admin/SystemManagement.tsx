import React, { useState } from 'react';
import { Tabs, Card, Typography, Breadcrumb } from 'antd';
import { 
  HomeOutlined, 
  DashboardOutlined, 
  FileSearchOutlined, 
  DatabaseOutlined,
  SettingOutlined,
  AlertOutlined
} from '@ant-design/icons';
import { Link } from 'react-router-dom';
import MonitoringDashboard from '../../components/admin/monitoring/MonitoringDashboard';
import LogViewer from '../../components/admin/monitoring/LogViewer';
import DatabaseManager from '../../components/admin/database/DatabaseManager';
import SystemSettings from '../../components/admin/settings/SystemSettings';
import AlertsManager from '../../components/admin/monitoring/AlertsManager';

const { TabPane } = Tabs;
const { Title } = Typography;

// 系统管理页面
const SystemManagement: React.FC = () => {
  const [activeTab, setActiveTab] = useState<string>('dashboard');

  return (
    <div className="system-management-page">
      <Breadcrumb style={{ marginBottom: 16 }}>
        <Breadcrumb.Item>
          <Link to="/admin/dashboard">
            <HomeOutlined /> 首页
          </Link>
        </Breadcrumb.Item>
        <Breadcrumb.Item>
          <SettingOutlined /> 系统管理
        </Breadcrumb.Item>
      </Breadcrumb>

      <Title level={2}>系统管理</Title>

      <Card>
        <Tabs 
          activeKey={activeTab} 
          onChange={setActiveTab}
          tabPosition="left"
          style={{ minHeight: 'calc(100vh - 250px)' }}
        >
          <TabPane 
            tab={
              <span>
                <DashboardOutlined />
                监控仪表板
              </span>
            } 
            key="dashboard"
          >
            <MonitoringDashboard />
          </TabPane>
          
          <TabPane 
            tab={
              <span>
                <FileSearchOutlined />
                日志查看
              </span>
            } 
            key="logs"
          >
            <LogViewer />
          </TabPane>
          
          <TabPane 
            tab={
              <span>
                <AlertOutlined />
                告警管理
              </span>
            } 
            key="alerts"
          >
            <AlertsManager />
          </TabPane>
          
          <TabPane 
            tab={
              <span>
                <DatabaseOutlined />
                数据库管理
              </span>
            } 
            key="database"
          >
            <DatabaseManager />
          </TabPane>
          
          <TabPane 
            tab={
              <span>
                <SettingOutlined />
                系统设置
              </span>
            } 
            key="settings"
          >
            <SystemSettings />
          </TabPane>
        </Tabs>
      </Card>
    </div>
  );
};

export default SystemManagement;
