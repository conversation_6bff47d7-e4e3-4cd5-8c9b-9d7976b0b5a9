# 数据架构优化分析

## 🎯 核心问题分析

### 1. UUID设计问题
**当前问题：**
- 问卷回复、心声、故事混合在一起，没有独立的UUID
- 用户身份标识不清晰（submittedById字段混乱）
- 内容归属关系模糊

**优化方案：**
- **UUID-A**: 问卷回复独立UUID (`QuestionnaireResponse.id`)
- **UUID-B**: 心声内容独立UUID (`QuestionnaireVoice.id`)
- **UUID-C**: 用户身份UUID (`User.id` + `User.anonymousId`)
- **会话ID**: 关联问卷和心声 (`submissionSessionId`)

### 2. 用户权限体系
**当前问题：**
- 审核员、管理员、超级管理员权限不清晰
- 缺少操作日志追踪
- 用户创建关系不明确

**优化方案：**
```
超级管理员 (superadmin)
├── 创建/管理 管理员账户
├── 查看所有操作日志
├── 系统配置管理
└── 全局内容管理

管理员 (admin)
├── 创建/管理 审核员账户
├── 查看审核日志
├── 用户管理
└── 内容管理

审核员 (reviewer)
├── 审核问卷心声
├── 审核故事内容
└── 记录审核日志

匿名注册用户 (registered)
├── 查看自己的内容
├── 删除自己的内容
└── 修改个人信息

匿名用户 (anonymous)
└── 提交问卷和故事
```

## 📊 数据流设计

### 1. 问卷提交流程
```
用户填写问卷 → 生成submissionSessionId
├── 问卷数据 → QuestionnaireResponse (UUID-A)
├── 心声数据 → QuestionnaireVoice (UUID-B)
└── 用户选择匿名注册 → User (UUID-C)
```

### 2. 内容审核流程
```
内容提交 → 待审核状态
├── 审核员审核 → ReviewLog记录
├── 审核通过 → 公开展示
└── 审核拒绝 → 记录原因
```

### 3. 用户内容管理
```
匿名注册用户登录 → 验证UUID-C
├── 查看自己的问卷心声 (通过userId关联)
├── 查看自己的故事 (通过userId关联)
└── 删除自己的内容 (软删除)
```

## 🔌 API调用优化

### 1. 数据可视化 API
**当前调用：** `/api/visualization/data`
**数据源：** `QuestionnaireResponse` 表
**优化后调用：**
```javascript
// 基础统计
GET /api/analytics/questionnaire-stats
// 数据源：QuestionnaireResponse + QuestionnaireVoice

// 就业分析
GET /api/analytics/employment-analysis
// 数据源：QuestionnaireResponse (employmentStatus, salary等)

// 地区分布
GET /api/analytics/regional-distribution
// 数据源：QuestionnaireResponse (region, educationLevel等)
```

### 2. 高级分析 API
**当前调用：** 使用可视化数据进行前端计算
**优化后调用：**
```javascript
// 趋势分析
GET /api/analytics/trends?metric=employmentRate&timeUnit=month
// 数据源：QuestionnaireResponse 按时间聚合

// 相关性分析
GET /api/analytics/correlation?xVariable=education&yVariable=salary
// 数据源：QuestionnaireResponse 交叉分析

// 分组比较
GET /api/analytics/group-comparison?groupBy=region&metrics=employment,salary
// 数据源：QuestionnaireResponse 分组统计
```

### 3. 问卷心声 API
**当前调用：** `/api/questionnaire-voices`
**数据源：** `QuestionnaireResponse.adviceForStudents + observationOnEmployment`
**优化后调用：**
```javascript
// 心声列表
GET /api/voices/questionnaire?page=1&status=approved
// 数据源：QuestionnaireVoice 表

// 心声详情
GET /api/voices/questionnaire/:id
// 数据源：QuestionnaireVoice + QuestionnaireResponse (关联信息)

// 用户自己的心声
GET /api/voices/my-voices
// 数据源：QuestionnaireVoice (通过userId筛选)
```

### 4. 故事墙 API
**当前调用：** `/api/story/list`
**数据源：** `Story` 表
**优化后调用：**
```javascript
// 故事列表
GET /api/stories?page=1&status=approved&category=experience
// 数据源：Story 表

// 故事详情
GET /api/stories/:id
// 数据源：Story + Vote (点赞数据)

// 用户自己的故事
GET /api/stories/my-stories
// 数据源：Story (通过userId筛选)
```

## 🔧 实施计划

### 阶段1：数据库结构优化
1. 创建新的优化schema
2. 数据迁移脚本
3. 测试数据重新生成

### 阶段2：API重构
1. 重写数据可视化API
2. 重写问卷心声API
3. 重写故事墙API
4. 添加用户内容管理API

### 阶段3：前端适配
1. 更新数据调用逻辑
2. 添加用户内容管理界面
3. 优化数据展示

### 阶段4：权限系统
1. 实现用户权限验证
2. 添加操作日志记录
3. 完善审核流程

## 📈 预期效果

### 数据一致性
- 每条内容都有独立UUID
- 用户内容关系清晰
- 审核流程可追踪

### 性能优化
- 独立的心声表提高查询效率
- 合理的索引设计
- 分页和缓存优化

### 功能完整性
- 用户可以管理自己的内容
- 管理员权限层级清晰
- 操作日志完整记录

### 可维护性
- 数据结构清晰
- API职责单一
- 易于扩展新功能
