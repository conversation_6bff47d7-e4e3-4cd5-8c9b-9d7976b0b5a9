name: Deploy

on:
  push:
    branches:
      - main
      - staging
      - preview
  pull_request:
    branches:
      - main
  workflow_dispatch:
    inputs:
      environment:
        description: 'Environment to deploy to'
        required: true
        default: 'preview'
        type: choice
        options:
          - preview
          - staging
          - production

jobs:
  deploy-api:
    name: Deploy API
    runs-on: ubuntu-latest
    environment: ${{ github.event.inputs.environment || (github.ref == 'refs/heads/main' && 'production') || (github.ref == 'refs/heads/staging' && 'staging') || 'preview' }}
    defaults:
      run:
        working-directory: ./frontend/api-server
    
    steps:
      - name: Checkout
        uses: actions/checkout@v3
      
      - name: Setup Node.js
        uses: actions/setup-node@v3
        with:
          node-version: '18'
          cache: 'npm'
          cache-dependency-path: './frontend/api-server/package-lock.json'
      
      - name: Install dependencies
        run: npm ci
      
      - name: Run tests
        run: npm test
      
      - name: Deploy to Cloudflare Workers
        uses: cloudflare/wrangler-action@v3
        with:
          apiToken: ${{ secrets.CF_API_TOKEN }}
          accountId: ${{ secrets.CF_ACCOUNT_ID }}
          workingDirectory: './frontend/api-server'
          command: deploy --env ${{ github.event.inputs.environment || (github.ref == 'refs/heads/main' && 'production') || (github.ref == 'refs/heads/staging' && 'staging') || 'preview' }}
      
      - name: Apply database migrations
        uses: cloudflare/wrangler-action@v3
        with:
          apiToken: ${{ secrets.CF_API_TOKEN }}
          accountId: ${{ secrets.CF_ACCOUNT_ID }}
          workingDirectory: './frontend/api-server'
          command: d1 migrations apply college-survey-db --env ${{ github.event.inputs.environment || (github.ref == 'refs/heads/main' && 'production') || (github.ref == 'refs/heads/staging' && 'staging') || 'preview' }}

  deploy-frontend:
    name: Deploy Frontend
    runs-on: ubuntu-latest
    environment: ${{ github.event.inputs.environment || (github.ref == 'refs/heads/main' && 'production') || (github.ref == 'refs/heads/staging' && 'staging') || 'preview' }}
    needs: deploy-api
    defaults:
      run:
        working-directory: ./frontend
    
    steps:
      - name: Checkout
        uses: actions/checkout@v3
      
      - name: Setup Node.js
        uses: actions/setup-node@v3
        with:
          node-version: '18'
          cache: 'npm'
          cache-dependency-path: './frontend/package-lock.json'
      
      - name: Install dependencies
        run: npm ci
      
      - name: Run tests
        run: npm test
      
      - name: Build
        run: npm run build
        env:
          VITE_USE_MOCK: 'false'
          VITE_API_VERSION: 'v1'
          VITE_API_BASE_URL: ${{ github.event.inputs.environment == 'production' && 'https://api.college-employment-survey.workers.dev' || github.event.inputs.environment == 'staging' && 'https://api-staging.college-employment-survey.workers.dev' || github.event.inputs.environment == 'preview' && 'https://api-preview.college-employment-survey.workers.dev' || (github.ref == 'refs/heads/main' && 'https://api.college-employment-survey.workers.dev') || (github.ref == 'refs/heads/staging' && 'https://api-staging.college-employment-survey.workers.dev') || 'https://api-preview.college-employment-survey.workers.dev' }}
      
      - name: Deploy to Cloudflare Pages
        uses: cloudflare/wrangler-action@v3
        with:
          apiToken: ${{ secrets.CF_API_TOKEN }}
          accountId: ${{ secrets.CF_ACCOUNT_ID }}
          workingDirectory: './frontend'
          command: pages deploy dist --project-name=college-employment-survey --env ${{ github.event.inputs.environment || (github.ref == 'refs/heads/main' && 'production') || (github.ref == 'refs/heads/staging' && 'staging') || 'preview' }}

  notify:
    name: Notify Deployment
    runs-on: ubuntu-latest
    needs: [deploy-api, deploy-frontend]
    if: success()
    
    steps:
      - name: Notify Slack
        uses: rtCamp/action-slack-notify@v2
        env:
          SLACK_WEBHOOK: ${{ secrets.SLACK_WEBHOOK }}
          SLACK_CHANNEL: deployments
          SLACK_COLOR: ${{ job.status }}
          SLACK_TITLE: Deployment Completed
          SLACK_MESSAGE: |
            Environment: ${{ github.event.inputs.environment || (github.ref == 'refs/heads/main' && 'production') || (github.ref == 'refs/heads/staging' && 'staging') || 'preview' }}
            Commit: ${{ github.sha }}
            Triggered by: ${{ github.actor }}
          SLACK_FOOTER: College Employment Survey
        if: env.SLACK_WEBHOOK != ''
      
      - name: Notify Email
        uses: dawidd6/action-send-mail@v3
        with:
          server_address: ${{ secrets.MAIL_SERVER }}
          server_port: ${{ secrets.MAIL_PORT }}
          username: ${{ secrets.MAIL_USERNAME }}
          password: ${{ secrets.MAIL_PASSWORD }}
          subject: Deployment Completed - College Employment Survey
          body: |
            Deployment completed successfully!
            
            Environment: ${{ github.event.inputs.environment || (github.ref == 'refs/heads/main' && 'production') || (github.ref == 'refs/heads/staging' && 'staging') || 'preview' }}
            Commit: ${{ github.sha }}
            Triggered by: ${{ github.actor }}
            
            Frontend URL: ${{ github.event.inputs.environment == 'production' && 'https://college-employment-survey.pages.dev' || github.event.inputs.environment == 'staging' && 'https://staging.college-employment-survey.pages.dev' || github.event.inputs.environment == 'preview' && 'https://preview.college-employment-survey.pages.dev' || (github.ref == 'refs/heads/main' && 'https://college-employment-survey.pages.dev') || (github.ref == 'refs/heads/staging' && 'https://staging.college-employment-survey.pages.dev') || 'https://preview.college-employment-survey.pages.dev' }}
            API URL: ${{ github.event.inputs.environment == 'production' && 'https://api.college-employment-survey.workers.dev' || github.event.inputs.environment == 'staging' && 'https://api-staging.college-employment-survey.workers.dev' || github.event.inputs.environment == 'preview' && 'https://api-preview.college-employment-survey.workers.dev' || (github.ref == 'refs/heads/main' && 'https://api.college-employment-survey.workers.dev') || (github.ref == 'refs/heads/staging' && 'https://api-staging.college-employment-survey.workers.dev') || 'https://api-preview.college-employment-survey.workers.dev' }}
          to: ${{ secrets.NOTIFICATION_EMAIL }}
          from: College Employment Survey <<EMAIL>>
        if: env.MAIL_SERVER != ''
