# 功能扩展示例：用户积分系统

基于数据库设计规范的新功能添加示例

## 🎯 需求分析

### 功能描述
为用户添加积分系统，用户通过完成问卷、发布故事、参与互动等行为获得积分，可用于兑换权益或提升等级。

### 数据需求分析
```typescript
interface PointSystemRequirement {
  name: "用户积分系统";
  module: "user";
  dataRequirements: {
    newTables: [
      "user_points",           // 用户积分表
      "point_transactions",    // 积分交易记录
      "point_rules",          // 积分规则配置
      "point_rewards"         // 积分奖励配置
    ];
    modifiedTables: [
      "user_profiles"         // 用户表添加积分字段
    ];
    kvKeys: [
      "cache:points:user:*",  // 用户积分缓存
      "cache:leaderboard:*",  // 排行榜缓存
      "config:points:rules"   // 积分规则缓存
    ];
    r2Paths: [
      "reports/points/*",     // 积分报表
      "backups/points/*"      // 积分数据备份
    ];
  };
  compatibility: {
    breakingChanges: false;      // 无破坏性变更
    migrationRequired: true;     // 需要数据迁移
    rollbackSupport: true;       // 支持回滚
  };
}
```

## 🏗️ 数据设计

### 1. **D1 数据库设计**

#### 用户积分表
```sql
-- 用户积分主表
CREATE TABLE user_points (
  -- 标准主键
  id TEXT PRIMARY KEY,                    -- point_xxxxxxxx-xxxx-xxxx
  
  -- 关联字段
  user_id TEXT NOT NULL,                  -- 关联用户
  
  -- 核心业务字段
  total_points INTEGER DEFAULT 0,         -- 总积分
  available_points INTEGER DEFAULT 0,     -- 可用积分
  frozen_points INTEGER DEFAULT 0,        -- 冻结积分
  lifetime_earned INTEGER DEFAULT 0,      -- 累计获得积分
  lifetime_spent INTEGER DEFAULT 0,       -- 累计消费积分
  
  -- 等级相关
  current_level INTEGER DEFAULT 1,        -- 当前等级
  level_progress INTEGER DEFAULT 0,       -- 等级进度
  next_level_threshold INTEGER,           -- 下一等级所需积分
  
  -- 状态管理 (标准字段)
  status TEXT DEFAULT 'active',           -- active, frozen, suspended
  is_deleted BOOLEAN DEFAULT false,
  is_public BOOLEAN DEFAULT true,
  
  -- 扩展字段 (标准字段)
  metadata TEXT,                          -- JSON: 积分详细信息
  tags TEXT,                              -- JSON: 标签
  extra_data TEXT,                        -- JSON: 扩展数据
  
  -- 版本管理 (标准字段)
  version INTEGER DEFAULT 1,
  schema_version TEXT DEFAULT '1.0',
  
  -- 审计字段 (标准字段)
  created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
  updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
  created_by TEXT,
  updated_by TEXT,
  
  -- 测试数据标识 (标准字段)
  is_test_data BOOLEAN DEFAULT false,
  test_data_set TEXT,
  test_data_version TEXT,
  
  -- 外键约束
  FOREIGN KEY (user_id) REFERENCES users_optimized(id)
);

-- 标准索引
CREATE INDEX idx_user_points_user_id ON user_points(user_id);
CREATE INDEX idx_user_points_status ON user_points(status);
CREATE INDEX idx_user_points_created_at ON user_points(created_at);
CREATE INDEX idx_user_points_is_deleted ON user_points(is_deleted);

-- 业务索引
CREATE INDEX idx_user_points_total ON user_points(total_points DESC);
CREATE INDEX idx_user_points_level ON user_points(current_level);
CREATE UNIQUE INDEX idx_user_points_user_unique ON user_points(user_id) WHERE is_deleted = false;
```

#### 积分交易记录表
```sql
-- 积分交易记录表
CREATE TABLE point_transactions (
  -- 标准主键
  id TEXT PRIMARY KEY,                    -- trans_xxxxxxxx-xxxx-xxxx
  
  -- 关联字段
  user_id TEXT NOT NULL,
  point_rule_id TEXT,                     -- 关联积分规则
  related_content_id TEXT,                -- 关联内容ID
  
  -- 核心业务字段
  transaction_type TEXT NOT NULL,         -- earn, spend, transfer, adjust
  points_amount INTEGER NOT NULL,         -- 积分数量 (正数为获得，负数为消费)
  points_before INTEGER NOT NULL,        -- 交易前积分
  points_after INTEGER NOT NULL,         -- 交易后积分
  
  -- 交易详情
  reason TEXT NOT NULL,                   -- 交易原因
  description TEXT,                       -- 详细描述
  reference_id TEXT,                      -- 外部参考ID
  
  -- 状态管理
  status TEXT DEFAULT 'completed',        -- pending, completed, failed, cancelled
  is_deleted BOOLEAN DEFAULT false,
  
  -- 扩展字段
  metadata TEXT,                          -- JSON: 交易元数据
  tags TEXT,                              -- JSON: 标签
  extra_data TEXT,                        -- JSON: 扩展数据
  
  -- 版本管理
  version INTEGER DEFAULT 1,
  schema_version TEXT DEFAULT '1.0',
  
  -- 审计字段
  created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
  updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
  created_by TEXT,
  updated_by TEXT,
  
  -- 测试数据标识
  is_test_data BOOLEAN DEFAULT false,
  test_data_set TEXT,
  test_data_version TEXT,
  
  -- 外键约束
  FOREIGN KEY (user_id) REFERENCES users_optimized(id)
);

-- 标准索引
CREATE INDEX idx_point_transactions_user_id ON point_transactions(user_id);
CREATE INDEX idx_point_transactions_status ON point_transactions(status);
CREATE INDEX idx_point_transactions_created_at ON point_transactions(created_at);
CREATE INDEX idx_point_transactions_is_deleted ON point_transactions(is_deleted);

-- 业务索引
CREATE INDEX idx_point_transactions_type ON point_transactions(transaction_type);
CREATE INDEX idx_point_transactions_user_type ON point_transactions(user_id, transaction_type);
CREATE INDEX idx_point_transactions_user_created ON point_transactions(user_id, created_at DESC);
```

#### 积分规则配置表
```sql
-- 积分规则配置表
CREATE TABLE point_rules (
  -- 标准主键
  id TEXT PRIMARY KEY,                    -- rule_xxxxxxxx-xxxx-xxxx
  
  -- 规则基本信息
  rule_name TEXT NOT NULL,                -- 规则名称
  rule_code TEXT NOT NULL UNIQUE,        -- 规则代码
  rule_type TEXT NOT NULL,                -- action, milestone, bonus
  
  -- 触发条件
  trigger_event TEXT NOT NULL,            -- 触发事件
  trigger_conditions TEXT,                -- JSON: 触发条件
  
  -- 积分设置
  points_amount INTEGER NOT NULL,         -- 积分数量
  points_type TEXT DEFAULT 'fixed',       -- fixed, percentage, formula
  calculation_formula TEXT,               -- 计算公式
  
  -- 限制条件
  daily_limit INTEGER,                    -- 每日限制
  weekly_limit INTEGER,                   -- 每周限制
  monthly_limit INTEGER,                  -- 每月限制
  total_limit INTEGER,                    -- 总限制
  
  -- 有效期
  valid_from DATETIME,                    -- 生效时间
  valid_until DATETIME,                   -- 失效时间
  
  -- 状态管理
  status TEXT DEFAULT 'active',           -- active, inactive, expired
  is_deleted BOOLEAN DEFAULT false,
  
  -- 扩展字段
  metadata TEXT,                          -- JSON: 规则元数据
  tags TEXT,                              -- JSON: 标签
  extra_data TEXT,                        -- JSON: 扩展数据
  
  -- 版本管理
  version INTEGER DEFAULT 1,
  schema_version TEXT DEFAULT '1.0',
  
  -- 审计字段
  created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
  updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
  created_by TEXT,
  updated_by TEXT,
  
  -- 测试数据标识
  is_test_data BOOLEAN DEFAULT false,
  test_data_set TEXT,
  test_data_version TEXT
);

-- 标准索引
CREATE INDEX idx_point_rules_status ON point_rules(status);
CREATE INDEX idx_point_rules_created_at ON point_rules(created_at);
CREATE INDEX idx_point_rules_is_deleted ON point_rules(is_deleted);

-- 业务索引
CREATE INDEX idx_point_rules_type ON point_rules(rule_type);
CREATE INDEX idx_point_rules_event ON point_rules(trigger_event);
CREATE INDEX idx_point_rules_valid ON point_rules(valid_from, valid_until);
```

### 2. **KV 缓存设计**

#### 用户积分缓存
```typescript
// Key: cache:points:user:{userId}
// TTL: 1800 (30分钟)
interface UserPointsCache {
  id: string;                             // point_xxx
  type: "user_points";
  version: "1.0";
  createdAt: number;
  
  data: {
    userId: string;
    totalPoints: number;
    availablePoints: number;
    currentLevel: number;
    levelProgress: number;
    nextLevelThreshold: number;
  };
  
  metadata: {
    source: "database";
    lastCalculated: number;
    tags: ["points", "user"];
  };
  
  audit: {
    lastModified: number;
    modifiedBy: "system";
  };
}
```

#### 排行榜缓存
```typescript
// Key: cache:leaderboard:points:daily
// TTL: 3600 (1小时)
interface PointsLeaderboard {
  id: string;
  type: "leaderboard";
  version: "1.0";
  createdAt: number;
  
  data: {
    timeframe: "daily" | "weekly" | "monthly" | "all";
    rankings: Array<{
      rank: number;
      userId: string;
      displayName: string;
      points: number;
      level: number;
    }>;
    totalUsers: number;
    lastUpdated: number;
  };
  
  metadata: {
    source: "aggregation";
    refreshInterval: 3600;
    tags: ["leaderboard", "points"];
  };
}
```

#### 积分规则缓存
```typescript
// Key: config:points:rules
// TTL: null (永久，手动更新)
interface PointRulesConfig {
  id: string;
  type: "point_rules";
  version: "1.0";
  createdAt: number;
  
  data: {
    rules: Array<{
      ruleCode: string;
      ruleName: string;
      triggerEvent: string;
      pointsAmount: number;
      dailyLimit?: number;
      conditions?: any;
    }>;
    levels: Array<{
      level: number;
      threshold: number;
      benefits: string[];
    }>;
  };
  
  metadata: {
    source: "configuration";
    tags: ["config", "points"];
  };
}
```

### 3. **R2 存储设计**

#### 积分报表存储
```typescript
// 路径: reports/points/2024/01/daily-report-2024-01-15.json
interface PointsReport {
  id: string;                             // report_xxx
  type: "points_report";
  reportDate: string;                     // 2024-01-15
  timeframe: "daily" | "weekly" | "monthly";
  
  data: {
    summary: {
      totalPointsEarned: number;
      totalPointsSpent: number;
      activeUsers: number;
      newUsers: number;
    };
    
    breakdown: {
      byAction: Record<string, number>;
      byLevel: Record<string, number>;
      byHour: Array<{
        hour: number;
        points: number;
        users: number;
      }>;
    };
    
    topUsers: Array<{
      userId: string;
      pointsEarned: number;
      actionsCount: number;
    }>;
  };
  
  metadata: {
    generatedAt: string;
    generatedBy: "system";
    dataSource: "point_transactions";
    version: "1.0";
  };
}
```

## 🔌 API 设计

### 1. **用户积分查询**
```typescript
// GET /api/v1/user/points
async function getUserPoints(c: Context<{ Bindings: Env }>) {
  const userId = c.req.header('X-User-ID');
  
  // 1. 尝试KV缓存
  const cacheKey = `cache:points:user:${userId}`;
  const cached = await c.env.KV.get(cacheKey);
  
  if (cached) {
    return c.json({
      success: true,
      data: JSON.parse(cached).data,
      source: 'cache'
    });
  }
  
  // 2. 查询D1数据库
  const points = await c.env.DB.prepare(`
    SELECT 
      total_points, available_points, frozen_points,
      current_level, level_progress, next_level_threshold,
      lifetime_earned, lifetime_spent
    FROM user_points 
    WHERE user_id = ? AND is_deleted = false
  `).bind(userId).first();
  
  if (!points) {
    // 3. 初始化用户积分
    await initializeUserPoints(c.env.DB, userId);
    return c.json({
      success: true,
      data: getDefaultPoints(),
      source: 'initialized'
    });
  }
  
  // 4. 写入缓存
  const cacheData = {
    id: `point_${userId}`,
    type: 'user_points',
    version: '1.0',
    createdAt: Date.now(),
    data: points,
    metadata: { source: 'database', tags: ['points', 'user'] }
  };
  
  await c.env.KV.put(cacheKey, JSON.stringify(cacheData), {
    expirationTtl: 1800
  });
  
  return c.json({
    success: true,
    data: points,
    source: 'database'
  });
}
```

### 2. **积分交易记录**
```typescript
// POST /api/v1/user/points/transaction
async function createPointTransaction(c: Context<{ Bindings: Env }>) {
  const userId = c.req.header('X-User-ID');
  const { action, contentId, amount } = await c.req.json();
  
  // 1. 验证积分规则
  const rule = await getPointRule(c.env.KV, action);
  if (!rule) {
    return c.json({ error: 'Invalid action' }, 400);
  }
  
  // 2. 检查限制条件
  const canEarn = await checkPointLimits(c.env.DB, userId, rule);
  if (!canEarn) {
    return c.json({ error: 'Daily limit exceeded' }, 429);
  }
  
  // 3. 执行积分交易
  const transaction = await executePointTransaction(c.env.DB, {
    userId,
    ruleId: rule.id,
    contentId,
    amount: rule.pointsAmount,
    reason: rule.ruleName
  });
  
  // 4. 更新用户积分
  await updateUserPoints(c.env.DB, userId, rule.pointsAmount);
  
  // 5. 清除缓存
  await c.env.KV.delete(`cache:points:user:${userId}`);
  
  return c.json({
    success: true,
    transaction: {
      id: transaction.id,
      points: rule.pointsAmount,
      reason: rule.ruleName,
      createdAt: transaction.createdAt
    }
  });
}
```

## 🔄 数据迁移

### 1. **迁移脚本**
```sql
-- 迁移脚本: 001_add_points_system.sql
BEGIN TRANSACTION;

-- 1. 创建积分相关表
-- (表创建语句如上所示)

-- 2. 为现有用户初始化积分
INSERT INTO user_points (
  id, user_id, total_points, available_points,
  current_level, level_progress, next_level_threshold,
  created_at, schema_version
)
SELECT 
  'point_' || u.id,
  u.id,
  0,  -- 初始积分为0
  0,
  1,  -- 初始等级为1
  0,
  100, -- 下一等级需要100积分
  CURRENT_TIMESTAMP,
  '1.0'
FROM users_optimized u
WHERE NOT EXISTS (
  SELECT 1 FROM user_points p WHERE p.user_id = u.id
);

-- 3. 插入默认积分规则
INSERT INTO point_rules (
  id, rule_name, rule_code, rule_type, trigger_event,
  points_amount, daily_limit, status, created_at, schema_version
) VALUES
('rule_questionnaire', '完成问卷', 'complete_questionnaire', 'action', 'questionnaire_completed', 10, 3, 'active', CURRENT_TIMESTAMP, '1.0'),
('rule_story_publish', '发布故事', 'publish_story', 'action', 'story_published', 20, 5, 'active', CURRENT_TIMESTAMP, '1.0'),
('rule_story_approved', '故事通过审核', 'story_approved', 'action', 'story_approved', 50, NULL, 'active', CURRENT_TIMESTAMP, '1.0'),
('rule_daily_login', '每日登录', 'daily_login', 'action', 'user_login', 5, 1, 'active', CURRENT_TIMESTAMP, '1.0');

-- 4. 更新schema版本
INSERT OR REPLACE INTO system_configs (
  id, key, value, description, category, created_at
) VALUES (
  'config_schema_version',
  'schema_version',
  '{"points_system": "1.0", "updated_at": "' || datetime('now') || '"}',
  '数据库结构版本',
  'system',
  CURRENT_TIMESTAMP
);

COMMIT;
```

### 2. **回滚脚本**
```sql
-- 回滚脚本: 001_rollback_points_system.sql
BEGIN TRANSACTION;

-- 1. 删除积分相关表
DROP TABLE IF EXISTS point_transactions;
DROP TABLE IF EXISTS point_rules;
DROP TABLE IF EXISTS user_points;

-- 2. 恢复schema版本
UPDATE system_configs 
SET value = json_remove(value, '$.points_system')
WHERE key = 'schema_version';

COMMIT;
```

## 📊 监控和告警

### 1. **性能监控**
```typescript
interface PointsSystemMetrics {
  // 性能指标
  performance: {
    avgQueryTime: number;        // 平均查询时间
    cacheHitRate: number;        // 缓存命中率
    transactionThroughput: number; // 交易吞吐量
  };
  
  // 业务指标
  business: {
    dailyActiveUsers: number;    // 日活跃用户
    dailyPointsEarned: number;   // 日积分获得
    averageUserLevel: number;    // 平均用户等级
  };
  
  // 错误指标
  errors: {
    failedTransactions: number;  // 失败交易数
    cacheErrors: number;         // 缓存错误数
    databaseErrors: number;      // 数据库错误数
  };
}
```

### 2. **告警规则**
```typescript
interface AlertRules {
  // 性能告警
  performance: {
    queryTimeThreshold: 100,     // 查询时间超过100ms
    cacheHitRateThreshold: 0.8,  // 缓存命中率低于80%
    errorRateThreshold: 0.01     // 错误率超过1%
  };
  
  // 业务告警
  business: {
    unusualPointsSpike: 1000,    // 积分异常增长
    suspiciousTransactions: 50,  // 可疑交易数量
    systemAbuseDetection: true   // 系统滥用检测
  };
}
```

## ✅ 兼容性验证

### 1. **现有功能验证**
- [ ] 用户注册流程正常
- [ ] 问卷提交功能正常
- [ ] 故事发布功能正常
- [ ] 现有API响应格式不变
- [ ] 数据库查询性能无影响

### 2. **新功能验证**
- [ ] 积分初始化正常
- [ ] 积分交易记录准确
- [ ] 缓存策略有效
- [ ] 排行榜计算正确
- [ ] 等级升级逻辑正确

### 3. **性能验证**
- [ ] 查询响应时间 < 50ms
- [ ] 缓存命中率 > 80%
- [ ] 并发处理能力满足需求
- [ ] 内存使用在合理范围
- [ ] 数据库连接池稳定

这个示例展示了如何严格按照数据库设计规范添加新功能，确保兼容性和可扩展性。你觉得这个规范和示例如何？
