/**
 * 内容审核路由
 *
 * 处理内容审核相关的API请求
 */

import { Hono } from 'hono';
import { Env } from '../../types';
import { adminAuthMiddleware } from '../../middlewares/auth.middleware';
import { reviewerAuthMiddleware } from '../../middlewares/reviewerAuth.middleware';
import * as contentModerationController from './content-moderation.controller';

// 创建路由
const app = new Hono<{ Bindings: Env }>();

// ==================== 待审核内容管理 ====================

// 获取待审核内容列表（需要审核员或管理员权限）
app.get('/pending', reviewerAuthMiddleware, contentModerationController.getPendingContents);

// 获取单个待审核内容详情（需要审核员或管理员权限）
app.get('/pending/:id', reviewerAuthMiddleware, contentModerationController.getPendingContentById);

// 审核通过内容（需要审核员或管理员权限）
app.post('/:id/approve', reviewerAuthMiddleware, contentModerationController.approveContent);

// 编辑并通过内容（需要审核员或管理员权限）
app.put('/:id/edit', reviewerAuthMiddleware, contentModerationController.editAndApproveContent);

// 拒绝内容（需要审核员或管理员权限）
app.post('/:id/reject', reviewerAuthMiddleware, contentModerationController.rejectContent);

// 批量审核内容（需要审核员或管理员权限）
app.post('/batch', reviewerAuthMiddleware, contentModerationController.batchModerateContent);

// ==================== 兼容旧API ====================

// 审核内容（需要审核员或管理员权限）- 保持向后兼容
app.post('/moderate', reviewerAuthMiddleware, contentModerationController.moderateContent);

// 批量审核内容（需要审核员或管理员权限）- 保持向后兼容
app.post('/moderate-batch', reviewerAuthMiddleware, contentModerationController.moderateContentBatch);

// ==================== 统计和分析 ====================

// 获取AI服务状态（需要管理员权限）
app.get('/status', adminAuthMiddleware, contentModerationController.getAIServiceStatus);

// 获取内容审核历史（需要管理员权限）
app.get('/history', adminAuthMiddleware, contentModerationController.getModerationHistory);

// 获取内容审核统计（需要管理员权限）
app.get('/stats', adminAuthMiddleware, contentModerationController.getModerationStats);

// 获取审核员绩效统计（需要管理员权限）
app.get('/reviewer-performance', adminAuthMiddleware, contentModerationController.getReviewerPerformance);

// 获取内容质量趋势分析（需要管理员权限）
app.get('/quality-trends', adminAuthMiddleware, contentModerationController.getContentQualityTrends);

export default app;
