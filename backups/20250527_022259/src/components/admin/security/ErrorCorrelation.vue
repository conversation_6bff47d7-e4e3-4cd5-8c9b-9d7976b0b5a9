<template>
  <div class="error-correlation-container">
    <div class="correlation-header">
      <h3>{{ title }}</h3>
      <div class="correlation-controls">
        <select v-model="correlationType" @change="updateCorrelation">
          <option value="errorCode">错误代码关联</option>
          <option value="source">错误来源关联</option>
          <option value="time">时间关联</option>
          <option value="severity">严重性关联</option>
        </select>
        <button class="refresh-button" @click="updateCorrelation">
          <i class="fas fa-sync-alt"></i>
        </button>
      </div>
    </div>
    
    <div class="correlation-container" ref="correlationContainer">
      <div v-if="loading" class="correlation-loading">
        <div class="spinner"></div>
        <p>加载中...</p>
      </div>
      
      <div v-else-if="error" class="correlation-error">
        <p>{{ error }}</p>
        <button @click="updateCorrelation">重试</button>
      </div>
      
      <div v-else-if="!hasData" class="correlation-empty">
        <p>没有数据可显示</p>
      </div>
      
      <div v-else class="correlation-graph" ref="correlationGraph"></div>
    </div>
    
    <div v-if="selectedNode" class="node-details">
      <div class="details-header">
        <h4>{{ selectedNode.label }}</h4>
        <button class="close-button" @click="clearSelection">&times;</button>
      </div>
      
      <div class="details-content">
        <div class="detail-item">
          <span class="detail-label">类型:</span>
          <span class="detail-value">{{ selectedNode.type }}</span>
        </div>
        
        <div class="detail-item">
          <span class="detail-label">错误数量:</span>
          <span class="detail-value">{{ selectedNode.count }}</span>
        </div>
        
        <div v-if="selectedNode.severity" class="detail-item">
          <span class="detail-label">严重性:</span>
          <span class="detail-value severity-badge" :class="selectedNode.severity">
            {{ selectedNode.severity }}
          </span>
        </div>
        
        <div v-if="selectedNode.connections && selectedNode.connections.length > 0" class="detail-section">
          <h5>关联</h5>
          <ul class="connections-list">
            <li v-for="(connection, index) in selectedNode.connections" :key="index">
              <span class="connection-label">{{ connection.label }}</span>
              <span class="connection-strength">
                强度: {{ Math.round(connection.strength * 100) }}%
              </span>
            </li>
          </ul>
        </div>
        
        <div v-if="selectedNode.samples && selectedNode.samples.length > 0" class="detail-section">
          <h5>示例错误</h5>
          <div class="error-samples">
            <div 
              v-for="(sample, index) in selectedNode.samples" 
              :key="index"
              class="error-sample"
            >
              <div class="sample-header">
                <span class="sample-severity" :class="sample.severity">{{ sample.severity }}</span>
                <span class="sample-time">{{ formatTime(sample.timestamp) }}</span>
              </div>
              <div class="sample-message">{{ sample.message }}</div>
              <div v-if="sample.errorCode" class="sample-code">代码: {{ sample.errorCode }}</div>
            </div>
          </div>
        </div>
      </div>
      
      <div class="details-footer">
        <button @click="viewAllRelated">查看所有相关错误</button>
      </div>
    </div>
    
    <div v-if="hasData && !loading && !error" class="correlation-insights">
      <h4>关联洞察</h4>
      <ul>
        <li v-for="(insight, index) in insights" :key="index">
          {{ insight }}
        </li>
      </ul>
    </div>
  </div>
</template>

<script>
import * as d3 from 'd3';

export default {
  name: 'ErrorCorrelation',
  props: {
    title: {
      type: String,
      default: '错误关联分析'
    },
    dataUrl: {
      type: String,
      required: true
    },
    initialCorrelationType: {
      type: String,
      default: 'errorCode'
    }
  },
  data() {
    return {
      correlationType: this.initialCorrelationType,
      correlationData: null,
      loading: true,
      error: null,
      selectedNode: null,
      insights: [],
      simulation: null
    };
  },
  computed: {
    hasData() {
      return this.correlationData && 
             this.correlationData.nodes && 
             this.correlationData.nodes.length > 0 && 
             this.correlationData.links && 
             this.correlationData.links.length > 0;
    }
  },
  mounted() {
    this.updateCorrelation();
    
    // 添加窗口大小变化监听器
    window.addEventListener('resize', this.resizeGraph);
  },
  beforeUnmount() {
    // 移除窗口大小变化监听器
    window.removeEventListener('resize', this.resizeGraph);
    
    // 停止模拟
    if (this.simulation) {
      this.simulation.stop();
    }
  },
  methods: {
    async updateCorrelation() {
      this.loading = true;
      this.error = null;
      this.selectedNode = null;
      
      try {
        // 构建请求 URL
        const url = `${this.dataUrl}?type=${this.correlationType}`;
        
        // 获取数据
        const response = await fetch(url);
        
        if (!response.ok) {
          throw new Error(`HTTP error! status: ${response.status}`);
        }
        
        const data = await response.json();
        
        if (!data.success) {
          throw new Error(data.error || '获取数据失败');
        }
        
        this.correlationData = data.correlation || { nodes: [], links: [] };
        this.insights = data.insights || [];
        
        // 渲染关联图
        this.$nextTick(() => {
          this.renderGraph();
        });
      } catch (error) {
        console.error('Error fetching correlation data:', error);
        this.error = '获取关联数据失败: ' + error.message;
      } finally {
        this.loading = false;
      }
    },
    
    renderGraph() {
      // 如果没有数据，不渲染图表
      if (!this.hasData) {
        return;
      }
      
      // 清空图表容器
      const container = this.$refs.correlationGraph;
      container.innerHTML = '';
      
      // 获取容器尺寸
      const width = container.clientWidth;
      const height = container.clientHeight || 400;
      
      // 创建 SVG 元素
      const svg = d3.select(container)
        .append('svg')
        .attr('width', width)
        .attr('height', height)
        .attr('viewBox', [0, 0, width, height])
        .attr('style', 'max-width: 100%; height: auto;');
      
      // 创建力导向图
      this.simulation = d3.forceSimulation(this.correlationData.nodes)
        .force('link', d3.forceLink(this.correlationData.links).id(d => d.id).distance(100))
        .force('charge', d3.forceManyBody().strength(-200))
        .force('center', d3.forceCenter(width / 2, height / 2))
        .force('collision', d3.forceCollide().radius(d => this.getNodeRadius(d) + 5));
      
      // 创建连线
      const link = svg.append('g')
        .attr('stroke', '#999')
        .attr('stroke-opacity', 0.6)
        .selectAll('line')
        .data(this.correlationData.links)
        .join('line')
        .attr('stroke-width', d => Math.max(1, d.value * 5));
      
      // 创建节点
      const node = svg.append('g')
        .selectAll('.node')
        .data(this.correlationData.nodes)
        .join('g')
        .attr('class', 'node')
        .call(this.drag(this.simulation))
        .on('click', (event, d) => this.selectNode(d));
      
      // 添加节点圆圈
      node.append('circle')
        .attr('r', d => this.getNodeRadius(d))
        .attr('fill', d => this.getNodeColor(d))
        .attr('stroke', '#fff')
        .attr('stroke-width', 1.5);
      
      // 添加节点标签
      node.append('text')
        .attr('dx', d => this.getNodeRadius(d) + 5)
        .attr('dy', '.35em')
        .text(d => this.truncateLabel(d.label))
        .attr('font-size', '10px')
        .attr('fill', '#333');
      
      // 更新模拟
      this.simulation.on('tick', () => {
        link
          .attr('x1', d => d.source.x)
          .attr('y1', d => d.source.y)
          .attr('x2', d => d.target.x)
          .attr('y2', d => d.target.y);
        
        node
          .attr('transform', d => `translate(${d.x},${d.y})`);
      });
    },
    
    getNodeRadius(node) {
      // 根据节点的错误数量计算半径
      const minRadius = 5;
      const maxRadius = 20;
      const maxCount = Math.max(...this.correlationData.nodes.map(n => n.count || 1));
      
      if (maxCount <= 1) return minRadius;
      
      return minRadius + (maxRadius - minRadius) * Math.sqrt((node.count || 1) / maxCount);
    },
    
    getNodeColor(node) {
      // 根据节点类型和严重性设置颜色
      if (node.severity) {
        switch (node.severity) {
          case 'critical': return '#e74c3c';
          case 'high': return '#e67e22';
          case 'medium': return '#f1c40f';
          case 'low': return '#3498db';
          default: return '#95a5a6';
        }
      }
      
      // 根据节点类型设置颜色
      switch (node.type) {
        case 'errorCode': return '#3498db';
        case 'source': return '#2ecc71';
        case 'time': return '#9b59b6';
        default: return '#95a5a6';
      }
    },
    
    truncateLabel(label) {
      // 截断过长的标签
      return label.length > 15 ? label.substring(0, 12) + '...' : label;
    },
    
    drag(simulation) {
      function dragstarted(event) {
        if (!event.active) simulation.alphaTarget(0.3).restart();
        event.subject.fx = event.subject.x;
        event.subject.fy = event.subject.y;
      }
      
      function dragged(event) {
        event.subject.fx = event.x;
        event.subject.fy = event.y;
      }
      
      function dragended(event) {
        if (!event.active) simulation.alphaTarget(0);
        event.subject.fx = null;
        event.subject.fy = null;
      }
      
      return d3.drag()
        .on('start', dragstarted)
        .on('drag', dragged)
        .on('end', dragended);
    },
    
    selectNode(node) {
      this.selectedNode = node;
    },
    
    clearSelection() {
      this.selectedNode = null;
    },
    
    viewAllRelated() {
      if (this.selectedNode) {
        this.$emit('view-related-errors', this.selectedNode);
      }
    },
    
    formatTime(timestamp) {
      const date = new Date(timestamp);
      return date.toLocaleString();
    },
    
    resizeGraph() {
      // 重新渲染图表
      this.renderGraph();
    }
  }
};
</script>

<style scoped>
.error-correlation-container {
  background-color: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  padding: 20px;
  margin-bottom: 20px;
}

.correlation-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
}

.correlation-header h3 {
  margin: 0;
  font-size: 18px;
  color: #333;
}

.correlation-controls {
  display: flex;
  align-items: center;
}

.correlation-controls select {
  margin-right: 10px;
  padding: 5px 10px;
  border: 1px solid #ddd;
  border-radius: 4px;
  background-color: #f9f9f9;
}

.refresh-button {
  background-color: #f1f1f1;
  border: none;
  border-radius: 4px;
  padding: 5px 10px;
  cursor: pointer;
}

.correlation-container {
  position: relative;
  height: 400px;
  margin-bottom: 15px;
  border: 1px solid #eee;
  border-radius: 4px;
}

.correlation-loading, .correlation-error, .correlation-empty {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  background-color: rgba(255, 255, 255, 0.8);
}

.spinner {
  border: 3px solid #f3f3f3;
  border-top: 3px solid #3498db;
  border-radius: 50%;
  width: 30px;
  height: 30px;
  animation: spin 1s linear infinite;
  margin-bottom: 10px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.correlation-graph {
  width: 100%;
  height: 100%;
}

.node-details {
  background-color: #f9f9f9;
  border-radius: 6px;
  padding: 15px;
  margin-bottom: 15px;
}

.details-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10px;
}

.details-header h4 {
  margin: 0;
  font-size: 16px;
  color: #333;
}

.close-button {
  background: none;
  border: none;
  font-size: 20px;
  cursor: pointer;
  color: #666;
}

.details-content {
  margin-bottom: 15px;
}

.detail-item {
  margin-bottom: 8px;
}

.detail-label {
  font-weight: bold;
  margin-right: 5px;
}

.severity-badge {
  display: inline-block;
  padding: 2px 6px;
  border-radius: 3px;
  font-size: 12px;
}

.severity-badge.critical {
  background-color: #e74c3c;
  color: #fff;
}

.severity-badge.high {
  background-color: #e67e22;
  color: #fff;
}

.severity-badge.medium {
  background-color: #f1c40f;
}

.severity-badge.low {
  background-color: #3498db;
  color: #fff;
}

.detail-section {
  margin-top: 15px;
}

.detail-section h5 {
  margin-top: 0;
  margin-bottom: 10px;
  font-size: 14px;
  color: #333;
}

.connections-list {
  margin: 0;
  padding-left: 20px;
}

.connections-list li {
  margin-bottom: 5px;
  font-size: 13px;
}

.connection-label {
  font-weight: bold;
  margin-right: 5px;
}

.connection-strength {
  color: #666;
  font-size: 12px;
}

.error-samples {
  margin-top: 10px;
}

.error-sample {
  background-color: #fff;
  border-left: 4px solid #ddd;
  padding: 10px;
  margin-bottom: 10px;
  border-radius: 0 4px 4px 0;
}

.sample-header {
  display: flex;
  justify-content: space-between;
  margin-bottom: 5px;
}

.sample-severity {
  font-weight: bold;
  padding: 2px 6px;
  border-radius: 3px;
  font-size: 12px;
}

.sample-severity.critical {
  background-color: #e74c3c;
  color: #fff;
}

.sample-severity.high {
  background-color: #e67e22;
  color: #fff;
}

.sample-severity.medium {
  background-color: #f1c40f;
}

.sample-severity.low {
  background-color: #3498db;
  color: #fff;
}

.sample-time {
  font-size: 12px;
  color: #666;
}

.sample-message {
  font-size: 14px;
  margin-bottom: 5px;
}

.sample-code {
  font-size: 12px;
  color: #666;
  font-family: monospace;
}

.details-footer {
  text-align: right;
}

.details-footer button {
  background-color: #3498db;
  color: #fff;
  border: none;
  border-radius: 4px;
  padding: 8px 15px;
  cursor: pointer;
}

.correlation-insights {
  background-color: #f9f9f9;
  border-radius: 6px;
  padding: 15px;
}

.correlation-insights h4 {
  margin-top: 0;
  margin-bottom: 10px;
  font-size: 16px;
  color: #333;
}

.correlation-insights ul {
  margin: 0;
  padding-left: 20px;
}

.correlation-insights li {
  margin-bottom: 5px;
  font-size: 14px;
  color: #555;
}
</style>
