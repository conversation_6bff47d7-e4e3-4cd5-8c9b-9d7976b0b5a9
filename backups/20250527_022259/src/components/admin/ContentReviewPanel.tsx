import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { useToast } from '@/components/ui/use-toast';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Badge } from '@/components/ui/badge';
import { Checkbox } from '@/components/ui/checkbox';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger
} from '@/components/ui/dialog';
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import {
  AlertCircle,
  CheckCircle,
  XCircle,
  Edit,
  Eye,
  Filter,
  RefreshCw,
  Clock,
  Tag,
  AlertTriangle,
  List,
  LayoutGrid,
  Zap
} from 'lucide-react';
import LoadingSpinner from '@/components/ui/loading-spinner';
import ErrorDisplay from '@/components/ui/error-display';
import { formatDistanceToNow } from 'date-fns';
import { zhCN } from 'date-fns/locale';
import { Pagination } from '@/components/ui/pagination';
import VirtualContentTable from './VirtualContentTable';

// 导入数据服务 - 使用新的API服务
import {
  getContentsPending,
  getContentPending,
  approveContentNew,
  editAndApproveContentNew,
  rejectContentNew,
  getReviewTemplates
} from '@/services/dataService';

// 导入预加载服务
import { preloadNextPageData } from '@/services/preloadService';

// 内容类型
enum ContentType {
  QUESTIONNAIRE = 'questionnaire',
  STORY = 'story'
}

// 内容状态
enum ContentStatus {
  PENDING = 'pending',
  APPROVED = 'approved',
  REJECTED = 'rejected',
  EDITED = 'edited'
}

// 待审核内容接口
interface PendingContent {
  id: string;
  sequenceNumber: string;
  type: ContentType;
  originalContent: any;
  sanitizedContent?: any;
  status: ContentStatus;
  reviewerId?: string;
  reviewedAt?: string;
  originIp?: string;
  userAgent?: string;
  flags: string[];
  reviewNotes?: string;
  createdAt: string;
  updatedAt: string;
}

// 内容审核面板组件
export default function ContentReviewPanel() {
  const navigate = useNavigate();
  const { toast } = useToast();
  const [pendingContents, setPendingContents] = useState<PendingContent[]>([]);
  const [selectedContent, setSelectedContent] = useState<PendingContent | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [isDetailLoading, setIsDetailLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [activeTab, setActiveTab] = useState('all');
  const [reviewNotes, setReviewNotes] = useState('');
  const [editedContent, setEditedContent] = useState<any>(null);
  const [rejectReason, setRejectReason] = useState('');
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [isDialogOpen, setIsDialogOpen] = useState(false);
  const [dialogMode, setDialogMode] = useState<'view' | 'edit' | 'reject'>('view');
  const [templates, setTemplates] = useState<any[]>([]);
  const [isTemplatesLoading, setIsTemplatesLoading] = useState(false);

  // 确认对话框状态
  const [isConfirmDialogOpen, setIsConfirmDialogOpen] = useState(false);
  const [confirmAction, setConfirmAction] = useState<'approve' | 'edit' | 'reject' | null>(null);

  // 筛选条件和分页
  const [filters, setFilters] = useState({
    type: '',
    flag: ''
  });
  const [pagination, setPagination] = useState({
    page: 1,
    pageSize: 20, // 增加每页数量，优化虚拟滚动体验
    total: 0,
    totalPages: 0
  });

  // 虚拟滚动相关状态
  const [useVirtualScroll, setUseVirtualScroll] = useState(false); // 是否使用虚拟滚动
  const [isLoadingMore, setIsLoadingMore] = useState(false); // 是否正在加载更多

  // 批量操作状态
  const [selectedIds, setSelectedIds] = useState<string[]>([]);
  const [isBatchDialogOpen, setIsBatchDialogOpen] = useState(false);
  const [batchAction, setBatchAction] = useState<'approve' | 'reject' | null>(null);
  const [batchRejectReason, setBatchRejectReason] = useState('');

  // 获取待审核内容列表
  const fetchPendingContents = async (page = pagination.page, append = false) => {
    try {
      if (append) {
        setIsLoadingMore(true);
      } else {
        setIsLoading(true);
      }
      setError(null);

      // 构建查询参数
      const queryParams = new URLSearchParams();

      // 添加分页参数
      queryParams.append('page', page.toString());
      queryParams.append('pageSize', pagination.pageSize.toString());

      if (filters.type) {
        queryParams.append('type', filters.type);
      }

      if (filters.flag) {
        queryParams.append('flag', filters.flag);
      }

      // 获取待审核内容 - 使用新的API
      const params = {
        page,
        pageSize: pagination.pageSize,
        type: filters.type || undefined,
        search: filters.flag || undefined
      };

      const response = await getContentsPending(params);

      if (response && response.success) {
        if (append) {
          // 追加数据（用于虚拟滚动）
          setPendingContents(prev => [...prev, ...(response.contents || [])]);
        } else {
          // 替换数据（用于普通分页）
          setPendingContents(response.contents || []);
        }

        // 更新分页信息
        if (response.pagination) {
          setPagination({
            page: response.pagination.page,
            pageSize: response.pagination.pageSize,
            total: response.pagination.total,
            totalPages: response.pagination.totalPages
          });
        }
      } else {
        console.log('获取待审核内容失败:', response);
        setError(response?.error || '获取待审核内容失败');
      }
    } catch (error) {
      console.error('获取待审核内容错误:', error);
      setError('服务器错误，请稍后再试');
    } finally {
      setIsLoading(false);
      setIsLoadingMore(false);
    }
  };

  // 处理页码变化
  const handlePageChange = (newPage: number) => {
    fetchPendingContents(newPage);

    // 预加载下一页数据
    preloadNextPageData(newPage, pagination.pageSize);
  };

  // 加载更多数据（用于虚拟滚动）
  const handleLoadMore = () => {
    if (isLoadingMore || pagination.page >= pagination.totalPages) {
      return;
    }

    const nextPage = pagination.page + 1;
    fetchPendingContents(nextPage, true);
  };

  // 切换虚拟滚动模式
  const toggleVirtualScroll = () => {
    setUseVirtualScroll(prev => !prev);

    // 如果开启虚拟滚动，重置数据并加载第一页
    if (!useVirtualScroll) {
      setPendingContents([]);
      fetchPendingContents(1);
    }
  };

  // 获取单条待审核内容详情
  const fetchPendingContent = async (id: string) => {
    try {
      setIsDetailLoading(true);

      const response = await getContentPending(id);

      if (response && response.success) {
        setSelectedContent(response.pendingContent);

        // 如果是编辑模式，初始化编辑内容
        if (dialogMode === 'edit') {
          setEditedContent(response.pendingContent.sanitizedContent || response.pendingContent.originalContent);
        }
      } else {
        console.log('获取待审核内容详情失败:', response);
        toast({
          title: '获取详情失败',
          description: response?.error || '获取待审核内容详情失败',
          variant: 'destructive'
        });
      }
    } catch (error) {
      console.error('获取待审核内容详情错误:', error);
      toast({
        title: '获取详情失败',
        description: '服务器错误，请稍后再试',
        variant: 'destructive'
      });
    } finally {
      setIsDetailLoading(false);
    }
  };

  // 打开确认对话框
  const openConfirmDialog = (action: 'approve' | 'edit' | 'reject') => {
    // 验证表单
    if (action === 'reject' && !rejectReason.trim()) {
      toast({
        title: '验证失败',
        description: '请输入拒绝原因',
        variant: 'destructive'
      });
      return;
    }

    if (action === 'edit' && !editedContent) {
      toast({
        title: '验证失败',
        description: '请编辑内容',
        variant: 'destructive'
      });
      return;
    }

    setConfirmAction(action);
    setIsConfirmDialogOpen(true);
  };

  // 执行确认的操作
  const executeConfirmedAction = async () => {
    if (!confirmAction || !selectedContent) return;

    try {
      setIsSubmitting(true);

      let response;

      switch (confirmAction) {
        case 'approve':
          response = await approveContentNew(selectedContent.id, { reviewNotes });
          break;
        case 'edit':
          response = await editAndApproveContentNew(selectedContent.id, {
            content: editedContent,
            reviewNotes
          });
          break;
        case 'reject':
          response = await rejectContentNew(selectedContent.id, { reason: rejectReason, reviewNotes });
          break;
      }

      if (response.success) {
        toast({
          title: confirmAction === 'approve' ? '审核成功' :
                 confirmAction === 'edit' ? '编辑成功' : '拒绝成功',
          description: confirmAction === 'approve' ? '内容已通过审核' :
                       confirmAction === 'edit' ? '内容已编辑并通过审核' : '内容已被拒绝',
        });

        // 关闭对话框
        setIsDialogOpen(false);
        setIsConfirmDialogOpen(false);

        // 刷新列表
        fetchPendingContents();
      } else {
        toast({
          title: confirmAction === 'approve' ? '审核失败' :
                 confirmAction === 'edit' ? '编辑失败' : '拒绝失败',
          description: response.error || '操作失败，请重试',
          variant: 'destructive'
        });
      }
    } catch (error) {
      console.error('操作内容错误:', error);
      toast({
        title: '操作失败',
        description: '服务器错误，请稍后再试',
        variant: 'destructive'
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  // 审核通过内容
  const handleApproveContent = () => {
    openConfirmDialog('approve');
  };

  // 编辑并通过内容
  const handleEditContent = () => {
    openConfirmDialog('edit');
  };

  // 拒绝内容
  const handleRejectContent = () => {
    openConfirmDialog('reject');
  };

  // 处理选择/取消选择内容
  const handleSelectContent = (id: string, isSelected: boolean) => {
    if (isSelected) {
      setSelectedIds(prev => [...prev, id]);
    } else {
      setSelectedIds(prev => prev.filter(itemId => itemId !== id));
    }
  };

  // 处理全选/取消全选
  const handleSelectAll = (isSelected: boolean) => {
    if (isSelected) {
      setSelectedIds(pendingContents.map(content => content.id));
    } else {
      setSelectedIds([]);
    }
  };

  // 打开批量操作对话框
  const openBatchDialog = (action: 'approve' | 'reject') => {
    if (selectedIds.length === 0) {
      toast({
        title: '请先选择内容',
        description: '请至少选择一项内容进行批量操作',
        variant: 'destructive'
      });
      return;
    }

    setBatchAction(action);
    setBatchRejectReason('');
    setIsBatchDialogOpen(true);
  };

  // 执行批量操作
  const executeBatchAction = async () => {
    if (!batchAction || selectedIds.length === 0) return;

    // 验证表单
    if (batchAction === 'reject' && !batchRejectReason.trim()) {
      toast({
        title: '验证失败',
        description: '请输入拒绝原因',
        variant: 'destructive'
      });
      return;
    }

    try {
      setIsSubmitting(true);

      // 执行批量操作
      const results = [];

      for (const id of selectedIds) {
        try {
          let response;

          if (batchAction === 'approve') {
            response = await approveContentNew(id, { reviewNotes: '批量通过' });
          } else if (batchAction === 'reject') {
            response = await rejectContentNew(id, { reason: batchRejectReason });
          }

          results.push({
            id,
            success: response.success,
            error: response.error
          });
        } catch (error) {
          console.error(`批量操作内容错误 (ID: ${id}):`, error);
          results.push({
            id,
            success: false,
            error: '操作失败'
          });
        }
      }

      // 统计结果
      const successCount = results.filter(r => r.success).length;
      const failCount = results.length - successCount;

      // 显示结果
      toast({
        title: '批量操作完成',
        description: `成功: ${successCount}, 失败: ${failCount}`,
        variant: successCount > 0 ? 'default' : 'destructive'
      });

      // 关闭对话框
      setIsBatchDialogOpen(false);

      // 清空选择
      setSelectedIds([]);

      // 刷新列表
      fetchPendingContents();
    } catch (error) {
      console.error('批量操作错误:', error);
      toast({
        title: '批量操作失败',
        description: '服务器错误，请稍后再试',
        variant: 'destructive'
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  // 打开内容详情对话框
  const openContentDialog = (content: PendingContent, mode: 'view' | 'edit' | 'reject') => {
    setSelectedContent(content);
    setDialogMode(mode);
    setReviewNotes('');
    setRejectReason('');

    if (mode === 'edit') {
      setEditedContent(content.sanitizedContent || content.originalContent);
    }

    setIsDialogOpen(true);

    // 获取最新详情
    fetchPendingContent(content.id);
  };

  // 获取审核模板
  const fetchTemplates = async () => {
    try {
      setIsTemplatesLoading(true);

      const response = await getReviewTemplates();

      if (response && response.success) {
        setTemplates(response.templates || []);
      } else {
        console.error('获取审核模板失败:', response?.error);
      }
    } catch (error) {
      console.error('获取审核模板错误:', error);
    } finally {
      setIsTemplatesLoading(false);
    }
  };

  // 应用模板
  const applyTemplate = (templateContent: string) => {
    if (dialogMode === 'reject') {
      setRejectReason(templateContent);
    } else {
      setReviewNotes(templateContent);
    }
  };

  // 处理键盘快捷键
  const handleKeyDown = (e: KeyboardEvent) => {
    // 只有在对话框打开时才处理快捷键
    if (!isDialogOpen || isSubmitting) return;

    // Alt + A: 通过
    if (e.altKey && e.key === 'a') {
      e.preventDefault();
      if (dialogMode === 'view') {
        handleApproveContent();
      }
    }

    // Alt + E: 编辑
    if (e.altKey && e.key === 'e') {
      e.preventDefault();
      if (dialogMode === 'edit') {
        handleEditContent();
      }
    }

    // Alt + R: 拒绝
    if (e.altKey && e.key === 'r') {
      e.preventDefault();
      if (dialogMode === 'reject') {
        handleRejectContent();
      }
    }

    // Escape: 关闭对话框
    if (e.key === 'Escape') {
      e.preventDefault();
      setIsDialogOpen(false);
    }
  };

  // 添加和移除键盘事件监听器
  useEffect(() => {
    window.addEventListener('keydown', handleKeyDown);

    return () => {
      window.removeEventListener('keydown', handleKeyDown);
    };
  }, [isDialogOpen, dialogMode, isSubmitting]);

  // 组件加载时获取待审核内容
  useEffect(() => {
    fetchPendingContents();
    fetchTemplates();

    // 预加载下一页数据
    const timer = setTimeout(() => {
      preloadNextPageData(pagination.page, pagination.pageSize);
    }, 1000); // 延迟1秒预加载，优先加载当前页

    return () => clearTimeout(timer);
  }, [filters]);

  // 渲染标记徽章
  const renderFlagBadge = (flag: string) => {
    let color = 'default';
    let icon = null;

    switch (flag) {
      case 'sensitive-words':
        color = 'destructive';
        icon = <AlertCircle className="h-3 w-3 mr-1" />;
        break;
      case 'high-exposure':
        color = 'destructive';
        icon = <AlertTriangle className="h-3 w-3 mr-1" />;
        break;
      case 'suspicious-behavior':
        color = 'warning';
        icon = <AlertCircle className="h-3 w-3 mr-1" />;
        break;
      default:
        color = 'secondary';
    }

    return (
      <Badge variant={color as any} className="mr-1 mb-1">
        {icon}
        {flag}
      </Badge>
    );
  };

  // 渲染内容类型徽章
  const renderTypeBadge = (type: ContentType) => {
    return (
      <Badge variant={type === ContentType.STORY ? 'default' : 'outline'}>
        {type === ContentType.STORY ? '故事' : '问卷'}
      </Badge>
    );
  };

  // 渲染加载状态
  if (isLoading) {
    return (
      <div className="flex justify-center items-center py-12">
        <LoadingSpinner size="lg" text="加载待审核内容中..." />
      </div>
    );
  }

  // 渲染错误状态
  if (error) {
    return (
      <ErrorDisplay
        title="加载失败"
        message={error}
        onRetry={fetchPendingContents}
      />
    );
  }

  return (
    <div className="space-y-4">
      <div className="flex justify-between items-center">
        <h2 className="text-2xl font-bold">内容审核</h2>

        <div className="flex items-center space-x-2">
          <Button
            variant="default"
            onClick={() => navigate('/admin/quick-review', {
              state: { filters }
            })}
            className="bg-primary text-primary-foreground hover:bg-primary/90"
          >
            <Zap className="h-4 w-4 mr-2" />
            快速审核模式
          </Button>

          <Select
            value={filters.type}
            onValueChange={(value) => setFilters({ ...filters, type: value })}
          >
            <SelectTrigger className="w-[150px]">
              <SelectValue placeholder="内容类型" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="">全部类型</SelectItem>
              <SelectItem value={ContentType.STORY}>故事</SelectItem>
              <SelectItem value={ContentType.QUESTIONNAIRE}>问卷</SelectItem>
            </SelectContent>
          </Select>

          <Select
            value={filters.flag}
            onValueChange={(value) => setFilters({ ...filters, flag: value })}
          >
            <SelectTrigger className="w-[150px]">
              <SelectValue placeholder="标记类型" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="">全部标记</SelectItem>
              <SelectItem value="sensitive-words">敏感词</SelectItem>
              <SelectItem value="high-exposure">高暴露</SelectItem>
              <SelectItem value="suspicious-behavior">可疑行为</SelectItem>
            </SelectContent>
          </Select>

          <Button variant="outline" onClick={fetchPendingContents}>
            <RefreshCw className="h-4 w-4 mr-2" />
            刷新
          </Button>

          <Button
            variant={useVirtualScroll ? "default" : "outline"}
            onClick={toggleVirtualScroll}
            title={useVirtualScroll ? "切换到普通模式" : "切换到虚拟滚动模式"}
          >
            {useVirtualScroll ? <LayoutGrid className="h-4 w-4" /> : <List className="h-4 w-4" />}
          </Button>
        </div>
      </div>

      {/* 批量操作按钮 */}
      {selectedIds.length > 0 && (
        <div className="bg-muted p-2 rounded-md flex items-center justify-between">
          <div className="text-sm">
            已选择 <span className="font-medium">{selectedIds.length}</span> 项
          </div>
          <div className="flex space-x-2">
            <Button
              variant="outline"
              size="sm"
              onClick={() => setSelectedIds([])}
            >
              取消选择
            </Button>
            <Button
              variant="default"
              size="sm"
              onClick={() => openBatchDialog('approve')}
            >
              <CheckCircle className="h-4 w-4 mr-1" />
              批量通过
            </Button>
            <Button
              variant="destructive"
              size="sm"
              onClick={() => openBatchDialog('reject')}
            >
              <XCircle className="h-4 w-4 mr-1" />
              批量拒绝
            </Button>
          </div>
        </div>
      )}

      {pendingContents.length === 0 ? (
        <div className="flex flex-col items-center justify-center p-12 bg-muted/50 rounded-lg">
          <CheckCircle className="h-12 w-12 text-muted-foreground mb-4" />
          <h3 className="text-xl font-medium">没有待审核内容</h3>
          <p className="text-muted-foreground mt-2">所有内容已审核完毕</p>
        </div>
      ) : useVirtualScroll ? (
        // 虚拟滚动表格
        <VirtualContentTable
          contents={pendingContents}
          selectedIds={selectedIds}
          onSelectContent={handleSelectContent}
          onSelectAll={handleSelectAll}
          onViewContent={(content) => openContentDialog(content, 'view')}
          onEditContent={(content) => openContentDialog(content, 'edit')}
          onRejectContent={(content) => openContentDialog(content, 'reject')}
          onLoadMore={handleLoadMore}
          isLoading={isLoadingMore}
          height={600}
        />
      ) : (
        // 普通表格
        <div className="border rounded-lg">
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead className="w-[40px]">
                  <Checkbox
                    checked={selectedIds.length === pendingContents.length && pendingContents.length > 0}
                    onCheckedChange={handleSelectAll}
                    aria-label="全选"
                  />
                </TableHead>
                <TableHead>序列号</TableHead>
                <TableHead>类型</TableHead>
                <TableHead>内容预览</TableHead>
                <TableHead>标记</TableHead>
                <TableHead>提交时间</TableHead>
                <TableHead>操作</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {pendingContents.map((content) => (
                <TableRow key={content.id}>
                  <TableCell>
                    <Checkbox
                      checked={selectedIds.includes(content.id)}
                      onCheckedChange={(checked) => handleSelectContent(content.id, !!checked)}
                      aria-label={`选择 ${content.sequenceNumber}`}
                    />
                  </TableCell>
                  <TableCell className="font-mono">{content.sequenceNumber}</TableCell>
                  <TableCell>{renderTypeBadge(content.type)}</TableCell>
                  <TableCell className="max-w-xs truncate">
                    {content.type === ContentType.STORY
                      ? content.originalContent.title
                      : `问卷回复 #${content.sequenceNumber}`}
                  </TableCell>
                  <TableCell>
                    <div className="flex flex-wrap">
                      {content.flags.map((flag) => (
                        <span key={flag}>{renderFlagBadge(flag)}</span>
                      ))}
                    </div>
                  </TableCell>
                  <TableCell>
                    <div className="flex items-center">
                      <Clock className="h-4 w-4 mr-1 text-muted-foreground" />
                      <span title={new Date(content.createdAt).toLocaleString()}>
                        {formatDistanceToNow(new Date(content.createdAt), { addSuffix: true, locale: zhCN })}
                      </span>
                    </div>
                  </TableCell>
                  <TableCell>
                    <div className="flex items-center space-x-2">
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => openContentDialog(content, 'view')}
                      >
                        <Eye className="h-4 w-4 mr-1" />
                        查看
                      </Button>
                      <Button
                        variant="default"
                        size="sm"
                        onClick={() => openContentDialog(content, 'edit')}
                      >
                        <Edit className="h-4 w-4 mr-1" />
                        编辑
                      </Button>
                      <Button
                        variant="destructive"
                        size="sm"
                        onClick={() => openContentDialog(content, 'reject')}
                      >
                        <XCircle className="h-4 w-4 mr-1" />
                        拒绝
                      </Button>
                    </div>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>

          {/* 分页 */}
          <div className="flex justify-center py-4">
            <Pagination
              currentPage={pagination.page}
              totalPages={pagination.totalPages}
              onPageChange={handlePageChange}
            />
          </div>
        </div>
      )}

      {/* 内容详情对话框 */}
      <Dialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
        <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
          {isDetailLoading ? (
            <div className="flex justify-center items-center py-12">
              <LoadingSpinner size="md" text="加载内容详情..." />
            </div>
          ) : selectedContent ? (
            <>
              <DialogHeader>
                <DialogTitle>
                  {dialogMode === 'view' && '查看内容'}
                  {dialogMode === 'edit' && '编辑内容'}
                  {dialogMode === 'reject' && '拒绝内容'}
                  {' - '}
                  {selectedContent.sequenceNumber}
                </DialogTitle>
                <DialogDescription>
                  {selectedContent.type === ContentType.STORY ? '故事内容' : '问卷回复'}
                  {' - '}
                  提交于 {new Date(selectedContent.createdAt).toLocaleString()}
                </DialogDescription>
              </DialogHeader>

              <div className="space-y-4">
                {/* 标记信息 */}
                {selectedContent.flags.length > 0 && (
                  <div className="bg-muted p-3 rounded-md">
                    <h4 className="font-medium mb-2 flex items-center">
                      <Tag className="h-4 w-4 mr-1" />
                      标记信息
                    </h4>
                    <div className="flex flex-wrap">
                      {selectedContent.flags.map((flag) => (
                        <span key={flag}>{renderFlagBadge(flag)}</span>
                      ))}
                    </div>
                  </div>
                )}

                {/* 内容显示/编辑 */}
                {selectedContent.type === ContentType.STORY && (
                  <div className="space-y-4">
                    {dialogMode === 'edit' ? (
                      <>
                        <div>
                          <label className="block text-sm font-medium mb-1">标题</label>
                          <Input
                            value={editedContent?.title || ''}
                            onChange={(e) => setEditedContent({ ...editedContent, title: e.target.value })}
                          />
                        </div>
                        <div>
                          <label className="block text-sm font-medium mb-1">内容</label>
                          <Textarea
                            value={editedContent?.content || ''}
                            onChange={(e) => setEditedContent({ ...editedContent, content: e.target.value })}
                            rows={10}
                          />
                        </div>
                      </>
                    ) : (
                      <>
                        <div>
                          <h3 className="text-lg font-medium">{selectedContent.originalContent.title}</h3>
                          <div className="mt-2 whitespace-pre-wrap">
                            {selectedContent.originalContent.content}
                          </div>
                        </div>

                        {/* 如果有脱敏内容，显示对比 */}
                        {selectedContent.sanitizedContent && (
                          <div className="mt-4 border-t pt-4">
                            <h4 className="font-medium mb-2">脱敏后内容</h4>
                            <div className="bg-muted p-3 rounded-md">
                              <h5 className="font-medium">{selectedContent.sanitizedContent.title}</h5>
                              <div className="mt-2 whitespace-pre-wrap">
                                {selectedContent.sanitizedContent.content}
                              </div>
                            </div>
                          </div>
                        )}
                      </>
                    )}
                  </div>
                )}

                {/* 提交信息 */}
                <div className="bg-muted p-3 rounded-md text-sm">
                  <div className="grid grid-cols-2 gap-2">
                    <div>
                      <span className="font-medium">IP地址:</span> {selectedContent.originIp || '未知'}
                    </div>
                    <div>
                      <span className="font-medium">匿名提交:</span> {selectedContent.originalContent.isAnonymous ? '是' : '否'}
                    </div>
                  </div>
                </div>

                {/* 审核意见/拒绝原因 */}
                {dialogMode === 'reject' ? (
                  <div className="space-y-3">
                    <div>
                      <div className="flex justify-between items-center">
                        <label className="block text-sm font-medium mb-1">拒绝原因</label>
                        {!isTemplatesLoading && templates.length > 0 && (
                          <Select
                            onValueChange={(value) => {
                              const template = templates.find(t => t.id === value);
                              if (template && template.type === 'rejection') {
                                applyTemplate(template.content);
                              }
                            }}
                          >
                            <SelectTrigger className="w-[180px]">
                              <SelectValue placeholder="选择拒绝模板" />
                            </SelectTrigger>
                            <SelectContent>
                              {templates
                                .filter(t => t.type === 'rejection')
                                .map(template => (
                                  <SelectItem key={template.id} value={template.id}>
                                    {template.name}
                                  </SelectItem>
                                ))
                              }
                            </SelectContent>
                          </Select>
                        )}
                      </div>
                      <Textarea
                        value={rejectReason}
                        onChange={(e) => setRejectReason(e.target.value)}
                        placeholder="请输入拒绝原因，将通知用户"
                        rows={3}
                      />
                    </div>
                  </div>
                ) : (
                  <div className="space-y-3">
                    <div>
                      <div className="flex justify-between items-center">
                        <label className="block text-sm font-medium mb-1">审核意见（可选）</label>
                        {!isTemplatesLoading && templates.length > 0 && (
                          <Select
                            onValueChange={(value) => {
                              const template = templates.find(t => t.id === value);
                              if (template) {
                                if ((dialogMode === 'view' && template.type === 'approval') ||
                                    (dialogMode === 'edit' && template.type === 'edit')) {
                                  applyTemplate(template.content);
                                }
                              }
                            }}
                          >
                            <SelectTrigger className="w-[180px]">
                              <SelectValue placeholder={`选择${dialogMode === 'edit' ? '编辑' : '通过'}模板`} />
                            </SelectTrigger>
                            <SelectContent>
                              {templates
                                .filter(t => dialogMode === 'edit' ? t.type === 'edit' : t.type === 'approval')
                                .map(template => (
                                  <SelectItem key={template.id} value={template.id}>
                                    {template.name}
                                  </SelectItem>
                                ))
                              }
                            </SelectContent>
                          </Select>
                        )}
                      </div>
                      <Textarea
                        value={reviewNotes}
                        onChange={(e) => setReviewNotes(e.target.value)}
                        placeholder="请输入审核意见（仅管理员可见）"
                        rows={3}
                      />
                    </div>
                  </div>
                )}
              </div>

              <DialogFooter>
                <Button variant="outline" onClick={() => setIsDialogOpen(false)}>
                  取消 <span className="ml-1 text-xs opacity-60">Esc</span>
                </Button>

                {dialogMode === 'view' && (
                  <Button
                    variant="default"
                    onClick={handleApproveContent}
                    disabled={isSubmitting}
                  >
                    {isSubmitting ? <LoadingSpinner size="sm" /> : <CheckCircle className="h-4 w-4 mr-1" />}
                    通过 <span className="ml-1 text-xs opacity-60">Alt+A</span>
                  </Button>
                )}

                {dialogMode === 'edit' && (
                  <Button
                    variant="default"
                    onClick={handleEditContent}
                    disabled={isSubmitting}
                  >
                    {isSubmitting ? <LoadingSpinner size="sm" /> : <Edit className="h-4 w-4 mr-1" />}
                    编辑并通过 <span className="ml-1 text-xs opacity-60">Alt+E</span>
                  </Button>
                )}

                {dialogMode === 'reject' && (
                  <Button
                    variant="destructive"
                    onClick={handleRejectContent}
                    disabled={isSubmitting || !rejectReason}
                  >
                    {isSubmitting ? <LoadingSpinner size="sm" /> : <XCircle className="h-4 w-4 mr-1" />}
                    拒绝 <span className="ml-1 text-xs opacity-60">Alt+R</span>
                  </Button>
                )}
              </DialogFooter>
            </>
          ) : (
            <div className="text-center py-8">
              <p>内容不存在或已被删除</p>
            </div>
          )}
        </DialogContent>
      </Dialog>

      {/* 确认对话框 */}
      <AlertDialog open={isConfirmDialogOpen} onOpenChange={setIsConfirmDialogOpen}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>
              {confirmAction === 'approve' ? '确认通过' :
               confirmAction === 'edit' ? '确认编辑并通过' : '确认拒绝'}
            </AlertDialogTitle>
            <AlertDialogDescription>
              {confirmAction === 'approve' && '您确定要通过此内容吗？通过后内容将被发布。'}
              {confirmAction === 'edit' && '您确定要编辑并通过此内容吗？编辑后的内容将被发布。'}
              {confirmAction === 'reject' && '您确定要拒绝此内容吗？拒绝后将通知用户。'}
              <div className="mt-4 p-3 bg-muted rounded-md">
                <p className="text-sm font-medium">操作不可撤销，请确认：</p>
                <ul className="mt-2 text-sm list-disc list-inside">
                  <li>您已仔细阅读了内容</li>
                  <li>您的决定符合平台规范</li>
                  <li>此操作将被记录在审核日志中</li>
                </ul>
              </div>
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel disabled={isSubmitting}>取消</AlertDialogCancel>
            <AlertDialogAction
              onClick={executeConfirmedAction}
              disabled={isSubmitting}
              className={
                confirmAction === 'approve' ? 'bg-green-600 hover:bg-green-700' :
                confirmAction === 'edit' ? 'bg-blue-600 hover:bg-blue-700' :
                'bg-red-600 hover:bg-red-700'
              }
            >
              {isSubmitting ? <LoadingSpinner size="sm" /> : (
                confirmAction === 'approve' ? '确认通过' :
                confirmAction === 'edit' ? '确认编辑' : '确认拒绝'
              )}
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>

      {/* 批量操作对话框 */}
      <Dialog open={isBatchDialogOpen} onOpenChange={setIsBatchDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>
              {batchAction === 'approve' ? '批量通过内容' : '批量拒绝内容'}
            </DialogTitle>
            <DialogDescription>
              您选择了 {selectedIds.length} 项内容进行{batchAction === 'approve' ? '批量通过' : '批量拒绝'}操作
            </DialogDescription>
          </DialogHeader>

          <div className="py-4">
            {batchAction === 'reject' ? (
              <div>
                <label className="block text-sm font-medium mb-1">拒绝原因</label>
                <Textarea
                  value={batchRejectReason}
                  onChange={(e) => setBatchRejectReason(e.target.value)}
                  placeholder="请输入拒绝原因，将通知用户"
                  rows={3}
                />
                <p className="text-sm text-muted-foreground mt-1">
                  此拒绝原因将应用于所有选中的内容
                </p>
              </div>
            ) : (
              <div className="bg-muted p-3 rounded-md">
                <p className="text-sm">
                  您即将批量通过 <span className="font-medium">{selectedIds.length}</span> 项内容。
                  这些内容将被发布到平台上，请确保您已经审核过这些内容。
                </p>
              </div>
            )}

            <div className="bg-yellow-50 border border-yellow-200 rounded-md p-3 mt-4 text-yellow-800">
              <p className="text-sm font-medium">批量操作注意事项：</p>
              <ul className="mt-1 text-sm list-disc list-inside">
                <li>批量操作将对所有选中的内容执行相同的操作</li>
                <li>操作完成后将显示成功和失败的数量</li>
                <li>所有操作都会被记录在审核日志中</li>
                <li>此操作不可撤销</li>
              </ul>
            </div>
          </div>

          <DialogFooter>
            <Button variant="outline" onClick={() => setIsBatchDialogOpen(false)}>
              取消
            </Button>
            <Button
              variant={batchAction === 'approve' ? 'default' : 'destructive'}
              onClick={executeBatchAction}
              disabled={isSubmitting || (batchAction === 'reject' && !batchRejectReason)}
            >
              {isSubmitting ? <LoadingSpinner size="sm" /> : (
                batchAction === 'approve' ?
                <CheckCircle className="h-4 w-4 mr-1" /> :
                <XCircle className="h-4 w-4 mr-1" />
              )}
              {batchAction === 'approve' ? '批量通过' : '批量拒绝'}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
}
