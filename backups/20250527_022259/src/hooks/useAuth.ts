import { useState, useEffect } from 'react';
import { Role } from '@/lib/permissions';

// 用户类型
export interface User {
  id: string;
  name: string;
  email: string;
  role: Role;
  avatar?: string;
  permissions?: string[];
  loginTime?: string; // 添加登录时间字段
}

// 认证状态
interface AuthState {
  isAuthenticated: boolean;
  user: User | null;
  loading: boolean;
  error: string | null;
}

/**
 * 认证钩子
 *
 * 提供用户认证状态和方法
 */
export function useAuth() {
  const [authState, setAuthState] = useState<AuthState>({
    isAuthenticated: false,
    user: null,
    loading: true,
    error: null
  });

  // 初始化时检查用户登录状态
  useEffect(() => {
    const checkAuth = async () => {
      try {
        // 从本地存储获取用户信息
        const token = localStorage.getItem('adminToken');
        const userJson = localStorage.getItem('adminUser');

        console.log('useAuth - 检查认证状态:', { hasToken: !!token, hasUser: !!userJson });

        if (!token || !userJson) {
          console.log('useAuth - 未找到登录信息，用户未登录');
          // 确保清除所有相关的存储
          localStorage.removeItem('adminToken');
          localStorage.removeItem('adminUser');
          sessionStorage.removeItem('redirectToRoleDashboard');
          setAuthState({
            isAuthenticated: false,
            user: null,
            loading: false,
            error: null
          });
          return;
        }

        // 解析用户信息
        let userData;
        try {
          userData = JSON.parse(userJson);
          console.log('useAuth - 从localStorage获取的用户数据:', userData);
        } catch (e) {
          console.error('useAuth - 解析用户数据失败:', e);
          // 清除无效的用户数据
          localStorage.removeItem('adminToken');
          localStorage.removeItem('adminUser');
          sessionStorage.removeItem('redirectToRoleDashboard');
          setAuthState({
            isAuthenticated: false,
            user: null,
            loading: false,
            error: '用户数据无效'
          });
          return;
        }

        // 检查用户数据是否包含必要的字段
        if (!userData || !userData.role) {
          console.error('useAuth - 用户数据不完整:', userData);
          // 清除无效的用户数据
          localStorage.removeItem('adminToken');
          localStorage.removeItem('adminUser');
          sessionStorage.removeItem('redirectToRoleDashboard');
          setAuthState({
            isAuthenticated: false,
            user: null,
            loading: false,
            error: '用户数据不完整'
          });
          return;
        }

        // 检查登录时间是否过期
        const loginTime = userData.loginTime ? new Date(userData.loginTime).getTime() : 0;
        const currentTime = new Date().getTime();
        const timeDiff = currentTime - loginTime;

        if (loginTime && timeDiff > 24 * 60 * 60 * 1000) {
          console.log('useAuth - 登录已过期，清除登录信息');
          localStorage.removeItem('adminToken');
          localStorage.removeItem('adminUser');
          sessionStorage.removeItem('redirectToRoleDashboard');
          setAuthState({
            isAuthenticated: false,
            user: null,
            loading: false,
            error: '登录已过期'
          });
          return;
        }

        // 确保角色是有效的
        let role: Role = 'user';
        if (userData.role === 'admin') {
          role = 'admin';
          console.log('useAuth - 用户角色: 管理员');
        } else if (userData.role === 'reviewer') {
          role = 'reviewer';
          console.log('useAuth - 用户角色: 审核员');
        } else if (userData.role === 'superadmin') {
          role = 'superadmin';
          console.log('useAuth - 用户角色: 超级管理员');
        } else {
          console.error('useAuth - 无效的用户角色:', userData.role);
          // 清除无效的用户数据
          localStorage.removeItem('adminToken');
          localStorage.removeItem('adminUser');
          sessionStorage.removeItem('redirectToRoleDashboard');
          setAuthState({
            isAuthenticated: false,
            user: null,
            loading: false,
            error: '用户角色无效'
          });
          return;
        }

        // 创建标准化的用户对象
        const user: User = {
          id: userData.id || userData.username || `user-${Date.now()}`,
          name: userData.name || userData.username || 'User',
          email: userData.email || `${userData.username || 'user'}@example.com`,
          role: role,
          permissions: userData.permissions || [],
          loginTime: userData.loginTime || new Date().toISOString()
        };

        console.log('useAuth - 标准化后的用户对象:', user);

        setAuthState({
          isAuthenticated: true,
          user,
          loading: false,
          error: null
        });
      } catch (error) {
        console.error('useAuth - 认证检查失败:', error);
        // 清除所有相关存储
        localStorage.removeItem('adminToken');
        localStorage.removeItem('adminUser');
        sessionStorage.removeItem('redirectToRoleDashboard');
        setAuthState({
          isAuthenticated: false,
          user: null,
          loading: false,
          error: error instanceof Error ? error.message : '认证失败'
        });
      }
    };

    checkAuth();
  }, []);

  /**
   * 登录方法
   *
   * @param email 邮箱
   * @param password 密码
   */
  const login = async (email: string, password: string) => {
    setAuthState(prev => ({ ...prev, loading: true, error: null }));

    try {
      // 实际应用中应该调用API进行登录
      // 这里使用模拟数据

      // 模拟API调用延迟
      await new Promise(resolve => setTimeout(resolve, 1000));

      // 根据邮箱确定用户角色（仅用于演示）
      let role: Role = 'user';
      if (email.includes('admin')) {
        role = 'admin';
        console.log('登录角色: 管理员');
      } else if (email.includes('reviewer')) {
        role = 'reviewer';
        console.log('登录角色: 审核员');
      } else if (email.includes('superadmin')) {
        role = 'superadmin';
        console.log('登录角色: 超级管理员');
      }

      // 创建模拟用户
      const user: User = {
        id: `user-${Date.now()}`,
        name: email.split('@')[0],
        email,
        role
      };

      // 保存到本地存储
      localStorage.setItem('adminToken', 'mock-token-' + Date.now());
      localStorage.setItem('adminUser', JSON.stringify(user));

      setAuthState({
        isAuthenticated: true,
        user,
        loading: false,
        error: null
      });

      return { success: true, user };
    } catch (error) {
      console.error('登录失败:', error);
      setAuthState(prev => ({
        ...prev,
        loading: false,
        error: error instanceof Error ? error.message : '登录失败'
      }));

      return { success: false, error };
    }
  };

  /**
   * 登出方法
   */
  const logout = () => {
    console.log('useAuth - 执行登出操作');

    // 清除所有相关的本地存储和会话存储
    localStorage.removeItem('adminToken');
    localStorage.removeItem('adminUser');
    sessionStorage.removeItem('redirectToRoleDashboard');

    // 清除其他可能的缓存数据
    sessionStorage.clear();

    // 重置认证状态
    setAuthState({
      isAuthenticated: false,
      user: null,
      loading: false,
      error: null
    });

    console.log('useAuth - 登出完成，所有缓存已清除');
  };

  /**
   * 更新用户信息
   *
   * @param userData 用户数据
   */
  const updateUser = (userData: Partial<User>) => {
    if (!authState.user) return;

    const updatedUser = {
      ...authState.user,
      ...userData
    };

    // 更新本地存储
    localStorage.setItem('adminUser', JSON.stringify(updatedUser));

    setAuthState(prev => ({
      ...prev,
      user: updatedUser
    }));
  };

  /**
   * 获取认证令牌
   */
  const getToken = () => {
    return localStorage.getItem('adminToken');
  };

  return {
    user: authState.user,
    isAuthenticated: authState.isAuthenticated,
    loading: authState.loading,
    error: authState.error,
    login,
    logout,
    updateUser,
    getToken
  };
}
