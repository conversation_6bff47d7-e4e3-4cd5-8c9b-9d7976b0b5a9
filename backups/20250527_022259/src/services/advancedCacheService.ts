/**
 * 高级缓存服务
 * 提供LRU缓存策略和高级缓存功能
 */

// 缓存项接口
interface CacheItem<T> {
  key: string;
  data: T;
  expiry: number; // 过期时间戳
  size: number;   // 数据大小（字节）
  lastAccessed: number; // 最后访问时间戳
}

/**
 * LRU缓存类
 * 实现最近最少使用缓存淘汰策略
 */
export class LRUCache<T> {
  private cache: Map<string, CacheItem<T>>;
  private maxSize: number;
  private currentSize: number;
  private maxItems: number;
  private defaultTTL: number;
  private hitCount: number;
  private missCount: number;

  /**
   * 构造函数
   * @param maxSize 最大缓存大小（字节）
   * @param maxItems 最大缓存项数量
   * @param defaultTTL 默认过期时间（毫秒）
   */
  constructor(maxSize: number = 5 * 1024 * 1024, maxItems: number = 100, defaultTTL: number = 5 * 60 * 1000) {
    this.cache = new Map();
    this.maxSize = maxSize;
    this.currentSize = 0;
    this.maxItems = maxItems;
    this.defaultTTL = defaultTTL;
    this.hitCount = 0;
    this.missCount = 0;
  }

  /**
   * 设置缓存项
   * @param key 缓存键
   * @param data 缓存数据
   * @param ttl 过期时间（毫秒）
   */
  set(key: string, data: T, ttl: number = this.defaultTTL): void {
    // 如果已存在，先移除旧项
    if (this.cache.has(key)) {
      const oldItem = this.cache.get(key)!;
      this.currentSize -= oldItem.size;
      this.cache.delete(key);
    }

    // 计算数据大小
    const size = this.calculateSize(data);

    // 如果单个项目超过最大缓存大小，不缓存
    if (size > this.maxSize) {
      console.warn(`Cache item size (${size} bytes) exceeds max cache size (${this.maxSize} bytes), not caching`);
      return;
    }

    // 确保有足够空间
    this.ensureSpace(size);

    // 创建缓存项
    const item: CacheItem<T> = {
      key,
      data,
      expiry: Date.now() + ttl,
      size,
      lastAccessed: Date.now()
    };

    // 添加到缓存
    this.cache.set(key, item);
    this.currentSize += size;
  }

  /**
   * 获取缓存项
   * @param key 缓存键
   * @returns 缓存数据，如果不存在或已过期则返回null
   */
  get(key: string): T | null {
    const item = this.cache.get(key);

    if (!item) {
      this.missCount++;
      return null;
    }

    // 检查是否过期
    if (Date.now() > item.expiry) {
      this.cache.delete(key);
      this.currentSize -= item.size;
      this.missCount++;
      return null;
    }

    // 更新最后访问时间
    item.lastAccessed = Date.now();
    this.hitCount++;

    return item.data;
  }

  /**
   * 删除缓存项
   * @param key 缓存键
   */
  delete(key: string): void {
    if (this.cache.has(key)) {
      const item = this.cache.get(key)!;
      this.currentSize -= item.size;
      this.cache.delete(key);
    }
  }

  /**
   * 清空缓存
   */
  clear(): void {
    this.cache.clear();
    this.currentSize = 0;
  }

  /**
   * 获取缓存统计信息
   */
  getStats(): {
    itemCount: number;
    currentSize: number;
    maxSize: number;
    hitRate: number;
    missRate: number;
  } {
    const totalRequests = this.hitCount + this.missCount;
    return {
      itemCount: this.cache.size,
      currentSize: this.currentSize,
      maxSize: this.maxSize,
      hitRate: totalRequests > 0 ? this.hitCount / totalRequests : 0,
      missRate: totalRequests > 0 ? this.missCount / totalRequests : 0
    };
  }

  /**
   * 确保有足够空间存储新项
   * @param size 新项大小
   */
  private ensureSpace(size: number): void {
    // 如果缓存项数量超过最大值或缓存大小不足，淘汰旧项
    while (
      (this.cache.size >= this.maxItems || this.currentSize + size > this.maxSize) &&
      this.cache.size > 0
    ) {
      this.evictLRU();
    }
  }

  /**
   * 淘汰最近最少使用的缓存项
   */
  private evictLRU(): void {
    let oldest: CacheItem<T> | null = null;
    let oldestKey: string | null = null;

    // 找出最近最少使用的项
    for (const [key, item] of this.cache.entries()) {
      if (!oldest || item.lastAccessed < oldest.lastAccessed) {
        oldest = item;
        oldestKey = key;
      }
    }

    // 移除最近最少使用的项
    if (oldestKey) {
      this.currentSize -= oldest!.size;
      this.cache.delete(oldestKey);
    }
  }

  /**
   * 计算数据大小（字节）
   * @param data 数据
   */
  private calculateSize(data: T): number {
    try {
      // 将数据转换为JSON字符串
      const json = JSON.stringify(data);
      // 计算字符串长度（近似字节数）
      return json.length * 2; // UTF-16编码，每个字符约2字节
    } catch (e) {
      // 如果无法序列化，返回估计值
      return 1024; // 默认1KB
    }
  }
}

// 创建全局LRU缓存实例
const contentCache = new LRUCache<any>(10 * 1024 * 1024); // 10MB
const settingsCache = new LRUCache<any>(1 * 1024 * 1024); // 1MB
const userCache = new LRUCache<any>(2 * 1024 * 1024); // 2MB

/**
 * 缓存内容列表
 * @param queryParams 查询参数
 * @param data 内容数据
 * @param ttl 过期时间（毫秒）
 */
export const cacheContentList = (queryParams: string, data: any, ttl?: number): void => {
  const cacheKey = `content_list_${queryParams || 'default'}`;
  contentCache.set(cacheKey, data, ttl);
};

/**
 * 获取缓存的内容列表
 * @param queryParams 查询参数
 * @returns 缓存的内容数据，如果不存在或已过期则返回null
 */
export const getCachedContentList = (queryParams: string): any | null => {
  const cacheKey = `content_list_${queryParams || 'default'}`;
  return contentCache.get(cacheKey);
};

/**
 * 缓存内容详情
 * @param id 内容ID
 * @param data 内容数据
 * @param ttl 过期时间（毫秒）
 */
export const cacheContentDetail = (id: string, data: any, ttl?: number): void => {
  const cacheKey = `content_detail_${id}`;
  contentCache.set(cacheKey, data, ttl);
};

/**
 * 获取缓存的内容详情
 * @param id 内容ID
 * @returns 缓存的内容数据，如果不存在或已过期则返回null
 */
export const getCachedContentDetail = (id: string): any | null => {
  const cacheKey = `content_detail_${id}`;
  return contentCache.get(cacheKey);
};

/**
 * 缓存设置
 * @param key 设置键
 * @param data 设置数据
 * @param ttl 过期时间（毫秒）
 */
export const cacheSettings = (key: string, data: any, ttl?: number): void => {
  const cacheKey = `settings_${key}`;
  settingsCache.set(cacheKey, data, ttl);
};

/**
 * 获取缓存的设置
 * @param key 设置键
 * @returns 缓存的设置数据，如果不存在或已过期则返回null
 */
export const getCachedSettings = (key: string): any | null => {
  const cacheKey = `settings_${key}`;
  return settingsCache.get(cacheKey);
};

/**
 * 缓存用户数据
 * @param userId 用户ID
 * @param data 用户数据
 * @param ttl 过期时间（毫秒）
 */
export const cacheUserData = (userId: string, data: any, ttl?: number): void => {
  const cacheKey = `user_${userId}`;
  userCache.set(cacheKey, data, ttl);
};

/**
 * 获取缓存的用户数据
 * @param userId 用户ID
 * @returns 缓存的用户数据，如果不存在或已过期则返回null
 */
export const getCachedUserData = (userId: string): any | null => {
  const cacheKey = `user_${userId}`;
  return userCache.get(cacheKey);
};

/**
 * 清除内容缓存
 * @param id 内容ID，如果不提供则清除所有内容缓存
 */
export const clearContentCache = (id?: string): void => {
  if (id) {
    contentCache.delete(`content_detail_${id}`);
  } else {
    // 清除所有内容缓存
    contentCache.clear();
  }
};

/**
 * 清除设置缓存
 * @param key 设置键，如果不提供则清除所有设置缓存
 */
export const clearSettingsCache = (key?: string): void => {
  if (key) {
    settingsCache.delete(`settings_${key}`);
  } else {
    // 清除所有设置缓存
    settingsCache.clear();
  }
};

/**
 * 清除用户缓存
 * @param userId 用户ID，如果不提供则清除所有用户缓存
 */
export const clearUserCache = (userId?: string): void => {
  if (userId) {
    userCache.delete(`user_${userId}`);
  } else {
    // 清除所有用户缓存
    userCache.clear();
  }
};

/**
 * 获取缓存统计信息
 */
export const getCacheStats = () => {
  return {
    content: contentCache.getStats(),
    settings: settingsCache.getStats(),
    user: userCache.getStats()
  };
};
