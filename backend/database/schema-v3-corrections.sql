-- 数据库结构修正脚本 v3.0
-- 根据项目运行逻辑重新设计数据库结构

-- 1. 修正用户表，添加匿名身份标识
ALTER TABLE users_v2 ADD COLUMN anonymous_identity_id TEXT; -- AB值
ALTER TABLE users_v2 ADD COLUMN identity_a TEXT; -- A值
ALTER TABLE users_v2 ADD COLUMN identity_b TEXT; -- B值
ALTER TABLE users_v2 ADD COLUMN is_anonymous_registered BOOLEAN DEFAULT FALSE;

-- 2. 创建标签系统
CREATE TABLE IF NOT EXISTS tags_v2 (
  id TEXT PRIMARY KEY,                    -- tag_xxx
  name TEXT NOT NULL UNIQUE,             -- 标签名称
  category TEXT,                         -- 标签分类
  description TEXT,                      -- 标签描述
  usage_count INTEGER DEFAULT 0,         -- 使用次数
  is_system_tag BOOLEAN DEFAULT FALSE,   -- 是否系统标签
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 3. 创建内容标签关联表
CREATE TABLE IF NOT EXISTS content_tags_v2 (
  content_id TEXT NOT NULL,              -- 内容ID
  tag_id TEXT NOT NULL,                  -- 标签ID
  content_type TEXT NOT NULL CHECK (content_type IN ('story', 'voice')),
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (content_id, tag_id),
  FOREIGN KEY (tag_id) REFERENCES tags_v2(id) ON DELETE CASCADE
);

-- 4. 创建审核记录表
CREATE TABLE IF NOT EXISTS review_records_v2 (
  id TEXT PRIMARY KEY,                    -- review_xxx
  reviewer_id TEXT NOT NULL,             -- 审核员ID
  content_id TEXT NOT NULL,              -- 内容ID
  content_type TEXT NOT NULL CHECK (content_type IN ('voice', 'story')),
  action TEXT NOT NULL CHECK (action IN ('approved', 'rejected', 'pending')),
  reason TEXT,                           -- 审核理由
  review_time INTEGER DEFAULT 0,         -- 审核耗时(秒)
  ai_pre_review_result TEXT,             -- AI预审结果
  quality_score REAL DEFAULT 0.0,       -- 内容质量评分
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  FOREIGN KEY (reviewer_id) REFERENCES users_v2(id)
);

-- 5. 创建系统配置表
CREATE TABLE IF NOT EXISTS system_config_v2 (
  key TEXT PRIMARY KEY,                  -- 配置键
  value TEXT NOT NULL,                   -- 配置值
  description TEXT,                      -- 配置描述
  category TEXT DEFAULT 'general',      -- 配置分类
  is_public BOOLEAN DEFAULT FALSE,      -- 是否公开
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 6. 修正问卷回复表，添加匿名选择字段
ALTER TABLE questionnaire_responses_v2 ADD COLUMN enable_anonymous_identity BOOLEAN DEFAULT FALSE;
ALTER TABLE questionnaire_responses_v2 ADD COLUMN is_completely_anonymous BOOLEAN DEFAULT TRUE;

-- 7. 修正故事表，添加标签支持
ALTER TABLE story_contents_v2 ADD COLUMN tag_count INTEGER DEFAULT 0;
ALTER TABLE story_contents_v2 ADD COLUMN ai_review_result TEXT;
ALTER TABLE story_contents_v2 ADD COLUMN ai_review_score REAL DEFAULT 0.0;

-- 8. 修正问卷心声表，添加AI审核字段
ALTER TABLE questionnaire_voices_v2 ADD COLUMN ai_review_result TEXT;
ALTER TABLE questionnaire_voices_v2 ADD COLUMN ai_review_score REAL DEFAULT 0.0;
ALTER TABLE questionnaire_voices_v2 ADD COLUMN review_priority INTEGER DEFAULT 0;

-- 9. 创建问卷统计缓存表（优化性能）
CREATE TABLE IF NOT EXISTS questionnaire_stats_cache_v2 (
  id TEXT PRIMARY KEY,
  question_key TEXT NOT NULL,            -- 问题标识
  option_key TEXT NOT NULL,              -- 选项标识
  option_display TEXT NOT NULL,          -- 选项显示名
  count INTEGER DEFAULT 0,               -- 选择次数
  percentage REAL DEFAULT 0.0,           -- 选择比例
  total_responses INTEGER DEFAULT 0,     -- 总回复数
  last_updated TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  UNIQUE(question_key, option_key)
);

-- 10. 插入系统默认配置
INSERT OR IGNORE INTO system_config_v2 (key, value, description, category) VALUES
('ai_review_enabled', 'true', 'AI审核开关', 'review'),
('anti_script_enabled', 'true', '防脚本刷问卷开关', 'security'),
('max_tags_per_story', '5', '每个故事最大标签数', 'content'),
('questionnaire_total_count', '0', '问卷总提交数', 'statistics'),
('review_batch_size', '10', '审核批次大小', 'review'),
('anonymous_identity_expiry_days', '365', '匿名身份有效期(天)', 'user');

-- 11. 插入系统默认标签
INSERT OR IGNORE INTO tags_v2 (id, name, display_name, category, description, is_system) VALUES
('tag_job_search', '求职经验', '求职经验', '经验分享', '求职过程中的经验和技巧', true),
('tag_career_planning', '职业规划', '职业规划', '规划发展', '职业发展规划和思考', true),
('tag_interview', '面试经验', '面试经验', '经验分享', '面试相关的经验分享', true),
('tag_workplace', '职场感悟', '职场感悟', '感悟思考', '职场生活的感悟和思考', true),
('tag_industry_insight', '行业观察', '行业观察', '行业分析', '对特定行业的观察和分析', true),
('tag_skill_development', '技能提升', '技能提升', '学习成长', '技能学习和提升经验', true),
('tag_entrepreneurship', '创业故事', '创业故事', '创业经历', '创业相关的故事和经验', true),
('tag_life_balance', '工作生活平衡', '工作生活平衡', '生活方式', '工作与生活平衡的思考', true);

-- 12. 创建索引优化查询性能
CREATE INDEX IF NOT EXISTS idx_users_v2_anonymous ON users_v2(is_anonymous_registered, anonymous_identity_id);
CREATE INDEX IF NOT EXISTS idx_content_tags_v2_content ON content_tags_v2(content_id, content_type);
CREATE INDEX IF NOT EXISTS idx_content_tags_v2_tag ON content_tags_v2(tag_id);
CREATE INDEX IF NOT EXISTS idx_review_records_v2_reviewer ON review_records_v2(reviewer_id, created_at);
CREATE INDEX IF NOT EXISTS idx_review_records_v2_content ON review_records_v2(content_id, content_type);
CREATE INDEX IF NOT EXISTS idx_questionnaire_stats_cache_v2_question ON questionnaire_stats_cache_v2(question_key);

-- 13. 创建视图简化查询
CREATE VIEW IF NOT EXISTS v_story_with_tags AS
SELECT
  s.*,
  GROUP_CONCAT(t.name, ',') as tag_names,
  GROUP_CONCAT(t.id, ',') as tag_ids
FROM story_contents_v2 s
LEFT JOIN content_tags_v2 ct ON s.id = ct.content_id AND ct.content_type = 'story'
LEFT JOIN tags_v2 t ON ct.tag_id = t.id
GROUP BY s.id;

CREATE VIEW IF NOT EXISTS v_voice_with_tags AS
SELECT
  v.*,
  GROUP_CONCAT(t.name, ',') as tag_names,
  GROUP_CONCAT(t.id, ',') as tag_ids
FROM questionnaire_voices_v2 v
LEFT JOIN content_tags_v2 ct ON v.id = ct.content_id AND ct.content_type = 'voice'
LEFT JOIN tags_v2 t ON ct.tag_id = t.id
GROUP BY v.id;

-- 14. 创建审核员工作量统计视图
CREATE VIEW IF NOT EXISTS v_reviewer_stats AS
SELECT
  r.reviewer_id,
  u.display_name as reviewer_name,
  COUNT(*) as total_reviews,
  COUNT(CASE WHEN r.action = 'approved' THEN 1 END) as approved_count,
  COUNT(CASE WHEN r.action = 'rejected' THEN 1 END) as rejected_count,
  AVG(r.review_time) as avg_review_time,
  AVG(r.quality_score) as avg_quality_score,
  DATE(r.created_at) as review_date
FROM review_records_v2 r
JOIN users_v2 u ON r.reviewer_id = u.id
GROUP BY r.reviewer_id, DATE(r.created_at);

-- 15. 更新统计信息
UPDATE system_config_v2
SET value = (SELECT COUNT(*) FROM questionnaire_responses_v2)
WHERE key = 'questionnaire_total_count';

-- 16. 数据迁移：为现有数据添加默认值
UPDATE users_v2 SET is_anonymous_registered = FALSE WHERE is_anonymous_registered IS NULL;
UPDATE questionnaire_responses_v2 SET enable_anonymous_identity = FALSE WHERE enable_anonymous_identity IS NULL;
UPDATE questionnaire_responses_v2 SET is_completely_anonymous = TRUE WHERE is_completely_anonymous IS NULL;
UPDATE story_contents_v2 SET tag_count = 0 WHERE tag_count IS NULL;
UPDATE questionnaire_voices_v2 SET review_priority = 0 WHERE review_priority IS NULL;
