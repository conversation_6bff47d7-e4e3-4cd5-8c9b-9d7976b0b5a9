import React, { useState, useEffect } from 'react';
import { useToast } from '@/components/ui/use-toast';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { 
  Dialog, 
  DialogContent, 
  DialogDescription, 
  DialogFooter, 
  DialogHeader, 
  DialogTitle,
  DialogTrigger
} from '@/components/ui/dialog';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import {
  Plus,
  Edit,
  Trash2,
  Copy,
  Save,
  FileText,
  CheckCircle,
  XCircle,
  AlertCircle
} from 'lucide-react';
import LoadingSpinner from '@/components/ui/loading-spinner';
import ErrorDisplay from '@/components/ui/error-display';

// 导入数据服务
import { getReviewTemplates, createReviewTemplate, updateReviewTemplate, deleteReviewTemplate } from '@/services/dataService';

// 审核模板类型
enum TemplateType {
  APPROVAL = 'approval',
  REJECTION = 'rejection',
  EDIT = 'edit'
}

// 审核模板接口
interface ReviewTemplate {
  id: string;
  name: string;
  type: TemplateType;
  content: string;
  createdBy: string;
  createdAt: string;
  updatedAt: string;
}

// 审核模板面板组件
export default function ReviewTemplatesPanel() {
  const { toast } = useToast();
  const [templates, setTemplates] = useState<ReviewTemplate[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [isDialogOpen, setIsDialogOpen] = useState(false);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [selectedTemplate, setSelectedTemplate] = useState<ReviewTemplate | null>(null);
  const [templateName, setTemplateName] = useState('');
  const [templateType, setTemplateType] = useState<TemplateType>(TemplateType.APPROVAL);
  const [templateContent, setTemplateContent] = useState('');
  const [filterType, setFilterType] = useState<string>('all');
  
  // 获取审核模板
  const fetchTemplates = async () => {
    try {
      setIsLoading(true);
      setError(null);
      
      const response = await getReviewTemplates();
      
      if (response.success) {
        setTemplates(response.templates);
      } else {
        setError(response.error || '获取审核模板失败');
      }
    } catch (error) {
      console.error('获取审核模板错误:', error);
      setError('服务器错误，请稍后再试');
    } finally {
      setIsLoading(false);
    }
  };
  
  // 组件加载时获取审核模板
  useEffect(() => {
    fetchTemplates();
  }, []);
  
  // 打开创建模板对话框
  const openCreateDialog = () => {
    setSelectedTemplate(null);
    setTemplateName('');
    setTemplateType(TemplateType.APPROVAL);
    setTemplateContent('');
    setIsDialogOpen(true);
  };
  
  // 打开编辑模板对话框
  const openEditDialog = (template: ReviewTemplate) => {
    setSelectedTemplate(template);
    setTemplateName(template.name);
    setTemplateType(template.type);
    setTemplateContent(template.content);
    setIsDialogOpen(true);
  };
  
  // 保存模板
  const handleSaveTemplate = async () => {
    // 验证表单
    if (!templateName.trim()) {
      toast({
        title: '验证失败',
        description: '请输入模板名称',
        variant: 'destructive'
      });
      return;
    }
    
    if (!templateContent.trim()) {
      toast({
        title: '验证失败',
        description: '请输入模板内容',
        variant: 'destructive'
      });
      return;
    }
    
    try {
      setIsSubmitting(true);
      
      const templateData = {
        name: templateName,
        type: templateType,
        content: templateContent
      };
      
      let response;
      
      if (selectedTemplate) {
        // 更新模板
        response = await updateReviewTemplate(selectedTemplate.id, templateData);
      } else {
        // 创建模板
        response = await createReviewTemplate(templateData);
      }
      
      if (response.success) {
        toast({
          title: '保存成功',
          description: selectedTemplate ? '模板已更新' : '模板已创建',
        });
        
        // 关闭对话框
        setIsDialogOpen(false);
        
        // 刷新模板列表
        fetchTemplates();
      } else {
        toast({
          title: '保存失败',
          description: response.error || '保存模板失败',
          variant: 'destructive'
        });
      }
    } catch (error) {
      console.error('保存模板错误:', error);
      toast({
        title: '保存失败',
        description: '服务器错误，请稍后再试',
        variant: 'destructive'
      });
    } finally {
      setIsSubmitting(false);
    }
  };
  
  // 删除模板
  const handleDeleteTemplate = async (id: string) => {
    if (!confirm('确定要删除此模板吗？此操作不可撤销。')) {
      return;
    }
    
    try {
      const response = await deleteReviewTemplate(id);
      
      if (response.success) {
        toast({
          title: '删除成功',
          description: '模板已删除',
        });
        
        // 刷新模板列表
        fetchTemplates();
      } else {
        toast({
          title: '删除失败',
          description: response.error || '删除模板失败',
          variant: 'destructive'
        });
      }
    } catch (error) {
      console.error('删除模板错误:', error);
      toast({
        title: '删除失败',
        description: '服务器错误，请稍后再试',
        variant: 'destructive'
      });
    }
  };
  
  // 复制模板内容
  const handleCopyTemplate = (content: string) => {
    navigator.clipboard.writeText(content)
      .then(() => {
        toast({
          title: '复制成功',
          description: '模板内容已复制到剪贴板',
        });
      })
      .catch(() => {
        toast({
          title: '复制失败',
          description: '无法复制到剪贴板',
          variant: 'destructive'
        });
      });
  };
  
  // 获取模板类型标签
  const getTemplateTypeLabel = (type: TemplateType) => {
    switch (type) {
      case TemplateType.APPROVAL:
        return '通过';
      case TemplateType.REJECTION:
        return '拒绝';
      case TemplateType.EDIT:
        return '编辑';
      default:
        return '未知';
    }
  };
  
  // 获取模板类型图标
  const getTemplateTypeIcon = (type: TemplateType) => {
    switch (type) {
      case TemplateType.APPROVAL:
        return <CheckCircle className="h-4 w-4 text-green-500" />;
      case TemplateType.REJECTION:
        return <XCircle className="h-4 w-4 text-red-500" />;
      case TemplateType.EDIT:
        return <Edit className="h-4 w-4 text-blue-500" />;
      default:
        return <AlertCircle className="h-4 w-4" />;
    }
  };
  
  // 过滤模板
  const filteredTemplates = filterType === 'all' 
    ? templates 
    : templates.filter(template => template.type === filterType);
  
  // 渲染加载状态
  if (isLoading) {
    return (
      <div className="flex justify-center items-center py-12">
        <LoadingSpinner size="lg" text="加载审核模板中..." />
      </div>
    );
  }
  
  // 渲染错误状态
  if (error) {
    return (
      <ErrorDisplay 
        title="加载失败" 
        message={error} 
        onRetry={fetchTemplates} 
      />
    );
  }
  
  return (
    <div className="space-y-4">
      <div className="flex justify-between items-center">
        <h2 className="text-2xl font-bold flex items-center">
          <FileText className="h-6 w-6 mr-2" />
          审核模板
        </h2>
        
        <div className="flex items-center space-x-2">
          <Select
            value={filterType}
            onValueChange={(value) => setFilterType(value)}
          >
            <SelectTrigger className="w-[150px]">
              <SelectValue placeholder="模板类型" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">全部类型</SelectItem>
              <SelectItem value={TemplateType.APPROVAL}>通过模板</SelectItem>
              <SelectItem value={TemplateType.REJECTION}>拒绝模板</SelectItem>
              <SelectItem value={TemplateType.EDIT}>编辑模板</SelectItem>
            </SelectContent>
          </Select>
          
          <Button onClick={openCreateDialog}>
            <Plus className="h-4 w-4 mr-2" />
            新建模板
          </Button>
        </div>
      </div>
      
      {filteredTemplates.length === 0 ? (
        <div className="flex flex-col items-center justify-center p-12 bg-muted/50 rounded-lg">
          <FileText className="h-12 w-12 text-muted-foreground mb-4" />
          <h3 className="text-xl font-medium">暂无审核模板</h3>
          <p className="text-muted-foreground mt-2">点击"新建模板"按钮创建第一个模板</p>
        </div>
      ) : (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
          {filteredTemplates.map((template) => (
            <Card key={template.id} className="overflow-hidden">
              <CardHeader className="pb-2">
                <div className="flex justify-between items-start">
                  <div>
                    <CardTitle className="flex items-center">
                      {getTemplateTypeIcon(template.type)}
                      <span className="ml-2">{template.name}</span>
                    </CardTitle>
                    <CardDescription>
                      {getTemplateTypeLabel(template.type)}模板
                    </CardDescription>
                  </div>
                  <div className="flex space-x-1">
                    <Button 
                      variant="ghost" 
                      size="icon"
                      onClick={() => handleCopyTemplate(template.content)}
                      title="复制内容"
                    >
                      <Copy className="h-4 w-4" />
                    </Button>
                    <Button 
                      variant="ghost" 
                      size="icon"
                      onClick={() => openEditDialog(template)}
                      title="编辑模板"
                    >
                      <Edit className="h-4 w-4" />
                    </Button>
                    <Button 
                      variant="ghost" 
                      size="icon"
                      onClick={() => handleDeleteTemplate(template.id)}
                      title="删除模板"
                    >
                      <Trash2 className="h-4 w-4" />
                    </Button>
                  </div>
                </div>
              </CardHeader>
              <CardContent className="pb-2">
                <div className="bg-muted p-3 rounded-md max-h-32 overflow-y-auto">
                  <p className="text-sm whitespace-pre-wrap">{template.content}</p>
                </div>
              </CardContent>
              <CardFooter className="pt-2 text-xs text-muted-foreground">
                创建于 {new Date(template.createdAt).toLocaleString()}
              </CardFooter>
            </Card>
          ))}
        </div>
      )}
      
      {/* 创建/编辑模板对话框 */}
      <Dialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>
              {selectedTemplate ? '编辑模板' : '创建模板'}
            </DialogTitle>
            <DialogDescription>
              {selectedTemplate ? '修改现有模板' : '创建新的审核模板'}
            </DialogDescription>
          </DialogHeader>
          
          <div className="space-y-4 py-4">
            <div className="grid grid-cols-4 gap-4">
              <div className="col-span-3">
                <label className="block text-sm font-medium mb-1">模板名称</label>
                <Input 
                  value={templateName} 
                  onChange={(e) => setTemplateName(e.target.value)}
                  placeholder="输入模板名称"
                />
              </div>
              <div>
                <label className="block text-sm font-medium mb-1">模板类型</label>
                <Select
                  value={templateType}
                  onValueChange={(value: TemplateType) => setTemplateType(value)}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="选择类型" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value={TemplateType.APPROVAL}>通过</SelectItem>
                    <SelectItem value={TemplateType.REJECTION}>拒绝</SelectItem>
                    <SelectItem value={TemplateType.EDIT}>编辑</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>
            
            <div>
              <label className="block text-sm font-medium mb-1">模板内容</label>
              <Textarea 
                value={templateContent} 
                onChange={(e) => setTemplateContent(e.target.value)}
                placeholder="输入模板内容"
                rows={8}
              />
            </div>
          </div>
          
          <DialogFooter>
            <Button variant="outline" onClick={() => setIsDialogOpen(false)}>
              取消
            </Button>
            <Button 
              onClick={handleSaveTemplate}
              disabled={isSubmitting}
            >
              {isSubmitting ? <LoadingSpinner size="sm" /> : <Save className="h-4 w-4 mr-2" />}
              保存
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
}
