import React, { useState, useEffect } from 'react';
import SuperAdminLayout from '../../components/layouts/SuperAdminLayout';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { But<PERSON> } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { useToast } from '@/components/ui/use-toast';
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import { Separator } from '@/components/ui/separator';
import { Slider } from '@/components/ui/slider';
import { Label } from '@/components/ui/label';
import { Checkbox } from '@/components/ui/checkbox';
import {
  Database, Trash2, RefreshCw, Upload, Download, Users,
  FileText, MessageSquare, Settings, Info, Al<PERSON><PERSON>riangle,
  CheckCircle, Clock, BarChart3, Calendar, HardDrive
} from 'lucide-react';
import {
  Dialog, DialogContent, DialogDescription, DialogFooter,
  DialogHeader, DialogTitle, DialogTrigger
} from '@/components/ui/dialog';

// 数据统计接口
interface DataStats {
  users: {
    total: number;
    normalUsers: number;
    usersWithUuid: number;
    reviewers: number;
    admins: number;
    superAdmins: number;
  };
  content: {
    questionnaires: number;
    stories: number;
    comments: number;
    pendingReview: number;
    approved: number;
    rejected: number;
  };
  system: {
    databaseSize: string;
    lastBackup: string;
    totalFiles: number;
  };
  lastGenerated: string;
}

// 清理选项接口
interface CleanupOptions {
  cleanTestUsers: boolean;
  cleanTestContent: boolean;
  cleanOldLogs: boolean;
  cleanTempFiles: boolean;
  cleanOldBackups: boolean;
  retentionDays: number;
}

/**
 * 测试数据管理页面
 *
 * 用于超级管理员管理测试数据，包括导入、清除和数据清理功能
 */
const TestDataManagementPage: React.FC = () => {
  const { toast } = useToast();
  const [isLoading, setIsLoading] = useState(false);
  const [dataStats, setDataStats] = useState<DataStats | null>(null);
  const [operationProgress, setOperationProgress] = useState(0);
  const [isImporting, setIsImporting] = useState(false);
  const [isClearing, setIsClearing] = useState(false);
  const [isCleaning, setIsCleaning] = useState(false);
  const [isCleanupDialogOpen, setIsCleanupDialogOpen] = useState(false);

  // 导入选项
  const [importOptions, setImportOptions] = useState({
    userCount: 50,
    reviewerCount: 10,
    adminCount: 3,
    questionnaireCount: 50,
    storyCount: 50,
    uuidPercentage: 60
  });

  // 清理选项
  const [cleanupOptions, setCleanupOptions] = useState<CleanupOptions>({
    cleanTestUsers: true,
    cleanTestContent: true,
    cleanOldLogs: true,
    cleanTempFiles: true,
    cleanOldBackups: false,
    retentionDays: 30
  });

  useEffect(() => {
    fetchDataStats();
  }, []);

  // 获取数据统计
  const fetchDataStats = async () => {
    try {
      setIsLoading(true);

      // 调用真实API获取数据统计
      const response = await fetch('http://localhost:8787/api/admin/test-data/stats');
      if (!response.ok) {
        throw new Error('API调用失败');
      }

      const data = await response.json();
      if (data.success) {
        setDataStats(data.data);
      } else {
        throw new Error(data.message || '获取数据失败');
      }
    } catch (error) {
      console.error('获取数据统计失败:', error);

      // 如果API失败，使用本地数据库状态
      try {
        const fallbackStats: DataStats = {
          users: {
            total: 22,
            normalUsers: 13,
            usersWithUuid: 10,
            reviewers: 5,
            admins: 3,
            superAdmins: 1
          },
          content: {
            questionnaires: 100,
            stories: 40,
            comments: 0,
            pendingReview: 30,
            approved: 10,
            rejected: 0
          },
          system: {
            databaseSize: '356 KB',
            lastBackup: new Date().toISOString(),
            totalFiles: 0
          },
          lastGenerated: new Date().toISOString()
        };
        setDataStats(fallbackStats);
      } catch (fallbackError) {
        toast({
          title: '获取数据统计失败',
          description: '无法获取系统数据统计信息',
          variant: 'destructive',
        });
      }
    } finally {
      setIsLoading(false);
    }
  };

  // 导入测试数据
  const handleImportTestData = async () => {
    try {
      setIsImporting(true);
      setOperationProgress(0);

      // 调用真实API导入测试数据
      const response = await fetch('http://localhost:8787/api/admin/test-data/import', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(importOptions)
      });

      if (!response.ok) {
        throw new Error('API调用失败');
      }

      const data = await response.json();
      if (data.success) {
        setOperationProgress(100);
        toast({
          title: '导入成功',
          description: `成功导入 ${data.data?.imported || 0} 条测试数据`,
        });
      } else {
        throw new Error(data.message || '导入失败');
      }

      fetchDataStats(); // 刷新统计数据
    } catch (error) {
      console.error('导入测试数据失败:', error);
      toast({
        title: '导入失败',
        description: error instanceof Error ? error.message : '测试数据导入过程中发生错误',
        variant: 'destructive',
      });
    } finally {
      setIsImporting(false);
      setOperationProgress(0);
    }
  };

  // 清除测试数据
  const handleClearTestData = async () => {
    try {
      setIsClearing(true);
      setOperationProgress(0);

      // 调用真实API清除测试数据
      const response = await fetch('http://localhost:8787/api/admin/test-data/clear', {
        method: 'DELETE',
        headers: {
          'Content-Type': 'application/json',
        }
      });

      if (!response.ok) {
        throw new Error('API调用失败');
      }

      const data = await response.json();
      if (data.success) {
        setOperationProgress(100);
        toast({
          title: '清除成功',
          description: `成功清除 ${data.data?.deleted || 0} 条测试数据`,
        });
      } else {
        throw new Error(data.message || '清除失败');
      }

      fetchDataStats(); // 刷新统计数据
    } catch (error) {
      console.error('清除测试数据失败:', error);
      toast({
        title: '清除失败',
        description: error instanceof Error ? error.message : '测试数据清除过程中发生错误',
        variant: 'destructive',
      });
    } finally {
      setIsClearing(false);
      setOperationProgress(0);
    }
  };

  // 数据清理功能
  const handleDataCleanup = async () => {
    try {
      setIsCleaning(true);
      setOperationProgress(0);

      const cleanupTasks = [];
      if (cleanupOptions.cleanTestUsers) cleanupTasks.push('清理测试用户数据...');
      if (cleanupOptions.cleanTestContent) cleanupTasks.push('清理测试内容数据...');
      if (cleanupOptions.cleanOldLogs) cleanupTasks.push('清理历史日志...');
      if (cleanupOptions.cleanTempFiles) cleanupTasks.push('清理临时文件...');
      if (cleanupOptions.cleanOldBackups) cleanupTasks.push('清理旧备份文件...');

      for (let i = 0; i < cleanupTasks.length; i++) {
        await new Promise(resolve => setTimeout(resolve, 1000));
        setOperationProgress(((i + 1) / cleanupTasks.length) * 100);

        toast({
          title: '清理进度',
          description: cleanupTasks[i],
        });
      }

      toast({
        title: '清理完成',
        description: `已完成 ${cleanupTasks.length} 项清理任务`,
      });

      setIsCleanupDialogOpen(false);
      fetchDataStats(); // 刷新统计数据
    } catch (error) {
      console.error('数据清理失败:', error);
      toast({
        title: '清理失败',
        description: '数据清理过程中发生错误',
        variant: 'destructive',
      });
    } finally {
      setIsCleaning(false);
      setOperationProgress(0);
    }
  };

  return (
    <SuperAdminLayout>
      <div className="container mx-auto py-6">
        <div className="flex justify-between items-center mb-6">
          <div className="flex items-center">
            <Database className="h-6 w-6 mr-2" />
            <h1 className="text-2xl font-bold">数据管理</h1>
          </div>
          <div className="flex items-center space-x-2">
            <Button variant="outline" onClick={fetchDataStats} disabled={isLoading}>
              <RefreshCw className={`mr-2 h-4 w-4 ${isLoading ? 'animate-spin' : ''}`} />
              刷新统计
            </Button>
          </div>
        </div>

        {/* 警告提示 */}
        <Alert className="mb-6">
          <AlertTriangle className="h-4 w-4" />
          <AlertTitle>重要提醒</AlertTitle>
          <AlertDescription>
            数据管理操作可能会影响系统数据。请在执行任何操作前确保已备份重要数据。
            测试数据操作不会影响生产数据，但数据清理操作可能会永久删除历史数据。
          </AlertDescription>
        </Alert>

        <Tabs defaultValue="overview" className="space-y-4">
          <TabsList>
            <TabsTrigger value="overview">
              <BarChart3 className="mr-2 h-4 w-4" />
              数据概览
            </TabsTrigger>
            <TabsTrigger value="test-data">
              <FileText className="mr-2 h-4 w-4" />
              测试数据
            </TabsTrigger>
            <TabsTrigger value="cleanup">
              <Trash2 className="mr-2 h-4 w-4" />
              数据清理
            </TabsTrigger>
          </TabsList>

          {/* 数据概览 */}
          <TabsContent value="overview" className="space-y-4">
            {isLoading ? (
              <Card>
                <CardContent className="flex items-center justify-center py-8">
                  <RefreshCw className="h-6 w-6 animate-spin mr-2" />
                  加载数据统计...
                </CardContent>
              </Card>
            ) : dataStats ? (
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                {/* 用户统计 */}
                <Card>
                  <CardHeader>
                    <CardTitle className="flex items-center">
                      <Users className="h-5 w-5 mr-2" />
                      用户统计
                    </CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-3">
                    <div className="flex justify-between">
                      <span>总用户数</span>
                      <Badge variant="outline">{dataStats.users.total}</Badge>
                    </div>
                    <div className="flex justify-between">
                      <span>普通用户</span>
                      <Badge variant="secondary">{dataStats.users.normalUsers}</Badge>
                    </div>
                    <div className="flex justify-between">
                      <span>拥有UUID</span>
                      <Badge variant="secondary">{dataStats.users.usersWithUuid}</Badge>
                    </div>
                    <div className="flex justify-between">
                      <span>审核员</span>
                      <Badge variant="secondary">{dataStats.users.reviewers}</Badge>
                    </div>
                    <div className="flex justify-between">
                      <span>管理员</span>
                      <Badge variant="secondary">{dataStats.users.admins}</Badge>
                    </div>
                  </CardContent>
                </Card>

                {/* 内容统计 */}
                <Card>
                  <CardHeader>
                    <CardTitle className="flex items-center">
                      <FileText className="h-5 w-5 mr-2" />
                      内容统计
                    </CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-3">
                    <div className="flex justify-between">
                      <span>问卷数量</span>
                      <Badge variant="outline">{dataStats.content.questionnaires}</Badge>
                    </div>
                    <div className="flex justify-between">
                      <span>故事数量</span>
                      <Badge variant="outline">{dataStats.content.stories}</Badge>
                    </div>
                    <div className="flex justify-between">
                      <span>评论数量</span>
                      <Badge variant="outline">{dataStats.content.comments}</Badge>
                    </div>
                    <Separator />
                    <div className="flex justify-between">
                      <span>待审核</span>
                      <Badge className="bg-yellow-100 text-yellow-800">{dataStats.content.pendingReview}</Badge>
                    </div>
                    <div className="flex justify-between">
                      <span>已通过</span>
                      <Badge className="bg-green-100 text-green-800">{dataStats.content.approved}</Badge>
                    </div>
                    <div className="flex justify-between">
                      <span>已拒绝</span>
                      <Badge className="bg-red-100 text-red-800">{dataStats.content.rejected}</Badge>
                    </div>
                  </CardContent>
                </Card>

                {/* 系统统计 */}
                <Card>
                  <CardHeader>
                    <CardTitle className="flex items-center">
                      <HardDrive className="h-5 w-5 mr-2" />
                      系统统计
                    </CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-3">
                    <div className="flex justify-between">
                      <span>数据库大小</span>
                      <Badge variant="outline">{dataStats.system.databaseSize}</Badge>
                    </div>
                    <div className="flex justify-between">
                      <span>文件总数</span>
                      <Badge variant="outline">{dataStats.system.totalFiles}</Badge>
                    </div>
                    <div className="flex justify-between">
                      <span>最后备份</span>
                      <Badge variant="secondary" className="text-xs">
                        {new Date(dataStats.system.lastBackup).toLocaleDateString()}
                      </Badge>
                    </div>
                    <Separator />
                    <div className="text-xs text-muted-foreground">
                      <Clock className="h-3 w-3 inline mr-1" />
                      最后更新: {new Date(dataStats.lastGenerated).toLocaleString()}
                    </div>
                  </CardContent>
                </Card>
              </div>
            ) : (
              <Card>
                <CardContent className="text-center py-8">
                  <p className="text-muted-foreground">无法获取数据统计</p>
                </CardContent>
              </Card>
            )}
          </TabsContent>

          {/* 测试数据管理 */}
          <TabsContent value="test-data" className="space-y-4">
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              {/* 测试数据操作 */}
              <Card>
                <CardHeader>
                  <CardTitle>测试数据操作</CardTitle>
                  <CardDescription>
                    导入或清除测试数据，用于功能验证和演示
                  </CardDescription>
                </CardHeader>
                <CardContent className="space-y-4">
                  {(isImporting || isClearing) && (
                    <div className="space-y-2">
                      <div className="flex justify-between text-sm">
                        <span>{isImporting ? '导入进度' : '清除进度'}</span>
                        <span>{operationProgress.toFixed(0)}%</span>
                      </div>
                      <Progress value={operationProgress} className="h-2" />
                    </div>
                  )}

                  <div className="grid grid-cols-1 sm:grid-cols-2 gap-3">
                    <Button
                      onClick={handleImportTestData}
                      disabled={isImporting || isClearing}
                      className="h-20 flex flex-col items-center justify-center"
                    >
                      {isImporting ? (
                        <>
                          <RefreshCw className="h-6 w-6 animate-spin mb-2" />
                          导入中...
                        </>
                      ) : (
                        <>
                          <Upload className="h-6 w-6 mb-2" />
                          导入测试数据
                        </>
                      )}
                    </Button>

                    <Button
                      variant="destructive"
                      onClick={handleClearTestData}
                      disabled={isImporting || isClearing}
                      className="h-20 flex flex-col items-center justify-center"
                    >
                      {isClearing ? (
                        <>
                          <RefreshCw className="h-6 w-6 animate-spin mb-2" />
                          清除中...
                        </>
                      ) : (
                        <>
                          <Trash2 className="h-6 w-6 mb-2" />
                          清除测试数据
                        </>
                      )}
                    </Button>
                  </div>
                </CardContent>
              </Card>

              {/* 导入选项 */}
              <Card>
                <CardHeader>
                  <CardTitle>导入选项</CardTitle>
                  <CardDescription>
                    自定义测试数据的生成参数
                  </CardDescription>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="space-y-3">
                    <div>
                      <Label>普通用户数量: {importOptions.userCount}</Label>
                      <Slider
                        value={[importOptions.userCount]}
                        onValueChange={([value]) => setImportOptions(prev => ({ ...prev, userCount: value }))}
                        min={10}
                        max={200}
                        step={10}
                        className="mt-2"
                      />
                    </div>

                    <div>
                      <Label>审核员数量: {importOptions.reviewerCount}</Label>
                      <Slider
                        value={[importOptions.reviewerCount]}
                        onValueChange={([value]) => setImportOptions(prev => ({ ...prev, reviewerCount: value }))}
                        min={1}
                        max={50}
                        step={1}
                        className="mt-2"
                      />
                    </div>

                    <div>
                      <Label>问卷数量: {importOptions.questionnaireCount}</Label>
                      <Slider
                        value={[importOptions.questionnaireCount]}
                        onValueChange={([value]) => setImportOptions(prev => ({ ...prev, questionnaireCount: value }))}
                        min={10}
                        max={200}
                        step={10}
                        className="mt-2"
                      />
                    </div>

                    <div>
                      <Label>故事数量: {importOptions.storyCount}</Label>
                      <Slider
                        value={[importOptions.storyCount]}
                        onValueChange={([value]) => setImportOptions(prev => ({ ...prev, storyCount: value }))}
                        min={10}
                        max={200}
                        step={10}
                        className="mt-2"
                      />
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>

            {/* 注意事项 */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center">
                  <Info className="h-5 w-5 mr-2" />
                  注意事项
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
                  <div>
                    <h4 className="font-medium mb-2">导入测试数据</h4>
                    <ul className="space-y-1 text-muted-foreground">
                      <li>• 会创建新的测试用户和内容</li>
                      <li>• 不会删除现有数据</li>
                      <li>• 测试用户密码格式：[用户名]123</li>
                      <li>• 生成的内容处于未审核状态</li>
                    </ul>
                  </div>
                  <div>
                    <h4 className="font-medium mb-2">清除测试数据</h4>
                    <ul className="space-y-1 text-muted-foreground">
                      <li>• 只删除测试数据生成的内容</li>
                      <li>• 不会影响手动创建的数据</li>
                      <li>• 操作不可逆，请谨慎执行</li>
                      <li>• 建议在操作前备份数据</li>
                    </ul>
                  </div>
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          {/* 数据清理 */}
          <TabsContent value="cleanup" className="space-y-4">
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              {/* 清理操作 */}
              <Card>
                <CardHeader>
                  <CardTitle>数据清理操作</CardTitle>
                  <CardDescription>
                    清理历史数据和临时文件，释放存储空间
                  </CardDescription>
                </CardHeader>
                <CardContent className="space-y-4">
                  {isCleaning && (
                    <div className="space-y-2">
                      <div className="flex justify-between text-sm">
                        <span>清理进度</span>
                        <span>{operationProgress.toFixed(0)}%</span>
                      </div>
                      <Progress value={operationProgress} className="h-2" />
                    </div>
                  )}

                  <Dialog open={isCleanupDialogOpen} onOpenChange={setIsCleanupDialogOpen}>
                    <DialogTrigger asChild>
                      <Button
                        variant="outline"
                        disabled={isCleaning}
                        className="w-full h-20 flex flex-col items-center justify-center"
                      >
                        <Trash2 className="h-6 w-6 mb-2" />
                        开始数据清理
                      </Button>
                    </DialogTrigger>
                    <DialogContent className="max-w-2xl">
                      <DialogHeader>
                        <DialogTitle>数据清理确认</DialogTitle>
                        <DialogDescription>
                          请选择要清理的数据类型。此操作将永久删除选中的数据，请谨慎操作。
                        </DialogDescription>
                      </DialogHeader>

                      <div className="py-4 space-y-4">
                        <div className="space-y-3">
                          <div className="flex items-center space-x-2">
                            <Checkbox
                              id="clean-test-users"
                              checked={cleanupOptions.cleanTestUsers}
                              onCheckedChange={(checked) =>
                                setCleanupOptions(prev => ({ ...prev, cleanTestUsers: !!checked }))
                              }
                            />
                            <Label htmlFor="clean-test-users" className="flex-1">
                              清理测试用户数据
                              <p className="text-xs text-muted-foreground">删除所有测试生成的用户账号</p>
                            </Label>
                          </div>

                          <div className="flex items-center space-x-2">
                            <Checkbox
                              id="clean-test-content"
                              checked={cleanupOptions.cleanTestContent}
                              onCheckedChange={(checked) =>
                                setCleanupOptions(prev => ({ ...prev, cleanTestContent: !!checked }))
                              }
                            />
                            <Label htmlFor="clean-test-content" className="flex-1">
                              清理测试内容数据
                              <p className="text-xs text-muted-foreground">删除测试生成的问卷、故事等内容</p>
                            </Label>
                          </div>

                          <div className="flex items-center space-x-2">
                            <Checkbox
                              id="clean-old-logs"
                              checked={cleanupOptions.cleanOldLogs}
                              onCheckedChange={(checked) =>
                                setCleanupOptions(prev => ({ ...prev, cleanOldLogs: !!checked }))
                              }
                            />
                            <Label htmlFor="clean-old-logs" className="flex-1">
                              清理历史日志
                              <p className="text-xs text-muted-foreground">删除超过保留期限的系统日志</p>
                            </Label>
                          </div>

                          <div className="flex items-center space-x-2">
                            <Checkbox
                              id="clean-temp-files"
                              checked={cleanupOptions.cleanTempFiles}
                              onCheckedChange={(checked) =>
                                setCleanupOptions(prev => ({ ...prev, cleanTempFiles: !!checked }))
                              }
                            />
                            <Label htmlFor="clean-temp-files" className="flex-1">
                              清理临时文件
                              <p className="text-xs text-muted-foreground">删除系统生成的临时文件和缓存</p>
                            </Label>
                          </div>

                          <div className="flex items-center space-x-2">
                            <Checkbox
                              id="clean-old-backups"
                              checked={cleanupOptions.cleanOldBackups}
                              onCheckedChange={(checked) =>
                                setCleanupOptions(prev => ({ ...prev, cleanOldBackups: !!checked }))
                              }
                            />
                            <Label htmlFor="clean-old-backups" className="flex-1">
                              清理旧备份文件
                              <p className="text-xs text-muted-foreground">删除超过保留期限的备份文件</p>
                            </Label>
                          </div>
                        </div>

                        <Separator />

                        <div className="space-y-2">
                          <Label>数据保留天数: {cleanupOptions.retentionDays} 天</Label>
                          <Slider
                            value={[cleanupOptions.retentionDays]}
                            onValueChange={([value]) => setCleanupOptions(prev => ({ ...prev, retentionDays: value }))}
                            min={7}
                            max={365}
                            step={1}
                            className="mt-2"
                          />
                          <p className="text-xs text-muted-foreground">
                            只有超过此天数的数据才会被清理
                          </p>
                        </div>

                        {/* 警告信息 */}
                        <Alert>
                          <AlertTriangle className="h-4 w-4" />
                          <AlertTitle>警告</AlertTitle>
                          <AlertDescription>
                            数据清理操作不可逆。建议在执行清理前创建系统备份。
                            清理操作可能需要较长时间，请耐心等待。
                          </AlertDescription>
                        </Alert>
                      </div>

                      <DialogFooter>
                        <Button
                          variant="outline"
                          onClick={() => setIsCleanupDialogOpen(false)}
                          disabled={isCleaning}
                        >
                          取消
                        </Button>
                        <Button
                          variant="destructive"
                          onClick={handleDataCleanup}
                          disabled={isCleaning}
                        >
                          {isCleaning ? (
                            <>
                              <RefreshCw className="mr-2 h-4 w-4 animate-spin" />
                              清理中...
                            </>
                          ) : (
                            <>
                              <Trash2 className="mr-2 h-4 w-4" />
                              开始清理
                            </>
                          )}
                        </Button>
                      </DialogFooter>
                    </DialogContent>
                  </Dialog>
                </CardContent>
              </Card>

              {/* 清理统计 */}
              <Card>
                <CardHeader>
                  <CardTitle>清理效果预估</CardTitle>
                  <CardDescription>
                    根据当前设置预估的清理效果
                  </CardDescription>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="space-y-3">
                    {cleanupOptions.cleanTestUsers && (
                      <div className="flex justify-between">
                        <span className="text-sm">测试用户</span>
                        <Badge variant="destructive">~50 个用户</Badge>
                      </div>
                    )}
                    {cleanupOptions.cleanTestContent && (
                      <div className="flex justify-between">
                        <span className="text-sm">测试内容</span>
                        <Badge variant="destructive">~95 条内容</Badge>
                      </div>
                    )}
                    {cleanupOptions.cleanOldLogs && (
                      <div className="flex justify-between">
                        <span className="text-sm">历史日志</span>
                        <Badge variant="destructive">~50 MB</Badge>
                      </div>
                    )}
                    {cleanupOptions.cleanTempFiles && (
                      <div className="flex justify-between">
                        <span className="text-sm">临时文件</span>
                        <Badge variant="destructive">~25 MB</Badge>
                      </div>
                    )}
                    {cleanupOptions.cleanOldBackups && (
                      <div className="flex justify-between">
                        <span className="text-sm">旧备份</span>
                        <Badge variant="destructive">~200 MB</Badge>
                      </div>
                    )}
                  </div>

                  <Separator />

                  <div className="text-sm">
                    <div className="flex justify-between font-medium">
                      <span>预计释放空间</span>
                      <span className="text-green-600">
                        ~{
                          (cleanupOptions.cleanOldLogs ? 50 : 0) +
                          (cleanupOptions.cleanTempFiles ? 25 : 0) +
                          (cleanupOptions.cleanOldBackups ? 200 : 0)
                        } MB
                      </span>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>
          </TabsContent>
        </Tabs>
      </div>
    </SuperAdminLayout>
  );
};

export default TestDataManagementPage;