import React, { useState } from 'react';
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Label } from '@/components/ui/label';
import { Input } from '@/components/ui/input';
import { Checkbox } from '@/components/ui/checkbox';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Calendar } from '@/components/ui/calendar';
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';
import { cn } from '@/lib/utils';
import { format } from 'date-fns';
import { CalendarIcon, Download, FileText, Braces, Code, Table, FileType, RefreshCw } from 'lucide-react';
import { useToast } from '@/components/ui/use-toast';
import {
  exportSecurityLogs,
  getExportFormats,
  getLogLevels,
  getLogCategories,
  ExportFormat,
  LogLevel,
  LogCategory,
  ExportParams
} from '@/services/securityLogExportService';

interface SecurityLogExportDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
}

const SecurityLogExportDialog: React.FC<SecurityLogExportDialogProps> = ({
  open,
  onOpenChange
}) => {
  const { toast } = useToast();
  const [isExporting, setIsExporting] = useState(false);
  const [exportFormat, setExportFormat] = useState<ExportFormat>('csv');
  const [logLevel, setLogLevel] = useState<LogLevel>('all');
  const [logCategory, setLogCategory] = useState<LogCategory>('all');
  const [startDate, setStartDate] = useState<Date | undefined>(undefined);
  const [endDate, setEndDate] = useState<Date | undefined>(undefined);
  const [search, setSearch] = useState('');
  const [includeDetails, setIncludeDetails] = useState(true);
  const [includeContext, setIncludeContext] = useState(false);
  const [maxRows, setMaxRows] = useState(1000);

  // 导出格式列表
  const exportFormats = getExportFormats();
  
  // 日志级别列表
  const logLevels = getLogLevels();
  
  // 日志类别列表
  const logCategories = getLogCategories();

  // 处理导出
  const handleExport = async () => {
    try {
      setIsExporting(true);

      // 构建导出参数
      const params: ExportParams = {
        format: exportFormat,
        level: logLevel,
        category: logCategory,
        startDate: startDate ? format(startDate, 'yyyy-MM-dd') : undefined,
        endDate: endDate ? format(endDate, 'yyyy-MM-dd') : undefined,
        search: search || undefined,
        includeDetails,
        includeContext,
        maxRows
      };

      // 调用导出服务
      const result = await exportSecurityLogs(params);

      if (result.success && result.data) {
        toast({
          title: '导出成功',
          description: `日志已成功导出为 ${result.data.fileName}（${result.data.fileSize}）`
        });

        // 如果有下载链接，则打开下载链接
        if (result.data.downloadUrl !== '#') {
          window.open(result.data.downloadUrl, '_blank');
        } else {
          toast({
            title: '开发环境提示',
            description: `${exportFormat.toUpperCase()} 格式在开发环境中不提供实际下载`,
            variant: 'default'
          });
        }

        // 关闭对话框
        onOpenChange(false);
      } else {
        toast({
          variant: 'destructive',
          title: '导出失败',
          description: result.message || '无法导出日志，请稍后再试'
        });
      }
    } catch (error) {
      console.error('导出日志失败:', error);
      toast({
        variant: 'destructive',
        title: '导出失败',
        description: '无法导出日志，请稍后再试'
      });
    } finally {
      setIsExporting(false);
    }
  };

  // 获取格式图标
  const getFormatIcon = (format: ExportFormat) => {
    switch (format) {
      case 'csv':
        return <FileText className="h-4 w-4" />;
      case 'json':
        return <Braces className="h-4 w-4" />;
      case 'xml':
        return <Code className="h-4 w-4" />;
      case 'excel':
        return <Table className="h-4 w-4" />;
      case 'pdf':
        return <FileType className="h-4 w-4" />;
      default:
        return <FileText className="h-4 w-4" />;
    }
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-2xl">
        <DialogHeader>
          <DialogTitle>导出安全日志</DialogTitle>
          <DialogDescription>
            选择导出格式和筛选条件，导出安全日志数据
          </DialogDescription>
        </DialogHeader>

        <Tabs defaultValue="format" className="w-full">
          <TabsList className="grid grid-cols-3 mb-4">
            <TabsTrigger value="format">导出格式</TabsTrigger>
            <TabsTrigger value="filter">筛选条件</TabsTrigger>
            <TabsTrigger value="options">高级选项</TabsTrigger>
          </TabsList>

          <TabsContent value="format" className="space-y-4">
            <div className="space-y-2">
              <Label>选择导出格式</Label>
              <div className="grid grid-cols-5 gap-2">
                {exportFormats.map((format) => (
                  <Button
                    key={format.value}
                    variant={exportFormat === format.value ? 'default' : 'outline'}
                    className="flex flex-col items-center justify-center h-24 p-2"
                    onClick={() => setExportFormat(format.value)}
                  >
                    <div className="mb-2">
                      {React.createElement(format.icon === 'FileText' ? FileText :
                        format.icon === 'Braces' ? Braces :
                        format.icon === 'Code' ? Code :
                        format.icon === 'Table' ? Table :
                        FileType, { className: "h-8 w-8" })}
                    </div>
                    <span>{format.label}</span>
                  </Button>
                ))}
              </div>
              <p className="text-xs text-muted-foreground mt-2">
                {exportFormat === 'csv' && 'CSV 格式适合在电子表格软件中查看和处理数据'}
                {exportFormat === 'json' && 'JSON 格式适合开发人员和系统集成'}
                {exportFormat === 'xml' && 'XML 格式适合系统集成和数据交换'}
                {exportFormat === 'excel' && 'Excel 格式适合高级数据分析和报表生成'}
                {exportFormat === 'pdf' && 'PDF 格式适合正式报告和存档'}
              </p>
            </div>
          </TabsContent>

          <TabsContent value="filter" className="space-y-4">
            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="log-level">日志级别</Label>
                <Select value={logLevel} onValueChange={(value) => setLogLevel(value as LogLevel)}>
                  <SelectTrigger id="log-level">
                    <SelectValue placeholder="选择日志级别" />
                  </SelectTrigger>
                  <SelectContent>
                    {logLevels.map((level) => (
                      <SelectItem key={level.value} value={level.value}>
                        <div className="flex items-center">
                          <div className={`w-2 h-2 rounded-full bg-${level.color}-500 mr-2`}></div>
                          {level.label}
                        </div>
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              <div className="space-y-2">
                <Label htmlFor="log-category">日志类别</Label>
                <Select value={logCategory} onValueChange={(value) => setLogCategory(value as LogCategory)}>
                  <SelectTrigger id="log-category">
                    <SelectValue placeholder="选择日志类别" />
                  </SelectTrigger>
                  <SelectContent>
                    {logCategories.map((category) => (
                      <SelectItem key={category.value} value={category.value}>
                        {category.label}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
            </div>

            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="start-date">开始日期</Label>
                <Popover>
                  <PopoverTrigger asChild>
                    <Button
                      id="start-date"
                      variant="outline"
                      className={cn(
                        "w-full justify-start text-left font-normal",
                        !startDate && "text-muted-foreground"
                      )}
                    >
                      <CalendarIcon className="mr-2 h-4 w-4" />
                      {startDate ? format(startDate, 'yyyy-MM-dd') : '选择开始日期'}
                    </Button>
                  </PopoverTrigger>
                  <PopoverContent className="w-auto p-0">
                    <Calendar
                      mode="single"
                      selected={startDate}
                      onSelect={setStartDate}
                      initialFocus
                    />
                  </PopoverContent>
                </Popover>
              </div>

              <div className="space-y-2">
                <Label htmlFor="end-date">结束日期</Label>
                <Popover>
                  <PopoverTrigger asChild>
                    <Button
                      id="end-date"
                      variant="outline"
                      className={cn(
                        "w-full justify-start text-left font-normal",
                        !endDate && "text-muted-foreground"
                      )}
                    >
                      <CalendarIcon className="mr-2 h-4 w-4" />
                      {endDate ? format(endDate, 'yyyy-MM-dd') : '选择结束日期'}
                    </Button>
                  </PopoverTrigger>
                  <PopoverContent className="w-auto p-0">
                    <Calendar
                      mode="single"
                      selected={endDate}
                      onSelect={setEndDate}
                      initialFocus
                    />
                  </PopoverContent>
                </Popover>
              </div>
            </div>

            <div className="space-y-2">
              <Label htmlFor="search">搜索关键词</Label>
              <Input
                id="search"
                placeholder="输入搜索关键词"
                value={search}
                onChange={(e) => setSearch(e.target.value)}
              />
              <p className="text-xs text-muted-foreground">
                搜索日志消息、用户名和IP地址等字段
              </p>
            </div>
          </TabsContent>

          <TabsContent value="options" className="space-y-4">
            <div className="space-y-2">
              <div className="flex items-center space-x-2">
                <Checkbox
                  id="include-details"
                  checked={includeDetails}
                  onCheckedChange={(checked) => setIncludeDetails(!!checked)}
                />
                <label
                  htmlFor="include-details"
                  className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
                >
                  包含详细信息
                </label>
              </div>
              <p className="text-xs text-muted-foreground ml-6">
                包含每个日志条目的详细信息，如浏览器、操作系统等
              </p>
            </div>

            <div className="space-y-2">
              <div className="flex items-center space-x-2">
                <Checkbox
                  id="include-context"
                  checked={includeContext}
                  onCheckedChange={(checked) => setIncludeContext(!!checked)}
                />
                <label
                  htmlFor="include-context"
                  className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
                >
                  包含上下文信息
                </label>
              </div>
              <p className="text-xs text-muted-foreground ml-6">
                包含每个日志条目的上下文信息，如请求ID、用户代理等
              </p>
            </div>

            <div className="space-y-2">
              <Label htmlFor="max-rows">最大行数</Label>
              <Input
                id="max-rows"
                type="number"
                min="1"
                max="10000"
                value={maxRows}
                onChange={(e) => setMaxRows(parseInt(e.target.value) || 1000)}
              />
              <p className="text-xs text-muted-foreground">
                导出的最大日志条目数，设置过大可能导致导出时间较长
              </p>
            </div>
          </TabsContent>
        </Tabs>

        <DialogFooter>
          <Button variant="outline" onClick={() => onOpenChange(false)}>
            取消
          </Button>
          <Button onClick={handleExport} disabled={isExporting}>
            {isExporting ? (
              <>
                <RefreshCw className="mr-2 h-4 w-4 animate-spin" />
                导出中...
              </>
            ) : (
              <>
                <Download className="mr-2 h-4 w-4" />
                导出 {getFormatIcon(exportFormat)}
              </>
            )}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};

export default SecurityLogExportDialog;
