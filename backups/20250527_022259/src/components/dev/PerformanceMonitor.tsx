import React, { useState, useEffect } from 'react';
import { 
  <PERSON>, 
  Button, 
  Drawer, 
  Tabs, 
  Table, 
  Typography, 
  Tag, 
  Progress, 
  Space, 
  Alert, 
  Tooltip,
  Collapse
} from 'antd';
import { 
  DashboardOutlined, 
  BarChartOutlined, 
  ClockCircleOutlined, 
  ApiOutlined, 
  WarningOutlined,
  CheckCircleOutlined,
  ReloadOutlined,
  SettingOutlined
} from '@ant-design/icons';
import { 
  getPerformanceMetrics, 
  clearPerformanceMetrics, 
  generatePerformanceReport,
  getPerformanceOptimizationTips,
  PerformanceMetrics
} from '../../utils/performance';

const { TabPane } = Tabs;
const { Title, Text, Paragraph } = Typography;
const { Panel } = Collapse;

// 性能指标评级
type MetricRating = 'good' | 'needs-improvement' | 'poor';

// 性能监控组件
const PerformanceMonitor: React.FC = () => {
  const [visible, setVisible] = useState<boolean>(false);
  const [metrics, setMetrics] = useState<PerformanceMetrics>({});
  const [tips, setTips] = useState<string[]>([]);
  const [activeTab, setActiveTab] = useState<string>('overview');

  // 定期更新性能指标
  useEffect(() => {
    if (visible) {
      const intervalId = setInterval(() => {
        setMetrics(getPerformanceMetrics());
        setTips(getPerformanceOptimizationTips());
      }, 2000);
      
      return () => clearInterval(intervalId);
    }
  }, [visible]);

  // 手动刷新性能指标
  const refreshMetrics = () => {
    setMetrics(getPerformanceMetrics());
    setTips(getPerformanceOptimizationTips());
  };

  // 清除性能指标
  const handleClearMetrics = () => {
    clearPerformanceMetrics();
    refreshMetrics();
  };

  // 复制性能报告
  const copyReport = () => {
    const report = generatePerformanceReport();
    navigator.clipboard.writeText(report)
      .then(() => {
        alert('性能报告已复制到剪贴板');
      })
      .catch(err => {
        console.error('无法复制报告:', err);
      });
  };

  // 获取指标评级
  const getMetricRating = (name: string, value: number): MetricRating => {
    switch (name) {
      case 'firstContentfulPaint':
        return value < 1800 ? 'good' : value < 3000 ? 'needs-improvement' : 'poor';
      case 'largestContentfulPaint':
        return value < 2500 ? 'good' : value < 4000 ? 'needs-improvement' : 'poor';
      case 'firstInputDelay':
        return value < 100 ? 'good' : value < 300 ? 'needs-improvement' : 'poor';
      case 'cumulativeLayoutShift':
        return value < 0.1 ? 'good' : value < 0.25 ? 'needs-improvement' : 'poor';
      case 'timeToFirstByte':
        return value < 800 ? 'good' : value < 1800 ? 'needs-improvement' : 'poor';
      case 'pageLoad':
        return value < 2500 ? 'good' : value < 4000 ? 'needs-improvement' : 'poor';
      default:
        return 'good';
    }
  };

  // 获取评级标签
  const getRatingTag = (rating: MetricRating) => {
    switch (rating) {
      case 'good':
        return <Tag color="success" icon={<CheckCircleOutlined />}>良好</Tag>;
      case 'needs-improvement':
        return <Tag color="warning" icon={<WarningOutlined />}>需改进</Tag>;
      case 'poor':
        return <Tag color="error" icon={<WarningOutlined />}>较差</Tag>;
      default:
        return null;
    }
  };

  // 渲染核心Web Vitals指标
  const renderCoreWebVitals = () => {
    const webVitals = [
      { name: 'LCP', label: '最大内容绘制', value: metrics.largestContentfulPaint, unit: 'ms', target: '< 2.5s' },
      { name: 'FID', label: '首次输入延迟', value: metrics.firstInputDelay, unit: 'ms', target: '< 100ms' },
      { name: 'CLS', label: '累积布局偏移', value: metrics.cumulativeLayoutShift, unit: '', target: '< 0.1' }
    ];

    return (
      <div style={{ marginBottom: 24 }}>
        <Title level={4}>核心Web Vitals</Title>
        <div style={{ display: 'flex', flexWrap: 'wrap', gap: 16 }}>
          {webVitals.map(metric => {
            if (metric.value === undefined) return null;
            
            const rating = getMetricRating(
              metric.name === 'LCP' ? 'largestContentfulPaint' : 
              metric.name === 'FID' ? 'firstInputDelay' : 'cumulativeLayoutShift', 
              metric.value
            );
            
            let percent = 0;
            if (metric.name === 'LCP') {
              percent = Math.min(100, (metric.value / 4000) * 100);
            } else if (metric.name === 'FID') {
              percent = Math.min(100, (metric.value / 300) * 100);
            } else if (metric.name === 'CLS') {
              percent = Math.min(100, (metric.value / 0.25) * 100);
            }
            
            const status = rating === 'good' ? 'success' : rating === 'needs-improvement' ? 'warning' : 'exception';
            
            return (
              <Card key={metric.name} style={{ width: 300 }}>
                <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: 8 }}>
                  <Text strong>{metric.label} ({metric.name})</Text>
                  {getRatingTag(rating)}
                </div>
                <Progress 
                  percent={percent} 
                  status={status} 
                  showInfo={false} 
                  strokeWidth={8}
                />
                <div style={{ display: 'flex', justifyContent: 'space-between', marginTop: 8 }}>
                  <Text>{metric.value.toFixed(metric.name === 'CLS' ? 3 : 0)}{metric.unit}</Text>
                  <Text type="secondary">目标: {metric.target}</Text>
                </div>
              </Card>
            );
          })}
        </div>
      </div>
    );
  };

  // 渲染页面加载指标
  const renderPageLoadMetrics = () => {
    const loadMetrics = [
      { key: 'pageLoad', name: '页面加载时间', value: metrics.pageLoad, unit: 'ms' },
      { key: 'domContentLoaded', name: 'DOM内容加载时间', value: metrics.domContentLoaded, unit: 'ms' },
      { key: 'firstPaint', name: '首次绘制时间', value: metrics.firstPaint, unit: 'ms' },
      { key: 'firstContentfulPaint', name: '首次内容绘制时间', value: metrics.firstContentfulPaint, unit: 'ms' },
      { key: 'timeToFirstByte', name: '首字节时间', value: metrics.timeToFirstByte, unit: 'ms' }
    ];

    return (
      <div style={{ marginBottom: 24 }}>
        <Title level={4}>页面加载指标</Title>
        <Table 
          dataSource={loadMetrics.filter(m => m.value !== undefined)}
          rowKey="key"
          pagination={false}
          size="small"
          columns={[
            {
              title: '指标',
              dataIndex: 'name',
              key: 'name',
            },
            {
              title: '值',
              dataIndex: 'value',
              key: 'value',
              render: (value, record) => `${value.toFixed(0)}${record.unit}`,
            },
            {
              title: '评级',
              key: 'rating',
              render: (_, record) => getRatingTag(getMetricRating(record.key, record.value)),
            }
          ]}
        />
      </div>
    );
  };

  // 渲染资源加载指标
  const renderResourceMetrics = () => {
    const resourceMetrics = [
      { key: 'resourceCount', name: '资源数量', value: metrics.resourceCount, unit: '' },
      { key: 'resourceLoadTime', name: '资源加载总时间', value: metrics.resourceLoadTime, unit: 'ms' },
      { key: 'jsLoadTime', name: 'JavaScript加载时间', value: metrics.jsLoadTime, unit: 'ms' },
      { key: 'cssLoadTime', name: 'CSS加载时间', value: metrics.cssLoadTime, unit: 'ms' },
      { key: 'imageLoadTime', name: '图片加载时间', value: metrics.imageLoadTime, unit: 'ms' }
    ];

    return (
      <div style={{ marginBottom: 24 }}>
        <Title level={4}>资源加载指标</Title>
        <Table 
          dataSource={resourceMetrics.filter(m => m.value !== undefined)}
          rowKey="key"
          pagination={false}
          size="small"
          columns={[
            {
              title: '指标',
              dataIndex: 'name',
              key: 'name',
            },
            {
              title: '值',
              dataIndex: 'value',
              key: 'value',
              render: (value, record) => `${value}${record.unit}`,
            }
          ]}
        />
      </div>
    );
  };

  // 渲染组件渲染时间
  const renderComponentRenderTimes = () => {
    if (!metrics.componentRenderTime || Object.keys(metrics.componentRenderTime).length === 0) {
      return (
        <div style={{ marginBottom: 24 }}>
          <Title level={4}>组件渲染时间</Title>
          <Alert message="暂无组件渲染数据" type="info" />
        </div>
      );
    }

    const componentData = Object.entries(metrics.componentRenderTime).map(([name, time]) => ({
      name,
      time,
      isSlow: time > 100
    }));

    return (
      <div style={{ marginBottom: 24 }}>
        <Title level={4}>组件渲染时间</Title>
        <Table 
          dataSource={componentData}
          rowKey="name"
          pagination={{ pageSize: 5 }}
          size="small"
          columns={[
            {
              title: '组件名称',
              dataIndex: 'name',
              key: 'name',
            },
            {
              title: '渲染时间',
              dataIndex: 'time',
              key: 'time',
              render: (time) => `${time.toFixed(2)}ms`,
              sorter: (a, b) => a.time - b.time,
              defaultSortOrder: 'descend',
            },
            {
              title: '状态',
              key: 'status',
              render: (_, record) => record.isSlow ? 
                <Tag color="error">缓慢</Tag> : 
                <Tag color="success">正常</Tag>,
            }
          ]}
        />
      </div>
    );
  };

  // 渲染API调用时间
  const renderApiCallTimes = () => {
    if (!metrics.apiCallTime || Object.keys(metrics.apiCallTime).length === 0) {
      return (
        <div style={{ marginBottom: 24 }}>
          <Title level={4}>API调用时间</Title>
          <Alert message="暂无API调用数据" type="info" />
        </div>
      );
    }

    const apiData = Object.entries(metrics.apiCallTime).map(([name, time]) => ({
      name,
      time,
      isSlow: time > 1000
    }));

    return (
      <div style={{ marginBottom: 24 }}>
        <Title level={4}>API调用时间</Title>
        <Table 
          dataSource={apiData}
          rowKey="name"
          pagination={{ pageSize: 5 }}
          size="small"
          columns={[
            {
              title: 'API名称',
              dataIndex: 'name',
              key: 'name',
            },
            {
              title: '调用时间',
              dataIndex: 'time',
              key: 'time',
              render: (time) => `${time.toFixed(2)}ms`,
              sorter: (a, b) => a.time - b.time,
              defaultSortOrder: 'descend',
            },
            {
              title: '状态',
              key: 'status',
              render: (_, record) => record.isSlow ? 
                <Tag color="error">缓慢</Tag> : 
                <Tag color="success">正常</Tag>,
            }
          ]}
        />
      </div>
    );
  };

  // 渲染优化建议
  const renderOptimizationTips = () => {
    if (tips.length === 0) {
      return (
        <Alert 
          message="性能良好" 
          description="当前没有发现明显的性能问题。" 
          type="success" 
          showIcon 
        />
      );
    }

    return (
      <Collapse defaultActiveKey={['1']}>
        <Panel header={`性能优化建议 (${tips.length})`} key="1">
          <ul>
            {tips.map((tip, index) => (
              <li key={index}>{tip}</li>
            ))}
          </ul>
        </Panel>
      </Collapse>
    );
  };

  return (
    <>
      {/* 悬浮按钮 */}
      <Tooltip title="性能监控">
        <Button
          type="primary"
          shape="circle"
          icon={<DashboardOutlined />}
          onClick={() => setVisible(true)}
          style={{
            position: 'fixed',
            bottom: 20,
            right: 20,
            zIndex: 1000,
          }}
        />
      </Tooltip>

      {/* 性能监控抽屉 */}
      <Drawer
        title="性能监控"
        placement="right"
        width={700}
        onClose={() => setVisible(false)}
        visible={visible}
        extra={
          <Space>
            <Button icon={<ReloadOutlined />} onClick={refreshMetrics}>
              刷新
            </Button>
            <Button icon={<SettingOutlined />} onClick={handleClearMetrics}>
              清除
            </Button>
          </Space>
        }
      >
        <Tabs activeKey={activeTab} onChange={setActiveTab}>
          <TabPane
            tab={
              <span>
                <DashboardOutlined />
                概览
              </span>
            }
            key="overview"
          >
            {renderCoreWebVitals()}
            {renderPageLoadMetrics()}
            {renderOptimizationTips()}
          </TabPane>
          <TabPane
            tab={
              <span>
                <BarChartOutlined />
                资源
              </span>
            }
            key="resources"
          >
            {renderResourceMetrics()}
          </TabPane>
          <TabPane
            tab={
              <span>
                <ClockCircleOutlined />
                组件
              </span>
            }
            key="components"
          >
            {renderComponentRenderTimes()}
          </TabPane>
          <TabPane
            tab={
              <span>
                <ApiOutlined />
                API
              </span>
            }
            key="api"
          >
            {renderApiCallTimes()}
          </TabPane>
        </Tabs>

        <div style={{ marginTop: 24 }}>
          <Button type="primary" onClick={copyReport}>
            复制完整报告
          </Button>
        </div>
      </Drawer>
    </>
  );
};

export default PerformanceMonitor;
