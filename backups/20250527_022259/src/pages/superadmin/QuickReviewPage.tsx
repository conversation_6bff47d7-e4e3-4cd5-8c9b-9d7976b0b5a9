import React, { useState, useEffect } from 'react';
import SuperAdminLayout from '@/components/layouts/SuperAdminLayout';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import {
  Clock,
  CheckCircle,
  XCircle,
  AlertTriangle,
  Zap,
  MessageSquare,
  FileText,
  Quote,
  ThumbsUp,
  ThumbsDown
} from 'lucide-react';
import { useToast } from '@/components/ui/use-toast';
import { getContentsPending, approveContentNew, rejectContentNew } from '@/services/dataService';

/**
 * 超级管理员快速审核页面
 *
 * 提供快速审核功能，支持键盘快捷键
 * 用于高效处理大量待审核内容
 */
const SuperAdminQuickReviewPage: React.FC = () => {
  const { toast } = useToast();
  const [pendingItems, setPendingItems] = useState<any[]>([]);
  const [currentIndex, setCurrentIndex] = useState(0);
  const [isLoading, setIsLoading] = useState(true);
  const [stats, setStats] = useState({
    total: 0,
    approved: 0,
    rejected: 0,
    remaining: 0
  });

  // 真实API数据加载
  useEffect(() => {
    const loadPendingItems = async () => {
      setIsLoading(true);
      try {
        // 使用真实API获取待审核内容
        const response = await getContentsPending({
          page: 1,
          pageSize: 50, // 获取更多数据用于快速审核
          status: 'pending'
        });

        if (response && response.success) {
          // 转换API数据格式
          const items = response.contents.map((content: any) => ({
            id: content.id,
            type: content.type,
            title: content.originalContent?.title || '无标题',
            content: typeof content.originalContent === 'string'
              ? content.originalContent
              : content.originalContent?.content || content.sanitizedContent || '无内容',
            author: content.originalContent?.author || '匿名用户',
            submittedAt: content.createdAt,
            priority: content.priority === 5 ? 'high' : content.priority >= 3 ? 'normal' : 'low'
          }));

          setPendingItems(items);
          setStats({
            total: items.length,
            approved: 0,
            rejected: 0,
            remaining: items.length
          });
        } else {
          throw new Error(response?.error || '获取待审核内容失败');
        }
      } catch (error) {
        console.error('加载待审核内容失败:', error);
        toast({
          title: '加载失败',
          description: error instanceof Error ? error.message : '无法加载待审核内容',
          variant: 'destructive',
        });

        // 如果API失败，使用备用数据
        const fallbackItems = [
          {
            id: 'fallback-1',
            type: 'story',
            title: '示例故事',
            content: '这是一个示例故事内容...',
            author: '匿名用户',
            submittedAt: new Date().toISOString(),
            priority: 'normal'
          }
        ];

        setPendingItems(fallbackItems);
        setStats({
          total: fallbackItems.length,
          approved: 0,
          rejected: 0,
          remaining: fallbackItems.length
        });
      } finally {
        setIsLoading(false);
      }
    };

    loadPendingItems();
  }, [toast]);

  // 键盘快捷键处理
  useEffect(() => {
    const handleKeyPress = (event: KeyboardEvent) => {
      if (event.target instanceof HTMLInputElement || event.target instanceof HTMLTextAreaElement) {
        return; // 如果焦点在输入框中，不处理快捷键
      }

      switch (event.key) {
        case 'a':
        case 'A':
          handleApprove();
          break;
        case 'r':
        case 'R':
          handleReject();
          break;
        case 'n':
        case 'N':
          handleNext();
          break;
        case 'p':
        case 'P':
          handlePrevious();
          break;
      }
    };

    window.addEventListener('keydown', handleKeyPress);
    return () => window.removeEventListener('keydown', handleKeyPress);
  }, [currentIndex, pendingItems]);

  const currentItem = pendingItems[currentIndex];

  const handleApprove = async () => {
    if (!currentItem) return;

    try {
      // 使用真实API批准内容
      const response = await approveContentNew(currentItem.id, {
        reviewNotes: '快速审核通过'
      });

      if (response.success) {
        toast({
          title: '审核成功',
          description: '内容已批准',
        });

        setStats(prev => ({
          ...prev,
          approved: prev.approved + 1,
          remaining: prev.remaining - 1
        }));

        handleNext();
      } else {
        throw new Error(response.error || '批准内容失败');
      }
    } catch (error) {
      console.error('批准内容失败:', error);
      toast({
        title: '操作失败',
        description: error instanceof Error ? error.message : '批准内容时发生错误',
        variant: 'destructive',
      });
    }
  };

  const handleReject = async () => {
    if (!currentItem) return;

    try {
      // 使用真实API拒绝内容
      const response = await rejectContentNew(currentItem.id, {
        reason: '快速审核拒绝',
        reviewNotes: '快速审核拒绝'
      });

      if (response.success) {
        toast({
          title: '审核成功',
          description: '内容已拒绝',
        });

        setStats(prev => ({
          ...prev,
          rejected: prev.rejected + 1,
          remaining: prev.remaining - 1
        }));

        handleNext();
      } else {
        throw new Error(response.error || '拒绝内容失败');
      }
    } catch (error) {
      console.error('拒绝内容失败:', error);
      toast({
        title: '操作失败',
        description: error instanceof Error ? error.message : '拒绝内容时发生错误',
        variant: 'destructive',
      });
    }
  };

  const handleNext = () => {
    if (currentIndex < pendingItems.length - 1) {
      setCurrentIndex(currentIndex + 1);
    }
  };

  const handlePrevious = () => {
    if (currentIndex > 0) {
      setCurrentIndex(currentIndex - 1);
    }
  };

  const getTypeIcon = (type: string) => {
    switch (type) {
      case 'story':
        return <FileText className="h-4 w-4" />;
      case 'comment':
        return <MessageSquare className="h-4 w-4" />;
      case 'voice':
        return <Quote className="h-4 w-4" />;
      default:
        return <FileText className="h-4 w-4" />;
    }
  };

  const getTypeName = (type: string) => {
    switch (type) {
      case 'story':
        return '故事';
      case 'comment':
        return '评论';
      case 'voice':
        return '问卷心声';
      default:
        return '内容';
    }
  };

  const getPriorityBadge = (priority: string) => {
    switch (priority) {
      case 'high':
        return <Badge variant="destructive">高优先级</Badge>;
      case 'normal':
        return <Badge variant="default">普通</Badge>;
      case 'low':
        return <Badge variant="secondary">低优先级</Badge>;
      default:
        return <Badge variant="outline">未知</Badge>;
    }
  };

  if (isLoading) {
    return (
      <SuperAdminLayout>
        <div className="container mx-auto py-6">
          <div className="flex justify-center items-center h-64">
            <div className="text-center">
              <Clock className="h-8 w-8 animate-spin mx-auto mb-4" />
              <p>加载待审核内容...</p>
            </div>
          </div>
        </div>
      </SuperAdminLayout>
    );
  }

  if (pendingItems.length === 0) {
    return (
      <SuperAdminLayout>
        <div className="container mx-auto py-6">
          <div className="text-center py-12">
            <CheckCircle className="h-16 w-16 text-green-500 mx-auto mb-4" />
            <h2 className="text-2xl font-bold mb-2">所有内容已审核完成</h2>
            <p className="text-gray-500">当前没有待审核的内容</p>
          </div>
        </div>
      </SuperAdminLayout>
    );
  }

  return (
    <SuperAdminLayout>
      <div className="container mx-auto py-6">
        <div className="flex justify-between items-center mb-6">
          <div>
            <h1 className="text-2xl font-bold flex items-center gap-2">
              <Zap className="h-6 w-6" />
              快速审核
            </h1>
            <p className="text-gray-500 mt-1">使用键盘快捷键快速审核内容 (A=批准, R=拒绝, N=下一个, P=上一个)</p>
          </div>
        </div>

        {/* 统计信息 */}
        <div className="grid grid-cols-4 gap-4 mb-6">
          <Card>
            <CardContent className="p-4">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm text-gray-500">总计</p>
                  <p className="text-2xl font-bold">{stats.total}</p>
                </div>
                <FileText className="h-8 w-8 text-blue-500" />
              </div>
            </CardContent>
          </Card>
          <Card>
            <CardContent className="p-4">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm text-gray-500">已批准</p>
                  <p className="text-2xl font-bold text-green-600">{stats.approved}</p>
                </div>
                <CheckCircle className="h-8 w-8 text-green-500" />
              </div>
            </CardContent>
          </Card>
          <Card>
            <CardContent className="p-4">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm text-gray-500">已拒绝</p>
                  <p className="text-2xl font-bold text-red-600">{stats.rejected}</p>
                </div>
                <XCircle className="h-8 w-8 text-red-500" />
              </div>
            </CardContent>
          </Card>
          <Card>
            <CardContent className="p-4">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm text-gray-500">剩余</p>
                  <p className="text-2xl font-bold text-orange-600">{stats.remaining}</p>
                </div>
                <AlertTriangle className="h-8 w-8 text-orange-500" />
              </div>
            </CardContent>
          </Card>
        </div>

        {/* 当前审核项目 */}
        {currentItem && (
          <Card className="mb-6">
            <CardHeader>
              <div className="flex justify-between items-start">
                <div className="flex items-center gap-2">
                  {getTypeIcon(currentItem.type)}
                  <CardTitle>{getTypeName(currentItem.type)} #{currentIndex + 1}</CardTitle>
                  {getPriorityBadge(currentItem.priority)}
                </div>
                <div className="text-sm text-gray-500">
                  {currentIndex + 1} / {pendingItems.length}
                </div>
              </div>
              {currentItem.title && (
                <CardDescription className="text-lg font-medium">
                  {currentItem.title}
                </CardDescription>
              )}
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div className="bg-gray-50 p-4 rounded-md">
                  <p className="whitespace-pre-wrap">{currentItem.content}</p>
                </div>

                <div className="flex justify-between items-center text-sm text-gray-500">
                  <span>作者: {currentItem.author}</span>
                  <span>提交时间: {new Date(currentItem.submittedAt).toLocaleString()}</span>
                </div>

                <div className="flex justify-center gap-4 pt-4">
                  <Button
                    onClick={handleApprove}
                    className="bg-green-600 hover:bg-green-700 text-white px-8"
                  >
                    <ThumbsUp className="mr-2 h-4 w-4" />
                    批准 (A)
                  </Button>
                  <Button
                    onClick={handleReject}
                    variant="destructive"
                    className="px-8"
                  >
                    <ThumbsDown className="mr-2 h-4 w-4" />
                    拒绝 (R)
                  </Button>
                </div>

                <div className="flex justify-center gap-2 pt-2">
                  <Button
                    onClick={handlePrevious}
                    variant="outline"
                    disabled={currentIndex === 0}
                  >
                    上一个 (P)
                  </Button>
                  <Button
                    onClick={handleNext}
                    variant="outline"
                    disabled={currentIndex === pendingItems.length - 1}
                  >
                    下一个 (N)
                  </Button>
                </div>
              </div>
            </CardContent>
          </Card>
        )}
      </div>
    </SuperAdminLayout>
  );
};

export default SuperAdminQuickReviewPage;
