/**
 * 简单的用户管理API测试服务器
 */

const http = require('http');
const url = require('url');

// 模拟用户数据
const mockUsers = [
  {
    id: 1,
    email: '<EMAIL>',
    username: 'superadmin',
    name: '超级管理员',
    role: 'superadmin',
    emailVerified: true,
    createdAt: '2025-05-24T05:10:35.000Z',
    updatedAt: '2025-05-24T05:10:35.000Z',
    lastLoginAt: '2025-05-24T10:30:15.000Z',
    lastLoginIp: '*************'
  },
  {
    id: 2,
    email: '<EMAIL>',
    username: 'admin',
    name: '管理员',
    role: 'admin',
    emailVerified: true,
    createdAt: '2025-05-24T05:10:35.000Z',
    updatedAt: '2025-05-24T05:10:35.000Z',
    lastLoginAt: '2025-05-24T09:15:20.000Z',
    lastLoginIp: '*************'
  },
  {
    id: 3,
    email: '<EMAIL>',
    username: 'reviewer',
    name: '审核员',
    role: 'reviewer',
    emailVerified: true,
    createdAt: '2025-05-24T05:10:35.000Z',
    updatedAt: '2025-05-24T05:10:35.000Z',
    lastLoginAt: '2025-05-24T08:45:10.000Z',
    lastLoginIp: '*************'
  },
  // 普通用户数据
  {
    id: 4,
    email: '<EMAIL>',
    username: 'zhangsan',
    name: '张三',
    role: 'user',
    emailVerified: true,
    createdAt: '2025-05-20T14:30:00.000Z',
    updatedAt: '2025-05-23T16:20:00.000Z',
    lastLoginAt: '2025-05-23T16:20:00.000Z',
    lastLoginIp: '*************'
  },
  {
    id: 5,
    email: '<EMAIL>',
    username: 'lisi',
    name: '李四',
    role: 'user',
    emailVerified: true,
    createdAt: '2025-05-19T09:15:00.000Z',
    updatedAt: '2025-05-23T11:30:00.000Z',
    lastLoginAt: '2025-05-23T11:30:00.000Z',
    lastLoginIp: '*************'
  },
  {
    id: 6,
    email: '<EMAIL>',
    username: 'wangwu',
    name: '王五',
    role: 'user',
    emailVerified: false,
    createdAt: '2025-05-18T16:45:00.000Z',
    updatedAt: '2025-05-18T16:45:00.000Z',
    lastLoginAt: null,
    lastLoginIp: null
  },
  {
    id: 7,
    email: '<EMAIL>',
    username: 'zhaoliu',
    name: '赵六',
    role: 'user',
    emailVerified: true,
    createdAt: '2025-05-17T13:20:00.000Z',
    updatedAt: '2025-05-22T19:10:00.000Z',
    lastLoginAt: '2025-05-22T19:10:00.000Z',
    lastLoginIp: '*************'
  },
  {
    id: 8,
    email: '<EMAIL>',
    username: 'sunqi',
    name: '孙七',
    role: 'user',
    emailVerified: true,
    createdAt: '2025-05-16T10:30:00.000Z',
    updatedAt: '2025-05-23T14:45:00.000Z',
    lastLoginAt: '2025-05-23T14:45:00.000Z',
    lastLoginIp: '*************'
  }
];

// 模拟角色数据
const mockRoles = [
  {
    id: 'superadmin',
    name: '超级管理员',
    description: '拥有系统所有权限',
    isSystem: true,
    userCount: 1,
    permissions: [
      { id: 'DASHBOARD_PERSONAL', name: '个人仪表盘' },
      { id: 'DASHBOARD_SYSTEM', name: '系统概览' },
      { id: 'USER_MANAGEMENT', name: '用户管理' },
      { id: 'ROLE_MANAGEMENT', name: '角色管理' }
    ]
  },
  {
    id: 'admin',
    name: '管理员',
    description: '拥有大部分管理权限',
    isSystem: true,
    userCount: 1,
    permissions: [
      { id: 'DASHBOARD_PERSONAL', name: '个人仪表盘' },
      { id: 'DASHBOARD_SYSTEM', name: '系统概览' },
      { id: 'USER_MANAGEMENT', name: '用户管理' }
    ]
  },
  {
    id: 'reviewer',
    name: '审核员',
    description: '负责内容审核',
    isSystem: true,
    userCount: 1,
    permissions: [
      { id: 'DASHBOARD_PERSONAL', name: '个人仪表盘' },
      { id: 'CONTENT_REVIEW', name: '内容审核' }
    ]
  }
];

// 操作日志数据
const operationLogs = [];

// 权限定义
const permissions = {
  superadmin: ['ALL'],
  admin: ['USER_MANAGEMENT', 'CONTENT_REVIEW', 'DATA_ANALYSIS', 'SYSTEM_CONFIG'],
  reviewer: ['CONTENT_REVIEW', 'DASHBOARD_PERSONAL'],
  user: ['DASHBOARD_PERSONAL', 'SUBMIT_CONTENT']
};

// 记录操作日志
function logOperation(adminId, action, target, details, ipAddress, result = 'success') {
  const log = {
    id: Date.now().toString(),
    adminId,
    adminName: mockUsers.find(u => u.id == adminId)?.name || '未知',
    action,
    target,
    details,
    timestamp: new Date().toISOString(),
    ipAddress,
    result
  };
  operationLogs.unshift(log); // 最新的在前面

  // 只保留最近100条记录
  if (operationLogs.length > 100) {
    operationLogs.splice(100);
  }

  console.log(`📝 操作日志: ${log.adminName} ${action} ${target} - ${result}`);
  return log;
}

// 权限验证
function hasPermission(userRole, requiredPermission) {
  const userPermissions = permissions[userRole] || [];
  return userPermissions.includes('ALL') || userPermissions.includes(requiredPermission);
}

// 获取客户端IP
function getClientIP(req) {
  return req.headers['x-forwarded-for'] ||
         req.connection.remoteAddress ||
         req.socket.remoteAddress ||
         '127.0.0.1';
}

// CORS 头
const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
  'Access-Control-Allow-Headers': 'Content-Type, Authorization',
  'Access-Control-Max-Age': '86400'
};

// 创建服务器
const server = http.createServer((req, res) => {
  const parsedUrl = url.parse(req.url, true);
  const path = parsedUrl.pathname;
  const method = req.method;

  // 设置CORS头
  Object.keys(corsHeaders).forEach(key => {
    res.setHeader(key, corsHeaders[key]);
  });

  // 处理OPTIONS请求
  if (method === 'OPTIONS') {
    res.writeHead(200);
    res.end();
    return;
  }

  // 设置JSON响应头
  res.setHeader('Content-Type', 'application/json');

  console.log(`${method} ${path}`);

  try {
    // 路由处理
    if (path === '/api/admin/users' && method === 'GET') {
      // 获取用户列表
      const roleFilter = parsedUrl.query.role;
      const searchTerm = parsedUrl.query.search;
      const page = parseInt(parsedUrl.query.page || '1');
      const pageSize = parseInt(parsedUrl.query.pageSize || '10');

      let filteredUsers = mockUsers;

      // 角色筛选
      if (roleFilter && roleFilter !== 'all') {
        filteredUsers = filteredUsers.filter(user => user.role === roleFilter);
      }

      // 搜索筛选
      if (searchTerm) {
        const term = searchTerm.toLowerCase();
        filteredUsers = filteredUsers.filter(user =>
          user.username.toLowerCase().includes(term) ||
          user.email.toLowerCase().includes(term) ||
          user.name.toLowerCase().includes(term) ||
          user.id.toString().includes(term)
        );
      }

      const total = filteredUsers.length;
      const offset = (page - 1) * pageSize;
      const paginatedUsers = filteredUsers.slice(offset, offset + pageSize);

      res.writeHead(200);
      res.end(JSON.stringify({
        success: true,
        data: {
          users: paginatedUsers,
          pagination: {
            page,
            pageSize,
            total,
            totalPages: Math.ceil(total / pageSize)
          }
        }
      }));
    } else if (path === '/api/admin/users' && method === 'POST') {
      // 创建用户
      let body = '';
      req.on('data', chunk => {
        body += chunk.toString();
      });

      req.on('end', () => {
        try {
          const userData = JSON.parse(body);
          const clientIP = getClientIP(req);

          // 验证必填字段
          if (!userData.username || !userData.email || !userData.password) {
            res.writeHead(400);
            res.end(JSON.stringify({
              success: false,
              error: '用户名、邮箱和密码为必填项'
            }));
            return;
          }

          // 检查邮箱是否已存在
          if (mockUsers.find(u => u.email === userData.email)) {
            res.writeHead(400);
            res.end(JSON.stringify({
              success: false,
              error: '邮箱已存在'
            }));
            return;
          }

          // 检查用户名是否已存在
          if (mockUsers.find(u => u.username === userData.username)) {
            res.writeHead(400);
            res.end(JSON.stringify({
              success: false,
              error: '用户名已存在'
            }));
            return;
          }

          // 创建新用户
          const newUser = {
            id: Math.max(...mockUsers.map(u => u.id)) + 1,
            email: userData.email,
            username: userData.username,
            name: userData.name || userData.username,
            role: userData.role || 'user',
            emailVerified: userData.status === 'active',
            createdAt: new Date().toISOString(),
            updatedAt: new Date().toISOString(),
            lastLoginAt: null,
            lastLoginIp: null
          };

          mockUsers.push(newUser);

          // 记录操作日志
          logOperation(
            1, // 假设是超级管理员操作
            '创建用户',
            `user_${newUser.id}`,
            `创建了新用户: ${newUser.username} (${newUser.email})`,
            clientIP
          );

          res.writeHead(201);
          res.end(JSON.stringify({
            success: true,
            data: newUser,
            message: '用户创建成功'
          }));
        } catch (error) {
          res.writeHead(400);
          res.end(JSON.stringify({
            success: false,
            error: '请求数据格式错误'
          }));
        }
      });
      return; // 重要：防止继续执行
    } else if (path.startsWith('/api/admin/users/') && method === 'PUT') {
      // 更新用户
      const userId = parseInt(path.split('/').pop());
      let body = '';

      req.on('data', chunk => {
        body += chunk.toString();
      });

      req.on('end', () => {
        try {
          const updateData = JSON.parse(body);
          const clientIP = getClientIP(req);

          const userIndex = mockUsers.findIndex(u => u.id === userId);
          if (userIndex === -1) {
            res.writeHead(404);
            res.end(JSON.stringify({
              success: false,
              error: '用户不存在'
            }));
            return;
          }

          const oldUser = { ...mockUsers[userIndex] };

          // 更新用户信息
          if (updateData.role) {
            mockUsers[userIndex].role = updateData.role;
          }
          if (updateData.status !== undefined) {
            mockUsers[userIndex].emailVerified = updateData.status === 'active';
          }
          if (updateData.name) {
            mockUsers[userIndex].name = updateData.name;
          }
          if (updateData.email) {
            // 检查邮箱是否已被其他用户使用
            const existingUser = mockUsers.find(u => u.email === updateData.email && u.id !== userId);
            if (existingUser) {
              res.writeHead(400);
              res.end(JSON.stringify({
                success: false,
                error: '邮箱已被其他用户使用'
              }));
              return;
            }
            mockUsers[userIndex].email = updateData.email;
          }

          mockUsers[userIndex].updatedAt = new Date().toISOString();

          // 记录操作日志
          const changes = [];
          if (oldUser.role !== mockUsers[userIndex].role) {
            changes.push(`角色: ${oldUser.role} → ${mockUsers[userIndex].role}`);
          }
          if (oldUser.emailVerified !== mockUsers[userIndex].emailVerified) {
            changes.push(`状态: ${oldUser.emailVerified ? '活跃' : '未激活'} → ${mockUsers[userIndex].emailVerified ? '活跃' : '未激活'}`);
          }
          if (oldUser.name !== mockUsers[userIndex].name) {
            changes.push(`姓名: ${oldUser.name} → ${mockUsers[userIndex].name}`);
          }
          if (oldUser.email !== mockUsers[userIndex].email) {
            changes.push(`邮箱: ${oldUser.email} → ${mockUsers[userIndex].email}`);
          }

          if (changes.length > 0) {
            logOperation(
              1, // 假设是超级管理员操作
              '更新用户',
              `user_${userId}`,
              `更新用户 ${mockUsers[userIndex].username}: ${changes.join(', ')}`,
              clientIP
            );
          }

          res.writeHead(200);
          res.end(JSON.stringify({
            success: true,
            data: mockUsers[userIndex],
            message: '用户更新成功'
          }));
        } catch (error) {
          res.writeHead(400);
          res.end(JSON.stringify({
            success: false,
            error: '请求数据格式错误'
          }));
        }
      });
      return;
    } else if (path.startsWith('/api/admin/users/') && method === 'DELETE') {
      // 删除用户
      const userId = parseInt(path.split('/').pop());
      const clientIP = getClientIP(req);

      const userIndex = mockUsers.findIndex(u => u.id === userId);
      if (userIndex === -1) {
        res.writeHead(404);
        res.end(JSON.stringify({
          success: false,
          error: '用户不存在'
        }));
        return;
      }

      const deletedUser = mockUsers[userIndex];

      // 防止删除超级管理员
      if (deletedUser.role === 'superadmin') {
        res.writeHead(403);
        res.end(JSON.stringify({
          success: false,
          error: '不能删除超级管理员账户'
        }));
        return;
      }

      mockUsers.splice(userIndex, 1);

      // 记录操作日志
      logOperation(
        1, // 假设是超级管理员操作
        '删除用户',
        `user_${userId}`,
        `删除了用户: ${deletedUser.username} (${deletedUser.email})`,
        clientIP
      );

      res.writeHead(200);
      res.end(JSON.stringify({
        success: true,
        message: '用户删除成功'
      }));
    } else if (path === '/api/admin/roles' && method === 'GET') {
      // 获取角色列表
      res.writeHead(200);
      res.end(JSON.stringify({
        success: true,
        data: mockRoles,
        pagination: {
          page: 1,
          pageSize: 10,
          total: mockRoles.length,
          totalPages: 1
        }
      }));
    } else if (path === '/api/admin/operation-logs' && method === 'GET') {
      // 获取操作日志
      const page = parseInt(parsedUrl.query.page || '1');
      const pageSize = parseInt(parsedUrl.query.pageSize || '20');
      const adminId = parsedUrl.query.adminId;
      const action = parsedUrl.query.action;

      let filteredLogs = operationLogs;

      // 按管理员筛选
      if (adminId) {
        filteredLogs = filteredLogs.filter(log => log.adminId == adminId);
      }

      // 按操作类型筛选
      if (action) {
        filteredLogs = filteredLogs.filter(log => log.action.includes(action));
      }

      const total = filteredLogs.length;
      const offset = (page - 1) * pageSize;
      const paginatedLogs = filteredLogs.slice(offset, offset + pageSize);

      res.writeHead(200);
      res.end(JSON.stringify({
        success: true,
        data: {
          logs: paginatedLogs,
          pagination: {
            page,
            pageSize,
            total,
            totalPages: Math.ceil(total / pageSize)
          }
        }
      }));
    } else if (path === '/api/admin/permissions' && method === 'GET') {
      // 获取权限列表
      const role = parsedUrl.query.role;

      if (role) {
        res.writeHead(200);
        res.end(JSON.stringify({
          success: true,
          data: {
            role,
            permissions: permissions[role] || []
          }
        }));
      } else {
        res.writeHead(200);
        res.end(JSON.stringify({
          success: true,
          data: permissions
        }));
      }
    } else if (path === '/health' && method === 'GET') {
      // 健康检查
      res.writeHead(200);
      res.end(JSON.stringify({
        status: 'ok',
        environment: 'development',
        timestamp: new Date().toISOString(),
        services: {
          userManagement: 'active',
          operationLogs: 'active',
          permissions: 'active'
        }
      }));
    } else {
      // 404
      res.writeHead(404);
      res.end(JSON.stringify({
        success: false,
        error: 'Not found'
      }));
    }
  } catch (error) {
    console.error('Server error:', error);
    res.writeHead(500);
    res.end(JSON.stringify({
      success: false,
      error: 'Internal Server Error'
    }));
  }
});

// 启动服务器
const PORT = 8789;
server.listen(PORT, () => {
  console.log(`🚀 测试API服务器启动成功！`);
  console.log(`📍 地址: http://localhost:${PORT}`);
  console.log(`🔍 健康检查: http://localhost:${PORT}/health`);
  console.log(`👥 用户管理: http://localhost:${PORT}/api/admin/users`);
  console.log(`🔐 角色管理: http://localhost:${PORT}/api/admin/roles`);
});

// 优雅关闭
process.on('SIGINT', () => {
  console.log('\n🛑 服务器正在关闭...');
  server.close(() => {
    console.log('✅ 服务器已关闭');
    process.exit(0);
  });
});
