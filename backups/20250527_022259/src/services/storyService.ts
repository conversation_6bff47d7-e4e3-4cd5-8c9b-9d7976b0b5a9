/**
 * 故事服务
 * 
 * 提供故事相关的功能
 */

import { fetchData } from './unifiedDataService';

/**
 * 获取故事列表
 * @param params 请求参数
 * @returns 故事列表
 */
export async function getStories(params: any = {}) {
  return fetchData(
    '/stories',
    'getStoriesMock',
    params,
    { method: 'GET' }
  );
}

/**
 * 获取故事详情
 * @param id 故事ID
 * @returns 故事详情
 */
export async function getStoryDetail(id: number) {
  return fetchData(
    `/stories/${id}`,
    'getStoryDetailMock',
    { id },
    { method: 'GET' }
  );
}

/**
 * 获取待审核故事列表
 * @returns 待审核故事列表
 */
export async function getPendingStories() {
  return fetchData(
    '/admin/stories/pending',
    'getPendingStoriesMock',
    {},
    { method: 'GET' }
  );
}

/**
 * 审核故事
 * @param id 故事ID
 * @param status 审核状态
 * @param reason 拒绝原因
 * @returns 审核结果
 */
export async function moderateStory(id: number, status: 'approved' | 'rejected', reason?: string) {
  return fetchData(
    `/admin/stories/${id}/moderate`,
    'moderateStoryMock',
    { id, status, reason },
    { method: 'POST' }
  );
}

/**
 * 创建故事
 * @param storyData 故事数据
 * @returns 创建结果
 */
export async function createStory(storyData: {
  title: string;
  content: string;
  isAnonymous: boolean;
  tags?: string[];
  identityA: string;
  identityB: string;
}) {
  return fetchData(
    '/stories',
    'createStoryMock',
    storyData,
    { method: 'POST' }
  );
}

/**
 * 更新故事
 * @param id 故事ID
 * @param storyData 故事数据
 * @returns 更新结果
 */
export async function updateStory(
  id: number,
  storyData: {
    title?: string;
    content?: string;
    isAnonymous?: boolean;
    tags?: string[];
  }
) {
  return fetchData(
    `/stories/${id}`,
    'updateStoryMock',
    { id, ...storyData },
    { method: 'PUT' }
  );
}

/**
 * 删除故事
 * @param id 故事ID
 * @returns 删除结果
 */
export async function deleteStory(id: number) {
  return fetchData(
    `/stories/${id}`,
    'deleteStoryMock',
    { id },
    { method: 'DELETE' }
  );
}

/**
 * 点赞故事
 * @param id 故事ID
 * @returns 点赞结果
 */
export async function likeStory(id: number) {
  return fetchData(
    `/stories/${id}/like`,
    'likeStoryMock',
    { id },
    { method: 'POST' }
  );
}

/**
 * 取消点赞故事
 * @param id 故事ID
 * @returns 取消点赞结果
 */
export async function unlikeStory(id: number) {
  return fetchData(
    `/stories/${id}/unlike`,
    'unlikeStoryMock',
    { id },
    { method: 'POST' }
  );
}
