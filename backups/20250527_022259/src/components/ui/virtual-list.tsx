import React, { useState, useEffect, useRef, useCallback } from 'react';
import { cn } from '@/lib/utils';

interface VirtualListProps<T> {
  items: T[];
  height: number;
  itemHeight: number;
  renderItem: (item: T, index: number, isVisible: boolean) => React.ReactNode;
  overscan?: number;
  className?: string;
  onEndReached?: () => void;
  endReachedThreshold?: number;
  scrollToIndex?: number;
  onScroll?: (scrollTop: number) => void;
}

/**
 * 虚拟滚动列表组件
 * 只渲染可见区域的项目，优化大量数据的显示性能
 */
export function VirtualList<T>({
  items,
  height,
  itemHeight,
  renderItem,
  overscan = 3,
  className,
  onEndReached,
  endReachedThreshold = 0.8,
  scrollToIndex,
  onScroll
}: VirtualListProps<T>) {
  const containerRef = useRef<HTMLDivElement>(null);
  const [scrollTop, setScrollTop] = useState(0);
  const [prevItems, setPrevItems] = useState<T[]>(items);
  const [prevScrollToIndex, setPrevScrollToIndex] = useState<number | undefined>(scrollToIndex);

  // 计算总高度
  const totalHeight = items.length * itemHeight;

  // 计算可见区域的起始和结束索引
  const startIndex = Math.max(0, Math.floor(scrollTop / itemHeight) - overscan);
  const visibleCount = Math.ceil(height / itemHeight) + 2 * overscan;
  const endIndex = Math.min(items.length - 1, startIndex + visibleCount);

  // 处理滚动事件
  const handleScroll = useCallback(() => {
    if (containerRef.current) {
      const newScrollTop = containerRef.current.scrollTop;
      setScrollTop(newScrollTop);
      
      // 触发滚动回调
      onScroll?.(newScrollTop);
      
      // 检查是否到达底部
      if (onEndReached) {
        const scrollHeight = containerRef.current.scrollHeight;
        const clientHeight = containerRef.current.clientHeight;
        const scrollPosition = newScrollTop + clientHeight;
        const threshold = scrollHeight * endReachedThreshold;
        
        if (scrollPosition >= threshold) {
          onEndReached();
        }
      }
    }
  }, [onEndReached, endReachedThreshold, onScroll]);

  // 监听滚动事件
  useEffect(() => {
    const container = containerRef.current;
    if (container) {
      container.addEventListener('scroll', handleScroll);
      return () => {
        container.removeEventListener('scroll', handleScroll);
      };
    }
  }, [handleScroll]);

  // 当items变化时，保持滚动位置
  useEffect(() => {
    if (items !== prevItems && containerRef.current) {
      // 如果是加载更多数据，保持滚动位置
      if (items.length > prevItems.length && items.slice(0, prevItems.length).every((item, i) => item === prevItems[i])) {
        // 保持滚动位置
      } else {
        // 重置滚动位置
        containerRef.current.scrollTop = 0;
        setScrollTop(0);
      }
      setPrevItems(items);
    }
  }, [items, prevItems]);

  // 滚动到指定索引
  useEffect(() => {
    if (scrollToIndex !== undefined && scrollToIndex !== prevScrollToIndex && containerRef.current) {
      const targetScrollTop = scrollToIndex * itemHeight;
      containerRef.current.scrollTop = targetScrollTop;
      setScrollTop(targetScrollTop);
      setPrevScrollToIndex(scrollToIndex);
    }
  }, [scrollToIndex, prevScrollToIndex, itemHeight]);

  // 渲染可见项目
  const visibleItems = [];
  for (let i = startIndex; i <= endIndex; i++) {
    const item = items[i];
    if (item) {
      const isVisible = i >= startIndex && i <= endIndex;
      const top = i * itemHeight;
      visibleItems.push(
        <div
          key={i}
          style={{
            position: 'absolute',
            top,
            height: itemHeight,
            width: '100%'
          }}
          data-index={i}
        >
          {renderItem(item, i, isVisible)}
        </div>
      );
    }
  }

  return (
    <div
      ref={containerRef}
      className={cn('relative overflow-auto', className)}
      style={{ height }}
    >
      <div style={{ height: totalHeight, position: 'relative' }}>
        {visibleItems}
      </div>
    </div>
  );
}

interface VirtualTableProps<T> {
  items: T[];
  height: number;
  itemHeight: number;
  renderHeader: () => React.ReactNode;
  renderRow: (item: T, index: number, isVisible: boolean) => React.ReactNode;
  overscan?: number;
  className?: string;
  onEndReached?: () => void;
  endReachedThreshold?: number;
  scrollToIndex?: number;
  onScroll?: (scrollTop: number) => void;
  headerHeight?: number;
}

/**
 * 虚拟滚动表格组件
 * 基于虚拟滚动列表，专为表格优化
 */
export function VirtualTable<T>({
  items,
  height,
  itemHeight,
  renderHeader,
  renderRow,
  overscan = 3,
  className,
  onEndReached,
  endReachedThreshold = 0.8,
  scrollToIndex,
  onScroll,
  headerHeight = 40
}: VirtualTableProps<T>) {
  const tableHeight = height - headerHeight;
  
  return (
    <div className={cn('flex flex-col', className)}>
      <div className="flex-none" style={{ height: headerHeight }}>
        {renderHeader()}
      </div>
      <VirtualList
        items={items}
        height={tableHeight}
        itemHeight={itemHeight}
        renderItem={renderRow}
        overscan={overscan}
        onEndReached={onEndReached}
        endReachedThreshold={endReachedThreshold}
        scrollToIndex={scrollToIndex}
        onScroll={onScroll}
        className="flex-1"
      />
    </div>
  );
}
