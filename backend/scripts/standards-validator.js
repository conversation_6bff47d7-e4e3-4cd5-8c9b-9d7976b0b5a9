/**
 * 数据库设计规范验证工具
 * 自动检查数据库结构是否符合项目规范
 */

const fs = require('fs');
const path = require('path');

class DatabaseStandardsValidator {
  constructor() {
    this.errors = [];
    this.warnings = [];
    this.passed = [];
    
    // 规范定义
    this.standards = {
      // 命名规范
      naming: {
        tablePattern: /^[a-z]+_[a-z_]+$/,
        fieldPattern: /^[a-z_]+$/,
        indexPattern: /^idx_[a-z_]+$/,
        uuidPattern: /^[a-z]+_[0-9a-f]{8}-[0-9a-f]{4}-4[0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i
      },
      
      // 必需字段
      requiredFields: [
        'id',
        'created_at',
        'updated_at',
        'is_deleted',
        'metadata',
        'version',
        'schema_version',
        'is_test_data'
      ],
      
      // 推荐字段
      recommendedFields: [
        'status',
        'tags',
        'extra_data',
        'created_by',
        'updated_by'
      ],
      
      // 必需索引
      requiredIndexes: [
        'status',
        'created_at',
        'is_deleted'
      ]
    };
  }

  /**
   * 验证数据库结构
   */
  async validateDatabase(schemaPath) {
    console.log('🔍 开始验证数据库设计规范...');
    
    try {
      const schema = await this.parseSchema(schemaPath);
      
      // 验证各个方面
      this.validateTableNaming(schema.tables);
      this.validateFieldNaming(schema.tables);
      this.validateRequiredFields(schema.tables);
      this.validateIndexes(schema.indexes);
      this.validateForeignKeys(schema.foreignKeys);
      this.validateDataTypes(schema.tables);
      
      // 生成报告
      this.generateReport();
      
      return {
        success: this.errors.length === 0,
        errors: this.errors,
        warnings: this.warnings,
        passed: this.passed
      };
      
    } catch (error) {
      console.error('❌ 验证过程中出错:', error);
      return { success: false, error: error.message };
    }
  }

  /**
   * 解析数据库结构
   */
  async parseSchema(schemaPath) {
    const schemaContent = fs.readFileSync(schemaPath, 'utf8');
    
    // 简单的SQL解析 (实际项目中可能需要更复杂的解析器)
    const tables = this.extractTables(schemaContent);
    const indexes = this.extractIndexes(schemaContent);
    const foreignKeys = this.extractForeignKeys(schemaContent);
    
    return { tables, indexes, foreignKeys };
  }

  /**
   * 提取表结构
   */
  extractTables(schemaContent) {
    const tables = {};
    const tableRegex = /CREATE TABLE (\w+) \(([\s\S]*?)\);/g;
    
    let match;
    while ((match = tableRegex.exec(schemaContent)) !== null) {
      const tableName = match[1];
      const tableContent = match[2];
      
      tables[tableName] = {
        name: tableName,
        fields: this.extractFields(tableContent)
      };
    }
    
    return tables;
  }

  /**
   * 提取字段信息
   */
  extractFields(tableContent) {
    const fields = {};
    const lines = tableContent.split('\n');
    
    for (const line of lines) {
      const trimmed = line.trim();
      if (trimmed && !trimmed.startsWith('--') && !trimmed.startsWith('FOREIGN KEY')) {
        const fieldMatch = trimmed.match(/^(\w+)\s+(\w+)/);
        if (fieldMatch) {
          const [, fieldName, fieldType] = fieldMatch;
          fields[fieldName] = {
            name: fieldName,
            type: fieldType,
            definition: trimmed
          };
        }
      }
    }
    
    return fields;
  }

  /**
   * 提取索引信息
   */
  extractIndexes(schemaContent) {
    const indexes = [];
    const indexRegex = /CREATE (?:UNIQUE )?INDEX (\w+) ON (\w+)\((.*?)\);/g;
    
    let match;
    while ((match = indexRegex.exec(schemaContent)) !== null) {
      indexes.push({
        name: match[1],
        table: match[2],
        columns: match[3].split(',').map(col => col.trim())
      });
    }
    
    return indexes;
  }

  /**
   * 提取外键信息
   */
  extractForeignKeys(schemaContent) {
    const foreignKeys = [];
    const fkRegex = /FOREIGN KEY \((\w+)\) REFERENCES (\w+)\((\w+)\)/g;
    
    let match;
    while ((match = fkRegex.exec(schemaContent)) !== null) {
      foreignKeys.push({
        column: match[1],
        referencedTable: match[2],
        referencedColumn: match[3]
      });
    }
    
    return foreignKeys;
  }

  /**
   * 验证表命名规范
   */
  validateTableNaming(tables) {
    console.log('📋 验证表命名规范...');
    
    for (const [tableName, table] of Object.entries(tables)) {
      if (!this.standards.naming.tablePattern.test(tableName)) {
        this.errors.push({
          type: 'naming',
          severity: 'error',
          message: `表名 "${tableName}" 不符合命名规范 (应为: module_entity 格式)`,
          suggestion: '使用 module_entity 格式，如: user_profiles, content_metadata'
        });
      } else {
        this.passed.push(`表名 "${tableName}" 符合命名规范`);
      }
    }
  }

  /**
   * 验证字段命名规范
   */
  validateFieldNaming(tables) {
    console.log('🏷️ 验证字段命名规范...');
    
    for (const [tableName, table] of Object.entries(tables)) {
      for (const [fieldName, field] of Object.entries(table.fields)) {
        if (!this.standards.naming.fieldPattern.test(fieldName)) {
          this.errors.push({
            type: 'naming',
            severity: 'error',
            message: `表 "${tableName}" 中字段 "${fieldName}" 不符合命名规范`,
            suggestion: '使用小写字母和下划线，如: user_id, created_at'
          });
        }
      }
    }
  }

  /**
   * 验证必需字段
   */
  validateRequiredFields(tables) {
    console.log('✅ 验证必需字段...');
    
    for (const [tableName, table] of Object.entries(tables)) {
      // 检查必需字段
      for (const requiredField of this.standards.requiredFields) {
        if (!table.fields[requiredField]) {
          this.errors.push({
            type: 'required_field',
            severity: 'error',
            message: `表 "${tableName}" 缺少必需字段 "${requiredField}"`,
            suggestion: `添加字段: ${requiredField} ${this.getFieldDefinition(requiredField)}`
          });
        } else {
          this.passed.push(`表 "${tableName}" 包含必需字段 "${requiredField}"`);
        }
      }
      
      // 检查推荐字段
      for (const recommendedField of this.standards.recommendedFields) {
        if (!table.fields[recommendedField]) {
          this.warnings.push({
            type: 'recommended_field',
            severity: 'warning',
            message: `表 "${tableName}" 建议添加字段 "${recommendedField}"`,
            suggestion: `添加字段: ${recommendedField} ${this.getFieldDefinition(recommendedField)}`
          });
        }
      }
    }
  }

  /**
   * 验证索引
   */
  validateIndexes(indexes) {
    console.log('🔍 验证索引设计...');
    
    // 按表分组索引
    const indexesByTable = {};
    for (const index of indexes) {
      if (!indexesByTable[index.table]) {
        indexesByTable[index.table] = [];
      }
      indexesByTable[index.table].push(index);
    }
    
    // 检查每个表的必需索引
    for (const [tableName, tableIndexes] of Object.entries(indexesByTable)) {
      for (const requiredIndex of this.standards.requiredIndexes) {
        const hasIndex = tableIndexes.some(index => 
          index.columns.some(col => col.includes(requiredIndex))
        );
        
        if (!hasIndex) {
          this.warnings.push({
            type: 'missing_index',
            severity: 'warning',
            message: `表 "${tableName}" 建议为字段 "${requiredIndex}" 创建索引`,
            suggestion: `CREATE INDEX idx_${tableName}_${requiredIndex} ON ${tableName}(${requiredIndex});`
          });
        } else {
          this.passed.push(`表 "${tableName}" 已为 "${requiredIndex}" 创建索引`);
        }
      }
    }
  }

  /**
   * 验证外键约束
   */
  validateForeignKeys(foreignKeys) {
    console.log('🔗 验证外键约束...');
    
    for (const fk of foreignKeys) {
      // 检查外键命名规范
      if (!fk.column.endsWith('_id')) {
        this.warnings.push({
          type: 'foreign_key_naming',
          severity: 'warning',
          message: `外键字段 "${fk.column}" 建议使用 "_id" 后缀`,
          suggestion: `重命名为: ${fk.referencedTable}_id`
        });
      } else {
        this.passed.push(`外键 "${fk.column}" 命名规范正确`);
      }
    }
  }

  /**
   * 验证数据类型
   */
  validateDataTypes(tables) {
    console.log('🎯 验证数据类型...');
    
    const typeStandards = {
      'id': 'TEXT',
      'created_at': 'DATETIME',
      'updated_at': 'DATETIME',
      'is_deleted': 'BOOLEAN',
      'is_test_data': 'BOOLEAN',
      'version': 'INTEGER',
      'metadata': 'TEXT',
      'tags': 'TEXT',
      'extra_data': 'TEXT'
    };
    
    for (const [tableName, table] of Object.entries(tables)) {
      for (const [fieldName, expectedType] of Object.entries(typeStandards)) {
        const field = table.fields[fieldName];
        if (field && !field.type.includes(expectedType)) {
          this.errors.push({
            type: 'data_type',
            severity: 'error',
            message: `表 "${tableName}" 字段 "${fieldName}" 类型应为 "${expectedType}"，当前为 "${field.type}"`,
            suggestion: `修改字段类型: ${fieldName} ${expectedType}`
          });
        }
      }
    }
  }

  /**
   * 获取字段定义
   */
  getFieldDefinition(fieldName) {
    const definitions = {
      'id': 'TEXT PRIMARY KEY',
      'created_at': 'DATETIME DEFAULT CURRENT_TIMESTAMP',
      'updated_at': 'DATETIME DEFAULT CURRENT_TIMESTAMP',
      'is_deleted': 'BOOLEAN DEFAULT false',
      'metadata': 'TEXT',
      'tags': 'TEXT',
      'extra_data': 'TEXT',
      'version': 'INTEGER DEFAULT 1',
      'schema_version': 'TEXT DEFAULT "1.0"',
      'is_test_data': 'BOOLEAN DEFAULT false',
      'status': 'TEXT DEFAULT "active"',
      'created_by': 'TEXT',
      'updated_by': 'TEXT'
    };
    
    return definitions[fieldName] || 'TEXT';
  }

  /**
   * 生成验证报告
   */
  generateReport() {
    console.log('\n📊 验证报告');
    console.log('='.repeat(50));
    
    // 统计信息
    console.log(`✅ 通过检查: ${this.passed.length}`);
    console.log(`⚠️  警告: ${this.warnings.length}`);
    console.log(`❌ 错误: ${this.errors.length}`);
    console.log('');
    
    // 错误详情
    if (this.errors.length > 0) {
      console.log('❌ 错误详情:');
      this.errors.forEach((error, index) => {
        console.log(`${index + 1}. ${error.message}`);
        console.log(`   建议: ${error.suggestion}`);
        console.log('');
      });
    }
    
    // 警告详情
    if (this.warnings.length > 0) {
      console.log('⚠️  警告详情:');
      this.warnings.forEach((warning, index) => {
        console.log(`${index + 1}. ${warning.message}`);
        console.log(`   建议: ${warning.suggestion}`);
        console.log('');
      });
    }
    
    // 总结
    if (this.errors.length === 0) {
      console.log('🎉 恭喜！数据库设计符合项目规范！');
    } else {
      console.log('🔧 请修复上述错误后重新验证。');
    }
  }

  /**
   * 生成修复脚本
   */
  generateFixScript() {
    const fixes = [];
    
    // 生成字段添加脚本
    for (const error of this.errors) {
      if (error.type === 'required_field') {
        const tableName = error.message.match(/表 "(\w+)"/)[1];
        const fieldName = error.message.match(/字段 "(\w+)"/)[1];
        const fieldDef = this.getFieldDefinition(fieldName);
        
        fixes.push(`ALTER TABLE ${tableName} ADD COLUMN ${fieldName} ${fieldDef};`);
      }
    }
    
    // 生成索引创建脚本
    for (const warning of this.warnings) {
      if (warning.type === 'missing_index') {
        fixes.push(warning.suggestion);
      }
    }
    
    if (fixes.length > 0) {
      const fixScript = `-- 数据库规范修复脚本
-- 生成时间: ${new Date().toISOString()}

BEGIN TRANSACTION;

${fixes.join('\n')}

COMMIT;
`;
      
      fs.writeFileSync('database-fixes.sql', fixScript);
      console.log('📝 修复脚本已生成: database-fixes.sql');
    }
  }
}

/**
 * 使用示例
 */
async function validateProjectDatabase() {
  const validator = new DatabaseStandardsValidator();
  
  // 验证优化后的schema
  const result = await validator.validateDatabase('./prisma/schema_optimized.prisma');
  
  if (!result.success) {
    // 生成修复脚本
    validator.generateFixScript();
  }
  
  return result;
}

// 命令行执行
if (require.main === module) {
  const schemaPath = process.argv[2] || './prisma/schema_optimized.prisma';
  
  const validator = new DatabaseStandardsValidator();
  validator.validateDatabase(schemaPath)
    .then(result => {
      process.exit(result.success ? 0 : 1);
    })
    .catch(error => {
      console.error('验证失败:', error);
      process.exit(1);
    });
}

module.exports = DatabaseStandardsValidator;
