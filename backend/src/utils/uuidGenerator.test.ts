/**
 * UUID生成工具测试
 */

import { generateUUID, isValidA, isValidB, verifyUUID } from './uuidGenerator';

describe('UUID Generator', () => {
  const testSalt = 'test-salt-value';

  describe('isValidA', () => {
    it('should return true for valid A values', () => {
      expect(isValidA('12345678901')).toBe(true);
      expect(isValidA('00000000000')).toBe(true);
    });

    it('should return false for invalid A values', () => {
      expect(isValidA('1234567890')).toBe(false); // 10位，太短
      expect(isValidA('123456789012')).toBe(false); // 12位，太长
      expect(isValidA('1234567890a')).toBe(false); // 包含非数字
      expect(isValidA('')).toBe(false); // 空字符串
    });
  });

  describe('isValidB', () => {
    it('should return true for valid B values', () => {
      expect(isValidB('1234')).toBe(true); // 4位
      expect(isValidB('123456')).toBe(true); // 6位
      expect(isValidB('0000')).toBe(true);
      expect(isValidB('000000')).toBe(true);
    });

    it('should return false for invalid B values', () => {
      expect(isValidB('123')).toBe(false); // 3位，太短
      expect(isValidB('12345')).toBe(false); // 5位，不是4位或6位
      expect(isValidB('1234567')).toBe(false); // 7位，太长
      expect(isValidB('123a')).toBe(false); // 包含非数字
      expect(isValidB('')).toBe(false); // 空字符串
    });
  });

  describe('generateUUID', () => {
    it('should generate consistent UUIDs for the same input', () => {
      const uuid1 = generateUUID('12345678901', '1234', testSalt);
      const uuid2 = generateUUID('12345678901', '1234', testSalt);
      expect(uuid1).toBe(uuid2);
    });

    it('should generate different UUIDs for different inputs', () => {
      const uuid1 = generateUUID('12345678901', '1234', testSalt);
      const uuid2 = generateUUID('12345678901', '5678', testSalt);
      const uuid3 = generateUUID('10987654321', '1234', testSalt);
      expect(uuid1).not.toBe(uuid2);
      expect(uuid1).not.toBe(uuid3);
      expect(uuid2).not.toBe(uuid3);
    });

    it('should generate different UUIDs with different salts', () => {
      const uuid1 = generateUUID('12345678901', '1234', testSalt);
      const uuid2 = generateUUID('12345678901', '1234', 'different-salt');
      expect(uuid1).not.toBe(uuid2);
    });

    it('should throw error for invalid inputs', () => {
      expect(() => generateUUID('1234567890', '1234', testSalt)).toThrow();
      expect(() => generateUUID('12345678901', '123', testSalt)).toThrow();
    });

    it('should return a string of length 32', () => {
      const uuid = generateUUID('12345678901', '1234', testSalt);
      expect(uuid.length).toBe(32);
    });
  });

  describe('verifyUUID', () => {
    it('should verify correct UUID matches', () => {
      const a = '12345678901';
      const b = '1234';
      const uuid = generateUUID(a, b, testSalt);
      expect(verifyUUID(uuid, a, b, testSalt)).toBe(true);
    });

    it('should reject incorrect UUID matches', () => {
      const uuid = generateUUID('12345678901', '1234', testSalt);
      expect(verifyUUID(uuid, '12345678901', '5678', testSalt)).toBe(false);
      expect(verifyUUID(uuid, '10987654321', '1234', testSalt)).toBe(false);
    });

    it('should handle invalid inputs gracefully', () => {
      const uuid = generateUUID('12345678901', '1234', testSalt);
      expect(verifyUUID(uuid, '1234567890', '1234', testSalt)).toBe(false);
      expect(verifyUUID(uuid, '12345678901', '123', testSalt)).toBe(false);
    });
  });
});
