/**
 * 🧪 端到端API测试
 * 测试实际的API端点响应
 */

const API_BASE_URL = process.env.API_BASE_URL || 'https://college-employment-survey.aibook2099.workers.dev';

describe('E2E API Tests', () => {
  // 辅助函数：发送HTTP请求
  const makeRequest = async (endpoint, options = {}) => {
    const url = `${API_BASE_URL}${endpoint}`;
    const defaultOptions = {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
        'User-Agent': 'Jest-E2E-Test'
      }
    };

    const response = await fetch(url, { ...defaultOptions, ...options });
    const data = await response.json();

    return {
      status: response.status,
      statusText: response.statusText,
      headers: response.headers,
      data
    };
  };

  describe('Health Check Endpoints', () => {
    test('GET / should return API information', async () => {
      const response = await makeRequest('/');

      expect(response.status).toBe(200);
      expect(response.data).toMatchObject({
        success: true,
        message: expect.any(String),
        data: expect.objectContaining({
          name: 'College Employment Survey API',
          version: expect.stringMatching(/v\d+\.\d+/),
          architecture: 'modular',
          modules: expect.arrayContaining([
            'questionnaire',
            'story',
            'admin',
            'deidentification',
            'system'
          ])
        }),
        meta: expect.objectContaining({
          timestamp: expect.any(String)
        })
      });
    });

    test('GET /health should return system health', async () => {
      const response = await makeRequest('/health');

      expect(response.status).toBe(200);
      expect(response.data).toMatchObject({
        status: 'ok',
        version: expect.any(String),
        checks: expect.objectContaining({
          database: expect.any(String),
          memory: expect.any(String)
        }),
        timestamp: expect.any(String)
      });
    });

    test('GET /api/system/health should return detailed health check', async () => {
      const response = await makeRequest('/api/system/health');

      expect(response.status).toBe(200);
      expect(response.data).toMatchObject({
        status: 'ok',
        version: expect.any(String),
        checks: expect.any(Object),
        timestamp: expect.any(String)
      });
    });
  });

  describe('Questionnaire API', () => {
    test('GET /api/questionnaire/stats should return statistics', async () => {
      const response = await makeRequest('/api/questionnaire/stats');

      expect(response.status).toBe(200);
      // 基本结构检查
      expect(response.data.success).toBe(true);
      expect(response.data.message).toBeDefined();
      expect(response.data.meta).toBeDefined();
      expect(response.data.meta.statistics).toBeDefined();

      // 统计数据检查
      const stats = response.data.meta.statistics;
      expect(typeof stats.totalResponses).toBe('number');
      expect(Array.isArray(stats.majors)).toBe(true);
      expect(Array.isArray(stats.graduationYears)).toBe(true);
      expect(Array.isArray(stats.educationLevels)).toBe(true);
      expect(typeof stats.lastUpdated).toBe('string');

      // 验证数据质量
      expect(stats.totalResponses).toBeGreaterThan(0);
      expect(stats.majors.length).toBeGreaterThan(0);

      // 验证专业数据格式
      if (stats.majors.length > 0) {
        expect(stats.majors[0]).toMatchObject({
          name: expect.any(String),
          count: expect.any(Number)
        });
      }
    }, 10000); // 增加超时时间

    test('should handle CORS headers correctly', async () => {
      const response = await makeRequest('/api/questionnaire/stats', {
        headers: {
          'Origin': 'https://b95169fc.college-employment-survey.pages.dev'
        }
      });

      expect(response.status).toBe(200);
      // CORS头部应该被正确设置（由Cloudflare Workers处理）
    });
  });

  describe('Story API', () => {
    test('GET /api/story/list should return paginated stories', async () => {
      const response = await makeRequest('/api/story/list?page=1&pageSize=5');

      expect(response.status).toBe(200);
      // 基本结构检查
      expect(response.data.success).toBe(true);
      expect(response.data.message).toBeDefined();
      expect(Array.isArray(response.data.data)).toBe(true);

      // 验证分页信息（如果存在）
      if (response.data.meta && response.data.meta.pagination) {
        expect(response.data.meta.pagination).toMatchObject({
          total: expect.any(Number),
          page: 1,
          limit: expect.any(Number), // 允许不同的limit值
          pages: expect.any(Number),
          hasNext: expect.any(Boolean),
          hasPrev: false
        });
      }

      // 验证分页逻辑
      const pagination = response.data.meta.pagination;
      expect(pagination.page).toBe(1);
      expect(pagination.limit).toBe(5);
      expect(pagination.hasPrev).toBe(false);

      if (pagination.total > 5) {
        expect(pagination.hasNext).toBe(true);
        expect(pagination.pages).toBeGreaterThan(1);
      }
    }, 10000);

    test('should handle pagination parameters correctly', async () => {
      const response = await makeRequest('/api/story/list?page=2&pageSize=3');

      expect(response.status).toBe(200);
      const pagination = response.data.meta.pagination;
      expect(pagination.page).toBe(2);
      expect(pagination.limit).toBe(3);
      expect(pagination.hasPrev).toBe(true);
    });

    test('should validate pagination limits', async () => {
      const response = await makeRequest('/api/story/list?page=1&pageSize=1000');

      expect(response.status).toBe(200);
      const pagination = response.data.meta.pagination;
      expect(pagination.limit).toBeLessThanOrEqual(20); // 应该被限制在最大值
    });
  });

  describe('Error Handling', () => {
    test('should return 404 for non-existent endpoints', async () => {
      const response = await makeRequest('/api/non-existent-endpoint');

      expect(response.status).toBe(404);
      // 基本结构检查
      expect(response.data.success).toBe(false);
      expect(response.data.error).toBeDefined();
      expect(response.data.code).toBe(404);
      expect(response.data.details).toBeDefined();
      expect(Array.isArray(response.data.details.availableEndpoints)).toBe(true);
    });

    test('should handle malformed requests gracefully', async () => {
      const response = await makeRequest('/api/questionnaire/stats', {
        method: 'POST',
        body: 'invalid-json'
      });

      // 我们的API对于不支持的方法返回404，这是正常的
      // 或者返回其他错误状态码
      expect([200, 400, 404, 405, 500].includes(response.status)).toBe(true);
    });
  });

  describe('Performance Tests', () => {
    test('API responses should be fast', async () => {
      const startTime = Date.now();
      const response = await makeRequest('/api/questionnaire/stats');
      const endTime = Date.now();
      const responseTime = endTime - startTime;

      expect(response.status).toBe(200);
      expect(responseTime).toBeLessThan(5000); // 应该在5秒内响应
    }, 10000);

    test('Health check should be very fast', async () => {
      const startTime = Date.now();
      const response = await makeRequest('/health');
      const endTime = Date.now();
      const responseTime = endTime - startTime;

      expect(response.status).toBe(200);
      expect(responseTime).toBeLessThan(1000); // 健康检查应该在1秒内响应
    });
  });

  describe('Data Consistency', () => {
    test('statistics should be consistent across multiple requests', async () => {
      const response1 = await makeRequest('/api/questionnaire/stats');
      const response2 = await makeRequest('/api/questionnaire/stats');

      expect(response1.status).toBe(200);
      expect(response2.status).toBe(200);

      const stats1 = response1.data.meta.statistics;
      const stats2 = response2.data.meta.statistics;

      // 总响应数应该一致（假设没有新数据写入）
      expect(stats1.totalResponses).toBe(stats2.totalResponses);
      expect(stats1.majors.length).toBe(stats2.majors.length);
    });

    test('pagination should be mathematically correct', async () => {
      const response = await makeRequest('/api/story/list?page=1&pageSize=10');

      if (response.status === 200) {
        const pagination = response.data.meta.pagination;
        const expectedPages = Math.ceil(pagination.total / pagination.limit);

        expect(pagination.pages).toBe(expectedPages);
        expect(pagination.hasNext).toBe(pagination.page < pagination.pages);
        expect(pagination.hasPrev).toBe(pagination.page > 1);
      }
    });
  });
});
