import React from 'react';
import { AlertCircle, RefreshCw } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import { cn } from '@/lib/utils';

interface ErrorDisplayProps {
  title?: string;
  message: string;
  onRetry?: () => void;
  fullPage?: boolean;
  className?: string;
}

export function ErrorDisplay({
  title = '发生错误',
  message,
  onRetry,
  fullPage = false,
  className
}: ErrorDisplayProps) {
  const errorDisplay = (
    <div className={cn('w-full', className)}>
      <Alert variant="destructive">
        <AlertCircle className="h-4 w-4" />
        <AlertTitle>{title}</AlertTitle>
        <AlertDescription>{message}</AlertDescription>
        {onRetry && (
          <div className="mt-4">
            <Button
              variant="outline"
              size="sm"
              onClick={onRetry}
              className="flex items-center gap-1"
            >
              <RefreshCw className="h-3 w-3" />
              重试
            </Button>
          </div>
        )}
      </Alert>
    </div>
  );

  if (fullPage) {
    return (
      <div className="fixed inset-0 flex items-center justify-center bg-background/80 backdrop-blur-sm z-50 p-4">
        <div className="max-w-md w-full">{errorDisplay}</div>
      </div>
    );
  }

  return errorDisplay;
}

export default ErrorDisplay;
