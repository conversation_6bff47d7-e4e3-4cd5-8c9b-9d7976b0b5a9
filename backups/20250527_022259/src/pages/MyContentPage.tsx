import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { useToast } from '@/components/ui/use-toast';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Badge } from '@/components/ui/badge';
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from '@/components/ui/alert-dialog';
import { Eye, EyeOff, Trash2, ArrowLeft, FileText, BookO<PERSON>, Clock, AlertTriangle } from 'lucide-react';
import { getMyContent, deleteStory, deleteQuestionnaireResponse, validateIdentity } from '@/services/anonymousAuthService';
import { formatDistanceToNow } from 'date-fns';
import { zhCN } from 'date-fns/locale';

/**
 * 我的内容页面
 *
 * 允许用户通过A+B组合查询和管理自己的内容
 */
export default function MyContentPage() {
  const navigate = useNavigate();
  const { toast } = useToast();

  // 身份验证状态
  const [identityA, setIdentityA] = useState('');
  const [identityB, setIdentityB] = useState('');
  const [showB, setShowB] = useState(false);
  const [isAuthenticated, setIsAuthenticated] = useState(false);

  // 内容状态
  const [isLoading, setIsLoading] = useState(false);
  const [stories, setStories] = useState<any[]>([]);
  const [questionnaireResponses, setQuestionnaireResponses] = useState<any[]>([]);
  const [pendingContents, setPendingContents] = useState<any[]>([]);

  // 删除对话框状态
  const [deleteType, setDeleteType] = useState<'story' | 'questionnaire' | null>(null);
  const [deleteId, setDeleteId] = useState<number | null>(null);
  const [isDeleting, setIsDeleting] = useState(false);

  // 处理A值变化
  const handleAChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value.replace(/\D/g, '').slice(0, 11);
    setIdentityA(value);
  };

  // 处理B值变化
  const handleBChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value.replace(/\D/g, '').slice(0, 6);
    setIdentityB(value);
  };

  // 验证身份
  const handleAuthenticate = async () => {
    if (!validateIdentity(identityA, identityB)) {
      toast({
        title: '输入错误',
        description: '请输入有效的A值（11位数字）和B值（4位或6位数字）',
        variant: 'destructive'
      });
      return;
    }

    setIsLoading(true);

    try {
      const response = await getMyContent(identityA, identityB);

      if (response.success) {
        setStories(response.data.stories || []);
        setQuestionnaireResponses(response.data.questionnaireResponses || []);
        setPendingContents(response.data.pendingContents || []);
        setIsAuthenticated(true);

        toast({
          title: '验证成功',
          description: '已获取您的内容',
          variant: 'default'
        });
      } else {
        toast({
          title: '获取内容失败',
          description: response.error || '请稍后再试',
          variant: 'destructive'
        });
      }
    } catch (error) {
      console.error('验证身份失败:', error);
      toast({
        title: '验证失败',
        description: '服务器错误，请稍后再试',
        variant: 'destructive'
      });
    } finally {
      setIsLoading(false);
    }
  };

  // 处理删除故事
  const handleDeleteStory = async () => {
    if (!deleteId) return;

    setIsDeleting(true);

    try {
      const response = await deleteStory(identityA, identityB, deleteId);

      if (response.success) {
        // 更新故事列表
        setStories(stories.filter(story => story.id !== deleteId));

        toast({
          title: '删除成功',
          description: '故事已成功删除',
          variant: 'default'
        });
      } else {
        toast({
          title: '删除失败',
          description: response.error || '请稍后再试',
          variant: 'destructive'
        });
      }
    } catch (error) {
      console.error('删除故事失败:', error);
      toast({
        title: '删除失败',
        description: '服务器错误，请稍后再试',
        variant: 'destructive'
      });
    } finally {
      setIsDeleting(false);
      setDeleteType(null);
      setDeleteId(null);
    }
  };

  // 处理删除问卷回复
  const handleDeleteQuestionnaireResponse = async () => {
    if (!deleteId) return;

    setIsDeleting(true);

    try {
      const response = await deleteQuestionnaireResponse(identityA, identityB, deleteId);

      if (response.success) {
        // 更新问卷回复列表
        setQuestionnaireResponses(questionnaireResponses.filter(response => response.id !== deleteId));

        toast({
          title: '删除成功',
          description: '问卷回复已成功删除',
          variant: 'default'
        });
      } else {
        toast({
          title: '删除失败',
          description: response.error || '请稍后再试',
          variant: 'destructive'
        });
      }
    } catch (error) {
      console.error('删除问卷回复失败:', error);
      toast({
        title: '删除失败',
        description: '服务器错误，请稍后再试',
        variant: 'destructive'
      });
    } finally {
      setIsDeleting(false);
      setDeleteType(null);
      setDeleteId(null);
    }
  };

  // 处理删除确认
  const handleDeleteConfirm = () => {
    if (deleteType === 'story') {
      handleDeleteStory();
    } else if (deleteType === 'questionnaire') {
      handleDeleteQuestionnaireResponse();
    }
  };

  // 自动填充测试用的A+B组合
  const fillTestCredentials = () => {
    setIdentityA('12345678901');
    setIdentityB('6789');

    // 显示提示信息
    toast({
      title: '测试账号已填充',
      description: '已自动填充测试账号信息，点击"验证身份"按钮继续',
      variant: 'default'
    });
  };

  // 渲染身份验证表单
  const renderAuthForm = () => {
    return (
      <Card className="max-w-md mx-auto">
        <CardHeader>
          <CardTitle>匿名身份验证</CardTitle>
          <CardDescription>
            请输入您的A+B组合以查询和管理您的内容
          </CardDescription>
        </CardHeader>

        <CardContent className="space-y-4">
          <div className="space-y-2">
            <Label htmlFor="identity-a">A值（11位数字，如手机号）</Label>
            <Input
              id="identity-a"
              type="text"
              inputMode="numeric"
              placeholder="请输入11位数字"
              value={identityA}
              onChange={handleAChange}
            />
          </div>

          <div className="space-y-2">
            <Label htmlFor="identity-b">B值（4位或6位数字密码）</Label>
            <div className="relative">
              <Input
                id="identity-b"
                type={showB ? "text" : "password"}
                inputMode="numeric"
                placeholder="请输入4位或6位数字"
                value={identityB}
                onChange={handleBChange}
                className="pr-10"
              />
              <Button
                type="button"
                variant="ghost"
                size="icon"
                className="absolute right-0 top-0 h-full"
                onClick={() => setShowB(!showB)}
              >
                {showB ? <EyeOff className="h-4 w-4" /> : <Eye className="h-4 w-4" />}
              </Button>
            </div>
          </div>

          {/* 本地开发环境中显示测试按钮 */}
          {import.meta.env.DEV && (
            <div className="pt-4 border-t mt-4">
              <h4 className="text-sm font-medium mb-2 text-center">测试账号</h4>
              <Button
                type="button"
                variant="default"
                size="sm"
                className="w-full bg-green-600 hover:bg-green-700 text-white"
                onClick={fillTestCredentials}
              >
                使用测试账号 (仅开发环境)
              </Button>
              <p className="text-xs text-muted-foreground mt-2 text-center">
                测试A+B组合: 12345678901 + 6789
              </p>
              <p className="text-xs text-muted-foreground mt-1 text-center">
                (点击按钮自动填充，然后点击"验证身份"按钮)
              </p>
            </div>
          )}
        </CardContent>

        <CardFooter className="flex justify-between">
          <Button
            variant="outline"
            onClick={() => navigate('/')}
          >
            <ArrowLeft className="h-4 w-4 mr-2" />
            返回首页
          </Button>

          <Button
            onClick={handleAuthenticate}
            disabled={isLoading}
          >
            {isLoading ? '验证中...' : '验证身份'}
          </Button>
        </CardFooter>
      </Card>
    );
  };

  // 渲染内容列表
  const renderContentList = () => {
    const totalItems = stories.length + questionnaireResponses.length + pendingContents.length;

    return (
      <div className="space-y-6">
        <div className="flex justify-between items-center">
          <h1 className="text-2xl font-bold">我的内容</h1>

          <Button
            variant="outline"
            onClick={() => navigate('/')}
          >
            <ArrowLeft className="h-4 w-4 mr-2" />
            返回首页
          </Button>
        </div>

        {totalItems === 0 ? (
          <Card>
            <CardContent className="flex flex-col items-center justify-center py-12">
              <AlertTriangle className="h-12 w-12 text-muted-foreground mb-4" />
              <h3 className="text-xl font-medium">未找到内容</h3>
              <p className="text-muted-foreground mt-2">
                您尚未使用此匿名身份标识提交任何内容，或者输入的A+B组合不正确
              </p>
            </CardContent>
          </Card>
        ) : (
          <Tabs defaultValue="stories">
            <TabsList>
              <TabsTrigger value="stories" className="flex items-center">
                <BookOpen className="h-4 w-4 mr-2" />
                故事 ({stories.length})
              </TabsTrigger>
              <TabsTrigger value="questionnaires" className="flex items-center">
                <FileText className="h-4 w-4 mr-2" />
                问卷 ({questionnaireResponses.length})
              </TabsTrigger>
              <TabsTrigger value="pending" className="flex items-center">
                <Clock className="h-4 w-4 mr-2" />
                待审核 ({pendingContents.length})
              </TabsTrigger>
            </TabsList>

            <TabsContent value="stories" className="space-y-4 mt-4">
              {stories.length === 0 ? (
                <p className="text-center text-muted-foreground py-8">
                  您尚未提交任何故事
                </p>
              ) : (
                stories.map(story => (
                  <Card key={story.id}>
                    <CardHeader className="pb-2">
                      <div className="flex justify-between items-start">
                        <CardTitle className="text-lg">{story.title}</CardTitle>
                        <Badge variant={story.status === 'approved' ? 'default' : 'outline'}>
                          {story.status === 'approved' ? '已通过' :
                           story.status === 'rejected' ? '已拒绝' :
                           story.status === 'pending' ? '审核中' : '已编辑'}
                        </Badge>
                      </div>
                      <CardDescription className="flex items-center">
                        <Clock className="h-3 w-3 mr-1" />
                        {formatDistanceToNow(new Date(story.createdAt), { addSuffix: true, locale: zhCN })}
                      </CardDescription>
                    </CardHeader>

                    <CardContent className="pb-2">
                      <p className="line-clamp-3">{story.content}</p>
                    </CardContent>

                    <CardFooter className="pt-2">
                      <div className="flex justify-between items-center w-full">
                        <div className="text-sm text-muted-foreground">
                          {story.likes} 点赞 · {story.dislikes} 不喜欢
                        </div>

                        <AlertDialog>
                          <AlertDialogTrigger asChild>
                            <Button
                              variant="destructive"
                              size="sm"
                              onClick={() => {
                                setDeleteType('story');
                                setDeleteId(story.id);
                              }}
                            >
                              <Trash2 className="h-4 w-4 mr-2" />
                              删除
                            </Button>
                          </AlertDialogTrigger>
                        </AlertDialog>
                      </div>
                    </CardFooter>
                  </Card>
                ))
              )}
            </TabsContent>

            <TabsContent value="questionnaires" className="space-y-4 mt-4">
              {questionnaireResponses.length === 0 ? (
                <p className="text-center text-muted-foreground py-8">
                  您尚未提交任何问卷
                </p>
              ) : (
                questionnaireResponses.map(response => (
                  <Card key={response.id}>
                    <CardHeader className="pb-2">
                      <div className="flex justify-between items-start">
                        <CardTitle className="text-lg">问卷回复</CardTitle>
                      </div>
                      <CardDescription className="flex items-center">
                        <Clock className="h-3 w-3 mr-1" />
                        {formatDistanceToNow(new Date(response.createdAt), { addSuffix: true, locale: zhCN })}
                      </CardDescription>
                    </CardHeader>

                    <CardContent className="pb-2">
                      <div className="grid grid-cols-2 gap-2 text-sm">
                        {response.educationLevel && (
                          <div>
                            <span className="text-muted-foreground">学历：</span>
                            <span>{response.educationLevel}</span>
                          </div>
                        )}

                        {response.major && (
                          <div>
                            <span className="text-muted-foreground">专业：</span>
                            <span>{response.major}</span>
                          </div>
                        )}

                        {response.graduationYear && (
                          <div>
                            <span className="text-muted-foreground">毕业年份：</span>
                            <span>{response.graduationYear}</span>
                          </div>
                        )}

                        {response.region && (
                          <div>
                            <span className="text-muted-foreground">地区：</span>
                            <span>{response.region}</span>
                          </div>
                        )}

                        {response.employmentStatus && (
                          <div>
                            <span className="text-muted-foreground">就业状态：</span>
                            <span>{response.employmentStatus}</span>
                          </div>
                        )}
                      </div>
                    </CardContent>

                    <CardFooter className="pt-2">
                      <div className="flex justify-end items-center w-full">
                        <AlertDialog>
                          <AlertDialogTrigger asChild>
                            <Button
                              variant="destructive"
                              size="sm"
                              onClick={() => {
                                setDeleteType('questionnaire');
                                setDeleteId(response.id);
                              }}
                            >
                              <Trash2 className="h-4 w-4 mr-2" />
                              删除
                            </Button>
                          </AlertDialogTrigger>
                        </AlertDialog>
                      </div>
                    </CardFooter>
                  </Card>
                ))
              )}
            </TabsContent>

            <TabsContent value="pending" className="space-y-4 mt-4">
              {pendingContents.length === 0 ? (
                <p className="text-center text-muted-foreground py-8">
                  您没有待审核的内容
                </p>
              ) : (
                pendingContents.map(content => (
                  <Card key={content.id}>
                    <CardHeader className="pb-2">
                      <div className="flex justify-between items-start">
                        <CardTitle className="text-lg">
                          {content.type === 'story' ? '故事' : '问卷回复'} #{content.sequenceNumber}
                        </CardTitle>
                        <Badge variant="secondary">
                          {content.status === 'pending' ? '审核中' :
                           content.status === 'approved' ? '已通过' :
                           content.status === 'rejected' ? '已拒绝' : '已编辑'}
                        </Badge>
                      </div>
                      <CardDescription className="flex items-center">
                        <Clock className="h-3 w-3 mr-1" />
                        {formatDistanceToNow(new Date(content.createdAt), { addSuffix: true, locale: zhCN })}
                      </CardDescription>
                    </CardHeader>

                    <CardContent className="pb-2">
                      <p className="text-sm text-muted-foreground">
                        内容正在审核中，审核通过后将显示在相应的标签页中。
                      </p>
                    </CardContent>
                  </Card>
                ))
              )}
            </TabsContent>
          </Tabs>
        )}
      </div>
    );
  };

  return (
    <div className="container mx-auto py-8">
      {isAuthenticated ? renderContentList() : renderAuthForm()}

      {/* 删除确认对话框 */}
      <AlertDialog open={!!deleteType} onOpenChange={(open) => !open && setDeleteType(null)}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>确认删除</AlertDialogTitle>
            <AlertDialogDescription>
              {deleteType === 'story'
                ? '您确定要删除这个故事吗？此操作无法撤销。'
                : '您确定要删除这个问卷回复吗？此操作无法撤销。'}
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel disabled={isDeleting}>取消</AlertDialogCancel>
            <AlertDialogAction
              onClick={handleDeleteConfirm}
              disabled={isDeleting}
            >
              {isDeleting ? '删除中...' : '确认删除'}
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </div>
  );
}
