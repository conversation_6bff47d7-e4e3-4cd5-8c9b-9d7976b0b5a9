/**
 * UUID相关处理程序
 */

import { generateHash } from '../utils/crypto';

/**
 * 处理UUID生成
 * @param request 请求对象
 * @param env 环境对象
 * @returns 响应对象
 */
export async function handleUUIDGenerate(request: Request, env: Env): Promise<Response> {
  try {
    // 解析请求体
    const { identityA, identityB } = await request.json() as { identityA: string; identityB: string };
    
    // 验证参数
    if (!identityA || !identityB) {
      return new Response(JSON.stringify({
        success: false,
        error: '缺少必要参数'
      }), {
        status: 400,
        headers: { 'Content-Type': 'application/json' }
      });
    }
    
    // 生成哈希值
    const identityAHash = generateHash(identityA);
    const identityBHash = generateHash(identityB);
    
    // 生成组合键
    const combinedKey = `${identityAHash}:${identityBHash}`;
    
    // 检查是否已存在
    const existingUUID = await env.COLLEGE_SURVEY_KV.get(`uuid:${combinedKey}`);
    
    if (existingUUID) {
      return new Response(JSON.stringify({
        success: true,
        uuid: existingUUID,
        isNew: false
      }), {
        status: 200,
        headers: { 'Content-Type': 'application/json' }
      });
    }
    
    // 生成新的UUID
    const uuid = crypto.randomUUID();
    
    // 存储UUID
    await env.COLLEGE_SURVEY_KV.put(`uuid:${combinedKey}`, uuid);
    
    // 存储反向映射
    await env.COLLEGE_SURVEY_KV.put(`uuid_reverse:${uuid}`, combinedKey);
    
    // 存储到D1数据库
    await env.DB.prepare(
      `INSERT INTO uuid_mappings (uuid, identity_a_hash, identity_b_hash, created_at) VALUES (?, ?, ?, CURRENT_TIMESTAMP)`
    )
    .bind(uuid, identityAHash, identityBHash)
    .run();
    
    // 返回响应
    return new Response(JSON.stringify({
      success: true,
      uuid,
      isNew: true
    }), {
      status: 200,
      headers: { 'Content-Type': 'application/json' }
    });
  } catch (error) {
    console.error('UUID生成失败:', error);
    
    return new Response(JSON.stringify({
      success: false,
      error: '服务器错误'
    }), {
      status: 500,
      headers: { 'Content-Type': 'application/json' }
    });
  }
}

/**
 * 处理UUID验证
 * @param request 请求对象
 * @param env 环境对象
 * @returns 响应对象
 */
export async function handleUUIDVerify(request: Request, env: Env): Promise<Response> {
  try {
    // 解析请求体
    const { identityA, identityB, uuid } = await request.json() as { identityA: string; identityB: string; uuid?: string };
    
    // 验证参数
    if (!identityA || !identityB) {
      return new Response(JSON.stringify({
        success: false,
        error: '缺少必要参数'
      }), {
        status: 400,
        headers: { 'Content-Type': 'application/json' }
      });
    }
    
    // 生成哈希值
    const identityAHash = generateHash(identityA);
    const identityBHash = generateHash(identityB);
    
    // 生成组合键
    const combinedKey = `${identityAHash}:${identityBHash}`;
    
    // 获取UUID
    const storedUUID = await env.COLLEGE_SURVEY_KV.get(`uuid:${combinedKey}`);
    
    // 如果没有找到UUID
    if (!storedUUID) {
      return new Response(JSON.stringify({
        success: true,
        valid: false,
        message: 'UUID not found'
      }), {
        status: 200,
        headers: { 'Content-Type': 'application/json' }
      });
    }
    
    // 如果提供了UUID，验证是否匹配
    if (uuid && storedUUID !== uuid) {
      return new Response(JSON.stringify({
        success: true,
        valid: false,
        message: 'UUID does not match'
      }), {
        status: 200,
        headers: { 'Content-Type': 'application/json' }
      });
    }
    
    // 更新验证次数
    await env.DB.prepare(
      `UPDATE uuid_mappings SET verification_count = verification_count + 1, last_verified_at = CURRENT_TIMESTAMP WHERE uuid = ?`
    )
    .bind(storedUUID)
    .run();
    
    // 返回响应
    return new Response(JSON.stringify({
      success: true,
      valid: true,
      uuid: storedUUID
    }), {
      status: 200,
      headers: { 'Content-Type': 'application/json' }
    });
  } catch (error) {
    console.error('UUID验证失败:', error);
    
    return new Response(JSON.stringify({
      success: false,
      error: '服务器错误'
    }), {
      status: 500,
      headers: { 'Content-Type': 'application/json' }
    });
  }
}
