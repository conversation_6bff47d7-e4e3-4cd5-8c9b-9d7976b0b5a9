-- 测试数据导入 - 少量数据验证

-- 插入几条问卷回复数据
INSERT OR IGNORE INTO questionnaire_responses_v2 (
    id, user_id, education_level_display, major_display, graduation_year, 
    region_display, employment_status, salary_range, status, created_at
) VALUES 
('qr_test_001', 'user_001', '本科', '计算机科学与技术', 2023, '北京', 'employed', '8-12万', 'processed', '2024-01-15 10:00:00'),
('qr_test_002', 'user_002', '硕士', '软件工程', 2023, '上海', 'employed', '12-18万', 'processed', '2024-01-16 11:00:00'),
('qr_test_003', 'user_003', '本科', '电子信息工程', 2023, '深圳', 'unemployed', NULL, 'processed', '2024-01-17 12:00:00'),
('qr_test_004', 'user_004', '大专', '市场营销', 2023, '广州', 'studying', NULL, 'processed', '2024-01-18 13:00:00'),
('qr_test_005', 'user_005', '博士', '人工智能', 2023, '杭州', 'employed', '25-35万', 'processed', '2024-01-19 14:00:00');

-- 插入几条问卷心声数据
INSERT OR IGNORE INTO questionnaire_voices_v2 (
    id, source_response_id, user_id, voice_type, title, content, 
    education_level_display, region_display, status, likes, views, created_at
) VALUES 
('qv_test_001', 'qr_test_001', 'user_001', 'advice', '给学弟学妹的建议', '要多实习，积累项目经验，技术栈要跟上时代。', '本科', '北京', 'approved', 5, 120, '2024-01-15 10:30:00'),
('qv_test_002', 'qr_test_001', 'user_001', 'observation', '对就业环境的观察', '今年就业形势比较严峻，但技术岗位需求还是很大的。', '本科', '北京', 'approved', 8, 200, '2024-01-15 10:35:00'),
('qv_test_003', 'qr_test_002', 'user_002', 'advice', '给学弟学妹的建议', '研究生期间要多发论文，同时也要关注工程实践。', '硕士', '上海', 'approved', 12, 180, '2024-01-16 11:30:00'),
('qv_test_004', 'qr_test_003', 'user_003', 'observation', '对就业环境的观察', '找工作确实不容易，要做好心理准备，多投简历。', '本科', '深圳', 'approved', 3, 90, '2024-01-17 12:30:00'),
('qv_test_005', 'qr_test_005', 'user_005', 'advice', '给学弟学妹的建议', '博士毕业要考虑清楚是走学术路线还是工业界。', '博士', '杭州', 'approved', 15, 300, '2024-01-19 14:30:00');

-- 插入几条故事内容数据
INSERT OR IGNORE INTO story_contents_v2 (
    id, user_id, title, content, summary, category, 
    education_level_display, industry_display, status, likes, views, created_at
) VALUES 
('sc_test_001', 'user_001', '我的求职经历', '从投简历到拿到offer，经历了3个月的时间...', '分享求职过程中的经验和教训', '求职经验', '本科', '互联网', 'approved', 25, 500, '2024-01-20 09:00:00'),
('sc_test_002', 'user_002', '转行到AI的心路历程', '从传统软件开发转向人工智能领域的经历...', '转行AI的完整过程分享', '转行经验', '硕士', '人工智能', 'approved', 30, 600, '2024-01-21 10:00:00'),
('sc_test_003', 'user_003', '失业后的反思', '失业三个月后，我重新审视了自己的职业规划...', '失业期间的思考和成长', '职业规划', '本科', '制造业', 'approved', 18, 350, '2024-01-22 11:00:00');
