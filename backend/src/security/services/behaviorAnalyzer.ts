/**
 * 行为分析服务
 * 
 * 负责分析用户行为数据，识别可疑活动
 */

import { BehaviorAnalysisConfig, ValidationResult } from '../types';

/**
 * 行为数据接口
 */
interface BehaviorData {
  completionTime?: number;
  mouseMovements?: number;
  mouseClicks?: number;
  keyPresses?: number;
  fieldChanges?: number;
  focusBlurEvents?: number;
  scrollEvents?: number;
}

/**
 * 行为分析服务类
 */
export class BehaviorAnalyzer {
  private config: BehaviorAnalysisConfig;

  /**
   * 构造函数
   */
  constructor() {
    this.config = {
      enabled: false,
      minCompletionTime: 0,
      trackMouseMovements: false,
      trackKeyboardEvents: false,
      suspiciousScoreThreshold: 100
    };
  }

  /**
   * 初始化服务
   * @param config 行为分析配置
   */
  public initialize(config: BehaviorAnalysisConfig): void {
    this.config = { ...config };
  }

  /**
   * 更新配置
   * @param config 行为分析配置
   */
  public updateConfig(config: BehaviorAnalysisConfig): void {
    this.config = { ...this.config, ...config };
  }

  /**
   * 分析行为数据
   * @param behaviorData 行为数据
   */
  public analyzeBehavior(behaviorData?: BehaviorData): ValidationResult {
    // 如果未启用行为分析，直接返回有效
    if (!this.config.enabled) {
      return { valid: true };
    }

    // 如果未提供行为数据，返回有效（兼容旧版本）
    if (!behaviorData) {
      return { valid: true };
    }

    // 计算可疑分数
    const { suspiciousScore, reasons } = this.calculateSuspiciousScore(behaviorData);

    // 如果可疑分数超过阈值，返回无效
    if (suspiciousScore >= this.config.suspiciousScoreThreshold) {
      return {
        valid: false,
        error: '提交被拒绝，请重试',
        suspiciousScore,
        reasons
      };
    }

    return { 
      valid: true, 
      suspiciousScore,
      reasons: reasons.length > 0 ? reasons : undefined
    };
  }

  /**
   * 计算可疑分数
   * @param behaviorData 行为数据
   */
  private calculateSuspiciousScore(behaviorData: BehaviorData): { 
    suspiciousScore: number; 
    reasons: string[] 
  } {
    let suspiciousScore = 0;
    const reasons: string[] = [];

    // 检查完成时间
    if (
      this.config.minCompletionTime > 0 && 
      behaviorData.completionTime !== undefined && 
      behaviorData.completionTime < this.config.minCompletionTime
    ) {
      // 完成时间过短，增加可疑分数
      const timeRatio = behaviorData.completionTime / this.config.minCompletionTime;
      const timeScore = Math.round(30 * (1 - timeRatio));
      suspiciousScore += timeScore;
      
      if (timeScore > 15) {
        reasons.push(`完成时间过短: ${Math.round(behaviorData.completionTime / 1000)}秒 (期望至少 ${Math.round(this.config.minCompletionTime / 1000)}秒)`);
      }
    }

    // 检查鼠标移动
    if (
      this.config.trackMouseMovements && 
      behaviorData.mouseMovements !== undefined && 
      behaviorData.mouseMovements < 10
    ) {
      // 鼠标移动过少，增加可疑分数
      const movementScore = Math.max(0, 10 - behaviorData.mouseMovements);
      suspiciousScore += movementScore;
      
      if (movementScore > 5) {
        reasons.push(`鼠标移动过少: ${behaviorData.mouseMovements} (期望至少 10)`);
      }
    }

    // 检查键盘事件
    if (
      this.config.trackKeyboardEvents && 
      behaviorData.keyPresses !== undefined && 
      behaviorData.keyPresses < 5
    ) {
      // 键盘事件过少，增加可疑分数
      const keyScore = Math.max(0, 5 - behaviorData.keyPresses) * 2;
      suspiciousScore += keyScore;
      
      if (keyScore > 5) {
        reasons.push(`键盘事件过少: ${behaviorData.keyPresses} (期望至少 5)`);
      }
    }

    // 检查字段变化
    if (
      behaviorData.fieldChanges !== undefined && 
      behaviorData.fieldChanges < 3
    ) {
      // 字段变化过少，增加可疑分数
      const fieldScore = Math.max(0, 3 - behaviorData.fieldChanges) * 5;
      suspiciousScore += fieldScore;
      
      if (fieldScore > 5) {
        reasons.push(`字段变化过少: ${behaviorData.fieldChanges} (期望至少 3)`);
      }
    }

    // 检查焦点事件
    if (
      behaviorData.focusBlurEvents !== undefined && 
      behaviorData.focusBlurEvents < 2
    ) {
      // 焦点事件过少，增加可疑分数
      const focusScore = Math.max(0, 2 - behaviorData.focusBlurEvents) * 5;
      suspiciousScore += focusScore;
      
      if (focusScore > 5) {
        reasons.push(`焦点事件过少: ${behaviorData.focusBlurEvents} (期望至少 2)`);
      }
    }

    return { suspiciousScore, reasons };
  }
}
