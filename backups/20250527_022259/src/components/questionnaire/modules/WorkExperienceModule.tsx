import { useEffect, useState } from 'react';
import { z } from 'zod';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage
} from '@/components/ui/form';
import { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue
} from '@/components/ui/select';
import QuestionItem from '../QuestionItem';
import AnswerStats from '../AnswerStats';
import { QuestionnaireData } from '@/lib/api';

// Define the form schema
const formSchema = z.object({
  employmentStatus: z.string().min(1, { message: '请选择就业状态' }),
  currentIndustry: z.string().optional(),
  currentPosition: z.string().optional(),
  monthlySalary: z.string().optional(),
  jobSatisfaction: z.string().optional(),
});

// Mock statistics data
const mockStats = {
  employmentStatus: [
    { label: '已就业', percentage: 65 },
    { label: '待业中', percentage: 20 },
    { label: '自由职业', percentage: 10 },
    { label: '创业', percentage: 5 },
  ],
  currentIndustry: [
    { label: '互联网/IT', percentage: 30 },
    { label: '金融', percentage: 15 },
    { label: '教育', percentage: 12 },
    { label: '医疗健康', percentage: 10 },
    { label: '制造业', percentage: 8 },
    { label: '其他', percentage: 25 },
  ],
  monthlySalary: [
    { label: '3000元以下', percentage: 5 },
    { label: '3000-5000元', percentage: 15 },
    { label: '5000-8000元', percentage: 25 },
    { label: '8000-12000元', percentage: 30 },
    { label: '12000-20000元', percentage: 20 },
    { label: '20000元以上', percentage: 5 },
  ],
  jobSatisfaction: [
    { label: '非常满意', percentage: 10 },
    { label: '比较满意', percentage: 35 },
    { label: '一般', percentage: 40 },
    { label: '不太满意', percentage: 10 },
    { label: '非常不满意', percentage: 5 },
  ],
};

interface WorkExperienceModuleProps {
  data: Partial<QuestionnaireData>;
  onUpdate: (data: Partial<QuestionnaireData>) => void;
}

export default function WorkExperienceModule({
  data,
  onUpdate
}: WorkExperienceModuleProps) {
  // Set up form with default values from data
  const form = useForm<z.infer<typeof formSchema>>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      employmentStatus: data.employmentStatus || '',
      currentIndustry: data.currentIndustry || '',
      currentPosition: data.currentPosition || '',
      monthlySalary: data.monthlySalary?.toString() || '',
      jobSatisfaction: data.jobSatisfaction?.toString() || '',
    },
  });

  // Track if user is employed
  const [isEmployed, setIsEmployed] = useState(data.employmentStatus === '已就业');

  // Track selected options for stats display
  const [selectedOptions, setSelectedOptions] = useState({
    employmentStatus: data.employmentStatus || '',
    currentIndustry: data.currentIndustry || '',
    monthlySalary: data.monthlySalary?.toString() || '',
    jobSatisfaction: data.jobSatisfaction?.toString() || '',
  });

  // Update stats when form values change
  useEffect(() => {
    const subscription = form.watch((value) => {
      // Check if user is employed
      const employed = value.employmentStatus === '已就业';
      setIsEmployed(employed);

      setSelectedOptions({
        employmentStatus: value.employmentStatus || '',
        currentIndustry: value.currentIndustry || '',
        monthlySalary: value.monthlySalary || '',
        jobSatisfaction: value.jobSatisfaction || '',
      });

      // Update parent component with form values
      if (value.employmentStatus) {
        const updateData: Partial<QuestionnaireData> = {
          employmentStatus: value.employmentStatus,
        };

        // Only include employment details if employed
        if (employed) {
          if (value.currentIndustry) updateData.currentIndustry = value.currentIndustry;
          if (value.currentPosition) updateData.currentPosition = value.currentPosition;
          if (value.monthlySalary) updateData.monthlySalary = parseInt(value.monthlySalary);
          if (value.jobSatisfaction) updateData.jobSatisfaction = parseInt(value.jobSatisfaction);
        }

        onUpdate(updateData);
      }
    });

    return () => subscription.unsubscribe();
  }, [form, onUpdate]);

  // Prepare stats data with selected indicators
  const statusStats = mockStats.employmentStatus.map(stat => ({
    ...stat,
    isSelected: stat.label === selectedOptions.employmentStatus,
  }));

  const industryStats = mockStats.currentIndustry.map(stat => ({
    ...stat,
    isSelected: stat.label === selectedOptions.currentIndustry,
  }));

  const salaryStats = mockStats.monthlySalary.map(stat => ({
    ...stat,
    isSelected: stat.label === selectedOptions.monthlySalary,
  }));

  const satisfactionStats = mockStats.jobSatisfaction.map(stat => ({
    ...stat,
    isSelected: stat.label === selectedOptions.jobSatisfaction,
  }));

  return (
    <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
      <div className="md:col-span-2">
        <Form {...form}>
          <form className="space-y-6">
            <QuestionItem
              number={1}
              title="您目前的就业状态是?"
              required
            >
              <FormField
                control={form.control}
                name="employmentStatus"
                render={({ field }) => (
                  <FormItem>
                    <FormControl>
                      <RadioGroup
                        onValueChange={field.onChange}
                        defaultValue={field.value}
                        className="flex flex-col space-y-2"
                      >
                        <FormItem className="flex items-center space-x-3 space-y-0">
                          <FormControl>
                            <RadioGroupItem value="已就业" />
                          </FormControl>
                          <FormLabel className="font-normal">已就业</FormLabel>
                        </FormItem>
                        <FormItem className="flex items-center space-x-3 space-y-0">
                          <FormControl>
                            <RadioGroupItem value="待业中" />
                          </FormControl>
                          <FormLabel className="font-normal">待业中</FormLabel>
                        </FormItem>
                        <FormItem className="flex items-center space-x-3 space-y-0">
                          <FormControl>
                            <RadioGroupItem value="自由职业" />
                          </FormControl>
                          <FormLabel className="font-normal">自由职业</FormLabel>
                        </FormItem>
                        <FormItem className="flex items-center space-x-3 space-y-0">
                          <FormControl>
                            <RadioGroupItem value="创业" />
                          </FormControl>
                          <FormLabel className="font-normal">创业</FormLabel>
                        </FormItem>
                        <FormItem className="flex items-center space-x-3 space-y-0">
                          <FormControl>
                            <RadioGroupItem value="其他" />
                          </FormControl>
                          <FormLabel className="font-normal">其他</FormLabel>
                        </FormItem>
                      </RadioGroup>
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </QuestionItem>

            {isEmployed && (
              <>
                <QuestionItem
                  number={2}
                  title="您当前所在的行业是?"
                  required
                >
                  <FormField
                    control={form.control}
                    name="currentIndustry"
                    render={({ field }) => (
                      <FormItem>
                        <Select
                          onValueChange={field.onChange}
                          defaultValue={field.value}
                        >
                          <FormControl>
                            <SelectTrigger>
                              <SelectValue placeholder="请选择行业" />
                            </SelectTrigger>
                          </FormControl>
                          <SelectContent>
                            <SelectItem value="互联网/IT">互联网/IT</SelectItem>
                            <SelectItem value="金融">金融</SelectItem>
                            <SelectItem value="教育">教育</SelectItem>
                            <SelectItem value="医疗健康">医疗健康</SelectItem>
                            <SelectItem value="制造业">制造业</SelectItem>
                            <SelectItem value="零售/快消">零售/快消</SelectItem>
                            <SelectItem value="文化/传媒">文化/传媒</SelectItem>
                            <SelectItem value="房地产">房地产</SelectItem>
                            <SelectItem value="政府/事业单位">政府/事业单位</SelectItem>
                            <SelectItem value="其他">其他</SelectItem>
                          </SelectContent>
                        </Select>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </QuestionItem>

                <QuestionItem
                  number={3}
                  title="您当前的岗位是?"
                  required
                >
                  <FormField
                    control={form.control}
                    name="currentPosition"
                    render={({ field }) => (
                      <FormItem>
                        <Select
                          onValueChange={field.onChange}
                          defaultValue={field.value}
                        >
                          <FormControl>
                            <SelectTrigger>
                              <SelectValue placeholder="请选择岗位" />
                            </SelectTrigger>
                          </FormControl>
                          <SelectContent>
                            <SelectItem value="技术研发">技术研发</SelectItem>
                            <SelectItem value="产品运营">产品运营</SelectItem>
                            <SelectItem value="市场营销">市场营销</SelectItem>
                            <SelectItem value="人力资源">人力资源</SelectItem>
                            <SelectItem value="财务会计">财务会计</SelectItem>
                            <SelectItem value="行政管理">行政管理</SelectItem>
                            <SelectItem value="教育培训">教育培训</SelectItem>
                            <SelectItem value="医疗卫生">医疗卫生</SelectItem>
                            <SelectItem value="其他">其他</SelectItem>
                          </SelectContent>
                        </Select>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </QuestionItem>

                <QuestionItem
                  number={4}
                  title="您当前的月薪范围是?"
                  required
                >
                  <FormField
                    control={form.control}
                    name="monthlySalary"
                    render={({ field }) => (
                      <FormItem>
                        <FormControl>
                          <RadioGroup
                            onValueChange={field.onChange}
                            defaultValue={field.value}
                            className="flex flex-col space-y-2"
                          >
                            <FormItem className="flex items-center space-x-3 space-y-0">
                              <FormControl>
                                <RadioGroupItem value="3000元以下" />
                              </FormControl>
                              <FormLabel className="font-normal">3000元以下</FormLabel>
                            </FormItem>
                            <FormItem className="flex items-center space-x-3 space-y-0">
                              <FormControl>
                                <RadioGroupItem value="3000-5000元" />
                              </FormControl>
                              <FormLabel className="font-normal">3000-5000元</FormLabel>
                            </FormItem>
                            <FormItem className="flex items-center space-x-3 space-y-0">
                              <FormControl>
                                <RadioGroupItem value="5000-8000元" />
                              </FormControl>
                              <FormLabel className="font-normal">5000-8000元</FormLabel>
                            </FormItem>
                            <FormItem className="flex items-center space-x-3 space-y-0">
                              <FormControl>
                                <RadioGroupItem value="8000-12000元" />
                              </FormControl>
                              <FormLabel className="font-normal">8000-12000元</FormLabel>
                            </FormItem>
                            <FormItem className="flex items-center space-x-3 space-y-0">
                              <FormControl>
                                <RadioGroupItem value="12000-20000元" />
                              </FormControl>
                              <FormLabel className="font-normal">12000-20000元</FormLabel>
                            </FormItem>
                            <FormItem className="flex items-center space-x-3 space-y-0">
                              <FormControl>
                                <RadioGroupItem value="20000元以上" />
                              </FormControl>
                              <FormLabel className="font-normal">20000元以上</FormLabel>
                            </FormItem>
                          </RadioGroup>
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </QuestionItem>

                <QuestionItem
                  number={5}
                  title="您对当前工作的满意度是? (1-5分)"
                  required
                >
                  <FormField
                    control={form.control}
                    name="jobSatisfaction"
                    render={({ field }) => (
                      <FormItem>
                        <FormControl>
                          <RadioGroup
                            onValueChange={field.onChange}
                            defaultValue={field.value}
                            className="flex space-x-4"
                          >
                            <FormItem className="flex flex-col items-center space-y-1">
                              <FormControl>
                                <RadioGroupItem value="1" />
                              </FormControl>
                              <FormLabel className="font-normal">1分<br/>非常不满意</FormLabel>
                            </FormItem>
                            <FormItem className="flex flex-col items-center space-y-1">
                              <FormControl>
                                <RadioGroupItem value="2" />
                              </FormControl>
                              <FormLabel className="font-normal">2分<br/>不太满意</FormLabel>
                            </FormItem>
                            <FormItem className="flex flex-col items-center space-y-1">
                              <FormControl>
                                <RadioGroupItem value="3" />
                              </FormControl>
                              <FormLabel className="font-normal">3分<br/>一般</FormLabel>
                            </FormItem>
                            <FormItem className="flex flex-col items-center space-y-1">
                              <FormControl>
                                <RadioGroupItem value="4" />
                              </FormControl>
                              <FormLabel className="font-normal">4分<br/>比较满意</FormLabel>
                            </FormItem>
                            <FormItem className="flex flex-col items-center space-y-1">
                              <FormControl>
                                <RadioGroupItem value="5" />
                              </FormControl>
                              <FormLabel className="font-normal">5分<br/>非常满意</FormLabel>
                            </FormItem>
                          </RadioGroup>
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </QuestionItem>
              </>
            )}
          </form>
        </Form>
      </div>

      <div className="space-y-6">
        <AnswerStats
          title="就业状态分布"
          options={statusStats}
        />

        {isEmployed && (
          <>
            <AnswerStats
              title="行业分布"
              options={industryStats}
            />

            <AnswerStats
              title="实际薪资分布"
              options={salaryStats}
            />

            <AnswerStats
              title="工作满意度分布"
              options={satisfactionStats}
            />
          </>
        )}
      </div>
    </div>
  );
}
