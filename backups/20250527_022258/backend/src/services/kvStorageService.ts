/**
 * KV 存储服务
 *
 * 提供与 Cloudflare KV 存储的交互功能
 */

import { Env } from '../types';

// 定义 KV 存储的键前缀
const KEY_PREFIXES = {
  USER: 'user:',
  QUESTIONNAIRE: 'questionnaire:',
  STORY: 'story:',
  STORY_LIST: 'story_list',
  QUESTIONNAIRE_STATS: 'questionnaire_stats',
  VISUALIZATION_DATA: 'visualization_data',
  TAGS: 'tags',
  CATEGORIES: 'categories',
  EDUCATION_LEVELS: 'education_levels',
  INDUSTRIES: 'industries',
  EXPORT_RECORD: 'export:',
  EXPORT_LIST: 'export_list',
};

/**
 * KV 存储服务类
 */
export class KVStorageService {
  private kv: KVNamespace;

  /**
   * 构造函数
   * @param env 环境变量
   */
  constructor(env: Env) {
    this.kv = env.SURVEY_KV;
  }

  /**
   * 保存用户数据
   * @param userId 用户ID
   * @param userData 用户数据
   */
  async saveUser(userId: number, userData: any): Promise<void> {
    const key = `${KEY_PREFIXES.USER}${userId}`;
    await this.kv.put(key, JSON.stringify(userData));
  }

  /**
   * 获取用户数据
   * @param userId 用户ID
   */
  async getUser(userId: number): Promise<any | null> {
    const key = `${KEY_PREFIXES.USER}${userId}`;
    const userData = await this.kv.get(key);
    return userData ? JSON.parse(userData) : null;
  }

  /**
   * 保存问卷数据
   * @param questionnaireId 问卷ID
   * @param questionnaireData 问卷数据
   */
  async saveQuestionnaire(questionnaireId: number, questionnaireData: any): Promise<void> {
    const key = `${KEY_PREFIXES.QUESTIONNAIRE}${questionnaireId}`;
    await this.kv.put(key, JSON.stringify(questionnaireData));
  }

  /**
   * 获取问卷数据
   * @param questionnaireId 问卷ID
   */
  async getQuestionnaire(questionnaireId: number): Promise<any | null> {
    const key = `${KEY_PREFIXES.QUESTIONNAIRE}${questionnaireId}`;
    const questionnaireData = await this.kv.get(key);
    return questionnaireData ? JSON.parse(questionnaireData) : null;
  }

  /**
   * 保存故事数据
   * @param storyId 故事ID
   * @param storyData 故事数据
   */
  async saveStory(storyId: number, storyData: any): Promise<void> {
    const key = `${KEY_PREFIXES.STORY}${storyId}`;
    await this.kv.put(key, JSON.stringify(storyData));
  }

  /**
   * 获取故事数据
   * @param storyId 故事ID
   */
  async getStory(storyId: number): Promise<any | null> {
    const key = `${KEY_PREFIXES.STORY}${storyId}`;
    const storyData = await this.kv.get(key);
    return storyData ? JSON.parse(storyData) : null;
  }

  /**
   * 保存故事列表
   * @param storyList 故事列表
   */
  async saveStoryList(storyList: any[]): Promise<void> {
    await this.kv.put(KEY_PREFIXES.STORY_LIST, JSON.stringify(storyList));
  }

  /**
   * 获取故事列表
   */
  async getStoryList(): Promise<any[]> {
    const storyList = await this.kv.get(KEY_PREFIXES.STORY_LIST);
    return storyList ? JSON.parse(storyList) : [];
  }

  /**
   * 保存问卷统计数据
   * @param stats 统计数据
   */
  async saveQuestionnaireStats(stats: any): Promise<void> {
    await this.kv.put(KEY_PREFIXES.QUESTIONNAIRE_STATS, JSON.stringify(stats));
  }

  /**
   * 获取问卷统计数据
   */
  async getQuestionnaireStats(): Promise<any | null> {
    const stats = await this.kv.get(KEY_PREFIXES.QUESTIONNAIRE_STATS);
    return stats ? JSON.parse(stats) : null;
  }

  /**
   * 保存可视化数据
   * @param data 可视化数据
   */
  async saveVisualizationData(data: any): Promise<void> {
    await this.kv.put(KEY_PREFIXES.VISUALIZATION_DATA, JSON.stringify(data));
  }

  /**
   * 获取可视化数据
   */
  async getVisualizationData(): Promise<any | null> {
    const data = await this.kv.get(KEY_PREFIXES.VISUALIZATION_DATA);
    return data ? JSON.parse(data) : null;
  }

  /**
   * 保存标签列表
   * @param tags 标签列表
   */
  async saveTags(tags: any[]): Promise<void> {
    await this.kv.put(KEY_PREFIXES.TAGS, JSON.stringify(tags));
  }

  /**
   * 获取标签列表
   */
  async getTags(): Promise<any[]> {
    const tags = await this.kv.get(KEY_PREFIXES.TAGS);
    return tags ? JSON.parse(tags) : [];
  }

  /**
   * 保存分类列表
   * @param categories 分类列表
   */
  async saveCategories(categories: any[]): Promise<void> {
    await this.kv.put(KEY_PREFIXES.CATEGORIES, JSON.stringify(categories));
  }

  /**
   * 获取分类列表
   */
  async getCategories(): Promise<any[]> {
    const categories = await this.kv.get(KEY_PREFIXES.CATEGORIES);
    return categories ? JSON.parse(categories) : [];
  }

  /**
   * 保存学历列表
   * @param educationLevels 学历列表
   */
  async saveEducationLevels(educationLevels: any[]): Promise<void> {
    await this.kv.put(KEY_PREFIXES.EDUCATION_LEVELS, JSON.stringify(educationLevels));
  }

  /**
   * 获取学历列表
   */
  async getEducationLevels(): Promise<any[]> {
    const educationLevels = await this.kv.get(KEY_PREFIXES.EDUCATION_LEVELS);
    return educationLevels ? JSON.parse(educationLevels) : [];
  }

  /**
   * 保存行业列表
   * @param industries 行业列表
   */
  async saveIndustries(industries: any[]): Promise<void> {
    await this.kv.put(KEY_PREFIXES.INDUSTRIES, JSON.stringify(industries));
  }

  /**
   * 获取行业列表
   */
  async getIndustries(): Promise<any[]> {
    const industries = await this.kv.get(KEY_PREFIXES.INDUSTRIES);
    return industries ? JSON.parse(industries) : [];
  }

  /**
   * 保存导出记录
   * @param exportRecord 导出记录
   */
  async saveExportRecord(exportRecord: any): Promise<void> {
    // 保存单个导出记录
    const key = `${KEY_PREFIXES.EXPORT_RECORD}${exportRecord.id}`;
    await this.kv.put(key, JSON.stringify(exportRecord));

    // 更新导出记录列表
    const exportList = await this.getExportList();
    const exportSummary = {
      id: exportRecord.id,
      fileName: exportRecord.fileName,
      type: exportRecord.type,
      format: exportRecord.format,
      url: exportRecord.url,
      createdAt: exportRecord.createdAt,
      expiresAt: exportRecord.expiresAt,
    };

    // 添加到列表开头
    exportList.unshift(exportSummary);

    // 只保留最近的50条记录
    if (exportList.length > 50) {
      exportList.length = 50;
    }

    // 保存更新后的列表
    await this.kv.put(KEY_PREFIXES.EXPORT_LIST, JSON.stringify(exportList));
  }

  /**
   * 获取导出记录
   * @param exportId 导出记录ID
   */
  async getExportRecord(exportId: string): Promise<any | null> {
    const key = `${KEY_PREFIXES.EXPORT_RECORD}${exportId}`;
    const exportRecord = await this.kv.get(key);
    return exportRecord ? JSON.parse(exportRecord) : null;
  }

  /**
   * 获取导出记录列表
   */
  async getExportList(): Promise<any[]> {
    const exportList = await this.kv.get(KEY_PREFIXES.EXPORT_LIST);
    return exportList ? JSON.parse(exportList) : [];
  }
}
