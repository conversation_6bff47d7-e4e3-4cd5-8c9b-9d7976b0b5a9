import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { StatisticsData } from '../StatisticsPanel';
import { 
  Users, 
  Briefcase, 
  GraduationCap, 
  Clock, 
  DollarSign, 
  Star, 
  TrendingUp, 
  TrendingDown 
} from 'lucide-react';
import { Progress } from '@/components/ui/progress';
import { Separator } from '@/components/ui/separator';

// 导入图表库
import { 
  PieChart, 
  Pie, 
  Cell, 
  ResponsiveContainer, 
  BarChart, 
  Bar, 
  XAxis, 
  YAxis, 
  CartesianGrid, 
  Tooltip, 
  Legend 
} from 'recharts';

interface OverviewDashboardProps {
  data: StatisticsData;
}

// 图表颜色
const COLORS = ['#0088FE', '#00C49F', '#FFBB28', '#FF8042', '#8884d8', '#82ca9d'];

export default function OverviewDashboard({ data }: OverviewDashboardProps) {
  // 计算就业率
  const employmentRate = data.overview.totalResponses > 0 
    ? (data.overview.employedCount / data.overview.totalResponses * 100).toFixed(1) 
    : '0';
  
  // 计算继续深造率
  const furtherEducationRate = data.overview.totalResponses > 0 
    ? (data.overview.furtherEducationCount / data.overview.totalResponses * 100).toFixed(1) 
    : '0';
  
  // 计算待业率
  const unemploymentRate = data.overview.totalResponses > 0 
    ? (data.overview.unemployedCount / data.overview.totalResponses * 100).toFixed(1) 
    : '0';
  
  // 格式化薪资
  const formattedSalary = data.overview.averageSalary.toLocaleString('zh-CN');
  
  // 格式化满意度（保留一位小数）
  const formattedSatisfaction = data.overview.averageSatisfaction.toFixed(1);
  
  // 准备就业状态饼图数据
  const employmentStatusData = data.employmentStatus.labels.map((label, index) => ({
    name: label,
    value: data.employmentStatus.data[index]
  }));
  
  // 准备学历分布饼图数据
  const educationLevelData = data.educationLevel.labels.map((label, index) => ({
    name: label,
    value: data.educationLevel.data[index]
  }));
  
  // 准备行业分布条形图数据
  const industryData = data.industryDistribution.labels.map((label, index) => ({
    name: label,
    value: data.industryDistribution.data[index]
  }));
  
  // 准备地区分布条形图数据
  const regionData = data.regionDistribution.labels.map((label, index) => ({
    name: label,
    value: data.regionDistribution.data[index]
  }));
  
  return (
    <div className="space-y-4">
      {/* 关键指标卡片 */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">总回复数</CardTitle>
            <Users className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{data.overview.totalResponses}</div>
            <p className="text-xs text-muted-foreground">
              问卷回复总数
            </p>
          </CardContent>
        </Card>
        
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">平均薪资</CardTitle>
            <DollarSign className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{formattedSalary}元/月</div>
            <p className="text-xs text-muted-foreground">
              已就业毕业生平均薪资
            </p>
          </CardContent>
        </Card>
        
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">平均满意度</CardTitle>
            <Star className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{formattedSatisfaction}/5</div>
            <p className="text-xs text-muted-foreground">
              已就业毕业生工作满意度
            </p>
          </CardContent>
        </Card>
      </div>
      
      {/* 就业状态进度条 */}
      <Card>
        <CardHeader>
          <CardTitle>就业状态概览</CardTitle>
          <CardDescription>毕业生就业状态分布</CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="space-y-2">
            <div className="flex items-center justify-between">
              <div className="flex items-center">
                <Briefcase className="mr-2 h-4 w-4 text-blue-500" />
                <span className="text-sm font-medium">已就业</span>
              </div>
              <span className="text-sm font-medium">{employmentRate}%</span>
            </div>
            <Progress value={parseFloat(employmentRate)} className="h-2 bg-blue-100" indicatorClassName="bg-blue-500" />
          </div>
          
          <div className="space-y-2">
            <div className="flex items-center justify-between">
              <div className="flex items-center">
                <GraduationCap className="mr-2 h-4 w-4 text-green-500" />
                <span className="text-sm font-medium">继续深造</span>
              </div>
              <span className="text-sm font-medium">{furtherEducationRate}%</span>
            </div>
            <Progress value={parseFloat(furtherEducationRate)} className="h-2 bg-green-100" indicatorClassName="bg-green-500" />
          </div>
          
          <div className="space-y-2">
            <div className="flex items-center justify-between">
              <div className="flex items-center">
                <Clock className="mr-2 h-4 w-4 text-amber-500" />
                <span className="text-sm font-medium">待业中</span>
              </div>
              <span className="text-sm font-medium">{unemploymentRate}%</span>
            </div>
            <Progress value={parseFloat(unemploymentRate)} className="h-2 bg-amber-100" indicatorClassName="bg-amber-500" />
          </div>
        </CardContent>
      </Card>
      
      {/* 图表区域 */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        {/* 就业状态饼图 */}
        <Card>
          <CardHeader>
            <CardTitle>就业状态分布</CardTitle>
            <CardDescription>毕业生就业状态比例</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="h-80">
              <ResponsiveContainer width="100%" height="100%">
                <PieChart>
                  <Pie
                    data={employmentStatusData}
                    cx="50%"
                    cy="50%"
                    labelLine={true}
                    label={({ name, percent }) => `${name}: ${(percent * 100).toFixed(1)}%`}
                    outerRadius={80}
                    fill="#8884d8"
                    dataKey="value"
                  >
                    {employmentStatusData.map((entry, index) => (
                      <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                    ))}
                  </Pie>
                  <Tooltip formatter={(value) => [`${value}人`, '数量']} />
                  <Legend />
                </PieChart>
              </ResponsiveContainer>
            </div>
          </CardContent>
        </Card>
        
        {/* 学历分布饼图 */}
        <Card>
          <CardHeader>
            <CardTitle>学历层次分布</CardTitle>
            <CardDescription>毕业生学历层次比例</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="h-80">
              <ResponsiveContainer width="100%" height="100%">
                <PieChart>
                  <Pie
                    data={educationLevelData}
                    cx="50%"
                    cy="50%"
                    labelLine={true}
                    label={({ name, percent }) => `${name}: ${(percent * 100).toFixed(1)}%`}
                    outerRadius={80}
                    fill="#8884d8"
                    dataKey="value"
                  >
                    {educationLevelData.map((entry, index) => (
                      <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                    ))}
                  </Pie>
                  <Tooltip formatter={(value) => [`${value}人`, '数量']} />
                  <Legend />
                </PieChart>
              </ResponsiveContainer>
            </div>
          </CardContent>
        </Card>
        
        {/* 行业分布条形图 */}
        <Card>
          <CardHeader>
            <CardTitle>行业分布</CardTitle>
            <CardDescription>毕业生就业行业分布</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="h-80">
              <ResponsiveContainer width="100%" height="100%">
                <BarChart
                  data={industryData}
                  layout="vertical"
                  margin={{ top: 5, right: 30, left: 20, bottom: 5 }}
                >
                  <CartesianGrid strokeDasharray="3 3" />
                  <XAxis type="number" />
                  <YAxis dataKey="name" type="category" width={100} />
                  <Tooltip formatter={(value) => [`${value}人`, '数量']} />
                  <Legend />
                  <Bar dataKey="value" fill="#0088FE" name="人数" />
                </BarChart>
              </ResponsiveContainer>
            </div>
          </CardContent>
        </Card>
        
        {/* 地区分布条形图 */}
        <Card>
          <CardHeader>
            <CardTitle>地区分布</CardTitle>
            <CardDescription>毕业生就业地区分布</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="h-80">
              <ResponsiveContainer width="100%" height="100%">
                <BarChart
                  data={regionData}
                  layout="vertical"
                  margin={{ top: 5, right: 30, left: 20, bottom: 5 }}
                >
                  <CartesianGrid strokeDasharray="3 3" />
                  <XAxis type="number" />
                  <YAxis dataKey="name" type="category" width={100} />
                  <Tooltip formatter={(value) => [`${value}人`, '数量']} />
                  <Legend />
                  <Bar dataKey="value" fill="#00C49F" name="人数" />
                </BarChart>
              </ResponsiveContainer>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
