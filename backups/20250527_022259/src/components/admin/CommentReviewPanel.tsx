import React, { useState, useEffect, useRef } from 'react';
import { useToast } from '@/components/ui/use-toast';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Badge } from '@/components/ui/badge';
import { Checkbox } from '@/components/ui/checkbox';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import {
  AlertCircle,
  CheckCircle,
  XCircle,
  Edit,
  Eye,
  Filter,
  RefreshCw,
  Clock,
  MessageSquare,
  AlertTriangle,
  Info,
} from 'lucide-react';
import LoadingSpinner from '@/components/ui/loading-spinner';
import ErrorDisplay from '@/components/ui/error-display';
import { formatDistanceToNow } from 'date-fns';
import { zhCN } from 'date-fns/locale';
import { Pagination } from '@/components/ui/pagination';

// 导入数据服务 - 使用新的API服务
import {
  getPendingCommentsNew,
  moderateCommentNew,
  getReviewTemplates
} from '@/services/dataService';

// 评论状态
enum CommentStatus {
  PENDING = 'pending',
  APPROVED = 'approved',
  REJECTED = 'rejected',
  EDITED = 'edited'
}

// 待审核评论接口
interface PendingComment {
  id: string;
  sequenceNumber: string;
  content: string;
  userId: string;
  userName: string;
  targetType: 'story' | 'questionnaire';
  targetId: string;
  targetTitle: string;
  status: CommentStatus;
  reviewerId?: string;
  reviewedAt?: string;
  originIp?: string;
  userAgent?: string;
  flags: string[];
  reviewNotes?: string;
  createdAt: string;
  updatedAt: string;
}

// 评论审核面板组件
export default function CommentReviewPanel() {
  const { toast } = useToast();
  const [pendingComments, setPendingComments] = useState<PendingComment[]>([]);
  const [selectedComment, setSelectedComment] = useState<PendingComment | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [isDetailLoading, setIsDetailLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [reviewNotes, setReviewNotes] = useState('');
  const [editedContent, setEditedContent] = useState<string>('');
  const [rejectReason, setRejectReason] = useState('');
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [isDialogOpen, setIsDialogOpen] = useState(false);
  const [dialogMode, setDialogMode] = useState<'view' | 'edit' | 'reject'>('view');
  const [templates, setTemplates] = useState<any[]>([]);
  const [isTemplatesLoading, setIsTemplatesLoading] = useState(false);

  // 确认对话框状态
  const [isConfirmDialogOpen, setIsConfirmDialogOpen] = useState(false);
  const [confirmAction, setConfirmAction] = useState<'approve' | 'edit' | 'reject' | null>(null);

  // 筛选条件和分页
  const [filters, setFilters] = useState({
    targetType: '',
    flag: '',
    timeRange: '',
    sortBy: 'newest'
  });
  const [pagination, setPagination] = useState({
    page: 1,
    pageSize: 10,
    total: 0,
    totalPages: 0
  });

  // 批量操作状态
  const [selectedIds, setSelectedIds] = useState<string[]>([]);
  const [isBatchDialogOpen, setIsBatchDialogOpen] = useState(false);
  const [batchAction, setBatchAction] = useState<'approve' | 'reject' | null>(null);
  const [batchRejectReason, setBatchRejectReason] = useState('');

  // 自动重试机制
  const [retryCountdown, setRetryCountdown] = useState(15);
  const retryTimerRef = useRef<NodeJS.Timeout | null>(null);

  // 获取待审核评论列表
  const fetchPendingComments = async (page = pagination.page) => {
    try {
      setIsLoading(true);
      setError(null);

      // 构建查询参数
      const queryParams = new URLSearchParams();

      // 添加分页参数
      queryParams.append('page', page.toString());
      queryParams.append('pageSize', pagination.pageSize.toString());

      if (filters.targetType) {
        queryParams.append('targetType', filters.targetType);
      }

      if (filters.flag) {
        queryParams.append('flag', filters.flag);
      }

      console.log('CommentReviewPanel: 获取待审核评论，查询参数:', queryParams.toString());

      // 获取待审核评论 - 使用新的API
      const params = {
        page,
        pageSize: pagination.pageSize,
        targetType: filters.targetType || undefined,
        search: filters.flag || undefined
      };

      const response = await getPendingCommentsNew(params);
      console.log('CommentReviewPanel: 获取待审核评论响应:', response);

      if (response && response.success) {
        setPendingComments(response.comments || []);
        console.log('CommentReviewPanel: 设置评论数据:', response.pendingComments);

        // 更新分页信息
        if (response.pagination) {
          setPagination({
            page: response.pagination.page,
            pageSize: response.pagination.pageSize,
            total: response.pagination.total,
            totalPages: response.pagination.totalPages
          });
          console.log('CommentReviewPanel: 更新分页信息:', response.pagination);
        }
      } else {
        console.log('CommentReviewPanel: 获取待审核评论失败:', response);
        setError(response?.error || '获取待审核评论失败');
        // 启动自动重试倒计时
        startRetryCountdown();
      }
    } catch (error) {
      console.error('CommentReviewPanel: 获取待审核评论错误:', error);
      setError('服务器错误，请稍后再试');
      // 启动自动重试倒计时
      startRetryCountdown();
    } finally {
      setIsLoading(false);
    }
  };

  // 处理页码变化
  const handlePageChange = (newPage: number) => {
    fetchPendingComments(newPage);
  };

  // 处理选择/取消选择内容
  const handleSelectContent = (id: string, isSelected: boolean) => {
    if (isSelected) {
      setSelectedIds(prev => [...prev, id]);
    } else {
      setSelectedIds(prev => prev.filter(itemId => itemId !== id));
    }
  };

  // 处理全选/取消全选
  const handleSelectAll = (isSelected: boolean) => {
    if (isSelected) {
      setSelectedIds(pendingComments.map(comment => comment.id));
    } else {
      setSelectedIds([]);
    }
  };

  // 打开批量操作对话框
  const openBatchDialog = (action: 'approve' | 'reject') => {
    if (selectedIds.length === 0) {
      toast({
        title: '请先选择评论',
        description: '请至少选择一项评论进行批量操作',
        variant: 'destructive'
      });
      return;
    }

    setBatchAction(action);
    setBatchRejectReason('');
    setIsBatchDialogOpen(true);
  };

  // 执行批量操作
  const executeBatchAction = async () => {
    if (!batchAction || selectedIds.length === 0) return;

    // 验证表单
    if (batchAction === 'reject' && !batchRejectReason.trim()) {
      toast({
        title: '验证失败',
        description: '请输入拒绝原因',
        variant: 'destructive'
      });
      return;
    }

    try {
      setIsSubmitting(true);

      // 执行批量操作
      const results = [];

      for (const id of selectedIds) {
        try {
          let response;

          if (batchAction === 'approve') {
            response = await moderateCommentNew(id, 'approve', { reviewNotes: '批量通过' });
          } else if (batchAction === 'reject') {
            response = await moderateCommentNew(id, 'reject', { reason: batchRejectReason });
          }

          results.push({
            id,
            success: response.success,
            error: response.error
          });
        } catch (error) {
          console.error(`批量操作评论错误 (ID: ${id}):`, error);
          results.push({
            id,
            success: false,
            error: '操作失败'
          });
        }
      }

      // 统计结果
      const successCount = results.filter(r => r.success).length;
      const failCount = results.length - successCount;

      // 保存批量审核结果到本地存储
      if (successCount > 0) {
        // 获取现有的审核结果
        const savedResults = localStorage.getItem('commentReviewResults');
        let storedResults = savedResults ? JSON.parse(savedResults) : {};

        // 添加成功的批量操作结果
        const successfulResults = results.filter(r => r.success);
        const timestamp = new Date().toISOString();

        successfulResults.forEach(result => {
          storedResults[result.id] = {
            action: batchAction,
            timestamp: timestamp,
            data: {
              reviewNotes: batchAction === 'approve' ? '批量通过' : null,
              rejectReason: batchAction === 'reject' ? batchRejectReason : null,
              batchOperation: true
            }
          };
        });

        // 保存回本地存储
        localStorage.setItem('commentReviewResults', JSON.stringify(storedResults));
        console.log('CommentReviewPanel: 保存批量审核结果到本地存储', successfulResults.length, batchAction);
      }

      // 显示结果
      toast({
        title: '批量操作完成',
        description: `成功: ${successCount}, 失败: ${failCount}`,
        variant: successCount > 0 ? 'default' : 'destructive'
      });

      // 关闭对话框
      setIsBatchDialogOpen(false);

      // 清空选择
      setSelectedIds([]);

      // 刷新列表
      fetchPendingComments();
    } catch (error) {
      console.error('批量操作错误:', error);
      toast({
        title: '批量操作失败',
        description: '服务器错误，请稍后再试',
        variant: 'destructive'
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  // 打开确认对话框
  const openConfirmDialog = (action: 'approve' | 'edit' | 'reject') => {
    // 验证表单
    if (action === 'reject' && !rejectReason.trim()) {
      toast({
        title: '验证失败',
        description: '请输入拒绝原因',
        variant: 'destructive'
      });
      return;
    }

    if (action === 'edit' && !editedContent.trim()) {
      toast({
        title: '验证失败',
        description: '请编辑内容',
        variant: 'destructive'
      });
      return;
    }

    setConfirmAction(action);
    setIsConfirmDialogOpen(true);
  };

  // 执行确认的操作
  const executeConfirmedAction = async () => {
    if (!confirmAction || !selectedComment) return;

    try {
      setIsSubmitting(true);

      let response;

      switch (confirmAction) {
        case 'approve':
          response = await moderateCommentNew(selectedComment.id, 'approve', { reviewNotes });
          break;
        case 'edit':
          // 对于编辑操作，我们先通过然后记录编辑内容
          response = await moderateCommentNew(selectedComment.id, 'approve', {
            reviewNotes: `编辑后通过: ${reviewNotes}`
          });
          break;
        case 'reject':
          response = await moderateCommentNew(selectedComment.id, 'reject', { reason: rejectReason, reviewNotes });
          break;
      }

      if (response.success) {
        toast({
          title: confirmAction === 'approve' ? '审核成功' :
                 confirmAction === 'edit' ? '编辑成功' : '拒绝成功',
          description: confirmAction === 'approve' ? '评论已通过审核' :
                       confirmAction === 'edit' ? '评论已编辑并通过审核' : '评论已被拒绝',
        });

        // 保存审核结果到本地存储
        if (selectedComment) {
          const reviewData = {
            reviewNotes: reviewNotes || '',
            editedContent: confirmAction === 'edit' ? editedContent : null,
            rejectReason: confirmAction === 'reject' ? rejectReason : null,
            commentData: selectedComment
          };

          // 获取现有的审核结果
          const savedResults = localStorage.getItem('commentReviewResults');
          let results = savedResults ? JSON.parse(savedResults) : {};

          // 添加新的审核结果
          results[selectedComment.id] = {
            action: confirmAction,
            timestamp: new Date().toISOString(),
            data: reviewData
          };

          // 保存回本地存储
          localStorage.setItem('commentReviewResults', JSON.stringify(results));
          console.log('CommentReviewPanel: 保存审核结果到本地存储', selectedComment.id, confirmAction);
        }

        // 关闭对话框
        setIsDialogOpen(false);
        setIsConfirmDialogOpen(false);

        // 刷新列表
        fetchPendingComments();
      } else {
        toast({
          title: confirmAction === 'approve' ? '审核失败' :
                 confirmAction === 'edit' ? '编辑失败' : '拒绝失败',
          description: response.error || '操作失败，请重试',
          variant: 'destructive'
        });
      }
    } catch (error) {
      console.error('操作评论错误:', error);
      toast({
        title: '操作失败',
        description: '服务器错误，请稍后再试',
        variant: 'destructive'
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  // 审核通过评论
  const handleApproveComment = () => {
    openConfirmDialog('approve');
  };

  // 编辑并通过评论
  const handleEditComment = () => {
    openConfirmDialog('edit');
  };

  // 拒绝评论
  const handleRejectComment = () => {
    openConfirmDialog('reject');
  };

  // 获取单条待审核评论详情
  const fetchPendingComment = async (id: string) => {
    try {
      setIsDetailLoading(true);
      console.log('CommentReviewPanel: 获取待审核评论详情, ID:', id);

      const response = await getPendingComment(id);
      console.log('CommentReviewPanel: 获取待审核评论详情响应:', response);

      if (response && response.success) {
        setSelectedComment(response.pendingComment);
        console.log('CommentReviewPanel: 设置选中评论:', response.pendingComment);

        // 如果是编辑模式，初始化编辑内容
        if (dialogMode === 'edit') {
          setEditedContent(response.pendingComment.content);
          console.log('CommentReviewPanel: 设置编辑内容:', response.pendingComment.content);
        }
      } else {
        console.log('CommentReviewPanel: 获取待审核评论详情失败:', response);
        toast({
          title: '获取详情失败',
          description: response?.error || '获取待审核评论详情失败',
          variant: 'destructive'
        });
      }
    } catch (error) {
      console.error('CommentReviewPanel: 获取待审核评论详情错误:', error);
      toast({
        title: '获取详情失败',
        description: '服务器错误，请稍后再试',
        variant: 'destructive'
      });
    } finally {
      setIsDetailLoading(false);
    }
  };

  // 启动自动重试倒计时
  const startRetryCountdown = () => {
    // 清除之前的计时器
    if (retryTimerRef.current) {
      clearInterval(retryTimerRef.current);
    }

    // 设置初始倒计时
    setRetryCountdown(15);

    // 启动新的倒计时
    retryTimerRef.current = setInterval(() => {
      setRetryCountdown(prev => {
        if (prev <= 1) {
          // 倒计时结束，清除计时器并重试
          if (retryTimerRef.current) {
            clearInterval(retryTimerRef.current);
            retryTimerRef.current = null;
          }
          // 自动重试
          fetchPendingComments();
          return 15;
        }
        return prev - 1;
      });
    }, 1000);
  };

  // 清除错误状态时也清除重试计时器
  useEffect(() => {
    if (!error && retryTimerRef.current) {
      clearInterval(retryTimerRef.current);
      retryTimerRef.current = null;
    }
  }, [error]);

  // 组件卸载时清除所有计时器
  useEffect(() => {
    return () => {
      if (retryTimerRef.current) {
        clearInterval(retryTimerRef.current);
      }
    };
  }, []);

  // 从本地存储加载筛选条件和分页状态
  useEffect(() => {
    const savedFilters = localStorage.getItem('commentReviewFilters');
    const savedPagination = localStorage.getItem('commentReviewPagination');

    if (savedFilters) {
      try {
        const parsedFilters = JSON.parse(savedFilters);
        setFilters(parsedFilters);
        console.log('CommentReviewPanel: 从本地存储加载筛选条件', parsedFilters);
      } catch (error) {
        console.error('CommentReviewPanel: 解析筛选条件失败', error);
      }
    }

    if (savedPagination) {
      try {
        const parsedPagination = JSON.parse(savedPagination);
        setPagination(prev => ({
          ...prev,
          page: parsedPagination.page || 1
        }));
        console.log('CommentReviewPanel: 从本地存储加载分页状态', parsedPagination);
      } catch (error) {
        console.error('CommentReviewPanel: 解析分页状态失败', error);
      }
    }
  }, []);

  // 保存筛选条件和分页状态到本地存储
  useEffect(() => {
    localStorage.setItem('commentReviewFilters', JSON.stringify(filters));
    console.log('CommentReviewPanel: 保存筛选条件到本地存储', filters);
  }, [filters]);

  useEffect(() => {
    localStorage.setItem('commentReviewPagination', JSON.stringify({ page: pagination.page }));
    console.log('CommentReviewPanel: 保存分页状态到本地存储', { page: pagination.page });
  }, [pagination.page]);

  // 组件加载时获取待审核评论
  useEffect(() => {
    console.log('CommentReviewPanel: 组件加载，获取待审核评论');

    // 直接获取数据，不使用延迟
    fetchPendingComments();
    fetchTemplates();

    // 设置定时刷新，每30秒刷新一次数据
    const refreshInterval = setInterval(() => {
      console.log('CommentReviewPanel: 定时刷新数据');
      fetchPendingComments();
    }, 30000);

    return () => clearInterval(refreshInterval);
  }, [filters]);

  // 获取审核模板
  const fetchTemplates = async () => {
    try {
      setIsTemplatesLoading(true);

      const response = await getReviewTemplates();

      if (response && response.success) {
        setTemplates(response.templates || []);
      } else {
        console.error('获取审核模板失败:', response?.error);
      }
    } catch (error) {
      console.error('获取审核模板错误:', error);
    } finally {
      setIsTemplatesLoading(false);
    }
  };

  // 应用模板
  const applyTemplate = (templateContent: string) => {
    if (dialogMode === 'reject') {
      setRejectReason(templateContent);
    } else {
      setReviewNotes(templateContent);
    }
  };

  // 打开内容详情对话框
  const openCommentDialog = (comment: PendingComment, mode: 'view' | 'edit' | 'reject') => {
    setSelectedComment(comment);
    setDialogMode(mode);
    setReviewNotes('');
    setRejectReason('');

    if (mode === 'edit') {
      setEditedContent(comment.content);
    }

    setIsDialogOpen(true);

    // 获取最新详情
    fetchPendingComment(comment.id);
  };

  // 渲染标记徽章
  const renderFlagBadge = (flag: string) => {
    let color = 'default';
    let icon = null;

    switch (flag) {
      case 'sensitive-words':
        color = 'destructive';
        icon = <AlertCircle className="h-3 w-3 mr-1" />;
        break;
      case 'spam':
        color = 'destructive';
        icon = <AlertTriangle className="h-3 w-3 mr-1" />;
        break;
      case 'suspicious-behavior':
        color = 'warning';
        icon = <AlertCircle className="h-3 w-3 mr-1" />;
        break;
      default:
        color = 'secondary';
    }

    return (
      <Badge variant={color as any} className="mr-1 mb-1">
        {icon}
        {flag}
      </Badge>
    );
  };

  // 渲染加载状态
  if (isLoading) {
    return (
      <div className="flex justify-center items-center py-12">
        <LoadingSpinner size="lg" text="加载待审核评论中..." />
      </div>
    );
  }

  // 渲染错误状态
  if (error) {
    console.log('CommentReviewPanel: 渲染错误状态, 错误信息:', error);
    return (
      <div className="space-y-4">
        <div className="flex justify-between items-center">
          <h2 className="text-2xl font-bold">评论审核</h2>
        </div>
        <div className="bg-red-50 border border-red-200 rounded-md p-4">
          <div className="flex items-start">
            <AlertCircle className="h-5 w-5 text-red-600 mr-2 mt-0.5" />
            <div>
              <h3 className="text-lg font-semibold text-red-800 mb-2">数据加载失败</h3>
              <p className="text-red-700 mb-2">{error}</p>
              <p className="text-sm text-red-600 mb-4">
                可能的原因：网络连接问题、服务器暂时不可用或数据格式错误。
                系统将在 {retryCountdown} 秒后自动重试，或者您可以立即手动重试。
              </p>
              <div className="flex justify-end space-x-2">
                <Button variant="outline" onClick={() => setError(null)}>
                  <XCircle className="h-4 w-4 mr-1" />
                  关闭错误
                </Button>
                <Button onClick={fetchPendingComments}>
                  <RefreshCw className="h-4 w-4 mr-1" />
                  立即重试
                </Button>
              </div>
            </div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-4">
      <div className="flex justify-between items-center">
        <h2 className="text-2xl font-bold">评论审核</h2>

        <div className="flex items-center space-x-2 flex-wrap">
          <div className="flex space-x-2 mb-2">
            <Select
              value={filters.targetType}
              onValueChange={(value) => setFilters({ ...filters, targetType: value })}
            >
              <SelectTrigger className="w-[150px]">
                <SelectValue placeholder="评论类型" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="">全部类型</SelectItem>
                <SelectItem value="story">故事评论</SelectItem>
                <SelectItem value="questionnaire">问卷评论</SelectItem>
              </SelectContent>
            </Select>

            <Select
              value={filters.flag}
              onValueChange={(value) => setFilters({ ...filters, flag: value })}
            >
              <SelectTrigger className="w-[150px]">
                <SelectValue placeholder="标记类型" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="">全部标记</SelectItem>
                <SelectItem value="sensitive-words">敏感词</SelectItem>
                <SelectItem value="spam">垃圾信息</SelectItem>
                <SelectItem value="suspicious-behavior">可疑行为</SelectItem>
                <SelectItem value="high-exposure">高曝光</SelectItem>
              </SelectContent>
            </Select>
          </div>

          <div className="flex space-x-2 mb-2">
            <Select
              value={filters.timeRange}
              onValueChange={(value) => setFilters({ ...filters, timeRange: value })}
            >
              <SelectTrigger className="w-[150px]">
                <SelectValue placeholder="时间范围" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="">全部时间</SelectItem>
                <SelectItem value="today">今天</SelectItem>
                <SelectItem value="yesterday">昨天</SelectItem>
                <SelectItem value="week">本周</SelectItem>
                <SelectItem value="month">本月</SelectItem>
              </SelectContent>
            </Select>

            <Select
              value={filters.sortBy}
              onValueChange={(value) => setFilters({ ...filters, sortBy: value })}
            >
              <SelectTrigger className="w-[150px]">
                <SelectValue placeholder="排序方式" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="newest">最新优先</SelectItem>
                <SelectItem value="oldest">最早优先</SelectItem>
                <SelectItem value="flagged">标记优先</SelectItem>
              </SelectContent>
            </Select>

            <Button variant="outline" onClick={() => setFilters({
              targetType: '',
              flag: '',
              timeRange: '',
              sortBy: 'newest'
            })}>
              重置筛选
            </Button>
          </div>

          <Button variant="outline" onClick={() => fetchPendingComments()}>
            <RefreshCw className={`h-4 w-4 mr-2 ${isLoading ? 'animate-spin' : ''}`} />
            刷新
          </Button>
        </div>
      </div>

      {/* 批量操作按钮 */}
      {selectedIds.length > 0 && (
        <div className="bg-muted p-2 rounded-md flex items-center justify-between">
          <div className="text-sm">
            已选择 <span className="font-medium">{selectedIds.length}</span> 项
          </div>
          <div className="flex space-x-2">
            <Button
              variant="outline"
              size="sm"
              onClick={() => setSelectedIds([])}
            >
              取消选择
            </Button>
            <Button
              variant="default"
              size="sm"
              onClick={() => openBatchDialog('approve')}
            >
              <CheckCircle className="h-4 w-4 mr-1" />
              批量通过
            </Button>
            <Button
              variant="destructive"
              size="sm"
              onClick={() => openBatchDialog('reject')}
            >
              <XCircle className="h-4 w-4 mr-1" />
              批量拒绝
            </Button>
          </div>
        </div>
      )}

      {/* 评论列表 */}
      {pendingComments.length === 0 ? (
        <div className="flex flex-col items-center justify-center p-12 bg-muted/50 rounded-lg">
          <CheckCircle className="h-12 w-12 text-muted-foreground mb-4" />
          <h3 className="text-xl font-medium">没有待审核评论</h3>
          <p className="text-muted-foreground mt-2">所有评论已审核完毕</p>
        </div>
      ) : (
        <div className="border rounded-lg">
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead className="w-[40px]">
                  <Checkbox
                    checked={selectedIds.length === pendingComments.length && pendingComments.length > 0}
                    onCheckedChange={(checked) => handleSelectAll(!!checked)}
                    aria-label="全选"
                  />
                </TableHead>
                <TableHead>序列号</TableHead>
                <TableHead>评论内容</TableHead>
                <TableHead>评论对象</TableHead>
                <TableHead>评论者</TableHead>
                <TableHead>标记</TableHead>
                <TableHead>提交时间</TableHead>
                <TableHead>操作</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {pendingComments.map((comment) => (
                <TableRow
                  key={comment.id}
                  className={comment.flags.length > 0 ? 'bg-amber-50' : ''}
                >
                  <TableCell>
                    <Checkbox
                      checked={selectedIds.includes(comment.id)}
                      onCheckedChange={(checked) => handleSelectContent(comment.id, !!checked)}
                      aria-label={`选择 ${comment.sequenceNumber}`}
                    />
                  </TableCell>
                  <TableCell className="font-mono">{comment.sequenceNumber}</TableCell>
                  <TableCell>
                    <div className="max-w-xs group relative">
                      <p className="truncate">
                        {comment.content.length > 50 ? `${comment.content.substring(0, 50)}...` : comment.content}
                      </p>
                      {comment.content.length > 50 && (
                        <div className="absolute hidden group-hover:block z-10 bg-white border rounded-md shadow-md p-2 w-[400px] max-w-full">
                          <p className="text-sm whitespace-pre-wrap">{comment.content}</p>
                        </div>
                      )}
                    </div>
                  </TableCell>
                  <TableCell>
                    <div className="flex items-center">
                      <Badge variant={comment.targetType === 'story' ? 'default' : 'outline'} className="mr-2">
                        {comment.targetType === 'story' ? '故事' : '问卷'}
                      </Badge>
                      <span className="truncate max-w-[150px]" title={comment.targetTitle}>
                        {comment.targetTitle}
                      </span>
                    </div>
                  </TableCell>
                  <TableCell>
                    {comment.userName}
                    {comment.userId && <span className="text-xs text-muted-foreground ml-1">({comment.userId.substring(0, 8)})</span>}
                  </TableCell>
                  <TableCell>
                    <div className="flex flex-wrap">
                      {comment.flags.map((flag) => (
                        <span key={flag}>{renderFlagBadge(flag)}</span>
                      ))}
                    </div>
                  </TableCell>
                  <TableCell>
                    <div className="flex items-center">
                      <Clock className="h-4 w-4 mr-1 text-muted-foreground" />
                      <span title={new Date(comment.createdAt).toLocaleString()}>
                        {formatDistanceToNow(new Date(comment.createdAt), { addSuffix: true, locale: zhCN })}
                      </span>
                    </div>
                  </TableCell>
                  <TableCell>
                    <div className="flex items-center space-x-2">
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => openCommentDialog(comment, 'view')}
                      >
                        <Eye className="h-4 w-4 mr-1" />
                        查看
                      </Button>
                      <Button
                        variant="default"
                        size="sm"
                        onClick={() => openCommentDialog(comment, 'edit')}
                      >
                        <Edit className="h-4 w-4 mr-1" />
                        编辑
                      </Button>
                      <Button
                        variant="destructive"
                        size="sm"
                        onClick={() => openCommentDialog(comment, 'reject')}
                      >
                        <XCircle className="h-4 w-4 mr-1" />
                        拒绝
                      </Button>
                    </div>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>

          {/* 分页 */}
          <div className="flex justify-center py-4">
            <Pagination
              currentPage={pagination.page}
              totalPages={pagination.totalPages}
              onPageChange={handlePageChange}
            />
          </div>
        </div>
      )}

      {/* 评论详情对话框 */}
      <Dialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
        <DialogContent className="max-w-3xl max-h-[90vh] overflow-y-auto">
          {isDetailLoading ? (
            <div className="flex justify-center items-center py-12">
              <LoadingSpinner size="md" text="加载评论详情..." />
            </div>
          ) : selectedComment ? (
            <>
              <DialogHeader>
                <DialogTitle className="flex items-center">
                  {dialogMode === 'view' && <Eye className="h-5 w-5 mr-2 text-primary" />}
                  {dialogMode === 'edit' && <Edit className="h-5 w-5 mr-2 text-primary" />}
                  {dialogMode === 'reject' && <AlertTriangle className="h-5 w-5 mr-2 text-destructive" />}
                  <span>
                    {dialogMode === 'view' && '查看评论'}
                    {dialogMode === 'edit' && '编辑评论'}
                    {dialogMode === 'reject' && '拒绝评论'}
                    {' - '}
                    <span className="font-mono">{selectedComment.sequenceNumber}</span>
                  </span>
                </DialogTitle>
                <DialogDescription className="flex items-center space-x-2">
                  <Clock className="h-4 w-4 text-muted-foreground" />
                  <span>评论于 {new Date(selectedComment.createdAt).toLocaleString()}</span>
                  {selectedComment.flags.length > 0 && (
                    <>
                      <span className="text-muted-foreground">•</span>
                      <span className="flex items-center">
                        <AlertCircle className="h-4 w-4 text-amber-500 mr-1" />
                        <span>已标记 {selectedComment.flags.length} 个问题</span>
                      </span>
                    </>
                  )}
                </DialogDescription>
              </DialogHeader>

              <div className="space-y-4">
                {/* 标记信息 */}
                {selectedComment.flags.length > 0 && (
                  <div className="bg-muted p-3 rounded-md">
                    <h4 className="font-medium mb-2">标记信息</h4>
                    <div className="flex flex-wrap">
                      {selectedComment.flags.map((flag) => (
                        <span key={flag}>{renderFlagBadge(flag)}</span>
                      ))}
                    </div>
                  </div>
                )}

                {/* 评论对象信息 */}
                <div className="bg-muted p-3 rounded-md">
                  <h4 className="font-medium mb-2">评论对象</h4>
                  <div className="flex items-center">
                    <Badge variant={selectedComment.targetType === 'story' ? 'default' : 'outline'} className="mr-2">
                      {selectedComment.targetType === 'story' ? '故事' : '问卷'}
                    </Badge>
                    <span>{selectedComment.targetTitle}</span>
                  </div>
                </div>

                {/* 评论者信息 */}
                <div className="bg-muted p-3 rounded-md">
                  <h4 className="font-medium mb-2">评论者</h4>
                  <div className="grid grid-cols-2 gap-2 text-sm">
                    <div>
                      <span className="font-medium">用户名:</span> {selectedComment.userName}
                    </div>
                    <div>
                      <span className="font-medium">用户ID:</span> {selectedComment.userId}
                    </div>
                    <div>
                      <span className="font-medium">IP地址:</span> {selectedComment.originIp || '未知'}
                    </div>
                  </div>
                </div>

                {/* 评论内容显示/编辑 */}
                <div className="space-y-4">
                  {dialogMode === 'edit' ? (
                    <div>
                      <label className="block text-sm font-medium mb-1">评论内容</label>
                      <Textarea
                        value={editedContent}
                        onChange={(e) => setEditedContent(e.target.value)}
                        rows={5}
                      />
                    </div>
                  ) : (
                    <div>
                      <h4 className="font-medium mb-2">评论内容</h4>
                      <div className="bg-muted p-3 rounded-md whitespace-pre-wrap">
                        {selectedComment.content}
                      </div>
                    </div>
                  )}
                </div>

                {/* 审核意见/拒绝原因 */}
                {dialogMode === 'reject' ? (
                  <div className="space-y-3">
                    <div>
                      <div className="flex justify-between items-center">
                        <label className="block text-sm font-medium mb-1">拒绝原因</label>
                        {!isTemplatesLoading && templates.length > 0 && (
                          <Select
                            onValueChange={(value) => {
                              const template = templates.find(t => t.id === value);
                              if (template && template.type === 'rejection') {
                                applyTemplate(template.content);
                              }
                            }}
                          >
                            <SelectTrigger className="w-[180px]">
                              <SelectValue placeholder="选择拒绝模板" />
                            </SelectTrigger>
                            <SelectContent>
                              {templates
                                .filter(t => t.type === 'rejection')
                                .map(template => (
                                  <SelectItem key={template.id} value={template.id}>
                                    {template.name}
                                  </SelectItem>
                                ))
                              }
                            </SelectContent>
                          </Select>
                        )}
                      </div>
                      <Textarea
                        value={rejectReason}
                        onChange={(e) => setRejectReason(e.target.value)}
                        placeholder="请输入拒绝原因，将通知用户"
                        rows={3}
                      />
                    </div>
                  </div>
                ) : (
                  <div className="space-y-3">
                    <div>
                      <div className="flex justify-between items-center">
                        <label className="block text-sm font-medium mb-1">审核意见（可选）</label>
                        {!isTemplatesLoading && templates.length > 0 && (
                          <Select
                            onValueChange={(value) => {
                              const template = templates.find(t => t.id === value);
                              if (template) {
                                if ((dialogMode === 'view' && template.type === 'approval') ||
                                    (dialogMode === 'edit' && template.type === 'edit')) {
                                  applyTemplate(template.content);
                                }
                              }
                            }}
                          >
                            <SelectTrigger className="w-[180px]">
                              <SelectValue placeholder={`选择${dialogMode === 'edit' ? '编辑' : '通过'}模板`} />
                            </SelectTrigger>
                            <SelectContent>
                              {templates
                                .filter(t => dialogMode === 'edit' ? t.type === 'edit' : t.type === 'approval')
                                .map(template => (
                                  <SelectItem key={template.id} value={template.id}>
                                    {template.name}
                                  </SelectItem>
                                ))
                              }
                            </SelectContent>
                          </Select>
                        )}
                      </div>
                      <Textarea
                        value={reviewNotes}
                        onChange={(e) => setReviewNotes(e.target.value)}
                        placeholder="请输入审核意见（仅管理员可见）"
                        rows={3}
                      />
                    </div>
                  </div>
                )}
              </div>

              <DialogFooter>
                <Button variant="outline" onClick={() => setIsDialogOpen(false)}>
                  取消
                </Button>

                {dialogMode === 'view' && (
                  <Button
                    variant="default"
                    onClick={handleApproveComment}
                    disabled={isSubmitting}
                  >
                    {isSubmitting ? <LoadingSpinner size="sm" /> : <CheckCircle className="h-4 w-4 mr-1" />}
                    通过
                  </Button>
                )}

                {dialogMode === 'edit' && (
                  <Button
                    variant="default"
                    onClick={handleEditComment}
                    disabled={isSubmitting}
                  >
                    {isSubmitting ? <LoadingSpinner size="sm" /> : <Edit className="h-4 w-4 mr-1" />}
                    编辑并通过
                  </Button>
                )}

                {dialogMode === 'reject' && (
                  <Button
                    variant="destructive"
                    onClick={handleRejectComment}
                    disabled={isSubmitting || !rejectReason}
                  >
                    {isSubmitting ? <LoadingSpinner size="sm" /> : <XCircle className="h-4 w-4 mr-1" />}
                    拒绝
                  </Button>
                )}
              </DialogFooter>
            </>
          ) : (
            <div className="text-center py-8">
              <p>评论不存在或已被删除</p>
            </div>
          )}
        </DialogContent>
      </Dialog>

      {/* 确认对话框 */}
      <AlertDialog open={isConfirmDialogOpen} onOpenChange={setIsConfirmDialogOpen}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>
              {confirmAction === 'approve' ? '确认通过' :
               confirmAction === 'edit' ? '确认编辑并通过' : '确认拒绝'}
            </AlertDialogTitle>
            <AlertDialogDescription>
              {confirmAction === 'approve' && '您确定要通过此评论吗？通过后评论将被发布。'}
              {confirmAction === 'edit' && '您确定要编辑并通过此评论吗？编辑后的评论将被发布。'}
              {confirmAction === 'reject' && '您确定要拒绝此评论吗？拒绝后将通知用户。'}
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel disabled={isSubmitting}>取消</AlertDialogCancel>
            <AlertDialogAction
              onClick={executeConfirmedAction}
              disabled={isSubmitting}
              className={
                confirmAction === 'approve' ? 'bg-green-600 hover:bg-green-700' :
                confirmAction === 'edit' ? 'bg-blue-600 hover:bg-blue-700' :
                'bg-red-600 hover:bg-red-700'
              }
            >
              {isSubmitting ? <LoadingSpinner size="sm" /> : (
                confirmAction === 'approve' ? '确认通过' :
                confirmAction === 'edit' ? '确认编辑' : '确认拒绝'
              )}
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>

      {/* 批量操作对话框 */}
      <Dialog open={isBatchDialogOpen} onOpenChange={setIsBatchDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>
              {batchAction === 'approve' ? '批量通过评论' : '批量拒绝评论'}
            </DialogTitle>
            <DialogDescription>
              您选择了 {selectedIds.length} 项评论进行{batchAction === 'approve' ? '批量通过' : '批量拒绝'}操作
            </DialogDescription>
          </DialogHeader>

          <div className="py-4">
            {batchAction === 'reject' ? (
              <div>
                <label className="block text-sm font-medium mb-1">拒绝原因</label>
                <Textarea
                  value={batchRejectReason}
                  onChange={(e) => setBatchRejectReason(e.target.value)}
                  placeholder="请输入拒绝原因，将通知用户"
                  rows={3}
                />
                <p className="text-sm text-muted-foreground mt-1">
                  此拒绝原因将应用于所有选中的评论
                </p>
              </div>
            ) : (
              <div className="bg-muted p-3 rounded-md">
                <p className="text-sm">
                  您即将批量通过 <span className="font-medium">{selectedIds.length}</span> 项评论。
                  这些评论将被发布到平台上，请确保您已经审核过这些评论。
                </p>
              </div>
            )}
          </div>

          <DialogFooter>
            <Button variant="outline" onClick={() => setIsBatchDialogOpen(false)}>
              取消
            </Button>
            <Button
              variant={batchAction === 'approve' ? 'default' : 'destructive'}
              onClick={executeBatchAction}
              disabled={isSubmitting || (batchAction === 'reject' && !batchRejectReason)}
            >
              {isSubmitting ? <LoadingSpinner size="sm" /> : (
                batchAction === 'approve' ?
                <CheckCircle className="h-4 w-4 mr-1" /> :
                <XCircle className="h-4 w-4 mr-1" />
              )}
              {batchAction === 'approve' ? '批量通过' : '批量拒绝'}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
}
