/**
 * 用户管理控制器
 */

import { Context } from 'hono';
import { Env } from '../../types';

/**
 * 获取用户列表
 */
export const getUsers = async (c: Context<{ Bindings: Env }>) => {
  try {
    console.log('🔍 开始获取用户列表');

    // 获取查询参数
    const url = new URL(c.req.url);
    const roleFilter = url.searchParams.get('role');
    const status = url.searchParams.get('status');
    const search = url.searchParams.get('search');
    const page = parseInt(url.searchParams.get('page') || '1', 10);
    const pageSize = parseInt(url.searchParams.get('pageSize') || '10', 10);

    console.log('📋 查询参数:', { roleFilter, status, search, page, pageSize });

    // 构建查询 - 使用正确的字段名
    let query = `
      SELECT
        id,
        email,
        username,
        name,
        role,
        emailVerified,
        lastLoginAt,
        createdAt,
        updatedAt
      FROM User
      WHERE 1=1
    `;

    const params = [];

    // 添加角色过滤
    if (roleFilter && roleFilter !== 'all') {
      query += ` AND role = ?`;
      params.push(roleFilter);
    }

    // 添加状态过滤
    if (status) {
      query += ` AND status = ?`;
      params.push(status);
    }

    // 添加搜索过滤
    if (search) {
      query += ` AND (username LIKE ? OR email LIKE ?)`;
      params.push(`%${search}%`, `%${search}%`);
    }

    // 添加排序
    query += ` ORDER BY created_at DESC`;

    // 添加分页
    const offset = (page - 1) * pageSize;
    query += ` LIMIT ? OFFSET ?`;
    params.push(pageSize, offset);

    console.log('🔍 执行查询:', query);
    console.log('📝 查询参数:', params);

    // 执行查询
    const result = await c.env.DB.prepare(query).bind(...params).all();

    console.log('📊 查询结果:', result);

    // 获取总数
    let countQuery = `
      SELECT COUNT(*) as total
      FROM User
      WHERE 1=1
    `;

    const countParams = [];

    // 添加相同的过滤条件
    if (roleFilter && roleFilter !== 'all') {
      countQuery += ` AND role = ?`;
      countParams.push(roleFilter);
    }

    if (status) {
      countQuery += ` AND status = ?`;
      countParams.push(status);
    }

    if (search) {
      countQuery += ` AND (username LIKE ? OR email LIKE ?)`;
      countParams.push(`%${search}%`, `%${search}%`);
    }

    const countResult = await c.env.DB.prepare(countQuery).bind(...countParams).first();
    const total = countResult?.total || 0;

    console.log('📊 总数:', total);

    return c.json({
      success: true,
      data: {
        users: result.results || [],
        pagination: {
          page,
          pageSize,
          total,
          totalPages: Math.ceil(total / pageSize)
        }
      }
    });

  } catch (error) {
    console.error('❌ 获取用户列表失败:', error);
    return c.json({
      success: false,
      error: '获取用户列表失败',
      details: error.message
    }, 500);
  }
};

/**
 * 获取单个用户详情
 */
export const getUser = async (c: Context<{ Bindings: Env }>) => {
  try {
    const userId = c.req.param('id');
    console.log('🔍 获取用户详情:', userId);

    const result = await c.env.DB.prepare(`
      SELECT
        id,
        email,
        username,
        name,
        role,
        emailVerified,
        lastLoginAt,
        createdAt,
        updatedAt
      FROM User
      WHERE id = ?
    `).bind(userId).first();

    if (!result) {
      return c.json({
        success: false,
        error: '用户不存在'
      }, 404);
    }

    return c.json({
      success: true,
      data: result
    });

  } catch (error) {
    console.error('❌ 获取用户详情失败:', error);
    return c.json({
      success: false,
      error: '获取用户详情失败',
      details: error.message
    }, 500);
  }
};

/**
 * 创建用户
 */
export const createUser = async (c: Context<{ Bindings: Env }>) => {
  try {
    const body = await c.req.json();
    const { username, email, password, role = 'user' } = body;

    console.log('🆕 创建用户:', { username, email, role });

    // 验证必填字段
    if (!username || !email || !password) {
      return c.json({
        success: false,
        error: '用户名、邮箱和密码为必填项'
      }, 400);
    }

    // 检查用户名是否已存在
    const existingUser = await c.env.DB.prepare(`
      SELECT id FROM User WHERE username = ? OR email = ?
    `).bind(username, email).first();

    if (existingUser) {
      return c.json({
        success: false,
        error: '用户名或邮箱已存在'
      }, 400);
    }

    const now = new Date().toISOString();

    // 插入用户
    const result = await c.env.DB.prepare(`
      INSERT INTO User (username, email, passwordHash, role, emailVerified, createdAt, updatedAt)
      VALUES (?, ?, ?, ?, false, ?, ?)
    `).bind(username, email, password, role, now, now).run();

    console.log('✅ 用户创建成功:', result.meta.last_row_id);

    return c.json({
      success: true,
      data: {
        id: result.meta.last_row_id,
        username,
        email,
        role,
        emailVerified: false,
        createdAt: now
      }
    });

  } catch (error) {
    console.error('❌ 创建用户失败:', error);
    return c.json({
      success: false,
      error: '创建用户失败',
      details: error.message
    }, 500);
  }
};

/**
 * 更新用户
 */
export const updateUser = async (c: Context<{ Bindings: Env }>) => {
  try {
    const userId = c.req.param('id');
    const body = await c.req.json();
    const { username, email, role, status } = body;

    console.log('📝 更新用户:', userId, body);

    // 检查用户是否存在
    const existingUser = await c.env.DB.prepare(`
      SELECT id FROM User WHERE id = ?
    `).bind(userId).first();

    if (!existingUser) {
      return c.json({
        success: false,
        error: '用户不存在'
      }, 404);
    }

    // 构建更新查询
    const updates = [];
    const params = [];

    if (username) {
      updates.push('username = ?');
      params.push(username);
    }

    if (email) {
      updates.push('email = ?');
      params.push(email);
    }

    if (role) {
      updates.push('role = ?');
      params.push(role);
    }

    if (status) {
      updates.push('emailVerified = ?');
      params.push(status === 'active');
    }

    if (updates.length === 0) {
      return c.json({
        success: false,
        error: '没有要更新的字段'
      }, 400);
    }

    updates.push('updatedAt = ?');
    params.push(new Date().toISOString());
    params.push(userId);

    const query = `UPDATE User SET ${updates.join(', ')} WHERE id = ?`;

    await c.env.DB.prepare(query).bind(...params).run();

    console.log('✅ 用户更新成功:', userId);

    return c.json({
      success: true,
      message: '用户更新成功'
    });

  } catch (error) {
    console.error('❌ 更新用户失败:', error);
    return c.json({
      success: false,
      error: '更新用户失败',
      details: error.message
    }, 500);
  }
};

/**
 * 删除用户
 */
export const deleteUser = async (c: Context<{ Bindings: Env }>) => {
  try {
    const userId = c.req.param('id');
    console.log('🗑️ 删除用户:', userId);

    // 检查用户是否存在
    const existingUser = await c.env.DB.prepare(`
      SELECT id FROM User WHERE id = ?
    `).bind(userId).first();

    if (!existingUser) {
      return c.json({
        success: false,
        error: '用户不存在'
      }, 404);
    }

    // 删除用户
    await c.env.DB.prepare(`
      DELETE FROM User WHERE id = ?
    `).bind(userId).run();

    console.log('✅ 用户删除成功:', userId);

    return c.json({
      success: true,
      message: '用户删除成功'
    });

  } catch (error) {
    console.error('❌ 删除用户失败:', error);
    return c.json({
      success: false,
      error: '删除用户失败',
      details: error.message
    }, 500);
  }
};

/**
 * 重置用户密码
 */
export const resetUserPassword = async (c: Context<{ Bindings: Env }>) => {
  try {
    const userId = c.req.param('id');
    const body = await c.req.json();
    const { newPassword } = body;

    console.log('🔑 重置用户密码:', userId);

    if (!newPassword) {
      return c.json({
        success: false,
        error: '新密码不能为空'
      }, 400);
    }

    // 检查用户是否存在
    const existingUser = await c.env.DB.prepare(`
      SELECT id FROM User WHERE id = ?
    `).bind(userId).first();

    if (!existingUser) {
      return c.json({
        success: false,
        error: '用户不存在'
      }, 404);
    }

    // 更新密码
    await c.env.DB.prepare(`
      UPDATE User SET passwordHash = ?, updatedAt = ? WHERE id = ?
    `).bind(newPassword, new Date().toISOString(), userId).run();

    console.log('✅ 密码重置成功:', userId);

    return c.json({
      success: true,
      message: '密码重置成功'
    });

  } catch (error) {
    console.error('❌ 重置密码失败:', error);
    return c.json({
      success: false,
      error: '重置密码失败',
      details: error.message
    }, 500);
  }
};
