import React, { useState, useEffect } from 'react';
import SuperAdminLayout from '@/components/layouts/SuperAdminLayout';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { useToast } from '@/components/ui/use-toast';
import {
  Shield, AlertTriangle, CheckCircle, Clock, Eye,
  Lock, Users, Activity, RefreshCw, TrendingUp
} from 'lucide-react';

interface SecurityEvent {
  id: string;
  type: 'login_attempt' | 'permission_denied' | 'suspicious_activity' | 'data_access';
  severity: 'low' | 'medium' | 'high' | 'critical';
  message: string;
  timestamp: string;
  user?: string;
  ip?: string;
}

interface SecurityMetric {
  name: string;
  value: number;
  change: number;
  status: 'good' | 'warning' | 'danger';
  icon: React.ReactNode;
}

/**
 * 安全监控页面
 * 
 * 显示系统安全状态和安全事件
 */
const SecurityPage: React.FC = () => {
  const { toast } = useToast();
  const [isLoading, setIsLoading] = useState(true);
  const [lastUpdate, setLastUpdate] = useState(new Date());

  // 安全事件数据
  const [securityEvents, setSecurityEvents] = useState<SecurityEvent[]>([
    {
      id: '1',
      type: 'login_attempt',
      severity: 'medium',
      message: '多次登录失败尝试',
      timestamp: '2023-06-20 14:30:25',
      user: 'unknown',
      ip: '*************'
    },
    {
      id: '2',
      type: 'permission_denied',
      severity: 'low',
      message: '用户尝试访问未授权页面',
      timestamp: '2023-06-20 14:25:10',
      user: 'user123',
      ip: '*************'
    },
    {
      id: '3',
      type: 'suspicious_activity',
      severity: 'high',
      message: '检测到异常数据访问模式',
      timestamp: '2023-06-20 14:20:45',
      user: 'admin456',
      ip: '*************'
    },
    {
      id: '4',
      type: 'data_access',
      severity: 'critical',
      message: '敏感数据被大量下载',
      timestamp: '2023-06-20 14:15:30',
      user: 'reviewer789',
      ip: '*************'
    }
  ]);

  // 安全指标
  const [securityMetrics, setSecurityMetrics] = useState<SecurityMetric[]>([
    {
      name: '今日登录尝试',
      value: 1247,
      change: 5.2,
      status: 'good',
      icon: <Users className="h-5 w-5" />
    },
    {
      name: '失败登录次数',
      value: 23,
      change: -12.5,
      status: 'warning',
      icon: <Lock className="h-5 w-5" />
    },
    {
      name: '安全事件',
      value: 8,
      change: 15.8,
      status: 'danger',
      icon: <AlertTriangle className="h-5 w-5" />
    },
    {
      name: '活跃会话',
      value: 156,
      change: 2.1,
      status: 'good',
      icon: <Activity className="h-5 w-5" />
    }
  ]);

  useEffect(() => {
    // 模拟数据加载
    setTimeout(() => {
      setIsLoading(false);
    }, 1000);

    // 设置定时刷新
    const interval = setInterval(() => {
      refreshData();
    }, 30000); // 每30秒刷新一次

    return () => clearInterval(interval);
  }, []);

  const refreshData = () => {
    setLastUpdate(new Date());
    
    toast({
      title: '数据已刷新',
      description: '安全监控数据已更新',
    });
  };

  const getSeverityBadge = (severity: string) => {
    switch (severity) {
      case 'low':
        return <Badge className="bg-blue-100 text-blue-800">低</Badge>;
      case 'medium':
        return <Badge className="bg-yellow-100 text-yellow-800">中</Badge>;
      case 'high':
        return <Badge className="bg-orange-100 text-orange-800">高</Badge>;
      case 'critical':
        return <Badge className="bg-red-100 text-red-800">严重</Badge>;
      default:
        return <Badge className="bg-gray-100 text-gray-800">{severity}</Badge>;
    }
  };

  const getEventTypeLabel = (type: string) => {
    switch (type) {
      case 'login_attempt':
        return '登录尝试';
      case 'permission_denied':
        return '权限拒绝';
      case 'suspicious_activity':
        return '可疑活动';
      case 'data_access':
        return '数据访问';
      default:
        return type;
    }
  };

  const getMetricStatusColor = (status: string) => {
    switch (status) {
      case 'good':
        return 'text-green-600';
      case 'warning':
        return 'text-yellow-600';
      case 'danger':
        return 'text-red-600';
      default:
        return 'text-gray-600';
    }
  };

  const getChangeIcon = (change: number) => {
    if (change > 0) {
      return <TrendingUp className="h-4 w-4 text-red-500" />;
    } else {
      return <TrendingUp className="h-4 w-4 text-green-500 transform rotate-180" />;
    }
  };

  return (
    <SuperAdminLayout>
      <div className="container mx-auto py-6">
        <div className="flex justify-between items-center mb-6">
          <div className="flex items-center">
            <Shield className="h-6 w-6 mr-2" />
            <h1 className="text-2xl font-bold">安全监控</h1>
          </div>
          <div className="flex items-center space-x-4">
            <div className="text-sm text-muted-foreground">
              <Clock className="h-4 w-4 inline mr-1" />
              最后更新: {lastUpdate.toLocaleTimeString()}
            </div>
            <Button
              variant="outline"
              onClick={refreshData}
              disabled={isLoading}
            >
              <RefreshCw className={`mr-2 h-4 w-4 ${isLoading ? 'animate-spin' : ''}`} />
              刷新
            </Button>
          </div>
        </div>

        {/* 安全指标概览 */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-6">
          {securityMetrics.map((metric, index) => (
            <Card key={index}>
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div className="flex items-center">
                    <div className={`p-2 rounded-lg ${getMetricStatusColor(metric.status)} bg-opacity-10`}>
                      {metric.icon}
                    </div>
                    <div className="ml-4">
                      <p className="text-sm font-medium text-muted-foreground">{metric.name}</p>
                      <p className="text-2xl font-bold">{metric.value.toLocaleString()}</p>
                    </div>
                  </div>
                  <div className="flex items-center space-x-1">
                    {getChangeIcon(metric.change)}
                    <span className={`text-sm ${metric.change > 0 ? 'text-red-500' : 'text-green-500'}`}>
                      {Math.abs(metric.change)}%
                    </span>
                  </div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {/* 最近安全事件 */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <AlertTriangle className="h-5 w-5 mr-2" />
                最近安全事件
              </CardTitle>
              <CardDescription>
                显示最近发生的安全相关事件
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {securityEvents.map((event) => (
                  <div key={event.id} className="flex items-start justify-between p-3 border rounded-lg">
                    <div className="flex-1">
                      <div className="flex items-center space-x-2 mb-1">
                        <span className="font-medium">{getEventTypeLabel(event.type)}</span>
                        {getSeverityBadge(event.severity)}
                      </div>
                      <p className="text-sm text-muted-foreground mb-2">{event.message}</p>
                      <div className="text-xs text-muted-foreground">
                        <span>时间: {event.timestamp}</span>
                        {event.user && <span className="ml-4">用户: {event.user}</span>}
                        {event.ip && <span className="ml-4">IP: {event.ip}</span>}
                      </div>
                    </div>
                    <Button variant="ghost" size="sm">
                      <Eye className="h-4 w-4" />
                    </Button>
                  </div>
                ))}
              </div>
              <div className="mt-4">
                <Button variant="outline" className="w-full">
                  查看所有事件
                </Button>
              </div>
            </CardContent>
          </Card>

          {/* 安全状态概览 */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <CheckCircle className="h-5 w-5 mr-2" />
                安全状态概览
              </CardTitle>
              <CardDescription>
                系统整体安全状态评估
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div className="flex items-center justify-between p-3 border rounded-lg">
                  <div className="flex items-center space-x-3">
                    <CheckCircle className="h-5 w-5 text-green-500" />
                    <div>
                      <div className="font-medium">防火墙状态</div>
                      <div className="text-sm text-muted-foreground">防火墙运行正常</div>
                    </div>
                  </div>
                  <Badge className="bg-green-100 text-green-800">正常</Badge>
                </div>

                <div className="flex items-center justify-between p-3 border rounded-lg">
                  <div className="flex items-center space-x-3">
                    <CheckCircle className="h-5 w-5 text-green-500" />
                    <div>
                      <div className="font-medium">SSL 证书</div>
                      <div className="text-sm text-muted-foreground">证书有效，90天后到期</div>
                    </div>
                  </div>
                  <Badge className="bg-green-100 text-green-800">有效</Badge>
                </div>

                <div className="flex items-center justify-between p-3 border rounded-lg">
                  <div className="flex items-center space-x-3">
                    <AlertTriangle className="h-5 w-5 text-yellow-500" />
                    <div>
                      <div className="font-medium">入侵检测</div>
                      <div className="text-sm text-muted-foreground">检测到可疑活动</div>
                    </div>
                  </div>
                  <Badge className="bg-yellow-100 text-yellow-800">警告</Badge>
                </div>

                <div className="flex items-center justify-between p-3 border rounded-lg">
                  <div className="flex items-center space-x-3">
                    <CheckCircle className="h-5 w-5 text-green-500" />
                    <div>
                      <div className="font-medium">数据加密</div>
                      <div className="text-sm text-muted-foreground">所有敏感数据已加密</div>
                    </div>
                  </div>
                  <Badge className="bg-green-100 text-green-800">启用</Badge>
                </div>

                <div className="flex items-center justify-between p-3 border rounded-lg">
                  <div className="flex items-center space-x-3">
                    <CheckCircle className="h-5 w-5 text-green-500" />
                    <div>
                      <div className="font-medium">访问控制</div>
                      <div className="text-sm text-muted-foreground">权限控制正常运行</div>
                    </div>
                  </div>
                  <Badge className="bg-green-100 text-green-800">正常</Badge>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* 快速操作 */}
        <Card className="mt-6">
          <CardHeader>
            <CardTitle>快速操作</CardTitle>
            <CardDescription>
              常用的安全管理操作
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <Button variant="outline" className="h-20 flex flex-col items-center justify-center">
                <Lock className="h-6 w-6 mb-2" />
                <span>锁定可疑账户</span>
              </Button>
              <Button variant="outline" className="h-20 flex flex-col items-center justify-center">
                <Eye className="h-6 w-6 mb-2" />
                <span>查看详细日志</span>
              </Button>
              <Button variant="outline" className="h-20 flex flex-col items-center justify-center">
                <AlertTriangle className="h-6 w-6 mb-2" />
                <span>生成安全报告</span>
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>
    </SuperAdminLayout>
  );
};

export default SecurityPage;
