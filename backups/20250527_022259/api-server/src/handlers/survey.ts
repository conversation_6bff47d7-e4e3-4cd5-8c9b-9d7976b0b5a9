/**
 * 问卷相关处理程序
 */

import { verifyToken } from '../utils/auth';
import { generateHash } from '../utils/crypto';
import { generateSequenceNumber } from '../utils/sequence';

/**
 * 处理获取问卷列表
 * @param request 请求对象
 * @param env 环境对象
 * @returns 响应对象
 */
export async function handleGetSurveys(request: Request, env: Env): Promise<Response> {
  try {
    // 获取查询参数
    const url = new URL(request.url);
    const status = url.searchParams.get('status') || 'active';
    const search = url.searchParams.get('search');
    const page = parseInt(url.searchParams.get('page') || '1', 10);
    const pageSize = parseInt(url.searchParams.get('pageSize') || '10', 10);

    // 构建查询
    let query = `
      SELECT * FROM surveys
      WHERE status = ?
    `;

    const params = [status];

    // 添加搜索过滤
    if (search) {
      query += `
        AND (title LIKE ? OR description LIKE ?)
      `;
      params.push(`%${search}%`, `%${search}%`);
    }

    // 添加排序
    query += ` ORDER BY created_at DESC`;

    // 添加分页
    query += ` LIMIT ? OFFSET ?`;
    params.push(pageSize.toString(), ((page - 1) * pageSize).toString());

    // 执行查询
    const surveys = await env.DB.prepare(query).bind(...params).all();

    // 获取总数
    let countQuery = `
      SELECT COUNT(*) as total
      FROM surveys
      WHERE status = ?
    `;

    const countParams = [status];

    // 添加搜索过滤
    if (search) {
      countQuery += `
        AND (title LIKE ? OR description LIKE ?)
      `;
      countParams.push(`%${search}%`, `%${search}%`);
    }

    const totalResult = await env.DB.prepare(countQuery).bind(...countParams).first();
    const total = totalResult ? (totalResult.total as number) : 0;

    // 组织响应数据
    const formattedSurveys = surveys.results.map(survey => ({
      id: survey.id,
      sequenceNumber: survey.sequence_number,
      title: survey.title,
      description: survey.description,
      createdAt: survey.created_at,
      updatedAt: survey.updated_at,
      status: survey.status
    }));

    // 返回响应
    return new Response(JSON.stringify({
      success: true,
      surveys: formattedSurveys,
      total,
      page,
      pageSize,
      totalPages: Math.ceil(total / pageSize)
    }), {
      status: 200,
      headers: { 'Content-Type': 'application/json' }
    });
  } catch (error) {
    console.error('获取问卷列表失败:', error);

    return new Response(JSON.stringify({
      success: false,
      error: '服务器错误'
    }), {
      status: 500,
      headers: { 'Content-Type': 'application/json' }
    });
  }
}

/**
 * 处理获取问卷详情
 * @param request 请求对象
 * @param env 环境对象
 * @param surveyId 问卷ID
 * @returns 响应对象
 */
export async function handleGetSurveyDetail(request: Request, env: Env, surveyId: string): Promise<Response> {
  try {
    // 查询问卷
    const survey = await env.DB.prepare(`
      SELECT * FROM surveys
      WHERE id = ?
    `).bind(surveyId).first();

    if (!survey) {
      return new Response(JSON.stringify({
        success: false,
        error: '问卷不存在'
      }), {
        status: 404,
        headers: { 'Content-Type': 'application/json' }
      });
    }

    // 查询问题
    const questions = await env.DB.prepare(`
      SELECT * FROM questions
      WHERE survey_id = ?
      ORDER BY order_index ASC
    `).bind(surveyId).all();

    // 组织响应数据
    const formattedSurvey = {
      id: survey.id,
      sequenceNumber: survey.sequence_number,
      title: survey.title,
      description: survey.description,
      createdAt: survey.created_at,
      updatedAt: survey.updated_at,
      status: survey.status,
      questions: questions.results.map(question => ({
        id: question.id,
        type: question.type,
        question: question.question,
        options: question.options ? JSON.parse(question.options) : [],
        required: question.required === 1,
        orderIndex: question.order_index
      }))
    };

    // 返回响应
    return new Response(JSON.stringify({
      success: true,
      survey: formattedSurvey
    }), {
      status: 200,
      headers: { 'Content-Type': 'application/json' }
    });
  } catch (error) {
    console.error('获取问卷详情失败:', error);

    return new Response(JSON.stringify({
      success: false,
      error: '服务器错误'
    }), {
      status: 500,
      headers: { 'Content-Type': 'application/json' }
    });
  }
}

/**
 * 处理创建问卷
 * @param request 请求对象
 * @param env 环境对象
 * @returns 响应对象
 */
export async function handleCreateSurvey(request: Request, env: Env): Promise<Response> {
  try {
    // 验证管理员权限
    const authHeader = request.headers.get('Authorization');

    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return new Response(JSON.stringify({
        success: false,
        error: '未授权'
      }), {
        status: 401,
        headers: { 'Content-Type': 'application/json' }
      });
    }

    const token = authHeader.substring(7);
    const tokenResult = verifyToken(token, env.JWT_SECRET);

    if (!tokenResult.valid || !tokenResult.payload) {
      return new Response(JSON.stringify({
        success: false,
        error: '无效的令牌'
      }), {
        status: 401,
        headers: { 'Content-Type': 'application/json' }
      });
    }

    // 验证角色权限
    const role = tokenResult.payload.role;

    if (role !== 'admin' && role !== 'superadmin') {
      return new Response(JSON.stringify({
        success: false,
        error: '没有权限'
      }), {
        status: 403,
        headers: { 'Content-Type': 'application/json' }
      });
    }

    // 解析请求体
    const { title, description, questions } = await request.json() as {
      title: string;
      description: string;
      questions: Array<{
        type: 'radio' | 'checkbox' | 'text';
        question: string;
        options?: string[];
        required: boolean;
      }>;
    };

    // 验证参数
    if (!title || !description || !questions || !Array.isArray(questions) || questions.length === 0) {
      return new Response(JSON.stringify({
        success: false,
        error: '缺少必要参数'
      }), {
        status: 400,
        headers: { 'Content-Type': 'application/json' }
      });
    }

    // 生成序列号
    const sequenceNumber = await generateSequenceNumber(env.DB, 'Q');

    // 创建问卷
    const result = await env.DB.prepare(`
      INSERT INTO surveys (
        sequence_number, title, description, status
      )
      VALUES (?, ?, ?, ?)
    `).bind(
      sequenceNumber,
      title,
      description,
      'active'
    ).run();

    if (!result.success) {
      throw new Error('Failed to create survey');
    }

    // 获取新创建的问卷ID
    const surveyId = result.meta.last_row_id;

    // 添加问题
    for (let i = 0; i < questions.length; i++) {
      const question = questions[i];

      await env.DB.prepare(`
        INSERT INTO questions (
          survey_id, type, question, options, required, order_index
        )
        VALUES (?, ?, ?, ?, ?, ?)
      `).bind(
        surveyId,
        question.type,
        question.question,
        question.options ? JSON.stringify(question.options) : null,
        question.required ? 1 : 0,
        i
      ).run();
    }

    // 返回响应
    return new Response(JSON.stringify({
      success: true,
      surveyId,
      sequenceNumber,
      message: '问卷创建成功'
    }), {
      status: 201,
      headers: { 'Content-Type': 'application/json' }
    });
  } catch (error) {
    console.error('创建问卷失败:', error);

    return new Response(JSON.stringify({
      success: false,
      error: '服务器错误'
    }), {
      status: 500,
      headers: { 'Content-Type': 'application/json' }
    });
  }
}

/**
 * 处理提交问卷回复
 * @param request 请求对象
 * @param env 环境对象
 * @param surveyId 问卷ID
 * @returns 响应对象
 */
export async function handleSubmitSurveyResponse(request: Request, env: Env, surveyId: string): Promise<Response> {
  try {
    // 解析请求体
    const { answers, isAnonymous, identityA, identityB } = await request.json() as {
      answers: Array<{
        questionId: number;
        answer: string | string[];
      }>;
      isAnonymous: boolean;
      identityA: string;
      identityB: string;
    };

    // 验证参数
    if (!answers || !Array.isArray(answers) || answers.length === 0 || !identityA || !identityB) {
      return new Response(JSON.stringify({
        success: false,
        error: '缺少必要参数'
      }), {
        status: 400,
        headers: { 'Content-Type': 'application/json' }
      });
    }

    // 检查问卷是否存在
    const survey = await env.DB.prepare(`
      SELECT * FROM surveys
      WHERE id = ? AND status = 'active'
    `).bind(surveyId).first();

    if (!survey) {
      return new Response(JSON.stringify({
        success: false,
        error: '问卷不存在或已关闭'
      }), {
        status: 404,
        headers: { 'Content-Type': 'application/json' }
      });
    }

    // 生成哈希值
    const identityAHash = generateHash(identityA);
    const identityBHash = generateHash(identityB);

    // 检查是否已提交过
    const existingResponse = await env.DB.prepare(`
      SELECT * FROM survey_responses
      WHERE survey_id = ? AND identity_a_hash = ? AND identity_b_hash = ?
    `).bind(surveyId, identityAHash, identityBHash).first();

    if (existingResponse) {
      return new Response(JSON.stringify({
        success: false,
        error: '您已提交过该问卷'
      }), {
        status: 400,
        headers: { 'Content-Type': 'application/json' }
      });
    }

    // 创建问卷回复
    const result = await env.DB.prepare(`
      INSERT INTO survey_responses (
        survey_id, is_anonymous, status, identity_a_hash, identity_b_hash
      )
      VALUES (?, ?, ?, ?, ?)
    `).bind(
      surveyId,
      isAnonymous ? 1 : 0,
      'pending',
      identityAHash,
      identityBHash
    ).run();

    if (!result.success) {
      throw new Error('Failed to create survey response');
    }

    // 获取新创建的回复ID
    const responseId = result.meta.last_row_id;

    // 添加回答
    for (const answer of answers) {
      await env.DB.prepare(`
        INSERT INTO answers (
          response_id, question_id, answer
        )
        VALUES (?, ?, ?)
      `).bind(
        responseId,
        answer.questionId,
        Array.isArray(answer.answer) ? JSON.stringify(answer.answer) : answer.answer
      ).run();
    }

    // 返回响应
    return new Response(JSON.stringify({
      success: true,
      responseId,
      message: '问卷提交成功，等待审核'
    }), {
      status: 201,
      headers: { 'Content-Type': 'application/json' }
    });
  } catch (error) {
    console.error('提交问卷回复失败:', error);

    return new Response(JSON.stringify({
      success: false,
      error: '服务器错误'
    }), {
      status: 500,
      headers: { 'Content-Type': 'application/json' }
    });
  }
}

/**
 * 处理获取问卷回复列表
 * @param request 请求对象
 * @param env 环境对象
 * @returns 响应对象
 */
export async function handleGetSurveyResponses(request: Request, env: Env): Promise<Response> {
  try {
    // 验证管理员权限
    const authHeader = request.headers.get('Authorization');

    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return new Response(JSON.stringify({
        success: false,
        error: '未授权'
      }), {
        status: 401,
        headers: { 'Content-Type': 'application/json' }
      });
    }

    const token = authHeader.substring(7);
    const tokenResult = verifyToken(token, env.JWT_SECRET);

    if (!tokenResult.valid || !tokenResult.payload) {
      return new Response(JSON.stringify({
        success: false,
        error: '无效的令牌'
      }), {
        status: 401,
        headers: { 'Content-Type': 'application/json' }
      });
    }

    // 验证角色权限
    const role = tokenResult.payload.role;

    if (role !== 'admin' && role !== 'superadmin' && role !== 'reviewer') {
      return new Response(JSON.stringify({
        success: false,
        error: '没有权限'
      }), {
        status: 403,
        headers: { 'Content-Type': 'application/json' }
      });
    }

    // 获取查询参数
    const url = new URL(request.url);
    const surveyId = url.searchParams.get('surveyId');
    const status = url.searchParams.get('status');
    const page = parseInt(url.searchParams.get('page') || '1', 10);
    const pageSize = parseInt(url.searchParams.get('pageSize') || '10', 10);

    // 构建查询
    let query = `
      SELECT sr.*, s.title as survey_title, s.sequence_number as survey_sequence_number
      FROM survey_responses sr
      JOIN surveys s ON sr.survey_id = s.id
      WHERE 1=1
    `;

    const params = [];

    // 添加问卷ID过滤
    if (surveyId) {
      query += ` AND sr.survey_id = ?`;
      params.push(surveyId);
    }

    // 添加状态过滤
    if (status) {
      query += ` AND sr.status = ?`;
      params.push(status);
    }

    // 添加排序
    query += ` ORDER BY sr.created_at DESC`;

    // 添加分页
    query += ` LIMIT ? OFFSET ?`;
    params.push(pageSize.toString(), ((page - 1) * pageSize).toString());

    // 执行查询
    const responses = await env.DB.prepare(query).bind(...params).all();

    // 获取总数
    let countQuery = `
      SELECT COUNT(*) as total
      FROM survey_responses sr
      WHERE 1=1
    `;

    const countParams = [];

    // 添加问卷ID过滤
    if (surveyId) {
      countQuery += ` AND sr.survey_id = ?`;
      countParams.push(surveyId);
    }

    // 添加状态过滤
    if (status) {
      countQuery += ` AND sr.status = ?`;
      countParams.push(status);
    }

    const totalResult = await env.DB.prepare(countQuery).bind(...countParams).first();
    const total = totalResult ? (totalResult.total as number) : 0;

    // 组织响应数据
    const formattedResponses = responses.results.map(response => ({
      id: response.id,
      surveyId: response.survey_id,
      surveyTitle: response.survey_title,
      surveySequenceNumber: response.survey_sequence_number,
      isAnonymous: response.is_anonymous === 1,
      createdAt: response.created_at,
      status: response.status,
      reviewerId: response.reviewer_id,
      reviewedAt: response.reviewed_at,
      rejectionReason: response.rejection_reason
    }));

    // 返回响应
    return new Response(JSON.stringify({
      success: true,
      responses: formattedResponses,
      total,
      page,
      pageSize,
      totalPages: Math.ceil(total / pageSize)
    }), {
      status: 200,
      headers: { 'Content-Type': 'application/json' }
    });
  } catch (error) {
    console.error('获取问卷回复列表失败:', error);

    return new Response(JSON.stringify({
      success: false,
      error: '服务器错误'
    }), {
      status: 500,
      headers: { 'Content-Type': 'application/json' }
    });
  }
}

/**
 * 处理获取问卷回复详情
 * @param request 请求对象
 * @param env 环境对象
 * @param responseId 回复ID
 * @returns 响应对象
 */
export async function handleGetSurveyResponseDetail(request: Request, env: Env, responseId: string): Promise<Response> {
  try {
    // 验证管理员权限
    const authHeader = request.headers.get('Authorization');

    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return new Response(JSON.stringify({
        success: false,
        error: '未授权'
      }), {
        status: 401,
        headers: { 'Content-Type': 'application/json' }
      });
    }

    const token = authHeader.substring(7);
    const tokenResult = verifyToken(token, env.JWT_SECRET);

    if (!tokenResult.valid || !tokenResult.payload) {
      return new Response(JSON.stringify({
        success: false,
        error: '无效的令牌'
      }), {
        status: 401,
        headers: { 'Content-Type': 'application/json' }
      });
    }

    // 验证角色权限
    const role = tokenResult.payload.role;

    if (role !== 'admin' && role !== 'superadmin' && role !== 'reviewer') {
      return new Response(JSON.stringify({
        success: false,
        error: '没有权限'
      }), {
        status: 403,
        headers: { 'Content-Type': 'application/json' }
      });
    }

    // 查询问卷回复
    const response = await env.DB.prepare(`
      SELECT sr.*, s.title as survey_title, s.sequence_number as survey_sequence_number
      FROM survey_responses sr
      JOIN surveys s ON sr.survey_id = s.id
      WHERE sr.id = ?
    `).bind(responseId).first();

    if (!response) {
      return new Response(JSON.stringify({
        success: false,
        error: '问卷回复不存在'
      }), {
        status: 404,
        headers: { 'Content-Type': 'application/json' }
      });
    }

    // 查询回答
    const answers = await env.DB.prepare(`
      SELECT a.*, q.question, q.type
      FROM answers a
      JOIN questions q ON a.question_id = q.id
      WHERE a.response_id = ?
      ORDER BY q.order_index ASC
    `).bind(responseId).all();

    // 组织响应数据
    const formattedResponse = {
      id: response.id,
      surveyId: response.survey_id,
      surveyTitle: response.survey_title,
      surveySequenceNumber: response.survey_sequence_number,
      isAnonymous: response.is_anonymous === 1,
      createdAt: response.created_at,
      status: response.status,
      reviewerId: response.reviewer_id,
      reviewedAt: response.reviewed_at,
      rejectionReason: response.rejection_reason,
      answers: answers.results.map(answer => ({
        id: answer.id,
        questionId: answer.question_id,
        question: answer.question,
        type: answer.type,
        answer: answer.type === 'checkbox' ? JSON.parse(answer.answer) : answer.answer
      }))
    };

    // 返回响应
    return new Response(JSON.stringify({
      success: true,
      response: formattedResponse
    }), {
      status: 200,
      headers: { 'Content-Type': 'application/json' }
    });
  } catch (error) {
    console.error('获取问卷回复详情失败:', error);

    return new Response(JSON.stringify({
      success: false,
      error: '服务器错误'
    }), {
      status: 500,
      headers: { 'Content-Type': 'application/json' }
    });
  }
}

/**
 * 处理审核问卷回复
 * @param request 请求对象
 * @param env 环境对象
 * @param responseId 回复ID
 * @returns 响应对象
 */
export async function handleModerateSurveyResponse(request: Request, env: Env, responseId: string): Promise<Response> {
  try {
    // 验证管理员权限
    const authHeader = request.headers.get('Authorization');

    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return new Response(JSON.stringify({
        success: false,
        error: '未授权'
      }), {
        status: 401,
        headers: { 'Content-Type': 'application/json' }
      });
    }

    const token = authHeader.substring(7);
    const tokenResult = verifyToken(token, env.JWT_SECRET);

    if (!tokenResult.valid || !tokenResult.payload) {
      return new Response(JSON.stringify({
        success: false,
        error: '无效的令牌'
      }), {
        status: 401,
        headers: { 'Content-Type': 'application/json' }
      });
    }

    // 验证角色权限
    const role = tokenResult.payload.role;
    const reviewerId = tokenResult.payload.sub;

    if (role !== 'admin' && role !== 'superadmin' && role !== 'reviewer') {
      return new Response(JSON.stringify({
        success: false,
        error: '没有权限'
      }), {
        status: 403,
        headers: { 'Content-Type': 'application/json' }
      });
    }

    // 解析请求体
    const { status, reason } = await request.json() as {
      status: 'verified' | 'rejected';
      reason?: string;
    };

    // 验证参数
    if (status !== 'verified' && status !== 'rejected') {
      return new Response(JSON.stringify({
        success: false,
        error: '无效的状态'
      }), {
        status: 400,
        headers: { 'Content-Type': 'application/json' }
      });
    }

    // 查询问卷回复
    const response = await env.DB.prepare(`
      SELECT * FROM survey_responses
      WHERE id = ?
    `).bind(responseId).first();

    if (!response) {
      return new Response(JSON.stringify({
        success: false,
        error: '问卷回复不存在'
      }), {
        status: 404,
        headers: { 'Content-Type': 'application/json' }
      });
    }

    // 检查问卷回复状态
    if (response.status !== 'pending') {
      return new Response(JSON.stringify({
        success: false,
        error: '问卷回复已审核'
      }), {
        status: 400,
        headers: { 'Content-Type': 'application/json' }
      });
    }

    // 更新问卷回复状态
    await env.DB.prepare(`
      UPDATE survey_responses
      SET status = ?, reviewer_id = ?, reviewed_at = CURRENT_TIMESTAMP, rejection_reason = ?
      WHERE id = ?
    `).bind(
      status,
      reviewerId,
      status === 'rejected' ? reason || null : null,
      responseId
    ).run();

    // 记录审核记录
    const startTime = Date.now();

    await env.DB.prepare(`
      INSERT INTO review_records (
        reviewer_id, content_id, content_type, decision, review_duration
      )
      VALUES (?, ?, ?, ?, ?)
    `).bind(
      reviewerId,
      responseId,
      'survey_response',
      status,
      Math.floor((Date.now() - startTime) / 1000)
    ).run();

    // 返回响应
    return new Response(JSON.stringify({
      success: true,
      id: responseId,
      status,
      message: status === 'verified' ? '问卷回复已验证' : '问卷回复已被拒绝'
    }), {
      status: 200,
      headers: { 'Content-Type': 'application/json' }
    });
  } catch (error) {
    console.error('审核问卷回复失败:', error);

    return new Response(JSON.stringify({
      success: false,
      error: '服务器错误'
    }), {
      status: 500,
      headers: { 'Content-Type': 'application/json' }
    });
  }
}