/**
 * WebSocket连接类型
 */
export enum WebSocketType {
  STATISTICS = 'statistics',
  RESPONSES = 'responses',
  STORY_WALL = 'story_wall',
}

/**
 * WebSocket消息类型
 */
export enum WebSocketMessageType {
  CONNECT = 'connect',
  DISCONNECT = 'disconnect',
  DATA_UPDATE = 'data_update',
  ERROR = 'error',
  PING = 'ping',
  PONG = 'pong',
}

/**
 * WebSocket消息接口
 */
export interface WebSocketMessage {
  type: WebSocketMessageType;
  data?: any;
  timestamp: number;
}

/**
 * WebSocket客户端选项
 */
export interface WebSocketClientOptions {
  url: string;
  type: WebSocketType;
  token?: string;
  autoReconnect?: boolean;
  reconnectInterval?: number;
  maxReconnectAttempts?: number;
  pingInterval?: number;
  onOpen?: (event: Event) => void;
  onMessage?: (message: WebSocketMessage) => void;
  onClose?: (event: CloseEvent) => void;
  onError?: (event: Event) => void;
  onReconnect?: (attempt: number) => void;
  onReconnectFailed?: () => void;
}

/**
 * WebSocket客户端
 */
export class WebSocketClient {
  private socket: WebSocket | null = null;
  private options: WebSocketClientOptions;
  private reconnectAttempts = 0;
  private reconnectTimer: number | null = null;
  private pingTimer: number | null = null;
  private isConnecting = false;
  private isReconnecting = false;
  private isClosed = false;
  
  constructor(options: WebSocketClientOptions) {
    this.options = {
      autoReconnect: true,
      reconnectInterval: 3000,
      maxReconnectAttempts: 5,
      pingInterval: 30000,
      ...options,
    };
  }
  
  /**
   * 连接WebSocket
   */
  public connect(): void {
    if (this.socket && (this.socket.readyState === WebSocket.OPEN || this.socket.readyState === WebSocket.CONNECTING)) {
      console.log('WebSocket已连接或正在连接');
      return;
    }
    
    if (this.isConnecting) {
      console.log('WebSocket正在连接中');
      return;
    }
    
    this.isConnecting = true;
    this.isClosed = false;
    
    // 构建URL
    let url = this.options.url;
    if (!url.endsWith('/')) {
      url += '/';
    }
    url += this.options.type;
    
    // 添加token
    if (this.options.token) {
      url += `?token=${this.options.token}`;
    }
    
    // 创建WebSocket
    this.socket = new WebSocket(url);
    
    // 设置事件处理程序
    this.socket.addEventListener('open', this.handleOpen);
    this.socket.addEventListener('message', this.handleMessage);
    this.socket.addEventListener('close', this.handleClose);
    this.socket.addEventListener('error', this.handleError);
  }
  
  /**
   * 断开WebSocket连接
   */
  public disconnect(): void {
    this.isClosed = true;
    this.clearTimers();
    
    if (this.socket) {
      this.socket.close();
      this.socket = null;
    }
  }
  
  /**
   * 发送消息
   */
  public send(message: Omit<WebSocketMessage, 'timestamp'>): void {
    if (!this.socket || this.socket.readyState !== WebSocket.OPEN) {
      console.error('WebSocket未连接');
      return;
    }
    
    const fullMessage: WebSocketMessage = {
      ...message,
      timestamp: Date.now(),
    };
    
    this.socket.send(JSON.stringify(fullMessage));
  }
  
  /**
   * 发送ping消息
   */
  private sendPing(): void {
    this.send({
      type: WebSocketMessageType.PING,
    });
  }
  
  /**
   * 处理WebSocket打开事件
   */
  private handleOpen = (event: Event): void => {
    console.log('WebSocket已连接');
    this.isConnecting = false;
    this.isReconnecting = false;
    this.reconnectAttempts = 0;
    
    // 启动ping定时器
    if (this.options.pingInterval && this.options.pingInterval > 0) {
      this.pingTimer = window.setInterval(() => {
        this.sendPing();
      }, this.options.pingInterval);
    }
    
    // 调用回调
    if (this.options.onOpen) {
      this.options.onOpen(event);
    }
  };
  
  /**
   * 处理WebSocket消息事件
   */
  private handleMessage = (event: MessageEvent): void => {
    try {
      const message = JSON.parse(event.data) as WebSocketMessage;
      
      // 调用回调
      if (this.options.onMessage) {
        this.options.onMessage(message);
      }
    } catch (error) {
      console.error('解析WebSocket消息错误:', error);
    }
  };
  
  /**
   * 处理WebSocket关闭事件
   */
  private handleClose = (event: CloseEvent): void => {
    console.log('WebSocket已关闭:', event.code, event.reason);
    this.isConnecting = false;
    this.clearTimers();
    
    // 调用回调
    if (this.options.onClose) {
      this.options.onClose(event);
    }
    
    // 自动重连
    if (this.options.autoReconnect && !this.isClosed) {
      this.reconnect();
    }
  };
  
  /**
   * 处理WebSocket错误事件
   */
  private handleError = (event: Event): void => {
    console.error('WebSocket错误:', event);
    
    // 调用回调
    if (this.options.onError) {
      this.options.onError(event);
    }
  };
  
  /**
   * 重连WebSocket
   */
  private reconnect(): void {
    if (this.isReconnecting || this.isClosed) {
      return;
    }
    
    this.isReconnecting = true;
    this.reconnectAttempts++;
    
    if (this.options.maxReconnectAttempts && this.reconnectAttempts > this.options.maxReconnectAttempts) {
      console.log('WebSocket重连失败，已达到最大重连次数');
      
      // 调用回调
      if (this.options.onReconnectFailed) {
        this.options.onReconnectFailed();
      }
      
      return;
    }
    
    console.log(`WebSocket重连中，第${this.reconnectAttempts}次尝试...`);
    
    // 调用回调
    if (this.options.onReconnect) {
      this.options.onReconnect(this.reconnectAttempts);
    }
    
    // 设置重连定时器
    this.reconnectTimer = window.setTimeout(() => {
      this.isReconnecting = false;
      this.connect();
    }, this.options.reconnectInterval);
  }
  
  /**
   * 清除定时器
   */
  private clearTimers(): void {
    if (this.pingTimer) {
      clearInterval(this.pingTimer);
      this.pingTimer = null;
    }
    
    if (this.reconnectTimer) {
      clearTimeout(this.reconnectTimer);
      this.reconnectTimer = null;
    }
  }
}

/**
 * 创建WebSocket客户端
 */
export function createWebSocketClient(options: WebSocketClientOptions): WebSocketClient {
  return new WebSocketClient(options);
}
