import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Switch } from '@/components/ui/switch';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Badge } from '@/components/ui/badge';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import { useToast } from '@/components/ui/use-toast';
import SuperAdminLayout from '@/components/layouts/SuperAdminLayout';
import {
  Database, Settings, Shield, Clock, Save, TestTube,
  AlertTriangle, CheckCircle, Wifi, Server, HardDrive
} from 'lucide-react';

// 配置数据类型定义
interface DatabaseConfig {
  host: string;
  port: number;
  database: string;
  maxConnections: number;
  connectionTimeout: number;
  retryAttempts: number;
  sslEnabled: boolean;
}

interface BackupConfig {
  enabled: boolean;
  frequency: 'daily' | 'weekly' | 'monthly';
  time: string;
  retentionDays: number;
  format: 'sql' | 'csv' | 'json';
  location: 'local' | 'cloud';
}

interface MonitorConfig {
  enabled: boolean;
  checkInterval: number;
  cpuThreshold: number;
  memoryThreshold: number;
  diskThreshold: number;
  emailNotifications: boolean;
  notificationEmail: string;
}

/**
 * 数据库配置页面
 */
const DatabaseConfigPage: React.FC = () => {
  const { toast } = useToast();
  const [isLoading, setIsLoading] = useState(false);
  const [isTesting, setIsTesting] = useState(false);
  const [connectionStatus, setConnectionStatus] = useState<'unknown' | 'success' | 'failed'>('unknown');

  // 配置状态
  const [databaseConfig, setDatabaseConfig] = useState<DatabaseConfig>({
    host: 'cloudflare-d1.database',
    port: 5432,
    database: 'college_employment_survey',
    maxConnections: 10,
    connectionTimeout: 30,
    retryAttempts: 3,
    sslEnabled: true
  });

  const [backupConfig, setBackupConfig] = useState<BackupConfig>({
    enabled: true,
    frequency: 'daily',
    time: '02:00',
    retentionDays: 30,
    format: 'sql',
    location: 'cloud'
  });

  const [monitorConfig, setMonitorConfig] = useState<MonitorConfig>({
    enabled: true,
    checkInterval: 5,
    cpuThreshold: 80,
    memoryThreshold: 85,
    diskThreshold: 90,
    emailNotifications: true,
    notificationEmail: '<EMAIL>'
  });

  // 加载配置
  useEffect(() => {
    loadConfigurations();
  }, []);

  const loadConfigurations = async () => {
    try {
      setIsLoading(true);

      const response = await fetch('/api/admin/database/config');

      if (!response.ok) {
        throw new Error(`API调用失败: ${response.status}`);
      }

      const data = await response.json();

      if (data.success) {
        // 更新配置状态
        if (data.data.database) {
          setDatabaseConfig(data.data.database);
        }
        if (data.data.backup) {
          setBackupConfig(data.data.backup);
        }
        if (data.data.monitor) {
          setMonitorConfig(data.data.monitor);
        }

        toast({
          title: '配置加载成功',
          description: '已加载当前数据库配置'
        });
      } else {
        throw new Error(data.error || '获取配置失败');
      }
    } catch (error) {
      console.error('加载配置失败:', error);
      toast({
        variant: 'destructive',
        title: '加载配置失败',
        description: error instanceof Error ? error.message : '无法加载当前配置，请稍后重试'
      });
    } finally {
      setIsLoading(false);
    }
  };

  // 测试数据库连接
  const testConnection = async () => {
    try {
      setIsTesting(true);
      setConnectionStatus('unknown');

      const response = await fetch('/api/admin/database/test-connection', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(databaseConfig)
      });

      if (!response.ok) {
        throw new Error(`API调用失败: ${response.status}`);
      }

      const data = await response.json();

      if (data.success) {
        setConnectionStatus('success');
        toast({
          title: '连接测试成功',
          description: `数据库连接正常，响应时间: ${data.data.responseTime}ms`
        });
      } else {
        throw new Error(data.message || '连接测试失败');
      }
    } catch (error) {
      setConnectionStatus('failed');
      toast({
        variant: 'destructive',
        title: '连接测试失败',
        description: error instanceof Error ? error.message : '无法连接到数据库，请检查配置'
      });
    } finally {
      setIsTesting(false);
    }
  };

  // 保存配置
  const saveConfiguration = async () => {
    try {
      setIsLoading(true);

      const response = await fetch('/api/admin/database/config', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          database: databaseConfig,
          backup: backupConfig,
          monitor: monitorConfig
        })
      });

      if (!response.ok) {
        throw new Error(`API调用失败: ${response.status}`);
      }

      const data = await response.json();

      if (data.success) {
        toast({
          title: '配置保存成功',
          description: data.message || '数据库配置已更新'
        });
      } else {
        throw new Error(data.error || '保存配置失败');
      }
    } catch (error) {
      console.error('保存配置失败:', error);
      toast({
        variant: 'destructive',
        title: '保存配置失败',
        description: error instanceof Error ? error.message : '无法保存配置，请稍后重试'
      });
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <SuperAdminLayout>
      <div className="container mx-auto py-6">
        <div className="flex justify-between items-center mb-6">
          <div className="flex items-center">
            <Settings className="h-6 w-6 mr-2" />
            <h1 className="text-2xl font-bold">数据库配置</h1>
            <Badge variant="secondary" className="ml-3">
              系统设置
            </Badge>
          </div>
          <div className="flex space-x-3">
            <Button
              variant="outline"
              onClick={testConnection}
              disabled={isTesting}
            >
              <TestTube className="mr-2 h-4 w-4" />
              {isTesting ? '测试中...' : '测试连接'}
            </Button>
            <Button
              onClick={saveConfiguration}
              disabled={isLoading}
            >
              <Save className="mr-2 h-4 w-4" />
              {isLoading ? '保存中...' : '保存配置'}
            </Button>
          </div>
        </div>

        {/* 连接状态提示 */}
        {connectionStatus !== 'unknown' && (
          <Alert className={`mb-6 ${connectionStatus === 'success' ? 'border-green-200 bg-green-50' : 'border-red-200 bg-red-50'}`}>
            {connectionStatus === 'success' ? (
              <CheckCircle className="h-4 w-4 text-green-600" />
            ) : (
              <AlertTriangle className="h-4 w-4 text-red-600" />
            )}
            <AlertTitle>
              {connectionStatus === 'success' ? '连接成功' : '连接失败'}
            </AlertTitle>
            <AlertDescription>
              {connectionStatus === 'success'
                ? '数据库连接测试通过，配置正确'
                : '数据库连接测试失败，请检查配置参数'
              }
            </AlertDescription>
          </Alert>
        )}

        <Tabs defaultValue="database" className="space-y-4">
          <TabsList>
            <TabsTrigger value="database">
              <Database className="mr-2 h-4 w-4" />
              数据库连接
            </TabsTrigger>
            <TabsTrigger value="backup">
              <HardDrive className="mr-2 h-4 w-4" />
              备份设置
            </TabsTrigger>
            <TabsTrigger value="monitor">
              <Wifi className="mr-2 h-4 w-4" />
              监控配置
            </TabsTrigger>
          </TabsList>

          {/* 数据库连接配置 */}
          <TabsContent value="database">
            <Card>
              <CardHeader>
                <CardTitle>数据库连接配置</CardTitle>
                <CardDescription>
                  配置数据库连接参数和连接池设置
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-6">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div className="space-y-2">
                    <Label htmlFor="host">主机地址</Label>
                    <Input
                      id="host"
                      value={databaseConfig.host}
                      onChange={(e) => setDatabaseConfig(prev => ({ ...prev, host: e.target.value }))}
                      placeholder="数据库主机地址"
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="port">端口</Label>
                    <Input
                      id="port"
                      type="number"
                      value={databaseConfig.port}
                      onChange={(e) => setDatabaseConfig(prev => ({ ...prev, port: parseInt(e.target.value) }))}
                      placeholder="5432"
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="database">数据库名称</Label>
                    <Input
                      id="database"
                      value={databaseConfig.database}
                      onChange={(e) => setDatabaseConfig(prev => ({ ...prev, database: e.target.value }))}
                      placeholder="数据库名称"
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="maxConnections">最大连接数</Label>
                    <Input
                      id="maxConnections"
                      type="number"
                      value={databaseConfig.maxConnections}
                      onChange={(e) => setDatabaseConfig(prev => ({ ...prev, maxConnections: parseInt(e.target.value) }))}
                      placeholder="10"
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="connectionTimeout">连接超时 (秒)</Label>
                    <Input
                      id="connectionTimeout"
                      type="number"
                      value={databaseConfig.connectionTimeout}
                      onChange={(e) => setDatabaseConfig(prev => ({ ...prev, connectionTimeout: parseInt(e.target.value) }))}
                      placeholder="30"
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="retryAttempts">重试次数</Label>
                    <Input
                      id="retryAttempts"
                      type="number"
                      value={databaseConfig.retryAttempts}
                      onChange={(e) => setDatabaseConfig(prev => ({ ...prev, retryAttempts: parseInt(e.target.value) }))}
                      placeholder="3"
                    />
                  </div>
                </div>

                <div className="flex items-center space-x-2">
                  <Switch
                    id="sslEnabled"
                    checked={databaseConfig.sslEnabled}
                    onCheckedChange={(checked) => setDatabaseConfig(prev => ({ ...prev, sslEnabled: checked }))}
                  />
                  <Label htmlFor="sslEnabled">启用SSL连接</Label>
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          {/* 备份设置 */}
          <TabsContent value="backup">
            <Card>
              <CardHeader>
                <CardTitle>备份设置</CardTitle>
                <CardDescription>
                  配置自动备份策略和备份保留规则
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-6">
                <div className="flex items-center space-x-2">
                  <Switch
                    id="backupEnabled"
                    checked={backupConfig.enabled}
                    onCheckedChange={(checked) => setBackupConfig(prev => ({ ...prev, enabled: checked }))}
                  />
                  <Label htmlFor="backupEnabled">启用自动备份</Label>
                </div>

                {backupConfig.enabled && (
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div className="space-y-2">
                      <Label htmlFor="frequency">备份频率</Label>
                      <Select
                        value={backupConfig.frequency}
                        onValueChange={(value: 'daily' | 'weekly' | 'monthly') =>
                          setBackupConfig(prev => ({ ...prev, frequency: value }))
                        }
                      >
                        <SelectTrigger>
                          <SelectValue placeholder="选择备份频率" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="daily">每日备份</SelectItem>
                          <SelectItem value="weekly">每周备份</SelectItem>
                          <SelectItem value="monthly">每月备份</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>

                    <div className="space-y-2">
                      <Label htmlFor="backupTime">备份时间</Label>
                      <Input
                        id="backupTime"
                        type="time"
                        value={backupConfig.time}
                        onChange={(e) => setBackupConfig(prev => ({ ...prev, time: e.target.value }))}
                      />
                    </div>

                    <div className="space-y-2">
                      <Label htmlFor="retentionDays">保留天数</Label>
                      <Input
                        id="retentionDays"
                        type="number"
                        value={backupConfig.retentionDays}
                        onChange={(e) => setBackupConfig(prev => ({ ...prev, retentionDays: parseInt(e.target.value) }))}
                        placeholder="30"
                      />
                    </div>

                    <div className="space-y-2">
                      <Label htmlFor="backupFormat">备份格式</Label>
                      <Select
                        value={backupConfig.format}
                        onValueChange={(value: 'sql' | 'csv' | 'json') =>
                          setBackupConfig(prev => ({ ...prev, format: value }))
                        }
                      >
                        <SelectTrigger>
                          <SelectValue placeholder="选择备份格式" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="sql">SQL 脚本</SelectItem>
                          <SelectItem value="csv">CSV 文件</SelectItem>
                          <SelectItem value="json">JSON 文件</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>

                    <div className="space-y-2">
                      <Label htmlFor="backupLocation">存储位置</Label>
                      <Select
                        value={backupConfig.location}
                        onValueChange={(value: 'local' | 'cloud') =>
                          setBackupConfig(prev => ({ ...prev, location: value }))
                        }
                      >
                        <SelectTrigger>
                          <SelectValue placeholder="选择存储位置" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="local">本地存储</SelectItem>
                          <SelectItem value="cloud">云端存储</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                  </div>
                )}

                <Alert>
                  <Clock className="h-4 w-4" />
                  <AlertTitle>备份提醒</AlertTitle>
                  <AlertDescription>
                    • 建议在业务低峰期进行备份操作<br/>
                    • 备份文件会自动压缩以节省存储空间<br/>
                    • 超过保留期限的备份文件将自动删除
                  </AlertDescription>
                </Alert>
              </CardContent>
            </Card>
          </TabsContent>

          {/* 监控配置 */}
          <TabsContent value="monitor">
            <Card>
              <CardHeader>
                <CardTitle>监控配置</CardTitle>
                <CardDescription>
                  配置系统监控和告警通知设置
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-6">
                <div className="flex items-center space-x-2">
                  <Switch
                    id="monitorEnabled"
                    checked={monitorConfig.enabled}
                    onCheckedChange={(checked) => setMonitorConfig(prev => ({ ...prev, enabled: checked }))}
                  />
                  <Label htmlFor="monitorEnabled">启用系统监控</Label>
                </div>

                {monitorConfig.enabled && (
                  <>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                      <div className="space-y-2">
                        <Label htmlFor="checkInterval">检查间隔 (分钟)</Label>
                        <Input
                          id="checkInterval"
                          type="number"
                          value={monitorConfig.checkInterval}
                          onChange={(e) => setMonitorConfig(prev => ({ ...prev, checkInterval: parseInt(e.target.value) }))}
                          placeholder="5"
                        />
                      </div>

                      <div className="space-y-2">
                        <Label htmlFor="cpuThreshold">CPU 告警阈值 (%)</Label>
                        <Input
                          id="cpuThreshold"
                          type="number"
                          value={monitorConfig.cpuThreshold}
                          onChange={(e) => setMonitorConfig(prev => ({ ...prev, cpuThreshold: parseInt(e.target.value) }))}
                          placeholder="80"
                        />
                      </div>

                      <div className="space-y-2">
                        <Label htmlFor="memoryThreshold">内存告警阈值 (%)</Label>
                        <Input
                          id="memoryThreshold"
                          type="number"
                          value={monitorConfig.memoryThreshold}
                          onChange={(e) => setMonitorConfig(prev => ({ ...prev, memoryThreshold: parseInt(e.target.value) }))}
                          placeholder="85"
                        />
                      </div>

                      <div className="space-y-2">
                        <Label htmlFor="diskThreshold">磁盘告警阈值 (%)</Label>
                        <Input
                          id="diskThreshold"
                          type="number"
                          value={monitorConfig.diskThreshold}
                          onChange={(e) => setMonitorConfig(prev => ({ ...prev, diskThreshold: parseInt(e.target.value) }))}
                          placeholder="90"
                        />
                      </div>
                    </div>

                    <div className="space-y-4">
                      <div className="flex items-center space-x-2">
                        <Switch
                          id="emailNotifications"
                          checked={monitorConfig.emailNotifications}
                          onCheckedChange={(checked) => setMonitorConfig(prev => ({ ...prev, emailNotifications: checked }))}
                        />
                        <Label htmlFor="emailNotifications">启用邮件通知</Label>
                      </div>

                      {monitorConfig.emailNotifications && (
                        <div className="space-y-2">
                          <Label htmlFor="notificationEmail">通知邮箱</Label>
                          <Input
                            id="notificationEmail"
                            type="email"
                            value={monitorConfig.notificationEmail}
                            onChange={(e) => setMonitorConfig(prev => ({ ...prev, notificationEmail: e.target.value }))}
                            placeholder="<EMAIL>"
                          />
                        </div>
                      )}
                    </div>

                    <Alert>
                      <Server className="h-4 w-4" />
                      <AlertTitle>监控说明</AlertTitle>
                      <AlertDescription>
                        • 系统会定期检查服务器资源使用情况<br/>
                        • 超过阈值时会自动发送告警通知<br/>
                        • 建议根据实际业务需求调整告警阈值
                      </AlertDescription>
                    </Alert>
                  </>
                )}
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
      </div>
    </SuperAdminLayout>
  );
};

export default DatabaseConfigPage;
