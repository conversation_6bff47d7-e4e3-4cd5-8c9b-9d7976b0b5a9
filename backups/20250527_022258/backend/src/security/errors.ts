/**
 * 安全模块错误类型和错误处理工具
 */

/**
 * 安全错误基类
 */
export class SecurityError extends Error {
  public code: string;
  public statusCode: number;
  public severity: 'low' | 'medium' | 'high' | 'critical';
  public context: Record<string, any>;
  public timestamp: number;
  public requestId?: string;
  public clientIp?: string;

  /**
   * 构造函数
   * @param message 错误消息
   * @param options 错误选项
   */
  constructor(message: string, options: {
    code?: string;
    statusCode?: number;
    severity?: 'low' | 'medium' | 'high' | 'critical';
    context?: Record<string, any>;
    requestId?: string;
    clientIp?: string;
    cause?: Error;
  } = {}) {
    super(message, { cause: options.cause });
    this.name = this.constructor.name;
    this.code = options.code || 'SECURITY_ERROR';
    this.statusCode = options.statusCode || 400;
    this.severity = options.severity || 'medium';
    this.context = options.context || {};
    this.timestamp = Date.now();
    this.requestId = options.requestId;
    this.clientIp = options.clientIp;
  }

  /**
   * 转换为日志对象
   */
  toLogObject(): Record<string, any> {
    return {
      name: this.name,
      message: this.message,
      code: this.code,
      statusCode: this.statusCode,
      severity: this.severity,
      context: this.context,
      timestamp: this.timestamp,
      requestId: this.requestId,
      clientIp: this.clientIp,
      stack: this.stack,
      cause: this.cause instanceof Error ? {
        name: this.cause.name,
        message: this.cause.message,
        stack: this.cause.stack
      } : this.cause
    };
  }

  /**
   * 转换为 JSON 响应
   */
  toResponse(): { success: false; error: string; code: string; details?: any } {
    return {
      success: false,
      error: this.message,
      code: this.code,
      ...(Object.keys(this.context).length > 0 && { details: this.context })
    };
  }
}

/**
 * 验证错误
 */
export class ValidationError extends SecurityError {
  constructor(message: string, options: {
    code?: string;
    statusCode?: number;
    severity?: 'low' | 'medium' | 'high' | 'critical';
    context?: Record<string, any>;
    requestId?: string;
    clientIp?: string;
    cause?: Error;
  } = {}) {
    super(message, {
      ...options,
      code: options.code || 'VALIDATION_ERROR',
      statusCode: options.statusCode || 400,
      severity: options.severity || 'low'
    });
  }
}

/**
 * 速率限制错误
 */
export class RateLimitError extends SecurityError {
  constructor(message: string, options: {
    code?: string;
    statusCode?: number;
    severity?: 'low' | 'medium' | 'high' | 'critical';
    context?: Record<string, any>;
    requestId?: string;
    clientIp?: string;
    cause?: Error;
  } = {}) {
    super(message, {
      ...options,
      code: options.code || 'RATE_LIMIT_ERROR',
      statusCode: options.statusCode || 429,
      severity: options.severity || 'medium'
    });
  }
}

/**
 * 验证码错误
 */
export class CaptchaError extends SecurityError {
  constructor(message: string, options: {
    code?: string;
    statusCode?: number;
    severity?: 'low' | 'medium' | 'high' | 'critical';
    context?: Record<string, any>;
    requestId?: string;
    clientIp?: string;
    cause?: Error;
  } = {}) {
    super(message, {
      ...options,
      code: options.code || 'CAPTCHA_ERROR',
      statusCode: options.statusCode || 400,
      severity: options.severity || 'medium'
    });
  }
}

/**
 * 可疑活动错误
 */
export class SuspiciousActivityError extends SecurityError {
  constructor(message: string, options: {
    code?: string;
    statusCode?: number;
    severity?: 'low' | 'medium' | 'high' | 'critical';
    context?: Record<string, any>;
    requestId?: string;
    clientIp?: string;
    cause?: Error;
  } = {}) {
    super(message, {
      ...options,
      code: options.code || 'SUSPICIOUS_ACTIVITY',
      statusCode: options.statusCode || 403,
      severity: options.severity || 'high'
    });
  }
}

/**
 * 配置错误
 */
export class ConfigurationError extends SecurityError {
  constructor(message: string, options: {
    code?: string;
    statusCode?: number;
    severity?: 'low' | 'medium' | 'high' | 'critical';
    context?: Record<string, any>;
    requestId?: string;
    clientIp?: string;
    cause?: Error;
  } = {}) {
    super(message, {
      ...options,
      code: options.code || 'CONFIGURATION_ERROR',
      statusCode: options.statusCode || 500,
      severity: options.severity || 'high'
    });
  }
}

/**
 * 存储错误
 */
export class StorageError extends SecurityError {
  constructor(message: string, options: {
    code?: string;
    statusCode?: number;
    severity?: 'low' | 'medium' | 'high' | 'critical';
    context?: Record<string, any>;
    requestId?: string;
    clientIp?: string;
    cause?: Error;
  } = {}) {
    super(message, {
      ...options,
      code: options.code || 'STORAGE_ERROR',
      statusCode: options.statusCode || 500,
      severity: options.severity || 'high'
    });
  }
}

/**
 * 创建错误处理函数
 * @param logger 日志记录器
 */
export function createErrorHandler(logger: any) {
  /**
   * 处理错误
   * @param error 错误对象
   * @param requestId 请求 ID
   * @param clientIp 客户端 IP
   * @param env 环境变量
   */
  return async function handleError(
    error: Error | SecurityError,
    requestId?: string,
    clientIp?: string,
    env?: any
  ): Promise<SecurityError> {
    // 如果不是 SecurityError，转换为 SecurityError
    if (!(error instanceof SecurityError)) {
      error = new SecurityError(error.message, {
        code: 'UNKNOWN_ERROR',
        statusCode: 500,
        severity: 'high',
        context: { originalError: error.name },
        requestId,
        clientIp,
        cause: error
      });
    }

    // 确保错误有请求 ID 和客户端 IP
    if (requestId && !error.requestId) {
      error.requestId = requestId;
    }
    
    if (clientIp && !error.clientIp) {
      error.clientIp = clientIp;
    }

    // 记录错误
    const logLevel = error.severity === 'critical' || error.severity === 'high' 
      ? 'error' 
      : error.severity === 'medium' ? 'warn' : 'info';

    await logger.log(
      logLevel,
      `Security error: ${error.message}`,
      error.toLogObject(),
      error.clientIp,
      env
    );

    return error as SecurityError;
  };
}
