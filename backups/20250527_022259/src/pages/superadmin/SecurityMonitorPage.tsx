import React, { useState, useEffect } from 'react';
import SuperAdminLayout from '@/components/layouts/SuperAdminLayout';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Tabs, Ta<PERSON>Content, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { useToast } from '@/components/ui/use-toast';
import {
  Shield, AlertTriangle, AlertCircle, CheckCircle, RefreshCw,
  Lock, UserX, Globe, Activity, Cpu, HardDrive, Database,
  BarChart2, PieChart, LineChart, Clock, Download, Filter,
  ExternalLink
} from 'lucide-react';
import { Permission } from '@/lib/permissions';
import { usePermission } from '@/hooks/usePermission';
import { useNavigate } from 'react-router-dom';
import {
  getSecurityEvents,
  getSystemResources,
  getSecurityStats,
  SecurityEvent,
  SystemResources
} from '@/services/securityMonitorService';

// 系统资源类型（用于显示）
interface SystemResource {
  name: string;
  usage: number;
  limit: number;
  status: 'normal' | 'warning' | 'critical';
}

/**
 * 系统安全监控页面
 *
 * 超级管理员专属功能，用于监控系统安全状态
 */
export default function SecurityMonitorPage() {
  const { toast } = useToast();
  const { checkPermission } = usePermission();
  const navigate = useNavigate();
  const [loading, setLoading] = useState(true);
  const [securityEvents, setSecurityEvents] = useState<SecurityEvent[]>([]);
  const [systemResourcesData, setSystemResourcesData] = useState<SystemResource[]>([]);
  const [securityScore, setSecurityScore] = useState(0);
  const [threatLevel, setThreatLevel] = useState<'low' | 'medium' | 'high' | 'critical'>('low');
  const [activeThreats, setActiveThreats] = useState(0);
  const [resolvedThreats, setResolvedThreats] = useState(0);

  // 检查权限
  const canViewSecurity = checkPermission(Permission.SECURITY_MONITOR);
  const canEditSecurity = checkPermission(Permission.SECURITY_MONITOR);
  const canViewLogs = checkPermission(Permission.SECURITY_LOGS);

  // 加载安全数据
  const loadSecurityData = async () => {
    if (!canViewSecurity) {
      toast({
        title: '权限不足',
        description: '您没有查看安全监控的权限',
        variant: 'destructive'
      });
      return;
    }

    try {
      setLoading(true);

      // 并行获取所有数据
      const [eventsResponse, resourcesResponse, statsResponse] = await Promise.all([
        getSecurityEvents(),
        getSystemResources(),
        getSecurityStats()
      ]);

      if (eventsResponse.success) {
        setSecurityEvents(eventsResponse.data);
      } else {
        toast({
          title: '获取安全事件失败',
          description: eventsResponse.message || '无法加载安全事件',
          variant: 'destructive'
        });
      }

      if (resourcesResponse.success) {
        // 转换系统资源数据为显示格式
        const resources: SystemResource[] = [
          {
            name: 'CPU使用率',
            usage: resourcesResponse.data.cpu,
            limit: 100,
            status: resourcesResponse.data.cpu > 90 ? 'critical' : resourcesResponse.data.cpu > 70 ? 'warning' : 'normal'
          },
          {
            name: '内存使用率',
            usage: resourcesResponse.data.memory,
            limit: 100,
            status: resourcesResponse.data.memory > 90 ? 'critical' : resourcesResponse.data.memory > 70 ? 'warning' : 'normal'
          },
          {
            name: '磁盘使用率',
            usage: resourcesResponse.data.disk,
            limit: 100,
            status: resourcesResponse.data.disk > 90 ? 'critical' : resourcesResponse.data.disk > 70 ? 'warning' : 'normal'
          },
          {
            name: '网络使用率',
            usage: resourcesResponse.data.network,
            limit: 100,
            status: resourcesResponse.data.network > 90 ? 'critical' : resourcesResponse.data.network > 70 ? 'warning' : 'normal'
          },
          {
            name: 'API请求率',
            usage: Math.min(100, resourcesResponse.data.requestsPerMinute / 10),
            limit: 100,
            status: resourcesResponse.data.errorRate > 5 ? 'critical' : resourcesResponse.data.errorRate > 2 ? 'warning' : 'normal'
          }
        ];

        setSystemResourcesData(resources);
      } else {
        toast({
          title: '获取系统资源失败',
          description: resourcesResponse.message || '无法加载系统资源',
          variant: 'destructive'
        });
      }

      if (statsResponse.success) {
        setSecurityScore(statsResponse.data.securityScore);
        setThreatLevel(statsResponse.data.threatLevel);
        setActiveThreats(statsResponse.data.activeThreats);
        setResolvedThreats(statsResponse.data.resolvedThreats);
      } else {
        toast({
          title: '获取安全统计失败',
          description: statsResponse.message || '无法加载安全统计',
          variant: 'destructive'
        });
      }
    } catch (error) {
      console.error('加载安全数据失败:', error);
      toast({
        title: '加载安全数据失败',
        description: '服务器错误，请稍后再试',
        variant: 'destructive'
      });
    } finally {
      setLoading(false);
    }
  };

  // 组件加载时获取数据
  useEffect(() => {
    loadSecurityData();
  }, [canViewSecurity]);

  // 刷新数据
  const handleRefresh = () => {
    toast({
      title: '刷新安全数据',
      description: '正在刷新安全监控数据...'
    });

    loadSecurityData();
  };

  // 获取威胁等级徽章
  const getThreatLevelBadge = (level: string) => {
    switch (level) {
      case 'low':
        return <Badge className="bg-green-500">低</Badge>;
      case 'medium':
        return <Badge className="bg-yellow-500">中</Badge>;
      case 'high':
        return <Badge className="bg-orange-500">高</Badge>;
      case 'critical':
        return <Badge className="bg-red-500">严重</Badge>;
      default:
        return <Badge>未知</Badge>;
    }
  };

  // 获取安全评分颜色
  const getScoreColor = (score: number) => {
    if (score >= 90) return 'text-green-500';
    if (score >= 80) return 'text-yellow-500';
    if (score >= 70) return 'text-orange-500';
    return 'text-red-500';
  };

  return (
    <SuperAdminLayout>
      <div className="flex justify-between items-center mb-6">
        <div className="flex items-center">
          <Shield className="h-6 w-6 mr-2" />
          <h1 className="text-2xl font-bold">系统安全监控</h1>
        </div>

        <div className="flex space-x-2">
          <Button
            variant="outline"
            onClick={() => navigate('/superadmin/security-logs')}
          >
            <Clock className="mr-2 h-4 w-4" />
            安全日志
          </Button>

          <Button
            variant="outline"
            onClick={() => navigate('/superadmin/enhanced-security-monitor')}
          >
            <Activity className="mr-2 h-4 w-4" />
            高级监控
          </Button>

          <Button
            variant="outline"
            onClick={handleRefresh}
            disabled={loading}
          >
            {loading ? (
              <>
                <RefreshCw className="mr-2 h-4 w-4 animate-spin" />
                刷新中...
              </>
            ) : (
              <>
                <RefreshCw className="mr-2 h-4 w-4" />
                刷新
              </>
            )}
          </Button>
        </div>
      </div>

      <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-4 mb-6">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">安全评分</CardTitle>
            <Shield className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-3xl font-bold">
              <span className={getScoreColor(securityScore)}>{securityScore}</span>
              <span className="text-sm text-muted-foreground">/100</span>
            </div>
            <Progress value={securityScore} className="mt-2" />
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">当前威胁等级</CardTitle>
            <AlertTriangle className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold flex items-center">
              {getThreatLevelBadge(threatLevel)}
            </div>
            <p className="text-xs text-muted-foreground mt-2">
              上次更新: {new Date().toLocaleString()}
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">活跃威胁</CardTitle>
            <AlertCircle className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{activeThreats}</div>
            <p className="text-xs text-muted-foreground mt-2">
              需要处理的安全事件
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">已解决威胁</CardTitle>
            <CheckCircle className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{resolvedThreats}</div>
            <p className="text-xs text-muted-foreground mt-2">
              过去30天内解决的安全事件
            </p>
          </CardContent>
        </Card>
      </div>

      <Tabs defaultValue="overview" className="space-y-4">
        <TabsList>
          <TabsTrigger value="overview">
            <BarChart2 className="h-4 w-4 mr-2" />
            概览
          </TabsTrigger>
          <TabsTrigger value="events">
            <Activity className="h-4 w-4 mr-2" />
            安全事件
          </TabsTrigger>
          <TabsTrigger value="resources">
            <Cpu className="h-4 w-4 mr-2" />
            系统资源
          </TabsTrigger>
          <TabsTrigger value="access">
            <Lock className="h-4 w-4 mr-2" />
            访问控制
          </TabsTrigger>
        </TabsList>

        <TabsContent value="overview">
          <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
            <Card>
              <CardHeader>
                <CardTitle>威胁分布</CardTitle>
                <CardDescription>按严重性级别</CardDescription>
              </CardHeader>
              <CardContent className="flex justify-center items-center h-[300px]">
                <div className="text-center text-muted-foreground">
                  <PieChart className="h-16 w-16 mx-auto mb-4" />
                  <p>图表数据加载中...</p>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>安全事件趋势</CardTitle>
                <CardDescription>过去30天</CardDescription>
              </CardHeader>
              <CardContent className="flex justify-center items-center h-[300px]">
                <div className="text-center text-muted-foreground">
                  <LineChart className="h-16 w-16 mx-auto mb-4" />
                  <p>图表数据加载中...</p>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>地理分布</CardTitle>
                <CardDescription>访问来源</CardDescription>
              </CardHeader>
              <CardContent className="flex justify-center items-center h-[300px]">
                <div className="text-center text-muted-foreground">
                  <Globe className="h-16 w-16 mx-auto mb-4" />
                  <p>图表数据加载中...</p>
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="events">
          <Card>
            <CardHeader>
              <CardTitle>安全事件</CardTitle>
              <CardDescription>最近检测到的安全事件</CardDescription>
            </CardHeader>
            <CardContent>
              {loading ? (
                <div className="flex justify-center items-center py-8">
                  <Clock className="h-5 w-5 animate-spin mr-2" />
                  加载中...
                </div>
              ) : securityEvents.length === 0 ? (
                <div className="text-center py-8 text-muted-foreground">
                  没有检测到安全事件
                </div>
              ) : (
                <div className="space-y-4">
                  {securityEvents.slice(0, 5).map((event) => (
                    <div key={event.id} className="flex items-start p-4 border rounded-lg">
                      <div className="mr-4">
                        {event.severity === 'critical' ? (
                          <div className="bg-red-100 p-2 rounded-full">
                            <AlertCircle className="h-5 w-5 text-red-500" />
                          </div>
                        ) : event.severity === 'high' ? (
                          <div className="bg-orange-100 p-2 rounded-full">
                            <AlertTriangle className="h-5 w-5 text-orange-500" />
                          </div>
                        ) : event.severity === 'medium' ? (
                          <div className="bg-yellow-100 p-2 rounded-full">
                            <AlertTriangle className="h-5 w-5 text-yellow-500" />
                          </div>
                        ) : (
                          <div className="bg-blue-100 p-2 rounded-full">
                            <Shield className="h-5 w-5 text-blue-500" />
                          </div>
                        )}
                      </div>
                      <div className="flex-1">
                        <div className="flex justify-between items-start">
                          <div>
                            <h4 className="font-medium">
                              {event.type === 'login_failure' ? '登录失败' :
                               event.type === 'brute_force' ? '暴力破解尝试' :
                               event.type === 'suspicious_activity' ? '可疑活动' :
                               event.type === 'data_access' ? '敏感数据访问' :
                               event.type === 'permission_change' ? '权限变更' : '未知事件'}
                            </h4>
                            <p className="text-sm text-muted-foreground">{event.source} - {event.ip}</p>
                          </div>
                          <Badge variant={
                            event.status === 'new' ? 'destructive' :
                            event.status === 'investigating' ? 'default' :
                            event.status === 'resolved' ? 'outline' : 'secondary'
                          }>
                            {event.status === 'new' ? '新事件' :
                             event.status === 'investigating' ? '调查中' :
                             event.status === 'resolved' ? '已解决' : '误报'}
                          </Badge>
                        </div>
                        <p className="text-sm mt-1">{event.details}</p>
                        <p className="text-xs text-muted-foreground mt-2">
                          {new Date(event.timestamp).toLocaleString()}
                        </p>
                      </div>
                    </div>
                  ))}

                  <div className="text-center">
                    <Button variant="outline">查看全部事件</Button>
                  </div>
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="resources">
          <Card>
            <CardHeader>
              <CardTitle>系统资源</CardTitle>
              <CardDescription>当前系统资源使用情况</CardDescription>
            </CardHeader>
            <CardContent>
              {loading ? (
                <div className="flex justify-center items-center py-8">
                  <Clock className="h-5 w-5 animate-spin mr-2" />
                  加载中...
                </div>
              ) : systemResourcesData.length === 0 ? (
                <div className="text-center py-8 text-muted-foreground">
                  没有系统资源数据
                </div>
              ) : (
                <div className="space-y-6">
                  {systemResourcesData.map((resource, index) => (
                    <div key={index} className="space-y-2">
                      <div className="flex justify-between items-center">
                        <div className="flex items-center">
                          {resource.name === 'CPU使用率' ? (
                            <Cpu className="h-4 w-4 mr-2" />
                          ) : resource.name === '内存使用率' ? (
                            <HardDrive className="h-4 w-4 mr-2" />
                          ) : resource.name === '磁盘使用率' ? (
                            <Database className="h-4 w-4 mr-2" />
                          ) : resource.name === '网络使用率' ? (
                            <Globe className="h-4 w-4 mr-2" />
                          ) : (
                            <Activity className="h-4 w-4 mr-2" />
                          )}
                          <span>{resource.name}</span>
                        </div>
                        <div className="flex items-center">
                          <span className={
                            resource.status === 'critical' ? 'text-red-500' :
                            resource.status === 'warning' ? 'text-yellow-500' : 'text-green-500'
                          }>
                            {Math.round(resource.usage)}%
                          </span>
                          <span className="text-muted-foreground text-sm ml-1">
                            / {resource.limit}%
                          </span>
                        </div>
                      </div>
                      <Progress
                        value={resource.usage}
                        className={
                          resource.status === 'critical' ? 'bg-red-100' :
                          resource.status === 'warning' ? 'bg-yellow-100' : 'bg-green-100'
                        }
                      />
                    </div>
                  ))}
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="access">
          <Card>
            <CardHeader>
              <CardTitle>访问控制</CardTitle>
              <CardDescription>用户访问和权限管理</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="text-center py-8 text-muted-foreground">
                <UserX className="h-16 w-16 mx-auto mb-4" />
                <p>访问控制数据加载中...</p>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </SuperAdminLayout>
  );
}
