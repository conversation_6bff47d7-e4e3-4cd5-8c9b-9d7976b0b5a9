/**
 * 执行数据库优化迁移
 * 将当前数据库结构迁移到Cloudflare优化版本
 */

const { PrismaClient } = require('@prisma/client');
const fs = require('fs');
const { v4: uuidv4 } = require('uuid');

const prisma = new PrismaClient();

class MigrationExecutor {
  constructor() {
    this.migrationLog = [];
    this.errors = [];
  }

  async execute() {
    console.log('🚀 开始执行数据库优化迁移...');
    
    try {
      // 1. 数据备份验证
      await this.verifyBackup();
      
      // 2. 创建优化后的表结构
      await this.createOptimizedTables();
      
      // 3. 迁移现有数据
      await this.migrateExistingData();
      
      // 4. 创建索引
      await this.createOptimizedIndexes();
      
      // 5. 验证数据完整性
      await this.verifyDataIntegrity();
      
      // 6. 生成迁移报告
      await this.generateMigrationReport();
      
      console.log('✅ 数据库优化迁移完成！');
      
    } catch (error) {
      console.error('❌ 迁移失败:', error);
      this.errors.push(error.message);
      throw error;
    } finally {
      await prisma.$disconnect();
    }
  }

  async verifyBackup() {
    console.log('📦 验证数据备份...');
    
    const backupFiles = fs.readdirSync('./backups').filter(f => f.includes('dev-backup'));
    if (backupFiles.length === 0) {
      throw new Error('未找到数据备份文件');
    }
    
    const latestBackup = backupFiles.sort().pop();
    console.log(`✅ 找到备份文件: ${latestBackup}`);
    
    this.migrationLog.push({
      step: 'backup_verification',
      status: 'success',
      backup_file: latestBackup,
      timestamp: new Date().toISOString()
    });
  }

  async createOptimizedTables() {
    console.log('🏗️ 创建优化后的表结构...');
    
    // 由于我们使用的是SQLite，我们需要通过原始SQL创建新表
    const optimizedTables = [
      // 用户优化表
      `CREATE TABLE IF NOT EXISTS users_optimized (
        id TEXT PRIMARY KEY,
        email TEXT UNIQUE,
        email_verified BOOLEAN DEFAULT false,
        user_type TEXT DEFAULT 'anonymous',
        role TEXT DEFAULT 'user',
        username TEXT,
        display_name TEXT,
        password_hash TEXT,
        anonymous_id TEXT UNIQUE,
        is_anonymous_registered BOOLEAN DEFAULT false,
        ip_address TEXT,
        user_agent TEXT,
        last_login_at DATETIME,
        is_active BOOLEAN DEFAULT true,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        created_by TEXT,
        metadata TEXT,
        tags TEXT,
        extra_data TEXT,
        version INTEGER DEFAULT 1,
        schema_version TEXT DEFAULT '1.0',
        is_test_data BOOLEAN DEFAULT false,
        test_data_version TEXT,
        test_data_set TEXT
      )`,
      
      // 内容元数据表
      `CREATE TABLE IF NOT EXISTS content_metadata (
        id TEXT PRIMARY KEY,
        user_id TEXT,
        content_type TEXT NOT NULL,
        title TEXT,
        category TEXT,
        tags TEXT,
        status TEXT DEFAULT 'pending',
        likes INTEGER DEFAULT 0,
        dislikes INTEGER DEFAULT 0,
        views INTEGER DEFAULT 0,
        word_count INTEGER DEFAULT 0,
        r2_path TEXT,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        metadata TEXT,
        extra_data TEXT,
        version INTEGER DEFAULT 1,
        schema_version TEXT DEFAULT '1.0',
        is_deleted BOOLEAN DEFAULT false,
        is_test_data BOOLEAN DEFAULT false,
        FOREIGN KEY (user_id) REFERENCES users_optimized(id)
      )`,
      
      // 问卷模板表
      `CREATE TABLE IF NOT EXISTS questionnaire_templates (
        id TEXT PRIMARY KEY,
        title TEXT NOT NULL,
        description TEXT,
        version TEXT DEFAULT '1.0',
        is_active BOOLEAN DEFAULT true,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        metadata TEXT,
        schema_version TEXT DEFAULT '1.0'
      )`,
      
      // 题目表
      `CREATE TABLE IF NOT EXISTS questions (
        id TEXT PRIMARY KEY,
        questionnaire_id TEXT NOT NULL,
        question_number INTEGER NOT NULL,
        question_text TEXT NOT NULL,
        question_type TEXT NOT NULL,
        options TEXT,
        validation TEXT,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (questionnaire_id) REFERENCES questionnaire_templates(id)
      )`,
      
      // 用户答案表
      `CREATE TABLE IF NOT EXISTS question_answers (
        id TEXT PRIMARY KEY,
        user_id TEXT NOT NULL,
        question_id TEXT NOT NULL,
        response_id TEXT NOT NULL,
        answer_type TEXT NOT NULL,
        answer_value TEXT NOT NULL,
        answer_text TEXT,
        answer_number REAL,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (user_id) REFERENCES users_optimized(id),
        FOREIGN KEY (question_id) REFERENCES questions(id)
      )`,
      
      // 问卷回复表（优化版）
      `CREATE TABLE IF NOT EXISTS questionnaire_responses_optimized (
        id TEXT PRIMARY KEY,
        user_id TEXT,
        questionnaire_id TEXT NOT NULL,
        sequence_number TEXT UNIQUE,
        submission_session_id TEXT,
        ip_address TEXT,
        user_agent TEXT,
        is_anonymous BOOLEAN DEFAULT true,
        status TEXT DEFAULT 'submitted',
        completion_rate REAL DEFAULT 0,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        metadata TEXT,
        version INTEGER DEFAULT 1,
        schema_version TEXT DEFAULT '1.0',
        is_test_data BOOLEAN DEFAULT false,
        FOREIGN KEY (user_id) REFERENCES users_optimized(id),
        FOREIGN KEY (questionnaire_id) REFERENCES questionnaire_templates(id)
      )`
    ];
    
    for (const sql of optimizedTables) {
      await prisma.$executeRawUnsafe(sql);
      console.log('✅ 创建表成功');
    }
    
    this.migrationLog.push({
      step: 'create_optimized_tables',
      status: 'success',
      tables_created: optimizedTables.length,
      timestamp: new Date().toISOString()
    });
  }

  async migrateExistingData() {
    console.log('📊 迁移现有数据...');
    
    // 1. 迁移用户数据
    await this.migrateUsers();
    
    // 2. 创建默认问卷模板
    await this.createDefaultQuestionnaire();
    
    // 3. 迁移问卷数据
    await this.migrateQuestionnaireData();
    
    // 4. 迁移故事数据
    await this.migrateStoryData();
  }

  async migrateUsers() {
    console.log('👥 迁移用户数据...');
    
    const users = await prisma.user.findMany();
    let migratedCount = 0;
    
    for (const user of users) {
      const newUserId = `user_${uuidv4()}`;
      const anonymousId = user.email ? `anon_${uuidv4().slice(0, 8)}` : null;
      
      await prisma.$executeRawUnsafe(`
        INSERT INTO users_optimized (
          id, email, email_verified, role, username, display_name,
          password_hash, anonymous_id, is_anonymous_registered,
          ip_address, last_login_at, is_active, created_at, updated_at,
          metadata, version, schema_version, is_test_data
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
      `, [
        newUserId,
        user.email,
        user.emailVerified || false,
        user.role || 'user',
        user.username,
        user.name,
        user.passwordHash,
        anonymousId,
        !!user.email,
        user.ipAddress,
        user.lastLoginAt,
        true,
        user.createdAt,
        user.updatedAt,
        JSON.stringify({ originalId: user.id }),
        1,
        '1.0',
        user.isTestData || false
      ]);
      
      // 记录ID映射
      await prisma.systemConfig.upsert({
        where: { key: `user_migration_${user.id}` },
        update: { value: newUserId },
        create: {
          key: `user_migration_${user.id}`,
          value: newUserId,
          description: '用户ID迁移映射',
          category: 'migration'
        }
      });
      
      migratedCount++;
    }
    
    console.log(`✅ 迁移用户数据完成: ${migratedCount} 个用户`);
    
    this.migrationLog.push({
      step: 'migrate_users',
      status: 'success',
      migrated_count: migratedCount,
      timestamp: new Date().toISOString()
    });
  }

  async createDefaultQuestionnaire() {
    console.log('📋 创建默认问卷模板...');
    
    const questionnaireId = `quest_${uuidv4()}`;
    
    // 创建问卷模板
    await prisma.$executeRawUnsafe(`
      INSERT INTO questionnaire_templates (
        id, title, description, version, is_active, created_at, metadata, schema_version
      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?)
    `, [
      questionnaireId,
      '大学生就业调研问卷',
      '针对大学生就业情况的调研问卷',
      '1.0',
      true,
      new Date().toISOString(),
      JSON.stringify({ isDefault: true }),
      '1.0'
    ]);
    
    // 创建问卷题目（基于现有QuestionnaireResponse字段）
    const questions = [
      { field: 'educationLevel', text: '教育水平', type: 'single', options: ['本科', '硕士', '博士'] },
      { field: 'major', text: '专业', type: 'text' },
      { field: 'graduationYear', text: '毕业年份', type: 'number' },
      { field: 'region', text: '所在地区', type: 'text' },
      { field: 'employmentStatus', text: '就业状态', type: 'single', options: ['已就业', '未就业', '继续深造'] },
      { field: 'currentIndustry', text: '当前行业', type: 'text' },
      { field: 'currentPosition', text: '当前职位', type: 'text' },
      { field: 'monthlySalary', text: '月薪', type: 'number' },
      { field: 'jobSatisfaction', text: '工作满意度', type: 'single', options: ['很满意', '满意', '一般', '不满意', '很不满意'] }
    ];
    
    for (let i = 0; i < questions.length; i++) {
      const question = questions[i];
      const questionId = `qitem_${uuidv4()}`;
      
      await prisma.$executeRawUnsafe(`
        INSERT INTO questions (
          id, questionnaire_id, question_number, question_text, question_type, options, created_at
        ) VALUES (?, ?, ?, ?, ?, ?, ?)
      `, [
        questionId,
        questionnaireId,
        i + 1,
        question.text,
        question.type,
        question.options ? JSON.stringify(question.options) : null,
        new Date().toISOString()
      ]);
      
      // 记录字段映射
      await prisma.systemConfig.create({
        data: {
          key: `question_field_${question.field}`,
          value: questionId,
          description: `问卷字段映射: ${question.field}`,
          category: 'migration'
        }
      });
    }
    
    // 记录问卷ID
    await prisma.systemConfig.create({
      data: {
        key: 'default_questionnaire_id',
        value: questionnaireId,
        description: '默认问卷模板ID',
        category: 'migration'
      }
    });
    
    console.log(`✅ 创建默认问卷模板: ${questionnaireId}`);
    
    this.migrationLog.push({
      step: 'create_default_questionnaire',
      status: 'success',
      questionnaire_id: questionnaireId,
      questions_count: questions.length,
      timestamp: new Date().toISOString()
    });
  }

  async migrateQuestionnaireData() {
    console.log('📝 迁移问卷数据...');
    
    const responses = await prisma.questionnaireResponse.findMany();
    const defaultQuestionnaireConfig = await prisma.systemConfig.findUnique({
      where: { key: 'default_questionnaire_id' }
    });
    
    if (!defaultQuestionnaireConfig) {
      throw new Error('未找到默认问卷模板ID');
    }
    
    const questionnaireId = defaultQuestionnaireConfig.value;
    let migratedCount = 0;
    
    for (const response of responses) {
      const responseId = `resp_${uuidv4()}`;
      const sessionId = `session_${uuidv4()}`;
      
      // 获取用户ID映射
      let newUserId = null;
      if (response.userId) {
        const userMapping = await prisma.systemConfig.findUnique({
          where: { key: `user_migration_${response.userId}` }
        });
        newUserId = userMapping?.value;
      }
      
      // 创建问卷回复记录
      await prisma.$executeRawUnsafe(`
        INSERT INTO questionnaire_responses_optimized (
          id, user_id, questionnaire_id, sequence_number, submission_session_id,
          ip_address, is_anonymous, status, completion_rate, created_at, updated_at,
          metadata, version, schema_version, is_test_data
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
      `, [
        responseId,
        newUserId,
        questionnaireId,
        response.sequenceNumber,
        sessionId,
        response.ipAddress,
        response.isAnonymous,
        response.status,
        1.0, // 假设都是完整提交
        response.createdAt,
        response.updatedAt,
        JSON.stringify({ originalId: response.id }),
        1,
        '1.0',
        response.isTestData || false
      ]);
      
      migratedCount++;
    }
    
    console.log(`✅ 迁移问卷数据完成: ${migratedCount} 个回复`);
    
    this.migrationLog.push({
      step: 'migrate_questionnaire_data',
      status: 'success',
      migrated_count: migratedCount,
      timestamp: new Date().toISOString()
    });
  }

  async migrateStoryData() {
    console.log('📖 迁移故事数据...');
    
    const stories = await prisma.story.findMany();
    let migratedCount = 0;
    
    for (const story of stories) {
      const contentId = `text_${uuidv4()}`;
      
      // 获取用户ID映射
      let newUserId = null;
      if (story.userId) {
        const userMapping = await prisma.systemConfig.findUnique({
          where: { key: `user_migration_${story.userId}` }
        });
        newUserId = userMapping?.value;
      }
      
      // 创建内容元数据记录
      await prisma.$executeRawUnsafe(`
        INSERT INTO content_metadata (
          id, user_id, content_type, title, category, tags, status,
          likes, dislikes, views, word_count, created_at, updated_at,
          metadata, version, schema_version, is_deleted, is_test_data
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
      `, [
        contentId,
        newUserId,
        'story',
        story.title,
        story.category,
        story.tags,
        story.status,
        story.likes,
        story.dislikes,
        0, // views 初始化为0
        story.content?.length || 0,
        story.createdAt,
        story.updatedAt,
        JSON.stringify({ 
          originalId: story.id,
          author: story.author,
          isAnonymous: story.isAnonymous,
          content: story.content // 暂时存储在metadata中，后续迁移到R2
        }),
        1,
        '1.0',
        false,
        story.isTestData || false
      ]);
      
      migratedCount++;
    }
    
    console.log(`✅ 迁移故事数据完成: ${migratedCount} 个故事`);
    
    this.migrationLog.push({
      step: 'migrate_story_data',
      status: 'success',
      migrated_count: migratedCount,
      timestamp: new Date().toISOString()
    });
  }

  async createOptimizedIndexes() {
    console.log('🔍 创建优化索引...');
    
    const indexes = [
      // 用户表索引
      'CREATE INDEX IF NOT EXISTS idx_users_optimized_role ON users_optimized(role)',
      'CREATE INDEX IF NOT EXISTS idx_users_optimized_anonymous_id ON users_optimized(anonymous_id)',
      'CREATE INDEX IF NOT EXISTS idx_users_optimized_active ON users_optimized(is_active)',
      'CREATE INDEX IF NOT EXISTS idx_users_optimized_created_at ON users_optimized(created_at)',
      
      // 内容元数据表索引
      'CREATE INDEX IF NOT EXISTS idx_content_metadata_user_id ON content_metadata(user_id)',
      'CREATE INDEX IF NOT EXISTS idx_content_metadata_type ON content_metadata(content_type)',
      'CREATE INDEX IF NOT EXISTS idx_content_metadata_status ON content_metadata(status)',
      'CREATE INDEX IF NOT EXISTS idx_content_metadata_created_at ON content_metadata(created_at)',
      'CREATE INDEX IF NOT EXISTS idx_content_metadata_user_type ON content_metadata(user_id, content_type)',
      
      // 问卷相关索引
      'CREATE INDEX IF NOT EXISTS idx_questions_questionnaire ON questions(questionnaire_id)',
      'CREATE INDEX IF NOT EXISTS idx_questions_type ON questions(question_type)',
      'CREATE INDEX IF NOT EXISTS idx_question_answers_question ON question_answers(question_id)',
      'CREATE INDEX IF NOT EXISTS idx_question_answers_user ON question_answers(user_id)',
      'CREATE INDEX IF NOT EXISTS idx_questionnaire_responses_user ON questionnaire_responses_optimized(user_id)',
      'CREATE INDEX IF NOT EXISTS idx_questionnaire_responses_status ON questionnaire_responses_optimized(status)'
    ];
    
    for (const indexSql of indexes) {
      await prisma.$executeRawUnsafe(indexSql);
    }
    
    console.log(`✅ 创建索引完成: ${indexes.length} 个索引`);
    
    this.migrationLog.push({
      step: 'create_optimized_indexes',
      status: 'success',
      indexes_created: indexes.length,
      timestamp: new Date().toISOString()
    });
  }

  async verifyDataIntegrity() {
    console.log('🔍 验证数据完整性...');
    
    const verification = {
      users: {
        original: await prisma.user.count(),
        migrated: await prisma.$queryRawUnsafe('SELECT COUNT(*) as count FROM users_optimized').then(r => r[0].count)
      },
      stories: {
        original: await prisma.story.count(),
        migrated: await prisma.$queryRawUnsafe('SELECT COUNT(*) as count FROM content_metadata WHERE content_type = \'story\'').then(r => r[0].count)
      },
      questionnaires: {
        original: await prisma.questionnaireResponse.count(),
        migrated: await prisma.$queryRawUnsafe('SELECT COUNT(*) as count FROM questionnaire_responses_optimized').then(r => r[0].count)
      }
    };
    
    console.log('📊 数据完整性验证结果:');
    console.log(`- 用户: ${verification.users.original} → ${verification.users.migrated}`);
    console.log(`- 故事: ${verification.stories.original} → ${verification.stories.migrated}`);
    console.log(`- 问卷: ${verification.questionnaires.original} → ${verification.questionnaires.migrated}`);
    
    // 检查数据一致性
    const inconsistencies = [];
    if (verification.users.original !== verification.users.migrated) {
      inconsistencies.push('用户数据不一致');
    }
    if (verification.stories.original !== verification.stories.migrated) {
      inconsistencies.push('故事数据不一致');
    }
    if (verification.questionnaires.original !== verification.questionnaires.migrated) {
      inconsistencies.push('问卷数据不一致');
    }
    
    if (inconsistencies.length > 0) {
      throw new Error(`数据完整性验证失败: ${inconsistencies.join(', ')}`);
    }
    
    console.log('✅ 数据完整性验证通过');
    
    this.migrationLog.push({
      step: 'verify_data_integrity',
      status: 'success',
      verification,
      timestamp: new Date().toISOString()
    });
  }

  async generateMigrationReport() {
    console.log('📋 生成迁移报告...');
    
    const report = {
      migration_id: `migration_${Date.now()}`,
      started_at: this.migrationLog[0]?.timestamp,
      completed_at: new Date().toISOString(),
      status: this.errors.length === 0 ? 'success' : 'failed',
      steps: this.migrationLog,
      errors: this.errors,
      summary: {
        total_steps: this.migrationLog.length,
        successful_steps: this.migrationLog.filter(s => s.status === 'success').length,
        failed_steps: this.migrationLog.filter(s => s.status === 'failed').length
      }
    };
    
    // 保存报告
    fs.writeFileSync(
      `./backups/migration-report-${Date.now()}.json`,
      JSON.stringify(report, null, 2)
    );
    
    console.log('✅ 迁移报告已生成');
    
    return report;
  }
}

// 执行迁移
if (require.main === module) {
  const executor = new MigrationExecutor();
  executor.execute()
    .then(() => {
      console.log('🎉 迁移执行完成！');
      process.exit(0);
    })
    .catch(error => {
      console.error('💥 迁移执行失败:', error);
      process.exit(1);
    });
}

module.exports = MigrationExecutor;
