import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Checkbox } from '@/components/ui/checkbox';
import { Separator } from '@/components/ui/separator';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import {
  Shield, Users, Database, Settings, FileText, Eye, Edit, Trash2,
  AlertTriangle, Info, CheckCircle, Lock, Unlock, Search, Filter
} from 'lucide-react';

// 权限分组定义
const PERMISSION_GROUPS = [
  {
    id: 'dashboard',
    name: '仪表盘权限',
    icon: <Eye className="h-4 w-4" />,
    description: '查看和管理仪表盘数据',
    permissions: [
      { id: 'dashboard.personal.view', name: '个人仪表盘查看', description: '查看个人数据统计' },
      { id: 'dashboard.system.view', name: '系统仪表盘查看', description: '查看系统整体数据' },
      { id: 'dashboard.analytics.view', name: '数据分析查看', description: '查看详细数据分析' }
    ]
  },
  {
    id: 'content',
    name: '内容管理权限',
    icon: <FileText className="h-4 w-4" />,
    description: '管理内容审核和发布',
    permissions: [
      { id: 'content.review', name: '内容审核', description: '审核用户提交的内容' },
      { id: 'content.story.review', name: '故事审核', description: '审核用户故事' },
      { id: 'content.quick.review', name: '快速审核', description: '使用快速审核功能' },
      { id: 'content.tag.manage', name: '标签管理', description: '管理内容标签' }
    ]
  },
  {
    id: 'data',
    name: '数据管理权限',
    icon: <Database className="h-4 w-4" />,
    description: '管理问卷和数据分析',
    permissions: [
      { id: 'data.questionnaire.view', name: '问卷查看', description: '查看问卷数据' },
      { id: 'data.questionnaire.edit', name: '问卷编辑', description: '编辑问卷内容' },
      { id: 'data.analysis', name: '数据分析', description: '进行数据分析' },
      { id: 'data.export', name: '数据导出', description: '导出数据文件' },
      { id: 'data.import', name: '数据导入', description: '导入数据文件' }
    ]
  },
  {
    id: 'user',
    name: '用户管理权限',
    icon: <Users className="h-4 w-4" />,
    description: '管理用户和角色',
    permissions: [
      { id: 'user.view', name: '用户查看', description: '查看用户列表' },
      { id: 'user.manage', name: '用户管理', description: '创建、编辑、删除用户' },
      { id: 'user.reviewer.manage', name: '审核员管理', description: '管理审核员账户' },
      { id: 'user.role.assign', name: '角色分配', description: '为用户分配角色' }
    ]
  },
  {
    id: 'system',
    name: '系统管理权限',
    icon: <Settings className="h-4 w-4" />,
    description: '系统设置和配置',
    permissions: [
      { id: 'system.settings.personal', name: '个人设置', description: '修改个人设置' },
      { id: 'system.settings.global', name: '全局设置', description: '修改系统全局设置' },
      { id: 'system.deidentification', name: '数据脱敏', description: '执行数据脱敏操作' },
      { id: 'system.backup', name: '系统备份', description: '创建和管理系统备份' },
      { id: 'system.audit', name: '审计日志', description: '查看和管理审计日志' }
    ]
  },
  {
    id: 'security',
    name: '安全管理权限',
    icon: <Shield className="h-4 w-4" />,
    description: '安全监控和管理',
    permissions: [
      { id: 'security.monitor', name: '安全监控', description: '监控系统安全状态' },
      { id: 'security.alert.manage', name: '安全告警管理', description: '管理安全告警' },
      { id: 'security.policy.manage', name: '安全策略管理', description: '配置安全策略' },
      { id: 'security.incident.handle', name: '安全事件处理', description: '处理安全事件' }
    ]
  }
];

// 权限依赖关系
const PERMISSION_DEPENDENCIES = {
  'data.export': ['data.questionnaire.view'],
  'data.import': ['data.questionnaire.edit'],
  'user.role.assign': ['user.view'],
  'user.reviewer.manage': ['user.manage'],
  'system.backup': ['system.settings.global'],
  'security.alert.manage': ['security.monitor']
};

// 角色模板
const ROLE_TEMPLATES = [
  {
    id: 'content_moderator',
    name: '内容审核员',
    description: '专门负责内容审核的角色',
    permissions: [
      'dashboard.personal.view',
      'content.review',
      'content.story.review',
      'content.quick.review',
      'content.tag.manage',
      'system.settings.personal'
    ]
  },
  {
    id: 'data_analyst',
    name: '数据分析师',
    description: '专门负责数据分析的角色',
    permissions: [
      'dashboard.personal.view',
      'dashboard.analytics.view',
      'data.questionnaire.view',
      'data.analysis',
      'data.export',
      'system.settings.personal'
    ]
  },
  {
    id: 'user_manager',
    name: '用户管理员',
    description: '专门负责用户管理的角色',
    permissions: [
      'dashboard.personal.view',
      'user.view',
      'user.manage',
      'user.reviewer.manage',
      'user.role.assign',
      'system.settings.personal'
    ]
  }
];

interface PermissionEditorProps {
  role: any;
  onSave: (permissions: string[]) => void;
  onCancel: () => void;
  isLoading?: boolean;
}

const PermissionEditor: React.FC<PermissionEditorProps> = ({
  role,
  onSave,
  onCancel,
  isLoading = false
}) => {
  const [selectedPermissions, setSelectedPermissions] = useState<string[]>(role?.permissions || []);
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedGroup, setSelectedGroup] = useState<string>('all');
  const [showDependencies, setShowDependencies] = useState(true);

  // 处理权限切换
  const handlePermissionToggle = (permissionId: string) => {
    setSelectedPermissions(prev => {
      const newPermissions = [...prev];
      
      if (newPermissions.includes(permissionId)) {
        // 移除权限
        const index = newPermissions.indexOf(permissionId);
        newPermissions.splice(index, 1);
        
        // 检查并移除依赖此权限的其他权限
        Object.entries(PERMISSION_DEPENDENCIES).forEach(([depPermission, dependencies]) => {
          if (dependencies.includes(permissionId) && newPermissions.includes(depPermission)) {
            const depIndex = newPermissions.indexOf(depPermission);
            newPermissions.splice(depIndex, 1);
          }
        });
      } else {
        // 添加权限
        newPermissions.push(permissionId);
        
        // 自动添加依赖权限
        const dependencies = PERMISSION_DEPENDENCIES[permissionId] || [];
        dependencies.forEach(dep => {
          if (!newPermissions.includes(dep)) {
            newPermissions.push(dep);
          }
        });
      }
      
      return newPermissions;
    });
  };

  // 处理权限组切换
  const handleGroupToggle = (groupId: string) => {
    const group = PERMISSION_GROUPS.find(g => g.id === groupId);
    if (!group) return;

    const groupPermissionIds = group.permissions.map(p => p.id);
    const allSelected = groupPermissionIds.every(id => selectedPermissions.includes(id));

    if (allSelected) {
      // 取消选中所有
      setSelectedPermissions(prev => 
        prev.filter(id => !groupPermissionIds.includes(id))
      );
    } else {
      // 选中所有
      setSelectedPermissions(prev => {
        const newPermissions = [...prev];
        groupPermissionIds.forEach(id => {
          if (!newPermissions.includes(id)) {
            newPermissions.push(id);
          }
        });
        return newPermissions;
      });
    }
  };

  // 应用角色模板
  const applyTemplate = (templateId: string) => {
    const template = ROLE_TEMPLATES.find(t => t.id === templateId);
    if (template) {
      setSelectedPermissions(template.permissions);
    }
  };

  // 过滤权限组
  const filteredGroups = PERMISSION_GROUPS.filter(group => {
    if (selectedGroup !== 'all' && group.id !== selectedGroup) return false;
    
    if (searchTerm) {
      return group.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
             group.permissions.some(p => 
               p.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
               p.description.toLowerCase().includes(searchTerm.toLowerCase())
             );
    }
    
    return true;
  });

  // 获取权限状态
  const getPermissionStatus = (permissionId: string) => {
    const isSelected = selectedPermissions.includes(permissionId);
    const dependencies = PERMISSION_DEPENDENCIES[permissionId] || [];
    const hasDependencies = dependencies.length > 0;
    const dependenciesMet = dependencies.every(dep => selectedPermissions.includes(dep));
    
    return {
      isSelected,
      hasDependencies,
      dependenciesMet,
      canSelect: !hasDependencies || dependenciesMet
    };
  };

  return (
    <div className="space-y-6">
      {/* 头部信息 */}
      <div className="flex items-center justify-between">
        <div>
          <h3 className="text-lg font-semibold">编辑角色权限</h3>
          <p className="text-sm text-muted-foreground">
            为角色 "{role?.name}" 配置权限
          </p>
        </div>
        <div className="flex items-center space-x-2">
          <Badge variant="outline">
            已选择 {selectedPermissions.length} 个权限
          </Badge>
        </div>
      </div>

      <Tabs defaultValue="permissions" className="space-y-4">
        <TabsList>
          <TabsTrigger value="permissions">权限配置</TabsTrigger>
          <TabsTrigger value="templates">角色模板</TabsTrigger>
          <TabsTrigger value="preview">权限预览</TabsTrigger>
        </TabsList>

        {/* 权限配置 */}
        <TabsContent value="permissions" className="space-y-4">
          {/* 搜索和筛选 */}
          <div className="flex items-center space-x-4">
            <div className="flex-1">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground h-4 w-4" />
                <Input
                  placeholder="搜索权限..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-10"
                />
              </div>
            </div>
            <Select value={selectedGroup} onValueChange={setSelectedGroup}>
              <SelectTrigger className="w-[200px]">
                <SelectValue placeholder="选择权限组" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">所有权限组</SelectItem>
                {PERMISSION_GROUPS.map(group => (
                  <SelectItem key={group.id} value={group.id}>
                    {group.name}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          {/* 权限组列表 */}
          <div className="space-y-4">
            {filteredGroups.map(group => {
              const groupPermissionIds = group.permissions.map(p => p.id);
              const selectedCount = groupPermissionIds.filter(id => selectedPermissions.includes(id)).length;
              const allSelected = selectedCount === groupPermissionIds.length;
              const someSelected = selectedCount > 0 && selectedCount < groupPermissionIds.length;

              return (
                <Card key={group.id}>
                  <CardHeader className="pb-3">
                    <div className="flex items-center justify-between">
                      <div className="flex items-center space-x-3">
                        <Checkbox
                          checked={allSelected}
                          ref={(el) => {
                            if (el) el.indeterminate = someSelected;
                          }}
                          onCheckedChange={() => handleGroupToggle(group.id)}
                        />
                        <div className="flex items-center space-x-2">
                          {group.icon}
                          <CardTitle className="text-base">{group.name}</CardTitle>
                        </div>
                        <Badge variant="secondary">
                          {selectedCount}/{groupPermissionIds.length}
                        </Badge>
                      </div>
                    </div>
                    <CardDescription>{group.description}</CardDescription>
                  </CardHeader>
                  <CardContent>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                      {group.permissions.map(permission => {
                        const status = getPermissionStatus(permission.id);
                        const dependencies = PERMISSION_DEPENDENCIES[permission.id] || [];

                        return (
                          <div
                            key={permission.id}
                            className={`flex items-start space-x-3 p-3 rounded-lg border ${
                              status.isSelected ? 'bg-blue-50 border-blue-200' : 'bg-gray-50'
                            } ${!status.canSelect ? 'opacity-50' : ''}`}
                          >
                            <Checkbox
                              checked={status.isSelected}
                              disabled={!status.canSelect}
                              onCheckedChange={() => handlePermissionToggle(permission.id)}
                            />
                            <div className="flex-1 min-w-0">
                              <div className="flex items-center space-x-2">
                                <Label className="text-sm font-medium">
                                  {permission.name}
                                </Label>
                                {status.hasDependencies && (
                                  <Badge variant="outline" className="text-xs">
                                    依赖
                                  </Badge>
                                )}
                              </div>
                              <p className="text-xs text-muted-foreground mt-1">
                                {permission.description}
                              </p>
                              {showDependencies && dependencies.length > 0 && (
                                <div className="mt-2">
                                  <p className="text-xs text-orange-600">
                                    依赖权限: {dependencies.join(', ')}
                                  </p>
                                </div>
                              )}
                            </div>
                          </div>
                        );
                      })}
                    </div>
                  </CardContent>
                </Card>
              );
            })}
          </div>
        </TabsContent>

        {/* 角色模板 */}
        <TabsContent value="templates" className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {ROLE_TEMPLATES.map(template => (
              <Card key={template.id} className="cursor-pointer hover:shadow-md transition-shadow">
                <CardHeader>
                  <CardTitle className="text-base">{template.name}</CardTitle>
                  <CardDescription>{template.description}</CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="space-y-3">
                    <div className="flex items-center justify-between">
                      <span className="text-sm text-muted-foreground">权限数量</span>
                      <Badge variant="secondary">{template.permissions.length}</Badge>
                    </div>
                    <Button
                      variant="outline"
                      size="sm"
                      className="w-full"
                      onClick={() => applyTemplate(template.id)}
                    >
                      应用模板
                    </Button>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </TabsContent>

        {/* 权限预览 */}
        <TabsContent value="preview" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>权限预览</CardTitle>
              <CardDescription>
                当前选择的权限列表
              </CardDescription>
            </CardHeader>
            <CardContent>
              {selectedPermissions.length === 0 ? (
                <p className="text-muted-foreground text-center py-8">
                  未选择任何权限
                </p>
              ) : (
                <div className="space-y-4">
                  {PERMISSION_GROUPS.map(group => {
                    const groupPermissions = group.permissions.filter(p => 
                      selectedPermissions.includes(p.id)
                    );
                    
                    if (groupPermissions.length === 0) return null;

                    return (
                      <div key={group.id}>
                        <div className="flex items-center space-x-2 mb-2">
                          {group.icon}
                          <h4 className="font-medium">{group.name}</h4>
                          <Badge variant="secondary">{groupPermissions.length}</Badge>
                        </div>
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-2 ml-6">
                          {groupPermissions.map(permission => (
                            <div key={permission.id} className="flex items-center space-x-2">
                              <CheckCircle className="h-3 w-3 text-green-600" />
                              <span className="text-sm">{permission.name}</span>
                            </div>
                          ))}
                        </div>
                      </div>
                    );
                  })}
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>

      {/* 操作按钮 */}
      <div className="flex items-center justify-end space-x-3">
        <Button variant="outline" onClick={onCancel} disabled={isLoading}>
          取消
        </Button>
        <Button onClick={() => onSave(selectedPermissions)} disabled={isLoading}>
          {isLoading ? '保存中...' : '保存权限'}
        </Button>
      </div>
    </div>
  );
};

export default PermissionEditor;
