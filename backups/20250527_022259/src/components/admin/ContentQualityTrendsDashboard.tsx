import React, { useState, useEffect } from 'react';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { But<PERSON> } from '@/components/ui/button';
import { Skeleton } from '@/components/ui/skeleton';
import { useToast } from '@/components/ui/use-toast';
import { 
  <PERSON><PERSON><PERSON>, 
  LineChart, 
  RefreshCw,
  Calendar,
  Filter,
  TrendingUp,
  AlertTriangle,
  CheckCircle,
  XCircle
} from 'lucide-react';
import { useApi } from '@/hooks/useApi';
import {
  Chart as ChartJS,
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  BarElement,
  Title,
  Tooltip,
  Legend,
} from 'chart.js';
import { Bar, Line } from 'react-chartjs-2';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { DatePicker } from '@/components/ui/date-picker';

// 注册Chart.js组件
ChartJS.register(
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  BarElement,
  Title,
  Tooltip,
  Legend
);

interface QualityTrend {
  timePeriod: string;
  contentType: string;
  avgDataQuality: number | null;
  avgConstructiveValue: number | null;
  avgStoryValue: number | null;
  avgConfidence: number;
  count: number;
  safeCount: number;
  unsafeCount: number;
}

interface SeverityTrend {
  timePeriod: string;
  severity: string;
  count: number;
}

interface ActionTrend {
  timePeriod: string;
  suggestedAction: string;
  count: number;
}

interface ContentQualityTrends {
  qualityTrends: QualityTrend[];
  severityTrends: SeverityTrend[];
  actionTrends: ActionTrend[];
  period: string;
  startDate: string;
  endDate: string;
}

interface ContentQualityTrendsDashboardProps {
  className?: string;
}

/**
 * 内容质量趋势分析仪表板组件
 * 
 * 用于显示内容质量的趋势分析数据
 */
const ContentQualityTrendsDashboard: React.FC<ContentQualityTrendsDashboardProps> = ({ className }) => {
  const { toast } = useToast();
  const api = useApi();
  const [isLoading, setIsLoading] = useState<boolean>(true);
  const [trends, setTrends] = useState<ContentQualityTrends | null>(null);
  const [startDate, setStartDate] = useState<Date | undefined>(undefined);
  const [endDate, setEndDate] = useState<Date | undefined>(undefined);
  const [period, setPeriod] = useState<string>('month');
  const [contentType, setContentType] = useState<string>('');
  
  // 加载趋势数据
  const loadTrends = async () => {
    setIsLoading(true);
    
    try {
      // 构建查询参数
      const params = new URLSearchParams();
      
      params.append('period', period);
      
      if (contentType) {
        params.append('contentType', contentType);
      }
      
      if (startDate) {
        params.append('startDate', startDate.toISOString());
      }
      
      if (endDate) {
        params.append('endDate', endDate.toISOString());
      }
      
      // 发送请求
      const response = await api.get(`/admin/content-moderation/quality-trends?${params.toString()}`);
      
      if (response.success) {
        setTrends(response.trends);
      } else {
        toast({
          title: '加载失败',
          description: response.error || '无法加载内容质量趋势数据',
          variant: 'destructive'
        });
      }
    } catch (error) {
      console.error('加载内容质量趋势数据失败:', error);
      toast({
        title: '加载失败',
        description: '服务器错误，请稍后再试',
        variant: 'destructive'
      });
    } finally {
      setIsLoading(false);
    }
  };
  
  // 组件加载时加载数据
  useEffect(() => {
    loadTrends();
  }, []);
  
  // 处理筛选
  const handleFilter = () => {
    loadTrends();
  };
  
  // 处理重置筛选
  const handleResetFilter = () => {
    setStartDate(undefined);
    setEndDate(undefined);
    setPeriod('month');
    setContentType('');
    
    // 重新加载数据
    setTimeout(() => {
      loadTrends();
    }, 0);
  };
  
  // 准备图表数据
  const prepareChartData = () => {
    if (!trends) return null;
    
    // 获取所有时间周期
    const timePeriods = [...new Set(trends.qualityTrends.map(item => item.timePeriod))].sort();
    
    // 获取所有内容类型
    const contentTypes = [...new Set(trends.qualityTrends.map(item => item.contentType))];
    
    // 质量评分趋势数据
    const qualityTrendsData = {
      labels: timePeriods,
      datasets: [
        {
          label: '数据质量评分',
          data: timePeriods.map(period => {
            const items = trends.qualityTrends.filter(item => item.timePeriod === period);
            const sum = items.reduce((acc, item) => acc + (item.avgDataQuality || 0) * item.count, 0);
            const totalCount = items.reduce((acc, item) => acc + item.count, 0);
            return totalCount > 0 ? sum / totalCount : null;
          }),
          borderColor: 'rgba(59, 130, 246, 1)',
          backgroundColor: 'rgba(59, 130, 246, 0.2)',
          fill: true,
          tension: 0.4,
        },
        {
          label: '建设性价值评分',
          data: timePeriods.map(period => {
            const items = trends.qualityTrends.filter(item => item.timePeriod === period);
            const sum = items.reduce((acc, item) => acc + (item.avgConstructiveValue || 0) * item.count, 0);
            const totalCount = items.reduce((acc, item) => acc + item.count, 0);
            return totalCount > 0 ? sum / totalCount : null;
          }),
          borderColor: 'rgba(16, 185, 129, 1)',
          backgroundColor: 'rgba(16, 185, 129, 0.2)',
          fill: true,
          tension: 0.4,
        },
        {
          label: '故事价值评分',
          data: timePeriods.map(period => {
            const items = trends.qualityTrends.filter(item => item.timePeriod === period);
            const sum = items.reduce((acc, item) => acc + (item.avgStoryValue || 0) * item.count, 0);
            const totalCount = items.reduce((acc, item) => acc + item.count, 0);
            return totalCount > 0 ? sum / totalCount : null;
          }),
          borderColor: 'rgba(249, 115, 22, 1)',
          backgroundColor: 'rgba(249, 115, 22, 0.2)',
          fill: true,
          tension: 0.4,
        },
      ],
    };
    
    // 安全性趋势数据
    const safetyTrendsData = {
      labels: timePeriods,
      datasets: [
        {
          label: '安全内容比例',
          data: timePeriods.map(period => {
            const items = trends.qualityTrends.filter(item => item.timePeriod === period);
            const safeCount = items.reduce((acc, item) => acc + item.safeCount, 0);
            const totalCount = items.reduce((acc, item) => acc + item.count, 0);
            return totalCount > 0 ? (safeCount / totalCount) * 100 : null;
          }),
          borderColor: 'rgba(34, 197, 94, 1)',
          backgroundColor: 'rgba(34, 197, 94, 0.2)',
          fill: true,
          tension: 0.4,
        },
        {
          label: '不安全内容比例',
          data: timePeriods.map(period => {
            const items = trends.qualityTrends.filter(item => item.timePeriod === period);
            const unsafeCount = items.reduce((acc, item) => acc + item.unsafeCount, 0);
            const totalCount = items.reduce((acc, item) => acc + item.count, 0);
            return totalCount > 0 ? (unsafeCount / totalCount) * 100 : null;
          }),
          borderColor: 'rgba(239, 68, 68, 1)',
          backgroundColor: 'rgba(239, 68, 68, 0.2)',
          fill: true,
          tension: 0.4,
        },
      ],
    };
    
    // 按内容类型的质量趋势
    const contentTypeQualityData = {
      labels: timePeriods,
      datasets: contentTypes.map((type, index) => {
        const colors = [
          { border: 'rgba(59, 130, 246, 1)', bg: 'rgba(59, 130, 246, 0.6)' },
          { border: 'rgba(16, 185, 129, 1)', bg: 'rgba(16, 185, 129, 0.6)' },
          { border: 'rgba(249, 115, 22, 1)', bg: 'rgba(249, 115, 22, 0.6)' },
          { border: 'rgba(139, 92, 246, 1)', bg: 'rgba(139, 92, 246, 0.6)' },
          { border: 'rgba(236, 72, 153, 1)', bg: 'rgba(236, 72, 153, 0.6)' },
        ];
        
        const colorIndex = index % colors.length;
        
        return {
          label: type === 'story' ? '故事' : 
                 type === 'questionnaire' ? '问卷' : 
                 type === 'comment' ? '评论' : 
                 type === 'profile' ? '个人资料' : 
                 type === 'feedback' ? '反馈' : type,
          data: timePeriods.map(period => {
            const item = trends.qualityTrends.find(i => i.timePeriod === period && i.contentType === type);
            return item ? (item.avgDataQuality || 0) : null;
          }),
          borderColor: colors[colorIndex].border,
          backgroundColor: colors[colorIndex].bg,
          borderWidth: 2,
        };
      }),
    };
    
    // 严重程度趋势数据
    const severityTrendsData = {
      labels: timePeriods,
      datasets: ['low', 'medium', 'high'].map((severity, index) => {
        const colors = [
          { border: 'rgba(234, 179, 8, 1)', bg: 'rgba(234, 179, 8, 0.6)' },
          { border: 'rgba(249, 115, 22, 1)', bg: 'rgba(249, 115, 22, 0.6)' },
          { border: 'rgba(239, 68, 68, 1)', bg: 'rgba(239, 68, 68, 0.6)' },
        ];
        
        return {
          label: severity === 'low' ? '低严重度' : 
                 severity === 'medium' ? '中严重度' : 
                 '高严重度',
          data: timePeriods.map(period => {
            const items = trends.severityTrends.filter(i => i.timePeriod === period && i.severity === severity);
            return items.length > 0 ? items[0].count : 0;
          }),
          borderColor: colors[index].border,
          backgroundColor: colors[index].bg,
          borderWidth: 2,
        };
      }),
    };
    
    return {
      qualityTrendsData,
      safetyTrendsData,
      contentTypeQualityData,
      severityTrendsData
    };
  };
  
  // 图表选项
  const lineChartOptions = {
    responsive: true,
    maintainAspectRatio: false,
    plugins: {
      legend: {
        position: 'bottom' as const,
      },
      tooltip: {
        callbacks: {
          label: function(context: any) {
            let label = context.dataset.label || '';
            if (label) {
              label += ': ';
            }
            if (context.parsed.y !== null) {
              label += context.parsed.y.toFixed(2);
            }
            return label;
          }
        }
      }
    },
    scales: {
      y: {
        beginAtZero: true
      }
    }
  };
  
  // 百分比图表选项
  const percentageChartOptions = {
    ...lineChartOptions,
    scales: {
      y: {
        beginAtZero: true,
        max: 100,
        ticks: {
          callback: function(value: any) {
            return value + '%';
          }
        }
      }
    }
  };
  
  // 准备图表数据
  const chartData = prepareChartData();
  
  // 获取周期显示文本
  const getPeriodText = () => {
    switch (period) {
      case 'day':
        return '日';
      case 'week':
        return '周';
      case 'year':
        return '年';
      default:
        return '月';
    }
  };
  
  return (
    <Card className={className}>
      <CardHeader className="flex flex-row items-center justify-between">
        <div>
          <CardTitle>内容质量趋势分析</CardTitle>
          <CardDescription>
            内容质量和安全性的变化趋势
          </CardDescription>
        </div>
        <Button variant="outline" size="sm" onClick={loadTrends} disabled={isLoading}>
          <RefreshCw className={`w-4 h-4 mr-2 ${isLoading ? 'animate-spin' : ''}`} />
          刷新
        </Button>
      </CardHeader>
      <CardContent>
        {/* 筛选条件 */}
        <div className="grid grid-cols-1 md:grid-cols-5 gap-4 mb-6">
          <div>
            <label className="text-sm font-medium mb-1 block">时间周期</label>
            <Select value={period} onValueChange={setPeriod}>
              <SelectTrigger>
                <SelectValue placeholder="选择时间周期" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="day">按日</SelectItem>
                <SelectItem value="week">按周</SelectItem>
                <SelectItem value="month">按月</SelectItem>
                <SelectItem value="year">按年</SelectItem>
              </SelectContent>
            </Select>
          </div>
          
          <div>
            <label className="text-sm font-medium mb-1 block">内容类型</label>
            <Select value={contentType} onValueChange={setContentType}>
              <SelectTrigger>
                <SelectValue placeholder="所有类型" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="">所有类型</SelectItem>
                <SelectItem value="story">故事</SelectItem>
                <SelectItem value="questionnaire">问卷</SelectItem>
                <SelectItem value="comment">评论</SelectItem>
                <SelectItem value="profile">个人资料</SelectItem>
                <SelectItem value="feedback">反馈</SelectItem>
              </SelectContent>
            </Select>
          </div>
          
          <div>
            <label className="text-sm font-medium mb-1 block">开始日期</label>
            <DatePicker
              date={startDate}
              setDate={setStartDate}
              placeholder="选择开始日期"
            />
          </div>
          
          <div>
            <label className="text-sm font-medium mb-1 block">结束日期</label>
            <DatePicker
              date={endDate}
              setDate={setEndDate}
              placeholder="选择结束日期"
            />
          </div>
          
          <div className="flex items-end gap-2">
            <Button onClick={handleFilter} className="flex-1">
              <Filter className="w-4 h-4 mr-2" />
              筛选
            </Button>
            <Button variant="outline" onClick={handleResetFilter}>
              重置
            </Button>
          </div>
        </div>
        
        {isLoading ? (
          <div className="space-y-4">
            <Skeleton className="h-[400px] w-full" />
          </div>
        ) : !trends || trends.qualityTrends.length === 0 ? (
          <div className="text-center py-8">
            <p className="text-muted-foreground">暂无内容质量趋势数据</p>
          </div>
        ) : (
          <>
            {/* 图表 */}
            <Tabs defaultValue="quality" className="w-full">
              <TabsList className="grid w-full grid-cols-4">
                <TabsTrigger value="quality">质量评分趋势</TabsTrigger>
                <TabsTrigger value="safety">安全性趋势</TabsTrigger>
                <TabsTrigger value="content-type">内容类型趋势</TabsTrigger>
                <TabsTrigger value="severity">严重程度趋势</TabsTrigger>
              </TabsList>
              
              <TabsContent value="quality" className="mt-4">
                <Card>
                  <CardHeader>
                    <CardTitle>质量评分趋势（按{getPeriodText()}）</CardTitle>
                    <CardDescription>
                      显示不同质量指标随时间的变化趋势
                    </CardDescription>
                  </CardHeader>
                  <CardContent>
                    <div className="h-[400px]">
                      {chartData && <Line data={chartData.qualityTrendsData} options={lineChartOptions} />}
                    </div>
                  </CardContent>
                </Card>
              </TabsContent>
              
              <TabsContent value="safety" className="mt-4">
                <Card>
                  <CardHeader>
                    <CardTitle>内容安全性趋势（按{getPeriodText()}）</CardTitle>
                    <CardDescription>
                      显示安全内容和不安全内容比例的变化趋势
                    </CardDescription>
                  </CardHeader>
                  <CardContent>
                    <div className="h-[400px]">
                      {chartData && <Line data={chartData.safetyTrendsData} options={percentageChartOptions} />}
                    </div>
                  </CardContent>
                </Card>
              </TabsContent>
              
              <TabsContent value="content-type" className="mt-4">
                <Card>
                  <CardHeader>
                    <CardTitle>按内容类型的质量趋势（按{getPeriodText()}）</CardTitle>
                    <CardDescription>
                      显示不同内容类型的质量评分变化趋势
                    </CardDescription>
                  </CardHeader>
                  <CardContent>
                    <div className="h-[400px]">
                      {chartData && <Line data={chartData.contentTypeQualityData} options={lineChartOptions} />}
                    </div>
                  </CardContent>
                </Card>
              </TabsContent>
              
              <TabsContent value="severity" className="mt-4">
                <Card>
                  <CardHeader>
                    <CardTitle>问题严重程度趋势（按{getPeriodText()}）</CardTitle>
                    <CardDescription>
                      显示不同严重程度问题数量的变化趋势
                    </CardDescription>
                  </CardHeader>
                  <CardContent>
                    <div className="h-[400px]">
                      {chartData && <Line data={chartData.severityTrendsData} options={lineChartOptions} />}
                    </div>
                  </CardContent>
                </Card>
              </TabsContent>
            </Tabs>
          </>
        )}
      </CardContent>
    </Card>
  );
};

export default ContentQualityTrendsDashboard;
