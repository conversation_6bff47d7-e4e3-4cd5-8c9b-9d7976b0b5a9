const http = require('http');
const url = require('url');
const path = require('path');
const { execSync } = require('child_process');

/**
 * 统一API服务器 v1.0
 *
 * 目标：将新功能逐步集成到原始API服务器中，实现单一API服务器部署
 * 策略：
 * 1. 保持现有管理员API不变
 * 2. 添加新的前端展示API
 * 3. 使用功能开关控制新功能
 * 4. 确保向后兼容
 */

const PORT = 8790; // 新的统一端口
const DB_PATH = path.join(__dirname, 'database.db');

// 功能开关配置
const FEATURE_FLAGS = {
  realtimeStats: true,        // 实时统计功能
  storyTagFilter: true,       // 故事标签筛选
  simpleReviewer: true,       // 简化审核工作台
  legacyAdminAPI: true,       // 保持原有管理员API
  newFrontendAPI: true        // 新的前端展示API
};

// CORS头
const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
  'Access-Control-Allow-Headers': 'Content-Type, Authorization',
  'Access-Control-Max-Age': '86400'
};

// 发送JSON响应
function sendJSON(res, data, statusCode = 200) {
  res.writeHead(statusCode, {
    'Content-Type': 'application/json',
    ...corsHeaders
  });
  res.end(JSON.stringify(data, null, 2));
}

// 数据库查询函数
function queryDatabase(sql, params = []) {
  try {
    let command;
    if (params.length > 0) {
      const escapedSql = sql.replace(/\?/g, () => {
        const param = params.shift();
        return typeof param === 'string' ? `'${param.replace(/'/g, "''")}'` : param;
      });
      command = `sqlite3 "${DB_PATH}" "${escapedSql.replace(/"/g, '\\"')}"`;
    } else {
      command = `sqlite3 "${DB_PATH}" "${sql.replace(/"/g, '\\"')}"`;
    }

    const result = execSync(command, { encoding: 'utf8' });
    return result.trim();
  } catch (error) {
    console.error('数据库查询失败:', error.message);
    return null;
  }
}

// 获取单个数值
function queryDatabaseValue(sql, params = []) {
  const result = queryDatabase(sql, params);
  return result ? parseInt(result) || result : 0;
}

// 创建服务器
const server = http.createServer(async (req, res) => {
  const parsedUrl = url.parse(req.url, true);
  const path = parsedUrl.pathname;
  const method = req.method;

  console.log(`📡 ${method} ${path}`);

  // 处理OPTIONS请求
  if (method === 'OPTIONS') {
    res.writeHead(200, corsHeaders);
    res.end();
    return;
  }

  // 健康检查
  if (path === '/health' && method === 'GET') {
    sendJSON(res, {
      success: true,
      message: '统一API服务器运行正常',
      version: '1.0',
      timestamp: new Date().toISOString(),
      features: FEATURE_FLAGS,
      services: {
        database: 'connected',
        legacyAPI: 'integrated',
        newAPI: 'active'
      }
    });
    return;
  }

  // ==========================================
  // 新功能API (前端展示用)
  // ==========================================

  // 问卷统计API (新版本，兼容原有格式)
  if (path === '/api/questionnaire/stats' && method === 'GET' && FEATURE_FLAGS.newFrontendAPI) {
    try {
      const totalResponses = queryDatabaseValue('SELECT COUNT(*) FROM questionnaire_responses_v2');
      const employedCount = queryDatabaseValue("SELECT COUNT(*) FROM questionnaire_responses_v2 WHERE employment_status = 'employed'");
      const verifiedCount = queryDatabaseValue("SELECT COUNT(*) FROM users_v2 WHERE user_type = 'registered'");
      const voicesCount = queryDatabaseValue("SELECT COUNT(*) FROM questionnaire_voices_v2 WHERE status = 'approved'");

      // 获取教育水平分布
      const educationLevels = [];
      const educationData = queryDatabase(`
        SELECT
          education_level as code,
          education_level_display as name,
          COUNT(*) as count
        FROM questionnaire_responses_v2
        WHERE education_level IS NOT NULL
        GROUP BY education_level, education_level_display
        ORDER BY
          CASE education_level
            WHEN 'high_school' THEN 1
            WHEN 'associate' THEN 2
            WHEN 'undergraduate' THEN 3
            WHEN 'master' THEN 4
            WHEN 'phd' THEN 5
            ELSE 6
          END
      `);

      if (educationData) {
        educationData.split('\n').forEach(line => {
          if (line.trim()) {
            const [code, name, count] = line.split('|');
            if (code && name && count) {
              educationLevels.push({
                code: code.trim(),
                name: name.trim(),
                count: parseInt(count.trim()),
                percentage: totalResponses > 0 ? Math.round((parseInt(count.trim()) / totalResponses) * 100) : 0
              });
            }
          }
        });
      }

      sendJSON(res, {
        success: true,
        statistics: {
          totalResponses,
          employedCount,
          unemployedCount: totalResponses - employedCount,
          verifiedCount,
          anonymousCount: totalResponses - verifiedCount,
          voicesCount,
          employmentRate: totalResponses > 0 ? Math.round((employedCount / totalResponses) * 100) : 0,
          educationLevels
        },
        metadata: {
          lastUpdated: new Date().toISOString(),
          dataVersion: '3.0',
          dataSource: 'unified_api_server',
          apiType: 'new_frontend_api'
        }
      });
    } catch (error) {
      console.error('问卷统计API错误:', error);
      sendJSON(res, {
        success: false,
        error: '获取统计数据失败',
        message: error.message
      }, 500);
    }
    return;
  }

  // 实时统计API (新功能)
  if (path === '/api/questionnaire/realtime-stats' && method === 'GET' && FEATURE_FLAGS.realtimeStats) {
    try {
      const totalSubmissions = queryDatabaseValue('SELECT COUNT(*) FROM questionnaire_responses_v2');

      const questionStats = {};

      // 教育水平统计
      const educationStats = [];
      const educationData = queryDatabase(`
        SELECT
          education_level as code,
          education_level_display as name,
          COUNT(*) as count
        FROM questionnaire_responses_v2
        WHERE education_level IS NOT NULL
        GROUP BY education_level, education_level_display
        ORDER BY
          CASE education_level
            WHEN 'high_school' THEN 1
            WHEN 'associate' THEN 2
            WHEN 'undergraduate' THEN 3
            WHEN 'master' THEN 4
            WHEN 'phd' THEN 5
            ELSE 6
          END
      `);

      if (educationData) {
        educationData.split('\n').forEach(line => {
          if (line.trim()) {
            const [code, name, count] = line.split('|');
            if (code && name && count) {
              const countNum = parseInt(count.trim());
              educationStats.push({
                code: code.trim(),
                name: name.trim(),
                count: countNum,
                percentage: totalSubmissions > 0 ? Math.round((countNum / totalSubmissions) * 100) : 0
              });
            }
          }
        });
      }
      questionStats.education_level = educationStats;

      sendJSON(res, {
        success: true,
        totalSubmissions,
        questionStats,
        metadata: {
          lastUpdated: new Date().toISOString(),
          dataVersion: '3.0',
          dataSource: 'unified_api_server',
          refreshInterval: 30000,
          featureFlag: 'realtimeStats'
        }
      });
    } catch (error) {
      console.error('问卷实时统计API错误:', error);
      sendJSON(res, {
        success: false,
        error: '获取实时统计失败',
        message: error.message
      }, 500);
    }
    return;
  }

  // 故事列表API (增强版，支持标签筛选)
  if (path === '/api/story/list' && method === 'GET' && FEATURE_FLAGS.storyTagFilter) {
    try {
      const urlParams = new URLSearchParams(parsedUrl.query);
      const page = parseInt(urlParams.get('page')) || 1;
      const pageSize = parseInt(urlParams.get('pageSize')) || 10;
      const category = urlParams.get('category');
      const tag = urlParams.get('tag');
      const offset = (page - 1) * pageSize;

      let whereClause = "WHERE s.status = 'approved'";
      if (category) {
        whereClause += ` AND s.category = '${category.replace(/'/g, "''")}'`;
      }
      if (tag) {
        whereClause += ` AND EXISTS (
          SELECT 1 FROM content_tags_v2 ct
          JOIN tags_v2 t ON ct.tag_id = t.id
          WHERE ct.content_id = s.id AND ct.content_type = 'story'
          AND (t.name = '${tag.replace(/'/g, "''")}' OR t.id = '${tag.replace(/'/g, "''")}')
        )`;
      }

      const stories = [];
      const storyData = queryDatabase(`
        SELECT
          s.id,
          s.title,
          s.content,
          s.summary,
          s.category,
          s.education_level_display as educationLevel,
          s.industry_display as industry,
          s.is_anonymous as isAnonymous,
          s.likes,
          s.dislikes,
          s.views,
          s.created_at,
          CASE WHEN s.is_anonymous = 1 THEN '匿名用户' ELSE u.display_name END as author
        FROM story_contents_v2 s
        LEFT JOIN users_v2 u ON s.user_id = u.id
        ${whereClause}
        ORDER BY s.created_at DESC
        LIMIT ${pageSize} OFFSET ${offset}
      `);

      if (storyData) {
        for (const line of storyData.split('\n')) {
          if (line.trim()) {
            const parts = line.split('|');
            if (parts.length >= 12) {
              const storyId = parts[0]?.trim();

              const tagData = queryDatabase(`
                SELECT t.name
                FROM content_tags_v2 ct
                JOIN tags_v2 t ON ct.tag_id = t.id
                WHERE ct.content_id = '${storyId}' AND ct.content_type = 'story'
              `);

              const tags = tagData ? tagData.split('\n').map(tag => tag.trim()).filter(tag => tag) : [];

              stories.push({
                id: storyId,
                title: parts[1]?.trim() || '',
                content: parts[2]?.trim() || '',
                summary: parts[3]?.trim() || '',
                category: parts[4]?.trim() || '',
                educationLevel: parts[5]?.trim() || '',
                industry: parts[6]?.trim() || '',
                isAnonymous: parts[7]?.trim() === '1',
                likes: parseInt(parts[8]?.trim()) || 0,
                dislikes: parseInt(parts[9]?.trim()) || 0,
                views: parseInt(parts[10]?.trim()) || 0,
                createdAt: parts[11]?.trim() || '',
                author: parts[12]?.trim() || '匿名用户',
                tags
              });
            }
          }
        }
      }

      const totalCount = queryDatabaseValue(`
        SELECT COUNT(*)
        FROM story_contents_v2 s
        ${whereClause}
      `);

      const totalPages = Math.ceil(totalCount / pageSize);

      const popularTags = [];
      const tagData = queryDatabase(`
        SELECT t.name, t.usage_count
        FROM tags_v2 t
        WHERE t.usage_count > 0
        ORDER BY t.usage_count DESC
        LIMIT 10
      `);

      if (tagData) {
        tagData.split('\n').forEach(line => {
          if (line.trim()) {
            const [name] = line.split('|');
            if (name) {
              popularTags.push(name.trim());
            }
          }
        });
      }

      sendJSON(res, {
        success: true,
        stories,
        pagination: {
          currentPage: page,
          pageSize,
          totalCount,
          totalPages,
          hasMore: page < totalPages
        },
        popularTags,
        filters: { category, tag },
        metadata: {
          lastUpdated: new Date().toISOString(),
          dataVersion: '3.0',
          dataSource: 'unified_api_server',
          featureFlag: 'storyTagFilter'
        }
      });
    } catch (error) {
      console.error('故事列表API错误:', error);
      sendJSON(res, {
        success: false,
        error: '获取故事列表失败',
        message: error.message
      }, 500);
    }
    return;
  }

  // 简化审核工作台API (新功能)
  if (path === '/api/reviewer/pending-content' && method === 'GET' && FEATURE_FLAGS.simpleReviewer) {
    try {
      const urlParams = new URLSearchParams(parsedUrl.query);
      const contentType = urlParams.get('type') || 'all';
      const limit = parseInt(urlParams.get('limit')) || 10;

      let pendingContent = [];

      if (contentType === 'voice' || contentType === 'all') {
        const voiceData = queryDatabase(`
          SELECT
            v.id,
            'voice' as content_type,
            v.voice_type,
            v.title,
            v.content,
            v.education_level_display,
            v.region_display,
            v.created_at,
            'pending' as status
          FROM questionnaire_voices_v2 v
          WHERE v.status = 'pending'
          ORDER BY v.created_at ASC
          LIMIT ${contentType === 'voice' ? limit : Math.floor(limit / 2)}
        `);

        if (voiceData) {
          voiceData.split('\n').forEach(line => {
            if (line.trim()) {
              const parts = line.split('|');
              if (parts.length >= 8) {
                pendingContent.push({
                  id: parts[0]?.trim(),
                  contentType: parts[1]?.trim(),
                  voiceType: parts[2]?.trim(),
                  title: parts[3]?.trim(),
                  content: parts[4]?.trim(),
                  educationLevel: parts[5]?.trim(),
                  region: parts[6]?.trim(),
                  createdAt: parts[7]?.trim(),
                  status: parts[8]?.trim()
                });
              }
            }
          });
        }
      }

      if (contentType === 'story' || contentType === 'all') {
        const storyData = queryDatabase(`
          SELECT
            s.id,
            'story' as content_type,
            s.category,
            s.title,
            s.content,
            s.education_level_display,
            s.industry_display,
            s.created_at,
            'pending' as status
          FROM story_contents_v2 s
          WHERE s.status = 'pending'
          ORDER BY s.created_at ASC
          LIMIT ${contentType === 'story' ? limit : Math.floor(limit / 2)}
        `);

        if (storyData) {
          storyData.split('\n').forEach(line => {
            if (line.trim()) {
              const parts = line.split('|');
              if (parts.length >= 8) {
                pendingContent.push({
                  id: parts[0]?.trim(),
                  contentType: parts[1]?.trim(),
                  category: parts[2]?.trim(),
                  title: parts[3]?.trim(),
                  content: parts[4]?.trim(),
                  educationLevel: parts[5]?.trim(),
                  industry: parts[6]?.trim(),
                  createdAt: parts[7]?.trim(),
                  status: parts[8]?.trim()
                });
              }
            }
          });
        }
      }

      pendingContent.sort((a, b) => new Date(a.createdAt).getTime() - new Date(b.createdAt).getTime());

      if (pendingContent.length > limit) {
        pendingContent = pendingContent.slice(0, limit);
      }

      sendJSON(res, {
        success: true,
        content: pendingContent,
        total: pendingContent.length,
        filters: { contentType },
        metadata: {
          lastUpdated: new Date().toISOString(),
          dataVersion: '3.0',
          dataSource: 'unified_api_server',
          featureFlag: 'simpleReviewer'
        }
      });
    } catch (error) {
      console.error('审核员工作台API错误:', error);
      sendJSON(res, {
        success: false,
        error: '获取待审核内容失败',
        message: error.message
      }, 500);
    }
    return;
  }

  // 审核内容API (新功能)
  if (path.startsWith('/api/reviewer/review/') && method === 'POST' && FEATURE_FLAGS.simpleReviewer) {
    try {
      const contentId = path.split('/').pop();

      let body = '';
      req.on('data', chunk => {
        body += chunk.toString();
      });

      req.on('end', () => {
        try {
          const { action, reason, contentType } = JSON.parse(body);

          if (!['approved', 'rejected'].includes(action)) {
            sendJSON(res, {
              success: false,
              error: '无效的审核操作'
            }, 400);
            return;
          }

          let updateSql = '';
          if (contentType === 'voice') {
            updateSql = `UPDATE questionnaire_voices_v2 SET status = '${action}' WHERE id = '${contentId}'`;
          } else if (contentType === 'story') {
            updateSql = `UPDATE story_contents_v2 SET status = '${action}' WHERE id = '${contentId}'`;
          } else {
            sendJSON(res, {
              success: false,
              error: '无效的内容类型'
            }, 400);
            return;
          }

          queryDatabase(updateSql);

          const reviewRecordSql = `
            INSERT INTO review_records_v2 (
              id, reviewer_id, content_id, content_type, action, reason, created_at
            ) VALUES (
              'review_${Date.now()}_${Math.random().toString(36).substr(2, 9)}',
              'reviewer_system',
              '${contentId}',
              '${contentType}',
              '${action}',
              '${reason ? reason.replace(/'/g, "''") : ''}',
              '${new Date().toISOString()}'
            )
          `;

          queryDatabase(reviewRecordSql);

          sendJSON(res, {
            success: true,
            message: `内容已${action === 'approved' ? '通过' : '拒绝'}审核`,
            contentId,
            action,
            reason,
            metadata: {
              featureFlag: 'simpleReviewer',
              apiType: 'unified_api_server'
            }
          });
        } catch (parseError) {
          sendJSON(res, {
            success: false,
            error: '请求数据格式错误',
            message: parseError.message
          }, 400);
        }
      });
    } catch (error) {
      console.error('审核内容API错误:', error);
      sendJSON(res, {
        success: false,
        error: '审核操作失败',
        message: error.message
      }, 500);
    }
    return;
  }

  // ==========================================
  // 原有管理员API占位符 (待集成)
  // ==========================================

  // 管理员API占位符 - 这里应该集成原有的管理员功能
  if (path.startsWith('/api/admin/') && FEATURE_FLAGS.legacyAdminAPI) {
    sendJSON(res, {
      success: false,
      error: '管理员API待集成',
      message: '此API端点需要从原始后端迁移',
      path: path,
      suggestion: '请使用原始API服务器或等待功能迁移完成'
    }, 501);
    return;
  }

  // 审核员API占位符 - 这里应该集成原有的审核员功能
  if (path.startsWith('/api/reviewer/') && !path.includes('pending-content') && !path.includes('review/') && FEATURE_FLAGS.legacyAdminAPI) {
    sendJSON(res, {
      success: false,
      error: '审核员API待集成',
      message: '此API端点需要从原始后端迁移',
      path: path,
      suggestion: '请使用原始API服务器或等待功能迁移完成'
    }, 501);
    return;
  }

  // 超级管理员API占位符
  if (path.startsWith('/api/superadmin/') && FEATURE_FLAGS.legacyAdminAPI) {
    sendJSON(res, {
      success: false,
      error: '超级管理员API待集成',
      message: '此API端点需要从原始后端迁移',
      path: path,
      suggestion: '请使用原始API服务器或等待功能迁移完成'
    }, 501);
    return;
  }

  // 404 处理
  sendJSON(res, {
    success: false,
    error: 'API端点不存在',
    path: path,
    availableEndpoints: [
      'GET /health',
      'GET /api/questionnaire/stats (新版)',
      'GET /api/questionnaire/realtime-stats (新功能)',
      'GET /api/story/list (增强版)',
      'GET /api/reviewer/pending-content (新功能)',
      'POST /api/reviewer/review/:id (新功能)',
      '待集成: /api/admin/*',
      '待集成: /api/reviewer/* (原有功能)',
      '待集成: /api/superadmin/*'
    ],
    featureFlags: FEATURE_FLAGS
  }, 404);
});

// 启动服务器
server.listen(PORT, () => {
  console.log('======================================');
  console.log('       统一API服务器 v1.0');
  console.log('======================================');
  console.log(`🚀 服务器启动成功！`);
  console.log(`📡 监听端口: ${PORT}`);
  console.log(`💾 数据库: ${DB_PATH}`);
  console.log(`🌐 健康检查: http://localhost:${PORT}/health`);
  console.log('======================================');
  console.log('');
  console.log('🎯 功能状态:');
  Object.entries(FEATURE_FLAGS).forEach(([feature, enabled]) => {
    const status = enabled ? '✅ 启用' : '❌ 禁用';
    console.log(`   ${feature}: ${status}`);
  });
  console.log('');
  console.log('📊 API分类:');
  console.log('✅ 新功能API → 完全集成');
  console.log('⏳ 管理员API → 待迁移 (返回501状态)');
  console.log('🔧 这是生产部署的单一API服务器方案');
});

// 错误处理
server.on('error', (error) => {
  console.error('❌ 服务器错误:', error);
});

process.on('uncaughtException', (error) => {
  console.error('❌ 未捕获的异常:', error);
});

process.on('unhandledRejection', (reason, promise) => {
  console.error('❌ 未处理的Promise拒绝:', reason);
});
