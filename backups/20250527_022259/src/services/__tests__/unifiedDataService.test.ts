/**
 * 统一数据服务层测试
 */

import { describe, it, expect, vi, beforeEach } from 'vitest';
import { apiClient } from '../apiClient';
import * as mockData from '../mockData';

// 模拟apiClient
vi.mock('../apiClient', () => ({
  apiClient: {
    get: vi.fn(),
    post: vi.fn(),
    put: vi.fn(),
    delete: vi.fn(),
    patch: vi.fn()
  }
}));

// 模拟mockData
vi.mock('../mockData', () => ({
  testMockFunction: vi.fn()
}));

describe('unifiedDataService', () => {
  beforeEach(() => {
    // 重置所有模拟
    vi.resetAllMocks();

    // 清除模块缓存
    vi.resetModules();
  });

  describe('fetchData', () => {
    it.skip('should use mock data when VITE_USE_MOCK is true', async () => {
      // 模拟环境变量
      vi.stubEnv('VITE_USE_MOCK', 'true');
      vi.stubEnv('VITE_API_VERSION', 'v1');
      vi.stubEnv('VITE_MOCK_DELAY', '0');

      // 设置模拟返回值
      (mockData.testMockFunction as any).mockReturnValue({ test: 'mock data' });

      // 模拟Promise.resolve以避免延迟
      const originalPromise = global.Promise;
      global.Promise = class extends originalPromise {
        static resolve(value) {
          return originalPromise.resolve(value);
        }
      } as any;

      // 调用函数
      const { fetchData } = await import('../unifiedDataService');
      const result = await fetchData('/test', 'testMockFunction', { param: 'value' });

      // 验证结果
      expect(mockData.testMockFunction).toHaveBeenCalledWith({ param: 'value' });
      expect(result).toEqual({
        success: true,
        data: { test: 'mock data' },
        source: 'mock'
      });
      expect(apiClient.get).not.toHaveBeenCalled();

      // 恢复原始Promise
      global.Promise = originalPromise;
    });

    it('should use real API when VITE_USE_MOCK is false', async () => {
      // 模拟环境变量
      vi.stubEnv('VITE_USE_MOCK', 'false');
      vi.stubEnv('VITE_API_VERSION', 'v1');

      // 设置模拟返回值
      (apiClient.get as any).mockResolvedValue({
        data: { test: 'real data' }
      });

      // 调用函数
      const { fetchData } = await import('../unifiedDataService');
      const result = await fetchData('/test', 'testMockFunction', { param: 'value' });

      // 验证结果
      expect(apiClient.get).toHaveBeenCalledWith('/v1/test', { params: { param: 'value' } });
      expect(result).toEqual({
        test: 'real data',
        source: 'api'
      });
      expect(mockData.testMockFunction).not.toHaveBeenCalled();
    });

    it('should use different HTTP methods based on options', async () => {
      // 模拟环境变量
      vi.stubEnv('VITE_USE_MOCK', 'false');
      vi.stubEnv('VITE_API_VERSION', 'v1');

      // 设置模拟返回值
      (apiClient.post as any).mockResolvedValue({
        data: { test: 'post data' }
      });

      // 调用函数
      const { fetchData } = await import('../unifiedDataService');
      const result = await fetchData('/test', 'testMockFunction', { param: 'value' }, { method: 'POST' });

      // 验证结果
      expect(apiClient.post).toHaveBeenCalledWith('/v1/test', { param: 'value' });
      expect(result).toEqual({
        test: 'post data',
        source: 'api'
      });
    });

    it.skip('should fall back to mock data when API request fails', async () => {
      // 模拟环境变量
      vi.stubEnv('VITE_USE_MOCK', 'false');
      vi.stubEnv('VITE_API_VERSION', 'v1');
      vi.stubEnv('VITE_MOCK_DELAY', '0');

      // 设置模拟返回值
      (apiClient.get as any).mockRejectedValue(new Error('API error'));
      (mockData.testMockFunction as any).mockReturnValue({ test: 'fallback data' });

      // 模拟Promise.resolve以避免延迟
      const originalPromise = global.Promise;
      global.Promise = class extends originalPromise {
        static resolve(value) {
          return originalPromise.resolve(value);
        }
      } as any;

      // 调用函数
      const { fetchData } = await import('../unifiedDataService');
      const result = await fetchData('/test', 'testMockFunction', { param: 'value' });

      // 验证结果
      expect(apiClient.get).toHaveBeenCalledWith('/v1/test', { params: { param: 'value' } });
      expect(mockData.testMockFunction).toHaveBeenCalledWith({ param: 'value' });
      expect(result).toEqual({
        success: true,
        data: { test: 'fallback data' },
        source: 'mock-fallback'
      });

      // 恢复原始Promise
      global.Promise = originalPromise;
    });

    it.skip('should throw error when mock data function does not exist', async () => {
      // 模拟环境变量
      vi.stubEnv('VITE_USE_MOCK', 'true');
      vi.stubEnv('VITE_API_VERSION', 'v1');
      vi.stubEnv('VITE_MOCK_DELAY', '0');

      // 修改模拟数据
      vi.mocked(mockData).testMockFunction = undefined as any;

      // 模拟Promise.resolve以避免延迟
      const originalPromise = global.Promise;
      global.Promise = class extends originalPromise {
        static resolve(value) {
          return originalPromise.resolve(value);
        }
      } as any;

      // 调用函数并验证错误
      const { fetchData } = await import('../unifiedDataService');

      try {
        await fetchData('/test', 'testMockFunction', { param: 'value' });
        // 如果没有抛出错误，测试应该失败
        expect(true).toBe(false);
      } catch (error) {
        expect(error.message).toContain("Mock data function 'testMockFunction' not found");
      }

      // 恢复原始Promise
      global.Promise = originalPromise;
    });
  });

  describe('getDataSourceType', () => {
    it('should return "mock" when VITE_USE_MOCK is true', async () => {
      // 模拟环境变量
      vi.stubEnv('VITE_USE_MOCK', 'true');

      // 调用函数
      const { getDataSourceType } = await import('../unifiedDataService');
      const result = getDataSourceType();

      // 验证结果
      expect(result).toBe('mock');
    });

    it('should return "api" when VITE_USE_MOCK is false', async () => {
      // 模拟环境变量
      vi.stubEnv('VITE_USE_MOCK', 'false');

      // 调用函数
      const { getDataSourceType } = await import('../unifiedDataService');
      const result = getDataSourceType();

      // 验证结果
      expect(result).toBe('api');
    });
  });

  describe('isUsingMockData', () => {
    it('should return true when VITE_USE_MOCK is true', async () => {
      // 模拟环境变量
      vi.stubEnv('VITE_USE_MOCK', 'true');

      // 调用函数
      const { isUsingMockData } = await import('../unifiedDataService');
      const result = isUsingMockData();

      // 验证结果
      expect(result).toBe(true);
    });

    it('should return false when VITE_USE_MOCK is false', async () => {
      // 模拟环境变量
      vi.stubEnv('VITE_USE_MOCK', 'false');

      // 调用函数
      const { isUsingMockData } = await import('../unifiedDataService');
      const result = isUsingMockData();

      // 验证结果
      expect(result).toBe(false);
    });
  });
});
