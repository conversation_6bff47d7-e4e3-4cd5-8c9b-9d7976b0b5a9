/**
 * 导出工具函数
 */

/**
 * 创建CSV格式的导出数据
 * @param data 要导出的数据
 * @returns CSV格式的字符串
 */
export function createCSV(data: any): string {
  if (!data || typeof data !== 'object') {
    return '';
  }
  
  // 如果是数组，导出为表格形式
  if (Array.isArray(data)) {
    // 获取所有可能的字段
    const fields = new Set<string>();
    data.forEach(item => {
      if (item && typeof item === 'object') {
        Object.keys(item).forEach(key => fields.add(key));
      }
    });
    
    // 转换为CSV
    const fieldArray = Array.from(fields);
    const header = fieldArray.join(',');
    const rows = data.map(item => {
      return fieldArray.map(field => {
        const value = item[field];
        // 处理包含逗号、引号或换行符的值
        if (value === null || value === undefined) {
          return '';
        } else if (typeof value === 'string' && (value.includes(',') || value.includes('"') || value.includes('\n'))) {
          return `"${value.replace(/"/g, '""')}"`;
        } else {
          return String(value);
        }
      }).join(',');
    });
    
    return `${header}\n${rows.join('\n')}`;
  }
  
  // 如果是对象，处理嵌套结构
  const rows: string[] = [];
  
  // 处理对象的每个属性
  Object.entries(data).forEach(([key, value]) => {
    if (Array.isArray(value)) {
      // 如果属性值是数组，为每个数组项创建一行
      rows.push(`"${key}"`);
      
      if (value.length > 0 && typeof value[0] === 'object') {
        // 如果数组项是对象，创建子表格
        const subFields = new Set<string>();
        value.forEach(item => {
          if (item && typeof item === 'object') {
            Object.keys(item).forEach(subKey => subFields.add(subKey));
          }
        });
        
        const subFieldArray = Array.from(subFields);
        rows.push(subFieldArray.join(','));
        
        value.forEach(item => {
          rows.push(subFieldArray.map(field => {
            const subValue = item[field];
            if (subValue === null || subValue === undefined) {
              return '';
            } else if (typeof subValue === 'string' && (subValue.includes(',') || subValue.includes('"') || subValue.includes('\n'))) {
              return `"${subValue.replace(/"/g, '""')}"`;
            } else {
              return String(subValue);
            }
          }).join(','));
        });
      } else {
        // 如果数组项是简单值，直接添加
        value.forEach(item => rows.push(String(item)));
      }
      
      rows.push(''); // 添加空行分隔
    } else if (value && typeof value === 'object') {
      // 如果属性值是对象，递归处理
      rows.push(`"${key}"`);
      rows.push(createCSV(value));
      rows.push(''); // 添加空行分隔
    } else {
      // 如果属性值是简单值，直接添加
      rows.push(`"${key}",${value === null || value === undefined ? '' : String(value)}`);
    }
  });
  
  return rows.join('\n');
}

/**
 * 创建Excel格式的导出数据
 * @param data 要导出的数据
 * @returns Excel文件的Buffer
 */
export async function createExcel(data: any): Promise<Buffer> {
  // 由于无法在Cloudflare Workers中使用Node.js的Excel库
  // 这里返回一个简单的CSV格式，前端可以将其转换为Excel
  const csv = createCSV(data);
  return Buffer.from(csv);
}

/**
 * 创建JSON格式的导出数据
 * @param data 要导出的数据
 * @returns JSON格式的字符串
 */
export function createJSON(data: any): string {
  return JSON.stringify(data, null, 2);
}

/**
 * 创建PDF格式的导出数据
 * @param data 要导出的数据
 * @param type 导出类型
 * @returns PDF文件的Buffer
 */
export async function createPDF(data: any, type: string): Promise<Buffer> {
  // 由于无法在Cloudflare Workers中使用Node.js的PDF库
  // 这里返回一个简单的文本，表示这是一个PDF
  const content = `This is a mock PDF for ${type} export.\n\nData: ${JSON.stringify(data, null, 2)}`;
  return Buffer.from(content);
}

/**
 * 创建PNG格式的图表
 * @param data 要导出的数据
 * @param chartType 图表类型
 * @returns PNG图片的Buffer
 */
export async function createPNG(data: any, chartType: string): Promise<Buffer> {
  // 由于无法在Cloudflare Workers中生成图片
  // 这里返回一个简单的文本，表示这是一个PNG图表
  const content = `This is a mock PNG chart of type ${chartType}.\n\nData: ${JSON.stringify(data, null, 2)}`;
  return Buffer.from(content);
}

/**
 * 创建SVG格式的图表
 * @param data 要导出的数据
 * @param chartType 图表类型
 * @returns SVG图片的字符串
 */
export function createSVG(data: any, chartType: string): string {
  // 创建一个简单的SVG图表
  const width = 500;
  const height = 300;
  
  // SVG头部
  let svg = `<svg width="${width}" height="${height}" xmlns="http://www.w3.org/2000/svg">`;
  
  // 添加标题
  svg += `<text x="${width/2}" y="30" text-anchor="middle" font-size="16" font-weight="bold">Mock ${chartType} Chart</text>`;
  
  // 添加一些模拟图表元素
  svg += `<rect x="50" y="50" width="400" height="200" fill="#f0f0f0" stroke="#ccc" />`;
  
  // 如果有数据，添加一些模拟数据点
  if (data && typeof data === 'object') {
    let dataPoints = [];
    
    if (Array.isArray(data)) {
      dataPoints = data.slice(0, 10);
    } else if (data.educationLevels) {
      dataPoints = data.educationLevels;
    } else if (data.regions) {
      dataPoints = data.regions;
    } else {
      dataPoints = Object.entries(data).map(([key, value]) => ({ name: key, value }));
    }
    
    // 绘制简单的柱状图
    const barWidth = 350 / dataPoints.length;
    const maxValue = Math.max(...dataPoints.map((p: any) => p.count || p.value || 0));
    
    dataPoints.forEach((point: any, index: number) => {
      const value = point.count || point.value || 0;
      const height = (value / maxValue) * 150;
      const x = 75 + index * barWidth;
      const y = 250 - height;
      
      svg += `<rect x="${x}" y="${y}" width="${barWidth * 0.8}" height="${height}" fill="#4f46e5" />`;
      svg += `<text x="${x + barWidth * 0.4}" y="${y - 5}" text-anchor="middle" font-size="10">${value}</text>`;
      svg += `<text x="${x + barWidth * 0.4}" y="270" text-anchor="middle" font-size="10" transform="rotate(-45 ${x + barWidth * 0.4}, 270)">${point.level || point.region || point.name || index}</text>`;
    });
  }
  
  // SVG尾部
  svg += `</svg>`;
  
  return svg;
}
