import { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Textarea } from '@/components/ui/textarea';
import { useToast } from '@/components/ui/use-toast';
import { 
  CheckCircle, 
  XCircle, 
  Clock, 
  MessageSquare, 
  BookOpen, 
  RefreshCw,
  Filter,
  Eye
} from 'lucide-react';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';

interface PendingContent {
  id: string;
  contentType: 'voice' | 'story';
  voiceType?: string;
  category?: string;
  title: string;
  content: string;
  educationLevel: string;
  region?: string;
  industry?: string;
  createdAt: string;
  status: string;
}

export default function ReviewerDashboard() {
  const [pendingContent, setPendingContent] = useState<PendingContent[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [contentTypeFilter, setContentTypeFilter] = useState<'all' | 'voice' | 'story'>('all');
  const [selectedContent, setSelectedContent] = useState<PendingContent | null>(null);
  const [reviewAction, setReviewAction] = useState<'approved' | 'rejected' | null>(null);
  const [reviewReason, setReviewReason] = useState('');
  const [isReviewing, setIsReviewing] = useState(false);
  const [showReviewDialog, setShowReviewDialog] = useState(false);
  
  const { toast } = useToast();

  // 获取待审核内容
  const fetchPendingContent = async () => {
    try {
      setLoading(true);
      setError(null);

      const response = await fetch(`/api/reviewer/pending-content?type=${contentTypeFilter}&limit=20`);
      
      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }
      
      const data = await response.json();
      
      if (data.success) {
        setPendingContent(data.content);
      } else {
        throw new Error(data.error || '获取待审核内容失败');
      }
    } catch (err) {
      console.error('获取待审核内容失败:', err);
      setError(err instanceof Error ? err.message : '获取待审核内容失败');
    } finally {
      setLoading(false);
    }
  };

  // 审核内容
  const handleReview = async (contentId: string, action: 'approved' | 'rejected', reason: string = '') => {
    try {
      setIsReviewing(true);

      const content = pendingContent.find(c => c.id === contentId);
      if (!content) {
        throw new Error('内容不存在');
      }

      const response = await fetch(`/api/reviewer/review/${contentId}`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          action,
          reason,
          contentType: content.contentType
        }),
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const data = await response.json();

      if (data.success) {
        // 从列表中移除已审核的内容
        setPendingContent(prev => prev.filter(c => c.id !== contentId));
        
        toast({
          title: '审核成功',
          description: `内容已${action === 'approved' ? '通过' : '拒绝'}审核`,
        });

        // 关闭对话框
        setShowReviewDialog(false);
        setSelectedContent(null);
        setReviewAction(null);
        setReviewReason('');
      } else {
        throw new Error(data.error || '审核操作失败');
      }
    } catch (err) {
      console.error('审核操作失败:', err);
      toast({
        title: '审核失败',
        description: err instanceof Error ? err.message : '审核操作失败',
        variant: 'destructive',
      });
    } finally {
      setIsReviewing(false);
    }
  };

  // 打开审核对话框
  const openReviewDialog = (content: PendingContent, action: 'approved' | 'rejected') => {
    setSelectedContent(content);
    setReviewAction(action);
    setReviewReason('');
    setShowReviewDialog(true);
  };

  // 确认审核
  const confirmReview = () => {
    if (selectedContent && reviewAction) {
      handleReview(selectedContent.id, reviewAction, reviewReason);
    }
  };

  // 格式化时间
  const formatTime = (dateString: string) => {
    return new Date(dateString).toLocaleString('zh-CN');
  };

  // 获取内容类型标签
  const getContentTypeBadge = (content: PendingContent) => {
    if (content.contentType === 'voice') {
      return (
        <Badge variant="secondary" className="mr-2">
          <MessageSquare className="h-3 w-3 mr-1" />
          {content.voiceType === 'advice' ? '建议' : '观察'}
        </Badge>
      );
    } else {
      return (
        <Badge variant="outline" className="mr-2">
          <BookOpen className="h-3 w-3 mr-1" />
          故事
        </Badge>
      );
    }
  };

  // 初始加载
  useEffect(() => {
    fetchPendingContent();
  }, [contentTypeFilter]);

  return (
    <div className="max-w-6xl mx-auto p-6">
      <div className="mb-6">
        <h1 className="text-3xl font-bold mb-2">审核员工作台</h1>
        <p className="text-gray-600">审核用户提交的问卷心声和故事内容</p>
      </div>

      {/* 筛选和刷新 */}
      <div className="flex items-center justify-between mb-6">
        <div className="flex items-center gap-4">
          <div className="flex items-center gap-2">
            <Filter className="h-4 w-4" />
            <Select value={contentTypeFilter} onValueChange={(value: 'all' | 'voice' | 'story') => setContentTypeFilter(value)}>
              <SelectTrigger className="w-40">
                <SelectValue placeholder="选择类型" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">全部内容</SelectItem>
                <SelectItem value="voice">问卷心声</SelectItem>
                <SelectItem value="story">故事内容</SelectItem>
              </SelectContent>
            </Select>
          </div>
          
          <Badge variant="secondary">
            待审核: {pendingContent.length} 条
          </Badge>
        </div>

        <Button onClick={fetchPendingContent} disabled={loading} variant="outline">
          <RefreshCw className={`h-4 w-4 mr-2 ${loading ? 'animate-spin' : ''}`} />
          刷新
        </Button>
      </div>

      {/* 内容列表 */}
      {loading && pendingContent.length === 0 ? (
        <div className="space-y-4">
          {[...Array(3)].map((_, i) => (
            <Card key={i}>
              <CardContent className="p-6">
                <div className="animate-pulse space-y-4">
                  <div className="h-4 bg-gray-200 rounded w-1/4"></div>
                  <div className="h-4 bg-gray-200 rounded"></div>
                  <div className="h-4 bg-gray-200 rounded w-3/4"></div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      ) : error ? (
        <Card>
          <CardContent className="p-6 text-center">
            <p className="text-red-600 mb-4">{error}</p>
            <Button onClick={fetchPendingContent} variant="outline">
              重试
            </Button>
          </CardContent>
        </Card>
      ) : pendingContent.length === 0 ? (
        <Card>
          <CardContent className="p-6 text-center">
            <Clock className="h-12 w-12 mx-auto mb-4 text-gray-400" />
            <h3 className="text-lg font-medium mb-2">暂无待审核内容</h3>
            <p className="text-gray-600">所有内容都已审核完成</p>
          </CardContent>
        </Card>
      ) : (
        <div className="space-y-4">
          {pendingContent.map((content) => (
            <Card key={content.id}>
              <CardHeader>
                <div className="flex items-start justify-between">
                  <div className="flex items-center">
                    {getContentTypeBadge(content)}
                    <Badge variant="outline">
                      {content.educationLevel}
                    </Badge>
                    {content.region && (
                      <Badge variant="outline" className="ml-2">
                        {content.region}
                      </Badge>
                    )}
                    {content.industry && (
                      <Badge variant="outline" className="ml-2">
                        {content.industry}
                      </Badge>
                    )}
                  </div>
                  <span className="text-sm text-gray-500">
                    {formatTime(content.createdAt)}
                  </span>
                </div>
                <CardTitle className="text-lg">{content.title}</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="mb-4">
                  <p className="text-gray-700 leading-relaxed">
                    {content.content.length > 200 
                      ? `${content.content.substring(0, 200)}...` 
                      : content.content
                    }
                  </p>
                  {content.content.length > 200 && (
                    <Button variant="link" className="p-0 h-auto text-sm">
                      <Eye className="h-3 w-3 mr-1" />
                      查看完整内容
                    </Button>
                  )}
                </div>
                
                <div className="flex items-center gap-2">
                  <Button
                    onClick={() => openReviewDialog(content, 'approved')}
                    disabled={isReviewing}
                    className="bg-green-600 hover:bg-green-700"
                  >
                    <CheckCircle className="h-4 w-4 mr-2" />
                    通过
                  </Button>
                  <Button
                    onClick={() => openReviewDialog(content, 'rejected')}
                    disabled={isReviewing}
                    variant="destructive"
                  >
                    <XCircle className="h-4 w-4 mr-2" />
                    拒绝
                  </Button>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      )}

      {/* 审核确认对话框 */}
      <Dialog open={showReviewDialog} onOpenChange={setShowReviewDialog}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>
              确认{reviewAction === 'approved' ? '通过' : '拒绝'}审核
            </DialogTitle>
            <DialogDescription>
              {selectedContent && (
                <div className="mt-2">
                  <p className="font-medium">{selectedContent.title}</p>
                  <p className="text-sm text-gray-600 mt-1">
                    {selectedContent.content.substring(0, 100)}...
                  </p>
                </div>
              )}
            </DialogDescription>
          </DialogHeader>
          
          {reviewAction === 'rejected' && (
            <div className="py-4">
              <label className="text-sm font-medium mb-2 block">
                拒绝理由 (可选)
              </label>
              <Textarea
                value={reviewReason}
                onChange={(e) => setReviewReason(e.target.value)}
                placeholder="请输入拒绝理由..."
                rows={3}
              />
            </div>
          )}

          <DialogFooter>
            <Button
              variant="outline"
              onClick={() => setShowReviewDialog(false)}
              disabled={isReviewing}
            >
              取消
            </Button>
            <Button
              onClick={confirmReview}
              disabled={isReviewing}
              className={reviewAction === 'approved' ? 'bg-green-600 hover:bg-green-700' : ''}
              variant={reviewAction === 'rejected' ? 'destructive' : 'default'}
            >
              {isReviewing ? '处理中...' : `确认${reviewAction === 'approved' ? '通过' : '拒绝'}`}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
}
