# 大学生就业调研系统 - 故障排除指南

## 📋 概述

本文档记录了系统中常见的功能故障及其修复方法，旨在帮助快速定位和解决问题，避免重复的调试过程。

---

## 🚨 常见故障类型及修复方法

### 1. API 404错误 - 路由未注册

#### 🔍 故障现象
- 前端控制台显示：`Failed to load resource: the server responded with a status of 404`
- API调用返回404 Not Found

#### 🎯 根本原因
- 新增的API路由没有在主路由文件中注册
- 路由路径配置错误

#### ✅ 修复步骤
1. **检查路由注册**：
   ```typescript
   // 在 backend/src/api/routes.ts 中确保路由已注册
   import deidentificationRoutes from './admin/deidentification.routes';
   api.route('/admin/deidentification', deidentificationRoutes);
   ```

2. **验证路由文件存在**：
   ```bash
   ls backend/src/api/admin/deidentification.routes.ts
   ```

3. **重新部署后端**：
   ```bash
   cd backend && npm run deploy
   ```

#### 🔧 预防措施
- 新增路由时，立即在主路由文件中注册
- 使用路由测试脚本验证所有端点

---

### 2. API 401错误 - 认证权限问题

#### 🔍 故障现象
- API返回：`{"success":false,"error":"Unauthorized"}`
- 前端用户已登录但无法访问某些功能

#### 🎯 根本原因
- 认证中间件权限配置不完整
- 前端API调用缺少Authorization头
- JWT token过期或无效

#### ✅ 修复步骤

**A. 修复认证中间件权限**：
```typescript
// backend/src/middlewares/adminAuth.ts
// 修复前：只允许admin角色
if (!payload || payload.role !== 'admin') {
  return c.json({ success: false, error: 'Unauthorized' }, 401);
}

// 修复后：允许admin和superadmin角色
if (!payload || (payload.role !== 'admin' && payload.role !== 'superadmin')) {
  return c.json({ success: false, error: 'Unauthorized' }, 401);
}
```

**B. 修复前端认证头**：
```typescript
// 为所有API调用添加认证头
const token = localStorage.getItem('adminToken');
const response = await fetch(`${API_BASE_URL}/api/admin/...`, {
  headers: {
    'Authorization': `Bearer ${token}`,
    'Content-Type': 'application/json'
  }
});
```

**C. 验证token有效性**：
```bash
# 测试登录获取token
curl -X POST https://your-api.workers.dev/api/admin/login \
  -H "Content-Type: application/json" \
  -d '{"username":"superadmin","password":"admin123"}'

# 使用token测试API
curl -X GET https://your-api.workers.dev/api/admin/protected-endpoint \
  -H "Authorization: Bearer YOUR_TOKEN"
```

#### 🔧 预防措施
- 统一认证中间件，支持所有管理员角色
- 创建API调用工具函数，自动添加认证头
- 实现token自动刷新机制

---

### 3. 前端页面空白或组件不显示

#### 🔍 故障现象
- 页面加载后显示空白
- 组件渲染但数据不显示
- 控制台有API错误

#### 🎯 根本原因
- API数据格式与前端期望不匹配
- 状态管理错误
- 组件生命周期问题

#### ✅ 修复步骤

**A. 检查API数据格式**：
```typescript
// 确保API返回格式一致
// 后端返回：
{
  "success": true,
  "data": { ... },
  "message": "操作成功"
}

// 前端处理：
if (response.ok) {
  const data = await response.json();
  if (data.success && data.data) {
    // 处理数据
  }
}
```

**B. 修复状态初始化**：
```typescript
// 确保状态有合理的初始值
const [data, setData] = useState([]);  // 而不是 useState()
const [loading, setLoading] = useState(true);
const [error, setError] = useState(null);
```

**C. 添加错误边界**：
```typescript
// 添加try-catch和错误处理
try {
  const response = await fetch(url);
  if (!response.ok) {
    throw new Error(`HTTP ${response.status}`);
  }
  // 处理成功响应
} catch (error) {
  console.error('API调用失败:', error);
  setError(error.message);
}
```

#### 🔧 预防措施
- 使用TypeScript定义API响应类型
- 实现统一的错误处理机制
- 添加加载状态和错误状态显示

---

### 4. 管理员登录问题

#### 🔍 故障现象
- 一键登录按钮无效
- 用户名密码不匹配
- 登录成功但权限不足

#### 🎯 根本原因
- 前后端用户名不一致
- 密码配置错误
- 角色权限配置问题

#### ✅ 修复步骤

**A. 统一用户名配置**：
```typescript
// 后端用户配置 (backend/src/controllers/admin.ts)
const users = [
  { id: 1, username: 'admin1', password: 'admin123', role: 'admin' },
  { id: 2, username: 'reviewer1', password: 'admin123', role: 'reviewer' },
  { id: 3, username: 'superadmin', password: 'admin123', role: 'superadmin' }
];

// 前端一键登录 (frontend/src/pages/admin/AdminLoginPage.tsx)
await adminLogin('admin1', 'admin123');      // 管理员
await adminLogin('reviewer1', 'admin123');   // 审核员
await adminLogin('superadmin', 'admin123');  // 超级管理员
```

**B. 验证登录流程**：
```bash
# 测试各角色登录
curl -X POST https://your-api.workers.dev/api/admin/login \
  -H "Content-Type: application/json" \
  -d '{"username":"admin1","password":"admin123"}'
```

#### 🔧 预防措施
- 维护统一的用户配置文档
- 实现用户配置的环境变量管理
- 添加登录状态的自动测试

---

## 🛠️ 快速诊断工具

### API连通性测试脚本
```bash
#!/bin/bash
# api-test.sh - 快速测试API连通性

API_BASE="https://college-employment-survey.aibook2099.workers.dev"

echo "🔍 测试API连通性..."

# 1. 测试健康检查
echo "1. 健康检查:"
curl -s "$API_BASE/health" | jq .

# 2. 测试管理员登录
echo "2. 管理员登录:"
TOKEN=$(curl -s -X POST "$API_BASE/api/admin/login" \
  -H "Content-Type: application/json" \
  -d '{"username":"superadmin","password":"admin123"}' | jq -r '.data.token')

if [ "$TOKEN" != "null" ]; then
  echo "✅ 登录成功，Token: ${TOKEN:0:20}..."

  # 3. 测试受保护的API
  echo "3. 测试受保护API:"
  curl -s -X GET "$API_BASE/api/admin/deidentification/config" \
    -H "Authorization: Bearer $TOKEN" | jq .success
else
  echo "❌ 登录失败"
fi
```

### 前端调试检查清单
```javascript
// 在浏览器控制台运行
console.log('🔍 前端状态检查');
console.log('1. Token:', localStorage.getItem('adminToken'));
console.log('2. API Base:', import.meta.env.VITE_API_BASE_URL);
console.log('3. 当前路由:', window.location.pathname);

// 测试API调用
fetch('/api/health')
  .then(r => r.json())
  .then(d => console.log('4. API健康:', d))
  .catch(e => console.error('4. API错误:', e));
```

---

## 📚 系统架构要点

### 正确的API调用模式
```typescript
// 标准API调用模板
const apiCall = async (endpoint: string, options: RequestInit = {}) => {
  const token = localStorage.getItem('adminToken');
  const API_BASE_URL = import.meta.env.VITE_API_BASE_URL || '';

  const response = await fetch(`${API_BASE_URL}${endpoint}`, {
    ...options,
    headers: {
      'Authorization': `Bearer ${token}`,
      'Content-Type': 'application/json',
      ...options.headers
    }
  });

  if (!response.ok) {
    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
  }

  return response.json();
};
```

### 路由注册检查清单
- [ ] 路由文件已创建
- [ ] 路由已在主文件中导入
- [ ] 路由已正确注册到API实例
- [ ] 认证中间件已应用
- [ ] 后端已重新部署

### 认证权限检查清单
- [ ] JWT密钥配置正确
- [ ] 用户角色定义完整
- [ ] 中间件支持所有必要角色
- [ ] 前端正确发送认证头
- [ ] Token有效期合理

---

## 🚀 部署检查清单

### 后端部署
```bash
cd backend
npm run deploy
# 等待部署完成，确认无错误
```

### 前端部署
```bash
cd frontend
npm run build
npx wrangler pages deploy dist --project-name college-employment-survey
# 确认部署成功
```

### 部署后验证
- [ ] API健康检查通过
- [ ] 管理员登录正常
- [ ] 主要功能页面可访问
- [ ] 数据加载正常

---

## 📞 紧急修复流程

1. **立即诊断**：运行API测试脚本
2. **定位问题**：检查控制台错误和网络请求
3. **快速修复**：根据本文档对应章节修复
4. **验证修复**：重新测试相关功能
5. **记录问题**：更新本文档

---

## 🎯 具体功能修复案例

### 案例1：去标识化设置页面API 404错误

**问题描述**：
- 页面加载时出现多个404错误
- `/api/admin/deidentification/test-data` 404
- `/api/admin/deidentification/validate-api-key` 404

**修复过程**：
1. **发现路由未注册**：
   ```bash
   # 测试API端点
   curl -I https://your-api.workers.dev/api/admin/deidentification/config
   # 返回404，确认路由问题
   ```

2. **添加路由注册**：
   ```typescript
   // backend/src/api/routes.ts
   import deidentificationRoutes from './admin/deidentification.routes';
   api.route('/admin/deidentification', deidentificationRoutes);
   ```

3. **重新部署并验证**：
   ```bash
   cd backend && npm run deploy
   # 测试API现在返回401而不是404，说明路由已注册
   ```

**修复时间**：15分钟

---

### 案例2：Superadmin角色无法访问API

**问题描述**：
- Superadmin登录成功但API调用返回401
- Token有效但权限被拒绝

**修复过程**：
1. **检查认证中间件**：
   ```typescript
   // 发现问题：只允许admin角色
   if (!payload || payload.role !== 'admin') {
     return c.json({ success: false, error: 'Unauthorized' }, 401);
   }
   ```

2. **修复权限检查**：
   ```typescript
   // 允许admin和superadmin角色
   if (!payload || (payload.role !== 'admin' && payload.role !== 'superadmin')) {
     return c.json({ success: false, error: 'Unauthorized' }, 401);
   }
   ```

3. **验证修复**：
   ```bash
   # 测试superadmin访问
   TOKEN=$(curl -s -X POST .../api/admin/login -d '{"username":"superadmin",...}' | jq -r '.data.token')
   curl -H "Authorization: Bearer $TOKEN" .../api/admin/deidentification/config
   # 现在返回成功响应
   ```

**修复时间**：10分钟

---

### 案例3：前端API调用缺少认证头

**问题描述**：
- 后端API正常但前端调用失败
- 所有API请求返回401

**修复过程**：
1. **检查前端API调用**：
   ```typescript
   // 发现问题：缺少Authorization头
   const response = await fetch(`${API_BASE_URL}/api/admin/...`, {
     method: 'POST',
     headers: { 'Content-Type': 'application/json' },
     body: JSON.stringify(data)
   });
   ```

2. **添加认证头**：
   ```typescript
   // 修复：添加认证头
   const token = localStorage.getItem('adminToken');
   const response = await fetch(`${API_BASE_URL}/api/admin/...`, {
     method: 'POST',
     headers: {
       'Authorization': `Bearer ${token}`,
       'Content-Type': 'application/json'
     },
     body: JSON.stringify(data)
   });
   ```

3. **批量修复所有API调用**：
   - loadDefaultTestData()
   - loadProviderStats()
   - handleSaveConfig()
   - handleTestProvider()
   - 等等...

**修复时间**：20分钟

---

## 🔧 自动化修复工具

### 1. API端点检查脚本
```bash
#!/bin/bash
# check-endpoints.sh - 检查所有API端点状态

ENDPOINTS=(
  "/health"
  "/api/admin/login"
  "/api/admin/deidentification/config"
  "/api/admin/deidentification/test-data"
  "/api/admin/deidentification/validate-api-key"
  "/api/admin/deidentification/provider-stats"
)

API_BASE="https://college-employment-survey.aibook2099.workers.dev"

echo "🔍 检查API端点状态..."

for endpoint in "${ENDPOINTS[@]}"; do
  status=$(curl -s -o /dev/null -w "%{http_code}" "$API_BASE$endpoint")
  if [ "$status" = "404" ]; then
    echo "❌ $endpoint - 404 (路由未注册)"
  elif [ "$status" = "401" ]; then
    echo "🔐 $endpoint - 401 (需要认证)"
  elif [ "$status" = "200" ]; then
    echo "✅ $endpoint - 200 (正常)"
  else
    echo "⚠️  $endpoint - $status"
  fi
done
```

### 2. 前端认证检查脚本
```javascript
// auth-check.js - 在浏览器控制台运行
(function() {
  console.log('🔍 前端认证状态检查');

  const token = localStorage.getItem('adminToken');
  if (!token) {
    console.error('❌ 未找到认证token');
    return;
  }

  console.log('✅ Token存在:', token.substring(0, 20) + '...');

  // 解析token查看角色
  try {
    const payload = JSON.parse(atob(token.split('.')[1]));
    console.log('👤 用户角色:', payload.role);
    console.log('⏰ Token过期时间:', new Date(payload.exp * 1000));
  } catch (e) {
    console.error('❌ Token格式错误');
  }

  // 测试API调用
  fetch('/api/admin/deidentification/config', {
    headers: { 'Authorization': `Bearer ${token}` }
  })
  .then(r => {
    console.log('🌐 API调用状态:', r.status);
    return r.json();
  })
  .then(d => console.log('📊 API响应:', d))
  .catch(e => console.error('❌ API调用失败:', e));
})();
```

### 3. 快速修复模板
```typescript
// quick-fix-template.ts - 标准修复模板

// 1. 路由注册模板
// backend/src/api/routes.ts
import newRoutes from './path/to/new.routes';
api.route('/api/path', newRoutes);

// 2. 认证中间件模板
// backend/src/middlewares/auth.ts
if (!payload || !['admin', 'superadmin', 'reviewer'].includes(payload.role)) {
  return c.json({ success: false, error: 'Unauthorized' }, 401);
}

// 3. 前端API调用模板
// frontend/src/utils/api.ts
const apiCall = async (endpoint: string, options: RequestInit = {}) => {
  const token = localStorage.getItem('adminToken');
  const API_BASE_URL = import.meta.env.VITE_API_BASE_URL || '';

  return fetch(`${API_BASE_URL}${endpoint}`, {
    ...options,
    headers: {
      'Authorization': `Bearer ${token}`,
      'Content-Type': 'application/json',
      ...options.headers
    }
  });
};

// 4. 错误处理模板
// frontend/src/components/ErrorBoundary.tsx
try {
  const response = await apiCall('/api/endpoint');
  if (!response.ok) {
    throw new Error(`HTTP ${response.status}`);
  }
  const data = await response.json();
  if (!data.success) {
    throw new Error(data.error || '操作失败');
  }
  return data.data;
} catch (error) {
  console.error('API调用失败:', error);
  toast.error(error.message);
  throw error;
}
```

---

## 📋 预防性维护检查清单

### 每次代码变更后
- [ ] 运行API端点检查脚本
- [ ] 验证所有管理员角色登录
- [ ] 测试主要功能页面
- [ ] 检查浏览器控制台错误

### 每周定期检查
- [ ] 验证所有API端点响应正常
- [ ] 检查认证token有效期设置
- [ ] 更新依赖包版本
- [ ] 备份重要配置文件

### 部署前检查
- [ ] 本地测试所有功能
- [ ] 验证环境变量配置
- [ ] 检查路由注册完整性
- [ ] 确认认证权限正确

---

## 🆘 紧急联系信息

### 关键文件位置
- 后端路由注册：`backend/src/api/routes.ts`
- 认证中间件：`backend/src/middlewares/adminAuth.ts`
- 管理员配置：`backend/src/controllers/admin.ts`
- 前端API配置：`frontend/src/services/`

### 常用命令
```bash
# 后端部署
cd backend && npm run deploy

# 前端部署
cd frontend && npm run build && npx wrangler pages deploy dist --project-name college-employment-survey

# 查看部署日志
npx wrangler tail

# 本地开发
npm run dev
```

### 快速回滚
如果修复失败，可以快速回滚到上一个工作版本：
```bash
# 查看部署历史
npx wrangler deployments list

# 回滚到指定版本
npx wrangler rollback [deployment-id]
```

---

*最后更新：2025-05-27*
*版本：v1.1*
*维护者：系统管理员*
