import { useState } from 'react';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle
} from '@/components/ui/card';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue
} from '@/components/ui/select';
import { Button } from '@/components/ui/button';
import { Checkbox } from '@/components/ui/checkbox';
import { Label } from '@/components/ui/label';
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { useToast } from '@/components/ui/use-toast';
import {
  Download,
  FileSpreadsheet,
  FileText,
  FileImage,
  FileType2,
  Calendar,
  Filter,
  Loader2
} from 'lucide-react';
import { DatePicker } from '@/components/ui/date-picker';
import { exportData } from '@/lib/api';

// 数据导出组件的属性接口
interface DataExportProps {
  data: any;
}

// 数据导出组件
export default function DataExport({ data }: DataExportProps) {
  const { toast } = useToast();

  // 状态管理
  const [exportType, setExportType] = useState('data');
  const [fileFormat, setFileFormat] = useState('csv');
  const [isExporting, setIsExporting] = useState(false);
  const [dateRange, setDateRange] = useState<{
    startDate: Date | null;
    endDate: Date | null;
  }>({
    startDate: null,
    endDate: null,
  });
  const [selectedFields, setSelectedFields] = useState<string[]>([
    'educationLevel',
    'employmentStatus',
    'region',
    'industry',
    'salary',
  ]);
  const [filters, setFilters] = useState<Record<string, string>>({});

  // 可用的导出类型
  const exportTypes = [
    { id: 'data', label: '原始数据', icon: <FileText className="h-4 w-4" /> },
    { id: 'stats', label: '统计数据', icon: <FileSpreadsheet className="h-4 w-4" /> },
    { id: 'charts', label: '图表', icon: <FileImage className="h-4 w-4" /> },
    { id: 'report', label: '完整报告', icon: <FileType2 className="h-4 w-4" /> },
  ];

  // 可用的文件格式
  const fileFormats = {
    data: [
      { id: 'csv', label: 'CSV格式', extension: '.csv' },
      { id: 'excel', label: 'Excel格式', extension: '.xlsx' },
      { id: 'json', label: 'JSON格式', extension: '.json' },
    ],
    stats: [
      { id: 'excel', label: 'Excel格式', extension: '.xlsx' },
      { id: 'pdf', label: 'PDF格式', extension: '.pdf' },
    ],
    charts: [
      { id: 'png', label: 'PNG图片', extension: '.png' },
      { id: 'svg', label: 'SVG矢量图', extension: '.svg' },
    ],
    report: [
      { id: 'pdf', label: 'PDF格式', extension: '.pdf' },
      { id: 'docx', label: 'Word格式', extension: '.docx' },
    ],
  };

  // 可用的数据字段
  const dataFields = [
    { id: 'id', label: 'ID' },
    { id: 'timestamp', label: '提交时间' },
    { id: 'educationLevel', label: '学历层次' },
    { id: 'major', label: '专业' },
    { id: 'graduationYear', label: '毕业年份' },
    { id: 'employmentStatus', label: '就业状态' },
    { id: 'region', label: '地区' },
    { id: 'industry', label: '行业' },
    { id: 'position', label: '职位' },
    { id: 'salary', label: '薪资' },
    { id: 'jobSatisfaction', label: '工作满意度' },
    { id: 'unemploymentDuration', label: '失业时长' },
    { id: 'careerChangeIntention', label: '转行意向' },
    { id: 'challenges', label: '就业挑战' },
    { id: 'suggestions', label: '建议' },
  ];

  // 可用的过滤条件
  const filterOptions = {
    educationLevel: [
      { id: 'all', label: '全部学历' },
      { id: 'high-school', label: '高中及以下' },
      { id: 'college', label: '专科' },
      { id: 'bachelor', label: '本科' },
      { id: 'master', label: '硕士' },
      { id: 'phd', label: '博士' },
    ],
    employmentStatus: [
      { id: 'all', label: '全部状态' },
      { id: 'employed', label: '已就业' },
      { id: 'unemployed', label: '待业中' },
      { id: 'studying', label: '继续深造' },
    ],
    region: [
      { id: 'all', label: '全部地区' },
      { id: 'north', label: '华北地区' },
      { id: 'east', label: '华东地区' },
      { id: 'south', label: '华南地区' },
      { id: 'central', label: '华中地区' },
      { id: 'southwest', label: '西南地区' },
      { id: 'northwest', label: '西北地区' },
      { id: 'northeast', label: '东北地区' },
    ],
  };

  // 处理字段选择变化
  const handleFieldChange = (field: string) => {
    if (selectedFields.includes(field)) {
      setSelectedFields(selectedFields.filter(f => f !== field));
    } else {
      setSelectedFields([...selectedFields, field]);
    }
  };

  // 处理过滤条件变化
  const handleFilterChange = (key: string, value: string) => {
    if (value === 'all') {
      const newFilters = { ...filters };
      delete newFilters[key];
      setFilters(newFilters);
    } else {
      setFilters({
        ...filters,
        [key]: value,
      });
    }
  };

  // 处理导出
  const handleExport = async () => {
    try {
      setIsExporting(true);

      // 准备导出参数
      const exportParams = {
        type: exportType,
        format: fileFormat,
        fields: selectedFields,
        filters: filters,
        dateRange: {
          startDate: dateRange.startDate ? dateRange.startDate.toISOString() : undefined,
          endDate: dateRange.endDate ? dateRange.endDate.toISOString() : undefined,
        },
      };

      // 调用导出API
      const result = await exportData(exportParams);

      if (result.success) {
        // 打开下载链接
        window.open(result.downloadUrl, '_blank');

        toast({
          title: '导出成功',
          description: `数据已导出为${fileFormat.toUpperCase()}格式，请查看下载内容`,
        });
      } else {
        toast({
          variant: 'destructive',
          title: '导出失败',
          description: result.error || '导出数据时发生错误',
        });
      }
    } catch (error) {
      console.error('导出错误:', error);
      toast({
        variant: 'destructive',
        title: '导出失败',
        description: '服务器错误，请稍后再试',
      });
    } finally {
      setIsExporting(false);
    }
  };

  // 获取当前文件格式的选项
  const currentFormatOptions = fileFormats[exportType as keyof typeof fileFormats] || fileFormats.data;

  // 获取文件扩展名
  const getFileExtension = () => {
    const format = currentFormatOptions.find(f => f.id === fileFormat);
    return format ? format.extension : '.csv';
  };

  // 获取导出文件名
  const getExportFileName = () => {
    const date = new Date().toISOString().split('T')[0];
    const type = exportType === 'data' ? '原始数据' :
                exportType === 'stats' ? '统计数据' :
                exportType === 'charts' ? '图表' : '报告';

    return `就业调查-${type}-${date}${getFileExtension()}`;
  };

  return (
    <Card className="w-full">
      <CardHeader>
        <CardTitle>数据导出</CardTitle>
        <CardDescription>
          导出数据、统计结果或图表，支持多种格式
        </CardDescription>
      </CardHeader>
      <CardContent>
        <Tabs defaultValue="content" className="mb-4">
          <TabsList className="mb-4">
            <TabsTrigger value="content">导出内容</TabsTrigger>
            <TabsTrigger value="fields">数据字段</TabsTrigger>
            <TabsTrigger value="filters">筛选条件</TabsTrigger>
          </TabsList>

          <TabsContent value="content" className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label className="text-sm font-medium mb-2 block">导出类型</label>
                <div className="grid grid-cols-2 gap-2">
                  {exportTypes.map((type) => (
                    <Button
                      key={type.id}
                      variant={exportType === type.id ? "default" : "outline"}
                      className="justify-start"
                      onClick={() => {
                        setExportType(type.id);
                        // 重置文件格式为该类型的第一个选项
                        const formats = fileFormats[type.id as keyof typeof fileFormats] || fileFormats.data;
                        setFileFormat(formats[0].id);
                      }}
                    >
                      {type.icon}
                      <span className="ml-2">{type.label}</span>
                    </Button>
                  ))}
                </div>
              </div>

              <div>
                <label className="text-sm font-medium mb-2 block">文件格式</label>
                <div className="grid grid-cols-2 gap-2">
                  {currentFormatOptions.map((format) => (
                    <Button
                      key={format.id}
                      variant={fileFormat === format.id ? "default" : "outline"}
                      className="justify-start"
                      onClick={() => setFileFormat(format.id)}
                    >
                      <span>{format.label}</span>
                      <span className="ml-2 text-xs opacity-70">{format.extension}</span>
                    </Button>
                  ))}
                </div>
              </div>
            </div>

            <div>
              <label className="text-sm font-medium mb-2 block">日期范围</label>
              <div className="flex flex-col md:flex-row gap-4">
                <div className="flex items-center gap-2">
                  <Calendar className="h-4 w-4 text-gray-500" />
                  <span className="text-sm">开始日期:</span>
                  <DatePicker
                    selected={dateRange.startDate}
                    onSelect={(date) => setDateRange({ ...dateRange, startDate: date })}
                    disabled={isExporting}
                  />
                </div>
                <div className="flex items-center gap-2">
                  <Calendar className="h-4 w-4 text-gray-500" />
                  <span className="text-sm">结束日期:</span>
                  <DatePicker
                    selected={dateRange.endDate}
                    onSelect={(date) => setDateRange({ ...dateRange, endDate: date })}
                    disabled={isExporting}
                  />
                </div>
              </div>
            </div>
          </TabsContent>

          <TabsContent value="fields">
            <div className="mb-4">
              <div className="flex justify-between items-center mb-2">
                <label className="text-sm font-medium">选择导出字段</label>
                <div className="flex gap-2">
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => setSelectedFields(dataFields.map(f => f.id))}
                  >
                    全选
                  </Button>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => setSelectedFields([])}
                  >
                    清空
                  </Button>
                </div>
              </div>
              <div className="grid grid-cols-2 md:grid-cols-3 gap-2">
                {dataFields.map((field) => (
                  <div key={field.id} className="flex items-center space-x-2">
                    <Checkbox
                      id={`field-${field.id}`}
                      checked={selectedFields.includes(field.id)}
                      onCheckedChange={() => handleFieldChange(field.id)}
                      disabled={isExporting}
                    />
                    <Label htmlFor={`field-${field.id}`}>{field.label}</Label>
                  </div>
                ))}
              </div>
            </div>
          </TabsContent>

          <TabsContent value="filters">
            <div className="space-y-4">
              <div>
                <label className="text-sm font-medium mb-2 block">学历筛选</label>
                <Select
                  value={filters.educationLevel || 'all'}
                  onValueChange={(value) => handleFilterChange('educationLevel', value)}
                  disabled={isExporting}
                >
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    {filterOptions.educationLevel.map((option) => (
                      <SelectItem key={option.id} value={option.id}>
                        {option.label}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              <div>
                <label className="text-sm font-medium mb-2 block">就业状态筛选</label>
                <Select
                  value={filters.employmentStatus || 'all'}
                  onValueChange={(value) => handleFilterChange('employmentStatus', value)}
                  disabled={isExporting}
                >
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    {filterOptions.employmentStatus.map((option) => (
                      <SelectItem key={option.id} value={option.id}>
                        {option.label}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              <div>
                <label className="text-sm font-medium mb-2 block">地区筛选</label>
                <Select
                  value={filters.region || 'all'}
                  onValueChange={(value) => handleFilterChange('region', value)}
                  disabled={isExporting}
                >
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    {filterOptions.region.map((option) => (
                      <SelectItem key={option.id} value={option.id}>
                        {option.label}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
            </div>
          </TabsContent>
        </Tabs>

        <div className="flex justify-between items-center mt-6">
          <div>
            <p className="text-sm text-gray-500">
              导出文件名: {getExportFileName()}
            </p>
            <p className="text-sm text-gray-500">
              已选择 {selectedFields.length} 个字段
              {Object.keys(filters).length > 0 && `, ${Object.keys(filters).length} 个筛选条件`}
            </p>
          </div>

          <Button
            onClick={handleExport}
            disabled={isExporting || selectedFields.length === 0}
            className="flex items-center gap-2"
          >
            {isExporting ? (
              <Loader2 className="h-4 w-4 animate-spin" />
            ) : (
              <Download className="h-4 w-4" />
            )}
            {isExporting ? '导出中...' : '导出数据'}
          </Button>
        </div>
      </CardContent>
    </Card>
  );
}
