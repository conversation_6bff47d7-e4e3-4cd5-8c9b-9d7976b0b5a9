/**
 * 自动审核服务
 *
 * 为新提交的内容自动触发审核，并根据审核结果自动执行相应操作
 */

import { PrismaClient } from '@prisma/client';
import { AIService, ContentModerationResult } from '../ai/ai-service';
import { Env } from '../../types';
import { ContentType, ReviewStatus } from '../../constants';

// 自动审核配置接口
export interface AutoModerationConfig {
  enabled: boolean;                      // 是否启用自动审核
  confidenceThreshold: number;           // 置信度阈值，高于此值的审核结果将被自动处理
  autoApproveThreshold: number;          // 自动通过阈值，高于此值的安全内容将被自动通过
  autoRejectThreshold: number;           // 自动拒绝阈值，高于此值的不安全内容将被自动拒绝
  severityThresholds: {                  // 严重程度阈值配置
    low: number;                         // 低严重度阈值
    medium: number;                      // 中严重度阈值
    high: number;                        // 高严重度阈值
  };
  notifyAdmin: boolean;                  // 是否通知管理员
  notifyUser: boolean;                   // 是否通知用户
  logResults: boolean;                   // 是否记录审核结果
}

// 自动审核结果接口
export interface AutoModerationResult {
  success: boolean;                      // 是否成功
  contentId?: string | number;           // 内容ID
  contentType: ContentType;              // 内容类型
  moderationResult: ContentModerationResult; // 审核结果
  action: 'approve' | 'reject' | 'review'; // 执行的操作
  message: string;                       // 消息
  error?: string;                        // 错误信息
}

/**
 * 自动审核服务类
 */
export class AutoModerationService {
  private static instance: AutoModerationService;
  private initialized: boolean = false;
  private config: AutoModerationConfig;
  private prisma: PrismaClient;

  /**
   * 私有构造函数，防止直接实例化
   */
  private constructor() {
    this.config = {
      enabled: true,
      confidenceThreshold: 0.7,
      autoApproveThreshold: 0.9,
      autoRejectThreshold: 0.9,
      severityThresholds: {
        low: 0.7,
        medium: 0.8,
        high: 0.9
      },
      notifyAdmin: true,
      notifyUser: true,
      logResults: true
    };
    this.prisma = new PrismaClient();
  }

  /**
   * 获取自动审核服务实例
   * @returns 自动审核服务实例
   */
  public static getInstance(): AutoModerationService {
    if (!AutoModerationService.instance) {
      AutoModerationService.instance = new AutoModerationService();
    }
    return AutoModerationService.instance;
  }

  /**
   * 初始化自动审核服务
   * @param config 自动审核配置
   */
  public async initialize(config?: Partial<AutoModerationConfig>): Promise<void> {
    if (config) {
      this.config = { ...this.config, ...config };
    }
    this.initialized = true;
  }

  /**
   * 检查服务是否已初始化
   * @returns 是否已初始化
   */
  public isInitialized(): boolean {
    return this.initialized;
  }

  /**
   * 获取当前配置
   * @returns 当前配置
   */
  public getConfig(): AutoModerationConfig {
    return { ...this.config };
  }

  /**
   * 更新配置
   * @param config 新配置
   */
  public updateConfig(config: Partial<AutoModerationConfig>): void {
    this.config = { ...this.config, ...config };
  }

  /**
   * 自动审核内容
   * @param content 内容
   * @param contentType 内容类型
   * @param env 环境变量
   * @returns 审核结果
   */
  public async moderateContent(
    content: any,
    contentType: ContentType,
    env: Env
  ): Promise<AutoModerationResult> {
    try {
      // 确保服务已初始化
      if (!this.initialized) {
        await this.initialize();
      }

      // 如果自动审核未启用，直接返回需要人工审核的结果
      if (!this.config.enabled) {
        return {
          success: true,
          contentType,
          moderationResult: {
            isSafe: false,
            issues: ['自动审核未启用'],
            confidence: 0,
            explanation: '自动审核功能已禁用，请进行人工审核。',
            suggestedAction: 'review'
          },
          action: 'review',
          message: '自动审核未启用，请进行人工审核'
        };
      }

      // 创建AI服务实例
      const aiService = new AIService(env);

      // 根据内容类型选择适当的审核方法
      let moderationResult: ContentModerationResult;
      let contentToModerate: string;
      let contextData: any = {};

      // 提取内容和上下文数据
      switch (contentType) {
        case ContentType.STORY:
          contentToModerate = content.content || '';
          contextData = {
            title: content.title || '',
            tags: content.tags || [],
            category: content.category || '',
            educationLevel: content.educationLevel || '',
            industry: content.industry || ''
          };
          moderationResult = await aiService.moderateStory(contentToModerate, contextData);
          break;
        case ContentType.QUESTIONNAIRE:
          // 将问卷内容转换为字符串
          contentToModerate = this.convertQuestionnaireToString(content);
          moderationResult = await aiService.moderateQuestionnaire(contentToModerate, content);
          break;
        case ContentType.COMMENT:
          contentToModerate = content.content || '';
          contextData = {
            parentId: content.parentId || '',
            parentType: content.parentType || ''
          };
          moderationResult = await aiService.moderateComment(contentToModerate, contextData);
          break;
        case ContentType.PROFILE:
          contentToModerate = this.convertProfileToString(content);
          moderationResult = await aiService.moderateProfile(contentToModerate, content);
          break;
        case ContentType.FEEDBACK:
          contentToModerate = content.content || '';
          contextData = {
            category: content.category || '',
            rating: content.rating || ''
          };
          moderationResult = await aiService.moderateFeedback(contentToModerate, contextData);
          break;
        default:
          // 默认使用通用审核
          contentToModerate = typeof content === 'string' ? content : JSON.stringify(content);
          moderationResult = await aiService.moderateContent(contentToModerate);
      }

      // 根据审核结果决定操作
      const action = this.determineAction(moderationResult);

      // 执行操作
      const result = await this.executeAction(content, contentType, action, moderationResult, env);

      // 记录审核结果
      if (this.config.logResults) {
        await this.logModerationResult(content, contentType, moderationResult, action, env);
      }

      // 返回结果
      return {
        success: true,
        contentId: result.contentId,
        contentType,
        moderationResult,
        action,
        message: result.message
      };
    } catch (error) {
      console.error('自动审核失败:', error);
      return {
        success: false,
        contentType,
        moderationResult: {
          isSafe: false,
          issues: ['自动审核失败'],
          confidence: 0,
          explanation: '自动审核过程中发生错误，请进行人工审核。',
          suggestedAction: 'review'
        },
        action: 'review',
        message: '自动审核失败，请进行人工审核',
        error: error instanceof Error ? error.message : String(error)
      };
    }
  }

  /**
   * 根据审核结果决定操作
   * @param result 审核结果
   * @returns 操作
   */
  private determineAction(result: ContentModerationResult): 'approve' | 'reject' | 'review' {
    // 如果置信度低于阈值，需要人工审核
    if (result.confidence < this.config.confidenceThreshold) {
      return 'review';
    }

    // 根据AI建议的操作
    if (result.suggestedAction === 'approve') {
      // 如果是安全内容，且置信度高于自动通过阈值，则自动通过
      if (result.isSafe && result.confidence >= this.config.autoApproveThreshold) {
        return 'approve';
      }
    } else if (result.suggestedAction === 'reject') {
      // 如果是不安全内容，且置信度高于自动拒绝阈值，则自动拒绝
      if (!result.isSafe && result.confidence >= this.config.autoRejectThreshold) {
        // 根据严重程度进一步判断
        if (result.severity === 'high' && result.confidence >= this.config.severityThresholds.high) {
          return 'reject';
        } else if (result.severity === 'medium' && result.confidence >= this.config.severityThresholds.medium) {
          return 'reject';
        } else if (result.severity === 'low' && result.confidence >= this.config.severityThresholds.low) {
          // 低严重度可以考虑人工审核
          return 'review';
        }
      }
    }

    // 默认需要人工审核
    return 'review';
  }

  /**
   * 执行操作
   * @param content 内容
   * @param contentType 内容类型
   * @param action 操作
   * @param moderationResult 审核结果
   * @param env 环境变量
   * @returns 操作结果
   */
  private async executeAction(
    content: any,
    contentType: ContentType,
    action: 'approve' | 'reject' | 'review',
    moderationResult: ContentModerationResult,
    env: Env
  ): Promise<{ contentId?: string | number; message: string }> {
    switch (action) {
      case 'approve':
        // 自动通过内容
        return await this.approveContent(content, contentType);
      case 'reject':
        // 自动拒绝内容
        return await this.rejectContent(content, contentType, moderationResult);
      case 'review':
      default:
        // 标记为需要人工审核
        return await this.markForReview(content, contentType, moderationResult);
    }
  }

  /**
   * 自动通过内容
   * @param content 内容
   * @param contentType 内容类型
   * @returns 操作结果
   */
  private async approveContent(
    content: any,
    contentType: ContentType
  ): Promise<{ contentId?: string | number; message: string }> {
    try {
      let contentId: string | number | undefined;

      // 根据内容类型处理
      switch (contentType) {
        case ContentType.STORY:
          // 创建故事
          const story = await this.prisma.story.create({
            data: {
              userId: content.userId,
              isAnonymous: content.isAnonymous || false,
              title: content.title || '',
              content: content.content || '',
              ipAddress: content.ipAddress || '',
              status: 'approved'
            }
          });
          contentId = story.id;
          break;
        case ContentType.QUESTIONNAIRE:
          // 创建问卷回复
          const response = await this.prisma.questionnaireResponse.create({
            data: {
              userId: content.userId,
              isAnonymous: content.isAnonymous || false,
              // 其他问卷字段...
              status: 'approved'
            }
          });
          contentId = response.id;
          break;
        case ContentType.COMMENT:
          // 创建评论
          const comment = await this.prisma.comment.create({
            data: {
              userId: content.userId,
              content: content.content || '',
              parentId: content.parentId,
              parentType: content.parentType,
              ipAddress: content.ipAddress || '',
              status: 'approved'
            }
          });
          contentId = comment.id;
          break;
        // 其他内容类型...
        default:
          throw new Error(`不支持的内容类型: ${contentType}`);
      }

      // 如果配置了通知用户，发送通知
      if (this.config.notifyUser && content.userId) {
        await this.notifyUser(content.userId, 'approve', contentType, contentId);
      }

      return {
        contentId,
        message: '内容已自动通过'
      };
    } catch (error) {
      console.error('自动通过内容失败:', error);
      throw error;
    }
  }

  /**
   * 自动拒绝内容
   * @param content 内容
   * @param contentType 内容类型
   * @param moderationResult 审核结果
   * @returns 操作结果
   */
  private async rejectContent(
    content: any,
    contentType: ContentType,
    moderationResult: ContentModerationResult
  ): Promise<{ contentId?: string | number; message: string }> {
    try {
      // 创建被拒绝的内容记录
      const rejectedContent = await this.prisma.rejectedContent.create({
        data: {
          userId: content.userId,
          contentType,
          originalContent: JSON.stringify(content),
          reason: moderationResult.explanation,
          issues: moderationResult.issues.join(', '),
          ipAddress: content.ipAddress || '',
          userAgent: content.userAgent || ''
        }
      });

      // 如果配置了通知用户，发送通知
      if (this.config.notifyUser && content.userId) {
        await this.notifyUser(content.userId, 'reject', contentType, rejectedContent.id, moderationResult.explanation);
      }

      // 如果配置了通知管理员，发送通知
      if (this.config.notifyAdmin) {
        await this.notifyAdmin('reject', contentType, rejectedContent.id, moderationResult);
      }

      return {
        contentId: rejectedContent.id,
        message: '内容已自动拒绝'
      };
    } catch (error) {
      console.error('自动拒绝内容失败:', error);
      throw error;
    }
  }

  /**
   * 标记为需要人工审核
   * @param content 内容
   * @param contentType 内容类型
   * @param moderationResult 审核结果
   * @returns 操作结果
   */
  private async markForReview(
    content: any,
    contentType: ContentType,
    moderationResult: ContentModerationResult
  ): Promise<{ contentId?: string | number; message: string }> {
    try {
      // 生成序列号
      const sequenceNumber = await this.generateSequenceNumber(contentType);

      // 计算优先级
      const priority = this.calculatePriority(moderationResult);

      // 创建待审核内容
      const pendingContent = await this.prisma.pendingContent.create({
        data: {
          sequenceNumber,
          type: contentType,
          originalContent: JSON.stringify(content),
          sanitizedContent: JSON.stringify(content), // 可以根据需要进行脱敏处理
          status: ReviewStatus.PENDING,
          originIp: content.ipAddress || '',
          userAgent: content.userAgent || '',
          flags: moderationResult.issues,
          priority,
          aiSuggestion: moderationResult.suggestedAction,
          aiConfidence: moderationResult.confidence,
          aiExplanation: moderationResult.explanation
        }
      });

      // 如果配置了通知用户，发送通知
      if (this.config.notifyUser && content.userId) {
        await this.notifyUser(content.userId, 'review', contentType, pendingContent.id);
      }

      // 如果配置了通知管理员，发送通知
      if (this.config.notifyAdmin) {
        await this.notifyAdmin('review', contentType, pendingContent.id, moderationResult);
      }

      return {
        contentId: pendingContent.id,
        message: '内容已标记为需要人工审核'
      };
    } catch (error) {
      console.error('标记为需要人工审核失败:', error);
      throw error;
    }
  }

  /**
   * 记录审核结果
   * @param content 内容
   * @param contentType 内容类型
   * @param moderationResult 审核结果
   * @param action 执行的操作
   * @param env 环境变量
   */
  private async logModerationResult(
    content: any,
    contentType: ContentType,
    moderationResult: ContentModerationResult,
    action: 'approve' | 'reject' | 'review',
    env: Env
  ): Promise<void> {
    try {
      // 创建审核历史记录
      await this.prisma.moderationHistory.create({
        data: {
          contentType: contentType,
          contentId: content.id || 'new',
          reviewerId: 'auto-moderation',
          isSafe: moderationResult.isSafe,
          issues: moderationResult.issues.join(','),
          confidence: moderationResult.confidence,
          explanation: moderationResult.explanation,
          suggestedAction: moderationResult.suggestedAction,
          severity: moderationResult.severity,
          dataQuality: moderationResult.dataQuality,
          constructiveValue: moderationResult.constructiveValue,
          storyValue: moderationResult.storyValue,
          ipAddress: content.ipAddress || '',
          userAgent: content.userAgent || ''
        }
      });
    } catch (error) {
      console.error('记录审核结果失败:', error);
      // 记录失败不影响主流程
    }
  }

  /**
   * 通知用户
   * @param userId 用户ID
   * @param action 操作
   * @param contentType 内容类型
   * @param contentId 内容ID
   * @param reason 原因
   */
  private async notifyUser(
    userId: string | number,
    action: 'approve' | 'reject' | 'review',
    contentType: ContentType,
    contentId?: string | number,
    reason?: string
  ): Promise<void> {
    try {
      // 创建通知
      await this.prisma.notification.create({
        data: {
          userId: String(userId),
          type: `content-${action}`,
          title: this.getNotificationTitle(action, contentType),
          content: this.getNotificationContent(action, contentType, reason),
          data: JSON.stringify({
            contentType,
            contentId,
            action
          }),
          isRead: false
        }
      });
    } catch (error) {
      console.error('通知用户失败:', error);
      // 通知失败不影响主流程
    }
  }

  /**
   * 通知管理员
   * @param action 操作
   * @param contentType 内容类型
   * @param contentId 内容ID
   * @param moderationResult 审核结果
   */
  private async notifyAdmin(
    action: 'approve' | 'reject' | 'review',
    contentType: ContentType,
    contentId?: string | number,
    moderationResult?: ContentModerationResult
  ): Promise<void> {
    try {
      // 获取管理员列表
      const admins = await this.prisma.user.findMany({
        where: {
          role: {
            in: ['admin', 'superadmin']
          }
        },
        select: {
          id: true
        }
      });

      // 为每个管理员创建通知
      for (const admin of admins) {
        await this.prisma.notification.create({
          data: {
            userId: String(admin.id),
            type: `admin-content-${action}`,
            title: `内容已自动${this.getActionName(action)}`,
            content: this.getAdminNotificationContent(action, contentType, moderationResult),
            data: JSON.stringify({
              contentType,
              contentId,
              action,
              moderationResult
            }),
            isRead: false
          }
        });
      }
    } catch (error) {
      console.error('通知管理员失败:', error);
      // 通知失败不影响主流程
    }
  }

  /**
   * 获取通知标题
   * @param action 操作
   * @param contentType 内容类型
   * @returns 通知标题
   */
  private getNotificationTitle(action: 'approve' | 'reject' | 'review', contentType: ContentType): string {
    const contentTypeName = this.getContentTypeName(contentType);
    switch (action) {
      case 'approve':
        return `您的${contentTypeName}已通过审核`;
      case 'reject':
        return `您的${contentTypeName}未通过审核`;
      case 'review':
        return `您的${contentTypeName}正在审核中`;
      default:
        return `您的${contentTypeName}状态已更新`;
    }
  }

  /**
   * 获取通知内容
   * @param action 操作
   * @param contentType 内容类型
   * @param reason 原因
   * @returns 通知内容
   */
  private getNotificationContent(action: 'approve' | 'reject' | 'review', contentType: ContentType, reason?: string): string {
    const contentTypeName = this.getContentTypeName(contentType);
    switch (action) {
      case 'approve':
        return `您提交的${contentTypeName}已通过审核，现已发布。`;
      case 'reject':
        return `您提交的${contentTypeName}未通过审核。${reason ? `原因：${reason}` : ''}`;
      case 'review':
        return `您提交的${contentTypeName}正在审核中，审核完成后会通知您。`;
      default:
        return `您的${contentTypeName}状态已更新。`;
    }
  }

  /**
   * 获取管理员通知内容
   * @param action 操作
   * @param contentType 内容类型
   * @param moderationResult 审核结果
   * @returns 通知内容
   */
  private getAdminNotificationContent(
    action: 'approve' | 'reject' | 'review',
    contentType: ContentType,
    moderationResult?: ContentModerationResult
  ): string {
    const contentTypeName = this.getContentTypeName(contentType);
    const baseContent = `一个${contentTypeName}已被自动${this.getActionName(action)}。`;

    if (!moderationResult) {
      return baseContent;
    }

    let details = '';
    if (action === 'reject') {
      details = `原因：${moderationResult.explanation}`;
      if (moderationResult.issues.length > 0) {
        details += `\n问题：${moderationResult.issues.join(', ')}`;
      }
      if (moderationResult.severity) {
        details += `\n严重程度：${this.getSeverityName(moderationResult.severity)}`;
      }
    } else if (action === 'review') {
      details = `AI建议：${this.getActionName(moderationResult.suggestedAction as any)}`;
      details += `\n置信度：${Math.round(moderationResult.confidence * 100)}%`;
      if (moderationResult.issues.length > 0) {
        details += `\n可能的问题：${moderationResult.issues.join(', ')}`;
      }
    }

    return `${baseContent}${details ? `\n\n${details}` : ''}`;
  }

  /**
   * 获取内容类型名称
   * @param contentType 内容类型
   * @returns 内容类型名称
   */
  private getContentTypeName(contentType: ContentType): string {
    switch (contentType) {
      case ContentType.STORY:
        return '故事';
      case ContentType.QUESTIONNAIRE:
        return '问卷';
      case ContentType.COMMENT:
        return '评论';
      case ContentType.PROFILE:
        return '个人资料';
      case ContentType.FEEDBACK:
        return '反馈';
      default:
        return '内容';
    }
  }

  /**
   * 获取操作名称
   * @param action 操作
   * @returns 操作名称
   */
  private getActionName(action: 'approve' | 'reject' | 'review'): string {
    switch (action) {
      case 'approve':
        return '通过';
      case 'reject':
        return '拒绝';
      case 'review':
        return '标记为需要人工审核';
      default:
        return '处理';
    }
  }

  /**
   * 获取严重程度名称
   * @param severity 严重程度
   * @returns 严重程度名称
   */
  private getSeverityName(severity: string): string {
    switch (severity) {
      case 'low':
        return '低';
      case 'medium':
        return '中';
      case 'high':
        return '高';
      default:
        return severity;
    }
  }

  /**
   * 计算优先级
   * @param moderationResult 审核结果
   * @returns 优先级
   */
  private calculatePriority(moderationResult: ContentModerationResult): number {
    // 基础优先级
    let priority = 1;

    // 根据置信度调整优先级
    if (moderationResult.confidence < 0.6) {
      priority += 1; // 低置信度需要更高优先级
    }

    // 根据严重程度调整优先级
    if (moderationResult.severity === 'high') {
      priority += 2;
    } else if (moderationResult.severity === 'medium') {
      priority += 1;
    }

    // 根据建议操作调整优先级
    if (moderationResult.suggestedAction === 'reject') {
      priority += 1;
    }

    // 确保优先级在合理范围内
    return Math.min(Math.max(priority, 1), 5);
  }

  /**
   * 生成序列号
   * @param contentType 内容类型
   * @returns 序列号
   */
  private async generateSequenceNumber(contentType: ContentType): Promise<string> {
    const prefix = contentType === ContentType.QUESTIONNAIRE ? 'Q' :
                  contentType === ContentType.STORY ? 'S' :
                  contentType === ContentType.COMMENT ? 'C' :
                  contentType === ContentType.PROFILE ? 'P' :
                  contentType === ContentType.FEEDBACK ? 'F' : 'X';

    const year = new Date().getFullYear().toString().substring(2);
    const month = (new Date().getMonth() + 1).toString().padStart(2, '0');
    const day = new Date().getDate().toString().padStart(2, '0');

    // 获取当天的序号
    const count = await this.prisma.pendingContent.count({
      where: {
        createdAt: {
          gte: new Date(new Date().setHours(0, 0, 0, 0))
        },
        type: contentType
      }
    });

    const sequence = (count + 1).toString().padStart(4, '0');

    return `${prefix}${year}${month}${day}${sequence}`;
  }

  /**
   * 将问卷内容转换为字符串
   * @param content 问卷内容
   * @returns 字符串形式的问卷内容
   */
  private convertQuestionnaireToString(content: any): string {
    if (!content) return '';

    let result = '';

    // 添加基本信息
    if (content.graduationYear) {
      result += `毕业年份: ${content.graduationYear}\n`;
    }

    if (content.major) {
      result += `专业: ${content.major}\n`;
    }

    if (content.educationLevel) {
      result += `学历: ${content.educationLevel}\n`;
    }

    // 添加就业状态
    if (content.employmentStatus) {
      result += `就业状态: ${content.employmentStatus}\n`;
    }

    // 添加就业信息
    if (content.employmentStatus === 'employed') {
      if (content.jobTitle) {
        result += `职位: ${content.jobTitle}\n`;
      }

      if (content.company) {
        result += `公司: ${content.company}\n`;
      }

      if (content.industry) {
        result += `行业: ${content.industry}\n`;
      }

      if (content.salary) {
        result += `薪资: ${content.salary}\n`;
      }

      if (content.jobSatisfaction) {
        result += `工作满意度: ${content.jobSatisfaction}\n`;
      }
    }

    // 添加失业信息
    if (content.employmentStatus === 'unemployed') {
      if (content.unemploymentDuration) {
        result += `失业时长: ${content.unemploymentDuration}\n`;
      }

      if (content.unemploymentReason) {
        result += `失业原因: ${content.unemploymentReason}\n`;
      }

      if (content.jobHuntingDifficulty) {
        result += `求职难度: ${content.jobHuntingDifficulty}\n`;
      }
    }

    // 添加反思和建议
    if (content.regretMajor !== undefined) {
      result += `是否后悔所学专业: ${content.regretMajor ? '是' : '否'}\n`;
    }

    if (content.preferredMajor) {
      result += `理想专业: ${content.preferredMajor}\n`;
    }

    if (content.careerChangeIntention) {
      result += `转行意向: ${content.careerChangeIntention}\n`;
    }

    if (content.careerChangeTarget) {
      result += `转行目标: ${content.careerChangeTarget}\n`;
    }

    if (content.adviceForStudents) {
      result += `给学生的建议: ${content.adviceForStudents}\n`;
    }

    if (content.observationOnEmployment) {
      result += `就业观察: ${content.observationOnEmployment}\n`;
    }

    return result;
  }

  /**
   * 将个人资料转换为字符串
   * @param content 个人资料
   * @returns 字符串形式的个人资料
   */
  private convertProfileToString(content: any): string {
    if (!content) return '';

    let result = '';

    if (content.username) {
      result += `用户名: ${content.username}\n`;
    }

    if (content.nickname) {
      result += `昵称: ${content.nickname}\n`;
    }

    if (content.bio) {
      result += `个人简介: ${content.bio}\n`;
    }

    if (content.avatar) {
      result += `头像: ${content.avatar}\n`;
    }

    if (content.education) {
      result += `教育背景: ${content.education}\n`;
    }

    if (content.work) {
      result += `工作经历: ${content.work}\n`;
    }

    if (content.interests) {
      result += `兴趣爱好: ${content.interests}\n`;
    }

    return result;
  }
}
