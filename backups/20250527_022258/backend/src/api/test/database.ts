import { Hono } from 'hono';
import type { Bindings } from '../../types';

const app = new Hono<{ Bindings: Bindings }>();

// 数据库查询测试端点

// 测试问卷响应数量
app.get('/count-responses', async (c) => {
  try {
    const result = await c.env.DB.prepare('SELECT COUNT(*) as count FROM questionnaire_responses_v2').first();
    return c.json({
      success: true,
      count: result?.count || 0,
      table: 'questionnaire_responses_v2',
      query: 'SELECT COUNT(*) as count FROM questionnaire_responses_v2'
    });
  } catch (error) {
    return c.json({
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error',
      count: 0
    }, 500);
  }
});

// 测试问卷声音数量
app.get('/count-voices', async (c) => {
  try {
    const result = await c.env.DB.prepare('SELECT COUNT(*) as count FROM questionnaire_voices_v2').first();
    return c.json({
      success: true,
      count: result?.count || 0,
      table: 'questionnaire_voices_v2',
      query: 'SELECT COUNT(*) as count FROM questionnaire_voices_v2'
    });
  } catch (error) {
    return c.json({
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error',
      count: 0
    }, 500);
  }
});

// 测试故事内容数量
app.get('/count-stories', async (c) => {
  try {
    const result = await c.env.DB.prepare('SELECT COUNT(*) as count FROM story_contents_v2').first();
    return c.json({
      success: true,
      count: result?.count || 0,
      table: 'story_contents_v2',
      query: 'SELECT COUNT(*) as count FROM story_contents_v2'
    });
  } catch (error) {
    return c.json({
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error',
      count: 0
    }, 500);
  }
});

// 测试用户数量
app.get('/count-users', async (c) => {
  try {
    const result = await c.env.DB.prepare('SELECT COUNT(*) as count FROM users_v2').first();
    return c.json({
      success: true,
      count: result?.count || 0,
      table: 'users_v2',
      query: 'SELECT COUNT(*) as count FROM users_v2'
    });
  } catch (error) {
    return c.json({
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error',
      count: 0
    }, 500);
  }
});

// 获取数据表信息
app.get('/table-info', async (c) => {
  try {
    const result = await c.env.DB.prepare('SELECT name, type FROM sqlite_master WHERE type="table"').all();
    return c.json({
      success: true,
      data: result.results || [],
      count: result.results?.length || 0,
      query: 'SELECT name, type FROM sqlite_master WHERE type="table"'
    });
  } catch (error) {
    return c.json({
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error',
      data: []
    }, 500);
  }
});

// 获取最近的问卷响应
app.get('/recent-responses', async (c) => {
  try {
    const result = await c.env.DB.prepare('SELECT id, created_at FROM questionnaire_responses_v2 ORDER BY created_at DESC LIMIT 5').all();
    return c.json({
      success: true,
      data: result.results || [],
      count: result.results?.length || 0,
      query: 'SELECT id, created_at FROM questionnaire_responses_v2 ORDER BY created_at DESC LIMIT 5'
    });
  } catch (error) {
    return c.json({
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error',
      data: []
    }, 500);
  }
});

// 综合数据库健康检查
app.get('/health', async (c) => {
  const results = {
    overall: 'healthy',
    tests: [] as any[],
    timestamp: new Date().toISOString()
  };

  const tests = [
    { name: 'responses', query: 'SELECT COUNT(*) as count FROM questionnaire_responses_v2' },
    { name: 'voices', query: 'SELECT COUNT(*) as count FROM questionnaire_voices_v2' },
    { name: 'stories', query: 'SELECT COUNT(*) as count FROM story_contents_v2' },
    { name: 'users', query: 'SELECT COUNT(*) as count FROM users_v2' },
    { name: 'tables', query: 'SELECT COUNT(*) as count FROM sqlite_master WHERE type="table"' }
  ];

  for (const test of tests) {
    const startTime = Date.now();
    try {
      const result = await c.env.DB.prepare(test.query).first();
      const responseTime = Date.now() - startTime;
      
      results.tests.push({
        name: test.name,
        status: 'healthy',
        count: result?.count || 0,
        responseTime,
        query: test.query
      });
    } catch (error) {
      const responseTime = Date.now() - startTime;
      results.overall = 'error';
      
      results.tests.push({
        name: test.name,
        status: 'error',
        error: error instanceof Error ? error.message : 'Unknown error',
        responseTime,
        query: test.query
      });
    }
  }

  return c.json({
    success: results.overall === 'healthy',
    ...results
  });
});

export default app;
