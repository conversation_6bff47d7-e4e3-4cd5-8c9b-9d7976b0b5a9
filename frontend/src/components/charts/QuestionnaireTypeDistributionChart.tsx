import React, { useState, useEffect } from 'react';
import { <PERSON><PERSON><PERSON>, Pie, Cell, ResponsiveContainer, <PERSON>, Toolt<PERSON>, <PERSON><PERSON><PERSON>, Bar, XAxis, <PERSON><PERSON><PERSON><PERSON>, CartesianGrid } from 'recharts';
import { Button } from '@/components/ui/button';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Loader2, RefreshCw } from 'lucide-react';

interface QuestionnaireTypeData {
  type: string;
  count: number;
  percentage: number;
  avgCompletionTime: number;
  completionRate: number;
}

interface QuestionnaireTypeDistributionChartProps {
  height?: number;
  showControls?: boolean;
  chartType?: 'pie' | 'bar';
}

const QuestionnaireTypeDistributionChart: React.FC<QuestionnaireTypeDistributionChartProps> = ({ 
  height = 300,
  showControls = true,
  chartType = 'pie'
}) => {
  const [data, setData] = useState<QuestionnaireTypeData[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [selectedChartType, setSelectedChartType] = useState<'pie' | 'bar'>(chartType);
  const [selectedMetric, setSelectedMetric] = useState<'count' | 'completionRate' | 'avgCompletionTime'>('count');

  // 颜色配置
  const COLORS = [
    '#8884d8', // 蓝色 - 就业调查
    '#82ca9d', // 绿色 - 满意度调查
    '#ffc658', // 黄色 - 薪资调查
    '#ff7c7c', // 红色 - 职业规划
    '#8dd1e1', // 浅蓝 - 技能评估
    '#d084d0', // 紫色 - 其他
  ];

  // 问卷类型映射
  const questionnaireTypeMap: { [key: string]: string } = {
    'employment': '就业状况调查',
    'satisfaction': '工作满意度调查',
    'salary': '薪资水平调查',
    'career': '职业规划调查',
    'skills': '技能评估调查',
    'other': '其他类型调查'
  };

  // 获取问卷类型分布数据
  const fetchQuestionnaireTypeData = async () => {
    setLoading(true);
    setError(null);
    
    try {
      const response = await fetch('/api/admin/dashboard/questionnaire-types');
      
      if (!response.ok) {
        throw new Error('获取问卷类型分布数据失败');
      }
      
      const result = await response.json();
      
      if (result.success) {
        // 转换数据格式
        const formattedData = result.data.map((item: any) => ({
          type: questionnaireTypeMap[item.type] || item.type,
          count: item.count,
          percentage: item.percentage,
          avgCompletionTime: item.avg_completion_time || 0,
          completionRate: item.completion_rate || 0
        }));
        setData(formattedData);
      } else {
        throw new Error(result.message || '数据格式错误');
      }
    } catch (err) {
      console.error('问卷类型分布数据获取错误:', err);
      setError(err instanceof Error ? err.message : '未知错误');
      
      // 使用模拟数据作为降级
      generateMockData();
    } finally {
      setLoading(false);
    }
  };

  // 生成模拟数据
  const generateMockData = () => {
    const mockData: QuestionnaireTypeData[] = [
      { 
        type: '就业状况调查', 
        count: 145, 
        percentage: 42.3, 
        avgCompletionTime: 8.5,
        completionRate: 87.2
      },
      { 
        type: '工作满意度调查', 
        count: 89, 
        percentage: 26.0, 
        avgCompletionTime: 6.2,
        completionRate: 92.1
      },
      { 
        type: '薪资水平调查', 
        count: 67, 
        percentage: 19.5, 
        avgCompletionTime: 4.8,
        completionRate: 94.5
      },
      { 
        type: '职业规划调查', 
        count: 28, 
        percentage: 8.2, 
        avgCompletionTime: 12.3,
        completionRate: 78.9
      },
      { 
        type: '技能评估调查', 
        count: 14, 
        percentage: 4.1, 
        avgCompletionTime: 15.7,
        completionRate: 71.4
      }
    ];
    
    setData(mockData);
  };

  // 初始化数据
  useEffect(() => {
    fetchQuestionnaireTypeData();
  }, []);

  // 获取显示数据
  const getDisplayData = () => {
    return data.map(item => ({
      ...item,
      value: selectedMetric === 'count' ? item.count :
             selectedMetric === 'completionRate' ? item.completionRate :
             item.avgCompletionTime
    }));
  };

  // 自定义Tooltip
  const CustomTooltip = ({ active, payload }: any) => {
    if (active && payload && payload.length) {
      const data = payload[0].payload;
      return (
        <div className="bg-white p-3 border rounded-lg shadow-lg">
          <p className="font-medium mb-1">{data.type}</p>
          <p className="text-sm text-gray-600">问卷数量: {data.count}</p>
          <p className="text-sm text-gray-600">占比: {data.percentage.toFixed(1)}%</p>
          <p className="text-sm text-gray-600">完成率: {data.completionRate.toFixed(1)}%</p>
          <p className="text-sm text-gray-600">平均完成时间: {data.avgCompletionTime.toFixed(1)}分钟</p>
        </div>
      );
    }
    return null;
  };

  // 自定义标签
  const renderCustomLabel = ({ cx, cy, midAngle, innerRadius, outerRadius, percent }: any) => {
    if (percent < 0.05) return null; // 小于5%不显示标签
    
    const RADIAN = Math.PI / 180;
    const radius = innerRadius + (outerRadius - innerRadius) * 0.5;
    const x = cx + radius * Math.cos(-midAngle * RADIAN);
    const y = cy + radius * Math.sin(-midAngle * RADIAN);

    return (
      <text 
        x={x} 
        y={y} 
        fill="white" 
        textAnchor={x > cx ? 'start' : 'end'} 
        dominantBaseline="central"
        fontSize={12}
        fontWeight="bold"
      >
        {`${(percent * 100).toFixed(0)}%`}
      </text>
    );
  };

  // 处理刷新
  const handleRefresh = () => {
    fetchQuestionnaireTypeData();
  };

  // 渲染加载状态
  if (loading) {
    return (
      <div className="flex items-center justify-center" style={{ height: `${height}px` }}>
        <Loader2 className="h-8 w-8 animate-spin text-muted-foreground" />
        <span className="ml-2 text-muted-foreground">加载问卷类型分布数据...</span>
      </div>
    );
  }

  // 渲染错误状态
  if (error) {
    return (
      <div className="flex flex-col items-center justify-center" style={{ height: `${height}px` }}>
        <p className="text-red-500 mb-2">加载失败: {error}</p>
        <Button variant="outline" size="sm" onClick={handleRefresh}>
          <RefreshCw className="h-4 w-4 mr-2" />
          重试
        </Button>
      </div>
    );
  }

  const displayData = getDisplayData();

  return (
    <div className="w-full">
      {showControls && (
        <div className="flex justify-between items-center mb-4">
          <div className="flex space-x-2">
            <Select value={selectedChartType} onValueChange={(value: any) => setSelectedChartType(value)}>
              <SelectTrigger className="w-24">
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="pie">饼图</SelectItem>
                <SelectItem value="bar">柱图</SelectItem>
              </SelectContent>
            </Select>
            
            <Select value={selectedMetric} onValueChange={(value: any) => setSelectedMetric(value)}>
              <SelectTrigger className="w-32">
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="count">问卷数量</SelectItem>
                <SelectItem value="completionRate">完成率</SelectItem>
                <SelectItem value="avgCompletionTime">完成时间</SelectItem>
              </SelectContent>
            </Select>
          </div>
          
          <Button variant="outline" size="sm" onClick={handleRefresh}>
            <RefreshCw className="h-4 w-4 mr-2" />
            刷新
          </Button>
        </div>
      )}
      
      <div style={{ height: `${height}px` }}>
        <ResponsiveContainer width="100%" height="100%">
          {selectedChartType === 'pie' ? (
            <PieChart>
              <Pie
                data={displayData}
                cx="50%"
                cy="50%"
                labelLine={false}
                label={renderCustomLabel}
                outerRadius={Math.min(height * 0.35, 120)}
                fill="#8884d8"
                dataKey="value"
              >
                {displayData.map((entry, index) => (
                  <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                ))}
              </Pie>
              <Tooltip content={<CustomTooltip />} />
              <Legend 
                verticalAlign="bottom" 
                height={36}
                formatter={(value, entry: any) => (
                  <span style={{ color: entry.color }}>
                    {value}
                  </span>
                )}
              />
            </PieChart>
          ) : (
            <BarChart data={displayData} margin={{ top: 20, right: 30, left: 20, bottom: 5 }}>
              <CartesianGrid strokeDasharray="3 3" />
              <XAxis 
                dataKey="type" 
                tick={{ fontSize: 12 }}
                angle={-45}
                textAnchor="end"
                height={80}
              />
              <YAxis tick={{ fontSize: 12 }} />
              <Tooltip content={<CustomTooltip />} />
              <Bar dataKey="value" fill="#8884d8">
                {displayData.map((entry, index) => (
                  <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                ))}
              </Bar>
            </BarChart>
          )}
        </ResponsiveContainer>
      </div>
      
      {data.length === 0 && (
        <div className="text-center text-muted-foreground mt-4">
          暂无问卷类型分布数据
        </div>
      )}
      
      {/* 数据摘要 */}
      {data.length > 0 && (
        <div className="mt-4 grid grid-cols-3 gap-4 text-sm">
          <div className="text-center">
            <p className="text-muted-foreground">总问卷数</p>
            <p className="font-semibold text-lg">
              {data.reduce((sum, item) => sum + item.count, 0).toLocaleString()}
            </p>
          </div>
          <div className="text-center">
            <p className="text-muted-foreground">平均完成率</p>
            <p className="font-semibold text-lg">
              {(data.reduce((sum, item) => sum + item.completionRate, 0) / data.length).toFixed(1)}%
            </p>
          </div>
          <div className="text-center">
            <p className="text-muted-foreground">问卷类型</p>
            <p className="font-semibold text-lg">{data.length}</p>
          </div>
        </div>
      )}
    </div>
  );
};

export default QuestionnaireTypeDistributionChart;
