/**
 * 备份计划服务
 *
 * 提供备份计划相关的 API 调用
 */

import { apiClient } from './apiClient';

// 备份计划类型
export interface BackupSchedule {
  id: string;
  name: string;
  type: 'full' | 'incremental' | 'differential';
  frequency: 'hourly' | 'daily' | 'weekly' | 'monthly';
  time: string;
  dayOfWeek?: number; // 0-6, 0 is Sunday
  dayOfMonth?: number; // 1-31
  status: 'enabled' | 'disabled';
  nextRun: string;
  lastRun?: string;
  createdAt: string;
  updatedAt: string;
}

// 备份计划设置
export interface BackupScheduleSettings {
  enabled: boolean;
  frequency: 'hourly' | 'daily' | 'weekly' | 'monthly';
  time: string;
  retention: number;
  backupType: 'full' | 'incremental' | 'differential' | 'mixed';
  location: 'local' | 'cloud' | 'remote';
  compressionLevel: 'none' | 'low' | 'medium' | 'high';
  includeUploads: boolean;
  includeDatabase: boolean;
  includeConfigs: boolean;
}

// 默认备份计划
const defaultBackupSchedules: BackupSchedule[] = [
  {
    id: '1',
    name: '每日增量备份',
    type: 'incremental',
    frequency: 'daily',
    time: '02:00',
    status: 'enabled',
    nextRun: new Date(new Date().setHours(2, 0, 0, 0)).toISOString(),
    createdAt: '2023-06-01T00:00:00Z',
    updatedAt: '2023-06-01T00:00:00Z'
  },
  {
    id: '2',
    name: '每周差异备份',
    type: 'differential',
    frequency: 'weekly',
    time: '03:00',
    dayOfWeek: 0, // Sunday
    status: 'enabled',
    nextRun: getNextWeekday(0, 3, 0).toISOString(),
    createdAt: '2023-06-01T00:00:00Z',
    updatedAt: '2023-06-01T00:00:00Z'
  },
  {
    id: '3',
    name: '每月完整备份',
    type: 'full',
    frequency: 'monthly',
    time: '01:00',
    dayOfMonth: 1,
    status: 'enabled',
    nextRun: getNextMonthDay(1, 1, 0).toISOString(),
    createdAt: '2023-06-01T00:00:00Z',
    updatedAt: '2023-06-01T00:00:00Z'
  }
];

// 获取下一个指定星期几的日期
function getNextWeekday(dayOfWeek: number, hour: number, minute: number): Date {
  const date = new Date();
  const currentDayOfWeek = date.getDay();
  const daysToAdd = (dayOfWeek + 7 - currentDayOfWeek) % 7;
  
  date.setDate(date.getDate() + daysToAdd);
  date.setHours(hour, minute, 0, 0);
  
  // 如果计算出的时间已经过去，则加7天
  if (date < new Date()) {
    date.setDate(date.getDate() + 7);
  }
  
  return date;
}

// 获取下一个指定日期的月份日期
function getNextMonthDay(dayOfMonth: number, hour: number, minute: number): Date {
  const date = new Date();
  const currentMonth = date.getMonth();
  const currentYear = date.getFullYear();
  
  // 设置为当月的指定日期
  date.setDate(dayOfMonth);
  date.setHours(hour, minute, 0, 0);
  
  // 如果计算出的时间已经过去，则设置为下个月的指定日期
  if (date < new Date()) {
    date.setMonth(currentMonth + 1);
  }
  
  // 检查日期是否有效（例如，2月30日是无效的）
  if (date.getDate() !== dayOfMonth) {
    // 如果无效，则设置为下个月的最后一天
    date.setDate(0);
  }
  
  return date;
}

/**
 * 获取备份计划列表
 * @returns 备份计划列表
 */
export async function getBackupSchedules() {
  try {
    // 检查是否为开发环境
    if (import.meta.env.DEV && import.meta.env.VITE_USE_MOCK === 'true') {
      console.log('使用模拟数据获取备份计划列表');

      // 模拟网络延迟
      await new Promise(resolve => setTimeout(resolve, 1000));

      return {
        success: true,
        data: defaultBackupSchedules
      };
    }

    // 生产环境使用真实API
    const response = await apiClient.get('/admin/backup/schedules');
    return response.data;
  } catch (error) {
    console.error('获取备份计划列表失败:', error);
    throw error;
  }
}

/**
 * 创建备份计划
 * @param schedule 备份计划
 * @returns 创建结果
 */
export async function createBackupSchedule(schedule: Partial<BackupSchedule>) {
  try {
    // 检查是否为开发环境
    if (import.meta.env.DEV && import.meta.env.VITE_USE_MOCK === 'true') {
      console.log('使用模拟数据创建备份计划');

      // 模拟网络延迟
      await new Promise(resolve => setTimeout(resolve, 1000));

      // 创建新的备份计划
      const newSchedule: BackupSchedule = {
        id: Math.random().toString(36).substring(2, 11),
        name: schedule.name || '新建备份计划',
        type: schedule.type || 'full',
        frequency: schedule.frequency || 'daily',
        time: schedule.time || '00:00',
        status: 'enabled',
        nextRun: new Date(new Date().setHours(0, 0, 0, 0) + 86400000).toISOString(),
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString()
      };

      return {
        success: true,
        data: newSchedule,
        message: '备份计划创建成功'
      };
    }

    // 生产环境使用真实API
    const response = await apiClient.post('/admin/backup/schedules', schedule);
    return response.data;
  } catch (error) {
    console.error('创建备份计划失败:', error);
    throw error;
  }
}

/**
 * 更新备份计划
 * @param id 备份计划ID
 * @param schedule 备份计划
 * @returns 更新结果
 */
export async function updateBackupSchedule(id: string, schedule: Partial<BackupSchedule>) {
  try {
    // 检查是否为开发环境
    if (import.meta.env.DEV && import.meta.env.VITE_USE_MOCK === 'true') {
      console.log('使用模拟数据更新备份计划');

      // 模拟网络延迟
      await new Promise(resolve => setTimeout(resolve, 1000));

      return {
        success: true,
        message: '备份计划更新成功'
      };
    }

    // 生产环境使用真实API
    const response = await apiClient.put(`/admin/backup/schedules/${id}`, schedule);
    return response.data;
  } catch (error) {
    console.error('更新备份计划失败:', error);
    throw error;
  }
}

/**
 * 删除备份计划
 * @param id 备份计划ID
 * @returns 删除结果
 */
export async function deleteBackupSchedule(id: string) {
  try {
    // 检查是否为开发环境
    if (import.meta.env.DEV && import.meta.env.VITE_USE_MOCK === 'true') {
      console.log('使用模拟数据删除备份计划');

      // 模拟网络延迟
      await new Promise(resolve => setTimeout(resolve, 1000));

      return {
        success: true,
        message: '备份计划删除成功'
      };
    }

    // 生产环境使用真实API
    const response = await apiClient.delete(`/admin/backup/schedules/${id}`);
    return response.data;
  } catch (error) {
    console.error('删除备份计划失败:', error);
    throw error;
  }
}

/**
 * 获取备份计划设置
 * @returns 备份计划设置
 */
export async function getBackupScheduleSettings() {
  try {
    // 检查是否为开发环境
    if (import.meta.env.DEV && import.meta.env.VITE_USE_MOCK === 'true') {
      console.log('使用模拟数据获取备份计划设置');

      // 模拟网络延迟
      await new Promise(resolve => setTimeout(resolve, 1000));

      return {
        success: true,
        data: {
          enabled: true,
          frequency: 'daily',
          time: '02:00',
          retention: 30,
          backupType: 'mixed',
          location: 'local',
          compressionLevel: 'medium',
          includeUploads: true,
          includeDatabase: true,
          includeConfigs: true
        }
      };
    }

    // 生产环境使用真实API
    const response = await apiClient.get('/admin/backup/settings');
    return response.data;
  } catch (error) {
    console.error('获取备份计划设置失败:', error);
    throw error;
  }
}

/**
 * 更新备份计划设置
 * @param settings 备份计划设置
 * @returns 更新结果
 */
export async function updateBackupScheduleSettings(settings: Partial<BackupScheduleSettings>) {
  try {
    // 检查是否为开发环境
    if (import.meta.env.DEV && import.meta.env.VITE_USE_MOCK === 'true') {
      console.log('使用模拟数据更新备份计划设置');

      // 模拟网络延迟
      await new Promise(resolve => setTimeout(resolve, 1000));

      return {
        success: true,
        message: '备份计划设置更新成功'
      };
    }

    // 生产环境使用真实API
    const response = await apiClient.put('/admin/backup/settings', settings);
    return response.data;
  } catch (error) {
    console.error('更新备份计划设置失败:', error);
    throw error;
  }
}
