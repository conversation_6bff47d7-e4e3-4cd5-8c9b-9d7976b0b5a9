/**
 * 风险评估中间件
 * 对高风险操作进行评估和控制
 */

import { Context, Next } from 'hono';
import { Env } from '../types';
import { RiskAssessmentService } from '../services/riskAssessmentService';
import { Permission } from '../services/permissionService';
import { AuditLogService, AuditAction, AuditSeverity } from '../services/auditLogService';

/**
 * 创建风险评估中间件
 * @param permission 权限
 * @param resourceType 资源类型
 * @param options 选项
 */
export const riskAssessmentMiddleware = (
  permission: Permission,
  resourceType: string,
  options: {
    bypassConfirmation?: boolean;
    bypassApproval?: boolean;
    confirmationHeader?: string;
  } = {}
) => {
  return async (c: Context<{ Bindings: Env }>, next: Next) => {
    // 开发环境下的简化处理
    if (c.env.ENVIRONMENT === 'development') {
      console.warn(`Development mode: Bypassing risk assessment for ${permission}`);
      await next();
      return;
    }

    // 获取用户信息
    const user = c.get('user');
    
    if (!user) {
      return c.json({ success: false, error: 'Unauthorized' }, 401);
    }

    // 获取用户ID
    const userId = user.id || user.username || 'unknown';
    
    // 获取资源ID（如果有）
    let resourceId: string | undefined;
    if (c.req.param('id')) {
      resourceId = c.req.param('id');
    }
    
    // 获取客户端信息
    const ipAddress = c.req.headers.get('CF-Connecting-IP') || c.req.headers.get('X-Forwarded-For') || 'unknown';
    const userAgent = c.req.headers.get('User-Agent') || 'unknown';
    
    // 获取操作详情
    let details: any = {};
    if (c.req.method === 'POST' || c.req.method === 'PUT' || c.req.method === 'PATCH') {
      try {
        // 克隆请求以避免消耗请求体
        const clonedReq = c.req.clone();
        details = await clonedReq.json();
      } catch (error) {
        // 忽略解析错误
        console.warn('Failed to parse request body for risk assessment:', error);
      }
    }
    
    // 获取风险评估服务实例
    const riskAssessmentService = RiskAssessmentService.getInstance();
    
    // 评估风险
    const riskAssessment = await riskAssessmentService.assessRisk(
      userId,
      permission,
      resourceType,
      resourceId,
      details,
      {
        ipAddress,
        userAgent,
        timestamp: new Date()
      },
      c.env
    );
    
    // 检查是否需要确认
    if (riskAssessment.requiresConfirmation && !options.bypassConfirmation) {
      const confirmationHeader = options.confirmationHeader || 'X-Confirm-Operation';
      const confirmationValue = c.req.headers.get(confirmationHeader);
      
      if (!confirmationValue || confirmationValue !== 'true') {
        // 记录确认缺失
        const auditLogService = AuditLogService.getInstance();
        await auditLogService.log({
          userId,
          action: AuditAction.PERMISSION_DENIED,
          resourceType,
          resourceId,
          details: {
            permission,
            reason: 'Missing confirmation for high-risk operation',
            riskAssessment
          },
          ipAddress,
          userAgent,
          severity: AuditSeverity.WARNING
        }, c.env);
        
        return c.json({ 
          success: false, 
          error: 'Confirmation required for high-risk operation',
          riskAssessment,
          confirmationRequired: true,
          confirmationHeader
        }, 403);
      }
    }
    
    // 检查是否需要审批
    if (riskAssessment.requiresApproval && !options.bypassApproval && user.role !== 'superadmin') {
      // 记录审批缺失
      const auditLogService = AuditLogService.getInstance();
      await auditLogService.log({
        userId,
        action: AuditAction.PERMISSION_DENIED,
        resourceType,
        resourceId,
        details: {
          permission,
          reason: 'Admin approval required for critical-risk operation',
          riskAssessment
        },
        ipAddress,
        userAgent,
        severity: AuditSeverity.WARNING
      }, c.env);
      
      return c.json({ 
        success: false, 
        error: 'Admin approval required for critical-risk operation',
        riskAssessment,
        approvalRequired: true
      }, 403);
    }
    
    // 将风险评估结果添加到请求上下文
    c.set('riskAssessment', riskAssessment);
    
    await next();
  };
};
