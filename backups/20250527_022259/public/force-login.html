<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>强制超级管理员登录</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 50px auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .button {
            background: #007bff;
            color: white;
            padding: 12px 24px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-size: 16px;
            margin: 10px 5px;
        }
        .button:hover {
            background: #0056b3;
        }
        .success {
            color: #28a745;
            font-weight: bold;
        }
        .error {
            color: #dc3545;
            font-weight: bold;
        }
        .info {
            color: #17a2b8;
        }
        #log {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 5px;
            margin-top: 20px;
            font-family: monospace;
            white-space: pre-wrap;
            max-height: 400px;
            overflow-y: auto;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔧 超级管理员登录工具</h1>
        <p>此工具用于强制设置超级管理员登录状态，解决权限问题。</p>
        
        <div>
            <button class="button" onclick="forceSuperAdminLogin()">🚀 强制超级管理员登录</button>
            <button class="button" onclick="clearAllCache()">🧹 清除所有缓存</button>
            <button class="button" onclick="checkCurrentStatus()">🔍 检查当前状态</button>
            <button class="button" onclick="goToDashboard()">📊 跳转到仪表盘</button>
        </div>
        
        <div id="log"></div>
    </div>

    <script>
        function log(message, type = 'info') {
            const logElement = document.getElementById('log');
            const timestamp = new Date().toLocaleTimeString();
            const className = type === 'success' ? 'success' : type === 'error' ? 'error' : 'info';
            logElement.innerHTML += `<span class="${className}">[${timestamp}] ${message}</span>\n`;
            logElement.scrollTop = logElement.scrollHeight;
        }

        function forceSuperAdminLogin() {
            log('🔧 开始强制超级管理员登录...', 'info');
            
            // 清除所有缓存
            localStorage.clear();
            sessionStorage.clear();
            log('🧹 已清除所有缓存数据', 'success');
            
            // 创建超级管理员用户数据
            const superAdminUser = {
                id: 'superadmin-001',
                name: 'Super Administrator',
                email: '<EMAIL>',
                role: 'superadmin',
                permissions: [
                    'dashboard:personal', 'dashboard:system', 'dashboard:security',
                    'content:review', 'story:review', 'quick:review', 'tag:management',
                    'questionnaire:view', 'questionnaire:edit', 'data:analysis', 'data:export',
                    'user:view', 'user:management', 'reviewer:management', 'admin:management', 'role:management',
                    'settings:personal', 'settings:deidentification', 'settings:security', 'settings:system',
                    'security:monitor', 'security:logs', 'security:admin-audit', 'security:login-records'
                ],
                loginTime: new Date().toISOString()
            };
            
            // 设置登录状态
            localStorage.setItem('adminToken', 'superadmin-token-' + Date.now());
            localStorage.setItem('adminUser', JSON.stringify(superAdminUser));
            log('👑 已设置超级管理员登录状态', 'success');
            
            // 设置菜单状态
            localStorage.setItem('superadmin_sidebar_collapsed', 'false');
            localStorage.setItem('superadmin_menu_collapsed_state', JSON.stringify({
                '仪表盘': false,
                '用户管理': false,
                '内容管理': false,
                '数据管理': false,
                '安全监控': false,
                '系统管理': false,
                '文档中心': false
            }));
            sessionStorage.setItem('superadmin_menu_initialized', 'true');
            log('📋 已设置菜单状态', 'success');
            
            // 验证设置
            checkCurrentStatus();
            log('🎉 超级管理员登录设置完成！', 'success');
        }

        function clearAllCache() {
            localStorage.clear();
            sessionStorage.clear();
            log('🧹 已清除所有缓存数据', 'success');
        }

        function checkCurrentStatus() {
            const token = localStorage.getItem('adminToken');
            const userJson = localStorage.getItem('adminUser');
            
            log('🔍 检查当前登录状态:', 'info');
            log(`Token: ${token ? '✅ 已设置' : '❌ 未设置'}`, token ? 'success' : 'error');
            
            if (userJson) {
                try {
                    const user = JSON.parse(userJson);
                    log(`用户角色: ${user.role === 'superadmin' ? '✅ 超级管理员' : '❌ ' + user.role}`, user.role === 'superadmin' ? 'success' : 'error');
                    log(`权限数量: ${user.permissions?.length || 0} 个`, user.permissions?.length > 0 ? 'success' : 'error');
                    log(`用户信息: ${JSON.stringify(user, null, 2)}`, 'info');
                } catch (e) {
                    log('❌ 用户数据解析失败', 'error');
                }
            } else {
                log('❌ 未找到用户数据', 'error');
            }
        }

        function goToDashboard() {
            log('🔄 跳转到超级管理员仪表盘...', 'info');
            window.location.href = '/superadmin/dashboard';
        }

        // 页面加载时检查状态
        window.onload = function() {
            log('📄 页面加载完成', 'info');
            checkCurrentStatus();
        };
    </script>
</body>
</html>
