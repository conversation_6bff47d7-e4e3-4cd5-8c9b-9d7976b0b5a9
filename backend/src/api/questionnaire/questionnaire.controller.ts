import { PrismaClient } from '@prisma/client';
import { z } from 'zod';
import { Context } from 'hono';
import { zValidator } from '@hono/zod-validator';
import { rateLimit } from '../../middlewares/rateLimit';
import { getMockQuestionnaireStats, getMockRealtimeStats } from '../../services/mockDataService';
import config, { shouldUseMockData } from '../../config';
import { SecurityModule } from '../../security';
import { generateUUID, isValidA, isValidB } from '../../utils/uuidGenerator';
import { ContentType } from '../../constants';
import { VoiceGenerationService } from '../../services/voiceGenerationService';

/**
 * 生成序列号
 * @param prefix 前缀
 * @returns 序列号
 */
function generateSequenceNumber(prefix: string): string {
  const year = new Date().getFullYear().toString().substring(2);
  const month = (new Date().getMonth() + 1).toString().padStart(2, '0');
  const day = new Date().getDate().toString().padStart(2, '0');
  const random = Math.floor(Math.random() * 10000).toString().padStart(4, '0');

  return `${prefix}${year}${month}${day}${random}`;
}

/**
 * 计算优先级
 * @param autoModeration 自动审核结果
 * @returns 优先级
 */
function calculatePriority(autoModeration: any): number {
  // 基础优先级
  let priority = 1;

  // 根据置信度调整优先级
  if (autoModeration.confidence < 0.6) {
    priority += 1; // 低置信度需要更高优先级
  }

  // 根据严重程度调整优先级
  if (autoModeration.severity === 'high') {
    priority += 2;
  } else if (autoModeration.severity === 'medium') {
    priority += 1;
  }

  // 确保优先级在合理范围内
  return Math.min(Math.max(priority, 1), 5);
}

// Define environment interface
interface Env {
  SURVEY_KV: KVNamespace;
  DATABASE_URL: string;
  UUID_SALT?: string;
  ENVIRONMENT?: string;
}

// Initialize Prisma client
const prisma = new PrismaClient();

// Define validation schema for questionnaire submission
const questionnaireSchema = z.object({
  // 1. Personal information
  educationLevel: z.string().optional(),
  major: z.string().optional(),
  graduationYear: z.number().optional(),
  region: z.string().optional(),

  // 2. Employment expectations
  expectedPosition: z.string().optional(),
  expectedSalaryRange: z.string().optional(),
  expectedWorkHours: z.number().optional(),
  expectedVacationDays: z.number().optional(),

  // 3. Work experience
  employmentStatus: z.string().optional(),
  currentIndustry: z.string().optional(),
  currentPosition: z.string().optional(),
  jobSatisfaction: z.number().min(1).max(5).optional(),

  // 4. Unemployment status
  unemploymentDuration: z.string().optional(),
  unemploymentReason: z.string().optional(),
  jobHuntingDifficulty: z.number().min(1).max(5).optional(),

  // 5. Career change and reflection
  regretMajor: z.boolean().optional(),
  preferredMajor: z.string().optional(),
  careerChangeIntention: z.boolean().optional(),
  careerChangeTarget: z.string().optional(),

  // 6. Advice and feedback
  adviceForStudents: z.string().optional(),
  observationOnEmployment: z.string().optional(),

  // Submission options
  isAnonymous: z.boolean().default(true),
  emailVerificationId: z.string().optional(),

  // 轻量级匿名身份验证字段（可选）
  identityA: z.string().regex(/^\d{11}$/, { message: 'A必须是11位数字' }).optional(),
  identityB: z.string().regex(/^\d{4}$|^\d{6}$/, { message: 'B必须是4位或6位数字' }).optional(),
});

// Submit questionnaire
export const submitQuestionnaire = [
  rateLimit({ limit: 1, window: 60 }), // 1 request per minute
  SecurityModule.createMiddleware(), // 安全中间件
  zValidator('json', questionnaireSchema),
  async (c: Context<{ Bindings: Env }>) => {
    const data = c.req.valid('json');
    const ip = c.req.header('CF-Connecting-IP') || c.req.header('X-Forwarded-For') || 'unknown';
    const userAgent = c.req.header('User-Agent') || 'unknown';

    // 获取请求 ID
    const requestId = c.get('requestId') || crypto.randomUUID();

    // 检查是否是静默拒绝
    if (c.get('silentRejection') === true) {
      // 记录安全事件
      await SecurityModule.log(
        'warn',
        'Silent rejection of questionnaire submission',
        { clientIp: ip },
        ip,
        c.env,
        {
          requestId,
          errorCode: 'HONEYPOT_TRIGGERED',
          severity: 'high',
          source: 'questionnaireController',
          tags: ['questionnaire', 'honeypot', 'silentRejection']
        }
      );

      // 返回成功响应，但不实际处理数据
      return c.json({
        success: true,
        message: 'Questionnaire submitted successfully',
        responseId: Math.floor(Math.random() * 1000000),
        verified: !data.isAnonymous
      });
    }

    try {
      // 分析提交数据
      const analysisResult = await SecurityModule.analyzeSubmission(data, ip, requestId, c.env);

      // 如果非常可疑，拒绝提交
      if (analysisResult.suspicious && analysisResult.suspiciousScore > 70) {
        return c.json({
          success: false,
          error: '提交被拒绝，请稍后重试',
          code: 'SUSPICIOUS_SUBMISSION'
        }, 400);
      }

      // 标记可疑提交
      const isSuspicious = analysisResult.suspicious;
      const securityMetadata = {
        timestamp: Date.now(),
        suspicious: isSuspicious,
        suspiciousScore: analysisResult.suspiciousScore,
        reasons: analysisResult.reasons,
        securityLevel: analysisResult.securityLevel
      };
      // Check if this is a verified submission
      let userId = null;
      let verified = false;

      if (data.emailVerificationId && !data.isAnonymous) {
        // Get verification data from KV
        const verification = await c.env.SURVEY_KV.get(
          `verification:${data.emailVerificationId}`,
          'json'
        ) as { userId: number, verified: boolean, email: string } | null;

        if (verification && verification.verified) {
          userId = verification.userId;
          verified = true;
        }
      }

      // 处理轻量级匿名身份验证
      let submittedById = null;
      if (data.identityA && data.identityB) {
        try {
          // 生成UUID，如果没有设置UUID_SALT则使用默认值
          const uuidSalt = c.env.UUID_SALT || 'default-uuid-salt-2024';
          submittedById = generateUUID(data.identityA, data.identityB, uuidSalt);
          console.log(`Generated UUID for anonymous identity: ${submittedById.substring(0, 8)}...`);
        } catch (error) {
          console.error('Error generating UUID:', error);
          // 继续处理，但不设置submittedById
        }
      }

      // 检测是否为测试数据生成器请求
      const isTestDataGenerator = data._testData === true ||
                                   c.req.header('X-Test-Data-Generator') === 'true' ||
                                   c.req.header('User-Agent')?.includes('TestDataGenerator');

      // 检查是否使用模拟数据
      if (shouldUseMockData({ isTestDataGenerator })) {
        console.log('Mock data enabled: Saving questionnaire to KV storage');

        // 获取当前问卷列表
        const questionnairesKey = 'questionnaire_list';
        const questionnairesJson = await c.env.SURVEY_KV.get(questionnairesKey);
        const questionnaires = questionnairesJson ? JSON.parse(questionnairesJson) : [];

        // 创建新问卷
        const newQuestionnaire = {
          id: questionnaires.length + 1,
          userId: userId,
          isAnonymous: data.isAnonymous,
          ipAddress: ip,
          submittedById, // 添加轻量级匿名身份标识
          createdAt: new Date().toISOString(),

          // 1. Personal information
          educationLevel: data.educationLevel,
          major: data.major,
          graduationYear: data.graduationYear,
          region: data.region,

          // 2. Employment expectations
          expectedPosition: data.expectedPosition,
          expectedSalaryRange: data.expectedSalaryRange,
          expectedWorkHours: data.expectedWorkHours,
          expectedVacationDays: data.expectedVacationDays,

          // 3. Work experience
          employmentStatus: data.employmentStatus,
          currentIndustry: data.currentIndustry,
          currentPosition: data.currentPosition,
          jobSatisfaction: data.jobSatisfaction,

          // 4. Unemployment status
          unemploymentDuration: data.unemploymentDuration,
          unemploymentReason: data.unemploymentReason,
          jobHuntingDifficulty: data.jobHuntingDifficulty,

          // 5. Career change and reflection
          regretMajor: data.regretMajor,
          preferredMajor: data.preferredMajor,
          careerChangeIntention: data.careerChangeIntention,
          careerChangeTarget: data.careerChangeTarget,

          // 6. Advice and feedback
          adviceForStudents: data.adviceForStudents,
          observationOnEmployment: data.observationOnEmployment,
        };

        // 添加到问卷列表
        questionnaires.push(newQuestionnaire);

        // 保存问卷列表
        await c.env.SURVEY_KV.put(questionnairesKey, JSON.stringify(questionnaires));

        // 保存问卷详情
        await c.env.SURVEY_KV.put(`questionnaire:${newQuestionnaire.id}`, JSON.stringify(newQuestionnaire));

        // 自动生成心声数据
        try {
          const voiceResult = await VoiceGenerationService.generateVoicesFromQuestionnaire(c, {
            id: newQuestionnaire.id.toString(),
            userId: newQuestionnaire.userId?.toString(),
            adviceForStudents: newQuestionnaire.adviceForStudents,
            observationOnEmployment: newQuestionnaire.observationOnEmployment,
            educationLevel: newQuestionnaire.educationLevel,
            region: newQuestionnaire.region,
            isAnonymous: newQuestionnaire.isAnonymous
          });

          if (voiceResult.success) {
            console.log(`✅ 自动生成了 ${voiceResult.voicesCreated} 条心声`);
          } else {
            console.warn('⚠️ 心声生成失败:', voiceResult.errors);
          }
        } catch (error) {
          console.error('❌ 心声生成异常:', error);
        }

        return c.json({
          success: true,
          message: 'Questionnaire submitted successfully',
          responseId: newQuestionnaire.id,
          verified,
        });
      } else {
        // 检查是否有待审核标记（来自异步审核中间件）
        if (data._pendingModeration) {
          return c.json({
            success: true,
            message: '问卷已提交，正在审核中',
            pendingId: data._pendingModeration.id,
            sequenceNumber: data._pendingModeration.sequenceNumber,
            verified
          });
        }

        // 检查是否有自动审核结果
        if (data._autoModeration) {
          if (data._autoModeration.action === 'approve') {
            // 内容已自动通过，直接创建问卷记录
            const response = await prisma.questionnaireResponse.create({
              data: {
                userId: userId,
                isAnonymous: data.isAnonymous,
                ipAddress: ip,
                submittedById, // 添加轻量级匿名身份标识

                // 1. Personal information
                educationLevel: data.educationLevel,
                major: data.major,
                graduationYear: data.graduationYear,
                region: data.region,

                // 2. Employment expectations
                expectedPosition: data.expectedPosition,
                expectedSalaryRange: data.expectedSalaryRange,
                expectedWorkHours: data.expectedWorkHours,
                expectedVacationDays: data.expectedVacationDays,

                // 3. Work experience
                employmentStatus: data.employmentStatus,
                currentIndustry: data.currentIndustry,
                currentPosition: data.currentPosition,
                jobSatisfaction: data.jobSatisfaction,

                // 4. Unemployment status
                unemploymentDuration: data.unemploymentDuration,
                unemploymentReason: data.unemploymentReason,
                jobHuntingDifficulty: data.jobHuntingDifficulty,

                // 5. Career change and reflection
                regretMajor: data.regretMajor,
                preferredMajor: data.preferredMajor,
                careerChangeIntention: data.careerChangeIntention,
                careerChangeTarget: data.careerChangeTarget,

                // 6. Advice and feedback
                adviceForStudents: data.adviceForStudents,
                observationOnEmployment: data.observationOnEmployment,
              },
            });

            // 自动生成心声数据
            try {
              const voiceResult = await VoiceGenerationService.generateVoicesFromQuestionnaire(c, {
                id: response.id,
                userId: response.userId?.toString(),
                adviceForStudents: data.adviceForStudents,
                observationOnEmployment: data.observationOnEmployment,
                educationLevel: data.educationLevel,
                region: data.region,
                isAnonymous: data.isAnonymous
              });

              if (voiceResult.success) {
                console.log(`✅ 自动生成了 ${voiceResult.voicesCreated} 条心声`);
              } else {
                console.warn('⚠️ 心声生成失败:', voiceResult.errors);
              }
            } catch (error) {
              console.error('❌ 心声生成异常:', error);
            }

            return c.json({
              success: true,
              message: 'Questionnaire submitted successfully',
              responseId: response.id,
              verified,
            });
          } else if (data._autoModeration.action === 'review') {
            // 内容需要人工审核，创建待审核记录
            const questionnaireContent = {
              userId,
              isAnonymous: data.isAnonymous,
              ipAddress: ip,
              userAgent,
              submittedById,
              educationLevel: data.educationLevel,
              major: data.major,
              graduationYear: data.graduationYear,
              region: data.region,
              expectedPosition: data.expectedPosition,
              expectedSalaryRange: data.expectedSalaryRange,
              expectedWorkHours: data.expectedWorkHours,
              expectedVacationDays: data.expectedVacationDays,
              employmentStatus: data.employmentStatus,
              currentIndustry: data.currentIndustry,
              currentPosition: data.currentPosition,
              jobSatisfaction: data.jobSatisfaction,
              unemploymentDuration: data.unemploymentDuration,
              unemploymentReason: data.unemploymentReason,
              jobHuntingDifficulty: data.jobHuntingDifficulty,
              regretMajor: data.regretMajor,
              preferredMajor: data.preferredMajor,
              careerChangeIntention: data.careerChangeIntention,
              careerChangeTarget: data.careerChangeTarget,
              adviceForStudents: data.adviceForStudents,
              observationOnEmployment: data.observationOnEmployment
            };

            const pendingContent = await prisma.pendingContent.create({
              data: {
                sequenceNumber: generateSequenceNumber('QUESTIONNAIRE'),
                type: ContentType.QUESTIONNAIRE,
                originalContent: JSON.stringify(questionnaireContent),
                sanitizedContent: JSON.stringify(questionnaireContent),
                status: 'pending',
                originIp: ip,
                userAgent,
                flags: data._autoModeration.issues?.join(',') || '',
                priority: calculatePriority(data._autoModeration),
                aiSuggestion: 'review',
                aiConfidence: data._autoModeration.confidence,
                aiExplanation: data._autoModeration.explanation
              }
            });

            return c.json({
              success: true,
              message: '问卷已提交，等待审核',
              pendingId: pendingContent.id,
              sequenceNumber: pendingContent.sequenceNumber,
              verified
            });
          }
        }

        // 如果没有自动审核结果，直接创建问卷记录
        const response = await prisma.questionnaireResponse.create({
          data: {
            userId: userId,
            isAnonymous: data.isAnonymous,
            ipAddress: ip,
            submittedById, // 添加轻量级匿名身份标识

            // 1. Personal information
            educationLevel: data.educationLevel,
            major: data.major,
            graduationYear: data.graduationYear,
            region: data.region,

            // 2. Employment expectations
            expectedPosition: data.expectedPosition,
            expectedSalaryRange: data.expectedSalaryRange,
            expectedWorkHours: data.expectedWorkHours,
            expectedVacationDays: data.expectedVacationDays,

            // 3. Work experience
            employmentStatus: data.employmentStatus,
            currentIndustry: data.currentIndustry,
            currentPosition: data.currentPosition,
            jobSatisfaction: data.jobSatisfaction,

            // 4. Unemployment status
            unemploymentDuration: data.unemploymentDuration,
            unemploymentReason: data.unemploymentReason,
            jobHuntingDifficulty: data.jobHuntingDifficulty,

            // 5. Career change and reflection
            regretMajor: data.regretMajor,
            preferredMajor: data.preferredMajor,
            careerChangeIntention: data.careerChangeIntention,
            careerChangeTarget: data.careerChangeTarget,

            // 6. Advice and feedback
            adviceForStudents: data.adviceForStudents,
            observationOnEmployment: data.observationOnEmployment,
          },
        });

        // 自动生成心声数据
        try {
          const voiceResult = await VoiceGenerationService.generateVoicesFromQuestionnaire(c, {
            id: response.id,
            userId: response.userId?.toString(),
            adviceForStudents: data.adviceForStudents,
            observationOnEmployment: data.observationOnEmployment,
            educationLevel: data.educationLevel,
            region: data.region,
            isAnonymous: data.isAnonymous
          });

          if (voiceResult.success) {
            console.log(`✅ 自动生成了 ${voiceResult.voicesCreated} 条心声`);
          } else {
            console.warn('⚠️ 心声生成失败:', voiceResult.errors);
          }
        } catch (error) {
          console.error('❌ 心声生成异常:', error);
        }

        return c.json({
          success: true,
          message: 'Questionnaire submitted successfully',
          responseId: response.id,
          verified,
        });
      }
    } catch (error) {
      console.error('Error submitting questionnaire:', error);

      // 获取请求 ID
      const requestId = c.get('requestId') || crypto.randomUUID();

      // 记录错误
      await SecurityModule.logError(
        error instanceof Error ? error : new Error(String(error)),
        ip,
        c.env,
        {
          requestId,
          source: 'questionnaireController',
          tags: ['questionnaire', 'error']
        }
      );

      return c.json({
        success: false,
        error: 'Failed to submit questionnaire',
        code: 'SUBMISSION_ERROR'
      }, 500);
    }
  }
];

// Get questionnaire statistics
export const getQuestionnaireStats = async (c: Context<{ Bindings: Env }>) => {
  try {
    // 检查是否使用模拟数据（统计API默认使用真实数据库）
    if (shouldUseMockData()) {
      console.log('Mock data enabled: Returning mock questionnaire stats');

      // 使用模拟数据服务
      const mockData = getMockQuestionnaireStats();
      return c.json(mockData);
    }
    // Get query parameters for filtering
    const verified = c.req.query('verified') === 'true';
    const educationLevel = c.req.query('educationLevel');
    const region = c.req.query('region');
    const graduationYear = c.req.query('graduationYear')
      ? parseInt(c.req.query('graduationYear')!)
      : undefined;

    // Build filter object
    const filter: any = {};

    if (verified) {
      filter.isAnonymous = false;
    }

    if (educationLevel) {
      filter.educationLevel = educationLevel;
    }

    if (region) {
      filter.region = region;
    }

    if (graduationYear) {
      filter.graduationYear = graduationYear;
    }

    // Get total count
    const totalCount = await prisma.questionnaireResponse.count({
      where: filter,
    });

    // Get verified count
    const verifiedCount = await prisma.questionnaireResponse.count({
      where: {
        ...filter,
        isAnonymous: false,
      },
    });

    // Get anonymous count
    const anonymousCount = await prisma.questionnaireResponse.count({
      where: {
        ...filter,
        isAnonymous: true,
      },
    });

    // Get employed count
    const employedCount = await prisma.questionnaireResponse.count({
      where: {
        ...filter,
        employmentStatus: '已就业',
      },
    });

    // Get unemployed count
    const unemployedCount = await prisma.questionnaireResponse.count({
      where: {
        ...filter,
        employmentStatus: '待业中',
      },
    });

    // Get education level distribution
    const educationLevels = await prisma.questionnaireResponse.groupBy({
      by: ['educationLevel'],
      _count: true,
      where: {
        ...filter,
        educationLevel: {
          not: null,
        },
      },
    });

    // Get region distribution
    const regions = await prisma.questionnaireResponse.groupBy({
      by: ['region'],
      _count: true,
      where: {
        ...filter,
        region: {
          not: null,
        },
      },
    });

    // Get industry distribution
    const industries = await prisma.questionnaireResponse.groupBy({
      by: ['currentIndustry'],
      _count: true,
      where: {
        ...filter,
        currentIndustry: {
          not: null,
        },
      },
    });

    // Get expected salary distribution
    const expectedSalaries = await prisma.questionnaireResponse.groupBy({
      by: ['expectedSalaryRange'],
      _count: true,
      where: {
        ...filter,
        expectedSalaryRange: {
          not: null,
        },
      },
    });

    // Get graduation year distribution
    const graduationYears = await prisma.questionnaireResponse.groupBy({
      by: ['graduationYear'],
      _count: true,
      where: {
        ...filter,
        graduationYear: {
          not: null,
        },
      },
    });

    // Get unemployment duration distribution
    const unemploymentDurations = await prisma.questionnaireResponse.groupBy({
      by: ['unemploymentDuration'],
      _count: true,
      where: {
        ...filter,
        unemploymentDuration: {
          not: null,
        },
      },
    });

    // Get most common education level
    const mostCommonEducation = educationLevels.length > 0
      ? educationLevels.sort((a, b) => b._count - a._count)[0].educationLevel
      : null;

    // Get most common industry
    const mostCommonIndustry = industries.length > 0
      ? industries.sort((a, b) => b._count - a._count)[0].currentIndustry
      : null;

    // Get career change intention by education level
    const careerChanges = await Promise.all(
      ['高中/中专', '大专', '本科', '硕士', '博士'].map(async (level) => {
        const total = await prisma.questionnaireResponse.count({
          where: {
            ...filter,
            educationLevel: level,
          },
        });

        const hasIntention = await prisma.questionnaireResponse.count({
          where: {
            ...filter,
            educationLevel: level,
            careerChangeIntention: true,
          },
        });

        return {
          group: level,
          count: total,
          hasIntention,
        };
      })
    );

    // Calculate average unemployment duration
    let averageUnemploymentDuration = '未知';

    if (unemploymentDurations.length > 0) {
      const durationMap: Record<string, number> = {
        '3个月以内': 1.5,
        '3-6个月': 4.5,
        '6-12个月': 9,
        '1年以上': 18,
        '应届生尚未就业': 0,
      };

      const totalMonths = unemploymentDurations.reduce((sum, duration) => {
        const months = durationMap[duration.unemploymentDuration] || 0;
        return sum + (months * duration._count);
      }, 0);

      const totalResponses = unemploymentDurations.reduce((sum, duration) => {
        return sum + duration._count;
      }, 0);

      if (totalResponses > 0) {
        const avgMonths = totalMonths / totalResponses;
        averageUnemploymentDuration = `${avgMonths.toFixed(1)} 个月`;
      }
    }

    // 计算百分比的辅助函数
    const calculatePercentages = (items: Array<{ name: string; count: number }>, total: number) => {
      return items.map(item => ({
        code: item.name.toLowerCase().replace(/[\/\s]/g, '_'),
        name: item.name,
        count: item.count,
        percentage: total > 0 ? Math.round((item.count / total) * 100) : 0
      }));
    };

    // 格式化教育水平数据
    const formattedEducationLevels = calculatePercentages(
      educationLevels.map(level => ({
        name: level.educationLevel,
        count: level._count,
      })),
      totalCount
    );

    // 格式化地区数据
    const formattedRegions = calculatePercentages(
      regions.map(region => ({
        name: region.region,
        count: region._count,
      })),
      totalCount
    );

    // 格式化行业数据
    const formattedIndustries = calculatePercentages(
      industries.map(industry => ({
        name: industry.currentIndustry,
        count: industry._count,
      })),
      totalCount
    );

    // 格式化就业状态数据
    const formattedEmploymentStatus = [
      { code: 'employed', name: '已就业', count: employedCount, percentage: Math.round((employedCount / totalCount) * 100) },
      { code: 'unemployed', name: '待业中', count: unemployedCount, percentage: Math.round((unemployedCount / totalCount) * 100) }
    ];

    // 格式化毕业年份数据
    const formattedGraduationYears = calculatePercentages(
      graduationYears.map(year => ({
        name: year.graduationYear?.toString() || '未知',
        count: year._count,
      })),
      totalCount
    );

    return c.json({
      success: true,
      statistics: {
        totalResponses: totalCount,
        verifiedCount,
        anonymousCount,
        educationLevels: formattedEducationLevels,
        regions: formattedRegions,
        majors: [], // 暂时为空，需要从数据库获取专业数据
        industries: formattedIndustries,
        employmentStatus: formattedEmploymentStatus,
        graduationYears: formattedGraduationYears,
        salaryRanges: expectedSalaries.map(salary => ({
          range: salary.expectedSalaryRange,
          count: salary._count,
          percentage: Math.round((salary._count / totalCount) * 100)
        }))
      },
      metadata: {
        lastUpdated: new Date().toISOString(),
        dataVersion: '1.0.0',
        cacheExpiry: 30000
      }
    });
  } catch (error) {
    console.error('Error getting questionnaire statistics:', error);
    return c.json({ success: false, error: 'Failed to get statistics' }, 500);
  }
};

// Get questionnaire voices (心声)
export const getQuestionnaireVoices = async (c: Context<{ Bindings: Env }>) => {
  try {
    // 获取查询参数
    const page = parseInt(c.req.query('page') || '1');
    const pageSize = parseInt(c.req.query('pageSize') || '10');
    const sortBy = c.req.query('sortBy') || 'latest';
    const search = c.req.query('search');
    const educationLevel = c.req.query('educationLevel');
    const region = c.req.query('region');

    // 计算分页
    const offset = (page - 1) * pageSize;

    // 构建查询 - 获取有心声内容的问卷回复
    let query = `
      SELECT
        id,
        education_level,
        major,
        graduation_year,
        region,
        employment_status,
        current_industry,
        advice_for_students,
        observation_on_employment,
        created_at,
        is_anonymous
      FROM questionnaire_responses
      WHERE (advice_for_students IS NOT NULL AND advice_for_students != '')
         OR (observation_on_employment IS NOT NULL AND observation_on_employment != '')
    `;

    const queryParams: any[] = [];

    // 添加筛选条件
    if (search) {
      query += ` AND (advice_for_students LIKE ? OR observation_on_employment LIKE ?)`;
      queryParams.push(`%${search}%`, `%${search}%`);
    }

    if (educationLevel) {
      query += ` AND education_level = ?`;
      queryParams.push(educationLevel);
    }

    if (region) {
      query += ` AND region = ?`;
      queryParams.push(region);
    }

    // 添加排序
    if (sortBy === 'latest') {
      query += ` ORDER BY created_at DESC`;
    } else if (sortBy === 'oldest') {
      query += ` ORDER BY created_at ASC`;
    }

    // 添加分页
    query += ` LIMIT ? OFFSET ?`;
    queryParams.push(pageSize, offset);

    // 执行查询
    const result = await c.env.DB.prepare(query).bind(...queryParams).all();

    // 获取总数
    let countQuery = `
      SELECT COUNT(*) as total
      FROM questionnaire_responses
      WHERE (advice_for_students IS NOT NULL AND advice_for_students != '')
         OR (observation_on_employment IS NOT NULL AND observation_on_employment != '')
    `;

    const countParams: any[] = [];
    if (search) {
      countQuery += ` AND (advice_for_students LIKE ? OR observation_on_employment LIKE ?)`;
      countParams.push(`%${search}%`, `%${search}%`);
    }
    if (educationLevel) {
      countQuery += ` AND education_level = ?`;
      countParams.push(educationLevel);
    }
    if (region) {
      countQuery += ` AND region = ?`;
      countParams.push(region);
    }

    const countResult = await c.env.DB.prepare(countQuery).bind(...countParams).first();
    const totalCount = countResult?.total || 0;

    // 格式化心声数据
    const voices = result.results?.map((row: any) => {
      const voices = [];

      if (row.advice_for_students && row.advice_for_students.trim()) {
        voices.push({
          type: 'advice',
          content: row.advice_for_students,
          title: '给学弟学妹的建议'
        });
      }

      if (row.observation_on_employment && row.observation_on_employment.trim()) {
        voices.push({
          type: 'observation',
          content: row.observation_on_employment,
          title: '对就业形势的观察'
        });
      }

      return {
        id: row.id,
        voices,
        author: row.is_anonymous ? '匿名用户' : '已验证用户',
        educationLevel: row.education_level,
        major: row.major,
        graduationYear: row.graduation_year,
        region: row.region,
        employmentStatus: row.employment_status,
        currentIndustry: row.current_industry,
        createdAt: row.created_at,
        isAnonymous: row.is_anonymous
      };
    }) || [];

    // 计算分页信息
    const totalPages = Math.ceil(totalCount / pageSize);

    return c.json({
      success: true,
      voices,
      totalCount,
      totalPages,
      currentPage: page,
      pageSize,
      hasMore: page < totalPages
    });

  } catch (error) {
    console.error('Error getting questionnaire voices:', error);
    return c.json({
      success: false,
      error: 'Failed to get questionnaire voices',
      details: error instanceof Error ? error.message : 'Unknown error'
    }, 500);
  }
};

// Get questionnaire realtime statistics (for QuestionnaireRealtimeStats component)
export const getQuestionnaireRealtimeStats = async (c: Context<{ Bindings: Env }>) => {
  try {
    // 检查是否使用模拟数据（实时统计API默认使用真实数据库）
    if (shouldUseMockData()) {
      console.log('Mock data enabled: Returning mock realtime stats');
      const mockData = getMockRealtimeStats();
      return c.json(mockData);
    }

    // 获取基础统计
    const totalSubmissions = await prisma.questionnaireResponse.count();

    // 获取教育水平分布
    const educationLevels = await prisma.questionnaireResponse.groupBy({
      by: ['educationLevel'],
      _count: true,
      where: {
        educationLevel: {
          not: null,
        },
      },
    });

    // 获取地区分布
    const regions = await prisma.questionnaireResponse.groupBy({
      by: ['region'],
      _count: true,
      where: {
        region: {
          not: null,
        },
      },
    });

    // 获取就业状态分布
    const employmentStatuses = await prisma.questionnaireResponse.groupBy({
      by: ['employmentStatus'],
      _count: true,
      where: {
        employmentStatus: {
          not: null,
        },
      },
    });

    // 获取毕业年份分布
    const graduationYears = await prisma.questionnaireResponse.groupBy({
      by: ['graduationYear'],
      _count: true,
      where: {
        graduationYear: {
          not: null,
        },
      },
    });

    // 计算百分比的辅助函数
    const calculatePercentages = (items: Array<{ name: string; count: number }>, total: number) => {
      return items.map(item => ({
        code: item.name.toLowerCase().replace(/[\/\s]/g, '_'),
        name: item.name,
        count: item.count,
        percentage: total > 0 ? Math.round((item.count / total) * 100) : 0
      }));
    };

    // 格式化数据
    const formattedEducationLevels = calculatePercentages(
      educationLevels.map(level => ({
        name: level.educationLevel,
        count: level._count,
      })),
      totalSubmissions
    );

    const formattedRegions = calculatePercentages(
      regions.map(region => ({
        name: region.region,
        count: region._count,
      })),
      totalSubmissions
    );

    const formattedEmploymentStatus = calculatePercentages(
      employmentStatuses.map(status => ({
        name: status.employmentStatus,
        count: status._count,
      })),
      totalSubmissions
    );

    const formattedGraduationYears = calculatePercentages(
      graduationYears.map(year => ({
        name: year.graduationYear?.toString() || '未知',
        count: year._count,
      })),
      totalSubmissions
    );

    return c.json({
      success: true,
      totalSubmissions,
      questionStats: {
        education_level: formattedEducationLevels,
        region: formattedRegions,
        employment_status: formattedEmploymentStatus,
        graduation_year: formattedGraduationYears
      },
      metadata: {
        lastUpdated: new Date().toISOString(),
        refreshInterval: 30000
      }
    });

  } catch (error) {
    console.error('Error getting questionnaire realtime statistics:', error);
    return c.json({ success: false, error: 'Failed to get realtime statistics' }, 500);
  }
};
