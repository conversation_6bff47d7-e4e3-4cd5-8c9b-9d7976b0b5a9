/**
 * 自动审核中间件
 *
 * 为内容提交路由添加自动审核功能
 */

import { Context, Next } from 'hono';
import { Env } from '../../types';
import { ContentType } from '../../constants';
import { AutoModerationService } from './autoModerationService';
import { AsyncModerationService } from './asyncModerationService';
import { PrismaClient } from '@prisma/client';

// 创建Prisma客户端
const prisma = new PrismaClient();

/**
 * 创建自动审核中间件
 * @param contentType 内容类型
 * @param useAsync 是否使用异步处理
 * @returns 中间件函数
 */
export function createAutoModerationMiddleware(contentType: ContentType, useAsync: boolean = true) {
  return async (c: Context<{ Bindings: Env }>, next: Next) => {
    // 获取请求体
    const body = await c.req.json();

    // 保存原始请求体，以便后续处理
    c.set('originalBody', body);

    // 获取自动审核服务实例
    const autoModerationService = AutoModerationService.getInstance();

    // 确保服务已初始化
    if (!autoModerationService.isInitialized()) {
      await autoModerationService.initialize();
    }

    // 获取配置
    const config = autoModerationService.getConfig();

    // 如果自动审核未启用，直接继续处理请求
    if (!config.enabled) {
      await next();
      return;
    }

    // 如果使用异步处理
    if (useAsync) {
      try {
        // 创建待审核记录
        const pendingContent = await prisma.pendingContent.create({
          data: {
            sequenceNumber: generateSequenceNumber(contentType),
            type: contentType,
            originalContent: JSON.stringify(body),
            sanitizedContent: JSON.stringify(body),
            status: 'pending',
            originIp: c.req.header('CF-Connecting-IP') || c.req.header('X-Forwarded-For') || 'unknown',
            userAgent: c.req.header('User-Agent') || 'unknown'
          }
        });

        // 获取异步审核服务实例
        const asyncModerationService = AsyncModerationService.getInstance();

        // 确保服务已初始化
        if (!asyncModerationService.isInitialized()) {
          await asyncModerationService.initialize();
        }

        // 异步执行审核
        c.executionCtx.waitUntil(
          asyncModerationService.moderateContentAsync(pendingContent.id, body, contentType, c.env)
        );

        // 修改请求体，添加待审核标记
        const modifiedBody = {
          ...body,
          _pendingModeration: {
            id: pendingContent.id,
            sequenceNumber: pendingContent.sequenceNumber
          }
        };

        // 替换请求体
        c.req.raw = new Request(c.req.url, {
          method: c.req.method,
          headers: c.req.raw.headers,
          body: JSON.stringify(modifiedBody)
        });

        // 继续处理请求
        await next();
        return;
      } catch (error) {
        console.error('创建待审核记录失败:', error);

        // 出错时，回退到同步处理
        console.log('回退到同步处理模式');
      }
    }

    // 同步处理（当异步处理失败或未启用时）
    try {
      // 执行自动审核
      const result = await autoModerationService.moderateContent(body, contentType, c.env);

      // 将审核结果添加到上下文中
      c.set('autoModerationResult', result);

      // 如果审核结果为拒绝，直接返回响应
      if (result.action === 'reject') {
        return c.json({
          success: false,
          error: '内容未通过自动审核',
          message: result.message,
          issues: result.moderationResult.issues,
          explanation: result.moderationResult.explanation
        }, 400);
      }

      // 如果审核结果为人工审核，修改请求体，添加审核标记
      if (result.action === 'review') {
        // 修改请求体，添加审核标记
        const modifiedBody = {
          ...body,
          _autoModeration: {
            action: 'review',
            issues: result.moderationResult.issues,
            explanation: result.moderationResult.explanation,
            confidence: result.moderationResult.confidence,
            severity: result.moderationResult.severity
          }
        };

        // 替换请求体
        c.req.raw = new Request(c.req.url, {
          method: c.req.method,
          headers: c.req.raw.headers,
          body: JSON.stringify(modifiedBody)
        });
      }

      // 如果审核结果为通过，修改请求体，添加审核标记
      if (result.action === 'approve') {
        // 修改请求体，添加审核标记
        const modifiedBody = {
          ...body,
          _autoModeration: {
            action: 'approve',
            confidence: result.moderationResult.confidence
          }
        };

        // 替换请求体
        c.req.raw = new Request(c.req.url, {
          method: c.req.method,
          headers: c.req.raw.headers,
          body: JSON.stringify(modifiedBody)
        });
      }
    } catch (error) {
      console.error('自动审核中间件错误:', error);

      // 出错时，添加错误标记，但继续处理请求
      const modifiedBody = {
        ...body,
        _autoModeration: {
          error: true,
          message: error instanceof Error ? error.message : String(error)
        }
      };

      // 替换请求体
      c.req.raw = new Request(c.req.url, {
        method: c.req.method,
        headers: c.req.raw.headers,
        body: JSON.stringify(modifiedBody)
      });
    }

    // 继续处理请求
    await next();
  };
}

/**
 * 生成序列号
 * @param contentType 内容类型
 * @returns 序列号
 */
function generateSequenceNumber(contentType: ContentType): string {
  const prefix = contentType.charAt(0).toUpperCase(); // 首字母大写
  const year = new Date().getFullYear().toString().substring(2);
  const month = (new Date().getMonth() + 1).toString().padStart(2, '0');
  const day = new Date().getDate().toString().padStart(2, '0');
  const random = Math.floor(Math.random() * 10000).toString().padStart(4, '0');

  return `${prefix}${year}${month}${day}${random}`;
}

/**
 * 故事自动审核中间件
 */
export const storyAutoModerationMiddleware = createAutoModerationMiddleware(ContentType.STORY, true);

/**
 * 问卷自动审核中间件
 */
export const questionnaireAutoModerationMiddleware = createAutoModerationMiddleware(ContentType.QUESTIONNAIRE, true);

/**
 * 评论自动审核中间件
 */
export const commentAutoModerationMiddleware = createAutoModerationMiddleware(ContentType.COMMENT, true);

/**
 * 个人资料自动审核中间件
 */
export const profileAutoModerationMiddleware = createAutoModerationMiddleware(ContentType.PROFILE, true);

/**
 * 反馈自动审核中间件
 */
export const feedbackAutoModerationMiddleware = createAutoModerationMiddleware(ContentType.FEEDBACK, true);

/**
 * 同步故事自动审核中间件
 */
export const syncStoryAutoModerationMiddleware = createAutoModerationMiddleware(ContentType.STORY, false);

/**
 * 同步问卷自动审核中间件
 */
export const syncQuestionnaireAutoModerationMiddleware = createAutoModerationMiddleware(ContentType.QUESTIONNAIRE, false);
