/**
 * API性能监控和优化工具
 */

// 性能指标收集
const performanceMetrics = {
  // 请求计数
  totalRequests: 0,
  successfulRequests: 0,
  failedRequests: 0,
  
  // 响应时间（毫秒）
  responseTimeTotal: 0,
  responseTimeMin: Number.MAX_SAFE_INTEGER,
  responseTimeMax: 0,
  responseTimeAvg: 0,
  
  // 路由性能
  routeMetrics: {},
  
  // 数据库性能
  dbQueryCount: 0,
  dbQueryTimeTotal: 0,
  dbQueryTimeMin: Number.MAX_SAFE_INTEGER,
  dbQueryTimeMax: 0,
  dbQueryTimeAvg: 0,
  
  // 缓存性能
  cacheHits: 0,
  cacheMisses: 0,
  cacheHitRate: 0,
  
  // 内存使用
  memoryUsage: {},
  
  // 启动时间
  startTime: Date.now(),
  
  // 最近的请求
  recentRequests: []
};

// 最大保存的最近请求数
const MAX_RECENT_REQUESTS = 100;

/**
 * 记录请求开始
 * @param {Request} request 请求对象
 * @returns {number} 请求开始时间戳
 */
function recordRequestStart(request) {
  const startTime = Date.now();
  
  // 存储请求开始时间
  request.startTime = startTime;
  
  // 更新请求计数
  performanceMetrics.totalRequests++;
  
  return startTime;
}

/**
 * 记录请求结束
 * @param {Request} request 请求对象
 * @param {Response} response 响应对象
 * @param {Error} [error] 错误对象（如果有）
 */
function recordRequestEnd(request, response, error = null) {
  const endTime = Date.now();
  const startTime = request.startTime || endTime;
  const responseTime = endTime - startTime;
  
  // 提取请求信息
  const method = request.method;
  const url = new URL(request.url);
  const path = url.pathname;
  const status = response.status;
  
  // 更新响应时间指标
  performanceMetrics.responseTimeTotal += responseTime;
  performanceMetrics.responseTimeMin = Math.min(performanceMetrics.responseTimeMin, responseTime);
  performanceMetrics.responseTimeMax = Math.max(performanceMetrics.responseTimeMax, responseTime);
  performanceMetrics.responseTimeAvg = performanceMetrics.responseTimeTotal / performanceMetrics.totalRequests;
  
  // 更新请求成功/失败计数
  if (error || status >= 400) {
    performanceMetrics.failedRequests++;
  } else {
    performanceMetrics.successfulRequests++;
  }
  
  // 更新路由指标
  if (!performanceMetrics.routeMetrics[path]) {
    performanceMetrics.routeMetrics[path] = {
      count: 0,
      timeTotal: 0,
      timeMin: Number.MAX_SAFE_INTEGER,
      timeMax: 0,
      timeAvg: 0,
      statusCodes: {}
    };
  }
  
  const routeMetric = performanceMetrics.routeMetrics[path];
  routeMetric.count++;
  routeMetric.timeTotal += responseTime;
  routeMetric.timeMin = Math.min(routeMetric.timeMin, responseTime);
  routeMetric.timeMax = Math.max(routeMetric.timeMax, responseTime);
  routeMetric.timeAvg = routeMetric.timeTotal / routeMetric.count;
  
  // 记录状态码
  const statusStr = status.toString();
  routeMetric.statusCodes[statusStr] = (routeMetric.statusCodes[statusStr] || 0) + 1;
  
  // 更新内存使用情况
  if (typeof process !== 'undefined' && process.memoryUsage) {
    performanceMetrics.memoryUsage = process.memoryUsage();
  }
  
  // 记录最近的请求
  const requestInfo = {
    method,
    path,
    status,
    responseTime,
    timestamp: endTime,
    error: error ? error.message : null
  };
  
  performanceMetrics.recentRequests.unshift(requestInfo);
  
  // 限制最近请求的数量
  if (performanceMetrics.recentRequests.length > MAX_RECENT_REQUESTS) {
    performanceMetrics.recentRequests.pop();
  }
}

/**
 * 记录数据库查询
 * @param {string} query 查询语句
 * @param {number} time 查询时间（毫秒）
 */
function recordDbQuery(query, time) {
  // 更新数据库查询指标
  performanceMetrics.dbQueryCount++;
  performanceMetrics.dbQueryTimeTotal += time;
  performanceMetrics.dbQueryTimeMin = Math.min(performanceMetrics.dbQueryTimeMin, time);
  performanceMetrics.dbQueryTimeMax = Math.max(performanceMetrics.dbQueryTimeMax, time);
  performanceMetrics.dbQueryTimeAvg = performanceMetrics.dbQueryTimeTotal / performanceMetrics.dbQueryCount;
}

/**
 * 记录缓存命中或未命中
 * @param {boolean} hit 是否命中缓存
 */
function recordCacheAccess(hit) {
  if (hit) {
    performanceMetrics.cacheHits++;
  } else {
    performanceMetrics.cacheMisses++;
  }
  
  const totalAccesses = performanceMetrics.cacheHits + performanceMetrics.cacheMisses;
  performanceMetrics.cacheHitRate = totalAccesses > 0 
    ? performanceMetrics.cacheHits / totalAccesses 
    : 0;
}

/**
 * 获取性能指标
 * @returns {Object} 性能指标对象
 */
function getPerformanceMetrics() {
  return { ...performanceMetrics };
}

/**
 * 重置性能指标
 */
function resetPerformanceMetrics() {
  // 保留启动时间
  const startTime = performanceMetrics.startTime;
  
  // 重置所有指标
  Object.keys(performanceMetrics).forEach(key => {
    if (key === 'startTime') {
      return;
    } else if (key === 'routeMetrics') {
      performanceMetrics[key] = {};
    } else if (key === 'recentRequests') {
      performanceMetrics[key] = [];
    } else if (key === 'memoryUsage') {
      performanceMetrics[key] = typeof process !== 'undefined' && process.memoryUsage 
        ? process.memoryUsage() 
        : {};
    } else if (typeof performanceMetrics[key] === 'number') {
      performanceMetrics[key] = key.includes('Min') ? Number.MAX_SAFE_INTEGER : 0;
    }
  });
  
  // 恢复启动时间
  performanceMetrics.startTime = startTime;
}

/**
 * 测量函数执行时间
 * @param {Function} fn 要测量的函数
 * @param {Array} args 函数参数
 * @returns {Array} [结果, 执行时间]
 */
async function measureExecutionTime(fn, ...args) {
  const startTime = Date.now();
  try {
    const result = await fn(...args);
    const endTime = Date.now();
    return [result, endTime - startTime];
  } catch (error) {
    const endTime = Date.now();
    error.executionTime = endTime - startTime;
    throw error;
  }
}

/**
 * 创建性能中间件
 * @returns {Function} Express/Cloudflare Workers 中间件
 */
function createPerformanceMiddleware() {
  return async (request, env, ctx) => {
    // 记录请求开始
    recordRequestStart(request);
    
    try {
      // 执行请求处理
      const response = await ctx.next();
      
      // 记录请求结束
      recordRequestEnd(request, response);
      
      return response;
    } catch (error) {
      // 创建错误响应
      const errorResponse = new Response(JSON.stringify({
        success: false,
        error: error.message || 'Internal Server Error'
      }), {
        status: 500,
        headers: {
          'Content-Type': 'application/json'
        }
      });
      
      // 记录请求结束（带错误）
      recordRequestEnd(request, errorResponse, error);
      
      return errorResponse;
    }
  };
}

/**
 * 生成性能报告
 * @returns {string} 格式化的性能报告
 */
function generatePerformanceReport() {
  const metrics = getPerformanceMetrics();
  const uptime = Date.now() - metrics.startTime;
  
  let report = '=== API性能报告 ===\n\n';
  
  // 基本信息
  report += `运行时间: ${formatDuration(uptime)}\n`;
  report += `总请求数: ${metrics.totalRequests}\n`;
  report += `成功请求: ${metrics.successfulRequests} (${formatPercentage(metrics.successfulRequests, metrics.totalRequests)})\n`;
  report += `失败请求: ${metrics.failedRequests} (${formatPercentage(metrics.failedRequests, metrics.totalRequests)})\n\n`;
  
  // 响应时间
  report += '响应时间:\n';
  report += `  平均: ${metrics.responseTimeAvg.toFixed(2)}ms\n`;
  report += `  最小: ${metrics.responseTimeMin === Number.MAX_SAFE_INTEGER ? 'N/A' : `${metrics.responseTimeMin}ms`}\n`;
  report += `  最大: ${metrics.responseTimeMax}ms\n\n`;
  
  // 路由性能
  report += '路由性能 (按平均响应时间排序):\n';
  const sortedRoutes = Object.entries(metrics.routeMetrics)
    .sort((a, b) => b[1].timeAvg - a[1].timeAvg);
  
  sortedRoutes.forEach(([path, routeMetric]) => {
    report += `  ${path}:\n`;
    report += `    请求数: ${routeMetric.count}\n`;
    report += `    平均响应时间: ${routeMetric.timeAvg.toFixed(2)}ms\n`;
    report += `    状态码分布: ${formatStatusCodes(routeMetric.statusCodes)}\n`;
  });
  
  // 数据库性能
  if (metrics.dbQueryCount > 0) {
    report += '\n数据库性能:\n';
    report += `  查询数: ${metrics.dbQueryCount}\n`;
    report += `  平均查询时间: ${metrics.dbQueryTimeAvg.toFixed(2)}ms\n`;
    report += `  最小查询时间: ${metrics.dbQueryTimeMin === Number.MAX_SAFE_INTEGER ? 'N/A' : `${metrics.dbQueryTimeMin}ms`}\n`;
    report += `  最大查询时间: ${metrics.dbQueryTimeMax}ms\n`;
  }
  
  // 缓存性能
  const totalCacheAccesses = metrics.cacheHits + metrics.cacheMisses;
  if (totalCacheAccesses > 0) {
    report += '\n缓存性能:\n';
    report += `  命中率: ${(metrics.cacheHitRate * 100).toFixed(2)}%\n`;
    report += `  命中数: ${metrics.cacheHits}\n`;
    report += `  未命中数: ${metrics.cacheMisses}\n`;
  }
  
  // 内存使用
  if (Object.keys(metrics.memoryUsage).length > 0) {
    report += '\n内存使用:\n';
    Object.entries(metrics.memoryUsage).forEach(([key, value]) => {
      report += `  ${key}: ${formatBytes(value)}\n`;
    });
  }
  
  return report;
}

// 辅助函数

/**
 * 格式化百分比
 * @param {number} part 部分
 * @param {number} total 总数
 * @returns {string} 格式化的百分比
 */
function formatPercentage(part, total) {
  if (total === 0) return '0%';
  return `${((part / total) * 100).toFixed(2)}%`;
}

/**
 * 格式化状态码分布
 * @param {Object} statusCodes 状态码计数对象
 * @returns {string} 格式化的状态码分布
 */
function formatStatusCodes(statusCodes) {
  return Object.entries(statusCodes)
    .map(([code, count]) => `${code}: ${count}`)
    .join(', ');
}

/**
 * 格式化字节数
 * @param {number} bytes 字节数
 * @returns {string} 格式化的字节数
 */
function formatBytes(bytes) {
  if (bytes === 0) return '0 B';
  
  const units = ['B', 'KB', 'MB', 'GB', 'TB'];
  const i = Math.floor(Math.log(bytes) / Math.log(1024));
  
  return `${(bytes / Math.pow(1024, i)).toFixed(2)} ${units[i]}`;
}

/**
 * 格式化持续时间
 * @param {number} ms 毫秒数
 * @returns {string} 格式化的持续时间
 */
function formatDuration(ms) {
  const seconds = Math.floor(ms / 1000);
  const minutes = Math.floor(seconds / 60);
  const hours = Math.floor(minutes / 60);
  const days = Math.floor(hours / 24);
  
  if (days > 0) {
    return `${days}天 ${hours % 24}小时`;
  } else if (hours > 0) {
    return `${hours}小时 ${minutes % 60}分钟`;
  } else if (minutes > 0) {
    return `${minutes}分钟 ${seconds % 60}秒`;
  } else {
    return `${seconds}秒`;
  }
}

module.exports = {
  recordRequestStart,
  recordRequestEnd,
  recordDbQuery,
  recordCacheAccess,
  getPerformanceMetrics,
  resetPerformanceMetrics,
  measureExecutionTime,
  createPerformanceMiddleware,
  generatePerformanceReport
};
