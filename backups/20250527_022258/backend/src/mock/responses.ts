/**
 * 问卷回复模拟数据
 */

export interface MockResponse {
  id: number;
  sequenceNumber: string;
  isAnonymous: boolean;
  educationLevel?: string;
  major?: string;
  graduationYear?: number;
  region?: string;
  employmentStatus?: string;
  industry?: string;
  position?: string;
  salary?: string;
  jobSatisfaction?: number;
  unemploymentDuration?: string;
  careerChangeIntention?: boolean;
  challenges?: string;
  suggestions?: string;
  createdAt: string;
  updatedAt?: string;
  ipAddress?: string;
  [key: string]: any;
}

/**
 * 模拟问卷回复数据
 */
export const mockResponses: MockResponse[] = [
  {
    id: 1,
    sequenceNumber: 'Q-00001',
    isAnonymous: false,
    educationLevel: '本科',
    major: '计算机科学与技术',
    graduationYear: 2023,
    region: '北京',
    employmentStatus: '已就业',
    industry: '互联网',
    position: '软件工程师',
    salary: '15k-20k',
    jobSatisfaction: 4,
    careerChangeIntention: false,
    challenges: '技术更新太快，需要不断学习新知识',
    suggestions: '建议学校增加实践课程，减少理论课程',
    createdAt: new Date(Date.now() - 30 * 86400000).toISOString(),
    updatedAt: new Date(Date.now() - 30 * 86400000).toISOString(),
    ipAddress: '***********',
    expectedPosition: '软件工程师',
    expectedSalaryRange: '15k-20k',
    expectedWorkHours: 40,
    expectedVacationDays: 15,
    currentIndustry: '互联网',
    currentPosition: '软件工程师',
    regretMajor: false,
    preferredMajor: '',
    adviceForStudents: '多参与实际项目，提前了解行业动态',
    observationOnEmployment: '就业形势总体良好，但竞争激烈'
  },
  {
    id: 2,
    sequenceNumber: 'Q-00002',
    isAnonymous: true,
    educationLevel: '硕士',
    major: '人工智能',
    graduationYear: 2022,
    region: '上海',
    employmentStatus: '已就业',
    industry: '科技',
    position: '算法工程师',
    salary: '25k-30k',
    jobSatisfaction: 5,
    careerChangeIntention: false,
    challenges: '工作压力大，经常加班',
    suggestions: '希望学校能够提供更多实习机会',
    createdAt: new Date(Date.now() - 25 * 86400000).toISOString(),
    updatedAt: new Date(Date.now() - 25 * 86400000).toISOString(),
    ipAddress: '***********',
    expectedPosition: '算法工程师',
    expectedSalaryRange: '20k-25k',
    expectedWorkHours: 45,
    expectedVacationDays: 10,
    currentIndustry: '科技',
    currentPosition: '算法工程师',
    regretMajor: false,
    preferredMajor: '',
    adviceForStudents: '深入学习数学和编程基础，多做项目',
    observationOnEmployment: '人工智能领域人才需求大，但要求也高'
  },
  {
    id: 3,
    sequenceNumber: 'Q-00003',
    isAnonymous: false,
    educationLevel: '本科',
    major: '市场营销',
    graduationYear: 2023,
    region: '广州',
    employmentStatus: '待业',
    unemploymentDuration: '3-6个月',
    careerChangeIntention: true,
    challenges: '专业不对口，找工作困难',
    suggestions: '希望学校能够提供更多就业指导',
    createdAt: new Date(Date.now() - 20 * 86400000).toISOString(),
    updatedAt: new Date(Date.now() - 20 * 86400000).toISOString(),
    ipAddress: '***********',
    expectedPosition: '产品经理',
    expectedSalaryRange: '10k-15k',
    expectedWorkHours: 40,
    expectedVacationDays: 15,
    regretMajor: true,
    preferredMajor: '计算机科学',
    adviceForStudents: '选择专业要慎重，考虑就业前景',
    observationOnEmployment: '传统行业就业压力大，建议向新兴行业转型'
  },
  {
    id: 4,
    sequenceNumber: 'Q-00004',
    isAnonymous: true,
    educationLevel: '硕士',
    major: '金融学',
    graduationYear: 2022,
    region: '深圳',
    employmentStatus: '已就业',
    industry: '金融',
    position: '投资分析师',
    salary: '20k-25k',
    jobSatisfaction: 3,
    careerChangeIntention: true,
    challenges: '工作压力大，晋升空间有限',
    suggestions: '希望学校能够加强实践教学',
    createdAt: new Date(Date.now() - 15 * 86400000).toISOString(),
    updatedAt: new Date(Date.now() - 15 * 86400000).toISOString(),
    ipAddress: '***********',
    expectedPosition: '投资经理',
    expectedSalaryRange: '25k-30k',
    expectedWorkHours: 50,
    expectedVacationDays: 10,
    currentIndustry: '金融',
    currentPosition: '投资分析师',
    regretMajor: false,
    preferredMajor: '',
    adviceForStudents: '多参与实习，了解行业实际情况',
    observationOnEmployment: '金融行业竞争激烈，需要持续学习'
  },
  {
    id: 5,
    sequenceNumber: 'Q-00005',
    isAnonymous: false,
    educationLevel: '博士',
    major: '生物医学工程',
    graduationYear: 2021,
    region: '杭州',
    employmentStatus: '已就业',
    industry: '医疗健康',
    position: '研发工程师',
    salary: '30k-35k',
    jobSatisfaction: 5,
    careerChangeIntention: false,
    challenges: '研发周期长，成果转化难',
    suggestions: '加强产学研合作，促进成果转化',
    createdAt: new Date(Date.now() - 10 * 86400000).toISOString(),
    updatedAt: new Date(Date.now() - 10 * 86400000).toISOString(),
    ipAddress: '***********',
    expectedPosition: '研发主管',
    expectedSalaryRange: '30k-35k',
    expectedWorkHours: 40,
    expectedVacationDays: 15,
    currentIndustry: '医疗健康',
    currentPosition: '研发工程师',
    regretMajor: false,
    preferredMajor: '',
    adviceForStudents: '选择热爱的领域，坚持深入研究',
    observationOnEmployment: '医疗健康领域前景广阔，但需要专业知识深厚'
  }
];
