import React, { useState, useEffect } from 'react';
import SuperAdminLayout from '@/components/layouts/SuperAdminLayout';
import OptimizedContentReviewPanel from '@/components/admin/OptimizedContentReviewPanel';
import CacheManagementPanel from '@/components/admin/CacheManagementPanel';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import {
  Zap,
  Database,
  BarChart3,
  Activity,
  Clock,
  HardDrive,
  Cpu,
  RefreshCw,
  TrendingUp,
  AlertTriangle,
  CheckCircle,
  Settings,
  Monitor
} from 'lucide-react';
import { useToast } from '@/components/ui/use-toast';
import { performanceMonitor, getPerformanceMetrics, generatePerformanceReport } from '@/utils/performance';
import { globalCache } from '@/utils/cache';
import { chunkProcessor } from '@/utils/bigDataOptimization';

/**
 * 性能优化页面
 *
 * 集成所有性能优化功能：
 * - 虚拟滚动演示
 * - 缓存管理
 * - 性能监控
 * - 大数据处理优化
 */
const PerformanceOptimizationPage: React.FC = () => {
  const { toast } = useToast();
  const [activeTab, setActiveTab] = useState('overview');

  // 性能指标状态
  const [performanceStats, setPerformanceStats] = useState({
    memoryUsage: 0,
    memoryLimit: 0,
    memoryUsagePercent: 0,
    cacheHitRate: 0,
    averageRenderTime: 0,
    totalCacheSize: 0,
    activeConnections: 0,
    cpuUsage: 0
  });

  // 优化建议
  const [optimizationTips, setOptimizationTips] = useState<string[]>([]);

  // 性能测试状态
  const [isRunningTest, setIsRunningTest] = useState(false);
  const [testResults, setTestResults] = useState<{
    virtualScrollTest: number;
    cacheTest: number;
    chunkProcessingTest: number;
    overallScore: number;
  } | null>(null);

  // 更新性能统计
  const updatePerformanceStats = () => {
    try {
      const metrics = getPerformanceMetrics();
      const cacheStats = globalCache.getStats();

      if (typeof window !== 'undefined' && 'memory' in performance) {
        const memory = (performance as any).memory;
        const memoryUsage = memory.usedJSHeapSize;
        const memoryLimit = memory.jsHeapSizeLimit;

        setPerformanceStats({
          memoryUsage: memoryUsage / 1024 / 1024, // MB
          memoryLimit: memoryLimit / 1024 / 1024, // MB
          memoryUsagePercent: (memoryUsage / memoryLimit) * 100,
          cacheHitRate: cacheStats.hitRate * 100,
          averageRenderTime: metrics.componentRenderTime ?
            Object.values(metrics.componentRenderTime).reduce((a, b) => a + b, 0) /
            Object.values(metrics.componentRenderTime).length : 0,
          totalCacheSize: cacheStats.totalSize / 1024 / 1024, // MB
          activeConnections: 0, // 这里可以集成实际的连接数统计
          cpuUsage: 0 // 这里可以集成实际的CPU使用率
        });
      } else {
        // 如果无法获取内存信息，使用默认值
        setPerformanceStats({
          memoryUsage: 0,
          memoryLimit: 0,
          memoryUsagePercent: 0,
          cacheHitRate: cacheStats.hitRate * 100,
          averageRenderTime: metrics.componentRenderTime ?
            Object.values(metrics.componentRenderTime).reduce((a, b) => a + b, 0) /
            Object.values(metrics.componentRenderTime).length : 0,
          totalCacheSize: cacheStats.totalSize / 1024 / 1024, // MB
          activeConnections: 0,
          cpuUsage: 0
        });
      }
    } catch (error) {
      console.error('更新性能统计失败:', error);
      // 设置安全的默认值
      setPerformanceStats({
        memoryUsage: 0,
        memoryLimit: 0,
        memoryUsagePercent: 0,
        cacheHitRate: 0,
        averageRenderTime: 0,
        totalCacheSize: 0,
        activeConnections: 0,
        cpuUsage: 0
      });
    }
  };

  // 运行性能测试
  const runPerformanceTest = async () => {
    setIsRunningTest(true);

    try {
      // 虚拟滚动性能测试
      const virtualScrollStart = performance.now();
      const largeArray = Array.from({ length: 10000 }, (_, i) => ({ id: i, data: `Item ${i}` }));
      // 模拟虚拟滚动渲染
      await new Promise(resolve => setTimeout(resolve, 100));
      const virtualScrollTime = performance.now() - virtualScrollStart;

      // 缓存性能测试
      const cacheStart = performance.now();
      for (let i = 0; i < 1000; i++) {
        globalCache.set(`test-${i}`, { data: `Test data ${i}` });
        globalCache.get(`test-${i}`);
      }
      const cacheTime = performance.now() - cacheStart;

      // 分片处理性能测试
      const chunkStart = performance.now();
      await chunkProcessor.process(
        Array.from({ length: 5000 }, (_, i) => i),
        async (chunk) => chunk.map(x => x * 2),
        { chunkSize: 100, delay: 1 }
      );
      const chunkTime = performance.now() - chunkStart;

      // 计算综合评分
      const virtualScrollScore = Math.max(0, 100 - virtualScrollTime);
      const cacheScore = Math.max(0, 100 - cacheTime / 10);
      const chunkScore = Math.max(0, 100 - chunkTime / 100);
      const overallScore = (virtualScrollScore + cacheScore + chunkScore) / 3;

      setTestResults({
        virtualScrollTest: virtualScrollScore,
        cacheTest: cacheScore,
        chunkProcessingTest: chunkScore,
        overallScore
      });

      // 清理测试数据
      for (let i = 0; i < 1000; i++) {
        globalCache.remove(`test-${i}`);
      }

      toast({
        title: '性能测试完成',
        description: `综合评分: ${overallScore.toFixed(1)}分`,
      });
    } catch (error) {
      console.error('性能测试失败:', error);
      toast({
        title: '性能测试失败',
        description: '测试过程中发生错误',
        variant: 'destructive',
      });
    } finally {
      setIsRunningTest(false);
    }
  };

  // 生成优化建议
  const generateOptimizationTips = () => {
    const tips: string[] = [];

    if (performanceStats.memoryUsagePercent > 80) {
      tips.push('内存使用率过高，建议清理缓存或优化数据结构');
    }

    if (performanceStats.cacheHitRate < 50) {
      tips.push('缓存命中率较低，建议优化缓存策略');
    }

    if (performanceStats.averageRenderTime > 100) {
      tips.push('组件渲染时间过长，建议使用虚拟滚动或优化渲染逻辑');
    }

    if (performanceStats.totalCacheSize > 50) {
      tips.push('缓存占用空间过大，建议定期清理过期缓存');
    }

    if (tips.length === 0) {
      tips.push('系统性能良好，可以考虑进一步的预加载优化');
    }

    setOptimizationTips(tips);
  };

  // 获取性能等级
  const getPerformanceGrade = (score: number): { grade: string; color: string } => {
    if (score >= 90) return { grade: 'A+', color: 'text-green-600' };
    if (score >= 80) return { grade: 'A', color: 'text-green-500' };
    if (score >= 70) return { grade: 'B', color: 'text-blue-500' };
    if (score >= 60) return { grade: 'C', color: 'text-yellow-500' };
    return { grade: 'D', color: 'text-red-500' };
  };

  // 定期更新统计
  useEffect(() => {
    const interval = setInterval(() => {
      updatePerformanceStats();
      generateOptimizationTips();
    }, 5000);

    // 初始更新
    updatePerformanceStats();
    generateOptimizationTips();

    return () => clearInterval(interval);
  }, []);

  return (
    <SuperAdminLayout>
      <div className="container mx-auto py-6 space-y-6">
        {/* 页面头部 */}
        <div className="flex justify-between items-start">
          <div>
            <h1 className="text-3xl font-bold">性能优化中心</h1>
            <p className="text-gray-500 mt-2">
              监控和优化系统性能，提升用户体验
            </p>
          </div>

          <div className="flex items-center gap-4">
            <Button
              onClick={runPerformanceTest}
              disabled={isRunningTest}
              className="bg-blue-600 hover:bg-blue-700"
            >
              {isRunningTest ? (
                <RefreshCw className="h-4 w-4 mr-2 animate-spin" />
              ) : (
                <Zap className="h-4 w-4 mr-2" />
              )}
              {isRunningTest ? '测试中...' : '运行性能测试'}
            </Button>
          </div>
        </div>

        {/* 性能概览 */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          <Card>
            <CardContent className="p-4">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-500">内存使用</p>
                  <p className="text-2xl font-bold">
                    {performanceStats.memoryUsage.toFixed(1)}MB
                  </p>
                </div>
                <HardDrive className="h-8 w-8 text-blue-500" />
              </div>
              <Progress
                value={performanceStats.memoryUsagePercent}
                className="mt-2"
              />
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-4">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-500">缓存命中率</p>
                  <p className="text-2xl font-bold">
                    {performanceStats.cacheHitRate.toFixed(1)}%
                  </p>
                </div>
                <Database className="h-8 w-8 text-green-500" />
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-4">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-500">平均渲染时间</p>
                  <p className="text-2xl font-bold">
                    {performanceStats.averageRenderTime.toFixed(1)}ms
                  </p>
                </div>
                <Clock className="h-8 w-8 text-purple-500" />
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-4">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-500">缓存大小</p>
                  <p className="text-2xl font-bold">
                    {performanceStats.totalCacheSize.toFixed(1)}MB
                  </p>
                </div>
                <BarChart3 className="h-8 w-8 text-orange-500" />
              </div>
            </CardContent>
          </Card>
        </div>

        {/* 性能测试结果 */}
        {testResults && (
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <TrendingUp className="h-5 w-5" />
                性能测试结果
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
                <div className="text-center">
                  <p className="text-sm text-gray-500 mb-2">虚拟滚动</p>
                  <p className={`text-3xl font-bold ${getPerformanceGrade(testResults.virtualScrollTest).color}`}>
                    {getPerformanceGrade(testResults.virtualScrollTest).grade}
                  </p>
                  <p className="text-sm text-gray-400">{testResults.virtualScrollTest.toFixed(1)}分</p>
                </div>
                <div className="text-center">
                  <p className="text-sm text-gray-500 mb-2">缓存性能</p>
                  <p className={`text-3xl font-bold ${getPerformanceGrade(testResults.cacheTest).color}`}>
                    {getPerformanceGrade(testResults.cacheTest).grade}
                  </p>
                  <p className="text-sm text-gray-400">{testResults.cacheTest.toFixed(1)}分</p>
                </div>
                <div className="text-center">
                  <p className="text-sm text-gray-500 mb-2">分片处理</p>
                  <p className={`text-3xl font-bold ${getPerformanceGrade(testResults.chunkProcessingTest).color}`}>
                    {getPerformanceGrade(testResults.chunkProcessingTest).grade}
                  </p>
                  <p className="text-sm text-gray-400">{testResults.chunkProcessingTest.toFixed(1)}分</p>
                </div>
                <div className="text-center">
                  <p className="text-sm text-gray-500 mb-2">综合评分</p>
                  <p className={`text-3xl font-bold ${getPerformanceGrade(testResults.overallScore).color}`}>
                    {getPerformanceGrade(testResults.overallScore).grade}
                  </p>
                  <p className="text-sm text-gray-400">{testResults.overallScore.toFixed(1)}分</p>
                </div>
              </div>
            </CardContent>
          </Card>
        )}

        {/* 优化建议 */}
        {optimizationTips.length > 0 && (
          <Alert>
            <AlertTriangle className="h-4 w-4" />
            <AlertTitle>性能优化建议</AlertTitle>
            <AlertDescription>
              <ul className="list-disc list-inside space-y-1 mt-2">
                {optimizationTips.map((tip, index) => (
                  <li key={index}>{tip}</li>
                ))}
              </ul>
            </AlertDescription>
          </Alert>
        )}

        {/* 功能标签页 */}
        <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
          <TabsList className="grid w-full grid-cols-3">
            <TabsTrigger value="overview" className="flex items-center gap-2">
              <Monitor className="h-4 w-4" />
              性能监控
            </TabsTrigger>
            <TabsTrigger value="cache" className="flex items-center gap-2">
              <Database className="h-4 w-4" />
              缓存管理
            </TabsTrigger>
            <TabsTrigger value="optimization" className="flex items-center gap-2">
              <Zap className="h-4 w-4" />
              优化演示
            </TabsTrigger>
          </TabsList>

          <TabsContent value="overview" className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle>系统性能监控</CardTitle>
                <CardDescription>
                  实时监控系统性能指标和资源使用情况
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <h4 className="font-medium mb-2">内存使用情况</h4>
                      <div className="space-y-2">
                        <div className="flex justify-between text-sm">
                          <span>已使用</span>
                          <span>{performanceStats.memoryUsage.toFixed(1)}MB</span>
                        </div>
                        <Progress value={performanceStats.memoryUsagePercent} />
                        <div className="flex justify-between text-xs text-gray-500">
                          <span>0MB</span>
                          <span>{performanceStats.memoryLimit.toFixed(1)}MB</span>
                        </div>
                      </div>
                    </div>

                    <div>
                      <h4 className="font-medium mb-2">缓存效率</h4>
                      <div className="space-y-2">
                        <div className="flex justify-between text-sm">
                          <span>命中率</span>
                          <span>{performanceStats.cacheHitRate.toFixed(1)}%</span>
                        </div>
                        <Progress value={performanceStats.cacheHitRate} />
                        <div className="flex justify-between text-xs text-gray-500">
                          <span>0%</span>
                          <span>100%</span>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="cache" className="space-y-6">
            <CacheManagementPanel />
          </TabsContent>

          <TabsContent value="optimization" className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle>虚拟滚动优化演示</CardTitle>
                <CardDescription>
                  展示虚拟滚动技术在大数据量场景下的性能优势
                </CardDescription>
              </CardHeader>
              <CardContent>
                <OptimizedContentReviewPanel />
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
      </div>
    </SuperAdminLayout>
  );
};

export default PerformanceOptimizationPage;
