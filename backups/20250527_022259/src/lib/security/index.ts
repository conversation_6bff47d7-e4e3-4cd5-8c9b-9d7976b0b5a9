/**
 * 问卷安全防护模块
 * 
 * 提供可配置的多级防护功能，可以灵活调整防范等级
 */

import { SecurityConfig } from './types';
import { defaultConfigs } from './config';
import { CaptchaService } from './services/captchaService';
import { BehaviorTrackingService } from './services/behaviorTrackingService';
import { HoneypotService } from './services/honeypotService';
import { FingerprintService } from './services/fingerprintService';
import { LoggingService } from './services/loggingService';

/**
 * 安全模块主类
 */
class SecurityModuleClass {
  private config: SecurityConfig;
  private initialized: boolean = false;
  private captchaService: CaptchaService;
  private behaviorTrackingService: BehaviorTrackingService;
  private honeypotService: HoneypotService;
  private fingerprintService: FingerprintService;
  private loggingService: LoggingService;

  constructor() {
    // 默认使用标准防护等级
    this.config = defaultConfigs[2];
    
    // 初始化服务
    this.captchaService = new CaptchaService();
    this.behaviorTrackingService = new BehaviorTrackingService();
    this.honeypotService = new HoneypotService();
    this.fingerprintService = new FingerprintService();
    this.loggingService = new LoggingService();
  }

  /**
   * 初始化安全模块
   * @param config 安全配置
   */
  public initialize(config?: Partial<SecurityConfig>): void {
    // 如果提供了防护等级，使用对应的默认配置
    if (config?.protectionLevel !== undefined) {
      this.config = {
        ...defaultConfigs[config.protectionLevel],
        ...config
      };
    } else if (config) {
      // 否则合并提供的配置
      this.config = {
        ...this.config,
        ...config
      };
    }

    // 初始化各服务
    this.captchaService.initialize(this.config.captcha);
    this.behaviorTrackingService.initialize(this.config.behaviorAnalysis);
    this.honeypotService.initialize(this.config.honeypot);
    this.fingerprintService.initialize(this.config.fingerprinting);
    this.loggingService.initialize(this.config.logging);

    this.initialized = true;
    this.log('info', 'Security module initialized', { protectionLevel: this.config.protectionLevel });
  }

  /**
   * 获取当前配置
   */
  public getConfig(): SecurityConfig {
    return { ...this.config };
  }

  /**
   * 更新配置
   * @param config 部分配置
   */
  public updateConfig(config: Partial<SecurityConfig>): void {
    // 如果更新了防护等级，使用对应的默认配置作为基础
    if (config.protectionLevel !== undefined && config.protectionLevel !== this.config.protectionLevel) {
      this.config = {
        ...defaultConfigs[config.protectionLevel],
        ...config
      };
    } else {
      // 否则只更新提供的配置项
      this.config = {
        ...this.config,
        ...config
      };
    }

    // 更新各服务的配置
    this.captchaService.updateConfig(this.config.captcha);
    this.behaviorTrackingService.updateConfig(this.config.behaviorAnalysis);
    this.honeypotService.updateConfig(this.config.honeypot);
    this.fingerprintService.updateConfig(this.config.fingerprinting);
    this.loggingService.updateConfig(this.config.logging);

    this.log('info', 'Security module configuration updated', { protectionLevel: this.config.protectionLevel });
  }

  /**
   * 获取安全元数据（提交表单时使用）
   */
  public getSecurityMetadata(): any {
    if (!this.initialized || !this.config.enabled) {
      return {};
    }

    const metadata: any = {
      timestamp: Date.now(),
      protectionLevel: this.config.protectionLevel
    };

    // 收集各服务的元数据
    if (this.config.captcha.enabled) {
      metadata.captcha = this.captchaService.getMetadata();
    }

    if (this.config.behaviorAnalysis.enabled) {
      metadata.behavior = this.behaviorTrackingService.getMetadata();
    }

    if (this.config.fingerprinting.enabled) {
      metadata.fingerprint = this.fingerprintService.getFingerprint();
    }

    return metadata;
  }

  /**
   * 验证表单（提交前调用）
   * @param formData 表单数据
   */
  public async validateForm(formData: any): Promise<{ valid: boolean; error?: string }> {
    if (!this.initialized || !this.config.enabled) {
      return { valid: true };
    }

    // 检查蜜罐字段
    if (this.config.honeypot.enabled) {
      const honeypotResult = this.honeypotService.validateForm(formData);
      if (!honeypotResult.valid) {
        this.log('warn', 'Honeypot field filled', { formData });
        return honeypotResult;
      }
    }

    // 验证验证码
    if (this.config.captcha.enabled) {
      const captchaResult = await this.captchaService.validateCaptcha();
      if (!captchaResult.valid) {
        this.log('warn', 'Captcha validation failed', {});
        return captchaResult;
      }
    }

    // 验证行为数据
    if (this.config.behaviorAnalysis.enabled) {
      const behaviorResult = this.behaviorTrackingService.validateBehavior();
      if (!behaviorResult.valid) {
        this.log('warn', 'Behavior validation failed', { behavior: behaviorResult });
        return behaviorResult;
      }
    }

    return { valid: true };
  }

  /**
   * 渲染安全组件（如验证码）
   * @param props 组件属性
   */
  public renderSecurityComponents(props: any = {}): React.ReactNode[] {
    if (!this.initialized || !this.config.enabled) {
      return [];
    }

    const components: React.ReactNode[] = [];

    // 添加验证码组件
    if (this.config.captcha.enabled) {
      components.push(this.captchaService.renderCaptcha(props));
    }

    // 添加蜜罐字段
    if (this.config.honeypot.enabled) {
      components.push(this.honeypotService.renderHoneypotFields());
    }

    return components;
  }

  /**
   * 记录日志
   * @param level 日志级别
   * @param message 日志消息
   * @param data 日志数据
   */
  private log(level: 'error' | 'warn' | 'info' | 'debug', message: string, data: any): void {
    if (this.initialized && this.config.logging.enabled) {
      this.loggingService.log(level, message, data);
    }
  }
}

// 导出单例实例
export const SecurityModule = new SecurityModuleClass();

// 导出类型
export * from './types';
