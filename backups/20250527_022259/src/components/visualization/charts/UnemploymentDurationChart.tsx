import { LineChart, Line, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer } from 'recharts';

interface UnemploymentDurationChartProps {
  data: Array<{ duration: string; count: number }>;
}

export default function UnemploymentDurationChart({ data }: UnemploymentDurationChartProps) {
  // If no data, show placeholder
  if (!data || data.length === 0) {
    return (
      <div className="h-full flex items-center justify-center">
        <p className="text-gray-500">暂无数据</p>
      </div>
    );
  }
  
  // Define the order of duration categories
  const durationOrder = [
    '3个月以内',
    '3-6个月',
    '6-12个月',
    '1年以上',
    '应届生尚未就业',
  ];
  
  // Create a map for data
  const dataMap = new Map();
  data.forEach(item => {
    dataMap.set(item.duration, item.count);
  });
  
  // Create ordered chart data
  const chartData = durationOrder.map(duration => ({
    duration,
    count: dataMap.get(duration) || 0,
  }));
  
  // Custom tooltip
  const CustomTooltip = ({ active, payload, label }: any) => {
    if (active && payload && payload.length) {
      return (
        <div className="bg-white p-2 border rounded shadow-sm">
          <p className="font-medium">{`${label}`}</p>
          <p className="text-sm">{`人数: ${payload[0].value}`}</p>
          <p className="text-sm">{`占比: ${((payload[0].value / data.reduce((sum, item) => sum + item.count, 0)) * 100).toFixed(1)}%`}</p>
        </div>
      );
    }
    return null;
  };
  
  return (
    <ResponsiveContainer width="100%" height="100%">
      <LineChart
        data={chartData}
        margin={{ top: 5, right: 30, left: 20, bottom: 5 }}
      >
        <CartesianGrid strokeDasharray="3 3" />
        <XAxis dataKey="duration" />
        <YAxis />
        <Tooltip content={<CustomTooltip />} />
        <Line 
          type="monotone" 
          dataKey="count" 
          name="人数" 
          stroke="#FF8042" 
          activeDot={{ r: 8 }}
          strokeWidth={2}
        />
      </LineChart>
    </ResponsiveContainer>
  );
}
