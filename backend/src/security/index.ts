/**
 * 后端安全模块
 *
 * 提供可配置的多级防护功能，可以灵活调整防范等级
 */

import { Context, Next } from 'hono';
import { SecurityConfig } from './types';
import { defaultConfigs } from './config';
import { CaptchaVerifier } from './services/captchaVerifier';
import { BehaviorAnalyzer } from './services/behaviorAnalyzer';
import { RateLimiter } from './services/rateLimiter';
import { HoneypotDetector } from './services/honeypotDetector';
import { FingerprintTracker } from './services/fingerprintTracker';
import { SecurityLogger } from './services/securityLogger';
import { ErrorAnalyzer } from './services/errorAnalyzer';
import { AlertManager } from './services/alertManager';
import { AutoFixer } from './services/autoFixer';
import {
  SecurityError,
  ValidationError,
  RateLimitError,
  CaptchaError,
  SuspiciousActivityError,
  ConfigurationError,
  StorageError,
  createErrorHandler
} from './errors';

/**
 * 安全模块类
 */
class SecurityModuleClass {
  private config: SecurityConfig;
  private initialized: boolean = false;
  private captchaVerifier: CaptchaVerifier;
  private behaviorAnalyzer: BehaviorAnalyzer;
  private rateLimiter: RateLimiter;
  private honeypotDetector: HoneypotDetector;
  private fingerprintTracker: FingerprintTracker;
  private securityLogger: SecurityLogger;
  private errorAnalyzer: ErrorAnalyzer;
  private alertManager: AlertManager;
  private autoFixer: AutoFixer;
  private errorHandler: ReturnType<typeof createErrorHandler>;
  private requestCounter: number = 0;
  private analysisInterval: NodeJS.Timeout | null = null;

  constructor() {
    // 默认使用标准防护等级
    this.config = defaultConfigs[2];

    // 初始化服务
    this.captchaVerifier = new CaptchaVerifier();
    this.behaviorAnalyzer = new BehaviorAnalyzer();
    this.rateLimiter = new RateLimiter();
    this.honeypotDetector = new HoneypotDetector();
    this.fingerprintTracker = new FingerprintTracker();
    this.securityLogger = new SecurityLogger();
    this.errorAnalyzer = new ErrorAnalyzer();
    this.alertManager = new AlertManager();
    this.autoFixer = new AutoFixer();

    // 初始化错误处理器
    this.errorHandler = createErrorHandler(this.securityLogger);
  }

  /**
   * 生成请求 ID
   */
  private generateRequestId(): string {
    this.requestCounter = (this.requestCounter + 1) % 1000000;
    const timestamp = Date.now();
    const random = Math.floor(Math.random() * 1000);
    return `${timestamp}-${this.requestCounter}-${random}`;
  }

  /**
   * 检查安全模块是否已初始化
   */
  public isInitialized(): boolean {
    return this.initialized;
  }

  /**
   * 初始化安全模块
   * @param config 安全配置
   */
  public initialize(config?: Partial<SecurityConfig>): void {
    // 如果提供了防护等级，使用对应的默认配置
    if (config?.protectionLevel !== undefined) {
      this.config = {
        ...defaultConfigs[config.protectionLevel],
        ...config
      };
    } else if (config) {
      // 否则合并提供的配置
      this.config = {
        ...this.config,
        ...config
      };
    }

    // 初始化各服务
    this.captchaVerifier.initialize(this.config.captcha);
    this.behaviorAnalyzer.initialize(this.config.behaviorAnalysis);
    this.rateLimiter.initialize(this.config.rateLimit);
    this.honeypotDetector.initialize(this.config.honeypot);
    this.fingerprintTracker.initialize(this.config.fingerprinting);
    this.securityLogger.initialize(this.config.logging);
    this.errorAnalyzer.initialize(this.config.errorAnalysis);
    this.alertManager.initialize(this.config.alerting);
    this.autoFixer.initialize(this.config.autoFix);

    this.initialized = true;
    this.log('info', 'Security module initialized', { protectionLevel: this.config.protectionLevel });

    // 启动定期错误分析
    this.startErrorAnalysis();
  }

  /**
   * 启动定期错误分析
   */
  private startErrorAnalysis(): void {
    // 清除现有的分析间隔
    if (this.analysisInterval) {
      clearInterval(this.analysisInterval);
      this.analysisInterval = null;
    }

    // 如果错误分析未启用，不启动定期分析
    if (!this.config.errorAnalysis.enabled) {
      return;
    }

    // 设置分析间隔
    const interval = this.config.errorAnalysis.analysisInterval || 3600000; // 默认1小时

    this.analysisInterval = setInterval(async () => {
      try {
        await this.runErrorAnalysis();
      } catch (error) {
        console.error('Error running scheduled error analysis:', error);
      }
    }, interval);
  }

  /**
   * 运行错误分析
   */
  private async runErrorAnalysis(): Promise<void> {
    if (!this.initialized || !this.config.errorAnalysis.enabled) {
      return;
    }

    try {
      // 获取最近的日志
      const logs = await this.securityLogger.getLogs('all', 1, 1000);

      // 分析错误
      const analysisResult = await this.errorAnalyzer.analyzeErrors(logs.logs);

      // 处理异常
      if (this.config.alerting.enabled) {
        for (const [anomalyType, anomaly] of Object.entries(analysisResult.anomalies)) {
          if (anomaly.isAnomaly) {
            // 发送异常告警
            await this.alertManager.sendAnomalyAlert(
              anomalyType,
              anomaly,
              'high',
              'errorAnalyzer'
            );
          }
        }
      }

      // 记录分析结果
      await this.log(
        'info',
        'Error analysis completed',
        {
          clusters: analysisResult.clusters.length,
          patterns: analysisResult.patterns.length,
          anomalies: Object.values(analysisResult.anomalies).filter(a => a.isAnomaly).length
        },
        undefined,
        undefined,
        {
          source: 'errorAnalyzer',
          tags: ['analysis', 'scheduled']
        }
      );
    } catch (error) {
      console.error('Error analyzing errors:', error);
    }
  }

  /**
   * 获取当前配置
   */
  public getConfig(): SecurityConfig {
    return { ...this.config };
  }

  /**
   * 更新配置
   * @param config 部分配置
   */
  public updateConfig(config: Partial<SecurityConfig>): void {
    // 如果更新了防护等级，使用对应的默认配置作为基础
    if (config.protectionLevel !== undefined && config.protectionLevel !== this.config.protectionLevel) {
      this.config = {
        ...defaultConfigs[config.protectionLevel],
        ...config
      };
    } else {
      // 否则只更新提供的配置项
      this.config = {
        ...this.config,
        ...config
      };
    }

    // 更新各服务的配置
    this.captchaVerifier.updateConfig(this.config.captcha);
    this.behaviorAnalyzer.updateConfig(this.config.behaviorAnalysis);
    this.rateLimiter.updateConfig(this.config.rateLimit);
    this.honeypotDetector.updateConfig(this.config.honeypot);
    this.fingerprintTracker.updateConfig(this.config.fingerprinting);
    this.securityLogger.updateConfig(this.config.logging);
    this.errorAnalyzer.updateConfig(this.config.errorAnalysis);
    this.alertManager.updateConfig(this.config.alerting);
    this.autoFixer.updateConfig(this.config.autoFix);

    this.log('info', 'Security module configuration updated', { protectionLevel: this.config.protectionLevel });

    // 重新启动错误分析
    this.startErrorAnalysis();
  }

  /**
   * 创建安全中间件
   */
  public createMiddleware() {
    return async (c: Context, next: Next) => {
      if (!this.initialized || !this.config.enabled) {
        return next();
      }

      // 生成请求 ID
      const requestId = this.generateRequestId();
      c.set('requestId', requestId);

      // 获取客户端 IP
      const clientIp = c.req.headers.get('CF-Connecting-IP') ||
                       c.req.headers.get('X-Forwarded-For') ||
                       'unknown';

      // 记录请求开始
      const startTime = Date.now();
      await this.log(
        'debug',
        `Security middleware: Processing ${c.req.method} request to ${c.req.path}`,
        {
          method: c.req.method,
          path: c.req.path,
          query: c.req.query(),
          headers: this.sanitizeHeaders(c.req.headers)
        },
        clientIp,
        c.env,
        {
          requestId,
          source: 'middleware',
          tags: ['request', 'start']
        }
      );

      try {
        // 验证请求
        const validationResult = await this.validateRequest(c);
        if (!validationResult.valid) {
          // 创建适当的错误
          let error: SecurityError;

          if (validationResult.status === 429) {
            error = new RateLimitError(
              validationResult.error || '请求频率过高，请稍后再试',
              {
                statusCode: 429,
                context: validationResult,
                requestId,
                clientIp
              }
            );
          } else if (validationResult.suspiciousScore && validationResult.suspiciousScore > 50) {
            error = new SuspiciousActivityError(
              validationResult.error || '请求被拒绝',
              {
                statusCode: validationResult.status || 403,
                context: validationResult,
                requestId,
                clientIp
              }
            );
          } else {
            error = new ValidationError(
              validationResult.error || '请求被拒绝',
              {
                statusCode: validationResult.status || 400,
                context: validationResult,
                requestId,
                clientIp
              }
            );
          }

          // 处理错误
          await this.errorHandler(error, requestId, clientIp, c.env);

          // 返回错误响应
          return c.json(error.toResponse(), error.statusCode);
        }

        // 继续处理请求
        const result = await next();

        // 记录请求完成
        const duration = Date.now() - startTime;
        await this.log(
          'debug',
          `Security middleware: Completed ${c.req.method} request to ${c.req.path}`,
          {
            method: c.req.method,
            path: c.req.path,
            duration,
            status: c.res.status
          },
          clientIp,
          c.env,
          {
            requestId,
            source: 'middleware',
            tags: ['request', 'complete'],
            duration
          }
        );

        return result;
      } catch (error) {
        // 计算持续时间
        const duration = Date.now() - startTime;

        // 处理错误
        const securityError = await this.handleMiddlewareError(error, requestId, clientIp, c.env, duration);

        // 返回错误响应
        return c.json(securityError.toResponse(), securityError.statusCode);
      }
    };
  }

  /**
   * 处理中间件错误
   * @param error 错误对象
   * @param requestId 请求 ID
   * @param clientIp 客户端 IP
   * @param env 环境变量
   * @param duration 请求持续时间
   */
  private async handleMiddlewareError(
    error: any,
    requestId: string,
    clientIp: string,
    env: any,
    duration?: number
  ): Promise<SecurityError> {
    // 如果已经是 SecurityError，直接使用
    if (error instanceof SecurityError) {
      // 确保错误有请求 ID 和客户端 IP
      if (!error.requestId) {
        error.requestId = requestId;
      }

      if (!error.clientIp) {
        error.clientIp = clientIp;
      }

      // 处理错误
      await this.errorHandler(error, requestId, clientIp, env);
      return error;
    }

    // 创建通用安全错误
    const securityError = new SecurityError(
      error.message || '请求处理失败',
      {
        code: 'MIDDLEWARE_ERROR',
        statusCode: 500,
        severity: 'high',
        context: {
          originalError: error.name || 'Unknown',
          duration
        },
        requestId,
        clientIp,
        cause: error
      }
    );

    // 处理错误
    await this.errorHandler(securityError, requestId, clientIp, env);
    return securityError;
  }

  /**
   * 清理请求头
   * @param headers 请求头
   */
  private sanitizeHeaders(headers: Headers): Record<string, string> {
    const result: Record<string, string> = {};
    const sensitiveHeaders = [
      'authorization',
      'cookie',
      'set-cookie',
      'x-api-key',
      'x-auth-token'
    ];

    headers.forEach((value, key) => {
      // 跳过敏感头部
      if (sensitiveHeaders.includes(key.toLowerCase())) {
        result[key] = '[REDACTED]';
      } else {
        result[key] = value;
      }
    });

    return result;
  }

  /**
   * 验证请求
   * @param c 上下文
   */
  public async validateRequest(c: Context): Promise<{ valid: boolean; error?: string; status?: number; suspiciousScore?: number; reasons?: string[] }> {
    if (!this.initialized || !this.config.enabled) {
      return { valid: true };
    }

    // 获取请求 ID
    const requestId = c.get('requestId') || this.generateRequestId();

    // 获取客户端 IP
    const clientIp = c.req.headers.get('CF-Connecting-IP') || c.req.headers.get('X-Forwarded-For') || 'unknown';

    try {
      // 检查速率限制
      if (this.config.rateLimit.enabled) {
        try {
          const rateLimitResult = await this.rateLimiter.checkRateLimit(c, clientIp);
          if (!rateLimitResult.valid) {
            await this.log(
              'warn',
              'Rate limit exceeded',
              {
                clientIp,
                limit: this.config.rateLimit.ipLimit,
                cooldownPeriod: this.config.rateLimit.cooldownPeriod
              },
              clientIp,
              c.env,
              {
                requestId,
                errorCode: 'RATE_LIMIT_EXCEEDED',
                severity: 'medium',
                source: 'rateLimiter',
                tags: ['rateLimit', 'blocked']
              }
            );

            return {
              valid: false,
              error: rateLimitResult.error || '请求频率过高，请稍后再试',
              status: 429
            };
          }
        } catch (error) {
          // 处理速率限制错误
          const rateLimitError = new RateLimitError(
            '速率限制检查失败',
            {
              code: 'RATE_LIMIT_CHECK_FAILED',
              statusCode: 500,
              severity: 'high',
              context: { originalError: error },
              requestId,
              clientIp,
              cause: error instanceof Error ? error : new Error(String(error))
            }
          );

          await this.errorHandler(rateLimitError, requestId, clientIp, c.env);

          // 出错时允许请求通过，避免阻止合法请求
          return { valid: true };
        }
      }

      // 如果不是 POST 请求，跳过其他验证
      if (c.req.method !== 'POST') {
        return { valid: true };
      }

      try {
        // 获取请求数据
        const data = await c.req.json();

        // 检查蜜罐字段
        if (this.config.honeypot.enabled) {
          try {
            const honeypotResult = this.honeypotDetector.checkHoneypot(data);
            if (!honeypotResult.valid) {
              await this.log(
                'warn',
                'Honeypot field filled',
                {
                  clientIp,
                  honeypotFields: this.honeypotDetector.getFilledFields(data)
                },
                clientIp,
                c.env,
                {
                  requestId,
                  errorCode: 'HONEYPOT_TRIGGERED',
                  severity: 'high',
                  source: 'honeypotDetector',
                  tags: ['honeypot', 'bot', 'blocked']
                }
              );

              // 如果配置为静默拒绝，返回成功但不实际处理
              if (this.config.honeypot.silentRejection) {
                c.set('silentRejection', true);
                return { valid: true };
              }

              return {
                valid: false,
                error: '请求被拒绝',
                status: 400,
                suspiciousScore: 100,
                reasons: ['蜜罐字段被填写']
              };
            }
          } catch (error) {
            // 处理蜜罐检测错误
            const honeypotError = new SecurityError(
              '蜜罐检测失败',
              {
                code: 'HONEYPOT_CHECK_FAILED',
                statusCode: 500,
                severity: 'medium',
                context: { originalError: error },
                requestId,
                clientIp,
                cause: error instanceof Error ? error : new Error(String(error))
              }
            );

            await this.errorHandler(honeypotError, requestId, clientIp, c.env);

            // 出错时继续验证其他部分
          }
        }

        // 验证验证码
        if (this.config.captcha.enabled && data._security?.captcha) {
          try {
            const captchaResult = await this.captchaVerifier.verifyCaptcha(
              data._security.captcha.token,
              clientIp,
              c.env
            );

            if (!captchaResult.valid) {
              await this.log(
                'warn',
                'Captcha verification failed',
                {
                  clientIp,
                  captchaError: captchaResult.error
                },
                clientIp,
                c.env,
                {
                  requestId,
                  errorCode: 'CAPTCHA_VERIFICATION_FAILED',
                  severity: 'medium',
                  source: 'captchaVerifier',
                  tags: ['captcha', 'blocked']
                }
              );

              return {
                valid: false,
                error: '验证码验证失败，请重试',
                status: 400,
                suspiciousScore: 80,
                reasons: ['验证码验证失败']
              };
            }
          } catch (error) {
            // 处理验证码验证错误
            const captchaError = new CaptchaError(
              '验证码验证过程中发生错误',
              {
                code: 'CAPTCHA_VERIFICATION_ERROR',
                statusCode: 500,
                severity: 'medium',
                context: { originalError: error },
                requestId,
                clientIp,
                cause: error instanceof Error ? error : new Error(String(error))
              }
            );

            await this.errorHandler(captchaError, requestId, clientIp, c.env);

            // 出错时继续验证其他部分
          }
        }

        // 分析行为数据
        let behaviorSuspiciousScore = 0;
        let behaviorReasons: string[] = [];

        if (this.config.behaviorAnalysis.enabled && data._security?.behavior) {
          try {
            const behaviorResult = this.behaviorAnalyzer.analyzeBehavior(data._security.behavior);

            // 记录可疑分数和原因
            behaviorSuspiciousScore = behaviorResult.suspiciousScore || 0;
            behaviorReasons = behaviorResult.reasons || [];

            if (!behaviorResult.valid) {
              await this.log(
                'warn',
                'Behavior analysis failed',
                {
                  clientIp,
                  behavior: data._security.behavior,
                  suspiciousScore: behaviorResult.suspiciousScore,
                  reasons: behaviorResult.reasons
                },
                clientIp,
                c.env,
                {
                  requestId,
                  errorCode: 'SUSPICIOUS_BEHAVIOR',
                  severity: 'high',
                  source: 'behaviorAnalyzer',
                  tags: ['behavior', 'suspicious', 'blocked']
                }
              );

              return {
                valid: false,
                error: '请求被拒绝',
                status: 400,
                suspiciousScore: behaviorResult.suspiciousScore,
                reasons: behaviorResult.reasons
              };
            }
          } catch (error) {
            // 处理行为分析错误
            const behaviorError = new SecurityError(
              '行为分析失败',
              {
                code: 'BEHAVIOR_ANALYSIS_FAILED',
                statusCode: 500,
                severity: 'medium',
                context: { originalError: error },
                requestId,
                clientIp,
                cause: error instanceof Error ? error : new Error(String(error))
              }
            );

            await this.errorHandler(behaviorError, requestId, clientIp, c.env);

            // 出错时继续验证其他部分
          }
        }

        // 跟踪指纹
        if (this.config.fingerprinting.enabled && data._security?.fingerprint) {
          try {
            const fingerprintResult = await this.fingerprintTracker.trackFingerprint(
              data._security.fingerprint,
              clientIp,
              c.env
            );

            if (!fingerprintResult.valid) {
              await this.log(
                'warn',
                'Fingerprint tracking failed',
                {
                  clientIp,
                  fingerprint: data._security.fingerprint
                },
                clientIp,
                c.env,
                {
                  requestId,
                  errorCode: 'FINGERPRINT_LIMIT_EXCEEDED',
                  severity: 'medium',
                  source: 'fingerprintTracker',
                  tags: ['fingerprint', 'blocked']
                }
              );

              return {
                valid: false,
                error: '请求频率过高，请稍后再试',
                status: 429,
                suspiciousScore: 70,
                reasons: ['指纹提交频率过高']
              };
            }
          } catch (error) {
            // 处理指纹跟踪错误
            const fingerprintError = new SecurityError(
              '指纹跟踪失败',
              {
                code: 'FINGERPRINT_TRACKING_FAILED',
                statusCode: 500,
                severity: 'medium',
                context: { originalError: error },
                requestId,
                clientIp,
                cause: error instanceof Error ? error : new Error(String(error))
              }
            );

            await this.errorHandler(fingerprintError, requestId, clientIp, c.env);

            // 出错时继续验证其他部分
          }
        }

        // 将验证后的数据附加到上下文
        c.set('validatedData', data);

        // 如果有可疑分数但未达到拒绝阈值，仍然返回这些信息
        if (behaviorSuspiciousScore > 0) {
          return {
            valid: true,
            suspiciousScore: behaviorSuspiciousScore,
            reasons: behaviorReasons
          };
        }

        return { valid: true };
      } catch (error) {
        // 处理请求数据解析错误
        const validationError = new ValidationError(
          '无效的请求数据',
          {
            code: 'INVALID_REQUEST_DATA',
            statusCode: 400,
            severity: 'low',
            context: { originalError: error },
            requestId,
            clientIp,
            cause: error instanceof Error ? error : new Error(String(error))
          }
        );

        await this.errorHandler(validationError, requestId, clientIp, c.env);

        return {
          valid: false,
          error: '无效的请求数据',
          status: 400
        };
      }
    } catch (error) {
      // 处理验证过程中的未捕获错误
      const securityError = new SecurityError(
        '请求验证过程中发生错误',
        {
          code: 'VALIDATION_PROCESS_ERROR',
          statusCode: 500,
          severity: 'high',
          context: { originalError: error },
          requestId,
          clientIp,
          cause: error instanceof Error ? error : new Error(String(error))
        }
      );

      await this.errorHandler(securityError, requestId, clientIp, c.env);

      return {
        valid: false,
        error: '请求验证失败',
        status: 500
      };
    }
  }

  /**
   * 分析提交数据
   * @param data 提交数据
   * @param clientIp 客户端 IP
   * @param requestId 请求 ID
   * @param env 环境变量
   */
  public async analyzeSubmission(
    data: any,
    clientIp?: string,
    requestId?: string,
    env?: any
  ): Promise<{ suspicious: boolean; suspiciousScore: number; reasons?: string[]; securityLevel?: string }> {
    if (!this.initialized || !this.config.enabled) {
      return { suspicious: false, suspiciousScore: 0 };
    }

    // 生成请求 ID（如果未提供）
    if (!requestId) {
      requestId = this.generateRequestId();
    }

    try {
      // 分析行为数据
      let suspiciousScore = 0;
      const reasons: string[] = [];

      // 检查行为数据
      if (this.config.behaviorAnalysis.enabled && data._security?.behavior) {
        try {
          const behaviorResult = this.behaviorAnalyzer.analyzeBehavior(data._security.behavior);
          suspiciousScore += behaviorResult.suspiciousScore || 0;

          if (behaviorResult.reasons) {
            reasons.push(...behaviorResult.reasons);
          }
        } catch (error) {
          // 处理行为分析错误
          const behaviorError = new SecurityError(
            '行为分析失败',
            {
              code: 'BEHAVIOR_ANALYSIS_FAILED',
              statusCode: 500,
              severity: 'medium',
              context: { originalError: error },
              requestId,
              clientIp,
              cause: error instanceof Error ? error : new Error(String(error))
            }
          );

          await this.errorHandler(behaviorError, requestId, clientIp, env);
        }
      }

      // 检查指纹数据
      if (this.config.fingerprinting.enabled && data._security?.fingerprint) {
        try {
          // 获取指纹记录
          const fingerprintRecord = await this.fingerprintTracker.getFingerprintRecord(
            data._security.fingerprint,
            env
          );

          // 如果指纹记录存在且提交次数过多，增加可疑分数
          if (fingerprintRecord && fingerprintRecord.count > 5) {
            suspiciousScore += Math.min(30, fingerprintRecord.count * 5);
            reasons.push(`指纹重复提交 (${fingerprintRecord.count} 次)`);
          }
        } catch (error) {
          // 处理指纹分析错误
          const fingerprintError = new SecurityError(
            '指纹分析失败',
            {
              code: 'FINGERPRINT_ANALYSIS_FAILED',
              statusCode: 500,
              severity: 'medium',
              context: { originalError: error },
              requestId,
              clientIp,
              cause: error instanceof Error ? error : new Error(String(error))
            }
          );

          await this.errorHandler(fingerprintError, requestId, clientIp, env);
        }
      }

      // 检查内容一致性
      try {
        if (data.content) {
          // 检查内容长度与填写时间的合理性
          const contentLength = data.content.length;
          const completionTime = data._security?.behavior?.completionTime || 0;

          if (contentLength > 0 && completionTime > 0) {
            // 计算每字符平均时间（毫秒）
            const timePerChar = completionTime / contentLength;

            // 如果平均每字符时间少于 50 毫秒（相当于每分钟 1200 字），可能是复制粘贴或自动填充
            if (timePerChar < 50) {
              suspiciousScore += 20;
              reasons.push(`内容填写速度过快 (${Math.round(timePerChar)}ms/字符)`);
            }
          }
        }
      } catch (error) {
        // 处理内容分析错误
        const contentError = new SecurityError(
          '内容分析失败',
          {
            code: 'CONTENT_ANALYSIS_FAILED',
            statusCode: 500,
            severity: 'low',
            context: { originalError: error },
            requestId,
            clientIp,
            cause: error instanceof Error ? error : new Error(String(error))
          }
        );

        await this.errorHandler(contentError, requestId, clientIp, env);
      }

      // 确定安全级别
      let securityLevel = '正常';
      if (suspiciousScore >= 70) {
        securityLevel = '高风险';
      } else if (suspiciousScore >= 40) {
        securityLevel = '中风险';
      } else if (suspiciousScore >= 20) {
        securityLevel = '低风险';
      }

      // 检查是否可疑
      const suspicious = suspiciousScore >= this.config.behaviorAnalysis.suspiciousScoreThreshold;

      // 记录可疑提交
      if (suspicious) {
        await this.log(
          'warn',
          'Suspicious submission detected',
          {
            suspiciousScore,
            reasons,
            securityData: data._security,
            securityLevel
          },
          clientIp,
          env,
          {
            requestId,
            errorCode: 'SUSPICIOUS_SUBMISSION',
            severity: suspiciousScore >= 70 ? 'high' : 'medium',
            source: 'submissionAnalyzer',
            tags: ['submission', 'suspicious']
          }
        );
      } else if (suspiciousScore > 0) {
        // 记录低可疑度提交
        await this.log(
          'info',
          'Slightly suspicious submission',
          {
            suspiciousScore,
            reasons,
            securityData: data._security,
            securityLevel
          },
          clientIp,
          env,
          {
            requestId,
            source: 'submissionAnalyzer',
            tags: ['submission', 'lowSuspicion']
          }
        );
      }

      return { suspicious, suspiciousScore, reasons, securityLevel };
    } catch (error) {
      // 处理分析过程中的未捕获错误
      const analysisError = new SecurityError(
        '提交分析过程中发生错误',
        {
          code: 'SUBMISSION_ANALYSIS_ERROR',
          statusCode: 500,
          severity: 'medium',
          context: { originalError: error },
          requestId,
          clientIp,
          cause: error instanceof Error ? error : new Error(String(error))
        }
      );

      await this.errorHandler(analysisError, requestId, clientIp, env);

      // 出错时返回低可疑度
      return { suspicious: false, suspiciousScore: 10, reasons: ['分析过程出错'] };
    }
  }

  /**
   * 获取安全日志
   * @param filter 过滤条件
   * @param page 页码
   * @param pageSize 每页大小
   * @param env 环境变量
   */
  public async getLogs(
    filter: string = 'all',
    page: number = 1,
    pageSize: number = 10,
    env?: any
  ) {
    return this.securityLogger.getLogs(filter, page, pageSize, env);
  }

  /**
   * 记录安全事件
   * @param level 日志级别
   * @param message 日志消息
   * @param data 日志数据
   * @param clientIp 客户端 IP
   * @param env 环境变量
   * @param options 其他日志选项
   */
  public async log(
    level: 'error' | 'warn' | 'info' | 'debug',
    message: string,
    data?: any,
    clientIp?: string,
    env?: any,
    options?: {
      requestId?: string;
      errorCode?: string;
      errorName?: string;
      severity?: 'low' | 'medium' | 'high' | 'critical';
      source?: string;
      tags?: string[];
      duration?: number;
      stack?: string;
    }
  ): Promise<string> {
    if (this.initialized && this.config.logging.enabled) {
      return await this.securityLogger.log(level, message, data, clientIp, env, options);
    }
    return '';
  }

  /**
   * 记录错误
   * @param error 错误对象
   * @param clientIp 客户端 IP
   * @param env 环境变量
   * @param options 其他日志选项
   */
  public async logError(
    error: Error,
    clientIp?: string,
    env?: any,
    options?: {
      requestId?: string;
      source?: string;
      tags?: string[];
      duration?: number;
      level?: 'error' | 'warn';
    }
  ): Promise<string> {
    if (this.initialized && this.config.logging.enabled) {
      return await this.securityLogger.logError(error, clientIp, env, options);
    }
    return '';
  }

  /**
   * 处理错误
   * @param error 错误对象
   * @param requestId 请求 ID
   * @param clientIp 客户端 IP
   * @param env 环境变量
   */
  public async handleError(
    error: Error | SecurityError,
    requestId?: string,
    clientIp?: string,
    env?: any
  ): Promise<SecurityError> {
    if (!requestId) {
      requestId = this.generateRequestId();
    }

    // 处理错误
    const securityError = await this.errorHandler(error, requestId, clientIp, env);

    try {
      // 如果启用了告警，发送错误告警
      if (this.config.alerting.enabled && securityError.severity) {
        const severityLevels = ['low', 'medium', 'high', 'critical'];
        const configSeverityIndex = severityLevels.indexOf(this.config.alerting.minSeverity);
        const errorSeverityIndex = severityLevels.indexOf(securityError.severity);

        if (errorSeverityIndex >= configSeverityIndex) {
          await this.alertManager.sendErrorAlert(securityError, {
            source: securityError.context?.source || 'security',
            details: securityError.context
          });
        }
      }

      // 如果启用了自动修复，尝试修复错误
      if (this.config.autoFix.enabled && securityError.code) {
        await this.autoFixer.tryFix(securityError, {
          clientIp,
          requestId,
          timestamp: Date.now()
        });
      }
    } catch (alertError) {
      console.error('Error handling alerts or auto-fix:', alertError);
    }

    return securityError;
  }

  /**
   * 获取错误分析结果
   */
  public async getErrorAnalysis(): Promise<any> {
    if (!this.initialized || !this.config.errorAnalysis.enabled) {
      return {
        enabled: false,
        clusters: [],
        patterns: [],
        anomalies: {}
      };
    }

    try {
      // 获取最近的日志
      const logs = await this.securityLogger.getLogs('all', 1, 1000);

      // 分析错误
      return await this.errorAnalyzer.analyzeErrors(logs.logs);
    } catch (error) {
      console.error('Error getting error analysis:', error);
      return {
        error: 'Failed to get error analysis',
        enabled: this.config.errorAnalysis.enabled
      };
    }
  }

  /**
   * 获取修复历史
   */
  public getFixHistory(): any[] {
    if (!this.initialized || !this.config.autoFix.enabled) {
      return [];
    }

    return this.autoFixer.getFixHistory();
  }

  /**
   * 获取性能指标
   */
  public async getPerformanceMetrics(): Promise<any> {
    if (!this.initialized) {
      return {
        totalLogs: 0,
        totalProcessingTime: 0,
        avgProcessingTime: 0,
        maxProcessingTime: 0,
        minProcessingTime: 0,
        lastProcessingTime: 0,
        bufferStats: {
          bufferSize: 0,
          totalProcessed: 0,
          totalBatches: 0,
          totalErrors: 0,
          lastFlushTime: 0,
          isProcessing: false
        },
        cacheStats: {
          size: 0,
          hits: 0,
          misses: 0,
          hitRate: 0,
          evictions: 0,
          expirations: 0
        }
      };
    }

    return await this.securityLogger.getPerformanceMetrics();
  }
}

// 导出单例实例
export const SecurityModule = new SecurityModuleClass();

// 导出类型
export * from './types';
