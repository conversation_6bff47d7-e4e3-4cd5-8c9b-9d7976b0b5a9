import React, { useState, useEffect } from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ist, Ta<PERSON>Trigger } from '@/components/ui/tabs';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Switch } from '@/components/ui/switch';
import { Label } from '@/components/ui/label';
import { Input } from '@/components/ui/input';
import { Slider } from '@/components/ui/slider';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Checkbox } from '@/components/ui/checkbox';
import { useToast } from '@/components/ui/use-toast';
import { 
  Settings, 
  Bell, 
  Table, 
  Eye, 
  Keyboard, 
  Moon, 
  Sun, 
  Monitor, 
  Volume2, 
  VolumeX,
  RefreshCw,
  Save
} from 'lucide-react';

import { 
  getUserSettings, 
  saveUserSettings, 
  resetUserSettings,
  ThemeType,
  TableLayoutType,
  UserSettings
} from '@/services/userSettingsService';

/**
 * 用户设置面板组件
 */
export default function UserSettingsPanel() {
  const { toast } = useToast();
  const [settings, setSettings] = useState<UserSettings>(getUserSettings());
  const [isChanged, setIsChanged] = useState(false);
  
  // 加载设置
  useEffect(() => {
    const userSettings = getUserSettings();
    setSettings(userSettings);
  }, []);
  
  // 更新设置
  const updateSettings = (newSettings: Partial<UserSettings>) => {
    setSettings(prev => {
      const updated = {
        ...prev,
        ...newSettings,
        notifications: {
          ...prev.notifications,
          ...(newSettings.notifications || {})
        },
        tables: {
          ...prev.tables,
          ...(newSettings.tables || {})
        },
        review: {
          ...prev.review,
          ...(newSettings.review || {})
        },
        ui: {
          ...prev.ui,
          ...(newSettings.ui || {})
        },
        shortcuts: {
          ...prev.shortcuts,
          ...(newSettings.shortcuts || {})
        }
      };
      
      setIsChanged(true);
      return updated;
    });
  };
  
  // 保存设置
  const handleSave = () => {
    saveUserSettings(settings);
    setIsChanged(false);
    
    toast({
      title: '设置已保存',
      description: '您的偏好设置已成功保存',
      variant: 'default'
    });
  };
  
  // 重置设置
  const handleReset = () => {
    resetUserSettings();
    setSettings(getUserSettings());
    setIsChanged(false);
    
    toast({
      title: '设置已重置',
      description: '您的偏好设置已恢复默认值',
      variant: 'default'
    });
  };
  
  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <h2 className="text-2xl font-bold">用户设置</h2>
        
        <div className="flex items-center space-x-2">
          <Button 
            variant="outline" 
            onClick={handleReset}
            disabled={!isChanged}
          >
            <RefreshCw className="h-4 w-4 mr-2" />
            重置
          </Button>
          
          <Button 
            variant="default" 
            onClick={handleSave}
            disabled={!isChanged}
          >
            <Save className="h-4 w-4 mr-2" />
            保存
          </Button>
        </div>
      </div>
      
      <Tabs defaultValue="ui">
        <TabsList className="grid grid-cols-4 w-full max-w-md">
          <TabsTrigger value="ui">
            <Settings className="h-4 w-4 mr-2" />
            界面
          </TabsTrigger>
          <TabsTrigger value="notifications">
            <Bell className="h-4 w-4 mr-2" />
            通知
          </TabsTrigger>
          <TabsTrigger value="tables">
            <Table className="h-4 w-4 mr-2" />
            表格
          </TabsTrigger>
          <TabsTrigger value="review">
            <Eye className="h-4 w-4 mr-2" />
            审核
          </TabsTrigger>
        </TabsList>
        
        {/* 界面设置 */}
        <TabsContent value="ui">
          <Card>
            <CardHeader>
              <CardTitle>界面设置</CardTitle>
              <CardDescription>自定义界面外观和行为</CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              {/* 主题设置 */}
              <div className="space-y-4">
                <h3 className="text-lg font-medium">主题</h3>
                <div className="grid grid-cols-3 gap-4">
                  <div 
                    className={`flex flex-col items-center justify-center p-4 border rounded-lg cursor-pointer ${settings.ui.theme === ThemeType.LIGHT ? 'border-primary bg-primary/10' : 'border-muted'}`}
                    onClick={() => updateSettings({ ui: { ...settings.ui, theme: ThemeType.LIGHT } })}
                  >
                    <Sun className="h-8 w-8 mb-2" />
                    <span>浅色</span>
                  </div>
                  
                  <div 
                    className={`flex flex-col items-center justify-center p-4 border rounded-lg cursor-pointer ${settings.ui.theme === ThemeType.DARK ? 'border-primary bg-primary/10' : 'border-muted'}`}
                    onClick={() => updateSettings({ ui: { ...settings.ui, theme: ThemeType.DARK } })}
                  >
                    <Moon className="h-8 w-8 mb-2" />
                    <span>深色</span>
                  </div>
                  
                  <div 
                    className={`flex flex-col items-center justify-center p-4 border rounded-lg cursor-pointer ${settings.ui.theme === ThemeType.SYSTEM ? 'border-primary bg-primary/10' : 'border-muted'}`}
                    onClick={() => updateSettings({ ui: { ...settings.ui, theme: ThemeType.SYSTEM } })}
                  >
                    <Monitor className="h-8 w-8 mb-2" />
                    <span>系统</span>
                  </div>
                </div>
              </div>
              
              {/* 字体大小 */}
              <div className="space-y-4">
                <div className="flex justify-between items-center">
                  <Label htmlFor="font-size">字体大小</Label>
                  <span className="text-sm">{settings.ui.fontSize}px</span>
                </div>
                <Slider
                  id="font-size"
                  min={12}
                  max={24}
                  step={1}
                  value={[settings.ui.fontSize]}
                  onValueChange={(value) => updateSettings({ ui: { ...settings.ui, fontSize: value[0] } })}
                />
              </div>
              
              {/* 其他界面设置 */}
              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <div className="space-y-0.5">
                    <Label htmlFor="animations">启用动画</Label>
                    <p className="text-sm text-muted-foreground">控制界面过渡和动画效果</p>
                  </div>
                  <Switch
                    id="animations"
                    checked={settings.ui.animationsEnabled}
                    onCheckedChange={(checked) => updateSettings({ ui: { ...settings.ui, animationsEnabled: checked } })}
                  />
                </div>
                
                <div className="flex items-center justify-between">
                  <div className="space-y-0.5">
                    <Label htmlFor="high-contrast">高对比度模式</Label>
                    <p className="text-sm text-muted-foreground">增强文本和控件的对比度</p>
                  </div>
                  <Switch
                    id="high-contrast"
                    checked={settings.ui.highContrastMode}
                    onCheckedChange={(checked) => updateSettings({ ui: { ...settings.ui, highContrastMode: checked } })}
                  />
                </div>
                
                <div className="flex items-center justify-between">
                  <div className="space-y-0.5">
                    <Label htmlFor="compact-mode">紧凑模式</Label>
                    <p className="text-sm text-muted-foreground">减少界面元素间距，显示更多内容</p>
                  </div>
                  <Switch
                    id="compact-mode"
                    checked={settings.ui.compactMode}
                    onCheckedChange={(checked) => updateSettings({ ui: { ...settings.ui, compactMode: checked } })}
                  />
                </div>
                
                <div className="flex items-center justify-between">
                  <div className="space-y-0.5">
                    <Label htmlFor="sidebar-collapsed">默认折叠侧边栏</Label>
                    <p className="text-sm text-muted-foreground">启动时自动折叠侧边栏</p>
                  </div>
                  <Switch
                    id="sidebar-collapsed"
                    checked={settings.ui.sidebarCollapsed}
                    onCheckedChange={(checked) => updateSettings({ ui: { ...settings.ui, sidebarCollapsed: checked } })}
                  />
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
        
        {/* 通知设置 */}
        <TabsContent value="notifications">
          <Card>
            <CardHeader>
              <CardTitle>通知设置</CardTitle>
              <CardDescription>自定义通知偏好</CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="flex items-center justify-between">
                <div className="space-y-0.5">
                  <Label htmlFor="notifications-enabled">启用通知</Label>
                  <p className="text-sm text-muted-foreground">控制是否接收通知</p>
                </div>
                <Switch
                  id="notifications-enabled"
                  checked={settings.notifications.enabled}
                  onCheckedChange={(checked) => updateSettings({ notifications: { ...settings.notifications, enabled: checked } })}
                />
              </div>
              
              <div className="flex items-center justify-between">
                <div className="space-y-0.5">
                  <Label htmlFor="sound-enabled">通知声音</Label>
                  <p className="text-sm text-muted-foreground">收到通知时播放声音</p>
                </div>
                <Switch
                  id="sound-enabled"
                  checked={settings.notifications.sound}
                  onCheckedChange={(checked) => updateSettings({ notifications: { ...settings.notifications, sound: checked } })}
                  disabled={!settings.notifications.enabled}
                />
              </div>
              
              <div className="flex items-center justify-between">
                <div className="space-y-0.5">
                  <Label htmlFor="desktop-notifications">桌面通知</Label>
                  <p className="text-sm text-muted-foreground">在桌面右下角显示通知</p>
                </div>
                <Switch
                  id="desktop-notifications"
                  checked={settings.notifications.desktop}
                  onCheckedChange={(checked) => updateSettings({ notifications: { ...settings.notifications, desktop: checked } })}
                  disabled={!settings.notifications.enabled}
                />
              </div>
              
              <div className="pt-4 border-t">
                <h3 className="text-lg font-medium mb-4">通知类型</h3>
                
                <div className="space-y-4">
                  <div className="flex items-center justify-between">
                    <Label htmlFor="new-content">新内容通知</Label>
                    <Switch
                      id="new-content"
                      checked={settings.notifications.newContent}
                      onCheckedChange={(checked) => updateSettings({ notifications: { ...settings.notifications, newContent: checked } })}
                      disabled={!settings.notifications.enabled}
                    />
                  </div>
                  
                  <div className="flex items-center justify-between">
                    <Label htmlFor="system-alerts">系统提醒</Label>
                    <Switch
                      id="system-alerts"
                      checked={settings.notifications.systemAlerts}
                      onCheckedChange={(checked) => updateSettings({ notifications: { ...settings.notifications, systemAlerts: checked } })}
                      disabled={!settings.notifications.enabled}
                    />
                  </div>
                  
                  <div className="flex items-center justify-between">
                    <Label htmlFor="review-reminders">审核提醒</Label>
                    <Switch
                      id="review-reminders"
                      checked={settings.notifications.reviewReminders}
                      onCheckedChange={(checked) => updateSettings({ notifications: { ...settings.notifications, reviewReminders: checked } })}
                      disabled={!settings.notifications.enabled}
                    />
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
        
        {/* 表格设置 */}
        <TabsContent value="tables">
          <Card>
            <CardHeader>
              <CardTitle>表格设置</CardTitle>
              <CardDescription>自定义表格显示和行为</CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="space-y-4">
                <Label htmlFor="table-layout">表格布局</Label>
                <Select
                  value={settings.tables.layout}
                  onValueChange={(value) => updateSettings({ tables: { ...settings.tables, layout: value as TableLayoutType } })}
                >
                  <SelectTrigger id="table-layout">
                    <SelectValue placeholder="选择表格布局" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value={TableLayoutType.COMPACT}>紧凑</SelectItem>
                    <SelectItem value={TableLayoutType.DEFAULT}>默认</SelectItem>
                    <SelectItem value={TableLayoutType.SPACIOUS}>宽松</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              
              <div className="space-y-4">
                <Label htmlFor="page-size">每页显示数量</Label>
                <Select
                  value={settings.tables.pageSize.toString()}
                  onValueChange={(value) => updateSettings({ tables: { ...settings.tables, pageSize: parseInt(value) } })}
                >
                  <SelectTrigger id="page-size">
                    <SelectValue placeholder="选择每页显示数量" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="10">10</SelectItem>
                    <SelectItem value="20">20</SelectItem>
                    <SelectItem value="50">50</SelectItem>
                    <SelectItem value="100">100</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              
              <div className="flex items-center justify-between">
                <div className="space-y-0.5">
                  <Label htmlFor="show-filters">默认显示筛选器</Label>
                  <p className="text-sm text-muted-foreground">打开表格时自动显示筛选选项</p>
                </div>
                <Switch
                  id="show-filters"
                  checked={settings.tables.showFilters}
                  onCheckedChange={(checked) => updateSettings({ tables: { ...settings.tables, showFilters: checked } })}
                />
              </div>
              
              <div className="flex items-center justify-between">
                <div className="space-y-0.5">
                  <Label htmlFor="virtual-scroll">使用虚拟滚动</Label>
                  <p className="text-sm text-muted-foreground">优化大量数据的显示性能</p>
                </div>
                <Switch
                  id="virtual-scroll"
                  checked={settings.tables.useVirtualScroll}
                  onCheckedChange={(checked) => updateSettings({ tables: { ...settings.tables, useVirtualScroll: checked } })}
                />
              </div>
            </CardContent>
          </Card>
        </TabsContent>
        
        {/* 审核设置 */}
        <TabsContent value="review">
          <Card>
            <CardHeader>
              <CardTitle>审核设置</CardTitle>
              <CardDescription>自定义内容审核体验</CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="space-y-4">
                <div className="flex justify-between items-center">
                  <Label htmlFor="auto-save">自动保存间隔（秒）</Label>
                  <span className="text-sm">{settings.review.autoSaveInterval}秒</span>
                </div>
                <Slider
                  id="auto-save"
                  min={0}
                  max={300}
                  step={30}
                  value={[settings.review.autoSaveInterval]}
                  onValueChange={(value) => updateSettings({ review: { ...settings.review, autoSaveInterval: value[0] } })}
                />
                <p className="text-sm text-muted-foreground">设置为0禁用自动保存</p>
              </div>
              
              <div className="flex items-center justify-between">
                <div className="space-y-0.5">
                  <Label htmlFor="confirm-submit">提交前确认</Label>
                  <p className="text-sm text-muted-foreground">审核操作前显示确认对话框</p>
                </div>
                <Switch
                  id="confirm-submit"
                  checked={settings.review.confirmBeforeSubmit}
                  onCheckedChange={(checked) => updateSettings({ review: { ...settings.review, confirmBeforeSubmit: checked } })}
                />
              </div>
              
              <div className="flex items-center justify-between">
                <div className="space-y-0.5">
                  <Label htmlFor="use-templates">使用审核模板</Label>
                  <p className="text-sm text-muted-foreground">启用审核模板功能</p>
                </div>
                <Switch
                  id="use-templates"
                  checked={settings.review.useTemplates}
                  onCheckedChange={(checked) => updateSettings({ review: { ...settings.review, useTemplates: checked } })}
                />
              </div>
              
              <div className="flex items-center justify-between">
                <div className="space-y-0.5">
                  <Label htmlFor="show-shortcuts">显示键盘快捷键提示</Label>
                  <p className="text-sm text-muted-foreground">在界面上显示可用的键盘快捷键</p>
                </div>
                <Switch
                  id="show-shortcuts"
                  checked={settings.review.showKeyboardShortcuts}
                  onCheckedChange={(checked) => updateSettings({ review: { ...settings.review, showKeyboardShortcuts: checked } })}
                />
              </div>
              
              <div className="space-y-4">
                <Label htmlFor="default-view">默认视图</Label>
                <Select
                  value={settings.review.defaultView}
                  onValueChange={(value) => updateSettings({ review: { ...settings.review, defaultView: value as 'list' | 'grid' | 'kanban' } })}
                >
                  <SelectTrigger id="default-view">
                    <SelectValue placeholder="选择默认视图" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="list">列表</SelectItem>
                    <SelectItem value="grid">网格</SelectItem>
                    <SelectItem value="kanban">看板</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              
              <div className="space-y-4">
                <Label htmlFor="default-tab">默认标签页</Label>
                <Select
                  value={settings.review.defaultTab}
                  onValueChange={(value) => updateSettings({ review: { ...settings.review, defaultTab: value as 'pending' | 'approved' | 'rejected' } })}
                >
                  <SelectTrigger id="default-tab">
                    <SelectValue placeholder="选择默认标签页" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="pending">待审核</SelectItem>
                    <SelectItem value="approved">已通过</SelectItem>
                    <SelectItem value="rejected">已拒绝</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}
