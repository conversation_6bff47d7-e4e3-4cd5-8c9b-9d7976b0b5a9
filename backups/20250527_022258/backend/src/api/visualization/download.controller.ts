/**
 * 导出文件下载控制器
 */

import { Context } from 'hono';
import { KVStorageService } from '../../services/kvStorageService';
import type { Env } from '../../types';
import { createCSV, createExcel, createJSON, createPDF } from '../../utils/exportUtils';

/**
 * 下载导出文件
 */
export const downloadExport = async (c: Context<{ Bindings: Env }>) => {
  try {
    // 获取导出ID
    const exportId = c.req.param('id');
    
    if (!exportId) {
      return c.text('Export ID is required', 400);
    }
    
    // 从KV存储中获取导出记录
    const kvStorage = new KVStorageService(c.env);
    const exportRecord = await kvStorage.getExportRecord(exportId);
    
    if (!exportRecord) {
      return c.text('Export not found', 404);
    }
    
    // 检查导出是否过期
    if (exportRecord.expiresAt && new Date(exportRecord.expiresAt) < new Date()) {
      return c.text('Export has expired', 410);
    }
    
    // 根据导出格式生成文件内容
    let fileContent: string | Buffer;
    let contentType: string;
    
    switch (exportRecord.format) {
      case 'csv':
        fileContent = createCSV(exportRecord.data);
        contentType = 'text/csv';
        break;
      case 'excel':
        fileContent = await createExcel(exportRecord.data);
        contentType = 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet';
        break;
      case 'json':
        fileContent = createJSON(exportRecord.data);
        contentType = 'application/json';
        break;
      case 'pdf':
        fileContent = await createPDF(exportRecord.data, exportRecord.type);
        contentType = 'application/pdf';
        break;
      case 'png':
      case 'svg':
        // 对于图表导出，返回一个简单的图片
        return c.text('Chart export is not fully implemented yet', 501);
      default:
        return c.text('Unsupported export format', 400);
    }
    
    // 设置响应头
    c.header('Content-Type', contentType);
    c.header('Content-Disposition', `attachment; filename="${exportRecord.fileName}"`);
    
    // 返回文件内容
    return c.body(fileContent);
  } catch (error) {
    console.error('Error downloading export:', error);
    return c.text('Failed to download export', 500);
  }
};

/**
 * 下载模拟导出文件
 */
export const downloadMockExport = async (c: Context<{ Bindings: Env }>) => {
  try {
    // 获取文件名
    const fileName = c.req.param('fileName');
    
    if (!fileName) {
      return c.text('File name is required', 400);
    }
    
    // 解析文件格式
    const format = fileName.split('.').pop()?.toLowerCase();
    
    // 生成模拟数据
    const mockData = generateMockData(format);
    
    // 设置内容类型
    let contentType: string;
    
    switch (format) {
      case 'csv':
        contentType = 'text/csv';
        break;
      case 'xlsx':
        contentType = 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet';
        break;
      case 'json':
        contentType = 'application/json';
        break;
      case 'pdf':
        contentType = 'application/pdf';
        break;
      case 'png':
        contentType = 'image/png';
        break;
      case 'svg':
        contentType = 'image/svg+xml';
        break;
      default:
        contentType = 'text/plain';
    }
    
    // 设置响应头
    c.header('Content-Type', contentType);
    c.header('Content-Disposition', `attachment; filename="${fileName}"`);
    
    // 返回模拟文件内容
    return c.body(mockData);
  } catch (error) {
    console.error('Error downloading mock export:', error);
    return c.text('Failed to download mock export', 500);
  }
};

/**
 * 生成模拟数据
 */
function generateMockData(format?: string): string | Buffer {
  // 生成一些模拟数据
  const mockData = {
    educationLevels: [
      { level: '高中/中专', count: 120 },
      { level: '大专', count: 250 },
      { level: '本科', count: 580 },
      { level: '硕士', count: 180 },
      { level: '博士', count: 70 },
    ],
    regions: [
      { region: '北京', count: 150 },
      { region: '上海', count: 180 },
      { region: '广州', count: 120 },
      { region: '深圳', count: 130 },
      { region: '其他一线城市', count: 200 },
      { region: '二线城市', count: 320 },
      { region: '三线及以下城市', count: 100 },
    ],
    employmentStatus: [
      { status: '已就业', count: 850 },
      { status: '待业中', count: 250 },
      { status: '继续深造', count: 100 },
    ],
  };
  
  // 根据格式返回不同的数据
  switch (format) {
    case 'csv':
      return createCSV(mockData);
    case 'xlsx':
      return createExcel(mockData);
    case 'json':
      return createJSON(mockData);
    case 'pdf':
      return createPDF(mockData, 'stats');
    case 'png':
    case 'svg':
      // 返回一个简单的文本，表示这是一个图表
      return 'This is a mock chart image';
    default:
      return JSON.stringify(mockData, null, 2);
  }
}
