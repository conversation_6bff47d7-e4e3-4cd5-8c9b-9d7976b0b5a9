/**
 * 轻量级匿名身份验证功能测试
 */

import { generateUUID, isValidA, isValidB, verifyUUID } from '../utils/uuidGenerator';

describe('Anonymous Authentication Tests', () => {
  const testSalt = 'test-salt-value';
  const validA = '12345678901';
  const validB = '123456';
  
  describe('Input Validation', () => {
    test('isValidA should validate A values correctly', () => {
      expect(isValidA(validA)).toBe(true);
      expect(isValidA('1234567890')).toBe(false); // 10位，太短
      expect(isValidA('123456789012')).toBe(false); // 12位，太长
      expect(isValidA('1234567890a')).toBe(false); // 包含非数字
    });
    
    test('isValidB should validate B values correctly', () => {
      expect(isValidB(validB)).toBe(true);
      expect(isValidB('1234')).toBe(true); // 4位也有效
      expect(isValidB('123')).toBe(false); // 3位，太短
      expect(isValidB('12345')).toBe(false); // 5位，不是4位或6位
      expect(isValidB('1234567')).toBe(false); // 7位，太长
    });
  });
  
  describe('UUID Generation', () => {
    test('generateUUID should produce consistent UUIDs for the same input', () => {
      const uuid1 = generateUUID(validA, validB, testSalt);
      const uuid2 = generateUUID(validA, validB, testSalt);
      expect(uuid1).toBe(uuid2);
    });
    
    test('generateUUID should produce different UUIDs for different inputs', () => {
      const uuid1 = generateUUID(validA, validB, testSalt);
      const uuid2 = generateUUID(validA, '1234', testSalt);
      const uuid3 = generateUUID('10987654321', validB, testSalt);
      expect(uuid1).not.toBe(uuid2);
      expect(uuid1).not.toBe(uuid3);
      expect(uuid2).not.toBe(uuid3);
    });
    
    test('generateUUID should produce different UUIDs with different salts', () => {
      const uuid1 = generateUUID(validA, validB, testSalt);
      const uuid2 = generateUUID(validA, validB, 'different-salt');
      expect(uuid1).not.toBe(uuid2);
    });
    
    test('generateUUID should throw error for invalid inputs', () => {
      expect(() => generateUUID('1234567890', validB, testSalt)).toThrow();
      expect(() => generateUUID(validA, '123', testSalt)).toThrow();
    });
  });
  
  describe('UUID Verification', () => {
    test('verifyUUID should verify correct UUID matches', () => {
      const uuid = generateUUID(validA, validB, testSalt);
      expect(verifyUUID(uuid, validA, validB, testSalt)).toBe(true);
    });
    
    test('verifyUUID should reject incorrect UUID matches', () => {
      const uuid = generateUUID(validA, validB, testSalt);
      expect(verifyUUID(uuid, validA, '1234', testSalt)).toBe(false);
      expect(verifyUUID(uuid, '10987654321', validB, testSalt)).toBe(false);
    });
    
    test('verifyUUID should handle invalid inputs gracefully', () => {
      const uuid = generateUUID(validA, validB, testSalt);
      expect(verifyUUID(uuid, '1234567890', validB, testSalt)).toBe(false);
      expect(verifyUUID(uuid, validA, '123', testSalt)).toBe(false);
    });
  });
  
  describe('Security Considerations', () => {
    test('UUIDs should be sufficiently different even with similar inputs', () => {
      // 测试相似的A值
      const uuid1 = generateUUID('12345678901', validB, testSalt);
      const uuid2 = generateUUID('12345678902', validB, testSalt);
      expect(uuid1).not.toBe(uuid2);
      
      // 测试相似的B值
      const uuid3 = generateUUID(validA, '123456', testSalt);
      const uuid4 = generateUUID(validA, '123457', testSalt);
      expect(uuid3).not.toBe(uuid4);
    });
    
    test('UUIDs should be of consistent length', () => {
      const uuid = generateUUID(validA, validB, testSalt);
      expect(uuid.length).toBe(32);
    });
    
    test('UUIDs should not contain the original A or B values', () => {
      const uuid = generateUUID(validA, validB, testSalt);
      expect(uuid.includes(validA)).toBe(false);
      expect(uuid.includes(validB)).toBe(false);
    });
  });
});
