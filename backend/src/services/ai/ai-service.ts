/**
 * AI服务
 *
 * 提供与AI模型交互的功能，包括内容审核、文本生成等
 */

import {
  getAIServiceConfig,
  getContentModerationPrompt,
  getQuestionnaireModerationPrompt,
  getCommentModerationPrompt,
  getProfileModerationPrompt,
  getFeedbackModerationPrompt,
  getStoryModerationPrompt,
  AIServiceConfig
} from './config';

// 内容审核结果接口
export interface ContentModerationResult {
  isSafe: boolean;
  issues: string[];
  confidence: number;
  explanation: string;
  suggestedAction: 'approve' | 'reject' | 'review';
  severity?: 'low' | 'medium' | 'high';
  dataQuality?: 'low' | 'medium' | 'high';
  constructiveValue?: 'none' | 'low' | 'medium' | 'high';
  storyValue?: 'none' | 'low' | 'medium' | 'high';
}

// 消息接口
interface Message {
  role: 'system' | 'user' | 'assistant';
  content: string;
}

// Grok API响应接口
interface GrokResponse {
  id: string;
  object: string;
  created: number;
  model: string;
  choices: {
    index: number;
    message: {
      role: string;
      content: string;
    };
    finish_reason: string;
  }[];
  usage: {
    prompt_tokens: number;
    completion_tokens: number;
    total_tokens: number;
  };
}

/**
 * AI服务类
 */
export class AIService {
  private config: AIServiceConfig;

  /**
   * 构造函数
   *
   * @param env 环境变量
   */
  constructor(env?: any) {
    this.config = getAIServiceConfig(env);
  }

  /**
   * 发送请求到AI API
   *
   * @param messages 消息列表
   * @returns AI响应
   */
  private async sendRequest(messages: Message[]): Promise<string> {
    // 如果是模拟模式，返回模拟响应
    if (this.config.provider === 'mock') {
      return this.getMockResponse(messages);
    }

    // 构建请求选项
    const requestOptions = {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${this.config.apiKey}`
      },
      body: JSON.stringify({
        messages,
        model: this.config.model,
        stream: false,
        temperature: this.config.temperature,
        max_tokens: this.config.maxTokens
      }),
      timeout: this.config.timeout
    };

    // 发送请求
    let retries = 0;
    let lastError: Error | null = null;

    while (retries <= this.config.retryCount) {
      try {
        const response = await fetch(this.config.apiUrl, requestOptions);

        if (!response.ok) {
          const errorText = await response.text();
          throw new Error(`API请求失败: ${response.status} ${response.statusText} - ${errorText}`);
        }

        const data = await response.json() as GrokResponse;
        return data.choices[0].message.content;
      } catch (error) {
        lastError = error instanceof Error ? error : new Error(String(error));
        console.error(`AI API请求失败 (尝试 ${retries + 1}/${this.config.retryCount + 1}):`, lastError);

        if (retries < this.config.retryCount) {
          // 等待一段时间后重试
          await new Promise(resolve => setTimeout(resolve, this.config.retryDelay));
          retries++;
        } else {
          throw lastError;
        }
      }
    }

    // 这行代码不应该被执行，但TypeScript需要它
    throw lastError || new Error('未知错误');
  }

  /**
   * 获取模拟响应（用于本地开发和测试）
   *
   * @param messages 消息列表
   * @returns 模拟响应
   */
  private getMockResponse(messages: Message[]): string {
    // 查找用户消息
    const userMessage = messages.find(m => m.role === 'user')?.content || '';

    // 如果是内容审核
    if (messages.some(m => m.content.includes('内容审核助手'))) {
      // 模拟一个安全的内容
      return JSON.stringify({
        isSafe: true,
        issues: [],
        confidence: 0.95,
        explanation: "内容不包含任何敏感或不适当的信息。",
        suggestedAction: "approve"
      });
    }

    // 默认响应
    return "这是一个模拟的AI响应。";
  }

  /**
   * 审核内容（通用）
   *
   * @param content 要审核的内容
   * @returns 审核结果
   */
  async moderateContent(content: string): Promise<ContentModerationResult> {
    try {
      // 构建消息
      const messages: Message[] = [
        { role: 'system', content: getContentModerationPrompt() },
        { role: 'user', content }
      ];

      // 发送请求
      const responseText = await this.sendRequest(messages);

      // 解析响应
      return this.parseResponse(responseText);
    } catch (error) {
      console.error('内容审核失败:', error);

      // 返回错误结果
      return this.getErrorResult('内容审核服务暂时不可用');
    }
  }

  /**
   * 审核故事内容
   *
   * @param content 要审核的故事内容
   * @param contextData 上下文数据（可选）
   * @returns 审核结果
   */
  async moderateStory(content: string, contextData?: any): Promise<ContentModerationResult> {
    try {
      // 构建消息
      let userContent = content;

      // 如果有上下文数据，添加到内容中
      if (contextData) {
        // 提取标题和内容
        const title = contextData.title || '';
        const mainContent = content;

        userContent = `标题：${title}\n\n内容：${mainContent}`;

        // 添加其他相关信息
        if (contextData.tags) {
          userContent += `\n\n标签：${Array.isArray(contextData.tags) ? contextData.tags.join(', ') : contextData.tags}`;
        }

        if (contextData.category) {
          userContent += `\n\n分类：${contextData.category}`;
        }

        if (contextData.educationLevel) {
          userContent += `\n\n教育程度：${contextData.educationLevel}`;
        }

        if (contextData.industry) {
          userContent += `\n\n行业：${contextData.industry}`;
        }
      }

      const messages: Message[] = [
        { role: 'system', content: getStoryModerationPrompt() },
        { role: 'user', content: userContent }
      ];

      // 发送请求
      const responseText = await this.sendRequest(messages);

      // 解析响应
      return this.parseResponse(responseText);
    } catch (error) {
      console.error('故事内容审核失败:', error);

      // 返回错误结果
      return this.getErrorResult('故事内容审核服务暂时不可用');
    }
  }

  /**
   * 审核问卷内容
   *
   * @param content 要审核的问卷内容
   * @param contextData 上下文数据（可选）
   * @returns 审核结果
   */
  async moderateQuestionnaire(content: string, contextData?: any): Promise<ContentModerationResult> {
    try {
      // 构建消息
      let userContent = content;

      // 如果有上下文数据，添加到内容中
      if (contextData) {
        userContent = `问卷内容：${content}\n\n上下文信息：${JSON.stringify(contextData)}`;
      }

      const messages: Message[] = [
        { role: 'system', content: getQuestionnaireModerationPrompt() },
        { role: 'user', content: userContent }
      ];

      // 发送请求
      const responseText = await this.sendRequest(messages);

      // 解析响应
      return this.parseResponse(responseText);
    } catch (error) {
      console.error('问卷内容审核失败:', error);

      // 返回错误结果
      return this.getErrorResult('问卷内容审核服务暂时不可用');
    }
  }

  /**
   * 审核评论内容
   *
   * @param content 要审核的评论内容
   * @param contextData 上下文数据（可选）
   * @returns 审核结果
   */
  async moderateComment(content: string, contextData?: any): Promise<ContentModerationResult> {
    try {
      // 构建消息
      let userContent = content;

      // 如果有上下文数据，添加到内容中
      if (contextData) {
        userContent = `评论内容：${content}\n\n上下文信息：${JSON.stringify(contextData)}`;
      }

      const messages: Message[] = [
        { role: 'system', content: getCommentModerationPrompt() },
        { role: 'user', content: userContent }
      ];

      // 发送请求
      const responseText = await this.sendRequest(messages);

      // 解析响应
      return this.parseResponse(responseText);
    } catch (error) {
      console.error('评论内容审核失败:', error);

      // 返回错误结果
      return this.getErrorResult('评论内容审核服务暂时不可用');
    }
  }

  /**
   * 审核个人资料内容
   *
   * @param content 要审核的个人资料内容
   * @param contextData 上下文数据（可选）
   * @returns 审核结果
   */
  async moderateProfile(content: string, contextData?: any): Promise<ContentModerationResult> {
    try {
      // 构建消息
      let userContent = content;

      // 如果有上下文数据，添加到内容中
      if (contextData) {
        userContent = `个人资料内容：${content}\n\n上下文信息：${JSON.stringify(contextData)}`;
      }

      const messages: Message[] = [
        { role: 'system', content: getProfileModerationPrompt() },
        { role: 'user', content: userContent }
      ];

      // 发送请求
      const responseText = await this.sendRequest(messages);

      // 解析响应
      return this.parseResponse(responseText);
    } catch (error) {
      console.error('个人资料内容审核失败:', error);

      // 返回错误结果
      return this.getErrorResult('个人资料内容审核服务暂时不可用');
    }
  }

  /**
   * 审核反馈内容
   *
   * @param content 要审核的反馈内容
   * @param contextData 上下文数据（可选）
   * @returns 审核结果
   */
  async moderateFeedback(content: string, contextData?: any): Promise<ContentModerationResult> {
    try {
      // 构建消息
      let userContent = content;

      // 如果有上下文数据，添加到内容中
      if (contextData) {
        userContent = `反馈内容：${content}\n\n上下文信息：${JSON.stringify(contextData)}`;
      }

      const messages: Message[] = [
        { role: 'system', content: getFeedbackModerationPrompt() },
        { role: 'user', content: userContent }
      ];

      // 发送请求
      const responseText = await this.sendRequest(messages);

      // 解析响应
      return this.parseResponse(responseText);
    } catch (error) {
      console.error('反馈内容审核失败:', error);

      // 返回错误结果
      return this.getErrorResult('反馈内容审核服务暂时不可用');
    }
  }

  /**
   * 解析AI响应
   *
   * @param responseText AI响应文本
   * @returns 解析后的内容审核结果
   */
  private parseResponse(responseText: string): ContentModerationResult {
    try {
      const result = JSON.parse(responseText) as ContentModerationResult;
      return result;
    } catch (error) {
      console.error('解析AI响应失败:', error);
      console.error('原始响应:', responseText);

      // 返回默认结果
      return {
        isSafe: false,
        issues: ['无法解析AI响应'],
        confidence: 0.5,
        explanation: '系统无法确定内容安全性，建议人工审核。',
        suggestedAction: 'review'
      };
    }
  }

  /**
   * 获取错误结果
   *
   * @param errorMessage 错误消息
   * @returns 错误的内容审核结果
   */
  private getErrorResult(errorMessage: string): ContentModerationResult {
    return {
      isSafe: false,
      issues: [errorMessage],
      confidence: 0.5,
      explanation: '由于技术原因，无法完成内容审核，建议人工审核。',
      suggestedAction: 'review'
    };
  }
}

// 导出默认实例
export default AIService;
