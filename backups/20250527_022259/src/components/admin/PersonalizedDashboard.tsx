import React, { useState, useEffect } from 'react';
import { DragDropContext, Droppable, Draggable } from 'react-beautiful-dnd';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { But<PERSON> } from '@/components/ui/button';
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { Checkbox } from '@/components/ui/checkbox';
import { Label } from '@/components/ui/label';
import { useToast } from '@/components/ui/use-toast';
import {
  Plus,
  X,
  GripVertical,
  Settings,
  RefreshCw,
  Maximize2,
  Minimize2,
  BarChart4,
  ListChecks,
  Clock,
  AlertTriangle,
  Users,
  Tag,
  FileText,
  Activity
} from 'lucide-react';

// 导入数据服务
import {
  getReviewStats,
  getReviewerPerformance,
  getPendingContents
} from '@/services/dataService';

// 导入组件
import { LoadingSpinner } from '@/components/ui/loading-spinner';
import { Badge } from '@/components/ui/badge';

// 小部件类型
export enum WidgetType {
  PENDING_REVIEWS = 'pending_reviews',
  REVIEW_STATS = 'review_stats',
  REVIEWER_PERFORMANCE = 'reviewer_performance',
  RECENT_ACTIVITY = 'recent_activity',
  SYSTEM_ALERTS = 'system_alerts',
  TAG_STATS = 'tag_stats',
  CONTENT_DISTRIBUTION = 'content_distribution',
  QUICK_ACTIONS = 'quick_actions'
}

// 小部件大小
export enum WidgetSize {
  SMALL = 'small',
  MEDIUM = 'medium',
  LARGE = 'large'
}

// 小部件接口
interface Widget {
  id: string;
  type: WidgetType;
  title: string;
  size: WidgetSize;
  refreshInterval?: number; // 刷新间隔（秒）
  isCollapsed?: boolean;
  isLoading?: boolean;
  data?: any;
  error?: string;
}

// 小部件配置
const widgetConfigs: Record<WidgetType, Omit<Widget, 'id'>> = {
  [WidgetType.PENDING_REVIEWS]: {
    type: WidgetType.PENDING_REVIEWS,
    title: '待审核内容',
    size: WidgetSize.MEDIUM,
    refreshInterval: 60
  },
  [WidgetType.REVIEW_STATS]: {
    type: WidgetType.REVIEW_STATS,
    title: '审核统计',
    size: WidgetSize.MEDIUM,
    refreshInterval: 300
  },
  [WidgetType.REVIEWER_PERFORMANCE]: {
    type: WidgetType.REVIEWER_PERFORMANCE,
    title: '审核员绩效',
    size: WidgetSize.LARGE,
    refreshInterval: 600
  },
  [WidgetType.RECENT_ACTIVITY]: {
    type: WidgetType.RECENT_ACTIVITY,
    title: '最近活动',
    size: WidgetSize.MEDIUM,
    refreshInterval: 120
  },
  [WidgetType.SYSTEM_ALERTS]: {
    type: WidgetType.SYSTEM_ALERTS,
    title: '系统提醒',
    size: WidgetSize.SMALL,
    refreshInterval: 300
  },
  [WidgetType.TAG_STATS]: {
    type: WidgetType.TAG_STATS,
    title: '标签统计',
    size: WidgetSize.SMALL,
    refreshInterval: 600
  },
  [WidgetType.CONTENT_DISTRIBUTION]: {
    type: WidgetType.CONTENT_DISTRIBUTION,
    title: '内容分布',
    size: WidgetSize.MEDIUM,
    refreshInterval: 600
  },
  [WidgetType.QUICK_ACTIONS]: {
    type: WidgetType.QUICK_ACTIONS,
    title: '快捷操作',
    size: WidgetSize.SMALL,
    refreshInterval: 0
  }
};

// 默认小部件
const defaultWidgets: Widget[] = [
  { id: '1', ...widgetConfigs[WidgetType.PENDING_REVIEWS] },
  { id: '2', ...widgetConfigs[WidgetType.REVIEW_STATS] },
  { id: '3', ...widgetConfigs[WidgetType.SYSTEM_ALERTS] },
  { id: '4', ...widgetConfigs[WidgetType.QUICK_ACTIONS] }
];

// 存储键
const STORAGE_KEY = 'dashboard_widgets';

/**
 * 个性化工作台组件
 */
export default function PersonalizedDashboard() {
  const { toast } = useToast();
  const [widgets, setWidgets] = useState<Widget[]>([]);
  const [isAddWidgetOpen, setIsAddWidgetOpen] = useState(false);
  const [isLoading, setIsLoading] = useState(true);

  // 加载小部件
  useEffect(() => {
    try {
      const storedWidgets = localStorage.getItem(STORAGE_KEY);

      if (storedWidgets) {
        setWidgets(JSON.parse(storedWidgets));
      } else {
        setWidgets(defaultWidgets);
      }
    } catch (error) {
      console.error('加载工作台小部件失败:', error);
      setWidgets(defaultWidgets);
    } finally {
      setIsLoading(false);
    }
  }, []);

  // 保存小部件
  const saveWidgets = (updatedWidgets: Widget[]) => {
    try {
      localStorage.setItem(STORAGE_KEY, JSON.stringify(updatedWidgets));
    } catch (error) {
      console.error('保存工作台小部件失败:', error);
    }
  };

  // 处理拖放结束
  const handleDragEnd = (result: any) => {
    if (!result.destination) return;

    const items = Array.from(widgets);
    const [reorderedItem] = items.splice(result.source.index, 1);
    items.splice(result.destination.index, 0, reorderedItem);

    setWidgets(items);
    saveWidgets(items);
  };

  // 添加小部件
  const addWidget = (type: WidgetType) => {
    const newWidget: Widget = {
      id: Date.now().toString(),
      ...widgetConfigs[type]
    };

    const updatedWidgets = [...widgets, newWidget];
    setWidgets(updatedWidgets);
    saveWidgets(updatedWidgets);

    setIsAddWidgetOpen(false);

    toast({
      title: '小部件已添加',
      description: `${newWidget.title} 已添加到工作台`,
      variant: 'default'
    });
  };

  // 移除小部件
  const removeWidget = (id: string) => {
    const updatedWidgets = widgets.filter(widget => widget.id !== id);
    setWidgets(updatedWidgets);
    saveWidgets(updatedWidgets);

    toast({
      title: '小部件已移除',
      description: '小部件已从工作台移除',
      variant: 'default'
    });
  };

  // 切换小部件折叠状态
  const toggleWidgetCollapse = (id: string) => {
    const updatedWidgets = widgets.map(widget =>
      widget.id === id ? { ...widget, isCollapsed: !widget.isCollapsed } : widget
    );
    setWidgets(updatedWidgets);
    saveWidgets(updatedWidgets);
  };

  // 刷新小部件数据
  const refreshWidget = async (id: string) => {
    const widget = widgets.find(w => w.id === id);
    if (!widget) return;

    // 更新加载状态
    setWidgets(prev => prev.map(w =>
      w.id === id ? { ...w, isLoading: true, error: undefined } : w
    ));

    try {
      let data;

      switch (widget.type) {
        case WidgetType.PENDING_REVIEWS:
          const pendingResponse = await getPendingContents('pageSize=5');
          data = pendingResponse.success ? pendingResponse : { error: '获取待审核内容失败' };
          break;

        case WidgetType.REVIEW_STATS:
          const statsResponse = await getReviewStats();
          data = statsResponse.success ? statsResponse : { error: '获取审核统计失败' };
          break;

        case WidgetType.REVIEWER_PERFORMANCE:
          const performanceResponse = await getReviewerPerformance();
          data = performanceResponse.success ? performanceResponse : { error: '获取审核员绩效失败' };
          break;

        // 其他小部件类型...
        default:
          data = { message: '模拟数据' };
      }

      // 更新小部件数据
      setWidgets(prev => prev.map(w =>
        w.id === id ? { ...w, data, isLoading: false } : w
      ));
    } catch (error) {
      console.error(`刷新小部件 ${widget.title} 失败:`, error);

      // 更新错误状态
      setWidgets(prev => prev.map(w =>
        w.id === id ? { ...w, error: '加载失败', isLoading: false } : w
      ));
    }
  };

  // 重置工作台
  const resetDashboard = () => {
    setWidgets(defaultWidgets);
    saveWidgets(defaultWidgets);

    toast({
      title: '工作台已重置',
      description: '工作台已恢复默认设置',
      variant: 'default'
    });
  };

  // 渲染小部件内容
  const renderWidgetContent = (widget: Widget) => {
    if (widget.isCollapsed) {
      return null;
    }

    if (widget.isLoading) {
      return (
        <div className="flex items-center justify-center p-6">
          <LoadingSpinner size="md" />
        </div>
      );
    }

    if (widget.error) {
      return (
        <div className="flex flex-col items-center justify-center p-6 text-destructive">
          <AlertTriangle className="h-8 w-8 mb-2" />
          <p>{widget.error}</p>
        </div>
      );
    }

    switch (widget.type) {
      case WidgetType.PENDING_REVIEWS:
        return <PendingReviewsWidget data={widget.data} />;

      case WidgetType.REVIEW_STATS:
        return <ReviewStatsWidget data={widget.data} />;

      case WidgetType.QUICK_ACTIONS:
        return <QuickActionsWidget />;

      case WidgetType.SYSTEM_ALERTS:
        return <SystemAlertsWidget data={widget.data} />;

      // 其他小部件类型...
      default:
        return (
          <div className="p-4 text-center text-muted-foreground">
            <p>暂无数据</p>
          </div>
        );
    }
  };

  // 获取小部件大小类名
  const getWidgetSizeClass = (size: WidgetSize) => {
    switch (size) {
      case WidgetSize.SMALL:
        return 'col-span-1';
      case WidgetSize.MEDIUM:
        return 'col-span-2';
      case WidgetSize.LARGE:
        return 'col-span-3';
      default:
        return 'col-span-1';
    }
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center p-12">
        <LoadingSpinner size="lg" />
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <h2 className="text-2xl font-bold">个性化工作台</h2>

        <div className="flex items-center space-x-2">
          <Button variant="outline" onClick={resetDashboard}>
            <RefreshCw className="h-4 w-4 mr-2" />
            重置
          </Button>

          <Dialog open={isAddWidgetOpen} onOpenChange={setIsAddWidgetOpen}>
            <DialogTrigger asChild>
              <Button variant="default">
                <Plus className="h-4 w-4 mr-2" />
                添加小部件
              </Button>
            </DialogTrigger>
            <DialogContent>
              <DialogHeader>
                <DialogTitle>添加小部件</DialogTitle>
                <DialogDescription>
                  选择要添加到工作台的小部件
                </DialogDescription>
              </DialogHeader>

              <div className="grid grid-cols-2 gap-4 py-4">
                {Object.entries(widgetConfigs).map(([type, config]) => (
                  <Card
                    key={type}
                    className="cursor-pointer hover:border-primary transition-colors"
                    onClick={() => addWidget(type as WidgetType)}
                  >
                    <CardHeader className="p-4">
                      <CardTitle className="text-base">{config.title}</CardTitle>
                    </CardHeader>
                  </Card>
                ))}
              </div>

              <DialogFooter>
                <Button variant="outline" onClick={() => setIsAddWidgetOpen(false)}>
                  取消
                </Button>
              </DialogFooter>
            </DialogContent>
          </Dialog>
        </div>
      </div>

      <DragDropContext onDragEnd={handleDragEnd}>
        <Droppable droppableId="widgets" direction="horizontal">
          {(provided) => (
            <div
              className="grid grid-cols-3 gap-4"
              {...provided.droppableProps}
              ref={provided.innerRef}
            >
              {widgets.map((widget, index) => (
                <Draggable key={widget.id} draggableId={widget.id} index={index}>
                  {(provided) => (
                    <div
                      ref={provided.innerRef}
                      {...provided.draggableProps}
                      className={getWidgetSizeClass(widget.size)}
                    >
                      <Card className="h-full">
                        <CardHeader className="p-4 pb-2">
                          <div className="flex items-center justify-between">
                            <div
                              className="flex items-center"
                              {...provided.dragHandleProps}
                            >
                              <GripVertical className="h-4 w-4 mr-2 text-muted-foreground cursor-move" />
                              <CardTitle className="text-base">{widget.title}</CardTitle>
                            </div>

                            <div className="flex items-center space-x-1">
                              <Button
                                variant="ghost"
                                size="icon"
                                className="h-7 w-7"
                                onClick={() => refreshWidget(widget.id)}
                              >
                                <RefreshCw className="h-4 w-4" />
                              </Button>

                              <Button
                                variant="ghost"
                                size="icon"
                                className="h-7 w-7"
                                onClick={() => toggleWidgetCollapse(widget.id)}
                              >
                                {widget.isCollapsed ? (
                                  <Maximize2 className="h-4 w-4" />
                                ) : (
                                  <Minimize2 className="h-4 w-4" />
                                )}
                              </Button>

                              <Button
                                variant="ghost"
                                size="icon"
                                className="h-7 w-7 text-destructive"
                                onClick={() => removeWidget(widget.id)}
                              >
                                <X className="h-4 w-4" />
                              </Button>
                            </div>
                          </div>
                        </CardHeader>

                        <CardContent className="p-4 pt-2">
                          {renderWidgetContent(widget)}
                        </CardContent>
                      </Card>
                    </div>
                  )}
                </Draggable>
              ))}
              {provided.placeholder}
            </div>
          )}
        </Droppable>
      </DragDropContext>
    </div>
  );
}

// 待审核内容小部件
function PendingReviewsWidget({ data }: { data?: any }) {
  if (!data || !data.success) {
    return (
      <div className="text-center p-4">
        <Button variant="outline" className="w-full">
          加载待审核内容
        </Button>
      </div>
    );
  }

  const { pendingContents } = data;

  if (!pendingContents || !Array.isArray(pendingContents) || pendingContents.length === 0) {
    return (
      <div className="text-center p-4 text-muted-foreground">
        <p>暂无待审核内容</p>
      </div>
    );
  }

  return (
    <div className="space-y-4">
      <div className="flex justify-between items-center">
        <Badge variant="outline">{pendingContents.length} 个待审核内容</Badge>
        <Button variant="link" size="sm" className="h-auto p-0">
          查看全部
        </Button>
      </div>

      <div className="space-y-2">
        {pendingContents.slice(0, 5).map((content: any) => (
          <div key={content.id} className="flex justify-between items-center p-2 border rounded-md">
            <div className="truncate">
              <span className="font-mono text-xs mr-2">{content.sequenceNumber}</span>
              <span>{content.type === 'story' && content.originalContent ? content.originalContent.title : `内容 #${content.sequenceNumber}`}</span>
            </div>
            <Button variant="outline" size="sm">
              审核
            </Button>
          </div>
        ))}
      </div>
    </div>
  );
}

// 审核统计小部件
function ReviewStatsWidget({ data }: { data?: any }) {
  if (!data || !data.success) {
    return (
      <div className="text-center p-4">
        <Button variant="outline" className="w-full">
          加载审核统计
        </Button>
      </div>
    );
  }

  const { stats } = data;

  if (!stats) {
    return (
      <div className="text-center p-4 text-muted-foreground">
        <p>暂无统计数据</p>
      </div>
    );
  }

  return (
    <div className="space-y-4">
      <div className="grid grid-cols-2 gap-4">
        <div className="bg-muted p-3 rounded-md text-center">
          <div className="text-2xl font-bold">{stats.pendingCount || 0}</div>
          <div className="text-sm text-muted-foreground">待审核</div>
        </div>

        <div className="bg-muted p-3 rounded-md text-center">
          <div className="text-2xl font-bold">{stats.approvedCount || 0}</div>
          <div className="text-sm text-muted-foreground">已通过</div>
        </div>

        <div className="bg-muted p-3 rounded-md text-center">
          <div className="text-2xl font-bold">{stats.rejectedCount || 0}</div>
          <div className="text-sm text-muted-foreground">已拒绝</div>
        </div>

        <div className="bg-muted p-3 rounded-md text-center">
          <div className="text-2xl font-bold">{stats.flaggedCount || 0}</div>
          <div className="text-sm text-muted-foreground">已标记</div>
        </div>
      </div>
    </div>
  );
}

// 快捷操作小部件
function QuickActionsWidget() {
  return (
    <div className="grid grid-cols-2 gap-2">
      <Button variant="outline" className="h-auto py-2 justify-start">
        <ListChecks className="h-4 w-4 mr-2" />
        审核内容
      </Button>

      <Button variant="outline" className="h-auto py-2 justify-start">
        <Tag className="h-4 w-4 mr-2" />
        管理标签
      </Button>

      <Button variant="outline" className="h-auto py-2 justify-start">
        <FileText className="h-4 w-4 mr-2" />
        问卷回复
      </Button>

      <Button variant="outline" className="h-auto py-2 justify-start">
        <BarChart4 className="h-4 w-4 mr-2" />
        数据分析
      </Button>

      <Button variant="outline" className="h-auto py-2 justify-start">
        <Users className="h-4 w-4 mr-2" />
        用户管理
      </Button>

      <Button variant="outline" className="h-auto py-2 justify-start">
        <Settings className="h-4 w-4 mr-2" />
        系统设置
      </Button>
    </div>
  );
}

// 系统提醒小部件
function SystemAlertsWidget({ data }: { data?: any }) {
  // 模拟数据
  const alerts = [
    { id: '1', type: 'warning', message: '系统将于今晚22:00进行维护', time: '2小时前' },
    { id: '2', type: 'info', message: '新版本已发布，请查看更新日志', time: '1天前' }
  ];

  return (
    <div className="space-y-2">
      {alerts.map(alert => (
        <div key={alert.id} className="flex items-start p-2 border rounded-md">
          <AlertTriangle className={`h-4 w-4 mr-2 ${alert.type === 'warning' ? 'text-amber-500' : 'text-blue-500'}`} />
          <div className="flex-1">
            <p className="text-sm">{alert.message}</p>
            <p className="text-xs text-muted-foreground flex items-center mt-1">
              <Clock className="h-3 w-3 mr-1" />
              {alert.time}
            </p>
          </div>
        </div>
      ))}
    </div>
  );
}
