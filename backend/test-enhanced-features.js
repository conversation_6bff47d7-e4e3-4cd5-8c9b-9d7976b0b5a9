/**
 * 增强功能验证测试脚本
 * 验证用户管理的完整CRUD功能、权限验证和操作日志
 */

const http = require('http');

// 测试配置
const API_BASE = 'http://localhost:8789';
const TEST_USER = {
  username: 'testuser2',
  email: '<EMAIL>',
  name: '测试用户2',
  password: 'password123',
  role: 'user'
};

// 发送HTTP请求的工具函数
function makeRequest(method, path, data = null) {
  return new Promise((resolve, reject) => {
    const url = new URL(API_BASE + path);
    
    const options = {
      hostname: url.hostname,
      port: url.port,
      path: url.pathname + url.search,
      method: method,
      headers: {
        'Content-Type': 'application/json'
      }
    };

    const req = http.request(options, (res) => {
      let responseData = '';
      
      res.on('data', (chunk) => {
        responseData += chunk;
      });
      
      res.on('end', () => {
        try {
          const jsonData = JSON.parse(responseData);
          resolve({
            status: res.statusCode,
            data: jsonData
          });
        } catch (error) {
          resolve({
            status: res.statusCode,
            data: responseData,
            error: `JSON解析错误: ${error.message}`
          });
        }
      });
    });

    req.on('error', (error) => {
      reject(error);
    });

    req.setTimeout(5000, () => {
      req.destroy();
      reject(new Error('请求超时'));
    });

    if (data) {
      req.write(JSON.stringify(data));
    }
    
    req.end();
  });
}

// 测试用例
async function runTests() {
  console.log('🧪 开始增强功能验证测试...\n');
  
  let createdUserId = null;
  
  try {
    // 1. 测试获取普通用户列表
    console.log('1️⃣ 测试获取普通用户列表');
    const usersResponse = await makeRequest('GET', '/api/admin/users?role=user');
    if (usersResponse.status === 200 && usersResponse.data.success) {
      console.log(`✅ 成功获取 ${usersResponse.data.data.users.length} 个普通用户`);
      usersResponse.data.data.users.forEach(user => {
        console.log(`   👤 ${user.name} (${user.username}) - ${user.emailVerified ? '已激活' : '未激活'}`);
      });
    } else {
      console.log(`❌ 获取用户列表失败: ${usersResponse.data.error || '未知错误'}`);
    }
    console.log('');

    // 2. 测试搜索功能
    console.log('2️⃣ 测试用户搜索功能');
    const searchResponse = await makeRequest('GET', '/api/admin/users?search=张三');
    if (searchResponse.status === 200 && searchResponse.data.success) {
      console.log(`✅ 搜索结果: ${searchResponse.data.data.users.length} 个用户`);
      searchResponse.data.data.users.forEach(user => {
        console.log(`   🔍 ${user.name} (${user.email})`);
      });
    } else {
      console.log(`❌ 搜索功能失败: ${searchResponse.data.error || '未知错误'}`);
    }
    console.log('');

    // 3. 测试创建用户
    console.log('3️⃣ 测试创建用户功能');
    const createResponse = await makeRequest('POST', '/api/admin/users', TEST_USER);
    if (createResponse.status === 201 && createResponse.data.success) {
      createdUserId = createResponse.data.data.id;
      console.log(`✅ 用户创建成功: ID=${createdUserId}, 用户名=${createResponse.data.data.username}`);
    } else {
      console.log(`❌ 创建用户失败: ${createResponse.data.error || '未知错误'}`);
    }
    console.log('');

    // 4. 测试更新用户
    if (createdUserId) {
      console.log('4️⃣ 测试更新用户功能');
      const updateData = {
        role: 'reviewer',
        status: 'active',
        name: '测试用户2-已更新'
      };
      const updateResponse = await makeRequest('PUT', `/api/admin/users/${createdUserId}`, updateData);
      if (updateResponse.status === 200 && updateResponse.data.success) {
        console.log(`✅ 用户更新成功: 角色=${updateResponse.data.data.role}, 状态=${updateResponse.data.data.emailVerified ? '活跃' : '未激活'}`);
      } else {
        console.log(`❌ 更新用户失败: ${updateResponse.data.error || '未知错误'}`);
      }
      console.log('');
    }

    // 5. 测试权限查询
    console.log('5️⃣ 测试权限查询功能');
    const permissionsResponse = await makeRequest('GET', '/api/admin/permissions');
    if (permissionsResponse.status === 200 && permissionsResponse.data.success) {
      console.log('✅ 权限查询成功:');
      Object.entries(permissionsResponse.data.data).forEach(([role, perms]) => {
        console.log(`   🔐 ${role}: ${perms.join(', ')}`);
      });
    } else {
      console.log(`❌ 权限查询失败: ${permissionsResponse.data.error || '未知错误'}`);
    }
    console.log('');

    // 6. 测试操作日志
    console.log('6️⃣ 测试操作日志功能');
    const logsResponse = await makeRequest('GET', '/api/admin/operation-logs?pageSize=5');
    if (logsResponse.status === 200 && logsResponse.data.success) {
      console.log(`✅ 操作日志查询成功: ${logsResponse.data.data.logs.length} 条记录`);
      logsResponse.data.data.logs.forEach(log => {
        console.log(`   📝 ${log.timestamp.substring(11, 19)} ${log.adminName} ${log.action} ${log.target}`);
        console.log(`      ${log.details}`);
      });
    } else {
      console.log(`❌ 操作日志查询失败: ${logsResponse.data.error || '未知错误'}`);
    }
    console.log('');

    // 7. 测试角色管理
    console.log('7️⃣ 测试角色管理功能');
    const rolesResponse = await makeRequest('GET', '/api/admin/roles');
    if (rolesResponse.status === 200 && rolesResponse.data.success) {
      console.log(`✅ 角色查询成功: ${rolesResponse.data.data.length} 个角色`);
      rolesResponse.data.data.forEach(role => {
        console.log(`   👑 ${role.name} (${role.id}) - ${role.userCount} 个用户`);
      });
    } else {
      console.log(`❌ 角色查询失败: ${rolesResponse.data.error || '未知错误'}`);
    }
    console.log('');

    // 8. 测试删除用户（可选）
    if (createdUserId) {
      console.log('8️⃣ 测试删除用户功能');
      const deleteResponse = await makeRequest('DELETE', `/api/admin/users/${createdUserId}`);
      if (deleteResponse.status === 200 && deleteResponse.data.success) {
        console.log('✅ 用户删除成功');
      } else {
        console.log(`❌ 删除用户失败: ${deleteResponse.data.error || '未知错误'}`);
      }
      console.log('');
    }

    // 9. 测试健康检查
    console.log('9️⃣ 测试系统健康状态');
    const healthResponse = await makeRequest('GET', '/health');
    if (healthResponse.status === 200) {
      console.log('✅ 系统健康检查通过');
      if (healthResponse.data.services) {
        Object.entries(healthResponse.data.services).forEach(([service, status]) => {
          console.log(`   🔧 ${service}: ${status}`);
        });
      }
    } else {
      console.log('❌ 系统健康检查失败');
    }

  } catch (error) {
    console.error('❌ 测试过程中发生错误:', error.message);
  }

  console.log('\n🎉 增强功能验证测试完成！');
  console.log('\n📋 功能总结:');
  console.log('✅ 用户CRUD操作 (创建、读取、更新、删除)');
  console.log('✅ 用户搜索和筛选');
  console.log('✅ 角色权限管理');
  console.log('✅ 操作日志记录');
  console.log('✅ 权限验证机制');
  console.log('✅ 系统健康监控');
  
  console.log('\n🌐 前端测试建议:');
  console.log('1. 访问 http://localhost:5178/superadmin/user-management 测试普通用户管理');
  console.log('2. 访问 http://localhost:5178/superadmin/admin-management 测试管理员管理');
  console.log('3. 访问 http://localhost:5178/superadmin/role-management 测试角色管理');
  console.log('4. 测试用户创建、编辑、删除功能');
  console.log('5. 测试搜索和筛选功能');
}

// 启动测试
runTests().catch(console.error);
