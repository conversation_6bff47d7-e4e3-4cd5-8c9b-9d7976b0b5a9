import { v4 as uuidv4 } from 'uuid';
import { AdvancedFilterOptions, SavedFilter } from '@/components/admin/AdvancedFilterDialog';

// 本地存储键
const SAVED_FILTERS_KEY = 'admin_saved_filters';
const DEFAULT_FILTER_KEY = 'admin_default_filter';

/**
 * 获取所有保存的筛选条件
 */
export function getSavedFilters(): SavedFilter[] {
  try {
    const savedFiltersJson = localStorage.getItem(SAVED_FILTERS_KEY);
    if (!savedFiltersJson) return [];
    
    const savedFilters = JSON.parse(savedFiltersJson);
    return Array.isArray(savedFilters) ? savedFilters : [];
  } catch (error) {
    console.error('获取保存的筛选条件失败:', error);
    return [];
  }
}

/**
 * 保存筛选条件
 */
export function saveFilter(name: string, description: string, filters: AdvancedFilterOptions): SavedFilter {
  try {
    const savedFilters = getSavedFilters();
    
    // 创建新的筛选条件
    const newFilter: SavedFilter = {
      id: uuidv4(),
      name,
      description,
      filters,
      createdAt: new Date().toISOString(),
    };
    
    // 添加到保存的筛选条件列表
    savedFilters.push(newFilter);
    
    // 保存到本地存储
    localStorage.setItem(SAVED_FILTERS_KEY, JSON.stringify(savedFilters));
    
    return newFilter;
  } catch (error) {
    console.error('保存筛选条件失败:', error);
    throw new Error('保存筛选条件失败');
  }
}

/**
 * 更新筛选条件
 */
export function updateFilter(id: string, updates: Partial<SavedFilter>): SavedFilter {
  try {
    const savedFilters = getSavedFilters();
    
    // 查找要更新的筛选条件
    const filterIndex = savedFilters.findIndex(filter => filter.id === id);
    if (filterIndex === -1) {
      throw new Error('筛选条件不存在');
    }
    
    // 更新筛选条件
    const updatedFilter = {
      ...savedFilters[filterIndex],
      ...updates,
    };
    
    savedFilters[filterIndex] = updatedFilter;
    
    // 保存到本地存储
    localStorage.setItem(SAVED_FILTERS_KEY, JSON.stringify(savedFilters));
    
    return updatedFilter;
  } catch (error) {
    console.error('更新筛选条件失败:', error);
    throw new Error('更新筛选条件失败');
  }
}

/**
 * 删除筛选条件
 */
export function deleteFilter(id: string): void {
  try {
    let savedFilters = getSavedFilters();
    
    // 查找要删除的筛选条件
    const filterIndex = savedFilters.findIndex(filter => filter.id === id);
    if (filterIndex === -1) {
      throw new Error('筛选条件不存在');
    }
    
    // 如果是默认筛选条件，也删除默认筛选条件设置
    const defaultFilterId = getDefaultFilterId();
    if (defaultFilterId === id) {
      localStorage.removeItem(DEFAULT_FILTER_KEY);
    }
    
    // 删除筛选条件
    savedFilters = savedFilters.filter(filter => filter.id !== id);
    
    // 保存到本地存储
    localStorage.setItem(SAVED_FILTERS_KEY, JSON.stringify(savedFilters));
  } catch (error) {
    console.error('删除筛选条件失败:', error);
    throw new Error('删除筛选条件失败');
  }
}

/**
 * 设置默认筛选条件
 */
export function setDefaultFilter(id: string): void {
  try {
    const savedFilters = getSavedFilters();
    
    // 查找要设置为默认的筛选条件
    const filter = savedFilters.find(filter => filter.id === id);
    if (!filter) {
      throw new Error('筛选条件不存在');
    }
    
    // 保存默认筛选条件ID
    localStorage.setItem(DEFAULT_FILTER_KEY, id);
    
    // 更新所有筛选条件的isDefault属性
    const updatedFilters = savedFilters.map(filter => ({
      ...filter,
      isDefault: filter.id === id,
    }));
    
    // 保存到本地存储
    localStorage.setItem(SAVED_FILTERS_KEY, JSON.stringify(updatedFilters));
  } catch (error) {
    console.error('设置默认筛选条件失败:', error);
    throw new Error('设置默认筛选条件失败');
  }
}

/**
 * 获取默认筛选条件ID
 */
export function getDefaultFilterId(): string | null {
  try {
    return localStorage.getItem(DEFAULT_FILTER_KEY);
  } catch (error) {
    console.error('获取默认筛选条件失败:', error);
    return null;
  }
}

/**
 * 获取默认筛选条件
 */
export function getDefaultFilter(): SavedFilter | null {
  try {
    const defaultFilterId = getDefaultFilterId();
    if (!defaultFilterId) return null;
    
    const savedFilters = getSavedFilters();
    return savedFilters.find(filter => filter.id === defaultFilterId) || null;
  } catch (error) {
    console.error('获取默认筛选条件失败:', error);
    return null;
  }
}

/**
 * 获取所有可用标签
 */
export function getAvailableTags(): string[] {
  try {
    // 从所有保存的筛选条件中提取标签
    const savedFilters = getSavedFilters();
    const tagSets = savedFilters
      .map(filter => filter.filters.hasTag || [])
      .filter(tags => tags.length > 0);
    
    // 合并所有标签并去重
    const allTags = Array.from(new Set(tagSets.flat()));
    
    return allTags;
  } catch (error) {
    console.error('获取可用标签失败:', error);
    return [];
  }
}
