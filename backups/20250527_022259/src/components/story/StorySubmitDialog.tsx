import { useState, useEffect } from 'react';
import { z } from 'zod';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Button } from '@/components/ui/button';
import { Checkbox } from '@/components/ui/checkbox';
import { Badge } from '@/components/ui/badge';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { useToast } from '@/components/ui/use-toast';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import { Info } from 'lucide-react';
import { submitStory } from '@/lib/api';
import ImprovedMultiTagSelector from './ImprovedMultiTagSelector';
import { AnonymousIdentityInput } from '@/components/anonymous-auth/AnonymousIdentityInput';
import { processContentWithDeidentification } from '@/services/deidentificationService';

// Story tags
const STORY_TAGS = [
  // 求职相关标签
  { id: 'job-hunting', label: '求职故事', category: 'job', color: 'blue' },
  { id: 'interview', label: '面试经验', category: 'job', color: 'blue' },
  { id: 'resume', label: '简历技巧', category: 'job', color: 'blue' },
  { id: 'job-search', label: '找工作', category: 'job', color: 'blue' },
  { id: 'salary', label: '薪资谈判', category: 'job', color: 'blue' },
  { id: 'offer', label: 'Offer选择', category: 'job', color: 'blue' },

  // 学历相关标签
  { id: 'bachelor', label: '本科经验', category: 'education', color: 'green' },
  { id: 'master', label: '硕士经验', category: 'education', color: 'green' },
  { id: 'phd', label: '博士经验', category: 'education', color: 'green' },
  { id: 'overseas-edu', label: '海外学历', category: 'education', color: 'green' },
  { id: 'continuing-edu', label: '继续教育', category: 'education', color: 'green' },
  { id: 'self-taught', label: '自学成才', category: 'education', color: 'green' },

  // 行业相关标签
  { id: 'it-industry', label: 'IT行业', category: 'industry', color: 'purple' },
  { id: 'finance', label: '金融行业', category: 'industry', color: 'purple' },
  { id: 'education-industry', label: '教育行业', category: 'industry', color: 'purple' },
  { id: 'healthcare', label: '医疗行业', category: 'industry', color: 'purple' },
  { id: 'manufacturing', label: '制造业', category: 'industry', color: 'purple' },
  { id: 'service', label: '服务业', category: 'industry', color: 'purple' },

  // 经验相关标签
  { id: 'career-change', label: '转行经历', category: 'experience', color: 'yellow' },
  { id: 'work-life', label: '工作生活', category: 'experience', color: 'yellow' },
  { id: 'advice', label: '建议分享', category: 'experience', color: 'yellow' },
  { id: 'internship', label: '实习经历', category: 'experience', color: 'yellow' },
  { id: 'overseas', label: '海外就业', category: 'experience', color: 'yellow' },
  { id: 'startup', label: '创业经历', category: 'experience', color: 'yellow' },
  { id: 'remote-work', label: '远程工作', category: 'experience', color: 'yellow' },
  { id: 'freelance', label: '自由职业', category: 'experience', color: 'yellow' },

  // 其他标签
  { id: 'success', label: '成功故事', category: 'other', color: 'gray' },
  { id: 'challenge', label: '挑战经历', category: 'other', color: 'gray' },
  { id: 'inspiration', label: '励志故事', category: 'other', color: 'gray' },
];

// Story categories
const STORY_CATEGORIES = [
  { id: 'success', label: '成功故事' },
  { id: 'challenge', label: '挑战经历' },
  { id: 'advice', label: '建议分享' },
  { id: 'experience', label: '经验分享' },
];

// Education levels
const EDUCATION_LEVELS = [
  { id: 'high-school', label: '高中及以下' },
  { id: 'college', label: '专科' },
  { id: 'bachelor', label: '本科' },
  { id: 'master', label: '硕士' },
  { id: 'phd', label: '博士' },
];

// Industries
const INDUSTRIES = [
  { id: 'it', label: '互联网/IT' },
  { id: 'finance', label: '金融' },
  { id: 'education', label: '教育' },
  { id: 'healthcare', label: '医疗' },
  { id: 'manufacturing', label: '制造业' },
  { id: 'service', label: '服务业' },
  { id: 'government', label: '政府/公共服务' },
  { id: 'media', label: '媒体/文化' },
];

// Form schema
const formSchema = z.object({
  title: z.string().min(5, { message: '标题至少需要5个字符' }).max(50, { message: '标题最多50个字符' }),
  content: z.string().min(20, { message: '内容至少需要20个字符' }).max(2000, { message: '内容最多2000个字符' }),
  isAnonymous: z.boolean().default(true),
  tags: z.array(z.string()).optional(),
  category: z.string().optional(),
  educationLevel: z.string().optional(),
  industry: z.string().optional(),
});

interface StorySubmitDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
}

export default function StorySubmitDialog({
  open,
  onOpenChange
}: StorySubmitDialogProps) {
  const { toast } = useToast();
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [selectedTags, setSelectedTags] = useState<string[]>([]);
  const [selectedCategory, setSelectedCategory] = useState<string>('');
  const [selectedEducationLevel, setSelectedEducationLevel] = useState<string>('');
  const [selectedIndustry, setSelectedIndustry] = useState<string>('');
  const [deidentificationEnabled, setDeidentificationEnabled] = useState<boolean>(false);
  const [deidentificationLevel, setDeidentificationLevel] = useState<string>('medium');

  // 匿名身份验证状态
  const [identityA, setIdentityA] = useState<string>('');
  const [identityB, setIdentityB] = useState<string>('');
  const [identityEnabled, setIdentityEnabled] = useState<boolean>(false);

  // 获取脱敏设置
  useEffect(() => {
    const fetchDeidentificationSettings = async () => {
      try {
        const response = await fetch('/api/admin/deidentification/config');
        if (response.ok) {
          const data = await response.json();
          if (data.success && data.config) {
            setDeidentificationEnabled(data.config.enabled && data.config.applyToStories);
            setDeidentificationLevel(data.config.level);
          }
        }
      } catch (error) {
        console.error('Error fetching deidentification settings:', error);
      }
    };

    fetchDeidentificationSettings();
  }, []);

  // Initialize form
  const form = useForm<z.infer<typeof formSchema>>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      title: '',
      content: '',
      isAnonymous: true,
      tags: [],
    },
  });

  // Handle form submission
  const onSubmit = async (values: z.infer<typeof formSchema>) => {
    try {
      setIsSubmitting(true);

      // Add tags to form data
      values.tags = selectedTags;

      // 准备基础故事数据
      let storyData = {
        title: values.title,
        content: values.content,
        isAnonymous: values.isAnonymous,
        tags: selectedTags,
        category: selectedCategory,
        educationLevel: selectedEducationLevel,
        industry: selectedIndustry,
        // 添加匿名身份验证信息
        identityA: identityEnabled ? identityA : undefined,
        identityB: identityEnabled ? identityB : undefined,
      };

      // 对故事内容进行脱敏处理
      console.log('🔒 开始对故事内容进行脱敏处理...');

      try {
        // 处理故事标题
        const titleResult = await processContentWithDeidentification(
          storyData.title,
          'story',
          identityEnabled ? `${identityA}-${identityB}` : 'anonymous'
        );

        storyData.title = titleResult.content;

        // 处理故事内容
        const contentResult = await processContentWithDeidentification(
          storyData.content,
          'story',
          identityEnabled ? `${identityA}-${identityB}` : 'anonymous'
        );

        storyData.content = contentResult.content;

        // 如果需要审核，提醒用户
        if (titleResult.needsReview || contentResult.needsReview) {
          console.log('⚠️ 故事内容需要人工审核');
          toast({
            title: '内容审核提醒',
            description: '您的故事内容将在审核通过后显示在故事墙上',
            variant: 'default',
          });
        }

        console.log('✅ 故事内容脱敏处理完成');
      } catch (deidentifyError) {
        console.error('❌ 故事内容脱敏处理失败:', deidentifyError);
        // 脱敏失败不阻止提交，但记录错误
        toast({
          title: '内容处理提醒',
          description: '内容安全检查遇到问题，但不影响提交',
          variant: 'default',
        });
      }

      // Submit story
      const response = await submitStory(storyData);

      if (response.success) {
        toast({
          title: '提交成功',
          description: '您的故事已成功提交，需要管理员审核后才会显示在故事墙上',
        });

        // Reset form and close dialog
        form.reset();
        setSelectedTags([]);
        setSelectedCategory('');
        setSelectedEducationLevel('');
        setSelectedIndustry('');
        onOpenChange(false);
      } else {
        toast({
          title: '提交失败',
          description: '故事提交失败，请稍后重试',
          variant: 'destructive',
        });
      }
    } catch (error) {
      console.error('Error submitting story:', error);
      toast({
        title: '提交失败',
        description: '发生错误，请稍后重试',
        variant: 'destructive',
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  // Handle tag selection
  const toggleTag = (tagId: string) => {
    setSelectedTags(prev =>
      prev.includes(tagId)
        ? prev.filter(id => id !== tagId)
        : [...prev, tagId]
    );
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[600px] max-h-[90vh] flex flex-col">
        <DialogHeader>
          <DialogTitle>分享你的故事</DialogTitle>
          <DialogDescription>
            分享你的就业经历、求职故事或建议，帮助其他人获取灵感和经验。
          </DialogDescription>
        </DialogHeader>

        <Form {...form} className="flex-grow overflow-hidden flex flex-col">
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6 overflow-y-auto pr-1 flex-grow">
            <FormField
              control={form.control}
              name="title"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>标题</FormLabel>
                  <FormControl>
                    <Input placeholder="请输入故事标题" {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="content"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>内容</FormLabel>
                  <FormControl>
                    <Textarea
                      placeholder="请分享你的经历、故事或建议..."
                      className="min-h-[200px] resize-none"
                      {...field}
                    />
                  </FormControl>
                  <div className="text-xs text-gray-500 flex justify-between">
                    <span>{field.value.length}/2000</span>
                    <span>至少20个字符</span>
                  </div>
                  <FormMessage />
                </FormItem>
              )}
            />

            {deidentificationEnabled && (
              <Alert className="mb-4">
                <Info className="h-4 w-4" />
                <AlertTitle>内容脱敏提示</AlertTitle>
                <AlertDescription>
                  为保护您的隐私，系统将自动对您提交的内容进行脱敏处理，
                  {deidentificationLevel === 'low' && '替换明显的个人身份信息，如具体姓名、精确地点等。'}
                  {deidentificationLevel === 'medium' && '替换可能暴露身份的信息，包括人名、地点、学校、公司等。'}
                  {deidentificationLevel === 'high' && '最大程度替换和泛化可能识别个人的信息，确保内容无法被用于识别个人。'}
                </AlertDescription>
              </Alert>
            )}

            <div className="space-y-4">
              <div>
                <FormLabel>标签</FormLabel>
                <div className="mt-2">
                  <ImprovedMultiTagSelector
                    tags={STORY_TAGS.map(tag => ({
                      id: tag.id,
                      label: tag.label,
                      category: tag.category,
                      color: tag.color
                    }))}
                    selectedTags={selectedTags}
                    onTagsChange={setSelectedTags}
                    maxTags={5}
                    content={form.getValues().content}
                    showRecommendations={true}
                  />
                </div>
                <p className="text-xs text-gray-500 mt-1">
                  选择1-5个标签，帮助其他人更好地找到你的故事
                </p>
              </div>

              <div>
                <FormLabel>分类</FormLabel>
                <Select value={selectedCategory} onValueChange={setSelectedCategory}>
                  <SelectTrigger className="w-full">
                    <SelectValue placeholder="选择故事分类" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="">不分类</SelectItem>
                    {STORY_CATEGORIES.map((category) => (
                      <SelectItem key={category.id} value={category.id}>
                        {category.label}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
                <p className="text-xs text-gray-500 mt-1">
                  选择一个分类，帮助组织你的故事
                </p>
              </div>

              <div>
                <FormLabel>学历背景</FormLabel>
                <Select value={selectedEducationLevel} onValueChange={setSelectedEducationLevel}>
                  <SelectTrigger className="w-full">
                    <SelectValue placeholder="选择你的学历" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="">不指定</SelectItem>
                    {EDUCATION_LEVELS.map((level) => (
                      <SelectItem key={level.id} value={level.id}>
                        {level.label}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              <div>
                <FormLabel>行业领域</FormLabel>
                <Select value={selectedIndustry} onValueChange={setSelectedIndustry}>
                  <SelectTrigger className="w-full">
                    <SelectValue placeholder="选择行业领域" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="">不指定</SelectItem>
                    {INDUSTRIES.map((industry) => (
                      <SelectItem key={industry.id} value={industry.id}>
                        {industry.label}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
            </div>

            <FormField
              control={form.control}
              name="isAnonymous"
              render={({ field }) => (
                <FormItem className="flex flex-row items-start space-x-3 space-y-0">
                  <FormControl>
                    <Checkbox
                      checked={field.value}
                      onCheckedChange={field.onChange}
                    />
                  </FormControl>
                  <div className="space-y-1 leading-none">
                    <FormLabel>匿名发布</FormLabel>
                    <p className="text-xs text-gray-500">
                      选择匿名发布，您的身份信息将不会被显示
                    </p>
                  </div>
                </FormItem>
              )}
            />

            {/* 匿名身份验证 */}
            <div className="pt-2 border-t">
              <AnonymousIdentityInput
                onIdentityChange={(a, b, enabled) => {
                  setIdentityA(a);
                  setIdentityB(b);
                  setIdentityEnabled(enabled);
                }}
              />
            </div>

            <DialogFooter className="mt-auto pt-4 border-t">
              <Button
                type="button"
                variant="outline"
                onClick={() => onOpenChange(false)}
                disabled={isSubmitting}
              >
                取消
              </Button>
              <Button type="submit" disabled={isSubmitting}>
                {isSubmitting ? '提交中...' : '提交故事'}
              </Button>
            </DialogFooter>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  );
}
