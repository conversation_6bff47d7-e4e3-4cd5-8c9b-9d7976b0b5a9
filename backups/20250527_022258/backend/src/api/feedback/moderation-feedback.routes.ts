/**
 * 审核反馈路由
 *
 * 处理用户对审核结果的反馈的路由
 */

import { Hono } from 'hono';
import { Env } from '../../types';
import * as moderationFeedbackController from './moderation-feedback.controller';
import { adminAuthMiddleware } from '../../middlewares/adminAuth.middleware';
import { feedbackModeration } from '../../middlewares/contentModeration.middleware';

// 创建路由
const app = new Hono<{ Bindings: Env }>();

// 提交审核反馈
app.post('/submit',
  // 使用同步审核中间件
  feedbackModeration({ async: false }),
  ...moderationFeedbackController.submitModerationFeedback
);

// 获取反馈状态
app.get('/:id', moderationFeedbackController.getFeedbackStatus);

// 获取内容的反馈列表（需要管理员权限）
app.get('/content', adminAuthMiddleware, moderationFeedbackController.getContentFeedbacks);

export default app;
