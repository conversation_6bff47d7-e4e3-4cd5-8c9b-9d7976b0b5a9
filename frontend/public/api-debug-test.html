<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>API连接调试测试</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
            color: white;
        }
        
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            padding: 40px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }
        
        h1 {
            font-size: 2rem;
            margin-bottom: 20px;
            text-align: center;
            background: linear-gradient(45deg, #fff, #f0f0f0);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }
        
        .test-item {
            background: rgba(255, 255, 255, 0.05);
            border-radius: 8px;
            padding: 15px;
            margin: 10px 0;
            display: flex;
            align-items: center;
            justify-content: space-between;
        }
        
        .status {
            padding: 5px 15px;
            border-radius: 20px;
            font-size: 0.9rem;
            font-weight: bold;
        }
        
        .status.success { background: #4CAF50; color: white; }
        .status.error { background: #f44336; color: white; }
        .status.testing { background: #ff9800; color: white; }
        
        button {
            background: linear-gradient(45deg, #4CAF50, #45a049);
            color: white;
            border: none;
            padding: 12px 25px;
            border-radius: 25px;
            font-size: 1rem;
            cursor: pointer;
            margin: 10px;
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(76, 175, 80, 0.3);
        }
        
        button:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(76, 175, 80, 0.4);
        }
        
        .response-box {
            background: rgba(0, 0, 0, 0.3);
            border-radius: 10px;
            padding: 15px;
            margin: 10px 0;
            font-family: 'Courier New', monospace;
            font-size: 0.9rem;
            max-height: 300px;
            overflow-y: auto;
            white-space: pre-wrap;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔍 API连接调试测试</h1>
        
        <div class="test-item">
            <span>环境检测</span>
            <span class="status testing" id="env-status">⏳ 检测中</span>
        </div>
        
        <div class="test-item">
            <span>API基础URL</span>
            <span class="status testing" id="api-url-status">⏳ 检测中</span>
        </div>
        
        <div class="test-item">
            <span>API连接测试</span>
            <span class="status testing" id="api-test-status">⏳ 待测试</span>
        </div>
        
        <button onclick="runTests()">🚀 运行测试</button>
        <button onclick="copyResults()">📋 复制结果</button>
        
        <div class="response-box" id="results"></div>
    </div>

    <script>
        let testResults = {};
        
        function runTests() {
            document.getElementById('results').textContent = '正在运行测试...\n';
            
            // 环境检测
            const hostname = window.location.hostname;
            const isLocal = hostname === 'localhost' || hostname === '127.0.0.1';
            const isPages = hostname.includes('.pages.dev');
            
            let environment = 'unknown';
            if (isLocal) environment = 'local';
            else if (isPages) environment = 'production';
            
            document.getElementById('env-status').textContent = `✅ ${environment}`;
            document.getElementById('env-status').className = 'status success';
            
            // API URL检测
            let apiBaseUrl = '';
            if (isLocal) {
                apiBaseUrl = 'http://localhost:8788';
            } else {
                apiBaseUrl = 'https://college-employment-survey.aibook2099.workers.dev';
            }
            
            document.getElementById('api-url-status').textContent = `✅ ${apiBaseUrl}`;
            document.getElementById('api-url-status').className = 'status success';
            
            // API连接测试
            testApiConnection(apiBaseUrl);
            
            testResults = {
                timestamp: new Date().toISOString(),
                environment,
                hostname,
                apiBaseUrl,
                userAgent: navigator.userAgent
            };
        }
        
        async function testApiConnection(apiBaseUrl) {
            try {
                const startTime = Date.now();
                const response = await fetch(`${apiBaseUrl}/api/questionnaire/stats`);
                const responseTime = Date.now() - startTime;
                
                if (response.ok) {
                    const data = await response.json();
                    document.getElementById('api-test-status').textContent = `✅ ${responseTime}ms`;
                    document.getElementById('api-test-status').className = 'status success';
                    
                    document.getElementById('results').textContent = 
                        `✅ API连接成功！\n` +
                        `响应时间: ${responseTime}ms\n` +
                        `状态码: ${response.status}\n` +
                        `数据: ${JSON.stringify(data, null, 2)}`;
                        
                    testResults.apiTest = {
                        success: true,
                        responseTime,
                        status: response.status,
                        data
                    };
                } else {
                    throw new Error(`HTTP ${response.status}`);
                }
            } catch (error) {
                document.getElementById('api-test-status').textContent = `❌ 失败`;
                document.getElementById('api-test-status').className = 'status error';
                
                document.getElementById('results').textContent = 
                    `❌ API连接失败！\n` +
                    `错误: ${error.message}\n` +
                    `API URL: ${apiBaseUrl}\n` +
                    `请检查网络连接和API配置`;
                    
                testResults.apiTest = {
                    success: false,
                    error: error.message
                };
            }
        }
        
        function copyResults() {
            const report = `# API连接调试测试报告
生成时间: ${new Date().toLocaleString()}
测试域名: ${window.location.hostname}
测试URL: ${window.location.href}

## 测试结果
${JSON.stringify(testResults, null, 2)}

## 环境信息
- 浏览器: ${navigator.userAgent}
- 在线状态: ${navigator.onLine ? '在线' : '离线'}
- 语言: ${navigator.language}

---
报告生成完成`;

            navigator.clipboard.writeText(report).then(() => {
                alert('测试结果已复制到剪贴板！');
            }).catch(() => {
                const textarea = document.createElement('textarea');
                textarea.value = report;
                document.body.appendChild(textarea);
                textarea.select();
                document.execCommand('copy');
                document.body.removeChild(textarea);
                alert('测试结果已复制到剪贴板！');
            });
        }
        
        // 页面加载后自动运行测试
        document.addEventListener('DOMContentLoaded', function() {
            setTimeout(runTests, 1000);
        });
    </script>
</body>
</html>
