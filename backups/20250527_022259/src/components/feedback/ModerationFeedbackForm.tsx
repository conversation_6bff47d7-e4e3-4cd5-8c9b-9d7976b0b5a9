import React, { useState } from 'react';
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { useToast } from '@/components/ui/use-toast';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import * as z from 'zod';
import { useApi } from '@/hooks/useApi';
import { AlertCircle, CheckCircle, MessageSquare } from 'lucide-react';

// 定义表单验证模式
const feedbackFormSchema = z.object({
  feedbackType: z.enum(['disagree', 'appeal', 'report', 'suggestion'], {
    required_error: '请选择反馈类型',
  }),
  reason: z.string()
    .min(5, { message: '原因至少需要5个字符' })
    .max(500, { message: '原因最多500个字符' }),
  details: z.string()
    .max(2000, { message: '详情最多2000个字符' })
    .optional(),
  contactEmail: z.string()
    .email({ message: '请提供有效的邮箱地址' })
    .optional(),
});

// 定义表单数据类型
type FeedbackFormValues = z.infer<typeof feedbackFormSchema>;

interface ModerationFeedbackFormProps {
  contentId: string;
  contentType: string;
  moderationId?: string;
  onSuccess?: (feedbackId: string) => void;
  onCancel?: () => void;
}

/**
 * 审核反馈表单组件
 */
const ModerationFeedbackForm: React.FC<ModerationFeedbackFormProps> = ({
  contentId,
  contentType,
  moderationId,
  onSuccess,
  onCancel,
}) => {
  const { toast } = useToast();
  const api = useApi();
  const [isSubmitting, setIsSubmitting] = useState(false);

  // 初始化表单
  const form = useForm<FeedbackFormValues>({
    resolver: zodResolver(feedbackFormSchema),
    defaultValues: {
      feedbackType: 'disagree',
      reason: '',
      details: '',
      contactEmail: '',
    },
  });

  // 提交表单
  const onSubmit = async (values: FeedbackFormValues) => {
    setIsSubmitting(true);

    try {
      const response = await api.post('/feedback/moderation/submit', {
        contentId,
        contentType,
        moderationId,
        ...values,
      });

      if (response.success) {
        toast({
          title: '反馈已提交',
          description: '感谢您的反馈，我们会尽快处理',
          variant: 'default',
        });

        if (onSuccess) {
          onSuccess(response.feedbackId);
        }
      } else {
        toast({
          title: '提交失败',
          description: response.error || '提交反馈失败，请稍后再试',
          variant: 'destructive',
        });
      }
    } catch (error) {
      console.error('提交反馈失败:', error);
      toast({
        title: '提交失败',
        description: '服务器错误，请稍后再试',
        variant: 'destructive',
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <Card className="w-full max-w-md mx-auto">
      <CardHeader>
        <CardTitle className="flex items-center">
          <MessageSquare className="w-5 h-5 mr-2" />
          提交反馈
        </CardTitle>
        <CardDescription>
          对审核结果有疑问？请提交您的反馈
        </CardDescription>
      </CardHeader>
      <CardContent>
        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
            <FormField
              control={form.control}
              name="feedbackType"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>反馈类型</FormLabel>
                  <Select
                    onValueChange={field.onChange}
                    defaultValue={field.value}
                    disabled={isSubmitting}
                  >
                    <FormControl>
                      <SelectTrigger>
                        <SelectValue placeholder="选择反馈类型" />
                      </SelectTrigger>
                    </FormControl>
                    <SelectContent>
                      <SelectItem value="disagree">我不同意审核结果</SelectItem>
                      <SelectItem value="appeal">我要申诉</SelectItem>
                      <SelectItem value="report">举报问题</SelectItem>
                      <SelectItem value="suggestion">提出建议</SelectItem>
                    </SelectContent>
                  </Select>
                  <FormDescription>
                    请选择最符合您情况的反馈类型
                  </FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="reason"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>反馈原因</FormLabel>
                  <FormControl>
                    <Textarea
                      placeholder="请简要说明您的反馈原因"
                      className="min-h-[80px]"
                      {...field}
                      disabled={isSubmitting}
                    />
                  </FormControl>
                  <FormDescription>
                    简要说明您的反馈原因（5-500字符）
                  </FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="details"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>详细说明（可选）</FormLabel>
                  <FormControl>
                    <Textarea
                      placeholder="请详细说明情况，提供更多信息"
                      className="min-h-[120px]"
                      {...field}
                      disabled={isSubmitting}
                    />
                  </FormControl>
                  <FormDescription>
                    提供更多详细信息，帮助我们更好地理解您的反馈（最多2000字符）
                  </FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="contactEmail"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>联系邮箱（可选）</FormLabel>
                  <FormControl>
                    <Input
                      placeholder="<EMAIL>"
                      {...field}
                      disabled={isSubmitting}
                    />
                  </FormControl>
                  <FormDescription>
                    留下您的邮箱，我们会将处理结果通知您
                  </FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            />

            <div className="flex justify-end space-x-2 pt-2">
              {onCancel && (
                <Button
                  type="button"
                  variant="outline"
                  onClick={onCancel}
                  disabled={isSubmitting}
                >
                  取消
                </Button>
              )}
              <Button
                type="submit"
                disabled={isSubmitting}
              >
                {isSubmitting ? (
                  <>
                    <span className="animate-spin mr-2">⏳</span>
                    提交中...
                  </>
                ) : (
                  <>
                    <CheckCircle className="w-4 h-4 mr-2" />
                    提交反馈
                  </>
                )}
              </Button>
            </div>
          </form>
        </Form>
      </CardContent>
      <CardFooter className="flex justify-center border-t pt-4">
        <div className="flex items-center text-sm text-muted-foreground">
          <AlertCircle className="w-4 h-4 mr-2" />
          我们会认真对待每一条反馈，并尽快处理
        </div>
      </CardFooter>
    </Card>
  );
};

export default ModerationFeedbackForm;
