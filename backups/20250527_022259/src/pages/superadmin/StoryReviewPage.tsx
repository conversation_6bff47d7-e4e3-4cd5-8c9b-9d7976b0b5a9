import React from 'react';
import SuperAdminLayout from '@/components/layouts/SuperAdminLayout';
import StoryModerationPanel from '@/components/admin/StoryModerationPanel';

/**
 * 超级管理员故事审核页面
 * 
 * 专门用于审核用户提交的故事内容
 * 只有审核通过的故事才会显示在故事墙上
 */
const SuperAdminStoryReviewPage: React.FC = () => {
  return (
    <SuperAdminLayout>
      <div className="container mx-auto py-6">
        <div className="flex justify-between items-center mb-6">
          <div>
            <h1 className="text-2xl font-bold">故事审核</h1>
            <p className="text-gray-500 mt-1">审核用户提交的故事，只有审核通过的故事才会显示在故事墙上</p>
          </div>
        </div>
        
        <StoryModerationPanel />
      </div>
    </SuperAdminLayout>
  );
};

export default SuperAdminStoryReviewPage;
