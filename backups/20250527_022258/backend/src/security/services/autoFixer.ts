/**
 * 自动修复服务
 * 
 * 负责自动修复常见错误
 */

import { AutoFixConfig, FixStrategyConfig } from '../types';
import { SecurityError } from '../errors';

/**
 * 修复结果
 */
interface FixResult {
  success: boolean;
  error?: string;
  action?: string;
  details?: any;
}

/**
 * 修复历史记录
 */
interface FixHistory {
  id: string;
  timestamp: number;
  errorCode: string;
  strategyId: string;
  success: boolean;
  error?: string;
  details?: any;
}

/**
 * 自动修复服务类
 */
export class AutoFixer {
  private config: AutoFixConfig;
  private fixHistory: FixHistory[] = [];
  private fixAttempts: Map<string, number> = new Map();
  private lastFixTime: Map<string, number> = new Map();

  /**
   * 构造函数
   */
  constructor() {
    this.config = {
      enabled: false,
      maxAttemptsPerError: 3,
      cooldownPeriod: 3600000, // 1小时
      requireApproval: true,
      notifyOnFix: true,
      fixStrategies: []
    };
    
    // 初始化预定义修复策略
    this.initializeFixStrategies();
  }

  /**
   * 初始化服务
   * @param config 自动修复配置
   */
  public initialize(config: AutoFixConfig): void {
    this.config = { ...config };
  }

  /**
   * 更新配置
   * @param config 自动修复配置
   */
  public updateConfig(config: Partial<AutoFixConfig>): void {
    this.config = { ...this.config, ...config };
  }

  /**
   * 尝试修复错误
   * @param error 错误对象
   * @param context 上下文信息
   */
  public async tryFix(
    error: SecurityError,
    context?: any
  ): Promise<FixResult> {
    if (!this.config.enabled) {
      return { success: false, error: 'Auto-fix is disabled' };
    }

    // 如果没有错误代码，无法修复
    if (!error.code) {
      return { success: false, error: 'No error code provided' };
    }

    // 检查是否超过最大尝试次数
    const errorKey = `${error.code}_${error.severity || 'medium'}`;
    const attempts = this.fixAttempts.get(errorKey) || 0;
    
    if (attempts >= this.config.maxAttemptsPerError) {
      return { 
        success: false, 
        error: `Exceeded maximum fix attempts (${this.config.maxAttemptsPerError})` 
      };
    }

    // 检查冷却期
    const now = Date.now();
    const lastFixTime = this.lastFixTime.get(errorKey) || 0;
    
    if (now - lastFixTime < this.config.cooldownPeriod) {
      const remainingTime = Math.ceil((this.config.cooldownPeriod - (now - lastFixTime)) / 60000);
      return { 
        success: false, 
        error: `In cooldown period, try again in ${remainingTime} minutes` 
      };
    }

    // 查找适用的修复策略
    const strategy = this.findFixStrategy(error.code);
    
    if (!strategy) {
      return { success: false, error: 'No fix strategy available for this error' };
    }

    // 执行修复
    try {
      // 更新尝试次数和最后修复时间
      this.fixAttempts.set(errorKey, attempts + 1);
      this.lastFixTime.set(errorKey, now);
      
      // 执行修复策略
      const result = await this.executeFixStrategy(strategy, error, context);
      
      // 记录修复历史
      this.recordFixHistory({
        id: `fix_${Date.now()}_${Math.random().toString(36).substring(2, 9)}`,
        timestamp: now,
        errorCode: error.code,
        strategyId: strategy.id,
        success: result.success,
        error: result.error,
        details: result.details
      });
      
      return result;
    } catch (fixError) {
      // 记录修复失败
      this.recordFixHistory({
        id: `fix_${Date.now()}_${Math.random().toString(36).substring(2, 9)}`,
        timestamp: now,
        errorCode: error.code,
        strategyId: strategy.id,
        success: false,
        error: fixError instanceof Error ? fixError.message : String(fixError)
      });
      
      return { 
        success: false, 
        error: `Fix attempt failed: ${fixError instanceof Error ? fixError.message : String(fixError)}` 
      };
    }
  }

  /**
   * 查找修复策略
   * @param errorCode 错误代码
   */
  private findFixStrategy(errorCode: string): FixStrategyConfig | undefined {
    return this.config.fixStrategies.find(strategy => 
      strategy.enabled && strategy.targetErrorCodes.includes(errorCode)
    );
  }

  /**
   * 执行修复策略
   * @param strategy 修复策略
   * @param error 错误对象
   * @param context 上下文信息
   */
  private async executeFixStrategy(
    strategy: FixStrategyConfig,
    error: SecurityError,
    context?: any
  ): Promise<FixResult> {
    // 根据策略类型执行不同的修复操作
    switch (strategy.action) {
      case 'clearRateLimit':
        return this.clearRateLimit(strategy, error, context);
      case 'resetFingerprint':
        return this.resetFingerprint(strategy, error, context);
      case 'updateConfiguration':
        return this.updateConfiguration(strategy, error, context);
      case 'restartService':
        return this.restartService(strategy, error, context);
      case 'blockIP':
        return this.blockIP(strategy, error, context);
      default:
        return { success: false, error: `Unknown fix action: ${strategy.action}` };
    }
  }

  /**
   * 清除速率限制
   * @param strategy 修复策略
   * @param error 错误对象
   * @param context 上下文信息
   */
  private async clearRateLimit(
    strategy: FixStrategyConfig,
    error: SecurityError,
    context?: any
  ): Promise<FixResult> {
    // 这里只是模拟实现，实际应用中需要集成速率限制服务
    console.log(`[AUTO-FIX] Would clear rate limit for: ${error.clientIp || 'unknown IP'}`);
    
    // 模拟成功
    return {
      success: true,
      action: 'clearRateLimit',
      details: {
        ip: error.clientIp,
        timestamp: Date.now()
      }
    };
  }

  /**
   * 重置指纹
   * @param strategy 修复策略
   * @param error 错误对象
   * @param context 上下文信息
   */
  private async resetFingerprint(
    strategy: FixStrategyConfig,
    error: SecurityError,
    context?: any
  ): Promise<FixResult> {
    // 这里只是模拟实现，实际应用中需要集成指纹服务
    const fingerprint = error.context?.fingerprint || context?.fingerprint;
    
    if (!fingerprint) {
      return { success: false, error: 'No fingerprint provided' };
    }
    
    console.log(`[AUTO-FIX] Would reset fingerprint: ${fingerprint}`);
    
    // 模拟成功
    return {
      success: true,
      action: 'resetFingerprint',
      details: {
        fingerprint,
        timestamp: Date.now()
      }
    };
  }

  /**
   * 更新配置
   * @param strategy 修复策略
   * @param error 错误对象
   * @param context 上下文信息
   */
  private async updateConfiguration(
    strategy: FixStrategyConfig,
    error: SecurityError,
    context?: any
  ): Promise<FixResult> {
    // 这里只是模拟实现，实际应用中需要集成配置服务
    if (!strategy.parameters || !strategy.parameters.configPath || !strategy.parameters.configValue) {
      return { success: false, error: 'Missing configuration parameters' };
    }
    
    const { configPath, configValue } = strategy.parameters;
    
    console.log(`[AUTO-FIX] Would update configuration: ${configPath} = ${configValue}`);
    
    // 模拟成功
    return {
      success: true,
      action: 'updateConfiguration',
      details: {
        configPath,
        configValue,
        timestamp: Date.now()
      }
    };
  }

  /**
   * 重启服务
   * @param strategy 修复策略
   * @param error 错误对象
   * @param context 上下文信息
   */
  private async restartService(
    strategy: FixStrategyConfig,
    error: SecurityError,
    context?: any
  ): Promise<FixResult> {
    // 这里只是模拟实现，实际应用中需要集成服务管理
    if (!strategy.parameters || !strategy.parameters.serviceName) {
      return { success: false, error: 'Missing service name parameter' };
    }
    
    const { serviceName } = strategy.parameters;
    
    console.log(`[AUTO-FIX] Would restart service: ${serviceName}`);
    
    // 模拟成功
    return {
      success: true,
      action: 'restartService',
      details: {
        serviceName,
        timestamp: Date.now()
      }
    };
  }

  /**
   * 阻止 IP
   * @param strategy 修复策略
   * @param error 错误对象
   * @param context 上下文信息
   */
  private async blockIP(
    strategy: FixStrategyConfig,
    error: SecurityError,
    context?: any
  ): Promise<FixResult> {
    // 这里只是模拟实现，实际应用中需要集成 IP 封禁服务
    const ip = error.clientIp || context?.clientIp;
    
    if (!ip) {
      return { success: false, error: 'No IP address provided' };
    }
    
    console.log(`[AUTO-FIX] Would block IP: ${ip}`);
    
    // 模拟成功
    return {
      success: true,
      action: 'blockIP',
      details: {
        ip,
        duration: strategy.parameters?.duration || '24h',
        timestamp: Date.now()
      }
    };
  }

  /**
   * 记录修复历史
   * @param history 修复历史记录
   */
  private recordFixHistory(history: FixHistory): void {
    this.fixHistory.push(history);
    
    // 限制历史记录数量
    if (this.fixHistory.length > 100) {
      this.fixHistory = this.fixHistory.slice(-100);
    }
  }

  /**
   * 获取修复历史
   */
  public getFixHistory(): FixHistory[] {
    return [...this.fixHistory];
  }

  /**
   * 初始化预定义修复策略
   */
  private initializeFixStrategies(): void {
    const strategies: FixStrategyConfig[] = [
      {
        id: 'fix_rate_limit',
        name: 'Clear Rate Limit',
        description: 'Clears rate limit for a specific IP address',
        enabled: true,
        targetErrorCodes: ['RATE_LIMIT_EXCEEDED', 'RATE_LIMIT_ERROR'],
        action: 'clearRateLimit'
      },
      {
        id: 'fix_fingerprint',
        name: 'Reset Fingerprint',
        description: 'Resets fingerprint tracking data',
        enabled: true,
        targetErrorCodes: ['FINGERPRINT_LIMIT_EXCEEDED', 'FINGERPRINT_TRACKING_FAILED'],
        action: 'resetFingerprint'
      },
      {
        id: 'fix_config',
        name: 'Fix Configuration',
        description: 'Updates invalid configuration settings',
        enabled: true,
        targetErrorCodes: ['CONFIGURATION_ERROR'],
        action: 'updateConfiguration',
        parameters: {
          configPath: '',
          configValue: ''
        }
      },
      {
        id: 'fix_honeypot',
        name: 'Block Bot IP',
        description: 'Blocks IP address that triggered honeypot',
        enabled: true,
        targetErrorCodes: ['HONEYPOT_TRIGGERED'],
        action: 'blockIP',
        parameters: {
          duration: '24h'
        }
      },
      {
        id: 'fix_suspicious',
        name: 'Block Suspicious IP',
        description: 'Blocks IP address with suspicious activity',
        enabled: true,
        targetErrorCodes: ['SUSPICIOUS_ACTIVITY'],
        action: 'blockIP',
        parameters: {
          duration: '12h'
        }
      }
    ];
    
    this.config.fixStrategies = strategies;
  }
}
