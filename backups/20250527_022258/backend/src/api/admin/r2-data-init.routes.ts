/**
 * R2 数据初始化路由
 */

import { Hono } from 'hono';
import { 
  initializeR2Data, 
  exportStoriesToR2, 
  exportQuestionnairesToR2, 
  exportStatisticsToR2 
} from './r2-data-init.controller';

// Define environment interface
interface Env {
  R2_BUCKET: R2Bucket;
}

// Create Hono app
const app = new Hono<{ Bindings: Env }>();

// R2 data initialization routes
app.get('/init', initializeR2Data);
app.get('/export/stories', exportStoriesToR2);
app.get('/export/questionnaires', exportQuestionnairesToR2);
app.get('/export/statistics', exportStatisticsToR2);

export default app;
