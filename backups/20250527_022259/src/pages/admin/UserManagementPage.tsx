import React, { useState, useEffect } from 'react';
import AdminLayout from '@/components/layouts/AdminLayout';
import { Card, CardContent, CardHeader, CardTitle, CardDescription, CardFooter } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { Label } from '@/components/ui/label';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Checkbox } from '@/components/ui/checkbox';
import { Badge } from '@/components/ui/badge';
import { useToast } from '@/components/ui/use-toast';
import {
  Search, UserSearch, Filter, RefreshCw, UserCheck, UserX, Trash2,
  Eye, FileText, CheckSquare, XSquare, AlertTriangle, Download
} from 'lucide-react';
import {
  Table, TableBody, TableCaption, TableCell, TableHead,
  TableHeader, TableRow
} from '@/components/ui/table';
import {
  Pagination, PaginationContent, PaginationItem,
  PaginationLink, PaginationNext, PaginationPrevious
} from '@/components/ui/pagination-new';
import {
  Dialog, DialogContent, DialogDescription, DialogFooter,
  DialogHeader, DialogTitle, DialogTrigger
} from '@/components/ui/dialog';
import {
  DropdownMenu, DropdownMenuContent, DropdownMenuItem,
  DropdownMenuLabel, DropdownMenuSeparator, DropdownMenuTrigger
} from '@/components/ui/dropdown-menu';
import {
  User, UserContent, UserSearchParams,
  searchUsers, getUserContents, updateUserStatus, batchUpdateUserStatus
} from '@/services/userManagementService';

const UserManagementPage: React.FC = () => {
  const { toast } = useToast();
  const [activeTab, setActiveTab] = useState('search');
  const [isLoading, setIsLoading] = useState(false);
  const [searchParams, setSearchParams] = useState<UserSearchParams>({
    page: 1,
    limit: 10
  });
  const [users, setUsers] = useState<User[]>([]);
  const [totalUsers, setTotalUsers] = useState(0);
  const [totalPages, setTotalPages] = useState(1);
  const [selectedUsers, setSelectedUsers] = useState<string[]>([]);
  const [selectedUser, setSelectedUser] = useState<User | null>(null);
  const [userContents, setUserContents] = useState<UserContent[]>([]);
  const [isUserContentLoading, setIsUserContentLoading] = useState(false);
  const [isUserDialogOpen, setIsUserDialogOpen] = useState(false);
  const [isBatchActionDialogOpen, setIsBatchActionDialogOpen] = useState(false);
  const [batchAction, setBatchAction] = useState<'activate' | 'deactivate' | 'ban' | null>(null);

  // 加载用户数据
  useEffect(() => {
    fetchUsers();
  }, [searchParams]);

  // 获取用户列表
  const fetchUsers = async () => {
    try {
      setIsLoading(true);
      const result = await searchUsers(searchParams);

      if (result.success) {
        setUsers(result.data.users);
        setTotalUsers(result.data.total);
        setTotalPages(result.data.totalPages);
      } else {
        toast({
          title: '获取用户失败',
          description: '无法加载用户列表，请稍后重试',
          variant: 'destructive',
        });
      }
    } catch (error) {
      console.error('获取用户失败:', error);
      toast({
        title: '获取用户失败',
        description: '发生错误，请稍后重试',
        variant: 'destructive',
      });
    } finally {
      setIsLoading(false);
    }
  };

  // 获取用户内容
  const fetchUserContents = async (uuid: string) => {
    try {
      setIsUserContentLoading(true);
      const result = await getUserContents(uuid);

      if (result.success) {
        setUserContents(result.data.contents);
      } else {
        toast({
          title: '获取用户内容失败',
          description: '无法加载用户内容，请稍后重试',
          variant: 'destructive',
        });
      }
    } catch (error) {
      console.error('获取用户内容失败:', error);
      toast({
        title: '获取用户内容失败',
        description: '发生错误，请稍后重试',
        variant: 'destructive',
      });
    } finally {
      setIsUserContentLoading(false);
    }
  };

  // 处理搜索参数变更
  const handleSearchParamChange = (key: keyof UserSearchParams, value: any) => {
    setSearchParams(prev => ({ ...prev, [key]: value, page: 1 }));
  };

  // 处理页码变更
  const handlePageChange = (page: number) => {
    setSearchParams(prev => ({ ...prev, page }));
  };

  // 处理用户选择
  const handleUserSelect = (uuid: string, checked: boolean) => {
    if (checked) {
      setSelectedUsers(prev => [...prev, uuid]);
    } else {
      setSelectedUsers(prev => prev.filter(id => id !== uuid));
    }
  };

  // 处理全选/取消全选
  const handleSelectAll = (checked: boolean) => {
    if (checked) {
      setSelectedUsers(users.map(user => user.uuid!));
    } else {
      setSelectedUsers([]);
    }
  };

  // 查看用户详情
  const handleViewUser = (user: User) => {
    setSelectedUser(user);
    fetchUserContents(user.uuid!);
    setIsUserDialogOpen(true);
  };

  // 更新用户状态
  const handleUpdateUserStatus = async (uuid: string, status: 'active' | 'inactive' | 'banned') => {
    try {
      const result = await updateUserStatus(uuid, status);

      if (result.success) {
        toast({
          title: '更新成功',
          description: result.message || `用户状态已更新为 ${status}`,
        });

        // 刷新用户列表
        fetchUsers();
      } else {
        toast({
          title: '更新失败',
          description: '无法更新用户状态，请稍后重试',
          variant: 'destructive',
        });
      }
    } catch (error) {
      console.error('更新用户状态失败:', error);
      toast({
        title: '更新失败',
        description: '发生错误，请稍后重试',
        variant: 'destructive',
      });
    }
  };

  // 批量更新用户状态
  const handleBatchUpdateUserStatus = async () => {
    if (!batchAction || selectedUsers.length === 0) return;

    try {
      let status: 'active' | 'inactive' | 'banned';

      switch (batchAction) {
        case 'activate':
          status = 'active';
          break;
        case 'deactivate':
          status = 'inactive';
          break;
        case 'ban':
          status = 'banned';
          break;
        default:
          return;
      }

      const result = await batchUpdateUserStatus(selectedUsers, status);

      if (result.success) {
        toast({
          title: '批量更新成功',
          description: result.message || `${selectedUsers.length} 个用户状态已更新`,
        });

        // 清空选择并刷新用户列表
        setSelectedUsers([]);
        fetchUsers();
      } else {
        toast({
          title: '批量更新失败',
          description: '无法更新用户状态，请稍后重试',
          variant: 'destructive',
        });
      }
    } catch (error) {
      console.error('批量更新用户状态失败:', error);
      toast({
        title: '批量更新失败',
        description: '发生错误，请稍后重试',
        variant: 'destructive',
      });
    } finally {
      setIsBatchActionDialogOpen(false);
    }
  };

  // 重置搜索
  const resetSearch = () => {
    setSearchParams({
      page: 1,
      limit: 10
    });
  };

  return (
    <AdminLayout>
      <div className="container mx-auto py-6">
        <h1 className="text-2xl font-bold mb-6">用户管理</h1>

        <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-4">
          <TabsList>
            <TabsTrigger value="search">
              <Search className="mr-2 h-4 w-4" />
              用户搜索
            </TabsTrigger>
            <TabsTrigger value="batch">
              <CheckSquare className="mr-2 h-4 w-4" />
              批量操作
            </TabsTrigger>
          </TabsList>

          <TabsContent value="search" className="space-y-4">
            {/* 搜索表单 */}
            <Card>
              <CardHeader>
                <CardTitle>搜索用户</CardTitle>
                <CardDescription>
                  通过UUID、邮箱或用户名搜索用户
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="uuid">用户UUID</Label>
                    <Input
                      id="uuid"
                      placeholder="输入用户UUID"
                      value={searchParams.uuid || ''}
                      onChange={(e) => handleSearchParamChange('uuid', e.target.value)}
                    />
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="email">邮箱</Label>
                    <Input
                      id="email"
                      placeholder="输入用户邮箱"
                      value={searchParams.email || ''}
                      onChange={(e) => handleSearchParamChange('email', e.target.value)}
                    />
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="username">用户名</Label>
                    <Input
                      id="username"
                      placeholder="输入用户名"
                      value={searchParams.username || ''}
                      onChange={(e) => handleSearchParamChange('username', e.target.value)}
                    />
                  </div>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="role">角色</Label>
                    <Select
                      value={searchParams.role || ''}
                      onValueChange={(value) => handleSearchParamChange('role', value)}
                    >
                      <SelectTrigger id="role">
                        <SelectValue placeholder="选择角色" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="">全部角色</SelectItem>
                        <SelectItem value="user">普通用户</SelectItem>
                        <SelectItem value="reviewer">审核员</SelectItem>
                        <SelectItem value="admin">管理员</SelectItem>
                        <SelectItem value="superadmin">超级管理员</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="status">状态</Label>
                    <Select
                      value={searchParams.status || ''}
                      onValueChange={(value) => handleSearchParamChange('status', value)}
                    >
                      <SelectTrigger id="status">
                        <SelectValue placeholder="选择状态" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="">全部状态</SelectItem>
                        <SelectItem value="active">活跃</SelectItem>
                        <SelectItem value="inactive">未激活</SelectItem>
                        <SelectItem value="banned">已禁用</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                </div>
              </CardContent>
              <CardFooter className="flex justify-between">
                <Button variant="outline" onClick={resetSearch}>
                  <RefreshCw className="mr-2 h-4 w-4" />
                  重置
                </Button>
                <Button onClick={() => fetchUsers()}>
                  <Search className="mr-2 h-4 w-4" />
                  搜索
                </Button>
              </CardFooter>
            </Card>

            {/* 用户列表 */}
            <Card>
              <CardHeader>
                <CardTitle>用户列表</CardTitle>
                <CardDescription>
                  共找到 {totalUsers} 个用户
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="rounded-md border">
                  <Table>
                    <TableHeader>
                      <TableRow>
                        <TableHead className="w-12">
                          <Checkbox
                            checked={selectedUsers.length === users.length && users.length > 0}
                            onCheckedChange={handleSelectAll}
                            aria-label="全选"
                          />
                        </TableHead>
                        <TableHead>UUID</TableHead>
                        <TableHead>用户名</TableHead>
                        <TableHead>邮箱</TableHead>
                        <TableHead>角色</TableHead>
                        <TableHead>状态</TableHead>
                        <TableHead>注册时间</TableHead>
                        <TableHead>提交数</TableHead>
                        <TableHead className="text-right">操作</TableHead>
                      </TableRow>
                    </TableHeader>
                    <TableBody>
                      {isLoading ? (
                        <TableRow>
                          <TableCell colSpan={9} className="text-center py-8">
                            <div className="flex justify-center items-center">
                              <RefreshCw className="h-6 w-6 animate-spin mr-2" />
                              <span>加载中...</span>
                            </div>
                          </TableCell>
                        </TableRow>
                      ) : users.length === 0 ? (
                        <TableRow>
                          <TableCell colSpan={9} className="text-center py-8">
                            未找到匹配的用户
                          </TableCell>
                        </TableRow>
                      ) : (
                        users.map((user) => (
                          <TableRow key={user.id}>
                            <TableCell>
                              <Checkbox
                                checked={selectedUsers.includes(user.uuid!)}
                                onCheckedChange={(checked) => handleUserSelect(user.uuid!, !!checked)}
                                aria-label={`选择用户 ${user.username}`}
                              />
                            </TableCell>
                            <TableCell className="font-mono text-xs">
                              {user.uuid}
                            </TableCell>
                            <TableCell>{user.username}</TableCell>
                            <TableCell>{user.email}</TableCell>
                            <TableCell>
                              <Badge variant={
                                user.role === 'superadmin' ? 'destructive' :
                                user.role === 'admin' ? 'default' :
                                user.role === 'reviewer' ? 'secondary' : 'outline'
                              }>
                                {user.role === 'superadmin' ? '超级管理员' :
                                 user.role === 'admin' ? '管理员' :
                                 user.role === 'reviewer' ? '审核员' : '普通用户'}
                              </Badge>
                            </TableCell>
                            <TableCell>
                              <Badge variant={
                                user.status === 'active' ? 'success' :
                                user.status === 'inactive' ? 'warning' : 'destructive'
                              }>
                                {user.status === 'active' ? '活跃' :
                                 user.status === 'inactive' ? '未激活' : '已禁用'}
                              </Badge>
                            </TableCell>
                            <TableCell>
                              {new Date(user.createdAt).toLocaleDateString()}
                            </TableCell>
                            <TableCell>
                              {user.submissionCount || 0}
                            </TableCell>
                            <TableCell className="text-right">
                              <DropdownMenu>
                                <DropdownMenuTrigger asChild>
                                  <Button variant="ghost" size="icon">
                                    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="h-4 w-4">
                                      <circle cx="12" cy="12" r="1" />
                                      <circle cx="12" cy="5" r="1" />
                                      <circle cx="12" cy="19" r="1" />
                                    </svg>
                                  </Button>
                                </DropdownMenuTrigger>
                                <DropdownMenuContent align="end">
                                  <DropdownMenuLabel>用户操作</DropdownMenuLabel>
                                  <DropdownMenuItem onClick={() => handleViewUser(user)}>
                                    <Eye className="mr-2 h-4 w-4" />
                                    查看详情
                                  </DropdownMenuItem>
                                  <DropdownMenuSeparator />
                                  {user.status !== 'active' && (
                                    <DropdownMenuItem onClick={() => handleUpdateUserStatus(user.uuid!, 'active')}>
                                      <UserCheck className="mr-2 h-4 w-4" />
                                      激活用户
                                    </DropdownMenuItem>
                                  )}
                                  {user.status !== 'inactive' && (
                                    <DropdownMenuItem onClick={() => handleUpdateUserStatus(user.uuid!, 'inactive')}>
                                      <UserX className="mr-2 h-4 w-4" />
                                      停用用户
                                    </DropdownMenuItem>
                                  )}
                                  {user.status !== 'banned' && (
                                    <DropdownMenuItem onClick={() => handleUpdateUserStatus(user.uuid!, 'banned')}>
                                      <AlertTriangle className="mr-2 h-4 w-4" />
                                      禁用用户
                                    </DropdownMenuItem>
                                  )}
                                </DropdownMenuContent>
                              </DropdownMenu>
                            </TableCell>
                          </TableRow>
                        ))
                      )}
                    </TableBody>
                  </Table>
                </div>

                {/* 分页 */}
                {totalPages > 1 && (
                  <div className="mt-4 flex justify-center">
                    <Pagination>
                      <PaginationContent>
                        <PaginationItem>
                          <PaginationPrevious
                            onClick={() => handlePageChange(Math.max(1, searchParams.page! - 1))}
                            disabled={searchParams.page === 1}
                          />
                        </PaginationItem>

                        {Array.from({ length: totalPages }, (_, i) => i + 1).map((page) => (
                          <PaginationItem key={page}>
                            <PaginationLink
                              onClick={() => handlePageChange(page)}
                              isActive={page === searchParams.page}
                            >
                              {page}
                            </PaginationLink>
                          </PaginationItem>
                        ))}

                        <PaginationItem>
                          <PaginationNext
                            onClick={() => handlePageChange(Math.min(totalPages, searchParams.page! + 1))}
                            disabled={searchParams.page === totalPages}
                          />
                        </PaginationItem>
                      </PaginationContent>
                    </Pagination>
                  </div>
                )}
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="batch" className="space-y-4">
            <Card>
              <CardHeader>
                <CardTitle>批量操作</CardTitle>
                <CardDescription>
                  对多个用户执行相同的操作
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                {selectedUsers.length === 0 ? (
                  <div className="text-center py-8 border rounded-md">
                    <p className="text-muted-foreground">请在用户搜索页面选择要操作的用户</p>
                  </div>
                ) : (
                  <>
                    <div className="flex items-center justify-between">
                      <p>已选择 <span className="font-bold">{selectedUsers.length}</span> 个用户</p>
                      <Button variant="outline" size="sm" onClick={() => setSelectedUsers([])}>
                        清除选择
                      </Button>
                    </div>

                    <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                      <Card className="hover:border-green-500 cursor-pointer transition-colors" onClick={() => {
                        setBatchAction('activate');
                        setIsBatchActionDialogOpen(true);
                      }}>
                        <CardContent className="p-6 flex flex-col items-center justify-center">
                          <UserCheck className="h-10 w-10 text-green-500 mb-2" />
                          <h3 className="font-medium">批量激活</h3>
                          <p className="text-sm text-muted-foreground text-center mt-1">
                            将所选用户状态设置为"活跃"
                          </p>
                        </CardContent>
                      </Card>

                      <Card className="hover:border-yellow-500 cursor-pointer transition-colors" onClick={() => {
                        setBatchAction('deactivate');
                        setIsBatchActionDialogOpen(true);
                      }}>
                        <CardContent className="p-6 flex flex-col items-center justify-center">
                          <UserX className="h-10 w-10 text-yellow-500 mb-2" />
                          <h3 className="font-medium">批量停用</h3>
                          <p className="text-sm text-muted-foreground text-center mt-1">
                            将所选用户状态设置为"未激活"
                          </p>
                        </CardContent>
                      </Card>

                      <Card className="hover:border-red-500 cursor-pointer transition-colors" onClick={() => {
                        setBatchAction('ban');
                        setIsBatchActionDialogOpen(true);
                      }}>
                        <CardContent className="p-6 flex flex-col items-center justify-center">
                          <AlertTriangle className="h-10 w-10 text-red-500 mb-2" />
                          <h3 className="font-medium">批量禁用</h3>
                          <p className="text-sm text-muted-foreground text-center mt-1">
                            将所选用户状态设置为"已禁用"
                          </p>
                        </CardContent>
                      </Card>
                    </div>
                  </>
                )}
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>

        {/* 用户详情对话框 */}
        <Dialog open={isUserDialogOpen} onOpenChange={setIsUserDialogOpen}>
          <DialogContent className="max-w-4xl max-h-[80vh] overflow-y-auto">
            {selectedUser && (
              <>
                <DialogHeader>
                  <DialogTitle>用户详情</DialogTitle>
                  <DialogDescription>
                    查看用户信息和提交的内容
                  </DialogDescription>
                </DialogHeader>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4 py-4">
                  <div>
                    <h3 className="text-lg font-medium mb-2">基本信息</h3>
                    <div className="space-y-2">
                      <div className="grid grid-cols-3 gap-2">
                        <span className="font-medium">UUID:</span>
                        <span className="col-span-2 font-mono text-xs">{selectedUser.uuid}</span>
                      </div>
                      <div className="grid grid-cols-3 gap-2">
                        <span className="font-medium">用户名:</span>
                        <span className="col-span-2">{selectedUser.username}</span>
                      </div>
                      <div className="grid grid-cols-3 gap-2">
                        <span className="font-medium">邮箱:</span>
                        <span className="col-span-2">{selectedUser.email}</span>
                      </div>
                      <div className="grid grid-cols-3 gap-2">
                        <span className="font-medium">角色:</span>
                        <span className="col-span-2">
                          <Badge variant={
                            selectedUser.role === 'superadmin' ? 'destructive' :
                            selectedUser.role === 'admin' ? 'default' :
                            selectedUser.role === 'reviewer' ? 'secondary' : 'outline'
                          }>
                            {selectedUser.role === 'superadmin' ? '超级管理员' :
                             selectedUser.role === 'admin' ? '管理员' :
                             selectedUser.role === 'reviewer' ? '审核员' : '普通用户'}
                          </Badge>
                        </span>
                      </div>
                      <div className="grid grid-cols-3 gap-2">
                        <span className="font-medium">状态:</span>
                        <span className="col-span-2">
                          <Badge variant={
                            selectedUser.status === 'active' ? 'success' :
                            selectedUser.status === 'inactive' ? 'warning' : 'destructive'
                          }>
                            {selectedUser.status === 'active' ? '活跃' :
                             selectedUser.status === 'inactive' ? '未激活' : '已禁用'}
                          </Badge>
                        </span>
                      </div>
                      <div className="grid grid-cols-3 gap-2">
                        <span className="font-medium">注册时间:</span>
                        <span className="col-span-2">{new Date(selectedUser.createdAt).toLocaleString()}</span>
                      </div>
                      <div className="grid grid-cols-3 gap-2">
                        <span className="font-medium">最后登录:</span>
                        <span className="col-span-2">{selectedUser.lastLoginAt ? new Date(selectedUser.lastLoginAt).toLocaleString() : '未登录'}</span>
                      </div>
                    </div>
                  </div>

                  <div>
                    <h3 className="text-lg font-medium mb-2">提交统计</h3>
                    <div className="space-y-2">
                      <div className="grid grid-cols-3 gap-2">
                        <span className="font-medium">总提交数:</span>
                        <span className="col-span-2">{selectedUser.submissionCount || 0}</span>
                      </div>
                      <div className="grid grid-cols-3 gap-2">
                        <span className="font-medium">故事数:</span>
                        <span className="col-span-2">{selectedUser.storyCount || 0}</span>
                      </div>
                      <div className="grid grid-cols-3 gap-2">
                        <span className="font-medium">问卷回复数:</span>
                        <span className="col-span-2">{selectedUser.questionnaireCount || 0}</span>
                      </div>
                    </div>

                    <div className="mt-4 space-y-2">
                      <h3 className="text-lg font-medium">用户操作</h3>
                      <div className="flex space-x-2">
                        {selectedUser.status !== 'active' && (
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => {
                              handleUpdateUserStatus(selectedUser.uuid!, 'active');
                              setIsUserDialogOpen(false);
                            }}
                          >
                            <UserCheck className="mr-2 h-4 w-4" />
                            激活用户
                          </Button>
                        )}
                        {selectedUser.status !== 'inactive' && (
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => {
                              handleUpdateUserStatus(selectedUser.uuid!, 'inactive');
                              setIsUserDialogOpen(false);
                            }}
                          >
                            <UserX className="mr-2 h-4 w-4" />
                            停用用户
                          </Button>
                        )}
                        {selectedUser.status !== 'banned' && (
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => {
                              handleUpdateUserStatus(selectedUser.uuid!, 'banned');
                              setIsUserDialogOpen(false);
                            }}
                          >
                            <AlertTriangle className="mr-2 h-4 w-4" />
                            禁用用户
                          </Button>
                        )}
                      </div>
                    </div>
                  </div>
                </div>

                <div className="space-y-4 mt-4">
                  <h3 className="text-lg font-medium">用户内容</h3>

                  {isUserContentLoading ? (
                    <div className="flex justify-center items-center py-8">
                      <RefreshCw className="h-6 w-6 animate-spin mr-2" />
                      <span>加载中...</span>
                    </div>
                  ) : userContents.length === 0 ? (
                    <div className="text-center py-8 border rounded-md">
                      <p className="text-muted-foreground">该用户暂无内容</p>
                    </div>
                  ) : (
                    <div className="space-y-4">
                      {userContents.map((content) => (
                        <Card key={content.id}>
                          <CardHeader className="py-3">
                            <div className="flex justify-between items-center">
                              <div className="flex items-center space-x-2">
                                <Badge variant={
                                  content.type === 'story' ? 'default' :
                                  content.type === 'questionnaire' ? 'secondary' : 'outline'
                                }>
                                  {content.type === 'story' ? '故事' :
                                   content.type === 'questionnaire' ? '问卷' : '评论'}
                                </Badge>
                                {content.title && <span className="font-medium">{content.title}</span>}
                                <Badge variant={
                                  content.status === 'approved' ? 'success' :
                                  content.status === 'rejected' ? 'destructive' : 'warning'
                                }>
                                  {content.status === 'approved' ? '已通过' :
                                   content.status === 'rejected' ? '已拒绝' : '待审核'}
                                </Badge>
                              </div>
                              <span className="text-xs text-muted-foreground">
                                {new Date(content.createdAt).toLocaleString()}
                              </span>
                            </div>
                          </CardHeader>
                          <CardContent className="py-2">
                            <div className="max-h-40 overflow-y-auto border rounded-md p-3 bg-muted">
                              {content.type === 'questionnaire' ? (
                                <pre className="text-xs whitespace-pre-wrap">{content.content}</pre>
                              ) : (
                                <p className="whitespace-pre-wrap">{content.content}</p>
                              )}
                            </div>

                            {content.tags && content.tags.length > 0 && (
                              <div className="flex flex-wrap gap-1 mt-2">
                                {content.tags.map((tag, index) => (
                                  <Badge key={index} variant="outline">{tag}</Badge>
                                ))}
                              </div>
                            )}
                          </CardContent>
                        </Card>
                      ))}
                    </div>
                  )}
                </div>

                <DialogFooter>
                  <Button variant="outline" onClick={() => setIsUserDialogOpen(false)}>
                    关闭
                  </Button>
                </DialogFooter>
              </>
            )}
          </DialogContent>
        </Dialog>

        {/* 批量操作确认对话框 */}
        <Dialog open={isBatchActionDialogOpen} onOpenChange={setIsBatchActionDialogOpen}>
          <DialogContent>
            <DialogHeader>
              <DialogTitle>确认批量操作</DialogTitle>
              <DialogDescription>
                {batchAction === 'activate' && '您确定要激活所选用户吗？'}
                {batchAction === 'deactivate' && '您确定要停用所选用户吗？'}
                {batchAction === 'ban' && '您确定要禁用所选用户吗？'}
              </DialogDescription>
            </DialogHeader>

            <div className="py-4">
              <p>此操作将影响 <span className="font-bold">{selectedUsers.length}</span> 个用户。</p>

              {batchAction === 'activate' && (
                <p className="mt-2 text-sm text-muted-foreground">
                  激活后，用户将能够正常登录和使用系统。
                </p>
              )}

              {batchAction === 'deactivate' && (
                <p className="mt-2 text-sm text-muted-foreground">
                  停用后，用户将无法登录系统，但其数据将被保留。
                </p>
              )}

              {batchAction === 'ban' && (
                <p className="mt-2 text-sm text-muted-foreground text-red-500">
                  禁用是一项严重操作，禁用后用户将无法登录系统，且可能会被标记为违规用户。
                </p>
              )}
            </div>

            <DialogFooter>
              <Button variant="outline" onClick={() => setIsBatchActionDialogOpen(false)}>
                取消
              </Button>
              <Button
                variant={batchAction === 'ban' ? 'destructive' : 'default'}
                onClick={handleBatchUpdateUserStatus}
              >
                确认
              </Button>
            </DialogFooter>
          </DialogContent>
        </Dialog>
      </div>
    </AdminLayout>
  );
};

export default UserManagementPage;
