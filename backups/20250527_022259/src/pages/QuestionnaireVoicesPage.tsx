import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import {
  Search, Filter, Heart, MessageCircle, Clock,
  BookOpen, Users, TrendingUp, Quote, ChevronDown,
  Loader2, ArrowUpDown, Calendar, Star
} from 'lucide-react';
import { ApiService } from '@/services/apiService';
import { useApiDebug, DataStructures } from '@/hooks/useApiDebug';
import ApiDebugPanel from '@/components/debug/ApiDebugPanel';

/**
 * 问卷心声页面
 *
 * 展示从问卷中提取的"给高三学子的建议"和"对当前就业环境的观察"内容
 */
const QuestionnaireVoicesPage: React.FC = () => {
  const [voices, setVoices] = useState<any[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [activeTab, setActiveTab] = useState('all');
  const [sortBy, setSortBy] = useState('latest'); // latest, oldest, popular
  const [error, setError] = useState<string | null>(null);
  const [pagination, setPagination] = useState({
    page: 1,
    pageSize: 12, // 减少每页显示数量
    total: 0,
    hasMore: false
  });
  const [isLoadingMore, setIsLoadingMore] = useState(false);

  // API调试配置
  const apiDebug = useApiDebug({
    pageName: '问卷心声页面',
    requests: [
      {
        id: 'questionnaire-voices',
        name: '问卷心声列表',
        endpoint: '/api/questionnaire-voices',
        method: 'GET',
        expectedStructure: DataStructures.questionnaireVoices,
        testFunction: async () => {
          const response = await fetch('/api/questionnaire-voices?pageSize=20');
          const data = await response.json();
          return data;
        }
      }
    ]
  });

  useEffect(() => {
    loadVoices(true); // 重置加载
  }, [activeTab, sortBy, searchTerm]);

  useEffect(() => {
    // 防抖搜索
    const timeoutId = setTimeout(() => {
      if (searchTerm !== '') {
        loadVoices(true);
      }
    }, 500);

    return () => clearTimeout(timeoutId);
  }, [searchTerm]);

  const loadVoices = async (reset = false) => {
    try {
      if (reset) {
        setIsLoading(true);
        setPagination(prev => ({ ...prev, page: 1 }));
      } else {
        setIsLoadingMore(true);
      }
      setError(null);

      // 构建查询参数
      const params = new URLSearchParams({
        page: reset ? '1' : pagination.page.toString(),
        pageSize: pagination.pageSize.toString(),
        sortBy: sortBy,
      });

      if (activeTab !== 'all') {
        params.append('type', activeTab);
      }

      if (searchTerm.trim()) {
        params.append('search', searchTerm.trim());
      }

      // 调用问卷心声API
      const response = await fetch(`/api/questionnaire-voices?${params.toString()}`);
      const data = await response.json();

      console.log('问卷心声API响应:', data);

      if (data.success && data.voices) {
        // 转换数据格式，将嵌套的voices展开
        const flattenedVoices = [];
        data.voices.forEach(voiceGroup => {
          voiceGroup.voices.forEach(voice => {
            flattenedVoices.push({
              id: `${voiceGroup.id}-${voice.type}`,
              type: voice.type,
              content: voice.content,
              title: voice.title,
              category: voiceGroup.educationLevel || '未分类',
              author: voiceGroup.author,
              educationLevel: voiceGroup.educationLevel,
              major: voiceGroup.major,
              graduationYear: voiceGroup.graduationYear,
              region: voiceGroup.region,
              employmentStatus: voiceGroup.employmentStatus,
              currentIndustry: voiceGroup.currentIndustry,
              createdAt: voiceGroup.createdAt,
              isAnonymous: voiceGroup.isAnonymous,
              likes: 0 // 默认点赞数
            });
          });
        });

        console.log('转换后的心声数据:', flattenedVoices);

        // 根据是否重置来设置数据
        if (reset) {
          setVoices(flattenedVoices);
        } else {
          setVoices(prev => [...prev, ...flattenedVoices]);
        }

        // 设置分页信息
        setPagination(prev => ({
          page: data.currentPage || 1,
          pageSize: data.pageSize || 12,
          total: data.totalCount || 0,
          hasMore: data.hasMore || false
        }));
      } else {
        throw new Error(data.error || '获取数据失败');
      }
    } catch (err) {
      console.error('加载问卷心声失败:', err);
      setError('加载数据失败，请稍后重试');
      if (reset) {
        setVoices([]);
      }
    } finally {
      setIsLoading(false);
      setIsLoadingMore(false);
    }
  };

  // 加载更多数据
  const loadMoreVoices = () => {
    if (!isLoadingMore && pagination.hasMore) {
      setPagination(prev => ({ ...prev, page: prev.page + 1 }));
      loadVoices(false);
    }
  };

  // 过滤数据
  const filteredVoices = voices.filter(voice => {
    const matchesSearch = voice.content?.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         voice.category?.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         voice.title?.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesTab = activeTab === 'all' || voice.type === activeTab;
    return matchesSearch && matchesTab;
  });

  // 统计数据
  const stats = {
    total: voices.length,
    advice: voices.filter(v => v.type === 'advice').length,
    observation: voices.filter(v => v.type === 'observation').length,
    totalLikes: voices.reduce((sum, v) => sum + v.likes, 0)
  };

  return (
    <div className="container mx-auto px-4 py-8">


      {/* 页面标题 */}
      <div className="text-center mb-8">
        <h1 className="text-3xl font-bold mb-4">问卷心声</h1>
        <p className="text-muted-foreground max-w-2xl mx-auto">
          汇聚来自问卷调查中的真实声音，包括给高三学子的建议和对当前就业环境的观察
        </p>
      </div>

      {/* 统计卡片 */}
      <div className="grid gap-4 md:grid-cols-4 mb-6">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between pb-2">
            <CardTitle className="text-sm font-medium">总心声数</CardTitle>
            <MessageCircle className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats.total}</div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between pb-2">
            <CardTitle className="text-sm font-medium">学习建议</CardTitle>
            <BookOpen className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats.advice}</div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between pb-2">
            <CardTitle className="text-sm font-medium">就业观察</CardTitle>
            <TrendingUp className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats.observation}</div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between pb-2">
            <CardTitle className="text-sm font-medium">总点赞数</CardTitle>
            <Heart className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats.totalLikes}</div>
          </CardContent>
        </Card>
      </div>

      {/* 搜索和筛选 */}
      <div className="flex flex-col sm:flex-row gap-4 justify-between items-start sm:items-center mb-6">
        <div className="relative flex-1 max-w-md">
          <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
          <Input
            type="search"
            placeholder="搜索心声内容、标题..."
            className="pl-8 w-full"
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
          />
        </div>

        <div className="flex gap-2">
          {/* 排序选择 */}
          <div className="flex items-center gap-2">
            <ArrowUpDown className="h-4 w-4 text-muted-foreground" />
            <select
              value={sortBy}
              onChange={(e) => setSortBy(e.target.value)}
              className="px-3 py-1.5 border border-input bg-background rounded-md text-sm"
            >
              <option value="latest">最新发布</option>
              <option value="oldest">最早发布</option>
              <option value="popular">最受欢迎</option>
            </select>
          </div>

          {/* 显示总数 */}
          <div className="flex items-center gap-2 px-3 py-1.5 bg-muted rounded-md text-sm">
            <MessageCircle className="h-4 w-4" />
            <span>共 {pagination.total} 条心声</span>
          </div>
        </div>
      </div>

      {/* 内容标签页 */}
      <Tabs value={activeTab} onValueChange={setActiveTab} className="mb-6">
        <TabsList>
          <TabsTrigger value="all">全部</TabsTrigger>
          <TabsTrigger value="advice">学习建议</TabsTrigger>
          <TabsTrigger value="observation">就业观察</TabsTrigger>
        </TabsList>

        <TabsContent value={activeTab} className="mt-6">
          {isLoading ? (
            <div className="text-center py-8">
              <p>加载中...</p>
            </div>
          ) : filteredVoices.length === 0 ? (
            <Card>
              <CardContent className="flex flex-col items-center justify-center h-[300px]">
                <Quote className="h-16 w-16 text-muted-foreground mb-4" />
                <h2 className="text-xl font-semibold mb-2">暂无相关内容</h2>
                <p className="text-muted-foreground">当前筛选条件下没有找到相关的心声内容</p>
              </CardContent>
            </Card>
          ) : (
            <>
              <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
                {filteredVoices.map(voice => (
                  <Card key={voice.id} className="hover:shadow-md transition-shadow">
                    <CardHeader>
                      <div className="flex justify-between items-start mb-2">
                        <Badge variant={voice.type === 'advice' ? 'default' : 'secondary'}>
                          {voice.type === 'advice' ? '学习建议' : '就业观察'}
                        </Badge>
                        <Badge variant="outline">{voice.category}</Badge>
                      </div>
                      <div className="text-xs text-muted-foreground">
                        {voice.author} • {voice.major} • {voice.graduationYear}年毕业
                      </div>
                    </CardHeader>
                    <CardContent>
                      <h4 className="font-medium text-sm mb-2">{voice.title}</h4>
                      <p className="text-sm mb-4 line-clamp-3">{voice.content}</p>
                      <div className="flex justify-between items-center text-xs text-muted-foreground">
                        <div className="flex items-center">
                          <Clock className="h-3 w-3 mr-1" />
                          {new Date(voice.createdAt).toLocaleDateString()}
                        </div>
                        <div className="flex items-center gap-3">
                          <div className="flex items-center">
                            <Heart className="h-3 w-3 mr-1" />
                            {voice.likes}
                          </div>
                          <Badge variant="outline" className="text-xs">
                            {voice.employmentStatus}
                          </Badge>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                ))}
              </div>

              {/* 加载更多按钮 */}
              {pagination.hasMore && (
                <div className="flex justify-center mt-8">
                  <Button
                    onClick={loadMoreVoices}
                    disabled={isLoadingMore}
                    variant="outline"
                    className="flex items-center gap-2"
                  >
                    {isLoadingMore ? (
                      <>
                        <Loader2 className="h-4 w-4 animate-spin" />
                        加载中...
                      </>
                    ) : (
                      <>
                        <ChevronDown className="h-4 w-4" />
                        加载更多心声
                      </>
                    )}
                  </Button>
                </div>
              )}

              {/* 分页信息 */}
              {!pagination.hasMore && voices.length > 0 && (
                <div className="text-center mt-8 text-sm text-muted-foreground">
                  已显示全部 {pagination.total} 条心声
                </div>
              )}
            </>
          )}
        </TabsContent>
      </Tabs>
    </div>
  );
};

export default QuestionnaireVoicesPage;
