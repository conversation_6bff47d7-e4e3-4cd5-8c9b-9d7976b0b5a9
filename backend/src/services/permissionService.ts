/**
 * 权限控制服务
 * 提供细粒度的权限控制功能
 */

import { Env } from '../types';
import { UserRole } from '../models/user';
import { AuditLogService, AuditAction, AuditSeverity } from './auditLogService';

// 权限类型
export enum Permission {
  // 仪表盘权限
  DASHBOARD_PERSONAL = 'dashboard:personal',   // 个人仪表盘
  DASHBOARD_SYSTEM = 'dashboard:system',       // 系统概览
  DASHBOARD_SECURITY = 'dashboard:security',   // 安全监控

  // 内容管理权限
  CONTENT_REVIEW = 'content:review',           // 内容审核
  STORY_REVIEW = 'story:review',               // 故事审核
  QUICK_REVIEW = 'quick:review',               // 快速审核
  TAG_MANAGEMENT = 'tag:management',           // 标签管理

  // 数据管理权限
  QUESTIONNAIRE_VIEW = 'questionnaire:view',   // 问卷回复查看
  QUESTIONNAIRE_EDIT = 'questionnaire:edit',   // 问卷回复编辑
  DATA_ANALYSIS = 'data:analysis',             // 数据分析
  DATA_EXPORT = 'data:export',                 // 数据导出

  // 用户管理权限
  USER_VIEW = 'user:view',                     // 用户查看
  USER_MANAGEMENT = 'user:management',         // 用户管理
  REVIEWER_MANAGEMENT = 'reviewer:management', // 审核员管理
  ADMIN_MANAGEMENT = 'admin:management',       // 管理员管理
  ROLE_MANAGEMENT = 'role:management',         // 角色权限管理

  // 系统设置权限
  SETTINGS_PERSONAL = 'settings:personal',     // 个人设置
  DEIDENTIFICATION = 'settings:deidentification', // 内容脱敏设置
  SECURITY_SETTINGS = 'settings:security',     // 安全设置
  SYSTEM_CONFIG = 'settings:system',           // 系统配置

  // 安全与审计权限
  SECURITY_MONITOR = 'security:monitor',       // 安全监控
  SECURITY_LOGS = 'security:logs',             // 安全日志
  ADMIN_AUDIT = 'security:admin-audit',        // 管理员审计
  LOGIN_RECORDS = 'security:login-records',    // 登录记录
}

// 角色权限映射
const rolePermissions: Record<UserRole, Permission[]> = {
  // 超级管理员拥有所有权限
  superadmin: Object.values(Permission),

  // 管理员拥有大部分权限，但不包括系统级别和敏感操作
  admin: [
    // 仪表盘权限
    Permission.DASHBOARD_PERSONAL,
    Permission.DASHBOARD_SYSTEM,

    // 内容管理权限
    Permission.CONTENT_REVIEW,
    Permission.STORY_REVIEW,
    Permission.QUICK_REVIEW,
    Permission.TAG_MANAGEMENT,

    // 数据管理权限
    Permission.QUESTIONNAIRE_VIEW,
    Permission.QUESTIONNAIRE_EDIT,
    Permission.DATA_ANALYSIS,
    Permission.DATA_EXPORT,

    // 用户管理权限
    Permission.USER_VIEW,
    Permission.USER_MANAGEMENT,
    Permission.REVIEWER_MANAGEMENT,

    // 系统设置权限
    Permission.SETTINGS_PERSONAL,
    Permission.DEIDENTIFICATION,
  ],

  // 审核员只有内容审核相关权限
  reviewer: [
    // 仪表盘权限
    Permission.DASHBOARD_PERSONAL,

    // 内容管理权限
    Permission.CONTENT_REVIEW,
    Permission.STORY_REVIEW,
    Permission.QUICK_REVIEW,

    // 数据管理权限
    Permission.QUESTIONNAIRE_VIEW,

    // 系统设置权限
    Permission.SETTINGS_PERSONAL,
  ],

  // 普通用户没有管理权限
  user: [],

  // 访客没有任何权限
  guest: [],
};

// 操作风险等级
export enum RiskLevel {
  LOW = 'low',         // 低风险
  MEDIUM = 'medium',   // 中风险
  HIGH = 'high',       // 高风险
  CRITICAL = 'critical' // 极高风险
}

// 权限风险映射
const permissionRisks: Record<Permission, RiskLevel> = {
  // 仪表盘权限
  [Permission.DASHBOARD_PERSONAL]: RiskLevel.LOW,
  [Permission.DASHBOARD_SYSTEM]: RiskLevel.LOW,
  [Permission.DASHBOARD_SECURITY]: RiskLevel.MEDIUM,

  // 内容管理权限
  [Permission.CONTENT_REVIEW]: RiskLevel.MEDIUM,
  [Permission.STORY_REVIEW]: RiskLevel.MEDIUM,
  [Permission.QUICK_REVIEW]: RiskLevel.MEDIUM,
  [Permission.TAG_MANAGEMENT]: RiskLevel.MEDIUM,

  // 数据管理权限
  [Permission.QUESTIONNAIRE_VIEW]: RiskLevel.LOW,
  [Permission.QUESTIONNAIRE_EDIT]: RiskLevel.MEDIUM,
  [Permission.DATA_ANALYSIS]: RiskLevel.LOW,
  [Permission.DATA_EXPORT]: RiskLevel.MEDIUM,

  // 用户管理权限
  [Permission.USER_VIEW]: RiskLevel.LOW,
  [Permission.USER_MANAGEMENT]: RiskLevel.MEDIUM,
  [Permission.REVIEWER_MANAGEMENT]: RiskLevel.HIGH,
  [Permission.ADMIN_MANAGEMENT]: RiskLevel.CRITICAL,
  [Permission.ROLE_MANAGEMENT]: RiskLevel.CRITICAL,

  // 系统设置权限
  [Permission.SETTINGS_PERSONAL]: RiskLevel.LOW,
  [Permission.DEIDENTIFICATION]: RiskLevel.HIGH,
  [Permission.SECURITY_SETTINGS]: RiskLevel.CRITICAL,
  [Permission.SYSTEM_CONFIG]: RiskLevel.CRITICAL,

  // 安全与审计权限
  [Permission.SECURITY_MONITOR]: RiskLevel.HIGH,
  [Permission.SECURITY_LOGS]: RiskLevel.HIGH,
  [Permission.ADMIN_AUDIT]: RiskLevel.HIGH,
  [Permission.LOGIN_RECORDS]: RiskLevel.MEDIUM,
};

/**
 * 权限服务类
 */
export class PermissionService {
  private static instance: PermissionService;
  private auditLogService: AuditLogService;

  private constructor() {
    this.auditLogService = AuditLogService.getInstance();
  }

  /**
   * 获取权限服务实例
   */
  public static getInstance(): PermissionService {
    if (!PermissionService.instance) {
      PermissionService.instance = new PermissionService();
    }
    return PermissionService.instance;
  }

  /**
   * 检查用户是否有指定权限
   * @param userRole 用户角色
   * @param permission 权限
   */
  public hasPermission(userRole: UserRole, permission: Permission): boolean {
    // 开发环境下，所有权限都通过
    if (process.env.ENVIRONMENT === 'development') {
      return true;
    }

    // 获取角色权限
    const permissions = rolePermissions[userRole] || [];

    // 检查是否有权限
    return permissions.includes(permission);
  }

  /**
   * 检查用户是否有多个权限中的任意一个
   * @param userRole 用户角色
   * @param permissions 权限列表
   */
  public hasAnyPermission(userRole: UserRole, permissions: Permission[]): boolean {
    return permissions.some(permission => this.hasPermission(userRole, permission));
  }

  /**
   * 检查用户是否有所有指定权限
   * @param userRole 用户角色
   * @param permissions 权限列表
   */
  public hasAllPermissions(userRole: UserRole, permissions: Permission[]): boolean {
    return permissions.every(permission => this.hasPermission(userRole, permission));
  }

  /**
   * 获取权限的风险等级
   * @param permission 权限
   */
  public getPermissionRisk(permission: Permission): RiskLevel {
    return permissionRisks[permission] || RiskLevel.LOW;
  }

  /**
   * 检查操作是否需要二次确认
   * @param permission 权限
   */
  public requiresConfirmation(permission: Permission): boolean {
    const risk = this.getPermissionRisk(permission);
    return risk === RiskLevel.HIGH || risk === RiskLevel.CRITICAL;
  }

  /**
   * 记录权限使用日志
   * @param userId 用户ID
   * @param permission 权限
   * @param resourceType 资源类型
   * @param resourceId 资源ID
   * @param details 详情
   * @param env 环境变量
   */
  public async logPermissionUse(
    userId: string,
    permission: Permission,
    resourceType: string,
    resourceId: string | undefined,
    details: any,
    env: Env
  ): Promise<void> {
    // 获取权限风险等级
    const risk = this.getPermissionRisk(permission);

    // 根据风险等级确定审计严重性
    let severity: AuditSeverity;
    switch (risk) {
      case RiskLevel.CRITICAL:
        severity = AuditSeverity.CRITICAL;
        break;
      case RiskLevel.HIGH:
        severity = AuditSeverity.WARNING;
        break;
      case RiskLevel.MEDIUM:
        severity = AuditSeverity.INFO;
        break;
      default:
        severity = AuditSeverity.DEBUG;
        break;
    }

    // 记录审计日志
    await this.auditLogService.log({
      userId,
      action: AuditAction.PERMISSION_USE,
      resourceType,
      resourceId,
      details: {
        permission,
        risk,
        ...details
      },
      severity
    }, env);
  }
}
