/**
 * 优化的图表组件
 * 
 * 使用组件级代码分割和按需加载优化图表组件
 */

import React, { useState } from 'react';
import { Box, Typography, Paper, Tabs, Tab, CircularProgress } from '@mui/material';
import { createLazyComponent, VisibilityLazyLoad, ConditionalLazyLoad } from '../../utils/componentSplitting';

// 懒加载图表组件
const LazyBarChart = createLazyComponent(
  () => import('./charts/BarChart'),
  {
    name: '<PERSON><PERSON><PERSON>',
    fallback: (
      <Box display="flex" justifyContent="center" alignItems="center" height={300}>
        <CircularProgress />
      </Box>
    ),
    timeout: 5000,
    retry: true,
    maxRetries: 2
  }
);

const LazyLineChart = createLazyComponent(
  () => import('./charts/LineChart'),
  {
    name: 'LineChart',
    fallback: (
      <Box display="flex" justifyContent="center" alignItems="center" height={300}>
        <CircularProgress />
      </Box>
    )
  }
);

const LazyPieChart = createLazyComponent(
  () => import('./charts/PieChart'),
  {
    name: 'PieChart',
    fallback: (
      <Box display="flex" justifyContent="center" alignItems="center" height={300}>
        <CircularProgress />
      </Box>
    )
  }
);

const LazyScatterChart = createLazyComponent(
  () => import('./charts/ScatterChart'),
  {
    name: 'ScatterChart',
    fallback: (
      <Box display="flex" justifyContent="center" alignItems="center" height={300}>
        <CircularProgress />
      </Box>
    )
  }
);

const LazyRadarChart = createLazyComponent(
  () => import('./charts/RadarChart'),
  {
    name: 'RadarChart',
    fallback: (
      <Box display="flex" justifyContent="center" alignItems="center" height={300}>
        <CircularProgress />
      </Box>
    )
  }
);

// 高级图表组件（仅在需要时加载）
const LazyHeatMap = createLazyComponent(
  () => import('./charts/HeatMap'),
  {
    name: 'HeatMap',
    fallback: (
      <Box display="flex" justifyContent="center" alignItems="center" height={300}>
        <CircularProgress />
      </Box>
    )
  }
);

const LazySankeyChart = createLazyComponent(
  () => import('./charts/SankeyChart'),
  {
    name: 'SankeyChart',
    fallback: (
      <Box display="flex" justifyContent="center" alignItems="center" height={300}>
        <CircularProgress />
      </Box>
    )
  }
);

const LazyTreeMap = createLazyComponent(
  () => import('./charts/TreeMap'),
  {
    name: 'TreeMap',
    fallback: (
      <Box display="flex" justifyContent="center" alignItems="center" height={300}>
        <CircularProgress />
      </Box>
    )
  }
);

/**
 * 优化的图表组件
 * @param {Object} props 组件属性
 * @param {Object} props.data 图表数据
 * @param {boolean} props.showAdvancedCharts 是否显示高级图表
 */
function OptimizedCharts({ data, showAdvancedCharts = false }) {
  const [activeTab, setActiveTab] = useState(0);
  
  // 处理标签页变化
  const handleTabChange = (event, newValue) => {
    setActiveTab(newValue);
    
    // 预加载下一个标签页的图表
    if (newValue < 4) {
      // 预加载下一个基础图表
      const nextChartIndex = (newValue + 1) % 5;
      preloadChartByIndex(nextChartIndex);
    } else if (showAdvancedCharts) {
      // 预加载下一个高级图表
      const nextChartIndex = (newValue + 1) % (showAdvancedCharts ? 8 : 5);
      preloadChartByIndex(nextChartIndex);
    }
  };
  
  // 根据索引预加载图表
  const preloadChartByIndex = (index) => {
    switch (index) {
      case 0:
        LazyBarChart.preload();
        break;
      case 1:
        LazyLineChart.preload();
        break;
      case 2:
        LazyPieChart.preload();
        break;
      case 3:
        LazyScatterChart.preload();
        break;
      case 4:
        LazyRadarChart.preload();
        break;
      case 5:
        LazyHeatMap.preload();
        break;
      case 6:
        LazySankeyChart.preload();
        break;
      case 7:
        LazyTreeMap.preload();
        break;
      default:
        break;
    }
  };
  
  return (
    <Paper elevation={2} sx={{ p: 2 }}>
      <Typography variant="h5" gutterBottom>
        数据可视化
      </Typography>
      
      <Tabs
        value={activeTab}
        onChange={handleTabChange}
        variant="scrollable"
        scrollButtons="auto"
        aria-label="chart tabs"
      >
        <Tab label="柱状图" />
        <Tab label="折线图" />
        <Tab label="饼图" />
        <Tab label="散点图" />
        <Tab label="雷达图" />
        {showAdvancedCharts && (
          <>
            <Tab label="热力图" />
            <Tab label="桑基图" />
            <Tab label="树形图" />
          </>
        )}
      </Tabs>
      
      <Box sx={{ mt: 2, minHeight: 300 }}>
        {/* 基础图表（使用标准懒加载） */}
        {activeTab === 0 && <LazyBarChart data={data.barData} />}
        {activeTab === 1 && <LazyLineChart data={data.lineData} />}
        {activeTab === 2 && <LazyPieChart data={data.pieData} />}
        
        {/* 使用可见性触发的懒加载 */}
        {activeTab === 3 && (
          <VisibilityLazyLoad
            factory={() => import('./charts/ScatterChart')}
            data={data.scatterData}
          />
        )}
        
        {activeTab === 4 && (
          <VisibilityLazyLoad
            factory={() => import('./charts/RadarChart')}
            data={data.radarData}
          />
        )}
        
        {/* 高级图表（使用条件懒加载） */}
        {activeTab === 5 && (
          <ConditionalLazyLoad
            factory={() => import('./charts/HeatMap')}
            condition={showAdvancedCharts}
            data={data.heatMapData}
            fallback={
              <Box display="flex" justifyContent="center" alignItems="center" height={300}>
                <Typography>请启用高级图表功能</Typography>
              </Box>
            }
          />
        )}
        
        {activeTab === 6 && (
          <ConditionalLazyLoad
            factory={() => import('./charts/SankeyChart')}
            condition={showAdvancedCharts}
            data={data.sankeyData}
          />
        )}
        
        {activeTab === 7 && (
          <ConditionalLazyLoad
            factory={() => import('./charts/TreeMap')}
            condition={showAdvancedCharts}
            data={data.treeMapData}
          />
        )}
      </Box>
    </Paper>
  );
}

export default OptimizedCharts;
