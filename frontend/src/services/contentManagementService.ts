/**
 * 内容管理API服务
 *
 * 提供完整的内容管理CRUD操作，包括：
 * - 待审核内容管理
 * - 故事审核
 * - 评论审核
 * - 标签管理
 * - 脱敏设置
 */

import { ApiResponse, PaginationParams, PaginationInfo } from '@/types/api';
import { getCurrentConfig } from '@/config/api.config';

// API基础配置
const API_BASE_URL = getCurrentConfig().api.baseURL;
const USER_MANAGEMENT_URL = getCurrentConfig().api.baseURL;

// 获取认证头
const getAuthHeaders = () => {
  const token = localStorage.getItem('adminToken') || localStorage.getItem('superadminToken');
  return {
    'Content-Type': 'application/json',
    'Authorization': token ? `Bearer ${token}` : '',
  };
};

// 通用API请求函数
const apiRequest = async <T>(
  url: string,
  options: RequestInit = {},
  useUserManagement = false
): Promise<ApiResponse<T>> => {
  try {
    const baseUrl = useUserManagement ? USER_MANAGEMENT_URL : API_BASE_URL;
    const response = await fetch(`${baseUrl}${url}`, {
      ...options,
      headers: {
        ...getAuthHeaders(),
        ...options.headers,
      },
    });

    if (!response.ok) {
      throw new Error(`HTTP ${response.status}: ${response.statusText}`);
    }

    const data = await response.json();
    return data;
  } catch (error) {
    console.error(`API请求失败 [${url}]:`, error);
    throw error;
  }
};

// ==================== 待审核内容管理 ====================

export interface PendingContent {
  id: string;
  sequenceNumber: string;
  type: 'story' | 'questionnaire' | 'comment' | 'profile' | 'feedback';
  originalContent: any;
  sanitizedContent: any;
  status: 'pending' | 'approved' | 'rejected' | 'flagged';
  originIp?: string;
  userAgent?: string;
  flags?: string[];
  priority: number;
  aiSuggestion?: string;
  aiConfidence?: number;
  aiExplanation?: string;
  reviewerId?: string;
  reviewedAt?: string;
  reviewNotes?: string;
  createdAt: string;
  updatedAt: string;
}

export interface PendingContentFilters {
  type?: string;
  status?: string;
  priority?: number;
  reviewerId?: string;
  dateFrom?: string;
  dateTo?: string;
  search?: string;
}

// 获取待审核内容列表
export const getPendingContents = async (
  params: PaginationParams & PendingContentFilters = {}
): Promise<ApiResponse<{ contents: PendingContent[]; pagination: PaginationInfo }>> => {
  const queryParams = new URLSearchParams();

  Object.entries(params).forEach(([key, value]) => {
    if (value !== undefined && value !== null && value !== '') {
      queryParams.append(key, String(value));
    }
  });

  return apiRequest(`/api/admin/review/pending?${queryParams}`);
};

// 别名导出，用于兼容性
export const getContentsPending = getPendingContents;

// 获取单个待审核内容详情
export const getPendingContent = async (id: string): Promise<ApiResponse<PendingContent>> => {
  return apiRequest(`/api/admin/review/pending/${id}`);
};

// 审核通过内容
export const approveContent = async (
  id: string,
  data: { reviewNotes?: string }
): Promise<ApiResponse<{ contentId: string }>> => {
  return apiRequest(`/api/admin/review/${id}/approve`, {
    method: 'POST',
    body: JSON.stringify(data),
  });
};

// 新版本审核通过函数
export const approveContentNew = approveContent;

// 编辑并通过内容
export const editAndApproveContent = async (
  id: string,
  data: { content: any; reviewNotes?: string }
): Promise<ApiResponse<{ contentId: string }>> => {
  return apiRequest(`/api/admin/review/${id}/edit`, {
    method: 'PUT',
    body: JSON.stringify(data),
  });
};

// 拒绝内容
export const rejectContent = async (
  id: string,
  data: { reason: string; reviewNotes?: string }
): Promise<ApiResponse<void>> => {
  return apiRequest(`/api/admin/review/${id}/reject`, {
    method: 'POST',
    body: JSON.stringify(data),
  });
};

// 新版本拒绝函数
export const rejectContentNew = rejectContent;

// 批量审核内容
export const batchModerateContent = async (
  contentIds: string[],
  action: 'approve' | 'reject',
  data: { reason?: string; reviewNotes?: string }
): Promise<ApiResponse<{ processed: number; failed: number }>> => {
  return apiRequest('/api/admin/review/batch', {
    method: 'POST',
    body: JSON.stringify({
      contentIds,
      action,
      ...data,
    }),
  });
};

// ==================== 故事管理 ====================

export interface Story {
  id: number;
  sequenceNumber: string;
  title: string;
  content: string;
  author?: string;
  isAnonymous: boolean;
  likes: number;
  dislikes: number;
  tags?: string[];
  category?: string;
  educationLevel?: string;
  industry?: string;
  status: 'pending' | 'approved' | 'rejected';
  submittedById?: string;
  createdAt: string;
  updatedAt: string;
}

// 获取待审核故事列表
export const getPendingStories = async (
  params: PaginationParams = {}
): Promise<ApiResponse<{ stories: Story[]; pagination: PaginationInfo }>> => {
  const queryParams = new URLSearchParams();

  Object.entries(params).forEach(([key, value]) => {
    if (value !== undefined && value !== null) {
      queryParams.append(key, String(value));
    }
  });

  return apiRequest(`/api/stories/pending?${queryParams}`);
};

// 审核故事
export const moderateStory = async (
  storyId: number,
  action: 'approve' | 'reject',
  data: { reason?: string; reviewNotes?: string } = {}
): Promise<ApiResponse<{ storyId: number }>> => {
  return apiRequest(`/api/stories/${storyId}/moderate`, {
    method: 'POST',
    body: JSON.stringify({
      action,
      ...data,
    }),
  });
};

// ==================== 评论管理 ====================

export interface Comment {
  id: string;
  sequenceNumber: string;
  content: string;
  userId?: string;
  userName?: string;
  targetType: 'story' | 'questionnaire';
  targetId: string;
  targetTitle?: string;
  status: 'pending' | 'approved' | 'rejected';
  flags?: string[];
  createdAt: string;
  updatedAt: string;
  originIp?: string;
}

// 获取待审核评论列表
export const getPendingComments = async (
  params: PaginationParams = {}
): Promise<ApiResponse<{ comments: Comment[]; pagination: PaginationInfo }>> => {
  const queryParams = new URLSearchParams();

  Object.entries(params).forEach(([key, value]) => {
    if (value !== undefined && value !== null) {
      queryParams.append(key, String(value));
    }
  });

  return apiRequest(`/api/admin/content-moderation/pending?${queryParams}`);
};

// 审核评论
export const moderateComment = async (
  commentId: string,
  action: 'approve' | 'reject',
  data: { reason?: string; reviewNotes?: string } = {}
): Promise<ApiResponse<{ commentId: string }>> => {
  return apiRequest(`/api/admin/content-moderation/${commentId}/moderate`, {
    method: 'POST',
    body: JSON.stringify({
      action,
      ...data,
    }),
  });
};

// ==================== 标签管理 ====================

export interface Tag {
  id: number;
  name: string;
  display_name?: string;
  description?: string;
  color: string;
  category: string;
  application?: string;
  usageCount: number;
  isSystem: boolean;
  createdAt: string;
  updatedAt: string;
}

export interface TagCreateData {
  name: string;
  description?: string;
  color: string;
  category: string;
}

export interface TagUpdateData extends Partial<TagCreateData> {
  id: number;
}

// 获取标签列表
export const getTags = async (
  params: { category?: string; search?: string } = {}
): Promise<ApiResponse<Tag[]>> => {
  const queryParams = new URLSearchParams();

  Object.entries(params).forEach(([key, value]) => {
    if (value !== undefined && value !== null && value !== '') {
      queryParams.append(key, String(value));
    }
  });

  return apiRequest(`/api/admin/tags?${queryParams}`);
};

// 创建标签
export const createTag = async (data: TagCreateData): Promise<ApiResponse<Tag>> => {
  return apiRequest('/api/admin/tags', {
    method: 'POST',
    body: JSON.stringify(data),
  });
};

// 更新标签
export const updateTag = async (id: number, data: Partial<TagCreateData>): Promise<ApiResponse<Tag>> => {
  return apiRequest(`/api/admin/tags/${id}`, {
    method: 'PUT',
    body: JSON.stringify(data),
  });
};

// 删除标签
export const deleteTag = async (id: number): Promise<ApiResponse<void>> => {
  return apiRequest(`/api/admin/tags/${id}`, {
    method: 'DELETE',
  });
};

// ==================== 审核统计 ====================

export interface ModerationStats {
  totalPending: number;
  totalApproved: number;
  totalRejected: number;
  todayProcessed: number;
  averageProcessingTime: number;
  topReviewers: Array<{
    reviewerId: string;
    reviewerName: string;
    processedCount: number;
    approvalRate: number;
  }>;
  contentTypeStats: Array<{
    type: string;
    pending: number;
    approved: number;
    rejected: number;
  }>;
}

// 获取审核统计
export const getModerationStats = async (
  params: { dateFrom?: string; dateTo?: string } = {}
): Promise<ApiResponse<ModerationStats>> => {
  const queryParams = new URLSearchParams();

  Object.entries(params).forEach(([key, value]) => {
    if (value !== undefined && value !== null && value !== '') {
      queryParams.append(key, String(value));
    }
  });

  return apiRequest(`/api/admin/content-moderation/stats?${queryParams}`);
};

// ==================== 审核历史 ====================

export interface ModerationHistory {
  id: string;
  contentId: string;
  contentType: string;
  reviewerId: string;
  reviewerName: string;
  action: 'approve' | 'reject' | 'edit';
  reviewNotes?: string;
  createdAt: string;
}

// 获取审核历史
export const getModerationHistory = async (
  params: PaginationParams & { contentId?: string; reviewerId?: string; action?: string } = {}
): Promise<ApiResponse<{ history: ModerationHistory[]; pagination: PaginationInfo }>> => {
  const queryParams = new URLSearchParams();

  Object.entries(params).forEach(([key, value]) => {
    if (value !== undefined && value !== null && value !== '') {
      queryParams.append(key, String(value));
    }
  });

  return apiRequest(`/api/admin/content-moderation/history?${queryParams}`);
};

// ==================== 脱敏设置管理 ====================

export interface DeidentificationRule {
  id: string;
  name: string;
  pattern: string;
  replacement: string;
  enabled: boolean;
  category: string;
}

export interface DeidentificationConfig {
  enabled: boolean;
  autoApply: boolean;
  logLevel: string;
  preserveLength: boolean;
  customReplacement: string;
}

export interface DeidentificationStats {
  totalProcessed: number;
  itemsDeidentified: number;
  rulesTriggered: number;
  lastProcessed: string;
}

// 获取脱敏规则列表
export const getDeidentificationRules = async (): Promise<ApiResponse<DeidentificationRule[]>> => {
  return apiRequest('/api/admin/deidentification/rules');
};

// 创建脱敏规则
export const createDeidentificationRule = async (rule: Omit<DeidentificationRule, 'id'>): Promise<ApiResponse<DeidentificationRule>> => {
  return apiRequest('/api/admin/deidentification/rules', {
    method: 'POST',
    body: JSON.stringify(rule),
  });
};

// 更新脱敏规则
export const updateDeidentificationRule = async (id: string, rule: Partial<DeidentificationRule>): Promise<ApiResponse<DeidentificationRule>> => {
  return apiRequest(`/api/admin/deidentification/rules/${id}`, {
    method: 'PUT',
    body: JSON.stringify(rule),
  });
};

// 删除脱敏规则
export const deleteDeidentificationRule = async (id: string): Promise<ApiResponse<void>> => {
  return apiRequest(`/api/admin/deidentification/rules/${id}`, {
    method: 'DELETE',
  });
};

// 获取脱敏配置
export const getDeidentificationConfig = async (): Promise<ApiResponse<DeidentificationConfig>> => {
  return apiRequest('/api/admin/deidentification/config');
};

// 更新脱敏配置
export const updateDeidentificationConfig = async (config: DeidentificationConfig): Promise<ApiResponse<DeidentificationConfig>> => {
  return apiRequest('/api/admin/deidentification/config', {
    method: 'PUT',
    body: JSON.stringify(config),
  });
};

// 测试脱敏
export const testDeidentification = async (content: string): Promise<ApiResponse<{ original: string; deidentified: string }>> => {
  return apiRequest('/api/admin/deidentification/test', {
    method: 'POST',
    body: JSON.stringify({ content }),
  });
};

// 获取脱敏统计
export const getDeidentificationStats = async (): Promise<ApiResponse<DeidentificationStats>> => {
  return apiRequest('/api/admin/deidentification/stats');
};

// ==================== 高级搜索和过滤 ====================

export interface AdvancedSearchFilters {
  // 基础过滤
  type?: string[];
  status?: string[];
  priority?: number[];

  // 时间过滤
  dateFrom?: string;
  dateTo?: string;
  timeRange?: 'today' | 'week' | 'month' | 'quarter' | 'year' | 'custom';

  // 内容过滤
  contentKeywords?: string[];
  contentLength?: { min?: number; max?: number };
  hasFlags?: boolean;
  flagTypes?: string[];

  // 用户过滤
  reviewerId?: string[];
  submitterId?: string[];
  isAnonymous?: boolean;

  // AI相关过滤
  aiConfidence?: { min?: number; max?: number };
  aiSuggestion?: string[];

  // 排序选项
  sortBy?: 'createdAt' | 'updatedAt' | 'priority' | 'aiConfidence' | 'contentLength';
  sortOrder?: 'asc' | 'desc';

  // 分组选项
  groupBy?: 'type' | 'status' | 'date' | 'reviewer' | 'priority';
}

export interface SearchResult<T> {
  items: T[];
  pagination: PaginationInfo;
  aggregations?: {
    totalByType: Record<string, number>;
    totalByStatus: Record<string, number>;
    totalByPriority: Record<string, number>;
    averageProcessingTime: number;
    flagDistribution: Record<string, number>;
  };
  suggestions?: string[];
}

// 高级搜索内容
export const advancedSearchContents = async (
  filters: AdvancedSearchFilters,
  params: PaginationParams = {}
): Promise<ApiResponse<SearchResult<PendingContent>>> => {
  const queryParams = new URLSearchParams();

  // 添加分页参数
  Object.entries(params).forEach(([key, value]) => {
    if (value !== undefined && value !== null && value !== '') {
      queryParams.append(key, String(value));
    }
  });

  // 添加过滤参数
  Object.entries(filters).forEach(([key, value]) => {
    if (value !== undefined && value !== null) {
      if (Array.isArray(value)) {
        value.forEach(v => queryParams.append(`${key}[]`, String(v)));
      } else if (typeof value === 'object') {
        queryParams.append(key, JSON.stringify(value));
      } else {
        queryParams.append(key, String(value));
      }
    }
  });

  return apiRequest(`/api/admin/search/contents?${queryParams}`);
};

// 获取搜索建议
export const getSearchSuggestions = async (
  query: string,
  type?: string
): Promise<ApiResponse<{ suggestions: string[]; recentSearches: string[] }>> => {
  const queryParams = new URLSearchParams();
  queryParams.append('q', query);
  if (type) queryParams.append('type', type);

  return apiRequest(`/api/admin/search/suggestions?${queryParams}`);
};

// 保存搜索条件
export const saveSearchFilter = async (
  name: string,
  filters: AdvancedSearchFilters
): Promise<ApiResponse<{ id: string; name: string }>> => {
  return apiRequest('/api/admin/search/filters', {
    method: 'POST',
    body: JSON.stringify({ name, filters }),
  });
};

// 获取保存的搜索条件
export const getSavedSearchFilters = async (): Promise<ApiResponse<Array<{ id: string; name: string; filters: AdvancedSearchFilters; createdAt: string }>>> => {
  return apiRequest('/api/admin/search/filters');
};

// ==================== 批量操作增强 ====================

export interface BatchOperation {
  action: 'approve' | 'reject' | 'edit' | 'assign' | 'tag' | 'priority' | 'export' | 'delete';
  itemIds: string[];
  data?: {
    // 审核相关
    reviewNotes?: string;
    reason?: string;

    // 分配相关
    assigneeId?: string;

    // 标签相关
    tags?: string[];
    addTags?: string[];
    removeTags?: string[];

    // 优先级相关
    priority?: number;

    // 编辑相关
    content?: any;

    // 导出相关
    format?: 'csv' | 'xlsx' | 'json' | 'pdf';
    fields?: string[];
  };
}

export interface BatchOperationResult {
  operationId: string;
  totalItems: number;
  processedItems: number;
  successfulItems: number;
  failedItems: number;
  errors: Array<{
    itemId: string;
    error: string;
  }>;
  status: 'pending' | 'processing' | 'completed' | 'failed';
  startedAt: string;
  completedAt?: string;
  downloadUrl?: string; // 用于导出操作
}

// 执行批量操作
export const executeBatchOperation = async (
  operation: BatchOperation
): Promise<ApiResponse<BatchOperationResult>> => {
  return apiRequest('/api/admin/batch/execute', {
    method: 'POST',
    body: JSON.stringify(operation),
  });
};

// 获取批量操作状态
export const getBatchOperationStatus = async (
  operationId: string
): Promise<ApiResponse<BatchOperationResult>> => {
  return apiRequest(`/api/admin/batch/status/${operationId}`);
};

// 获取批量操作历史
export const getBatchOperationHistory = async (
  params: PaginationParams = {}
): Promise<ApiResponse<{ operations: BatchOperationResult[]; pagination: PaginationInfo }>> => {
  const queryParams = new URLSearchParams();

  Object.entries(params).forEach(([key, value]) => {
    if (value !== undefined && value !== null && value !== '') {
      queryParams.append(key, String(value));
    }
  });

  return apiRequest(`/api/admin/batch/history?${queryParams}`);
};

// 取消批量操作
export const cancelBatchOperation = async (
  operationId: string
): Promise<ApiResponse<void>> => {
  return apiRequest(`/api/admin/batch/cancel/${operationId}`, {
    method: 'POST',
  });
};

// ==================== 导出功能 ====================

export interface ExportOptions {
  format: 'csv' | 'xlsx' | 'json' | 'pdf';
  fields?: string[];
  filters?: AdvancedSearchFilters;
  includeMetadata?: boolean;
  includeStatistics?: boolean;
  dateRange?: {
    from: string;
    to: string;
  };
  template?: string;
}

export interface ExportJob {
  id: string;
  type: 'contents' | 'stories' | 'comments' | 'tags' | 'statistics' | 'audit_log';
  status: 'pending' | 'processing' | 'completed' | 'failed';
  progress: number;
  totalItems: number;
  processedItems: number;
  options: ExportOptions;
  downloadUrl?: string;
  fileSize?: number;
  createdAt: string;
  completedAt?: string;
  expiresAt?: string;
  error?: string;
}

// 创建导出任务
export const createExportJob = async (
  type: ExportJob['type'],
  options: ExportOptions
): Promise<ApiResponse<ExportJob>> => {
  return apiRequest('/api/admin/export/create', {
    method: 'POST',
    body: JSON.stringify({ type, options }),
  });
};

// 获取导出任务状态
export const getExportJobStatus = async (
  jobId: string
): Promise<ApiResponse<ExportJob>> => {
  return apiRequest(`/api/admin/export/status/${jobId}`);
};

// 获取导出任务列表
export const getExportJobs = async (
  params: PaginationParams = {}
): Promise<ApiResponse<{ jobs: ExportJob[]; pagination: PaginationInfo }>> => {
  const queryParams = new URLSearchParams();

  Object.entries(params).forEach(([key, value]) => {
    if (value !== undefined && value !== null && value !== '') {
      queryParams.append(key, String(value));
    }
  });

  return apiRequest(`/api/admin/export/jobs?${queryParams}`);
};

// 下载导出文件
export const downloadExportFile = async (
  jobId: string
): Promise<Blob> => {
  const token = localStorage.getItem('adminToken') || localStorage.getItem('superadminToken');
  const response = await fetch(`${API_BASE_URL}/api/admin/export/download/${jobId}`, {
    headers: {
      'Authorization': token ? `Bearer ${token}` : '',
    },
  });

  if (!response.ok) {
    throw new Error(`下载失败: ${response.statusText}`);
  }

  return response.blob();
};

// 删除导出任务
export const deleteExportJob = async (
  jobId: string
): Promise<ApiResponse<void>> => {
  return apiRequest(`/api/admin/export/delete/${jobId}`, {
    method: 'DELETE',
  });
};

// 获取导出模板
export const getExportTemplates = async (
  type: ExportJob['type']
): Promise<ApiResponse<Array<{ id: string; name: string; description: string; fields: string[] }>>> => {
  return apiRequest(`/api/admin/export/templates/${type}`);
};
