{"info": {"_postman_id": "12345678-1234-1234-1234-123456789012", "name": "College Employment Survey API", "description": "API collection for testing the College Employment Survey API", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json"}, "item": [{"name": "Questionnaire", "item": [{"name": "Submit Questionnaire", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"educationLevel\": \"本科\",\n  \"major\": \"计算机科学\",\n  \"graduationYear\": 2023,\n  \"region\": \"北上广深\",\n  \"expectedPosition\": \"软件工程师\",\n  \"expectedSalaryRange\": \"12000-20000元\",\n  \"expectedWorkHours\": 40,\n  \"expectedVacationDays\": 15,\n  \"employmentStatus\": \"已就业\",\n  \"currentIndustry\": \"互联网/IT\",\n  \"currentPosition\": \"前端开发\",\n  \"jobSatisfaction\": 4,\n  \"regretMajor\": false,\n  \"careerChangeIntention\": false,\n  \"adviceForStudents\": \"多参加实习，提前了解行业情况，培养实际技能，不要只关注理论知识。\",\n  \"observationOnEmployment\": \"就业压力大，竞争激烈，需要不断学习和提升自己的能力。\",\n  \"isAnonymous\": true\n}"}, "url": {"raw": "{{baseUrl}}/questionnaire/submit", "host": ["{{baseUrl}}"], "path": ["questionnaire", "submit"]}, "description": "Submit a questionnaire response"}, "response": []}, {"name": "Get Questionnaire Stats", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/questionnaire/stats", "host": ["{{baseUrl}}"], "path": ["questionnaire", "stats"], "query": [{"key": "verified", "value": "true", "description": "Filter by verified responses", "disabled": true}, {"key": "educationLevel", "value": "本科", "description": "Filter by education level", "disabled": true}, {"key": "region", "value": "北上广深", "description": "Filter by region", "disabled": true}, {"key": "graduationYear", "value": "2023", "description": "Filter by graduation year", "disabled": true}]}, "description": "Get questionnaire statistics"}, "response": []}], "description": "Questionnaire API endpoints"}, {"name": "Visualization", "item": [{"name": "Get Visualization Data", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/visualization/data", "host": ["{{baseUrl}}"], "path": ["visualization", "data"], "query": [{"key": "verified", "value": "true", "description": "Filter by verified responses", "disabled": true}, {"key": "educationLevel", "value": "本科", "description": "Filter by education level", "disabled": true}, {"key": "region", "value": "北上广深", "description": "Filter by region", "disabled": true}, {"key": "graduationYear", "value": "2023", "description": "Filter by graduation year", "disabled": true}]}, "description": "Get visualization data"}, "response": []}], "description": "Visualization API endpoints"}, {"name": "Story", "item": [{"name": "Submit Story", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"title\": \"我的求职经历与感悟\",\n  \"content\": \"毕业后我经历了三个月的求职期，投了上百份简历，参加了二十多场面试，最终找到了一份满意的工作。期间有很多挫折和困难，但也学到了很多东西。\",\n  \"isAnonymous\": true,\n  \"tags\": [\"job-hunting\", \"advice\"]\n}"}, "url": {"raw": "{{baseUrl}}/story/submit", "host": ["{{baseUrl}}"], "path": ["story", "submit"]}, "description": "Submit a story"}, "response": []}, {"name": "Vote Story", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"storyId\": 1,\n  \"voteType\": \"like\"\n}"}, "url": {"raw": "{{baseUrl}}/story/vote", "host": ["{{baseUrl}}"], "path": ["story", "vote"]}, "description": "Vote on a story"}, "response": []}, {"name": "Get Story List", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/story/list", "host": ["{{baseUrl}}"], "path": ["story", "list"], "query": [{"key": "page", "value": "1", "description": "Page number", "disabled": true}, {"key": "pageSize", "value": "6", "description": "Page size", "disabled": true}, {"key": "sortBy", "value": "latest", "description": "Sort by (latest or popular)", "disabled": true}, {"key": "tag", "value": "job-hunting", "description": "Filter by tag", "disabled": true}]}, "description": "Get a list of stories"}, "response": []}], "description": "Story API endpoints"}], "event": [{"listen": "prerequest", "script": {"type": "text/javascript", "exec": [""]}}, {"listen": "test", "script": {"type": "text/javascript", "exec": [""]}}], "variable": [{"key": "baseUrl", "value": "http://localhost:8787/api", "type": "string"}]}