#!/usr/bin/env node

/**
 * 🚀 本地开发服务器
 * 为Cloudflare Workers应用提供本地开发环境
 */

import { serve } from '@hono/node-server';
import app from './index.js';

// 模拟Cloudflare环境变量
const mockEnv = {
  DB: {
    prepare: (query) => {
      console.log('Mock DB Query:', query);

      const mockStatement = {
        // 直接支持first()和all()方法，不需要bind()
        first: async () => {
          console.log('Mock DB Query (first):', query);

          // 根据查询类型返回不同的模拟数据
          if (query.includes('COUNT(*)') || query.includes('count(*)')) {
            if (query.includes('is_anonymous = 0')) {
              return { total: 126 }; // 实名用户
            } else if (query.includes('is_anonymous = 1')) {
              return { total: 47 }; // 匿名用户
            } else if (query.includes('employment_status_display')) {
              return { total: 89 }; // 就业状态
            } else {
              return { total: 173 }; // 总数
            }
          }

          if (query.includes('SELECT') && query.includes('major_display')) {
            return { name: '计算机科学', count: 45 };
          }

          return { id: 1, value: 'mock_data' };
        },
        all: async () => {
          console.log('Mock DB Query (all):', query);

          // 返回模拟的专业分布数据
          if (query.includes('major_display')) {
            return {
              results: [
                { name: '计算机科学', count: 45 },
                { name: '软件工程', count: 32 },
                { name: '数据科学', count: 28 },
                { name: '人工智能', count: 25 },
                { name: '网络工程', count: 22 }
              ]
            };
          }

          // 返回模拟的毕业年份数据
          if (query.includes('graduation_year')) {
            return {
              results: [
                { name: '2024', count: 89 },
                { name: '2023', count: 56 },
                { name: '2022', count: 28 }
              ]
            };
          }

          // 返回模拟的教育水平数据
          if (query.includes('education_level')) {
            return {
              results: [
                { name: '本科', count: 134 },
                { name: '硕士', count: 32 },
                { name: '博士', count: 7 }
              ]
            };
          }

          return {
            results: [
              { id: 1, name: '模拟数据1', value: 45 },
              { id: 2, name: '模拟数据2', value: 32 },
              { id: 3, name: '模拟数据3', value: 28 }
            ]
          };
        },
        run: async () => {
          console.log('Mock DB Query (run):', query);
          return { success: true, changes: 1 };
        },
        bind: (...params) => {
          console.log('Mock DB Params:', params);
          return {
            first: async () => {
              console.log('Mock DB Query (first):', query, params);

              // 根据查询类型返回不同的模拟数据
              if (query.includes('COUNT(*)') || query.includes('count(*)')) {
                if (query.includes('is_anonymous = 0')) {
                  return { total: 126 }; // 实名用户
                } else if (query.includes('is_anonymous = 1')) {
                  return { total: 47 }; // 匿名用户
                } else if (query.includes('employment_status_display')) {
                  return { total: 89 }; // 就业状态
                } else {
                  return { total: 173 }; // 总数
                }
              }

              if (query.includes('SELECT') && query.includes('major_display')) {
                return { name: '计算机科学', count: 45 };
              }

              return { id: 1, value: 'mock_data' };
            },
            all: async () => {
              console.log('Mock DB Query (all):', query, params);
              // 返回模拟的问卷心声数据
              if (query.includes('questionnaire_voices_v2')) {
                return {
                  results: [
                    {
                      id: 1,
                      voice_type: 'advice',
                      title: '给高三学子的建议',
                      content: '要保持积极的心态，相信自己的能力。大学只是人生的一个阶段，不是终点。无论选择什么专业，都要努力学习，培养自己的核心竞争力。',
                      education_level_display: '本科',
                      region_display: '北京',
                      likes: 15,
                      views: 120,
                      created_at: '2024-05-27T08:00:00Z'
                    },
                    {
                      id: 2,
                      voice_type: 'observation',
                      title: '对当前就业环境的观察',
                      content: '当前就业市场竞争激烈，但机会依然存在。技术类岗位需求较大，建议同学们提升自己的技术能力和实践经验。',
                      education_level_display: '硕士',
                      region_display: '上海',
                      likes: 23,
                      views: 89,
                      created_at: '2024-05-27T07:30:00Z'
                    },
                    {
                      id: 3,
                      voice_type: 'advice',
                      title: '学习方法分享',
                      content: '学习要有计划性，制定明确的目标。多参与实践项目，理论结合实际。保持好奇心，持续学习新知识。',
                      education_level_display: '本科',
                      region_display: '广州',
                      likes: 8,
                      views: 67,
                      created_at: '2024-05-27T07:00:00Z'
                    }
                  ]
                };
              }


              // 返回模拟的专业分布数据
              if (query.includes('major_display')) {
                return {
                  results: [
                    { name: '计算机科学', count: 45 },
                    { name: '软件工程', count: 32 },
                    { name: '数据科学', count: 28 },
                    { name: '人工智能', count: 25 },
                    { name: '网络工程', count: 22 }
                  ]
                };
              }

              // 返回模拟的毕业年份数据
              if (query.includes('graduation_year')) {
                return {
                  results: [
                    { name: '2024', count: 89 },
                    { name: '2023', count: 56 },
                    { name: '2022', count: 28 }
                  ]
                };
              }

              // 返回模拟的教育水平数据
              if (query.includes('education_level')) {
                return {
                  results: [
                    { name: '本科', count: 134 },
                    { name: '硕士', count: 32 },
                    { name: '博士', count: 7 }
                  ]
                };
              }

              return {
                results: [
                  { id: 1, name: '模拟数据1', value: 45 },
                  { id: 2, name: '模拟数据2', value: 32 },
                  { id: 3, name: '模拟数据3', value: 28 }
                ]
              };
            },
            run: async () => {
              console.log('Mock DB Query (run):', query, params);
              return { success: true, changes: 1 };
            }
          };
        }
      };

      return mockStatement;
    }
  },
  KV: {
    get: async (key) => {
      console.log('Mock KV Get:', key);
      return null;
    },
    put: async (key, value) => {
      console.log('Mock KV Put:', key, value);
      return;
    }
  }
};

// 创建带有模拟环境的应用
const createAppWithEnv = () => {
  return {
    fetch: (request, env = mockEnv, ctx = {}) => {
      // 为每个请求添加模拟环境
      return app.fetch(request, env, ctx);
    }
  };
};

const port = process.env.PORT || 8788;

console.log('🚀 启动本地开发服务器...');
console.log(`📍 服务器地址: http://localhost:${port}`);
console.log('🔧 环境: 本地开发模式 (使用模拟数据)');

// 启动服务器
serve({
  fetch: (request) => {
    return app.fetch(request, mockEnv, {});
  },
  port: port
}, (info) => {
  console.log(`✅ 服务器已启动在端口 ${info.port}`);
  console.log('\n📋 可用的API端点:');
  console.log('  🏥 健康检查: GET /health');
  console.log('  📊 问卷统计: GET /api/questionnaire/stats');
  console.log('  📈 可视化数据: GET /api/visualization/data');
  console.log('  📝 问卷心声: GET /api/questionnaire-voices');
  console.log('  📚 故事列表: GET /api/story/list');
  console.log('  👤 管理员登录: POST /api/admin/login');
  console.log('  🎛️ 管理员仪表板: GET /api/admin/dashboard/stats');
  console.log('\n🔍 测试命令:');
  console.log(`  curl http://localhost:${port}/health`);
  console.log(`  curl http://localhost:${port}/api/questionnaire/stats`);
});
