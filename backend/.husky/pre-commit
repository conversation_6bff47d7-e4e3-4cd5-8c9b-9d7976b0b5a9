#!/usr/bin/env sh
. "$(dirname -- "$0")/_/husky.sh"

echo "🔍 Running pre-commit checks..."

# 运行代码质量检查
echo "📋 Checking code quality..."
npm run quality:check

# 运行单元测试
echo "🧪 Running unit tests..."
npm run test:unit

# 检查是否有未提交的格式化更改
echo "🎨 Checking for formatting issues..."
if ! npm run format:check; then
  echo "❌ Code formatting issues found. Run 'npm run format' to fix them."
  exit 1
fi

echo "✅ Pre-commit checks passed!"
