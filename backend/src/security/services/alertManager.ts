/**
 * 告警管理服务
 * 
 * 负责管理和发送安全告警
 */

import { AlertingConfig, AlertChannelConfig } from '../types';
import { SecurityError } from '../errors';
import { LogEntry } from './securityLogger';

/**
 * 告警信息
 */
interface Alert {
  id: string;
  timestamp: number;
  title: string;
  message: string;
  severity: 'low' | 'medium' | 'high' | 'critical';
  source: string;
  errorCode?: string;
  details?: any;
  relatedLogIds?: string[];
  fingerprint?: string;
}

/**
 * 告警状态
 */
interface AlertState {
  sentAlerts: Map<string, Alert>;
  alertCountByHour: Map<string, number>;
  lastAlertTime: Map<string, number>;
  alertGroups: Map<string, Alert[]>;
}

/**
 * 告警管理服务类
 */
export class AlertManager {
  private config: AlertingConfig;
  private state: AlertState;

  /**
   * 构造函数
   */
  constructor() {
    this.config = {
      enabled: false,
      channels: [],
      throttleInterval: 300000, // 5分钟
      minSeverity: 'high',
      groupSimilarAlerts: true,
      includeDetails: true,
      maxAlertsPerHour: 10
    };

    this.state = {
      sentAlerts: new Map(),
      alertCountByHour: new Map(),
      lastAlertTime: new Map(),
      alertGroups: new Map()
    };
  }

  /**
   * 初始化服务
   * @param config 告警配置
   */
  public initialize(config: AlertingConfig): void {
    this.config = { ...config };
    
    // 确保至少有一个启用的渠道
    if (this.config.enabled && this.config.channels.length === 0) {
      // 添加默认控制台渠道
      this.config.channels.push({
        type: 'console',
        enabled: true,
        name: 'Console'
      });
    }
  }

  /**
   * 更新配置
   * @param config 告警配置
   */
  public updateConfig(config: Partial<AlertingConfig>): void {
    this.config = { ...this.config, ...config };
  }

  /**
   * 发送错误告警
   * @param error 错误对象
   * @param context 上下文信息
   */
  public async sendErrorAlert(
    error: SecurityError,
    context?: {
      source?: string;
      details?: any;
      relatedLogIds?: string[];
    }
  ): Promise<boolean> {
    if (!this.config.enabled) {
      return false;
    }

    // 创建告警
    const alert: Alert = {
      id: `alert_${Date.now()}_${Math.random().toString(36).substring(2, 9)}`,
      timestamp: Date.now(),
      title: `Security Error: ${error.code || error.name}`,
      message: error.message,
      severity: error.severity || 'medium',
      source: context?.source || error.context?.source || 'security',
      errorCode: error.code,
      details: this.config.includeDetails ? {
        ...error.context,
        ...context?.details
      } : undefined,
      relatedLogIds: context?.relatedLogIds,
      fingerprint: this.generateAlertFingerprint(error)
    };

    // 检查是否应该发送告警
    if (!this.shouldSendAlert(alert)) {
      return false;
    }

    // 发送告警
    const success = await this.sendAlert(alert);

    // 更新状态
    if (success) {
      this.updateAlertState(alert);
    }

    return success;
  }

  /**
   * 发送日志告警
   * @param logs 日志条目
   * @param title 告警标题
   * @param message 告警消息
   * @param severity 严重性
   * @param source 来源
   * @param details 详细信息
   */
  public async sendLogAlert(
    logs: LogEntry[],
    title: string,
    message: string,
    severity: 'low' | 'medium' | 'high' | 'critical',
    source: string,
    details?: any
  ): Promise<boolean> {
    if (!this.config.enabled || logs.length === 0) {
      return false;
    }

    // 创建告警
    const alert: Alert = {
      id: `alert_${Date.now()}_${Math.random().toString(36).substring(2, 9)}`,
      timestamp: Date.now(),
      title,
      message,
      severity,
      source,
      details: this.config.includeDetails ? details : undefined,
      relatedLogIds: logs.map(log => log.id),
      fingerprint: this.generateLogAlertFingerprint(title, source)
    };

    // 检查是否应该发送告警
    if (!this.shouldSendAlert(alert)) {
      return false;
    }

    // 发送告警
    const success = await this.sendAlert(alert);

    // 更新状态
    if (success) {
      this.updateAlertState(alert);
    }

    return success;
  }

  /**
   * 发送异常检测告警
   * @param anomalyType 异常类型
   * @param anomalyData 异常数据
   * @param severity 严重性
   * @param source 来源
   */
  public async sendAnomalyAlert(
    anomalyType: string,
    anomalyData: any,
    severity: 'low' | 'medium' | 'high' | 'critical',
    source: string
  ): Promise<boolean> {
    if (!this.config.enabled) {
      return false;
    }

    // 创建告警
    const alert: Alert = {
      id: `alert_${Date.now()}_${Math.random().toString(36).substring(2, 9)}`,
      timestamp: Date.now(),
      title: `Anomaly Detected: ${anomalyType}`,
      message: anomalyData.reason || `Anomaly score: ${anomalyData.score}`,
      severity,
      source,
      details: this.config.includeDetails ? anomalyData : undefined,
      fingerprint: this.generateAnomalyAlertFingerprint(anomalyType, source)
    };

    // 检查是否应该发送告警
    if (!this.shouldSendAlert(alert)) {
      return false;
    }

    // 发送告警
    const success = await this.sendAlert(alert);

    // 更新状态
    if (success) {
      this.updateAlertState(alert);
    }

    return success;
  }

  /**
   * 检查是否应该发送告警
   * @param alert 告警信息
   */
  private shouldSendAlert(alert: Alert): boolean {
    // 检查严重性
    const severityLevels = ['low', 'medium', 'high', 'critical'];
    const configSeverityIndex = severityLevels.indexOf(this.config.minSeverity);
    const alertSeverityIndex = severityLevels.indexOf(alert.severity);
    
    if (alertSeverityIndex < configSeverityIndex) {
      return false;
    }

    // 检查节流
    const now = Date.now();
    const lastAlertTime = this.state.lastAlertTime.get(alert.fingerprint || '');
    
    if (lastAlertTime && now - lastAlertTime < this.config.throttleInterval) {
      // 如果在节流间隔内，添加到分组
      if (this.config.groupSimilarAlerts && alert.fingerprint) {
        const group = this.state.alertGroups.get(alert.fingerprint) || [];
        group.push(alert);
        this.state.alertGroups.set(alert.fingerprint, group);
      }
      return false;
    }

    // 检查每小时最大告警数量
    const hourKey = new Date().toISOString().substring(0, 13);
    const hourCount = this.state.alertCountByHour.get(hourKey) || 0;
    
    if (hourCount >= this.config.maxAlertsPerHour) {
      return false;
    }

    return true;
  }

  /**
   * 发送告警
   * @param alert 告警信息
   */
  private async sendAlert(alert: Alert): Promise<boolean> {
    let success = false;

    // 遍历所有启用的渠道
    for (const channel of this.config.channels) {
      if (!channel.enabled) {
        continue;
      }

      // 检查渠道特定的严重性
      if (channel.minSeverity) {
        const severityLevels = ['low', 'medium', 'high', 'critical'];
        const channelSeverityIndex = severityLevels.indexOf(channel.minSeverity);
        const alertSeverityIndex = severityLevels.indexOf(alert.severity);
        
        if (alertSeverityIndex < channelSeverityIndex) {
          continue;
        }
      }

      // 检查渠道特定的节流
      if (channel.throttleInterval) {
        const now = Date.now();
        const channelKey = `${channel.name}_${alert.fingerprint || ''}`;
        const lastAlertTime = this.state.lastAlertTime.get(channelKey);
        
        if (lastAlertTime && now - lastAlertTime < channel.throttleInterval) {
          continue;
        }
        
        this.state.lastAlertTime.set(channelKey, now);
      }

      // 发送到特定渠道
      try {
        await this.sendToChannel(alert, channel);
        success = true;
      } catch (error) {
        console.error(`Failed to send alert to channel ${channel.name}:`, error);
      }
    }

    return success;
  }

  /**
   * 发送到特定渠道
   * @param alert 告警信息
   * @param channel 渠道配置
   */
  private async sendToChannel(alert: Alert, channel: AlertChannelConfig): Promise<void> {
    switch (channel.type) {
      case 'console':
        this.sendToConsole(alert);
        break;
      case 'email':
        await this.sendToEmail(alert, channel);
        break;
      case 'webhook':
        await this.sendToWebhook(alert, channel);
        break;
      case 'custom':
        await this.sendToCustom(alert, channel);
        break;
    }
  }

  /**
   * 发送到控制台
   * @param alert 告警信息
   */
  private sendToConsole(alert: Alert): void {
    const timestamp = new Date(alert.timestamp).toISOString();
    const prefix = `[SECURITY ALERT][${alert.severity.toUpperCase()}] ${timestamp}:`;
    
    console.warn(prefix, alert.title);
    console.warn(alert.message);
    
    if (alert.details) {
      console.warn('Details:', alert.details);
    }
  }

  /**
   * 发送到邮件
   * @param alert 告警信息
   * @param channel 渠道配置
   */
  private async sendToEmail(alert: Alert, channel: AlertChannelConfig): Promise<void> {
    // 这里只是模拟实现，实际应用中需要集成邮件发送服务
    if (!channel.recipients || channel.recipients.length === 0) {
      console.warn('No email recipients configured');
      return;
    }
    
    console.log(`[EMAIL ALERT] Would send email to ${channel.recipients.join(', ')}`);
    console.log(`Subject: ${alert.title}`);
    console.log(`Body: ${alert.message}`);
    
    if (alert.details) {
      console.log('Details:', JSON.stringify(alert.details, null, 2));
    }
  }

  /**
   * 发送到 Webhook
   * @param alert 告警信息
   * @param channel 渠道配置
   */
  private async sendToWebhook(alert: Alert, channel: AlertChannelConfig): Promise<void> {
    if (!channel.webhookUrl) {
      console.warn('No webhook URL configured');
      return;
    }
    
    try {
      // 这里只是模拟实现，实际应用中需要发送 HTTP 请求
      console.log(`[WEBHOOK ALERT] Would send webhook to ${channel.webhookUrl}`);
      console.log('Payload:', JSON.stringify({
        title: alert.title,
        message: alert.message,
        severity: alert.severity,
        timestamp: alert.timestamp,
        source: alert.source,
        details: alert.details
      }, null, 2));
    } catch (error) {
      console.error('Error sending webhook:', error);
      throw error;
    }
  }

  /**
   * 发送到自定义渠道
   * @param alert 告警信息
   * @param channel 渠道配置
   */
  private async sendToCustom(alert: Alert, channel: AlertChannelConfig): Promise<void> {
    // 这里只是占位实现，实际应用中需要根据需求实现自定义渠道
    console.log(`[CUSTOM ALERT] Would send to custom channel ${channel.name}`);
    console.log('Alert:', JSON.stringify(alert, null, 2));
  }

  /**
   * 更新告警状态
   * @param alert 告警信息
   */
  private updateAlertState(alert: Alert): void {
    // 保存已发送的告警
    this.state.sentAlerts.set(alert.id, alert);
    
    // 更新最后告警时间
    if (alert.fingerprint) {
      this.state.lastAlertTime.set(alert.fingerprint, alert.timestamp);
    }
    
    // 更新每小时告警计数
    const hourKey = new Date(alert.timestamp).toISOString().substring(0, 13);
    const hourCount = this.state.alertCountByHour.get(hourKey) || 0;
    this.state.alertCountByHour.set(hourKey, hourCount + 1);
    
    // 清理旧的告警组
    if (alert.fingerprint && this.state.alertGroups.has(alert.fingerprint)) {
      this.state.alertGroups.delete(alert.fingerprint);
    }
    
    // 清理旧数据
    this.cleanupOldData();
  }

  /**
   * 清理旧数据
   */
  private cleanupOldData(): void {
    const now = Date.now();
    const oneDayAgo = now - 24 * 60 * 60 * 1000;
    
    // 清理旧的已发送告警
    for (const [id, alert] of this.state.sentAlerts.entries()) {
      if (alert.timestamp < oneDayAgo) {
        this.state.sentAlerts.delete(id);
      }
    }
    
    // 清理旧的每小时计数
    const currentHour = new Date().toISOString().substring(0, 13);
    for (const hourKey of this.state.alertCountByHour.keys()) {
      if (hourKey < currentHour) {
        this.state.alertCountByHour.delete(hourKey);
      }
    }
  }

  /**
   * 生成告警指纹
   * @param error 错误对象
   */
  private generateAlertFingerprint(error: SecurityError): string {
    return `error_${error.code || error.name}_${error.severity || 'medium'}`;
  }

  /**
   * 生成日志告警指纹
   * @param title 告警标题
   * @param source 来源
   */
  private generateLogAlertFingerprint(title: string, source: string): string {
    return `log_${title.replace(/\s+/g, '_').toLowerCase()}_${source}`;
  }

  /**
   * 生成异常告警指纹
   * @param anomalyType 异常类型
   * @param source 来源
   */
  private generateAnomalyAlertFingerprint(anomalyType: string, source: string): string {
    return `anomaly_${anomalyType.replace(/\s+/g, '_').toLowerCase()}_${source}`;
  }
}
