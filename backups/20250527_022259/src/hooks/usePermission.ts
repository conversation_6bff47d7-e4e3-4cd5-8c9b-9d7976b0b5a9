import { useAuth } from '@/contexts/AuthContext';
import { Permission, hasPermission, hasAnyPermission, hasAllPermissions } from '@/lib/permissions';

/**
 * 权限检查钩子
 *
 * 提供检查当前用户权限的方法
 */
export function usePermission() {
  const { user } = useAuth();

  /**
   * 检查用户是否拥有指定权限
   *
   * @param permission 需要检查的权限
   * @returns 是否拥有权限
   */
  const checkPermission = (permission: Permission): boolean => {
    if (!user || !user.role) return false;
    return hasPermission(user.role, permission);
  };

  /**
   * 检查用户是否拥有指定权限中的任意一个
   *
   * @param permissions 需要检查的权限列表
   * @returns 是否拥有任意一个权限
   */
  const checkAnyPermission = (permissions: Permission[]): boolean => {
    if (!user || !user.role) return false;
    return hasAnyPermission(user.role, permissions);
  };

  /**
   * 检查用户是否拥有指定权限中的所有权限
   *
   * @param permissions 需要检查的权限列表
   * @returns 是否拥有所有权限
   */
  const checkAllPermissions = (permissions: Permission[]): boolean => {
    if (!user || !user.role) return false;
    return hasAllPermissions(user.role, permissions);
  };

  return {
    checkPermission,
    checkAnyPermission,
    checkAllPermissions
  };
}
