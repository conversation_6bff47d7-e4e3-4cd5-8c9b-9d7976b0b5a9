# 用户认证故事发布系统 API 设计 (基于A+B匿名身份验证)

## 🔐 用户认证 API

### A+B匿名身份注册 (主要方式)
```http
POST /api/auth/register-anonymous
Content-Type: application/json

{
  "identity_a": "12345678901",          // A值 (11位数字，如手机号)
  "identity_b": "1234",                 // B值 (4位或6位密码)
  "display_name": "用户显示名",          // 可选
  "education_level": "本科",
  "industry_code": "technology",
  "graduation_year": 2023,
  "location": "北京"
}

Response:
{
  "success": true,
  "data": {
    "user_uuid": "anon_sha256_hash_uuid",
    "anonymous_uuid": "uuid_generated_from_ab",
    "display_name": "用户显示名",
    "auth_type": "anonymous",
    "token": "jwt_token_here",
    "expires_at": "2025-05-26T10:00:00Z"
  },
  "message": "匿名身份注册成功"
}
```

### A+B匿名身份登录
```http
POST /api/auth/login-anonymous
Content-Type: application/json

{
  "identity_a": "12345678901",
  "identity_b": "1234"
}

Response:
{
  "success": true,
  "data": {
    "user_uuid": "anon_sha256_hash_uuid",
    "anonymous_uuid": "uuid_generated_from_ab",
    "display_name": "用户显示名",
    "auth_type": "anonymous",
    "token": "jwt_token_here",
    "expires_at": "2025-05-26T10:00:00Z",
    "stories_count": 5,
    "questionnaire_count": 3
  },
  "message": "登录成功"
}
```

### 邮箱注册 (可选方式)
```http
POST /api/auth/register-email
Content-Type: application/json

{
  "email": "<EMAIL>",
  "password": "password123",
  "display_name": "用户显示名",
  "education_level": "本科",
  "industry_code": "technology",
  "graduation_year": 2023,
  "location": "北京"
}

Response:
{
  "success": true,
  "data": {
    "user_uuid": "user_1234567890_abcdef",
    "email": "<EMAIL>",
    "display_name": "用户显示名",
    "auth_type": "email",
    "email_verification_required": true
  },
  "message": "注册成功，请验证邮箱"
}
```

### 邮箱登录
```http
POST /api/auth/login-email
Content-Type: application/json

{
  "email": "<EMAIL>",
  "password": "password123"
}

Response:
{
  "success": true,
  "data": {
    "user_uuid": "user_1234567890_abcdef",
    "display_name": "用户显示名",
    "auth_type": "email",
    "token": "jwt_token_here",
    "expires_at": "2025-05-26T10:00:00Z"
  },
  "message": "登录成功"
}
```

### 获取用户信息
```http
GET /api/auth/profile
Authorization: Bearer jwt_token_here

Response:
{
  "success": true,
  "data": {
    "uuid": "anon_sha256_hash_uuid",
    "auth_type": "anonymous",
    "anonymous_uuid": "uuid_generated_from_ab",
    "display_name": "用户显示名",
    "education_level": "本科",
    "industry_display": "互联网/IT",
    "stories_count": 5,
    "questionnaire_count": 3,
    "is_anonymous": true,
    "created_at": "2025-05-25T10:00:00Z"
  }
}
```

### 查询用户内容 (A+B身份验证)
```http
POST /api/auth/my-content
Content-Type: application/json

{
  "identity_a": "12345678901",
  "identity_b": "1234"
}

Response:
{
  "success": true,
  "data": {
    "user_info": {
      "uuid": "anon_sha256_hash_uuid",
      "anonymous_uuid": "uuid_generated_from_ab",
      "display_name": "用户显示名",
      "stories_count": 5,
      "questionnaire_count": 3
    },
    "stories": [
      {
        "id": "story_1234567890_abcdef",
        "title": "我的求职经历",
        "status": "approved",
        "views": 150,
        "likes": 25,
        "created_at": "2025-05-25T10:00:00Z"
      }
    ],
    "questionnaires": [
      {
        "id": "quest_1234567890_abcdef",
        "status": "submitted",
        "submitted_at": "2025-05-24T15:30:00Z"
      }
    ],
    "pending_reviews": [
      {
        "id": "story_9876543210_fedcba",
        "type": "story",
        "title": "待审核的故事",
        "status": "pending",
        "submitted_at": "2025-05-25T12:00:00Z"
      }
    ]
  }
}
```

## 📝 故事发布 API

### 创建故事
```http
POST /api/stories
Authorization: Bearer jwt_token_here
Content-Type: application/json

{
  "title": "我的求职经历分享",
  "content": "详细的故事内容...",
  "category": "就业经历",
  "subcategory": "技术岗位",
  "tags": ["前端开发", "求职经验", "面试技巧"],
  "is_anonymous": false,
  "education_level": "本科",
  "industry_code": "technology",
  "graduation_year": 2023,
  "work_experience_years": 2,
  "location": "北京"
}

Response:
{
  "success": true,
  "data": {
    "story_id": "story_1234567890_abcdef",
    "status": "pending",
    "estimated_review_time": "24小时内"
  },
  "message": "故事提交成功，等待审核"
}
```

### 获取故事列表 (支持多维度搜索)
```http
GET /api/stories?page=1&pageSize=10&category=就业经历&education_level=本科&industry=technology&tags=前端开发&sort=trending

Response:
{
  "success": true,
  "data": {
    "stories": [
      {
        "id": "story_1234567890_abcdef",
        "title": "我的求职经历分享",
        "summary": "故事摘要...",
        "category": "就业经历",
        "education_level_display": "本科",
        "industry_display": "互联网/IT",
        "tags": [
          {"id": "tag_001", "name": "前端开发", "color": "#3B82F6"},
          {"id": "tag_002", "name": "求职经验", "color": "#10B981"}
        ],
        "author": {
          "display_name": "用户显示名",
          "avatar_url": "https://...",
          "is_anonymous": false
        },
        "stats": {
          "views": 150,
          "likes": 25,
          "comments_count": 8
        },
        "published_at": "2025-05-25T10:00:00Z"
      }
    ],
    "pagination": {
      "page": 1,
      "pageSize": 10,
      "total": 100,
      "totalPages": 10
    },
    "filters": {
      "categories": ["就业经历", "转行经历", "创业经历"],
      "education_levels": ["本科", "硕士", "博士"],
      "industries": ["互联网/IT", "金融", "教育"],
      "popular_tags": ["前端开发", "求职经验", "面试技巧"]
    }
  }
}
```

### 获取故事详情
```http
GET /api/stories/{story_id}

Response:
{
  "success": true,
  "data": {
    "id": "story_1234567890_abcdef",
    "title": "我的求职经历分享",
    "content": "完整的故事内容...",
    "category": "就业经历",
    "subcategory": "技术岗位",
    "education_level_display": "本科",
    "industry_display": "互联网/IT",
    "graduation_year": 2023,
    "work_experience_years": 2,
    "location": "北京",
    "tags": [
      {"id": "tag_001", "name": "前端开发", "color": "#3B82F6"},
      {"id": "tag_002", "name": "求职经验", "color": "#10B981"}
    ],
    "author": {
      "display_name": "用户显示名",
      "avatar_url": "https://...",
      "education_level": "本科",
      "industry_display": "互联网/IT",
      "is_anonymous": false
    },
    "stats": {
      "views": 150,
      "likes": 25,
      "dislikes": 2,
      "shares": 10,
      "comments_count": 8,
      "bookmarks_count": 15
    },
    "quality_score": 4.5,
    "reading_time": 5,
    "word_count": 800,
    "published_at": "2025-05-25T10:00:00Z",
    "updated_at": "2025-05-25T10:00:00Z"
  }
}
```

## 🏷️ 标签管理 API

### 获取标签列表
```http
GET /api/tags?category=industry&featured=true

Response:
{
  "success": true,
  "data": {
    "tags": [
      {
        "id": "tag_001",
        "name": "前端开发",
        "display_name": "前端开发",
        "category": "skill",
        "color": "#3B82F6",
        "usage_count": 150,
        "is_featured": true
      }
    ],
    "categories": ["industry", "skill", "topic", "location"]
  }
}
```

### 标签搜索建议
```http
GET /api/tags/suggestions?q=前端&limit=10

Response:
{
  "success": true,
  "data": [
    {"id": "tag_001", "name": "前端开发", "usage_count": 150},
    {"id": "tag_002", "name": "前端框架", "usage_count": 80}
  ]
}
```

## 📊 搜索和筛选 API

### 高级搜索
```http
POST /api/stories/search
Content-Type: application/json

{
  "query": "前端开发求职",
  "filters": {
    "categories": ["就业经历"],
    "education_levels": ["本科", "硕士"],
    "industries": ["technology"],
    "tags": ["前端开发", "求职经验"],
    "graduation_year_range": [2020, 2024],
    "work_experience_range": [0, 5],
    "locations": ["北京", "上海"]
  },
  "sort": "trending",
  "page": 1,
  "pageSize": 20
}

Response: (同获取故事列表)
```

## 💡 设计亮点

### 1. 多维度搜索支持
- **分类维度**: 主分类、子分类、故事类型
- **用户背景**: 学历、行业、毕业年份、工作经验
- **地理维度**: 地区、城市
- **标签维度**: 多标签组合搜索
- **时间维度**: 发布时间、更新时间

### 2. 用户认证与权限
- **JWT令牌认证**: 安全的用户身份验证
- **UUID绑定**: 故事与用户UUID强绑定
- **邮箱验证**: 确保用户身份真实性
- **会话管理**: 支持多设备登录管理

### 3. 标签系统设计
- **分层标签**: 支持父子标签关系
- **标签分类**: 按行业、技能、话题等分类
- **智能推荐**: 基于使用频率的标签建议
- **相关度评分**: 标签与故事的相关度量化

### 4. 性能优化
- **索引设计**: 针对多维度查询优化的索引
- **视图简化**: 预计算的统计视图
- **缓存策略**: 热门内容和标签缓存
- **分页查询**: 高效的分页实现
