/**
 * 日志服务
 */

import { LoggingConfig } from '../types';

/**
 * 日志级别枚举
 */
enum LogLevel {
  ERROR = 0,
  WARN = 1,
  INFO = 2,
  DEBUG = 3
}

/**
 * 日志条目接口
 */
interface LogEntry {
  timestamp: number;
  level: 'error' | 'warn' | 'info' | 'debug';
  message: string;
  data?: any;
}

/**
 * 日志服务类
 */
export class LoggingService {
  private config: LoggingConfig;
  private logs: LogEntry[] = [];
  private maxLogs: number = 100;

  /**
   * 构造函数
   */
  constructor() {
    this.config = {
      enabled: true,
      logLevel: 'error',
      storeSubmissionData: false
    };
  }

  /**
   * 初始化服务
   * @param config 日志配置
   */
  public initialize(config: LoggingConfig): void {
    this.config = { ...config };
  }

  /**
   * 更新配置
   * @param config 日志配置
   */
  public updateConfig(config: LoggingConfig): void {
    this.config = { ...this.config, ...config };
  }

  /**
   * 记录日志
   * @param level 日志级别
   * @param message 日志消息
   * @param data 日志数据
   */
  public log(level: 'error' | 'warn' | 'info' | 'debug', message: string, data?: any): void {
    if (!this.config.enabled) {
      return;
    }
    
    // 检查日志级别
    if (!this.shouldLog(level)) {
      return;
    }
    
    // 创建日志条目
    const logEntry: LogEntry = {
      timestamp: Date.now(),
      level,
      message,
      data: this.sanitizeData(data)
    };
    
    // 添加到日志数组
    this.logs.push(logEntry);
    
    // 如果超过最大日志数，移除最旧的日志
    if (this.logs.length > this.maxLogs) {
      this.logs.shift();
    }
    
    // 输出到控制台
    this.logToConsole(logEntry);
    
    // 存储到本地存储
    this.storeLog(logEntry);
  }

  /**
   * 获取所有日志
   */
  public getLogs(): LogEntry[] {
    return [...this.logs];
  }

  /**
   * 清除日志
   */
  public clearLogs(): void {
    this.logs = [];
    localStorage.removeItem('security_logs');
  }

  /**
   * 检查是否应该记录该级别的日志
   * @param level 日志级别
   */
  private shouldLog(level: 'error' | 'warn' | 'info' | 'debug'): boolean {
    const configLevel = this.getLogLevelValue(this.config.logLevel);
    const messageLevel = this.getLogLevelValue(level);
    
    return messageLevel <= configLevel;
  }

  /**
   * 获取日志级别值
   * @param level 日志级别
   */
  private getLogLevelValue(level: 'error' | 'warn' | 'info' | 'debug'): number {
    switch (level) {
      case 'error': return LogLevel.ERROR;
      case 'warn': return LogLevel.WARN;
      case 'info': return LogLevel.INFO;
      case 'debug': return LogLevel.DEBUG;
      default: return LogLevel.ERROR;
    }
  }

  /**
   * 清理数据
   * @param data 日志数据
   */
  private sanitizeData(data: any): any {
    if (!data) {
      return undefined;
    }
    
    // 如果不存储提交数据，移除敏感信息
    if (!this.config.storeSubmissionData) {
      // 创建数据的副本
      const sanitized = { ...data };
      
      // 移除可能的敏感字段
      const sensitiveFields = ['password', 'token', 'secret', 'key', 'auth', 'credentials'];
      
      // 递归清理对象
      const cleanObject = (obj: any) => {
        if (!obj || typeof obj !== 'object') {
          return;
        }
        
        for (const key in obj) {
          // 检查是否是敏感字段
          if (sensitiveFields.some(field => key.toLowerCase().includes(field))) {
            obj[key] = '[REDACTED]';
          } else if (typeof obj[key] === 'object') {
            // 递归清理嵌套对象
            cleanObject(obj[key]);
          }
        }
      };
      
      cleanObject(sanitized);
      
      return sanitized;
    }
    
    return data;
  }

  /**
   * 输出日志到控制台
   * @param logEntry 日志条目
   */
  private logToConsole(logEntry: LogEntry): void {
    const timestamp = new Date(logEntry.timestamp).toISOString();
    const prefix = `[Security ${logEntry.level.toUpperCase()}] ${timestamp}:`;
    
    switch (logEntry.level) {
      case 'error':
        console.error(prefix, logEntry.message, logEntry.data);
        break;
      case 'warn':
        console.warn(prefix, logEntry.message, logEntry.data);
        break;
      case 'info':
        console.info(prefix, logEntry.message, logEntry.data);
        break;
      case 'debug':
        console.debug(prefix, logEntry.message, logEntry.data);
        break;
    }
  }

  /**
   * 存储日志到本地存储
   * @param logEntry 日志条目
   */
  private storeLog(logEntry: LogEntry): void {
    // 只存储错误和警告日志
    if (logEntry.level !== 'error' && logEntry.level !== 'warn') {
      return;
    }
    
    try {
      // 获取现有日志
      const storedLogs = localStorage.getItem('security_logs');
      let logs: LogEntry[] = storedLogs ? JSON.parse(storedLogs) : [];
      
      // 添加新日志
      logs.push(logEntry);
      
      // 限制存储的日志数量
      if (logs.length > this.maxLogs) {
        logs = logs.slice(-this.maxLogs);
      }
      
      // 存储日志
      localStorage.setItem('security_logs', JSON.stringify(logs));
    } catch (error) {
      console.error('Failed to store security log:', error);
    }
  }
}
