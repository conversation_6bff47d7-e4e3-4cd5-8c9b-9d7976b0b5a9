/**
 * 数据迁移计划
 * 从当前数据结构迁移到优化后的数据结构
 */

const { PrismaClient } = require('@prisma/client');
const { v4: uuidv4 } = require('uuid');

const prisma = new PrismaClient();

/**
 * 数据迁移主函数
 */
async function migrateData() {
  console.log('🚀 开始数据迁移...');
  
  try {
    // 1. 备份当前数据
    await backupCurrentData();
    
    // 2. 创建新的数据结构
    await createOptimizedSchema();
    
    // 3. 迁移用户数据
    await migrateUsers();
    
    // 4. 迁移问卷数据
    await migrateQuestionnaireResponses();
    
    // 5. 迁移心声数据
    await migrateQuestionnaireVoices();
    
    // 6. 迁移故事数据
    await migrateStories();
    
    // 7. 创建测试管理员账户
    await createTestAdminAccounts();
    
    // 8. 验证数据完整性
    await verifyDataIntegrity();
    
    console.log('✅ 数据迁移完成！');
    
  } catch (error) {
    console.error('❌ 数据迁移失败:', error);
    throw error;
  }
}

/**
 * 备份当前数据
 */
async function backupCurrentData() {
  console.log('📦 备份当前数据...');
  
  const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
  const backupFile = `backup-${timestamp}.json`;
  
  // 导出当前所有数据
  const backup = {
    questionnaireResponses: await prisma.questionnaireResponse.findMany(),
    stories: await prisma.story.findMany(),
    users: await prisma.user.findMany(),
    votes: await prisma.vote.findMany()
  };
  
  // 保存到文件
  const fs = require('fs');
  fs.writeFileSync(`./backups/${backupFile}`, JSON.stringify(backup, null, 2));
  
  console.log(`✅ 数据已备份到: ${backupFile}`);
}

/**
 * 迁移用户数据
 */
async function migrateUsers() {
  console.log('👥 迁移用户数据...');
  
  // 获取所有现有用户
  const existingUsers = await prisma.user.findMany();
  
  for (const user of existingUsers) {
    // 为现有用户生成UUID
    const newUserId = uuidv4();
    
    // 确定用户类型
    let userType = 'anonymous';
    let isAnonymousRegistered = false;
    
    if (user.email && user.emailVerified) {
      userType = 'registered';
      isAnonymousRegistered = true;
    }
    
    // 创建新的用户记录
    await prisma.user.upsert({
      where: { id: newUserId },
      update: {},
      create: {
        id: newUserId,
        email: user.email,
        emailVerified: user.emailVerified,
        userType: userType,
        role: user.role,
        username: user.username,
        displayName: user.name,
        passwordHash: user.passwordHash,
        anonymousId: user.email ? `anon_${newUserId.slice(0, 8)}` : null,
        isAnonymousRegistered: isAnonymousRegistered,
        ipAddress: user.ipAddress,
        userAgent: user.userAgent,
        lastLoginAt: user.lastLoginAt,
        createdAt: user.createdAt,
        updatedAt: user.updatedAt,
        isTestData: user.isTestData,
        testDataVersion: user.testDataVersion,
        testDataSet: user.testDataSet
      }
    });
    
    // 记录ID映射关系
    await prisma.systemConfig.upsert({
      where: { key: `user_id_mapping_${user.id}` },
      update: { value: newUserId },
      create: {
        key: `user_id_mapping_${user.id}`,
        value: newUserId,
        description: '用户ID映射关系',
        category: 'migration'
      }
    });
  }
  
  console.log(`✅ 已迁移 ${existingUsers.length} 个用户`);
}

/**
 * 迁移问卷回复数据
 */
async function migrateQuestionnaireResponses() {
  console.log('📋 迁移问卷回复数据...');
  
  const responses = await prisma.questionnaireResponse.findMany();
  
  for (const response of responses) {
    const newResponseId = uuidv4();
    const submissionSessionId = uuidv4();
    
    // 获取用户ID映射
    let newUserId = null;
    if (response.userId) {
      const mapping = await prisma.systemConfig.findUnique({
        where: { key: `user_id_mapping_${response.userId}` }
      });
      newUserId = mapping?.value;
    }
    
    // 创建新的问卷回复记录
    await prisma.questionnaireResponse.create({
      data: {
        id: newResponseId,
        userId: newUserId,
        sequenceNumber: response.sequenceNumber,
        submissionSessionId: submissionSessionId,
        ipAddress: response.ipAddress,
        isAnonymous: response.isAnonymous,
        
        // 问卷内容
        educationLevel: response.educationLevel,
        major: response.major,
        graduationYear: response.graduationYear,
        region: response.region,
        expectedPosition: response.expectedPosition,
        expectedSalaryRange: response.expectedSalaryRange,
        expectedWorkHours: response.expectedWorkHours,
        expectedVacationDays: response.expectedVacationDays,
        employmentStatus: response.employmentStatus,
        currentIndustry: response.currentIndustry,
        currentPosition: response.currentPosition,
        monthlySalary: response.monthlySalary,
        jobSatisfaction: response.jobSatisfaction,
        unemploymentDuration: response.unemploymentDuration,
        unemploymentReason: response.unemploymentReason,
        jobHuntingDifficulty: response.jobHuntingDifficulty,
        regretMajor: response.regretMajor,
        preferredMajor: response.preferredMajor,
        careerChangeIntention: response.careerChangeIntention,
        careerChangeTarget: response.careerChangeTarget,
        
        status: response.status,
        tags: response.tags,
        createdAt: response.createdAt,
        updatedAt: response.updatedAt,
        isTestData: response.isTestData,
        testDataVersion: response.testDataVersion,
        testDataSet: response.testDataSet
      }
    });
    
    // 记录映射关系
    await prisma.systemConfig.create({
      data: {
        key: `questionnaire_mapping_${response.id}`,
        value: JSON.stringify({
          oldId: response.id,
          newId: newResponseId,
          submissionSessionId: submissionSessionId
        }),
        description: '问卷回复ID映射关系',
        category: 'migration'
      }
    });
  }
  
  console.log(`✅ 已迁移 ${responses.length} 个问卷回复`);
}

/**
 * 迁移问卷心声数据
 */
async function migrateQuestionnaireVoices() {
  console.log('💭 迁移问卷心声数据...');
  
  // 从问卷回复中提取心声数据
  const responses = await prisma.questionnaireResponse.findMany({
    where: {
      OR: [
        { adviceForStudents: { not: null } },
        { observationOnEmployment: { not: null } }
      ]
    }
  });
  
  let voiceCount = 0;
  
  for (const response of responses) {
    // 获取映射信息
    const mapping = await prisma.systemConfig.findUnique({
      where: { key: `questionnaire_mapping_${response.id}` }
    });
    
    if (!mapping) {continue;}
    
    const mappingData = JSON.parse(mapping.value);
    
    // 创建建议心声
    if (response.adviceForStudents && response.adviceForStudents.trim()) {
      await prisma.questionnaireVoice.create({
        data: {
          id: uuidv4(),
          questionnaireId: mappingData.newId,
          userId: response.userId,
          submissionSessionId: mappingData.submissionSessionId,
          voiceType: 'advice',
          title: '给学弟学妹的建议',
          content: response.adviceForStudents,
          status: 'approved', // 现有数据默认为已审核
          isPublic: true,
          isAnonymous: response.isAnonymous,
          ipAddress: response.ipAddress,
          createdAt: response.createdAt,
          updatedAt: response.updatedAt,
          isTestData: response.isTestData,
          testDataVersion: response.testDataVersion,
          testDataSet: response.testDataSet
        }
      });
      voiceCount++;
    }
    
    // 创建观察心声
    if (response.observationOnEmployment && response.observationOnEmployment.trim()) {
      await prisma.questionnaireVoice.create({
        data: {
          id: uuidv4(),
          questionnaireId: mappingData.newId,
          userId: response.userId,
          submissionSessionId: mappingData.submissionSessionId,
          voiceType: 'observation',
          title: '对就业形势的观察',
          content: response.observationOnEmployment,
          status: 'approved',
          isPublic: true,
          isAnonymous: response.isAnonymous,
          ipAddress: response.ipAddress,
          createdAt: response.createdAt,
          updatedAt: response.updatedAt,
          isTestData: response.isTestData,
          testDataVersion: response.testDataVersion,
          testDataSet: response.testDataSet
        }
      });
      voiceCount++;
    }
  }
  
  console.log(`✅ 已迁移 ${voiceCount} 条问卷心声`);
}

/**
 * 迁移故事数据
 */
async function migrateStories() {
  console.log('📖 迁移故事数据...');
  
  const stories = await prisma.story.findMany();
  
  for (const story of stories) {
    const newStoryId = uuidv4();
    
    // 获取用户ID映射
    let newUserId = null;
    if (story.userId) {
      const mapping = await prisma.systemConfig.findUnique({
        where: { key: `user_id_mapping_${story.userId}` }
      });
      newUserId = mapping?.value;
    }
    
    // 创建新的故事记录
    await prisma.story.create({
      data: {
        id: newStoryId,
        userId: newUserId,
        title: story.title,
        content: story.content,
        category: story.category,
        tags: story.tags,
        author: story.author,
        isAnonymous: story.isAnonymous,
        educationLevel: story.educationLevel,
        industry: story.industry,
        likes: story.likes,
        dislikes: story.dislikes,
        status: story.status,
        ipAddress: story.ipAddress,
        userAgent: story.userAgent,
        createdAt: story.createdAt,
        updatedAt: story.updatedAt,
        isTestData: story.isTestData,
        testDataVersion: story.testDataVersion,
        testDataSet: story.testDataSet
      }
    });
    
    // 记录映射关系
    await prisma.systemConfig.create({
      data: {
        key: `story_mapping_${story.id}`,
        value: JSON.stringify({
          oldId: story.id,
          newId: newStoryId
        }),
        description: '故事ID映射关系',
        category: 'migration'
      }
    });
  }
  
  console.log(`✅ 已迁移 ${stories.length} 个故事`);
}

/**
 * 创建测试管理员账户
 */
async function createTestAdminAccounts() {
  console.log('👑 创建测试管理员账户...');
  
  // 创建超级管理员
  const superAdminId = uuidv4();
  await prisma.user.create({
    data: {
      id: superAdminId,
      email: '<EMAIL>',
      emailVerified: true,
      userType: 'registered',
      role: 'superadmin',
      username: 'superadmin',
      displayName: '超级管理员',
      passwordHash: '$2b$10$example', // 实际应用中需要正确的密码哈希
      isActive: true,
      isTestData: true,
      testDataVersion: '1.0',
      testDataSet: 'admin_accounts'
    }
  });
  
  // 创建管理员
  const adminId = uuidv4();
  await prisma.user.create({
    data: {
      id: adminId,
      email: '<EMAIL>',
      emailVerified: true,
      userType: 'registered',
      role: 'admin',
      username: 'admin',
      displayName: '管理员',
      passwordHash: '$2b$10$example',
      isActive: true,
      createdBy: superAdminId,
      isTestData: true,
      testDataVersion: '1.0',
      testDataSet: 'admin_accounts'
    }
  });
  
  // 创建审核员
  for (let i = 1; i <= 3; i++) {
    const reviewerId = uuidv4();
    await prisma.user.create({
      data: {
        id: reviewerId,
        email: `reviewer${i}@test.com`,
        emailVerified: true,
        userType: 'registered',
        role: 'reviewer',
        username: `reviewer${i}`,
        displayName: `审核员${i}`,
        passwordHash: '$2b$10$example',
        isActive: true,
        createdBy: adminId,
        isTestData: true,
        testDataVersion: '1.0',
        testDataSet: 'admin_accounts'
      }
    });
  }
  
  console.log('✅ 已创建测试管理员账户');
}

/**
 * 验证数据完整性
 */
async function verifyDataIntegrity() {
  console.log('🔍 验证数据完整性...');
  
  const counts = {
    users: await prisma.user.count(),
    questionnaireResponses: await prisma.questionnaireResponse.count(),
    questionnaireVoices: await prisma.questionnaireVoice.count(),
    stories: await prisma.story.count()
  };
  
  console.log('📊 迁移后数据统计:');
  console.log(`- 用户: ${counts.users}`);
  console.log(`- 问卷回复: ${counts.questionnaireResponses}`);
  console.log(`- 问卷心声: ${counts.questionnaireVoices}`);
  console.log(`- 故事: ${counts.stories}`);
  
  // 验证关联关系
  const orphanedVoices = await prisma.questionnaireVoice.count({
    where: {
      questionnaire: null
    }
  });
  
  if (orphanedVoices > 0) {
    console.warn(`⚠️  发现 ${orphanedVoices} 条孤立的心声记录`);
  }
  
  console.log('✅ 数据完整性验证完成');
}

// 执行迁移
if (require.main === module) {
  migrateData()
    .catch(console.error)
    .finally(() => prisma.$disconnect());
}

module.exports = {
  migrateData,
  backupCurrentData,
  migrateUsers,
  migrateQuestionnaireResponses,
  migrateQuestionnaireVoices,
  migrateStories,
  createTestAdminAccounts,
  verifyDataIntegrity
};
