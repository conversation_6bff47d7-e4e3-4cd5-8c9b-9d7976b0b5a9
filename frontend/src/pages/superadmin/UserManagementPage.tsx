import React, { useState, useEffect } from 'react';
import SuperAdminLayout from '@/components/layouts/SuperAdminLayout';
import { Card, CardContent, CardHeader, CardTitle, CardDescription, CardFooter } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { Label } from '@/components/ui/label';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Checkbox } from '@/components/ui/checkbox';
import { Badge } from '@/components/ui/badge';
import { useToast } from '@/components/ui/use-toast';
import {
  Search, RefreshCw, UserCheck, UserX, Trash2,
  Eye, FileText, UserPlus as UserPlusIcon, Key, Shield, Filter, Users
} from 'lucide-react';
import {
  Table, TableBody, TableCaption, TableCell, TableHead,
  TableHeader, TableRow
} from '@/components/ui/table';
import {
  Pagination, PaginationContent, PaginationItem,
  PaginationLink, PaginationNext, PaginationPrevious
} from '@/components/ui/pagination-new';
import {
  Dialog, DialogContent, DialogDescription, DialogFooter,
  DialogHeader, DialogTitle, DialogTrigger
} from '@/components/ui/dialog';
import {
  DropdownMenu, DropdownMenuContent, DropdownMenuItem,
  DropdownMenuLabel, DropdownMenuSeparator, DropdownMenuTrigger
} from '@/components/ui/dropdown-menu';

// 用户类型
interface User {
  id: string;
  uuid?: string;
  username: string;
  email: string;
  role: 'user' | 'reviewer' | 'admin' | 'superadmin';
  status: 'active' | 'inactive' | 'banned';
  createdAt: string;
  lastLoginAt?: string;
  lastLoginIp?: string;
  submissionCount?: number;
  storyCount?: number;
  questionnaireCount?: number;
}

// 用户内容类型
interface UserContent {
  id: string;
  type: 'story' | 'questionnaire' | 'comment';
  title?: string;
  content: string;
  status: 'pending' | 'approved' | 'rejected';
  createdAt: string;
  reviewedAt?: string;
  reviewerId?: string;
  tags?: string[];
}

// 用户搜索参数
interface UserSearchParams {
  uuid?: string;
  email?: string;
  username?: string;
  role?: string;
  status?: string;
  page?: number;
  limit?: number;
}

/**
 * 超级管理员用户管理页面
 *
 * 管理所有用户，包括审核员、管理员等角色的用户
 */
const SuperAdminUserManagementPage: React.FC = () => {
  const { toast } = useToast();
  const [activeTab, setActiveTab] = useState('all');
  const [isLoading, setIsLoading] = useState(false);
  const [searchParams, setSearchParams] = useState<UserSearchParams>({
    page: 1,
    limit: 10
  });
  const [users, setUsers] = useState<User[]>([]);
  const [totalUsers, setTotalUsers] = useState(0);
  const [totalPages, setTotalPages] = useState(1);
  const [selectedUsers, setSelectedUsers] = useState<string[]>([]);
  const [selectedUser, setSelectedUser] = useState<User | null>(null);
  const [userContents, setUserContents] = useState<UserContent[]>([]);
  const [isUserContentLoading, setIsUserContentLoading] = useState(false);
  const [isUserDialogOpen, setIsUserDialogOpen] = useState(false);
  const [isRoleDialogOpen, setIsRoleDialogOpen] = useState(false);
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);
  const [isCreateUserDialogOpen, setIsCreateUserDialogOpen] = useState(false);
  const [newRole, setNewRole] = useState<string>('');
  const [isCreatingUser, setIsCreatingUser] = useState(false);

  // 新用户表单数据
  const [newUserForm, setNewUserForm] = useState({
    username: '',
    email: '',
    password: '',
    confirmPassword: '',
    role: 'user' as 'user' | 'reviewer' | 'admin' | 'superadmin',
    status: 'active' as 'active' | 'inactive',
    sendWelcomeEmail: true
  });

  // 模拟用户数据
  const mockUsers: User[] = [
    {
      id: '1',
      uuid: 'usr_123456789',
      username: 'zhangsan',
      email: '<EMAIL>',
      role: 'user',
      status: 'active',
      createdAt: '2023-01-15T08:30:00Z',
      lastLoginAt: '2023-05-20T14:22:10Z',
      lastLoginIp: '*************',
      submissionCount: 5,
      storyCount: 3,
      questionnaireCount: 2
    },
    {
      id: '2',
      uuid: 'usr_987654321',
      username: 'lisi',
      email: '<EMAIL>',
      role: 'user',
      status: 'active',
      createdAt: '2023-02-10T10:15:00Z',
      lastLoginAt: '2023-05-18T09:45:30Z',
      lastLoginIp: '*************',
      submissionCount: 8,
      storyCount: 5,
      questionnaireCount: 3
    },
    {
      id: '3',
      uuid: 'usr_456789123',
      username: 'wangwu',
      email: '<EMAIL>',
      role: 'user',
      status: 'inactive',
      createdAt: '2023-03-05T14:20:00Z',
      lastLoginAt: '2023-04-10T16:30:00Z',
      lastLoginIp: '*************',
      submissionCount: 2,
      storyCount: 1,
      questionnaireCount: 1
    },
    {
      id: '4',
      uuid: 'usr_789123456',
      username: 'zhaoliu',
      email: '<EMAIL>',
      role: 'user',
      status: 'banned',
      createdAt: '2023-01-20T11:45:00Z',
      lastLoginAt: '2023-02-15T13:10:20Z',
      lastLoginIp: '*************',
      submissionCount: 3,
      storyCount: 2,
      questionnaireCount: 1
    },
    {
      id: '5',
      uuid: 'usr_321654987',
      username: 'reviewer1',
      email: '<EMAIL>',
      role: 'reviewer',
      status: 'active',
      createdAt: '2023-01-05T08:30:00Z',
      lastLoginAt: '2023-05-22T11:30:45Z',
      lastLoginIp: '*************',
      submissionCount: 0,
      storyCount: 0,
      questionnaireCount: 0
    },
    {
      id: '6',
      uuid: 'usr_654987321',
      username: 'admin1',
      email: '<EMAIL>',
      role: 'admin',
      status: 'active',
      createdAt: '2023-01-01T10:00:00Z',
      lastLoginAt: '2023-05-22T14:15:30Z',
      lastLoginIp: '*************',
      submissionCount: 0,
      storyCount: 0,
      questionnaireCount: 0
    },
    {
      id: '7',
      uuid: 'usr_987321654',
      username: 'superadmin',
      email: '<EMAIL>',
      role: 'superadmin',
      status: 'active',
      createdAt: '2023-01-01T09:00:00Z',
      lastLoginAt: '2023-05-22T15:45:20Z',
      lastLoginIp: '*************',
      submissionCount: 0,
      storyCount: 0,
      questionnaireCount: 0
    }
  ];

  // 模拟用户内容数据
  const mockUserContents: Record<string, UserContent[]> = {
    'usr_123456789': [
      {
        id: '101',
        type: 'story',
        title: '我的大学经历',
        content: '大学四年，我参加了很多社团活动，收获了宝贵的经验...',
        status: 'approved',
        createdAt: '2023-03-10T14:30:00Z',
        reviewedAt: '2023-03-12T10:15:00Z',
        reviewerId: 'reviewer-1',
        tags: ['大学生活', '社团活动']
      },
      {
        id: '102',
        type: 'questionnaire',
        content: '{"question1":"回答1","question2":"回答2","question3":"回答3"}',
        status: 'approved',
        createdAt: '2023-04-05T09:20:00Z',
        reviewedAt: '2023-04-06T11:30:00Z',
        reviewerId: 'reviewer-2'
      }
    ],
    'usr_987654321': [
      {
        id: '201',
        type: 'story',
        title: '实习经历分享',
        content: '在某科技公司实习的三个月里，我学到了很多实用技能...',
        status: 'approved',
        createdAt: '2023-03-15T16:45:00Z',
        reviewedAt: '2023-03-17T14:20:00Z',
        reviewerId: 'reviewer-1',
        tags: ['实习', '职场经验']
      },
      {
        id: '202',
        type: 'comment',
        content: '这个故事很有启发性，谢谢分享！',
        status: 'approved',
        createdAt: '2023-04-10T13:25:00Z',
        reviewedAt: '2023-04-10T15:40:00Z',
        reviewerId: 'reviewer-3'
      }
    ]
  };

  // 加载用户数据
  useEffect(() => {
    console.log('🔄 useEffect触发，加载用户数据', { searchParams, activeTab });
    fetchUsers();
  }, [activeTab]); // 移除searchParams依赖，避免无限循环

  // 实时搜索防抖
  useEffect(() => {
    const timeoutId = setTimeout(() => {
      console.log('🔍 搜索防抖触发', { searchParams });
      fetchUsers();
    }, 500); // 500ms防抖

    return () => clearTimeout(timeoutId);
  }, [searchParams.username, searchParams.email, searchParams.uuid, searchParams.role, searchParams.status]);

  // 获取用户列表
  const fetchUsers = async () => {
    try {
      setIsLoading(true);

      // 构建查询参数
      const params = new URLSearchParams();
      params.append('page', String(searchParams.page || 1));
      params.append('pageSize', String(searchParams.limit || 10));

      // 合并搜索条件为单一搜索参数
      const searchTerms = [
        searchParams.uuid,
        searchParams.email,
        searchParams.username
      ].filter(Boolean);

      if (searchTerms.length > 0) {
        // 使用第一个非空搜索词
        params.append('search', searchTerms[0]);
      }
      // 只显示普通用户
      params.append('role', 'user');

      // 使用环境变量或默认的线上API地址
      const baseUrl = import.meta.env.VITE_API_BASE_URL || 'https://college-employment-survey.aibook2099.workers.dev';
      const apiUrl = `${baseUrl}/api/admin/users?${params}`;
      console.log('🔍 正在调用用户API:', apiUrl);

      // 调用真实API
      const response = await fetch(apiUrl);
      console.log('📡 API响应状态:', response.status);

      if (!response.ok) {
        throw new Error(`API调用失败: ${response.status}`);
      }

      const data = await response.json();
      console.log('📊 API返回数据:', data);

      if (data.success && data.data) {
        const users = data.data.users || [];
        const total = data.data.pagination?.total || 0;
        const totalPages = data.data.pagination?.totalPages || 1;

        setUsers(users);
        setTotalUsers(total);
        setTotalPages(totalPages);
        console.log('✅ 用户数据加载成功:', { total, count: users.length });
      } else {
        console.warn('⚠️ API返回数据格式异常:', data);
        setUsers([]);
        setTotalUsers(0);
        setTotalPages(1);
        throw new Error(data.message || '获取用户失败');
      }
    } catch (error) {
      console.error('❌ 获取用户失败:', error);
      toast({
        title: '获取用户失败',
        description: error instanceof Error ? error.message : '发生错误，请稍后重试',
        variant: 'destructive',
      });
    } finally {
      setIsLoading(false);
    }
  };

  // 获取用户内容
  const fetchUserContents = async (uuid: string) => {
    try {
      setIsUserContentLoading(true);

      // 模拟网络延迟
      await new Promise(resolve => setTimeout(resolve, 500));

      // 获取用户内容
      const contents = mockUserContents[uuid] || [];

      setUserContents(contents);
      setIsUserContentLoading(false);
    } catch (error) {
      console.error('获取用户内容失败:', error);
      toast({
        title: '获取用户内容失败',
        description: '发生错误，请稍后重试',
        variant: 'destructive',
      });
      setIsUserContentLoading(false);
    }
  };

  // 处理搜索参数变更
  const handleSearchParamChange = (key: keyof UserSearchParams, value: any) => {
    setSearchParams(prev => ({ ...prev, [key]: value, page: 1 }));
  };

  // 处理页码变更
  const handlePageChange = (page: number) => {
    setSearchParams(prev => ({ ...prev, page }));
  };

  // 处理用户选择
  const handleUserSelect = (id: string, checked: boolean) => {
    if (checked) {
      setSelectedUsers(prev => [...prev, id]);
    } else {
      setSelectedUsers(prev => prev.filter(userId => userId !== id));
    }
  };

  // 处理全选/取消全选
  const handleSelectAll = (checked: boolean) => {
    if (checked) {
      setSelectedUsers(users.map(user => user.id));
    } else {
      setSelectedUsers([]);
    }
  };

  // 查看用户详情
  const handleViewUser = (user: User) => {
    setSelectedUser(user);
    if (user.uuid) {
      fetchUserContents(user.uuid);
    }
    setIsUserDialogOpen(true);
  };

  // 更新用户角色
  const handleUpdateRole = async () => {
    if (!selectedUser || !newRole) return;

    try {
      const baseUrl = import.meta.env.VITE_API_BASE_URL || 'https://college-employment-survey.aibook2099.workers.dev';
      const apiUrl = `${baseUrl}/api/admin/users/${selectedUser.id}`;
      console.log('🔄 正在更新用户角色:', selectedUser.username, '→', newRole);

      // 调用真实API更新用户
      const response = await fetch(apiUrl, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          role: newRole
        })
      });

      console.log('📡 更新用户API响应状态:', response.status);

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || `API调用失败: ${response.status}`);
      }

      const data = await response.json();
      console.log('✅ 用户角色更新成功:', data);

      if (data.success) {
        toast({
          title: '角色更新成功',
          description: `用户 ${selectedUser.username} 的角色已更新为 ${newRole}`,
        });

        setIsRoleDialogOpen(false);
        fetchUsers();
      } else {
        throw new Error(data.message || '更新角色失败');
      }
    } catch (error) {
      console.error('❌ 更新角色失败:', error);
      toast({
        title: '更新角色失败',
        description: error instanceof Error ? error.message : '发生错误，请稍后重试',
        variant: 'destructive',
      });
    }
  };

  // 删除用户
  const handleDeleteUser = async () => {
    if (!selectedUser) return;

    try {
      const apiUrl = `http://localhost:8789/api/admin/users/${selectedUser.id}`;
      console.log('🗑️ 正在删除用户:', selectedUser.username);

      // 调用真实API删除用户
      const response = await fetch(apiUrl, {
        method: 'DELETE',
        headers: {
          'Content-Type': 'application/json',
        }
      });

      console.log('📡 删除用户API响应状态:', response.status);

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || `API调用失败: ${response.status}`);
      }

      const data = await response.json();
      console.log('✅ 用户删除成功:', data);

      if (data.success) {
        toast({
          title: '删除成功',
          description: `用户 ${selectedUser.username} 已删除`,
        });

        setIsDeleteDialogOpen(false);
        fetchUsers();
      } else {
        throw new Error(data.message || '删除用户失败');
      }
    } catch (error) {
      console.error('❌ 删除用户失败:', error);
      toast({
        title: '删除失败',
        description: error instanceof Error ? error.message : '发生错误，请稍后重试',
        variant: 'destructive',
      });
    }
  };

  // 重置搜索
  const resetSearch = () => {
    setSearchParams({
      page: 1,
      limit: 10
    });
  };

  // 处理新用户表单变更
  const handleNewUserFormChange = (field: string, value: any) => {
    setNewUserForm(prev => ({ ...prev, [field]: value }));
  };

  // 重置新用户表单
  const resetNewUserForm = () => {
    setNewUserForm({
      username: '',
      email: '',
      password: '',
      confirmPassword: '',
      role: 'user',
      status: 'active',
      sendWelcomeEmail: true
    });
  };

  // 验证新用户表单
  const validateNewUserForm = () => {
    const { username, email, password, confirmPassword } = newUserForm;

    if (!username.trim()) {
      toast({
        title: '验证失败',
        description: '请输入用户名',
        variant: 'destructive',
      });
      return false;
    }

    if (username.length < 3) {
      toast({
        title: '验证失败',
        description: '用户名至少需要3个字符',
        variant: 'destructive',
      });
      return false;
    }

    if (!email.trim()) {
      toast({
        title: '验证失败',
        description: '请输入邮箱地址',
        variant: 'destructive',
      });
      return false;
    }

    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(email)) {
      toast({
        title: '验证失败',
        description: '请输入有效的邮箱地址',
        variant: 'destructive',
      });
      return false;
    }

    if (!password.trim()) {
      toast({
        title: '验证失败',
        description: '请输入密码',
        variant: 'destructive',
      });
      return false;
    }

    if (password.length < 6) {
      toast({
        title: '验证失败',
        description: '密码至少需要6个字符',
        variant: 'destructive',
      });
      return false;
    }

    if (password !== confirmPassword) {
      toast({
        title: '验证失败',
        description: '两次输入的密码不一致',
        variant: 'destructive',
      });
      return false;
    }

    // 用户名和邮箱重复检查将在服务器端进行

    return true;
  };

  // 创建新用户
  const handleCreateUser = async () => {
    if (!validateNewUserForm()) return;

    try {
      setIsCreatingUser(true);

      const baseUrl = import.meta.env.VITE_API_BASE_URL || 'https://college-employment-survey.aibook2099.workers.dev';
      const apiUrl = `${baseUrl}/api/admin/users`;
      console.log('🔄 正在创建用户:', newUserForm.username);

      // 调用真实API创建用户
      const response = await fetch(apiUrl, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          username: newUserForm.username,
          email: newUserForm.email,
          name: newUserForm.username, // 使用用户名作为显示名称
          role: newUserForm.role,
          password: newUserForm.password
        })
      });

      console.log('📡 创建用户API响应状态:', response.status);

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || `API调用失败: ${response.status}`);
      }

      const data = await response.json();
      console.log('✅ 用户创建成功:', data);

      if (data.success) {
        toast({
          title: '用户创建成功',
          description: `用户 ${newUserForm.username} 已成功创建`,
        });

        if (newUserForm.sendWelcomeEmail) {
          toast({
            title: '欢迎邮件已发送',
            description: `欢迎邮件已发送到 ${newUserForm.email}`,
          });
        }

        setIsCreateUserDialogOpen(false);
        resetNewUserForm();
        fetchUsers(); // 刷新用户列表
      } else {
        throw new Error(data.message || '创建用户失败');
      }
    } catch (error) {
      console.error('❌ 创建用户失败:', error);
      toast({
        title: '创建用户失败',
        description: error instanceof Error ? error.message : '发生错误，请稍后重试',
        variant: 'destructive',
      });
    } finally {
      setIsCreatingUser(false);
    }
  };

  return (
    <SuperAdminLayout>
        <div className="container mx-auto py-6">
          <h1 className="text-2xl font-bold mb-6">普通用户管理</h1>

        <div className="space-y-4">

          <Card>
            <CardHeader>
              <CardTitle>搜索用户</CardTitle>
              <CardDescription>
                通过UUID、邮箱或用户名搜索用户
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="search">搜索关键词</Label>
                  <Input
                    id="search"
                    placeholder="输入用户名、邮箱或UUID进行模糊搜索"
                    value={searchParams.username || searchParams.email || searchParams.uuid || ''}
                    onChange={(e) => {
                      const value = e.target.value;
                      setSearchParams(prev => ({
                        ...prev,
                        username: value,
                        email: '',
                        uuid: '',
                        page: 1
                      }));
                    }}
                  />
                  <p className="text-sm text-muted-foreground">
                    支持模糊搜索，会同时匹配用户名、邮箱和UUID
                  </p>
                </div>


              </div>

              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="status">状态</Label>
                  <Select
                    value={searchParams.status || ''}
                    onValueChange={(value) => handleSearchParamChange('status', value)}
                  >
                    <SelectTrigger id="status">
                      <SelectValue placeholder="选择状态" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="">全部状态</SelectItem>
                      <SelectItem value="active">活跃</SelectItem>
                      <SelectItem value="inactive">未激活</SelectItem>
                      <SelectItem value="banned">已禁用</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>
            </CardContent>
            <CardFooter className="flex justify-between">
              <Button variant="outline" onClick={resetSearch}>
                <RefreshCw className="mr-2 h-4 w-4" />
                重置
              </Button>
              <Button onClick={() => fetchUsers()}>
                <Search className="mr-2 h-4 w-4" />
                搜索
              </Button>
            </CardFooter>
          </Card>

          <Card>
            <CardHeader>
              <div className="flex justify-between items-center">
                <div>
                  <CardTitle>用户列表</CardTitle>
                  <CardDescription>
                    共找到 {totalUsers} 个用户
                  </CardDescription>
                </div>
                <div className="flex space-x-2">
                  <Button
                    variant="default"
                    size="sm"
                    onClick={() => setIsCreateUserDialogOpen(true)}
                  >
                    <UserPlusIcon className="mr-2 h-4 w-4" />
                    创建用户
                  </Button>
                  <Button variant="outline" size="sm">
                    <Filter className="mr-2 h-4 w-4" />
                    高级筛选
                  </Button>
                </div>
              </div>
            </CardHeader>
            <CardContent>
              <div className="rounded-md border">
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead className="w-12">
                        <Checkbox
                          checked={selectedUsers.length === users.length && users.length > 0}
                          onCheckedChange={handleSelectAll}
                          aria-label="全选"
                        />
                      </TableHead>
                      <TableHead>用户名</TableHead>
                      <TableHead>UUID</TableHead>
                      <TableHead>邮箱</TableHead>
                      <TableHead>角色</TableHead>
                      <TableHead>状态</TableHead>
                      <TableHead>最后登录</TableHead>
                      <TableHead>IP地址</TableHead>
                      <TableHead>提交数</TableHead>
                      <TableHead className="text-right">操作</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {isLoading ? (
                      <TableRow>
                        <TableCell colSpan={10} className="text-center py-8">
                          <div className="flex justify-center items-center">
                            <RefreshCw className="h-6 w-6 animate-spin mr-2" />
                            <span>加载中...</span>
                          </div>
                        </TableCell>
                      </TableRow>
                    ) : users.length === 0 ? (
                      <TableRow>
                        <TableCell colSpan={10} className="text-center py-8">
                          未找到匹配的用户
                        </TableCell>
                      </TableRow>
                    ) : (
                      users.map((user) => (
                        <TableRow key={user.id}>
                          <TableCell>
                            <Checkbox
                              checked={selectedUsers.includes(user.id)}
                              onCheckedChange={(checked) => handleUserSelect(user.id, !!checked)}
                              aria-label={`选择用户 ${user.username}`}
                            />
                          </TableCell>
                          <TableCell>{user.username}</TableCell>
                          <TableCell>
                            <code className="text-xs bg-gray-100 px-2 py-1 rounded">
                              {user.uuid}
                            </code>
                          </TableCell>
                          <TableCell>{user.email}</TableCell>
                          <TableCell>
                            <Badge variant={
                              user.role === 'superadmin' ? 'destructive' :
                              user.role === 'admin' ? 'default' :
                              user.role === 'reviewer' ? 'secondary' : 'outline'
                            }>
                              {user.role === 'superadmin' ? '超级管理员' :
                               user.role === 'admin' ? '管理员' :
                               user.role === 'reviewer' ? '审核员' : '普通用户'}
                            </Badge>
                          </TableCell>
                          <TableCell>
                            <Badge variant={
                              user.status === 'active' ? 'success' :
                              user.status === 'inactive' ? 'warning' : 'destructive'
                            }>
                              {user.status === 'active' ? '活跃' :
                               user.status === 'inactive' ? '未激活' : '已禁用'}
                            </Badge>
                          </TableCell>
                          <TableCell>
                            {user.lastLoginAt ? new Date(user.lastLoginAt).toLocaleString() : '未登录'}
                          </TableCell>
                          <TableCell>
                            {user.lastLoginIp || '-'}
                          </TableCell>
                          <TableCell>
                            {user.submissionCount || 0}
                          </TableCell>
                          <TableCell className="text-right">
                            <DropdownMenu>
                              <DropdownMenuTrigger asChild>
                                <Button variant="ghost" size="icon">
                                  <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="h-4 w-4">
                                    <circle cx="12" cy="12" r="1" />
                                    <circle cx="12" cy="5" r="1" />
                                    <circle cx="12" cy="19" r="1" />
                                  </svg>
                                </Button>
                              </DropdownMenuTrigger>
                              <DropdownMenuContent align="end">
                                <DropdownMenuLabel>用户操作</DropdownMenuLabel>
                                <DropdownMenuItem onClick={() => handleViewUser(user)}>
                                  <Eye className="mr-2 h-4 w-4" />
                                  查看详情
                                </DropdownMenuItem>
                                <DropdownMenuSeparator />
                                <DropdownMenuItem onClick={() => {
                                  setSelectedUser(user);
                                  setNewRole(user.role);
                                  setIsRoleDialogOpen(true);
                                }}>
                                  <Shield className="mr-2 h-4 w-4" />
                                  修改角色
                                </DropdownMenuItem>
                                {user.status !== 'active' && (
                                  <DropdownMenuItem>
                                    <UserCheck className="mr-2 h-4 w-4" />
                                    激活用户
                                  </DropdownMenuItem>
                                )}
                                {user.status !== 'inactive' && (
                                  <DropdownMenuItem>
                                    <UserX className="mr-2 h-4 w-4" />
                                    停用用户
                                  </DropdownMenuItem>
                                )}
                                <DropdownMenuSeparator />
                                <DropdownMenuItem
                                  onClick={() => {
                                    setSelectedUser(user);
                                    setIsDeleteDialogOpen(true);
                                  }}
                                  className="text-red-600"
                                >
                                  <Trash2 className="mr-2 h-4 w-4" />
                                  删除用户
                                </DropdownMenuItem>
                              </DropdownMenuContent>
                            </DropdownMenu>
                          </TableCell>
                        </TableRow>
                      ))
                    )}
                  </TableBody>
                </Table>
              </div>

              {/* 分页 */}
              {totalPages > 1 && (
                <div className="mt-4 flex justify-center">
                  <Pagination>
                    <PaginationContent>
                      <PaginationItem>
                        <PaginationPrevious
                          onClick={() => handlePageChange(Math.max(1, searchParams.page! - 1))}
                          disabled={searchParams.page === 1}
                        />
                      </PaginationItem>

                      {Array.from({ length: totalPages }, (_, i) => i + 1).map((page) => (
                        <PaginationItem key={page}>
                          <PaginationLink
                            onClick={() => handlePageChange(page)}
                            isActive={page === searchParams.page}
                          >
                            {page}
                          </PaginationLink>
                        </PaginationItem>
                      ))}

                      <PaginationItem>
                        <PaginationNext
                          onClick={() => handlePageChange(Math.min(totalPages, searchParams.page! + 1))}
                          disabled={searchParams.page === totalPages}
                        />
                      </PaginationItem>
                    </PaginationContent>
                  </Pagination>
                </div>
              )}
            </CardContent>
          </Card>

        {/* 创建用户对话框 */}
        <Dialog open={isCreateUserDialogOpen} onOpenChange={setIsCreateUserDialogOpen}>
          <DialogContent className="max-w-2xl">
            <DialogHeader>
              <DialogTitle>创建新用户</DialogTitle>
              <DialogDescription>
                创建一个新的用户账号，并设置其角色和权限
              </DialogDescription>
            </DialogHeader>

            <div className="py-4 space-y-6">
              {/* 基本信息 */}
              <div className="space-y-4">
                <h3 className="text-lg font-medium">基本信息</h3>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="new-username">用户名 *</Label>
                    <Input
                      id="new-username"
                      placeholder="输入用户名"
                      value={newUserForm.username}
                      onChange={(e) => handleNewUserFormChange('username', e.target.value)}
                    />
                    <p className="text-xs text-muted-foreground">
                      用户名至少3个字符，只能包含字母、数字和下划线
                    </p>
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="new-email">邮箱地址 *</Label>
                    <Input
                      id="new-email"
                      type="email"
                      placeholder="输入邮箱地址"
                      value={newUserForm.email}
                      onChange={(e) => handleNewUserFormChange('email', e.target.value)}
                    />
                    <p className="text-xs text-muted-foreground">
                      用于登录和接收系统通知
                    </p>
                  </div>
                </div>
              </div>

              {/* 密码设置 */}
              <div className="space-y-4">
                <h3 className="text-lg font-medium">密码设置</h3>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="new-password">密码 *</Label>
                    <Input
                      id="new-password"
                      type="password"
                      placeholder="输入密码"
                      value={newUserForm.password}
                      onChange={(e) => handleNewUserFormChange('password', e.target.value)}
                    />
                    <p className="text-xs text-muted-foreground">
                      密码至少6个字符
                    </p>
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="confirm-password">确认密码 *</Label>
                    <Input
                      id="confirm-password"
                      type="password"
                      placeholder="再次输入密码"
                      value={newUserForm.confirmPassword}
                      onChange={(e) => handleNewUserFormChange('confirmPassword', e.target.value)}
                    />
                    <p className="text-xs text-muted-foreground">
                      请再次输入相同的密码
                    </p>
                  </div>
                </div>
              </div>

              {/* 角色和状态 */}
              <div className="space-y-4">
                <h3 className="text-lg font-medium">角色和状态</h3>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="new-role">用户角色</Label>
                    <Select
                      value={newUserForm.role}
                      onValueChange={(value) => handleNewUserFormChange('role', value)}
                    >
                      <SelectTrigger id="new-role">
                        <SelectValue placeholder="选择角色" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="user">普通用户</SelectItem>
                        <SelectItem value="reviewer">审核员</SelectItem>
                        <SelectItem value="admin">管理员</SelectItem>
                        <SelectItem value="superadmin">超级管理员</SelectItem>
                      </SelectContent>
                    </Select>
                    <p className="text-xs text-muted-foreground">
                      {newUserForm.role === 'user' && '可以提交内容和参与调查'}
                      {newUserForm.role === 'reviewer' && '可以审核用户提交的内容'}
                      {newUserForm.role === 'admin' && '可以管理用户和审核员'}
                      {newUserForm.role === 'superadmin' && '拥有系统的所有权限'}
                    </p>
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="new-status">账号状态</Label>
                    <Select
                      value={newUserForm.status}
                      onValueChange={(value) => handleNewUserFormChange('status', value)}
                    >
                      <SelectTrigger id="new-status">
                        <SelectValue placeholder="选择状态" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="active">活跃</SelectItem>
                        <SelectItem value="inactive">未激活</SelectItem>
                      </SelectContent>
                    </Select>
                    <p className="text-xs text-muted-foreground">
                      {newUserForm.status === 'active' ? '用户可以立即登录使用' : '用户需要激活后才能登录'}
                    </p>
                  </div>
                </div>
              </div>

              {/* 其他选项 */}
              <div className="space-y-4">
                <h3 className="text-lg font-medium">其他选项</h3>
                <div className="flex items-center space-x-2">
                  <Checkbox
                    id="send-welcome-email"
                    checked={newUserForm.sendWelcomeEmail}
                    onCheckedChange={(checked) => handleNewUserFormChange('sendWelcomeEmail', !!checked)}
                  />
                  <Label htmlFor="send-welcome-email" className="text-sm">
                    发送欢迎邮件
                  </Label>
                  <p className="text-xs text-muted-foreground ml-2">
                    向新用户发送包含登录信息的欢迎邮件
                  </p>
                </div>
              </div>

              {/* 警告信息 */}
              {newUserForm.role === 'superadmin' && (
                <div className="bg-red-50 border border-red-200 rounded-md p-3 text-sm text-red-800">
                  <p className="font-medium">⚠️ 警告</p>
                  <p>您正在创建一个超级管理员账号。超级管理员拥有系统的所有权限，包括删除数据和修改系统设置。请确保只将此角色授予可信任的人员。</p>
                </div>
              )}

              {newUserForm.role === 'admin' && (
                <div className="bg-yellow-50 border border-yellow-200 rounded-md p-3 text-sm text-yellow-800">
                  <p className="font-medium">💡 提示</p>
                  <p>管理员可以管理用户和审核员，但无法访问系统配置和超级管理员功能。</p>
                </div>
              )}
            </div>

            <DialogFooter>
              <Button
                variant="outline"
                onClick={() => {
                  setIsCreateUserDialogOpen(false);
                  resetNewUserForm();
                }}
                disabled={isCreatingUser}
              >
                取消
              </Button>
              <Button
                onClick={handleCreateUser}
                disabled={isCreatingUser}
                className={newUserForm.role === 'superadmin' ? 'bg-red-600 hover:bg-red-700' : ''}
              >
                {isCreatingUser ? (
                  <>
                    <RefreshCw className="mr-2 h-4 w-4 animate-spin" />
                    创建中...
                  </>
                ) : (
                  <>
                    <UserPlusIcon className="mr-2 h-4 w-4" />
                    创建用户
                  </>
                )}
              </Button>
            </DialogFooter>
          </DialogContent>
        </Dialog>

        {/* 用户详情对话框 */}
        <Dialog open={isUserDialogOpen} onOpenChange={setIsUserDialogOpen}>
          <DialogContent className="max-w-4xl max-h-[80vh] overflow-y-auto">
            {selectedUser && (
              <>
                <DialogHeader>
                  <DialogTitle>用户详情</DialogTitle>
                  <DialogDescription>
                    查看用户信息和提交的内容
                  </DialogDescription>
                </DialogHeader>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4 py-4">
                  <div>
                    <h3 className="text-lg font-medium mb-2">基本信息</h3>
                    <div className="space-y-2">
                      <div className="grid grid-cols-3 gap-2">
                        <span className="font-medium">UUID:</span>
                        <span className="col-span-2 font-mono text-xs">{selectedUser.uuid}</span>
                      </div>
                      <div className="grid grid-cols-3 gap-2">
                        <span className="font-medium">用户名:</span>
                        <span className="col-span-2">{selectedUser.username}</span>
                      </div>
                      <div className="grid grid-cols-3 gap-2">
                        <span className="font-medium">邮箱:</span>
                        <span className="col-span-2">{selectedUser.email}</span>
                      </div>
                      <div className="grid grid-cols-3 gap-2">
                        <span className="font-medium">角色:</span>
                        <span className="col-span-2">
                          <Badge variant={
                            selectedUser.role === 'superadmin' ? 'destructive' :
                            selectedUser.role === 'admin' ? 'default' :
                            selectedUser.role === 'reviewer' ? 'secondary' : 'outline'
                          }>
                            {selectedUser.role === 'superadmin' ? '超级管理员' :
                             selectedUser.role === 'admin' ? '管理员' :
                             selectedUser.role === 'reviewer' ? '审核员' : '普通用户'}
                          </Badge>
                        </span>
                      </div>
                      <div className="grid grid-cols-3 gap-2">
                        <span className="font-medium">状态:</span>
                        <span className="col-span-2">
                          <Badge variant={
                            selectedUser.status === 'active' ? 'success' :
                            selectedUser.status === 'inactive' ? 'warning' : 'destructive'
                          }>
                            {selectedUser.status === 'active' ? '活跃' :
                             selectedUser.status === 'inactive' ? '未激活' : '已禁用'}
                          </Badge>
                        </span>
                      </div>
                      <div className="grid grid-cols-3 gap-2">
                        <span className="font-medium">注册时间:</span>
                        <span className="col-span-2">{new Date(selectedUser.createdAt).toLocaleString()}</span>
                      </div>
                      <div className="grid grid-cols-3 gap-2">
                        <span className="font-medium">最后登录:</span>
                        <span className="col-span-2">{selectedUser.lastLoginAt ? new Date(selectedUser.lastLoginAt).toLocaleString() : '未登录'}</span>
                      </div>
                      <div className="grid grid-cols-3 gap-2">
                        <span className="font-medium">IP地址:</span>
                        <span className="col-span-2">{selectedUser.lastLoginIp || '-'}</span>
                      </div>
                    </div>
                  </div>

                  <div>
                    <h3 className="text-lg font-medium mb-2">提交统计</h3>
                    <div className="space-y-2">
                      <div className="grid grid-cols-3 gap-2">
                        <span className="font-medium">总提交数:</span>
                        <span className="col-span-2">{selectedUser.submissionCount || 0}</span>
                      </div>
                      <div className="grid grid-cols-3 gap-2">
                        <span className="font-medium">故事数:</span>
                        <span className="col-span-2">{selectedUser.storyCount || 0}</span>
                      </div>
                      <div className="grid grid-cols-3 gap-2">
                        <span className="font-medium">问卷回复数:</span>
                        <span className="col-span-2">{selectedUser.questionnaireCount || 0}</span>
                      </div>
                    </div>

                    <div className="mt-4 space-y-2">
                      <h3 className="text-lg font-medium">用户操作</h3>
                      <div className="flex flex-wrap gap-2">
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => {
                            setIsUserDialogOpen(false);
                            setNewRole(selectedUser.role);
                            setIsRoleDialogOpen(true);
                          }}
                        >
                          <Shield className="mr-2 h-4 w-4" />
                          修改角色
                        </Button>
                        {selectedUser.status !== 'active' && (
                          <Button
                            variant="outline"
                            size="sm"
                          >
                            <UserCheck className="mr-2 h-4 w-4" />
                            激活用户
                          </Button>
                        )}
                        {selectedUser.status !== 'inactive' && (
                          <Button
                            variant="outline"
                            size="sm"
                          >
                            <UserX className="mr-2 h-4 w-4" />
                            停用用户
                          </Button>
                        )}
                        <Button
                          variant="outline"
                          size="sm"
                          className="text-red-600"
                          onClick={() => {
                            setIsUserDialogOpen(false);
                            setIsDeleteDialogOpen(true);
                          }}
                        >
                          <Trash2 className="mr-2 h-4 w-4" />
                          删除用户
                        </Button>
                      </div>
                    </div>
                  </div>
                </div>

                <div className="space-y-4 mt-4">
                  <h3 className="text-lg font-medium">用户内容</h3>

                  {isUserContentLoading ? (
                    <div className="flex justify-center items-center py-8">
                      <RefreshCw className="h-6 w-6 animate-spin mr-2" />
                      <span>加载中...</span>
                    </div>
                  ) : userContents.length === 0 ? (
                    <div className="text-center py-8 border rounded-md">
                      <p className="text-muted-foreground">该用户暂无内容</p>
                    </div>
                  ) : (
                    <div className="space-y-4">
                      {userContents.map((content) => (
                        <Card key={content.id}>
                          <CardHeader className="py-3">
                            <div className="flex justify-between items-center">
                              <div className="flex items-center space-x-2">
                                <Badge variant={
                                  content.type === 'story' ? 'default' :
                                  content.type === 'questionnaire' ? 'secondary' : 'outline'
                                }>
                                  {content.type === 'story' ? '故事' :
                                   content.type === 'questionnaire' ? '问卷' : '评论'}
                                </Badge>
                                {content.title && <span className="font-medium">{content.title}</span>}
                                <Badge variant={
                                  content.status === 'approved' ? 'success' :
                                  content.status === 'rejected' ? 'destructive' : 'warning'
                                }>
                                  {content.status === 'approved' ? '已通过' :
                                   content.status === 'rejected' ? '已拒绝' : '待审核'}
                                </Badge>
                              </div>
                              <span className="text-xs text-muted-foreground">
                                {new Date(content.createdAt).toLocaleString()}
                              </span>
                            </div>
                          </CardHeader>
                          <CardContent className="py-2">
                            <div className="max-h-40 overflow-y-auto border rounded-md p-3 bg-muted">
                              {content.type === 'questionnaire' ? (
                                <pre className="text-xs whitespace-pre-wrap">{content.content}</pre>
                              ) : (
                                <p className="whitespace-pre-wrap">{content.content}</p>
                              )}
                            </div>

                            {content.tags && content.tags.length > 0 && (
                              <div className="flex flex-wrap gap-1 mt-2">
                                {content.tags.map((tag, index) => (
                                  <Badge key={index} variant="outline">{tag}</Badge>
                                ))}
                              </div>
                            )}
                          </CardContent>
                        </Card>
                      ))}
                    </div>
                  )}
                </div>

                <DialogFooter>
                  <Button variant="outline" onClick={() => setIsUserDialogOpen(false)}>
                    关闭
                  </Button>
                </DialogFooter>
              </>
            )}
          </DialogContent>
        </Dialog>

        {/* 修改角色对话框 */}
        <Dialog open={isRoleDialogOpen} onOpenChange={setIsRoleDialogOpen}>
          <DialogContent>
            <DialogHeader>
              <DialogTitle>修改用户角色</DialogTitle>
              <DialogDescription>
                更改用户的系统角色和权限
              </DialogDescription>
            </DialogHeader>

            <div className="py-4">
              {selectedUser && (
                <>
                  <div className="mb-4 p-3 bg-muted rounded-md">
                    <p><span className="font-medium">用户名:</span> {selectedUser.username}</p>
                    <p><span className="font-medium">邮箱:</span> {selectedUser.email}</p>
                    <p><span className="font-medium">当前角色:</span>
                      <Badge className="ml-2" variant={
                        selectedUser.role === 'superadmin' ? 'destructive' :
                        selectedUser.role === 'admin' ? 'default' :
                        selectedUser.role === 'reviewer' ? 'secondary' : 'outline'
                      }>
                        {selectedUser.role === 'superadmin' ? '超级管理员' :
                         selectedUser.role === 'admin' ? '管理员' :
                         selectedUser.role === 'reviewer' ? '审核员' : '普通用户'}
                      </Badge>
                    </p>
                  </div>

                  <div className="space-y-4">
                    <div className="space-y-2">
                      <Label htmlFor="role">新角色</Label>
                      <Select
                        value={newRole}
                        onValueChange={setNewRole}
                      >
                        <SelectTrigger id="role">
                          <SelectValue placeholder="选择角色" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="user">普通用户</SelectItem>
                          <SelectItem value="reviewer">审核员</SelectItem>
                          <SelectItem value="admin">管理员</SelectItem>
                          <SelectItem value="superadmin">超级管理员</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>

                    <div className="bg-yellow-50 border border-yellow-200 rounded-md p-3 text-sm text-yellow-800">
                      <p>修改用户角色将改变其在系统中的权限。请确保您了解此操作的影响。</p>
                      {newRole === 'superadmin' && (
                        <p className="mt-2 text-red-600 font-medium">警告：超级管理员拥有系统的所有权限，请谨慎授予此角色。</p>
                      )}
                    </div>
                  </div>
                </>
              )}
            </div>

            <DialogFooter>
              <Button variant="outline" onClick={() => setIsRoleDialogOpen(false)}>
                取消
              </Button>
              <Button
                variant={newRole === 'superadmin' ? 'destructive' : 'default'}
                onClick={handleUpdateRole}
              >
                确认修改
              </Button>
            </DialogFooter>
          </DialogContent>
        </Dialog>

        {/* 删除用户确认对话框 */}
        <Dialog open={isDeleteDialogOpen} onOpenChange={setIsDeleteDialogOpen}>
          <DialogContent>
            <DialogHeader>
              <DialogTitle>删除用户</DialogTitle>
              <DialogDescription>
                此操作将永久删除用户账号，无法恢复
              </DialogDescription>
            </DialogHeader>

            <div className="py-4">
              {selectedUser && (
                <>
                  <p>您确定要删除以下用户吗？</p>
                  <div className="mt-2 p-3 bg-muted rounded-md">
                    <p><span className="font-medium">用户名:</span> {selectedUser.username}</p>
                    <p><span className="font-medium">邮箱:</span> {selectedUser.email}</p>
                    <p><span className="font-medium">角色:</span>
                      <Badge className="ml-2" variant={
                        selectedUser.role === 'superadmin' ? 'destructive' :
                        selectedUser.role === 'admin' ? 'default' :
                        selectedUser.role === 'reviewer' ? 'secondary' : 'outline'
                      }>
                        {selectedUser.role === 'superadmin' ? '超级管理员' :
                         selectedUser.role === 'admin' ? '管理员' :
                         selectedUser.role === 'reviewer' ? '审核员' : '普通用户'}
                      </Badge>
                    </p>
                  </div>

                  <div className="mt-4 bg-red-50 border border-red-200 rounded-md p-3 text-sm text-red-800">
                    <p>警告：删除操作不可逆，用户的所有数据将被永久删除。</p>
                    {(selectedUser.role === 'admin' || selectedUser.role === 'superadmin') && (
                      <p className="mt-2 font-medium">您正在删除一个管理员账号，这可能会影响系统的管理功能。</p>
                    )}
                  </div>
                </>
              )}
            </div>

            <DialogFooter>
              <Button variant="outline" onClick={() => setIsDeleteDialogOpen(false)}>
                取消
              </Button>
              <Button
                variant="destructive"
                onClick={handleDeleteUser}
              >
                确认删除
              </Button>
            </DialogFooter>
          </DialogContent>
        </Dialog>
        </div>
      </div>
    </SuperAdminLayout>
  );
};

export default SuperAdminUserManagementPage;
