/**
 * 前端认证状态检查脚本
 * 在浏览器控制台中运行此脚本来诊断认证问题
 * 
 * 使用方法：
 * 1. 打开浏览器开发者工具
 * 2. 切换到 Console 标签
 * 3. 复制粘贴此脚本并回车执行
 */

(function() {
    'use strict';
    
    console.log('🔍 大学生就业调研系统 - 前端认证状态检查');
    console.log('='.repeat(50));
    
    // 检查基础环境
    function checkEnvironment() {
        console.log('\n📋 环境信息检查');
        console.log('-'.repeat(30));
        
        console.log('🌐 当前URL:', window.location.href);
        console.log('🏠 域名:', window.location.hostname);
        console.log('📍 路径:', window.location.pathname);
        
        // 检查环境变量
        if (typeof import !== 'undefined' && import.meta && import.meta.env) {
            console.log('⚙️  API Base URL:', import.meta.env.VITE_API_BASE_URL || '未设置');
            console.log('🔧 环境模式:', import.meta.env.MODE || '未知');
        } else {
            console.log('⚠️  无法获取环境变量信息');
        }
    }
    
    // 检查认证状态
    function checkAuthStatus() {
        console.log('\n🔐 认证状态检查');
        console.log('-'.repeat(30));
        
        const token = localStorage.getItem('adminToken');
        if (!token) {
            console.error('❌ 未找到认证token');
            console.log('💡 建议：请先登录管理员账户');
            return null;
        }
        
        console.log('✅ Token存在:', token.substring(0, 20) + '...');
        
        // 解析token
        try {
            const parts = token.split('.');
            if (parts.length !== 3) {
                throw new Error('Token格式错误');
            }
            
            const payload = JSON.parse(atob(parts[1]));
            console.log('👤 用户信息:');
            console.log('   - 用户名:', payload.username);
            console.log('   - 角色:', payload.role);
            console.log('   - 用户ID:', payload.id);
            console.log('   - 签发时间:', new Date(payload.iat * 1000).toLocaleString());
            console.log('   - 过期时间:', new Date(payload.exp * 1000).toLocaleString());
            
            // 检查token是否过期
            const now = Math.floor(Date.now() / 1000);
            if (payload.exp < now) {
                console.error('❌ Token已过期');
                console.log('💡 建议：请重新登录');
                return null;
            } else {
                const remainingTime = payload.exp - now;
                console.log('⏰ Token剩余有效时间:', Math.floor(remainingTime / 3600), '小时');
            }
            
            return { token, payload };
        } catch (e) {
            console.error('❌ Token解析失败:', e.message);
            console.log('💡 建议：请清除localStorage并重新登录');
            return null;
        }
    }
    
    // 测试API连接
    async function testAPIConnection(authInfo) {
        console.log('\n🌐 API连接测试');
        console.log('-'.repeat(30));
        
        if (!authInfo) {
            console.log('⏭️  跳过API测试 (认证信息无效)');
            return;
        }
        
        const { token } = authInfo;
        const apiBase = (typeof import !== 'undefined' && import.meta && import.meta.env && import.meta.env.VITE_API_BASE_URL) || '';
        
        // 测试端点列表
        const endpoints = [
            { path: '/health', name: '健康检查', needAuth: false },
            { path: '/api/admin/deidentification/config', name: '去标识化配置', needAuth: true },
            { path: '/api/admin/deidentification/test-data', name: '测试数据', needAuth: true },
            { path: '/api/admin/deidentification/provider-stats', name: '提供商统计', needAuth: true }
        ];
        
        for (const endpoint of endpoints) {
            try {
                console.log(`🔍 测试 ${endpoint.name}...`);
                
                const headers = { 'Content-Type': 'application/json' };
                if (endpoint.needAuth) {
                    headers['Authorization'] = `Bearer ${token}`;
                }
                
                const response = await fetch(`${apiBase}${endpoint.path}`, { headers });
                
                if (response.ok) {
                    console.log(`✅ ${endpoint.name} - 状态码 ${response.status}`);
                    
                    // 尝试解析响应
                    try {
                        const data = await response.json();
                        if (data.success) {
                            console.log(`   📊 响应正常:`, data.success);
                        } else {
                            console.log(`   ⚠️  响应异常:`, data.error || '未知错误');
                        }
                    } catch (e) {
                        console.log(`   📄 响应格式: 非JSON`);
                    }
                } else {
                    console.error(`❌ ${endpoint.name} - 状态码 ${response.status}`);
                    
                    if (response.status === 401) {
                        console.log('   🔐 认证失败，可能的原因：');
                        console.log('      - Token无效或过期');
                        console.log('      - 权限不足');
                        console.log('      - 认证头格式错误');
                    } else if (response.status === 404) {
                        console.log('   🔍 路由未找到，可能的原因：');
                        console.log('      - API路由未注册');
                        console.log('      - 路径配置错误');
                        console.log('      - 后端部署问题');
                    }
                }
            } catch (error) {
                console.error(`❌ ${endpoint.name} - 网络错误:`, error.message);
            }
        }
    }
    
    // 检查本地存储
    function checkLocalStorage() {
        console.log('\n💾 本地存储检查');
        console.log('-'.repeat(30));
        
        const keys = ['adminToken', 'userInfo', 'lastLogin'];
        keys.forEach(key => {
            const value = localStorage.getItem(key);
            if (value) {
                console.log(`✅ ${key}:`, value.length > 50 ? value.substring(0, 50) + '...' : value);
            } else {
                console.log(`❌ ${key}: 未设置`);
            }
        });
        
        // 检查sessionStorage
        console.log('\n📝 会话存储检查');
        const sessionKeys = Object.keys(sessionStorage);
        if (sessionKeys.length > 0) {
            sessionKeys.forEach(key => {
                console.log(`📋 ${key}:`, sessionStorage.getItem(key));
            });
        } else {
            console.log('📭 会话存储为空');
        }
    }
    
    // 生成修复建议
    function generateFixSuggestions(authInfo) {
        console.log('\n🔧 修复建议');
        console.log('-'.repeat(30));
        
        if (!authInfo) {
            console.log('🎯 认证问题修复步骤：');
            console.log('1. 清除本地存储: localStorage.clear()');
            console.log('2. 刷新页面');
            console.log('3. 重新登录管理员账户');
            console.log('4. 检查用户名密码是否正确');
        } else {
            console.log('🎯 API连接问题修复步骤：');
            console.log('1. 检查网络连接');
            console.log('2. 验证API服务器状态');
            console.log('3. 检查CORS配置');
            console.log('4. 查看浏览器网络标签页的详细错误');
        }
        
        console.log('\n📚 更多帮助：');
        console.log('- 查看 TROUBLESHOOTING_GUIDE.md');
        console.log('- 运行后端健康检查脚本');
        console.log('- 检查部署日志');
    }
    
    // 主执行函数
    async function runDiagnostics() {
        try {
            checkEnvironment();
            const authInfo = checkAuthStatus();
            await testAPIConnection(authInfo);
            checkLocalStorage();
            generateFixSuggestions(authInfo);
            
            console.log('\n✨ 诊断完成');
            console.log('='.repeat(50));
            
        } catch (error) {
            console.error('❌ 诊断过程中发生错误:', error);
        }
    }
    
    // 执行诊断
    runDiagnostics();
    
    // 提供手动清理函数
    window.clearAuthData = function() {
        localStorage.removeItem('adminToken');
        localStorage.removeItem('userInfo');
        localStorage.removeItem('lastLogin');
        sessionStorage.clear();
        console.log('🧹 认证数据已清除，请刷新页面重新登录');
    };
    
    console.log('\n💡 提示：如需清除认证数据，请运行 clearAuthData()');
    
})();
