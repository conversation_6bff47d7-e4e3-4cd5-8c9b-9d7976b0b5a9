/**
 * 安全模块类型定义
 */

/**
 * 安全配置
 */
export interface SecurityConfig {
  // 全局设置
  enabled: boolean;           // 是否启用安全模块
  protectionLevel: 0 | 1 | 2 | 3 | 4;  // 防护等级
  
  // 验证码设置
  captcha: CaptchaConfig;
  
  // 行为分析设置
  behaviorAnalysis: BehaviorAnalysisConfig;
  
  // 速率限制设置
  rateLimit: RateLimitConfig;
  
  // 蜜罐设置
  honeypot: HoneypotConfig;
  
  // 指纹识别设置
  fingerprinting: FingerprintingConfig;
  
  // 日志和监控设置
  logging: LoggingConfig;
}

/**
 * 验证码配置
 */
export interface CaptchaConfig {
  enabled: boolean;         // 是否启用验证码
  type: 'none' | 'turnstile' | 'custom';  // 验证码类型
  siteKey?: string;         // 验证码站点密钥
  secretKey?: string;       // 验证码密钥（仅后端使用）
  triggerThreshold: number; // 触发验证码的阈值
}

/**
 * 行为分析配置
 */
export interface BehaviorAnalysisConfig {
  enabled: boolean;         // 是否启用行为分析
  minCompletionTime: number;  // 最短完成时间（毫秒）
  trackMouseMovements: boolean;  // 是否跟踪鼠标移动
  trackKeyboardEvents: boolean;  // 是否跟踪键盘事件
  suspiciousScoreThreshold: number;  // 可疑分数阈值
}

/**
 * 速率限制配置
 */
export interface RateLimitConfig {
  enabled: boolean;         // 是否启用速率限制
  ipLimit: number;          // 每IP限制（次/小时）
  fingerprintLimit: number; // 每指纹限制（次/小时）
  cooldownPeriod: number;   // 冷却期（秒）
}

/**
 * 蜜罐配置
 */
export interface HoneypotConfig {
  enabled: boolean;         // 是否启用蜜罐
  fieldCount: number;       // 蜜罐字段数量
  silentRejection: boolean; // 是否静默拒绝
}

/**
 * 指纹识别配置
 */
export interface FingerprintingConfig {
  enabled: boolean;         // 是否启用指纹识别
  storageTime: number;      // 存储时间（天）
}

/**
 * 日志和监控配置
 */
export interface LoggingConfig {
  enabled: boolean;         // 是否启用日志
  logLevel: 'error' | 'warn' | 'info' | 'debug';  // 日志级别
  storeSubmissionData: boolean;  // 是否存储提交数据
}

/**
 * 验证结果
 */
export interface ValidationResult {
  valid: boolean;           // 是否有效
  error?: string;           // 错误信息
  suspiciousScore?: number; // 可疑分数
}

/**
 * 安全元数据
 */
export interface SecurityMetadata {
  timestamp: number;        // 时间戳
  protectionLevel: number;  // 防护等级
  captcha?: any;            // 验证码数据
  behavior?: any;           // 行为数据
  fingerprint?: string;     // 指纹数据
}

/**
 * 安全事件
 */
export interface SecurityEvent {
  type: string;             // 事件类型
  timestamp?: number;       // 时间戳
  level?: 'error' | 'warn' | 'info' | 'debug';  // 事件级别
  message?: string;         // 事件消息
  data?: any;               // 事件数据
}

/**
 * 分析结果
 */
export interface AnalysisResult {
  suspicious: boolean;      // 是否可疑
  suspiciousScore: number;  // 可疑分数
  reasons?: string[];       // 可疑原因
  data?: any;               // 分析数据
}
