/**
 * 审核员管理服务
 * 
 * 提供审核员管理相关的功能
 */

import { fetchData } from './unifiedDataService';

/**
 * 获取审核员列表
 * @param params 搜索参数
 * @returns 审核员列表
 */
export async function getReviewers(params: {
  status?: string;
  search?: string;
  page?: number;
  limit?: number;
} = {}) {
  return fetchData(
    '/admin/reviewers',
    'getReviewersMock',
    params,
    { method: 'GET' }
  );
}

/**
 * 获取审核员详情
 * @param id 审核员ID
 * @returns 审核员详情
 */
export async function getReviewerDetails(id: number) {
  return fetchData(
    `/admin/reviewers/${id}`,
    'getReviewerDetailMock',
    { id },
    { method: 'GET' }
  );
}

/**
 * 添加审核员
 * @param reviewerData 审核员数据
 * @returns 添加结果
 */
export async function addReviewer(reviewerData: {
  username: string;
  password: string;
  name: string;
  permissions?: string[];
}) {
  return fetchData(
    '/admin/reviewers',
    'addReviewerMock',
    reviewerData,
    { method: 'POST' }
  );
}

/**
 * 更新审核员状态
 * @param id 审核员ID
 * @param status 状态
 * @param duration 停用时长
 * @returns 更新结果
 */
export async function updateReviewerStatus(
  id: number,
  status: 'active' | 'disabled',
  duration?: '1day' | '1week' | '1month' | 'permanent'
) {
  return fetchData(
    `/admin/reviewers/${id}/status`,
    'updateReviewerStatusMock',
    { id, status, duration },
    { method: 'PUT' }
  );
}

/**
 * 删除审核员
 * @param id 审核员ID
 * @returns 删除结果
 */
export async function deleteReviewer(id: number) {
  return fetchData(
    `/admin/reviewers/${id}`,
    'deleteReviewerMock',
    { id },
    { method: 'DELETE' }
  );
}

/**
 * 获取审核员绩效数据
 * @param timeRange 时间范围
 * @returns 审核员绩效数据
 */
export async function getReviewerPerformance(timeRange: string = 'week') {
  return fetchData(
    '/admin/reviewers/performance',
    'getReviewerPerformanceMock',
    { timeRange },
    { method: 'GET' }
  );
}
