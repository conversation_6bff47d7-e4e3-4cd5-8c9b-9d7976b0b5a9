-- 测试数据插入脚本
-- 为系统添加基础测试数据

-- 插入测试用户（匿名用户）
INSERT OR IGNORE INTO users (id, username, email, role, is_anonymous, identity_a, identity_b, created_at) VALUES 
('anon-001', NULL, NULL, 'user', TRUE, '12345678901', '1234', '2024-01-15 10:00:00'),
('anon-002', NULL, NULL, 'user', TRUE, '12345678902', '5678', '2024-01-16 11:00:00'),
('anon-003', NULL, NULL, 'user', TRUE, '12345678903', '9012', '2024-01-17 12:00:00'),
('anon-004', NULL, NULL, 'user', TRUE, '12345678904', '3456', '2024-01-18 13:00:00'),
('anon-005', NULL, NULL, 'user', TRUE, '12345678905', '7890', '2024-01-19 14:00:00'),
('anon-006', NULL, NULL, 'user', TRUE, '12345678906', '2468', '2024-01-20 15:00:00'),
('anon-007', NULL, NULL, 'user', TRUE, '12345678907', '1357', '2024-01-21 16:00:00'),
('anon-008', NULL, NULL, 'user', TRUE, '12345678908', '9753', '2024-01-22 17:00:00'),
('anon-009', NULL, NULL, 'user', TRUE, '12345678909', '8642', '2024-01-23 18:00:00'),
('anon-010', NULL, NULL, 'user', TRUE, '12345678910', '7531', '2024-01-24 19:00:00');

-- 插入问卷回复数据
INSERT OR IGNORE INTO questionnaire_responses (
    id, user_id, sequence_number, education_level, graduation_year, major, school_name, region,
    employment_status, current_salary, expected_salary, job_search_duration,
    interview_count, offer_count, rejection_reasons,
    advice_for_students, observation_on_employment,
    is_anonymous, created_at
) VALUES 
('qr-001', 'anon-001', 1, '本科', 2023, '计算机科学与技术', '北京大学', '北京市', 
 '已就业', 15000, 18000, 3, 8, 2, '技术能力不足,缺乏项目经验',
 '多做项目，提升技术能力，关注行业动态', '互联网行业竞争激烈，但机会也多',
 TRUE, '2024-01-15 10:30:00'),

('qr-002', 'anon-002', 2, '硕士', 2023, '软件工程', '清华大学', '上海市',
 '已就业', 22000, 25000, 2, 12, 4, '薪资期望过高',
 '保持学习，适当降低期望', '大厂门槛高，中小企业机会多',
 TRUE, '2024-01-16 11:30:00'),

('qr-003', 'anon-003', 3, '本科', 2024, '数据科学', '复旦大学', '广东省',
 '求职中', NULL, 20000, 6, 15, 1, '经验不足,地域限制',
 '多投简历，扩大求职范围', '数据分析岗位需求大，但要求也高',
 TRUE, '2024-01-17 12:30:00'),

('qr-004', 'anon-004', 4, '本科', 2023, '电子信息工程', '华中科技大学', '湖北省',
 '已就业', 12000, 15000, 4, 6, 1, '专业不对口,竞争激烈',
 '提前准备，多关注招聘信息', '传统行业转型，新兴技术岗位增多',
 TRUE, '2024-01-18 13:30:00'),

('qr-005', 'anon-005', 5, '硕士', 2024, '人工智能', '中山大学', '广东省',
 '求职中', NULL, 30000, 4, 10, 2, '技术深度不够',
 '深入学习算法，多做实践项目', 'AI行业发展迅速，人才需求大',
 TRUE, '2024-01-19 14:30:00'),

('qr-006', 'anon-006', 6, '本科', 2023, '市场营销', '南京大学', '江苏省',
 '已就业', 8000, 12000, 5, 20, 3, '缺乏实习经验,行业不景气',
 '多参加实习，积累经验', '营销行业变化快，需要不断学习',
 TRUE, '2024-01-20 15:30:00'),

('qr-007', 'anon-007', 7, '本科', 2024, '金融学', '上海财经大学', '上海市',
 '求职中', NULL, 18000, 3, 8, 0, '行业门槛高,需要证书',
 '考取相关证书，提升专业能力', '金融行业稳定，但竞争激烈',
 TRUE, '2024-01-21 16:30:00'),

('qr-008', 'anon-008', 8, '硕士', 2023, '机械工程', '西安交通大学', '陕西省',
 '已就业', 14000, 16000, 6, 5, 1, '传统行业转型慢',
 '关注新技术，提升综合能力', '制造业升级，智能化需求增加',
 TRUE, '2024-01-22 17:30:00'),

('qr-009', 'anon-009', 9, '本科', 2024, '英语', '北京外国语大学', '北京市',
 '求职中', NULL, 10000, 8, 25, 2, '专业就业面窄,薪资偏低',
 '拓宽就业思路，考虑跨行业发展', '语言类专业需要结合其他技能',
 TRUE, '2024-01-23 18:30:00'),

('qr-010', 'anon-010', 10, '硕士', 2024, '生物医学工程', '华南理工大学', '广东省',
 '求职中', NULL, 25000, 5, 7, 1, '专业对口岗位少',
 '考虑相关行业，如医疗器械', '生物医学前景好，但需要时间发展',
 TRUE, '2024-01-24 19:30:00');

-- 插入故事数据
INSERT OR IGNORE INTO stories (
    id, user_id, sequence_number, title, content, category, tags, status, is_anonymous, created_at
) VALUES 
('story-001', 'anon-001', 1, '从迷茫到清晰：我的求职之路', 
 '刚开始求职时完全不知道自己想要什么，投了很多简历都石沉大海。后来通过实习和项目经历，逐渐明确了自己的方向。现在在一家互联网公司做后端开发，虽然工作强度大，但学到了很多东西。建议学弟学妹们早点开始准备，多做项目积累经验。',
 '求职经历', '["求职经历", "技能提升"]', 'approved', TRUE, '2024-01-25 10:00:00'),

('story-002', 'anon-002', 2, '大厂面试的那些坑', 
 '面试了几家大厂，发现每家的风格都不一样。有的注重算法，有的看重项目经验，还有的考察系统设计。最重要的是要针对不同公司做准备，不能一套模板走天下。最终拿到了心仪的offer，薪资也比较满意。',
 '面试经验', '["面试技巧", "求职经历"]', 'approved', TRUE, '2024-01-26 11:00:00'),

('story-003', 'anon-003', 3, '转行程序员的心路历程', 
 '本来学的是机械专业，但对编程很感兴趣。大三开始自学编程，参加了一些培训班。虽然转行很难，但坚持下来了。现在在一家创业公司做前端开发，工作很有挑战性。转行需要很大勇气，但如果真的喜欢，就要坚持下去。',
 '职场感悟', '["职场感悟", "技能提升"]', 'approved', TRUE, '2024-01-27 12:00:00'),

('story-004', 'anon-004', 4, '小公司也有大机会', 
 '很多同学都想去大厂，但我选择了一家小公司。虽然薪资不如大厂，但成长空间很大，能接触到各个方面的工作。在这里我从一个新人成长为团队负责人，收获了很多宝贵经验。选择工作不只看薪资，更要看发展前景。',
 '职场感悟', '["职场感悟", "行业观察"]', 'approved', TRUE, '2024-01-28 13:00:00'),

('story-005', 'anon-005', 5, '研究生vs本科生：就业差异观察', 
 '作为研究生，我发现在求职过程中确实有一些优势，比如起薪更高，选择面更广。但也有压力，比如年龄偏大，期望值更高。本科同学虽然起点低一些，但适应性更强，发展也不一定比研究生差。关键还是看个人能力和努力程度。',
 '行业观察', '["行业观察", "求职经历"]', 'approved', TRUE, '2024-01-29 14:00:00');

-- 插入问卷心声数据
INSERT OR IGNORE INTO questionnaire_voices (
    id, questionnaire_id, content, status, is_anonymous, created_at
) VALUES 
('voice-001', 'qr-001', '希望学校能提供更多实习机会，让我们提前了解职场环境。', 'approved', TRUE, '2024-01-25 20:00:00'),
('voice-002', 'qr-002', '建议增加职业规划课程，帮助学生明确发展方向。', 'approved', TRUE, '2024-01-26 20:00:00'),
('voice-003', 'qr-003', '希望企业能给应届生更多机会，不要总是要求工作经验。', 'approved', TRUE, '2024-01-27 20:00:00'),
('voice-004', 'qr-004', '学校的就业指导服务需要改进，更贴近实际需求。', 'approved', TRUE, '2024-01-28 20:00:00'),
('voice-005', 'qr-005', '希望社会对年轻人更包容，给我们试错的机会。', 'approved', TRUE, '2024-01-29 20:00:00');

-- 更新AI脱敏统计数据
UPDATE ai_deidentification_stats SET 
    total_processed = 50,
    success_count = 48,
    failure_count = 2,
    avg_response_time = 1200,
    last_used = '2024-01-29 20:00:00'
WHERE provider = 'openai';

UPDATE ai_deidentification_stats SET 
    total_processed = 25,
    success_count = 24,
    failure_count = 1,
    avg_response_time = 1500,
    last_used = '2024-01-29 19:30:00'
WHERE provider = 'grok';
