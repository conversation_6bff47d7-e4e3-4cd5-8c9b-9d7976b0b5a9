/**
 * 数据测试页面
 * 
 * 用于测试前后端数据对接，验证数据转换和API功能
 */

import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '../../components/ui/card';
import { Button } from '../../components/ui/button';
import { Badge } from '../../components/ui/badge';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '../../components/ui/tabs';
import { 
  Database, 
  TestTube, 
  CheckCircle, 
  XCircle, 
  AlertTriangle,
  RefreshCw,
  Activity,
  BarChart,
  Users,
  FileText
} from 'lucide-react';
import { UnifiedDataServiceEnhanced } from '../../services/unifiedDataService.enhanced';

interface TestResult {
  name: string;
  status: 'success' | 'error' | 'warning' | 'pending';
  message: string;
  duration?: number;
  data?: any;
}

interface TestSuite {
  name: string;
  tests: TestResult[];
  status: 'idle' | 'running' | 'completed';
}

const DataTestingPage: React.FC = () => {
  const [testSuites, setTestSuites] = useState<TestSuite[]>([]);
  const [isRunning, setIsRunning] = useState(false);
  const [dataSource, setDataSource] = useState<'mock' | 'api'>('mock');
  const [apiConnection, setApiConnection] = useState<boolean | null>(null);
  const [cacheStatus, setCacheStatus] = useState({ size: 0, keys: [] });

  useEffect(() => {
    initializeTests();
    checkDataSource();
    updateCacheStatus();
  }, []);

  /**
   * 初始化测试套件
   */
  const initializeTests = () => {
    const suites: TestSuite[] = [
      {
        name: '连接测试',
        status: 'idle',
        tests: [
          { name: 'API连接检查', status: 'pending', message: '等待测试' },
          { name: '数据源识别', status: 'pending', message: '等待测试' },
          { name: '环境配置验证', status: 'pending', message: '等待测试' }
        ]
      },
      {
        name: '问卷数据测试',
        status: 'idle',
        tests: [
          { name: '获取问卷统计', status: 'pending', message: '等待测试' },
          { name: '获取问卷列表', status: 'pending', message: '等待测试' },
          { name: '获取问卷详情', status: 'pending', message: '等待测试' },
          { name: '提交问卷回复', status: 'pending', message: '等待测试' }
        ]
      },
      {
        name: '故事数据测试',
        status: 'idle',
        tests: [
          { name: '获取故事列表', status: 'pending', message: '等待测试' },
          { name: '获取故事详情', status: 'pending', message: '等待测试' },
          { name: '提交故事', status: 'pending', message: '等待测试' },
          { name: '故事投票', status: 'pending', message: '等待测试' }
        ]
      },
      {
        name: '用户数据测试',
        status: 'idle',
        tests: [
          { name: '获取用户列表', status: 'pending', message: '等待测试' },
          { name: '用户权限验证', status: 'pending', message: '等待测试' }
        ]
      },
      {
        name: '可视化数据测试',
        status: 'idle',
        tests: [
          { name: '获取可视化数据', status: 'pending', message: '等待测试' },
          { name: '数据格式验证', status: 'pending', message: '等待测试' }
        ]
      }
    ];
    
    setTestSuites(suites);
  };

  /**
   * 检查数据源
   */
  const checkDataSource = async () => {
    const source = UnifiedDataServiceEnhanced.getDataSource();
    setDataSource(source);
    
    const connected = await UnifiedDataServiceEnhanced.checkApiConnection();
    setApiConnection(connected);
  };

  /**
   * 更新缓存状态
   */
  const updateCacheStatus = () => {
    const status = UnifiedDataServiceEnhanced.getCacheStatus();
    setCacheStatus(status);
  };

  /**
   * 运行单个测试
   */
  const runSingleTest = async (suiteIndex: number, testIndex: number): Promise<TestResult> => {
    const suite = testSuites[suiteIndex];
    const test = suite.tests[testIndex];
    const startTime = Date.now();

    try {
      let result: TestResult;

      // 根据测试类型执行不同的测试逻辑
      switch (test.name) {
        case 'API连接检查':
          const connected = await UnifiedDataServiceEnhanced.checkApiConnection();
          result = {
            ...test,
            status: connected ? 'success' : 'warning',
            message: connected ? 'API连接正常' : 'API连接失败，将使用模拟数据',
            duration: Date.now() - startTime
          };
          break;

        case '数据源识别':
          const source = UnifiedDataServiceEnhanced.getDataSource();
          result = {
            ...test,
            status: 'success',
            message: `当前数据源: ${source === 'mock' ? '模拟数据' : '真实API'}`,
            duration: Date.now() - startTime,
            data: { source }
          };
          break;

        case '获取问卷统计':
          const stats = await UnifiedDataServiceEnhanced.getQuestionnaireStats();
          result = {
            ...test,
            status: stats ? 'success' : 'error',
            message: stats ? `获取成功，共 ${stats.totalResponses || 0} 条回复` : '获取失败',
            duration: Date.now() - startTime,
            data: stats
          };
          break;

        case '获取问卷列表':
          const questionnaires = await UnifiedDataServiceEnhanced.getQuestionnaires({ page: 1, pageSize: 5 });
          result = {
            ...test,
            status: questionnaires.success ? 'success' : 'error',
            message: questionnaires.success ? 
              `获取成功，返回 ${questionnaires.data?.length || 0} 条数据` : 
              questionnaires.error || '获取失败',
            duration: Date.now() - startTime,
            data: questionnaires
          };
          break;

        case '获取故事列表':
          const stories = await UnifiedDataServiceEnhanced.getStories({ page: 1, pageSize: 5 });
          result = {
            ...test,
            status: stories.success ? 'success' : 'error',
            message: stories.success ? 
              `获取成功，返回 ${stories.data?.length || 0} 条数据` : 
              stories.error || '获取失败',
            duration: Date.now() - startTime,
            data: stories
          };
          break;

        case '获取可视化数据':
          const vizData = await UnifiedDataServiceEnhanced.getVisualizationData();
          result = {
            ...test,
            status: vizData ? 'success' : 'error',
            message: vizData ? '获取成功' : '获取失败',
            duration: Date.now() - startTime,
            data: vizData
          };
          break;

        default:
          result = {
            ...test,
            status: 'warning',
            message: '测试未实现',
            duration: Date.now() - startTime
          };
      }

      return result;
    } catch (error) {
      return {
        ...test,
        status: 'error',
        message: `测试失败: ${error instanceof Error ? error.message : '未知错误'}`,
        duration: Date.now() - startTime
      };
    }
  };

  /**
   * 运行测试套件
   */
  const runTestSuite = async (suiteIndex: number) => {
    const newSuites = [...testSuites];
    newSuites[suiteIndex].status = 'running';
    setTestSuites(newSuites);

    for (let testIndex = 0; testIndex < newSuites[suiteIndex].tests.length; testIndex++) {
      const result = await runSingleTest(suiteIndex, testIndex);
      newSuites[suiteIndex].tests[testIndex] = result;
      setTestSuites([...newSuites]);
    }

    newSuites[suiteIndex].status = 'completed';
    setTestSuites([...newSuites]);
  };

  /**
   * 运行所有测试
   */
  const runAllTests = async () => {
    setIsRunning(true);
    
    for (let i = 0; i < testSuites.length; i++) {
      await runTestSuite(i);
    }
    
    setIsRunning(false);
    updateCacheStatus();
  };

  /**
   * 清除缓存
   */
  const clearCache = () => {
    UnifiedDataServiceEnhanced.clearCache();
    updateCacheStatus();
  };

  /**
   * 获取状态图标
   */
  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'success':
        return <CheckCircle className="h-4 w-4 text-green-500" />;
      case 'error':
        return <XCircle className="h-4 w-4 text-red-500" />;
      case 'warning':
        return <AlertTriangle className="h-4 w-4 text-yellow-500" />;
      case 'pending':
        return <div className="h-4 w-4 rounded-full bg-gray-300" />;
      default:
        return null;
    }
  };

  /**
   * 获取状态颜色
   */
  const getStatusColor = (status: string) => {
    switch (status) {
      case 'success':
        return 'bg-green-100 text-green-800';
      case 'error':
        return 'bg-red-100 text-red-800';
      case 'warning':
        return 'bg-yellow-100 text-yellow-800';
      case 'pending':
        return 'bg-gray-100 text-gray-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  return (
    <div className="container mx-auto p-6 space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold flex items-center gap-2">
            <TestTube className="h-8 w-8" />
            数据测试中心
          </h1>
          <p className="text-gray-600 mt-2">验证前后端数据对接和API功能</p>
        </div>
        
        <div className="flex items-center gap-4">
          <Button
            onClick={clearCache}
            variant="outline"
            size="sm"
          >
            清除缓存
          </Button>
          
          <Button
            onClick={runAllTests}
            disabled={isRunning}
            className="flex items-center gap-2"
          >
            {isRunning ? (
              <RefreshCw className="h-4 w-4 animate-spin" />
            ) : (
              <Activity className="h-4 w-4" />
            )}
            {isRunning ? '测试中...' : '运行所有测试'}
          </Button>
        </div>
      </div>

      {/* 状态概览 */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center gap-2">
              <Database className="h-5 w-5 text-blue-500" />
              <div>
                <p className="text-sm text-gray-600">数据源</p>
                <p className="font-semibold">
                  {dataSource === 'mock' ? '模拟数据' : '真实API'}
                </p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center gap-2">
              <Activity className="h-5 w-5 text-green-500" />
              <div>
                <p className="text-sm text-gray-600">API状态</p>
                <div className="flex items-center gap-1">
                  {apiConnection === null ? (
                    <Badge variant="secondary">检查中</Badge>
                  ) : apiConnection ? (
                    <Badge className="bg-green-100 text-green-800">正常</Badge>
                  ) : (
                    <Badge variant="destructive">离线</Badge>
                  )}
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center gap-2">
              <BarChart className="h-5 w-5 text-purple-500" />
              <div>
                <p className="text-sm text-gray-600">缓存项目</p>
                <p className="font-semibold">{cacheStatus.size}</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center gap-2">
              <Users className="h-5 w-5 text-orange-500" />
              <div>
                <p className="text-sm text-gray-600">测试状态</p>
                <p className="font-semibold">
                  {isRunning ? '运行中' : '就绪'}
                </p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* 测试套件 */}
      <Tabs defaultValue="0" className="space-y-4">
        <TabsList className="grid w-full grid-cols-5">
          {testSuites.map((suite, index) => (
            <TabsTrigger key={index} value={index.toString()}>
              {suite.name}
            </TabsTrigger>
          ))}
        </TabsList>

        {testSuites.map((suite, suiteIndex) => (
          <TabsContent key={suiteIndex} value={suiteIndex.toString()}>
            <Card>
              <CardHeader>
                <div className="flex items-center justify-between">
                  <CardTitle className="flex items-center gap-2">
                    <FileText className="h-5 w-5" />
                    {suite.name}
                  </CardTitle>
                  <Button
                    onClick={() => runTestSuite(suiteIndex)}
                    disabled={suite.status === 'running'}
                    size="sm"
                  >
                    {suite.status === 'running' ? (
                      <RefreshCw className="h-4 w-4 animate-spin" />
                    ) : (
                      '运行测试'
                    )}
                  </Button>
                </div>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  {suite.tests.map((test, testIndex) => (
                    <div
                      key={testIndex}
                      className="flex items-center justify-between p-3 border rounded-lg"
                    >
                      <div className="flex items-center gap-3">
                        {getStatusIcon(test.status)}
                        <div>
                          <p className="font-medium">{test.name}</p>
                          <p className="text-sm text-gray-600">{test.message}</p>
                        </div>
                      </div>
                      
                      <div className="flex items-center gap-2">
                        {test.duration && (
                          <span className="text-xs text-gray-500">
                            {test.duration}ms
                          </span>
                        )}
                        <Badge className={getStatusColor(test.status)}>
                          {test.status}
                        </Badge>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </TabsContent>
        ))}
      </Tabs>
    </div>
  );
};

export default DataTestingPage;
