/**
 * 测试工具函数
 */

import { vi } from 'vitest';
import { verifyToken } from '../src/utils/auth';

// 模拟 verifyToken 函数
export function mockVerifyToken(valid: boolean, payload?: any) {
  vi.mock('../src/utils/auth', () => ({
    verifyToken: vi.fn().mockReturnValue({
      valid,
      payload: payload || null
    })
  }));
}

// 重置所有模拟
export function resetAllMocks() {
  vi.resetAllMocks();
  vi.clearAllMocks();
}

// 模拟数据库查询结果
export function mockDbQueryResult(mockDb: any, queryResults: Record<string, any>) {
  // 根据SQL查询的开头部分返回不同的结果
  mockDb.prepare.mockImplementation((sql: string) => {
    const key = Object.keys(queryResults).find(k => sql.trim().startsWith(k));
    const result = key ? queryResults[key] : { results: [] };
    
    return {
      bind: vi.fn().mockReturnThis(),
      first: vi.fn().mockResolvedValue(result.single || null),
      all: vi.fn().mockResolvedValue({ results: result.multiple || [] }),
      run: vi.fn().mockResolvedValue({ success: true, meta: { last_row_id: 1 } })
    };
  });
}

// 创建模拟用户数据
export function createMockUser(role: 'admin' | 'superadmin' | 'reviewer', id: number = 1) {
  return {
    id,
    username: `${role}1`,
    name: `${role.charAt(0).toUpperCase() + role.slice(1)} User`,
    role,
    status: 'active',
    last_login: '2024-07-01T08:00:00Z',
    created_at: '2024-06-01T08:00:00Z'
  };
}

// 创建模拟故事数据
export function createMockStory(id: number = 1) {
  return {
    id,
    sequence_number: `S${id.toString().padStart(5, '0')}`,
    title: `测试故事 ${id}`,
    content: `这是测试故事内容 ${id}`,
    is_anonymous: 1,
    identity_a_hash: 'hash1',
    identity_b_hash: 'hash2',
    status: 'pending',
    reviewer_id: null,
    reviewed_at: null,
    rejection_reason: null,
    created_at: '2024-07-01T08:00:00Z',
    updated_at: '2024-07-01T08:00:00Z'
  };
}

// 创建模拟问卷数据
export function createMockSurvey(id: number = 1) {
  return {
    id,
    sequence_number: `Q${id.toString().padStart(5, '0')}`,
    title: `测试问卷 ${id}`,
    description: `这是测试问卷描述 ${id}`,
    status: 'active',
    created_at: '2024-07-01T08:00:00Z',
    updated_at: '2024-07-01T08:00:00Z'
  };
}

// 创建模拟问题数据
export function createMockQuestion(id: number = 1, surveyId: number = 1) {
  return {
    id,
    survey_id: surveyId,
    type: id % 3 === 0 ? 'text' : id % 3 === 1 ? 'radio' : 'checkbox',
    question: `测试问题 ${id}`,
    options: id % 3 === 0 ? null : JSON.stringify(['选项1', '选项2', '选项3']),
    required: id % 2 === 0 ? 1 : 0,
    order_index: id - 1
  };
}

// 创建模拟问卷回复数据
export function createMockSurveyResponse(id: number = 1, surveyId: number = 1) {
  return {
    id,
    survey_id: surveyId,
    survey_title: `测试问卷 ${surveyId}`,
    survey_sequence_number: `Q${surveyId.toString().padStart(5, '0')}`,
    is_anonymous: 1,
    identity_a_hash: 'hash1',
    identity_b_hash: 'hash2',
    status: 'pending',
    reviewer_id: null,
    reviewed_at: null,
    rejection_reason: null,
    created_at: '2024-07-01T08:00:00Z'
  };
}

// 创建模拟回答数据
export function createMockAnswer(id: number = 1, responseId: number = 1, questionId: number = 1) {
  return {
    id,
    response_id: responseId,
    question_id: questionId,
    question: `测试问题 ${questionId}`,
    type: id % 3 === 0 ? 'text' : id % 3 === 1 ? 'radio' : 'checkbox',
    answer: id % 3 === 0 ? '这是文本回答' : 
            id % 3 === 1 ? '选项1' : 
            JSON.stringify(['选项1', '选项3'])
  };
}

// 创建模拟标签数据
export function createMockTag(id: number = 1) {
  return {
    id,
    name: `标签${id}`,
    description: `这是标签${id}的描述`,
    created_at: '2024-07-01T08:00:00Z'
  };
}

// 创建模拟审核记录数据
export function createMockReviewRecord(id: number = 1, reviewerId: number = 1) {
  return {
    id,
    reviewer_id: reviewerId,
    content_id: id,
    content_type: id % 2 === 0 ? 'story' : 'survey_response',
    decision: id % 3 === 0 ? 'rejected' : 'verified',
    review_duration: 60,
    created_at: '2024-07-01T08:00:00Z'
  };
}
