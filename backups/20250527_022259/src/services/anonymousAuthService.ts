/**
 * 匿名身份验证服务
 *
 * 提供基于A+B组合的匿名身份验证功能
 */

import { apiClient } from './apiClient';

// 模拟数据 - 故事
const mockStories = [
  {
    id: 1,
    title: '我的求职经历',
    content: '毕业后，我投了近50份简历，参加了10多场面试，最终在一家互联网公司找到了合适的工作。这个过程让我明白，技术能力很重要，但沟通能力和学习态度同样重要。',
    status: 'approved',
    createdAt: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000).toISOString(), // 30天前
    updatedAt: new Date(Date.now() - 29 * 24 * 60 * 60 * 1000).toISOString(),
    likes: 24,
    dislikes: 2
  },
  {
    id: 2,
    title: '实习经验分享',
    content: '大三暑假，我在一家创业公司实习了两个月。虽然工资不高，但学到了很多实用技能，也让我对行业有了更清晰的认识。建议学弟学妹们一定要重视实习机会。',
    status: 'pending',
    createdAt: new Date(Date.now() - 15 * 24 * 60 * 60 * 1000).toISOString(), // 15天前
    updatedAt: new Date(Date.now() - 15 * 24 * 60 * 60 * 1000).toISOString(),
    likes: 0,
    dislikes: 0
  },
  {
    id: 3,
    title: '考研vs就业的思考',
    content: '毕业前，我纠结了很久是考研还是直接就业。最终我选择了就业，因为我所在的行业更看重实际经验。但我认为这是个人选择，没有标准答案，要根据自己的情况决定。',
    status: 'approved',
    createdAt: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000).toISOString(), // 7天前
    updatedAt: new Date(Date.now() - 6 * 24 * 60 * 60 * 1000).toISOString(),
    likes: 15,
    dislikes: 3
  },
  {
    id: 4,
    title: '毕业后的职业规划',
    content: '毕业后我选择了一家大型国企工作，虽然薪资不如互联网企业，但工作稳定，有更多时间思考长期发展。三年后我转行进入了教育行业，现在很满意这个选择。职业规划需要结合自身兴趣和市场需求。',
    status: 'approved',
    createdAt: new Date(Date.now() - 20 * 24 * 60 * 60 * 1000).toISOString(), // 20天前
    updatedAt: new Date(Date.now() - 19 * 24 * 60 * 60 * 1000).toISOString(),
    likes: 32,
    dislikes: 1
  },
  {
    id: 5,
    title: '跨专业就业的经验',
    content: '我本科学的是生物工程，但毕业后发现就业前景不理想。通过自学编程，最终成功转行到IT行业。跨专业就业最重要的是找到自己的兴趣点，然后持续学习相关技能，同时要善于展示自己的学习能力和解决问题的能力。',
    status: 'approved',
    createdAt: new Date(Date.now() - 12 * 24 * 60 * 60 * 1000).toISOString(), // 12天前
    updatedAt: new Date(Date.now() - 11 * 24 * 60 * 60 * 1000).toISOString(),
    likes: 18,
    dislikes: 2
  }
];

// 模拟数据 - 问卷回复
const mockQuestionnaireResponses = [
  {
    id: 101,
    createdAt: new Date(Date.now() - 45 * 24 * 60 * 60 * 1000).toISOString(), // 45天前
    updatedAt: new Date(Date.now() - 45 * 24 * 60 * 60 * 1000).toISOString(),
    educationLevel: '本科',
    major: '计算机科学与技术',
    graduationYear: 2023,
    region: '北京',
    employmentStatus: '已就业'
  },
  {
    id: 102,
    createdAt: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000).toISOString(), // 30天前
    updatedAt: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000).toISOString(),
    educationLevel: '硕士',
    major: '软件工程',
    graduationYear: 2022,
    region: '上海',
    employmentStatus: '已就业'
  },
  {
    id: 103,
    createdAt: new Date(Date.now() - 15 * 24 * 60 * 60 * 1000).toISOString(), // 15天前
    updatedAt: new Date(Date.now() - 15 * 24 * 60 * 60 * 1000).toISOString(),
    educationLevel: '本科',
    major: '市场营销',
    graduationYear: 2023,
    region: '广州',
    employmentStatus: '求职中'
  }
];

// 模拟数据 - 待审核内容
const mockPendingContents = [
  {
    id: 201,
    sequenceNumber: 'S-2024-0042',
    type: 'story',
    status: 'pending',
    createdAt: new Date(Date.now() - 2 * 24 * 60 * 60 * 1000).toISOString(), // 2天前
    updatedAt: new Date(Date.now() - 2 * 24 * 60 * 60 * 1000).toISOString()
  },
  {
    id: 202,
    sequenceNumber: 'Q-2024-0078',
    type: 'questionnaire',
    status: 'pending',
    createdAt: new Date(Date.now() - 1 * 24 * 60 * 60 * 1000).toISOString(), // 1天前
    updatedAt: new Date(Date.now() - 1 * 24 * 60 * 60 * 1000).toISOString()
  }
];

// 模拟的有效A+B组合
const validCombinations = [
  { a: '13800138000', b: '1234' },
  { a: '13900139000', b: '123456' },
  { a: '12345678901', b: '6789' }, // 测试用组合
  { a: '18888888888', b: '1234' },
  { a: '19999999999', b: '123456' }
];

/**
 * 获取我的内容
 * @param identityA A值（11位数字）
 * @param identityB B值（4位或6位数字）
 * @returns 内容列表
 */
export async function getMyContent(identityA: string, identityB: string) {
  try {
    // 检查是否为本地开发环境
    if (import.meta.env.DEV) {
      console.log('使用模拟数据');

      // 验证A+B组合
      const isValid = validCombinations.some(
        combo => combo.a === identityA && combo.b === identityB
      );

      // 如果是有效组合，返回模拟数据
      if (isValid) {
        // 模拟网络延迟
        await new Promise(resolve => setTimeout(resolve, 500));

        return {
          success: true,
          data: {
            stories: mockStories,
            questionnaireResponses: mockQuestionnaireResponses,
            pendingContents: mockPendingContents
          }
        };
      } else {
        // 模拟验证失败
        await new Promise(resolve => setTimeout(resolve, 500));

        return {
          success: false,
          error: 'A+B组合无效或未找到相关内容'
        };
      }
    }

    // 生产环境使用真实API
    const response = await apiClient.get('/anonymous-auth/my-content', {
      params: { a: identityA, b: identityB }
    });

    return response.data;
  } catch (error) {
    console.error('获取我的内容失败:', error);

    // 在开发环境中，如果API请求失败，返回模拟数据
    if (import.meta.env.DEV) {
      console.log('API请求失败，使用模拟数据');

      return {
        success: true,
        data: {
          stories: mockStories,
          questionnaireResponses: mockQuestionnaireResponses,
          pendingContents: mockPendingContents
        }
      };
    }

    throw error;
  }
}

/**
 * 删除故事
 * @param identityA A值（11位数字）
 * @param identityB B值（4位或6位数字）
 * @param storyId 故事ID
 * @returns 操作结果
 */
export async function deleteStory(identityA: string, identityB: string, storyId: number) {
  try {
    // 检查是否为本地开发环境
    if (import.meta.env.DEV) {
      console.log('使用模拟数据删除故事');

      // 验证A+B组合
      const isValid = validCombinations.some(
        combo => combo.a === identityA && combo.b === identityB
      );

      // 模拟网络延迟
      await new Promise(resolve => setTimeout(resolve, 500));

      if (isValid) {
        // 从模拟数据中查找故事
        const storyIndex = mockStories.findIndex(story => story.id === storyId);

        if (storyIndex !== -1) {
          // 从数组中移除故事（实际应用中会保留在数组中，但在UI中不显示）
          mockStories.splice(storyIndex, 1);

          return {
            success: true,
            message: '故事已成功删除'
          };
        } else {
          return {
            success: false,
            error: '未找到指定的故事'
          };
        }
      } else {
        return {
          success: false,
          error: 'A+B组合无效，无权删除此内容'
        };
      }
    }

    // 生产环境使用真实API
    const response = await apiClient.delete(`/anonymous-auth/story/${storyId}`, {
      params: { a: identityA, b: identityB }
    });

    return response.data;
  } catch (error) {
    console.error('删除故事失败:', error);

    // 在开发环境中，如果API请求失败，返回模拟成功响应
    if (import.meta.env.DEV) {
      console.log('API请求失败，使用模拟数据');

      return {
        success: true,
        message: '故事已成功删除（模拟）'
      };
    }

    throw error;
  }
}

/**
 * 删除问卷回复
 * @param identityA A值（11位数字）
 * @param identityB B值（4位或6位数字）
 * @param responseId 问卷回复ID
 * @returns 操作结果
 */
export async function deleteQuestionnaireResponse(identityA: string, identityB: string, responseId: number) {
  try {
    // 检查是否为本地开发环境
    if (import.meta.env.DEV) {
      console.log('使用模拟数据删除问卷回复');

      // 验证A+B组合
      const isValid = validCombinations.some(
        combo => combo.a === identityA && combo.b === identityB
      );

      // 模拟网络延迟
      await new Promise(resolve => setTimeout(resolve, 500));

      if (isValid) {
        // 从模拟数据中查找问卷回复
        const responseIndex = mockQuestionnaireResponses.findIndex(response => response.id === responseId);

        if (responseIndex !== -1) {
          // 从数组中移除问卷回复
          mockQuestionnaireResponses.splice(responseIndex, 1);

          return {
            success: true,
            message: '问卷回复已成功删除'
          };
        } else {
          return {
            success: false,
            error: '未找到指定的问卷回复'
          };
        }
      } else {
        return {
          success: false,
          error: 'A+B组合无效，无权删除此内容'
        };
      }
    }

    // 生产环境使用真实API
    const response = await apiClient.delete(`/anonymous-auth/questionnaire/${responseId}`, {
      params: { a: identityA, b: identityB }
    });

    return response.data;
  } catch (error) {
    console.error('删除问卷回复失败:', error);

    // 在开发环境中，如果API请求失败，返回模拟成功响应
    if (import.meta.env.DEV) {
      console.log('API请求失败，使用模拟数据');

      return {
        success: true,
        message: '问卷回复已成功删除（模拟）'
      };
    }

    throw error;
  }
}

/**
 * 验证A+B组合是否有效
 * @param identityA A值（11位数字）
 * @param identityB B值（4位或6位数字）
 * @returns 是否有效
 */
export function validateIdentity(identityA: string, identityB: string): boolean {
  // 验证A是否有效（11位数字）
  const isValidA = /^\d{11}$/.test(identityA);

  // 验证B是否有效（4位或6位数字）
  const isValidB = /^\d{4}$/.test(identityB) || /^\d{6}$/.test(identityB);

  return isValidA && isValidB;
}
