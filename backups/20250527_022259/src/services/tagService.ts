/**
 * 标签服务
 * 
 * 提供标签相关的功能
 */

import { fetchData } from './unifiedDataService';

/**
 * 获取标签列表
 * @param params 请求参数
 * @returns 标签列表
 */
export async function getTags(params: any = {}) {
  return fetchData(
    '/tags',
    'getTagsMock',
    params,
    { method: 'GET' }
  );
}

/**
 * 获取标签详情
 * @param id 标签ID
 * @returns 标签详情
 */
export async function getTagDetail(id: number) {
  return fetchData(
    `/tags/${id}`,
    'getTagDetailMock',
    { id },
    { method: 'GET' }
  );
}

/**
 * 创建标签
 * @param tagData 标签数据
 * @returns 创建结果
 */
export async function createTag(tagData: {
  name: string;
  color: string;
  priority: number;
  category: string;
}) {
  return fetchData(
    '/tags',
    'createTagMock',
    tagData,
    { method: 'POST' }
  );
}

/**
 * 更新标签
 * @param id 标签ID
 * @param tagData 标签数据
 * @returns 更新结果
 */
export async function updateTag(
  id: number,
  tagData: {
    name?: string;
    color?: string;
    priority?: number;
    category?: string;
  }
) {
  return fetchData(
    `/tags/${id}`,
    'updateTagMock',
    { id, ...tagData },
    { method: 'PUT' }
  );
}

/**
 * 删除标签
 * @param id 标签ID
 * @returns 删除结果
 */
export async function deleteTag(id: number) {
  return fetchData(
    `/tags/${id}`,
    'deleteTagMock',
    { id },
    { method: 'DELETE' }
  );
}
