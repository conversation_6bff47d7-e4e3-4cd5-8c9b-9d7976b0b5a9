/**
 * 故事墙API实现
 * 
 * 核心功能：
 * 1. 用户UUID管理
 * 2. 故事内容ID管理  
 * 3. 标签ID关联
 * 4. KV+D1+R2混合架构
 * 5. PNG导出功能预留
 */

import { Hono } from 'hono';
import { z } from 'zod';
import { v4 as uuidv4 } from 'uuid';

// 环境类型定义
interface Env {
  DB: D1Database;
  KV: KVNamespace;
  R2: R2Bucket;
  ENVIRONMENT: 'development' | 'production';
}

// 数据验证Schema
const CreateStorySchema = z.object({
  title: z.string().min(1).max(200),
  content: z.string().min(10).max(5000),
  category: z.string(),
  educationLevel: z.string(),
  industry: z.string(),
  tags: z.array(z.string()).max(10),
  metadata: z.object({
    source: z.enum(['web', 'mobile', 'api']).optional(),
    userAgent: z.string().optional(),
    location: z.string().optional(),
  }).optional(),
});

const PNGExportSchema = z.object({
  template: z.enum(['card', 'poster', 'story', 'custom']),
  style: z.object({
    theme: z.enum(['light', 'dark', 'gradient']),
    colorScheme: z.string(),
    font: z.enum(['default', 'serif', 'sans']),
    size: z.enum(['square', 'portrait', 'landscape']),
  }),
  content: z.object({
    includeAuthor: z.boolean(),
    includeStats: z.boolean(),
    includeTags: z.boolean(),
    includeQR: z.boolean(),
    watermark: z.boolean(),
  }),
  branding: z.object({
    logo: z.boolean(),
    website: z.boolean(),
    customText: z.string().optional(),
  }),
});

// UUID生成工具
class UUIDManager {
  static generateUserUUID(type: 'anonymous' | 'registered' = 'anonymous'): string {
    const timestamp = Date.now();
    const random = Math.random().toString(36).substring(2, 8);
    const prefix = type === 'anonymous' ? 'anon' : 'user';
    return `${prefix}-${timestamp}-${random}`;
  }

  static generateContentID(): string {
    const timestamp = Date.now();
    const random = Math.random().toString(36).substring(2, 8);
    return `txid-${timestamp}-${random}`;
  }

  static generateTagID(category: string, name: string): string {
    const nameHash = name.toLowerCase().replace(/[^a-z0-9]/g, '');
    return `tag-${category}-${nameHash}`;
  }

  static validateUUID(uuid: string, type?: string): boolean {
    if (type) {
      return uuid.startsWith(`${type}-`);
    }
    return /^(anon|user|sys)-\d+-[a-z0-9]+$/.test(uuid);
  }

  static validateContentID(contentId: string): boolean {
    return /^txid-\d+-[a-z0-9]+$/.test(contentId);
  }
}

// 缓存管理工具
class CacheManager {
  constructor(private kv: KVNamespace) {}

  async getStoryList(cacheKey: string): Promise<any> {
    return await this.kv.get(cacheKey, 'json');
  }

  async setStoryList(cacheKey: string, data: any, ttl: number = 300): Promise<void> {
    await this.kv.put(cacheKey, JSON.stringify(data), { expirationTtl: ttl });
  }

  async invalidatePattern(pattern: string): Promise<void> {
    // 注意：KV不支持模式删除，需要维护键列表
    const keys = await this.kv.list({ prefix: pattern.replace('*', '') });
    for (const key of keys.keys) {
      await this.kv.delete(key.name);
    }
  }

  generateCacheKey(prefix: string, params: Record<string, any>): string {
    const sortedParams = Object.keys(params)
      .sort()
      .map(key => `${key}:${params[key]}`)
      .join('|');
    return `${prefix}:${Buffer.from(sortedParams).toString('base64')}`;
  }
}

// 数据库操作工具
class DatabaseManager {
  constructor(private db: D1Database) {}

  async ensureUser(userUuid?: string): Promise<{ uuid: string; isNew: boolean }> {
    if (!userUuid) {
      userUuid = UUIDManager.generateUserUUID('anonymous');
    }

    if (!UUIDManager.validateUUID(userUuid)) {
      throw new Error('Invalid user UUID format');
    }

    // 检查用户是否存在
    const existingUser = await this.db
      .prepare('SELECT uuid FROM users WHERE uuid = ?')
      .bind(userUuid)
      .first();

    if (existingUser) {
      return { uuid: userUuid, isNew: false };
    }

    // 创建新用户
    await this.db
      .prepare(`
        INSERT INTO users (uuid, type, created_at) 
        VALUES (?, ?, CURRENT_TIMESTAMP)
      `)
      .bind(userUuid, userUuid.startsWith('anon-') ? 'anonymous' : 'registered')
      .run();

    return { uuid: userUuid, isNew: true };
  }

  async createStory(storyData: any, userUuid: string): Promise<string> {
    const contentId = UUIDManager.generateContentID();

    // 开始事务
    const transaction = this.db.batch([
      // 插入故事
      this.db.prepare(`
        INSERT INTO stories (
          content_id, user_uuid, title, content, category, 
          education_level, industry, status, created_at
        ) VALUES (?, ?, ?, ?, ?, ?, ?, 'pending', CURRENT_TIMESTAMP)
      `).bind(
        contentId, userUuid, storyData.title, storyData.content,
        storyData.category, storyData.educationLevel, storyData.industry
      ),

      // 关联标签
      ...storyData.tags.map((tagId: string) =>
        this.db.prepare(`
          INSERT OR IGNORE INTO story_tags (story_content_id, tag_id) 
          VALUES (?, ?)
        `).bind(contentId, tagId)
      ),

      // 更新标签使用统计
      ...storyData.tags.map((tagId: string) =>
        this.db.prepare(`
          UPDATE tags SET usage_count = usage_count + 1 
          WHERE tag_id = ?
        `).bind(tagId)
      )
    ]);

    await transaction;
    return contentId;
  }

  async getStoryList(filters: any, pagination: any): Promise<any> {
    let query = `
      SELECT 
        s.content_id,
        s.title,
        s.content,
        s.user_uuid,
        s.category,
        s.education_level,
        s.industry,
        s.likes_count,
        s.views_count,
        s.created_at,
        GROUP_CONCAT(t.name) as tags
      FROM stories s
      LEFT JOIN story_tags st ON s.content_id = st.story_content_id
      LEFT JOIN tags t ON st.tag_id = t.tag_id
      WHERE s.status = 'approved'
    `;

    const params: any[] = [];

    // 添加筛选条件
    if (filters.category && filters.category !== 'all') {
      query += ' AND s.category = ?';
      params.push(filters.category);
    }

    if (filters.search) {
      query += ' AND (s.title LIKE ? OR s.content LIKE ?)';
      params.push(`%${filters.search}%`, `%${filters.search}%`);
    }

    if (filters.tags && filters.tags.length > 0) {
      const tagPlaceholders = filters.tags.map(() => '?').join(',');
      query += ` AND s.content_id IN (
        SELECT story_content_id FROM story_tags 
        WHERE tag_id IN (${tagPlaceholders})
      )`;
      params.push(...filters.tags);
    }

    // 分组和排序
    query += ' GROUP BY s.content_id';
    
    if (filters.sort === 'popular') {
      query += ' ORDER BY s.likes_count DESC, s.views_count DESC';
    } else {
      query += ' ORDER BY s.created_at DESC';
    }

    // 分页
    query += ' LIMIT ? OFFSET ?';
    params.push(pagination.limit, (pagination.page - 1) * pagination.limit);

    const result = await this.db.prepare(query).bind(...params).all();
    return result.results;
  }
}

// 主API路由
const app = new Hono<{ Bindings: Env }>();

// 获取故事列表
app.get('/stories', async (c) => {
  try {
    const query = c.req.query();
    const filters = {
      category: query.category || 'all',
      search: query.search || '',
      tags: query.tags ? query.tags.split(',') : [],
      sort: query.sort || 'latest',
    };
    const pagination = {
      page: parseInt(query.page || '1'),
      limit: parseInt(query.limit || '20'),
    };

    const cacheManager = new CacheManager(c.env.KV);
    const cacheKey = cacheManager.generateCacheKey('stories', { ...filters, ...pagination });

    // 尝试从缓存获取
    let stories = await cacheManager.getStoryList(cacheKey);
    
    if (!stories) {
      const dbManager = new DatabaseManager(c.env.DB);
      stories = await dbManager.getStoryList(filters, pagination);
      
      // 缓存结果
      await cacheManager.setStoryList(cacheKey, stories, 300);
    }

    return c.json({
      success: true,
      data: {
        stories,
        pagination,
        filters,
      },
      meta: {
        cacheKey,
        generatedAt: new Date().toISOString(),
        source: stories ? 'cache' : 'database',
      },
    });

  } catch (error) {
    console.error('Error fetching stories:', error);
    return c.json({
      success: false,
      error: 'Failed to fetch stories',
      details: error instanceof Error ? error.message : 'Unknown error',
    }, 500);
  }
});

// 创建故事
app.post('/stories', async (c) => {
  try {
    const body = await c.req.json();
    const validatedData = CreateStorySchema.parse(body);
    
    const userUuid = c.req.header('X-User-UUID');
    
    const dbManager = new DatabaseManager(c.env.DB);
    const user = await dbManager.ensureUser(userUuid);
    
    const contentId = await dbManager.createStory(validatedData, user.uuid);
    
    // 清除相关缓存
    const cacheManager = new CacheManager(c.env.KV);
    await cacheManager.invalidatePattern('stories:*');
    await cacheManager.invalidatePattern('tags:hot:*');

    return c.json({
      success: true,
      data: {
        contentId,
        userUuid: user.uuid,
        status: 'pending',
        estimatedReviewTime: '24小时内',
      },
      message: '故事提交成功，等待审核',
    });

  } catch (error) {
    console.error('Error creating story:', error);
    return c.json({
      success: false,
      error: 'Failed to create story',
      details: error instanceof Error ? error.message : 'Unknown error',
    }, 500);
  }
});

// PNG导出功能（预留）
app.post('/stories/:contentId/export/png', async (c) => {
  try {
    const contentId = c.req.param('contentId');
    const userUuid = c.req.header('X-User-UUID');
    
    if (!UUIDManager.validateContentID(contentId)) {
      return c.json({ success: false, error: 'Invalid content ID' }, 400);
    }

    const body = await c.req.json();
    const exportOptions = PNGExportSchema.parse(body);

    // 生成导出ID
    const exportId = `export-${Date.now()}-${Math.random().toString(36).substring(2)}`;

    // TODO: 实现PNG生成逻辑
    // 1. 从数据库获取故事数据
    // 2. 根据模板生成HTML
    // 3. 使用Puppeteer或类似工具生成PNG
    // 4. 上传到R2存储
    // 5. 返回下载链接

    return c.json({
      success: true,
      data: {
        exportId,
        status: 'processing',
        estimatedTime: 30,
      },
      message: 'PNG导出任务已创建',
    });

  } catch (error) {
    console.error('Error creating PNG export:', error);
    return c.json({
      success: false,
      error: 'Failed to create PNG export',
      details: error instanceof Error ? error.message : 'Unknown error',
    }, 500);
  }
});

export default app;
