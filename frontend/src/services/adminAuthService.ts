/**
 * 管理员认证服务
 *
 * 提供管理员登录和权限验证功能
 */

import { fetchData } from './unifiedDataService';

/**
 * 管理员登录
 * @param username 用户名
 * @param password 密码
 * @returns 登录结果
 */
export async function adminLogin(username: string, password: string) {
  // 使用正确的API URL构建
  const API_BASE_URL = import.meta.env.VITE_API_BASE_URL;
  const url = API_BASE_URL ? `${API_BASE_URL}/api/admin/login` : '/api/admin/login';

  console.log('管理员登录API URL:', url);

  const response = await fetch(url, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    body: JSON.stringify({ username, password }),
  });

  if (!response.ok) {
    const errorText = await response.text();
    console.error(`HTTP错误 ${response.status}:`, errorText);
    throw new Error('登录失败');
  }

  return await response.json();
}

/**
 * 获取待审核故事列表
 * @returns 待审核故事列表
 */
export async function getPendingStories() {
  return fetchData(
    '/admin/stories/pending',
    'getPendingStoriesMock',
    {},
    { method: 'GET' }
  );
}

/**
 * 获取已审核故事列表
 * @returns 已审核故事列表
 */
export async function getReviewedStories() {
  return fetchData(
    '/admin/stories/reviewed',
    'getReviewedStoriesMock',
    {},
    { method: 'GET' }
  );
}

/**
 * 获取标签列表
 * @returns 标签列表
 */
export async function getTags() {
  return fetchData(
    '/admin/tags',
    'getTagsMock',
    {},
    { method: 'GET' }
  );
}
