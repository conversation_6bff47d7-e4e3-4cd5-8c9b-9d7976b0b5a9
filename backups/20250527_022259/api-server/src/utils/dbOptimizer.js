/**
 * 数据库查询优化工具
 * 提供查询缓存、批量操作和性能监控功能
 */

const { recordDbQuery } = require('./performance');
const { getCache, setCache } = require('./cache');

// 缓存配置
const DEFAULT_CACHE_TTL = 300; // 默认缓存时间（秒）
const CACHE_NAMESPACE = 'db-queries';

/**
 * 执行优化的数据库查询
 * @param {D1Database} db D1数据库实例
 * @param {string} query SQL查询语句
 * @param {Array} params 查询参数
 * @param {Object} options 选项
 * @returns {Promise<Object>} 查询结果
 */
async function executeQuery(db, query, params = [], options = {}) {
  const {
    useCache = false,
    cacheTTL = DEFAULT_CACHE_TTL,
    env = null,
    measurePerformance = true,
    logQuery = false
  } = options;
  
  // 记录查询开始时间
  const startTime = Date.now();
  
  try {
    // 如果启用缓存，尝试从缓存获取结果
    if (useCache && env) {
      const cacheKey = generateCacheKey(query, params);
      const cachedResult = await getCache(env, CACHE_NAMESPACE, cacheKey);
      
      if (cachedResult) {
        // 记录查询性能（从缓存）
        if (measurePerformance) {
          const endTime = Date.now();
          recordDbQuery(`${query} [CACHED]`, endTime - startTime);
        }
        
        if (logQuery) {
          console.log(`[DB] Cached query: ${query}`, params);
        }
        
        return cachedResult;
      }
    }
    
    // 执行查询
    let result;
    if (query.trim().toUpperCase().startsWith('SELECT')) {
      // 查询操作
      result = await db.prepare(query).bind(...params).all();
    } else {
      // 修改操作
      result = await db.prepare(query).bind(...params).run();
    }
    
    // 如果启用缓存，缓存结果（仅对SELECT查询）
    if (useCache && env && query.trim().toUpperCase().startsWith('SELECT')) {
      const cacheKey = generateCacheKey(query, params);
      await setCache(env, CACHE_NAMESPACE, cacheKey, result, cacheTTL);
    }
    
    // 记录查询性能
    if (measurePerformance) {
      const endTime = Date.now();
      recordDbQuery(query, endTime - startTime);
    }
    
    if (logQuery) {
      console.log(`[DB] Query: ${query}`, params);
    }
    
    return result;
  } catch (error) {
    // 记录查询错误
    console.error(`[DB] Query error: ${query}`, params, error);
    
    // 记录查询性能（错误）
    if (measurePerformance) {
      const endTime = Date.now();
      recordDbQuery(`${query} [ERROR]`, endTime - startTime);
    }
    
    throw error;
  }
}

/**
 * 执行批量查询
 * @param {D1Database} db D1数据库实例
 * @param {Array} queries 查询数组，每个元素包含query和params
 * @param {Object} options 选项
 * @returns {Promise<Array>} 查询结果数组
 */
async function executeBatchQueries(db, queries, options = {}) {
  const {
    useTransaction = true,
    measurePerformance = true,
    logQuery = false
  } = options;
  
  // 记录批量查询开始时间
  const startTime = Date.now();
  
  try {
    let results = [];
    
    if (useTransaction) {
      // 使用事务执行批量查询
      await db.exec('BEGIN TRANSACTION');
      
      try {
        for (const { query, params = [] } of queries) {
          const result = await executeQuery(db, query, params, {
            ...options,
            measurePerformance: false // 避免重复测量
          });
          
          results.push(result);
        }
        
        await db.exec('COMMIT');
      } catch (error) {
        // 回滚事务
        await db.exec('ROLLBACK');
        throw error;
      }
    } else {
      // 不使用事务执行批量查询
      for (const { query, params = [] } of queries) {
        const result = await executeQuery(db, query, params, {
          ...options,
          measurePerformance: false // 避免重复测量
        });
        
        results.push(result);
      }
    }
    
    // 记录批量查询性能
    if (measurePerformance) {
      const endTime = Date.now();
      recordDbQuery(`Batch Queries (${queries.length})`, endTime - startTime);
    }
    
    if (logQuery) {
      console.log(`[DB] Batch Queries (${queries.length})`);
    }
    
    return results;
  } catch (error) {
    // 记录批量查询错误
    console.error(`[DB] Batch Queries Error (${queries.length})`, error);
    
    // 记录批量查询性能（错误）
    if (measurePerformance) {
      const endTime = Date.now();
      recordDbQuery(`Batch Queries (${queries.length}) [ERROR]`, endTime - startTime);
    }
    
    throw error;
  }
}

/**
 * 执行分页查询
 * @param {D1Database} db D1数据库实例
 * @param {string} baseQuery 基础SQL查询语句（不包含LIMIT和OFFSET）
 * @param {string} countQuery 计数SQL查询语句（用于获取总记录数）
 * @param {Array} params 查询参数
 * @param {Object} options 选项
 * @returns {Promise<Object>} 分页查询结果
 */
async function executePaginatedQuery(db, baseQuery, countQuery, params = [], options = {}) {
  const {
    page = 1,
    pageSize = 10,
    useCache = false,
    cacheTTL = DEFAULT_CACHE_TTL,
    env = null,
    measurePerformance = true,
    logQuery = false
  } = options;
  
  // 记录分页查询开始时间
  const startTime = Date.now();
  
  try {
    // 计算分页参数
    const offset = (page - 1) * pageSize;
    const limit = pageSize;
    
    // 构建完整查询
    const fullQuery = `${baseQuery} LIMIT ? OFFSET ?`;
    const fullParams = [...params, limit, offset];
    
    // 并行执行数据查询和计数查询
    const [dataResult, countResult] = await Promise.all([
      executeQuery(db, fullQuery, fullParams, {
        useCache,
        cacheTTL,
        env,
        measurePerformance: false, // 避免重复测量
        logQuery
      }),
      executeQuery(db, countQuery, params, {
        useCache,
        cacheTTL,
        env,
        measurePerformance: false, // 避免重复测量
        logQuery
      })
    ]);
    
    // 计算分页信息
    const totalItems = countResult.results[0]?.count || 0;
    const totalPages = Math.ceil(totalItems / pageSize);
    
    // 构建结果
    const result = {
      data: dataResult.results,
      pagination: {
        page: Number(page),
        pageSize: Number(pageSize),
        totalItems: Number(totalItems),
        totalPages: Number(totalPages),
        hasNext: page < totalPages,
        hasPrev: page > 1
      }
    };
    
    // 记录分页查询性能
    if (measurePerformance) {
      const endTime = Date.now();
      recordDbQuery(`Paginated Query: ${baseQuery}`, endTime - startTime);
    }
    
    return result;
  } catch (error) {
    // 记录分页查询错误
    console.error(`[DB] Paginated Query Error: ${baseQuery}`, params, error);
    
    // 记录分页查询性能（错误）
    if (measurePerformance) {
      const endTime = Date.now();
      recordDbQuery(`Paginated Query: ${baseQuery} [ERROR]`, endTime - startTime);
    }
    
    throw error;
  }
}

/**
 * 生成缓存键
 * @param {string} query SQL查询语句
 * @param {Array} params 查询参数
 * @returns {string} 缓存键
 */
function generateCacheKey(query, params) {
  return `${query}:${JSON.stringify(params)}`;
}

/**
 * 清除查询缓存
 * @param {Object} env 环境对象
 * @param {string} pattern 缓存键模式（可选）
 * @returns {Promise<number>} 清除的缓存项数量
 */
async function clearQueryCache(env, pattern = null) {
  if (!env) {
    return 0;
  }
  
  if (pattern) {
    // 清除匹配模式的缓存
    const cacheKeys = Object.keys(await getCache(env, CACHE_NAMESPACE, '__all_keys__') || {})
      .filter(key => key.includes(pattern));
    
    let count = 0;
    for (const key of cacheKeys) {
      await deleteCache(env, CACHE_NAMESPACE, key);
      count++;
    }
    
    return count;
  } else {
    // 清除所有查询缓存
    return clearNamespaceCache(env, CACHE_NAMESPACE);
  }
}

module.exports = {
  executeQuery,
  executeBatchQueries,
  executePaginatedQuery,
  clearQueryCache
};
