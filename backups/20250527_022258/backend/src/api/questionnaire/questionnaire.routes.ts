/**
 * 问卷路由
 *
 * 处理问卷相关的路由
 */

import { Hono } from 'hono';
import { Env } from '../../types';
import * as questionnaireController from './questionnaire.controller';
import { rateLimit } from '../../middlewares/rateLimit';
import { questionnaireModeration } from '../../middlewares/contentModeration.middleware';

// 创建路由
const app = new Hono<{ Bindings: Env }>();

// 提交问卷
app.post('/submit',
  // 使用异步审核中间件
  questionnaireModeration({ async: true }),
  ...questionnaireController.submitQuestionnaire
);

// 获取问卷统计
app.get('/stats', questionnaireController.getQuestionnaireStats);

// 获取实时统计
app.get('/realtime-stats', questionnaireController.getQuestionnaireStats);

// 获取问卷详情
app.get('/:id', questionnaireController.getQuestionnaireById);

// 获取问卷列表
app.get('/', questionnaireController.getQuestionnaires);

export default app;
