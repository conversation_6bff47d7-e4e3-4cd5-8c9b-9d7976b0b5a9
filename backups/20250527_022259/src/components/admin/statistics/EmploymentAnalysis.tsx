import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { StatisticsData } from '../StatisticsPanel';
import { Separator } from '@/components/ui/separator';
import { Badge } from '@/components/ui/badge';
import { 
  Briefcase, 
  GraduationCap, 
  MapPin, 
  Building, 
  Clock, 
  TrendingUp, 
  TrendingDown 
} from 'lucide-react';

// 导入图表库
import { 
  PieChart, 
  Pie, 
  Cell, 
  ResponsiveContainer, 
  BarChart, 
  Bar, 
  XAxis, 
  YAxis, 
  CartesianGrid, 
  Tooltip, 
  Legend,
  RadarChart,
  Radar,
  PolarGrid,
  PolarAngleAxis,
  PolarRadiusAxis,
  Treemap
} from 'recharts';

interface EmploymentAnalysisProps {
  data: StatisticsData;
}

// 图表颜色
const COLORS = ['#0088FE', '#00C49F', '#FFBB28', '#FF8042', '#8884d8', '#82ca9d'];

export default function EmploymentAnalysis({ data }: EmploymentAnalysisProps) {
  // 计算就业率
  const employmentRate = data.overview.totalResponses > 0 
    ? (data.overview.employedCount / data.overview.totalResponses * 100).toFixed(1) 
    : '0';
  
  // 准备就业状态饼图数据
  const employmentStatusData = data.employmentStatus.labels.map((label, index) => ({
    name: label,
    value: data.employmentStatus.data[index]
  }));
  
  // 准备行业分布数据
  const industryData = data.industryDistribution.labels.map((label, index) => ({
    name: label,
    value: data.industryDistribution.data[index]
  }));
  
  // 准备地区分布数据
  const regionData = data.regionDistribution.labels.map((label, index) => ({
    name: label,
    value: data.regionDistribution.data[index]
  }));
  
  // 准备学历与就业状态关联数据（模拟数据，实际应从API获取）
  const educationEmploymentData = [
    { name: '高中/中专', 已就业: 65, 待业中: 25, 继续深造: 10 },
    { name: '大专', 已就业: 70, 待业中: 20, 继续深造: 10 },
    { name: '本科', 已就业: 75, 待业中: 15, 继续深造: 10 },
    { name: '硕士', 已就业: 80, 待业中: 10, 继续深造: 10 },
    { name: '博士', 已就业: 85, 待业中: 5, 继续深造: 10 },
  ];
  
  // 准备行业雷达图数据
  const industryRadarData = [
    { subject: 'IT/互联网', A: 120, fullMark: 150 },
    { subject: '金融', A: 98, fullMark: 150 },
    { subject: '教育', A: 86, fullMark: 150 },
    { subject: '医疗', A: 99, fullMark: 150 },
    { subject: '制造业', A: 85, fullMark: 150 },
    { subject: '服务业', A: 65, fullMark: 150 },
  ];
  
  // 准备树状图数据
  const treeMapData = industryData.map(item => ({
    name: item.name,
    size: item.value,
    fill: COLORS[industryData.indexOf(item) % COLORS.length]
  }));
  
  return (
    <div className="space-y-4">
      {/* 就业状态概览 */}
      <Card>
        <CardHeader>
          <CardTitle>就业状态概览</CardTitle>
          <CardDescription>毕业生就业状态分析</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className="flex flex-col items-center justify-center p-4 bg-blue-50 rounded-lg">
              <Briefcase className="h-8 w-8 text-blue-500 mb-2" />
              <h3 className="text-xl font-bold text-blue-700">{data.overview.employedCount}</h3>
              <p className="text-sm text-blue-600">已就业</p>
              <Badge variant="outline" className="mt-2 bg-blue-100">
                {employmentRate}%
              </Badge>
            </div>
            
            <div className="flex flex-col items-center justify-center p-4 bg-amber-50 rounded-lg">
              <Clock className="h-8 w-8 text-amber-500 mb-2" />
              <h3 className="text-xl font-bold text-amber-700">{data.overview.unemployedCount}</h3>
              <p className="text-sm text-amber-600">待业中</p>
              <Badge variant="outline" className="mt-2 bg-amber-100">
                {data.overview.totalResponses > 0 
                  ? (data.overview.unemployedCount / data.overview.totalResponses * 100).toFixed(1) 
                  : '0'}%
              </Badge>
            </div>
            
            <div className="flex flex-col items-center justify-center p-4 bg-green-50 rounded-lg">
              <GraduationCap className="h-8 w-8 text-green-500 mb-2" />
              <h3 className="text-xl font-bold text-green-700">{data.overview.furtherEducationCount}</h3>
              <p className="text-sm text-green-600">继续深造</p>
              <Badge variant="outline" className="mt-2 bg-green-100">
                {data.overview.totalResponses > 0 
                  ? (data.overview.furtherEducationCount / data.overview.totalResponses * 100).toFixed(1) 
                  : '0'}%
              </Badge>
            </div>
          </div>
          
          <Separator className="my-6" />
          
          <div className="h-80">
            <ResponsiveContainer width="100%" height="100%">
              <PieChart>
                <Pie
                  data={employmentStatusData}
                  cx="50%"
                  cy="50%"
                  labelLine={true}
                  label={({ name, percent }) => `${name}: ${(percent * 100).toFixed(1)}%`}
                  outerRadius={120}
                  fill="#8884d8"
                  dataKey="value"
                >
                  {employmentStatusData.map((entry, index) => (
                    <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                  ))}
                </Pie>
                <Tooltip formatter={(value) => [`${value}人`, '数量']} />
                <Legend />
              </PieChart>
            </ResponsiveContainer>
          </div>
        </CardContent>
      </Card>
      
      {/* 学历与就业状态关联分析 */}
      <Card>
        <CardHeader>
          <CardTitle>学历与就业状态关联分析</CardTitle>
          <CardDescription>不同学历层次的就业状态分布</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="h-80">
            <ResponsiveContainer width="100%" height="100%">
              <BarChart
                data={educationEmploymentData}
                margin={{ top: 20, right: 30, left: 20, bottom: 5 }}
              >
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis dataKey="name" />
                <YAxis />
                <Tooltip formatter={(value) => [`${value}%`, '比例']} />
                <Legend />
                <Bar dataKey="已就业" stackId="a" fill="#0088FE" />
                <Bar dataKey="待业中" stackId="a" fill="#FFBB28" />
                <Bar dataKey="继续深造" stackId="a" fill="#00C49F" />
              </BarChart>
            </ResponsiveContainer>
          </div>
        </CardContent>
      </Card>
      
      {/* 行业分布分析 */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <Card>
          <CardHeader>
            <CardTitle>行业分布</CardTitle>
            <CardDescription>毕业生就业行业分布</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="h-80">
              <ResponsiveContainer width="100%" height="100%">
                <BarChart
                  data={industryData}
                  layout="vertical"
                  margin={{ top: 5, right: 30, left: 20, bottom: 5 }}
                >
                  <CartesianGrid strokeDasharray="3 3" />
                  <XAxis type="number" />
                  <YAxis dataKey="name" type="category" width={100} />
                  <Tooltip formatter={(value) => [`${value}人`, '数量']} />
                  <Legend />
                  <Bar dataKey="value" fill="#0088FE" name="人数" />
                </BarChart>
              </ResponsiveContainer>
            </div>
          </CardContent>
        </Card>
        
        <Card>
          <CardHeader>
            <CardTitle>行业分布树状图</CardTitle>
            <CardDescription>毕业生就业行业比例可视化</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="h-80">
              <ResponsiveContainer width="100%" height="100%">
                <Treemap
                  data={treeMapData}
                  dataKey="size"
                  ratio={4/3}
                  stroke="#fff"
                  fill="#8884d8"
                  content={({ root, depth, x, y, width, height, index, payload, colors, rank, name }) => {
                    return (
                      <g>
                        <rect
                          x={x}
                          y={y}
                          width={width}
                          height={height}
                          style={{
                            fill: depth < 2 ? treeMapData[index]?.fill || '#8884d8' : 'none',
                            stroke: '#fff',
                            strokeWidth: 2 / (depth + 1e-10),
                            strokeOpacity: 1 / (depth + 1e-10),
                          }}
                        />
                        {depth === 1 && (
                          <text
                            x={x + width / 2}
                            y={y + height / 2 + 7}
                            textAnchor="middle"
                            fill="#fff"
                            fontSize={14}
                          >
                            {name}
                          </text>
                        )}
                      </g>
                    );
                  }}
                />
              </ResponsiveContainer>
            </div>
          </CardContent>
        </Card>
      </div>
      
      {/* 地区分布分析 */}
      <Card>
        <CardHeader>
          <CardTitle>地区分布</CardTitle>
          <CardDescription>毕业生就业地区分布</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="h-80">
            <ResponsiveContainer width="100%" height="100%">
              <BarChart
                data={regionData}
                margin={{ top: 5, right: 30, left: 20, bottom: 5 }}
              >
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis dataKey="name" />
                <YAxis />
                <Tooltip formatter={(value) => [`${value}人`, '数量']} />
                <Legend />
                <Bar dataKey="value" fill="#00C49F" name="人数" />
              </BarChart>
            </ResponsiveContainer>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
