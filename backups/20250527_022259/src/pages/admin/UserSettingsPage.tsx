import React, { useEffect } from 'react';
import AdminLayout from '@/components/layouts/AdminLayout';
import UserSettingsPanel from '@/components/admin/UserSettingsPanel';
import { initializeSettings } from '@/services/userSettingsService';

/**
 * 用户设置页面
 */
export default function UserSettingsPage() {
  // 初始化设置
  useEffect(() => {
    initializeSettings();
  }, []);
  
  return (
    <AdminLayout>
      <div className="container mx-auto py-6">
        <UserSettingsPanel />
      </div>
    </AdminLayout>
  );
}
