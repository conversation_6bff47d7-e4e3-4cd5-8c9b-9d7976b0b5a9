/**
 * 优化后的API服务器
 * 基于新的数据库结构提供API服务
 */

const express = require('express');
const cors = require('cors');
const sqlite3 = require('sqlite3').verbose();
const path = require('path');

const app = express();
const PORT = 8789; // 使用不同端口避免冲突

// 中间件
app.use(cors());
app.use(express.json());

// 数据库连接
const dbPath = path.join(__dirname, '../../prisma/dev.db');
const db = new sqlite3.Database(dbPath, (err) => {
  if (err) {
    console.error('❌ 数据库连接失败:', err.message);
  } else {
    console.log('✅ 连接到优化后的SQLite数据库');
  }
});

// 健康检查
app.get('/health', (req, res) => {
  res.json({ 
    status: 'ok', 
    message: '优化后的API服务器运行正常',
    timestamp: new Date().toISOString(),
    database: 'optimized_structure'
  });
});

// ========== 优化后的API端点 ==========

// 1. 问卷心声API (基于新的questionnaire_voices表)
app.get('/api/v2/questionnaire-voices', (req, res) => {
  const page = parseInt(req.query.page) || 1;
  const pageSize = parseInt(req.query.pageSize) || 20;
  const voiceType = req.query.type; // advice, observation
  const status = req.query.status || 'approved';
  const offset = (page - 1) * pageSize;
  
  let whereClause = 'WHERE qv.is_deleted = 0 AND qv.status = ?';
  let params = [status];
  
  if (voiceType) {
    whereClause += ' AND qv.voice_type = ?';
    params.push(voiceType);
  }
  
  const query = `
    SELECT 
      qv.id,
      qv.voice_type,
      qv.title,
      qv.content,
      qv.content_id,
      qv.is_anonymous,
      qv.created_at,
      qv.status,
      json_extract(qro.metadata, '$.educationLevel') as education_level,
      json_extract(qro.metadata, '$.region') as region,
      json_extract(qro.metadata, '$.employmentStatus') as employment_status
    FROM questionnaire_voices qv
    LEFT JOIN questionnaire_responses_optimized qro ON qv.response_id = qro.id
    ${whereClause}
    ORDER BY qv.created_at DESC
    LIMIT ? OFFSET ?
  `;
  
  const countQuery = `
    SELECT COUNT(*) as total
    FROM questionnaire_voices qv
    ${whereClause}
  `;
  
  // 获取总数
  db.get(countQuery, params, (err, countResult) => {
    if (err) {
      console.error('查询心声总数失败:', err);
      return res.status(500).json({ error: '查询失败' });
    }
    
    const total = countResult.total;
    
    // 获取分页数据
    db.all(query, [...params, pageSize, offset], (err, rows) => {
      if (err) {
        console.error('查询心声列表失败:', err);
        return res.status(500).json({ error: '查询失败' });
      }
      
      res.json({
        success: true,
        data: {
          voices: rows.map(row => ({
            id: row.id,
            voiceType: row.voice_type,
            title: row.title,
            content: row.content,
            contentId: row.content_id,
            isAnonymous: row.is_anonymous,
            createdAt: row.created_at,
            status: row.status,
            metadata: {
              educationLevel: row.education_level,
              region: row.region,
              employmentStatus: row.employment_status
            }
          })),
          pagination: {
            page,
            pageSize,
            total,
            totalPages: Math.ceil(total / pageSize),
            hasMore: offset + pageSize < total
          }
        },
        source: 'optimized_database',
        version: '2.0'
      });
    });
  });
});

// 2. 故事墙API (基于新的content_metadata表)
app.get('/api/v2/stories', (req, res) => {
  const page = parseInt(req.query.page) || 1;
  const pageSize = parseInt(req.query.pageSize) || 20;
  const category = req.query.category;
  const status = req.query.status || 'approved';
  const offset = (page - 1) * pageSize;
  
  let whereClause = "WHERE cm.content_type = 'story' AND cm.is_deleted = 0 AND cm.status = ?";
  let params = [status];
  
  if (category) {
    whereClause += ' AND cm.category = ?';
    params.push(category);
  }
  
  const query = `
    SELECT 
      cm.id,
      cm.title,
      cm.category,
      cm.tags,
      cm.status,
      cm.likes,
      cm.dislikes,
      cm.views,
      cm.word_count,
      cm.created_at,
      cm.updated_at,
      json_extract(cm.metadata, '$.author') as author,
      json_extract(cm.metadata, '$.isAnonymous') as is_anonymous,
      json_extract(cm.metadata, '$.educationLevel') as education_level,
      json_extract(cm.metadata, '$.industry') as industry,
      SUBSTR(json_extract(cm.metadata, '$.content'), 1, 200) as content_preview
    FROM content_metadata cm
    ${whereClause}
    ORDER BY cm.created_at DESC
    LIMIT ? OFFSET ?
  `;
  
  const countQuery = `
    SELECT COUNT(*) as total
    FROM content_metadata cm
    ${whereClause}
  `;
  
  // 获取总数
  db.get(countQuery, params, (err, countResult) => {
    if (err) {
      console.error('查询故事总数失败:', err);
      return res.status(500).json({ error: '查询失败' });
    }
    
    const total = countResult.total;
    
    // 获取分页数据
    db.all(query, [...params, pageSize, offset], (err, rows) => {
      if (err) {
        console.error('查询故事列表失败:', err);
        return res.status(500).json({ error: '查询失败' });
      }
      
      res.json({
        success: true,
        data: {
          stories: rows.map(row => ({
            id: row.id,
            title: row.title,
            category: row.category,
            tags: row.tags ? JSON.parse(row.tags) : [],
            status: row.status,
            likes: row.likes,
            dislikes: row.dislikes,
            views: row.views,
            wordCount: row.word_count,
            createdAt: row.created_at,
            updatedAt: row.updated_at,
            author: row.author || '匿名用户',
            isAnonymous: row.is_anonymous,
            educationLevel: row.education_level,
            industry: row.industry,
            contentPreview: row.content_preview
          })),
          pagination: {
            page,
            pageSize,
            total,
            totalPages: Math.ceil(total / pageSize),
            hasMore: offset + pageSize < total
          }
        },
        source: 'optimized_database',
        version: '2.0'
      });
    });
  });
});

// 3. 问卷统计API (基于新的问卷结构)
app.get('/api/v2/questionnaire-stats', (req, res) => {
  const questionId = req.query.questionId;
  
  if (!questionId) {
    return res.status(400).json({ error: '需要提供questionId参数' });
  }
  
  // 从问卷回复的metadata中提取对应字段的统计
  const fieldMapping = {
    'qitem_education_level': 'educationLevel',
    'qitem_employment_status': 'employmentStatus',
    'qitem_job_satisfaction': 'jobSatisfaction',
    'qitem_region': 'region',
    'qitem_current_industry': 'currentIndustry'
  };
  
  const field = fieldMapping[questionId];
  if (!field) {
    return res.status(400).json({ error: '不支持的questionId' });
  }
  
  const query = `
    SELECT 
      json_extract(metadata, '$.' || ?) as answer_value,
      COUNT(*) as count
    FROM questionnaire_responses_optimized
    WHERE json_extract(metadata, '$.' || ?) IS NOT NULL
      AND json_extract(metadata, '$.' || ?) != ''
    GROUP BY json_extract(metadata, '$.' || ?)
    ORDER BY count DESC
  `;
  
  db.all(query, [field, field, field, field], (err, rows) => {
    if (err) {
      console.error('查询问卷统计失败:', err);
      return res.status(500).json({ error: '查询失败' });
    }
    
    const total = rows.reduce((sum, row) => sum + row.count, 0);
    
    const stats = rows.map(row => ({
      value: row.answer_value,
      count: row.count,
      percentage: total > 0 ? Math.round((row.count / total) * 100 * 100) / 100 : 0
    }));
    
    res.json({
      success: true,
      data: {
        questionId,
        field,
        totalResponses: total,
        options: stats,
        lastUpdated: new Date().toISOString()
      },
      source: 'optimized_database',
      version: '2.0'
    });
  });
});

// 4. 用户内容管理API
app.get('/api/v2/user/:userId/content', (req, res) => {
  const userId = req.params.userId;
  const contentType = req.query.type; // story, voice
  
  let query = `
    SELECT 
      id, content_type, title, status, likes, views, word_count,
      created_at, updated_at
    FROM content_metadata
    WHERE user_id = ? AND is_deleted = 0
  `;
  
  let params = [userId];
  
  if (contentType) {
    query += ' AND content_type = ?';
    params.push(contentType);
  }
  
  query += ' ORDER BY created_at DESC';
  
  db.all(query, params, (err, rows) => {
    if (err) {
      console.error('查询用户内容失败:', err);
      return res.status(500).json({ error: '查询失败' });
    }
    
    res.json({
      success: true,
      data: {
        userId,
        contents: rows.map(row => ({
          id: row.id,
          contentType: row.content_type,
          title: row.title,
          status: row.status,
          stats: {
            likes: row.likes,
            views: row.views,
            wordCount: row.word_count
          },
          createdAt: row.created_at,
          updatedAt: row.updated_at,
          canEdit: ['pending', 'rejected'].includes(row.status),
          canDelete: true
        }))
      },
      source: 'optimized_database',
      version: '2.0'
    });
  });
});

// 5. 数据库状态API
app.get('/api/v2/database-status', (req, res) => {
  const queries = [
    { name: 'users_optimized', query: 'SELECT COUNT(*) as count FROM users_optimized' },
    { name: 'content_metadata', query: 'SELECT COUNT(*) as count FROM content_metadata' },
    { name: 'questionnaire_responses_optimized', query: 'SELECT COUNT(*) as count FROM questionnaire_responses_optimized' },
    { name: 'questionnaire_voices', query: 'SELECT COUNT(*) as count FROM questionnaire_voices' },
    { name: 'questionnaire_templates', query: 'SELECT COUNT(*) as count FROM questionnaire_templates' },
    { name: 'questions', query: 'SELECT COUNT(*) as count FROM questions' }
  ];
  
  const results = {};
  let completed = 0;
  
  queries.forEach(({ name, query }) => {
    db.get(query, (err, row) => {
      if (err) {
        results[name] = { error: err.message };
      } else {
        results[name] = { count: row.count };
      }
      
      completed++;
      if (completed === queries.length) {
        res.json({
          success: true,
          data: {
            database: 'optimized_structure',
            version: '2.0',
            tables: results,
            timestamp: new Date().toISOString()
          }
        });
      }
    });
  });
});

// 错误处理
app.use((err, req, res, next) => {
  console.error('服务器错误:', err);
  res.status(500).json({ 
    error: '服务器内部错误',
    message: err.message,
    version: '2.0'
  });
});

// 404处理
app.use((req, res) => {
  res.status(404).json({ 
    error: '接口不存在',
    path: req.path,
    version: '2.0'
  });
});

// 启动服务器
app.listen(PORT, () => {
  console.log(`🚀 优化后的API服务器启动成功！`);
  console.log(`📍 地址: http://localhost:${PORT}`);
  console.log(`🔍 健康检查: http://localhost:${PORT}/health`);
  console.log(`📊 数据库状态: http://localhost:${PORT}/api/v2/database-status`);
  console.log(`💭 问卷心声: http://localhost:${PORT}/api/v2/questionnaire-voices`);
  console.log(`📖 故事墙: http://localhost:${PORT}/api/v2/stories`);
  console.log('');
  console.log('🎯 基于优化后的数据库结构运行');
});

// 优雅关闭
process.on('SIGINT', () => {
  console.log('\n🛑 正在关闭服务器...');
  db.close((err) => {
    if (err) {
      console.error('关闭数据库连接失败:', err.message);
    } else {
      console.log('✅ 数据库连接已关闭');
    }
    process.exit(0);
  });
});
