/**
 * 数据初始化服务
 *
 * 将模拟数据导入到 KV 存储中
 */

import { Env } from '../types';
import { KVStorageService } from './kvStorageService';
import {
  getMockUsers,
  getMockQuestionnaireResponses,
  getMockStories,
  getMockPendingStories,
  getMockStatistics,
} from './mockDataService';

/**
 * 数据初始化服务类
 */
export class DataInitService {
  private kvStorage: KVStorageService;

  /**
   * 构造函数
   * @param env 环境变量
   */
  constructor(env: Env) {
    this.kvStorage = new KVStorageService(env);
  }

  /**
   * 初始化所有数据
   */
  async initializeAllData(): Promise<void> {
    await this.initializeUsers();
    await this.initializeQuestionnaires();
    await this.initializeStories();
    await this.initializeStatistics();
    await this.initializeMetadata();
    console.log('All data initialized successfully');
  }

  /**
   * 初始化用户数据
   */
  async initializeUsers(): Promise<void> {
    const users = getMockUsers();
    
    // 保存每个用户
    for (const user of users) {
      await this.kvStorage.saveUser(user.id, user);
    }
    
    console.log(`Initialized ${users.length} users`);
  }

  /**
   * 初始化问卷数据
   */
  async initializeQuestionnaires(): Promise<void> {
    const questionnaires = getMockQuestionnaireResponses();
    
    // 保存每个问卷
    for (const questionnaire of questionnaires) {
      await this.kvStorage.saveQuestionnaire(questionnaire.id, questionnaire);
    }
    
    console.log(`Initialized ${questionnaires.length} questionnaires`);
  }

  /**
   * 初始化故事数据
   */
  async initializeStories(): Promise<void> {
    const approvedStories = getMockStories();
    const pendingStories = getMockPendingStories();
    const allStories = [...approvedStories, ...pendingStories];
    
    // 保存每个故事
    for (const story of allStories) {
      await this.kvStorage.saveStory(story.id, story);
    }
    
    // 保存故事列表
    await this.kvStorage.saveStoryList(allStories);
    
    console.log(`Initialized ${allStories.length} stories`);
  }

  /**
   * 初始化统计数据
   */
  async initializeStatistics(): Promise<void> {
    const stats = getMockStatistics();
    
    // 保存问卷统计数据
    await this.kvStorage.saveQuestionnaireStats(stats);
    
    // 保存可视化数据
    await this.kvStorage.saveVisualizationData({
      success: true,
      stats: stats,
    });
    
    console.log('Initialized statistics data');
  }

  /**
   * 初始化元数据
   */
  async initializeMetadata(): Promise<void> {
    // 提取标签
    const stories = getMockStories();
    const allTags: string[] = [];
    stories.forEach(story => {
      allTags.push(...story.tags);
    });
    
    // 计算标签出现次数
    const tagCounts: Record<string, number> = {};
    allTags.forEach(tag => {
      tagCounts[tag] = (tagCounts[tag] || 0) + 1;
    });
    
    // 转换为数组并按出现次数排序
    const popularTags = Object.entries(tagCounts)
      .map(([tag, count]) => ({ tag, count }))
      .sort((a, b) => b.count - a.count);
    
    // 保存标签
    await this.kvStorage.saveTags(popularTags);
    
    // 提取分类
    const categories = Array.from(new Set(stories.map(story => story.category)));
    await this.kvStorage.saveCategories(categories);
    
    // 提取学历
    const educationLevels = Array.from(new Set(stories.map(story => story.educationLevel)));
    await this.kvStorage.saveEducationLevels(educationLevels);
    
    // 提取行业
    const industries = Array.from(new Set(stories.map(story => story.industry)));
    await this.kvStorage.saveIndustries(industries);
    
    console.log('Initialized metadata');
  }
}
