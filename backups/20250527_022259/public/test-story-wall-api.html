<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>故事墙 API 测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .button {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 10px 5px;
            font-size: 14px;
        }
        .button:hover {
            background-color: #0056b3;
        }
        .button.outline {
            background-color: transparent;
            color: #007bff;
            border: 1px solid #007bff;
        }
        .button.outline:hover {
            background-color: #007bff;
            color: white;
        }
        .result {
            margin-top: 20px;
            padding: 15px;
            background-color: #f8f9fa;
            border-radius: 4px;
            border-left: 4px solid #007bff;
        }
        .error {
            border-left-color: #dc3545;
            background-color: #f8d7da;
        }
        .success {
            border-left-color: #28a745;
            background-color: #d4edda;
        }
        .story-card {
            border: 1px solid #ddd;
            border-radius: 8px;
            padding: 15px;
            margin: 10px 0;
            background: white;
        }
        .story-title {
            font-weight: bold;
            color: #333;
            margin-bottom: 8px;
        }
        .story-content {
            color: #666;
            line-height: 1.5;
            margin-bottom: 10px;
        }
        .story-meta {
            font-size: 12px;
            color: #999;
            display: flex;
            justify-content: space-between;
        }
        .loading {
            text-align: center;
            color: #666;
            font-style: italic;
        }
        pre {
            background: #f4f4f4;
            padding: 10px;
            border-radius: 4px;
            overflow-x: auto;
            font-size: 12px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>故事墙 API 测试页面</h1>
        <p>这个页面用于测试故事墙的API功能，验证前端和后端的连接是否正常。</p>
        
        <div>
            <button class="button outline" onclick="testApiCall()">测试API</button>
            <button class="button" onclick="loadStories()">加载故事列表</button>
            <button class="button" onclick="testDirectAPI()">直接测试后端API</button>
            <button class="button" onclick="testProxyAPI()">测试代理API</button>
            <button class="button outline" onclick="clearResults()">清除结果</button>
        </div>

        <div id="results"></div>
    </div>

    <script>
        // 测试API调用函数（与StoryWallPage中的相同）
        async function testApiCall() {
            console.log('Testing API call...');
            showLoading('正在测试API调用...');
            
            try {
                const response = await fetch('/api/story/list?page=1&pageSize=6');
                console.log('Direct fetch response:', response);
                console.log('Response status:', response.status);
                console.log('Response ok:', response.ok);

                if (response.ok) {
                    const data = await response.json();
                    console.log('Response data:', data);
                    showSuccess('API调用成功！', data);
                } else {
                    console.error('Response not ok:', response.statusText);
                    showError(`API调用失败: ${response.status} ${response.statusText}`);
                }
            } catch (error) {
                console.error('Fetch error:', error);
                showError(`网络错误: ${error.message}`);
            }
        }

        // 加载故事列表
        async function loadStories() {
            showLoading('正在加载故事列表...');
            
            try {
                const response = await fetch('/api/story/list?page=1&pageSize=6');
                
                if (response.ok) {
                    const data = await response.json();
                    displayStories(data);
                } else {
                    showError(`加载故事失败: ${response.status} ${response.statusText}`);
                }
            } catch (error) {
                showError(`加载故事时发生错误: ${error.message}`);
            }
        }

        // 直接测试后端API
        async function testDirectAPI() {
            showLoading('正在测试直接后端API...');
            
            try {
                const response = await fetch('http://localhost:8788/api/story/list?page=1&pageSize=3');
                
                if (response.ok) {
                    const data = await response.json();
                    showSuccess('直接后端API调用成功！', data);
                } else {
                    showError(`直接API调用失败: ${response.status} ${response.statusText}`);
                }
            } catch (error) {
                showError(`直接API调用错误: ${error.message}`);
            }
        }

        // 测试代理API
        async function testProxyAPI() {
            showLoading('正在测试代理API...');
            
            try {
                // 测试当前页面的代理
                const response = await fetch('/api/story/list?page=1&pageSize=3');
                
                if (response.ok) {
                    const data = await response.json();
                    showSuccess('代理API调用成功！', data);
                } else {
                    showError(`代理API调用失败: ${response.status} ${response.statusText}`);
                }
            } catch (error) {
                showError(`代理API调用错误: ${error.message}`);
            }
        }

        // 显示加载状态
        function showLoading(message) {
            const results = document.getElementById('results');
            results.innerHTML = `<div class="result loading">${message}</div>`;
        }

        // 显示成功结果
        function showSuccess(message, data = null) {
            const results = document.getElementById('results');
            let html = `<div class="result success">
                <h3>✅ ${message}</h3>`;
            
            if (data) {
                html += `<pre>${JSON.stringify(data, null, 2)}</pre>`;
            }
            
            html += `</div>`;
            results.innerHTML = html;
        }

        // 显示错误结果
        function showError(message) {
            const results = document.getElementById('results');
            results.innerHTML = `<div class="result error">
                <h3>❌ ${message}</h3>
            </div>`;
        }

        // 显示故事列表
        function displayStories(data) {
            const results = document.getElementById('results');
            
            if (!data.success || !data.stories || data.stories.length === 0) {
                showError('没有找到故事数据');
                return;
            }

            let html = `<div class="result success">
                <h3>✅ 成功加载 ${data.stories.length} 个故事</h3>
                <p><strong>总页数:</strong> ${data.totalPages} | <strong>当前页:</strong> ${data.currentPage}</p>
            `;

            data.stories.forEach((story, index) => {
                html += `
                    <div class="story-card">
                        <div class="story-title">${story.title}</div>
                        <div class="story-content">${story.content.substring(0, 200)}${story.content.length > 200 ? '...' : ''}</div>
                        <div class="story-meta">
                            <span>作者: ${story.author || '匿名用户'}</span>
                            <span>👍 ${story.likes} | 创建时间: ${new Date(story.createdAt).toLocaleDateString()}</span>
                        </div>
                    </div>
                `;
            });

            html += `</div>`;
            results.innerHTML = html;
        }

        // 清除结果
        function clearResults() {
            document.getElementById('results').innerHTML = '';
        }

        // 页面加载时自动测试
        window.addEventListener('load', function() {
            console.log('页面加载完成，准备测试API...');
            console.log('当前URL:', window.location.href);
            console.log('当前端口:', window.location.port);
        });
    </script>
</body>
</html>
