import { Hono } from 'hono';
import { cors } from 'hono/cors';
import { secureHeaders } from 'hono/secure-headers';
import { logger } from 'hono/logger';
import { submitQuestionnaire } from './questionnaire/questionnaire.controller';
import { getQuestionnaireStats, getQuestionnaireRealtimeStats, getQuestionnaireVoices } from './questionnaire/questionnaire.d1.controller';
import { getVisualizationData } from './visualization/visualization.d1.controller';
import { exportData } from './visualization/export.controller';
import { downloadExport, downloadMockExport } from './visualization/download.controller';
import { getDocuments, getDocument, syncDocuments } from './documentation/documentation.controller';
import { submitStory, voteStory, getStoryDetail } from './story/story.controller';
import { getStoryList as getStoryListD1 } from './story/story.d1.controller';
import {
  storyAutoModerationMiddleware,
  questionnaireAutoModerationMiddleware
} from '../services/autoModeration/autoModerationMiddleware';
import {
  initializeAllData,
  initializeUsers,
  initializeQuestionnaires,
  initializeStories,
  initializeStatistics
} from './admin/data-init.controller';
import dataManagementRoutes from './admin/data-management.routes';
import tagManagementRoutes from './admin/tag-management.routes';
import tagRecommendationRoutes from './story/tag-recommendation.routes';
import r2DataInitRoutes from './admin/r2-data-init.routes';
import r2ApiRoutes from './r2/r2-api.routes';
import securityRoutes from './admin/security';
import errorReportRoutes from './admin/error-report';
import errorAnalysisRoutes from './admin/error-analysis';
import errorVisualizationRoutes from './admin/error-visualization';
import reviewRoutes from './admin/review.routes';
import auditRoutes from './admin/audit.routes';
import { SecurityModule } from '../security';
import { admin } from '../controllers/admin';
import { statistics } from '../controllers/statistics';
import { analytics } from '../controllers/analytics';
import { responses } from '../controllers/responses';
import databaseTestRoutes from './test/database';

// Define environment interface
interface Env {
  SURVEY_KV: KVNamespace;
  R2_BUCKET: R2Bucket;
  DATABASE_URL: string;
  RESEND_API_KEY: string;
  AES_SECRET_KEY: string;
  AES_IV: string;
  JWT_SECRET: string;
  ENVIRONMENT: string;
}

// Create Hono app
const app = new Hono<{ Bindings: Env }>();

// Middleware
app.use('*', logger());
app.use('*', secureHeaders());

// CORS configuration - 修复返回值问题
app.use('*', cors({
  origin: (origin) => {
    // 允许无origin的请求（如直接访问API）
    if (!origin) return '*';

    // 检查是否是college-employment-survey.pages.dev的子域名
    if (origin.match(/^https:\/\/[a-f0-9]{8}\.college-employment-survey\.pages\.dev$/)) {
      return origin; // 返回具体的origin而不是true
    }

    // 检查是否是主域名
    if (origin === 'https://college-employment-survey.pages.dev') {
      return origin; // 返回具体的origin而不是true
    }

    // 检查是否是localhost开发环境
    if (origin.match(/^http:\/\/localhost:\d+$/)) {
      return origin; // 返回具体的origin而不是true
    }

    return false;
  },
  allowMethods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS', 'PATCH'],
  allowHeaders: [
    'Content-Type',
    'Authorization',
    'X-Requested-With',
    'Accept',
    'Origin',
    'Access-Control-Request-Method',
    'Access-Control-Request-Headers',
    'X-Test-Data-Generator'
  ],
  exposeHeaders: [
    'Content-Length',
    'Content-Type',
    'X-Total-Count',
    'X-Page-Count'
  ],
  maxAge: 86400,
  credentials: true
}));

// API routes with /api prefix
const api = app.basePath('/api');

// Questionnaire routes
api.post('/questionnaire/submit', questionnaireAutoModerationMiddleware, ...submitQuestionnaire);
api.get('/questionnaire/stats', getQuestionnaireStats);
api.get('/questionnaire/realtime-stats', getQuestionnaireRealtimeStats);
api.get('/questionnaire-voices', getQuestionnaireVoices);

// Database test routes (added here to ensure they work)
api.get('/questionnaire/test-db-count', async (c) => {
  try {
    const result = await c.env.DB.prepare('SELECT COUNT(*) as count FROM questionnaire_responses_v2').first();
    return c.json({
      success: true,
      count: result?.count || 0,
      table: 'questionnaire_responses_v2',
      query: 'SELECT COUNT(*) as count FROM questionnaire_responses_v2'
    });
  } catch (error) {
    return c.json({
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error',
      count: 0
    }, 500);
  }
});

// Visualization routes
api.get('/visualization/data', getVisualizationData);
api.post('/visualization/export', ...exportData);
api.get('/exports/:id', downloadExport);
api.get('/mock-exports/:fileName', downloadMockExport);

// Story routes
api.post('/story/submit', storyAutoModerationMiddleware, ...submitStory);
api.post('/story/vote', ...voteStory);
api.get('/story/list', getStoryListD1);
api.get('/story/detail/:id', getStoryDetail);

// Public tags route (disabled - controller not found)
// api.route('/tags', tags);

// Data initialization routes
api.get('/admin/data/init', initializeAllData);
api.get('/admin/data/init/users', initializeUsers);
api.get('/admin/data/init/questionnaires', initializeQuestionnaires);
api.get('/admin/data/init/stories', initializeStories);
api.get('/admin/data/init/statistics', initializeStatistics);

// Data management routes
api.route('/admin/data-management', dataManagementRoutes);

// Tag management routes
api.route('/admin/tags', tagManagementRoutes);

// Tag recommendation routes
api.route('/story/tag-recommendations', tagRecommendationRoutes);

// R2 data initialization routes
api.route('/admin/r2', r2DataInitRoutes);

// R2 API routes
api.route('/r2', r2ApiRoutes);

// Admin routes
api.route('/admin', admin);
// api.route('/admin/tags', tags); // disabled - controller not found
api.route('/admin/statistics', statistics);
api.route('/admin/responses', responses);
api.route('/analytics', analytics);

// Security routes
api.route('/admin/security', securityRoutes);

// Error report routes
api.route('/admin/errors', errorReportRoutes);

// Error analysis routes
api.route('/admin/error-analysis', errorAnalysisRoutes);

// Error visualization routes
api.route('/admin/error-visualization', errorVisualizationRoutes);

// Content review routes
api.route('/admin/review', reviewRoutes);

// Audit log routes
api.route('/admin/audit', auditRoutes);

// Content moderation routes
import contentModerationRoutes from './admin/content-moderation.routes';
api.route('/admin/content-moderation', contentModerationRoutes);

// Auto moderation routes
import autoModerationRoutes from './admin/auto-moderation.routes';
api.route('/admin/auto-moderation', autoModerationRoutes);

// Moderation feedback routes
import moderationFeedbackRoutes from './feedback/moderation-feedback.routes';
api.route('/feedback/moderation', moderationFeedbackRoutes);

// Notification routes
import notificationRoutes from './notification/notification.routes';
api.route('/notifications', notificationRoutes);

// Test data management routes
import testDataRoutes from './admin/test-data.routes';
api.route('/admin/test-data', testDataRoutes);

// User management routes
import userManagementRoutes from './admin/user-management.routes';
api.route('/admin/users', userManagementRoutes);

// Role management routes
import roleManagementRoutes from './admin/role-management.routes';
api.route('/admin/roles', roleManagementRoutes);

// Deidentification routes
import deidentificationRoutes from './admin/deidentification.routes';
api.route('/admin/deidentification', deidentificationRoutes);

// Documentation routes
api.get('/documentation', getDocuments);
api.get('/documentation/documents', getDocuments);
api.get('/documentation/documents/:id', getDocument);
api.post('/documentation/sync', syncDocuments);

// Database test routes
// api.route('/test/db', databaseTestRoutes);

// 简单测试端点
api.get('/test/simple', async (c) => {
  return c.json({ success: true, message: 'Test endpoint working' });
});

// 系统监控端点 - 用于数据监测系统
api.get('/system/monitor', async (c) => {
  return c.json({
    success: true,
    status: 'healthy',
    timestamp: new Date().toISOString(),
    message: 'System monitor endpoint'
  });
});

// 系统诊断端点 - 用于数据监测系统
api.get('/system/diagnostics', async (c) => {
  try {
    const databases = {
      D1: {
        status: 'connected',
        questionnaire_responses_v2: {
          count: 0,
          status: 'healthy'
        },
        questionnaire_voices_v2: {
          count: 0,
          status: 'healthy'
        },
        story_contents_v2: {
          count: 0,
          status: 'healthy'
        }
      }
    };

    // 检查数据库连接和表数据
    if (c.env.DB) {
      try {
        const responseCount = await c.env.DB.prepare('SELECT COUNT(*) as count FROM questionnaire_responses_v2').first();
        databases.D1.questionnaire_responses_v2.count = responseCount?.count || 0;
      } catch (e) {
        databases.D1.questionnaire_responses_v2.status = 'error';
      }

      try {
        const voiceCount = await c.env.DB.prepare('SELECT COUNT(*) as count FROM questionnaire_voices_v2').first();
        databases.D1.questionnaire_voices_v2.count = voiceCount?.count || 0;
      } catch (e) {
        databases.D1.questionnaire_voices_v2.status = 'error';
      }

      try {
        const storyCount = await c.env.DB.prepare('SELECT COUNT(*) as count FROM story_contents_v2').first();
        databases.D1.story_contents_v2.count = storyCount?.count || 0;
      } catch (e) {
        databases.D1.story_contents_v2.status = 'error';
      }
    } else {
      databases.D1.status = 'disconnected';
    }

    return c.json({
      success: true,
      databases,
      timestamp: new Date().toISOString()
    });
  } catch (error) {
    return c.json({
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error',
      timestamp: new Date().toISOString()
    }, 500);
  }
});

// 直接添加数据库测试端点
api.get('/test/db/count-responses', async (c) => {
  try {
    const result = await c.env.DB.prepare('SELECT COUNT(*) as count FROM questionnaire_responses_v2').first();
    return c.json({
      success: true,
      count: result?.count || 0,
      table: 'questionnaire_responses_v2',
      query: 'SELECT COUNT(*) as count FROM questionnaire_responses_v2'
    });
  } catch (error) {
    return c.json({
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error',
      count: 0
    }, 500);
  }
});

api.get('/test/db/count-voices', async (c) => {
  try {
    const result = await c.env.DB.prepare('SELECT COUNT(*) as count FROM questionnaire_voices_v2').first();
    return c.json({
      success: true,
      count: result?.count || 0,
      table: 'questionnaire_voices_v2',
      query: 'SELECT COUNT(*) as count FROM questionnaire_voices_v2'
    });
  } catch (error) {
    return c.json({
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error',
      count: 0
    }, 500);
  }
});

api.get('/test/db/count-stories', async (c) => {
  try {
    const result = await c.env.DB.prepare('SELECT COUNT(*) as count FROM story_contents_v2').first();
    return c.json({
      success: true,
      count: result?.count || 0,
      table: 'story_contents_v2',
      query: 'SELECT COUNT(*) as count FROM story_contents_v2'
    });
  } catch (error) {
    return c.json({
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error',
      count: 0
    }, 500);
  }
});

api.get('/test/db/count-users', async (c) => {
  try {
    const result = await c.env.DB.prepare('SELECT COUNT(*) as count FROM users_v2').first();
    return c.json({
      success: true,
      count: result?.count || 0,
      table: 'users_v2',
      query: 'SELECT COUNT(*) as count FROM users_v2'
    });
  } catch (error) {
    return c.json({
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error',
      count: 0
    }, 500);
  }
});

api.get('/test/db/table-info', async (c) => {
  try {
    const result = await c.env.DB.prepare('SELECT name, type FROM sqlite_master WHERE type="table"').all();
    return c.json({
      success: true,
      data: result.results || [],
      count: result.results?.length || 0,
      query: 'SELECT name, type FROM sqlite_master WHERE type="table"'
    });
  } catch (error) {
    return c.json({
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error',
      data: []
    }, 500);
  }
});

api.get('/test/db/recent-responses', async (c) => {
  try {
    const result = await c.env.DB.prepare('SELECT id, created_at FROM questionnaire_responses_v2 ORDER BY created_at DESC LIMIT 5').all();
    return c.json({
      success: true,
      data: result.results || [],
      count: result.results?.length || 0,
      query: 'SELECT id, created_at FROM questionnaire_responses_v2 ORDER BY created_at DESC LIMIT 5'
    });
  } catch (error) {
    return c.json({
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error',
      data: []
    }, 500);
  }
});

// 不在全局范围内初始化安全模块，而是在第一个请求中初始化
// 添加中间件来初始化安全模块
app.use('*', async (c, next) => {
  // 检查安全模块是否已初始化
  if (!SecurityModule.isInitialized()) {
    SecurityModule.initialize({
      protectionLevel: 2, // 使用标准防护等级
      captcha: {
        secretKey: c.env.TURNSTILE_SECRET_KEY || 'development-key'
      }
    });
  }
  await next();
});

// For backward compatibility, also register routes without /api prefix
app.post('/questionnaire/submit', questionnaireAutoModerationMiddleware, ...submitQuestionnaire);
app.get('/questionnaire/stats', getQuestionnaireStats);
app.get('/questionnaire/realtime-stats', getQuestionnaireRealtimeStats);
app.get('/questionnaire-voices', getQuestionnaireVoices);
app.get('/visualization/data', getVisualizationData);
app.post('/story/submit', storyAutoModerationMiddleware, ...submitStory);
app.post('/story/vote', ...voteStory);
app.get('/story/list', getStoryListD1);
app.get('/story/detail/:id', getStoryDetail);

export default app;
