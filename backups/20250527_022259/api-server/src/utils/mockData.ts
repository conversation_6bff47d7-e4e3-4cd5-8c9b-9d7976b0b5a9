/**
 * 模拟数据生成工具
 */

/**
 * 生成随机数据
 * @param startDate 开始日期
 * @param endDate 结束日期
 * @param series 数据系列配置
 * @returns 随机数据
 */
export function generateRandomData(
  startDate: Date,
  endDate: Date,
  series: Array<{ type: string; min: number; max: number }>
): any[] {
  const data: any[] = [];
  const dayDiff = Math.ceil((endDate.getTime() - startDate.getTime()) / (1000 * 60 * 60 * 24));
  const interval = Math.max(1, Math.floor(dayDiff / 30));
  
  for (let i = 0; i <= dayDiff; i += interval) {
    const date = new Date(startDate.getTime() + i * 24 * 60 * 60 * 1000);
    const dateStr = date.toISOString().split('T')[0];
    
    for (const s of series) {
      data.push({
        date: dateStr,
        type: s.type,
        value: Math.floor(Math.random() * (s.max - s.min + 1)) + s.min
      });
    }
  }
  
  return data;
}

/**
 * 生成随机ID
 * @returns 随机ID
 */
export function generateRandomId(): string {
  return Math.random().toString(36).substring(2, 15) + Math.random().toString(36).substring(2, 15);
}

/**
 * 生成随机日期
 * @param start 开始日期
 * @param end 结束日期
 * @returns 随机日期
 */
export function generateRandomDate(start: Date, end: Date): Date {
  return new Date(start.getTime() + Math.random() * (end.getTime() - start.getTime()));
}

/**
 * 生成随机布尔值
 * @param trueProb 为true的概率
 * @returns 随机布尔值
 */
export function generateRandomBoolean(trueProb: number = 0.5): boolean {
  return Math.random() < trueProb;
}

/**
 * 从数组中随机选择一个元素
 * @param array 数组
 * @returns 随机元素
 */
export function getRandomElement<T>(array: T[]): T {
  return array[Math.floor(Math.random() * array.length)];
}

/**
 * 从数组中随机选择多个元素
 * @param array 数组
 * @param count 元素数量
 * @returns 随机元素数组
 */
export function getRandomElements<T>(array: T[], count: number): T[] {
  const shuffled = [...array].sort(() => 0.5 - Math.random());
  return shuffled.slice(0, count);
}

/**
 * 生成随机整数
 * @param min 最小值
 * @param max 最大值
 * @returns 随机整数
 */
export function getRandomInt(min: number, max: number): number {
  return Math.floor(Math.random() * (max - min + 1)) + min;
}

/**
 * 生成随机浮点数
 * @param min 最小值
 * @param max 最大值
 * @param decimals 小数位数
 * @returns 随机浮点数
 */
export function getRandomFloat(min: number, max: number, decimals: number = 2): number {
  const value = Math.random() * (max - min) + min;
  return Number(value.toFixed(decimals));
}

/**
 * 生成随机字符串
 * @param length 字符串长度
 * @returns 随机字符串
 */
export function getRandomString(length: number): string {
  const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
  let result = '';
  for (let i = 0; i < length; i++) {
    result += chars.charAt(Math.floor(Math.random() * chars.length));
  }
  return result;
}

/**
 * 生成随机IP地址
 * @returns 随机IP地址
 */
export function getRandomIp(): string {
  return `${getRandomInt(1, 255)}.${getRandomInt(0, 255)}.${getRandomInt(0, 255)}.${getRandomInt(0, 255)}`;
}

/**
 * 生成随机用户代理
 * @returns 随机用户代理
 */
export function getRandomUserAgent(): string {
  const userAgents = [
    'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
    'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
    'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:89.0) Gecko/20100101 Firefox/89.0',
    'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/14.1.1 Safari/605.1.15',
    'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36 Edg/91.0.864.59'
  ];
  
  return getRandomElement(userAgents);
}

/**
 * 生成随机HTTP方法
 * @returns 随机HTTP方法
 */
export function getRandomHttpMethod(): string {
  const methods = ['GET', 'POST', 'PUT', 'DELETE', 'PATCH'];
  return getRandomElement(methods);
}

/**
 * 生成随机HTTP状态码
 * @returns 随机HTTP状态码
 */
export function getRandomHttpStatus(): number {
  const statuses = [
    200, 200, 200, 200, 200, 200, 200, 200, // 增加200的权重
    201, 201, 201, // 增加201的权重
    204, 204, // 增加204的权重
    400, 401, 403, 404, 500
  ];
  
  return getRandomElement(statuses);
}

/**
 * 生成随机API路径
 * @returns 随机API路径
 */
export function getRandomApiPath(): string {
  const resources = ['users', 'surveys', 'stories', 'tags', 'stats', 'settings', 'logs', 'alerts'];
  const resource = getRandomElement(resources);
  
  if (Math.random() < 0.3) {
    // 添加ID
    return `/api/v1/${resource}/${getRandomInt(1, 100)}`;
  }
  
  return `/api/v1/${resource}`;
}

/**
 * 生成随机日志级别
 * @returns 随机日志级别
 */
export function getRandomLogLevel(): string {
  const levels = ['info', 'info', 'info', 'warning', 'warning', 'error', 'critical'];
  return getRandomElement(levels);
}

/**
 * 生成随机日志消息
 * @returns 随机日志消息
 */
export function getRandomLogMessage(): string {
  const messages = [
    '请求成功',
    '请求失败',
    '认证失败',
    '权限不足',
    '资源不存在',
    '服务器错误',
    '数据库连接失败',
    'API请求超时',
    '无效的参数',
    '无效的令牌',
    '令牌已过期',
    '用户已锁定',
    '密码错误',
    '用户不存在',
    '数据验证失败'
  ];
  
  return getRandomElement(messages);
}
