#!/usr/bin/env node

/**
 * API服务器清理脚本
 * 
 * 用于清理冗余的API服务器文件，统一到real-db-api-server.js
 */

const fs = require('fs');
const path = require('path');

// 颜色定义
const colors = {
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  cyan: '\x1b[36m',
  reset: '\x1b[0m'
};

const log = (message, color = 'reset') => {
  console.log(`${colors[color]}${message}${colors.reset}`);
};

// 需要清理的冗余服务器文件
const REDUNDANT_SERVERS = [
  'admin-login-server.js',      // 功能已迁移到real-db-api-server.js
  'user-management-server.js',  // 功能已迁移到real-db-api-server.js
  'api-test-server.js',         // 测试服务器，功能已迁移
  'simple-server.js',           // 简单服务器，功能重复
  'test-server.js'              // 测试服务器，功能重复
];

// 保留的服务器文件
const KEEP_SERVERS = [
  'real-db-api-server.js',      // 主API服务器
  'simple-doc-server.js'        // 文档服务器，独立功能
];

// 需要备份的文件
const BACKUP_SERVERS = [
  'admin-login-server.js',
  'user-management-server.js',
  'api-test-server.js'
];

function main() {
  log('🧹 开始API服务器清理...', 'blue');
  log('=' * 50, 'blue');

  // 1. 创建备份目录
  const backupDir = path.join(__dirname, 'archive', `api-servers-backup-${new Date().toISOString().slice(0, 10).replace(/-/g, '')}`);
  
  if (!fs.existsSync(backupDir)) {
    fs.mkdirSync(backupDir, { recursive: true });
    log(`📁 创建备份目录: ${backupDir}`, 'cyan');
  }

  // 2. 备份重要的服务器文件
  log('\n📦 备份重要服务器文件...', 'yellow');
  
  BACKUP_SERVERS.forEach(serverFile => {
    const sourcePath = path.join(__dirname, serverFile);
    const backupPath = path.join(backupDir, serverFile);
    
    if (fs.existsSync(sourcePath)) {
      fs.copyFileSync(sourcePath, backupPath);
      log(`✅ 已备份: ${serverFile}`, 'green');
    } else {
      log(`⚠️  文件不存在: ${serverFile}`, 'yellow');
    }
  });

  // 3. 分析当前API服务器状态
  log('\n📊 分析当前API服务器状态...', 'yellow');
  
  const allServerFiles = fs.readdirSync(__dirname)
    .filter(file => file.endsWith('.js') && (file.includes('server') || file.includes('api')))
    .filter(file => !file.includes('test-') && !file.includes('cleanup'));

  allServerFiles.forEach(serverFile => {
    const filePath = path.join(__dirname, serverFile);
    const stats = fs.statSync(filePath);
    const sizeKB = (stats.size / 1024).toFixed(1);
    
    let status = '';
    let action = '';
    
    if (serverFile === 'real-db-api-server.js') {
      status = '✅ 主服务器';
      action = '保留';
    } else if (KEEP_SERVERS.includes(serverFile)) {
      status = '📚 独立功能';
      action = '保留';
    } else if (REDUNDANT_SERVERS.includes(serverFile)) {
      status = '❌ 冗余服务器';
      action = '移除';
    } else {
      status = '🔍 待评估';
      action = '检查';
    }
    
    log(`   ${serverFile} (${sizeKB}KB) - ${status} - ${action}`, 
        action === '保留' ? 'green' : action === '移除' ? 'red' : 'yellow');
  });

  // 4. 移除冗余服务器文件
  log('\n🗑️  移除冗余服务器文件...', 'yellow');
  
  let removedCount = 0;
  
  REDUNDANT_SERVERS.forEach(serverFile => {
    const filePath = path.join(__dirname, serverFile);
    
    if (fs.existsSync(filePath)) {
      try {
        fs.unlinkSync(filePath);
        log(`✅ 已移除: ${serverFile}`, 'green');
        removedCount++;
      } catch (error) {
        log(`❌ 移除失败: ${serverFile} - ${error.message}`, 'red');
      }
    } else {
      log(`⚠️  文件不存在: ${serverFile}`, 'yellow');
    }
  });

  // 5. 更新package.json脚本
  log('\n📝 更新package.json脚本...', 'yellow');
  
  const packageJsonPath = path.join(__dirname, 'package.json');
  
  if (fs.existsSync(packageJsonPath)) {
    try {
      const packageJson = JSON.parse(fs.readFileSync(packageJsonPath, 'utf8'));
      
      // 更新scripts部分
      if (!packageJson.scripts) {
        packageJson.scripts = {};
      }
      
      // 添加统一的API服务器启动脚本
      packageJson.scripts['api:start'] = 'node real-db-api-server.js';
      packageJson.scripts['api:dev'] = 'nodemon real-db-api-server.js';
      
      // 移除旧的脚本
      const oldScripts = ['admin-server', 'user-server', 'test-server'];
      oldScripts.forEach(script => {
        if (packageJson.scripts[script]) {
          delete packageJson.scripts[script];
          log(`   移除旧脚本: ${script}`, 'cyan');
        }
      });
      
      fs.writeFileSync(packageJsonPath, JSON.stringify(packageJson, null, 2));
      log('✅ package.json已更新', 'green');
      
    } catch (error) {
      log(`❌ 更新package.json失败: ${error.message}`, 'red');
    }
  }

  // 6. 生成清理报告
  log('\n📋 生成清理报告...', 'yellow');
  
  const reportContent = `# API服务器清理报告

## 清理时间
${new Date().toISOString()}

## 清理结果
- 移除冗余服务器: ${removedCount}个
- 备份文件位置: ${backupDir}
- 主API服务器: real-db-api-server.js (保留)

## 移除的服务器文件
${REDUNDANT_SERVERS.map(file => `- ${file}`).join('\n')}

## 保留的服务器文件
${KEEP_SERVERS.map(file => `- ${file}`).join('\n')}

## 统一后的API架构
- **主API服务器**: real-db-api-server.js (端口8788)
  - 管理员认证
  - 用户管理 (CRUD)
  - 内容审核
  - 标签管理
  - AI脱敏设置
  - 数据可视化
  - 问卷管理
  - 故事管理

## 启动命令
\`\`\`bash
# 启动API服务器
npm run api:start

# 开发模式启动
npm run api:dev
\`\`\`

## 前端配置
前端Vite代理已配置为统一指向8788端口，无需修改。

## 验证步骤
1. 启动API服务器: \`npm run api:start\`
2. 测试健康检查: \`curl http://localhost:8788/health\`
3. 测试用户管理: \`curl http://localhost:8788/api/admin/users\`
4. 测试内容审核: \`curl http://localhost:8788/api/admin/review/pending\`
`;

  const reportPath = path.join(__dirname, 'API_CLEANUP_REPORT.md');
  fs.writeFileSync(reportPath, reportContent);
  log(`✅ 清理报告已生成: ${reportPath}`, 'green');

  // 7. 总结
  log('\n🎉 API服务器清理完成！', 'green');
  log('=' * 50, 'green');
  log(`📊 清理统计:`, 'cyan');
  log(`   - 移除冗余服务器: ${removedCount}个`, 'cyan');
  log(`   - 备份文件: ${BACKUP_SERVERS.length}个`, 'cyan');
  log(`   - 保留服务器: ${KEEP_SERVERS.length}个`, 'cyan');
  log(`📁 备份位置: ${backupDir}`, 'cyan');
  log(`📋 详细报告: ${reportPath}`, 'cyan');
  
  log('\n🚀 下一步操作:', 'yellow');
  log('1. 启动统一API服务器: npm run api:start', 'yellow');
  log('2. 测试前端功能是否正常', 'yellow');
  log('3. 如有问题，可从备份目录恢复文件', 'yellow');
}

// 执行清理
if (require.main === module) {
  main();
}

module.exports = { main };
