/**
 * API请求优化中间件
 * 提供缓存、压缩、限流和性能监控功能
 */

const { createPerformanceMiddleware } = require('../utils/performance');
const { getCache, setCache } = require('../utils/cache');

// 请求限流配置
const RATE_LIMIT_WINDOW = 60; // 时间窗口（秒）
const DEFAULT_RATE_LIMIT = 100; // 默认限制（请求/窗口）
const RATE_LIMIT_BY_PATH = {
  '/api/v1/surveys': 200,
  '/api/v1/stories': 200,
  '/api/v1/admin': 50
};

// 缓存配置
const DEFAULT_CACHE_TTL = 300; // 默认缓存时间（秒）
const CACHE_CONTROL_BY_PATH = {
  '/api/v1/surveys': 600, // 10分钟
  '/api/v1/stories': 600, // 10分钟
  '/api/v1/stats': 1800, // 30分钟
  '/api/v1/settings': 3600 // 1小时
};

// 压缩配置
const COMPRESSION_THRESHOLD = 1024; // 压缩阈值（字节）

// 限流存储
const rateLimitStore = new Map();

/**
 * 创建性能监控中间件
 * @returns {Function} 性能监控中间件
 */
function createPerformanceMonitoringMiddleware() {
  return createPerformanceMiddleware();
}

/**
 * 创建缓存中间件
 * @returns {Function} 缓存中间件
 */
function createCacheMiddleware() {
  return async (request, env, ctx) => {
    // 只缓存GET请求
    if (request.method !== 'GET') {
      return ctx.next();
    }
    
    const url = new URL(request.url);
    const path = url.pathname;
    
    // 检查是否应该缓存此路径
    let shouldCache = false;
    let cacheTTL = DEFAULT_CACHE_TTL;
    
    // 检查路径是否匹配缓存配置
    for (const [cachePath, ttl] of Object.entries(CACHE_CONTROL_BY_PATH)) {
      if (path.startsWith(cachePath)) {
        shouldCache = true;
        cacheTTL = ttl;
        break;
      }
    }
    
    // 如果不应该缓存，直接继续
    if (!shouldCache) {
      return ctx.next();
    }
    
    // 生成缓存键
    const cacheKey = `${path}${url.search}`;
    const cacheNamespace = 'api-responses';
    
    // 尝试从缓存获取响应
    const cachedResponse = await getCache(env, cacheNamespace, cacheKey);
    
    if (cachedResponse) {
      // 返回缓存的响应
      return new Response(JSON.stringify(cachedResponse.body), {
        status: cachedResponse.status,
        headers: {
          'Content-Type': 'application/json',
          'X-Cache': 'HIT',
          'Cache-Control': `max-age=${cacheTTL}`
        }
      });
    }
    
    // 继续处理请求
    const response = await ctx.next();
    
    // 只缓存成功的响应
    if (response.status === 200) {
      // 克隆响应以便读取内容
      const clonedResponse = response.clone();
      
      try {
        const responseBody = await clonedResponse.json();
        
        // 缓存响应
        await setCache(env, cacheNamespace, cacheKey, {
          body: responseBody,
          status: response.status
        }, cacheTTL);
        
        // 添加缓存头
        const headers = new Headers(response.headers);
        headers.set('X-Cache', 'MISS');
        headers.set('Cache-Control', `max-age=${cacheTTL}`);
        
        // 返回带有缓存头的响应
        return new Response(JSON.stringify(responseBody), {
          status: response.status,
          headers
        });
      } catch (error) {
        console.error('缓存响应失败:', error);
      }
    }
    
    return response;
  };
}

/**
 * 创建压缩中间件
 * @returns {Function} 压缩中间件
 */
function createCompressionMiddleware() {
  return async (request, env, ctx) => {
    // 检查客户端是否支持压缩
    const acceptEncoding = request.headers.get('Accept-Encoding') || '';
    const supportsGzip = acceptEncoding.includes('gzip');
    const supportsDeflate = acceptEncoding.includes('deflate');
    const supportsBrotli = acceptEncoding.includes('br');
    
    // 如果客户端不支持任何压缩方式，直接继续
    if (!supportsGzip && !supportsDeflate && !supportsBrotli) {
      return ctx.next();
    }
    
    // 继续处理请求
    const response = await ctx.next();
    
    // 克隆响应以便读取内容
    const clonedResponse = response.clone();
    
    try {
      const responseBody = await clonedResponse.text();
      
      // 如果响应体小于阈值，不进行压缩
      if (responseBody.length < COMPRESSION_THRESHOLD) {
        return response;
      }
      
      // 选择压缩方法
      let compressedBody;
      let contentEncoding;
      
      if (supportsBrotli) {
        // 使用Brotli压缩（如果支持）
        // 注意：Cloudflare Workers环境可能不支持Brotli压缩
        // 这里仅作为示例，实际使用时需要检查环境支持
        contentEncoding = 'br';
        // compressedBody = await compressBrotli(responseBody);
        // 由于Brotli可能不可用，这里回退到Gzip
        contentEncoding = 'gzip';
        compressedBody = await compressGzip(responseBody);
      } else if (supportsGzip) {
        // 使用Gzip压缩
        contentEncoding = 'gzip';
        compressedBody = await compressGzip(responseBody);
      } else if (supportsDeflate) {
        // 使用Deflate压缩
        contentEncoding = 'deflate';
        compressedBody = await compressDeflate(responseBody);
      } else {
        // 不压缩
        return response;
      }
      
      // 创建新的响应头
      const headers = new Headers(response.headers);
      headers.set('Content-Encoding', contentEncoding);
      headers.set('Content-Length', compressedBody.length.toString());
      headers.set('Vary', 'Accept-Encoding');
      
      // 返回压缩后的响应
      return new Response(compressedBody, {
        status: response.status,
        headers
      });
    } catch (error) {
      console.error('压缩响应失败:', error);
      return response;
    }
  };
}

/**
 * 创建限流中间件
 * @returns {Function} 限流中间件
 */
function createRateLimitMiddleware() {
  return async (request, env, ctx) => {
    const url = new URL(request.url);
    const path = url.pathname;
    const ip = request.headers.get('CF-Connecting-IP') || 'unknown';
    
    // 确定此路径的速率限制
    let rateLimit = DEFAULT_RATE_LIMIT;
    
    // 检查路径是否匹配限流配置
    for (const [limitPath, limit] of Object.entries(RATE_LIMIT_BY_PATH)) {
      if (path.startsWith(limitPath)) {
        rateLimit = limit;
        break;
      }
    }
    
    // 生成限流键
    const rateLimitKey = `${ip}:${path}`;
    
    // 获取当前窗口的请求计数
    const now = Math.floor(Date.now() / 1000);
    const windowStart = now - (now % RATE_LIMIT_WINDOW);
    
    let rateLimitInfo = rateLimitStore.get(rateLimitKey);
    
    // 如果不存在或窗口已过期，创建新的限流信息
    if (!rateLimitInfo || rateLimitInfo.windowStart !== windowStart) {
      rateLimitInfo = {
        windowStart,
        count: 0
      };
    }
    
    // 增加请求计数
    rateLimitInfo.count++;
    
    // 更新限流存储
    rateLimitStore.set(rateLimitKey, rateLimitInfo);
    
    // 检查是否超过限制
    if (rateLimitInfo.count > rateLimit) {
      return new Response(JSON.stringify({
        success: false,
        error: '请求过于频繁，请稍后再试'
      }), {
        status: 429,
        headers: {
          'Content-Type': 'application/json',
          'Retry-After': RATE_LIMIT_WINDOW.toString(),
          'X-RateLimit-Limit': rateLimit.toString(),
          'X-RateLimit-Remaining': '0',
          'X-RateLimit-Reset': (windowStart + RATE_LIMIT_WINDOW).toString()
        }
      });
    }
    
    // 继续处理请求
    const response = await ctx.next();
    
    // 添加限流头
    const headers = new Headers(response.headers);
    headers.set('X-RateLimit-Limit', rateLimit.toString());
    headers.set('X-RateLimit-Remaining', (rateLimit - rateLimitInfo.count).toString());
    headers.set('X-RateLimit-Reset', (windowStart + RATE_LIMIT_WINDOW).toString());
    
    // 返回带有限流头的响应
    return new Response(response.body, {
      status: response.status,
      headers
    });
  };
}

/**
 * Gzip压缩
 * @param {string} data 要压缩的数据
 * @returns {Promise<Uint8Array>} 压缩后的数据
 */
async function compressGzip(data) {
  // 在Cloudflare Workers环境中，可以使用CompressionStream API
  const encoder = new TextEncoder();
  const stream = encoder.encode(data);
  
  const cs = new CompressionStream('gzip');
  const writer = cs.writable.getWriter();
  writer.write(stream);
  writer.close();
  
  return new Response(cs.readable).arrayBuffer().then(buffer => new Uint8Array(buffer));
}

/**
 * Deflate压缩
 * @param {string} data 要压缩的数据
 * @returns {Promise<Uint8Array>} 压缩后的数据
 */
async function compressDeflate(data) {
  // 在Cloudflare Workers环境中，可以使用CompressionStream API
  const encoder = new TextEncoder();
  const stream = encoder.encode(data);
  
  const cs = new CompressionStream('deflate');
  const writer = cs.writable.getWriter();
  writer.write(stream);
  writer.close();
  
  return new Response(cs.readable).arrayBuffer().then(buffer => new Uint8Array(buffer));
}

/**
 * 创建优化中间件
 * @returns {Function} 优化中间件
 */
function createOptimizationMiddleware() {
  const performanceMiddleware = createPerformanceMonitoringMiddleware();
  const cacheMiddleware = createCacheMiddleware();
  const compressionMiddleware = createCompressionMiddleware();
  const rateLimitMiddleware = createRateLimitMiddleware();
  
  return async (request, env, ctx) => {
    // 按顺序应用中间件
    return performanceMiddleware(request, env, {
      next: async () => rateLimitMiddleware(request, env, {
        next: async () => cacheMiddleware(request, env, {
          next: async () => compressionMiddleware(request, env, ctx)
        })
      })
    });
  };
}

// 定期清理过期的限流信息
setInterval(() => {
  const now = Math.floor(Date.now() / 1000);
  const windowStart = now - (now % RATE_LIMIT_WINDOW);
  
  for (const [key, info] of rateLimitStore.entries()) {
    if (info.windowStart < windowStart) {
      rateLimitStore.delete(key);
    }
  }
}, 60 * 1000); // 每分钟清理一次

module.exports = {
  createPerformanceMonitoringMiddleware,
  createCacheMiddleware,
  createCompressionMiddleware,
  createRateLimitMiddleware,
  createOptimizationMiddleware
};
