import { useEffect, useState } from 'react';
import { z } from 'zod';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { 
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage 
} from '@/components/ui/form';
import { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group';
import { 
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue
} from '@/components/ui/select';
import { Input } from '@/components/ui/input';
import QuestionItem from '../QuestionItem';
import AnswerStats from '../AnswerStats';
import { QuestionnaireData } from '@/lib/api';

// Define the form schema
const formSchema = z.object({
  regretMajor: z.string().min(1, { message: '请选择是否后悔所学专业' }),
  preferredMajor: z.string().optional(),
  careerChangeIntention: z.string().min(1, { message: '请选择是否有转行意向' }),
  careerChangeTarget: z.string().optional(),
});

// Mock statistics data
const mockStats = {
  regretMajor: [
    { label: '是', percentage: 45 },
    { label: '否', percentage: 55 },
  ],
  preferredMajor: [
    { label: '计算机/IT', percentage: 30 },
    { label: '金融/经济', percentage: 20 },
    { label: '医学', percentage: 15 },
    { label: '教育', percentage: 10 },
    { label: '其他', percentage: 25 },
  ],
  careerChangeIntention: [
    { label: '是', percentage: 60 },
    { label: '否', percentage: 40 },
  ],
  careerChangeTarget: [
    { label: 'IT/互联网', percentage: 35 },
    { label: '金融', percentage: 20 },
    { label: '教育培训', percentage: 15 },
    { label: '医疗健康', percentage: 10 },
    { label: '其他', percentage: 20 },
  ],
};

interface CareerReflectionModuleProps {
  data: Partial<QuestionnaireData>;
  onUpdate: (data: Partial<QuestionnaireData>) => void;
}

export default function CareerReflectionModule({ 
  data, 
  onUpdate 
}: CareerReflectionModuleProps) {
  // Set up form with default values from data
  const form = useForm<z.infer<typeof formSchema>>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      regretMajor: data.regretMajor !== undefined ? data.regretMajor.toString() : '',
      preferredMajor: data.preferredMajor || '',
      careerChangeIntention: data.careerChangeIntention !== undefined ? data.careerChangeIntention.toString() : '',
      careerChangeTarget: data.careerChangeTarget || '',
    },
  });
  
  // Track if user regrets major and has career change intention
  const [regretsMajor, setRegretsMajor] = useState(data.regretMajor === true);
  const [hasCareerChangeIntention, setHasCareerChangeIntention] = useState(data.careerChangeIntention === true);
  
  // Track selected options for stats display
  const [selectedOptions, setSelectedOptions] = useState({
    regretMajor: data.regretMajor !== undefined ? (data.regretMajor ? '是' : '否') : '',
    preferredMajor: data.preferredMajor || '',
    careerChangeIntention: data.careerChangeIntention !== undefined ? (data.careerChangeIntention ? '是' : '否') : '',
    careerChangeTarget: data.careerChangeTarget || '',
  });
  
  // Update stats when form values change
  useEffect(() => {
    const subscription = form.watch((value) => {
      // Check if user regrets major
      const regrets = value.regretMajor === 'true';
      setRegretsMajor(regrets);
      
      // Check if user has career change intention
      const hasIntention = value.careerChangeIntention === 'true';
      setHasCareerChangeIntention(hasIntention);
      
      setSelectedOptions({
        regretMajor: value.regretMajor === 'true' ? '是' : value.regretMajor === 'false' ? '否' : '',
        preferredMajor: value.preferredMajor || '',
        careerChangeIntention: value.careerChangeIntention === 'true' ? '是' : value.careerChangeIntention === 'false' ? '否' : '',
        careerChangeTarget: value.careerChangeTarget || '',
      });
      
      // Update parent component with form values
      if (value.regretMajor && value.careerChangeIntention) {
        const updateData: Partial<QuestionnaireData> = {
          regretMajor: value.regretMajor === 'true',
          careerChangeIntention: value.careerChangeIntention === 'true',
        };
        
        // Only include preferred major if regrets
        if (regrets && value.preferredMajor) {
          updateData.preferredMajor = value.preferredMajor;
        }
        
        // Only include career change target if has intention
        if (hasIntention && value.careerChangeTarget) {
          updateData.careerChangeTarget = value.careerChangeTarget;
        }
        
        onUpdate(updateData);
      }
    });
    
    return () => subscription.unsubscribe();
  }, [form, onUpdate]);
  
  // Prepare stats data with selected indicators
  const regretStats = mockStats.regretMajor.map(stat => ({
    ...stat,
    isSelected: stat.label === selectedOptions.regretMajor,
  }));
  
  const preferredMajorStats = mockStats.preferredMajor.map(stat => ({
    ...stat,
    isSelected: stat.label === selectedOptions.preferredMajor,
  }));
  
  const careerChangeStats = mockStats.careerChangeIntention.map(stat => ({
    ...stat,
    isSelected: stat.label === selectedOptions.careerChangeIntention,
  }));
  
  const careerTargetStats = mockStats.careerChangeTarget.map(stat => ({
    ...stat,
    isSelected: stat.label === selectedOptions.careerChangeTarget,
  }));
  
  return (
    <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
      <div className="md:col-span-2">
        <Form {...form}>
          <form className="space-y-6">
            <QuestionItem
              number={1}
              title="您是否后悔所学专业?"
              required
            >
              <FormField
                control={form.control}
                name="regretMajor"
                render={({ field }) => (
                  <FormItem>
                    <FormControl>
                      <RadioGroup
                        onValueChange={field.onChange}
                        defaultValue={field.value}
                        className="flex space-x-8"
                      >
                        <FormItem className="flex items-center space-x-3 space-y-0">
                          <FormControl>
                            <RadioGroupItem value="true" />
                          </FormControl>
                          <FormLabel className="font-normal">是</FormLabel>
                        </FormItem>
                        <FormItem className="flex items-center space-x-3 space-y-0">
                          <FormControl>
                            <RadioGroupItem value="false" />
                          </FormControl>
                          <FormLabel className="font-normal">否</FormLabel>
                        </FormItem>
                      </RadioGroup>
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </QuestionItem>
            
            {regretsMajor && (
              <QuestionItem
                number={2}
                title="若重来，您会选择什么专业?"
                required
              >
                <FormField
                  control={form.control}
                  name="preferredMajor"
                  render={({ field }) => (
                    <FormItem>
                      <Select
                        onValueChange={field.onChange}
                        defaultValue={field.value}
                      >
                        <FormControl>
                          <SelectTrigger>
                            <SelectValue placeholder="请选择专业" />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          <SelectItem value="计算机/IT">计算机/IT</SelectItem>
                          <SelectItem value="金融/经济">金融/经济</SelectItem>
                          <SelectItem value="医学">医学</SelectItem>
                          <SelectItem value="教育">教育</SelectItem>
                          <SelectItem value="法律">法律</SelectItem>
                          <SelectItem value="艺术设计">艺术设计</SelectItem>
                          <SelectItem value="语言文学">语言文学</SelectItem>
                          <SelectItem value="工程">工程</SelectItem>
                          <SelectItem value="其他">其他</SelectItem>
                        </SelectContent>
                      </Select>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </QuestionItem>
            )}
            
            <QuestionItem
              number={3}
              title="您是否有转行意向?"
              required
            >
              <FormField
                control={form.control}
                name="careerChangeIntention"
                render={({ field }) => (
                  <FormItem>
                    <FormControl>
                      <RadioGroup
                        onValueChange={field.onChange}
                        defaultValue={field.value}
                        className="flex space-x-8"
                      >
                        <FormItem className="flex items-center space-x-3 space-y-0">
                          <FormControl>
                            <RadioGroupItem value="true" />
                          </FormControl>
                          <FormLabel className="font-normal">是</FormLabel>
                        </FormItem>
                        <FormItem className="flex items-center space-x-3 space-y-0">
                          <FormControl>
                            <RadioGroupItem value="false" />
                          </FormControl>
                          <FormLabel className="font-normal">否</FormLabel>
                        </FormItem>
                      </RadioGroup>
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </QuestionItem>
            
            {hasCareerChangeIntention && (
              <QuestionItem
                number={4}
                title="您的转行目标行业是?"
                required
              >
                <FormField
                  control={form.control}
                  name="careerChangeTarget"
                  render={({ field }) => (
                    <FormItem>
                      <Select
                        onValueChange={field.onChange}
                        defaultValue={field.value}
                      >
                        <FormControl>
                          <SelectTrigger>
                            <SelectValue placeholder="请选择目标行业" />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          <SelectItem value="IT/互联网">IT/互联网</SelectItem>
                          <SelectItem value="金融">金融</SelectItem>
                          <SelectItem value="教育培训">教育培训</SelectItem>
                          <SelectItem value="医疗健康">医疗健康</SelectItem>
                          <SelectItem value="文化/传媒">文化/传媒</SelectItem>
                          <SelectItem value="公务员/事业单位">公务员/事业单位</SelectItem>
                          <SelectItem value="自主创业">自主创业</SelectItem>
                          <SelectItem value="其他">其他</SelectItem>
                        </SelectContent>
                      </Select>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </QuestionItem>
            )}
          </form>
        </Form>
      </div>
      
      <div className="space-y-6">
        <AnswerStats 
          title="是否后悔所学专业" 
          options={regretStats} 
        />
        
        {regretsMajor && (
          <AnswerStats 
            title="理想专业分布" 
            options={preferredMajorStats} 
          />
        )}
        
        <AnswerStats 
          title="是否有转行意向" 
          options={careerChangeStats} 
        />
        
        {hasCareerChangeIntention && (
          <AnswerStats 
            title="转行目标行业" 
            options={careerTargetStats} 
          />
        )}
      </div>
    </div>
  );
}
