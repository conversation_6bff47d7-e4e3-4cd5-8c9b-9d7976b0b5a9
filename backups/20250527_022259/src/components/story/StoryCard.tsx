import { useState, useRef, useEffect } from 'react';
import { Card, CardContent, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { ThumbsUp, ThumbsDown, Calendar, User, Tag, Bookmark } from 'lucide-react';
import { formatDate, truncateText, cn } from '@/lib/utils';
import { useToast } from '@/components/ui/use-toast';
import { voteStory } from '@/lib/api';
import { motion } from 'framer-motion';

// Map tag IDs to labels
const TAG_LABELS: Record<string, string> = {
  'career-change': '转行经历',
  'job-hunting': '求职故事',
  'interview': '面试经验',
  'salary': '薪资谈判',
  'work-life': '工作生活',
  'advice': '建议分享',
  'internship': '实习经历',
  'overseas': '海外就业',
  'startup': '创业经历',
};

// Map category IDs to labels
const CATEGORY_LABELS: Record<string, string> = {
  'success': '成功故事',
  'challenge': '挑战经历',
  'advice': '建议分享',
  'experience': '经验分享',
};

// Map education level IDs to labels
const EDUCATION_LABELS: Record<string, string> = {
  'high-school': '高中及以下',
  'college': '专科',
  'bachelor': '本科',
  'master': '硕士',
  'phd': '博士',
};

// Map industry IDs to labels
const INDUSTRY_LABELS: Record<string, string> = {
  'it': '互联网/IT',
  'finance': '金融',
  'education': '教育',
  'healthcare': '医疗',
  'manufacturing': '制造业',
  'service': '服务业',
  'government': '政府/公共服务',
  'media': '媒体/文化',
};

interface Story {
  id: number;
  title: string;
  content: string;
  author: string;
  createdAt: string;
  likes: number;
  dislikes: number;
  tags: string[];
  category?: string;
  educationLevel?: string;
  industry?: string;
}

interface StoryCardProps {
  story: Story;
  onClick?: () => void;
  searchQuery?: string; // 用于高亮搜索关键词
  compact?: boolean; // 紧凑模式
  featured?: boolean; // 特色故事
  className?: string;
}

export default function StoryCard({
  story,
  onClick,
  searchQuery = '',
  compact = false,
  featured = false,
  className
}: StoryCardProps) {
  const { toast } = useToast();
  const [isExpanded, setIsExpanded] = useState(featured);
  const [likes, setLikes] = useState(story.likes);
  const [dislikes, setDislikes] = useState(story.dislikes);
  const [userVote, setUserVote] = useState<'like' | 'dislike' | null>(null);
  const [isVoting, setIsVoting] = useState(false);
  const [isHovered, setIsHovered] = useState(false);
  const contentRef = useRef<HTMLParagraphElement>(null);

  // 计算截断长度
  const truncateLength = compact ? 100 : 150;

  // 处理内容高亮
  const highlightContent = (content: string) => {
    if (!searchQuery) return content;

    try {
      const regex = new RegExp(`(${searchQuery})`, 'gi');
      return content.replace(regex, '<mark>$1</mark>');
    } catch (e) {
      // 如果正则表达式无效，返回原始内容
      return content;
    }
  };

  // 处理HTML内容
  const createMarkup = (content: string) => {
    return { __html: highlightContent(content) };
  };

  // Truncate content if not expanded
  const displayContent = isExpanded
    ? story.content
    : truncateText(story.content, truncateLength);

  // Toggle content expansion
  const toggleExpand = (e: React.MouseEvent) => {
    e.stopPropagation();
    setIsExpanded(!isExpanded);
  };

  // Handle card click
  const handleCardClick = () => {
    if (onClick) {
      onClick();
    }
  };

  // 处理鼠标悬停
  const handleMouseEnter = () => {
    setIsHovered(true);
  };

  const handleMouseLeave = () => {
    setIsHovered(false);
  };

  // Handle voting
  const handleVote = async (e: React.MouseEvent, voteType: 'like' | 'dislike') => {
    e.stopPropagation();
    // Prevent double voting
    if (userVote === voteType || isVoting) {
      return;
    }

    try {
      setIsVoting(true);

      // Optimistic update
      if (userVote === null) {
        // First vote
        if (voteType === 'like') {
          setLikes(likes + 1);
        } else {
          setDislikes(dislikes + 1);
        }
      } else {
        // Changing vote
        if (voteType === 'like') {
          setLikes(likes + 1);
          setDislikes(dislikes - 1);
        } else {
          setLikes(likes - 1);
          setDislikes(dislikes + 1);
        }
      }

      setUserVote(voteType);

      // Call API
      const response = await voteStory({
        storyId: story.id,
        voteType,
      });

      // Update with actual values from server
      if (response.success) {
        setLikes(response.likes);
        setDislikes(response.dislikes);
      } else {
        // Revert optimistic update on error
        setLikes(story.likes);
        setDislikes(story.dislikes);
        setUserVote(null);

        toast({
          title: '投票失败',
          description: '请稍后重试',
          variant: 'destructive',
        });
      }
    } catch (error) {
      console.error('Error voting:', error);

      // Revert optimistic update on error
      setLikes(story.likes);
      setDislikes(story.dislikes);
      setUserVote(null);

      toast({
        title: '投票失败',
        description: '发生错误，请稍后重试',
        variant: 'destructive',
      });
    } finally {
      setIsVoting(false);
    }
  };

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.3 }}
      whileHover={{ scale: featured ? 1 : 1.02 }}
    >
      <Card
        className={cn(
          "h-full flex flex-col cursor-pointer transition-all duration-300",
          isHovered ? "shadow-lg" : "shadow-sm",
          featured && "border-primary border-2",
          compact && "max-h-[300px]",
          className
        )}
        onClick={handleCardClick}
        onMouseEnter={handleMouseEnter}
        onMouseLeave={handleMouseLeave}
      >
        <CardHeader className={cn("pb-2", compact && "p-4")}>
          <div className="flex items-start justify-between">
            <CardTitle className={cn("text-lg", featured && "text-xl")}>
              {searchQuery ? (
                <span dangerouslySetInnerHTML={createMarkup(story.title)} />
              ) : (
                story.title
              )}
            </CardTitle>

            {featured && (
              <Badge variant="default" className="ml-2">
                <Bookmark className="h-3 w-3 mr-1" />
                精选
              </Badge>
            )}
          </div>

          <div className="flex flex-wrap gap-1 mt-2">
            {story.tags.slice(0, compact ? 2 : undefined).map((tag) => (
              <Badge key={tag} variant="secondary" className="text-xs">
                <Tag className="h-3 w-3 mr-1" />
                {TAG_LABELS[tag] || tag}
              </Badge>
            ))}

            {!compact && story.category && (
              <Badge variant="outline" className="text-xs bg-blue-50">
                {CATEGORY_LABELS[story.category] || story.category}
              </Badge>
            )}

            {!compact && story.educationLevel && (
              <Badge variant="outline" className="text-xs bg-green-50">
                {EDUCATION_LABELS[story.educationLevel] || story.educationLevel}
              </Badge>
            )}

            {!compact && story.industry && (
              <Badge variant="outline" className="text-xs bg-purple-50">
                {INDUSTRY_LABELS[story.industry] || story.industry}
              </Badge>
            )}

            {compact && story.tags.length > 2 && (
              <Badge variant="outline" className="text-xs">
                +{story.tags.length - 2}
              </Badge>
            )}
          </div>
        </CardHeader>

        <CardContent className={cn("flex-grow", compact && "p-4 pt-0")}>
          <p
            ref={contentRef}
            className={cn(
              "text-sm text-gray-700 whitespace-pre-line",
              featured && "text-base"
            )}
          >
            {searchQuery ? (
              <span dangerouslySetInnerHTML={createMarkup(displayContent)} />
            ) : (
              displayContent
            )}
            {!isExpanded && story.content.length > truncateLength && '...'}
          </p>

          {story.content.length > truncateLength && (
            <Button
              variant="link"
              className="p-0 h-auto text-sm mt-1"
              onClick={toggleExpand}
            >
              {isExpanded ? '收起' : '展开全文'}
            </Button>
          )}
        </CardContent>

        <CardFooter className={cn(
          "flex justify-between items-center pt-2 border-t",
          compact && "p-4 pt-2"
        )}>
          <div className="flex items-center text-xs text-gray-500">
            <User className="h-3 w-3 mr-1" />
            <span>{story.author}</span>
            <span className="mx-1">·</span>
            <Calendar className="h-3 w-3 mr-1" />
            <span>{formatDate(new Date(story.createdAt))}</span>
          </div>

          <div className="flex items-center gap-2">
            <Button
              variant="ghost"
              size="sm"
              className={cn(
                "flex items-center gap-1 px-2",
                userVote === 'like' && "text-green-600"
              )}
              onClick={(e) => handleVote(e, 'like')}
              disabled={isVoting}
            >
              <ThumbsUp className="h-4 w-4" />
              <span>{likes}</span>
            </Button>

            <Button
              variant="ghost"
              size="sm"
              className={cn(
                "flex items-center gap-1 px-2",
                userVote === 'dislike' && "text-red-600"
              )}
              onClick={(e) => handleVote(e, 'dislike')}
              disabled={isVoting}
            >
              <ThumbsDown className="h-4 w-4" />
              <span>{dislikes}</span>
            </Button>
          </div>
        </CardFooter>
      </Card>
    </motion.div>
  );
}
