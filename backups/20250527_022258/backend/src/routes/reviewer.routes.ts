/**
 * 审核员路由
 *
 * 处理审核员相关的API请求
 */

import { Hono } from 'hono';
import { authMiddleware } from '../middlewares/auth.middleware';
import { roleMiddleware } from '../middlewares/role.middleware';

const reviewerRoutes = new Hono();

// 应用认证中间件
reviewerRoutes.use('*', authMiddleware);

// 应用角色中间件 - 只允许审核员和管理员访问
reviewerRoutes.use('*', roleMiddleware(['reviewer', 'admin', 'superadmin']));

/**
 * 获取审核员仪表盘统计数据
 */
reviewerRoutes.get('/dashboard/stats', async (c) => {
  try {
    const user = c.get('user');
    console.log('获取审核员仪表盘统计数据，用户:', user);

    // 从数据库获取真实统计数据
    const db = c.env.DB;

    // 获取待审核故事数量
    const pendingStoriesResult = await db.prepare(`
      SELECT COUNT(*) as count
      FROM stories
      WHERE status = 'pending'
    `).first();

    // 获取已批准故事数量（按审核员筛选）
    const approvedStoriesResult = await db.prepare(`
      SELECT COUNT(*) as count
      FROM stories
      WHERE status = 'approved' AND reviewer_id = ?
    `).bind(user.id || user.username).first();

    // 获取已拒绝故事数量（按审核员筛选）
    const rejectedStoriesResult = await db.prepare(`
      SELECT COUNT(*) as count
      FROM stories
      WHERE status = 'rejected' AND reviewer_id = ?
    `).bind(user.id || user.username).first();

    // 获取已批准问卷心声数量（按审核员筛选）
    const approvedVoicesResult = await db.prepare(`
      SELECT COUNT(*) as count
      FROM questionnaire_responses
      WHERE status = 'approved' AND reviewer_id = ? AND step6_content IS NOT NULL AND step6_content != ''
    `).bind(user.id || user.username).first();

    // 获取已拒绝问卷心声数量（按审核员筛选）
    const rejectedVoicesResult = await db.prepare(`
      SELECT COUNT(*) as count
      FROM questionnaire_responses
      WHERE status = 'rejected' AND reviewer_id = ? AND step6_content IS NOT NULL AND step6_content != ''
    `).bind(user.id || user.username).first();

    const pendingStories = pendingStoriesResult?.count || 0;
    const approvedStories = approvedStoriesResult?.count || 0;
    const rejectedStories = rejectedStoriesResult?.count || 0;
    const approvedVoices = approvedVoicesResult?.count || 0;
    const rejectedVoices = rejectedVoicesResult?.count || 0;

    // 计算审核通过率
    const totalReviewed = approvedStories + rejectedStories + approvedVoices + rejectedVoices;
    const reviewRate = totalReviewed > 0 ? Math.round((approvedStories / totalReviewed) * 100) : 0;

    const stats = {
      pendingStories,
      approvedStories,
      rejectedStories,
      reviewRate,
      totalReviewed,
      approvedVoices,
      rejectedVoices,
      lastUpdated: new Date().toISOString()
    };

    console.log('审核员统计数据:', stats);

    return c.json({
      success: true,
      ...stats
    });

  } catch (error) {
    console.error('获取审核员统计数据失败:', error);

    return c.json({
      success: false,
      error: '获取统计数据失败',
      // 提供默认值以防止前端错误
      pendingStories: 0,
      approvedStories: 0,
      rejectedStories: 0,
      reviewRate: 0,
      totalReviewed: 0,
      approvedVoices: 0,
      rejectedVoices: 0
    }, 500);
  }
});

/**
 * 获取待审核内容列表
 */
reviewerRoutes.get('/pending-content', async (c) => {
  try {
    const db = c.env.DB;
    const page = parseInt(c.req.query('page') || '1');
    const limit = parseInt(c.req.query('limit') || '10');
    const offset = (page - 1) * limit;

    // 获取待审核故事
    const stories = await db.prepare(`
      SELECT id, title, content, created_at, is_anonymous
      FROM stories
      WHERE status = 'pending'
      ORDER BY created_at DESC
      LIMIT ? OFFSET ?
    `).bind(limit, offset).all();

    // 获取待审核问卷心声
    const voices = await db.prepare(`
      SELECT id, step6_content as content, created_at, is_anonymous
      FROM questionnaire_responses
      WHERE status = 'pending' AND step6_content IS NOT NULL AND step6_content != ''
      ORDER BY created_at DESC
      LIMIT ? OFFSET ?
    `).bind(limit, offset).all();

    return c.json({
      success: true,
      data: {
        stories: stories.results || [],
        voices: voices.results || [],
        pagination: {
          page,
          limit,
          total: (stories.results?.length || 0) + (voices.results?.length || 0)
        }
      }
    });

  } catch (error) {
    console.error('获取待审核内容失败:', error);
    return c.json({
      success: false,
      error: '获取待审核内容失败'
    }, 500);
  }
});

/**
 * 审核故事
 */
reviewerRoutes.post('/review/story/:id', async (c) => {
  try {
    const storyId = c.req.param('id');
    const { action, reason } = await c.req.json();
    const user = c.get('user');

    if (!['approve', 'reject'].includes(action)) {
      return c.json({
        success: false,
        error: '无效的审核操作'
      }, 400);
    }

    const db = c.env.DB;
    const status = action === 'approve' ? 'approved' : 'rejected';

    await db.prepare(`
      UPDATE stories
      SET status = ?, reviewer_id = ?, review_reason = ?, reviewed_at = ?
      WHERE id = ?
    `).bind(status, user.id || user.username, reason || '', new Date().toISOString(), storyId).run();

    return c.json({
      success: true,
      message: `故事已${action === 'approve' ? '批准' : '拒绝'}`
    });

  } catch (error) {
    console.error('审核故事失败:', error);
    return c.json({
      success: false,
      error: '审核操作失败'
    }, 500);
  }
});

/**
 * 审核问卷心声
 */
reviewerRoutes.post('/review/voice/:id', async (c) => {
  try {
    const voiceId = c.req.param('id');
    const { action, reason } = await c.req.json();
    const user = c.get('user');

    if (!['approve', 'reject'].includes(action)) {
      return c.json({
        success: false,
        error: '无效的审核操作'
      }, 400);
    }

    const db = c.env.DB;
    const status = action === 'approve' ? 'approved' : 'rejected';

    await db.prepare(`
      UPDATE questionnaire_responses
      SET status = ?, reviewer_id = ?, review_reason = ?, reviewed_at = ?
      WHERE id = ?
    `).bind(status, user.id || user.username, reason || '', new Date().toISOString(), voiceId).run();

    return c.json({
      success: true,
      message: `问卷心声已${action === 'approve' ? '批准' : '拒绝'}`
    });

  } catch (error) {
    console.error('审核问卷心声失败:', error);
    return c.json({
      success: false,
      error: '审核操作失败'
    }, 500);
  }
});

export default reviewerRoutes;
