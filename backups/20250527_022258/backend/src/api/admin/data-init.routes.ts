/**
 * 数据初始化路由
 */

import { Hono } from 'hono';
import { 
  initializeAllData, 
  initializeUsers, 
  initializeQuestionnaires, 
  initializeStories, 
  initializeStatistics 
} from './data-init.controller';

// Define environment interface
interface Env {
  SURVEY_KV: KVNamespace;
}

// Create Hono app
const app = new Hono<{ Bindings: Env }>();

// Data initialization routes
app.get('/init', initializeAllData);
app.get('/init/users', initializeUsers);
app.get('/init/questionnaires', initializeQuestionnaires);
app.get('/init/stories', initializeStories);
app.get('/init/statistics', initializeStatistics);

export default app;
