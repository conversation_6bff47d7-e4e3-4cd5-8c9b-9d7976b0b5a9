import { useState } from 'react';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { X, Check, ChevronDown, ChevronUp } from 'lucide-react';
import {
  Collapsible,
  CollapsibleContent,
  CollapsibleTrigger,
} from '@/components/ui/collapsible.tsx';
import { cn } from '@/lib/utils';

interface Tag {
  id: string;
  label: string;
  category?: string;
}

interface TagCategory {
  id: string;
  label: string;
  tags: Tag[];
}

interface MultiTagSelectorProps {
  tags: Tag[];
  selectedTags: string[];
  onTagsChange: (tags: string[]) => void;
  maxTags?: number;
  className?: string;
}

export default function MultiTagSelector({
  tags,
  selectedTags,
  onTagsChange,
  maxTags = 5,
  className,
}: MultiTagSelectorProps) {
  const [isOpen, setIsOpen] = useState(false);

  // 将标签按类别分组
  const categorizedTags: TagCategory[] = [];

  // 创建"全部"类别
  categorizedTags.push({
    id: 'all',
    label: '全部标签',
    tags: tags.filter(tag => !tag.category),
  });

  // 按类别分组其他标签
  const categories = [...new Set(tags.filter(tag => tag.category).map(tag => tag.category))];
  categories.forEach(category => {
    if (category) {
      categorizedTags.push({
        id: category,
        label: getCategoryLabel(category),
        tags: tags.filter(tag => tag.category === category),
      });
    }
  });

  // 获取类别显示名称
  function getCategoryLabel(categoryId: string): string {
    const categoryLabels: Record<string, string> = {
      'job': '求职相关',
      'education': '学历相关',
      'industry': '行业相关',
      'experience': '经验相关',
    };
    return categoryLabels[categoryId] || categoryId;
  }

  // 切换标签选择状态
  const toggleTag = (tagId: string) => {
    if (selectedTags.includes(tagId)) {
      // 移除标签
      onTagsChange(selectedTags.filter(id => id !== tagId));
    } else {
      // 添加标签，但要检查是否超过最大数量
      if (selectedTags.length < maxTags) {
        onTagsChange([...selectedTags, tagId]);
      }
    }
  };

  // 清除所有选中的标签
  const clearTags = () => {
    onTagsChange([]);
  };

  return (
    <div className={cn("w-full", className)}>
      <div className="flex flex-wrap gap-2 mb-2">
        {selectedTags.length > 0 ? (
          selectedTags.map(tagId => {
            const tag = tags.find(t => t.id === tagId);
            return (
              <Badge key={tagId} className="flex items-center gap-1">
                {tag?.label || tagId}
                <X
                  className="h-3 w-3 cursor-pointer"
                  onClick={() => toggleTag(tagId)}
                />
              </Badge>
            );
          })
        ) : (
          <span className="text-sm text-gray-500">未选择标签</span>
        )}

        {selectedTags.length > 0 && (
          <Button
            variant="ghost"
            size="sm"
            className="h-6 px-2 text-xs"
            onClick={clearTags}
          >
            清除
          </Button>
        )}
      </div>

      <Collapsible open={isOpen} onOpenChange={setIsOpen}>
        <CollapsibleTrigger asChild>
          <Button
            variant="outline"
            size="sm"
            className="w-full flex justify-between items-center"
          >
            <span>选择标签 ({selectedTags.length}/{maxTags})</span>
            {isOpen ? (
              <ChevronUp className="h-4 w-4" />
            ) : (
              <ChevronDown className="h-4 w-4" />
            )}
          </Button>
        </CollapsibleTrigger>
        <CollapsibleContent>
          <div className="mt-2 border rounded-md p-4 space-y-4">
            {categorizedTags.map(category => (
              <div key={category.id}>
                <h4 className="text-sm font-medium mb-2">{category.label}</h4>
                <div className="flex flex-wrap gap-2">
                  {category.tags.map(tag => (
                    <Badge
                      key={tag.id}
                      variant={selectedTags.includes(tag.id) ? "default" : "outline"}
                      className="cursor-pointer flex items-center gap-1"
                      onClick={() => toggleTag(tag.id)}
                    >
                      {tag.label}
                      {selectedTags.includes(tag.id) && (
                        <Check className="h-3 w-3" />
                      )}
                    </Badge>
                  ))}
                </div>
              </div>
            ))}

            {selectedTags.length >= maxTags && (
              <p className="text-xs text-amber-600">
                已达到最大标签数量 ({maxTags})
              </p>
            )}
          </div>
        </CollapsibleContent>
      </Collapsible>
    </div>
  );
}
