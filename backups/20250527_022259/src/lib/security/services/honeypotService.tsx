/**
 * 蜜罐服务
 */

import React from 'react';
import { HoneypotConfig, ValidationResult } from '../types';

/**
 * 蜜罐字段定义
 */
interface HoneypotField {
  name: string;
  label: string;
  type: string;
}

/**
 * 蜜罐服务类
 */
export class HoneypotService {
  private config: HoneypotConfig;
  private honeypotFields: HoneypotField[] = [];
  private honeypotFilled: boolean = false;

  /**
   * 构造函数
   */
  constructor() {
    this.config = {
      enabled: false,
      fieldCount: 0,
      silentRejection: true
    };
  }

  /**
   * 初始化服务
   * @param config 蜜罐配置
   */
  public initialize(config: HoneypotConfig): void {
    this.config = { ...config };
    
    // 生成蜜罐字段
    this.generateHoneypotFields();
  }

  /**
   * 更新配置
   * @param config 蜜罐配置
   */
  public updateConfig(config: HoneypotConfig): void {
    const oldFieldCount = this.config.fieldCount;
    this.config = { ...this.config, ...config };
    
    // 如果字段数量变更，重新生成蜜罐字段
    if (oldFieldCount !== this.config.fieldCount) {
      this.generateHoneypotFields();
    }
  }

  /**
   * 验证表单
   * @param formData 表单数据
   */
  public validateForm(formData: any): ValidationResult {
    if (!this.config.enabled) {
      return { valid: true };
    }
    
    // 检查蜜罐字段是否被填写
    for (const field of this.honeypotFields) {
      if (formData[field.name] && formData[field.name].trim() !== '') {
        this.honeypotFilled = true;
        
        // 如果配置为静默拒绝，返回有效（但后端会忽略提交）
        if (this.config.silentRejection) {
          return { valid: true };
        }
        
        // 否则返回无效
        return { valid: false, error: '提交被拒绝，请重试' };
      }
    }
    
    return { valid: true };
  }

  /**
   * 渲染蜜罐字段
   */
  public renderHoneypotFields(): React.ReactNode {
    if (!this.config.enabled) {
      return null;
    }
    
    return (
      <div aria-hidden="true" style={{ position: 'absolute', left: '-9999px', opacity: 0, pointerEvents: 'none' }}>
        {this.honeypotFields.map((field, index) => (
          <div key={index}>
            <label htmlFor={field.name}>{field.label}</label>
            <input
              type={field.type}
              id={field.name}
              name={field.name}
              tabIndex={-1}
              autoComplete="off"
              aria-hidden="true"
              onChange={(e) => {
                if (e.target.value.trim() !== '') {
                  this.honeypotFilled = true;
                }
              }}
            />
          </div>
        ))}
      </div>
    );
  }

  /**
   * 生成蜜罐字段
   */
  private generateHoneypotFields(): void {
    // 清空现有字段
    this.honeypotFields = [];
    
    // 常见的表单字段名称
    const commonFields = [
      { name: 'name', label: 'Name', type: 'text' },
      { name: 'email', label: 'Email', type: 'email' },
      { name: 'phone', label: 'Phone', type: 'tel' },
      { name: 'address', label: 'Address', type: 'text' },
      { name: 'website', label: 'Website', type: 'url' },
      { name: 'company', label: 'Company', type: 'text' },
      { name: 'message', label: 'Message', type: 'textarea' },
      { name: 'subject', label: 'Subject', type: 'text' },
      { name: 'comment', label: 'Comment', type: 'textarea' },
      { name: 'username', label: 'Username', type: 'text' }
    ];
    
    // 随机选择字段
    const selectedIndices = new Set<number>();
    while (selectedIndices.size < this.config.fieldCount && selectedIndices.size < commonFields.length) {
      const index = Math.floor(Math.random() * commonFields.length);
      selectedIndices.add(index);
    }
    
    // 添加选中的字段
    selectedIndices.forEach(index => {
      const field = commonFields[index];
      // 添加 _hp 后缀以避免与实际字段冲突
      this.honeypotFields.push({
        name: `${field.name}_hp`,
        label: field.label,
        type: field.type
      });
    });
  }
}
