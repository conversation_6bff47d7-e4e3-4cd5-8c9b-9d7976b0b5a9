import React from 'react';
import { Link, Outlet } from 'react-router-dom';
import { cn } from '@/lib/utils';
import NotificationCenter from '@/components/notification/NotificationCenter';
import { useAuth } from '@/hooks/useAuth';

interface MainLayoutProps {
  className?: string;
}

/**
 * 主布局组件
 *
 * 提供应用程序的主要布局结构，包括导航栏和内容区域
 */
export default function MainLayout({ className }: MainLayoutProps) {
  const { isAuthenticated } = useAuth();

  return (
    <div className={cn('min-h-screen bg-background', className)}>
      <header className="sticky top-0 z-50 w-full border-b bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60">
        <div className="container flex h-14 items-center justify-between">
          <div className="flex">
            <Link to="/" className="mr-6 flex items-center space-x-2">
              <span className="font-bold">大学生就业调查问卷与故事墙</span>
            </Link>
            <nav className="flex items-center space-x-6 text-sm font-medium">
              <Link to="/questionnaire" className="transition-colors hover:text-foreground/80">问卷调查</Link>
              <Link to="/story-wall" className="transition-colors hover:text-foreground/80">故事墙</Link>
              <Link to="/questionnaire-voices" className="transition-colors hover:text-foreground/80">问卷心声</Link>
              <Link to="/visualization" className="transition-colors hover:text-foreground/80">数据可视化</Link>
              <Link to="/my-content" className="transition-colors hover:text-foreground/80">我的内容</Link>
              <Link to="/about" className="transition-colors hover:text-foreground/80">关于项目</Link>
            </nav>
          </div>
          <div className="flex items-center space-x-2">
            {isAuthenticated && <NotificationCenter />}
          </div>
        </div>
      </header>
      <main className="container py-6">
        <Outlet />
      </main>
      <footer className="border-t py-6">
        <div className="container flex flex-col items-center justify-between gap-4 md:flex-row">
          <p className="text-center text-sm leading-loose text-muted-foreground md:text-left">
            © 2024 大学生就业调查问卷与故事墙. 保留所有权利.
          </p>
        </div>
      </footer>
    </div>
  );
}
