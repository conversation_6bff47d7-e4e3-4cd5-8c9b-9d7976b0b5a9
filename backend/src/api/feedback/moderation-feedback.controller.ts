/**
 * 审核反馈控制器
 * 
 * 处理用户对审核结果的反馈
 */

import { Context } from 'hono';
import { zValidator } from '@hono/zod-validator';
import { z } from 'zod';
import { PrismaClient } from '@prisma/client';
import { Env } from '../../types';
import { rateLimit } from '../../middlewares/rateLimit.middleware';

// 初始化Prisma客户端
const prisma = new PrismaClient();

// 定义反馈提交验证模式
const moderationFeedbackSchema = z.object({
  contentId: z.string(),
  contentType: z.string(),
  moderationId: z.string().optional(),
  feedbackType: z.enum(['disagree', 'appeal', 'report', 'suggestion']),
  reason: z.string().min(5, { message: '原因至少需要5个字符' }).max(500, { message: '原因最多500个字符' }),
  details: z.string().max(2000, { message: '详情最多2000个字符' }).optional(),
  contactEmail: z.string().email({ message: '请提供有效的邮箱地址' }).optional(),
});

/**
 * 提交审核反馈
 */
export const submitModerationFeedback = [
  rateLimit({ limit: 10, window: 60 * 60 }), // 10 requests per hour
  zValidator('json', moderationFeedbackSchema),
  async (c: Context<{ Bindings: Env }>) => {
    const data = c.req.valid('json');
    const ip = c.req.header('CF-Connecting-IP') || c.req.header('X-Forwarded-For') || 'unknown';
    const userAgent = c.req.header('User-Agent') || 'unknown';

    try {
      // 创建反馈记录
      const feedback = await prisma.moderationFeedback.create({
        data: {
          contentId: data.contentId,
          contentType: data.contentType,
          moderationId: data.moderationId,
          feedbackType: data.feedbackType,
          reason: data.reason,
          details: data.details || '',
          contactEmail: data.contactEmail,
          ipAddress: ip,
          userAgent: userAgent,
          status: 'pending',
        },
      });

      // 如果是申诉，创建申诉记录
      if (data.feedbackType === 'appeal') {
        await prisma.appealRequest.create({
          data: {
            feedbackId: feedback.id,
            contentId: data.contentId,
            contentType: data.contentType,
            reason: data.reason,
            status: 'pending',
            ipAddress: ip,
            userAgent: userAgent,
          },
        });
      }

      // 通知管理员
      // TODO: 实现通知功能

      return c.json({
        success: true,
        message: '反馈已提交，感谢您的反馈',
        feedbackId: feedback.id,
      });
    } catch (error) {
      console.error('提交审核反馈失败:', error);
      return c.json({
        success: false,
        error: '提交反馈失败，请稍后再试',
      }, 500);
    }
  },
];

/**
 * 获取反馈状态
 */
export const getFeedbackStatus = async (c: Context<{ Bindings: Env }>) => {
  const { id } = c.req.param();

  try {
    // 获取反馈记录
    const feedback = await prisma.moderationFeedback.findUnique({
      where: { id },
    });

    if (!feedback) {
      return c.json({
        success: false,
        error: '找不到反馈记录',
      }, 404);
    }

    // 如果是申诉，获取申诉记录
    let appeal = null;
    if (feedback.feedbackType === 'appeal') {
      appeal = await prisma.appealRequest.findFirst({
        where: { feedbackId: feedback.id },
      });
    }

    return c.json({
      success: true,
      feedback: {
        id: feedback.id,
        status: feedback.status,
        feedbackType: feedback.feedbackType,
        createdAt: feedback.createdAt,
        updatedAt: feedback.updatedAt,
        response: feedback.response,
        appeal: appeal ? {
          status: appeal.status,
          result: appeal.result,
          updatedAt: appeal.updatedAt,
        } : null,
      },
    });
  } catch (error) {
    console.error('获取反馈状态失败:', error);
    return c.json({
      success: false,
      error: '获取反馈状态失败，请稍后再试',
    }, 500);
  }
};

/**
 * 获取内容的反馈列表
 */
export const getContentFeedbacks = async (c: Context<{ Bindings: Env }>) => {
  const { contentId, contentType } = c.req.query();

  if (!contentId || !contentType) {
    return c.json({
      success: false,
      error: '缺少必要参数',
    }, 400);
  }

  try {
    // 获取反馈记录
    const feedbacks = await prisma.moderationFeedback.findMany({
      where: {
        contentId,
        contentType,
      },
      orderBy: {
        createdAt: 'desc',
      },
    });

    return c.json({
      success: true,
      feedbacks: feedbacks.map(feedback => ({
        id: feedback.id,
        feedbackType: feedback.feedbackType,
        reason: feedback.reason,
        status: feedback.status,
        createdAt: feedback.createdAt,
        response: feedback.response,
      })),
    });
  } catch (error) {
    console.error('获取内容反馈列表失败:', error);
    return c.json({
      success: false,
      error: '获取反馈列表失败，请稍后再试',
    }, 500);
  }
};
