import { ReactNode } from 'react';

interface QuestionItemProps {
  number: number;
  title: string;
  required?: boolean;
  children: ReactNode;
  description?: string;
}

export default function QuestionItem({
  number,
  title,
  required = false,
  children,
  description,
}: QuestionItemProps) {
  return (
    <div className="mb-8 p-4 border border-gray-200 rounded-lg">
      <div className="flex items-start mb-3">
        <div className="flex-shrink-0 bg-primary text-primary-foreground w-8 h-8 rounded-full flex items-center justify-center mr-3">
          {number}
        </div>
        <div>
          <h3 className="text-lg font-medium">
            {title}
            {required && <span className="text-red-500 ml-1">*</span>}
          </h3>
          {description && (
            <p className="text-sm text-gray-500 mt-1">{description}</p>
          )}
        </div>
      </div>
      
      <div className="ml-11">
        {children}
      </div>
    </div>
  );
}
