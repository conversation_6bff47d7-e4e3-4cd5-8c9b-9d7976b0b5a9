import React, { useState, useEffect } from 'react';
import { <PERSON><PERSON><PERSON>, <PERSON>, <PERSON>Axis, <PERSON>Axis, CartesianGrid, Tooltip, Responsive<PERSON>ontainer, Legend, <PERSON>Chart, PolarGrid, PolarAngleAxis, PolarRadiusAxis, Radar } from 'recharts';
import { But<PERSON> } from '@/components/ui/button';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Loader2, RefreshCw, Users, Activity, Clock } from 'lucide-react';

interface UserActivityData {
  userType: string;
  dailyActiveUsers: number;
  weeklyActiveUsers: number;
  monthlyActiveUsers: number;
  avgSessionDuration: number;
  avgActionsPerSession: number;
  retentionRate: number;
  engagementScore: number;
}

interface UserActivityComparisonChartProps {
  height?: number;
  showControls?: boolean;
  chartType?: 'bar' | 'radar';
}

const UserActivityComparisonChart: React.FC<UserActivityComparisonChartProps> = ({ 
  height = 300,
  showControls = true,
  chartType = 'bar'
}) => {
  const [data, setData] = useState<UserActivityData[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [selectedChartType, setSelectedChartType] = useState<'bar' | 'radar'>(chartType);
  const [selectedMetric, setSelectedMetric] = useState<'activeUsers' | 'sessionDuration' | 'engagement' | 'retention'>('activeUsers');

  // 用户类型映射
  const userTypeMap: { [key: string]: string } = {
    'student': '学生用户',
    'graduate': '毕业生用户',
    'professional': '职场用户',
    'admin': '管理员用户',
    'reviewer': '审核员用户'
  };

  // 获取用户活跃度对比数据
  const fetchUserActivityData = async () => {
    setLoading(true);
    setError(null);
    
    try {
      const response = await fetch('/api/admin/dashboard/user-activity-comparison');
      
      if (!response.ok) {
        throw new Error('获取用户活跃度对比数据失败');
      }
      
      const result = await response.json();
      
      if (result.success) {
        // 转换数据格式
        const formattedData = result.data.map((item: any) => ({
          userType: userTypeMap[item.user_type] || item.user_type,
          dailyActiveUsers: item.daily_active_users || 0,
          weeklyActiveUsers: item.weekly_active_users || 0,
          monthlyActiveUsers: item.monthly_active_users || 0,
          avgSessionDuration: item.avg_session_duration || 0,
          avgActionsPerSession: item.avg_actions_per_session || 0,
          retentionRate: item.retention_rate || 0,
          engagementScore: item.engagement_score || 0
        }));
        setData(formattedData);
      } else {
        throw new Error(result.message || '数据格式错误');
      }
    } catch (err) {
      console.error('用户活跃度对比数据获取错误:', err);
      setError(err instanceof Error ? err.message : '未知错误');
      
      // 使用模拟数据作为降级
      generateMockData();
    } finally {
      setLoading(false);
    }
  };

  // 生成模拟数据
  const generateMockData = () => {
    const mockData: UserActivityData[] = [
      {
        userType: '学生用户',
        dailyActiveUsers: 245,
        weeklyActiveUsers: 1680,
        monthlyActiveUsers: 6720,
        avgSessionDuration: 12.5,
        avgActionsPerSession: 8.3,
        retentionRate: 78.5,
        engagementScore: 85.2
      },
      {
        userType: '毕业生用户',
        dailyActiveUsers: 189,
        weeklyActiveUsers: 1323,
        monthlyActiveUsers: 5292,
        avgSessionDuration: 15.2,
        avgActionsPerSession: 11.7,
        retentionRate: 82.1,
        engagementScore: 88.9
      },
      {
        userType: '职场用户',
        dailyActiveUsers: 156,
        weeklyActiveUsers: 1092,
        monthlyActiveUsers: 4368,
        avgSessionDuration: 9.8,
        avgActionsPerSession: 6.4,
        retentionRate: 71.3,
        engagementScore: 76.8
      },
      {
        userType: '管理员用户',
        dailyActiveUsers: 12,
        weeklyActiveUsers: 84,
        monthlyActiveUsers: 336,
        avgSessionDuration: 25.6,
        avgActionsPerSession: 18.9,
        retentionRate: 95.2,
        engagementScore: 96.7
      },
      {
        userType: '审核员用户',
        dailyActiveUsers: 8,
        weeklyActiveUsers: 56,
        monthlyActiveUsers: 224,
        avgSessionDuration: 22.1,
        avgActionsPerSession: 15.3,
        retentionRate: 91.8,
        engagementScore: 93.4
      }
    ];
    
    setData(mockData);
  };

  // 初始化数据
  useEffect(() => {
    fetchUserActivityData();
  }, []);

  // 获取显示数据
  const getDisplayData = () => {
    if (selectedChartType === 'radar') {
      // 雷达图需要标准化数据
      return data.map(item => ({
        userType: item.userType,
        dailyActive: Math.round((item.dailyActiveUsers / Math.max(...data.map(d => d.dailyActiveUsers))) * 100),
        sessionDuration: Math.round((item.avgSessionDuration / Math.max(...data.map(d => d.avgSessionDuration))) * 100),
        actionsPerSession: Math.round((item.avgActionsPerSession / Math.max(...data.map(d => d.avgActionsPerSession))) * 100),
        retention: Math.round(item.retentionRate),
        engagement: Math.round(item.engagementScore)
      }));
    }
    
    return data.map(item => ({
      ...item,
      value: selectedMetric === 'activeUsers' ? item.dailyActiveUsers :
             selectedMetric === 'sessionDuration' ? item.avgSessionDuration :
             selectedMetric === 'engagement' ? item.engagementScore :
             item.retentionRate
    }));
  };

  // 自定义Tooltip
  const CustomTooltip = ({ active, payload, label }: any) => {
    if (active && payload && payload.length) {
      const data = payload[0].payload;
      return (
        <div className="bg-white p-3 border rounded-lg shadow-lg">
          <p className="font-medium mb-2">{label}</p>
          {selectedChartType === 'radar' ? (
            <>
              <p className="text-sm text-gray-600">日活跃度: {data.dailyActive}%</p>
              <p className="text-sm text-gray-600">会话时长: {data.sessionDuration}%</p>
              <p className="text-sm text-gray-600">会话操作: {data.actionsPerSession}%</p>
              <p className="text-sm text-gray-600">留存率: {data.retention}%</p>
              <p className="text-sm text-gray-600">参与度: {data.engagement}%</p>
            </>
          ) : (
            <>
              <div className="flex items-center text-sm text-gray-600">
                <Users className="h-3 w-3 mr-1" />
                日活跃: {data.dailyActiveUsers}
              </div>
              <div className="flex items-center text-sm text-gray-600">
                <Clock className="h-3 w-3 mr-1" />
                会话时长: {data.avgSessionDuration}分钟
              </div>
              <div className="flex items-center text-sm text-gray-600">
                <Activity className="h-3 w-3 mr-1" />
                参与度: {data.engagementScore}%
              </div>
              <p className="text-sm text-gray-600">留存率: {data.retentionRate}%</p>
            </>
          )}
        </div>
      );
    }
    return null;
  };

  // 处理刷新
  const handleRefresh = () => {
    fetchUserActivityData();
  };

  // 渲染加载状态
  if (loading) {
    return (
      <div className="flex items-center justify-center" style={{ height: `${height}px` }}>
        <Loader2 className="h-8 w-8 animate-spin text-muted-foreground" />
        <span className="ml-2 text-muted-foreground">加载用户活跃度对比数据...</span>
      </div>
    );
  }

  // 渲染错误状态
  if (error) {
    return (
      <div className="flex flex-col items-center justify-center" style={{ height: `${height}px` }}>
        <p className="text-red-500 mb-2">加载失败: {error}</p>
        <Button variant="outline" size="sm" onClick={handleRefresh}>
          <RefreshCw className="h-4 w-4 mr-2" />
          重试
        </Button>
      </div>
    );
  }

  const displayData = getDisplayData();

  return (
    <div className="w-full">
      {showControls && (
        <div className="flex justify-between items-center mb-4">
          <div className="flex space-x-2">
            <Select value={selectedChartType} onValueChange={(value: any) => setSelectedChartType(value)}>
              <SelectTrigger className="w-24">
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="bar">柱图</SelectItem>
                <SelectItem value="radar">雷达图</SelectItem>
              </SelectContent>
            </Select>
            
            {selectedChartType === 'bar' && (
              <Select value={selectedMetric} onValueChange={(value: any) => setSelectedMetric(value)}>
                <SelectTrigger className="w-32">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="activeUsers">活跃用户</SelectItem>
                  <SelectItem value="sessionDuration">会话时长</SelectItem>
                  <SelectItem value="engagement">参与度</SelectItem>
                  <SelectItem value="retention">留存率</SelectItem>
                </SelectContent>
              </Select>
            )}
          </div>
          
          <Button variant="outline" size="sm" onClick={handleRefresh}>
            <RefreshCw className="h-4 w-4 mr-2" />
            刷新
          </Button>
        </div>
      )}
      
      <div style={{ height: `${height}px` }}>
        <ResponsiveContainer width="100%" height="100%">
          {selectedChartType === 'radar' ? (
            <RadarChart data={displayData} margin={{ top: 20, right: 80, bottom: 20, left: 80 }}>
              <PolarGrid />
              <PolarAngleAxis dataKey="userType" tick={{ fontSize: 12 }} />
              <PolarRadiusAxis angle={90} domain={[0, 100]} tick={{ fontSize: 10 }} />
              <Radar
                name="活跃度指标"
                dataKey="dailyActive"
                stroke="#8884d8"
                fill="#8884d8"
                fillOpacity={0.1}
                strokeWidth={2}
              />
              <Radar
                name="会话时长"
                dataKey="sessionDuration"
                stroke="#82ca9d"
                fill="#82ca9d"
                fillOpacity={0.1}
                strokeWidth={2}
              />
              <Radar
                name="参与度"
                dataKey="engagement"
                stroke="#ffc658"
                fill="#ffc658"
                fillOpacity={0.1}
                strokeWidth={2}
              />
              <Tooltip content={<CustomTooltip />} />
              <Legend />
            </RadarChart>
          ) : (
            <BarChart data={displayData} margin={{ top: 20, right: 30, left: 20, bottom: 5 }}>
              <CartesianGrid strokeDasharray="3 3" />
              <XAxis 
                dataKey="userType" 
                tick={{ fontSize: 12 }}
                angle={-45}
                textAnchor="end"
                height={80}
              />
              <YAxis tick={{ fontSize: 12 }} />
              <Tooltip content={<CustomTooltip />} />
              <Bar dataKey="value" fill="#8884d8" />
            </BarChart>
          )}
        </ResponsiveContainer>
      </div>
      
      {data.length === 0 && (
        <div className="text-center text-muted-foreground mt-4">
          暂无用户活跃度对比数据
        </div>
      )}
      
      {/* 活跃度摘要 */}
      {data.length > 0 && (
        <div className="mt-4 grid grid-cols-3 gap-4 text-sm">
          <div className="text-center">
            <p className="text-muted-foreground">总日活跃用户</p>
            <p className="font-semibold text-lg">
              {data.reduce((sum, item) => sum + item.dailyActiveUsers, 0).toLocaleString()}
            </p>
          </div>
          <div className="text-center">
            <p className="text-muted-foreground">平均参与度</p>
            <p className="font-semibold text-lg">
              {(data.reduce((sum, item) => sum + item.engagementScore, 0) / data.length).toFixed(1)}%
            </p>
          </div>
          <div className="text-center">
            <p className="text-muted-foreground">平均留存率</p>
            <p className="font-semibold text-lg">
              {(data.reduce((sum, item) => sum + item.retentionRate, 0) / data.length).toFixed(1)}%
            </p>
          </div>
        </div>
      )}
    </div>
  );
};

export default UserActivityComparisonChart;
