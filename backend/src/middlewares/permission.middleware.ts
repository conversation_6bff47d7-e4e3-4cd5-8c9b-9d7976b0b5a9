/**
 * 权限中间件
 * 提供基于权限的访问控制
 */

import { Context, Next } from 'hono';
import { Env } from '../types';
import { PermissionService, Permission } from '../services/permissionService';
import { UserRole } from '../models/user';

/**
 * 创建权限检查中间件
 * @param requiredPermission 所需权限
 * @param resourceType 资源类型（用于审计日志）
 */
export const permissionMiddleware = (requiredPermission: Permission, resourceType: string) => {
  return async (c: Context<{ Bindings: Env }>, next: Next) => {
    // 开发环境下的简化认证
    if (c.env.ENVIRONMENT === 'development') {
      console.warn(`Development mode: Bypassing permission check for ${requiredPermission}`);
      await next();
      return;
    }

    // 获取用户信息
    const user = c.get('user');
    
    if (!user) {
      return c.json({ success: false, error: 'Unauthorized' }, 401);
    }

    // 获取用户角色
    const userRole = user.role as UserRole || 'guest';
    
    // 获取权限服务实例
    const permissionService = PermissionService.getInstance();
    
    // 检查权限
    if (!permissionService.hasPermission(userRole, requiredPermission)) {
      return c.json({ 
        success: false, 
        error: 'Forbidden: Insufficient permissions',
        requiredPermission
      }, 403);
    }
    
    // 获取资源ID（如果有）
    let resourceId: string | undefined;
    if (c.req.param('id')) {
      resourceId = c.req.param('id');
    }
    
    // 记录权限使用日志
    try {
      await permissionService.logPermissionUse(
        user.id || user.username,
        requiredPermission,
        resourceType,
        resourceId,
        {
          method: c.req.method,
          path: c.req.path,
          ip: c.req.header('CF-Connecting-IP') || c.req.header('X-Forwarded-For')
        },
        c.env
      );
    } catch (error) {
      console.error('Failed to log permission use:', error);
      // 继续处理请求，即使日志记录失败
    }
    
    await next();
  };
};

/**
 * 创建多权限检查中间件（需要满足所有权限）
 * @param requiredPermissions 所需权限列表
 * @param resourceType 资源类型（用于审计日志）
 */
export const allPermissionsMiddleware = (requiredPermissions: Permission[], resourceType: string) => {
  return async (c: Context<{ Bindings: Env }>, next: Next) => {
    // 开发环境下的简化认证
    if (c.env.ENVIRONMENT === 'development') {
      console.warn(`Development mode: Bypassing permission check for ${requiredPermissions.join(', ')}`);
      await next();
      return;
    }

    // 获取用户信息
    const user = c.get('user');
    
    if (!user) {
      return c.json({ success: false, error: 'Unauthorized' }, 401);
    }

    // 获取用户角色
    const userRole = user.role as UserRole || 'guest';
    
    // 获取权限服务实例
    const permissionService = PermissionService.getInstance();
    
    // 检查是否有所有权限
    if (!permissionService.hasAllPermissions(userRole, requiredPermissions)) {
      return c.json({ 
        success: false, 
        error: 'Forbidden: Insufficient permissions',
        requiredPermissions
      }, 403);
    }
    
    // 获取资源ID（如果有）
    let resourceId: string | undefined;
    if (c.req.param('id')) {
      resourceId = c.req.param('id');
    }
    
    // 记录权限使用日志（只记录第一个权限，避免多条日志）
    try {
      await permissionService.logPermissionUse(
        user.id || user.username,
        requiredPermissions[0],
        resourceType,
        resourceId,
        {
          method: c.req.method,
          path: c.req.path,
          permissions: requiredPermissions,
          ip: c.req.header('CF-Connecting-IP') || c.req.header('X-Forwarded-For')
        },
        c.env
      );
    } catch (error) {
      console.error('Failed to log permission use:', error);
      // 继续处理请求，即使日志记录失败
    }
    
    await next();
  };
};

/**
 * 创建多权限检查中间件（满足任意一个权限即可）
 * @param requiredPermissions 所需权限列表
 * @param resourceType 资源类型（用于审计日志）
 */
export const anyPermissionMiddleware = (requiredPermissions: Permission[], resourceType: string) => {
  return async (c: Context<{ Bindings: Env }>, next: Next) => {
    // 开发环境下的简化认证
    if (c.env.ENVIRONMENT === 'development') {
      console.warn(`Development mode: Bypassing permission check for ${requiredPermissions.join(', ')}`);
      await next();
      return;
    }

    // 获取用户信息
    const user = c.get('user');
    
    if (!user) {
      return c.json({ success: false, error: 'Unauthorized' }, 401);
    }

    // 获取用户角色
    const userRole = user.role as UserRole || 'guest';
    
    // 获取权限服务实例
    const permissionService = PermissionService.getInstance();
    
    // 检查是否有任意一个权限
    if (!permissionService.hasAnyPermission(userRole, requiredPermissions)) {
      return c.json({ 
        success: false, 
        error: 'Forbidden: Insufficient permissions',
        requiredPermissions
      }, 403);
    }
    
    // 获取资源ID（如果有）
    let resourceId: string | undefined;
    if (c.req.param('id')) {
      resourceId = c.req.param('id');
    }
    
    // 找出用户拥有的第一个权限
    const grantedPermission = requiredPermissions.find(
      permission => permissionService.hasPermission(userRole, permission)
    );
    
    // 记录权限使用日志
    if (grantedPermission) {
      try {
        await permissionService.logPermissionUse(
          user.id || user.username,
          grantedPermission,
          resourceType,
          resourceId,
          {
            method: c.req.method,
            path: c.req.path,
            permissions: requiredPermissions,
            grantedPermission,
            ip: c.req.header('CF-Connecting-IP') || c.req.header('X-Forwarded-For')
          },
          c.env
        );
      } catch (error) {
        console.error('Failed to log permission use:', error);
        // 继续处理请求，即使日志记录失败
      }
    }
    
    await next();
  };
};
