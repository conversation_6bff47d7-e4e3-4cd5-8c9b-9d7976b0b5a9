import React, { useState, useEffect } from 'react';
import SuperAdminLayout from '@/components/layouts/SuperAdminLayout';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { useToast } from '@/components/ui/use-toast';
import { Button } from '@/components/ui/button';
import {
  Activity, Server, Database, HardDrive, Cpu, MemoryStick,
  Wifi, AlertTriangle, CheckCircle, RefreshCw, Clock
} from 'lucide-react';

interface SystemStatus {
  name: string;
  status: 'healthy' | 'warning' | 'error';
  value: string;
  description: string;
  lastCheck: string;
}

interface ResourceUsage {
  name: string;
  usage: number;
  total: string;
  status: 'normal' | 'warning' | 'critical';
}

/**
 * 系统监控页面
 * 
 * 显示系统健康状态和资源使用情况
 */
const SystemMonitorPage: React.FC = () => {
  const { toast } = useToast();
  const [isLoading, setIsLoading] = useState(true);
  const [lastUpdate, setLastUpdate] = useState(new Date());

  // 系统状态数据
  const [systemStatus, setSystemStatus] = useState<SystemStatus[]>([
    {
      name: '应用服务器',
      status: 'healthy',
      value: '运行中',
      description: 'Node.js 应用服务器运行正常',
      lastCheck: '2 分钟前'
    },
    {
      name: '数据库',
      status: 'healthy',
      value: '连接正常',
      description: 'PostgreSQL 数据库连接稳定',
      lastCheck: '1 分钟前'
    },
    {
      name: 'Redis 缓存',
      status: 'warning',
      value: '内存使用率高',
      description: 'Redis 内存使用率达到 85%',
      lastCheck: '30 秒前'
    },
    {
      name: '文件存储',
      status: 'healthy',
      value: '可用',
      description: '文件存储服务运行正常',
      lastCheck: '5 分钟前'
    },
    {
      name: '邮件服务',
      status: 'healthy',
      value: '连接正常',
      description: 'SMTP 邮件服务可用',
      lastCheck: '10 分钟前'
    },
    {
      name: '外部 API',
      status: 'error',
      value: '连接失败',
      description: '第三方 API 服务暂时不可用',
      lastCheck: '1 分钟前'
    }
  ]);

  // 资源使用情况
  const [resourceUsage, setResourceUsage] = useState<ResourceUsage[]>([
    {
      name: 'CPU 使用率',
      usage: 45,
      total: '8 核心',
      status: 'normal'
    },
    {
      name: '内存使用率',
      usage: 72,
      total: '16 GB',
      status: 'warning'
    },
    {
      name: '磁盘使用率',
      usage: 58,
      total: '500 GB',
      status: 'normal'
    },
    {
      name: '网络带宽',
      usage: 23,
      total: '1 Gbps',
      status: 'normal'
    }
  ]);

  useEffect(() => {
    // 模拟数据加载
    setTimeout(() => {
      setIsLoading(false);
    }, 1000);

    // 设置定时刷新
    const interval = setInterval(() => {
      refreshData();
    }, 30000); // 每30秒刷新一次

    return () => clearInterval(interval);
  }, []);

  const refreshData = () => {
    setLastUpdate(new Date());
    
    // 模拟数据更新
    setResourceUsage(prev => prev.map(resource => ({
      ...resource,
      usage: Math.max(10, Math.min(95, resource.usage + (Math.random() - 0.5) * 10))
    })));

    toast({
      title: '数据已刷新',
      description: '系统监控数据已更新',
    });
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'healthy':
        return <CheckCircle className="h-5 w-5 text-green-500" />;
      case 'warning':
        return <AlertTriangle className="h-5 w-5 text-yellow-500" />;
      case 'error':
        return <AlertTriangle className="h-5 w-5 text-red-500" />;
      default:
        return <Activity className="h-5 w-5 text-gray-500" />;
    }
  };

  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'healthy':
        return <Badge className="bg-green-100 text-green-800">正常</Badge>;
      case 'warning':
        return <Badge className="bg-yellow-100 text-yellow-800">警告</Badge>;
      case 'error':
        return <Badge className="bg-red-100 text-red-800">错误</Badge>;
      default:
        return <Badge className="bg-gray-100 text-gray-800">未知</Badge>;
    }
  };

  const getUsageColor = (usage: number) => {
    if (usage < 60) return 'bg-green-500';
    if (usage < 80) return 'bg-yellow-500';
    return 'bg-red-500';
  };

  const getResourceIcon = (name: string) => {
    if (name.includes('CPU')) return <Cpu className="h-5 w-5" />;
    if (name.includes('内存')) return <MemoryStick className="h-5 w-5" />;
    if (name.includes('磁盘')) return <HardDrive className="h-5 w-5" />;
    if (name.includes('网络')) return <Wifi className="h-5 w-5" />;
    return <Activity className="h-5 w-5" />;
  };

  return (
    <SuperAdminLayout>
      <div className="container mx-auto py-6">
        <div className="flex justify-between items-center mb-6">
          <div className="flex items-center">
            <Activity className="h-6 w-6 mr-2" />
            <h1 className="text-2xl font-bold">系统健康监控</h1>
          </div>
          <div className="flex items-center space-x-4">
            <div className="text-sm text-muted-foreground">
              <Clock className="h-4 w-4 inline mr-1" />
              最后更新: {lastUpdate.toLocaleTimeString()}
            </div>
            <Button
              variant="outline"
              onClick={refreshData}
              disabled={isLoading}
            >
              <RefreshCw className={`mr-2 h-4 w-4 ${isLoading ? 'animate-spin' : ''}`} />
              刷新
            </Button>
          </div>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {/* 系统服务状态 */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <Server className="h-5 w-5 mr-2" />
                系统服务状态
              </CardTitle>
              <CardDescription>
                监控各个系统服务的运行状态
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {systemStatus.map((service, index) => (
                  <div key={index} className="flex items-center justify-between p-3 border rounded-lg">
                    <div className="flex items-center space-x-3">
                      {getStatusIcon(service.status)}
                      <div>
                        <div className="font-medium">{service.name}</div>
                        <div className="text-sm text-muted-foreground">{service.description}</div>
                      </div>
                    </div>
                    <div className="text-right">
                      {getStatusBadge(service.status)}
                      <div className="text-xs text-muted-foreground mt-1">{service.lastCheck}</div>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>

          {/* 资源使用情况 */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <Database className="h-5 w-5 mr-2" />
                资源使用情况
              </CardTitle>
              <CardDescription>
                监控系统资源的使用情况
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-6">
                {resourceUsage.map((resource, index) => (
                  <div key={index} className="space-y-2">
                    <div className="flex items-center justify-between">
                      <div className="flex items-center space-x-2">
                        {getResourceIcon(resource.name)}
                        <span className="font-medium">{resource.name}</span>
                      </div>
                      <div className="text-sm text-muted-foreground">
                        {resource.usage}% / {resource.total}
                      </div>
                    </div>
                    <Progress 
                      value={resource.usage} 
                      className="h-2"
                    />
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </div>

        {/* 系统概览统计 */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mt-6">
          <Card>
            <CardContent className="p-6">
              <div className="flex items-center">
                <CheckCircle className="h-8 w-8 text-green-500" />
                <div className="ml-4">
                  <p className="text-sm font-medium text-muted-foreground">正常服务</p>
                  <p className="text-2xl font-bold">4</p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center">
                <AlertTriangle className="h-8 w-8 text-yellow-500" />
                <div className="ml-4">
                  <p className="text-sm font-medium text-muted-foreground">警告服务</p>
                  <p className="text-2xl font-bold">1</p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center">
                <AlertTriangle className="h-8 w-8 text-red-500" />
                <div className="ml-4">
                  <p className="text-sm font-medium text-muted-foreground">错误服务</p>
                  <p className="text-2xl font-bold">1</p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center">
                <Activity className="h-8 w-8 text-blue-500" />
                <div className="ml-4">
                  <p className="text-sm font-medium text-muted-foreground">系统负载</p>
                  <p className="text-2xl font-bold">中等</p>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </SuperAdminLayout>
  );
};

export default SystemMonitorPage;
