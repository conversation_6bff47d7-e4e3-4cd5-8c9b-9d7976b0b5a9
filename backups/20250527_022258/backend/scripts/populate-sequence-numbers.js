/**
 * 填充序列号脚本
 * 
 * 为现有的问卷回复和故事数据生成序列号
 */

const { PrismaClient } = require('@prisma/client');
const prisma = new PrismaClient();

/**
 * 生成序列号
 */
function generateSequenceNumber(type, id, createdAt) {
  const prefix = type === 'questionnaire' ? 'Q' : 'S';
  const date = new Date(createdAt);
  const year = date.getFullYear().toString().slice(-2);
  const month = (date.getMonth() + 1).toString().padStart(2, '0');
  const day = date.getDate().toString().padStart(2, '0');
  return `${prefix}${year}${month}${day}${id.toString().padStart(4, '0')}`;
}

/**
 * 填充问卷回复序列号
 */
async function populateQuestionnaireSequenceNumbers() {
  console.log('开始填充问卷回复序列号...');
  
  const responses = await prisma.questionnaireResponse.findMany({
    where: {
      sequenceNumber: null
    },
    orderBy: {
      id: 'asc'
    }
  });
  
  console.log(`找到 ${responses.length} 条需要填充序列号的问卷回复`);
  
  for (const response of responses) {
    const sequenceNumber = generateSequenceNumber('questionnaire', response.id, response.createdAt);
    
    try {
      await prisma.questionnaireResponse.update({
        where: { id: response.id },
        data: { sequenceNumber }
      });
      console.log(`✓ 问卷回复 ${response.id} 序列号: ${sequenceNumber}`);
    } catch (error) {
      console.error(`✗ 更新问卷回复 ${response.id} 失败:`, error.message);
    }
  }
  
  console.log('问卷回复序列号填充完成');
}

/**
 * 填充故事作者字段
 */
async function populateStoryAuthors() {
  console.log('开始填充故事作者字段...');
  
  const stories = await prisma.story.findMany({
    where: {
      author: null
    },
    include: {
      user: true
    }
  });
  
  console.log(`找到 ${stories.length} 条需要填充作者的故事`);
  
  for (const story of stories) {
    let author = '匿名用户';
    
    if (!story.isAnonymous && story.user) {
      author = story.user.name || story.user.username || story.user.email.split('@')[0];
    }
    
    try {
      await prisma.story.update({
        where: { id: story.id },
        data: { author }
      });
      console.log(`✓ 故事 ${story.id} 作者: ${author}`);
    } catch (error) {
      console.error(`✗ 更新故事 ${story.id} 失败:`, error.message);
    }
  }
  
  console.log('故事作者字段填充完成');
}

/**
 * 填充标签字段
 */
async function populateTagsFields() {
  console.log('开始填充标签字段...');
  
  // 为问卷回复添加默认标签
  const responsesWithoutTags = await prisma.questionnaireResponse.findMany({
    where: {
      tags: null
    }
  });
  
  console.log(`找到 ${responsesWithoutTags.length} 条需要填充标签的问卷回复`);
  
  for (const response of responsesWithoutTags) {
    const tags = [];
    
    // 根据数据内容生成标签
    if (response.employmentStatus) {
      tags.push(response.employmentStatus);
    }
    if (response.currentIndustry) {
      tags.push(response.currentIndustry);
    }
    if (response.educationLevel) {
      tags.push(response.educationLevel);
    }
    
    try {
      await prisma.questionnaireResponse.update({
        where: { id: response.id },
        data: { tags: JSON.stringify(tags) }
      });
      console.log(`✓ 问卷回复 ${response.id} 标签: ${tags.join(', ')}`);
    } catch (error) {
      console.error(`✗ 更新问卷回复 ${response.id} 标签失败:`, error.message);
    }
  }
  
  // 为故事添加默认标签
  const storiesWithoutTags = await prisma.story.findMany({
    where: {
      tags: null
    }
  });
  
  console.log(`找到 ${storiesWithoutTags.length} 条需要填充标签的故事`);
  
  for (const story of storiesWithoutTags) {
    const tags = ['就业故事'];
    
    // 根据内容生成标签
    if (story.industry) {
      tags.push(story.industry);
    }
    if (story.category) {
      tags.push(story.category);
    }
    
    try {
      await prisma.story.update({
        where: { id: story.id },
        data: { tags: JSON.stringify(tags) }
      });
      console.log(`✓ 故事 ${story.id} 标签: ${tags.join(', ')}`);
    } catch (error) {
      console.error(`✗ 更新故事 ${story.id} 标签失败:`, error.message);
    }
  }
  
  console.log('标签字段填充完成');
}

/**
 * 主函数
 */
async function main() {
  try {
    console.log('开始数据填充...');
    
    await populateQuestionnaireSequenceNumbers();
    await populateStoryAuthors();
    await populateTagsFields();
    
    console.log('✅ 所有数据填充完成！');
    
    // 显示统计信息
    const questionnaireCount = await prisma.questionnaireResponse.count();
    const storyCount = await prisma.story.count();
    
    console.log('\n📊 数据统计:');
    console.log(`- 问卷回复总数: ${questionnaireCount}`);
    console.log(`- 故事总数: ${storyCount}`);
    
  } catch (error) {
    console.error('❌ 数据填充过程中发生错误:', error);
    process.exit(1);
  } finally {
    await prisma.$disconnect();
  }
}

// 运行脚本
if (require.main === module) {
  main();
}

module.exports = {
  generateSequenceNumber,
  populateQuestionnaireSequenceNumbers,
  populateStoryAuthors,
  populateTagsFields
};
