/**
 * 安全模块配置
 */

import { SecurityConfig } from './types';

/**
 * 默认配置 - 关闭
 */
const disabledConfig: SecurityConfig = {
  enabled: false,
  protectionLevel: 0,
  captcha: { 
    enabled: false,
    type: 'none',
    triggerThreshold: 0
  },
  behaviorAnalysis: { 
    enabled: false,
    minCompletionTime: 0,
    trackMouseMovements: false,
    trackKeyboardEvents: false,
    suspiciousScoreThreshold: 100
  },
  rateLimit: { 
    enabled: false,
    ipLimit: 0,
    fingerprintLimit: 0,
    cooldownPeriod: 0
  },
  honeypot: { 
    enabled: false,
    fieldCount: 0,
    silentRejection: true
  },
  fingerprinting: { 
    enabled: false,
    storageTime: 0
  },
  logging: { 
    enabled: true,
    logLevel: 'error',
    storeSubmissionData: false
  }
};

/**
 * 默认配置 - 基础
 */
const basicConfig: SecurityConfig = {
  enabled: true,
  protectionLevel: 1,
  captcha: { 
    enabled: true,
    type: 'turnstile',
    triggerThreshold: 5  // 5次提交后触发
  },
  behaviorAnalysis: { 
    enabled: false,
    minCompletionTime: 0,
    trackMouseMovements: false,
    trackKeyboardEvents: false,
    suspiciousScoreThreshold: 100
  },
  rateLimit: { 
    enabled: true,
    ipLimit: 10,  // 每IP每小时10次
    fingerprintLimit: 0,
    cooldownPeriod: 3600
  },
  honeypot: { 
    enabled: false,
    fieldCount: 0,
    silentRejection: true
  },
  fingerprinting: { 
    enabled: false,
    storageTime: 0
  },
  logging: { 
    enabled: true,
    logLevel: 'warn',
    storeSubmissionData: false
  }
};

/**
 * 默认配置 - 标准
 */
const standardConfig: SecurityConfig = {
  enabled: true,
  protectionLevel: 2,
  captcha: { 
    enabled: true,
    type: 'turnstile',
    triggerThreshold: 3  // 3次提交后触发
  },
  behaviorAnalysis: { 
    enabled: true,
    minCompletionTime: 30000,  // 30秒
    trackMouseMovements: true,
    trackKeyboardEvents: false,
    suspiciousScoreThreshold: 50
  },
  rateLimit: { 
    enabled: true,
    ipLimit: 5,  // 每IP每小时5次
    fingerprintLimit: 0,
    cooldownPeriod: 3600
  },
  honeypot: { 
    enabled: true,
    fieldCount: 1,
    silentRejection: true
  },
  fingerprinting: { 
    enabled: false,
    storageTime: 0
  },
  logging: { 
    enabled: true,
    logLevel: 'warn',
    storeSubmissionData: false
  }
};

/**
 * 默认配置 - 增强
 */
const enhancedConfig: SecurityConfig = {
  enabled: true,
  protectionLevel: 3,
  captcha: { 
    enabled: true,
    type: 'turnstile',
    triggerThreshold: 1  // 每次提交都触发
  },
  behaviorAnalysis: { 
    enabled: true,
    minCompletionTime: 60000,  // 60秒
    trackMouseMovements: true,
    trackKeyboardEvents: true,
    suspiciousScoreThreshold: 30
  },
  rateLimit: { 
    enabled: true,
    ipLimit: 3,  // 每IP每小时3次
    fingerprintLimit: 5,  // 每指纹每小时5次
    cooldownPeriod: 7200  // 2小时
  },
  honeypot: { 
    enabled: true,
    fieldCount: 2,
    silentRejection: true
  },
  fingerprinting: { 
    enabled: true,
    storageTime: 7  // 存储7天
  },
  logging: { 
    enabled: true,
    logLevel: 'info',
    storeSubmissionData: false
  }
};

/**
 * 默认配置 - 最高
 */
const maximumConfig: SecurityConfig = {
  enabled: true,
  protectionLevel: 4,
  captcha: { 
    enabled: true,
    type: 'turnstile',
    triggerThreshold: 1  // 每次提交都触发
  },
  behaviorAnalysis: { 
    enabled: true,
    minCompletionTime: 90000,  // 90秒
    trackMouseMovements: true,
    trackKeyboardEvents: true,
    suspiciousScoreThreshold: 20
  },
  rateLimit: { 
    enabled: true,
    ipLimit: 2,  // 每IP每小时2次
    fingerprintLimit: 3,  // 每指纹每小时3次
    cooldownPeriod: 14400  // 4小时
  },
  honeypot: { 
    enabled: true,
    fieldCount: 3,
    silentRejection: false  // 明确拒绝
  },
  fingerprinting: { 
    enabled: true,
    storageTime: 30  // 存储30天
  },
  logging: { 
    enabled: true, 
    logLevel: 'debug',
    storeSubmissionData: true
  }
};

/**
 * 默认配置数组
 */
export const defaultConfigs: SecurityConfig[] = [
  disabledConfig,    // 0: 关闭
  basicConfig,       // 1: 基础
  standardConfig,    // 2: 标准
  enhancedConfig,    // 3: 增强
  maximumConfig      // 4: 最高
];
