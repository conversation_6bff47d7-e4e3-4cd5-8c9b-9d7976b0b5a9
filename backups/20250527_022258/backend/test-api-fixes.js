#!/usr/bin/env node

/**
 * 测试API修复后的数据
 * 验证本地数据库查询是否正常工作
 */

const sqlite3 = require('sqlite3').verbose();
const path = require('path');

// 数据库路径
const dbPath = path.join(__dirname, 'database.db');

console.log('🧪 测试API修复后的数据查询...\n');

// 创建数据库连接
const db = new sqlite3.Database(dbPath, (err) => {
  if (err) {
    console.error('❌ 数据库连接失败:', err.message);
    process.exit(1);
  }
  console.log('✅ 数据库连接成功');
});

// 测试函数
async function testQueries() {
  console.log('\n📊 测试问卷统计查询...');
  
  // 1. 测试总数查询
  await new Promise((resolve, reject) => {
    db.get('SELECT COUNT(*) as total FROM questionnaire_responses_v2', (err, row) => {
      if (err) {
        console.error('❌ 总数查询失败:', err);
        reject(err);
      } else {
        console.log(`✅ 总数: ${row.total}`);
        resolve();
      }
    });
  });

  // 2. 测试教育水平分布
  await new Promise((resolve, reject) => {
    db.all(`
      SELECT education_level_display as education_level, COUNT(*) as count
      FROM questionnaire_responses_v2
      WHERE education_level_display IS NOT NULL
      GROUP BY education_level_display
    `, (err, rows) => {
      if (err) {
        console.error('❌ 教育水平查询失败:', err);
        reject(err);
      } else {
        console.log('✅ 教育水平分布:');
        rows.forEach(row => {
          console.log(`   ${row.education_level}: ${row.count}人`);
        });
        resolve();
      }
    });
  });

  // 3. 测试地区分布
  await new Promise((resolve, reject) => {
    db.all(`
      SELECT region_display as region, COUNT(*) as count
      FROM questionnaire_responses_v2
      WHERE region_display IS NOT NULL
      GROUP BY region_display
    `, (err, rows) => {
      if (err) {
        console.error('❌ 地区分布查询失败:', err);
        reject(err);
      } else {
        console.log('✅ 地区分布:');
        rows.forEach(row => {
          console.log(`   ${row.region}: ${row.count}人`);
        });
        resolve();
      }
    });
  });

  // 4. 测试就业状态分布
  await new Promise((resolve, reject) => {
    db.all(`
      SELECT employment_status, COUNT(*) as count
      FROM questionnaire_responses_v2
      WHERE employment_status IS NOT NULL
      GROUP BY employment_status
    `, (err, rows) => {
      if (err) {
        console.error('❌ 就业状态查询失败:', err);
        reject(err);
      } else {
        console.log('✅ 就业状态分布:');
        rows.forEach(row => {
          console.log(`   ${row.employment_status}: ${row.count}人`);
        });
        resolve();
      }
    });
  });

  // 5. 测试问卷心声数据
  console.log('\n💬 测试问卷心声查询...');
  await new Promise((resolve, reject) => {
    db.get('SELECT COUNT(*) as total FROM questionnaire_voices_v2 WHERE status = ?', ['approved'], (err, row) => {
      if (err) {
        console.error('❌ 心声总数查询失败:', err);
        reject(err);
      } else {
        console.log(`✅ 心声总数: ${row.total}`);
        resolve();
      }
    });
  });

  // 6. 测试心声详情
  await new Promise((resolve, reject) => {
    db.all(`
      SELECT
        qv.id,
        qv.voice_type,
        qv.title,
        qv.education_level_display,
        qv.region_display,
        qv.likes,
        qv.views
      FROM questionnaire_voices_v2 qv
      WHERE qv.status = ?
      ORDER BY qv.created_at DESC
      LIMIT 5
    `, ['approved'], (err, rows) => {
      if (err) {
        console.error('❌ 心声详情查询失败:', err);
        reject(err);
      } else {
        console.log('✅ 心声详情 (前5条):');
        rows.forEach((row, index) => {
          console.log(`   ${index + 1}. ${row.title} (${row.voice_type})`);
          console.log(`      教育水平: ${row.education_level_display}, 地区: ${row.region_display}`);
          console.log(`      点赞: ${row.likes}, 浏览: ${row.views}`);
        });
        resolve();
      }
    });
  });

  console.log('\n🎉 所有查询测试完成！');
}

// 运行测试
testQueries()
  .then(() => {
    console.log('\n✅ 测试成功完成');
    db.close();
    process.exit(0);
  })
  .catch((error) => {
    console.error('\n❌ 测试失败:', error);
    db.close();
    process.exit(1);
  });
