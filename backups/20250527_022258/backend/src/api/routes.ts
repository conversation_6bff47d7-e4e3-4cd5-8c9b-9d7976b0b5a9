import { Hono } from 'hono';
import { cors } from 'hono/cors';
import { secureHeaders } from 'hono/secure-headers';
import { logger } from 'hono/logger';
import { submitQuestionnaire } from './questionnaire/questionnaire.controller';
import { getQuestionnaireStats, getQuestionnaireRealtimeStats, getQuestionnaireVoices } from './questionnaire/questionnaire.d1.controller';
import { getVisualizationData } from './visualization/visualization.d1.controller';
import { exportData } from './visualization/export.controller';
import { downloadExport, downloadMockExport } from './visualization/download.controller';
import { getDocuments, getDocument, syncDocuments } from './documentation/documentation.controller';
import { submitStory, voteStory, getStoryDetail } from './story/story.controller';
import { getStoryList as getStoryListD1 } from './story/story.d1.controller';
import {
  storyAutoModerationMiddleware,
  questionnaireAutoModerationMiddleware
} from '../services/autoModeration/autoModerationMiddleware';
import {
  initializeAllData,
  initializeUsers,
  initializeQuestionnaires,
  initializeStories,
  initializeStatistics
} from './admin/data-init.controller';
import dataManagementRoutes from './admin/data-management.routes';
import tagManagementRoutes from './admin/tag-management.routes';
import tagRecommendationRoutes from './story/tag-recommendation.routes';
import r2DataInitRoutes from './admin/r2-data-init.routes';
import r2ApiRoutes from './r2/r2-api.routes';
import securityRoutes from './admin/security';
import errorReportRoutes from './admin/error-report';
import errorAnalysisRoutes from './admin/error-analysis';
import errorVisualizationRoutes from './admin/error-visualization';
import reviewRoutes from './admin/review.routes';
import auditRoutes from './admin/audit.routes';
import { SecurityModule } from '../security';
import { admin } from '../controllers/admin';
import { tags } from '../controllers/tags';
import { statistics } from '../controllers/statistics';
import { analytics } from '../controllers/analytics';
import { responses } from '../controllers/responses';
import databaseTestRoutes from './test/database';

// Define environment interface
interface Env {
  SURVEY_KV: KVNamespace;
  R2_BUCKET: R2Bucket;
  DATABASE_URL: string;
  RESEND_API_KEY: string;
  AES_SECRET_KEY: string;
  AES_IV: string;
  JWT_SECRET: string;
  ENVIRONMENT: string;
}

// Create Hono app
const app = new Hono<{ Bindings: Env }>();

// Middleware
app.use('*', logger());
app.use('*', secureHeaders());

// API routes with /api prefix
const api = app.basePath('/api');

// Questionnaire routes
api.post('/questionnaire/submit', questionnaireAutoModerationMiddleware, ...submitQuestionnaire);
api.get('/questionnaire/stats', getQuestionnaireStats);
api.get('/questionnaire/realtime-stats', getQuestionnaireRealtimeStats);
api.get('/questionnaire-voices', getQuestionnaireVoices);

// Database test routes (added here to ensure they work)
api.get('/questionnaire/test-db-count', async (c) => {
  try {
    const result = await c.env.DB.prepare('SELECT COUNT(*) as count FROM questionnaire_responses_v2').first();
    return c.json({
      success: true,
      count: result?.count || 0,
      table: 'questionnaire_responses_v2',
      query: 'SELECT COUNT(*) as count FROM questionnaire_responses_v2'
    });
  } catch (error) {
    return c.json({
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error',
      count: 0
    }, 500);
  }
});

// Visualization routes
api.get('/visualization/data', getVisualizationData);
api.post('/visualization/export', ...exportData);
api.get('/exports/:id', downloadExport);
api.get('/mock-exports/:fileName', downloadMockExport);

// Story routes
api.post('/story/submit', storyAutoModerationMiddleware, ...submitStory);
api.post('/story/vote', ...voteStory);
api.get('/story/list', getStoryListD1);
api.get('/story/detail/:id', getStoryDetail);

// Public tags route
api.route('/tags', tags);

// Data initialization routes
api.get('/admin/data/init', initializeAllData);
api.get('/admin/data/init/users', initializeUsers);
api.get('/admin/data/init/questionnaires', initializeQuestionnaires);
api.get('/admin/data/init/stories', initializeStories);
api.get('/admin/data/init/statistics', initializeStatistics);

// Data management routes
api.route('/admin/data-management', dataManagementRoutes);

// Tag recommendation routes
api.route('/story/tag-recommendations', tagRecommendationRoutes);

// R2 data initialization routes
api.route('/admin/r2', r2DataInitRoutes);

// R2 API routes
api.route('/r2', r2ApiRoutes);

// Admin routes
api.route('/admin', admin);
api.route('/admin/tags', tags);
api.route('/admin/statistics', statistics);
api.route('/admin/responses', responses);
api.route('/analytics', analytics);

// Security routes
api.route('/admin/security', securityRoutes);

// Error report routes
api.route('/admin/errors', errorReportRoutes);

// Error analysis routes
api.route('/admin/error-analysis', errorAnalysisRoutes);

// Error visualization routes
api.route('/admin/error-visualization', errorVisualizationRoutes);

// Content review routes
api.route('/admin/review', reviewRoutes);

// Audit log routes
api.route('/admin/audit', auditRoutes);

// Content moderation routes
import contentModerationRoutes from './admin/content-moderation.routes';
api.route('/admin/content-moderation', contentModerationRoutes);

// Auto moderation routes
import autoModerationRoutes from './admin/auto-moderation.routes';
api.route('/admin/auto-moderation', autoModerationRoutes);

// Moderation feedback routes
import moderationFeedbackRoutes from './feedback/moderation-feedback.routes';
api.route('/feedback/moderation', moderationFeedbackRoutes);

// Notification routes
import notificationRoutes from './notification/notification.routes';
api.route('/notifications', notificationRoutes);

// Test data management routes
import testDataRoutes from './admin/test-data.routes';
api.route('/admin/test-data', testDataRoutes);

// User management routes
import userManagementRoutes from './admin/user-management.routes';
api.route('/admin/users', userManagementRoutes);

// Role management routes
import roleManagementRoutes from './admin/role-management.routes';
api.route('/admin/roles', roleManagementRoutes);

// Documentation routes
api.get('/documentation', getDocuments);
api.get('/documentation/documents', getDocuments);
api.get('/documentation/documents/:id', getDocument);
api.post('/documentation/sync', syncDocuments);

// Database test routes
// api.route('/test/db', databaseTestRoutes);

// 简单测试端点
api.get('/test/simple', async (c) => {
  return c.json({ success: true, message: 'Test endpoint working' });
});

// 直接添加数据库测试端点
api.get('/test/db/count-responses', async (c) => {
  try {
    const result = await c.env.DB.prepare('SELECT COUNT(*) as count FROM questionnaire_responses_v2').first();
    return c.json({
      success: true,
      count: result?.count || 0,
      table: 'questionnaire_responses_v2',
      query: 'SELECT COUNT(*) as count FROM questionnaire_responses_v2'
    });
  } catch (error) {
    return c.json({
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error',
      count: 0
    }, 500);
  }
});

api.get('/test/db/count-voices', async (c) => {
  try {
    const result = await c.env.DB.prepare('SELECT COUNT(*) as count FROM questionnaire_voices_v2').first();
    return c.json({
      success: true,
      count: result?.count || 0,
      table: 'questionnaire_voices_v2',
      query: 'SELECT COUNT(*) as count FROM questionnaire_voices_v2'
    });
  } catch (error) {
    return c.json({
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error',
      count: 0
    }, 500);
  }
});

api.get('/test/db/count-stories', async (c) => {
  try {
    const result = await c.env.DB.prepare('SELECT COUNT(*) as count FROM story_contents_v2').first();
    return c.json({
      success: true,
      count: result?.count || 0,
      table: 'story_contents_v2',
      query: 'SELECT COUNT(*) as count FROM story_contents_v2'
    });
  } catch (error) {
    return c.json({
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error',
      count: 0
    }, 500);
  }
});

api.get('/test/db/count-users', async (c) => {
  try {
    const result = await c.env.DB.prepare('SELECT COUNT(*) as count FROM users_v2').first();
    return c.json({
      success: true,
      count: result?.count || 0,
      table: 'users_v2',
      query: 'SELECT COUNT(*) as count FROM users_v2'
    });
  } catch (error) {
    return c.json({
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error',
      count: 0
    }, 500);
  }
});

api.get('/test/db/table-info', async (c) => {
  try {
    const result = await c.env.DB.prepare('SELECT name, type FROM sqlite_master WHERE type="table"').all();
    return c.json({
      success: true,
      data: result.results || [],
      count: result.results?.length || 0,
      query: 'SELECT name, type FROM sqlite_master WHERE type="table"'
    });
  } catch (error) {
    return c.json({
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error',
      data: []
    }, 500);
  }
});

api.get('/test/db/recent-responses', async (c) => {
  try {
    const result = await c.env.DB.prepare('SELECT id, created_at FROM questionnaire_responses_v2 ORDER BY created_at DESC LIMIT 5').all();
    return c.json({
      success: true,
      data: result.results || [],
      count: result.results?.length || 0,
      query: 'SELECT id, created_at FROM questionnaire_responses_v2 ORDER BY created_at DESC LIMIT 5'
    });
  } catch (error) {
    return c.json({
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error',
      data: []
    }, 500);
  }
});

// 不在全局范围内初始化安全模块，而是在第一个请求中初始化
// 添加中间件来初始化安全模块
app.use('*', async (c, next) => {
  // 检查安全模块是否已初始化
  if (!SecurityModule.isInitialized()) {
    SecurityModule.initialize({
      protectionLevel: 2, // 使用标准防护等级
      captcha: {
        secretKey: c.env.TURNSTILE_SECRET_KEY || 'development-key'
      }
    });
  }
  await next();
});

// For backward compatibility, also register routes without /api prefix
app.post('/questionnaire/submit', questionnaireAutoModerationMiddleware, ...submitQuestionnaire);
app.get('/questionnaire/stats', getQuestionnaireStats);
app.get('/questionnaire/realtime-stats', getQuestionnaireRealtimeStats);
app.get('/questionnaire-voices', getQuestionnaireVoices);
app.get('/visualization/data', getVisualizationData);
app.post('/story/submit', storyAutoModerationMiddleware, ...submitStory);
app.post('/story/vote', ...voteStory);
app.get('/story/list', getStoryListD1);
app.get('/story/detail/:id', getStoryDetail);

export default app;
