# 基于A+B匿名身份验证的故事发布系统设计总结

## 🎯 设计理念

基于项目中已有的轻量级匿名身份验证功能，重新设计了故事发布系统，主要特点：

1. **A+B匿名身份认证为主**: 用户使用A值(11位数字)+B值(4位或6位密码)进行身份验证
2. **邮箱认证为辅**: 可选的邮箱注册登录方式
3. **故事与用户UUID强绑定**: 确保内容归属明确
4. **多维度搜索支持**: 支持按标题、内容、标签、分类、学历、行业等搜索

## 📊 数据表设计

### 1. 用户表 (users)
```sql
CREATE TABLE users (
  id TEXT PRIMARY KEY,                    -- user_xxx
  uuid TEXT UNIQUE NOT NULL,             -- 用户UUID (SHA-256哈希生成)
  
  -- 认证方式 (支持两种)
  auth_type TEXT NOT NULL CHECK (auth_type IN ('anonymous', 'email')),
  
  -- A+B匿名身份验证 (主要方式)
  identity_a_hash TEXT,                  -- A值哈希 (11位数字，如手机号)
  identity_b_hash TEXT,                  -- B值哈希 (4位或6位密码)
  anonymous_uuid TEXT,                   -- A+B组合生成的UUID
  
  -- 邮箱认证 (可选方式)
  email TEXT UNIQUE,                     -- 邮箱 (可选)
  password_hash TEXT,                    -- 密码哈希 (可选)
  
  -- 用户档案
  display_name TEXT,                     -- 显示名称
  education_level TEXT,                  -- 学历背景
  industry_code TEXT,                    -- 行业领域
  graduation_year INTEGER,              -- 毕业年份
  location TEXT,                         -- 所在地区
  
  -- 统计信息
  stories_count INTEGER DEFAULT 0,      -- 发布故事数
  questionnaire_count INTEGER DEFAULT 0, -- 问卷提交数
  
  -- 约束条件
  CONSTRAINT check_auth_fields CHECK (
    (auth_type = 'anonymous' AND identity_a_hash IS NOT NULL AND identity_b_hash IS NOT NULL) OR
    (auth_type = 'email' AND email IS NOT NULL AND password_hash IS NOT NULL)
  )
);
```

### 2. 故事表 (stories)
```sql
CREATE TABLE stories (
  id TEXT PRIMARY KEY,                   -- story_xxx
  user_uuid TEXT NOT NULL,              -- 关联用户UUID
  
  -- 基本内容
  title TEXT NOT NULL,                  -- 标题 (5-50字符)
  content TEXT NOT NULL,                -- 内容 (20-2000字符)
  
  -- 分类维度
  category TEXT NOT NULL,               -- 主分类
  subcategory TEXT,                     -- 子分类
  
  -- 用户背景维度 (用于搜索和筛选)
  education_level TEXT,                 -- 学历背景
  industry_code TEXT,                   -- 行业领域
  graduation_year INTEGER,             -- 毕业年份
  location TEXT,                        -- 地区
  
  -- 发布设置
  is_anonymous BOOLEAN DEFAULT FALSE,   -- 是否匿名发布
  
  -- 审核状态
  status TEXT DEFAULT 'pending' CHECK (status IN ('draft', 'pending', 'approved', 'rejected')),
  
  -- 互动数据
  views INTEGER DEFAULT 0,
  likes INTEGER DEFAULT 0,
  dislikes INTEGER DEFAULT 0,
  
  FOREIGN KEY (user_uuid) REFERENCES users(uuid)
);
```

### 3. 标签系统
- **tags表**: 分层标签设计，支持父子关系
- **story_tags表**: 故事标签关联，支持相关度评分

## 🔐 认证流程

### A+B匿名身份注册
1. 用户输入A值(11位数字)和B值(4位或6位密码)
2. 服务器端加盐生成SHA-256哈希UUID
3. 创建用户记录，存储哈希值(不存储原始A+B)
4. 返回JWT令牌和用户信息

### A+B匿名身份登录
1. 用户输入A+B组合
2. 服务器生成相同的哈希UUID
3. 查找匹配的用户记录
4. 返回JWT令牌和用户信息

### 内容管理
1. 用户可使用A+B组合查询自己的内容
2. 支持查看故事、问卷回复、待审核内容
3. 提供内容删除和编辑功能

## 🔍 搜索能力

支持以下维度的组合搜索：
- **内容维度**: 标题、内容全文搜索
- **分类维度**: 主分类、子分类
- **用户背景**: 学历、行业、毕业年份
- **地理维度**: 地区、城市
- **标签维度**: 多标签组合搜索
- **时间维度**: 发布时间、更新时间

## 🚀 API设计

### 主要端点
```
POST /api/auth/register-anonymous  # A+B匿名注册
POST /api/auth/login-anonymous     # A+B匿名登录
POST /api/auth/my-content          # 查询用户内容
POST /api/stories                  # 创建故事 (需认证)
GET  /api/stories                  # 搜索故事 (支持多维度筛选)
```

### 认证中间件
```javascript
const requireAuth = async (c, next) => {
  const token = c.req.header('Authorization')?.replace('Bearer ', '');
  const user = await authService.verifyToken(token);
  c.set('user', user);
  await next();
};
```

## 🛡️ 安全性考虑

1. **哈希安全**: 使用SHA-256+服务器端加盐
2. **不存储原始值**: 只存储哈希值，无法反推原始A+B
3. **JWT令牌**: 安全的会话管理
4. **频率限制**: 防止暴力破解
5. **输入验证**: 严格的A+B格式验证

## 📈 优势特点

1. **用户友好**: 无需邮箱，使用简单的A+B组合
2. **隐私保护**: 真正的匿名身份，无法追溯
3. **内容管理**: 用户可管理自己的内容
4. **强绑定**: 故事与用户UUID强关联
5. **多维搜索**: 丰富的搜索和筛选功能
6. **扩展性**: 支持邮箱认证作为补充

## 🔄 与现有系统集成

1. **兼容现有功能**: 与问卷系统的A+B认证保持一致
2. **数据迁移**: 可从现有story_contents_v2表迁移数据
3. **API兼容**: 保持现有API的基本结构
4. **前端适配**: 最小化前端改动

## 📋 实施步骤

1. **数据库迁移**: 创建新表结构，迁移现有数据
2. **认证服务**: 实现AuthService类
3. **API端点**: 添加认证相关API
4. **故事API**: 修改故事创建和查询API
5. **前端改造**: 添加A+B认证界面
6. **测试验证**: 全面测试认证和故事发布流程

## ✅ 验收标准

1. **A+B认证**: 用户可使用A+B组合注册和登录
2. **故事发布**: 只有认证用户可发布故事
3. **内容管理**: 用户可查询和管理自己的内容
4. **多维搜索**: 支持按各种维度搜索故事
5. **安全性**: 通过安全测试，无法破解A+B组合
6. **性能**: 搜索响应时间 < 500ms

这个设计完全基于项目现有的A+B匿名身份验证功能，确保了用户体验的一致性，同时满足了故事发布系统的所有需求。
