// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "sqlite"
  url      = env("DATABASE_URL")
}

// User model
model User {
  id                   Int                   @id @default(autoincrement())
  email                String                @unique
  emailVerified        <PERSON>olean               @default(false)
  verificationCode     String?
  verificationExpiresAt DateTime?
  ipAddress            String?
  userAgent            String?
  role                 String                @default("user") // 'user', 'reviewer', 'admin', 'superadmin'
  username             String?               // 用户名，管理员和审核员需要
  name                 String?               // 显示名称，用于前端显示
  passwordHash         String?               // 密码哈希，管理员和审核员需要
  lastLoginAt          DateTime?             // 最后登录时间
  createdAt            DateTime              @default(now())
  updatedAt            DateTime              @updatedAt

  // 测试数据标识
  isTestData           Boolean               @default(false)
  testDataVersion      String?               // 测试数据版本
  testDataSet          String?               // 测试数据集名称

  questionnaireResponses QuestionnaireResponse[]
  stories              Story[]
}

// Questionnaire response model
model QuestionnaireResponse {
  id                   Int                   @id @default(autoincrement())
  userId               Int?
  sequenceNumber       String                @unique // 前端需要的序列号
  isAnonymous          Boolean               @default(true)
  ipAddress            String?
  submittedById        String?               // 轻量级匿名身份标识（UUID）

  // 1. Personal information
  educationLevel       String?
  major                String?
  graduationYear       Int?
  region               String?

  // 2. Employment expectations
  expectedPosition     String?
  expectedSalaryRange  String?
  expectedWorkHours    Int?
  expectedVacationDays Int?

  // 3. Work experience
  employmentStatus     String?
  currentIndustry      String?               // 映射到前端 industry
  currentPosition      String?               // 映射到前端 position
  monthlySalary        Int?                  // 映射到前端 salary (需要转换)
  jobSatisfaction      String?               // 改为String类型以匹配前端

  // 4. Unemployment status
  unemploymentDuration String?               // 改为String类型以匹配前端
  unemploymentReason   String?
  jobHuntingDifficulty Int?

  // 5. Career change and reflection
  regretMajor          Boolean?
  preferredMajor       String?
  careerChangeIntention Boolean?
  careerChangeTarget   String?

  // 6. Advice and feedback
  adviceForStudents    String?               // 映射到前端 suggestions
  observationOnEmployment String?            // 映射到前端 challenges

  // 前端需要的额外字段
  tags                 String?               // JSON字符串存储标签数组
  status               String                @default("normal") // 'verified', 'pending', 'normal'

  createdAt            DateTime              @default(now())
  updatedAt            DateTime              @updatedAt

  // 测试数据标识
  isTestData           Boolean               @default(false)
  testDataVersion      String?               // 测试数据版本
  testDataSet          String?               // 测试数据集名称

  // Relations
  user                 User?                 @relation(fields: [userId], references: [id])
}

// Story model
model Story {
  id                   Int                   @id @default(autoincrement())
  userId               Int?
  isAnonymous          Boolean               @default(true)
  title                String
  content              String
  author               String?               // 前端需要的作者字段，可以是用户名或"匿名用户"
  ipAddress            String?
  likes                Int                   @default(0)
  dislikes             Int                   @default(0)
  tags                 String?               // JSON字符串存储标签数组
  category             String?               // 故事分类
  educationLevel       String?               // 教育水平
  industry             String?               // 行业
  status               String                @default("pending") // pending, approved, rejected
  submittedById        String?               // 轻量级匿名身份标识（UUID）
  createdAt            DateTime              @default(now())
  updatedAt            DateTime              @updatedAt

  // 测试数据标识
  isTestData           Boolean               @default(false)
  testDataVersion      String?               // 测试数据版本
  testDataSet          String?               // 测试数据集名称

  // Relations
  user                 User?                 @relation(fields: [userId], references: [id])
  votes                Vote[]
}

// Vote model
model Vote {
  id                   Int                   @id @default(autoincrement())
  storyId              Int
  ipAddress            String
  voteType             String                // like, dislike
  createdAt            DateTime              @default(now())

  // 测试数据标识
  isTestData           Boolean               @default(false)
  testDataVersion      String?               // 测试数据版本
  testDataSet          String?               // 测试数据集名称

  // Relations
  story                Story                 @relation(fields: [storyId], references: [id])

  // Unique constraint to prevent multiple votes from the same IP
  @@unique([storyId, ipAddress])
}

// Sensitive word model
model SensitiveWord {
  id                   Int                   @id @default(autoincrement())
  word                 String                @unique
  level                Int                   @default(1) // 1: low, 2: medium, 3: high
  createdAt            DateTime              @default(now())
  updatedAt            DateTime              @updatedAt
}

// Tag model
model Tag {
  id                   Int                   @id @default(autoincrement())
  name                 String                @unique
  description          String?
  color                String                @default("#3B82F6") // 标签颜色
  category             String                @default("general") // 标签分类：general, emotion, topic, quality, system
  usageCount           Int                   @default(0) // 使用次数
  isSystem             Boolean               @default(false) // 是否为系统标签
  createdAt            DateTime              @default(now())
  updatedAt            DateTime              @updatedAt

  // 测试数据标识
  isTestData           Boolean               @default(false)
  testDataVersion      String?               // 测试数据版本
  testDataSet          String?               // 测试数据集名称

  // 索引
  @@index([category])
  @@index([isSystem])
  @@index([usageCount])
}



// 审核操作日志表
model ReviewLog {
  id                   String                @id @default(uuid())
  reviewerId           String                // 审核员ID
  contentId            String                // 关联的内容ID
  action               String                // 'approve', 'edit', 'reject'
  diff                 String?               // 对比内容差异 (JSON as string)
  reviewNotes          String?               // 审核意见
  ipAddress            String?               // 审核员IP
  userAgent            String?               // 审核员浏览器信息
  timestamp            DateTime              @default(now())

  // 关联待审核内容
  pendingContent       PendingContent        @relation(fields: [contentId], references: [id])
}

// AI内容审核历史表
model ModerationHistory {
  id                   String                @id @default(uuid())
  contentType          String                // 内容类型：'story', 'questionnaire', 'comment', 'profile', 'feedback'
  contentId            String                // 内容ID
  reviewerId           String                // 审核员ID（可能是系统或真实审核员）
  isSafe               Boolean               // 内容是否安全
  issues               String                // 发现的问题（逗号分隔）
  confidence           Float                 // 判断的置信度
  explanation          String                // 解释
  suggestedAction      String                // 建议的操作：'approve', 'reject', 'review'
  severity             String?               // 问题严重程度：'low', 'medium', 'high'
  dataQuality          String?               // 数据质量：'low', 'medium', 'high'
  constructiveValue    String?               // 建设性价值：'none', 'low', 'medium', 'high'
  storyValue           String?               // 故事价值：'none', 'low', 'medium', 'high'
  ipAddress            String?               // IP地址
  userAgent            String?               // 用户代理
  createdAt            DateTime              @default(now())

  // 索引
  @@index([contentType, contentId])
  @@index([reviewerId])
  @@index([suggestedAction])
  @@index([severity])
  @@index([createdAt])
}

// 待审核内容表
model PendingContent {
  id                   String                @id @default(uuid())
  sequenceNumber       String                // 序列号
  type                 String                // 内容类型：'story', 'questionnaire', 'comment', 'profile', 'feedback'
  originalContent      String                // 原始内容（JSON格式）
  sanitizedContent     String                // 脱敏内容（JSON格式）
  status               String                @default("pending") // 状态：'pending', 'approved', 'rejected', 'flagged'
  originIp             String?               // 来源IP
  userAgent            String?               // 用户代理
  flags                String?               // 标记（逗号分隔）
  priority             Int                   @default(1) // 优先级：1-5
  aiSuggestion         String?               // AI建议的操作
  aiConfidence         Float?                // AI判断的置信度
  aiExplanation        String?               // AI解释
  reviewerId           String?               // 审核员ID
  reviewedAt           DateTime?             // 审核时间
  reviewNotes          String?               // 审核备注
  createdAt            DateTime              @default(now())
  updatedAt            DateTime              @default(now()) @updatedAt

  // 关联审核日志
  reviewLogs           ReviewLog[]

  // 索引
  @@index([sequenceNumber])
  @@index([type])
  @@index([status])
  @@index([priority])
  @@index([reviewerId])
  @@index([createdAt])
}

// 被拒绝内容表
model RejectedContent {
  id                   String                @id @default(uuid())
  userId               String?               // 用户ID
  contentType          String                // 内容类型
  originalContent      String                // 原始内容（JSON格式）
  reason               String?               // 拒绝原因
  issues               String?               // 发现的问题（逗号分隔）
  ipAddress            String?               // IP地址
  userAgent            String?               // 用户代理
  createdAt            DateTime              @default(now())

  // 索引
  @@index([userId])
  @@index([contentType])
  @@index([createdAt])
}

// 系统配置表
model SystemConfig {
  id                   String                @id @default(uuid())
  key                  String                @unique // 配置键
  value                String                // 配置值（JSON格式）
  description          String?               // 描述
  createdAt            DateTime              @default(now())
  updatedAt            DateTime              @default(now()) @updatedAt
}

// 审核反馈表
model ModerationFeedback {
  id                   String                @id @default(uuid())
  contentId            String                // 内容ID
  contentType          String                // 内容类型
  moderationId         String?               // 审核记录ID
  feedbackType         String                // 反馈类型：'disagree', 'appeal', 'report', 'suggestion'
  reason               String                // 反馈原因
  details              String                // 详细说明
  contactEmail         String?               // 联系邮箱
  ipAddress            String?               // IP地址
  userAgent            String?               // 用户代理
  status               String                @default("pending") // 状态：'pending', 'reviewed', 'resolved', 'rejected'
  response             String?               // 管理员回复
  reviewerId           String?               // 处理反馈的管理员ID
  reviewedAt           DateTime?             // 处理时间
  createdAt            DateTime              @default(now())
  updatedAt            DateTime              @default(now()) @updatedAt

  // 索引
  @@index([contentId, contentType])
  @@index([feedbackType])
  @@index([status])
  @@index([createdAt])
}

// 申诉请求表
model AppealRequest {
  id                   String                @id @default(uuid())
  feedbackId           String                // 反馈ID
  contentId            String                // 内容ID
  contentType          String                // 内容类型
  reason               String                // 申诉原因
  status               String                @default("pending") // 状态：'pending', 'approved', 'rejected'
  result               String?               // 申诉结果
  reviewerId           String?               // 处理申诉的管理员ID
  reviewedAt           DateTime?             // 处理时间
  ipAddress            String?               // IP地址
  userAgent            String?               // 用户代理
  createdAt            DateTime              @default(now())
  updatedAt            DateTime              @default(now()) @updatedAt

  // 索引
  @@index([feedbackId])
  @@index([contentId, contentType])
  @@index([status])
  @@index([createdAt])
}

// 通知表
model Notification {
  id                   String                @id @default(uuid())
  userId               String                // 用户ID
  type                 String                // 通知类型
  title                String                // 通知标题
  message              String                // 通知内容
  data                 String                // 通知数据（JSON格式）
  isRead               Boolean               @default(false) // 是否已读
  readAt               DateTime?             // 阅读时间
  createdAt            DateTime              @default(now())
  updatedAt            DateTime              @default(now()) @updatedAt

  // 索引
  @@index([userId])
  @@index([type])
  @@index([isRead])
  @@index([createdAt])
}

// 系统设置表
model SystemSetting {
  id                   String                @id @default(uuid())
  key                  String                @unique
  value                String
  description          String?
  updatedBy            String?               // 更新者ID
  createdAt            DateTime              @default(now())
  updatedAt            DateTime              @updatedAt
}
