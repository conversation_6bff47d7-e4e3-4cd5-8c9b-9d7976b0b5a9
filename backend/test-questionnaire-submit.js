// 测试问卷提交 API

async function testSubmitQuestionnaire() {
  try {
    const response = await fetch('http://localhost:8787/api/questionnaire/submit', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        isAnonymous: true,
        educationLevel: '本科',
        major: '计算机科学',
        graduationYear: 2020,
        region: '北京',
        expectedPosition: '软件工程师',
        expectedSalaryRange: '10k-15k',
        expectedWorkHours: 40,
        expectedVacationDays: 15,
        employmentStatus: '已就业',
        currentIndustry: '互联网',
        currentPosition: '前端开发',
        jobSatisfaction: 4,
        unemploymentDuration: '3个月以内',
        unemploymentReason: '正在找工作',
        jobHuntingDifficulty: 3,
        regretMajor: false,
        preferredMajor: '',
        careerChangeIntention: false,
        careerChangeTarget: '',
        adviceForStudents: '多学习实践技能',
        observationOnEmployment: '就业形势良好',
      }),
    });

    const data = await response.json();
    console.log('Response:', data);
  } catch (error) {
    console.error('Error:', error);
  }
}

testSubmitQuestionnaire();
