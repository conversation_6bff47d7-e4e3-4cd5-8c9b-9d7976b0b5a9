# 🚀 CORS解决方案快速开始指南

> **5分钟解决CORS问题，15分钟完成完整配置**

## 🆘 **紧急情况 - 立即修复**

### **步骤1: 快速诊断 (1分钟)**
```bash
# 替换为你的实际URL
./cors-solution-guide/scripts/cors-diagnose.sh \
  https://your-api.workers.dev \
  https://your-frontend.pages.dev
```

### **步骤2: 自动修复 (1分钟)**
```bash
./cors-solution-guide/scripts/auto-fix-cors.sh
```

### **步骤3: 重新部署 (2分钟)**
```bash
cd backend && npx wrangler deploy
```

### **步骤4: 验证修复 (1分钟)**
```bash
./cors-solution-guide/scripts/cors-diagnose.sh \
  https://your-api.workers.dev \
  https://your-frontend.pages.dev
```

---

## 🎯 **新项目开发 - 完整配置**

### **步骤1: 复制配置模板 (2分钟)**
```bash
# 复制CORS配置模板
cp cors-solution-guide/templates/hono-cors-config.ts backend/src/cors-config.ts

# 复制npm脚本
cat cors-solution-guide/templates/package-scripts.json >> package.json
```

### **步骤2: 修改配置 (5分钟)**
```typescript
// 编辑 backend/src/cors-config.ts
const corsEnvironments = {
  development: {
    origins: [
      'http://localhost:3000',  // 修改为你的前端端口
      'http://localhost:5173'
    ]
  },
  production: {
    origins: [
      'https://app.yourdomain.com'  // 修改为你的域名
    ]
  }
};

// 修改项目名称
const projectName = 'your-project-name';  // 修改为你的项目名
```

### **步骤3: 集成到应用 (3分钟)**
```typescript
// backend/src/index.ts
import { createCORSConfig } from './cors-config';

const app = new Hono();
app.use('*', createCORSConfig());
```

### **步骤4: 环境变量配置 (2分钟)**
```bash
# .env.development
NODE_ENV=development
ALLOWED_ORIGINS=http://localhost:3000

# .env.production  
NODE_ENV=production
ALLOWED_ORIGINS=https://app.yourdomain.com
```

### **步骤5: 测试配置 (3分钟)**
```bash
# 启动开发环境
npm run dev

# 测试CORS配置
npm run test:cors:local
```

---

## 🔍 **问题排查流程**

### **症状: "Failed to fetch" 错误**
```bash
# 1. 运行诊断
./cors-solution-guide/scripts/cors-diagnose.sh API_URL FRONTEND_URL

# 2. 查看具体错误类型
# 如果显示 "access-control-allow-origin: true"
./cors-solution-guide/scripts/auto-fix-cors.sh

# 3. 如果显示 "Origin不匹配"
# 编辑 backend/index.js，添加域名到允许列表
```

### **症状: 某个域名被拒绝**
```bash
# 1. 检查域名格式
echo "检查域名: https://abc123.project.pages.dev"

# 2. 添加到允许列表
# 编辑 backend/index.js，在allowedDomains数组中添加域名

# 3. 重新部署
cd backend && npx wrangler deploy
```

### **症状: 开发环境正常，生产环境失败**
```bash
# 1. 检查环境变量
echo $NODE_ENV
echo $ALLOWED_ORIGINS

# 2. 检查生产域名配置
./cors-solution-guide/scripts/cors-diagnose.sh \
  https://production-api.workers.dev \
  https://production-frontend.pages.dev
```

---

## 📚 **学习资源**

### **必读文档 (按优先级)**
1. [`problem-analysis.md`](./docs/problem-analysis.md) - 理解问题本质
2. [`best-practices.md`](./docs/best-practices.md) - 掌握最佳实践  
3. [`development-planning.md`](./docs/development-planning.md) - 项目规划
4. [`case-studies.md`](./docs/case-studies.md) - 实际案例学习

### **工具使用**
```bash
# 诊断工具
./cors-solution-guide/scripts/cors-diagnose.sh

# 修复工具  
./cors-solution-guide/scripts/auto-fix-cors.sh

# 部署前检查
./cors-solution-guide/scripts/pre-deploy-check.sh

# 浏览器测试 (复制到浏览器控制台)
# cors-solution-guide/scripts/browser-cors-test.js
```

---

## 🎯 **AI助手使用指南**

### **开发前规划阶段**
```markdown
AI助手应该:
1. 阅读 development-planning.md
2. 使用 templates/hono-cors-config.ts 模板
3. 运行 pre-deploy-check.sh 验证配置
```

### **问题诊断阶段**
```markdown
AI助手应该:
1. 运行 cors-diagnose.sh 获取详细诊断
2. 参考 problem-analysis.md 理解错误类型
3. 使用 browser-cors-test.js 进行浏览器环境测试
```

### **修复实施阶段**
```markdown
AI助手应该:
1. 优先使用 auto-fix-cors.sh 自动修复
2. 参考 best-practices.md 进行手动修复
3. 使用 case-studies.md 中的解决方案
```

---

## 📋 **检查清单**

### **部署前检查**
- [ ] 运行 `pre-deploy-check.sh` 通过
- [ ] CORS配置返回具体域名而不是布尔值
- [ ] 所有目标域名已添加到允许列表
- [ ] 环境变量正确配置

### **部署后验证**
- [ ] 运行 `cors-diagnose.sh` 显示正常
- [ ] 浏览器环境测试通过
- [ ] 所有API端点可访问
- [ ] 数据监测页面显示绿色状态

### **维护检查**
- [ ] 定期清理过期的临时域名
- [ ] 监控CORS相关错误日志
- [ ] 更新文档和工具

---

## 🔗 **相关链接**

- **完整文档**: [README.md](./README.md)
- **问题分析**: [problem-analysis.md](./docs/problem-analysis.md)
- **最佳实践**: [best-practices.md](./docs/best-practices.md)
- **案例研究**: [case-studies.md](./docs/case-studies.md)

---

## 📞 **获取帮助**

### **常见问题**
1. **工具无法执行**: 运行 `chmod +x cors-solution-guide/scripts/*.sh`
2. **诊断脚本报错**: 检查curl是否安装，URL是否正确
3. **自动修复无效**: 手动检查backend/index.js中的CORS配置

### **联系支持**
- 查看 [case-studies.md](./docs/case-studies.md) 寻找类似问题
- 运行诊断工具获取详细错误信息
- 提供完整的错误日志和配置信息

---

**记住**: 大多数CORS问题都可以在5分钟内解决，关键是使用正确的诊断工具！
