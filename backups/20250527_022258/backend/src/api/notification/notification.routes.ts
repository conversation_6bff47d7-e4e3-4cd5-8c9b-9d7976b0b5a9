/**
 * 通知路由
 * 
 * 处理通知相关的路由
 */

import { Hono } from 'hono';
import { Env } from '../../types';
import * as notificationController from './notification.controller';
import { authMiddleware } from '../../middlewares/auth.middleware';

// 创建路由
const app = new Hono<{ Bindings: Env }>();

// 所有路由都需要认证
app.use('*', authMiddleware);

// 获取用户通知列表
app.get('/', notificationController.getUserNotifications);

// 获取未读通知数量
app.get('/unread-count', notificationController.getUnreadCount);

// 标记通知为已读
app.post('/:id/read', notificationController.markAsRead);

// 标记所有通知为已读
app.post('/read-all', notificationController.markAllAsRead);

// 删除通知
app.delete('/:id', notificationController.deleteNotification);

export default app;
