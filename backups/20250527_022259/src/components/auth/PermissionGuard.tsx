import React from 'react';
import { Navigate, useLocation } from 'react-router-dom';
import { useToast } from '@/components/ui/use-toast';
import LoadingSpinner from '@/components/ui/loading-spinner';
import { useAuth } from '@/contexts/AuthContext';
import { Role } from '@/lib/permissions';

interface PermissionGuardProps {
  children: React.ReactNode;
  allowedRoles: Role[];
}

/**
 * 权限守卫组件
 *
 * 用于控制页面访问权限，只允许特定角色的用户访问
 */
const PermissionGuard: React.FC<PermissionGuardProps> = ({ children, allowedRoles }) => {
  const { toast } = useToast();
  const location = useLocation();
  const auth = useAuth();
  const { user, loading: isLoading, isAuthenticated } = auth;

  // 如果正在加载，显示加载中
  if (isLoading) {
    return (
      <div className="flex justify-center items-center min-h-screen">
        <LoadingSpinner size="lg" text="验证权限中..." />
      </div>
    );
  }

  // 如果未登录，重定向到登录页面
  if (!isAuthenticated || !user) {
    // 清除可能存在的无效登录信息
    localStorage.removeItem('adminToken');
    localStorage.removeItem('adminUser');
    sessionStorage.removeItem('redirectToRoleDashboard');
    return <Navigate to="/admin/login" state={{ from: location }} replace />;
  }

  // 检查登录时间是否超过24小时
  const loginTime = user.loginTime ? new Date(user.loginTime).getTime() : 0;
  const currentTime = new Date().getTime();
  const timeDiff = currentTime - loginTime;

  if (loginTime && timeDiff > 24 * 60 * 60 * 1000) {
    localStorage.removeItem('adminToken');
    localStorage.removeItem('adminUser');
    sessionStorage.removeItem('redirectToRoleDashboard');

    toast({
      title: '登录已过期',
      description: '请重新登录',
      variant: 'destructive',
    });

    return <Navigate to="/admin/login" state={{ from: location }} replace />;
  }

  // 检查用户角色是否在允许的角色列表中
  // 确保用户角色是有效的Role类型
  const userRole = user.role as Role;
  const hasPermission = allowedRoles.includes(userRole);

  // 检查是否需要重定向到角色对应的主页
  // 如果用户是超级管理员但当前在管理员页面，重定向到超级管理员仪表盘
  if (userRole === 'superadmin' && location.pathname.startsWith('/admin/') && location.pathname !== '/admin/login') {
    // 设置重定向标志，确保RoleRedirector能够处理
    sessionStorage.setItem('redirectToRoleDashboard', 'true');
    return <Navigate to="/superadmin/dashboard" replace />;
  }

  // 如果用户是管理员但当前在审核员页面，重定向到管理员仪表盘
  if (userRole === 'admin' && location.pathname.startsWith('/reviewer/') && location.pathname !== '/admin/login') {
    // 设置重定向标志，确保RoleRedirector能够处理
    sessionStorage.setItem('redirectToRoleDashboard', 'true');
    return <Navigate to="/admin/dashboard" replace />;
  }

  // 如果用户是审核员但当前在超级管理员页面，重定向到审核员仪表盘
  if (userRole === 'reviewer' && location.pathname.startsWith('/superadmin/') && location.pathname !== '/admin/login') {
    // 设置重定向标志，确保RoleRedirector能够处理
    sessionStorage.setItem('redirectToRoleDashboard', 'true');
    return <Navigate to="/reviewer/dashboard" replace />;
  }

  // 如果没有权限，显示提示并重定向
  if (!hasPermission) {
    toast({
      title: '访问受限',
      description: '您没有权限访问此页面',
      variant: 'destructive',
    });

    // 根据用户角色重定向到相应的仪表盘
    if (user.role === 'admin') {
      return <Navigate to="/admin/dashboard" replace />;
    } else if (user.role === 'reviewer') {
      return <Navigate to="/reviewer/dashboard" replace />;
    } else if (user.role === 'superadmin') {
      return <Navigate to="/superadmin/dashboard" replace />;
    } else {
      return <Navigate to="/admin/login" state={{ from: location }} replace />;
    }
  }

  // 如果已授权，渲染子组件
  return <>{children}</>;
};

export default PermissionGuard;
