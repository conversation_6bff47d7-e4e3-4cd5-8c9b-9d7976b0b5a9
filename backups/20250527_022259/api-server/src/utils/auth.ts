/**
 * 认证工具函数
 */

/**
 * 生成JWT令牌
 * @param user 用户对象
 * @param secret 密钥
 * @returns JWT令牌
 */
export function generateToken(user: any, secret: string): string {
  // 创建头部
  const header = {
    alg: 'HS256',
    typ: 'JWT'
  };
  
  // 创建载荷
  const payload = {
    sub: user.id,
    name: user.name,
    role: user.role,
    iat: Math.floor(Date.now() / 1000),
    exp: Math.floor(Date.now() / 1000) + 24 * 60 * 60 // 24小时后过期
  };
  
  // 编码头部和载荷
  const encodedHeader = btoa(JSON.stringify(header));
  const encodedPayload = btoa(JSON.stringify(payload));
  
  // 创建签名
  const signature = createSignature(`${encodedHeader}.${encodedPayload}`, secret);
  
  // 返回JWT令牌
  return `${encodedHeader}.${encodedPayload}.${signature}`;
}

/**
 * 验证JWT令牌
 * @param token JWT令牌
 * @param secret 密钥
 * @returns 验证结果
 */
export function verifyToken(token: string, secret: string): { valid: boolean; payload?: any } {
  try {
    // 分割令牌
    const [encodedHeader, encodedPayload, signature] = token.split('.');
    
    // 验证签名
    const expectedSignature = createSignature(`${encodedHeader}.${encodedPayload}`, secret);
    
    if (signature !== expectedSignature) {
      return { valid: false };
    }
    
    // 解码载荷
    const payload = JSON.parse(atob(encodedPayload));
    
    // 验证过期时间
    if (payload.exp < Math.floor(Date.now() / 1000)) {
      return { valid: false };
    }
    
    // 返回验证结果
    return { valid: true, payload };
  } catch (error) {
    console.error('令牌验证失败:', error);
    return { valid: false };
  }
}

/**
 * 创建签名
 * @param data 数据
 * @param secret 密钥
 * @returns 签名
 */
function createSignature(data: string, secret: string): string {
  // 在实际应用中，应该使用更安全的方法，如HMAC-SHA256
  // 这里使用简化的方法，仅用于演示
  const encoder = new TextEncoder();
  const dataBuffer = encoder.encode(data);
  const secretBuffer = encoder.encode(secret);
  
  // 简单的签名算法
  let signature = '';
  for (let i = 0; i < dataBuffer.length; i++) {
    signature += String.fromCharCode(dataBuffer[i] ^ secretBuffer[i % secretBuffer.length]);
  }
  
  return btoa(signature);
}
