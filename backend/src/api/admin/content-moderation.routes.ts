/**
 * 内容审核路由
 *
 * 处理内容审核相关的API请求
 */

import { Hono } from 'hono';
import { Env } from '../../types';
import { adminAuthMiddleware } from '../../middlewares/auth.middleware';
import { reviewerAuthMiddleware } from '../../middlewares/reviewerAuth.middleware';
import { superAdminAuthMiddleware } from '../../middlewares/superAdminAuth.middleware';
import * as contentModerationController from './content-moderation.controller';

// 创建路由
const contentModerationRouter = new Hono<{ Bindings: Env }>();

// 通用认证中间件 - 允许审核员、管理员和超级管理员访问
const anyRoleAuthMiddleware = async (c: any, next: any) => {
  // 尝试超级管理员认证
  try {
    await superAdminAuthMiddleware(c, () => {});
    await next();
    return;
  } catch (error) {
    // 超级管理员认证失败，继续尝试管理员认证
  }

  // 尝试管理员认证
  try {
    await adminAuthMiddleware(c, () => {});
    await next();
    return;
  } catch (error) {
    // 管理员认证失败，继续尝试审核员认证
  }

  // 尝试审核员认证
  try {
    await reviewerAuthMiddleware(c, next);
    return;
  } catch (error) {
    // 所有认证都失败
    return c.json({ success: false, error: 'Unauthorized' }, 401);
  }
};

// ==================== 待审核内容管理 ====================

// 获取待审核内容列表（允许审核员、管理员和超级管理员访问）
contentModerationRouter.get('/pending', anyRoleAuthMiddleware, contentModerationController.getPendingContents);

// 获取单个待审核内容详情（允许审核员、管理员和超级管理员访问）
contentModerationRouter.get('/pending/:id', anyRoleAuthMiddleware, contentModerationController.getPendingContentById);

// 审核通过内容（允许审核员、管理员和超级管理员访问）
contentModerationRouter.post('/:id/approve', anyRoleAuthMiddleware, contentModerationController.approveContent);

// 编辑并通过内容（允许审核员、管理员和超级管理员访问）
contentModerationRouter.put('/:id/edit', anyRoleAuthMiddleware, contentModerationController.editAndApproveContent);

// 拒绝内容（允许审核员、管理员和超级管理员访问）
contentModerationRouter.post('/:id/reject', anyRoleAuthMiddleware, contentModerationController.rejectContent);

// 批量审核内容（允许审核员、管理员和超级管理员访问）
contentModerationRouter.post('/batch', anyRoleAuthMiddleware, contentModerationController.batchModerateContent);

// ==================== 兼容旧API ====================

// 审核内容（允许审核员、管理员和超级管理员访问）- 保持向后兼容
contentModerationRouter.post('/moderate', anyRoleAuthMiddleware, contentModerationController.moderateContent);

// 批量审核内容（允许审核员、管理员和超级管理员访问）- 保持向后兼容
contentModerationRouter.post('/moderate-batch', anyRoleAuthMiddleware, contentModerationController.moderateContentBatch);

// ==================== 统计和分析 ====================

// 管理员或超级管理员认证中间件
const adminOrSuperAdminAuthMiddleware = async (c: any, next: any) => {
  // 尝试超级管理员认证
  try {
    await superAdminAuthMiddleware(c, () => {});
    await next();
    return;
  } catch (error) {
    // 超级管理员认证失败，尝试管理员认证
  }

  // 尝试管理员认证
  try {
    await adminAuthMiddleware(c, next);
    return;
  } catch (error) {
    // 所有认证都失败
    return c.json({ success: false, error: 'Unauthorized' }, 401);
  }
};

// 获取AI服务状态（允许管理员和超级管理员访问）
contentModerationRouter.get('/status', adminOrSuperAdminAuthMiddleware, contentModerationController.getAIServiceStatus);

// 获取内容审核历史（允许管理员和超级管理员访问）
contentModerationRouter.get('/history', adminOrSuperAdminAuthMiddleware, contentModerationController.getModerationHistory);

// 获取内容审核统计（允许管理员和超级管理员访问）
contentModerationRouter.get('/stats', adminOrSuperAdminAuthMiddleware, contentModerationController.getModerationStats);

// 获取审核员绩效统计（允许管理员和超级管理员访问）
contentModerationRouter.get('/reviewer-performance', adminOrSuperAdminAuthMiddleware, contentModerationController.getReviewerPerformance);

// 获取内容质量趋势分析（允许管理员和超级管理员访问）
contentModerationRouter.get('/quality-trends', adminOrSuperAdminAuthMiddleware, contentModerationController.getContentQualityTrends);

export default contentModerationRouter;
