/**
 * 数据库迁移脚本 - 从v1迁移到v2
 * 
 * 功能：
 * 1. 创建新的v2表结构
 * 2. 迁移现有数据到新表
 * 3. 生成统一的UUID标识
 * 4. 数据标准化处理
 */

import Database from 'better-sqlite3';
import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// ID生成器
class IDGenerator {
  static generateUUID() {
    return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, (c) => {
      const r = Math.random() * 16 | 0;
      const v = c === 'x' ? r : (r & 0x3 | 0x8);
      return v.toString(16);
    });
  }
  
  static generateUserID(type = 'anon') {
    return `${type}_${this.generateUUID()}`;
  }
  
  static generateContentID(type) {
    return `${type}_${this.generateUUID()}`;
  }
  
  static generateSystemID(type) {
    return `${type}_${this.generateUUID()}`;
  }
}

// 数据标准化工具
class DataNormalizer {
  static educationMapping = {
    '高中': 'high_school',
    '中专': 'vocational',
    '大专': 'associate', 
    '本科': 'undergraduate',
    '硕士': 'master',
    '博士': 'doctorate'
  };
  
  static regionMapping = {
    '北上广深': 'tier1_cities',
    '省会城市': 'provincial_capitals',
    '二线城市': 'tier2_cities',
    '三四线城市': 'tier3_cities',
    '县城或乡镇': 'county_town',
    '海外': 'overseas'
  };
  
  static industryMapping = {
    '互联网/IT': 'technology',
    '金融': 'finance',
    '教育': 'education',
    '医疗': 'healthcare',
    '制造业': 'manufacturing',
    '服务业': 'service',
    '政府/公共部门': 'government'
  };
  
  static normalizeEducation(input) {
    return this.educationMapping[input] || 'other';
  }
  
  static normalizeRegion(input) {
    return this.regionMapping[input] || 'other';
  }
  
  static normalizeIndustry(input) {
    return this.industryMapping[input] || 'other';
  }
}

// 迁移管理器
class MigrationManager {
  constructor(dbPath) {
    this.db = new Database(dbPath);
    this.db.pragma('journal_mode = WAL');
    this.migrationLog = [];
  }
  
  log(message) {
    const timestamp = new Date().toISOString();
    const logEntry = `[${timestamp}] ${message}`;
    console.log(logEntry);
    this.migrationLog.push(logEntry);
  }
  
  // 执行完整迁移
  async migrate() {
    try {
      this.log('开始数据库迁移到v2...');
      
      // 1. 创建新表结构
      await this.createV2Schema();
      
      // 2. 迁移用户数据
      await this.migrateUsers();
      
      // 3. 迁移问卷数据
      await this.migrateQuestionnaires();
      
      // 4. 迁移故事数据
      await this.migrateStories();
      
      // 5. 生成问卷心声
      await this.generateVoices();
      
      // 6. 验证迁移结果
      await this.validateMigration();
      
      // 7. 保存迁移日志
      await this.saveMigrationLog();
      
      this.log('数据库迁移完成！');
      
    } catch (error) {
      this.log(`迁移失败: ${error.message}`);
      throw error;
    }
  }
  
  // 创建v2表结构
  async createV2Schema() {
    this.log('创建v2表结构...');
    
    const schemaPath = path.join(__dirname, 'schema-v2.sql');
    const schemaSql = fs.readFileSync(schemaPath, 'utf8');
    
    // 分割SQL语句并执行
    const statements = schemaSql.split(';').filter(stmt => stmt.trim());
    
    for (const statement of statements) {
      if (statement.trim()) {
        try {
          this.db.exec(statement);
        } catch (error) {
          if (!error.message.includes('already exists')) {
            throw error;
          }
        }
      }
    }
    
    this.log('v2表结构创建完成');
  }
  
  // 迁移用户数据
  async migrateUsers() {
    this.log('迁移用户数据...');
    
    // 检查是否有现有用户表
    const hasUsersTable = this.db.prepare(`
      SELECT name FROM sqlite_master 
      WHERE type='table' AND name='users'
    `).get();
    
    if (hasUsersTable) {
      // 迁移现有用户
      const users = this.db.prepare('SELECT * FROM users').all();
      const insertUser = this.db.prepare(`
        INSERT INTO users_v2 (id, user_type, email, display_name, created_at)
        VALUES (?, ?, ?, ?, ?)
      `);
      
      for (const user of users) {
        const newId = IDGenerator.generateUserID(user.email ? 'user' : 'anon');
        insertUser.run(
          newId,
          user.email ? 'registered' : 'anonymous',
          user.email,
          user.display_name || user.username,
          user.created_at
        );
      }
      
      this.log(`迁移了 ${users.length} 个用户`);
    } else {
      this.log('未找到现有用户表，跳过用户迁移');
    }
  }
  
  // 迁移问卷数据
  async migrateQuestionnaires() {
    this.log('迁移问卷数据...');
    
    // 检查现有问卷表
    const hasQuestionnaireTable = this.db.prepare(`
      SELECT name FROM sqlite_master 
      WHERE type='table' AND name='questionnaire_responses'
    `).get();
    
    if (hasQuestionnaireTable) {
      const questionnaires = this.db.prepare('SELECT * FROM questionnaire_responses').all();
      const insertQuestionnaire = this.db.prepare(`
        INSERT INTO questionnaire_responses_v2 (
          id, user_id, session_id, education_level, education_level_display,
          major_category, major_display, graduation_year, region_code, region_display,
          employment_status, advice_content, observation_content, original_data,
          ip_address, created_at
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
      `);
      
      for (const q of questionnaires) {
        const newId = IDGenerator.generateContentID('quest');
        const userId = IDGenerator.generateUserID('anon');
        const sessionId = IDGenerator.generateSystemID('sess');
        
        // 数据标准化
        const educationLevel = DataNormalizer.normalizeEducation(q.education_level);
        const regionCode = DataNormalizer.normalizeRegion(q.region);
        
        insertQuestionnaire.run(
          newId,
          userId,
          sessionId,
          educationLevel,
          q.education_level,
          q.major,
          q.major,
          q.graduation_year,
          regionCode,
          q.region,
          q.employment_status || 'unknown',
          q.advice_for_students,
          q.observation_on_employment,
          JSON.stringify(q),
          q.ip_address || '127.0.0.1',
          q.created_at
        );
      }
      
      this.log(`迁移了 ${questionnaires.length} 个问卷回复`);
    } else {
      this.log('未找到现有问卷表，跳过问卷迁移');
    }
  }
  
  // 迁移故事数据
  async migrateStories() {
    this.log('迁移故事数据...');
    
    const hasStoriesTable = this.db.prepare(`
      SELECT name FROM sqlite_master 
      WHERE type='table' AND name='stories'
    `).get();
    
    if (hasStoriesTable) {
      const stories = this.db.prepare('SELECT * FROM stories').all();
      const insertStory = this.db.prepare(`
        INSERT INTO story_contents_v2 (
          id, user_id, title, content, category, education_level, education_level_display,
          is_anonymous, status, likes, dislikes, views, created_at
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
      `);
      
      for (const story of stories) {
        const newId = IDGenerator.generateContentID('story');
        const userId = IDGenerator.generateUserID('anon');
        
        // 标准化教育水平
        const educationLevel = DataNormalizer.normalizeEducation(story.education_level);
        
        insertStory.run(
          newId,
          userId,
          story.title,
          story.content,
          story.category,
          educationLevel,
          story.education_level,
          story.is_anonymous !== false,
          story.status || 'approved',
          story.likes || 0,
          story.dislikes || 0,
          story.views || 0,
          story.created_at
        );
      }
      
      this.log(`迁移了 ${stories.length} 个故事`);
    } else {
      this.log('未找到现有故事表，跳过故事迁移');
    }
  }
  
  // 生成问卷心声
  async generateVoices() {
    this.log('生成问卷心声...');
    
    const questionnaires = this.db.prepare(`
      SELECT * FROM questionnaire_responses_v2 
      WHERE advice_content IS NOT NULL OR observation_content IS NOT NULL
    `).all();
    
    const insertVoice = this.db.prepare(`
      INSERT INTO questionnaire_voices_v2 (
        id, source_response_id, user_id, voice_type, title, content,
        education_level, education_level_display, region_code, region_display,
        status, created_at
      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
    `);
    
    let voiceCount = 0;
    
    for (const q of questionnaires) {
      // 生成建议类心声
      if (q.advice_content && q.advice_content.trim()) {
        const voiceId = IDGenerator.generateContentID('voice');
        insertVoice.run(
          voiceId,
          q.id,
          q.user_id,
          'advice',
          '给高三学子的建议',
          q.advice_content,
          q.education_level,
          q.education_level_display,
          q.region_code,
          q.region_display,
          'approved',
          q.created_at
        );
        voiceCount++;
      }
      
      // 生成观察类心声
      if (q.observation_content && q.observation_content.trim()) {
        const voiceId = IDGenerator.generateContentID('voice');
        insertVoice.run(
          voiceId,
          q.id,
          q.user_id,
          'observation',
          '对当前就业环境的观察',
          q.observation_content,
          q.education_level,
          q.education_level_display,
          q.region_code,
          q.region_display,
          'approved',
          q.created_at
        );
        voiceCount++;
      }
    }
    
    this.log(`生成了 ${voiceCount} 个问卷心声`);
  }
  
  // 验证迁移结果
  async validateMigration() {
    this.log('验证迁移结果...');
    
    const tables = ['users_v2', 'questionnaire_responses_v2', 'story_contents_v2', 'questionnaire_voices_v2'];
    const results = {};
    
    for (const table of tables) {
      const count = this.db.prepare(`SELECT COUNT(*) as count FROM ${table}`).get();
      results[table] = count.count;
      this.log(`${table}: ${count.count} 条记录`);
    }
    
    // 检查数据完整性
    const voicesWithSource = this.db.prepare(`
      SELECT COUNT(*) as count FROM questionnaire_voices_v2 v
      JOIN questionnaire_responses_v2 q ON v.source_response_id = q.id
    `).get();
    
    this.log(`问卷心声关联检查: ${voicesWithSource.count}/${results.questionnaire_voices_v2} 条记录有效`);
    
    return results;
  }
  
  // 保存迁移日志
  async saveMigrationLog() {
    const logPath = path.join(__dirname, `migration-log-${Date.now()}.txt`);
    fs.writeFileSync(logPath, this.migrationLog.join('\n'));
    this.log(`迁移日志已保存到: ${logPath}`);
  }
  
  // 清理旧表（可选）
  async cleanupOldTables() {
    this.log('清理旧表...');
    
    const oldTables = ['users', 'questionnaire_responses', 'stories'];
    
    for (const table of oldTables) {
      try {
        this.db.exec(`DROP TABLE IF EXISTS ${table}_backup`);
        this.db.exec(`ALTER TABLE ${table} RENAME TO ${table}_backup`);
        this.log(`已备份旧表: ${table} -> ${table}_backup`);
      } catch (error) {
        this.log(`备份表 ${table} 失败: ${error.message}`);
      }
    }
  }
  
  close() {
    this.db.close();
  }
}

// 主执行函数
async function main() {
  const dbPath = process.argv[2] || './database.db';
  
  console.log(`开始迁移数据库: ${dbPath}`);
  
  const migrator = new MigrationManager(dbPath);
  
  try {
    await migrator.migrate();
    console.log('迁移成功完成！');
  } catch (error) {
    console.error('迁移失败:', error);
    process.exit(1);
  } finally {
    migrator.close();
  }
}

// 如果直接运行此脚本
if (import.meta.url === `file://${process.argv[1]}`) {
  main();
}

export { MigrationManager, IDGenerator, DataNormalizer };
