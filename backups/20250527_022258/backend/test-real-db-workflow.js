const http = require('http');

const API_BASE = 'http://localhost:8788';

// 发送HTTP请求的辅助函数
function makeRequest(method, path, data = null, headers = {}) {
  return new Promise((resolve, reject) => {
    const options = {
      hostname: 'localhost',
      port: 8788,
      path: path,
      method: method,
      headers: {
        'Content-Type': 'application/json',
        'Authorization': 'Bearer reviewer1',
        ...headers
      }
    };

    const req = http.request(options, (res) => {
      let body = '';
      res.on('data', (chunk) => {
        body += chunk;
      });
      res.on('end', () => {
        try {
          const jsonData = JSON.parse(body);
          resolve({ status: res.statusCode, data: jsonData });
        } catch (e) {
          resolve({ status: res.statusCode, data: body });
        }
      });
    });

    req.on('error', (err) => {
      reject(err);
    });

    if (data) {
      req.write(JSON.stringify(data));
    }
    req.end();
  });
}

async function testRealDatabaseWorkflow() {
  console.log('🧪 开始测试真实数据库审核工作流程...\n');

  try {
    // 1. 检查健康状态
    console.log('1️⃣ 检查API健康状态...');
    const health = await makeRequest('GET', '/health');
    console.log('✅ 健康检查:', health.data);
    console.log('');

    // 2. 获取审核员统计数据
    console.log('2️⃣ 获取审核员统计数据...');
    const stats = await makeRequest('GET', '/api/reviewer/dashboard/stats');
    console.log('📊 审核员统计:', stats.data);
    console.log('');

    // 3. 获取待审核故事
    console.log('3️⃣ 获取待审核故事...');
    const stories = await makeRequest('GET', '/api/reviewer/batch/stories');
    console.log('📚 待审核故事数量:', stories.data.stories?.length || 0);
    if (stories.data.stories && stories.data.stories.length > 0) {
      console.log('📖 第一个故事:', {
        id: stories.data.stories[0].id,
        title: stories.data.stories[0].title,
        author: stories.data.stories[0].author
      });
    }
    console.log('');

    // 4. 获取待审核问卷心声
    console.log('4️⃣ 获取待审核问卷心声...');
    const voices = await makeRequest('GET', '/api/reviewer/batch/voices');
    console.log('💬 待审核心声数量:', voices.data.voices?.length || 0);
    if (voices.data.voices && voices.data.voices.length > 0) {
      console.log('💭 第一个心声:', {
        id: voices.data.voices[0].id,
        content: voices.data.voices[0].content.substring(0, 50) + '...',
        author: voices.data.voices[0].author
      });
    }
    console.log('');

    // 5. 模拟批量审核提交
    if (stories.data.stories && stories.data.stories.length > 0) {
      console.log('5️⃣ 模拟批量审核提交...');
      const reviewResults = [
        {
          id: stories.data.stories[0].id,
          action: 'approve',
          reason: '内容积极正面，符合要求'
        }
      ];

      const submitResult = await makeRequest('POST', '/api/reviewer/batch/submit', {
        batchId: 'test-batch-' + Date.now(),
        results: reviewResults,
        type: 'stories'
      });
      
      console.log('✅ 提交结果:', submitResult.data);
      console.log('');

      // 6. 再次获取统计数据，验证更新
      console.log('6️⃣ 验证统计数据更新...');
      const updatedStats = await makeRequest('GET', '/api/reviewer/dashboard/stats');
      console.log('📊 更新后的统计:', updatedStats.data);
      
      // 比较前后数据
      if (stats.data.approvedStories !== undefined && updatedStats.data.approvedStories !== undefined) {
        const increase = updatedStats.data.approvedStories - stats.data.approvedStories;
        console.log(`📈 已批准故事增加了: ${increase} 条`);
      }
    }

    console.log('\n🎉 真实数据库审核工作流程测试完成！');
    console.log('\n📋 测试总结:');
    console.log('- ✅ API健康检查正常');
    console.log('- ✅ 审核员统计数据获取正常');
    console.log('- ✅ 待审核内容获取正常');
    console.log('- ✅ 批量审核提交正常');
    console.log('- ✅ 统计数据实时更新正常');
    console.log('\n🔑 关键特性:');
    console.log('- 📊 真实数据库查询');
    console.log('- 👤 审核员UUID识别');
    console.log('- 📝 审核记录持久化');
    console.log('- 🔄 统计数据实时更新');

  } catch (error) {
    console.error('❌ 测试失败:', error);
  }
}

// 运行测试
testRealDatabaseWorkflow();
