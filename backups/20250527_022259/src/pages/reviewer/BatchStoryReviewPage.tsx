import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import ReviewerLayout from '@/components/layouts/ReviewerLayout';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Textarea } from '@/components/ui/textarea';
import { Badge } from '@/components/ui/badge';
import {
  CheckCircle, XCircle, Clock, ArrowLeft, ArrowRight,
  Save, Send, BookOpen, User, Calendar
} from 'lucide-react';
import { useToast } from '@/components/ui/use-toast';

interface Story {
  id: number;
  sequenceNumber: string;
  title: string;
  content: string;
  author: string;
  isAnonymous: boolean;
  createdAt: string;
  tags: string[];
  category: string;
}

interface ReviewResult {
  id: number;
  action: 'approve' | 'reject';
  reason: string;
}

/**
 * 批量故事审核页面
 */
const BatchStoryReviewPage: React.FC = () => {
  const navigate = useNavigate();
  const { toast } = useToast();

  const [stories, setStories] = useState<Story[]>([]);
  const [currentIndex, setCurrentIndex] = useState(0);
  const [reviewResults, setReviewResults] = useState<ReviewResult[]>([]);
  const [currentReason, setCurrentReason] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [batchId, setBatchId] = useState('');

  // 加载批量审核数据
  const loadBatchData = async () => {
    setIsLoading(true);
    try {
      const response = await fetch('http://localhost:8788/api/reviewer/batch/stories', {
        headers: {
          'Authorization': `Bearer reviewer1`,
          'Content-Type': 'application/json'
        }
      });

      if (response.ok) {
        const data = await response.json();
        setStories(data.stories || []);
        setBatchId(data.batchId || '');
        setReviewResults([]);
        setCurrentIndex(0);
        setCurrentReason('');
      } else {
        throw new Error('加载失败');
      }
    } catch (error) {
      console.error('加载批量审核数据失败:', error);
      toast({
        title: '加载失败',
        description: '无法加载待审核的故事，请稍后再试',
        variant: 'destructive'
      });
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    loadBatchData();
  }, []);

  const currentStory = stories[currentIndex];
  const currentResult = reviewResults.find(r => r.id === currentStory?.id);

  // 处理审核决定
  const handleReview = (action: 'approve' | 'reject') => {
    if (!currentStory) return;

    const newResult: ReviewResult = {
      id: currentStory.id,
      action,
      reason: currentReason
    };

    setReviewResults(prev => {
      const filtered = prev.filter(r => r.id !== currentStory.id);
      return [...filtered, newResult];
    });

    // 清空原因输入
    setCurrentReason('');

    // 自动跳转到下一个
    if (currentIndex < stories.length - 1) {
      setCurrentIndex(prev => prev + 1);
    }
  };

  // 提交所有审核结果
  const submitAllResults = async () => {
    if (reviewResults.length === 0) {
      toast({
        title: '提交失败',
        description: '请至少审核一条内容',
        variant: 'destructive'
      });
      return;
    }

    setIsLoading(true);
    try {
      const response = await fetch('http://localhost:8788/api/reviewer/batch/submit', {
        method: 'POST',
        headers: {
          'Authorization': `Bearer reviewer1`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          batchId,
          results: reviewResults,
          type: 'stories'
        })
      });

      if (response.ok) {
        const data = await response.json();
        toast({
          title: '提交成功',
          description: data.message || '审核结果已提交'
        });

        // 询问是否继续审核
        if (confirm('是否继续审核下一批内容？')) {
          loadBatchData();
        } else {
          navigate('/reviewer/dashboard');
        }
      } else {
        throw new Error('提交失败');
      }
    } catch (error) {
      console.error('提交审核结果失败:', error);
      toast({
        title: '提交失败',
        description: '无法提交审核结果，请稍后再试',
        variant: 'destructive'
      });
    } finally {
      setIsLoading(false);
    }
  };

  if (isLoading && stories.length === 0) {
    return (
      <ReviewerLayout>
        <div className="flex items-center justify-center h-64">
          <div className="text-center">
            <Clock className="h-8 w-8 animate-spin mx-auto mb-4" />
            <p>正在加载待审核内容...</p>
          </div>
        </div>
      </ReviewerLayout>
    );
  }

  if (stories.length === 0) {
    return (
      <ReviewerLayout>
        <div className="text-center py-12">
          <BookOpen className="h-12 w-12 mx-auto mb-4 text-muted-foreground" />
          <h2 className="text-xl font-semibold mb-2">暂无待审核故事</h2>
          <p className="text-muted-foreground mb-4">当前没有需要审核的故事内容</p>
          <Button onClick={() => navigate('/reviewer/dashboard')}>
            返回仪表盘
          </Button>
        </div>
      </ReviewerLayout>
    );
  }

  return (
    <ReviewerLayout>
      <div className="space-y-6">
        {/* 头部 */}
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-4">
            <Button variant="outline" onClick={() => navigate('/reviewer/dashboard')}>
              <ArrowLeft className="mr-2 h-4 w-4" />
              返回仪表盘
            </Button>
            <div>
              <h1 className="text-2xl font-bold">批量故事审核</h1>
              <p className="text-muted-foreground">
                进度: {currentIndex + 1} / {stories.length} |
                已审核: {reviewResults.length} / {stories.length}
              </p>
            </div>
          </div>

          <div className="flex space-x-2">
            <Button
              variant="outline"
              onClick={submitAllResults}
              disabled={reviewResults.length === 0 || isLoading}
            >
              <Save className="mr-2 h-4 w-4" />
              保存进度
            </Button>
            <Button
              onClick={submitAllResults}
              disabled={reviewResults.length === 0 || isLoading}
            >
              <Send className="mr-2 h-4 w-4" />
              提交审核结果
            </Button>
          </div>
        </div>

        {/* 进度条 */}
        <div className="w-full bg-gray-200 rounded-full h-2">
          <div
            className="bg-blue-600 h-2 rounded-full transition-all duration-300"
            style={{ width: `${((currentIndex + 1) / stories.length) * 100}%` }}
          />
        </div>

        {/* 当前故事 */}
        {currentStory && (
          <Card>
            <CardHeader>
              <div className="flex items-center justify-between">
                <div>
                  <CardTitle className="flex items-center space-x-2">
                    <BookOpen className="h-5 w-5" />
                    <span>{currentStory.title}</span>
                    {currentResult && (
                      <Badge variant={currentResult.action === 'approve' ? 'default' : 'destructive'}>
                        {currentResult.action === 'approve' ? '已批准' : '已拒绝'}
                      </Badge>
                    )}
                  </CardTitle>
                  <CardDescription className="flex items-center space-x-4 mt-2">
                    <span className="flex items-center">
                      <User className="h-4 w-4 mr-1" />
                      {currentStory.author}
                    </span>
                    <span className="flex items-center">
                      <Calendar className="h-4 w-4 mr-1" />
                      {new Date(currentStory.createdAt).toLocaleString()}
                    </span>
                    <span>序号: {currentStory.sequenceNumber}</span>
                  </CardDescription>
                </div>

                <div className="flex space-x-2">
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => setCurrentIndex(Math.max(0, currentIndex - 1))}
                    disabled={currentIndex === 0}
                  >
                    <ArrowLeft className="h-4 w-4" />
                  </Button>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => setCurrentIndex(Math.min(stories.length - 1, currentIndex + 1))}
                    disabled={currentIndex === stories.length - 1}
                  >
                    <ArrowRight className="h-4 w-4" />
                  </Button>
                </div>
              </div>
            </CardHeader>
            <CardContent className="space-y-4">
              {/* 故事内容 */}
              <div className="bg-gray-50 p-4 rounded-lg">
                <p className="whitespace-pre-wrap">{currentStory.content}</p>
              </div>

              {/* 标签 */}
              <div className="flex flex-wrap gap-2">
                {currentStory.tags.map((tag, index) => (
                  <Badge key={index} variant="secondary">{tag}</Badge>
                ))}
                <Badge variant="outline">{currentStory.category}</Badge>
              </div>

              {/* 审核原因 */}
              <div>
                <label className="block text-sm font-medium mb-2">
                  审核意见 {currentResult?.action === 'reject' && <span className="text-red-500">*</span>}
                </label>
                <Textarea
                  value={currentReason}
                  onChange={(e) => setCurrentReason(e.target.value)}
                  placeholder={currentResult?.action === 'reject' ? '请说明拒绝原因...' : '可选：添加审核意见...'}
                  rows={3}
                />
              </div>

              {/* 审核按钮 */}
              <div className="flex space-x-4">
                <Button
                  onClick={() => handleReview('approve')}
                  className="flex-1"
                  variant={currentResult?.action === 'approve' ? 'default' : 'outline'}
                >
                  <CheckCircle className="mr-2 h-4 w-4" />
                  批准
                </Button>
                <Button
                  onClick={() => handleReview('reject')}
                  className="flex-1"
                  variant={currentResult?.action === 'reject' ? 'destructive' : 'outline'}
                >
                  <XCircle className="mr-2 h-4 w-4" />
                  拒绝
                </Button>
              </div>
            </CardContent>
          </Card>
        )}
      </div>
    </ReviewerLayout>
  );
};

export default BatchStoryReviewPage;
