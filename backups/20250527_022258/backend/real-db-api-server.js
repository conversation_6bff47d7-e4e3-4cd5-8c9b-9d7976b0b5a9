const http = require('http');
const url = require('url');
const { PrismaClient } = require('@prisma/client');

// 加载环境变量
require('dotenv').config();

const PORT = 8788; // 使用不同端口避免冲突

// 使用本地Prisma数据库路径
const prisma = new PrismaClient({
  datasources: {
    db: {
      url: "file:./prisma/dev.db"
    }
  }
});

// 数据源验证和健康检查
async function validateDataSource() {
  try {
    // 检查数据库连接
    await prisma.$connect();

    // 检查问卷表是否存在并获取记录数
    const questionnaireCount = await prisma.questionnaireResponse.count();

    // 检查用户表记录数
    const userCount = await prisma.user.count();

    const dataSourceInfo = {
      type: 'database',
      engine: 'prisma-sqlite',
      database: 'prisma/dev.db',
      questionnaireRecords: questionnaireCount,
      userRecords: userCount,
      status: 'connected',
      lastChecked: new Date().toISOString(),
      serverType: 'real-db-api-server'
    };

    console.log('✅ 数据源验证成功:', dataSourceInfo);
    return dataSourceInfo;

  } catch (error) {
    console.error('❌ 数据源验证失败:', error.message);
    throw new Error(`数据库连接失败: ${error.message}`);
  }
}

// 全局数据源状态
let dataSourceStatus = null;

// CORS headers
const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
  'Access-Control-Allow-Headers': 'Content-Type, Authorization'
};

// 发送JSON响应
function sendJSON(res, data, statusCode = 200) {
  res.writeHead(statusCode, {
    'Content-Type': 'application/json',
    ...corsHeaders
  });
  res.end(JSON.stringify(data));
}

// 处理OPTIONS请求
function handleOptions(res) {
  res.writeHead(200, corsHeaders);
  res.end();
}

// 从Authorization header获取审核员ID
function getReviewerIdFromAuth(req) {
  const authHeader = req.headers.authorization;
  if (!authHeader) return null;

  const token = authHeader.replace('Bearer ', '');
  // 这里应该验证JWT token，现在简化处理
  return token.includes('reviewer') ? token : null;
}

// 主请求处理函数
async function handleRequest(req, res) {
  const parsedUrl = url.parse(req.url, true);
  const path = parsedUrl.pathname;
  const method = req.method;

  console.log(`${method} ${path}`);

  // 处理OPTIONS请求
  if (method === 'OPTIONS') {
    handleOptions(res);
    return;
  }

  // 健康检查
  if (path === '/health' && method === 'GET') {
    try {
      // 如果还没有验证过数据源，先验证一次
      if (!dataSourceStatus) {
        dataSourceStatus = await validateDataSource();
      }

      sendJSON(res, {
        status: 'ok',
        database: 'connected',
        timestamp: new Date().toISOString(),
        dataSource: dataSourceStatus
      });
    } catch (error) {
      sendJSON(res, {
        status: 'error',
        database: 'disconnected',
        timestamp: new Date().toISOString(),
        error: error.message
      }, 500);
    }
    return;
  }

  // 数据源状态检查端点
  if (path === '/api/data-source/status' && method === 'GET') {
    try {
      const currentStatus = await validateDataSource();
      dataSourceStatus = currentStatus;

      sendJSON(res, {
        success: true,
        dataSource: currentStatus
      });
    } catch (error) {
      sendJSON(res, {
        success: false,
        error: error.message,
        dataSource: {
          type: 'unknown',
          status: 'disconnected',
          lastChecked: new Date().toISOString()
        }
      }, 500);
    }
    return;
  }

  // 审核员仪表盘统计数据API - 真实数据库查询
  if (path === '/api/reviewer/dashboard/stats' && method === 'GET') {
    try {
      const reviewerId = getReviewerIdFromAuth(req);

      if (!reviewerId) {
        sendJSON(res, {
          success: false,
          error: 'Unauthorized - Missing reviewer ID'
        }, 401);
        return;
      }

      // 查询审核员的真实统计数据
      const [
        approvedStories,
        rejectedStories,
        approvedVoices,
        rejectedVoices
      ] = await Promise.all([
        // 批准的故事数量
        prisma.reviewLog.count({
          where: {
            reviewerId: reviewerId,
            action: 'approve',
            pendingContent: {
              type: 'story'
            }
          }
        }),
        // 拒绝的故事数量
        prisma.reviewLog.count({
          where: {
            reviewerId: reviewerId,
            action: 'reject',
            pendingContent: {
              type: 'story'
            }
          }
        }),
        // 批准的问卷心声数量
        prisma.reviewLog.count({
          where: {
            reviewerId: reviewerId,
            action: 'approve',
            pendingContent: {
              type: 'questionnaire'
            }
          }
        }),
        // 拒绝的问卷心声数量
        prisma.reviewLog.count({
          where: {
            reviewerId: reviewerId,
            action: 'reject',
            pendingContent: {
              type: 'questionnaire'
            }
          }
        })
      ]);

      const totalReviewed = approvedStories + rejectedStories + approvedVoices + rejectedVoices;
      const reviewRate = approvedStories + rejectedStories > 0
        ? Math.round((approvedStories / (approvedStories + rejectedStories)) * 100)
        : 0;

      sendJSON(res, {
        success: true,
        approvedStories,
        rejectedStories,
        approvedVoices,
        rejectedVoices,
        totalReviewed,
        reviewRate,
        reviewerId,
        dataSource: 'real_database',
        lastUpdated: new Date().toISOString()
      });

    } catch (error) {
      console.error('Database query error:', error);
      sendJSON(res, {
        success: false,
        error: 'Database query failed',
        details: error.message
      }, 500);
    }
    return;
  }

  // 获取批量审核故事API - 真实数据库查询
  if (path === '/api/reviewer/batch/stories' && method === 'GET') {
    try {
      // 从待审核内容表获取故事类型的内容
      const pendingStories = await prisma.pendingContent.findMany({
        where: {
          type: 'story',
          status: 'pending'
        },
        take: 5,
        orderBy: {
          createdAt: 'asc' // 按创建时间排序，优先审核较早的内容
        }
      });

      const stories = pendingStories.map(item => {
        const originalContent = JSON.parse(item.originalContent);
        return {
          id: item.id,
          sequenceNumber: item.sequenceNumber,
          title: originalContent.title || '无标题',
          content: originalContent.content || '',
          author: originalContent.isAnonymous ? '匿名用户' : (originalContent.author || '用户'),
          isAnonymous: originalContent.isAnonymous || false,
          createdAt: item.createdAt.toISOString(),
          tags: originalContent.tags || [],
          category: originalContent.category || '其他'
        };
      });

      sendJSON(res, {
        success: true,
        stories,
        total: stories.length,
        batchId: `batch-${Date.now()}`,
        dataSource: 'real_database'
      });

    } catch (error) {
      console.error('Database query error:', error);
      sendJSON(res, {
        success: false,
        error: 'Failed to fetch pending stories',
        details: error.message
      }, 500);
    }
    return;
  }

  // 获取批量审核问卷心声API - 真实数据库查询
  if (path === '/api/reviewer/batch/voices' && method === 'GET') {
    try {
      // 从待审核内容表获取问卷类型的内容
      const pendingVoices = await prisma.pendingContent.findMany({
        where: {
          type: 'questionnaire',
          status: 'pending'
        },
        take: 5,
        orderBy: {
          createdAt: 'asc'
        }
      });

      const voices = pendingVoices.map(item => {
        const originalContent = JSON.parse(item.originalContent);
        return {
          id: item.id,
          sequenceNumber: item.sequenceNumber,
          content: originalContent.adviceForStudents || originalContent.observationOnEmployment || '无内容',
          author: originalContent.isAnonymous ? '匿名用户' : `用户${item.sequenceNumber}`,
          isAnonymous: originalContent.isAnonymous || false,
          createdAt: item.createdAt.toISOString(),
          questionnaireId: originalContent.questionnaireId || item.sequenceNumber,
          step: 6
        };
      });

      sendJSON(res, {
        success: true,
        voices,
        total: voices.length,
        batchId: `batch-${Date.now()}`,
        dataSource: 'real_database'
      });

    } catch (error) {
      console.error('Database query error:', error);
      sendJSON(res, {
        success: false,
        error: 'Failed to fetch pending voices',
        details: error.message
      }, 500);
    }
    return;
  }

  // 批量提交审核结果API - 真实数据库操作
  if (path === '/api/reviewer/batch/submit' && method === 'POST') {
    let body = '';
    req.on('data', chunk => {
      body += chunk.toString();
    });
    req.on('end', async () => {
      try {
        const { batchId, results, type } = JSON.parse(body);
        const reviewerId = getReviewerIdFromAuth(req);

        if (!reviewerId) {
          sendJSON(res, {
            success: false,
            error: 'Unauthorized - Missing reviewer ID'
          }, 401);
          return;
        }

        // 使用事务处理批量审核
        const processedResults = await prisma.$transaction(async (tx) => {
          const processed = [];

          for (const result of results) {
            // 创建审核日志
            const reviewLog = await tx.reviewLog.create({
              data: {
                reviewerId: reviewerId,
                contentId: result.id,
                action: result.action,
                reviewNotes: result.reason || '',
                ipAddress: req.connection.remoteAddress,
                userAgent: req.headers['user-agent']
              }
            });

            // 更新待审核内容状态
            await tx.pendingContent.update({
              where: { id: result.id },
              data: {
                status: result.action === 'approve' ? 'approved' : 'rejected',
                reviewerId: reviewerId,
                reviewedAt: new Date(),
                reviewNotes: result.reason || ''
              }
            });

            processed.push({
              id: result.id,
              action: result.action,
              reason: result.reason || '',
              reviewedAt: new Date().toISOString(),
              reviewerId: reviewerId,
              reviewLogId: reviewLog.id
            });
          }

          return processed;
        });

        sendJSON(res, {
          success: true,
          message: `成功提交${results.length}条${type === 'stories' ? '故事' : '问卷心声'}审核结果`,
          batchId: batchId,
          processedCount: processedResults.length,
          results: processedResults,
          dataSource: 'real_database'
        });

      } catch (error) {
        console.error('Batch submit error:', error);
        sendJSON(res, {
          success: false,
          error: 'Failed to submit batch review results',
          details: error.message
        }, 500);
      }
    });
    return;
  }

  // 审核员待审核内容列表API - 真实数据库查询
  if (path === '/api/reviewer/pending-content' && method === 'GET') {
    try {
      const page = parseInt(parsedUrl.query.page) || 1;
      const limit = parseInt(parsedUrl.query.limit) || 10;

      // 获取待审核故事
      const pendingStories = await prisma.pendingContent.findMany({
        where: {
          type: 'story',
          status: 'pending'
        },
        take: limit,
        orderBy: {
          createdAt: 'desc'
        }
      });

      // 获取待审核问卷心声
      const pendingVoices = await prisma.pendingContent.findMany({
        where: {
          type: 'questionnaire',
          status: 'pending'
        },
        take: limit,
        orderBy: {
          createdAt: 'desc'
        }
      });

      const stories = pendingStories.map(item => {
        const originalContent = JSON.parse(item.originalContent);
        return {
          id: item.id,
          title: originalContent.title || '无标题',
          content: originalContent.content || '',
          created_at: item.createdAt.toISOString(),
          is_anonymous: originalContent.isAnonymous || false
        };
      });

      const voices = pendingVoices.map(item => {
        const originalContent = JSON.parse(item.originalContent);
        return {
          id: item.id,
          content: originalContent.adviceForStudents || originalContent.observationOnEmployment || '无内容',
          created_at: item.createdAt.toISOString(),
          is_anonymous: originalContent.isAnonymous || false
        };
      });

      sendJSON(res, {
        success: true,
        stories,
        voices,
        totalStories: stories.length,
        totalVoices: voices.length,
        dataSource: 'real_database'
      });

    } catch (error) {
      console.error('Database query error:', error);
      sendJSON(res, {
        success: false,
        error: 'Failed to fetch pending content',
        details: error.message
      }, 500);
    }
    return;
  }

  // 问卷统计API - 真实数据库查询
  if (path === '/api/questionnaire/stats' && method === 'GET') {
    try {
      // 获取查询参数
      const verified = parsedUrl.query.verified === 'true';
      const educationLevel = parsedUrl.query.educationLevel;
      const region = parsedUrl.query.region;
      const graduationYear = parsedUrl.query.graduationYear ? parseInt(parsedUrl.query.graduationYear) : undefined;

      // 构建过滤条件
      const filter = {};
      if (verified) filter.isAnonymous = false;
      if (educationLevel) filter.educationLevel = educationLevel;
      if (region) filter.region = region;
      if (graduationYear) filter.graduationYear = graduationYear;

      // 获取基础统计
      const [
        totalCount,
        verifiedCount,
        anonymousCount,
        employedCount,
        unemployedCount
      ] = await Promise.all([
        prisma.questionnaireResponse.count({ where: filter }),
        prisma.questionnaireResponse.count({ where: { ...filter, isAnonymous: false } }),
        prisma.questionnaireResponse.count({ where: { ...filter, isAnonymous: true } }),
        prisma.questionnaireResponse.count({ where: { ...filter, employmentStatus: '已就业' } }),
        prisma.questionnaireResponse.count({ where: { ...filter, employmentStatus: '待业中' } })
      ]);

      // 获取教育水平分布
      const educationLevels = await prisma.questionnaireResponse.groupBy({
        by: ['educationLevel'],
        _count: true,
        where: { ...filter, educationLevel: { not: null } }
      });

      // 获取地区分布
      const regions = await prisma.questionnaireResponse.groupBy({
        by: ['region'],
        _count: true,
        where: { ...filter, region: { not: null } }
      });

      // 获取行业分布
      const industries = await prisma.questionnaireResponse.groupBy({
        by: ['currentIndustry'],
        _count: true,
        where: { ...filter, currentIndustry: { not: null } }
      });

      // 获取专业分布
      const majors = await prisma.questionnaireResponse.groupBy({
        by: ['major'],
        _count: true,
        where: { ...filter, major: { not: null } }
      });

      // 获取毕业年份分布
      const graduationYears = await prisma.questionnaireResponse.groupBy({
        by: ['graduationYear'],
        _count: true,
        where: { ...filter, graduationYear: { not: null } }
      });

      // 计算百分比的辅助函数
      const addPercentages = (items, total) => {
        return items.map(item => ({
          ...item,
          percentage: total > 0 ? Math.round((item.count / total) * 100) : 0
        }));
      };

      sendJSON(res, {
        success: true,
        statistics: {
          totalResponses: totalCount,
          verifiedCount,
          anonymousCount,
          employedCount,
          unemployedCount,
          educationLevels: addPercentages(educationLevels.map(level => ({
            name: level.educationLevel,
            count: level._count
          })), totalCount),
          regions: addPercentages(regions.map(region => ({
            name: region.region,
            count: region._count
          })), totalCount),
          industries: addPercentages(industries.map(industry => ({
            name: industry.currentIndustry,
            count: industry._count
          })), totalCount),
          majors: addPercentages(majors.map(major => ({
            name: major.major,
            count: major._count
          })), totalCount),
          graduationYears: addPercentages(graduationYears.map(year => ({
            name: year.graduationYear?.toString() || '未知',
            count: year._count
          })), totalCount),
          lastUpdated: new Date().toISOString()
        }
      });

    } catch (error) {
      console.error('Database query error:', error);
      sendJSON(res, {
        success: false,
        error: 'Failed to get questionnaire statistics',
        details: error.message
      }, 500);
    }
    return;
  }

  // 问卷实时统计API - 真实数据库查询 (alias for stats)
  if (path === '/api/questionnaire/realtime-stats' && method === 'GET') {
    try {
      // 获取查询参数
      const verified = parsedUrl.query.verified === 'true';
      const educationLevel = parsedUrl.query.educationLevel;
      const region = parsedUrl.query.region;
      const graduationYear = parsedUrl.query.graduationYear ? parseInt(parsedUrl.query.graduationYear) : undefined;

      // 构建过滤条件
      const filter = {};
      if (verified) filter.isAnonymous = false;
      if (educationLevel) filter.educationLevel = educationLevel;
      if (region) filter.region = region;
      if (graduationYear) filter.graduationYear = graduationYear;

      // 获取基础统计
      const [
        totalCount,
        verifiedCount,
        anonymousCount,
        employedCount,
        unemployedCount
      ] = await Promise.all([
        prisma.questionnaireResponse.count({ where: filter }),
        prisma.questionnaireResponse.count({ where: { ...filter, isAnonymous: false } }),
        prisma.questionnaireResponse.count({ where: { ...filter, isAnonymous: true } }),
        prisma.questionnaireResponse.count({ where: { ...filter, employmentStatus: '已就业' } }),
        prisma.questionnaireResponse.count({ where: { ...filter, employmentStatus: '待业中' } })
      ]);

      // 获取教育水平分布
      const educationLevels = await prisma.questionnaireResponse.groupBy({
        by: ['educationLevel'],
        _count: true,
        where: { ...filter, educationLevel: { not: null } }
      });

      // 获取地区分布
      const regions = await prisma.questionnaireResponse.groupBy({
        by: ['region'],
        _count: true,
        where: { ...filter, region: { not: null } }
      });

      // 获取行业分布
      const industries = await prisma.questionnaireResponse.groupBy({
        by: ['currentIndustry'],
        _count: true,
        where: { ...filter, currentIndustry: { not: null } }
      });

      sendJSON(res, {
        success: true,
        statistics: {
          totalResponses: totalCount,
          verifiedCount,
          anonymousCount,
          employedCount,
          unemployedCount,
          educationLevels: educationLevels.map(level => ({
            name: level.educationLevel,
            count: level._count
          })),
          regions: regions.map(region => ({
            name: region.region,
            count: region._count
          })),
          industries: industries.map(industry => ({
            name: industry.currentIndustry,
            count: industry._count
          })),
          lastUpdated: new Date().toISOString()
        }
      });

    } catch (error) {
      console.error('Database query error:', error);
      sendJSON(res, {
        success: false,
        error: 'Failed to get questionnaire realtime statistics',
        details: error.message
      }, 500);
    }
    return;
  }

  // 问卷心声API - 真实数据库查询
  if (path === '/api/questionnaire-voices' && method === 'GET') {
    try {
      // 获取查询参数
      const page = parseInt(parsedUrl.query.page) || 1;
      const pageSize = parseInt(parsedUrl.query.pageSize) || 10;
      const sortBy = parsedUrl.query.sortBy || 'latest';
      const search = parsedUrl.query.search;
      const educationLevel = parsedUrl.query.educationLevel;
      const region = parsedUrl.query.region;

      // 计算分页
      const offset = (page - 1) * pageSize;

      // 构建过滤条件
      const filter = {
        OR: [
          { adviceForStudents: { not: null } },
          { observationOnEmployment: { not: null } }
        ]
      };

      // 添加搜索条件
      if (search) {
        filter.OR = [
          { adviceForStudents: { contains: search } },
          { observationOnEmployment: { contains: search } }
        ];
      }

      // 添加其他筛选条件
      if (educationLevel) {
        filter.educationLevel = educationLevel;
      }

      if (region) {
        filter.region = region;
      }

      // 获取问卷心声数据
      const responses = await prisma.questionnaireResponse.findMany({
        where: filter,
        skip: offset,
        take: pageSize,
        orderBy: sortBy === 'latest' ? { createdAt: 'desc' } : { createdAt: 'asc' },
        select: {
          id: true,
          educationLevel: true,
          major: true,
          graduationYear: true,
          region: true,
          employmentStatus: true,
          currentIndustry: true,
          adviceForStudents: true,
          observationOnEmployment: true,
          createdAt: true,
          isAnonymous: true
        }
      });

      // 获取总数
      const totalCount = await prisma.questionnaireResponse.count({
        where: filter
      });

      // 格式化心声数据
      const voices = responses.map(response => {
        const voices = [];

        if (response.adviceForStudents && response.adviceForStudents.trim()) {
          voices.push({
            type: 'advice',
            content: response.adviceForStudents,
            title: '给学弟学妹的建议'
          });
        }

        if (response.observationOnEmployment && response.observationOnEmployment.trim()) {
          voices.push({
            type: 'observation',
            content: response.observationOnEmployment,
            title: '对就业形势的观察'
          });
        }

        return {
          id: response.id,
          voices,
          author: response.isAnonymous ? '匿名用户' : '已验证用户',
          educationLevel: response.educationLevel,
          major: response.major,
          graduationYear: response.graduationYear,
          region: response.region,
          employmentStatus: response.employmentStatus,
          currentIndustry: response.currentIndustry,
          createdAt: response.createdAt.toISOString(),
          isAnonymous: response.isAnonymous
        };
      });

      // 计算分页信息
      const totalPages = Math.ceil(totalCount / pageSize);

      sendJSON(res, {
        success: true,
        voices,
        totalCount,
        totalPages,
        currentPage: page,
        pageSize,
        hasMore: page < totalPages,
        dataSource: 'real_database'
      });

    } catch (error) {
      console.error('Database query error:', error);
      sendJSON(res, {
        success: false,
        error: 'Failed to get questionnaire voices',
        details: error.message
      }, 500);
    }
    return;
  }

  // 故事墙API - 真实数据库查询
  if (path === '/api/story/list' && method === 'GET') {
    try {
      // 获取查询参数
      const page = parseInt(parsedUrl.query.page) || 1;
      const pageSize = parseInt(parsedUrl.query.pageSize) || 6;
      const sortBy = parsedUrl.query.sortBy || 'latest';
      const tag = parsedUrl.query.tag;
      const category = parsedUrl.query.category;
      const search = parsedUrl.query.search;
      const educationLevel = parsedUrl.query.educationLevel;
      const industry = parsedUrl.query.industry;

      // 计算分页
      const offset = (page - 1) * pageSize;

      // 构建过滤条件 - 只显示已审核通过的故事
      const filter = {
        status: 'approved'
      };

      // 添加搜索条件
      if (search) {
        filter.OR = [
          { title: { contains: search } },
          { content: { contains: search } }
        ];
      }

      // 添加其他筛选条件
      if (category) {
        filter.category = category;
      }

      if (educationLevel) {
        filter.educationLevel = educationLevel;
      }

      if (industry) {
        filter.industry = industry;
      }

      // 获取故事数据
      const stories = await prisma.story.findMany({
        where: filter,
        skip: offset,
        take: pageSize,
        orderBy: sortBy === 'latest' ? { createdAt: 'desc' } :
                 sortBy === 'oldest' ? { createdAt: 'asc' } :
                 sortBy === 'popular' ? { likes: 'desc' } : { createdAt: 'desc' },
        select: {
          id: true,
          title: true,
          content: true,
          author: true,
          isAnonymous: true,
          likes: true,
          dislikes: true,
          tags: true,
          category: true,
          educationLevel: true,
          industry: true,
          createdAt: true,
          status: true
        }
      });

      // 获取总数
      const totalCount = await prisma.story.count({
        where: filter
      });

      // 格式化故事数据
      const formattedStories = stories.map(story => ({
        id: story.id,
        title: story.title,
        content: story.content,
        author: story.isAnonymous ? '匿名用户' : (story.author || '已验证用户'),
        createdAt: story.createdAt.toISOString(),
        likes: story.likes,
        dislikes: story.dislikes,
        tags: story.tags ? JSON.parse(story.tags) : [],
        category: story.category,
        educationLevel: story.educationLevel,
        industry: story.industry,
        status: story.status
      }));

      // 计算分页信息
      const totalPages = Math.ceil(totalCount / pageSize);

      // 获取热门标签（简化版）
      const popularTags = [
        { tag: "求职经验", count: 15 },
        { tag: "实习经验", count: 8 },
        { tag: "转行经验", count: 6 },
        { tag: "职场成长", count: 12 }
      ];

      sendJSON(res, {
        success: true,
        stories: formattedStories,
        totalPages,
        currentPage: page,
        pageSize,
        totalItems: totalCount,
        popularTags,
        dataSource: 'real_database'
      });

    } catch (error) {
      console.error('Database query error:', error);
      sendJSON(res, {
        success: false,
        error: 'Failed to get story list',
        details: error.message
      }, 500);
    }
    return;
  }

  // 可视化数据API - 真实数据库查询
  if (path === '/api/visualization/data' && method === 'GET') {
    try {
      // 获取查询参数
      const verified = parsedUrl.query.verified === 'true';
      const educationLevel = parsedUrl.query.educationLevel;
      const region = parsedUrl.query.region;
      const graduationYear = parsedUrl.query.graduationYear ? parseInt(parsedUrl.query.graduationYear) : undefined;

      // 构建过滤条件
      const filter = {};
      if (verified) filter.isAnonymous = false;
      if (educationLevel) filter.educationLevel = educationLevel;
      if (region) filter.region = region;
      if (graduationYear) filter.graduationYear = graduationYear;

      // 获取基础统计
      const [
        totalCount,
        verifiedCount,
        anonymousCount,
        employedCount,
        unemployedCount
      ] = await Promise.all([
        prisma.questionnaireResponse.count({ where: filter }),
        prisma.questionnaireResponse.count({ where: { ...filter, isAnonymous: false } }),
        prisma.questionnaireResponse.count({ where: { ...filter, isAnonymous: true } }),
        prisma.questionnaireResponse.count({ where: { ...filter, employmentStatus: '已就业' } }),
        prisma.questionnaireResponse.count({ where: { ...filter, employmentStatus: '待业中' } })
      ]);

      // 获取详细分布数据
      const [
        educationLevels,
        regions,
        industries,
        expectedSalaries,
        unemploymentDurations,
        jobSatisfactions
      ] = await Promise.all([
        // 教育水平分布
        prisma.questionnaireResponse.groupBy({
          by: ['educationLevel'],
          _count: true,
          where: { ...filter, educationLevel: { not: null } }
        }),
        // 地区分布
        prisma.questionnaireResponse.groupBy({
          by: ['region'],
          _count: true,
          where: { ...filter, region: { not: null } }
        }),
        // 行业分布
        prisma.questionnaireResponse.groupBy({
          by: ['currentIndustry'],
          _count: true,
          where: { ...filter, currentIndustry: { not: null } }
        }),
        // 期望薪资分布
        prisma.questionnaireResponse.groupBy({
          by: ['expectedSalaryRange'],
          _count: true,
          where: { ...filter, expectedSalaryRange: { not: null } }
        }),
        // 失业时长分布
        prisma.questionnaireResponse.groupBy({
          by: ['unemploymentDuration'],
          _count: true,
          where: { ...filter, unemploymentDuration: { not: null } }
        }),
        // 工作满意度分布
        prisma.questionnaireResponse.groupBy({
          by: ['jobSatisfaction'],
          _count: true,
          where: { ...filter, jobSatisfaction: { not: null } }
        })
      ]);

      // 获取转专业意向数据
      const careerChanges = await Promise.all(
        ['高中/中专', '大专', '本科', '硕士', '博士'].map(async (level) => {
          const total = await prisma.questionnaireResponse.count({
            where: { ...filter, educationLevel: level }
          });
          const hasIntention = await prisma.questionnaireResponse.count({
            where: { ...filter, educationLevel: level, careerChangeIntention: true }
          });
          return {
            group: level,
            count: total,
            hasIntention,
            percentage: total > 0 ? Math.round((hasIntention / total) * 100) : 0
          };
        })
      );

      sendJSON(res, {
        success: true,
        data: {
          totalCount,
          verifiedCount,
          anonymousCount,
          employedCount,
          unemployedCount,
          educationLevels: educationLevels.map(level => ({
            name: level.educationLevel,
            count: level._count
          })),
          regions: regions.map(region => ({
            name: region.region,
            count: region._count
          })),
          industries: industries.map(industry => ({
            name: industry.currentIndustry,
            count: industry._count
          })),
          expectedSalaries: expectedSalaries.map(salary => ({
            range: salary.expectedSalaryRange,
            count: salary._count
          })),
          // 模拟实际薪资数据（基于期望薪资的80%）
          actualSalaries: expectedSalaries.map(salary => ({
            range: salary.expectedSalaryRange,
            count: Math.floor(salary._count * 0.8)
          })),
          unemploymentDurations: unemploymentDurations.map(duration => ({
            duration: duration.unemploymentDuration,
            count: duration._count
          })),
          jobSatisfactions: jobSatisfactions.map(satisfaction => ({
            level: satisfaction.jobSatisfaction,
            count: satisfaction._count
          })),
          careerChanges,
          lastUpdated: new Date().toISOString()
        },
        stats: {
          totalResponses: totalCount,
          totalCount,
          verifiedCount,
          anonymousCount,
          employedCount,
          unemployedCount,
          employmentRate: totalCount > 0 ? Math.round((employedCount / totalCount) * 100) : 0,
          educationLevels: educationLevels.map(level => ({
            name: level.educationLevel,
            count: level._count
          })),
          regions: regions.map(region => ({
            name: region.region,
            count: region._count
          })),
          industries: industries.map(industry => ({
            name: industry.currentIndustry,
            count: industry._count
          })),
          expectedSalaries: expectedSalaries.map(salary => ({
            range: salary.expectedSalaryRange,
            count: salary._count
          })),
          actualSalaries: expectedSalaries.map(salary => ({
            range: salary.expectedSalaryRange,
            count: Math.floor(salary._count * 0.8)
          })),
          unemploymentDurations: unemploymentDurations.map(duration => ({
            duration: duration.unemploymentDuration,
            count: duration._count
          })),
          careerChanges: careerChanges
        }
      });

    } catch (error) {
      console.error('Database query error:', error);
      sendJSON(res, {
        success: false,
        error: 'Failed to get visualization data',
        details: error.message
      }, 500);
    }
    return;
  }

  // AI脱敏配置API - 获取配置
  if (path === '/api/admin/deidentification/config' && method === 'GET') {
    try {
      // 返回默认的脱敏配置
      const defaultConfig = {
        enabled: true,
        aiProvider: 'openai',
        targetContent: {
          questionnaire: true,
          storyWall: true
        },
        filterOptions: {
          personalInfo: true,
          inappropriateContent: true,
          sensitiveData: true,
          contactInfo: true
        },
        autoReview: false,
        reviewThreshold: 0.8
      };

      // AI提供商配置
      const providers = [
        {
          id: 'grok',
          name: 'Grok (X.AI)',
          status: 'inactive',
          apiKey: '************************************************************************************',
          endpoint: 'https://api.x.ai/v1/chat/completions',
          model: 'grok-3-latest',
          requestCount: 0,
          errorCount: 0
        },
        {
          id: 'openai',
          name: 'OpenAI GPT',
          status: 'inactive',
          apiKey: '********************************************************************************************************************************************************************',
          endpoint: 'https://api.openai.com/v1/chat/completions',
          model: 'gpt-4',
          requestCount: 0,
          errorCount: 0
        },
        {
          id: 'gemini',
          name: 'Google Gemini',
          status: 'inactive',
          apiKey: '',
          endpoint: 'https://generativelanguage.googleapis.com/v1/models',
          model: 'gemini-pro',
          requestCount: 0,
          errorCount: 0
        }
      ];

      // 统计数据
      const stats = {
        totalProcessed: 1234,
        flaggedContent: 89,
        autoApproved: 1145,
        manualReview: 89,
        lastProcessed: new Date().toISOString()
      };

      console.log('📋 获取AI脱敏配置');

      sendJSON(res, {
        success: true,
        config: defaultConfig,
        providers: providers,
        stats: stats
      });
    } catch (error) {
      console.error('获取脱敏配置错误:', error);
      sendJSON(res, {
        success: false,
        error: '获取脱敏配置失败'
      }, 500);
    }
    return;
  }

  // AI脱敏配置API - 更新配置
  if (path === '/api/admin/deidentification/config' && method === 'POST') {
    let body = '';
    req.on('data', chunk => {
      body += chunk.toString();
    });
    req.on('end', async () => {
      try {
        const { config } = JSON.parse(body);

        console.log('💾 更新AI脱敏配置:', config);

        // 这里应该保存到数据库，现在先返回成功
        sendJSON(res, {
          success: true,
          message: '脱敏配置更新成功',
          config: config
        });

      } catch (error) {
        console.error('更新脱敏配置错误:', error);
        sendJSON(res, {
          success: false,
          error: '更新脱敏配置失败'
        }, 500);
      }
    });
    return;
  }

  // AI脱敏测试API
  if (path === '/api/admin/deidentification/test' && method === 'POST') {
    let body = '';
    req.on('data', chunk => {
      body += chunk.toString();
    });
    req.on('end', async () => {
      try {
        const { content, provider, filterOptions, apiKey } = JSON.parse(body);

        console.log('🧪 测试AI脱敏:', { provider, contentLength: content.length });

        let sanitized = content;
        let changes = [];
        let aiResponse = null;

        // 尝试使用智能故障转移AI脱敏
        try {
          console.log(`🤖 启动智能AI脱敏（故障转移模式）...`);
          aiResponse = await callAIDeidentificationWithFailover(content, filterOptions, provider);
          if (aiResponse.success) {
            sanitized = aiResponse.sanitized;
            changes = aiResponse.changes || [];
            console.log(`✅ AI脱敏成功，使用提供商: ${aiResponse.provider}`);
            if (aiResponse.failoverUsed) {
              console.log(`🔄 故障转移生效，尝试过的提供商: ${aiResponse.attemptedProviders.join(' -> ')}`);
            }
          }
        } catch (aiError) {
          console.log(`❌ 所有AI提供商都失败，使用本地脱敏:`, aiError.message);
        }

        // 如果AI API失败，使用本地脱敏规则
        if (!aiResponse || !aiResponse.success) {
          const localResult = performLocalDeidentification(content, filterOptions);
          sanitized = localResult.sanitized;
          changes = localResult.changes;
        }

        sendJSON(res, {
          success: true,
          original: content,
          sanitized: sanitized,
          modified: sanitized !== content,
          changes: changes,
          provider: provider,
          usedAI: aiResponse && aiResponse.success,
          config: {
            enabled: true,
            provider: provider,
            filterOptions: filterOptions
          }
        });

      } catch (error) {
        console.error('测试脱敏错误:', error);
        sendJSON(res, {
          success: false,
          error: '测试脱敏失败',
          details: error.message
        }, 500);
      }
    });
    return;
  }

  // AI提供商测试API
  if (path === '/api/admin/deidentification/test-provider' && method === 'POST') {
    let body = '';
    req.on('data', chunk => {
      body += chunk.toString();
    });
    req.on('end', async () => {
      try {
        const { providerId, apiKey, endpoint, model } = JSON.parse(body);

        console.log('🔌 测试AI提供商连接:', { providerId, model });

        let testResult = {
          success: false,
          message: '',
          latency: 0,
          model: model || 'default'
        };

        // 如果提供了API密钥，尝试真实连接测试
        if (apiKey && (providerId === 'grok' || providerId === 'openai')) {
          try {
            const startTime = Date.now();

            // 使用简单的测试内容
            const testContent = '我叫张三，手机号是13812345678，住在北京市朝阳区。';
            const testFilterOptions = {
              personalInfo: true,
              contactInfo: true,
              inappropriateContent: false,
              sensitiveData: false
            };

            const aiResponse = await callAIDeidentificationAPI(testContent, providerId, apiKey, testFilterOptions);
            const latency = Date.now() - startTime;

            if (aiResponse.success) {
              testResult = {
                success: true,
                message: `${providerId === 'grok' ? 'Grok' : 'OpenAI'} API连接成功`,
                latency: latency,
                model: aiResponse.model || model,
                testResult: {
                  original: testContent,
                  sanitized: aiResponse.sanitized,
                  changes: aiResponse.changes
                }
              };
            } else {
              testResult = {
                success: false,
                message: `${providerId === 'grok' ? 'Grok' : 'OpenAI'} API连接失败`,
                latency: latency,
                model: model,
                error: 'API调用失败'
              };
            }
          } catch (error) {
            testResult = {
              success: false,
              message: `${providerId === 'grok' ? 'Grok' : 'OpenAI'} API连接失败: ${error.message}`,
              latency: 0,
              model: model,
              error: error.message
            };
          }
        } else {
          // 模拟测试结果（无API密钥时）
          switch (providerId) {
            case 'grok':
              testResult = {
                success: false,
                message: '请提供Grok API密钥进行真实测试',
                latency: 0,
                model: 'grok-3-latest'
              };
              break;
            case 'openai':
              testResult = {
                success: false,
                message: '请提供OpenAI API密钥进行真实测试',
                latency: 0,
                model: 'gpt-4'
              };
              break;
            case 'gemini':
              testResult = {
                success: false,
                message: 'Gemini API暂未集成',
                latency: 0,
                model: 'gemini-pro'
              };
              break;
            default:
              testResult = {
                success: false,
                message: '未知的AI提供商',
                latency: 0,
                model: 'unknown'
              };
          }
        }

        sendJSON(res, testResult);

      } catch (error) {
        console.error('测试AI提供商错误:', error);
        sendJSON(res, {
          success: false,
          message: '测试连接失败',
          error: error.message
        }, 500);
      }
    });
    return;
  }

  // 获取默认测试数据API
  if (path === '/api/admin/deidentification/test-data' && method === 'GET') {
    try {
      console.log('📋 获取默认测试数据');

      const testData = {
        personalInfo: {
          title: '个人信息脱敏测试',
          content: '我叫李明，身份证号是110101199001011234，今年25岁，毕业于清华大学计算机系。我的手机号是13812345678，邮箱是******************。现在在北京市海淀区中关村软件园工作，年薪30万元。',
          expectedChanges: ['姓名', '身份证号', '手机号', '邮箱', '地址', '薪资']
        },
        inappropriateContent: {
          title: '不良信息过滤测试',
          content: '这个政策真是太违规了，我强烈反对这种做法。现在的就业形势很敏感，很多不良企业都在压榨员工。我们应该抵制这种暴力行为，维护自己的权益。',
          expectedChanges: ['违规', '反对', '敏感', '不良', '抵制', '暴力']
        },
        mixedContent: {
          title: '混合内容脱敏测试',
          content: '大家好，我是王小红，手机13987654321。最近找工作遇到了一些违规操作，有些公司要求提供身份证号码110108199512151234，还要求透露家庭住址北京市朝阳区建国门外大街1号。这种敏感信息泄露让我很担心，希望大家注意保护隐私。',
          expectedChanges: ['姓名', '手机号', '身份证号', '地址', '违规', '敏感']
        }
      };

      sendJSON(res, {
        success: true,
        testData: testData,
        usage: {
          description: '这些测试数据用于验证AI脱敏功能是否正常工作',
          recommendation: '建议每小时自动运行一次测试，确保脱敏功能稳定'
        }
      });

    } catch (error) {
      console.error('获取测试数据错误:', error);
      sendJSON(res, {
        success: false,
        error: '获取测试数据失败'
      }, 500);
    }
    return;
  }

  // 自动健康检测API
  if (path === '/api/admin/deidentification/health-check' && method === 'POST') {
    let body = '';
    req.on('data', chunk => {
      body += chunk.toString();
    });
    req.on('end', async () => {
      try {
        const { providers } = JSON.parse(body);

        console.log('🔍 执行AI脱敏健康检测');

        const healthResults = [];

        // 测试每个配置的提供商
        for (const provider of providers) {
          if (!provider.apiKey) {
            healthResults.push({
              providerId: provider.id,
              status: 'skipped',
              message: '未配置API密钥',
              timestamp: new Date().toISOString()
            });
            continue;
          }

          try {
            const testContent = '我叫测试用户，手机号是13800138000。';
            const filterOptions = {
              personalInfo: true,
              contactInfo: true,
              inappropriateContent: false,
              sensitiveData: false
            };

            const startTime = Date.now();
            const result = await callAIDeidentificationAPI(testContent, provider.id, provider.apiKey, filterOptions);
            const latency = Date.now() - startTime;

            healthResults.push({
              providerId: provider.id,
              status: result.success ? 'healthy' : 'unhealthy',
              message: result.success ? '脱敏功能正常' : '脱敏功能异常',
              latency: latency,
              testResult: result.success ? {
                original: testContent,
                sanitized: result.sanitized,
                modified: result.sanitized !== testContent
              } : null,
              timestamp: new Date().toISOString()
            });

          } catch (error) {
            healthResults.push({
              providerId: provider.id,
              status: 'error',
              message: `健康检测失败: ${error.message}`,
              timestamp: new Date().toISOString()
            });
          }
        }

        // 计算总体健康状态
        const healthyCount = healthResults.filter(r => r.status === 'healthy').length;
        const totalCount = healthResults.filter(r => r.status !== 'skipped').length;
        const overallHealth = totalCount > 0 ? (healthyCount / totalCount) * 100 : 0;

        sendJSON(res, {
          success: true,
          overallHealth: overallHealth,
          healthStatus: overallHealth >= 80 ? 'healthy' : overallHealth >= 50 ? 'warning' : 'critical',
          results: healthResults,
          summary: {
            total: providers.length,
            healthy: healthyCount,
            unhealthy: healthResults.filter(r => r.status === 'unhealthy' || r.status === 'error').length,
            skipped: healthResults.filter(r => r.status === 'skipped').length
          },
          timestamp: new Date().toISOString()
        });

      } catch (error) {
        console.error('健康检测错误:', error);
        sendJSON(res, {
          success: false,
          error: '健康检测失败',
          details: error.message
        }, 500);
      }
    });
    return;
  }

  // 获取AI提供商状态统计API
  if (path === '/api/admin/deidentification/provider-stats' && method === 'GET') {
    try {
      console.log('📊 获取AI提供商状态统计');

      const stats = aiProviderManager.getProviderStats();
      const availableCount = stats.filter(p => p.status === 'active').length;
      const totalCount = stats.length;

      sendJSON(res, {
        success: true,
        providers: stats,
        summary: {
          total: totalCount,
          available: availableCount,
          unavailable: totalCount - availableCount,
          healthPercentage: Math.round((availableCount / totalCount) * 100)
        },
        failoverConfig: {
          failureThreshold: aiProviderManager.failureThreshold,
          recoveryTime: aiProviderManager.recoveryTime / 1000 / 60, // 转换为分钟
          maxRetries: aiProviderManager.maxRetries
        },
        timestamp: new Date().toISOString()
      });

    } catch (error) {
      console.error('获取提供商统计错误:', error);
      sendJSON(res, {
        success: false,
        error: '获取提供商统计失败',
        details: error.message
      }, 500);
    }
    return;
  }

  // 立即执行健康检测API
  if (path === '/api/admin/deidentification/health-check-now' && method === 'POST') {
    try {
      console.log('🔍 手动触发AI脱敏健康检测');

      // 立即执行健康检测
      await performAutoHealthCheck();

      sendJSON(res, {
        success: true,
        message: '健康检测已执行，请查看服务器日志获取详细结果',
        timestamp: new Date().toISOString()
      });

    } catch (error) {
      console.error('手动健康检测错误:', error);
      sendJSON(res, {
        success: false,
        error: '手动健康检测失败',
        details: error.message
      }, 500);
    }
    return;
  }

  // 用户管理API - 获取用户列表
  if (path === '/api/admin/users' && method === 'GET') {
    try {
      console.log('🔍 获取用户列表请求');

      const { role, status, search, page = 1, pageSize = 10 } = parsedUrl.query;

      // 构建查询条件
      const where = {};

      // 角色过滤
      if (role && role !== 'all') {
        where.role = role;
      }

      // 状态过滤 (基于emailVerified字段)
      if (status === 'active') {
        where.emailVerified = true;
      } else if (status === 'inactive') {
        where.emailVerified = false;
      }

      // 搜索过滤
      if (search) {
        where.OR = [
          { username: { contains: search } },
          { email: { contains: search } },
          { name: { contains: search } }
        ];
      }

      // 获取总数
      const total = await prisma.user.count({ where });

      // 分页查询
      const pageNum = parseInt(page);
      const pageSizeNum = parseInt(pageSize);
      const skip = (pageNum - 1) * pageSizeNum;

      const users = await prisma.user.findMany({
        where,
        skip,
        take: pageSizeNum,
        orderBy: { createdAt: 'desc' }
      });

      // 格式化用户数据
      const formattedUsers = users.map(user => ({
        id: user.id.toString(),
        username: user.username || user.name || `用户${user.id}`,
        email: user.email,
        name: user.name,
        role: user.role || 'user',
        status: user.emailVerified ? 'active' : 'inactive',
        created_at: user.createdAt?.toISOString(),
        updated_at: user.updatedAt?.toISOString(),
        lastLoginAt: user.lastLoginAt?.toISOString(),
        submissionCount: 0 // 暂时设为0，后续可以添加统计
      }));

      console.log('📊 返回用户数据:', formattedUsers.length, '条记录，总计:', total);

      sendJSON(res, {
        success: true,
        data: {
          users: formattedUsers,
          pagination: {
            page: pageNum,
            pageSize: pageSizeNum,
            total: total,
            totalPages: Math.ceil(total / pageSizeNum)
          }
        }
      });

    } catch (error) {
      console.error('❌ 获取用户列表失败:', error);
      sendJSON(res, {
        success: false,
        error: '获取用户列表失败',
        details: error.message
      }, 500);
    }
    return;
  }

  // 用户管理API - 创建用户
  if (path === '/api/admin/users' && method === 'POST') {
    let body = '';
    req.on('data', chunk => {
      body += chunk.toString();
    });
    req.on('end', async () => {
      try {
        const { username, email, password, role = 'user', name } = JSON.parse(body);

        console.log('🆕 创建用户:', { username, email, role });

        // 验证必填字段
        if (!email) {
          sendJSON(res, {
            success: false,
            error: '邮箱为必填项'
          }, 400);
          return;
        }

        // 检查邮箱是否已存在
        const existingUser = await prisma.user.findUnique({
          where: { email }
        });

        if (existingUser) {
          sendJSON(res, {
            success: false,
            error: '邮箱已存在'
          }, 400);
          return;
        }

        // 创建新用户
        const newUser = await prisma.user.create({
          data: {
            email,
            username: username || name,
            name: name || username,
            role,
            emailVerified: true, // 管理员创建的用户默认已验证
            passwordHash: password ? require('crypto').createHash('sha256').update(password).digest('hex') : null
          }
        });

        console.log('✅ 用户创建成功:', newUser.id);

        // 格式化返回数据
        const formattedUser = {
          id: newUser.id.toString(),
          username: newUser.username,
          email: newUser.email,
          name: newUser.name,
          role: newUser.role,
          status: 'active',
          created_at: newUser.createdAt?.toISOString()
        };

        sendJSON(res, {
          success: true,
          data: formattedUser
        }, 201);

      } catch (error) {
        console.error('❌ 创建用户失败:', error);
        sendJSON(res, {
          success: false,
          error: '创建用户失败',
          details: error.message
        }, 500);
      }
    });
    return;
  }

  // 用户管理API - 更新用户
  if (path.startsWith('/api/admin/users/') && method === 'PUT') {
    const userId = path.split('/').pop();
    let body = '';
    req.on('data', chunk => {
      body += chunk.toString();
    });
    req.on('end', async () => {
      try {
        const updateData = JSON.parse(body);
        console.log('🔄 更新用户信息:', userId, updateData);

        // 查找用户
        const user = await prisma.user.findUnique({
          where: { id: parseInt(userId) || 0 }
        });

        if (!user) {
          sendJSON(res, {
            success: false,
            error: '用户不存在'
          }, 404);
          return;
        }

        // 构建更新数据
        const updateFields = {};
        if (updateData.username) updateFields.username = updateData.username;
        if (updateData.name) updateFields.name = updateData.name;
        if (updateData.email) updateFields.email = updateData.email;
        if (updateData.role) updateFields.role = updateData.role;
        if (updateData.status !== undefined) {
          updateFields.emailVerified = updateData.status === 'active';
        }
        if (updateData.password) {
          updateFields.passwordHash = require('crypto').createHash('sha256').update(updateData.password).digest('hex');
        }

        // 更新用户
        const updatedUser = await prisma.user.update({
          where: { id: user.id },
          data: updateFields
        });

        console.log('✅ 用户更新成功:', updatedUser.id);

        // 格式化返回数据
        const formattedUser = {
          id: updatedUser.id.toString(),
          username: updatedUser.username,
          email: updatedUser.email,
          name: updatedUser.name,
          role: updatedUser.role,
          status: updatedUser.emailVerified ? 'active' : 'inactive',
          updated_at: updatedUser.updatedAt?.toISOString()
        };

        sendJSON(res, {
          success: true,
          data: formattedUser
        });

      } catch (error) {
        console.error('❌ 更新用户失败:', error);
        sendJSON(res, {
          success: false,
          error: '更新用户失败',
          details: error.message
        }, 500);
      }
    });
    return;
  }

  // 用户管理API - 删除用户
  if (path.startsWith('/api/admin/users/') && method === 'DELETE') {
    const userId = path.split('/').pop();
    try {
      console.log('🗑️ 删除用户:', userId);

      // 查找用户
      const user = await prisma.user.findUnique({
        where: { id: parseInt(userId) || 0 }
      });

      if (!user) {
        sendJSON(res, {
          success: false,
          error: '用户不存在'
        }, 404);
        return;
      }

      // 删除用户
      await prisma.user.delete({
        where: { id: user.id }
      });

      console.log('✅ 用户删除成功:', user.id);

      sendJSON(res, {
        success: true,
        message: '用户删除成功'
      });

    } catch (error) {
      console.error('❌ 删除用户失败:', error);
      sendJSON(res, {
        success: false,
        error: '删除用户失败',
        details: error.message
      }, 500);
    }
    return;
  }

  // 内容审核API - 获取待审核内容
  if (path === '/api/admin/review/pending' && method === 'GET') {
    try {
      const { page = 1, pageSize = 10, type = 'all' } = parsedUrl.query;

      console.log('📋 获取待审核内容:', { page, pageSize, type });

      // 生成模拟的待审核内容
      const allContents = [];

      // 生成故事类型的内容
      for (let i = 1; i <= 10; i++) {
        allContents.push({
          id: `story-${i}`,
          sequenceNumber: `S${String(i).padStart(5, '0')}`,
          type: 'story',
          title: `待审核故事 ${i}`,
          content: `这是一个待审核的故事内容，描述了毕业生的就业经历和感受。故事 ${i}。`,
          originalContent: JSON.stringify({
            title: `待审核故事 ${i}`,
            content: `这是一个待审核的故事内容，描述了毕业生的就业经历和感受。故事 ${i}。`,
            tags: ['本科', '计算机', '求职经历'],
            isAnonymous: i % 2 === 0
          }),
          status: 'pending',
          flags: i % 4 === 0 ? ['sensitive'] : [],
          createdAt: new Date(Date.now() - (i * 24 * 60 * 60 * 1000)).toISOString(),
          updatedAt: new Date(Date.now() - (i * 24 * 60 * 60 * 1000)).toISOString()
        });
      }

      // 生成问卷心声类型的内容
      for (let i = 1; i <= 8; i++) {
        allContents.push({
          id: `questionnaire-${i}`,
          sequenceNumber: `Q${String(i).padStart(5, '0')}`,
          type: 'questionnaire',
          title: `问卷心声 ${i}`,
          content: `希望能够找到一份符合专业的工作，为社会做出贡献。这是来自问卷第6步的心声内容 ${i}。`,
          originalContent: JSON.stringify({
            adviceForStudents: i % 2 === 0 ? `对学弟学妹的建议：要多实习，积累经验，提前做好职业规划。内容 ${i}` : null,
            observationOnEmployment: i % 3 === 0 ? `当前就业形势比较严峻，需要做好充分准备，保持积极心态。观察 ${i}` : null,
            userId: `user-${i}`
          }),
          status: 'pending',
          flags: i % 5 === 0 ? ['sensitive'] : [],
          createdAt: new Date(Date.now() - (i * 12 * 60 * 60 * 1000)).toISOString(),
          updatedAt: new Date(Date.now() - (i * 12 * 60 * 60 * 1000)).toISOString()
        });
      }

      // 根据类型过滤内容
      let filteredContents = allContents;
      if (type === 'story') {
        filteredContents = allContents.filter(content => content.type === 'story');
      } else if (type === 'questionnaire') {
        filteredContents = allContents.filter(content => content.type === 'questionnaire');
      }

      const pageNum = parseInt(page);
      const pageSizeNum = parseInt(pageSize);
      const startIndex = (pageNum - 1) * pageSizeNum;
      const endIndex = startIndex + pageSizeNum;
      const paginatedContents = filteredContents.slice(startIndex, endIndex);

      sendJSON(res, {
        success: true,
        pendingContents: paginatedContents,
        data: paginatedContents,
        total: filteredContents.length,
        pagination: {
          page: pageNum,
          pageSize: pageSizeNum,
          total: filteredContents.length,
          totalPages: Math.ceil(filteredContents.length / pageSizeNum)
        }
      });

    } catch (error) {
      console.error('❌ 获取待审核内容失败:', error);
      sendJSON(res, {
        success: false,
        error: '获取待审核内容失败',
        details: error.message
      }, 500);
    }
    return;
  }

  // 内容审核API - 审核通过
  if (path.match(/^\/api\/admin\/review\/(.+)\/approve$/) && method === 'POST') {
    const contentId = path.match(/^\/api\/admin\/review\/(.+)\/approve$/)[1];

    try {
      console.log('✅ 审核通过内容:', contentId);

      sendJSON(res, {
        success: true,
        message: '内容审核通过',
        contentId: contentId,
        status: 'approved'
      });

    } catch (error) {
      console.error('❌ 审核通过失败:', error);
      sendJSON(res, {
        success: false,
        error: '审核通过失败',
        details: error.message
      }, 500);
    }
    return;
  }

  // 内容审核API - 审核拒绝
  if (path.match(/^\/api\/admin\/review\/(.+)\/reject$/) && method === 'POST') {
    const contentId = path.match(/^\/api\/admin\/review\/(.+)\/reject$/)[1];

    try {
      console.log('❌ 审核拒绝内容:', contentId);

      sendJSON(res, {
        success: true,
        message: '内容已被拒绝',
        contentId: contentId,
        status: 'rejected'
      });

    } catch (error) {
      console.error('❌ 审核拒绝失败:', error);
      sendJSON(res, {
        success: false,
        error: '审核拒绝失败',
        details: error.message
      }, 500);
    }
    return;
  }

  // 标签管理API - 获取标签列表
  if (path === '/api/admin/tags' && method === 'GET') {
    try {
      console.log('🏷️ 获取标签列表');

      const mockTags = [
        { id: 1, name: "本科", count: 25, category: "education" },
        { id: 2, name: "硕士", count: 18, category: "education" },
        { id: 3, name: "博士", count: 7, category: "education" },
        { id: 4, name: "计算机", count: 20, category: "major" },
        { id: 5, name: "金融", count: 15, category: "major" },
        { id: 6, name: "互联网", count: 22, category: "industry" },
        { id: 7, name: "就业经历", count: 30, category: "content" },
        { id: 8, name: "求职经历", count: 20, category: "content" }
      ];

      sendJSON(res, {
        success: true,
        tags: mockTags
      });

    } catch (error) {
      console.error('❌ 获取标签列表失败:', error);
      sendJSON(res, {
        success: false,
        error: '获取标签列表失败',
        details: error.message
      }, 500);
    }
    return;
  }

  // 管理员登录API
  if (path === '/api/admin/login' && method === 'POST') {
    let body = '';
    req.on('data', chunk => {
      body += chunk.toString();
    });
    req.on('end', async () => {
      try {
        const { username, password } = JSON.parse(body);

        console.log(`🔐 管理员登录尝试: ${username}`);

        // 定义管理员用户
        const adminUsers = [
          {
            username: 'admin1',
            password: 'admin123',
            role: 'admin',
            name: '管理员',
            id: 1,
            permissions: ['content_review', 'user_management', 'data_analysis']
          },
          {
            username: 'reviewer1',
            password: 'admin123',
            role: 'reviewer',
            name: '审核员',
            id: 2,
            permissions: ['content_review']
          },
          {
            username: 'superadmin',
            password: 'admin123',
            role: 'superadmin',
            name: '超级管理员',
            id: 3,
            permissions: ['content_review', 'user_management', 'data_analysis', 'system_config', 'security_management']
          }
        ];

        // 查找匹配的用户
        const user = adminUsers.find(u => u.username === username && u.password === password);

        if (user) {
          // 生成简单的token
          const token = `token_${user.role}_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;

          console.log(`✅ 登录成功: ${user.name} (${user.role})`);

          sendJSON(res, {
            success: true,
            data: {
              token,
              user: {
                id: user.id,
                username: user.username,
                name: user.name,
                role: user.role,
                permissions: user.permissions
              }
            },
            message: '登录成功'
          });
        } else {
          console.log(`❌ 登录失败: 用户名或密码错误`);
          sendJSON(res, {
            success: false,
            error: '用户名或密码错误'
          }, 401);
        }

      } catch (error) {
        console.error('管理员登录错误:', error);
        sendJSON(res, {
          success: false,
          error: '服务器错误'
        }, 500);
      }
    });
    return;
  }

  // 404处理
  sendJSON(res, {
    success: false,
    error: 'API endpoint not found',
    path: path,
    method: method
  }, 404);
}

// AI提供商故障转移和轮询机制
class AIProviderManager {
  constructor() {
    this.providers = [
      {
        id: 'openai',
        name: 'OpenAI GPT',
        apiKey: '********************************************************************************************************************************************************************',
        endpoint: 'api.openai.com',
        model: 'gpt-4',
        priority: 1,
        status: 'active',
        failureCount: 0,
        lastFailure: null,
        avgResponseTime: 500
      },
      {
        id: 'grok',
        name: 'Grok (X.AI)',
        apiKey: '************************************************************************************',
        endpoint: 'api.x.ai',
        model: 'grok-3-latest',
        priority: 2,
        status: 'active',
        failureCount: 0,
        lastFailure: null,
        avgResponseTime: 1000
      }
    ];
    this.currentProviderIndex = 0;
    this.maxRetries = 3;
    this.failureThreshold = 3; // 连续失败3次后标记为不可用
    this.recoveryTime = 300000; // 5分钟后重新尝试不可用的提供商
  }

  // 获取可用的提供商列表（按优先级排序）
  getAvailableProviders() {
    const now = Date.now();
    return this.providers
      .filter(provider => {
        // 如果提供商被标记为不可用，检查是否到了恢复时间
        if (provider.status === 'unavailable' && provider.lastFailure) {
          if (now - provider.lastFailure > this.recoveryTime) {
            provider.status = 'active';
            provider.failureCount = 0;
            console.log(`🔄 ${provider.id} 提供商恢复可用状态`);
          }
        }
        return provider.status === 'active' && provider.apiKey;
      })
      .sort((a, b) => a.priority - b.priority);
  }

  // 记录提供商调用成功
  recordSuccess(providerId, responseTime) {
    const provider = this.providers.find(p => p.id === providerId);
    if (provider) {
      provider.failureCount = 0;
      provider.status = 'active';
      provider.avgResponseTime = Math.round((provider.avgResponseTime + responseTime) / 2);
      console.log(`✅ ${providerId} 调用成功 (${responseTime}ms)`);
    }
  }

  // 记录提供商调用失败
  recordFailure(providerId, error) {
    const provider = this.providers.find(p => p.id === providerId);
    if (provider) {
      provider.failureCount++;
      provider.lastFailure = Date.now();

      if (provider.failureCount >= this.failureThreshold) {
        provider.status = 'unavailable';
        console.log(`❌ ${providerId} 连续失败${provider.failureCount}次，标记为不可用`);
      } else {
        console.log(`⚠️ ${providerId} 失败 (${provider.failureCount}/${this.failureThreshold}): ${error}`);
      }
    }
  }

  // 获取提供商状态统计
  getProviderStats() {
    return this.providers.map(provider => ({
      id: provider.id,
      name: provider.name,
      status: provider.status,
      failureCount: provider.failureCount,
      avgResponseTime: provider.avgResponseTime,
      priority: provider.priority
    }));
  }
}

// 全局AI提供商管理器实例
const aiProviderManager = new AIProviderManager();

// 智能AI脱敏调用函数（支持故障转移）
async function callAIDeidentificationWithFailover(content, filterOptions, preferredProvider = null) {
  const availableProviders = aiProviderManager.getAvailableProviders();

  if (availableProviders.length === 0) {
    throw new Error('没有可用的AI提供商');
  }

  // 如果指定了首选提供商，优先使用
  if (preferredProvider) {
    const preferred = availableProviders.find(p => p.id === preferredProvider);
    if (preferred) {
      availableProviders.unshift(preferred);
      // 移除重复项
      const uniqueProviders = availableProviders.filter((provider, index, self) =>
        index === 0 || self.findIndex(p => p.id === provider.id) === index
      );
      availableProviders.splice(0, availableProviders.length, ...uniqueProviders);
    }
  }

  console.log(`🔄 开始AI脱敏，可用提供商: ${availableProviders.map(p => p.id).join(', ')}`);

  // 依次尝试每个可用的提供商
  for (let i = 0; i < availableProviders.length; i++) {
    const provider = availableProviders[i];

    try {
      console.log(`🤖 尝试使用 ${provider.id} (优先级: ${provider.priority})`);
      const startTime = Date.now();

      const result = await callAIDeidentificationAPI(content, provider.id, provider.apiKey, filterOptions);
      const responseTime = Date.now() - startTime;

      // 记录成功
      aiProviderManager.recordSuccess(provider.id, responseTime);

      return {
        ...result,
        provider: provider.id,
        responseTime: responseTime,
        failoverUsed: i > 0,
        attemptedProviders: availableProviders.slice(0, i + 1).map(p => p.id)
      };

    } catch (error) {
      console.log(`❌ ${provider.id} 失败: ${error.message}`);

      // 记录失败
      aiProviderManager.recordFailure(provider.id, error.message);

      // 如果不是最后一个提供商，继续尝试下一个
      if (i < availableProviders.length - 1) {
        console.log(`🔄 切换到下一个提供商...`);
        continue;
      } else {
        // 所有提供商都失败了
        throw new Error(`所有AI提供商都不可用。最后错误: ${error.message}`);
      }
    }
  }
}

// AI脱敏API调用函数
async function callAIDeidentificationAPI(content, provider, apiKey, filterOptions) {
  const https = require('https');

  let endpoint, headers, requestBody;

  // 构建脱敏提示词
  const deidentificationPrompt = `请对以下文本进行脱敏处理，根据以下规则：
${filterOptions.personalInfo ? '- 替换个人信息（姓名、身份证号等）为***' : ''}
${filterOptions.contactInfo ? '- 替换联系方式（手机号、邮箱等）为***' : ''}
${filterOptions.inappropriateContent ? '- 替换不良信息（违规言论、敏感词汇）为***' : ''}
${filterOptions.sensitiveData ? '- 替换敏感数据（地址、财务信息）为***' : ''}

请直接返回脱敏后的文本，不要添加任何解释。

原文本：
${content}`;

  if (provider === 'grok') {
    endpoint = 'api.x.ai';
    headers = {
      'Authorization': `Bearer ${apiKey}`,
      'Content-Type': 'application/json'
    };
    requestBody = JSON.stringify({
      messages: [
        {
          role: 'system',
          content: '你是一个专业的内容脱敏助手，负责保护用户隐私信息。'
        },
        {
          role: 'user',
          content: deidentificationPrompt
        }
      ],
      model: 'grok-3-latest',
      stream: false,
      temperature: 0.1
    });
  } else if (provider === 'openai') {
    endpoint = 'api.openai.com';
    headers = {
      'Authorization': `Bearer ${apiKey}`,
      'Content-Type': 'application/json'
    };
    requestBody = JSON.stringify({
      model: 'gpt-4',
      messages: [
        {
          role: 'system',
          content: '你是一个专业的内容脱敏助手，负责保护用户隐私信息。'
        },
        {
          role: 'user',
          content: deidentificationPrompt
        }
      ],
      temperature: 0.1,
      max_tokens: 1000
    });
  }

  return new Promise((resolve, reject) => {
    const options = {
      hostname: endpoint,
      port: 443,
      path: '/v1/chat/completions',
      method: 'POST',
      headers: {
        ...headers,
        'Content-Length': Buffer.byteLength(requestBody)
      }
    };

    const req = https.request(options, (res) => {
      let data = '';

      res.on('data', (chunk) => {
        data += chunk;
      });

      res.on('end', () => {
        try {
          console.log(`🔍 ${provider} API响应状态: ${res.statusCode}`);
          console.log(`🔍 ${provider} API响应数据: ${data.substring(0, 500)}...`);

          const response = JSON.parse(data);

          if (res.statusCode === 200 && response.choices && response.choices[0]) {
            const sanitized = response.choices[0].message.content.trim();

            // 分析变更
            const changes = analyzeChanges(content, sanitized);

            console.log(`✅ ${provider} API调用成功`);
            resolve({
              success: true,
              sanitized: sanitized,
              changes: changes,
              provider: provider,
              model: response.model || 'unknown'
            });
          } else {
            const errorMsg = response.error?.message || response.message || `HTTP ${res.statusCode}`;
            console.log(`❌ ${provider} API错误: ${errorMsg}`);
            reject(new Error(`AI API错误: ${errorMsg}`));
          }
        } catch (error) {
          console.log(`❌ ${provider} 解析响应失败: ${error.message}`);
          console.log(`❌ 原始响应数据: ${data}`);
          reject(new Error(`解析AI响应失败: ${error.message}`));
        }
      });
    });

    req.on('error', (error) => {
      console.log(`❌ ${provider} API请求失败: ${error.message}`);
      reject(new Error(`AI API请求失败: ${error.message}`));
    });

    req.on('timeout', () => {
      console.log(`❌ ${provider} API请求超时`);
      req.destroy();
      reject(new Error(`AI API请求超时`));
    });

    // 设置超时时间为30秒
    req.setTimeout(30000);

    console.log(`🔍 ${provider} API请求开始: ${endpoint}${'/v1/chat/completions'}`);
    req.write(requestBody);
    req.end();
  });
}

// 本地脱敏处理函数
function performLocalDeidentification(content, filterOptions) {
  let sanitized = content;
  let changes = [];

  if (filterOptions.personalInfo) {
    // 替换中文姓名（2-4个汉字）
    sanitized = sanitized.replace(/(?:我叫|姓名[是：]?|名字[是：]?)[\s]*([一-龯]{2,4})/g, (match, name) => {
      changes.push({ type: 'name', original: name, sanitized: '***' });
      return match.replace(name, '***');
    });

    // 替换身份证号
    sanitized = sanitized.replace(/\d{15}|\d{18}/g, (match) => {
      changes.push({ type: 'idcard', original: match, sanitized: '***************' });
      return '***************';
    });
  }

  if (filterOptions.contactInfo) {
    // 替换手机号
    sanitized = sanitized.replace(/1[3-9]\d{9}/g, (match) => {
      changes.push({ type: 'phone', original: match, sanitized: '1****5678' });
      return '1****5678';
    });

    // 替换邮箱
    sanitized = sanitized.replace(/\b[\w.-]+@[\w.-]+\.\w+\b/g, (match) => {
      changes.push({ type: 'email', original: match, sanitized: '***@***.com' });
      return '***@***.com';
    });
  }

  if (filterOptions.inappropriateContent) {
    // 替换不良信息关键词
    const badWords = ['违规', '敏感', '不良', '抵制', '反对', '政治', '暴力', '色情'];
    badWords.forEach(word => {
      const regex = new RegExp(word, 'g');
      if (regex.test(sanitized)) {
        changes.push({ type: 'inappropriate', original: word, sanitized: '***' });
        sanitized = sanitized.replace(regex, '***');
      }
    });
  }

  if (filterOptions.sensitiveData) {
    // 替换地址信息
    sanitized = sanitized.replace(/[一-龯]{2,}[省市区县][一-龯]{2,}[路街道巷弄][一-龯\d]{1,}/g, (match) => {
      changes.push({ type: 'address', original: match, sanitized: '***地址***' });
      return '***地址***';
    });

    // 替换金额信息
    sanitized = sanitized.replace(/\d+[万千百十]?元/g, (match) => {
      changes.push({ type: 'money', original: match, sanitized: '***元' });
      return '***元';
    });
  }

  return {
    sanitized: sanitized,
    changes: changes
  };
}

// 分析内容变更
function analyzeChanges(original, sanitized) {
  const changes = [];

  // 简单的差异检测
  if (original !== sanitized) {
    // 这里可以实现更复杂的差异分析
    changes.push({
      type: 'ai_processed',
      original: '原始内容',
      sanitized: '已脱敏',
      description: 'AI处理后的内容'
    });
  }

  return changes;
}

// 自动健康检测功能
let healthCheckInterval = null;

async function performAutoHealthCheck() {
  try {
    console.log('🔍 执行自动AI脱敏健康检测（故障转移模式）...');

    const testContent = '我叫测试用户，手机号是13800138000。';
    const filterOptions = {
      personalInfo: true,
      contactInfo: true,
      inappropriateContent: false,
      sensitiveData: false
    };

    // 测试故障转移机制
    try {
      const result = await callAIDeidentificationWithFailover(testContent, filterOptions);
      console.log(`✅ 故障转移测试成功，使用提供商: ${result.provider} (${result.responseTime}ms)`);
      if (result.failoverUsed) {
        console.log(`🔄 故障转移生效: ${result.attemptedProviders.join(' -> ')}`);
      }
    } catch (error) {
      console.log(`❌ 故障转移测试失败: ${error.message}`);
    }

    // 获取当前提供商状态
    const stats = aiProviderManager.getProviderStats();
    const availableCount = stats.filter(p => p.status === 'active').length;
    const overallHealth = (availableCount / stats.length) * 100;

    console.log(`📊 提供商状态统计:`);
    stats.forEach(provider => {
      console.log(`  - ${provider.id}: ${provider.status} (失败次数: ${provider.failureCount}, 平均响应: ${provider.avgResponseTime}ms)`);
    });

    console.log(`📊 自动健康检测完成: ${overallHealth.toFixed(1)}% (${availableCount}/${stats.length})`);

    // 如果健康度低于80%，发出警告
    if (overallHealth < 80) {
      console.log(`⚠️  警告: AI脱敏系统健康度较低 (${overallHealth.toFixed(1)}%)，故障转移机制已激活`);
    }

  } catch (error) {
    console.error('❌ 自动健康检测失败:', error.message);
  }
}

// 启动自动健康检测
function startAutoHealthCheck() {
  // 每小时执行一次健康检测 (3600000ms = 1小时)
  healthCheckInterval = setInterval(performAutoHealthCheck, 3600000);
  console.log('⏰ 自动健康检测已启动 (每小时执行一次)');

  // 启动后5分钟执行第一次检测
  setTimeout(performAutoHealthCheck, 300000);
  console.log('⏰ 首次健康检测将在5分钟后执行');
}

// 停止自动健康检测
function stopAutoHealthCheck() {
  if (healthCheckInterval) {
    clearInterval(healthCheckInterval);
    healthCheckInterval = null;
    console.log('⏰ 自动健康检测已停止');
  }
}

// 创建服务器
const server = http.createServer(handleRequest);

server.listen(PORT, async () => {
  console.log(`🚀 真实数据库API服务器启动成功！`);
  console.log(`📍 地址: http://localhost:${PORT}`);
  console.log(`🔍 健康检查: http://localhost:${PORT}/health`);
  console.log(`📊 数据源状态: http://localhost:${PORT}/api/data-source/status`);

  // 启动时验证数据源
  try {
    dataSourceStatus = await validateDataSource();
    console.log(`✅ 数据源验证通过 - ${dataSourceStatus.questionnaireRecords}条问卷记录`);
  } catch (error) {
    console.error(`❌ 数据源验证失败: ${error.message}`);
    console.error(`⚠️  服务器将继续运行，但可能无法正常工作`);
  }

  // 启动自动健康检测
  startAutoHealthCheck();

  console.log(`\n按 Ctrl+C 停止服务器`);
});

// 优雅关闭
process.on('SIGINT', async () => {
  console.log('\n🛑 正在关闭服务器...');

  // 停止自动健康检测
  stopAutoHealthCheck();

  await prisma.$disconnect();
  server.close(() => {
    console.log('✅ 服务器已关闭');
    process.exit(0);
  });
});
