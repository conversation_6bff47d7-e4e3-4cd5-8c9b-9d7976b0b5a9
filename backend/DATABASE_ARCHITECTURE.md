# 数据库架构统一方案

## 当前问题分析

### 发现的数据库文件
1. **./prisma/prisma/dev.db** (Prisma当前使用) - 152条问卷 + 50个故事 + 23个用户
2. **./prisma/dev.db** (备份) - 152条问卷 + 50个故事 + 23个用户  
3. **./database.db** (v2版本) - 55条问卷 + 33个故事 + 95个用户
4. **./backups/dev-backup-20250524-213253.db** (备份) - 150条问卷 + 50个故事 + 23个用户
5. **./dev.db** (空文件)

### 数据不一致问题
- 问卷统计API使用Prisma数据库 (152条记录) ✅
- 数据可视化API可能使用不同数据源
- 高级分析API使用混合数据源
- 问卷心声和故事墙可能使用v2数据库

## 统一架构方案

### 1. 数据库命名规范

```
backend/
├── databases/
│   ├── production.db           # 生产数据库
│   ├── development.db          # 开发主数据库 (统一使用)
│   ├── test_data.db           # 测试数据库
│   ├── debug_temp.db          # 临时调试数据库
│   └── backups/
│       ├── dev_backup_YYYYMMDD_HHMMSS.db
│       └── prod_backup_YYYYMMDD_HHMMSS.db
```

### 2. 环境配置

```env
# 开发环境
DATABASE_URL="file:./databases/development.db"

# 测试环境  
DATABASE_URL="file:./databases/test_data.db"

# 生产环境
DATABASE_URL="file:./databases/production.db"
```

### 3. 数据表标识规范

所有表添加数据类型标识字段：
```sql
-- 添加到所有主要表
ALTER TABLE QuestionnaireResponse ADD COLUMN dataType TEXT DEFAULT 'production';
ALTER TABLE Story ADD COLUMN dataType TEXT DEFAULT 'production';
ALTER TABLE User ADD COLUMN dataType TEXT DEFAULT 'production';

-- 数据类型枚举
-- 'production' - 生产数据
-- 'test' - 测试数据  
-- 'debug' - 调试数据
-- 'demo' - 演示数据
```

### 4. 统一API数据源

所有API端点使用同一个数据库：

```javascript
// 统一的数据库连接
const prisma = new PrismaClient({
  datasources: {
    db: {
      url: process.env.DATABASE_URL || "file:./databases/development.db"
    }
  }
});

// 所有API使用相同的Prisma实例
app.get('/api/questionnaire/stats', handler);
app.get('/api/visualization/data', handler);  
app.get('/api/questionnaire-voices', handler);
app.get('/api/story/list', handler);
```

## 迁移计划

### 第一步：备份现有数据
```bash
# 创建备份目录
mkdir -p backend/databases/backups

# 备份所有现有数据库
cp ./prisma/prisma/dev.db ./databases/backups/prisma_dev_$(date +%Y%m%d_%H%M%S).db
cp ./database.db ./databases/backups/database_v2_$(date +%Y%m%d_%H%M%S).db
```

### 第二步：选择主数据库
使用 `./prisma/prisma/dev.db` 作为主数据库（152条问卷数据最完整）

### 第三步：重命名和整理
```bash
# 创建新的数据库目录结构
mkdir -p backend/databases

# 复制主数据库
cp ./prisma/prisma/dev.db ./databases/development.db

# 更新环境变量
echo 'DATABASE_URL="file:./databases/development.db"' > .env.local
```

### 第四步：验证数据一致性
```bash
# 验证所有API使用相同数据源
curl http://localhost:8787/api/questionnaire/stats | jq '.statistics.totalResponses'
curl http://localhost:8787/api/visualization/data | jq '.data.overview.totalResponses'  
curl http://localhost:8787/api/questionnaire-voices | jq '.pagination.total'
```

## 预期结果

✅ **统一数据源**：所有功能使用同一个数据库
✅ **数据一致性**：问卷、可视化、分析显示相同的152条记录
✅ **清晰架构**：明确的数据库文件命名和用途
✅ **易于维护**：统一的备份和迁移策略
