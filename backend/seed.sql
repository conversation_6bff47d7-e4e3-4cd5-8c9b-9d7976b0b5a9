-- 插入用户数据
INSERT INTO users (email, is_verified, created_at) VALUES
('<EMAIL>', TRUE, datetime('now', '-30 days')),
('<EMAIL>', TRUE, datetime('now', '-25 days')),
('<EMAIL>', TRUE, datetime('now', '-20 days')),
('<EMAIL>', TRUE, datetime('now', '-15 days')),
('<EMAIL>', TRUE, datetime('now', '-10 days')),
('<EMAIL>', FALSE, datetime('now', '-5 days')),
('<EMAIL>', FALSE, datetime('now', '-3 days')),
('<EMAIL>', FALSE, datetime('now', '-2 days')),
('<EMAIL>', FALSE, datetime('now', '-1 day')),
('<EMAIL>', FALSE, datetime('now'));

-- 插入问卷回复数据
INSERT INTO questionnaire_responses (
  user_id, is_anonymous, education_level, major, graduation_year, region,
  employment_status, current_industry, current_position, monthly_salary,
  expected_salary_range, job_satisfaction, unemployment_duration,
  career_change_intention, advice_for_students, observation_on_employment,
  created_at, ip_address
) VALUES
(1, FALSE, '本科', '计算机科学', 2023, '华东',
 '全职就业', '互联网/IT', '软件工程师', 15000,
 '10000-20000', '满意', NULL,
 FALSE, '多参加实习，提前积累经验', '就业形势良好，但竞争激烈',
 datetime('now', '-29 days'), '***********'),
 
(2, FALSE, '硕士', '人工智能', 2022, '华北',
 '全职就业', '互联网/IT', '算法工程师', 25000,
 '20000-30000', '非常满意', NULL,
 FALSE, '专注于实际项目经验', '高学历在就业市场更有竞争力',
 datetime('now', '-24 days'), '***********'),
 
(3, TRUE, '本科', '电子工程', 2023, '华南',
 '兼职/自由职业', '制造业', '电子工程师', 8000,
 '10000-15000', '一般', NULL,
 TRUE, '多学习跨领域知识', '传统行业转型压力大',
 datetime('now', '-19 days'), '***********'),
 
(4, FALSE, '专科', '市场营销', 2022, '西南',
 '待业', NULL, NULL, NULL,
 '5000-10000', NULL, 6,
 FALSE, '重视实践能力培养', '就业难度增加',
 datetime('now', '-14 days'), '192.168.1.4'),
 
(5, FALSE, '博士', '生物技术', 2021, '华中',
 '全职就业', '医疗健康', '研究员', 30000,
 '25000-35000', '满意', NULL,
 FALSE, '坚持自己的研究方向', '高精尖人才需求旺盛',
 datetime('now', '-9 days'), '192.168.1.5'),
 
(6, TRUE, '本科', '金融学', 2023, '华东',
 '进修/继续深造', NULL, NULL, NULL,
 '15000-25000', NULL, NULL,
 FALSE, '考虑读研深造', '金融行业竞争激烈',
 datetime('now', '-4 days'), '192.168.1.6'),
 
(7, FALSE, '硕士', '教育学', 2022, '西北',
 '全职就业', '教育', '教师', 10000,
 '8000-15000', '一般', NULL,
 TRUE, '多参加教学实践', '教育行业稳定但薪资偏低',
 datetime('now', '-2 days'), '192.168.1.7'),
 
(8, TRUE, '本科', '机械工程', 2023, '东北',
 '待业', NULL, NULL, NULL,
 '8000-12000', NULL, 3,
 FALSE, '多学习新兴技术', '传统工程专业需要转型',
 datetime('now', '-1 day'), '192.168.1.8'),
 
(9, FALSE, '专科', '护理学', 2022, '华南',
 '全职就业', '医疗健康', '护士', 12000,
 '10000-15000', '满意', NULL,
 FALSE, '专业技能很重要', '医疗行业人才缺口大',
 datetime('now', '-12 hours'), '***********'),
 
(10, TRUE, '本科', '环境工程', 2023, '华北',
 '创业', '环保', '创始人', 5000,
 '15000-25000', '一般', NULL,
 FALSE, '关注社会需求', '环保行业前景广阔但起步难',
 datetime('now'), '***********0');

-- 插入故事数据
INSERT INTO stories (
  user_id, is_anonymous, title, content, status,
  likes, dislikes, created_at, ip_address, category,
  education_level, industry
) VALUES
(1, FALSE, '从校园到职场：我的软件工程师之路',
 '毕业后，我很幸运地进入了一家知名互联网公司。刚开始工作时，我发现学校学的知识与实际工作有很大差距。我花了三个月时间适应，通过不断学习和请教同事，逐渐掌握了工作所需的技能。现在工作一年了，薪资涨了30%，工作也越来越得心应手。建议在校生多参与实际项目，提前了解行业动态。',
 'approved', 45, 2, datetime('now', '-28 days'), '***********', '成功经验',
 '本科', '互联网/IT'),
 
(2, FALSE, '硕士毕业后的职业选择',
 '作为人工智能专业的硕士毕业生，我在毕业前就收到了多家公司的offer。最终我选择了一家AI创业公司，虽然薪资比大厂低，但能接触到最前沿的技术和项目。一年后，我的技术能力得到了极大提升，现在已经成为团队的核心成员。我认为对于刚毕业的学生，选择能够快速成长的平台比起高薪更重要。',
 'approved', 38, 5, datetime('now', '-23 days'), '***********', '职业发展',
 '硕士', '互联网/IT'),
 
(3, TRUE, '电子工程专业的就业困境',
 '作为电子工程专业的毕业生，我发现传统制造业的就业机会在减少，薪资也不如互联网行业。毕业后我在一家电子厂工作了半年，工作内容与所学相关但很基础，薪资也不高。现在我正在自学编程，希望能转行到软件开发领域。建议学弟学妹们在校期间多关注行业发展趋势，提前规划职业方向。',
 'approved', 25, 3, datetime('now', '-18 days'), '***********', '行业洞察',
 '本科', '制造业'),
 
(4, FALSE, '找工作的艰难历程',
 '作为一名专科市场营销毕业生，我在找工作时遇到了很多困难。很多公司更倾向于招聘本科及以上学历的应聘者。我投了近百份简历，参加了十几次面试，最终还是没能找到理想的工作。现在我正在考虑是否要继续深造或转行。希望学校能够加强与企业的合作，提供更多实习和就业机会。',
 'approved', 42, 1, datetime('now', '-13 days'), '192.168.1.4', '求职经历',
 '专科', NULL),
 
(5, FALSE, '博士毕业生在生物技术行业的发展',
 '作为生物技术专业的博士，我毕业后加入了一家医药研发公司。我发现高学历在这个行业确实很有优势，能够参与到前沿的研究项目中。但同时，实验室研究和产业化之间存在很大差距，需要不断学习新知识。薪资待遇不错，但工作压力也很大。建议有志于此行业的学生，要做好长期学习和研究的准备。',
 'approved', 30, 2, datetime('now', '-8 days'), '192.168.1.5', '行业洞察',
 '博士', '医疗健康'),
 
(6, TRUE, '金融专业毕业生的迷茫',
 '金融专业毕业后，我发现行业竞争异常激烈，很多岗位要求有相关实习经验或者名校背景。在投递了几十份简历后，我只收到了几家小公司的面试邀请，薪资远低于预期。经过深思熟虑，我决定先考研提升学历，同时积累一些实习经验。希望学弟学妹们能够提前规划，多参加实习，建立人脉网络。',
 'approved', 28, 4, datetime('now', '-3 days'), '192.168.1.6', '求职经历',
 '本科', '金融'),
 
(7, FALSE, '教育行业的就业现状',
 '作为教育学硕士，我毕业后在一所高中任教。教师工作稳定，但工作强度大，薪资相对较低。不过，看到学生们的进步和成长，我感到很有成就感。教育行业需要热情和耐心，不适合以高薪为目标的毕业生。建议有志于教育事业的学生，要做好心理准备，同时可以考虑教育科技等新兴方向。',
 'approved', 35, 0, datetime('now', '-1 day'), '192.168.1.7', '行业洞察',
 '硕士', '教育'),
 
(8, TRUE, '机械工程专业的就业挑战',
 '机械工程曾经是热门专业，但随着自动化和人工智能的发展，传统机械岗位在减少。毕业三个月了，我仍然没有找到合适的工作。很多企业更青睐复合型人才，既懂机械又懂编程或自动化。我正在学习编程和CAD设计，希望能提升竞争力。建议学弟学妹们要关注行业发展趋势，培养多元化技能。',
 'pending', 0, 0, datetime('now', '-12 hours'), '192.168.1.8', '求职经历',
 '本科', '制造业'),
 
(9, FALSE, '护理专业的就业优势',
 '作为护理专业毕业生，我很快就找到了工作。医疗行业人才需求大，尤其是护理人员。工作虽然辛苦，但薪资待遇不错，而且很有职业发展空间。我现在在一家三甲医院工作，计划两年后考取护师资格，进一步提升职业水平。建议护理专业的学生要重视实践能力的培养，多参加实习和志愿服务。',
 'pending', 0, 0, datetime('now', '-6 hours'), '***********', '成功经验',
 '专科', '医疗健康'),
 
(10, TRUE, '环保创业的挑战与机遇',
 '环境工程专业毕业后，我和几个同学一起创办了一家环保科技公司。创业初期非常艰难，我们面临资金短缺、客户开发难等问题。但随着国家对环保的重视，我们的业务逐渐有了起色。创业需要勇气和毅力，也需要专业知识和商业头脑。建议有创业想法的同学，可以先在相关企业积累经验，了解行业和市场需求。',
 'pending', 0, 0, datetime('now'), '***********0', '创业经历',
 '本科', '环保');

-- 插入标签数据
INSERT INTO tags (id, name, color, priority, category, count, created_at, updated_at) VALUES
('tag1', '软件开发', 'blue', 10, '技术', 45, datetime('now', '-30 days'), datetime('now')),
('tag2', '人工智能', 'purple', 9, '技术', 38, datetime('now', '-29 days'), datetime('now')),
('tag3', '求职技巧', 'green', 8, '职场', 67, datetime('now', '-28 days'), datetime('now')),
('tag4', '实习经验', 'cyan', 7, '职场', 52, datetime('now', '-27 days'), datetime('now')),
('tag5', '考研', 'orange', 6, '学习', 28, datetime('now', '-26 days'), datetime('now')),
('tag6', '创业', 'red', 5, '职场', 15, datetime('now', '-25 days'), datetime('now')),
('tag7', '职业规划', 'yellow', 4, '职场', 73, datetime('now', '-24 days'), datetime('now')),
('tag8', '薪资待遇', 'pink', 3, '职场', 81, datetime('now', '-23 days'), datetime('now')),
('tag9', '工作环境', 'indigo', 2, '职场', 47, datetime('now', '-22 days'), datetime('now')),
('tag10', '行业前景', 'teal', 1, '行业', 63, datetime('now', '-21 days'), datetime('now')),
('tag11', '技能提升', 'lime', 1, '学习', 59, datetime('now', '-20 days'), datetime('now')),
('tag12', '校园招聘', 'amber', 1, '求职', 41, datetime('now', '-19 days'), datetime('now')),
('tag13', '社会招聘', 'brown', 1, '求职', 37, datetime('now', '-18 days'), datetime('now')),
('tag14', '面试经验', 'gray', 1, '求职', 55, datetime('now', '-17 days'), datetime('now')),
('tag15', '职场文化', 'blue-gray', 1, '职场', 33, datetime('now', '-16 days'), datetime('now'));

-- 插入故事标签关联数据
INSERT INTO story_tags (story_id, tag_id) VALUES
(1, 'tag1'), (1, 'tag3'), (1, 'tag4'), (1, 'tag7'),
(2, 'tag2'), (2, 'tag7'), (2, 'tag8'), (2, 'tag10'),
(3, 'tag3'), (3, 'tag7'), (3, 'tag10'), (3, 'tag11'),
(4, 'tag3'), (4, 'tag12'), (4, 'tag13'), (4, 'tag14'),
(5, 'tag7'), (5, 'tag8'), (5, 'tag10'), (5, 'tag11'),
(6, 'tag5'), (6, 'tag7'), (6, 'tag12'), (6, 'tag14'),
(7, 'tag7'), (7, 'tag8'), (7, 'tag10'), (7, 'tag15'),
(8, 'tag3'), (8, 'tag7'), (8, 'tag10'), (8, 'tag11'),
(9, 'tag3'), (9, 'tag7'), (9, 'tag8'), (9, 'tag10'),
(10, 'tag6'), (10, 'tag7'), (10, 'tag10'), (10, 'tag11');

-- 插入管理员数据
INSERT INTO admins (username, password_hash, role, created_at) VALUES
('admin', '$2a$10$JEBqK1c5sUuLj8SVrXg3ZO9J8mgVCGb.S7Xu4MGcPwNh.YEr0jKJ2', 'admin', datetime('now', '-30 days')),
('moderator', '$2a$10$8KvTkwJ8Br/BGMpU/TGsIe6ORGQnD6FZE0RJCjpLf8qbIGGRQgyMq', 'moderator', datetime('now', '-15 days'));

-- 插入审计日志数据
INSERT INTO audit_logs (admin_id, action, entity_type, entity_id, details, created_at) VALUES
(1, 'approve', 'story', '1', '{"reason": "符合发布标准"}', datetime('now', '-27 days')),
(1, 'approve', 'story', '2', '{"reason": "内容有价值"}', datetime('now', '-22 days')),
(2, 'approve', 'story', '3', '{"reason": "分享了真实经历"}', datetime('now', '-17 days')),
(1, 'approve', 'story', '4', '{"reason": "内容真实有用"}', datetime('now', '-12 days')),
(2, 'approve', 'story', '5', '{"reason": "行业洞察有价值"}', datetime('now', '-7 days')),
(1, 'approve', 'story', '6', '{"reason": "分享了求职经历"}', datetime('now', '-2 days')),
(2, 'approve', 'story', '7', '{"reason": "教育行业经验分享"}', datetime('now', '-1 day'));

-- 插入筛选器预设数据
INSERT INTO filter_presets (id, name, user_id, filter_data, is_default, created_at, updated_at) VALUES
('preset1', '互联网行业', 1, '{"industry": "互联网/IT", "sortBy": "latest"}', TRUE, datetime('now', '-20 days'), datetime('now')),
('preset2', '高薪职位', 1, '{"minSalary": 15000, "sortBy": "popular"}', FALSE, datetime('now', '-15 days'), datetime('now')),
('preset3', '求职经验', 2, '{"category": "求职经历", "sortBy": "latest"}', TRUE, datetime('now', '-10 days'), datetime('now')),
('preset4', '创业故事', 2, '{"tags": ["tag6"], "sortBy": "popular"}', FALSE, datetime('now', '-5 days'), datetime('now'));
