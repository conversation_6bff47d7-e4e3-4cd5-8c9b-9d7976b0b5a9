import React, { useState, useEffect } from 'react';
import SuperAdminLayout from '@/components/layouts/SuperAdminLayout';
import PermissionGuard from '@/components/security/PermissionGuard';
import SensitiveOperationConfirm, { SENSITIVE_OPERATIONS } from '@/components/security/SensitiveOperationConfirm';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { useToast } from '@/components/ui/use-toast';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import {
  Shield, AlertTriangle, Eye, Search, RefreshCw, Download, Filter,
  Clock, Users, Database, Settings, Activity, TrendingUp, Bell, Lock, Key
} from 'lucide-react';
import {
  auditLogger,
  type AuditLogEntry,
  type SecurityEvent,
  type AuditLogFilter
} from '@/utils/auditLog';
import { permissionManager, permissionUtils } from '@/utils/permissions';

// 审计日志类型定义
interface AuditLog {
  id: string;
  timestamp: string;
  userId: string;
  userName: string;
  action: string;
  resourceType: string;
  resourceId?: string;
  ipAddress: string;
  userAgent: string;
  severity: 'low' | 'medium' | 'high' | 'critical';
  details: any;
  status: 'success' | 'failed' | 'warning';
}

// 安全事件类型定义
interface SecurityEvent {
  id: string;
  type: string;
  severity: 'low' | 'medium' | 'high' | 'critical';
  timestamp: string;
  description: string;
  source: string;
  status: 'open' | 'investigating' | 'resolved' | 'false_positive';
  assignedTo?: string;
  details: any;
}

// 安全统计数据
interface SecurityStats {
  totalEvents: number;
  criticalEvents: number;
  resolvedEvents: number;
  activeThreats: number;
  loginAttempts: number;
  failedLogins: number;
  suspiciousActivities: number;
  blockedIPs: number;
}

/**
 * 安全审计页面
 */
const SecurityAuditPage: React.FC = () => {
  const { toast } = useToast();
  const [isLoading, setIsLoading] = useState(false);
  const [auditLogs, setAuditLogs] = useState<AuditLog[]>([]);
  const [realAuditLogs, setRealAuditLogs] = useState<AuditLogEntry[]>([]);
  const [securityEvents, setSecurityEvents] = useState<SecurityEvent[]>([]);
  const [realSecurityEvents, setRealSecurityEvents] = useState<SecurityEvent[]>([]);
  const [securityStats, setSecurityStats] = useState<SecurityStats>({
    totalEvents: 0,
    criticalEvents: 0,
    resolvedEvents: 0,
    activeThreats: 0,
    loginAttempts: 0,
    failedLogins: 0,
    suspiciousActivities: 0,
    blockedIPs: 0
  });

  // 敏感操作确认状态
  const [showSensitiveConfirm, setShowSensitiveConfirm] = useState(false);
  const [pendingSensitiveOperation, setPendingSensitiveOperation] = useState<any>(null);

  // 筛选状态
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedSeverity, setSelectedSeverity] = useState<string>('all');
  const [selectedTimeRange, setSelectedTimeRange] = useState<string>('24h');
  const [selectedEventType, setSelectedEventType] = useState<string>('all');

  // 加载真实审计数据
  const loadRealAuditData = () => {
    try {
      setIsLoading(true);

      // 获取真实审计日志
      const logs = auditLogger.getLogs({}, 100);
      setRealAuditLogs(logs);

      // 获取真实安全事件
      const events = auditLogger.getSecurityEvents();
      setRealSecurityEvents(events);

      // 计算统计数据
      const stats = calculateSecurityStats(logs, events);
      setSecurityStats(stats);

      toast({
        title: '数据加载成功',
        description: '已加载最新的安全审计数据',
      });
    } catch (error) {
      console.error('加载审计数据失败:', error);
      toast({
        variant: 'destructive',
        title: '加载失败',
        description: '无法加载安全审计数据'
      });
    } finally {
      setIsLoading(false);
    }
  };

  // 计算安全统计数据
  const calculateSecurityStats = (logs: AuditLogEntry[], events: SecurityEvent[]): SecurityStats => {
    const today = new Date().toDateString();

    return {
      totalEvents: events.length,
      criticalEvents: events.filter(e => e.severity === 'critical').length,
      resolvedEvents: events.filter(e => e.resolved).length,
      activeThreats: events.filter(e => !e.resolved && (e.severity === 'high' || e.severity === 'critical')).length,
      loginAttempts: logs.filter(l => l.action === 'login').length,
      failedLogins: logs.filter(l => l.action === 'login' && !l.success).length,
      suspiciousActivities: events.filter(e => e.type === 'suspicious_activity').length,
      blockedIPs: 0 // 这里可以集成实际的IP封禁数据
    };
  };

  // 获取安全统计数据
  const fetchSecurityStats = async () => {
    try {
      setIsLoading(true);
      // 模拟API调用
      const mockStats: SecurityStats = {
        totalEvents: 1247,
        criticalEvents: 23,
        resolvedEvents: 1156,
        activeThreats: 5,
        loginAttempts: 8934,
        failedLogins: 234,
        suspiciousActivities: 67,
        blockedIPs: 12
      };
      setSecurityStats(mockStats);
    } catch (error) {
      console.error('获取安全统计失败:', error);
      toast({
        variant: 'destructive',
        title: '获取安全统计失败',
        description: '无法加载安全统计数据'
      });
    } finally {
      setIsLoading(false);
    }
  };

  // 获取审计日志
  const fetchAuditLogs = async () => {
    try {
      setIsLoading(true);
      // 模拟API调用
      const mockLogs: AuditLog[] = [
        {
          id: '1',
          timestamp: '2024-01-15 14:30:25',
          userId: 'user123',
          userName: '张三',
          action: 'LOGIN',
          resourceType: 'USER',
          ipAddress: '*************',
          userAgent: 'Mozilla/5.0...',
          severity: 'low',
          details: { loginMethod: 'password' },
          status: 'success'
        },
        {
          id: '2',
          timestamp: '2024-01-15 14:25:12',
          userId: 'admin456',
          userName: '李四',
          action: 'DELETE_USER',
          resourceType: 'USER',
          resourceId: 'user789',
          ipAddress: '*************',
          userAgent: 'Mozilla/5.0...',
          severity: 'high',
          details: { deletedUser: '王五' },
          status: 'success'
        }
      ];
      setAuditLogs(mockLogs);
    } catch (error) {
      console.error('获取审计日志失败:', error);
      toast({
        variant: 'destructive',
        title: '获取审计日志失败',
        description: '无法加载审计日志数据'
      });
    } finally {
      setIsLoading(false);
    }
  };

  // 获取安全事件
  const fetchSecurityEvents = async () => {
    try {
      setIsLoading(true);
      // 模拟API调用
      const mockEvents: SecurityEvent[] = [
        {
          id: '1',
          type: 'BRUTE_FORCE_ATTACK',
          severity: 'high',
          timestamp: '2024-01-15 14:20:00',
          description: '检测到来自 ************* 的暴力破解攻击',
          source: 'LOGIN_MONITOR',
          status: 'investigating',
          details: { attempts: 15, targetUser: 'admin' }
        },
        {
          id: '2',
          type: 'SUSPICIOUS_DATA_ACCESS',
          severity: 'medium',
          timestamp: '2024-01-15 13:45:30',
          description: '用户在短时间内访问大量敏感数据',
          source: 'DATA_MONITOR',
          status: 'open',
          details: { userId: 'user123', recordsAccessed: 500 }
        }
      ];
      setSecurityEvents(mockEvents);
    } catch (error) {
      console.error('获取安全事件失败:', error);
      toast({
        variant: 'destructive',
        title: '获取安全事件失败',
        description: '无法加载安全事件数据'
      });
    } finally {
      setIsLoading(false);
    }
  };

  // 处理敏感操作
  const handleSensitiveOperation = async (operationType: string, targetResource?: any) => {
    const operation = SENSITIVE_OPERATIONS[operationType as keyof typeof SENSITIVE_OPERATIONS];
    if (!operation) {
      toast({
        variant: 'destructive',
        title: '操作失败',
        description: '未知的敏感操作类型'
      });
      return;
    }

    setPendingSensitiveOperation({ operation, targetResource });
    setShowSensitiveConfirm(true);
  };

  // 确认敏感操作
  const confirmSensitiveOperation = async (data: any) => {
    try {
      const { operation, targetResource } = pendingSensitiveOperation;

      // 记录敏感操作
      const userId = localStorage.getItem('userId') || 'unknown';
      const userRole = localStorage.getItem('userRole') || 'unknown';

      await auditLogger.log({
        userId,
        userRole,
        action: 'system',
        resource: 'sensitive_operation',
        resourceId: targetResource?.id,
        description: `执行敏感操作: ${operation.title}`,
        level: operation.riskLevel === 'critical' ? 'critical' : 'warn',
        success: true,
        details: {
          operationType: operation.type,
          riskLevel: operation.riskLevel,
          targetResource,
          reason: data.reason,
          hasPassword: !!data.password
        },
        tags: ['sensitive_operation', 'security']
      });

      // 这里执行实际的敏感操作逻辑
      // 例如：删除用户、修改配置等

      toast({
        title: '操作成功',
        description: `${operation.title}已成功执行`,
      });

      // 刷新数据
      loadRealAuditData();

      setShowSensitiveConfirm(false);
      setPendingSensitiveOperation(null);
    } catch (error) {
      console.error('敏感操作失败:', error);
      toast({
        variant: 'destructive',
        title: '操作失败',
        description: error instanceof Error ? error.message : '执行敏感操作时发生错误'
      });
    }
  };

  // 导出审计日志
  const exportAuditLogs = async () => {
    try {
      await handleSensitiveOperation('data_export', {
        id: 'audit_logs',
        name: '审计日志',
        type: 'security_data'
      });

      // 如果敏感操作确认通过，则执行导出
      const csvData = auditLogger.exportLogs({}, 'csv');
      const blob = new Blob([csvData], { type: 'text/csv' });
      const url = URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = `security_audit_${new Date().toISOString().split('T')[0]}.csv`;
      document.body.appendChild(a);
      a.click();
      document.body.removeChild(a);
      URL.revokeObjectURL(url);
    } catch (error) {
      console.error('导出失败:', error);
    }
  };

  // 解决安全事件
  const resolveSecurityEvent = async (eventId: string) => {
    try {
      const userId = localStorage.getItem('userId') || 'unknown';
      await auditLogger.resolveSecurityEvent(eventId, userId);

      // 刷新数据
      loadRealAuditData();

      toast({
        title: '事件已解决',
        description: '安全事件已标记为已解决',
      });
    } catch (error) {
      toast({
        variant: 'destructive',
        title: '操作失败',
        description: '解决安全事件时发生错误'
      });
    }
  };

  // 组件加载时获取数据
  useEffect(() => {
    loadRealAuditData(); // 优先加载真实数据
    fetchSecurityStats();
    fetchAuditLogs();
    fetchSecurityEvents();
  }, []);

  // 获取严重性颜色
  const getSeverityColor = (severity: string) => {
    switch (severity) {
      case 'critical': return 'bg-red-100 text-red-800 border-red-200';
      case 'high': return 'bg-orange-100 text-orange-800 border-orange-200';
      case 'medium': return 'bg-yellow-100 text-yellow-800 border-yellow-200';
      case 'low': return 'bg-green-100 text-green-800 border-green-200';
      default: return 'bg-gray-100 text-gray-800 border-gray-200';
    }
  };

  // 获取状态颜色
  const getStatusColor = (status: string) => {
    switch (status) {
      case 'success': return 'bg-green-100 text-green-800';
      case 'failed': return 'bg-red-100 text-red-800';
      case 'warning': return 'bg-yellow-100 text-yellow-800';
      case 'open': return 'bg-red-100 text-red-800';
      case 'investigating': return 'bg-yellow-100 text-yellow-800';
      case 'resolved': return 'bg-green-100 text-green-800';
      case 'false_positive': return 'bg-gray-100 text-gray-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  return (
    <SuperAdminLayout>
      <PermissionGuard requiredPermissions={['system.audit']} showDetails={true}>
        <div className="container mx-auto py-6">
        <div className="flex justify-between items-center mb-6">
          <div className="flex items-center">
            <Shield className="h-6 w-6 mr-2" />
            <h1 className="text-2xl font-bold">安全审计中心</h1>
            <Badge variant="secondary" className="ml-3">
              实时监控
            </Badge>
          </div>
          <div className="flex items-center space-x-2">
            <Button
              variant="outline"
              onClick={() => {
                loadRealAuditData();
                fetchSecurityStats();
                fetchAuditLogs();
                fetchSecurityEvents();
              }}
              disabled={isLoading}
            >
              {isLoading ? (
                <RefreshCw className="mr-2 h-4 w-4 animate-spin" />
              ) : (
                <RefreshCw className="mr-2 h-4 w-4" />
              )}
              刷新数据
            </Button>
            <Button variant="outline" onClick={exportAuditLogs}>
              <Download className="mr-2 h-4 w-4" />
              导出报告
            </Button>
            <Button
              variant="outline"
              onClick={() => handleSensitiveOperation('security_config')}
            >
              <Lock className="mr-2 h-4 w-4" />
              安全配置
            </Button>
          </div>
        </div>

        {/* 安全统计概览 */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-6">
          <Card>
            <CardContent className="p-4">
              <div className="flex items-center">
                <Activity className="h-8 w-8 text-blue-600 mr-3" />
                <div>
                  <p className="text-sm font-medium text-muted-foreground">总事件数</p>
                  <p className="text-2xl font-bold">{securityStats.totalEvents}</p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-4">
              <div className="flex items-center">
                <AlertTriangle className="h-8 w-8 text-red-600 mr-3" />
                <div>
                  <p className="text-sm font-medium text-muted-foreground">关键事件</p>
                  <p className="text-2xl font-bold">{securityStats.criticalEvents}</p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-4">
              <div className="flex items-center">
                <TrendingUp className="h-8 w-8 text-green-600 mr-3" />
                <div>
                  <p className="text-sm font-medium text-muted-foreground">已解决</p>
                  <p className="text-2xl font-bold">{securityStats.resolvedEvents}</p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-4">
              <div className="flex items-center">
                <Bell className="h-8 w-8 text-orange-600 mr-3" />
                <div>
                  <p className="text-sm font-medium text-muted-foreground">活跃威胁</p>
                  <p className="text-2xl font-bold">{securityStats.activeThreats}</p>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        <Tabs defaultValue="events" className="space-y-4">
          <TabsList>
            <TabsTrigger value="events">
              <AlertTriangle className="mr-2 h-4 w-4" />
              安全事件
            </TabsTrigger>
            <TabsTrigger value="audit">
              <Eye className="mr-2 h-4 w-4" />
              审计日志
            </TabsTrigger>
            <TabsTrigger value="monitoring">
              <Activity className="mr-2 h-4 w-4" />
              实时监控
            </TabsTrigger>
            <TabsTrigger value="reports">
              <Database className="mr-2 h-4 w-4" />
              安全报告
            </TabsTrigger>
          </TabsList>

          {/* 安全事件监控 */}
          <TabsContent value="events">
            <Card>
              <CardHeader>
                <div className="flex justify-between items-center">
                  <div>
                    <CardTitle>安全事件监控</CardTitle>
                    <CardDescription>
                      实时监控和管理安全威胁事件
                    </CardDescription>
                  </div>
                  <div className="flex items-center space-x-2">
                    <Select value={selectedSeverity} onValueChange={setSelectedSeverity}>
                      <SelectTrigger className="w-[120px]">
                        <SelectValue placeholder="严重性" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="all">全部</SelectItem>
                        <SelectItem value="critical">关键</SelectItem>
                        <SelectItem value="high">高</SelectItem>
                        <SelectItem value="medium">中</SelectItem>
                        <SelectItem value="low">低</SelectItem>
                      </SelectContent>
                    </Select>
                    <Select value={selectedTimeRange} onValueChange={setSelectedTimeRange}>
                      <SelectTrigger className="w-[120px]">
                        <SelectValue placeholder="时间范围" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="1h">1小时</SelectItem>
                        <SelectItem value="24h">24小时</SelectItem>
                        <SelectItem value="7d">7天</SelectItem>
                        <SelectItem value="30d">30天</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                </div>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {securityEvents.map((event) => (
                    <div key={event.id} className="border rounded-lg p-4 hover:shadow-md transition-shadow">
                      <div className="flex items-start justify-between">
                        <div className="flex-1">
                          <div className="flex items-center space-x-2 mb-2">
                            <Badge className={getSeverityColor(event.severity)}>
                              {event.severity.toUpperCase()}
                            </Badge>
                            <Badge className={getStatusColor(event.status)}>
                              {event.status.replace('_', ' ').toUpperCase()}
                            </Badge>
                            <span className="text-sm text-muted-foreground">
                              {event.timestamp}
                            </span>
                          </div>
                          <h4 className="font-medium mb-1">{event.type.replace('_', ' ')}</h4>
                          <p className="text-sm text-muted-foreground mb-2">{event.description}</p>
                          <div className="text-xs text-muted-foreground">
                            来源: {event.source} | ID: {event.id}
                          </div>
                        </div>
                        <div className="flex space-x-2">
                          <Button variant="outline" size="sm">
                            <Eye className="h-3 w-3 mr-1" />
                            详情
                          </Button>
                          <Button variant="outline" size="sm">
                            处理
                          </Button>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          {/* 审计日志 */}
          <TabsContent value="audit">
            <Card>
              <CardHeader>
                <div className="flex justify-between items-center">
                  <div>
                    <CardTitle>审计日志</CardTitle>
                    <CardDescription>
                      查看和分析系统审计日志
                    </CardDescription>
                  </div>
                  <div className="flex items-center space-x-2">
                    <div className="relative">
                      <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground h-4 w-4" />
                      <Input
                        placeholder="搜索日志..."
                        value={searchTerm}
                        onChange={(e) => setSearchTerm(e.target.value)}
                        className="pl-10 w-[200px]"
                      />
                    </div>
                    <Select value={selectedEventType} onValueChange={setSelectedEventType}>
                      <SelectTrigger className="w-[120px]">
                        <SelectValue placeholder="事件类型" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="all">全部</SelectItem>
                        <SelectItem value="LOGIN">登录</SelectItem>
                        <SelectItem value="LOGOUT">登出</SelectItem>
                        <SelectItem value="CREATE">创建</SelectItem>
                        <SelectItem value="UPDATE">更新</SelectItem>
                        <SelectItem value="DELETE">删除</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                </div>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  {auditLogs.map((log) => (
                    <div key={log.id} className="border rounded-lg p-3 hover:bg-gray-50 transition-colors">
                      <div className="flex items-center justify-between">
                        <div className="flex-1">
                          <div className="flex items-center space-x-2 mb-1">
                            <Badge className={getSeverityColor(log.severity)}>
                              {log.severity.toUpperCase()}
                            </Badge>
                            <Badge className={getStatusColor(log.status)}>
                              {log.status.toUpperCase()}
                            </Badge>
                            <span className="text-sm font-medium">{log.action}</span>
                            <span className="text-sm text-muted-foreground">
                              {log.resourceType}
                            </span>
                          </div>
                          <div className="flex items-center space-x-4 text-sm text-muted-foreground">
                            <span>用户: {log.userName} ({log.userId})</span>
                            <span>IP: {log.ipAddress}</span>
                            <span>时间: {log.timestamp}</span>
                          </div>
                          {log.resourceId && (
                            <div className="text-xs text-muted-foreground mt-1">
                              资源ID: {log.resourceId}
                            </div>
                          )}
                        </div>
                        <Button variant="ghost" size="sm">
                          <Eye className="h-3 w-3 mr-1" />
                          详情
                        </Button>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="monitoring">
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              {/* 登录监控 */}
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center">
                    <Users className="h-5 w-5 mr-2" />
                    登录监控
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    <div className="grid grid-cols-2 gap-4">
                      <div className="text-center p-3 bg-blue-50 rounded-lg">
                        <p className="text-2xl font-bold text-blue-600">{securityStats.loginAttempts}</p>
                        <p className="text-sm text-muted-foreground">登录尝试</p>
                      </div>
                      <div className="text-center p-3 bg-red-50 rounded-lg">
                        <p className="text-2xl font-bold text-red-600">{securityStats.failedLogins}</p>
                        <p className="text-sm text-muted-foreground">失败登录</p>
                      </div>
                    </div>
                    <Alert>
                      <AlertTriangle className="h-4 w-4" />
                      <AlertTitle>异常检测</AlertTitle>
                      <AlertDescription>
                        检测到 {securityStats.suspiciousActivities} 个可疑登录活动
                      </AlertDescription>
                    </Alert>
                  </div>
                </CardContent>
              </Card>

              {/* 网络监控 */}
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center">
                    <Activity className="h-5 w-5 mr-2" />
                    网络监控
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    <div className="grid grid-cols-2 gap-4">
                      <div className="text-center p-3 bg-orange-50 rounded-lg">
                        <p className="text-2xl font-bold text-orange-600">{securityStats.blockedIPs}</p>
                        <p className="text-sm text-muted-foreground">被阻止IP</p>
                      </div>
                      <div className="text-center p-3 bg-green-50 rounded-lg">
                        <p className="text-2xl font-bold text-green-600">99.8%</p>
                        <p className="text-sm text-muted-foreground">系统可用性</p>
                      </div>
                    </div>
                    <div className="space-y-2">
                      <div className="flex justify-between text-sm">
                        <span>防火墙状态</span>
                        <Badge className="bg-green-100 text-green-800">正常</Badge>
                      </div>
                      <div className="flex justify-between text-sm">
                        <span>入侵检测</span>
                        <Badge className="bg-green-100 text-green-800">活跃</Badge>
                      </div>
                      <div className="flex justify-between text-sm">
                        <span>DDoS防护</span>
                        <Badge className="bg-green-100 text-green-800">启用</Badge>
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>

              {/* 数据访问监控 */}
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center">
                    <Database className="h-5 w-5 mr-2" />
                    数据访问监控
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-3">
                    <div className="flex justify-between items-center p-2 border rounded">
                      <span className="text-sm">敏感数据访问</span>
                      <Badge className="bg-yellow-100 text-yellow-800">监控中</Badge>
                    </div>
                    <div className="flex justify-between items-center p-2 border rounded">
                      <span className="text-sm">批量数据导出</span>
                      <Badge className="bg-green-100 text-green-800">正常</Badge>
                    </div>
                    <div className="flex justify-between items-center p-2 border rounded">
                      <span className="text-sm">异常查询模式</span>
                      <Badge className="bg-red-100 text-red-800">2个警告</Badge>
                    </div>
                  </div>
                </CardContent>
              </Card>

              {/* 系统健康监控 */}
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center">
                    <Settings className="h-5 w-5 mr-2" />
                    系统健康
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-3">
                    <div className="space-y-2">
                      <div className="flex justify-between text-sm">
                        <span>CPU使用率</span>
                        <span>23%</span>
                      </div>
                      <div className="w-full bg-gray-200 rounded-full h-2">
                        <div className="bg-green-600 h-2 rounded-full" style={{ width: '23%' }}></div>
                      </div>
                    </div>
                    <div className="space-y-2">
                      <div className="flex justify-between text-sm">
                        <span>内存使用率</span>
                        <span>67%</span>
                      </div>
                      <div className="w-full bg-gray-200 rounded-full h-2">
                        <div className="bg-yellow-600 h-2 rounded-full" style={{ width: '67%' }}></div>
                      </div>
                    </div>
                    <div className="space-y-2">
                      <div className="flex justify-between text-sm">
                        <span>磁盘使用率</span>
                        <span>45%</span>
                      </div>
                      <div className="w-full bg-gray-200 rounded-full h-2">
                        <div className="bg-green-600 h-2 rounded-full" style={{ width: '45%' }}></div>
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>
          </TabsContent>

          <TabsContent value="reports">
            <div className="space-y-6">
              {/* 报告生成 */}
              <Card>
                <CardHeader>
                  <CardTitle>生成安全报告</CardTitle>
                  <CardDescription>
                    创建定制化的安全分析报告
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <Card className="cursor-pointer hover:shadow-md transition-shadow">
                      <CardContent className="p-4 text-center">
                        <Shield className="h-8 w-8 mx-auto mb-2 text-blue-600" />
                        <h4 className="font-medium mb-1">日常安全报告</h4>
                        <p className="text-sm text-muted-foreground mb-3">过去24小时的安全状况</p>
                        <Button size="sm" className="w-full">生成报告</Button>
                      </CardContent>
                    </Card>

                    <Card className="cursor-pointer hover:shadow-md transition-shadow">
                      <CardContent className="p-4 text-center">
                        <TrendingUp className="h-8 w-8 mx-auto mb-2 text-green-600" />
                        <h4 className="font-medium mb-1">周度安全报告</h4>
                        <p className="text-sm text-muted-foreground mb-3">过去7天的安全趋势</p>
                        <Button size="sm" className="w-full">生成报告</Button>
                      </CardContent>
                    </Card>

                    <Card className="cursor-pointer hover:shadow-md transition-shadow">
                      <CardContent className="p-4 text-center">
                        <AlertTriangle className="h-8 w-8 mx-auto mb-2 text-red-600" />
                        <h4 className="font-medium mb-1">事件分析报告</h4>
                        <p className="text-sm text-muted-foreground mb-3">安全事件详细分析</p>
                        <Button size="sm" className="w-full">生成报告</Button>
                      </CardContent>
                    </Card>
                  </div>
                </CardContent>
              </Card>

              {/* 历史报告 */}
              <Card>
                <CardHeader>
                  <CardTitle>历史报告</CardTitle>
                  <CardDescription>
                    查看和下载历史安全报告
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="space-y-3">
                    {[
                      { id: '1', name: '2024-01-15 日常安全报告', type: '日常报告', date: '2024-01-15', status: '已完成' },
                      { id: '2', name: '2024-01-08 周度安全报告', type: '周度报告', date: '2024-01-08', status: '已完成' },
                      { id: '3', name: '2024-01-10 事件分析报告', type: '事件报告', date: '2024-01-10', status: '已完成' }
                    ].map((report) => (
                      <div key={report.id} className="flex items-center justify-between p-3 border rounded-lg">
                        <div className="flex-1">
                          <h4 className="font-medium">{report.name}</h4>
                          <div className="flex items-center space-x-4 text-sm text-muted-foreground">
                            <span>类型: {report.type}</span>
                            <span>日期: {report.date}</span>
                            <Badge className="bg-green-100 text-green-800">{report.status}</Badge>
                          </div>
                        </div>
                        <div className="flex space-x-2">
                          <Button variant="outline" size="sm">
                            <Eye className="h-3 w-3 mr-1" />
                            查看
                          </Button>
                          <Button variant="outline" size="sm">
                            <Download className="h-3 w-3 mr-1" />
                            下载
                          </Button>
                        </div>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>

              {/* 报告配置 */}
              <Card>
                <CardHeader>
                  <CardTitle>自动报告配置</CardTitle>
                  <CardDescription>
                    配置自动生成的安全报告
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    <div className="flex items-center justify-between p-3 border rounded-lg">
                      <div>
                        <h4 className="font-medium">日常安全报告</h4>
                        <p className="text-sm text-muted-foreground">每日自动生成安全状况报告</p>
                      </div>
                      <div className="flex items-center space-x-2">
                        <Badge className="bg-green-100 text-green-800">已启用</Badge>
                        <Button variant="outline" size="sm">配置</Button>
                      </div>
                    </div>

                    <div className="flex items-center justify-between p-3 border rounded-lg">
                      <div>
                        <h4 className="font-medium">周度趋势报告</h4>
                        <p className="text-sm text-muted-foreground">每周生成安全趋势分析报告</p>
                      </div>
                      <div className="flex items-center space-x-2">
                        <Badge className="bg-green-100 text-green-800">已启用</Badge>
                        <Button variant="outline" size="sm">配置</Button>
                      </div>
                    </div>

                    <div className="flex items-center justify-between p-3 border rounded-lg">
                      <div>
                        <h4 className="font-medium">月度合规报告</h4>
                        <p className="text-sm text-muted-foreground">每月生成合规性检查报告</p>
                      </div>
                      <div className="flex items-center space-x-2">
                        <Badge className="bg-gray-100 text-gray-800">已禁用</Badge>
                        <Button variant="outline" size="sm">配置</Button>
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>
          </TabsContent>
        </Tabs>

        {/* 敏感操作确认对话框 */}
        {showSensitiveConfirm && pendingSensitiveOperation && (
          <SensitiveOperationConfirm
            isOpen={showSensitiveConfirm}
            operation={pendingSensitiveOperation.operation}
            targetResource={pendingSensitiveOperation.targetResource}
            onConfirm={confirmSensitiveOperation}
            onCancel={() => {
              setShowSensitiveConfirm(false);
              setPendingSensitiveOperation(null);
            }}
          />
        )}
      </div>
      </PermissionGuard>
    </SuperAdminLayout>
  );
};

export default SecurityAuditPage;
