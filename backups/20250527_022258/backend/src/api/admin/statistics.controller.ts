import { Context } from 'hono';
import { Env } from '@/types';
import { KVStorageService } from '@/services/kv-storage.service';
import { config } from '@/config';
import { generateMockStatistics } from '@/mock/statistics';

/**
 * 获取统计数据
 */
export const getStatistics = async (c: Context<{ Bindings: Env }>) => {
  try {
    // 获取查询参数
    const educationLevel = c.req.query('educationLevel');
    const graduationYear = c.req.query('graduationYear');
    const region = c.req.query('region');
    const startDate = c.req.query('startDate');
    const endDate = c.req.query('endDate');
    
    // 如果启用了模拟数据，返回模拟统计数据
    if (config.mockData.enabled) {
      const mockStatistics = generateMockStatistics({
        educationLevel,
        graduationYear,
        region,
        startDate,
        endDate,
      });
      
      return c.json({
        success: true,
        statistics: mockStatistics,
      });
    }
    
    // 实际实现：从KV存储中获取问卷回复数据，然后计算统计数据
    const kvStorage = new KVStorageService(c.env);
    
    // 获取所有问卷回复
    const responses = await kvStorage.getAllResponses();
    
    // 筛选数据
    let filteredResponses = [...responses];
    
    if (educationLevel && educationLevel !== 'all') {
      filteredResponses = filteredResponses.filter(response => 
        response.educationLevel === educationLevel
      );
    }
    
    if (graduationYear && graduationYear !== 'all') {
      if (graduationYear === 'earlier') {
        filteredResponses = filteredResponses.filter(response => 
          parseInt(response.graduationYear) < 2019
        );
      } else {
        filteredResponses = filteredResponses.filter(response => 
          response.graduationYear === graduationYear
        );
      }
    }
    
    if (region && region !== 'all') {
      filteredResponses = filteredResponses.filter(response => 
        response.region === region
      );
    }
    
    if (startDate) {
      const startDateTime = new Date(startDate).getTime();
      filteredResponses = filteredResponses.filter(response => 
        new Date(response.createdAt).getTime() >= startDateTime
      );
    }
    
    if (endDate) {
      const endDateTime = new Date(endDate).getTime();
      filteredResponses = filteredResponses.filter(response => 
        new Date(response.createdAt).getTime() <= endDateTime
      );
    }
    
    // 计算统计数据
    const statistics = calculateStatistics(filteredResponses);
    
    return c.json({
      success: true,
      statistics,
    });
  } catch (error) {
    console.error('获取统计数据失败', error);
    return c.json({
      success: false,
      error: '服务器错误，请稍后再试',
    }, 500);
  }
};

/**
 * 导出统计报表
 */
export const exportStatisticsReport = async (c: Context<{ Bindings: Env }>) => {
  try {
    // 获取查询参数
    const format = c.req.query('format') || 'excel';
    const reportType = c.req.query('reportType') || 'overview';
    const educationLevel = c.req.query('educationLevel');
    const graduationYear = c.req.query('graduationYear');
    const region = c.req.query('region');
    const startDate = c.req.query('startDate');
    const endDate = c.req.query('endDate');
    
    // 如果启用了模拟数据，返回模拟下载链接
    if (config.mockData.enabled) {
      // 模拟生成报表并返回下载链接
      return c.json({
        success: true,
        downloadUrl: `https://example.com/reports/statistics_${reportType}_${format}_${Date.now()}.${format}`,
      });
    }
    
    // 实际实现：生成报表并上传到R2存储，然后返回下载链接
    const kvStorage = new KVStorageService(c.env);
    
    // 获取所有问卷回复
    const responses = await kvStorage.getAllResponses();
    
    // 筛选数据
    let filteredResponses = [...responses];
    
    if (educationLevel && educationLevel !== 'all') {
      filteredResponses = filteredResponses.filter(response => 
        response.educationLevel === educationLevel
      );
    }
    
    if (graduationYear && graduationYear !== 'all') {
      if (graduationYear === 'earlier') {
        filteredResponses = filteredResponses.filter(response => 
          parseInt(response.graduationYear) < 2019
        );
      } else {
        filteredResponses = filteredResponses.filter(response => 
          response.graduationYear === graduationYear
        );
      }
    }
    
    if (region && region !== 'all') {
      filteredResponses = filteredResponses.filter(response => 
        response.region === region
      );
    }
    
    if (startDate) {
      const startDateTime = new Date(startDate).getTime();
      filteredResponses = filteredResponses.filter(response => 
        new Date(response.createdAt).getTime() >= startDateTime
      );
    }
    
    if (endDate) {
      const endDateTime = new Date(endDate).getTime();
      filteredResponses = filteredResponses.filter(response => 
        new Date(response.createdAt).getTime() <= endDateTime
      );
    }
    
    // 计算统计数据
    const statistics = calculateStatistics(filteredResponses);
    
    // 生成报表
    // 这里应该根据format和reportType生成不同格式的报表
    // 然后上传到R2存储，并返回下载链接
    
    // 模拟生成报表并返回下载链接
    return c.json({
      success: true,
      downloadUrl: `https://example.com/reports/statistics_${reportType}_${format}_${Date.now()}.${format}`,
    });
  } catch (error) {
    console.error('导出统计报表失败', error);
    return c.json({
      success: false,
      error: '服务器错误，请稍后再试',
    }, 500);
  }
};

/**
 * 计算统计数据
 */
function calculateStatistics(responses: any[]) {
  // 计算总回复数
  const totalResponses = responses.length;
  
  // 计算就业状态分布
  const employmentStatusMap: Record<string, number> = {};
  responses.forEach(response => {
    const status = response.employmentStatus || '未知';
    employmentStatusMap[status] = (employmentStatusMap[status] || 0) + 1;
  });
  
  // 计算已就业、待业中、继续深造的人数
  const employedCount = employmentStatusMap['已就业'] || 0;
  const unemployedCount = employmentStatusMap['待业中'] || 0;
  const furtherEducationCount = employmentStatusMap['继续深造'] || 0;
  
  // 计算学历层次分布
  const educationLevelMap: Record<string, number> = {};
  responses.forEach(response => {
    const level = response.educationLevel || '未知';
    educationLevelMap[level] = (educationLevelMap[level] || 0) + 1;
  });
  
  // 计算行业分布
  const industryMap: Record<string, number> = {};
  responses.forEach(response => {
    if (response.employmentStatus === '已就业') {
      const industry = response.industry || '未知';
      industryMap[industry] = (industryMap[industry] || 0) + 1;
    }
  });
  
  // 计算地区分布
  const regionMap: Record<string, number> = {};
  responses.forEach(response => {
    if (response.employmentStatus === '已就业') {
      const region = response.region || '未知';
      regionMap[region] = (regionMap[region] || 0) + 1;
    }
  });
  
  // 计算薪资范围分布
  const salaryRanges = [
    '5000以下',
    '5000-8000',
    '8000-10000',
    '10000-15000',
    '15000-20000',
    '20000以上',
  ];
  const salaryRangeMap: Record<string, number> = {};
  salaryRanges.forEach(range => {
    salaryRangeMap[range] = 0;
  });
  
  responses.forEach(response => {
    if (response.employmentStatus === '已就业' && response.salary) {
      const salary = parseInt(response.salary);
      let range = '';
      
      if (salary < 5000) range = '5000以下';
      else if (salary < 8000) range = '5000-8000';
      else if (salary < 10000) range = '8000-10000';
      else if (salary < 15000) range = '10000-15000';
      else if (salary < 20000) range = '15000-20000';
      else range = '20000以上';
      
      salaryRangeMap[range] = (salaryRangeMap[range] || 0) + 1;
    }
  });
  
  // 计算平均薪资
  let totalSalary = 0;
  let salaryCount = 0;
  
  responses.forEach(response => {
    if (response.employmentStatus === '已就业' && response.salary) {
      const salary = parseInt(response.salary);
      if (!isNaN(salary)) {
        totalSalary += salary;
        salaryCount++;
      }
    }
  });
  
  const averageSalary = salaryCount > 0 ? Math.round(totalSalary / salaryCount) : 0;
  
  // 计算平均满意度
  let totalSatisfaction = 0;
  let satisfactionCount = 0;
  
  responses.forEach(response => {
    if (response.employmentStatus === '已就业' && response.jobSatisfaction) {
      const satisfaction = parseFloat(response.jobSatisfaction);
      if (!isNaN(satisfaction)) {
        totalSatisfaction += satisfaction;
        satisfactionCount++;
      }
    }
  });
  
  const averageSatisfaction = satisfactionCount > 0 ? totalSatisfaction / satisfactionCount : 0;
  
  // 返回统计数据
  return {
    overview: {
      totalResponses,
      employedCount,
      unemployedCount,
      furtherEducationCount,
      averageSalary,
      averageSatisfaction,
    },
    employmentStatus: {
      labels: Object.keys(employmentStatusMap),
      data: Object.values(employmentStatusMap),
    },
    educationLevel: {
      labels: Object.keys(educationLevelMap),
      data: Object.values(educationLevelMap),
    },
    industryDistribution: {
      labels: Object.keys(industryMap),
      data: Object.values(industryMap),
    },
    regionDistribution: {
      labels: Object.keys(regionMap),
      data: Object.values(regionMap),
    },
    salaryRanges: {
      labels: Object.keys(salaryRangeMap),
      data: Object.values(salaryRangeMap),
    },
    salaryByEducation: {
      labels: Object.keys(educationLevelMap),
      data: Object.keys(educationLevelMap).map(level => {
        const levelResponses = responses.filter(r => 
          r.educationLevel === level && 
          r.employmentStatus === '已就业' && 
          r.salary
        );
        
        if (levelResponses.length === 0) return 0;
        
        const totalSalary = levelResponses.reduce((sum, r) => {
          const salary = parseInt(r.salary);
          return sum + (isNaN(salary) ? 0 : salary);
        }, 0);
        
        return Math.round(totalSalary / levelResponses.length);
      }),
    },
    salaryByIndustry: {
      labels: Object.keys(industryMap),
      data: Object.keys(industryMap).map(industry => {
        const industryResponses = responses.filter(r => 
          r.industry === industry && 
          r.employmentStatus === '已就业' && 
          r.salary
        );
        
        if (industryResponses.length === 0) return 0;
        
        const totalSalary = industryResponses.reduce((sum, r) => {
          const salary = parseInt(r.salary);
          return sum + (isNaN(salary) ? 0 : salary);
        }, 0);
        
        return Math.round(totalSalary / industryResponses.length);
      }),
    },
    satisfactionDistribution: {
      labels: ['1分以下', '1-2分', '2-3分', '3-4分', '4-5分'],
      data: [0, 0, 0, 0, 0],
    },
    satisfactionByIndustry: {
      labels: Object.keys(industryMap),
      data: Object.keys(industryMap).map(industry => {
        const industryResponses = responses.filter(r => 
          r.industry === industry && 
          r.employmentStatus === '已就业' && 
          r.jobSatisfaction
        );
        
        if (industryResponses.length === 0) return 0;
        
        const totalSatisfaction = industryResponses.reduce((sum, r) => {
          const satisfaction = parseFloat(r.jobSatisfaction);
          return sum + (isNaN(satisfaction) ? 0 : satisfaction);
        }, 0);
        
        return parseFloat((totalSatisfaction / industryResponses.length).toFixed(1));
      }),
    },
    trends: {
      timeLabels: ['2018', '2019', '2020', '2021', '2022', '2023'],
      employmentRate: [85, 86, 82, 84, 87, 88],
      averageSalary: [6500, 7000, 7200, 7800, 8500, 9200],
      averageSatisfaction: [3.5, 3.6, 3.5, 3.7, 3.8, 4.0],
    },
  };
}
