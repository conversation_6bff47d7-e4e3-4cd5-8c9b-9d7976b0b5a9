# 📚 CORS问题案例研究

## 案例1: 大学就业调研系统 - access-control-allow-origin: true 错误

### **项目背景**
- **技术栈**: Cloudflare Workers + Hono + React + Cloudflare Pages
- **问题**: 前端完全无法访问API，所有请求返回 "Failed to fetch"
- **影响**: 系统完全不可用，用户看到空白页面

### **问题现象**
```javascript
// 浏览器控制台错误
TypeError: Failed to fetch
    at testBasicConnection (debug-test:182:40)

// 数据监测报告显示
- API端点总数: 6
- 正常API: 0  
- 异常API: 6
- 错误信息: Failed to fetch
```

### **诊断过程**

#### **第1阶段: 表面诊断 (失败)**
```bash
# curl测试成功，误导了诊断方向
curl -H "Origin: https://frontend.pages.dev" \
     "https://api.workers.dev/api/test"
# ✅ 返回200，数据正常
```

#### **第2阶段: 深度分析 (成功)**
```bash
# 检查OPTIONS预检请求
curl -X OPTIONS \
  -H "Origin: https://frontend.pages.dev" \
  -H "Access-Control-Request-Method: GET" \
  "https://api.workers.dev/api/test" \
  -v

# 发现问题: access-control-allow-origin: true
```

### **根本原因**
```typescript
// ❌ 错误配置
app.use('*', cors({
  origin: (origin) => {
    if (allowedDomains.includes(origin)) {
      return true;  // 问题所在！
    }
    return false;
  }
}));
```

### **解决方案**
```typescript
// ✅ 正确配置
app.use('*', cors({
  origin: (origin) => {
    if (!origin) return origin;
    if (allowedDomains.includes(origin)) {
      return origin;  // 返回具体域名
    }
    return false;
  }
}));
```

### **修复时间线**
- **初始尝试**: 2小时 (添加域名到列表，但没解决根本问题)
- **使用工具后**: 5分钟 (精确定位并修复)

### **经验教训**
1. **curl测试不等于浏览器环境测试**
2. **需要检查响应头的具体值，不只是状态码**
3. **框架特定的配置要求很重要**

---

## 案例2: 多域名电商平台 - 子域名匹配问题

### **项目背景**
- **业务**: 多租户电商平台
- **域名策略**: 每个商户一个子域名
- **问题**: 新商户域名无法访问API

### **问题现象**
```javascript
// 新商户域名
https://shop123.platform.com → ❌ CORS错误
https://shop456.platform.com → ❌ CORS错误

// 已配置域名
https://shop001.platform.com → ✅ 正常
```

### **原始配置**
```typescript
// ❌ 硬编码域名列表
const allowedDomains = [
  'https://shop001.platform.com',
  'https://shop002.platform.com',
  // ... 需要手动添加每个新商户
];
```

### **解决方案**
```typescript
// ✅ 使用正则表达式匹配
app.use('*', cors({
  origin: (origin) => {
    if (!origin) return origin;
    
    // 管理后台
    if (origin === 'https://admin.platform.com') {
      return origin;
    }
    
    // 商户子域名 (shop + 数字)
    if (origin.match(/^https:\/\/shop\d+\.platform\.com$/)) {
      return origin;
    }
    
    return false;
  }
}));
```

### **进一步优化**
```typescript
// ✅ 数据库验证 + 缓存
const validShopCache = new Map();

app.use('*', cors({
  origin: async (origin) => {
    if (!origin) return origin;
    
    // 提取商户ID
    const match = origin.match(/^https:\/\/shop(\d+)\.platform\.com$/);
    if (!match) return false;
    
    const shopId = match[1];
    
    // 检查缓存
    if (validShopCache.has(shopId)) {
      return validShopCache.get(shopId) ? origin : false;
    }
    
    // 验证商户是否存在且活跃
    const isValid = await validateShop(shopId);
    validShopCache.set(shopId, isValid);
    
    return isValid ? origin : false;
  }
}));
```

---

## 案例3: 微服务架构 - 服务间通信CORS问题

### **项目背景**
- **架构**: 微服务 + API网关
- **问题**: 服务间调用被CORS阻止
- **场景**: 用户服务调用订单服务API

### **问题分析**
```javascript
// 服务间调用
fetch('https://order-service.workers.dev/api/orders', {
  headers: {
    'Authorization': 'Bearer ' + serviceToken,
    'X-Service-Name': 'user-service'
  }
});
// ❌ CORS错误
```

### **解决方案1: 服务标识**
```typescript
app.use('*', cors({
  origin: (origin) => {
    // 前端域名
    const frontendDomains = ['https://app.platform.com'];
    if (frontendDomains.includes(origin)) return origin;
    
    // 服务间调用 (无origin)
    if (!origin) return origin;
    
    // 其他微服务
    const serviceDomains = [
      'https://user-service.workers.dev',
      'https://order-service.workers.dev',
      'https://payment-service.workers.dev'
    ];
    
    if (serviceDomains.includes(origin)) return origin;
    
    return false;
  }
}));
```

### **解决方案2: 内部网络绕过CORS**
```typescript
// 使用内部服务发现，绕过CORS
const callInternalService = async (serviceName, endpoint, data) => {
  const internalUrl = getInternalServiceUrl(serviceName);
  
  return fetch(`${internalUrl}${endpoint}`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      'X-Internal-Call': 'true',
      'X-Service-Token': getServiceToken()
    },
    body: JSON.stringify(data)
  });
};
```

---

## 案例4: 开发环境配置泄露到生产

### **问题现象**
```typescript
// ❌ 开发时的宽松配置意外部署到生产
app.use('*', cors({
  origin: true,  // 允许所有域名！
  credentials: true
}));
```

### **安全风险**
- 任何网站都能访问API
- 用户凭证可能被恶意网站窃取
- 数据泄露风险

### **解决方案**
```typescript
// ✅ 环境感知的配置
const getCORSConfig = () => {
  const env = process.env.NODE_ENV;
  
  if (env === 'development') {
    return {
      origin: (origin) => {
        // 开发环境只允许localhost
        if (!origin || origin.startsWith('http://localhost:')) {
          return origin;
        }
        return false;
      }
    };
  }
  
  // 生产环境严格配置
  return {
    origin: (origin) => {
      if (!origin) return origin;
      const allowedDomains = ['https://app.platform.com'];
      return allowedDomains.includes(origin) ? origin : false;
    }
  };
};

app.use('*', cors(getCORSConfig()));
```

---

## 案例5: CDN缓存导致的CORS问题

### **问题背景**
- **场景**: 使用CDN加速API响应
- **问题**: CORS头被CDN缓存，新域名无法访问

### **问题现象**
```bash
# 第一次请求 (domain1.com)
curl -H "Origin: https://domain1.com" https://api.platform.com/test
# 返回: access-control-allow-origin: https://domain1.com

# 第二次请求 (domain2.com) 
curl -H "Origin: https://domain2.com" https://api.platform.com/test  
# 错误返回: access-control-allow-origin: https://domain1.com (缓存的值)
```

### **解决方案**
```typescript
// ✅ 设置正确的缓存头
app.use('*', cors({
  origin: (origin) => {
    // ... CORS逻辑
  }
}));

// 添加Vary头，告诉CDN根据Origin缓存不同版本
app.use('*', async (c, next) => {
  await next();
  c.header('Vary', 'Origin');
});
```

---

## 📊 **案例总结**

### **常见问题类型**
1. **配置错误** (60%) - 返回值类型、语法错误
2. **域名管理** (25%) - 新域名未添加、格式错误  
3. **环境配置** (10%) - 开发配置泄露到生产
4. **缓存问题** (5%) - CDN、浏览器缓存

### **修复时间对比**
| 问题类型 | 传统方法 | 使用工具 | 时间节省 |
|---------|---------|---------|---------|
| 配置错误 | 2-4小时 | 5-10分钟 | 95% |
| 域名管理 | 30-60分钟 | 2-5分钟 | 90% |
| 环境配置 | 1-2小时 | 10-15分钟 | 85% |

### **预防措施**
1. **使用配置模板** - 避免语法错误
2. **自动化测试** - 每次部署前检查
3. **环境隔离** - 严格的环境配置管理
4. **监控告警** - 实时检测CORS问题
