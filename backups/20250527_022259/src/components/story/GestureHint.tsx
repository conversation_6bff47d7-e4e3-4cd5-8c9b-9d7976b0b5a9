import { useState, useEffect } from 'react';
import { Card, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { 
  Heart, 
  Share2, 
  X, 
  ChevronRight, 
  ChevronLeft,
  Hand,
  Info
} from 'lucide-react';
import { cn } from '@/lib/utils';
import { motion, AnimatePresence } from 'framer-motion';

interface GestureHintProps {
  show?: boolean;
  onDismiss?: () => void;
  className?: string;
}

export default function GestureHint({ 
  show = true, 
  onDismiss,
  className 
}: GestureHintProps) {
  const [currentStep, setCurrentStep] = useState(0);
  const [isVisible, setIsVisible] = useState(show);

  useEffect(() => {
    setIsVisible(show);
  }, [show]);

  // 手势提示步骤
  const steps = [
    {
      title: '点击查看详情',
      description: '轻触故事卡片查看完整内容',
      icon: <Hand className="h-6 w-6" />,
      gesture: 'tap',
      color: 'blue'
    },
    {
      title: '右滑点赞',
      description: '向右滑动快速点赞故事',
      icon: <Heart className="h-6 w-6" />,
      gesture: 'swipe-right',
      color: 'red'
    },
    {
      title: '左滑分享',
      description: '向左滑动快速分享故事',
      icon: <Share2 className="h-6 w-6" />,
      gesture: 'swipe-left',
      color: 'green'
    }
  ];

  const currentStepData = steps[currentStep];

  const handleNext = () => {
    if (currentStep < steps.length - 1) {
      setCurrentStep(currentStep + 1);
    } else {
      handleDismiss();
    }
  };

  const handlePrev = () => {
    if (currentStep > 0) {
      setCurrentStep(currentStep - 1);
    }
  };

  const handleDismiss = () => {
    setIsVisible(false);
    onDismiss?.();
  };

  // 手势动画组件
  const GestureAnimation = ({ gesture }: { gesture: string }) => {
    switch (gesture) {
      case 'tap':
        return (
          <motion.div
            className="relative w-16 h-16 mx-auto"
            animate={{
              scale: [1, 1.2, 1],
            }}
            transition={{
              duration: 1.5,
              repeat: Infinity,
              ease: "easeInOut"
            }}
          >
            <div className="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center">
              <Hand className="h-8 w-8 text-blue-600" />
            </div>
            <motion.div
              className="absolute inset-0 bg-blue-200 rounded-full"
              animate={{
                scale: [1, 1.5],
                opacity: [0.5, 0]
              }}
              transition={{
                duration: 1.5,
                repeat: Infinity,
                ease: "easeOut"
              }}
            />
          </motion.div>
        );
      
      case 'swipe-right':
        return (
          <motion.div
            className="relative w-24 h-16 mx-auto flex items-center justify-center"
          >
            <motion.div
              className="w-12 h-12 bg-red-100 rounded-full flex items-center justify-center"
              animate={{
                x: [-20, 20, -20]
              }}
              transition={{
                duration: 2,
                repeat: Infinity,
                ease: "easeInOut"
              }}
            >
              <Heart className="h-6 w-6 text-red-600" />
            </motion.div>
            <ChevronRight className="h-8 w-8 text-red-400 absolute right-0" />
          </motion.div>
        );
      
      case 'swipe-left':
        return (
          <motion.div
            className="relative w-24 h-16 mx-auto flex items-center justify-center"
          >
            <motion.div
              className="w-12 h-12 bg-green-100 rounded-full flex items-center justify-center"
              animate={{
                x: [20, -20, 20]
              }}
              transition={{
                duration: 2,
                repeat: Infinity,
                ease: "easeInOut"
              }}
            >
              <Share2 className="h-6 w-6 text-green-600" />
            </motion.div>
            <ChevronLeft className="h-8 w-8 text-green-400 absolute left-0" />
          </motion.div>
        );
      
      default:
        return null;
    }
  };

  if (!isVisible) return null;

  return (
    <AnimatePresence>
      <motion.div
        initial={{ opacity: 0, y: 50 }}
        animate={{ opacity: 1, y: 0 }}
        exit={{ opacity: 0, y: 50 }}
        className={cn(
          "fixed bottom-4 left-4 right-4 z-50 max-w-sm mx-auto",
          className
        )}
      >
        <Card className="shadow-lg border-2 border-blue-200 bg-gradient-to-br from-blue-50 to-white">
          <CardContent className="p-6">
            {/* 关闭按钮 */}
            <div className="flex justify-between items-start mb-4">
              <Badge variant="outline" className="bg-blue-100 text-blue-700 border-blue-300">
                <Info className="h-3 w-3 mr-1" />
                手势提示 {currentStep + 1}/{steps.length}
              </Badge>
              <Button
                variant="ghost"
                size="sm"
                onClick={handleDismiss}
                className="h-6 w-6 p-0 text-gray-400 hover:text-gray-600"
              >
                <X className="h-4 w-4" />
              </Button>
            </div>

            {/* 手势动画 */}
            <div className="mb-6">
              <GestureAnimation gesture={currentStepData.gesture} />
            </div>

            {/* 说明文字 */}
            <div className="text-center mb-6">
              <h3 className="font-medium text-gray-900 mb-2">
                {currentStepData.title}
              </h3>
              <p className="text-sm text-gray-600">
                {currentStepData.description}
              </p>
            </div>

            {/* 导航按钮 */}
            <div className="flex items-center justify-between">
              <Button
                variant="ghost"
                size="sm"
                onClick={handlePrev}
                disabled={currentStep === 0}
                className="flex items-center gap-1"
              >
                <ChevronLeft className="h-4 w-4" />
                上一步
              </Button>

              {/* 步骤指示器 */}
              <div className="flex gap-2">
                {steps.map((_, index) => (
                  <div
                    key={index}
                    className={cn(
                      "w-2 h-2 rounded-full transition-colors",
                      index === currentStep ? "bg-blue-600" : "bg-gray-300"
                    )}
                  />
                ))}
              </div>

              <Button
                size="sm"
                onClick={handleNext}
                className="flex items-center gap-1"
              >
                {currentStep === steps.length - 1 ? '完成' : '下一步'}
                {currentStep < steps.length - 1 && <ChevronRight className="h-4 w-4" />}
              </Button>
            </div>
          </CardContent>
        </Card>
      </motion.div>
    </AnimatePresence>
  );
}
