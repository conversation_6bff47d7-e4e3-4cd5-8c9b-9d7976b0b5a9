import { useState, useEffect } from 'react';
import { 
  Drawer, 
  DrawerClose, 
  Drawer<PERSON>ontent, 
  <PERSON>erD<PERSON><PERSON>, 
  DrawerFooter, 
  <PERSON>er<PERSON>eader, 
  DrawerTitle 
} from '@/components/ui/drawer';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Badge } from '@/components/ui/badge';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { 
  Search, 
  Tag as TagIcon, 
  X, 
  Filter, 
  SlidersHorizontal,
  RotateCcw
} from 'lucide-react';
import { 
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { cn } from '@/lib/utils';
import { motion, AnimatePresence } from 'framer-motion';

interface MobileFilterDrawerProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  tags: Array<{ id: string; label: string; category?: string; color?: string }>;
  selectedTags: string[];
  onTagsChange: (tags: string[]) => void;
  searchQuery: string;
  onSearchChange: (query: string) => void;
  categories: Array<{ id: string; label: string }>;
  selectedCategory: string;
  onCategoryChange: (category: string) => void;
  educationLevels: Array<{ id: string; label: string }>;
  selectedEducationLevel: string;
  onEducationLevelChange: (level: string) => void;
  industries: Array<{ id: string; label: string }>;
  selectedIndustry: string;
  onIndustryChange: (industry: string) => void;
  onClearFilters: () => void;
  onApplyFilters: () => void;
}

export default function MobileFilterDrawer({
  open,
  onOpenChange,
  tags,
  selectedTags,
  onTagsChange,
  searchQuery,
  onSearchChange,
  categories,
  selectedCategory,
  onCategoryChange,
  educationLevels,
  selectedEducationLevel,
  onEducationLevelChange,
  industries,
  selectedIndustry,
  onIndustryChange,
  onClearFilters,
  onApplyFilters
}: MobileFilterDrawerProps) {
  const [activeTab, setActiveTab] = useState('basic');
  const [localSearchQuery, setLocalSearchQuery] = useState(searchQuery);
  const [localSelectedTags, setLocalSelectedTags] = useState<string[]>(selectedTags);
  const [localSelectedCategory, setLocalSelectedCategory] = useState(selectedCategory);
  const [localSelectedEducationLevel, setLocalSelectedEducationLevel] = useState(selectedEducationLevel);
  const [localSelectedIndustry, setLocalSelectedIndustry] = useState(selectedIndustry);
  
  // 监听打开筛选抽屉事件
  useEffect(() => {
    const handleOpenFilterDrawer = () => {
      onOpenChange(true);
    };
    
    document.addEventListener('open-filter-drawer', handleOpenFilterDrawer);
    
    return () => {
      document.removeEventListener('open-filter-drawer', handleOpenFilterDrawer);
    };
  }, [onOpenChange]);
  
  // 当抽屉打开时，初始化本地状态
  useEffect(() => {
    if (open) {
      setLocalSearchQuery(searchQuery);
      setLocalSelectedTags(selectedTags);
      setLocalSelectedCategory(selectedCategory);
      setLocalSelectedEducationLevel(selectedEducationLevel);
      setLocalSelectedIndustry(selectedIndustry);
    }
  }, [open, searchQuery, selectedTags, selectedCategory, selectedEducationLevel, selectedIndustry]);
  
  // 处理标签选择
  const toggleTag = (tagId: string) => {
    setLocalSelectedTags(prev => 
      prev.includes(tagId)
        ? prev.filter(id => id !== tagId)
        : [...prev, tagId]
    );
  };
  
  // 处理应用筛选
  const handleApplyFilters = () => {
    onSearchChange(localSearchQuery);
    onTagsChange(localSelectedTags);
    onCategoryChange(localSelectedCategory);
    onEducationLevelChange(localSelectedEducationLevel);
    onIndustryChange(localSelectedIndustry);
    onApplyFilters();
    onOpenChange(false);
  };
  
  // 处理清除筛选
  const handleClearFilters = () => {
    setLocalSearchQuery('');
    setLocalSelectedTags([]);
    setLocalSelectedCategory('all');
    setLocalSelectedEducationLevel('all');
    setLocalSelectedIndustry('all');
  };
  
  // 检查是否有活跃的筛选条件
  const isFilterActive = 
    localSearchQuery !== '' ||
    localSelectedTags.length > 0 ||
    localSelectedCategory !== 'all' ||
    localSelectedEducationLevel !== 'all' ||
    localSelectedIndustry !== 'all';
  
  // 渲染标签
  const renderTags = () => {
    // 按分类分组标签
    const tagsByCategory: Record<string, typeof tags> = {
      'all': [],
      'job': [],
      'education': [],
      'industry': [],
      'experience': [],
      'other': []
    };
    
    // 分组标签
    tags.forEach(tag => {
      const category = tag.category || 'other';
      if (tagsByCategory[category]) {
        tagsByCategory[category].push(tag);
      } else {
        tagsByCategory.other.push(tag);
      }
      
      // 同时添加到全部分类
      tagsByCategory.all.push(tag);
    });
    
    // 分类标签
    const categoryLabels: Record<string, string> = {
      'all': '全部标签',
      'job': '求职相关',
      'education': '学历相关',
      'industry': '行业相关',
      'experience': '经验相关',
      'other': '其他标签'
    };
    
    return (
      <Tabs defaultValue="all" className="w-full">
        <TabsList className="w-full grid grid-cols-5 mb-4">
          {Object.keys(tagsByCategory).map(category => (
            <TabsTrigger key={category} value={category} className="text-xs">
              {categoryLabels[category]}
            </TabsTrigger>
          ))}
        </TabsList>
        
        {Object.entries(tagsByCategory).map(([category, categoryTags]) => (
          <TabsContent key={category} value={category} className="mt-0">
            <div className="flex flex-wrap gap-2">
              {categoryTags.map(tag => {
                const isSelected = localSelectedTags.includes(tag.id);
                const colorClass = tag.color ? `bg-${tag.color}-50 text-${tag.color}-700 border-${tag.color}-200` : '';
                
                return (
                  <Badge
                    key={tag.id}
                    variant={isSelected ? "default" : "outline"}
                    className={cn(
                      "cursor-pointer transition-all",
                      !isSelected && colorClass
                    )}
                    onClick={() => toggleTag(tag.id)}
                  >
                    {isSelected ? (
                      <X className="h-3 w-3 mr-1" />
                    ) : (
                      <TagIcon className="h-3 w-3 mr-1" />
                    )}
                    {tag.label}
                  </Badge>
                );
              })}
              
              {categoryTags.length === 0 && (
                <p className="text-sm text-gray-500 w-full text-center py-4">
                  该分类下暂无标签
                </p>
              )}
            </div>
          </TabsContent>
        ))}
      </Tabs>
    );
  };
  
  return (
    <Drawer open={open} onOpenChange={onOpenChange}>
      <DrawerContent className="max-h-[85vh]">
        <DrawerHeader>
          <DrawerTitle>筛选故事</DrawerTitle>
          <DrawerDescription>
            设置筛选条件，找到你感兴趣的故事
          </DrawerDescription>
        </DrawerHeader>
        
        <div className="px-4">
          <Tabs value={activeTab} onValueChange={setActiveTab}>
            <TabsList className="grid w-full grid-cols-2 mb-4">
              <TabsTrigger value="basic" className="flex items-center gap-1">
                <Search className="h-4 w-4" />
                基本筛选
              </TabsTrigger>
              <TabsTrigger value="advanced" className="flex items-center gap-1">
                <SlidersHorizontal className="h-4 w-4" />
                高级筛选
              </TabsTrigger>
            </TabsList>
            
            <TabsContent value="basic" className="mt-0 space-y-4">
              {/* 搜索框 */}
              <div className="space-y-2">
                <Label htmlFor="mobile-search">搜索关键词</Label>
                <div className="relative">
                  <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
                  <Input
                    id="mobile-search"
                    placeholder="搜索故事标题和内容..."
                    className="pl-8"
                    value={localSearchQuery}
                    onChange={(e) => setLocalSearchQuery(e.target.value)}
                  />
                </div>
              </div>
              
              {/* 标签选择 */}
              <div className="space-y-2">
                <div className="flex items-center justify-between">
                  <Label>标签</Label>
                  {localSelectedTags.length > 0 && (
                    <Button 
                      variant="ghost" 
                      size="sm" 
                      className="h-6 text-xs"
                      onClick={() => setLocalSelectedTags([])}
                    >
                      清除
                    </Button>
                  )}
                </div>
                
                {/* 已选标签 */}
                {localSelectedTags.length > 0 && (
                  <div className="flex flex-wrap gap-1 mb-2">
                    <AnimatePresence>
                      {localSelectedTags.map(tagId => {
                        const tag = tags.find(t => t.id === tagId);
                        if (!tag) return null;
                        
                        return (
                          <motion.div
                            key={tagId}
                            initial={{ opacity: 0, scale: 0.8 }}
                            animate={{ opacity: 1, scale: 1 }}
                            exit={{ opacity: 0, scale: 0.8 }}
                            transition={{ duration: 0.2 }}
                          >
                            <Badge
                              variant="default"
                              className="cursor-pointer"
                              onClick={() => toggleTag(tagId)}
                            >
                              <X className="h-3 w-3 mr-1" />
                              {tag.label}
                            </Badge>
                          </motion.div>
                        );
                      })}
                    </AnimatePresence>
                  </div>
                )}
                
                {renderTags()}
              </div>
            </TabsContent>
            
            <TabsContent value="advanced" className="mt-0 space-y-4">
              {/* 分类选择 */}
              <div className="space-y-2">
                <Label htmlFor="mobile-category">故事分类</Label>
                <Select
                  value={localSelectedCategory}
                  onValueChange={setLocalSelectedCategory}
                >
                  <SelectTrigger id="mobile-category">
                    <SelectValue placeholder="选择分类" />
                  </SelectTrigger>
                  <SelectContent>
                    {categories.map((category) => (
                      <SelectItem key={category.id} value={category.id}>
                        {category.label}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
              
              {/* 学历选择 */}
              <div className="space-y-2">
                <Label htmlFor="mobile-education">学历背景</Label>
                <Select
                  value={localSelectedEducationLevel}
                  onValueChange={setLocalSelectedEducationLevel}
                >
                  <SelectTrigger id="mobile-education">
                    <SelectValue placeholder="选择学历" />
                  </SelectTrigger>
                  <SelectContent>
                    {educationLevels.map((level) => (
                      <SelectItem key={level.id} value={level.id}>
                        {level.label}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
              
              {/* 行业选择 */}
              <div className="space-y-2">
                <Label htmlFor="mobile-industry">行业领域</Label>
                <Select
                  value={localSelectedIndustry}
                  onValueChange={setLocalSelectedIndustry}
                >
                  <SelectTrigger id="mobile-industry">
                    <SelectValue placeholder="选择行业" />
                  </SelectTrigger>
                  <SelectContent>
                    {industries.map((industry) => (
                      <SelectItem key={industry.id} value={industry.id}>
                        {industry.label}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
            </TabsContent>
          </Tabs>
        </div>
        
        <DrawerFooter className="pt-2">
          <div className="flex items-center justify-between mb-2">
            <Button
              variant="ghost"
              size="sm"
              onClick={handleClearFilters}
              disabled={!isFilterActive}
              className="flex items-center gap-1"
            >
              <RotateCcw className="h-4 w-4" />
              清除筛选
            </Button>
            
            {isFilterActive && (
              <Badge variant="outline" className="bg-blue-50 text-blue-700">
                <Filter className="h-3 w-3 mr-1" />
                已设置筛选条件
              </Badge>
            )}
          </div>
          
          <Button onClick={handleApplyFilters}>应用筛选</Button>
          <DrawerClose asChild>
            <Button variant="outline">取消</Button>
          </DrawerClose>
        </DrawerFooter>
      </DrawerContent>
    </Drawer>
  );
}
