import { Hono } from 'hono';
import { Env } from '@/types';
import * as statisticsController from './statistics.controller';
import { adminAuthMiddleware } from '@/middlewares/auth.middleware';

// 创建路由
const statisticsRouter = new Hono<{ Bindings: Env }>();

// 添加管理员认证中间件
statisticsRouter.use('*', adminAuthMiddleware);

// 获取统计数据
statisticsRouter.get('/', statisticsController.getStatistics);

// 导出统计报表
statisticsRouter.get('/export', statisticsController.exportStatisticsReport);

export default statisticsRouter;
