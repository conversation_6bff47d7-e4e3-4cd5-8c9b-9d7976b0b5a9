/**
 * 模拟统计数据
 */
export const mockStatistics = {
  totalResponses: 1250,
  verifiedResponses: 875,
  anonymousResponses: 375,

  // 教育水平分布
  educationLevels: [
    { level: '本科', count: 750 },
    { level: '硕士', count: 350 },
    { level: '博士', count: 50 },
    { level: '专科', count: 100 }
  ],

  // 地区分布
  regions: [
    { region: '北京', count: 280 },
    { region: '上海', count: 250 },
    { region: '广州', count: 180 },
    { region: '深圳', count: 170 },
    { region: '杭州', count: 120 },
    { region: '成都', count: 90 },
    { region: '武汉', count: 80 },
    { region: '其他', count: 80 }
  ],

  // 就业状态分布
  employmentStatus: [
    { status: '全职', count: 850 },
    { status: '兼职', count: 100 },
    { status: '自由职业', count: 50 },
    { status: '待业', count: 200 },
    { status: '其他', count: 50 }
  ],

  // 行业分布
  industries: [
    { industry: '互联网', count: 350 },
    { industry: '金融', count: 200 },
    { industry: '教育', count: 150 },
    { industry: '医疗健康', count: 120 },
    { industry: '制造业', count: 100 },
    { industry: '咨询', count: 80 },
    { industry: '文化创意', count: 50 },
    { industry: '其他', count: 200 }
  ],

  // 薪资范围分布
  salaryRanges: [
    { range: '5k以下', count: 100 },
    { range: '5k-10k', count: 250 },
    { range: '10k-15k', count: 300 },
    { range: '15k-20k', count: 200 },
    { range: '20k-25k', count: 150 },
    { range: '25k-30k', count: 50 },
    { range: '30k以上', count: 30 }
  ],

  // 转行意向分布
  careerChangeIntention: [
    { name: '有转行意向', count: 375 },
    { name: '无转行意向', count: 875 }
  ],

  // 按日期统计的问卷提交数量
  responsesByDate: [
    { date: '2023-01', count: 50 },
    { date: '2023-02', count: 65 },
    { date: '2023-03', count: 80 },
    { date: '2023-04', count: 95 },
    { date: '2023-05', count: 110 },
    { date: '2023-06', count: 125 },
    { date: '2023-07', count: 140 },
    { date: '2023-08', count: 155 },
    { date: '2023-09', count: 170 },
    { date: '2023-10', count: 185 },
    { date: '2023-11', count: 200 },
    { date: '2023-12', count: 215 }
  ]
};

/**
 * 生成模拟统计数据
 */
export function generateMockStatistics(filters: any = {}) {
  // 基础数据
  const totalResponses = 1250;
  const employedCount = 950;
  const unemployedCount = 200;
  const furtherEducationCount = 100;
  const averageSalary = 9200;
  const averageSatisfaction = 4.0;

  // 就业状态分布
  const employmentStatus = {
    labels: ['已就业', '待业中', '继续深造'],
    data: [employedCount, unemployedCount, furtherEducationCount],
  };

  // 学历层次分布
  const educationLevel = {
    labels: ['高中/中专', '大专', '本科', '硕士', '博士'],
    data: [50, 250, 650, 250, 50],
  };

  // 行业分布
  const industryDistribution = {
    labels: ['IT/互联网', '金融', '教育', '医疗', '制造业', '服务业', '政府/事业单位', '其他'],
    data: [320, 180, 120, 80, 100, 60, 50, 40],
  };

  // 地区分布
  const regionDistribution = {
    labels: ['北京', '上海', '广州', '深圳', '杭州', '南京', '武汉', '成都', '重庆', '西安', '其他'],
    data: [150, 140, 100, 120, 80, 70, 60, 70, 50, 40, 70],
  };

  // 薪资范围分布
  const salaryRanges = {
    labels: ['5000以下', '5000-8000', '8000-10000', '10000-15000', '15000-20000', '20000以上'],
    data: [100, 250, 300, 200, 70, 30],
  };

  // 学历与薪资关联
  const salaryByEducation = {
    labels: ['高中/中专', '大专', '本科', '硕士', '博士'],
    data: [5500, 7000, 9500, 15000, 18500],
  };

  // 行业与薪资关联
  const salaryByIndustry = {
    labels: ['IT/互联网', '金融', '教育', '医疗', '制造业', '服务业', '政府/事业单位', '其他'],
    data: [12000, 11000, 8000, 9000, 7500, 6500, 8500, 7000],
  };

  // 满意度分布
  const satisfactionDistribution = {
    labels: ['1分以下', '1-2分', '2-3分', '3-4分', '4-5分'],
    data: [20, 50, 180, 400, 300],
  };

  // 行业与满意度关联
  const satisfactionByIndustry = {
    labels: ['IT/互联网', '金融', '教育', '医疗', '制造业', '服务业', '政府/事业单位', '其他'],
    data: [4.2, 4.0, 4.1, 3.9, 3.7, 3.6, 3.8, 3.5],
  };

  // 趋势数据
  const trends = {
    timeLabels: ['2018', '2019', '2020', '2021', '2022', '2023'],
    employmentRate: [85, 86, 82, 84, 87, 88],
    averageSalary: [6500, 7000, 7200, 7800, 8500, 9200],
    averageSatisfaction: [3.5, 3.6, 3.5, 3.7, 3.8, 4.0],
  };

  // 根据筛选条件调整数据
  if (filters.educationLevel && filters.educationLevel !== 'all') {
    // 调整学历相关数据
    const educationIndex = educationLevel.labels.indexOf(filters.educationLevel);
    if (educationIndex !== -1) {
      // 调整总数
      const educationCount = educationLevel.data[educationIndex];
      const ratio = educationCount / totalResponses;

      // 调整就业状态
      employmentStatus.data = employmentStatus.data.map(count => Math.round(count * ratio));

      // 调整薪资
      const educationSalary = salaryByEducation.data[educationIndex];
      const salaryRatio = educationSalary / averageSalary;

      // 调整满意度
      const satisfactionAdjustment = (educationIndex - 2) * 0.1;
    }
  }

  if (filters.region && filters.region !== 'all') {
    // 调整地区相关数据
    const regionIndex = regionDistribution.labels.indexOf(filters.region);
    if (regionIndex !== -1) {
      // 调整总数
      const regionCount = regionDistribution.data[regionIndex];
      const ratio = regionCount / totalResponses;

      // 调整就业状态
      employmentStatus.data = employmentStatus.data.map(count => Math.round(count * ratio));
    }
  }

  // 返回统计数据
  return {
    overview: {
      totalResponses,
      employedCount,
      unemployedCount,
      furtherEducationCount,
      averageSalary,
      averageSatisfaction,
    },
    employmentStatus,
    educationLevel,
    industryDistribution,
    regionDistribution,
    salaryRanges,
    salaryByEducation,
    salaryByIndustry,
    satisfactionDistribution,
    satisfactionByIndustry,
    trends,
  };
}
