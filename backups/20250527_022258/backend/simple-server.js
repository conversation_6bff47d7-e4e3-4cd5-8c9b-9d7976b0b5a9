const express = require('express');
const cors = require('cors');
const { PrismaClient } = require('@prisma/client');

const app = express();
const prisma = new PrismaClient();
const PORT = 8787;

// 中间件
app.use(cors({
  origin: [
    'http://localhost:5173',
    'http://localhost:5174',
    'http://localhost:5175',
    'http://localhost:5176'
  ],
  credentials: true
}));
app.use(express.json());

// 健康检查
app.get('/health', (req, res) => {
  res.json({ status: 'ok', environment: 'development' });
});

// 超级管理员仪表盘统计
app.get('/api/admin/dashboard/stats', async (req, res) => {
  console.log('📊 收到仪表盘统计请求');
  try {
    console.log('🔍 开始查询数据库...');

    // 获取今日开始时间
    const today = new Date();
    today.setHours(0, 0, 0, 0);

    // 并行查询各种统计数据
    const [
      userCount,
      storyCount,
      responseCount,
      pendingStories,
      todayUsers,
      todayStories,
      todayResponses
    ] = await Promise.all([
      prisma.user.count(),
      prisma.story.count(),
      prisma.questionnaireResponse.count(),
      prisma.story.count({ where: { status: 'pending' } }),
      prisma.user.count({ where: { createdAt: { gte: today } } }),
      prisma.story.count({ where: { createdAt: { gte: today } } }),
      prisma.questionnaireResponse.count({ where: { createdAt: { gte: today } } })
    ]);

    console.log('📈 查询结果:', {
      userCount,
      storyCount,
      responseCount,
      pendingStories,
      todayUsers,
      todayStories,
      todayResponses
    });

    const result = {
      success: true,
      data: {
        totalUsers: userCount,
        activeUsers: Math.floor(userCount * 0.8), // 假设80%活跃
        totalStories: storyCount,
        pendingStories: pendingStories,
        totalResponses: responseCount,
        todayUsers: todayUsers,
        todayStories: todayStories,
        todayResponses: todayResponses,
        securityAlerts: 0,
        testDataStatus: 'loaded'
      }
    };

    console.log('✅ 返回结果:', JSON.stringify(result, null, 2));
    res.json(result);
  } catch (error) {
    console.error('❌ 获取仪表盘统计失败:', error);
    res.status(500).json({
      success: false,
      message: '获取统计数据失败',
      error: error.message
    });
  }
});

// 测试数据统计
app.get('/api/admin/test-data/stats', async (req, res) => {
  try {
    const [userStats, responseCount, storyCount] = await Promise.all([
      prisma.user.groupBy({
        by: ['role'],
        _count: { id: true }
      }),
      prisma.questionnaireResponse.count(),
      prisma.story.count()
    ]);

    const roleMap = {};
    userStats.forEach(stat => {
      roleMap[stat.role] = stat._count.id;
    });

    res.json({
      success: true,
      data: {
        users: {
          total: userStats.reduce((sum, stat) => sum + stat._count.id, 0),
          normalUsers: roleMap.user || 0,
          usersWithUuid: roleMap.user || 0,
          reviewers: roleMap.reviewer || 0,
          admins: roleMap.admin || 0,
          superAdmins: roleMap.superadmin || 0
        },
        content: {
          questionnaires: responseCount,
          stories: storyCount,
          comments: 0,
          pendingReview: 0,
          approved: 0,
          rejected: 0
        },
        system: {
          databaseSize: '356 KB',
          lastBackup: new Date().toISOString(),
          totalFiles: 0
        },
        lastGenerated: new Date().toISOString()
      }
    });
  } catch (error) {
    console.error('获取测试数据统计失败:', error);
    res.status(500).json({
      success: false,
      message: '获取测试数据统计失败'
    });
  }
});

// 测试数据状态
app.get('/api/admin/test-data/status', async (req, res) => {
  try {
    const userCount = await prisma.user.count();
    res.json({
      success: true,
      data: {
        isLoaded: userCount > 0
      }
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: '检查测试数据状态失败'
    });
  }
});

// 导入测试数据
app.post('/api/admin/test-data/import', async (req, res) => {
  try {
    // 这里可以实现实际的测试数据导入逻辑
    res.json({
      success: true,
      data: {
        imported: 100
      }
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: '导入测试数据失败'
    });
  }
});

// 清除测试数据
app.delete('/api/admin/test-data/clear', async (req, res) => {
  try {
    // 这里可以实现实际的测试数据清除逻辑
    res.json({
      success: true,
      data: {
        deleted: 50
      }
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: '清除测试数据失败'
    });
  }
});

// 404处理
app.use((req, res) => {
  res.status(404).json({
    success: false,
    error: 'API endpoint not found',
    path: req.originalUrl,
    method: req.method
  });
});

// 错误处理
app.use((err, req, res, next) => {
  console.error('服务器错误:', err);
  res.status(500).json({
    success: false,
    error: 'Internal server error',
    message: err.message
  });
});

// 启动服务器
app.listen(PORT, () => {
  console.log(`🚀 简单API服务器运行在 http://localhost:${PORT}`);
  console.log(`📊 健康检查: http://localhost:${PORT}/health`);
  console.log(`📊 仪表盘统计: http://localhost:${PORT}/api/admin/dashboard/stats`);
});

// 优雅关闭
process.on('SIGINT', async () => {
  console.log('\n正在关闭服务器...');
  await prisma.$disconnect();
  process.exit(0);
});

// 处理未捕获的异常
process.on('uncaughtException', async (err) => {
  console.error('未捕获的异常:', err);
  await prisma.$disconnect();
  process.exit(1);
});

process.on('unhandledRejection', async (reason, promise) => {
  console.error('未处理的Promise拒绝:', reason);
  await prisma.$disconnect();
  process.exit(1);
});
