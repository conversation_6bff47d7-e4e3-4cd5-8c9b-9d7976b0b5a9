/**
 * 标签模拟数据
 */

export interface MockTag {
  id: string;
  name: string;
  color: string;
  priority: number;
  category?: string;
  parentId?: string;
  count: number;
  createdAt: string;
  updatedAt: string;
}

/**
 * 模拟标签数据
 */
export const mockTags: MockTag[] = [
  {
    id: 'tag-001',
    name: '软件开发',
    color: 'blue',
    priority: 10,
    category: 'industry',
    count: 42,
    createdAt: new Date(Date.now() - 30 * 86400000).toISOString(),
    updatedAt: new Date(Date.now() - 5 * 86400000).toISOString()
  },
  {
    id: 'tag-002',
    name: '前端开发',
    color: 'blue',
    priority: 8,
    category: 'industry',
    parentId: 'tag-001',
    count: 28,
    createdAt: new Date(Date.now() - 29 * 86400000).toISOString(),
    updatedAt: new Date(Date.now() - 5 * 86400000).toISOString()
  },
  {
    id: 'tag-003',
    name: '后端开发',
    color: 'blue',
    priority: 8,
    category: 'industry',
    parentId: 'tag-001',
    count: 25,
    createdAt: new Date(Date.now() - 29 * 86400000).toISOString(),
    updatedAt: new Date(Date.now() - 5 * 86400000).toISOString()
  },
  {
    id: 'tag-004',
    name: '人工智能',
    color: 'purple',
    priority: 9,
    category: 'industry',
    count: 18,
    createdAt: new Date(Date.now() - 28 * 86400000).toISOString(),
    updatedAt: new Date(Date.now() - 4 * 86400000).toISOString()
  },
  {
    id: 'tag-005',
    name: '机器学习',
    color: 'purple',
    priority: 7,
    category: 'industry',
    parentId: 'tag-004',
    count: 15,
    createdAt: new Date(Date.now() - 27 * 86400000).toISOString(),
    updatedAt: new Date(Date.now() - 4 * 86400000).toISOString()
  },
  {
    id: 'tag-006',
    name: '数据分析',
    color: 'green',
    priority: 8,
    category: 'industry',
    count: 22,
    createdAt: new Date(Date.now() - 26 * 86400000).toISOString(),
    updatedAt: new Date(Date.now() - 3 * 86400000).toISOString()
  },
  {
    id: 'tag-007',
    name: '产品经理',
    color: 'yellow',
    priority: 9,
    category: 'job',
    count: 31,
    createdAt: new Date(Date.now() - 25 * 86400000).toISOString(),
    updatedAt: new Date(Date.now() - 3 * 86400000).toISOString()
  },
  {
    id: 'tag-008',
    name: '用户体验',
    color: 'yellow',
    priority: 7,
    category: 'job',
    parentId: 'tag-007',
    count: 19,
    createdAt: new Date(Date.now() - 24 * 86400000).toISOString(),
    updatedAt: new Date(Date.now() - 3 * 86400000).toISOString()
  },
  {
    id: 'tag-009',
    name: '市场营销',
    color: 'red',
    priority: 8,
    category: 'job',
    count: 24,
    createdAt: new Date(Date.now() - 23 * 86400000).toISOString(),
    updatedAt: new Date(Date.now() - 2 * 86400000).toISOString()
  },
  {
    id: 'tag-010',
    name: '人力资源',
    color: 'pink',
    priority: 7,
    category: 'job',
    count: 17,
    createdAt: new Date(Date.now() - 22 * 86400000).toISOString(),
    updatedAt: new Date(Date.now() - 2 * 86400000).toISOString()
  },
  {
    id: 'tag-011',
    name: '本科',
    color: 'indigo',
    priority: 10,
    category: 'education',
    count: 45,
    createdAt: new Date(Date.now() - 21 * 86400000).toISOString(),
    updatedAt: new Date(Date.now() - 1 * 86400000).toISOString()
  },
  {
    id: 'tag-012',
    name: '硕士',
    color: 'indigo',
    priority: 9,
    category: 'education',
    count: 32,
    createdAt: new Date(Date.now() - 20 * 86400000).toISOString(),
    updatedAt: new Date(Date.now() - 1 * 86400000).toISOString()
  },
  {
    id: 'tag-013',
    name: '博士',
    color: 'indigo',
    priority: 8,
    category: 'education',
    count: 12,
    createdAt: new Date(Date.now() - 19 * 86400000).toISOString(),
    updatedAt: new Date(Date.now() - 1 * 86400000).toISOString()
  },
  {
    id: 'tag-014',
    name: '实习经历',
    color: 'green',
    priority: 9,
    category: 'experience',
    count: 38,
    createdAt: new Date(Date.now() - 18 * 86400000).toISOString(),
    updatedAt: new Date(Date.now()).toISOString()
  },
  {
    id: 'tag-015',
    name: '求职经验',
    color: 'blue',
    priority: 10,
    category: 'experience',
    count: 41,
    createdAt: new Date(Date.now() - 17 * 86400000).toISOString(),
    updatedAt: new Date(Date.now()).toISOString()
  },
  {
    id: 'tag-016',
    name: '职场新人',
    color: 'yellow',
    priority: 8,
    category: 'experience',
    count: 35,
    createdAt: new Date(Date.now() - 16 * 86400000).toISOString(),
    updatedAt: new Date(Date.now()).toISOString()
  },
  {
    id: 'tag-017',
    name: '转行',
    color: 'red',
    priority: 7,
    category: 'experience',
    count: 23,
    createdAt: new Date(Date.now() - 15 * 86400000).toISOString(),
    updatedAt: new Date(Date.now()).toISOString()
  },
  {
    id: 'tag-018',
    name: '创业',
    color: 'purple',
    priority: 8,
    category: 'experience',
    count: 19,
    createdAt: new Date(Date.now() - 14 * 86400000).toISOString(),
    updatedAt: new Date(Date.now()).toISOString()
  },
  {
    id: 'tag-019',
    name: '互联网',
    color: 'blue',
    priority: 10,
    category: 'industry',
    count: 47,
    createdAt: new Date(Date.now() - 13 * 86400000).toISOString(),
    updatedAt: new Date(Date.now()).toISOString()
  },
  {
    id: 'tag-020',
    name: '金融',
    color: 'green',
    priority: 9,
    category: 'industry',
    count: 29,
    createdAt: new Date(Date.now() - 12 * 86400000).toISOString(),
    updatedAt: new Date(Date.now()).toISOString()
  }
];
