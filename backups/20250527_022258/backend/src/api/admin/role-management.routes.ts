/**
 * 角色管理路由
 */

import { Hono } from 'hono';
import { Env } from '../../types';
import { superAdminAuthMiddleware } from '../../middlewares/superAdminAuth.middleware';
import * as roleController from './role-management.controller';

// 创建路由
const app = new Hono<{ Bindings: Env }>();

// 应用超级管理员认证中间件
app.use('*', superAdminAuthMiddleware);

// 角色管理路由
app.get('/', roleController.getRoles);           // 获取角色列表
app.get('/:id', roleController.getRole);         // 获取单个角色详情
app.post('/', roleController.createRole);        // 创建角色
app.put('/:id', roleController.updateRole);      // 更新角色
app.delete('/:id', roleController.deleteRole);   // 删除角色

// 权限管理路由
app.get('/permissions/all', roleController.getAllPermissions); // 获取所有权限

export default app;
