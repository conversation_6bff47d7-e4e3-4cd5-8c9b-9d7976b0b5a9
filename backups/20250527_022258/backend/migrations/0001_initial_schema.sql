-- Cloudflare D1 数据库初始化脚本
-- 创建基础表结构

-- 用户表
CREATE TABLE IF NOT EXISTS users (
    id TEXT PRIMARY KEY,
    username TEXT UNIQUE,
    email TEXT,
    password_hash TEXT,
    role TEXT DEFAULT 'user',
    is_anonymous BOOLEAN DEFAULT FALSE,
    identity_a TEXT,
    identity_b TEXT,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    last_login DATETIME,
    is_active BOOLEAN DEFAULT TRUE
);

-- 问卷回复表
CREATE TABLE IF NOT EXISTS questionnaire_responses (
    id TEXT PRIMARY KEY,
    user_id TEXT,
    sequence_number INTEGER,
    
    -- 基本信息
    education_level TEXT,
    graduation_year INTEGER,
    major TEXT,
    school_name TEXT,
    region TEXT,
    
    -- 就业状态
    employment_status TEXT,
    current_salary INTEGER,
    expected_salary INTEGER,
    job_search_duration INTEGER,
    
    -- 求职经历
    interview_count INTEGER,
    offer_count INTEGER,
    rejection_reasons TEXT,
    
    -- 建议和观察
    advice_for_students TEXT,
    observation_on_employment TEXT,
    
    -- 元数据
    is_anonymous BOOLEAN DEFAULT FALSE,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    
    FOREIGN KEY (user_id) REFERENCES users(id)
);

-- 故事墙表
CREATE TABLE IF NOT EXISTS stories (
    id TEXT PRIMARY KEY,
    user_id TEXT,
    sequence_number INTEGER,
    
    title TEXT NOT NULL,
    content TEXT NOT NULL,
    category TEXT,
    tags TEXT, -- JSON array as text
    
    -- 状态管理
    status TEXT DEFAULT 'pending', -- pending, approved, rejected
    is_anonymous BOOLEAN DEFAULT FALSE,
    
    -- 审核信息
    reviewer_id TEXT,
    reviewed_at DATETIME,
    review_notes TEXT,
    
    -- 统计信息
    view_count INTEGER DEFAULT 0,
    like_count INTEGER DEFAULT 0,
    
    -- 元数据
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    
    FOREIGN KEY (user_id) REFERENCES users(id),
    FOREIGN KEY (reviewer_id) REFERENCES users(id)
);

-- 待审核内容表
CREATE TABLE IF NOT EXISTS pending_content (
    id TEXT PRIMARY KEY,
    sequence_number INTEGER,
    type TEXT NOT NULL, -- 'story' or 'questionnaire'
    
    original_content TEXT NOT NULL, -- JSON string
    sanitized_content TEXT, -- AI处理后的内容
    
    status TEXT DEFAULT 'pending', -- pending, approved, rejected
    
    -- 审核信息
    reviewer_id TEXT,
    reviewed_at DATETIME,
    review_notes TEXT,
    
    -- AI处理信息
    ai_processed BOOLEAN DEFAULT FALSE,
    ai_provider TEXT,
    ai_confidence REAL,
    
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    
    FOREIGN KEY (reviewer_id) REFERENCES users(id)
);

-- 审核日志表
CREATE TABLE IF NOT EXISTS review_logs (
    id TEXT PRIMARY KEY,
    reviewer_id TEXT NOT NULL,
    content_id TEXT NOT NULL,
    action TEXT NOT NULL, -- approve, reject, flag
    
    review_notes TEXT,
    
    -- 审核环境信息
    ip_address TEXT,
    user_agent TEXT,
    
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    
    FOREIGN KEY (reviewer_id) REFERENCES users(id)
);

-- 标签表
CREATE TABLE IF NOT EXISTS tags (
    id TEXT PRIMARY KEY,
    name TEXT UNIQUE NOT NULL,
    category TEXT,
    description TEXT,
    color TEXT,
    is_active BOOLEAN DEFAULT TRUE,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP
);

-- 问卷心声表
CREATE TABLE IF NOT EXISTS questionnaire_voices (
    id TEXT PRIMARY KEY,
    questionnaire_id TEXT NOT NULL,
    content TEXT NOT NULL,
    
    status TEXT DEFAULT 'pending', -- pending, approved, rejected
    is_anonymous BOOLEAN DEFAULT FALSE,
    
    -- 审核信息
    reviewer_id TEXT,
    reviewed_at DATETIME,
    review_notes TEXT,
    
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    
    FOREIGN KEY (questionnaire_id) REFERENCES questionnaire_responses(id),
    FOREIGN KEY (reviewer_id) REFERENCES users(id)
);

-- AI脱敏配置表
CREATE TABLE IF NOT EXISTS ai_deidentification_config (
    id TEXT PRIMARY KEY,
    enabled BOOLEAN DEFAULT TRUE,
    ai_provider TEXT DEFAULT 'openai',
    
    -- 目标内容类型
    target_questionnaire BOOLEAN DEFAULT TRUE,
    target_story_wall BOOLEAN DEFAULT TRUE,
    
    -- 过滤选项
    filter_personal_info BOOLEAN DEFAULT TRUE,
    filter_contact_info BOOLEAN DEFAULT TRUE,
    filter_inappropriate_content BOOLEAN DEFAULT TRUE,
    filter_sensitive_data BOOLEAN DEFAULT TRUE,
    
    -- 自动审核
    auto_review BOOLEAN DEFAULT FALSE,
    review_threshold REAL DEFAULT 0.8,
    
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
);

-- AI脱敏统计表
CREATE TABLE IF NOT EXISTS ai_deidentification_stats (
    id TEXT PRIMARY KEY,
    provider TEXT NOT NULL,
    
    total_processed INTEGER DEFAULT 0,
    success_count INTEGER DEFAULT 0,
    failure_count INTEGER DEFAULT 0,
    
    avg_response_time REAL DEFAULT 0,
    last_used DATETIME,
    
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
);

-- 创建索引
CREATE INDEX IF NOT EXISTS idx_users_username ON users(username);
CREATE INDEX IF NOT EXISTS idx_users_role ON users(role);
CREATE INDEX IF NOT EXISTS idx_users_is_anonymous ON users(is_anonymous);

CREATE INDEX IF NOT EXISTS idx_questionnaire_responses_user_id ON questionnaire_responses(user_id);
CREATE INDEX IF NOT EXISTS idx_questionnaire_responses_education_level ON questionnaire_responses(education_level);
CREATE INDEX IF NOT EXISTS idx_questionnaire_responses_graduation_year ON questionnaire_responses(graduation_year);
CREATE INDEX IF NOT EXISTS idx_questionnaire_responses_created_at ON questionnaire_responses(created_at);

CREATE INDEX IF NOT EXISTS idx_stories_user_id ON stories(user_id);
CREATE INDEX IF NOT EXISTS idx_stories_status ON stories(status);
CREATE INDEX IF NOT EXISTS idx_stories_category ON stories(category);
CREATE INDEX IF NOT EXISTS idx_stories_created_at ON stories(created_at);

CREATE INDEX IF NOT EXISTS idx_pending_content_type ON pending_content(type);
CREATE INDEX IF NOT EXISTS idx_pending_content_status ON pending_content(status);
CREATE INDEX IF NOT EXISTS idx_pending_content_reviewer_id ON pending_content(reviewer_id);
CREATE INDEX IF NOT EXISTS idx_pending_content_created_at ON pending_content(created_at);

CREATE INDEX IF NOT EXISTS idx_review_logs_reviewer_id ON review_logs(reviewer_id);
CREATE INDEX IF NOT EXISTS idx_review_logs_content_id ON review_logs(content_id);
CREATE INDEX IF NOT EXISTS idx_review_logs_created_at ON review_logs(created_at);

CREATE INDEX IF NOT EXISTS idx_questionnaire_voices_questionnaire_id ON questionnaire_voices(questionnaire_id);
CREATE INDEX IF NOT EXISTS idx_questionnaire_voices_status ON questionnaire_voices(status);
CREATE INDEX IF NOT EXISTS idx_questionnaire_voices_created_at ON questionnaire_voices(created_at);

-- 插入默认数据

-- 插入默认管理员用户
INSERT OR IGNORE INTO users (id, username, password_hash, role, is_anonymous) VALUES 
('superadmin-001', 'superadmin', '$2b$10$hash_for_super123', 'superadmin', FALSE),
('admin-001', 'admin', '$2b$10$hash_for_admin123', 'admin', FALSE),
('reviewer-001', 'reviewer', '$2b$10$hash_for_reviewer123', 'reviewer', FALSE);

-- 插入默认标签
INSERT OR IGNORE INTO tags (id, name, category, description, color) VALUES 
('tag-001', '求职经历', 'experience', '分享求职过程中的经历', '#3B82F6'),
('tag-002', '职场感悟', 'insight', '工作中的感悟和思考', '#10B981'),
('tag-003', '行业观察', 'industry', '对特定行业的观察和分析', '#F59E0B'),
('tag-004', '技能提升', 'skill', '技能学习和提升相关', '#8B5CF6'),
('tag-005', '面试技巧', 'interview', '面试相关的经验和技巧', '#EF4444');

-- 插入默认AI脱敏配置
INSERT OR IGNORE INTO ai_deidentification_config (id, enabled, ai_provider) VALUES 
('config-001', TRUE, 'openai');

-- 插入AI提供商统计初始数据
INSERT OR IGNORE INTO ai_deidentification_stats (id, provider) VALUES 
('stats-openai', 'openai'),
('stats-grok', 'grok');
