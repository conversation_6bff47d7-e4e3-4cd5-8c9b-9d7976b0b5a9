/**
 * 错误分析 API
 */

import { Hono } from 'hono';
import { SecurityModule } from '../../security';

// 创建路由
const app = new Hono();

/**
 * 获取错误分析结果
 */
app.get('/analysis', async (c) => {
  try {
    // 获取错误分析结果
    const analysis = await SecurityModule.getErrorAnalysis();
    
    return c.json({
      success: true,
      analysis
    });
  } catch (error) {
    console.error('Error getting error analysis:', error);
    return c.json({
      success: false,
      error: '获取错误分析结果失败'
    }, 500);
  }
});

/**
 * 获取错误聚类详情
 */
app.get('/clusters/:id', async (c) => {
  try {
    const id = c.req.param('id');
    
    // 获取错误分析结果
    const analysis = await SecurityModule.getErrorAnalysis();
    
    // 查找指定 ID 的聚类
    const cluster = analysis.clusters.find((cluster: any) => cluster.id === id);
    
    if (!cluster) {
      return c.json({
        success: false,
        error: '未找到指定的错误聚类'
      }, 404);
    }
    
    return c.json({
      success: true,
      cluster
    });
  } catch (error) {
    console.error('Error getting cluster details:', error);
    return c.json({
      success: false,
      error: '获取错误聚类详情失败'
    }, 500);
  }
});

/**
 * 获取错误模式详情
 */
app.get('/patterns/:id', async (c) => {
  try {
    const id = c.req.param('id');
    
    // 获取错误分析结果
    const analysis = await SecurityModule.getErrorAnalysis();
    
    // 查找指定 ID 的模式
    const pattern = analysis.patterns.find((pattern: any) => pattern.id === id);
    
    if (!pattern) {
      return c.json({
        success: false,
        error: '未找到指定的错误模式'
      }, 404);
    }
    
    return c.json({
      success: true,
      pattern
    });
  } catch (error) {
    console.error('Error getting pattern details:', error);
    return c.json({
      success: false,
      error: '获取错误模式详情失败'
    }, 500);
  }
});

/**
 * 获取异常详情
 */
app.get('/anomalies/:type', async (c) => {
  try {
    const type = c.req.param('type');
    
    // 获取错误分析结果
    const analysis = await SecurityModule.getErrorAnalysis();
    
    // 查找指定类型的异常
    const anomaly = analysis.anomalies[type];
    
    if (!anomaly) {
      return c.json({
        success: false,
        error: '未找到指定的异常'
      }, 404);
    }
    
    return c.json({
      success: true,
      anomaly
    });
  } catch (error) {
    console.error('Error getting anomaly details:', error);
    return c.json({
      success: false,
      error: '获取异常详情失败'
    }, 500);
  }
});

/**
 * 获取修复历史
 */
app.get('/fixes', async (c) => {
  try {
    // 获取修复历史
    const fixes = SecurityModule.getFixHistory();
    
    return c.json({
      success: true,
      fixes
    });
  } catch (error) {
    console.error('Error getting fix history:', error);
    return c.json({
      success: false,
      error: '获取修复历史失败'
    }, 500);
  }
});

/**
 * 获取错误仪表板数据
 */
app.get('/dashboard', async (c) => {
  try {
    // 获取错误分析结果
    const analysis = await SecurityModule.getErrorAnalysis();
    
    // 获取日志
    const logs = await SecurityModule.getLogs('all', 1, 1000, c.env);
    
    // 获取修复历史
    const fixes = SecurityModule.getFixHistory();
    
    // 计算仪表板数据
    const dashboard = {
      summary: {
        totalErrors: logs.total,
        criticalErrors: logs.logs.filter(log => log.severity === 'critical').length,
        highErrors: logs.logs.filter(log => log.severity === 'high').length,
        mediumErrors: logs.logs.filter(log => log.severity === 'medium').length,
        lowErrors: logs.logs.filter(log => log.severity === 'low').length,
        anomalies: Object.values(analysis.anomalies).filter((a: any) => a.isAnomaly).length,
        clusters: analysis.clusters.length,
        patterns: analysis.patterns.length,
        fixes: fixes.length,
        successfulFixes: fixes.filter((fix: any) => fix.success).length
      },
      trends: {
        errorsByDay: calculateErrorsByDay(logs.logs),
        errorsBySeverity: calculateErrorsBySeverity(logs.logs),
        errorsBySource: calculateErrorsBySource(logs.logs)
      },
      topIssues: {
        topErrorCodes: calculateTopErrorCodes(logs.logs),
        topSources: calculateTopSources(logs.logs),
        topPatterns: analysis.patterns.slice(0, 5)
      }
    };
    
    return c.json({
      success: true,
      dashboard
    });
  } catch (error) {
    console.error('Error getting dashboard data:', error);
    return c.json({
      success: false,
      error: '获取仪表板数据失败'
    }, 500);
  }
});

/**
 * 计算每天的错误数量
 * @param logs 日志条目
 */
function calculateErrorsByDay(logs: any[]): { date: string; count: number }[] {
  const errorsByDay: { [key: string]: number } = {};
  
  for (const log of logs) {
    const date = new Date(log.timestamp).toISOString().substring(0, 10);
    errorsByDay[date] = (errorsByDay[date] || 0) + 1;
  }
  
  return Object.entries(errorsByDay)
    .map(([date, count]) => ({ date, count }))
    .sort((a, b) => a.date.localeCompare(b.date));
}

/**
 * 计算各严重性级别的错误数量
 * @param logs 日志条目
 */
function calculateErrorsBySeverity(logs: any[]): { severity: string; count: number }[] {
  const errorsBySeverity: { [key: string]: number } = {
    critical: 0,
    high: 0,
    medium: 0,
    low: 0,
    unknown: 0
  };
  
  for (const log of logs) {
    const severity = log.severity || 'unknown';
    errorsBySeverity[severity] = (errorsBySeverity[severity] || 0) + 1;
  }
  
  return Object.entries(errorsBySeverity)
    .map(([severity, count]) => ({ severity, count }));
}

/**
 * 计算各来源的错误数量
 * @param logs 日志条目
 */
function calculateErrorsBySource(logs: any[]): { source: string; count: number }[] {
  const errorsBySource: { [key: string]: number } = {};
  
  for (const log of logs) {
    const source = log.source || 'unknown';
    errorsBySource[source] = (errorsBySource[source] || 0) + 1;
  }
  
  return Object.entries(errorsBySource)
    .map(([source, count]) => ({ source, count }))
    .sort((a, b) => b.count - a.count);
}

/**
 * 计算最常见的错误代码
 * @param logs 日志条目
 */
function calculateTopErrorCodes(logs: any[]): { code: string; count: number }[] {
  const errorsByCode: { [key: string]: number } = {};
  
  for (const log of logs) {
    if (log.errorCode) {
      errorsByCode[log.errorCode] = (errorsByCode[log.errorCode] || 0) + 1;
    }
  }
  
  return Object.entries(errorsByCode)
    .map(([code, count]) => ({ code, count }))
    .sort((a, b) => b.count - a.count)
    .slice(0, 10);
}

/**
 * 计算最常见的错误来源
 * @param logs 日志条目
 */
function calculateTopSources(logs: any[]): { source: string; count: number }[] {
  const errorsBySource: { [key: string]: number } = {};
  
  for (const log of logs) {
    const source = log.source || 'unknown';
    errorsBySource[source] = (errorsBySource[source] || 0) + 1;
  }
  
  return Object.entries(errorsBySource)
    .map(([source, count]) => ({ source, count }))
    .sort((a, b) => b.count - a.count)
    .slice(0, 5);
}

// 导出路由
export default app;
