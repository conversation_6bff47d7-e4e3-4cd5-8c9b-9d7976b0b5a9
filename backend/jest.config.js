/**
 * 🧪 Jest测试配置文件
 * 自动化测试框架配置
 */

module.exports = {
  // 测试环境
  testEnvironment: 'node',

  // 根目录
  rootDir: '.',

  // 测试文件匹配模式
  testMatch: [
    '**/tests/**/*.test.js',
    '**/tests/**/*.spec.js',
    '**/__tests__/**/*.js'
  ],

  // 忽略的测试文件
  testPathIgnorePatterns: [
    '/node_modules/',
    '/dist/',
    '/build/',
    '/coverage/',
    '/backups/',
    '/archive/'
  ],

  // 覆盖率配置
  collectCoverage: false, // 默认不收集，通过命令行参数控制
  collectCoverageFrom: [
    'src/**/*.js',
    'index-modular-simple.js',
    '!src/**/*.test.js',
    '!src/**/*.spec.js',
    '!**/node_modules/**',
    '!**/tests/**',
    '!**/coverage/**'
  ],

  // 覆盖率报告格式
  coverageReporters: [
    'text',
    'text-summary',
    'html',
    'lcov',
    'json'
  ],

  // 覆盖率阈值
  coverageThreshold: {
    global: {
      branches: 70,
      functions: 70,
      lines: 70,
      statements: 70
    }
  },

  // 覆盖率输出目录
  coverageDirectory: 'coverage',

  // 设置文件
  setupFilesAfterEnv: [
    '<rootDir>/tests/setup.js'
  ],



  // 全局变量
  globals: {
    'process.env.NODE_ENV': 'test'
  },

  // 测试超时时间
  testTimeout: 10000,

  // 详细输出
  verbose: true,

  // 错误时停止
  bail: false,

  // 清除模拟
  clearMocks: true,
  restoreMocks: true,

  // 转换配置
  transform: {
    '^.+\\.js$': 'babel-jest'
  },

  // 模块文件扩展名
  moduleFileExtensions: [
    'js',
    'json',
    'node'
  ],

  // 测试结果处理器
  reporters: [
    'default'
  ]
};
