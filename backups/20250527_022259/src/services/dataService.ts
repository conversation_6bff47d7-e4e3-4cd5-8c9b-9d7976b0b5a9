/**
 * 数据服务层
 *
 * 这个服务层负责处理所有数据操作，包括：
 * 1. 根据环境变量决定使用模拟数据还是真实API
 * 2. 提供统一的数据操作接口
 * 3. 处理错误和日志记录
 */

import * as api from '@/lib/api';
import {
  QuestionnaireResponse,
  FilterOptions,
  TagItem,
  DeidentificationConfig,
  DeidentificationTestRequest,
  Story
} from '@/types/api';

// 模拟数据导入已移除 - 现在完全使用真实API

// 环境变量控制是否使用模拟数据和缓存
const USE_MOCK = false; // 强制禁用模拟数据，完全使用真实API
const USE_CACHE = import.meta.env.VITE_USE_CACHE !== 'false';
const ENVIRONMENT = import.meta.env.VITE_ENVIRONMENT || 'development';

// 导入新的内容管理服务
import * as contentManagementService from './contentManagementService';

// 导入高级缓存服务
import {
  cacheContentList,
  getCachedContentList,
  cacheContentDetail,
  getCachedContentDetail,
  cacheSettings,
  getCachedSettings,
  clearContentCache,
  getCacheStats
} from './advancedCacheService';

// 日志函数
const logInfo = (message: string, data?: any) => {
  if (ENVIRONMENT !== 'production') {
    console.info(`[DataService] ${message}`, data);
  }
};

const logError = (message: string, error: any) => {
  console.error(`[DataService] ${message}`, error);
};

/**
 * 问卷回复数据服务
 */

// 获取问卷回复列表
export const getResponses = async (
  page = 1,
  pageSize = 10,
  filters: FilterOptions = {}
) => {
  try {
    logInfo(`获取问卷回复列表 (页码: ${page}, 每页数量: ${pageSize})`, { filters });
    logInfo('使用真实API');
    return await api.getResponses(page, pageSize, filters);
  } catch (error) {
    logError('获取问卷回复列表失败', error);
    throw error;
  }
};

// 获取单个问卷回复
export const getResponse = async (id: number) => {
  try {
    logInfo(`获取问卷回复详情 (ID: ${id})`);
    logInfo('使用真实API');
    return await api.getResponse(id);
  } catch (error) {
    logError(`获取问卷回复详情失败 (ID: ${id})`, error);
    throw error;
  }
};

// 更新问卷回复
export const updateResponse = async (
  id: number,
  data: Partial<QuestionnaireResponse>
) => {
  try {
    logInfo(`更新问卷回复 (ID: ${id})`, { data });

    if (USE_MOCK) {
      logInfo('使用模拟数据');
      // 在实际应用中，这里应该更新本地模拟数据
      return { success: true, response: { ...data, id } };
    } else {
      logInfo('使用真实API');
      return await api.updateResponse(id, data);
    }
  } catch (error) {
    logError(`更新问卷回复失败 (ID: ${id})`, error);
    throw error;
  }
};

// 删除问卷回复
export const deleteResponse = async (id: number) => {
  try {
    logInfo(`删除问卷回复 (ID: ${id})`);

    if (USE_MOCK) {
      logInfo('使用模拟数据');
      // 在实际应用中，这里应该从本地模拟数据中删除
      return { success: true };
    } else {
      logInfo('使用真实API');
      return await api.deleteResponse(id);
    }
  } catch (error) {
    logError(`删除问卷回复失败 (ID: ${id})`, error);
    throw error;
  }
};

// 批量操作
export const bulkDeleteResponses = async (ids: number[]) => {
  try {
    logInfo(`批量删除问卷回复`, { ids });

    if (USE_MOCK) {
      logInfo('使用模拟数据');
      return { success: true };
    } else {
      logInfo('使用真实API');
      return await api.bulkDeleteResponses(ids);
    }
  } catch (error) {
    logError('批量删除问卷回复失败', error);
    throw error;
  }
};

export const bulkAddTags = async (ids: number[], tags: string[]) => {
  try {
    logInfo(`批量添加标签`, { ids, tags });

    if (USE_MOCK) {
      logInfo('使用模拟数据');
      return { success: true };
    } else {
      logInfo('使用真实API');
      return await api.bulkAddTags(ids, tags);
    }
  } catch (error) {
    logError('批量添加标签失败', error);
    throw error;
  }
};

// 批量编辑响应
export const bulkEditResponses = async (ids: number[], data: Partial<QuestionnaireResponse>) => {
  try {
    logInfo(`批量编辑响应`, { ids, data });

    if (USE_MOCK) {
      logInfo('使用模拟数据');
      return { success: true };
    } else {
      logInfo('使用真实API');
      // 在实际应用中，这里应该调用真实API
      // return await api.bulkEditResponses(ids, data);
      return { success: true };
    }
  } catch (error) {
    logError('批量编辑响应失败', error);
    throw error;
  }
};

// 批量验证响应
export const bulkVerifyResponses = async (ids: number[], verified: boolean) => {
  try {
    logInfo(`批量验证响应`, { ids, verified });

    if (USE_MOCK) {
      logInfo('使用模拟数据');
      return { success: true };
    } else {
      logInfo('使用真实API');
      // 在实际应用中，这里应该调用真实API
      // return await api.bulkVerifyResponses(ids, verified);
      return { success: true };
    }
  } catch (error) {
    logError('批量验证响应失败', error);
    throw error;
  }
};

/**
 * 标签管理数据服务
 */

// 获取标签列表
export const getTags = async () => {
  try {
    logInfo('获取标签列表');

    if (USE_MOCK) {
      logInfo('使用模拟数据');
      return getMockTags();
    } else {
      logInfo('使用真实API');
      return await api.getTags();
    }
  } catch (error) {
    logError('获取标签列表失败', error);
    throw error;
  }
};

/**
 * 故事管理数据服务
 */

// 获取故事列表
export const getStories = async (
  page = 1,
  sortBy: 'latest' | 'popular' = 'latest',
  tag?: string,
  options?: any
) => {
  try {
    logInfo(`获取故事列表 (页码: ${page}, 排序: ${sortBy})`, { tag, options });

    if (USE_MOCK) {
      logInfo('使用模拟数据');
      return getMockStories(page, sortBy, tag, options);
    } else {
      logInfo('使用真实API');
      return await api.getStories(page, sortBy, tag, options);
    }
  } catch (error) {
    logError('获取故事列表失败', error);
    throw error;
  }
};

/**
 * 数据分析服务
 */

// 获取统计数据
export const getStatistics = async (filters: Record<string, any> = {}) => {
  try {
    logInfo('获取统计数据', { filters });

    if (USE_MOCK) {
      logInfo('使用模拟数据');
      return getMockStatistics(filters);
    } else {
      logInfo('使用真实API');
      return await api.getStatistics(filters);
    }
  } catch (error) {
    logError('获取统计数据失败', error);
    throw error;
  }
};

/**
 * 内容脱敏服务
 */

// 获取脱敏配置
export const getDeidentificationConfig = async () => {
  try {
    logInfo('获取脱敏配置');

    if (USE_MOCK) {
      logInfo('使用模拟数据');
      return getMockDeidentificationConfig();
    } else {
      logInfo('使用真实API');
      return await api.getDeidentificationConfig();
    }
  } catch (error) {
    logError('获取脱敏配置失败', error);
    throw error;
  }
};

// 测试脱敏
export const testDeidentification = async (request: DeidentificationTestRequest) => {
  try {
    logInfo('测试脱敏', { request });

    if (USE_MOCK) {
      logInfo('使用模拟数据');
      return {
        success: true,
        original: request.content,
        sanitized: `[脱敏后] ${request.content}`,
        modified: true
      };
    } else {
      logInfo('使用真实API');
      return await api.testDeidentification(request);
    }
  } catch (error) {
    logError('测试脱敏失败', error);
    throw error;
  }
};

/**
 * 内容审核服务
 */

// 获取待审核内容列表
export const getPendingContents = async (queryParams?: string) => {
  try {
    logInfo('获取待审核内容列表', { queryParams });

    // 尝试从缓存获取
    if (USE_CACHE) {
      const cachedData = getCachedContentList(queryParams || '');
      if (cachedData) {
        logInfo('使用缓存数据');
        return cachedData;
      }
    }

    let result;
    if (USE_MOCK) {
      logInfo('使用模拟数据');
      result = getMockPendingContents(queryParams);
    } else {
      logInfo('使用真实API');
      const token = localStorage.getItem('adminToken');
      const url = queryParams
        ? `${import.meta.env.VITE_API_BASE_URL}/admin/review/pending?${queryParams}`
        : `${import.meta.env.VITE_API_BASE_URL}/admin/review/pending`;

      const response = await fetch(url, {
        headers: {
          'Authorization': `Bearer ${token}`,
        },
      });

      result = await response.json();
    }

    // 缓存结果
    if (USE_CACHE && result.success) {
      cacheContentList(queryParams || '', result);
    }

    return result;
  } catch (error) {
    logError('获取待审核内容列表失败', error);
    throw error;
  }
};

// 获取单条待审核内容详情
export const getPendingContent = async (id: string) => {
  try {
    logInfo('获取待审核内容详情', { id });

    // 尝试从缓存获取
    if (USE_CACHE) {
      const cachedData = getCachedContentDetail(id);
      if (cachedData) {
        logInfo('使用缓存数据');
        return cachedData;
      }
    }

    let result;
    if (USE_MOCK) {
      logInfo('使用模拟数据');
      result = getMockPendingContent(id);
    } else {
      logInfo('使用真实API');
      const token = localStorage.getItem('adminToken');
      const response = await fetch(`${import.meta.env.VITE_API_BASE_URL}/admin/review/${id}`, {
        headers: {
          'Authorization': `Bearer ${token}`,
        },
      });

      result = await response.json();
    }

    // 缓存结果
    if (USE_CACHE && result.success) {
      cacheContentDetail(id, result);
    }

    return result;
  } catch (error) {
    logError('获取待审核内容详情失败', error);
    throw error;
  }
};

// 审核通过内容
export const approveContent = async (id: string, data: { reviewNotes?: string }) => {
  try {
    logInfo('审核通过内容', { id, data });

    let result;
    if (USE_MOCK) {
      logInfo('使用模拟数据');
      result = {
        success: true,
        message: '审核通过',
        storyId: 'mock-story-id'
      };
    } else {
      logInfo('使用真实API');
      const token = localStorage.getItem('adminToken');
      const response = await fetch(`${import.meta.env.VITE_API_BASE_URL}/admin/review/${id}/approve`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`,
        },
        body: JSON.stringify(data),
      });

      result = await response.json();
    }

    // 如果操作成功，清除相关缓存
    if (result.success && USE_CACHE) {
      clearContentCache();
    }

    return result;
  } catch (error) {
    logError('审核通过内容失败', error);
    throw error;
  }
};

// 编辑并通过内容
export const editContent = async (id: string, data: { content: any, reviewNotes?: string }) => {
  try {
    logInfo('编辑并通过内容', { id, data });

    let result;
    if (USE_MOCK) {
      logInfo('使用模拟数据');
      result = {
        success: true,
        message: '编辑并通过',
        storyId: 'mock-story-id'
      };
    } else {
      logInfo('使用真实API');
      const token = localStorage.getItem('adminToken');
      const response = await fetch(`${import.meta.env.VITE_API_BASE_URL}/admin/review/${id}/edit`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`,
        },
        body: JSON.stringify(data),
      });

      result = await response.json();
    }

    // 如果操作成功，清除相关缓存
    if (result.success && USE_CACHE) {
      clearContentCache();
    }

    return result;
  } catch (error) {
    logError('编辑并通过内容失败', error);
    throw error;
  }
};

// 拒绝内容
export const rejectContent = async (id: string, data: { reason: string }) => {
  try {
    logInfo('拒绝内容', { id, data });

    let result;
    if (USE_MOCK) {
      logInfo('使用模拟数据');
      result = {
        success: true,
        message: '已拒绝内容'
      };
    } else {
      logInfo('使用真实API');
      const token = localStorage.getItem('adminToken');
      const response = await fetch(`${import.meta.env.VITE_API_BASE_URL}/admin/review/${id}/reject`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`,
        },
        body: JSON.stringify(data),
      });

      result = await response.json();
    }

    // 如果操作成功，清除相关缓存
    if (result.success && USE_CACHE) {
      clearContentCache();
    }

    return result;
  } catch (error) {
    logError('拒绝内容失败', error);
    throw error;
  }
};

// 获取审核设置
export const getReviewSettings = async () => {
  try {
    logInfo('获取审核设置');

    // 尝试从缓存获取
    if (USE_CACHE) {
      const cachedData = getCachedSettings('review');
      if (cachedData) {
        logInfo('使用缓存数据');
        return cachedData;
      }
    }

    let result;
    if (USE_MOCK) {
      logInfo('使用模拟数据');
      result = getMockReviewSettings();
    } else {
      logInfo('使用真实API');
      const token = localStorage.getItem('adminToken');
      const response = await fetch(`${import.meta.env.VITE_API_BASE_URL}/admin/review/settings`, {
        headers: {
          'Authorization': `Bearer ${token}`,
        },
      });

      result = await response.json();
    }

    // 缓存结果
    if (USE_CACHE && result.success) {
      // 缓存30分钟
      cacheSettings('review', result, 30 * 60 * 1000);
    }

    return result;
  } catch (error) {
    logError('获取审核设置失败', error);
    throw error;
  }
};

// 更新审核设置
export const updateReviewSettings = async (settings: any) => {
  try {
    logInfo('更新审核设置', { settings });

    if (USE_MOCK) {
      logInfo('使用模拟数据');
      return {
        success: true,
        message: '审核设置已更新'
      };
    } else {
      logInfo('使用真实API');
      const token = localStorage.getItem('adminToken');
      const response = await fetch(`${import.meta.env.VITE_API_BASE_URL}/admin/review/settings`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`,
        },
        body: JSON.stringify(settings),
      });

      return await response.json();
    }
  } catch (error) {
    logError('更新审核设置失败', error);
    throw error;
  }
};

// 获取审核统计信息
export const getReviewStats = async () => {
  try {
    logInfo('获取审核统计信息');

    if (USE_MOCK) {
      logInfo('使用模拟数据');
      return getMockReviewStats();
    } else {
      logInfo('使用真实API');
      const token = localStorage.getItem('adminToken');
      const response = await fetch(`${import.meta.env.VITE_API_BASE_URL}/admin/review/stats`, {
        headers: {
          'Authorization': `Bearer ${token}`,
        },
      });

      return await response.json();
    }
  } catch (error) {
    logError('获取审核统计信息失败', error);
    throw error;
  }
};

// 获取审核员绩效数据
export const getReviewerPerformance = async (timeRange: string = 'week') => {
  try {
    logInfo('获取审核员绩效数据', { timeRange });

    if (USE_MOCK) {
      logInfo('使用模拟数据');
      return getMockReviewerPerformance(timeRange);
    } else {
      logInfo('使用真实API');
      const token = localStorage.getItem('adminToken');
      const response = await fetch(`${import.meta.env.VITE_API_BASE_URL}/admin/review/performance?timeRange=${timeRange}`, {
        headers: {
          'Authorization': `Bearer ${token}`,
        },
      });

      return await response.json();
    }
  } catch (error) {
    logError('获取审核员绩效数据失败', error);
    throw error;
  }
};

// 获取审核模板
export const getReviewTemplates = async () => {
  try {
    logInfo('获取审核模板');

    let result;
    if (USE_MOCK) {
      logInfo('使用模拟数据');
      result = getMockReviewTemplates();
      return result;
    } else {
      try {
        logInfo('使用真实API');
        const token = localStorage.getItem('adminToken');
        const response = await fetch(`${import.meta.env.VITE_API_BASE_URL}/admin/review/templates`, {
          headers: {
            'Authorization': `Bearer ${token}`,
          },
        });

        result = await response.json();
        return result;
      } catch (apiError) {
        // 如果API调用失败且FALLBACK_TO_MOCK为true，则使用模拟数据
        if (import.meta.env.VITE_FALLBACK_TO_MOCK === 'true') {
          logInfo('API调用失败，回退到模拟数据');
          result = getMockReviewTemplates();
          return result;
        } else {
          throw apiError;
        }
      }
    }
  } catch (error) {
    logError('获取审核模板失败', error);
    console.error('获取审核模板错误:', error);
    return {
      success: true,
      templates: []
    };
  }
};

// 创建审核模板
export const createReviewTemplate = async (data: any) => {
  try {
    logInfo('创建审核模板', { data });

    if (USE_MOCK) {
      logInfo('使用模拟数据');
      return {
        success: true,
        message: '模板已创建',
        templateId: 'mock-template-id'
      };
    } else {
      logInfo('使用真实API');
      const token = localStorage.getItem('adminToken');
      const response = await fetch(`${import.meta.env.VITE_API_BASE_URL}/admin/review/templates`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`,
        },
        body: JSON.stringify(data),
      });

      return await response.json();
    }
  } catch (error) {
    logError('创建审核模板失败', error);
    throw error;
  }
};

// 更新审核模板
export const updateReviewTemplate = async (id: string, data: any) => {
  try {
    logInfo('更新审核模板', { id, data });

    if (USE_MOCK) {
      logInfo('使用模拟数据');
      return {
        success: true,
        message: '模板已更新'
      };
    } else {
      logInfo('使用真实API');
      const token = localStorage.getItem('adminToken');
      const response = await fetch(`${import.meta.env.VITE_API_BASE_URL}/admin/review/templates/${id}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`,
        },
        body: JSON.stringify(data),
      });

      return await response.json();
    }
  } catch (error) {
    logError('更新审核模板失败', error);
    throw error;
  }
};

// 删除审核模板
export const deleteReviewTemplate = async (id: string) => {
  try {
    logInfo('删除审核模板', { id });

    if (USE_MOCK) {
      logInfo('使用模拟数据');
      return {
        success: true,
        message: '模板已删除'
      };
    } else {
      logInfo('使用真实API');
      const token = localStorage.getItem('adminToken');
      const response = await fetch(`${import.meta.env.VITE_API_BASE_URL}/admin/review/templates/${id}`, {
        method: 'DELETE',
        headers: {
          'Authorization': `Bearer ${token}`,
        },
      });

      return await response.json();
    }
  } catch (error) {
    logError('删除审核模板失败', error);
    throw error;
  }
};

/**
 * 评论审核服务
 */

// 获取待审核评论列表
export const getPendingComments = async (queryParams?: string) => {
  try {
    logInfo('获取待审核评论列表', { queryParams });
    console.log('dataService: 获取待审核评论列表, 查询参数:', queryParams);

    // 使用真实API
    const token = localStorage.getItem('adminToken');
    const response = await fetch(`${import.meta.env.VITE_API_BASE_URL}/admin/comments/pending?${queryParams || ''}`, {
      headers: {
        'Authorization': `Bearer ${token}`,
      },
    });

    return await response.json();
  } catch (error) {
    logError('获取待审核评论列表失败', error);
    console.error('dataService: 获取待审核评论列表错误:', error);
    throw error;
  }
};

// 获取单条待审核评论详情
export const getPendingComment = async (id: string) => {
  try {
    logInfo('获取待审核评论详情', { id });
    console.log('dataService: 获取待审核评论详情, ID:', id);

    // 使用真实API
    const token = localStorage.getItem('adminToken');
    const response = await fetch(`${import.meta.env.VITE_API_BASE_URL}/admin/comments/${id}`, {
      headers: {
        'Authorization': `Bearer ${token}`,
      },
    });

    return await response.json();
  } catch (error) {
    logError('获取待审核评论详情失败', error);
    console.error('dataService: 获取待审核评论详情错误:', error);
    throw error;
  }
};

// 审核通过评论
export const approveComment = async (id: string, data: { reviewNotes?: string }) => {
  try {
    logInfo('审核通过评论', { id, data });

    const token = localStorage.getItem('adminToken');
    const response = await fetch(`${import.meta.env.VITE_API_BASE_URL}/admin/comments/${id}/approve`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${token}`,
      },
      body: JSON.stringify(data),
    });

    return await response.json();
  } catch (error) {
    logError('审核通过评论失败', error);
    console.error('审核通过评论错误:', error);
    throw error;
  }
};

// 编辑并通过评论
export const editComment = async (id: string, data: { content: string, reviewNotes?: string }) => {
  try {
    logInfo('编辑并通过评论', { id, data });

    let result;
    if (USE_MOCK) {
      logInfo('使用模拟数据');
      result = {
        success: true,
        message: '评论已编辑并通过审核'
      };
      return result;
    } else {
      try {
        logInfo('使用真实API');
        const token = localStorage.getItem('adminToken');
        const response = await fetch(`${import.meta.env.VITE_API_BASE_URL}/admin/comments/${id}/edit`, {
          method: 'PUT',
          headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${token}`,
          },
          body: JSON.stringify(data),
        });

        result = await response.json();
        return result;
      } catch (apiError) {
        // 如果API调用失败且FALLBACK_TO_MOCK为true，则使用模拟数据
        if (import.meta.env.VITE_FALLBACK_TO_MOCK === 'true') {
          logInfo('API调用失败，回退到模拟数据');
          result = {
            success: true,
            message: '评论已编辑并通过审核'
          };
          return result;
        } else {
          throw apiError;
        }
      }
    }
  } catch (error) {
    logError('编辑并通过评论失败', error);
    console.error('编辑并通过评论错误:', error);
    return {
      success: false,
      error: '编辑并通过评论失败，请稍后再试'
    };
  }
};

// 拒绝评论
export const rejectComment = async (id: string, data: { reason: string }) => {
  try {
    logInfo('拒绝评论', { id, data });

    let result;
    if (USE_MOCK) {
      logInfo('使用模拟数据');
      result = {
        success: true,
        message: '评论已被拒绝'
      };
      return result;
    } else {
      try {
        logInfo('使用真实API');
        const token = localStorage.getItem('adminToken');
        const response = await fetch(`${import.meta.env.VITE_API_BASE_URL}/admin/comments/${id}/reject`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${token}`,
          },
          body: JSON.stringify(data),
        });

        result = await response.json();
        return result;
      } catch (apiError) {
        // 如果API调用失败且FALLBACK_TO_MOCK为true，则使用模拟数据
        if (import.meta.env.VITE_FALLBACK_TO_MOCK === 'true') {
          logInfo('API调用失败，回退到模拟数据');
          result = {
            success: true,
            message: '评论已被拒绝'
          };
          return result;
        } else {
          throw apiError;
        }
      }
    }
  } catch (error) {
    logError('拒绝评论失败', error);
    console.error('拒绝评论错误:', error);
    return {
      success: false,
      error: '拒绝评论失败，请稍后再试'
    };
  }
};

/**
 * 故事审核服务
 */

// 获取待审核的故事
export const getPendingStories = async () => {
  try {
    logInfo('获取待审核的故事');

    // 完全使用真实API，不再支持模拟数据
    logInfo('使用真实API获取待审核故事');
    const token = localStorage.getItem('adminToken');
    const response = await fetch(`${import.meta.env.VITE_API_BASE_URL || ''}/admin/stories/pending`, {
      headers: {
        'Authorization': `Bearer ${token}`,
      },
    });

    return await response.json();
  } catch (error) {
    logError('获取待审核的故事失败', error);
    throw error;
  }
};

// 审核故事
export const moderateStory = async (storyId: number, status: 'approved' | 'rejected') => {
  try {
    logInfo('审核故事', { storyId, status });

    if (USE_MOCK) {
      logInfo('使用模拟数据');
      return {
        success: true,
        message: `故事已${status === 'approved' ? '通过' : '拒绝'}`
      };
    } else {
      logInfo('使用真实API');
      const token = localStorage.getItem('adminToken');
      const response = await fetch(`${import.meta.env.VITE_API_BASE_URL}/admin/stories/moderate`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`,
        },
        body: JSON.stringify({ storyId, status }),
      });

      return await response.json();
    }
  } catch (error) {
    logError('审核故事失败', error);
    throw error;
  }
};

/**
 * 标签管理服务
 */

// 获取所有标签
export const getAllTags = async () => {
  try {
    logInfo('获取所有标签');

    if (USE_MOCK) {
      logInfo('使用模拟数据');
      return getMockTags();
    } else {
      logInfo('使用真实API');
      return await api.getTags();
    }
  } catch (error) {
    logError('获取所有标签失败', error);
    throw error;
  }
};

// 创建标签
export const createTag = async (tagData: any) => {
  try {
    logInfo('创建标签', { tagData });

    if (USE_MOCK) {
      logInfo('使用模拟数据');
      const newTag = {
        id: `tag-${Date.now()}`,
        name: tagData.name,
        color: tagData.color,
        priority: tagData.priority,
        category: tagData.category,
        parentId: tagData.parentId,
        count: 0,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString()
      };

      return {
        success: true,
        tag: newTag
      };
    } else {
      logInfo('使用真实API');
      const token = localStorage.getItem('adminToken');
      const response = await fetch(`${import.meta.env.VITE_API_BASE_URL}/admin/tags`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`,
        },
        body: JSON.stringify(tagData),
      });

      return await response.json();
    }
  } catch (error) {
    logError('创建标签失败', error);
    throw error;
  }
};

// 更新标签
export const updateTag = async (tagId: string, tagData: any) => {
  try {
    logInfo('更新标签', { tagId, tagData });

    if (USE_MOCK) {
      logInfo('使用模拟数据');
      return {
        success: true,
        tag: {
          id: tagId,
          ...tagData,
          count: Math.floor(Math.random() * 50),
          updatedAt: new Date().toISOString()
        }
      };
    } else {
      logInfo('使用真实API');
      const token = localStorage.getItem('adminToken');
      const response = await fetch(`${import.meta.env.VITE_API_BASE_URL}/admin/tags/${tagId}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`,
        },
        body: JSON.stringify(tagData),
      });

      return await response.json();
    }
  } catch (error) {
    logError('更新标签失败', error);
    throw error;
  }
};

// 删除标签
export const deleteTag = async (tagId: string) => {
  try {
    logInfo('删除标签', { tagId });

    if (USE_MOCK) {
      logInfo('使用模拟数据');
      return {
        success: true
      };
    } else {
      logInfo('使用真实API');
      const token = localStorage.getItem('adminToken');
      const response = await fetch(`${import.meta.env.VITE_API_BASE_URL}/admin/tags/${tagId}`, {
        method: 'DELETE',
        headers: {
          'Authorization': `Bearer ${token}`,
        },
      });

      return await response.json();
    }
  } catch (error) {
    logError('删除标签失败', error);
    throw error;
  }
};

/**
 * 数据分析服务
 */

// 获取分析数据
export const getAnalyticsData = async (filters: any = {}) => {
  try {
    logInfo('获取分析数据', { filters });

    if (USE_MOCK) {
      logInfo('使用模拟数据');
      return {
        success: true,
        stats: {
          totalResponses: 8742,
          verifiedResponses: 6521,
          anonymousResponses: 2221,
          responsesByDate: [
            { date: '2023-01', count: 1245 },
            { date: '2023-02', count: 1356 },
            { date: '2023-03', count: 1478 },
            { date: '2023-04', count: 1589 },
            { date: '2023-05', count: 1632 },
            { date: '2023-06', count: 1442 }
          ],
          educationLevels: [
            { level: '本科', count: 4521 },
            { level: '硕士', count: 2356 },
            { level: '博士', count: 987 },
            { level: '专科', count: 878 }
          ],
          regions: [
            { region: '北京', count: 1856 },
            { region: '上海', count: 1742 },
            { region: '广州', count: 1245 },
            { region: '深圳', count: 1189 },
            { region: '杭州', count: 987 },
            { region: '成都', count: 856 },
            { region: '其他', count: 867 }
          ],
          employmentStatus: [
            { status: '已就业', count: 6521 },
            { status: '待业', count: 1542 },
            { status: '自由职业', count: 456 },
            { status: '创业', count: 223 }
          ],
          industries: [
            { industry: '互联网', count: 3245 },
            { industry: '金融', count: 1856 },
            { industry: '教育', count: 1245 },
            { industry: '医疗', count: 987 },
            { industry: '制造业', count: 756 },
            { industry: '其他', count: 653 }
          ],
          salaryRanges: [
            { range: '5k以下', count: 456 },
            { range: '5k-10k', count: 1245 },
            { range: '10k-15k', count: 2356 },
            { range: '15k-20k', count: 1856 },
            { range: '20k-30k', count: 1542 },
            { range: '30k以上', count: 1287 }
          ],
          jobSatisfaction: [
            { level: '1', count: 456 },
            { level: '2', count: 987 },
            { level: '3', count: 2356 },
            { level: '4', count: 3245 },
            { level: '5', count: 1698 }
          ],
          careerChangeIntention: [
            { intention: '有意向', count: 3245 },
            { intention: '无意向', count: 5497 }
          ]
        }
      };
    } else {
      logInfo('使用真实API');
      // 构建查询参数
      const queryParams = new URLSearchParams();

      Object.entries(filters).forEach(([key, value]) => {
        if (value !== undefined && value !== null && value !== '') {
          queryParams.append(key, String(value));
        }
      });

      const token = localStorage.getItem('adminToken');
      const response = await fetch(
        `${import.meta.env.VITE_API_BASE_URL}/analytics/stats?${queryParams.toString()}`,
        {
          headers: {
            'Authorization': `Bearer ${token}`,
          },
        }
      );

      return await response.json();
    }
  } catch (error) {
    logError('获取分析数据失败', error);
    throw error;
  }
};

// 导出数据
export const exportAnalyticsData = async (format: 'csv' | 'excel', filters: any = {}) => {
  try {
    logInfo('导出数据', { format, filters });

    if (USE_MOCK) {
      logInfo('使用模拟数据');
      return {
        success: true,
        exportUrl: '#',
        message: `数据已导出为${format.toUpperCase()}格式，链接有效期24小时`
      };
    } else {
      logInfo('使用真实API');
      // 构建查询参数
      const queryParams = new URLSearchParams();

      Object.entries(filters).forEach(([key, value]) => {
        if (value !== undefined && value !== null && value !== '') {
          queryParams.append(key, String(value));
        }
      });

      queryParams.append('format', format);

      const token = localStorage.getItem('adminToken');
      const response = await fetch(
        `${import.meta.env.VITE_API_BASE_URL}/analytics/export?${queryParams.toString()}`,
        {
          headers: {
            'Authorization': `Bearer ${token}`,
          },
        }
      );

      return await response.json();
    }
  } catch (error) {
    logError('导出数据失败', error);
    throw error;
  }
};

// ==================== 新增内容管理API ====================

/**
 * 内容管理服务 - 使用新的contentManagementService
 */

// 获取待审核内容列表（新版本）
export const getContentsPending = async (params: any = {}) => {
  try {
    logInfo('获取待审核内容列表（新版本）', { params });
    return await contentManagementService.getPendingContents(params);
  } catch (error) {
    logError('获取待审核内容列表失败', error);
    throw error;
  }
};

// 获取单个待审核内容详情（新版本）
export const getContentPending = async (id: string) => {
  try {
    logInfo('获取待审核内容详情（新版本）', { id });
    return await contentManagementService.getPendingContent(id);
  } catch (error) {
    logError('获取待审核内容详情失败', error);
    throw error;
  }
};

// 审核通过内容（新版本）
export const approveContentNew = async (id: string, data: { reviewNotes?: string }) => {
  try {
    logInfo('审核通过内容（新版本）', { id, data });
    const result = await contentManagementService.approveContent(id, data);

    // 清除相关缓存
    if (result.success && USE_CACHE) {
      clearContentCache();
    }

    return result;
  } catch (error) {
    logError('审核通过内容失败', error);
    throw error;
  }
};

// 编辑并通过内容（新版本）
export const editAndApproveContentNew = async (id: string, data: { content: any; reviewNotes?: string }) => {
  try {
    logInfo('编辑并通过内容（新版本）', { id, data });
    const result = await contentManagementService.editAndApproveContent(id, data);

    // 清除相关缓存
    if (result.success && USE_CACHE) {
      clearContentCache();
    }

    return result;
  } catch (error) {
    logError('编辑并通过内容失败', error);
    throw error;
  }
};

// 拒绝内容（新版本）
export const rejectContentNew = async (id: string, data: { reason: string; reviewNotes?: string }) => {
  try {
    logInfo('拒绝内容（新版本）', { id, data });
    const result = await contentManagementService.rejectContent(id, data);

    // 清除相关缓存
    if (result.success && USE_CACHE) {
      clearContentCache();
    }

    return result;
  } catch (error) {
    logError('拒绝内容失败', error);
    throw error;
  }
};

// 批量审核内容（新版本）
export const batchModerateContentNew = async (
  contentIds: string[],
  action: 'approve' | 'reject',
  data: { reason?: string; reviewNotes?: string }
) => {
  try {
    logInfo('批量审核内容（新版本）', { contentIds, action, data });
    const result = await contentManagementService.batchModerateContent(contentIds, action, data);

    // 清除相关缓存
    if (result.success && USE_CACHE) {
      clearContentCache();
    }

    return result;
  } catch (error) {
    logError('批量审核内容失败', error);
    throw error;
  }
};

// 获取待审核故事列表（新版本）
export const getPendingStoriesNew = async (params: any = {}) => {
  try {
    logInfo('获取待审核故事列表（新版本）', { params });
    return await contentManagementService.getPendingStories(params);
  } catch (error) {
    logError('获取待审核故事列表失败', error);
    throw error;
  }
};

// 审核故事（新版本）
export const moderateStoryNew = async (
  storyId: number,
  action: 'approve' | 'reject',
  data: { reason?: string; reviewNotes?: string } = {}
) => {
  try {
    logInfo('审核故事（新版本）', { storyId, action, data });
    return await contentManagementService.moderateStory(storyId, action, data);
  } catch (error) {
    logError('审核故事失败', error);
    throw error;
  }
};

// 获取待审核评论列表（新版本）
export const getPendingCommentsNew = async (params: any = {}) => {
  try {
    logInfo('获取待审核评论列表（新版本）', { params });
    return await contentManagementService.getPendingComments(params);
  } catch (error) {
    logError('获取待审核评论列表失败', error);
    throw error;
  }
};

// 审核评论（新版本）
export const moderateCommentNew = async (
  commentId: string,
  action: 'approve' | 'reject',
  data: { reason?: string; reviewNotes?: string } = {}
) => {
  try {
    logInfo('审核评论（新版本）', { commentId, action, data });
    return await contentManagementService.moderateComment(commentId, action, data);
  } catch (error) {
    logError('审核评论失败', error);
    throw error;
  }
};

// 获取标签列表（新版本）
export const getTagsNew = async (params: { category?: string; search?: string } = {}) => {
  try {
    logInfo('获取标签列表（新版本）', { params });
    return await contentManagementService.getTags(params);
  } catch (error) {
    logError('获取标签列表失败', error);
    throw error;
  }
};

// 创建标签（新版本）
export const createTagNew = async (data: any) => {
  try {
    logInfo('创建标签（新版本）', { data });
    return await contentManagementService.createTag(data);
  } catch (error) {
    logError('创建标签失败', error);
    throw error;
  }
};

// 更新标签（新版本）
export const updateTagNew = async (id: number, data: any) => {
  try {
    logInfo('更新标签（新版本）', { id, data });
    return await contentManagementService.updateTag(id, data);
  } catch (error) {
    logError('更新标签失败', error);
    throw error;
  }
};

// 删除标签（新版本）
export const deleteTagNew = async (id: number) => {
  try {
    logInfo('删除标签（新版本）', { id });
    return await contentManagementService.deleteTag(id);
  } catch (error) {
    logError('删除标签失败', error);
    throw error;
  }
};

// 获取审核统计（新版本）
export const getModerationStatsNew = async (params: { dateFrom?: string; dateTo?: string } = {}) => {
  try {
    logInfo('获取审核统计（新版本）', { params });
    return await contentManagementService.getModerationStats(params);
  } catch (error) {
    logError('获取审核统计失败', error);
    throw error;
  }
};

// 获取审核历史（新版本）
export const getModerationHistoryNew = async (params: any = {}) => {
  try {
    logInfo('获取审核历史（新版本）', { params });
    return await contentManagementService.getModerationHistory(params);
  } catch (error) {
    logError('获取审核历史失败', error);
    throw error;
  }
};