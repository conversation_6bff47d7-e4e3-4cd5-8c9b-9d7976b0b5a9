import React, { useState, useEffect } from 'react';
import { Link, useLocation } from 'react-router-dom';
import { ChevronDown, ChevronRight } from 'lucide-react';
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip';

interface MenuItem {
  title: string;
  path?: string;
  icon?: React.ReactNode;
  children?: MenuItem[];
}

interface MenuGroup {
  title: string;
  icon?: React.ReactNode;
  items: MenuItem[];
}

interface EnhancedSidebarMenuProps {
  groups: MenuGroup[];
  role: 'reviewer' | 'admin' | 'superadmin';
  collapsed?: boolean;
}

/**
 * 增强版侧边栏菜单组件
 * 
 * 用于三个角色（审核员、管理员、超级管理员）的导航栏
 * 支持菜单分组的展开/折叠功能
 * 支持侧边栏折叠时的图标提示
 */
const EnhancedSidebarMenu: React.FC<EnhancedSidebarMenuProps> = ({ groups, role, collapsed = false }) => {
  const location = useLocation();
  
  // 从localStorage加载菜单分组折叠状态
  const [collapsedGroups, setCollapsedGroups] = useState<Record<string, boolean>>(() => {
    try {
      const savedState = localStorage.getItem(`${role}_menu_collapsed_state`);
      return savedState ? JSON.parse(savedState) : {};
    } catch (error) {
      console.error('加载菜单状态失败:', error);
      return {};
    }
  });

  // 检查当前路径是否匹配
  const isActive = (path: string) => {
    return location.pathname === path;
  };

  // 切换菜单分组折叠状态
  const toggleGroup = (groupName: string) => {
    setCollapsedGroups(prev => {
      const newState = {
        ...prev,
        [groupName]: !prev[groupName]
      };

      // 保存到localStorage
      try {
        localStorage.setItem(`${role}_menu_collapsed_state`, JSON.stringify(newState));
      } catch (error) {
        console.error('保存菜单状态失败:', error);
      }

      return newState;
    });
  };

  // 检查菜单分组是否折叠
  const isGroupCollapsed = (groupName: string) => {
    return !!collapsedGroups[groupName];
  };

  // 自动展开包含当前活动菜单项的分组
  useEffect(() => {
    // 查找当前路径所在的分组
    const currentPath = location.pathname;
    let currentGroup: string | null = null;

    // 遍历所有分组，查找包含当前路径的分组
    for (const group of groups) {
      for (const item of group.items) {
        if (item.path === currentPath) {
          currentGroup = group.title;
          break;
        }
      }
      if (currentGroup) break;
    }

    // 如果找到了当前路径所在的分组，并且该分组当前是折叠状态，则展开它
    if (currentGroup && isGroupCollapsed(currentGroup)) {
      setCollapsedGroups(prev => {
        const newState = {
          ...prev,
          [currentGroup!]: false
        };

        // 保存到localStorage
        try {
          localStorage.setItem(`${role}_menu_collapsed_state`, JSON.stringify(newState));
        } catch (error) {
          console.error('保存菜单状态失败:', error);
        }

        return newState;
      });
    }
  }, [location.pathname, groups]);

  return (
    <ul className="sidebar-menu">
      {groups.map((group, groupIndex) => (
        <React.Fragment key={`group-${groupIndex}`}>
          <li
            className={`menu-group ${isGroupCollapsed(group.title) ? 'collapsed' : ''}`}
            onClick={() => toggleGroup(group.title)}
          >
            {collapsed ? (
              <TooltipProvider>
                <Tooltip>
                  <TooltipTrigger asChild>
                    <div className="flex justify-center items-center py-2">
                      {group.icon || <ChevronRight className="h-5 w-5" />}
                    </div>
                  </TooltipTrigger>
                  <TooltipContent side="right">
                    <p>{group.title}</p>
                  </TooltipContent>
                </Tooltip>
              </TooltipProvider>
            ) : (
              <>
                <div className="flex items-center">
                  {group.icon && <span className="mr-2">{group.icon}</span>}
                  <span>{group.title}</span>
                </div>
                {isGroupCollapsed(group.title) ? (
                  <ChevronRight className="h-4 w-4 menu-icon" />
                ) : (
                  <ChevronDown className="h-4 w-4 menu-icon" />
                )}
              </>
            )}
          </li>
          {group.items.map((item, itemIndex) => (
            <li 
              key={`item-${groupIndex}-${itemIndex}`}
              className={`${isActive(item.path || '') ? 'active' : ''} ${
                isGroupCollapsed(group.title) ? 'hidden-menu-item' : ''
              }`}
            >
              {item.path ? (
                collapsed ? (
                  <TooltipProvider>
                    <Tooltip>
                      <TooltipTrigger asChild>
                        <Link to={item.path} className="flex justify-center">
                          {item.icon || <div className="w-4 h-4" />}
                        </Link>
                      </TooltipTrigger>
                      <TooltipContent side="right">
                        <p>{item.title}</p>
                      </TooltipContent>
                    </Tooltip>
                  </TooltipProvider>
                ) : (
                  <Link to={item.path}>
                    {item.icon}
                    <span>{item.title}</span>
                  </Link>
                )
              ) : (
                <div className="menu-item-no-link">
                  {item.icon}
                  {!collapsed && <span>{item.title}</span>}
                </div>
              )}
            </li>
          ))}
        </React.Fragment>
      ))}
    </ul>
  );
};

export default EnhancedSidebarMenu;
