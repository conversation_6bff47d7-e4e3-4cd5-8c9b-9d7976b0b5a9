/**
 * 内容审核路由
 */

import { Hono } from 'hono';
import { Env } from '../../types';
import { adminAuthMiddleware } from '../../middlewares/auth.middleware';
import { reviewerAuthMiddleware } from '../../middlewares/reviewerAuth.middleware';
import { permissionMiddleware, anyPermissionMiddleware } from '../../middlewares/permission.middleware';
import { riskAssessmentMiddleware } from '../../middlewares/riskAssessment.middleware';
import { anomalyDetectionMiddleware } from '../../middlewares/anomalyDetection.middleware';
import { Permission } from '../../services/permissionService';
import { AuditAction } from '../../services/auditLogService';
import * as reviewController from './review.controller';
import * as reviewSettingsController from './review-settings.controller';

// 创建路由
const reviewRouter = new Hono<{ Bindings: Env }>();

// 审核员路由（需要审核员权限）
// 获取待审核内容列表
reviewRouter.get('/pending',
  reviewerAuthMiddleware,
  permissionMiddleware(Permission.REVIEW_VIEW, 'review'),
  reviewController.getPendingContents
);

// 获取单条待审核内容详情
reviewRouter.get('/:id',
  reviewerAuthMiddleware,
  permissionMiddleware(Permission.REVIEW_VIEW, 'review'),
  reviewController.getPendingContent
);

// 审核通过内容
reviewRouter.post('/:id/approve',
  reviewerAuthMiddleware,
  permissionMiddleware(Permission.REVIEW_APPROVE, 'review'),
  reviewController.approveContent
);

// 编辑并通过内容
reviewRouter.put('/:id/edit',
  reviewerAuthMiddleware,
  permissionMiddleware(Permission.REVIEW_EDIT, 'review'),
  reviewController.editContent
);

// 拒绝内容
reviewRouter.post('/:id/reject',
  reviewerAuthMiddleware,
  permissionMiddleware(Permission.REVIEW_REJECT, 'review'),
  reviewController.rejectContent
);

// 批量操作
reviewRouter.post('/batch',
  reviewerAuthMiddleware,
  permissionMiddleware(Permission.REVIEW_BATCH, 'review'),
  riskAssessmentMiddleware(Permission.REVIEW_BATCH, 'review_batch'),
  anomalyDetectionMiddleware(AuditAction.BATCH_APPROVE, 'review_batch', {
    timeWindowMinutes: 10,
    thresholdCount: 5
  }),
  reviewController.batchOperation
);

// 管理员路由（需要管理员权限）
// 获取审核设置
reviewRouter.get('/settings',
  adminAuthMiddleware,
  permissionMiddleware(Permission.SETTINGS_VIEW, 'review_settings'),
  reviewSettingsController.getReviewSettings
);

// 更新审核设置
reviewRouter.post('/settings',
  adminAuthMiddleware,
  permissionMiddleware(Permission.SETTINGS_EDIT, 'review_settings'),
  reviewSettingsController.updateReviewSettings
);

// 获取审核统计信息
reviewRouter.get('/stats',
  adminAuthMiddleware,
  permissionMiddleware(Permission.ANALYTICS_VIEW, 'review_stats'),
  reviewSettingsController.getReviewStats
);

// 获取审核员绩效数据
reviewRouter.get('/performance',
  adminAuthMiddleware,
  anyPermissionMiddleware([Permission.ANALYTICS_VIEW, Permission.USER_VIEW], 'reviewer_performance'),
  reviewSettingsController.getReviewerPerformance
);

export default reviewRouter;
