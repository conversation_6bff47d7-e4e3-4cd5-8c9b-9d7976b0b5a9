-- 添加isTestData字段到相关表
ALTER TABLE users ADD COLUMN is_test_data BOOLEAN NOT NULL DEFAULT false;
ALTER TABLE questionnaire_responses ADD COLUMN is_test_data BOOLEAN NOT NULL DEFAULT false;
ALTER TABLE stories ADD COLUMN is_test_data BOOLEAN NOT NULL DEFAULT false;

-- 创建索引以提高查询性能
CREATE INDEX IF NOT EXISTS users_is_test_data_idx ON users(is_test_data);
CREATE INDEX IF NOT EXISTS questionnaire_responses_is_test_data_idx ON questionnaire_responses(is_test_data);
CREATE INDEX IF NOT EXISTS stories_is_test_data_idx ON stories(is_test_data);
