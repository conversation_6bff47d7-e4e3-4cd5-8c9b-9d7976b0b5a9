import React, { useState, useEffect } from 'react';
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { useToast } from '@/components/ui/use-toast';
import { useApi } from '@/hooks/useApi';
import { 
  AlertCircle, 
  CheckCircle, 
  Clock, 
  HelpCircle, 
  RefreshCw, 
  XCircle 
} from 'lucide-react';

interface ModerationFeedbackStatusProps {
  feedbackId: string;
  onClose?: () => void;
}

/**
 * 审核反馈状态组件
 */
const ModerationFeedbackStatus: React.FC<ModerationFeedbackStatusProps> = ({
  feedbackId,
  onClose,
}) => {
  const { toast } = useToast();
  const api = useApi();
  const [isLoading, setIsLoading] = useState<boolean>(true);
  const [feedback, setFeedback] = useState<any>(null);
  const [error, setError] = useState<string | null>(null);

  // 加载反馈状态
  const loadFeedbackStatus = async () => {
    setIsLoading(true);
    setError(null);

    try {
      const response = await api.get(`/feedback/moderation/${feedbackId}`);

      if (response.success) {
        setFeedback(response.feedback);
      } else {
        setError(response.error || '获取反馈状态失败');
        toast({
          title: '获取失败',
          description: response.error || '获取反馈状态失败，请稍后再试',
          variant: 'destructive',
        });
      }
    } catch (error) {
      console.error('获取反馈状态失败:', error);
      setError('服务器错误，请稍后再试');
      toast({
        title: '获取失败',
        description: '服务器错误，请稍后再试',
        variant: 'destructive',
      });
    } finally {
      setIsLoading(false);
    }
  };

  // 组件加载时获取反馈状态
  useEffect(() => {
    loadFeedbackStatus();
  }, [feedbackId]);

  // 渲染状态图标
  const renderStatusIcon = (status: string) => {
    switch (status) {
      case 'pending':
        return <Clock className="w-6 h-6 text-yellow-500" />;
      case 'reviewed':
        return <HelpCircle className="w-6 h-6 text-blue-500" />;
      case 'resolved':
        return <CheckCircle className="w-6 h-6 text-green-500" />;
      case 'rejected':
        return <XCircle className="w-6 h-6 text-red-500" />;
      default:
        return <HelpCircle className="w-6 h-6 text-gray-500" />;
    }
  };

  // 渲染状态文本
  const renderStatusText = (status: string) => {
    switch (status) {
      case 'pending':
        return '等待处理';
      case 'reviewed':
        return '已审核';
      case 'resolved':
        return '已解决';
      case 'rejected':
        return '已拒绝';
      default:
        return '未知状态';
    }
  };

  // 渲染申诉状态
  const renderAppealStatus = (appeal: any) => {
    if (!appeal) return null;

    return (
      <div className="mt-4 p-3 border rounded-md">
        <h4 className="text-sm font-medium mb-2">申诉状态</h4>
        <div className="flex items-center mb-2">
          {appeal.status === 'pending' && (
            <Clock className="w-4 h-4 text-yellow-500 mr-2" />
          )}
          {appeal.status === 'approved' && (
            <CheckCircle className="w-4 h-4 text-green-500 mr-2" />
          )}
          {appeal.status === 'rejected' && (
            <XCircle className="w-4 h-4 text-red-500 mr-2" />
          )}
          <span className="text-sm">
            {appeal.status === 'pending' && '申诉正在处理中'}
            {appeal.status === 'approved' && '申诉已通过'}
            {appeal.status === 'rejected' && '申诉已拒绝'}
          </span>
        </div>
        {appeal.result && (
          <p className="text-sm text-gray-600">{appeal.result}</p>
        )}
      </div>
    );
  };

  return (
    <Card className="w-full max-w-md mx-auto">
      <CardHeader>
        <CardTitle className="flex items-center">
          <AlertCircle className="w-5 h-5 mr-2" />
          反馈状态
        </CardTitle>
        <CardDescription>
          查看您的反馈处理状态
        </CardDescription>
      </CardHeader>
      <CardContent>
        {isLoading ? (
          <div className="flex flex-col items-center justify-center py-8">
            <RefreshCw className="w-8 h-8 animate-spin text-primary mb-4" />
            <p className="text-sm text-muted-foreground">正在加载反馈状态...</p>
          </div>
        ) : error ? (
          <div className="flex flex-col items-center justify-center py-8">
            <XCircle className="w-8 h-8 text-destructive mb-4" />
            <p className="text-sm text-destructive">{error}</p>
            <Button
              variant="outline"
              size="sm"
              className="mt-4"
              onClick={loadFeedbackStatus}
            >
              <RefreshCw className="w-4 h-4 mr-2" />
              重试
            </Button>
          </div>
        ) : feedback ? (
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <div className="flex items-center">
                {renderStatusIcon(feedback.status)}
                <div className="ml-3">
                  <h3 className="text-lg font-medium">
                    {renderStatusText(feedback.status)}
                  </h3>
                  <p className="text-sm text-muted-foreground">
                    反馈ID: {feedback.id}
                  </p>
                </div>
              </div>
              <div className="text-sm text-muted-foreground">
                {new Date(feedback.createdAt).toLocaleString()}
              </div>
            </div>

            <div className="p-3 bg-muted rounded-md">
              <h4 className="text-sm font-medium mb-1">反馈类型</h4>
              <p className="text-sm">
                {feedback.feedbackType === 'disagree' && '不同意审核结果'}
                {feedback.feedbackType === 'appeal' && '申诉'}
                {feedback.feedbackType === 'report' && '举报问题'}
                {feedback.feedbackType === 'suggestion' && '提出建议'}
              </p>
            </div>

            {feedback.response && (
              <div className="p-3 border rounded-md">
                <h4 className="text-sm font-medium mb-1">管理员回复</h4>
                <p className="text-sm">{feedback.response}</p>
              </div>
            )}

            {feedback.appeal && renderAppealStatus(feedback.appeal)}

            <div className="flex justify-end">
              <Button
                variant="outline"
                size="sm"
                onClick={loadFeedbackStatus}
                className="mr-2"
              >
                <RefreshCw className="w-4 h-4 mr-2" />
                刷新
              </Button>
            </div>
          </div>
        ) : (
          <div className="flex flex-col items-center justify-center py-8">
            <HelpCircle className="w-8 h-8 text-muted-foreground mb-4" />
            <p className="text-sm text-muted-foreground">未找到反馈信息</p>
          </div>
        )}
      </CardContent>
      <CardFooter className="flex justify-center border-t pt-4">
        {onClose && (
          <Button variant="ghost" onClick={onClose}>
            关闭
          </Button>
        )}
      </CardFooter>
    </Card>
  );
};

export default ModerationFeedbackStatus;
