const { PrismaClient } = require('@prisma/client');

const prisma = new PrismaClient();

async function createTestReviewData() {
  console.log('🚀 开始创建测试审核数据...');

  try {
    // 1. 创建测试审核员
    const reviewers = [
      {
        email: '<EMAIL>',
        username: 'reviewer1',
        name: '审核员张三',
        role: 'reviewer',
        passwordHash: 'hashed_password_123',
        isTestData: true,
        testDataVersion: '1.0',
        testDataSet: 'reviewer_test'
      },
      {
        email: '<EMAIL>',
        username: 'reviewer2',
        name: '审核员李四',
        role: 'reviewer',
        passwordHash: 'hashed_password_456',
        isTestData: true,
        testDataVersion: '1.0',
        testDataSet: 'reviewer_test'
      }
    ];

    for (const reviewer of reviewers) {
      await prisma.user.upsert({
        where: { email: reviewer.email },
        update: reviewer,
        create: reviewer
      });
    }
    console.log('✅ 创建了2个测试审核员');

    // 2. 创建待审核的故事内容
    const storyContents = [];
    for (let i = 1; i <= 10; i++) {
      storyContents.push({
        id: `story-${i}`,
        sequenceNumber: `S${String(i).padStart(5, '0')}`,
        type: 'story',
        originalContent: JSON.stringify({
          title: `测试故事 ${i}`,
          content: `这是第${i}个测试故事的内容。描述了一个毕业生的求职经历，包含了挑战、收获和感悟。这个故事希望能够为其他同学提供参考和帮助。`,
          author: i % 2 === 0 ? '匿名用户' : `用户${i}`,
          isAnonymous: i % 2 === 0,
          tags: ['本科', '计算机', '求职经历'],
          category: '就业经历'
        }),
        sanitizedContent: JSON.stringify({
          title: `测试故事 ${i}`,
          content: `这是第${i}个测试故事的内容。描述了一个毕业生的求职经历，包含了挑战、收获和感悟。这个故事希望能够为其他同学提供参考和帮助。`,
          author: i % 2 === 0 ? '匿名用户' : `用户${i}`,
          isAnonymous: i % 2 === 0,
          tags: ['本科', '计算机', '求职经历'],
          category: '就业经历'
        }),
        status: 'pending',
        priority: Math.floor(Math.random() * 3) + 1,
        originIp: '192.168.1.' + (100 + i),
        userAgent: 'Mozilla/5.0 Test Browser'
      });
    }

    for (const content of storyContents) {
      await prisma.pendingContent.upsert({
        where: { id: content.id },
        update: content,
        create: content
      });
    }
    console.log('✅ 创建了10个待审核故事');

    // 3. 创建待审核的问卷心声内容
    const voiceContents = [];
    for (let i = 1; i <= 8; i++) {
      voiceContents.push({
        id: `voice-${i}`,
        sequenceNumber: `Q${String(i).padStart(5, '0')}`,
        type: 'questionnaire',
        originalContent: JSON.stringify({
          adviceForStudents: i % 2 === 0 ? `对学弟学妹的建议 ${i}：要多实习，积累经验，提前做好职业规划。` : null,
          observationOnEmployment: i % 3 === 0 ? `就业观察 ${i}：当前就业形势比较严峻，需要做好充分准备。` : null,
          isAnonymous: i % 3 === 0,
          questionnaireId: `questionnaire-${i}`
        }),
        sanitizedContent: JSON.stringify({
          adviceForStudents: i % 2 === 0 ? `对学弟学妹的建议 ${i}：要多实习，积累经验，提前做好职业规划。` : null,
          observationOnEmployment: i % 3 === 0 ? `就业观察 ${i}：当前就业形势比较严峻，需要做好充分准备。` : null,
          isAnonymous: i % 3 === 0,
          questionnaireId: `questionnaire-${i}`
        }),
        status: 'pending',
        priority: Math.floor(Math.random() * 3) + 1,
        originIp: '192.168.1.' + (200 + i),
        userAgent: 'Mozilla/5.0 Test Browser'
      });
    }

    for (const content of voiceContents) {
      await prisma.pendingContent.upsert({
        where: { id: content.id },
        update: content,
        create: content
      });
    }
    console.log('✅ 创建了8个待审核问卷心声');

    // 4. 创建一些历史审核记录（模拟reviewer1的工作量）
    const reviewLogs = [
      // reviewer1的审核记录
      { reviewerId: 'reviewer1', contentId: 'story-old-1', action: 'approve', reviewNotes: '内容积极正面' },
      { reviewerId: 'reviewer1', contentId: 'story-old-2', action: 'approve', reviewNotes: '经验分享有价值' },
      { reviewerId: 'reviewer1', contentId: 'story-old-3', action: 'reject', reviewNotes: '内容过于简单' },
      { reviewerId: 'reviewer1', contentId: 'voice-old-1', action: 'approve', reviewNotes: '建议很实用' },
      { reviewerId: 'reviewer1', contentId: 'voice-old-2', action: 'approve', reviewNotes: '观察深刻' },

      // reviewer2的审核记录
      { reviewerId: 'reviewer2', contentId: 'story-old-4', action: 'approve', reviewNotes: '故事完整' },
      { reviewerId: 'reviewer2', contentId: 'story-old-5', action: 'reject', reviewNotes: '信息不准确' },
      { reviewerId: 'reviewer2', contentId: 'voice-old-3', action: 'approve', reviewNotes: '很好的建议' }
    ];

    // 为历史审核记录创建对应的PendingContent
    for (const log of reviewLogs) {
      const isStory = log.contentId.includes('story');
      await prisma.pendingContent.upsert({
        where: { id: log.contentId },
        update: {
          status: log.action === 'approve' ? 'approved' : 'rejected',
          reviewerId: log.reviewerId,
          reviewedAt: new Date(Date.now() - Math.random() * 7 * 24 * 60 * 60 * 1000) // 过去7天内随机时间
        },
        create: {
          id: log.contentId,
          sequenceNumber: log.contentId.toUpperCase(),
          type: isStory ? 'story' : 'questionnaire',
          originalContent: JSON.stringify({
            title: isStory ? '历史故事' : undefined,
            content: isStory ? '历史故事内容' : undefined,
            adviceForStudents: !isStory ? '历史建议内容' : undefined
          }),
          sanitizedContent: JSON.stringify({
            title: isStory ? '历史故事' : undefined,
            content: isStory ? '历史故事内容' : undefined,
            adviceForStudents: !isStory ? '历史建议内容' : undefined
          }),
          status: log.action === 'approve' ? 'approved' : 'rejected',
          reviewerId: log.reviewerId,
          reviewedAt: new Date(Date.now() - Math.random() * 7 * 24 * 60 * 60 * 1000)
        }
      });

      await prisma.reviewLog.create({
        data: {
          reviewerId: log.reviewerId,
          contentId: log.contentId,
          action: log.action,
          reviewNotes: log.reviewNotes,
          ipAddress: '*************',
          userAgent: 'Mozilla/5.0 Test Browser',
          timestamp: new Date(Date.now() - Math.random() * 7 * 24 * 60 * 60 * 1000)
        }
      });
    }
    console.log('✅ 创建了历史审核记录');

    console.log('\n🎉 测试数据创建完成！');
    console.log('📊 数据统计：');
    console.log('- 审核员: 2个');
    console.log('- 待审核故事: 10个');
    console.log('- 待审核问卷心声: 8个');
    console.log('- 历史审核记录: 8条');
    console.log('\n🔑 测试审核员账号：');
    console.log('- reviewer1 (张三)');
    console.log('- reviewer2 (李四)');

  } catch (error) {
    console.error('❌ 创建测试数据失败:', error);
  } finally {
    await prisma.$disconnect();
  }
}

// 运行脚本
createTestReviewData();
