/**
 * 角色管理控制器
 */

import { Context } from 'hono';
import { Env } from '../../types';

// 预定义的系统权限
const SYSTEM_PERMISSIONS = [
  // 仪表盘权限
  { id: 'DASHBOARD_PERSONAL', name: '个人仪表盘', description: '查看个人仪表盘', level: 'basic', group: 'dashboard' },
  { id: 'DASHBOARD_SYSTEM', name: '系统概览', description: '查看系统概览', level: 'advanced', group: 'dashboard' },
  { id: 'DASHBOARD_SECURITY', name: '安全监控', description: '查看安全监控', level: 'advanced', group: 'dashboard' },
  { id: 'DASHBOARD_ANALYTICS', name: '数据分析', description: '查看数据分析仪表盘', level: 'advanced', group: 'dashboard' },

  // 内容管理权限
  { id: 'CONTENT_VIEW', name: '内容查看', description: '查看所有内容', level: 'basic', group: 'content' },
  { id: 'CONTENT_REVIEW', name: '内容审核', description: '审核用户提交的内容', level: 'intermediate', group: 'content' },
  { id: 'STORY_REVIEW', name: '故事审核', description: '审核用户提交的故事', level: 'intermediate', group: 'content' },
  { id: 'QUICK_REVIEW', name: '快速审核', description: '使用快速审核功能', level: 'intermediate', group: 'content' },
  { id: 'TAG_MANAGEMENT', name: '标签管理', description: '管理内容标签', level: 'advanced', group: 'content' },
  { id: 'CONTENT_DELETE', name: '内容删除', description: '删除内容', level: 'advanced', group: 'content' },

  // 数据管理权限
  { id: 'QUESTIONNAIRE_VIEW', name: '问卷查看', description: '查看问卷回复', level: 'basic', group: 'data' },
  { id: 'QUESTIONNAIRE_EDIT', name: '问卷编辑', description: '编辑问卷回复', level: 'intermediate', group: 'data' },
  { id: 'DATA_ANALYSIS', name: '数据分析', description: '查看数据分析', level: 'intermediate', group: 'data' },
  { id: 'DATA_EXPORT', name: '数据导出', description: '导出数据', level: 'intermediate', group: 'data' },
  { id: 'DATA_IMPORT', name: '数据导入', description: '导入数据', level: 'advanced', group: 'data' },

  // 用户管理权限
  { id: 'USER_VIEW', name: '用户查看', description: '查看用户列表', level: 'basic', group: 'user' },
  { id: 'USER_CREATE', name: '用户创建', description: '创建新用户', level: 'intermediate', group: 'user' },
  { id: 'USER_EDIT', name: '用户编辑', description: '编辑用户信息', level: 'intermediate', group: 'user' },
  { id: 'USER_DELETE', name: '用户删除', description: '删除用户', level: 'advanced', group: 'user' },
  { id: 'USER_MANAGEMENT', name: '用户管理', description: '完整的用户管理权限', level: 'advanced', group: 'user' },
  { id: 'REVIEWER_MANAGEMENT', name: '审核员管理', description: '管理审核员', level: 'advanced', group: 'user' },
  { id: 'ADMIN_MANAGEMENT', name: '管理员管理', description: '管理管理员', level: 'critical', group: 'user' },
  { id: 'ROLE_MANAGEMENT', name: '角色管理', description: '管理角色和权限', level: 'critical', group: 'user' },

  // 系统设置权限
  { id: 'SETTINGS_PERSONAL', name: '个人设置', description: '修改个人设置', level: 'basic', group: 'system' },
  { id: 'DEIDENTIFICATION', name: '内容脱敏', description: '管理内容脱敏设置', level: 'intermediate', group: 'system' },
  { id: 'SECURITY_SETTINGS', name: '安全设置', description: '管理安全设置', level: 'advanced', group: 'system' },
  { id: 'SYSTEM_CONFIG', name: '系统配置', description: '管理系统配置', level: 'critical', group: 'system' },

  // 安全与审计权限
  { id: 'SECURITY_MONITOR', name: '安全监控', description: '查看安全监控', level: 'intermediate', group: 'security' },
  { id: 'SECURITY_LOGS', name: '安全日志', description: '查看安全日志', level: 'intermediate', group: 'security' },
  { id: 'ADMIN_AUDIT', name: '管理员审计', description: '查看管理员审计', level: 'advanced', group: 'security' },
  { id: 'LOGIN_RECORDS', name: '登录记录', description: '查看登录记录', level: 'intermediate', group: 'security' }
];

// 预定义的系统角色
const SYSTEM_ROLES = [
  {
    id: 'superadmin',
    name: '超级管理员',
    description: '拥有系统所有权限',
    isSystem: true,
    permissions: SYSTEM_PERMISSIONS.map(p => p.id)
  },
  {
    id: 'admin',
    name: '管理员',
    description: '拥有大部分管理权限，但不包括系统级别和敏感操作',
    isSystem: true,
    permissions: [
      'DASHBOARD_PERSONAL', 'DASHBOARD_SYSTEM', 'CONTENT_VIEW', 'CONTENT_REVIEW',
      'STORY_REVIEW', 'QUICK_REVIEW', 'TAG_MANAGEMENT', 'QUESTIONNAIRE_VIEW',
      'QUESTIONNAIRE_EDIT', 'DATA_ANALYSIS', 'DATA_EXPORT', 'USER_VIEW',
      'USER_MANAGEMENT', 'REVIEWER_MANAGEMENT', 'SETTINGS_PERSONAL', 'DEIDENTIFICATION'
    ]
  },
  {
    id: 'reviewer',
    name: '审核员',
    description: '负责内容审核，包括故事和问卷回复的审核',
    isSystem: true,
    permissions: [
      'DASHBOARD_PERSONAL', 'CONTENT_VIEW', 'CONTENT_REVIEW', 'STORY_REVIEW',
      'QUICK_REVIEW', 'QUESTIONNAIRE_VIEW', 'SETTINGS_PERSONAL'
    ]
  },
  {
    id: 'user',
    name: '普通用户',
    description: '基础用户权限',
    isSystem: true,
    permissions: ['DASHBOARD_PERSONAL', 'SETTINGS_PERSONAL']
  }
];

/**
 * 获取角色列表
 */
export const getRoles = async (c: Context<{ Bindings: Env }>) => {
  try {
    console.log('🔍 开始获取角色列表');

    // 获取查询参数
    const url = new URL(c.req.url);
    const search = url.searchParams.get('search');
    const page = parseInt(url.searchParams.get('page') || '1', 10);
    const pageSize = parseInt(url.searchParams.get('pageSize') || '10', 10);

    console.log('📋 查询参数:', { search, page, pageSize });

    // 模拟从数据库获取角色（实际应该从数据库获取）
    let roles = SYSTEM_ROLES.map(role => {
      const rolePermissions = role.permissions.map(permId => {
        const perm = SYSTEM_PERMISSIONS.find(p => p.id === permId);
        return perm ? { id: perm.id, name: perm.name } : { id: permId, name: permId };
      });

      return {
        ...role,
        userCount: role.id === 'user' ? 1000 : role.id === 'reviewer' ? 10 : role.id === 'admin' ? 3 : 1,
        permissions: rolePermissions
      };
    });

    // 应用搜索过滤
    if (search) {
      roles = roles.filter(role => 
        role.name.toLowerCase().includes(search.toLowerCase()) ||
        role.description.toLowerCase().includes(search.toLowerCase())
      );
    }

    // 应用分页
    const total = roles.length;
    const offset = (page - 1) * pageSize;
    const paginatedRoles = roles.slice(offset, offset + pageSize);

    console.log('📊 角色查询结果:', { total, page, pageSize });

    return c.json({
      success: true,
      data: paginatedRoles,
      pagination: {
        page,
        pageSize,
        total,
        totalPages: Math.ceil(total / pageSize)
      }
    });

  } catch (error) {
    console.error('❌ 获取角色列表失败:', error);
    return c.json({
      success: false,
      error: '获取角色列表失败',
      details: error.message
    }, 500);
  }
};

/**
 * 获取单个角色详情
 */
export const getRole = async (c: Context<{ Bindings: Env }>) => {
  try {
    const roleId = c.req.param('id');
    console.log('🔍 获取角色详情:', roleId);

    const role = SYSTEM_ROLES.find(r => r.id === roleId);

    if (!role) {
      return c.json({
        success: false,
        error: '角色不存在'
      }, 404);
    }

    const rolePermissions = role.permissions.map(permId => {
      const perm = SYSTEM_PERMISSIONS.find(p => p.id === permId);
      return perm ? { id: perm.id, name: perm.name } : { id: permId, name: permId };
    });

    const result = {
      ...role,
      userCount: role.id === 'user' ? 1000 : role.id === 'reviewer' ? 10 : role.id === 'admin' ? 3 : 1,
      permissions: rolePermissions
    };

    return c.json({
      success: true,
      data: result
    });

  } catch (error) {
    console.error('❌ 获取角色详情失败:', error);
    return c.json({
      success: false,
      error: '获取角色详情失败',
      details: error.message
    }, 500);
  }
};

/**
 * 创建角色
 */
export const createRole = async (c: Context<{ Bindings: Env }>) => {
  try {
    const body = await c.req.json();
    const { name, description, permissions = [] } = body;

    console.log('🆕 创建角色:', { name, description, permissions });

    // 验证必填字段
    if (!name || !description) {
      return c.json({
        success: false,
        error: '角色名称和描述为必填项'
      }, 400);
    }

    // 检查角色名称是否已存在
    const existingRole = SYSTEM_ROLES.find(r => r.name === name);
    if (existingRole) {
      return c.json({
        success: false,
        error: '角色名称已存在'
      }, 400);
    }

    // 验证权限是否有效
    const invalidPermissions = permissions.filter(permId => 
      !SYSTEM_PERMISSIONS.some(p => p.id === permId)
    );

    if (invalidPermissions.length > 0) {
      return c.json({
        success: false,
        error: `无效的权限: ${invalidPermissions.join(', ')}`
      }, 400);
    }

    // 生成新角色ID
    const roleId = `custom_${Date.now()}`;

    console.log('✅ 角色创建成功:', roleId);

    return c.json({
      success: true,
      data: {
        id: roleId,
        name,
        description,
        isSystem: false,
        userCount: 0,
        permissions: permissions.map(permId => {
          const perm = SYSTEM_PERMISSIONS.find(p => p.id === permId);
          return perm ? { id: perm.id, name: perm.name } : { id: permId, name: permId };
        })
      }
    });

  } catch (error) {
    console.error('❌ 创建角色失败:', error);
    return c.json({
      success: false,
      error: '创建角色失败',
      details: error.message
    }, 500);
  }
};

/**
 * 更新角色
 */
export const updateRole = async (c: Context<{ Bindings: Env }>) => {
  try {
    const roleId = c.req.param('id');
    const body = await c.req.json();
    const { name, description, permissions } = body;

    console.log('📝 更新角色:', roleId, body);

    // 检查角色是否存在
    const existingRole = SYSTEM_ROLES.find(r => r.id === roleId);
    if (!existingRole) {
      return c.json({
        success: false,
        error: '角色不存在'
      }, 404);
    }

    // 检查是否为系统角色
    if (existingRole.isSystem) {
      return c.json({
        success: false,
        error: '系统角色不可修改'
      }, 400);
    }

    console.log('✅ 角色更新成功:', roleId);

    return c.json({
      success: true,
      message: '角色更新成功'
    });

  } catch (error) {
    console.error('❌ 更新角色失败:', error);
    return c.json({
      success: false,
      error: '更新角色失败',
      details: error.message
    }, 500);
  }
};

/**
 * 删除角色
 */
export const deleteRole = async (c: Context<{ Bindings: Env }>) => {
  try {
    const roleId = c.req.param('id');
    console.log('🗑️ 删除角色:', roleId);

    // 检查角色是否存在
    const existingRole = SYSTEM_ROLES.find(r => r.id === roleId);
    if (!existingRole) {
      return c.json({
        success: false,
        error: '角色不存在'
      }, 404);
    }

    // 检查是否为系统角色
    if (existingRole.isSystem) {
      return c.json({
        success: false,
        error: '系统角色不可删除'
      }, 400);
    }

    console.log('✅ 角色删除成功:', roleId);

    return c.json({
      success: true,
      message: '角色删除成功'
    });

  } catch (error) {
    console.error('❌ 删除角色失败:', error);
    return c.json({
      success: false,
      error: '删除角色失败',
      details: error.message
    }, 500);
  }
};

/**
 * 获取所有权限
 */
export const getAllPermissions = async (c: Context<{ Bindings: Env }>) => {
  try {
    console.log('🔍 获取所有权限');

    return c.json({
      success: true,
      data: SYSTEM_PERMISSIONS
    });

  } catch (error) {
    console.error('❌ 获取权限失败:', error);
    return c.json({
      success: false,
      error: '获取权限失败',
      details: error.message
    }, 500);
  }
};
