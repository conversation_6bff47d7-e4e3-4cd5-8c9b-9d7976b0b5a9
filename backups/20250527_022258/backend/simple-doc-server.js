/**
 * 简单的文档服务器
 * 专门用于读取和提供项目文档内容
 */

const express = require('express');
const cors = require('cors');
const fs = require('fs');
const path = require('path');

const app = express();
const PORT = 8787;

// 中间件
app.use(cors({
  origin: [
    'http://localhost:5173',
    'http://localhost:5174',
    'http://localhost:5175',
    'http://localhost:5176',
    'http://localhost:5177'
  ],
  credentials: true
}));
app.use(express.json());

// 文档根目录
const DOCS_ROOT = path.join(__dirname, '..', 'docs');

// 扫描并读取真实文档
async function scanRealDocuments() {
  const documents = [];

  try {
    // 检查docs目录是否存在
    if (!fs.existsSync(DOCS_ROOT)) {
      console.log('📁 docs目录不存在，使用模拟数据');
      return getMockDocuments();
    }

    // 递归扫描文档文件
    const files = await scanDirectory(DOCS_ROOT);

    for (const file of files) {
      const doc = await createDocumentFromFile(file);
      if (doc) {
        documents.push(doc);
      }
    }

    console.log(`📚 找到 ${documents.length} 个真实文档`);
    return documents.length > 0 ? documents : getMockDocuments();
  } catch (error) {
    console.error('❌ 扫描文档失败:', error);
    return getMockDocuments();
  }
}

// 递归扫描目录
async function scanDirectory(dirPath) {
  const files = [];

  try {
    const entries = fs.readdirSync(dirPath, { withFileTypes: true });

    for (const entry of entries) {
      const fullPath = path.join(dirPath, entry.name);

      if (entry.isDirectory()) {
        // 递归扫描子目录
        const subFiles = await scanDirectory(fullPath);
        files.push(...subFiles);
      } else if (entry.isFile() && isDocumentFile(entry.name)) {
        files.push(fullPath);
      }
    }
  } catch (error) {
    console.error(`❌ 扫描目录失败: ${dirPath}`, error);
  }

  return files;
}

// 检查是否为文档文件
function isDocumentFile(filename) {
  const ext = path.extname(filename).toLowerCase();
  return ['.md', '.txt'].includes(ext);
}

// 从文件创建文档对象
async function createDocumentFromFile(filePath) {
  try {
    const stats = fs.statSync(filePath);
    const relativePath = path.relative(path.join(DOCS_ROOT, '..'), filePath);
    const filename = path.relative(DOCS_ROOT, filePath);

    // 读取文件内容
    const content = fs.readFileSync(filePath, 'utf-8');

    // 从文件内容提取元数据
    const metadata = extractMetadata(content, filename);

    const document = {
      id: generateDocumentId(relativePath),
      title: metadata.title || filename.replace(path.extname(filename), ''),
      description: metadata.description || '项目文档',
      filePath: `/${relativePath.replace(/\\/g, '/')}`,
      category: metadata.category || categorizeDocument(filename),
      type: getDocumentType(path.extname(filePath)),
      tags: Array.isArray(metadata.tags) ? metadata.tags : [],
      lastUpdated: stats.mtime.toISOString().split('T')[0],
      size: formatFileSize(stats.size),
      author: metadata.author || '项目团队',
      version: metadata.version || '1.0.0',
      status: 'published',
      accessLevel: metadata.accessLevel || 'internal',
      isLocal: true,
      createdBy: metadata.createdBy || 'augment',
      syncStatus: 'synced',
      content: content, // 完整内容
      versions: [{
        version: metadata.version || '1.0.0',
        date: stats.mtime.toISOString().split('T')[0],
        author: metadata.author || '项目团队',
        changes: '文档更新',
        filePath: `/${relativePath.replace(/\\/g, '/')}`,
        size: formatFileSize(stats.size)
      }]
    };

    return document;
  } catch (error) {
    console.error(`❌ 处理文档文件失败: ${filePath}`, error);
    return null;
  }
}

// 从文档内容提取元数据
function extractMetadata(content, filename) {
  const metadata = {};

  // 提取Markdown前置元数据
  const frontMatterMatch = content.match(/^---\n([\s\S]*?)\n---/);
  if (frontMatterMatch) {
    const frontMatter = frontMatterMatch[1];
    const lines = frontMatter.split('\n');

    for (const line of lines) {
      const [key, ...valueParts] = line.split(':');
      if (key && valueParts.length > 0) {
        const value = valueParts.join(':').trim();
        metadata[key.trim()] = value.replace(/^["']|["']$/g, '');
      }
    }
  }

  // 提取标题（如果没有前置元数据）
  if (!metadata.title) {
    const titleMatch = content.match(/^#\s+(.+)$/m);
    if (titleMatch) {
      metadata.title = titleMatch[1];
    }
  }

  // 提取描述
  if (!metadata.description) {
    const lines = content.split('\n').filter(line => line.trim());
    for (const line of lines) {
      if (!line.startsWith('#') && line.length > 20) {
        metadata.description = line.substring(0, 100);
        break;
      }
    }
  }

  // 处理标签，确保返回数组
  if (metadata.tags) {
    if (typeof metadata.tags === 'string') {
      // 如果是字符串，尝试解析
      try {
        // 尝试JSON解析
        metadata.tags = JSON.parse(metadata.tags);
      } catch (e) {
        // 如果解析失败，按逗号分割
        metadata.tags = metadata.tags
          .replace(/[\[\]]/g, '') // 移除方括号
          .split(',')
          .map(tag => tag.trim())
          .filter(tag => tag);
      }
    }
    // 确保是数组
    if (!Array.isArray(metadata.tags)) {
      metadata.tags = [];
    }
  } else {
    metadata.tags = [];
  }

  return metadata;
}

// 根据文件名分类文档
function categorizeDocument(filename) {
  const name = filename.toLowerCase();

  if (name.includes('api') || name.includes('接口')) return 'API文档';
  if (name.includes('deploy') || name.includes('部署')) return '部署文档';
  if (name.includes('test') || name.includes('测试')) return '测试文档';
  if (name.includes('user') || name.includes('用户') || name.includes('manual')) return '用户指南';
  if (name.includes('admin') || name.includes('管理')) return '用户指南';
  if (name.includes('architecture') || name.includes('架构')) return '技术文档';
  if (name.includes('security') || name.includes('安全')) return '技术文档';
  if (name.includes('readme') || name.includes('概述')) return '项目概述';
  if (name.includes('data') || name.includes('数据')) return '维护文档';
  if (name.includes('documentation') || name.includes('文档')) return '项目概述';

  return '项目概述';
}

// 获取文档类型
function getDocumentType(ext) {
  switch (ext) {
    case '.md': return 'markdown';
    case '.pdf': return 'pdf';
    case '.docx': return 'word';
    default: return 'text';
  }
}

// 格式化文件大小
function formatFileSize(bytes) {
  if (bytes < 1024) return `${bytes} B`;
  if (bytes < 1024 * 1024) return `${(bytes / 1024).toFixed(1)} KB`;
  return `${(bytes / (1024 * 1024)).toFixed(1)} MB`;
}

// 生成文档ID
function generateDocumentId(filePath) {
  return Buffer.from(filePath).toString('base64').replace(/[^a-zA-Z0-9]/g, '').substring(0, 8);
}

// 简单的Markdown到HTML转换
function convertMarkdownToHtml(markdown) {
  if (!markdown) return '';

  let html = markdown
    // 转义HTML特殊字符
    .replace(/&/g, '&amp;')
    .replace(/</g, '&lt;')
    .replace(/>/g, '&gt;')

    // 标题
    .replace(/^### (.*$)/gim, '<h3>$1</h3>')
    .replace(/^## (.*$)/gim, '<h2>$1</h2>')
    .replace(/^# (.*$)/gim, '<h1>$1</h1>')

    // 粗体和斜体
    .replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>')
    .replace(/\*(.*?)\*/g, '<em>$1</em>')

    // 代码块
    .replace(/```([\s\S]*?)```/g, '<pre><code>$1</code></pre>')
    .replace(/`(.*?)`/g, '<code>$1</code>')

    // 链接
    .replace(/\[([^\]]+)\]\(([^)]+)\)/g, '<a href="$2" target="_blank">$1</a>')

    // 列表
    .replace(/^\* (.*$)/gim, '<li>$1</li>')
    .replace(/^- (.*$)/gim, '<li>$1</li>')
    .replace(/^\d+\. (.*$)/gim, '<li>$1</li>')

    // 引用
    .replace(/^> (.*$)/gim, '<blockquote>$1</blockquote>')

    // 段落
    .replace(/\n\n/g, '</p><p>')
    .replace(/\n/g, '<br>');

  // 包装列表项
  html = html.replace(/(<li>.*<\/li>)/gs, '<ul>$1</ul>');

  // 包装段落
  if (html && !html.startsWith('<h') && !html.startsWith('<ul') && !html.startsWith('<pre')) {
    html = '<p>' + html + '</p>';
  }

  return html;
}

// 获取模拟文档数据
function getMockDocuments() {
  return [
    {
      id: '1',
      title: '项目文档管理规则',
      description: '定义项目文档管理的规则、流程和标准',
      filePath: '/docs/documentation-management-rules.md',
      category: '项目概述',
      type: 'markdown',
      tags: ['文档', '规则', '流程'],
      lastUpdated: '2024-05-23',
      size: '12.8 KB',
      author: '项目团队',
      version: '1.0.0',
      status: 'published',
      accessLevel: 'internal',
      isLocal: true,
      createdBy: 'augment',
      syncStatus: 'synced',
      content: '# 项目文档管理规则\n\n这是模拟的文档内容，因为无法读取真实文件。\n\n## 主要内容\n\n1. 文档管理流程\n2. 版本控制规范\n3. 访问权限设置\n\n请确保后端服务正常运行以获取真实文档内容。',
      versions: [{
        version: '1.0.0',
        date: '2024-05-23',
        author: '项目团队',
        changes: '初始版本',
        filePath: '/docs/documentation-management-rules.md',
        size: '12.8 KB'
      }]
    }
  ];
}

// API路由
app.get('/health', (req, res) => {
  res.json({
    status: 'ok',
    environment: 'development',
    timestamp: new Date().toISOString(),
    service: 'document-server'
  });
});

app.get('/api/documentation/documents', async (req, res) => {
  try {
    const documents = await scanRealDocuments();
    const syncStatus = {
      lastSync: new Date().toISOString().slice(0, 19).replace('T', ' '),
      status: 'success',
      totalDocs: documents.length,
      syncedDocs: documents.length,
      errorDocs: 0
    };

    res.json({
      success: true,
      data: documents,
      syncStatus,
      fallback: documents.length === 1 && documents[0].id === '1' // 检测是否为模拟数据
    });
  } catch (error) {
    console.error('❌ 获取文档列表失败:', error);
    res.status(500).json({ success: false, error: error.message });
  }
});

app.get('/api/documentation/documents/:id', async (req, res) => {
  try {
    const { id } = req.params;
    const documents = await scanRealDocuments();
    const document = documents.find(doc => doc.id === id);

    if (!document) {
      return res.status(404).json({ success: false, error: '文档不存在' });
    }

    res.json({ success: true, data: document });
  } catch (error) {
    console.error('❌ 获取文档详情失败:', error);
    res.status(500).json({ success: false, error: error.message });
  }
});

app.post('/api/documentation/sync', async (req, res) => {
  try {
    const documents = await scanRealDocuments();
    const syncStatus = {
      lastSync: new Date().toISOString().slice(0, 19).replace('T', ' '),
      status: 'success',
      totalDocs: documents.length,
      syncedDocs: documents.length,
      errorDocs: 0
    };

    res.json({
      success: true,
      message: '文档同步完成',
      syncStatus,
      documentsCount: documents.length
    });
  } catch (error) {
    console.error('❌ 文档同步失败:', error);
    res.status(500).json({ success: false, error: error.message });
  }
});

// 在新窗口中查看文档的HTML页面
app.get('/view/:id', async (req, res) => {
  try {
    const { id } = req.params;
    const documents = await scanRealDocuments();
    const document = documents.find(doc => doc.id === id);

    if (!document) {
      return res.status(404).send(`
        <!DOCTYPE html>
        <html>
        <head>
          <meta charset="utf-8">
          <title>文档未找到</title>
          <style>
            body { font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif; padding: 40px; text-align: center; }
            .error { color: #dc3545; }
          </style>
        </head>
        <body>
          <h1 class="error">文档未找到</h1>
          <p>请求的文档不存在或已被删除。</p>
          <a href="javascript:window.close()">关闭窗口</a>
        </body>
        </html>
      `);
    }

    // 将Markdown转换为HTML（简单处理）
    const htmlContent = convertMarkdownToHtml(document.content);

    const html = `
      <!DOCTYPE html>
      <html>
      <head>
        <meta charset="utf-8">
        <title>${document.title}</title>
        <style>
          body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            line-height: 1.6;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            color: #333;
          }
          .header {
            border-bottom: 1px solid #eee;
            padding-bottom: 20px;
            margin-bottom: 30px;
          }
          .title {
            margin: 0 0 10px 0;
            color: #2c3e50;
          }
          .meta {
            color: #666;
            font-size: 14px;
          }
          .badge {
            display: inline-block;
            padding: 2px 8px;
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 4px;
            font-size: 12px;
            margin-right: 5px;
          }
          .content {
            line-height: 1.8;
          }
          .content h1, .content h2, .content h3 {
            color: #2c3e50;
            margin-top: 30px;
            margin-bottom: 15px;
          }
          .content h1 { font-size: 28px; }
          .content h2 { font-size: 24px; }
          .content h3 { font-size: 20px; }
          .content p { margin-bottom: 15px; }
          .content code {
            background: #f8f9fa;
            padding: 2px 4px;
            border-radius: 3px;
            font-family: 'Monaco', 'Consolas', monospace;
          }
          .content pre {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 5px;
            overflow-x: auto;
            border-left: 4px solid #007bff;
          }
          .content blockquote {
            border-left: 4px solid #ddd;
            margin: 0;
            padding-left: 20px;
            color: #666;
          }
          .content ul, .content ol {
            padding-left: 30px;
          }
          .content li {
            margin-bottom: 5px;
          }
          .actions {
            position: fixed;
            top: 20px;
            right: 20px;
            background: white;
            padding: 10px;
            border-radius: 5px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
          }
          .btn {
            display: inline-block;
            padding: 8px 16px;
            background: #007bff;
            color: white;
            text-decoration: none;
            border-radius: 4px;
            font-size: 14px;
            margin-left: 5px;
          }
          .btn:hover {
            background: #0056b3;
          }
          .btn-secondary {
            background: #6c757d;
          }
          .btn-secondary:hover {
            background: #545b62;
          }
        </style>
      </head>
      <body>
        <div class="actions">
          <a href="#" onclick="window.print()" class="btn btn-secondary">打印</a>
          <a href="#" onclick="copyDocument()" class="btn btn-secondary">复制</a>
          <a href="#" onclick="downloadDocument()" class="btn">下载</a>
        </div>

        <div class="header">
          <h1 class="title">${document.title}</h1>
          <div class="meta">
            <span class="badge">${document.category}</span>
            <span class="badge">作者: ${document.author}</span>
            <span class="badge">版本: ${document.version}</span>
            <span class="badge">更新: ${document.lastUpdated}</span>
            <span class="badge">大小: ${document.size}</span>
          </div>
          ${document.description ? `<p style="margin-top: 15px; color: #666;">${document.description}</p>` : ''}
        </div>

        <div class="content">
          ${htmlContent}
        </div>

        <script>
          function downloadDocument() {
            const content = ${JSON.stringify(document.content)};
            const blob = new Blob([content], { type: 'text/markdown' });
            const url = URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = '${document.title}.md';
            document.body.appendChild(a);
            a.click();
            document.body.removeChild(a);
            URL.revokeObjectURL(url);
          }

          function copyDocument() {
            const content = ${JSON.stringify(document.content)};

            // 使用现代的 Clipboard API
            if (navigator.clipboard && window.isSecureContext) {
              navigator.clipboard.writeText(content).then(function() {
                showCopySuccess();
              }).catch(function(err) {
                console.error('复制失败:', err);
                fallbackCopy(content);
              });
            } else {
              // 回退到传统方法
              fallbackCopy(content);
            }
          }

          function fallbackCopy(content) {
            // 创建临时文本区域
            const textArea = document.createElement('textarea');
            textArea.value = content;
            textArea.style.position = 'fixed';
            textArea.style.left = '-999999px';
            textArea.style.top = '-999999px';
            document.body.appendChild(textArea);

            // 选择并复制
            textArea.focus();
            textArea.select();

            try {
              const successful = document.execCommand('copy');
              if (successful) {
                showCopySuccess();
              } else {
                showCopyError();
              }
            } catch (err) {
              console.error('复制失败:', err);
              showCopyError();
            }

            document.body.removeChild(textArea);
          }

          function showCopySuccess() {
            // 创建成功提示
            const toast = document.createElement('div');
            toast.innerHTML = '✅ 文档内容已复制到剪贴板';
            toast.style.cssText = \`
              position: fixed;
              top: 20px;
              left: 50%;
              transform: translateX(-50%);
              background: #10b981;
              color: white;
              padding: 12px 24px;
              border-radius: 6px;
              font-size: 14px;
              z-index: 10000;
              box-shadow: 0 4px 12px rgba(0,0,0,0.15);
            \`;

            document.body.appendChild(toast);

            // 3秒后自动移除
            setTimeout(() => {
              if (toast.parentNode) {
                document.body.removeChild(toast);
              }
            }, 3000);
          }

          function showCopyError() {
            // 创建错误提示
            const toast = document.createElement('div');
            toast.innerHTML = '❌ 复制失败，请手动选择文本复制';
            toast.style.cssText = \`
              position: fixed;
              top: 20px;
              left: 50%;
              transform: translateX(-50%);
              background: #ef4444;
              color: white;
              padding: 12px 24px;
              border-radius: 6px;
              font-size: 14px;
              z-index: 10000;
              box-shadow: 0 4px 12px rgba(0,0,0,0.15);
            \`;

            document.body.appendChild(toast);

            // 5秒后自动移除
            setTimeout(() => {
              if (toast.parentNode) {
                document.body.removeChild(toast);
              }
            }, 5000);
          }
        </script>
      </body>
      </html>
    `;

    res.send(html);
  } catch (error) {
    console.error('❌ 查看文档失败:', error);
    res.status(500).send(`
      <!DOCTYPE html>
      <html>
      <head>
        <meta charset="utf-8">
        <title>服务器错误</title>
        <style>
          body { font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif; padding: 40px; text-align: center; }
          .error { color: #dc3545; }
        </style>
      </head>
      <body>
        <h1 class="error">服务器错误</h1>
        <p>加载文档时发生错误，请稍后再试。</p>
        <a href="javascript:window.close()">关闭窗口</a>
      </body>
      </html>
    `);
  }
});

// 启动服务器
app.listen(PORT, () => {
  console.log(`🚀 文档服务器运行在 http://localhost:${PORT}`);
  console.log(`📚 文档API: http://localhost:${PORT}/api/documentation/documents`);
  console.log(`🏥 健康检查: http://localhost:${PORT}/health`);
  console.log(`📁 文档目录: ${DOCS_ROOT}`);

  // 检查文档目录
  if (fs.existsSync(DOCS_ROOT)) {
    console.log(`✅ 文档目录存在`);
  } else {
    console.log(`⚠️ 文档目录不存在，将使用模拟数据`);
  }
});

module.exports = app;
