import { Context } from 'hono';
import { PrismaClient } from '@prisma/client';
import { z } from 'zod';
import { zValidator } from '@hono/zod-validator';
import { rateLimit } from '../../middlewares/rateLimit';
import { filterSensitiveWords } from '../../utils/sensitiveWordFilter';
import { getMockStoryList, getMockStoryDetail } from '../../services/mockDataService';
import { KVStorageService } from '../../services/kvStorageService';
import { DeidentificationService } from '../../services/deidentificationService';
import config, { shouldUseMockData } from '../../config';
import { SecurityModule } from '../../security';
import { generateUUID, isValidA, isValidB } from '../../utils/uuidGenerator';

// Define environment interface
interface Env {
  SURVEY_KV: KVNamespace;
  DATABASE_URL: string;
  UUID_SALT?: string;
  ENVIRONMENT?: string;
}

// Initialize Prisma client
const prisma = new PrismaClient();

// Define validation schema for story submission
const storySubmitSchema = z.object({
  title: z.string().min(5, { message: '标题至少需要5个字符' }).max(50, { message: '标题最多50个字符' }),
  content: z.string().min(20, { message: '内容至少需要20个字符' }).max(2000, { message: '内容最多2000个字符' }),
  isAnonymous: z.boolean().default(true),
  emailVerificationId: z.string().optional(),
  tags: z.array(z.string()).optional(),
  category: z.string().optional(),
  educationLevel: z.string().optional(),
  industry: z.string().optional(),
  // 轻量级匿名身份验证字段（可选）
  identityA: z.string().regex(/^\d{11}$/, { message: 'A必须是11位数字' }).optional(),
  identityB: z.string().regex(/^\d{4}$|^\d{6}$/, { message: 'B必须是4位或6位数字' }).optional(),
});

// Define validation schema for story voting
const storyVoteSchema = z.object({
  storyId: z.number(),
  voteType: z.enum(['like', 'dislike']),
});

/**
 * 生成序列号
 * @param prefix 前缀
 * @returns 序列号
 */
function generateSequenceNumber(prefix: string): string {
  const year = new Date().getFullYear().toString().substring(2);
  const month = (new Date().getMonth() + 1).toString().padStart(2, '0');
  const day = new Date().getDate().toString().padStart(2, '0');
  const random = Math.floor(Math.random() * 10000).toString().padStart(4, '0');

  return `${prefix}${year}${month}${day}${random}`;
}

/**
 * 计算优先级
 * @param autoModeration 自动审核结果
 * @returns 优先级
 */
function calculatePriority(autoModeration: any): number {
  // 基础优先级
  let priority = 1;

  // 根据置信度调整优先级
  if (autoModeration.confidence < 0.6) {
    priority += 1; // 低置信度需要更高优先级
  }

  // 根据严重程度调整优先级
  if (autoModeration.severity === 'high') {
    priority += 2;
  } else if (autoModeration.severity === 'medium') {
    priority += 1;
  }

  // 确保优先级在合理范围内
  return Math.min(Math.max(priority, 1), 5);
}

// Submit story
export const submitStory = [
  rateLimit({ limit: 1, window: 60 }), // 1 request per minute
  SecurityModule.createMiddleware(), // 安全中间件
  zValidator('json', storySubmitSchema),
  async (c: Context<{ Bindings: Env }>) => {
    const data = c.req.valid('json');
    const ip = c.req.header('CF-Connecting-IP') || c.req.header('X-Forwarded-For') || 'unknown';

    // 获取请求 ID
    const requestId = c.get('requestId') || crypto.randomUUID();

    // 检查是否是静默拒绝
    if (c.get('silentRejection') === true) {
      // 记录安全事件
      await SecurityModule.log(
        'warn',
        'Silent rejection of story submission',
        { clientIp: ip },
        ip,
        c.env,
        {
          requestId,
          errorCode: 'HONEYPOT_TRIGGERED',
          severity: 'high',
          source: 'storyController',
          tags: ['story', 'honeypot', 'silentRejection']
        }
      );

      // 返回成功响应，但不实际处理数据
      return c.json({
        success: true,
        message: 'Story submitted successfully',
        storyId: Math.floor(Math.random() * 1000000)
      });
    }

    try {
      // Check if this is a verified submission
      let userId = null;

      if (data.emailVerificationId && !data.isAnonymous) {
        // Get verification data from KV
        const verification = await c.env.SURVEY_KV.get(
          `verification:${data.emailVerificationId}`,
          'json'
        ) as { userId: number, verified: boolean, email: string } | null;

        if (verification && verification.verified) {
          userId = verification.userId;
        }
      }

      // 在模拟数据模式下，简化处理流程
      let title = data.title;
      let content = data.content;

      // 如果不是模拟数据模式，才进行敏感词过滤和脱敏处理
      if (!config.mockData.enabled) {
        // Filter sensitive words
        const filteredTitle = await filterSensitiveWords(data.title);
        const filteredContent = await filterSensitiveWords(data.content);

        // Check if content contains sensitive words
        if (filteredTitle !== data.title || filteredContent !== data.content) {
          return c.json({
            success: false,
            error: '内容包含敏感词，请修改后重新提交',
          }, 400);
        }

        // 应用内容脱敏处理
        const deidentificationConfig = DeidentificationService.getConfig();

        if (deidentificationConfig.enabled && deidentificationConfig.applyToStories) {
          console.log('Applying de-identification to story content');

          // 脱敏标题
          const titleResult = await DeidentificationService.sanitizeContent(
            title,
            'story',
            c.env
          );

          // 脱敏内容
          const contentResult = await DeidentificationService.sanitizeContent(
            content,
            'story',
            c.env
          );

          // 更新标题和内容
          title = titleResult.sanitized;
          content = contentResult.sanitized;

          // 记录脱敏操作
          if (titleResult.modified || contentResult.modified) {
            console.log('Content was de-identified');
          }
        }
      }

      // Build content with metadata
      let contentWithMetadata = content;

      // Add tags if provided
      if (data.tags && data.tags.length > 0) {
        contentWithMetadata += `\n\n标签: ${data.tags.join(', ')}`;
      }

      // Add category if provided
      if (data.category) {
        contentWithMetadata += `\n\n分类: ${data.category}`;
      }

      // Add education level if provided
      if (data.educationLevel) {
        contentWithMetadata += `\n\n学历: ${data.educationLevel}`;
      }

      // Add industry if provided
      if (data.industry) {
        contentWithMetadata += `\n\n行业: ${data.industry}`;
      }

      // 处理轻量级匿名身份验证
      let submittedById = null;
      if (data.identityA && data.identityB) {
        try {
          // 生成UUID，如果没有设置UUID_SALT则使用默认值
          const uuidSalt = c.env.UUID_SALT || 'default-uuid-salt-2024';
          submittedById = generateUUID(data.identityA, data.identityB, uuidSalt);
          console.log(`Generated UUID for anonymous identity: ${submittedById.substring(0, 8)}...`);
        } catch (error) {
          console.error('Error generating UUID:', error);
          // 继续处理，但不设置submittedById
        }
      }

      // 检测是否为测试数据生成器请求
      const isTestDataGenerator = data._testData === true ||
                                   c.req.header('X-Test-Data-Generator') === 'true' ||
                                   c.req.header('User-Agent')?.includes('TestDataGenerator');

      // 检查是否使用模拟数据
      if (shouldUseMockData({ isTestDataGenerator })) {
        console.log('Mock data enabled: Saving story to KV storage');

        // 获取当前故事列表
        const storiesKey = 'story_list';
        const storiesJson = await c.env.SURVEY_KV.get(storiesKey);
        const stories = storiesJson ? JSON.parse(storiesJson) : [];

        // 创建新故事
        const newStory = {
          id: stories.length + 1,
          userId: userId,
          title: title,
          content: contentWithMetadata,
          isAnonymous: data.isAnonymous,
          ipAddress: ip,
          userAgent: c.req.header('User-Agent') || 'unknown',
          submittedById, // 添加轻量级匿名身份标识
          status: 'approved', // 模拟数据直接通过审核
          likes: 0,
          dislikes: 0,
          createdAt: new Date().toISOString(),
          tags: data.tags || [],
          category: data.category || 'experience',
          educationLevel: data.educationLevel || 'bachelor',
          industry: data.industry || 'it'
        };

        // 添加到故事列表
        stories.push(newStory);

        // 保存故事列表
        await c.env.SURVEY_KV.put(storiesKey, JSON.stringify(stories));

        // 保存故事详情
        await c.env.SURVEY_KV.put(`story:${newStory.id}`, JSON.stringify(newStory));

        return c.json({
          success: true,
          message: '故事已发布',
          storyId: newStory.id
        });
      }

      // 非模拟数据模式的处理（使用Prisma和审核服务）
      // 获取审核服务实例
      const { ReviewService, ContentType } = await import('../../services/reviewService');
      const reviewService = ReviewService.getInstance();

      // 确保审核服务已初始化
      if (!reviewService.isInitialized) {
        await reviewService.initialize();
      }

      // 构建故事内容对象
      const storyContent = {
        userId,
        isAnonymous: data.isAnonymous,
        title: title, // 使用脱敏后的标题
        content: contentWithMetadata, // 使用脱敏后的内容
        ipAddress: ip,
        userAgent: c.req.header('User-Agent') || 'unknown',
        submittedById, // 添加轻量级匿名身份标识
        metadata: {
          // 不存储原始A/B值，只存储是否使用了匿名身份验证
          hasAnonymousIdentity: !!submittedById
        },
        _security: data._security // 传递安全数据
      };

      // 检查是否有待审核标记（来自异步审核中间件）
      if (data._pendingModeration) {
        return c.json({
          success: true,
          message: '故事已提交，正在审核中',
          pendingId: data._pendingModeration.id,
          sequenceNumber: data._pendingModeration.sequenceNumber
        });
      }

      // 检查是否有自动审核结果
      if (data._autoModeration) {
        if (data._autoModeration.action === 'approve') {
          // 内容已自动通过，直接创建故事记录
          const story = await prisma.story.create({
            data: {
              userId: storyContent.userId,
              title: storyContent.title,
              content: storyContent.content,
              isAnonymous: storyContent.isAnonymous || false,
              ipAddress: storyContent.ipAddress,
              userAgent: storyContent.userAgent,
              status: 'approved'
            }
          });

          return c.json({
            success: true,
            message: '故事已发布',
            storyId: story.id
          });
        } else if (data._autoModeration.action === 'review') {
          // 内容需要人工审核，创建待审核记录
          const pendingContent = await prisma.pendingContent.create({
            data: {
              sequenceNumber: generateSequenceNumber('STORY'),
              type: ContentType.STORY,
              originalContent: JSON.stringify(storyContent),
              sanitizedContent: JSON.stringify(storyContent),
              status: 'pending',
              originIp: storyContent.ipAddress,
              userAgent: storyContent.userAgent,
              flags: data._autoModeration.issues?.join(',') || '',
              priority: calculatePriority(data._autoModeration),
              aiSuggestion: 'review',
              aiConfidence: data._autoModeration.confidence,
              aiExplanation: data._autoModeration.explanation
            }
          });

          return c.json({
            success: true,
            message: '故事已提交，等待审核',
            pendingId: pendingContent.id,
            sequenceNumber: pendingContent.sequenceNumber
          });
        }
      }

      // 如果没有自动审核结果，使用传统审核流程
      const result = await reviewService.processContent(storyContent, ContentType.STORY, c.env);

      return c.json(result);
    } catch (error) {
      console.error('Error submitting story:', error);

      // 获取请求 ID
      const requestId = c.get('requestId') || crypto.randomUUID();

      // 记录错误
      await SecurityModule.logError(
        error instanceof Error ? error : new Error(String(error)),
        ip,
        c.env,
        {
          requestId,
          source: 'storyController',
          tags: ['story', 'error']
        }
      );

      return c.json({
        success: false,
        error: 'Failed to submit story',
        code: 'SUBMISSION_ERROR'
      }, 500);
    }
  }
];

// Vote on story
export const voteStory = [
  rateLimit({ limit: 20, window: 60 * 60 }), // 20 requests per hour
  zValidator('json', storyVoteSchema),
  async (c: Context<{ Bindings: Env }>) => {
    const data = c.req.valid('json');
    const ip = c.req.header('CF-Connecting-IP') || c.req.header('X-Forwarded-For') || 'unknown';

    try {
      // Check if user has already voted on this story
      const existingVote = await prisma.vote.findFirst({
        where: {
          storyId: data.storyId,
          ipAddress: ip,
        },
      });

      // Get the story
      const story = await prisma.story.findUnique({
        where: {
          id: data.storyId,
        },
      });

      if (!story) {
        return c.json({ success: false, error: 'Story not found' }, 404);
      }

      // Check if user has already voted today
      const today = new Date();
      today.setHours(0, 0, 0, 0);

      const votesToday = await prisma.vote.count({
        where: {
          ipAddress: ip,
          createdAt: {
            gte: today,
          },
        },
      });

      if (votesToday >= 10) {
        return c.json({
          success: false,
          error: '您今天的投票次数已达上限，请明天再试'
        }, 429);
      }

      // Handle vote
      if (existingVote) {
        // User has already voted, update the vote
        if (existingVote.voteType === data.voteType) {
          // User is voting the same way, do nothing
          return c.json({
            success: true,
            message: 'Vote already recorded',
            likes: story.likes,
            dislikes: story.dislikes,
          });
        } else {
          // User is changing their vote
          await prisma.vote.update({
            where: {
              id: existingVote.id,
            },
            data: {
              voteType: data.voteType,
            },
          });

          // Update story vote counts
          const likes = data.voteType === 'like'
            ? story.likes + 1
            : story.likes - 1;

          const dislikes = data.voteType === 'dislike'
            ? story.dislikes + 1
            : story.dislikes - 1;

          await prisma.story.update({
            where: {
              id: data.storyId,
            },
            data: {
              likes: Math.max(0, likes),
              dislikes: Math.max(0, dislikes),
            },
          });

          return c.json({
            success: true,
            message: 'Vote updated',
            likes: Math.max(0, likes),
            dislikes: Math.max(0, dislikes),
          });
        }
      } else {
        // User has not voted yet, create a new vote
        await prisma.vote.create({
          data: {
            storyId: data.storyId,
            ipAddress: ip,
            voteType: data.voteType,
          },
        });

        // Update story vote counts
        const likes = data.voteType === 'like'
          ? story.likes + 1
          : story.likes;

        const dislikes = data.voteType === 'dislike'
          ? story.dislikes + 1
          : story.dislikes;

        await prisma.story.update({
          where: {
            id: data.storyId,
          },
          data: {
            likes,
            dislikes,
          },
        });

        return c.json({
          success: true,
          message: 'Vote recorded',
          likes,
          dislikes,
        });
      }
    } catch (error) {
      console.error('Error voting on story:', error);
      return c.json({ success: false, error: 'Failed to vote on story' }, 500);
    }
  }
];

// Get story detail
export const getStoryDetail = async (c: Context<{ Bindings: Env }>) => {
  try {
    const storyId = parseInt(c.req.param('id'));

    if (isNaN(storyId)) {
      return c.json({ success: false, error: 'Invalid story ID' }, 400);
    }

    // 检查是否使用模拟数据（故事详情API默认使用真实数据库）
    if (shouldUseMockData()) {
      console.log(`Mock data enabled: Returning story detail from KV storage for ID ${storyId}`);

      // 使用 KV 存储服务
      const kvStorage = new KVStorageService(c.env);
      const story = await kvStorage.getStory(storyId);

      if (!story) {
        return c.json({ success: false, error: 'Story not found' }, 404);
      }

      return c.json({
        success: true,
        story,
      });
    }

    // Get story
    const story = await prisma.story.findUnique({
      where: {
        id: storyId,
        status: 'approved', // Only show approved stories
      },
      select: {
        id: true,
        title: true,
        content: true,
        isAnonymous: true,
        likes: true,
        dislikes: true,
        createdAt: true,
        user: {
          select: {
            id: true,
            email: true,
          },
        },
      },
    });

    if (!story) {
      // 在开发环境中，如果找不到故事，返回模拟数据
      if (c.env.ENVIRONMENT === 'development') {
        console.log(`Development mode: Story not found, returning mock data for ID ${storyId}`);

        // 模拟数据
        const mockStory = {
          id: storyId,
          title: '我的求职经历与感悟',
          content: '毕业后我经历了三个月的求职期，投了上百份简历，参加了二十多场面试，最终找到了一份满意的工作。期间有很多挫折和困难，但也学到了很多东西。\n\n首先，求职前的准备非常重要。包括完善简历、准备自我介绍、熟悉面试常见问题等。简历要突出自己的优势和特长，针对不同的岗位可以做适当调整。\n\n其次，面试技巧也很关键。面试前要了解公司背景和岗位要求，面试中要保持自信，清晰表达自己的想法，展示自己的能力和潜力。\n\n最后，心态很重要。求职过程中难免会遇到挫折和拒绝，要保持积极心态，从失败中吸取经验教训，不断调整和改进。',
          author: '匿名用户',
          createdAt: new Date(Date.now() - 7 * 86400000).toISOString(),
          likes: 42,
          dislikes: 5,
          tags: ['job-hunting', 'interview', 'advice'],
          category: 'experience',
          educationLevel: 'bachelor',
          industry: 'it',
        };

        return c.json({
          success: true,
          story: mockStory,
        });
      }

      return c.json({ success: false, error: 'Story not found' }, 404);
    }

    // Extract tags from content
    const tagsMatch = story.content.match(/标签: (.*?)$/m);
    const tags = tagsMatch
      ? tagsMatch[1].split(', ').map(tag => tag.trim())
      : [];

    // Extract category from content if exists
    const categoryMatch = story.content.match(/分类: (.*?)$/m);
    const category = categoryMatch ? categoryMatch[1].trim() : null;

    // Extract education level from content if exists
    const educationLevelMatch = story.content.match(/学历: (.*?)$/m);
    const educationLevel = educationLevelMatch ? educationLevelMatch[1].trim() : null;

    // Extract industry from content if exists
    const industryMatch = story.content.match(/行业: (.*?)$/m);
    const industry = industryMatch ? industryMatch[1].trim() : null;

    // Remove metadata from content
    let content = story.content
      .replace(/\n\n标签: .*?$/m, '')
      .replace(/\n\n分类: .*?$/m, '')
      .replace(/\n\n学历: .*?$/m, '')
      .replace(/\n\n行业: .*?$/m, '');

    return c.json({
      success: true,
      story: {
        id: story.id,
        title: story.title,
        content,
        author: story.isAnonymous ? '匿名用户' : '已验证用户',
        createdAt: story.createdAt,
        likes: story.likes,
        dislikes: story.dislikes,
        tags,
        category,
        educationLevel,
        industry,
      },
    });
  } catch (error) {
    console.error('Error getting story detail:', error);

    // 在开发环境中，即使出错也返回模拟数据
    if (c.env.ENVIRONMENT === 'development') {
      console.log(`Development mode: Error, returning mock data for ID ${c.req.param('id')}`);

      const storyId = parseInt(c.req.param('id'));

      // 模拟数据
      const mockStory = {
        id: storyId,
        title: '我的求职经历与感悟',
        content: '毕业后我经历了三个月的求职期，投了上百份简历，参加了二十多场面试，最终找到了一份满意的工作。期间有很多挫折和困难，但也学到了很多东西。\n\n首先，求职前的准备非常重要。包括完善简历、准备自我介绍、熟悉面试常见问题等。简历要突出自己的优势和特长，针对不同的岗位可以做适当调整。\n\n其次，面试技巧也很关键。面试前要了解公司背景和岗位要求，面试中要保持自信，清晰表达自己的想法，展示自己的能力和潜力。\n\n最后，心态很重要。求职过程中难免会遇到挫折和拒绝，要保持积极心态，从失败中吸取经验教训，不断调整和改进。',
        author: '匿名用户',
        createdAt: new Date(Date.now() - 7 * 86400000).toISOString(),
        likes: 42,
        dislikes: 5,
        tags: ['job-hunting', 'interview', 'advice'],
        category: 'experience',
        educationLevel: 'bachelor',
        industry: 'it',
      };

      return c.json({
        success: true,
        story: mockStory,
      });
    }

    return c.json({ success: false, error: 'Failed to get story detail' }, 500);
  }
};

// Get story list
export const getStoryList = async (c: Context<{ Bindings: Env }>) => {
  try {
    // 检查是否使用模拟数据（故事列表API默认使用真实数据库）
    if (shouldUseMockData()) {
      console.log('Mock data enabled: Returning story list from KV storage');

      // 获取查询参数
      const page = parseInt(c.req.query('page') || '1');
      const pageSize = parseInt(c.req.query('pageSize') || '6');
      const sortBy = c.req.query('sortBy') || 'latest';
      const tag = c.req.query('tag');
      const category = c.req.query('category');
      const search = c.req.query('search');
      const educationLevel = c.req.query('educationLevel');
      const industry = c.req.query('industry');

      // 使用 KV 存储服务
      const kvStorage = new KVStorageService(c.env);
      let storyList = await kvStorage.getStoryList();

      // 只显示已审核的故事
      storyList = storyList.filter(story => story.status === 'approved');

      // 应用筛选条件
      if (tag) {
        storyList = storyList.filter(story => story.tags.includes(tag));
      }

      if (category) {
        storyList = storyList.filter(story => story.category === category);
      }

      if (educationLevel) {
        storyList = storyList.filter(story => story.educationLevel === educationLevel);
      }

      if (industry) {
        storyList = storyList.filter(story => story.industry === industry);
      }

      if (search) {
        const searchLower = search.toLowerCase();
        storyList = storyList.filter(story =>
          story.title.toLowerCase().includes(searchLower) ||
          story.content.toLowerCase().includes(searchLower)
        );
      }

      // 应用排序
      if (sortBy === 'latest') {
        storyList.sort((a, b) => new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime());
      } else if (sortBy === 'popular') {
        storyList.sort((a, b) => b.likes - a.likes);
      }

      // 计算分页
      const totalCount = storyList.length;
      const totalPages = Math.ceil(totalCount / pageSize);
      const startIndex = (page - 1) * pageSize;
      const endIndex = startIndex + pageSize;
      const paginatedStories = storyList.slice(startIndex, endIndex);

      // 获取热门标签
      const allTags: string[] = [];
      storyList.forEach(story => {
        allTags.push(...story.tags);
      });

      // 计算标签出现次数
      const tagCounts: Record<string, number> = {};
      allTags.forEach(tag => {
        tagCounts[tag] = (tagCounts[tag] || 0) + 1;
      });

      // 转换为数组并按出现次数排序
      const popularTags = Object.entries(tagCounts)
        .map(([tag, count]) => ({ tag, count }))
        .sort((a, b) => b.count - a.count)
        .slice(0, 10); // 获取前10个标签

      return c.json({
        success: true,
        stories: paginatedStories,
        totalPages,
        currentPage: page,
        popularTags,
      });
    }
    // Get query parameters
    const page = parseInt(c.req.query('page') || '1');
    const pageSize = parseInt(c.req.query('pageSize') || '6');
    const sortBy = c.req.query('sortBy') || 'latest';
    const tag = c.req.query('tag');
    const tags = c.req.query('tags') ? c.req.query('tags').split(',') : [];
    const category = c.req.query('category');
    const search = c.req.query('search');
    const educationLevel = c.req.query('educationLevel');
    const industry = c.req.query('industry');

    // Calculate pagination
    const skip = (page - 1) * pageSize;

    // Build filter object
    const filter: any = {
      status: 'approved', // Only show approved stories
    };

    // Add tag filter if provided
    // In a real app, we would have a separate table for tags
    // For now, we'll just search in the content
    if (tag) {
      filter.content = {
        contains: `标签: ${tag}`,
      };
    }

    // Add multiple tags filter if provided
    if (tags.length > 0) {
      // Create an array of conditions for each tag
      const tagConditions = tags.map(tag => ({
        content: {
          contains: tag,
        },
      }));

      // Add AND condition to filter
      filter.AND = tagConditions;
    }

    // Add category filter if provided
    if (category) {
      filter.content = {
        ...filter.content,
        contains: `分类: ${category}`,
      };
    }

    // Add education level filter if provided
    if (educationLevel) {
      filter.content = {
        ...filter.content,
        contains: `学历: ${educationLevel}`,
      };
    }

    // Add industry filter if provided
    if (industry) {
      filter.content = {
        ...filter.content,
        contains: `行业: ${industry}`,
      };
    }

    // Add search filter if provided
    if (search) {
      // Search in title and content
      filter.OR = [
        {
          title: {
            contains: search,
          },
        },
        {
          content: {
            contains: search,
          },
        },
      ];
    }

    // Get stories
    const stories = await prisma.story.findMany({
      where: filter,
      orderBy: sortBy === 'latest'
        ? { createdAt: 'desc' }
        : sortBy === 'popular'
          ? { likes: 'desc' }
          : { createdAt: 'desc' },
      skip,
      take: pageSize,
      select: {
        id: true,
        title: true,
        content: true,
        isAnonymous: true,
        likes: true,
        dislikes: true,
        createdAt: true,
        user: {
          select: {
            id: true,
            email: true,
          },
        },
      },
    });

    // Get total count
    const totalCount = await prisma.story.count({
      where: filter,
    });

    // Calculate total pages
    const totalPages = Math.ceil(totalCount / pageSize);

    // Format stories
    const formattedStories = stories.map(story => {
      // Extract tags from content
      const tagsMatch = story.content.match(/标签: (.*?)$/m);
      const tags = tagsMatch
        ? tagsMatch[1].split(', ').map(tag => tag.trim())
        : [];

      // Extract category from content if exists
      const categoryMatch = story.content.match(/分类: (.*?)$/m);
      const category = categoryMatch ? categoryMatch[1].trim() : null;

      // Extract education level from content if exists
      const educationLevelMatch = story.content.match(/学历: (.*?)$/m);
      const educationLevel = educationLevelMatch ? educationLevelMatch[1].trim() : null;

      // Extract industry from content if exists
      const industryMatch = story.content.match(/行业: (.*?)$/m);
      const industry = industryMatch ? industryMatch[1].trim() : null;

      // Remove metadata from content
      let content = story.content
        .replace(/\n\n标签: .*?$/m, '')
        .replace(/\n\n分类: .*?$/m, '')
        .replace(/\n\n学历: .*?$/m, '')
        .replace(/\n\n行业: .*?$/m, '');

      // Highlight search terms if search is provided
      if (search) {
        const searchRegex = new RegExp(`(${search})`, 'gi');
        content = content.replace(searchRegex, '<mark>$1</mark>');
      }

      return {
        id: story.id,
        title: story.title,
        content,
        author: story.isAnonymous ? '匿名用户' : '已验证用户',
        createdAt: story.createdAt,
        likes: story.likes,
        dislikes: story.dislikes,
        tags,
        category,
        educationLevel,
        industry,
      };
    });

    // Get popular tags
    const allStories = await prisma.story.findMany({
      where: {
        status: 'approved',
      },
      select: {
        content: true,
      },
    });

    // Extract all tags from all stories
    const allTags: string[] = [];
    allStories.forEach(story => {
      const tagsMatch = story.content.match(/标签: (.*?)$/m);
      if (tagsMatch) {
        const storyTags = tagsMatch[1].split(', ').map(tag => tag.trim());
        allTags.push(...storyTags);
      }
    });

    // Count tag occurrences
    const tagCounts: Record<string, number> = {};
    allTags.forEach(tag => {
      tagCounts[tag] = (tagCounts[tag] || 0) + 1;
    });

    // Convert to array and sort by count
    const popularTags = Object.entries(tagCounts)
      .map(([tag, count]) => ({ tag, count }))
      .sort((a, b) => b.count - a.count)
      .slice(0, 10); // Get top 10 tags

    return c.json({
      success: true,
      stories: formattedStories,
      totalPages,
      currentPage: page,
      popularTags,
    });
  } catch (error) {
    console.error('Error getting story list:', error);
    return c.json({ success: false, error: 'Failed to get story list' }, 500);
  }
};
