import React, { useState, useEffect } from 'react';
import { fetchData } from '@/services/unifiedDataService';

export function ApiTest() {
  const [apiResult, setApiResult] = useState<any>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const testApi = async () => {
    setLoading(true);
    setError(null);
    setApiResult(null);

    try {
      console.log('🧪 开始API测试...');

      const response = await fetchData(
        '/api/questionnaire/stats',
        'api',
        {},
        { method: 'GET', useCache: false }
      );

      console.log('🧪 API测试结果:', response);
      setApiResult(response);
    } catch (err) {
      console.error('🧪 API测试失败:', err);
      setError(err instanceof Error ? err.message : '未知错误');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    testApi();
  }, []);

  return (
    <div className="p-6 bg-gray-50 rounded-lg">
      <h3 className="text-lg font-semibold mb-4">API连接测试</h3>

      <button
        onClick={testApi}
        disabled={loading}
        className="mb-4 px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600 disabled:opacity-50"
      >
        {loading ? '测试中...' : '重新测试'}
      </button>

      {loading && (
        <div className="text-blue-600">正在测试API连接...</div>
      )}

      {error && (
        <div className="text-red-600 bg-red-50 p-3 rounded">
          <strong>错误:</strong> {error}
        </div>
      )}

      {apiResult && (
        <div className="mt-4">
          <h4 className="font-semibold text-green-600">✅ API连接成功!</h4>
          <div className="mt-2 p-3 bg-white rounded border">
            <div className="text-sm text-gray-600">数据源: {apiResult.source}</div>
            {apiResult.success && apiResult.statistics && (
              <div className="mt-2">
                <div>总问卷数: <strong>{apiResult.statistics.totalResponses}</strong></div>
                <div>已就业: <strong>{apiResult.statistics.employedCount}</strong></div>
                <div>未就业: <strong>{apiResult.statistics.unemployedCount}</strong></div>
                <div>已验证: <strong>{apiResult.statistics.verifiedCount}</strong></div>
                <div>匿名: <strong>{apiResult.statistics.anonymousCount}</strong></div>
                <div>问卷心声: <strong>{apiResult.statistics.voicesCount}</strong></div>
                <div>就业率: <strong>{apiResult.statistics.employmentRate}%</strong></div>
                <div>教育水平数据: <strong>{apiResult.statistics.educationLevels?.length || 0}</strong> 项</div>
              </div>
            )}
            <details className="mt-2">
              <summary className="cursor-pointer text-sm text-gray-500">查看完整响应</summary>
              <pre className="mt-2 text-xs bg-gray-100 p-2 rounded overflow-auto">
                {JSON.stringify(apiResult, null, 2)}
              </pre>
            </details>
          </div>
        </div>
      )}
    </div>
  );
}
