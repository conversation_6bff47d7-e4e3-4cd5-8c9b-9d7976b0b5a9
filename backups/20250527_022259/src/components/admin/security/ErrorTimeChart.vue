<template>
  <div class="error-time-chart-container">
    <div class="chart-header">
      <h3>{{ title }}</h3>
      <div class="chart-controls">
        <select v-model="timeRange" @change="updateChart">
          <option value="day">24小时</option>
          <option value="week">7天</option>
          <option value="month">30天</option>
        </select>
        <select v-model="groupBy" @change="updateChart">
          <option value="all">所有错误</option>
          <option value="severity">按严重性</option>
          <option value="source">按来源</option>
          <option value="errorCode">按错误代码</option>
        </select>
      </div>
    </div>
    
    <div class="chart-container" ref="chartContainer">
      <div v-if="loading" class="chart-loading">
        <div class="spinner"></div>
        <p>加载中...</p>
      </div>
      
      <div v-else-if="error" class="chart-error">
        <p>{{ error }}</p>
        <button @click="updateChart">重试</button>
      </div>
      
      <div v-else-if="!hasData" class="chart-empty">
        <p>没有数据可显示</p>
      </div>
      
      <canvas v-else ref="chartCanvas"></canvas>
    </div>
    
    <div v-if="hasData && !loading && !error" class="chart-legend">
      <div 
        v-for="(item, index) in chartLegend" 
        :key="index"
        class="legend-item"
        @click="toggleDataset(index)"
        :class="{ 'legend-item-hidden': hiddenDatasets.includes(index) }"
      >
        <div class="legend-color" :style="{ backgroundColor: item.color }"></div>
        <div class="legend-label">{{ item.label }}</div>
      </div>
    </div>
    
    <div v-if="hasData && !loading && !error" class="chart-insights">
      <h4>数据洞察</h4>
      <ul>
        <li v-for="(insight, index) in insights" :key="index">
          {{ insight }}
        </li>
      </ul>
    </div>
  </div>
</template>

<script>
import Chart from 'chart.js/auto';

export default {
  name: 'ErrorTimeChart',
  props: {
    title: {
      type: String,
      default: '错误趋势'
    },
    dataUrl: {
      type: String,
      required: true
    },
    initialTimeRange: {
      type: String,
      default: 'week'
    },
    initialGroupBy: {
      type: String,
      default: 'all'
    }
  },
  data() {
    return {
      timeRange: this.initialTimeRange,
      groupBy: this.initialGroupBy,
      chartData: null,
      chartInstance: null,
      loading: true,
      error: null,
      chartLegend: [],
      hiddenDatasets: [],
      insights: []
    };
  },
  computed: {
    hasData() {
      return this.chartData && 
             this.chartData.labels && 
             this.chartData.labels.length > 0 && 
             this.chartData.datasets && 
             this.chartData.datasets.length > 0;
    }
  },
  mounted() {
    this.updateChart();
    
    // 添加窗口大小变化监听器
    window.addEventListener('resize', this.resizeChart);
  },
  beforeUnmount() {
    // 移除窗口大小变化监听器
    window.removeEventListener('resize', this.resizeChart);
    
    // 销毁图表实例
    if (this.chartInstance) {
      this.chartInstance.destroy();
    }
  },
  methods: {
    async updateChart() {
      this.loading = true;
      this.error = null;
      
      try {
        // 构建请求 URL
        const url = `${this.dataUrl}?timeRange=${this.timeRange}&groupBy=${this.groupBy}`;
        
        // 获取数据
        const response = await fetch(url);
        
        if (!response.ok) {
          throw new Error(`HTTP error! status: ${response.status}`);
        }
        
        const data = await response.json();
        
        if (!data.success) {
          throw new Error(data.error || '获取数据失败');
        }
        
        this.chartData = data.chart || { labels: [], datasets: [] };
        this.insights = data.insights || [];
        
        // 更新图表
        this.renderChart();
        
        // 更新图例
        this.updateLegend();
      } catch (error) {
        console.error('Error fetching chart data:', error);
        this.error = '获取图表数据失败: ' + error.message;
      } finally {
        this.loading = false;
      }
    },
    
    renderChart() {
      // 如果已有图表实例，先销毁
      if (this.chartInstance) {
        this.chartInstance.destroy();
      }
      
      // 如果没有数据，不渲染图表
      if (!this.hasData) {
        return;
      }
      
      // 获取 canvas 上下文
      const ctx = this.$refs.chartCanvas.getContext('2d');
      
      // 创建图表实例
      this.chartInstance = new Chart(ctx, {
        type: 'line',
        data: this.chartData,
        options: {
          responsive: true,
          maintainAspectRatio: false,
          plugins: {
            tooltip: {
              mode: 'index',
              intersect: false
            },
            legend: {
              display: false // 使用自定义图例
            }
          },
          scales: {
            x: {
              grid: {
                color: 'rgba(0, 0, 0, 0.05)'
              }
            },
            y: {
              beginAtZero: true,
              grid: {
                color: 'rgba(0, 0, 0, 0.05)'
              },
              ticks: {
                precision: 0
              }
            }
          },
          elements: {
            line: {
              tension: 0.3 // 使线条更平滑
            },
            point: {
              radius: 3,
              hoverRadius: 5
            }
          },
          interaction: {
            mode: 'nearest',
            axis: 'x',
            intersect: false
          }
        }
      });
    },
    
    updateLegend() {
      if (!this.hasData) {
        this.chartLegend = [];
        return;
      }
      
      this.chartLegend = this.chartData.datasets.map(dataset => ({
        label: dataset.label,
        color: dataset.borderColor
      }));
    },
    
    toggleDataset(index) {
      // 切换数据集的可见性
      const isHidden = this.hiddenDatasets.includes(index);
      
      if (isHidden) {
        // 如果已隐藏，则显示
        this.hiddenDatasets = this.hiddenDatasets.filter(i => i !== index);
      } else {
        // 如果已显示，则隐藏
        this.hiddenDatasets.push(index);
      }
      
      // 更新图表
      if (this.chartInstance) {
        this.chartInstance.data.datasets[index].hidden = !isHidden;
        this.chartInstance.update();
      }
    },
    
    resizeChart() {
      if (this.chartInstance) {
        this.chartInstance.resize();
      }
    }
  }
};
</script>

<style scoped>
.error-time-chart-container {
  background-color: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  padding: 20px;
  margin-bottom: 20px;
}

.chart-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
}

.chart-header h3 {
  margin: 0;
  font-size: 18px;
  color: #333;
}

.chart-controls select {
  margin-left: 10px;
  padding: 5px 10px;
  border: 1px solid #ddd;
  border-radius: 4px;
  background-color: #f9f9f9;
}

.chart-container {
  position: relative;
  height: 300px;
  margin-bottom: 15px;
}

.chart-loading, .chart-error, .chart-empty {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  background-color: rgba(255, 255, 255, 0.8);
}

.spinner {
  border: 3px solid #f3f3f3;
  border-top: 3px solid #3498db;
  border-radius: 50%;
  width: 30px;
  height: 30px;
  animation: spin 1s linear infinite;
  margin-bottom: 10px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.chart-legend {
  display: flex;
  flex-wrap: wrap;
  margin-bottom: 15px;
}

.legend-item {
  display: flex;
  align-items: center;
  margin-right: 15px;
  margin-bottom: 8px;
  cursor: pointer;
  padding: 3px 8px;
  border-radius: 4px;
  transition: background-color 0.2s;
}

.legend-item:hover {
  background-color: #f5f5f5;
}

.legend-item-hidden {
  opacity: 0.5;
}

.legend-color {
  width: 15px;
  height: 15px;
  border-radius: 3px;
  margin-right: 5px;
}

.legend-label {
  font-size: 12px;
  color: #666;
}

.chart-insights {
  background-color: #f9f9f9;
  border-radius: 6px;
  padding: 15px;
}

.chart-insights h4 {
  margin-top: 0;
  margin-bottom: 10px;
  font-size: 16px;
  color: #333;
}

.chart-insights ul {
  margin: 0;
  padding-left: 20px;
}

.chart-insights li {
  margin-bottom: 5px;
  font-size: 14px;
  color: #555;
}
</style>
