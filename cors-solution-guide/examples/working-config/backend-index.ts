/**
 * 正确的Hono + Cloudflare Workers CORS配置示例
 * 这是一个完整的、经过验证的配置
 */

import { Hono } from 'hono';
import { cors } from 'hono/cors';
import { logger } from 'hono/logger';
import { secureHeaders } from 'hono/secure-headers';

const app = new Hono();

// 中间件
app.use('*', logger());
app.use('*', secureHeaders());

// ✅ 正确的CORS配置
app.use('*', cors({
  origin: (origin) => {
    // 处理无origin的请求（如Postman、curl、服务器到服务器请求）
    if (!origin) return origin;

    // 允许的域名列表
    const allowedDomains = [
      // 开发环境
      'http://localhost:3000',
      'http://localhost:5173',
      'http://localhost:5174',
      
      // 生产环境
      'https://app.yourdomain.com',
      'https://admin.yourdomain.com',
      
      // 测试环境
      'https://staging.yourdomain.com',
      
      // 已知的Cloudflare Pages域名
      'https://abc12345.your-project.pages.dev',
      'https://def67890.your-project.pages.dev'
    ];

    // 检查是否在允许列表中
    if (allowedDomains.includes(origin)) {
      return origin;  // ✅ 返回具体域名，不是true
    }

    // 检查是否是Cloudflare Pages的临时域名
    // 格式: https://[8位16进制].your-project.pages.dev
    if (origin.match(/^https:\/\/[a-f0-9]{8}\.your-project\.pages\.dev$/)) {
      return origin;
    }

    // 拒绝其他域名
    return false;
  },
  
  // 允许的HTTP方法
  allowMethods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
  
  // 允许的请求头
  allowHeaders: [
    'Content-Type',
    'Authorization', 
    'X-Requested-With',
    'Accept',
    'Origin'
  ],
  
  // 暴露给前端的响应头
  exposeHeaders: ['X-Total-Count'],
  
  // 允许发送凭证
  credentials: true,
  
  // 预检请求缓存时间（24小时）
  maxAge: 86400
}));

// API路由
app.get('/api/health', (c) => {
  return c.json({ 
    status: 'ok', 
    timestamp: new Date().toISOString(),
    cors: 'configured'
  });
});

app.get('/api/test', (c) => {
  return c.json({
    message: 'CORS test successful',
    origin: c.req.header('Origin'),
    method: c.req.method
  });
});

// 系统监控端点
app.get('/api/system/monitor', async (c) => {
  return c.json({
    timestamp: new Date().toISOString(),
    status: 'monitoring',
    checks: {
      cors: { status: 'healthy' },
      api: { status: 'healthy' }
    }
  });
});

// 错误处理
app.onError((err, c) => {
  console.error('Application Error:', err);
  
  return c.json({
    error: 'Internal Server Error',
    message: process.env.NODE_ENV === 'development' ? err.message : 'Something went wrong'
  }, 500);
});

// 404处理
app.notFound((c) => {
  return c.json({
    error: 'Not Found',
    message: `Route ${c.req.method} ${c.req.path} not found`
  }, 404);
});

export default app;
