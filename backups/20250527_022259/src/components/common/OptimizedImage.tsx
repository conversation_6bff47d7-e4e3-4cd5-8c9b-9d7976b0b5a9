import React, { useState, useEffect, useRef } from 'react';

// 图片格式类型
type ImageFormat = 'webp' | 'jpeg' | 'png' | 'avif';

// 图片加载状态
type LoadingState = 'loading' | 'loaded' | 'error';

// 组件属性
interface OptimizedImageProps {
  src: string;
  alt: string;
  width?: number | string;
  height?: number | string;
  className?: string;
  style?: React.CSSProperties;
  fallbackSrc?: string;
  placeholderColor?: string;
  formats?: ImageFormat[];
  sizes?: string;
  lazy?: boolean;
  threshold?: number;
  blur?: boolean;
  blurAmount?: number;
  onLoad?: () => void;
  onError?: () => void;
}

/**
 * 优化的图片组件
 * 
 * 特性:
 * - 支持WebP和AVIF等现代图片格式
 * - 懒加载
 * - 渐进式加载（低质量图片占位符）
 * - 加载失败时的回退图片
 * - 响应式图片尺寸
 */
const OptimizedImage: React.FC<OptimizedImageProps> = ({
  src,
  alt,
  width,
  height,
  className = '',
  style = {},
  fallbackSrc,
  placeholderColor = '#f0f0f0',
  formats = ['webp', 'jpeg'],
  sizes = '100vw',
  lazy = true,
  threshold = 0.1,
  blur = true,
  blurAmount = 10,
  onLoad,
  onError
}) => {
  const [loadingState, setLoadingState] = useState<LoadingState>('loading');
  const imgRef = useRef<HTMLImageElement>(null);
  const observer = useRef<IntersectionObserver | null>(null);

  // 检测浏览器支持的图片格式
  const getSupportedFormat = (): ImageFormat => {
    if (typeof window === 'undefined') return 'jpeg';
    
    const hasWebP = () => {
      const canvas = document.createElement('canvas');
      if (canvas.getContext && canvas.getContext('2d')) {
        return canvas.toDataURL('image/webp').indexOf('data:image/webp') === 0;
      }
      return false;
    };
    
    const hasAVIF = () => {
      const img = new Image();
      img.src = 'data:image/avif;base64,AAAAIGZ0eXBhdmlmAAAAAGF2aWZtaWYxbWlhZk1BMUIAAADybWV0YQAAAAAAAAAoaGRscgAAAAAAAAAAcGljdAAAAAAAAAAAAAAAAGxpYmF2aWYAAAAADnBpdG0AAAAAAAEAAAAeaWxvYwAAAABEAAABAAEAAAABAAABGgAAAB0AAAAoaWluZgAAAAAAAQAAABppbmZlAgAAAAABAABhdjAxQ29sb3IAAAAAamlwcnAAAABLaXBjbwAAABRpc3BlAAAAAAAAAAIAAAACAAAAEHBpeGkAAAAAAwgICAAAAAxhdjFDgQ0MAAAAABNjb2xybmNseAACAAIAAYAAAAAXaXBtYQAAAAAAAAABAAEEAQKDBAAAACVtZGF0EgAKCBgANogQEAwgMg8f8D///8WfhwB8+ErK42A=';
      return img.complete;
    };
    
    // 检查支持的格式
    for (const format of formats) {
      if (format === 'webp' && hasWebP()) return 'webp';
      if (format === 'avif' && hasAVIF()) return 'avif';
      if (format === 'jpeg' || format === 'png') return format;
    }
    
    return 'jpeg';
  };

  // 获取优化后的图片URL
  const getOptimizedSrc = (): string => {
    // 如果是外部URL或者已经包含查询参数，则不处理
    if (src.startsWith('http') || src.includes('?')) return src;
    
    const format = getSupportedFormat();
    return `${src}?format=${format}`;
  };

  // 处理图片加载完成
  const handleImageLoaded = () => {
    setLoadingState('loaded');
    if (onLoad) onLoad();
  };

  // 处理图片加载失败
  const handleImageError = () => {
    setLoadingState('error');
    if (onError) onError();
  };

  // 设置懒加载
  useEffect(() => {
    if (!lazy || typeof IntersectionObserver === 'undefined') return;
    
    const observerOptions = {
      root: null,
      rootMargin: '0px',
      threshold
    };
    
    observer.current = new IntersectionObserver((entries) => {
      entries.forEach(entry => {
        if (entry.isIntersecting && imgRef.current) {
          // 当图片进入视口时，设置src属性
          const img = imgRef.current;
          if (img.dataset.src) {
            img.src = img.dataset.src;
            img.removeAttribute('data-src');
          }
          
          // 停止观察
          if (observer.current) {
            observer.current.unobserve(img);
          }
        }
      });
    }, observerOptions);
    
    if (imgRef.current) {
      observer.current.observe(imgRef.current);
    }
    
    return () => {
      if (observer.current) {
        observer.current.disconnect();
      }
    };
  }, [lazy, threshold]);

  // 计算样式
  const computedStyle: React.CSSProperties = {
    ...style,
    backgroundColor: placeholderColor,
    width: width || 'auto',
    height: height || 'auto',
    objectFit: 'cover',
    transition: 'filter 0.3s ease-in-out',
    filter: loadingState === 'loading' && blur ? `blur(${blurAmount}px)` : 'none',
  };

  // 如果加载失败且有回退图片，则显示回退图片
  if (loadingState === 'error' && fallbackSrc) {
    return (
      <img
        src={fallbackSrc}
        alt={alt}
        width={width}
        height={height}
        className={className}
        style={computedStyle}
      />
    );
  }

  // 获取优化后的图片URL
  const optimizedSrc = getOptimizedSrc();

  return (
    <img
      ref={imgRef}
      src={lazy ? undefined : optimizedSrc}
      data-src={lazy ? optimizedSrc : undefined}
      alt={alt}
      width={width}
      height={height}
      className={className}
      style={computedStyle}
      onLoad={handleImageLoaded}
      onError={handleImageError}
      loading={lazy ? 'lazy' : undefined}
      sizes={sizes}
    />
  );
};

export default OptimizedImage;
