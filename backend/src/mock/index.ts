/**
 * 模拟数据入口文件
 * 
 * 本文件集中管理所有模拟数据，用于本地开发环境
 */

import { mockStories } from './stories';
import { mockTags } from './tags';
import { mockResponses } from './responses';
import { mockStatistics } from './statistics';

// 导出所有模拟数据
export {
  mockStories,
  mockTags,
  mockResponses,
  mockStatistics
};

/**
 * 获取模拟数据
 * 
 * @param type 数据类型
 * @param options 选项
 * @returns 模拟数据
 */
export function getMockData(type: string, options: any = {}) {
  switch (type) {
    case 'stories':
      return mockStories;
    case 'tags':
      return mockTags;
    case 'responses':
      return mockResponses;
    case 'statistics':
      return mockStatistics;
    default:
      return null;
  }
}

/**
 * 检查是否应该使用模拟数据
 * 
 * @param env 环境变量
 * @returns 是否应该使用模拟数据
 */
export function shouldUseMockData(env: any): boolean {
  // 如果环境变量明确指定使用模拟数据，则使用模拟数据
  if (env.USE_MOCK_DATA === 'true') {
    return true;
  }
  
  // 如果是开发环境，则使用模拟数据
  if (env.ENVIRONMENT === 'development') {
    return true;
  }
  
  // 默认不使用模拟数据
  return false;
}

/**
 * 本地存储模拟类
 * 用于在浏览器本地存储模拟数据
 */
export class LocalStorageMock {
  private prefix: string;
  
  constructor(prefix: string = 'mock_') {
    this.prefix = prefix;
  }
  
  /**
   * 获取数据
   * 
   * @param key 键
   * @returns 值
   */
  getItem(key: string): any {
    const value = localStorage.getItem(`${this.prefix}${key}`);
    if (!value) return null;
    
    try {
      return JSON.parse(value);
    } catch (error) {
      return value;
    }
  }
  
  /**
   * 设置数据
   * 
   * @param key 键
   * @param value 值
   */
  setItem(key: string, value: any): void {
    const stringValue = typeof value === 'string' ? value : JSON.stringify(value);
    localStorage.setItem(`${this.prefix}${key}`, stringValue);
  }
  
  /**
   * 删除数据
   * 
   * @param key 键
   */
  removeItem(key: string): void {
    localStorage.removeItem(`${this.prefix}${key}`);
  }
  
  /**
   * 清除所有数据
   */
  clear(): void {
    Object.keys(localStorage).forEach(key => {
      if (key.startsWith(this.prefix)) {
        localStorage.removeItem(key);
      }
    });
  }
  
  /**
   * 获取所有键
   * 
   * @returns 所有键
   */
  keys(): string[] {
    return Object.keys(localStorage)
      .filter(key => key.startsWith(this.prefix))
      .map(key => key.substring(this.prefix.length));
  }
}

// 导出模拟存储实例
export const mockStorage = new LocalStorageMock();

// 初始化模拟数据
export function initMockData(): void {
  // 检查是否已经初始化
  if (mockStorage.getItem('initialized')) {
    return;
  }
  
  // 初始化模拟数据
  mockStorage.setItem('stories', mockStories);
  mockStorage.setItem('tags', mockTags);
  mockStorage.setItem('responses', mockResponses);
  mockStorage.setItem('statistics', mockStatistics);
  
  // 标记为已初始化
  mockStorage.setItem('initialized', true);
}
