/**
 * 🚀 简化的模块化API
 * 基于现有index.js，重新组织为模块化结构
 */

import { Hono } from 'hono';
import { logger } from 'hono/logger';
import { secureHeaders } from 'hono/secure-headers';
import { cors } from 'hono/cors';

const app = new Hono();

// 全局中间件
app.use('*', logger());
app.use('*', secureHeaders());
app.use('*', cors({
  origin: [
    'http://localhost:5173',
    'http://localhost:5174',
    'http://localhost:5175',
    'http://localhost:5176',
    'http://localhost:5177',
    'https://college-employment-survey.pages.dev',
    'https://*.college-employment-survey.pages.dev',
    'https://6599d22b.college-employment-survey-realapi.pages.dev',
    'https://beb4f845.college-employment-survey.pages.dev',
    'https://d2292b83.college-employment-survey.pages.dev',
    'https://c4534e21.college-employment-survey.pages.dev',
    'https://2ba531d5.college-employment-survey.pages.dev',
    'https://e4cd94b1.college-employment-survey.pages.dev',
    'https://d68f23d0.college-employment-survey.pages.dev',
    'https://edfb082f.college-employment-survey.pages.dev',
    'https://b104b50c.college-employment-survey.pages.dev',
    'https://de2fcae1.college-employment-survey.pages.dev',
    'https://b95169fc.college-employment-survey.pages.dev'
  ],
  allowMethods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
  allowHeaders: ['Content-Type', 'Authorization'],
  maxAge: 86400,
}));

// 工具函数
function createSuccessResponse(data, message = 'Success', meta = {}) {
  return {
    success: true,
    message,
    data,
    meta: {
      timestamp: new Date().toISOString(),
      ...meta
    }
  };
}

function createErrorResponse(error, code = 500, details = {}) {
  return {
    success: false,
    error,
    code,
    details,
    timestamp: new Date().toISOString()
  };
}

function validatePaginationParams(page = 1, limit = 20, maxLimit = 100) {
  const validPage = Math.max(1, parseInt(page) || 1);
  const validLimit = Math.min(maxLimit, Math.max(1, parseInt(limit) || 20));

  return {
    page: validPage,
    limit: validLimit,
    offset: (validPage - 1) * validLimit
  };
}

// 根路径 - API信息
app.get('/', (c) => {
  const info = {
    name: 'College Employment Survey API',
    version: 'v3.0-modular-simple',
    status: 'running',
    environment: c.env?.ENVIRONMENT || 'production',
    timestamp: new Date().toISOString(),
    architecture: 'modular',
    modules: [
      'questionnaire',
      'story',
      'admin',
      'deidentification',
      'system'
    ],
    endpoints: {
      questionnaireStats: '/api/questionnaire/stats',
      questionnaireVoices: '/api/questionnaire/voices',
      visualizationData: '/api/questionnaire/visualization/data',
      storyList: '/api/story/list',
      adminDashboard: '/api/admin/dashboard/stats',
      systemHealth: '/api/system/health'
    }
  };

  return c.json(createSuccessResponse(info, 'API information retrieved successfully'));
});

// 健康检查端点
app.get('/health', (c) => {
  return c.json({
    status: 'ok',
    version: 'v3.0-modular-simple',
    environment: c.env?.ENVIRONMENT || 'production',
    timestamp: new Date().toISOString(),
    checks: {
      database: 'healthy',
      memory: 'healthy',
      modules: 'loaded'
    }
  });
});

// 问卷模块路由
app.get('/api/questionnaire/stats', async (c) => {
  try {
    // 获取总数
    const totalResult = await c.env.DB.prepare(
      'SELECT COUNT(*) as total FROM questionnaire_responses_v2'
    ).first();
    const total = totalResult?.total || 0;

    // 获取就业状态统计
    const employedResult = await c.env.DB.prepare(`
      SELECT COUNT(*) as count
      FROM questionnaire_responses_v2
      WHERE employment_status = '已就业'
    `).first();
    const employedCount = employedResult?.count || 0;

    const unemployedResult = await c.env.DB.prepare(`
      SELECT COUNT(*) as count
      FROM questionnaire_responses_v2
      WHERE employment_status = '未就业'
    `).first();
    const unemployedCount = unemployedResult?.count || 0;

    // 获取教育水平分布
    const educationResult = await c.env.DB.prepare(`
      SELECT education_level_display as name, COUNT(*) as count
      FROM questionnaire_responses_v2
      WHERE education_level_display IS NOT NULL
      GROUP BY education_level_display
      ORDER BY count DESC
    `).all();

    // 获取地区分布
    const regionResult = await c.env.DB.prepare(`
      SELECT region_display as name, COUNT(*) as count
      FROM questionnaire_responses_v2
      WHERE region_display IS NOT NULL
      GROUP BY region_display
      ORDER BY count DESC
      LIMIT 10
    `).all();

    // 获取专业分布
    const majorResult = await c.env.DB.prepare(`
      SELECT major_display as name, COUNT(*) as count
      FROM questionnaire_responses_v2
      WHERE major_display IS NOT NULL AND major_display != ''
      GROUP BY major_display
      ORDER BY count DESC
      LIMIT 15
    `).all();

    // 获取毕业年份分布
    const graduationYearResult = await c.env.DB.prepare(`
      SELECT graduation_year as name, COUNT(*) as count
      FROM questionnaire_responses_v2
      WHERE graduation_year IS NOT NULL
      GROUP BY graduation_year
      ORDER BY graduation_year DESC
    `).all();

    // 获取行业分布
    const industryResult = await c.env.DB.prepare(`
      SELECT current_industry_display as name, COUNT(*) as count
      FROM questionnaire_responses_v2
      WHERE current_industry_display IS NOT NULL AND current_industry_display != ''
      GROUP BY current_industry_display
      ORDER BY count DESC
      LIMIT 10
    `).all();

    // 获取就业状态分布
    const employmentResult = await c.env.DB.prepare(`
      SELECT employment_status as name, COUNT(*) as count
      FROM questionnaire_responses_v2
      WHERE employment_status IS NOT NULL
      GROUP BY employment_status
      ORDER BY count DESC
    `).all();

    const statistics = {
      totalResponses: total,
      verifiedCount: total,
      anonymousCount: 0,
      employedCount,
      unemployedCount,
      educationLevels: educationResult.results?.map(item => ({
        name: item.name,
        count: item.count
      })) || [],
      regions: regionResult.results?.map(item => ({
        name: item.name,
        count: item.count
      })) || [],
      majors: majorResult.results?.map(item => ({
        name: item.name,
        count: item.count
      })) || [],
      graduationYears: graduationYearResult.results?.map(item => ({
        name: item.name?.toString() || 'unknown',
        count: item.count
      })) || [],
      industries: industryResult.results?.map(item => ({
        name: item.name,
        count: item.count
      })) || [],
      employmentStatus: employmentResult.results?.map(item => ({
        name: item.name,
        count: item.count
      })) || [],
      lastUpdated: new Date().toISOString()
    };

    return c.json(createSuccessResponse(null, 'Statistics retrieved successfully', { statistics }));
  } catch (error) {
    console.error('问卷统计API错误:', error);
    return c.json(createErrorResponse(error.message), 500);
  }
});

// 故事列表API
app.get('/api/story/list', async (c) => {
  try {
    const { page, limit, offset } = validatePaginationParams(
      c.req.query('page'),
      c.req.query('pageSize'),
      20
    );

    // 获取总数
    const totalResult = await c.env.DB.prepare(
      'SELECT COUNT(*) as total FROM story_contents_v2 WHERE status = ?'
    ).bind('approved').first();
    const total = totalResult?.total || 0;

    // 获取故事列表
    const storiesResult = await c.env.DB.prepare(`
      SELECT
        id,
        title,
        content,
        summary,
        category,
        education_level_display,
        industry_display,
        likes,
        views,
        created_at
      FROM story_contents_v2
      WHERE status = ?
      ORDER BY created_at DESC
      LIMIT ? OFFSET ?
    `).bind('approved', limit, offset).all();

    const stories = storiesResult.results?.map(story => ({
      id: story.id,
      title: story.title,
      content: story.content,
      summary: story.summary,
      category: story.category,
      metadata: {
        educationLevel: story.education_level_display,
        industry: story.industry_display
      },
      stats: {
        likes: story.likes,
        views: story.views
      },
      createdAt: story.created_at
    })) || [];

    const pagination = {
      total,
      page,
      limit,
      pages: Math.ceil(total / limit),
      hasNext: page * limit < total,
      hasPrev: page > 1
    };

    return c.json(createSuccessResponse(stories, 'Stories retrieved successfully', { pagination }));
  } catch (error) {
    console.error('故事列表API错误:', error);
    return c.json(createErrorResponse(error.message), 500);
  }
});

// 系统健康检查API
app.get('/api/system/health', (c) => {
  const checks = {
    database: 'healthy',
    memory: 'healthy',
    disk: 'healthy'
  };

  return c.json({
    status: 'ok',
    version: 'v3.0-modular-simple',
    checks,
    timestamp: new Date().toISOString()
  });
});

// 404处理器
app.notFound((c) => {
  return c.json(createErrorResponse('Endpoint not found', 404, {
    path: c.req.path,
    method: c.req.method,
    availableEndpoints: [
      '/api/questionnaire/stats',
      '/api/story/list',
      '/api/system/health'
    ]
  }), 404);
});

// 全局错误处理器
app.onError((err, c) => {
  console.error(`${c.req.method} ${c.req.url}`, err);
  return c.json(createErrorResponse(
    process.env.NODE_ENV === 'development' ? err.message : 'Internal server error',
    500
  ), 500);
});

export default app;
