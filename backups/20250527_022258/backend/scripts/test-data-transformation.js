/**
 * 数据转换测试脚本
 *
 * 验证前后端数据转换是否正常工作
 */

const { PrismaClient } = require('@prisma/client');
const { DataTransformService } = require('./dataTransformService');

const prisma = new PrismaClient();

/**
 * 测试问卷数据转换
 */
async function testQuestionnaireTransformation() {
  console.log('\n🧪 测试问卷数据转换...');

  try {
    // 获取一个问卷回复
    const response = await prisma.questionnaireResponse.findFirst({
      include: {
        user: true
      }
    });

    if (!response) {
      console.log('❌ 没有找到问卷回复数据');
      return;
    }

    console.log('📊 原始数据库数据:');
    console.log({
      id: response.id,
      sequenceNumber: response.sequenceNumber,
      educationLevel: response.educationLevel,
      currentIndustry: response.currentIndustry,
      currentPosition: response.currentPosition,
      monthlySalary: response.monthlySalary,
      tags: response.tags,
      status: response.status
    });

    // 转换为前端格式
    const frontendData = DataTransformService.transformQuestionnaireToFrontend(response);

    console.log('\n🔄 转换后的前端数据:');
    console.log({
      id: frontendData.id,
      sequenceNumber: frontendData.sequenceNumber,
      educationLevel: frontendData.educationLevel,
      industry: frontendData.industry,
      position: frontendData.position,
      salary: frontendData.salary,
      tags: frontendData.tags,
      status: frontendData.status
    });

    // 验证关键字段映射
    const mappingTests = [
      {
        name: '行业字段映射',
        backend: response.currentIndustry,
        frontend: frontendData.industry,
        pass: (response.currentIndustry === null && frontendData.industry === undefined) ||
              (response.currentIndustry === frontendData.industry)
      },
      {
        name: '职位字段映射',
        backend: response.currentPosition,
        frontend: frontendData.position,
        pass: (response.currentPosition === null && frontendData.position === undefined) ||
              (response.currentPosition === frontendData.position)
      },
      {
        name: '薪资类型转换',
        backend: response.monthlySalary,
        frontend: frontendData.salary,
        pass: response.monthlySalary ? response.monthlySalary.toString() === frontendData.salary : !frontendData.salary
      },
      {
        name: '标签JSON解析',
        backend: response.tags,
        frontend: frontendData.tags,
        pass: response.tags ? JSON.stringify(JSON.parse(response.tags)) === JSON.stringify(frontendData.tags) : !frontendData.tags
      }
    ];

    console.log('\n✅ 字段映射验证:');
    mappingTests.forEach(test => {
      const status = test.pass ? '✅' : '❌';
      console.log(`${status} ${test.name}: ${test.pass ? '通过' : '失败'}`);
      if (!test.pass) {
        console.log(`   后端: ${test.backend}, 前端: ${test.frontend}`);
      }
    });

    return mappingTests.every(test => test.pass);

  } catch (error) {
    console.error('❌ 问卷数据转换测试失败:', error);
    return false;
  }
}

/**
 * 测试故事数据转换
 */
async function testStoryTransformation() {
  console.log('\n🧪 测试故事数据转换...');

  try {
    // 获取一个故事
    const story = await prisma.story.findFirst({
      include: {
        user: true
      }
    });

    if (!story) {
      console.log('❌ 没有找到故事数据');
      return;
    }

    console.log('📊 原始数据库数据:');
    console.log({
      id: story.id,
      title: story.title,
      author: story.author,
      isAnonymous: story.isAnonymous,
      tags: story.tags,
      likes: story.likes,
      dislikes: story.dislikes
    });

    // 转换为前端格式
    const frontendData = DataTransformService.transformStoryToFrontend(story);

    console.log('\n🔄 转换后的前端数据:');
    console.log({
      id: frontendData.id,
      title: frontendData.title,
      author: frontendData.author,
      tags: frontendData.tags,
      likes: frontendData.likes,
      dislikes: frontendData.dislikes
    });

    // 验证关键字段
    const mappingTests = [
      {
        name: '作者字段处理',
        pass: frontendData.author && frontendData.author.length > 0
      },
      {
        name: '标签JSON解析',
        backend: story.tags,
        frontend: frontendData.tags,
        pass: story.tags ? Array.isArray(frontendData.tags) : Array.isArray(frontendData.tags) && frontendData.tags.length === 0
      },
      {
        name: '点赞数转换',
        backend: story.likes,
        frontend: frontendData.likes,
        pass: story.likes === frontendData.likes
      }
    ];

    console.log('\n✅ 字段映射验证:');
    mappingTests.forEach(test => {
      const status = test.pass ? '✅' : '❌';
      console.log(`${status} ${test.name}: ${test.pass ? '通过' : '失败'}`);
      if (!test.pass && test.backend !== undefined) {
        console.log(`   后端: ${test.backend}, 前端: ${test.frontend}`);
      }
    });

    return mappingTests.every(test => test.pass);

  } catch (error) {
    console.error('❌ 故事数据转换测试失败:', error);
    return false;
  }
}

/**
 * 测试数据验证
 */
function testDataValidation() {
  console.log('\n🧪 测试数据验证...');

  // 测试问卷数据验证
  const invalidQuestionnaireData = {
    // 缺少必需字段
  };

  const questionnaireErrors = DataTransformService.validateQuestionnaireData(invalidQuestionnaireData);
  console.log('📋 问卷验证错误:', questionnaireErrors);

  // 测试故事数据验证
  const invalidStoryData = {
    title: '短', // 太短
    content: '内容太短' // 太短
  };

  const storyErrors = DataTransformService.validateStoryData(invalidStoryData);
  console.log('📖 故事验证错误:', storyErrors);

  // 测试有效数据
  const validQuestionnaireData = {
    educationLevel: '本科',
    region: '北京',
    graduationYear: 2023
  };

  const validQuestionnaireErrors = DataTransformService.validateQuestionnaireData(validQuestionnaireData);
  console.log('✅ 有效问卷验证:', validQuestionnaireErrors.length === 0 ? '通过' : '失败');

  const validStoryData = {
    title: '我的就业经历分享',
    content: '这是一个关于我就业经历的详细分享，包含了很多有用的信息和经验。'
  };

  const validStoryErrors = DataTransformService.validateStoryData(validStoryData);
  console.log('✅ 有效故事验证:', validStoryErrors.length === 0 ? '通过' : '失败');

  return questionnaireErrors.length > 0 && storyErrors.length > 0 &&
         validQuestionnaireErrors.length === 0 && validStoryErrors.length === 0;
}

/**
 * 测试序列号生成
 */
function testSequenceNumberGeneration() {
  console.log('\n🧪 测试序列号生成...');

  const questionnaireSeq = DataTransformService.generateSequenceNumber('questionnaire', 123);
  const storySeq = DataTransformService.generateSequenceNumber('story', 456);

  console.log('📋 问卷序列号:', questionnaireSeq);
  console.log('📖 故事序列号:', storySeq);

  // 验证格式 (Q/S + 年月日 + 4位ID)
  const questionnaireValid = /^Q\d{6}\d{4}$/.test(questionnaireSeq);
  const storyValid = /^S\d{6}\d{4}$/.test(storySeq);

  console.log('✅ 问卷序列号格式:', questionnaireValid ? '正确' : '错误');
  console.log('✅ 故事序列号格式:', storyValid ? '正确' : '错误');

  return questionnaireValid && storyValid;
}

/**
 * 主测试函数
 */
async function runTests() {
  console.log('🚀 开始数据转换测试...');

  const results = [];

  try {
    // 运行各项测试
    results.push(await testQuestionnaireTransformation());
    results.push(await testStoryTransformation());
    results.push(testDataValidation());
    results.push(testSequenceNumberGeneration());

    // 汇总结果
    const passedTests = results.filter(Boolean).length;
    const totalTests = results.length;

    console.log('\n📊 测试结果汇总:');
    console.log(`✅ 通过: ${passedTests}/${totalTests}`);
    console.log(`❌ 失败: ${totalTests - passedTests}/${totalTests}`);

    if (passedTests === totalTests) {
      console.log('🎉 所有测试通过！数据转换服务工作正常。');
    } else {
      console.log('⚠️  部分测试失败，请检查数据转换逻辑。');
    }

  } catch (error) {
    console.error('❌ 测试过程中发生错误:', error);
  } finally {
    await prisma.$disconnect();
  }
}

// 运行测试
if (require.main === module) {
  runTests();
}

module.exports = {
  testQuestionnaireTransformation,
  testStoryTransformation,
  testDataValidation,
  testSequenceNumberGeneration
};
