import { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Input } from '@/components/ui/input';
import { ThumbsUp, Calendar, User, Tag as TagIcon, ChevronLeft, ChevronRight, Flame, Clock, Search, Plus, Share2, X } from 'lucide-react';
import SimpleAdvancedSearch from '@/components/story/SimpleAdvancedSearch';
// import SimpleTagSelector from '@/components/story/SimpleTagSelector';
// import EnhancedMultiTagSelector from '@/components/story/EnhancedMultiTagSelector';
// import HotTagCloud from '@/components/story/HotTagCloud';
// import SmartTagRecommendation from '@/components/story/SmartTagRecommendation';
import MobileFilterDrawer from '@/components/story/MobileFilterDrawer';
import ResponsiveStoryGrid from '@/components/story/ResponsiveStoryGrid';
import { STORY_TAGS, STORY_CATEGORIES, EDUCATION_LEVELS, INDUSTRIES } from '@/constants/storyConstants';
import { useApiDebug, DataStructures } from '@/hooks/useApiDebug';
import ApiDebugPanel from '@/components/debug/ApiDebugPanel';

interface Story {
  id: number;
  title: string;
  content: string;
  author: string;
  createdAt: string;
  likes: number;
  tags: string[];
  category: string;
  educationLevel: string;
  industry: string;
}

interface ApiResponse {
  success: boolean;
  stories: Story[];
  totalPages: number;
  currentPage: number;
  totalItems: number;
}

export default function SimpleStoryWall() {
  const navigate = useNavigate();
  const [stories, setStories] = useState<Story[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [page, setPage] = useState(1);
  const [pageSize, setPageSize] = useState(6);
  const [totalPages, setTotalPages] = useState(1);
  const [totalItems, setTotalItems] = useState(0);
  const [sortBy, setSortBy] = useState<'latest' | 'popular'>('latest');
  const [searchQuery, setSearchQuery] = useState('');

  // 高级筛选状态
  const [selectedCategory, setSelectedCategory] = useState('all');
  const [selectedEducationLevel, setSelectedEducationLevel] = useState('all');
  const [selectedIndustry, setSelectedIndustry] = useState('all');
  const [selectedTag, setSelectedTag] = useState('all');
  const [selectedTags, setSelectedTags] = useState<string[]>([]);
  const [tagMode, setTagMode] = useState<'single' | 'multi'>('single');

  // 移动端筛选抽屉状态
  const [isMobileFilterOpen, setIsMobileFilterOpen] = useState(false);

  // 热门标签状态
  const [popularTags, setPopularTags] = useState<string[]>([]);

  // API调试配置
  const apiDebug = useApiDebug({
    pageName: '简化版故事墙',
    requests: [
      {
        id: 'story-list',
        name: '故事列表',
        endpoint: '/api/story/list',
        method: 'GET',
        expectedStructure: DataStructures.storyList,
        testFunction: async () => {
          const queryParams = new URLSearchParams({
            page: page.toString(),
            sortBy: sortBy,
            pageSize: pageSize.toString()
          });

          if (searchQuery.trim()) {
            queryParams.append('search', searchQuery.trim());
          }
          if (selectedCategory !== 'all') {
            queryParams.append('category', selectedCategory);
          }
          if (selectedEducationLevel !== 'all') {
            queryParams.append('educationLevel', selectedEducationLevel);
          }
          if (selectedIndustry !== 'all') {
            queryParams.append('industry', selectedIndustry);
          }
          if (tagMode === 'single' && selectedTag !== 'all') {
            queryParams.append('tag', selectedTag);
          } else if (tagMode === 'multi' && selectedTags.length > 0) {
            queryParams.append('tags', selectedTags.join(','));
          }

          const response = await fetch(`/api/story/list?${queryParams.toString()}`);
          if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
          }
          return await response.json();
        }
      }
    ]
  });

  // 获取故事数据 (使用真实API v3.0)
  const fetchStories = async (
    pageNum: number = 1,
    size: number = pageSize,
    sort: string = sortBy,
    search: string = searchQuery,
    category: string = selectedCategory,
    educationLevel: string = selectedEducationLevel,
    industry: string = selectedIndustry,
    tag: string = selectedTag
  ) => {
    try {
      setLoading(true);
      setError(null);

      // 构建查询参数
      const queryParams = new URLSearchParams({
        page: pageNum.toString(),
        pageSize: size.toString()
      });

      // 添加筛选条件
      if (search.trim()) {
        queryParams.append('search', search.trim());
      }
      if (category !== 'all') {
        queryParams.append('category', category);
      }
      if (educationLevel !== 'all') {
        queryParams.append('educationLevel', educationLevel);
      }
      if (industry !== 'all') {
        queryParams.append('industry', industry);
      }

      // 标签筛选逻辑
      if (tagMode === 'single' && tag !== 'all') {
        queryParams.append('tag', tag);
      } else if (tagMode === 'multi' && selectedTags.length > 0) {
        queryParams.append('tags', selectedTags.join(','));
      }

      // 排序参数
      if (sort === 'popular') {
        queryParams.append('sortBy', 'likes');
      } else {
        queryParams.append('sortBy', 'created_at');
      }

      console.log('🔍 故事墙API请求:', {
        url: `/api/story/list?${queryParams.toString()}`,
        params: Object.fromEntries(queryParams.entries())
      });

      const response = await fetch(`/api/story/list?${queryParams.toString()}`);

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const data = await response.json();
      console.log('📊 故事墙API响应:', data);

      if (data.success && data.stories) {
        console.log('✅ 设置故事数据:', data.stories.length, '个故事');
        setStories(data.stories);
        setTotalPages(data.pagination?.totalPages || Math.ceil(data.pagination?.totalCount / size) || 1);
        setTotalItems(data.pagination?.totalCount || data.stories.length);

        // 更新热门标签
        if (data.popularTags) {
          setPopularTags(data.popularTags);
        }

        console.log('📊 状态更新完成 - 故事数量:', data.stories.length);
      } else {
        console.error('❌ API返回格式错误:', data);
        setError(data.error || 'API返回数据格式错误');
      }
    } catch (err) {
      console.error('❌ 故事墙API错误:', err);
      setError(err instanceof Error ? err.message : '获取故事失败');
    } finally {
      setLoading(false);
    }
  };

  // 页面加载时获取数据
  useEffect(() => {
    fetchStories(page, pageSize, sortBy, searchQuery, selectedCategory, selectedEducationLevel, selectedIndustry, selectedTag);
  }, [page, pageSize, sortBy, searchQuery, selectedCategory, selectedEducationLevel, selectedIndustry, selectedTag]);

  // 处理页面变化
  const handlePageChange = (newPage: number) => {
    setPage(newPage);
  };

  // 处理每页大小变化
  const handlePageSizeChange = (newSize: number) => {
    setPageSize(newSize);
    setPage(1); // 重置到第一页
  };

  // 处理排序变化
  const handleSortChange = (newSort: 'latest' | 'popular') => {
    setSortBy(newSort);
    setPage(1); // 重置到第一页
  };

  // 处理搜索
  const handleSearch = (query: string) => {
    setSearchQuery(query);
    setPage(1); // 重置到第一页
  };

  // 处理单标签选择
  const handleTagSelect = (tagId: string) => {
    setSelectedTag(tagId);
    setPage(1); // 重置到第一页
  };

  // 处理多标签选择
  const handleTagsChange = (tags: string[]) => {
    setSelectedTags(tags);
    setPage(1); // 重置到第一页
  };

  // 处理标签模式切换
  const handleTagModeChange = (mode: 'single' | 'multi') => {
    setTagMode(mode);
    // 切换模式时清除标签选择
    setSelectedTag('all');
    setSelectedTags([]);
    setPage(1);
  };

  // 处理清除筛选
  const handleClearFilters = () => {
    setSearchQuery('');
    setSelectedCategory('all');
    setSelectedEducationLevel('all');
    setSelectedIndustry('all');
    setSelectedTag('all');
    setSelectedTags([]);
    setPage(1);
  };

  // 检查是否有活跃的筛选条件
  const isFilterActive =
    searchQuery !== '' ||
    selectedCategory !== 'all' ||
    selectedEducationLevel !== 'all' ||
    selectedIndustry !== 'all' ||
    (tagMode === 'single' && selectedTag !== 'all') ||
    (tagMode === 'multi' && selectedTags.length > 0);

  // 分享故事
  const handleShareStory = () => {
    // 这里可以跳转到故事创建页面或打开分享对话框
    navigate('/story/create');
  };

  // 移动端筛选抽屉处理
  const handleMobileApplyFilters = () => {
    // 筛选已经在抽屉内部应用，这里只需要关闭抽屉
    setIsMobileFilterOpen(false);
  };

  // 分享单个故事
  const handleShareSingleStory = (story: Story, event: React.MouseEvent) => {
    event.stopPropagation(); // 阻止卡片点击事件

    if (navigator.share) {
      // 使用原生分享API
      navigator.share({
        title: story.title,
        text: story.content.substring(0, 100) + '...',
        url: `${window.location.origin}/story/${story.id}`
      }).catch(console.error);
    } else {
      // 复制链接到剪贴板
      const url = `${window.location.origin}/story/${story.id}`;
      navigator.clipboard.writeText(url).then(() => {
        alert('故事链接已复制到剪贴板！');
      }).catch(() => {
        alert(`故事链接: ${url}`);
      });
    }
  };

  // 格式化日期
  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('zh-CN');
  };

  // 截断文本
  const truncateText = (text: string, maxLength: number = 200) => {
    if (text.length <= maxLength) return text;
    return text.substring(0, maxLength) + '...';
  };

  if (loading) {
    return (
      <div className="min-h-screen">
        <div className="container mx-auto py-8 px-6">
          <h1 className="text-3xl font-bold mb-6">故事墙</h1>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {Array.from({ length: 6 }).map((_, index) => (
              <Card key={index} className="animate-pulse">
                <CardHeader>
                  <div className="h-6 bg-gray-200 rounded w-3/4"></div>
                </CardHeader>
                <CardContent>
                  <div className="space-y-2">
                    <div className="h-4 bg-gray-200 rounded"></div>
                    <div className="h-4 bg-gray-200 rounded"></div>
                    <div className="h-4 bg-gray-200 rounded w-2/3"></div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="min-h-screen">
        <div className="container mx-auto py-8 px-6">
          <h1 className="text-3xl font-bold mb-6">故事墙</h1>
          <Card className="border-red-200 bg-red-50">
            <CardContent className="pt-6">
              <div className="text-center">
                <h3 className="text-lg font-medium text-red-800 mb-2">加载失败</h3>
                <p className="text-red-600 mb-4">{error}</p>
                <Button onClick={() => fetchStories(page)} variant="outline">
                  重试
                </Button>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen">
      <div className="container mx-auto py-8 px-6">


        {/* 状态监控面板 */}
        <Card className="mb-6 bg-yellow-50 border-yellow-200">
          <CardHeader>
            <CardTitle className="text-lg">状态监控</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
              <div>
                <span className="font-medium">加载状态:</span>
                <span className={`ml-2 ${loading ? 'text-blue-600' : 'text-green-600'}`}>
                  {loading ? '加载中...' : '已完成'}
                </span>
              </div>
              <div>
                <span className="font-medium">故事数量:</span>
                <span className="ml-2 text-blue-600">{stories.length}</span>
              </div>
              <div>
                <span className="font-medium">错误状态:</span>
                <span className={`ml-2 ${error ? 'text-red-600' : 'text-green-600'}`}>
                  {error || '正常'}
                </span>
              </div>
              <div>
                <span className="font-medium">总页数:</span>
                <span className="ml-2 text-blue-600">{totalPages}</span>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* 页面标题 */}
        <div className="mb-8">
          <div className="flex items-center gap-3 mb-2">
            <h1 className="text-3xl font-bold">故事墙</h1>
            <div className="flex items-center gap-1 px-3 py-1 bg-gray-100 rounded-full">
              {sortBy === 'popular' ? (
                <>
                  <Flame className="h-4 w-4 text-orange-500" />
                  <span className="text-sm font-medium text-orange-700">热门</span>
                </>
              ) : (
                <>
                  <Clock className="h-4 w-4 text-blue-500" />
                  <span className="text-sm font-medium text-blue-700">最新</span>
                </>
              )}
            </div>
          </div>
          <p className="text-gray-600">
            在这里，你可以匿名分享你的就业故事、经验和建议，也可以阅读其他人的故事，获取灵感和共鸣。
          </p>
        </div>



        {/* 移动端筛选按钮 */}
        <div className="mb-6 lg:hidden">
          <div className="flex items-center gap-4">
            <Button onClick={handleShareStory} className="bg-blue-600 hover:bg-blue-700">
              <Plus className="h-4 w-4 mr-2" />
              分享故事
            </Button>

            <Button
              variant="outline"
              onClick={() => setIsMobileFilterOpen(true)}
              className="relative"
            >
              <Search className="h-4 w-4 mr-2" />
              搜索筛选
              {isFilterActive && (
                <Badge
                  variant="destructive"
                  className="absolute -top-2 -right-2 h-5 w-5 p-0 flex items-center justify-center text-xs"
                >
                  !
                </Badge>
              )}
            </Button>
          </div>

          {/* 活跃筛选条件显示 */}
          {isFilterActive && (
            <div className="mt-4 p-3 bg-blue-50 border border-blue-200 rounded-lg">
              <div className="flex items-center justify-between">
                <span className="text-sm text-blue-700">
                  已应用 {[
                    searchQuery && '搜索',
                    selectedCategory !== 'all' && '分类',
                    selectedEducationLevel !== 'all' && '学历',
                    selectedIndustry !== 'all' && '行业',
                    (tagMode === 'single' && selectedTag !== 'all') && '单标签',
                    (tagMode === 'multi' && selectedTags.length > 0) && '多标签'
                  ].filter(Boolean).length} 个筛选条件
                </span>
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={handleClearFilters}
                  className="text-blue-700 hover:text-blue-800"
                >
                  清除
                </Button>
              </div>
            </div>
          )}
        </div>

        {/* 桌面端高级搜索区域 */}
        <div className="mb-6 hidden lg:block">
          <SimpleAdvancedSearch
            searchQuery={searchQuery}
            onSearchChange={handleSearch}
            selectedCategory={selectedCategory}
            onCategoryChange={setSelectedCategory}
            selectedEducationLevel={selectedEducationLevel}
            onEducationLevelChange={setSelectedEducationLevel}
            selectedIndustry={selectedIndustry}
            onIndustryChange={setSelectedIndustry}
            onClearFilters={handleClearFilters}
            isFilterActive={isFilterActive}
          />
        </div>

        {/* 桌面端标签选择区域 - 临时简化版本 */}
        <div className="mb-6 hidden lg:block">
          <Card>
            <CardHeader>
              <CardTitle className="text-lg flex items-center gap-2">
                <TagIcon className="h-5 w-5" />
                标签筛选 (简化版)
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div className="flex items-center gap-2">
                  <span className="text-sm text-gray-600">标签模式:</span>
                  <Button
                    variant={tagMode === 'single' ? 'default' : 'outline'}
                    size="sm"
                    onClick={() => handleTagModeChange('single')}
                  >
                    单标签
                  </Button>
                  <Button
                    variant={tagMode === 'multi' ? 'default' : 'outline'}
                    size="sm"
                    onClick={() => handleTagModeChange('multi')}
                  >
                    多标签
                  </Button>
                </div>

                {tagMode === 'single' && (
                  <div className="space-y-2">
                    <span className="text-sm text-gray-600">当前选择: {selectedTag}</span>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => handleTagSelect('all')}
                    >
                      清除标签
                    </Button>
                  </div>
                )}

                {tagMode === 'multi' && (
                  <div className="space-y-2">
                    <span className="text-sm text-gray-600">
                      已选择 {selectedTags.length} 个标签: {selectedTags.join(', ') || '无'}
                    </span>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => handleTagsChange([])}
                    >
                      清除所有标签
                    </Button>
                  </div>
                )}
              </div>
            </CardContent>
          </Card>
        </div>

        {/* 操作按钮区域 */}
        <div className="mb-6 space-y-4">
          <div className="flex justify-between items-center">
            <Button onClick={handleShareStory} className="bg-blue-600 hover:bg-blue-700">
              <Plus className="h-4 w-4 mr-2" />
              分享故事
            </Button>

            {isFilterActive && (
              <Badge variant="outline" className="bg-blue-50 text-blue-700">
                已应用 {[
                  searchQuery && '搜索',
                  selectedCategory !== 'all' && '分类',
                  selectedEducationLevel !== 'all' && '学历',
                  selectedIndustry !== 'all' && '行业',
                  (tagMode === 'single' && selectedTag !== 'all') && '单标签',
                  (tagMode === 'multi' && selectedTags.length > 0) && '多标签'
                ].filter(Boolean).length} 个筛选条件
              </Badge>
            )}
          </div>

          {/* 操作按钮和排序 */}
          <div className="flex flex-wrap gap-4 items-center">
            <Button onClick={() => fetchStories(page, pageSize, sortBy, searchQuery, selectedCategory, selectedEducationLevel, selectedIndustry, selectedTag)}>
              刷新数据
            </Button>
            <Button onClick={() => navigate('/story-wall')} variant="outline">
              返回原版故事墙
            </Button>

            {/* 排序选择器 */}
            <div className="flex items-center gap-2">
              <span className="text-sm text-gray-600">排序:</span>
              <Select value={sortBy} onValueChange={(value: 'latest' | 'popular') => handleSortChange(value)}>
                <SelectTrigger className="w-24">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="latest">最新</SelectItem>
                  <SelectItem value="popular">热门</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>

          {/* 分页控制 */}
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-4">
              <span className="text-sm text-gray-600">
                每页显示:
              </span>
              <Select value={pageSize.toString()} onValueChange={(value) => handlePageSizeChange(Number(value))}>
                <SelectTrigger className="w-20">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="3">3</SelectItem>
                  <SelectItem value="6">6</SelectItem>
                  <SelectItem value="9">9</SelectItem>
                  <SelectItem value="12">12</SelectItem>
                  <SelectItem value="18">18</SelectItem>
                  <SelectItem value="24">24</SelectItem>
                </SelectContent>
              </Select>
              <span className="text-sm text-gray-600">
                条记录
              </span>
            </div>

            <div className="flex items-center gap-2">
              <Button
                variant="outline"
                size="sm"
                onClick={() => handlePageChange(page - 1)}
                disabled={page <= 1}
              >
                <ChevronLeft className="h-4 w-4" />
                上一页
              </Button>

              <span className="text-sm text-gray-600 px-4">
                第 {page} 页，共 {totalPages} 页
              </span>

              <Button
                variant="outline"
                size="sm"
                onClick={() => handlePageChange(page + 1)}
                disabled={page >= totalPages}
              >
                下一页
                <ChevronRight className="h-4 w-4" />
              </Button>
            </div>
          </div>
        </div>

        {/* 搜索结果提示 */}
        {searchQuery && (
          <div className="mb-4 p-3 bg-blue-50 border border-blue-200 rounded-lg">
            <p className="text-sm text-blue-700">
              搜索 "{searchQuery}" 的结果：共找到 {totalItems} 个故事
            </p>
          </div>
        )}

        {/* 故事列表 - 响应式网格 */}
        <ResponsiveStoryGrid
          stories={stories}
          loading={loading}
          sortBy={sortBy}
          onStoryClick={(story) => navigate(`/story/${story.id}`)}
          onStoryShare={handleShareSingleStory}
          onStoryLike={(story) => {
            // TODO: 实现点赞功能
            console.log('点赞故事:', story.title);
          }}
          onStoryBookmark={(story) => {
            // TODO: 实现收藏功能
            console.log('收藏故事:', story.title);
          }}
          emptyMessage={searchQuery ? '未找到匹配的故事' : '暂无故事'}
          emptyDescription={
            searchQuery
              ? `没有找到包含 "${searchQuery}" 的故事，试试其他关键词吧！`
              : '暂无故事，成为第一个分享故事的人吧！'
          }
        />

        {/* 底部分页控制 */}
        {stories.length > 0 && totalPages > 1 && (
          <div className="mt-8 flex items-center justify-center">
            <div className="flex items-center gap-2">
              <Button
                variant="outline"
                size="sm"
                onClick={() => handlePageChange(page - 1)}
                disabled={page <= 1}
              >
                <ChevronLeft className="h-4 w-4" />
                上一页
              </Button>

              {/* 页码按钮 */}
              {Array.from({ length: Math.min(5, totalPages) }, (_, i) => {
                let pageNum;
                if (totalPages <= 5) {
                  pageNum = i + 1;
                } else if (page <= 3) {
                  pageNum = i + 1;
                } else if (page >= totalPages - 2) {
                  pageNum = totalPages - 4 + i;
                } else {
                  pageNum = page - 2 + i;
                }

                return (
                  <Button
                    key={pageNum}
                    variant={page === pageNum ? "default" : "outline"}
                    size="sm"
                    onClick={() => handlePageChange(pageNum)}
                    className="w-10"
                  >
                    {pageNum}
                  </Button>
                );
              })}

              <Button
                variant="outline"
                size="sm"
                onClick={() => handlePageChange(page + 1)}
                disabled={page >= totalPages}
              >
                下一页
                <ChevronRight className="h-4 w-4" />
              </Button>
            </div>
          </div>
        )}

        {/* 移动端筛选抽屉 */}
        <MobileFilterDrawer
          open={isMobileFilterOpen}
          onOpenChange={setIsMobileFilterOpen}
          tags={STORY_TAGS.filter(tag => tag.id !== 'all')}
          selectedTags={tagMode === 'multi' ? selectedTags : (selectedTag !== 'all' ? [selectedTag] : [])}
          onTagsChange={(tags) => {
            if (tagMode === 'multi') {
              setSelectedTags(tags);
            } else {
              setSelectedTag(tags.length > 0 ? tags[0] : 'all');
            }
            setPage(1);
          }}
          searchQuery={searchQuery}
          onSearchChange={handleSearch}
          categories={STORY_CATEGORIES}
          selectedCategory={selectedCategory}
          onCategoryChange={setSelectedCategory}
          educationLevels={EDUCATION_LEVELS}
          selectedEducationLevel={selectedEducationLevel}
          onEducationLevelChange={setSelectedEducationLevel}
          industries={INDUSTRIES}
          selectedIndustry={selectedIndustry}
          onIndustryChange={setSelectedIndustry}
          onClearFilters={handleClearFilters}
          onApplyFilters={handleMobileApplyFilters}
        />
      </div>
    </div>
  );
}
