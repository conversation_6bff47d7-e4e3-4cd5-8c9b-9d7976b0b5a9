<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>登录功能测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        .test-section {
            margin: 20px 0;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 8px;
        }
        .test-button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            margin: 5px;
            border-radius: 4px;
            cursor: pointer;
        }
        .test-button:hover {
            background: #0056b3;
        }
        .result {
            margin: 10px 0;
            padding: 10px;
            border-radius: 4px;
            font-family: monospace;
            white-space: pre-wrap;
        }
        .success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .info {
            background: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        #results {
            max-height: 400px;
            overflow-y: auto;
        }
    </style>
</head>
<body>
    <h1>登录功能直接测试</h1>
    
    <div class="test-section">
        <h2>说明</h2>
        <p>这个页面直接在浏览器环境中测试登录功能，可以看到详细的控制台输出。</p>
        <p>请打开浏览器开发者工具的控制台查看详细日志。</p>
    </div>

    <div class="test-section">
        <h2>测试按钮</h2>
        <button class="test-button" onclick="testReviewerLogin()">测试审核员登录</button>
        <button class="test-button" onclick="testAdminLogin()">测试管理员登录</button>
        <button class="test-button" onclick="testSuperAdminLogin()">测试超级管理员登录</button>
        <button class="test-button" onclick="clearCache()">清除缓存</button>
    </div>

    <div class="test-section">
        <h2>测试结果</h2>
        <div id="results"></div>
    </div>

    <script type="module">
        function addResult(message, type = 'info') {
            const results = document.getElementById('results');
            const div = document.createElement('div');
            div.className = `result ${type}`;
            div.innerHTML = `<strong>${new Date().toLocaleTimeString()}</strong>: ${message}`;
            results.appendChild(div);
            results.scrollTop = results.scrollHeight;
        }

        function clearCache() {
            try {
                localStorage.clear();
                sessionStorage.clear();
                addResult('所有缓存已清除', 'success');
                console.log('缓存已清除');
            } catch (error) {
                addResult(`清除缓存失败: ${error.message}`, 'error');
                console.error('清除缓存失败:', error);
            }
        }

        async function testLogin(username, password, role) {
            addResult(`开始测试 ${role} 登录 (${username})`, 'info');
            console.log(`开始测试 ${role} 登录`, { username, password });
            
            try {
                // 清除之前的登录信息
                localStorage.removeItem('adminToken');
                localStorage.removeItem('adminUser');
                sessionStorage.removeItem('redirectToRoleDashboard');
                
                console.log('已清除之前的登录信息');
                
                // 直接调用登录API
                const response = await fetch('/api/admin/login', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        username: username,
                        password: password
                    })
                });

                console.log('API响应状态:', response.status);
                console.log('API响应头:', Object.fromEntries(response.headers.entries()));

                if (response.ok) {
                    const data = await response.json();
                    console.log('登录成功，响应数据:', data);
                    
                    addResult(`登录成功: ${JSON.stringify(data, null, 2)}`, 'success');
                    
                    // 保存登录信息
                    if (data.token) {
                        localStorage.setItem('adminToken', data.token);
                    }
                    if (data.user) {
                        localStorage.setItem('adminUser', JSON.stringify({
                            ...data.user,
                            loginTime: new Date().toISOString()
                        }));
                    }
                    
                    addResult('登录信息已保存到localStorage', 'info');
                    
                } else {
                    const errorText = await response.text();
                    console.error('登录失败，响应内容:', errorText);
                    addResult(`登录失败 (${response.status}): ${errorText}`, 'error');
                }
                
            } catch (error) {
                console.error('登录请求异常:', error);
                addResult(`登录请求异常: ${error.message}`, 'error');
            }
        }

        window.testReviewerLogin = () => testLogin('reviewer', 'reviewer123', '审核员');
        window.testAdminLogin = () => testLogin('admin', 'admin123', '管理员');
        window.testSuperAdminLogin = () => testLogin('superadmin', 'super123', '超级管理员');
        window.clearCache = clearCache;

        // 页面加载时的初始化
        window.addEventListener('load', function() {
            addResult('页面加载完成，准备开始测试', 'info');
            console.log('测试页面已加载');
            
            // 检查当前环境
            console.log('当前URL:', window.location.href);
            console.log('User Agent:', navigator.userAgent);
        });
    </script>
</body>
</html>
