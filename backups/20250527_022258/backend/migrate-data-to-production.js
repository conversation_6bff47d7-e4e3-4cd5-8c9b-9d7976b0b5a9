#!/usr/bin/env node

/**
 * 数据迁移脚本：将本地数据库数据导出并导入到Cloudflare D1生产环境
 *
 * 使用方法：
 * 1. node migrate-data-to-production.js export  # 导出本地数据
 * 2. node migrate-data-to-production.js import  # 导入到生产环境
 */

import sqlite3 from 'sqlite3';
import { open } from 'sqlite';
import fs from 'fs/promises';
import path from 'path';
import { execSync } from 'child_process';

const LOCAL_DB_PATH = './database.db';
const EXPORT_DIR = './data-export';

// 需要迁移的表
const TABLES_TO_MIGRATE = [
  'questionnaire_responses_v2',
  'questionnaire_voices_v2',
  'story_contents_v2',
  'users_v2',
  'review_logs_v2',
  'review_records_v2',
  'content_tags_v2',
  'tags_v2',
  'system_config_v2'
];

async function openLocalDatabase() {
  return await open({
    filename: LOCAL_DB_PATH,
    driver: sqlite3.Database
  });
}

async function exportTableData(db, tableName) {
  console.log(`📤 导出表: ${tableName}`);

  try {
    // 获取表结构
    const schema = await db.get(`SELECT sql FROM sqlite_master WHERE type='table' AND name=?`, tableName);

    // 获取数据
    const rows = await db.all(`SELECT * FROM ${tableName}`);

    console.log(`   ✅ 导出 ${rows.length} 条记录`);

    return {
      tableName,
      schema: schema?.sql,
      data: rows,
      count: rows.length
    };
  } catch (error) {
    console.error(`   ❌ 导出表 ${tableName} 失败:`, error.message);
    return {
      tableName,
      schema: null,
      data: [],
      count: 0,
      error: error.message
    };
  }
}

async function exportData() {
  console.log('🚀 开始导出本地数据库数据...');

  // 创建导出目录
  await fs.mkdir(EXPORT_DIR, { recursive: true });

  const db = await openLocalDatabase();
  const exportData = {
    timestamp: new Date().toISOString(),
    tables: {}
  };

  let totalRecords = 0;

  for (const tableName of TABLES_TO_MIGRATE) {
    const tableData = await exportTableData(db, tableName);
    exportData.tables[tableName] = tableData;
    totalRecords += tableData.count;
  }

  await db.close();

  // 保存导出数据
  const exportFile = path.join(EXPORT_DIR, `export-${Date.now()}.json`);
  await fs.writeFile(exportFile, JSON.stringify(exportData, null, 2));

  console.log(`\n📊 导出完成！`);
  console.log(`   总记录数: ${totalRecords}`);
  console.log(`   导出文件: ${exportFile}`);

  // 生成导入脚本
  await generateImportScript(exportData);

  return exportFile;
}

async function generateImportScript(exportData) {
  console.log('📝 生成导入脚本...');

  const sqlStatements = [];

  // 禁用外键约束
  sqlStatements.push('-- 禁用外键约束');
  sqlStatements.push('PRAGMA foreign_keys = OFF;');
  sqlStatements.push('');

  // 按依赖顺序排序表（只包含生产环境存在的表）
  const tableOrder = [
    'users_v2',
    'questionnaire_responses_v2',
    'questionnaire_voices_v2',
    'story_contents_v2'
  ];

  for (const tableName of tableOrder) {
    const tableData = exportData.tables[tableName];
    if (!tableData || tableData.data.length === 0) continue;

    // 清空表
    sqlStatements.push(`-- 清空表 ${tableName}`);
    sqlStatements.push(`DELETE FROM ${tableName};`);
    sqlStatements.push('');

    // 插入数据
    sqlStatements.push(`-- 插入数据到 ${tableName} (${tableData.count} 条记录)`);

    for (const row of tableData.data) {
      const columns = Object.keys(row);
      const values = Object.values(row).map(val => {
        if (val === null) return 'NULL';
        if (typeof val === 'string') return `'${val.replace(/'/g, "''")}'`;
        return val;
      });

      const insertSQL = `INSERT INTO ${tableName} (${columns.join(', ')}) VALUES (${values.join(', ')});`;
      sqlStatements.push(insertSQL);
    }

    sqlStatements.push('');
  }

  // 重新启用外键约束
  sqlStatements.push('-- 重新启用外键约束');
  sqlStatements.push('PRAGMA foreign_keys = ON;');
  sqlStatements.push('');

  const scriptFile = path.join(EXPORT_DIR, 'import-to-d1.sql');
  await fs.writeFile(scriptFile, sqlStatements.join('\n'));

  console.log(`   ✅ 导入脚本已生成: ${scriptFile}`);
}

async function importToProduction() {
  console.log('🚀 开始导入数据到生产环境...');

  // 查找最新的导出文件
  const files = await fs.readdir(EXPORT_DIR);
  const exportFiles = files.filter(f => f.startsWith('export-') && f.endsWith('.json'));

  if (exportFiles.length === 0) {
    console.error('❌ 未找到导出文件，请先运行导出命令');
    process.exit(1);
  }

  const latestFile = exportFiles.sort().pop();
  const exportFile = path.join(EXPORT_DIR, latestFile);

  console.log(`📂 使用导出文件: ${exportFile}`);

  // 检查SQL脚本文件
  const sqlFile = path.join(EXPORT_DIR, 'import-to-d1.sql');
  try {
    await fs.access(sqlFile);
  } catch {
    console.error('❌ 未找到SQL导入脚本，请先运行导出命令');
    process.exit(1);
  }

  console.log('📤 执行数据导入到Cloudflare D1...');

  try {
    // 使用wrangler执行SQL文件
    const command = `wrangler d1 execute college-employment-survey-realapi --file="${sqlFile}" --remote`;
    console.log(`执行命令: ${command}`);

    const output = execSync(command, {
      encoding: 'utf8',
      stdio: 'pipe'
    });

    console.log('✅ 数据导入成功！');
    console.log(output);

  } catch (error) {
    console.error('❌ 数据导入失败:', error.message);
    console.error('输出:', error.stdout);
    console.error('错误:', error.stderr);
    process.exit(1);
  }
}

async function verifyImport() {
  console.log('🔍 验证导入结果...');

  for (const tableName of TABLES_TO_MIGRATE) {
    try {
      const command = `wrangler d1 execute college-employment-survey-realapi --command="SELECT COUNT(*) as count FROM ${tableName};" --remote --json`;
      const output = execSync(command, { encoding: 'utf8' });
      const result = JSON.parse(output);
      const count = result[0]?.results?.[0]?.count || 0;

      console.log(`   ${tableName}: ${count} 条记录`);
    } catch (error) {
      console.error(`   ❌ 验证表 ${tableName} 失败:`, error.message);
    }
  }
}

// 主函数
async function main() {
  const command = process.argv[2];

  switch (command) {
    case 'export':
      await exportData();
      break;

    case 'import':
      await importToProduction();
      await verifyImport();
      break;

    case 'verify':
      await verifyImport();
      break;

    default:
      console.log(`
使用方法:
  node migrate-data-to-production.js export   # 导出本地数据
  node migrate-data-to-production.js import   # 导入到生产环境
  node migrate-data-to-production.js verify   # 验证导入结果
      `);
      process.exit(1);
  }
}

// 错误处理
process.on('unhandledRejection', (error) => {
  console.error('❌ 未处理的错误:', error);
  process.exit(1);
});

main().catch(console.error);
