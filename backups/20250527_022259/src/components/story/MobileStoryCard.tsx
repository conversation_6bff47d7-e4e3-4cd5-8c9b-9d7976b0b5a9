import { useState, useRef, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import {
  ThumbsUp,
  Calendar,
  User,
  Share2,
  Heart,
  MessageCircle,
  Bookmark,
  MoreHorizontal,
  ExternalLink
} from 'lucide-react';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { cn } from '@/lib/utils';

interface Story {
  id: number;
  title: string;
  content: string;
  author: string;
  likes: number;
  createdAt: string;
  category: string;
  educationLevel: string;
  industry: string;
  tags?: string[];
}

interface MobileStoryCardProps {
  story: Story;
  onShare?: (story: Story) => void;
  onLike?: (story: Story) => void;
  onBookmark?: (story: Story) => void;
  onViewDetails?: (story: Story) => void;
  className?: string;
  isLiked?: boolean;
  isBookmarked?: boolean;
}

export default function MobileStoryCard({
  story,
  onShare,
  onLike,
  onBookmark,
  onViewDetails,
  className,
  isLiked = false,
  isBookmarked = false,
}: MobileStoryCardProps) {
  const [isExpanded, setIsExpanded] = useState(false);
  const [localLikes, setLocalLikes] = useState(story.likes);
  const [localIsLiked, setLocalIsLiked] = useState(isLiked);

  // 触摸手势状态
  const [touchStart, setTouchStart] = useState<{ x: number; y: number; time: number } | null>(null);
  const [isPressed, setIsPressed] = useState(false);
  const cardRef = useRef<HTMLDivElement>(null);

  // 格式化日期
  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    const now = new Date();
    const diffTime = Math.abs(now.getTime() - date.getTime());
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));

    if (diffDays === 1) return '今天';
    if (diffDays === 2) return '昨天';
    if (diffDays <= 7) return `${diffDays}天前`;
    if (diffDays <= 30) return `${Math.ceil(diffDays / 7)}周前`;
    if (diffDays <= 365) return `${Math.ceil(diffDays / 30)}个月前`;
    return `${Math.ceil(diffDays / 365)}年前`;
  };

  // 处理点赞
  const handleLike = (e: React.MouseEvent) => {
    e.stopPropagation();
    const newIsLiked = !localIsLiked;
    setLocalIsLiked(newIsLiked);
    setLocalLikes(prev => newIsLiked ? prev + 1 : prev - 1);
    onLike?.(story);
  };

  // 处理分享
  const handleShare = (e: React.MouseEvent) => {
    e.stopPropagation();
    onShare?.(story);
  };

  // 处理收藏
  const handleBookmark = (e: React.MouseEvent) => {
    e.stopPropagation();
    onBookmark?.(story);
  };

  // 处理查看详情
  const handleViewDetails = () => {
    onViewDetails?.(story);
  };

  // 切换展开状态
  const toggleExpanded = (e: React.MouseEvent) => {
    e.stopPropagation();
    setIsExpanded(!isExpanded);
  };

  // 触摸事件处理
  const handleTouchStart = (e: React.TouchEvent) => {
    const touch = e.touches[0];
    setTouchStart({
      x: touch.clientX,
      y: touch.clientY,
      time: Date.now()
    });
    setIsPressed(true);
  };

  const handleTouchEnd = (e: React.TouchEvent) => {
    if (!touchStart) return;

    const touch = e.changedTouches[0];
    const deltaX = touch.clientX - touchStart.x;
    const deltaY = touch.clientY - touchStart.y;
    const deltaTime = Date.now() - touchStart.time;

    // 检测滑动手势
    const isSwipe = Math.abs(deltaX) > 50 && Math.abs(deltaY) < 100 && deltaTime < 300;

    if (isSwipe) {
      e.preventDefault();
      if (deltaX > 0) {
        // 右滑 - 点赞
        handleLike(e as any);
      } else {
        // 左滑 - 分享
        handleShare(e as any);
      }
    } else if (Math.abs(deltaX) < 10 && Math.abs(deltaY) < 10 && deltaTime < 300) {
      // 点击
      handleViewDetails();
    }

    setTouchStart(null);
    setIsPressed(false);
  };

  const handleTouchMove = (e: React.TouchEvent) => {
    if (!touchStart) return;

    const touch = e.touches[0];
    const deltaX = touch.clientX - touchStart.x;

    // 如果是水平滑动，阻止默认行为（防止页面滚动）
    if (Math.abs(deltaX) > 10) {
      e.preventDefault();
    }
  };

  // 截断内容
  const truncatedContent = story.content.length > 150
    ? story.content.substring(0, 150) + '...'
    : story.content;

  return (
    <Card
      ref={cardRef}
      className={cn(
        "cursor-pointer transition-all duration-200 hover:shadow-md",
        "border-l-4 border-l-blue-500 select-none",
        isPressed && "scale-[0.98] shadow-lg",
        className
      )}
      onClick={handleViewDetails}
      onTouchStart={handleTouchStart}
      onTouchEnd={handleTouchEnd}
      onTouchMove={handleTouchMove}
    >
      <CardHeader className="pb-3">
        <div className="flex items-start justify-between">
          <CardTitle className="text-base font-medium line-clamp-2 flex-1 pr-2">
            {story.title}
          </CardTitle>

          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button
                variant="ghost"
                size="sm"
                className="h-8 w-8 p-0 flex-shrink-0"
                onClick={(e) => e.stopPropagation()}
              >
                <MoreHorizontal className="h-4 w-4" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end">
              <DropdownMenuItem onClick={handleViewDetails}>
                <ExternalLink className="h-4 w-4 mr-2" />
                查看详情
              </DropdownMenuItem>
              <DropdownMenuItem onClick={handleShare}>
                <Share2 className="h-4 w-4 mr-2" />
                分享故事
              </DropdownMenuItem>
              <DropdownMenuItem onClick={handleBookmark}>
                <Bookmark className="h-4 w-4 mr-2" />
                {isBookmarked ? '取消收藏' : '收藏故事'}
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        </div>

        {/* 标签 */}
        <div className="flex flex-wrap gap-1 mt-2">
          <Badge variant="outline" className="text-xs px-2 py-0.5">
            {story.category}
          </Badge>
          <Badge variant="outline" className="text-xs px-2 py-0.5">
            {story.educationLevel}
          </Badge>
          <Badge variant="outline" className="text-xs px-2 py-0.5">
            {story.industry}
          </Badge>
        </div>
      </CardHeader>

      <CardContent className="pt-0">
        {/* 内容 */}
        <div className="text-sm text-gray-600 mb-4">
          <p className={cn(
            "leading-relaxed",
            !isExpanded && "line-clamp-3"
          )}>
            {isExpanded ? story.content : truncatedContent}
          </p>

          {story.content.length > 150 && (
            <Button
              variant="ghost"
              size="sm"
              onClick={toggleExpanded}
              className="h-auto p-0 mt-2 text-blue-600 hover:text-blue-700"
            >
              {isExpanded ? '收起' : '展开'}
            </Button>
          )}
        </div>

        {/* 底部信息和操作 */}
        <div className="space-y-3">
          {/* 作者和时间 */}
          <div className="flex items-center text-xs text-gray-500">
            <User className="h-3 w-3 mr-1" />
            <span className="mr-3">{story.author}</span>
            <Calendar className="h-3 w-3 mr-1" />
            <span>{formatDate(story.createdAt)}</span>
          </div>

          {/* 操作按钮 */}
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-4">
              {/* 点赞 */}
              <Button
                variant="ghost"
                size="sm"
                onClick={handleLike}
                className={cn(
                  "h-8 px-2 gap-1",
                  localIsLiked && "text-red-600 hover:text-red-700"
                )}
              >
                <Heart className={cn(
                  "h-4 w-4",
                  localIsLiked && "fill-current"
                )} />
                <span className="text-xs">{localLikes}</span>
              </Button>

              {/* 评论 */}
              <Button
                variant="ghost"
                size="sm"
                className="h-8 px-2 gap-1"
                onClick={(e) => {
                  e.stopPropagation();
                  // TODO: 实现评论功能
                }}
              >
                <MessageCircle className="h-4 w-4" />
                <span className="text-xs">评论</span>
              </Button>
            </div>

            <div className="flex items-center gap-2">
              {/* 收藏 */}
              <Button
                variant="ghost"
                size="sm"
                onClick={handleBookmark}
                className={cn(
                  "h-8 w-8 p-0",
                  isBookmarked && "text-yellow-600 hover:text-yellow-700"
                )}
              >
                <Bookmark className={cn(
                  "h-4 w-4",
                  isBookmarked && "fill-current"
                )} />
              </Button>

              {/* 分享 */}
              <Button
                variant="ghost"
                size="sm"
                onClick={handleShare}
                className="h-8 w-8 p-0"
              >
                <Share2 className="h-4 w-4" />
              </Button>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}
