#!/usr/bin/env node

/**
 * 故事墙数据结构验证工具
 * 
 * 功能：
 * 1. 验证UUID格式和唯一性
 * 2. 检查内容ID的一致性
 * 3. 验证标签ID关联
 * 4. 检查数据库完整性
 * 5. 生成数据迁移脚本
 */

const fs = require('fs');
const path = require('path');
const crypto = require('crypto');

// UUID格式验证
class UUIDValidator {
  static patterns = {
    user: /^(anon|user)-\d+-[a-z0-9]+$/,
    content: /^txid-\d+-[a-z0-9]+$/,
    tag: /^tag-[a-z]+-[a-z0-9]+$/,
    export: /^export-\d+-[a-z0-9]+$/,
  };

  static validate(id, type) {
    const pattern = this.patterns[type];
    if (!pattern) {
      throw new Error(`Unknown ID type: ${type}`);
    }
    return pattern.test(id);
  }

  static generateReport(ids, type) {
    const valid = [];
    const invalid = [];
    
    ids.forEach(id => {
      if (this.validate(id, type)) {
        valid.push(id);
      } else {
        invalid.push(id);
      }
    });

    return {
      type,
      total: ids.length,
      valid: valid.length,
      invalid: invalid.length,
      invalidIds: invalid,
      validationRate: (valid.length / ids.length * 100).toFixed(2) + '%'
    };
  }
}

// 数据一致性检查器
class DataConsistencyChecker {
  constructor(dbPath) {
    this.dbPath = dbPath;
    this.issues = [];
  }

  async checkUserUUIDs() {
    console.log('🔍 检查用户UUID...');
    
    // 模拟数据库查询（实际应该连接真实数据库）
    const mockUsers = [
      'anon-1703123456-abc123',
      'user-1703123457-def456',
      'invalid-uuid-format',
      'anon-1703123458-ghi789'
    ];

    const report = UUIDValidator.generateReport(mockUsers, 'user');
    
    if (report.invalid > 0) {
      this.issues.push({
        type: 'UUID_FORMAT_ERROR',
        severity: 'HIGH',
        description: `发现 ${report.invalid} 个格式错误的用户UUID`,
        details: report.invalidIds,
        solution: '需要重新生成或修复这些UUID'
      });
    }

    console.log(`✅ 用户UUID检查完成: ${report.validationRate} 通过率`);
    return report;
  }

  async checkContentIDs() {
    console.log('🔍 检查内容ID...');
    
    const mockContentIds = [
      'txid-1703123456-abc123',
      'txid-1703123457-def456',
      'invalid-content-id',
      'txid-1703123458-ghi789'
    ];

    const report = UUIDValidator.generateReport(mockContentIds, 'content');
    
    if (report.invalid > 0) {
      this.issues.push({
        type: 'CONTENT_ID_FORMAT_ERROR',
        severity: 'HIGH',
        description: `发现 ${report.invalid} 个格式错误的内容ID`,
        details: report.invalidIds,
        solution: '需要重新生成或修复这些内容ID'
      });
    }

    console.log(`✅ 内容ID检查完成: ${report.validationRate} 通过率`);
    return report;
  }

  async checkTagAssociations() {
    console.log('🔍 检查标签关联...');
    
    // 模拟检查标签关联的完整性
    const mockAssociations = [
      { storyId: 'txid-1703123456-abc123', tagId: 'tag-job-hunting' },
      { storyId: 'txid-1703123457-def456', tagId: 'tag-education-bachelor' },
      { storyId: 'txid-invalid', tagId: 'tag-job-hunting' }, // 无效关联
      { storyId: 'txid-1703123458-ghi789', tagId: 'tag-invalid' }, // 无效标签
    ];

    const orphanedAssociations = mockAssociations.filter(assoc => 
      !UUIDValidator.validate(assoc.storyId, 'content') || 
      !UUIDValidator.validate(assoc.tagId, 'tag')
    );

    if (orphanedAssociations.length > 0) {
      this.issues.push({
        type: 'ORPHANED_TAG_ASSOCIATIONS',
        severity: 'MEDIUM',
        description: `发现 ${orphanedAssociations.length} 个无效的标签关联`,
        details: orphanedAssociations,
        solution: '清理无效的标签关联记录'
      });
    }

    console.log(`✅ 标签关联检查完成: 发现 ${orphanedAssociations.length} 个问题`);
    return {
      total: mockAssociations.length,
      valid: mockAssociations.length - orphanedAssociations.length,
      invalid: orphanedAssociations.length,
      issues: orphanedAssociations
    };
  }

  async checkDataIntegrity() {
    console.log('🔍 检查数据完整性...');
    
    // 检查引用完整性
    const integrityIssues = [];
    
    // 模拟检查故事是否有对应的用户
    const storiesWithoutUsers = [
      { contentId: 'txid-1703123459-orphan', userUuid: 'user-nonexistent' }
    ];
    
    if (storiesWithoutUsers.length > 0) {
      integrityIssues.push({
        type: 'STORIES_WITHOUT_USERS',
        count: storiesWithoutUsers.length,
        details: storiesWithoutUsers
      });
    }

    // 模拟检查标签使用统计
    const tagsWithWrongStats = [
      { tagId: 'tag-job-hunting', actualUsage: 5, recordedUsage: 3 }
    ];

    if (tagsWithWrongStats.length > 0) {
      integrityIssues.push({
        type: 'INCORRECT_TAG_STATS',
        count: tagsWithWrongStats.length,
        details: tagsWithWrongStats
      });
    }

    if (integrityIssues.length > 0) {
      this.issues.push({
        type: 'DATA_INTEGRITY_ERROR',
        severity: 'HIGH',
        description: '发现数据完整性问题',
        details: integrityIssues,
        solution: '需要修复数据引用关系和统计信息'
      });
    }

    console.log(`✅ 数据完整性检查完成: 发现 ${integrityIssues.length} 类问题`);
    return integrityIssues;
  }

  generateReport() {
    const report = {
      timestamp: new Date().toISOString(),
      summary: {
        totalIssues: this.issues.length,
        highSeverity: this.issues.filter(i => i.severity === 'HIGH').length,
        mediumSeverity: this.issues.filter(i => i.severity === 'MEDIUM').length,
        lowSeverity: this.issues.filter(i => i.severity === 'LOW').length,
      },
      issues: this.issues,
      recommendations: this.generateRecommendations()
    };

    return report;
  }

  generateRecommendations() {
    const recommendations = [];

    if (this.issues.some(i => i.type.includes('UUID'))) {
      recommendations.push({
        priority: 'HIGH',
        action: '修复UUID格式',
        description: '运行UUID修复脚本，重新生成格式错误的UUID',
        script: 'npm run fix-uuids'
      });
    }

    if (this.issues.some(i => i.type.includes('TAG'))) {
      recommendations.push({
        priority: 'MEDIUM',
        action: '清理标签关联',
        description: '删除无效的标签关联，重新计算标签使用统计',
        script: 'npm run cleanup-tags'
      });
    }

    if (this.issues.some(i => i.type.includes('INTEGRITY'))) {
      recommendations.push({
        priority: 'HIGH',
        action: '修复数据完整性',
        description: '修复引用关系，重新计算统计信息',
        script: 'npm run fix-integrity'
      });
    }

    return recommendations;
  }
}

// 数据迁移脚本生成器
class MigrationGenerator {
  static generateUUIDFixScript(invalidUuids) {
    const script = `
-- UUID修复脚本
-- 生成时间: ${new Date().toISOString()}

BEGIN TRANSACTION;

-- 备份原始数据
CREATE TABLE users_backup AS SELECT * FROM users;
CREATE TABLE stories_backup AS SELECT * FROM stories;

-- 修复用户UUID
${invalidUuids.map(uuid => `
-- 修复UUID: ${uuid}
UPDATE users SET uuid = 'user-${Date.now()}-${Math.random().toString(36).substring(2, 8)}' 
WHERE uuid = '${uuid}';
`).join('')}

-- 验证修复结果
SELECT 'UUID修复完成' as status, COUNT(*) as total_users FROM users;

COMMIT;
`;
    return script;
  }

  static generateTagCleanupScript(orphanedTags) {
    const script = `
-- 标签清理脚本
-- 生成时间: ${new Date().toISOString()}

BEGIN TRANSACTION;

-- 备份标签关联数据
CREATE TABLE story_tags_backup AS SELECT * FROM story_tags;

-- 删除无效的标签关联
${orphanedTags.map(assoc => `
DELETE FROM story_tags 
WHERE story_content_id = '${assoc.storyId}' AND tag_id = '${assoc.tagId}';
`).join('')}

-- 重新计算标签使用统计
UPDATE tags SET usage_count = (
  SELECT COUNT(*) FROM story_tags 
  WHERE story_tags.tag_id = tags.tag_id
);

-- 验证清理结果
SELECT '标签清理完成' as status, COUNT(*) as total_associations FROM story_tags;

COMMIT;
`;
    return script;
  }

  static generateIntegrityFixScript() {
    const script = `
-- 数据完整性修复脚本
-- 生成时间: ${new Date().toISOString()}

BEGIN TRANSACTION;

-- 删除没有对应用户的故事
DELETE FROM stories 
WHERE user_uuid NOT IN (SELECT uuid FROM users);

-- 删除没有对应故事的标签关联
DELETE FROM story_tags 
WHERE story_content_id NOT IN (SELECT content_id FROM stories);

-- 删除没有对应标签的关联
DELETE FROM story_tags 
WHERE tag_id NOT IN (SELECT tag_id FROM tags);

-- 重新计算所有统计信息
UPDATE stories SET 
  likes_count = (
    SELECT COUNT(*) FROM user_interactions 
    WHERE story_content_id = stories.content_id 
    AND interaction_type = 'like'
  ),
  views_count = (
    SELECT COUNT(*) FROM user_interactions 
    WHERE story_content_id = stories.content_id 
    AND interaction_type = 'view'
  );

UPDATE tags SET usage_count = (
  SELECT COUNT(*) FROM story_tags 
  WHERE story_tags.tag_id = tags.tag_id
);

-- 验证修复结果
SELECT '数据完整性修复完成' as status;

COMMIT;
`;
    return script;
  }
}

// 主函数
async function main() {
  console.log('🚀 故事墙数据结构验证开始...\n');

  const checker = new DataConsistencyChecker('./database.db');

  // 执行所有检查
  await checker.checkUserUUIDs();
  await checker.checkContentIDs();
  await checker.checkTagAssociations();
  await checker.checkDataIntegrity();

  // 生成报告
  const report = checker.generateReport();
  
  console.log('\n📊 验证报告:');
  console.log(`总问题数: ${report.summary.totalIssues}`);
  console.log(`高严重性: ${report.summary.highSeverity}`);
  console.log(`中严重性: ${report.summary.mediumSeverity}`);
  console.log(`低严重性: ${report.summary.lowSeverity}`);

  // 保存报告
  const reportPath = path.join(__dirname, '../data/validation-report.json');
  fs.writeFileSync(reportPath, JSON.stringify(report, null, 2));
  console.log(`\n📄 详细报告已保存: ${reportPath}`);

  // 生成修复脚本
  if (report.summary.totalIssues > 0) {
    console.log('\n🔧 生成修复脚本...');
    
    const scriptsDir = path.join(__dirname, '../data/migration-scripts');
    if (!fs.existsSync(scriptsDir)) {
      fs.mkdirSync(scriptsDir, { recursive: true });
    }

    // 生成各种修复脚本
    const uuidFixScript = MigrationGenerator.generateUUIDFixScript(['invalid-uuid-format']);
    fs.writeFileSync(path.join(scriptsDir, 'fix-uuids.sql'), uuidFixScript);

    const tagCleanupScript = MigrationGenerator.generateTagCleanupScript([
      { storyId: 'txid-invalid', tagId: 'tag-job-hunting' }
    ]);
    fs.writeFileSync(path.join(scriptsDir, 'cleanup-tags.sql'), tagCleanupScript);

    const integrityFixScript = MigrationGenerator.generateIntegrityFixScript();
    fs.writeFileSync(path.join(scriptsDir, 'fix-integrity.sql'), integrityFixScript);

    console.log(`✅ 修复脚本已生成: ${scriptsDir}`);
  }

  console.log('\n🎯 建议的下一步操作:');
  report.recommendations.forEach((rec, index) => {
    console.log(`${index + 1}. [${rec.priority}] ${rec.action}`);
    console.log(`   ${rec.description}`);
    console.log(`   运行: ${rec.script}\n`);
  });

  console.log('✅ 故事墙数据结构验证完成！');
}

// 如果直接运行此脚本
if (require.main === module) {
  main().catch(console.error);
}

module.exports = {
  UUIDValidator,
  DataConsistencyChecker,
  MigrationGenerator
};
