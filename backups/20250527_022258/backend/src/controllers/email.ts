import { Hono } from 'hono';
import { z } from 'zod';
import { zValidator } from '@hono/zod-validator';
import { PrismaClient } from '@prisma/client';
import { EmailService } from '../services/emailService';

// Define environment interface
interface Env {
  SURVEY_KV: KVNamespace;
  RESEND_API_KEY: string;
  DATABASE_URL: string;
  AES_SECRET_KEY: string;
  AES_IV: string;
}

// Create Hono app
const app = new Hono<{ Bindings: Env }>();

// Define validation schema for email verification request
const verifyEmailSchema = z.object({
  email: z.string().email(),
});

// Define validation schema for code verification
const verifyCodeSchema = z.object({
  email: z.string().email(),
  code: z.string().length(6),
});

// Send verification email
app.post('/verify', zValidator('json', verifyEmailSchema), async (c) => {
  const { email } = c.req.valid('json');
  const ip = c.req.header('CF-Connecting-IP') || c.req.header('X-Forwarded-For') || 'unknown';
  const userAgent = c.req.header('User-Agent') || 'unknown';

  try {
    // Initialize email service
    const emailService = new EmailService({
      resendApiKey: c.env.RESEND_API_KEY,
      encryptionKey: c.env.AES_SECRET_KEY,
      encryptionIv: c.env.AES_IV,
      fromEmail: '<EMAIL>',
      verificationExpiryMinutes: 10,
    });

    // Initialize Prisma client
    const prisma = new PrismaClient({
      datasources: {
        db: {
          url: c.env.DATABASE_URL,
        },
      },
    });

    // Check if email already exists
    let user = await prisma.user.findUnique({
      where: { email },
    });

    // Generate verification code
    const verificationCode = emailService.generateVerificationCode();
    const verificationExpiresAt = emailService.getVerificationExpiryTime();

    // Create or update user
    if (user) {
      user = await prisma.user.update({
        where: { id: user.id },
        data: {
          verificationCode,
          verificationExpiresAt,
          ipAddress: ip,
          userAgent,
        },
      });
    } else {
      user = await prisma.user.create({
        data: {
          email,
          verificationCode,
          verificationExpiresAt,
          ipAddress: ip,
          userAgent,
        },
      });
    }

    await prisma.$disconnect();

    // Send verification email
    const emailSent = await emailService.sendVerificationEmail(email, verificationCode);

    if (!emailSent) {
      return c.json({ success: false, error: 'Failed to send verification email' }, 500);
    }

    return c.json({
      success: true,
      message: 'Verification email sent',
      expiresAt: verificationExpiresAt,
    });
  } catch (error) {
    console.error('Error sending verification email:', error);
    return c.json({ success: false, error: 'Failed to send verification email' }, 500);
  }
});

// Verify code
app.post('/validate', zValidator('json', verifyCodeSchema), async (c) => {
  const { email, code } = c.req.valid('json');

  try {
    // Initialize Prisma client
    const prisma = new PrismaClient({
      datasources: {
        db: {
          url: c.env.DATABASE_URL,
        },
      },
    });

    // Find user by email
    const user = await prisma.user.findUnique({
      where: { email },
    });

    if (!user) {
      await prisma.$disconnect();
      return c.json({ success: false, error: 'User not found' }, 404);
    }

    // Check if verification code is valid
    if (user.verificationCode !== code) {
      await prisma.$disconnect();
      return c.json({ success: false, error: 'Invalid verification code' }, 400);
    }

    // Check if verification code is expired
    if (!user.verificationExpiresAt || new Date() > user.verificationExpiresAt) {
      await prisma.$disconnect();
      return c.json({ success: false, error: 'Verification code expired' }, 400);
    }

    // Update user as verified
    await prisma.user.update({
      where: { id: user.id },
      data: {
        emailVerified: true,
        verificationCode: null,
        verificationExpiresAt: null,
      },
    });

    await prisma.$disconnect();

    // Generate verification ID for questionnaire submission
    const verificationId = crypto.randomUUID();

    // Store verification data in KV
    await c.env.SURVEY_KV.put(
      `verification:${verificationId}`,
      JSON.stringify({
        userId: user.id,
        verified: true,
        email,
      }),
      { expirationTtl: 3600 } // 1 hour expiry
    );

    return c.json({
      success: true,
      message: 'Email verified successfully',
      verificationId,
    });
  } catch (error) {
    console.error('Error verifying code:', error);
    return c.json({ success: false, error: 'Failed to verify code' }, 500);
  }
});

export { app as email };
