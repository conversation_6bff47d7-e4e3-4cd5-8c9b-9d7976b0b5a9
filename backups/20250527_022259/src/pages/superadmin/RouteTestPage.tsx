import React, { useState, useEffect } from 'react';
import { Card, Table, Tag, Button, Space, Typography, Alert, Spin } from 'antd';
import { CheckCircleOutlined, CloseCircleOutlined, ReloadOutlined } from '@ant-design/icons';
import { testAllRoutes, RouteTestResult } from '@/utils/routeTester';
import { useSnackbar } from '@/contexts/SnackbarContext';
import SuperAdminLayout from '@/components/layouts/SuperAdminLayout';

const { Title, Text } = Typography;

/**
 * 路由测试页面
 * 
 * 用于测试所有路由是否可以正常访问
 */
const RouteTestPage: React.FC = () => {
  const [results, setResults] = useState<RouteTestResult[]>([]);
  const [loading, setLoading] = useState(false);
  const [lastTestTime, setLastTestTime] = useState<string | null>(null);
  const { enqueueSnackbar } = useSnackbar();

  // 表格列定义
  const columns = [
    {
      title: '路由路径',
      dataIndex: 'path',
      key: 'path',
      render: (text: string) => <Text copyable>{text}</Text>,
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      render: (status: string) => (
        status === 'success' 
          ? <Tag icon={<CheckCircleOutlined />} color="success">成功</Tag>
          : <Tag icon={<CloseCircleOutlined />} color="error">失败</Tag>
      ),
      filters: [
        { text: '成功', value: 'success' },
        { text: '失败', value: 'error' },
      ],
      onFilter: (value: string, record: RouteTestResult) => record.status === value,
    },
    {
      title: '组件',
      dataIndex: 'component',
      key: 'component',
    },
    {
      title: '错误信息',
      dataIndex: 'error',
      key: 'error',
      render: (text: string) => text && <Text type="danger">{text}</Text>,
    },
  ];

  // 运行测试
  const runTest = async () => {
    setLoading(true);
    try {
      const testResults = await testAllRoutes();
      setResults(testResults);
      setLastTestTime(new Date().toLocaleString());
      
      // 计算成功和失败的数量
      const successCount = testResults.filter(r => r.status === 'success').length;
      const errorCount = testResults.filter(r => r.status === 'error').length;
      
      enqueueSnackbar(`测试完成: ${successCount} 成功, ${errorCount} 失败`, { 
        variant: errorCount > 0 ? 'warning' : 'success' 
      });
    } catch (error) {
      enqueueSnackbar(`测试失败: ${error instanceof Error ? error.message : String(error)}`, { 
        variant: 'error' 
      });
    } finally {
      setLoading(false);
    }
  };

  // 组件挂载时运行测试
  useEffect(() => {
    runTest();
  }, []);

  // 计算统计信息
  const totalRoutes = results.length;
  const successRoutes = results.filter(r => r.status === 'success').length;
  const errorRoutes = results.filter(r => r.status === 'error').length;
  const successRate = totalRoutes > 0 ? Math.round((successRoutes / totalRoutes) * 100) : 0;

  return (
    <SuperAdminLayout>
      <Card title={
        <Space>
          <Title level={4}>路由测试</Title>
          {loading && <Spin />}
        </Space>
      }
      extra={
        <Button 
          type="primary" 
          icon={<ReloadOutlined />} 
          onClick={runTest}
          loading={loading}
        >
          重新测试
        </Button>
      }>
        <Space direction="vertical" style={{ width: '100%' }}>
          {lastTestTime && (
            <Text type="secondary">上次测试时间: {lastTestTime}</Text>
          )}
          
          <Space style={{ marginBottom: 16 }}>
            <Card size="small">
              <Statistic title="总路由数" value={totalRoutes} />
            </Card>
            <Card size="small">
              <Statistic title="成功" value={successRoutes} valueStyle={{ color: '#52c41a' }} />
            </Card>
            <Card size="small">
              <Statistic title="失败" value={errorRoutes} valueStyle={{ color: '#f5222d' }} />
            </Card>
            <Card size="small">
              <Statistic title="成功率" value={`${successRate}%`} valueStyle={{ color: successRate > 90 ? '#52c41a' : '#f5222d' }} />
            </Card>
          </Space>

          {errorRoutes > 0 && (
            <Alert
              message="发现路由错误"
              description={`有 ${errorRoutes} 个路由测试失败，请检查下面的错误信息。`}
              type="warning"
              showIcon
              style={{ marginBottom: 16 }}
            />
          )}

          <Table
            columns={columns}
            dataSource={results.map((r, i) => ({ ...r, key: i }))}
            pagination={{ pageSize: 50 }}
            size="middle"
            bordered
            loading={loading}
          />
        </Space>
      </Card>
    </SuperAdminLayout>
  );
};

// 统计组件
const Statistic: React.FC<{
  title: string;
  value: string | number;
  valueStyle?: React.CSSProperties;
}> = ({ title, value, valueStyle }) => (
  <div>
    <div style={{ fontSize: 14, color: 'rgba(0, 0, 0, 0.45)' }}>{title}</div>
    <div style={{ fontSize: 24, ...valueStyle }}>{value}</div>
  </div>
);

export default RouteTestPage;
