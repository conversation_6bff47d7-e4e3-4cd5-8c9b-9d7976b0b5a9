/**
 * 安全管理 API
 */

import { Hono } from 'hono';
import { SecurityModule } from '../../security';
import { SecurityConfig } from '../../security/types';

// 创建路由
const app = new Hono();

/**
 * 获取安全配置
 */
app.get('/config', async (c) => {
  try {
    // 获取当前配置
    const config = SecurityModule.getConfig();
    
    // 返回配置
    return c.json({
      success: true,
      config
    });
  } catch (error) {
    console.error('Error getting security config:', error);
    return c.json({
      success: false,
      error: '获取安全配置失败'
    }, 500);
  }
});

/**
 * 更新安全配置
 */
app.post('/config', async (c) => {
  try {
    // 获取请求数据
    const data = await c.req.json();
    
    // 验证数据
    if (!data || typeof data !== 'object') {
      return c.json({
        success: false,
        error: '无效的配置数据'
      }, 400);
    }
    
    // 更新配置
    SecurityModule.updateConfig(data as Partial<SecurityConfig>);
    
    // 记录日志
    await SecurityModule.log(
      'info',
      'Security configuration updated',
      { config: data },
      c.req.headers.get('CF-Connecting-IP') || c.req.headers.get('X-Forwarded-For') || 'unknown',
      c.env
    );
    
    // 返回更新后的配置
    return c.json({
      success: true,
      config: SecurityModule.getConfig()
    });
  } catch (error) {
    console.error('Error updating security config:', error);
    return c.json({
      success: false,
      error: '更新安全配置失败'
    }, 500);
  }
});

/**
 * 获取安全日志
 */
app.get('/logs', async (c) => {
  try {
    // 获取查询参数
    const filter = c.req.query('filter') || 'all';
    const page = parseInt(c.req.query('page') || '1', 10);
    const pageSize = parseInt(c.req.query('pageSize') || '10', 10);
    
    // 获取日志
    const logs = await SecurityModule.getLogs(filter, page, pageSize, c.env);
    
    // 返回日志
    return c.json({
      success: true,
      ...logs
    });
  } catch (error) {
    console.error('Error getting security logs:', error);
    return c.json({
      success: false,
      error: '获取安全日志失败'
    }, 500);
  }
});

/**
 * 获取安全统计信息
 */
app.get('/stats', async (c) => {
  try {
    // 获取所有日志
    const allLogs = await SecurityModule.getLogs('all', 1, 1000, c.env);
    
    // 计算统计信息
    const stats = {
      total: allLogs.total,
      error: allLogs.logs.filter(log => log.level === 'error').length,
      warn: allLogs.logs.filter(log => log.level === 'warn').length,
      suspicious: allLogs.logs.filter(log => 
        log.message.toLowerCase().includes('suspicious') || 
        log.message.toLowerCase().includes('可疑')
      ).length,
      blocked: allLogs.logs.filter(log => 
        log.message.toLowerCase().includes('rejected') || 
        log.message.toLowerCase().includes('拒绝')
      ).length
    };
    
    // 返回统计信息
    return c.json({
      success: true,
      stats
    });
  } catch (error) {
    console.error('Error getting security stats:', error);
    return c.json({
      success: false,
      error: '获取安全统计信息失败'
    }, 500);
  }
});

/**
 * 清除安全日志
 */
app.delete('/logs', async (c) => {
  try {
    // 暂时不实现，需要额外的权限检查
    return c.json({
      success: false,
      error: '此功能暂未实现'
    }, 501);
  } catch (error) {
    console.error('Error clearing security logs:', error);
    return c.json({
      success: false,
      error: '清除安全日志失败'
    }, 500);
  }
});

// 导出路由
export default app;
