/**
 * 高级代码分割和预加载策略
 * 
 * 提供更智能的代码分割、预加载和按需加载功能
 */

import React, { lazy, Suspense, useEffect } from 'react';
import { useLocation, useNavigate } from 'react-router-dom';
import { measureComponentRender } from '../utils/performance';

// 路由加载状态组件
import RouteLoading from '../components/common/RouteLoading';

// 路由组分组配置
const ROUTE_GROUPS = {
  // 公共路由组
  PUBLIC: [
    '/',
    '/questionnaire',
    '/visualization',
    '/story-wall',
    '/story'
  ],
  // 管理员路由组
  ADMIN: [
    '/admin/dashboard',
    '/admin/story-review',
    '/admin/content-review',
    '/admin/comment-review',
    '/admin/user-management',
    '/admin/reviewer-management',
    '/admin/settings'
  ],
  // 审核员路由组
  REVIEWER: [
    '/reviewer/dashboard',
    '/reviewer/story-review',
    '/reviewer/content-review',
    '/reviewer/quick-review',
    '/reviewer/settings'
  ],
  // 超级管理员路由组
  SUPERADMIN: [
    '/superadmin/dashboard',
    '/superadmin/user-management',
    '/superadmin/system-config',
    '/superadmin/security-logs'
  ]
};

// 预加载优先级
const PRELOAD_PRIORITY = {
  HIGH: 1,    // 立即预加载
  MEDIUM: 2,  // 空闲时预加载
  LOW: 3      // 用户悬停或其他触发时预加载
};

// 路由预加载配置
const PRELOAD_CONFIG = {
  // 首页相关路由
  '/': {
    preloadRoutes: ['/questionnaire', '/story-wall'],
    priority: PRELOAD_PRIORITY.HIGH
  },
  // 问卷页面相关路由
  '/questionnaire': {
    preloadRoutes: ['/submit-success'],
    priority: PRELOAD_PRIORITY.MEDIUM
  },
  // 管理员仪表盘相关路由
  '/admin/dashboard': {
    preloadRoutes: ['/admin/story-review', '/admin/content-review', '/admin/user-management'],
    priority: PRELOAD_PRIORITY.HIGH
  }
  // 可以继续添加更多路由预加载配置
};

/**
 * 智能预加载组件
 * 根据当前路由和预加载配置，智能预加载相关路由
 */
export const SmartPreloader: React.FC = () => {
  const location = useLocation();
  const currentPath = location.pathname;

  useEffect(() => {
    // 获取当前路由的预加载配置
    const preloadConfig = Object.entries(PRELOAD_CONFIG).find(([path]) => 
      currentPath.startsWith(path)
    );

    if (!preloadConfig) return;

    const [_, config] = preloadConfig;
    
    // 根据优先级执行预加载
    if (config.priority === PRELOAD_PRIORITY.HIGH) {
      // 立即预加载
      config.preloadRoutes.forEach(route => {
        preloadRoute(route);
      });
    } else if (config.priority === PRELOAD_PRIORITY.MEDIUM) {
      // 使用requestIdleCallback在浏览器空闲时预加载
      if ('requestIdleCallback' in window) {
        window.requestIdleCallback(() => {
          config.preloadRoutes.forEach(route => {
            preloadRoute(route);
          });
        });
      } else {
        // 降级为setTimeout
        setTimeout(() => {
          config.preloadRoutes.forEach(route => {
            preloadRoute(route);
          });
        }, 2000);
      }
    }
    // LOW优先级预加载由用户交互触发，不在这里处理
  }, [currentPath]);

  return null;
};

/**
 * 增强的懒加载组件
 * @param componentName 组件名称
 * @param factory 组件导入函数
 * @param options 额外选项
 */
export const enhancedLazyLoad = (
  componentName: string,
  factory: () => Promise<any>,
  options: {
    fallback?: React.ReactNode;
    errorFallback?: React.ReactNode;
    timeout?: number;
    onLoad?: () => void;
    onError?: (error: Error) => void;
    retry?: boolean;
    maxRetries?: number;
  } = {}
) => {
  const {
    fallback = <RouteLoading />,
    errorFallback = <div>加载失败，请刷新页面重试</div>,
    timeout = 10000,
    onLoad,
    onError,
    retry = true,
    maxRetries = 3
  } = options;

  // 带重试和超时的懒加载
  const LazyComponent = lazy(() => {
    let retryCount = 0;

    const loadComponent = () => {
      return Promise.race([
        measureComponentRender(`LazyLoad_${componentName}`, () => factory())
          .then(module => {
            if (onLoad) onLoad();
            return module;
          }),
        new Promise<never>((_, reject) => {
          setTimeout(() => reject(new Error(`加载超时: ${componentName}`)), timeout);
        })
      ]).catch(error => {
        if (retry && retryCount < maxRetries) {
          retryCount++;
          console.warn(`组件加载失败，正在重试 (${retryCount}/${maxRetries}): ${componentName}`);
          return new Promise(resolve => {
            setTimeout(() => {
              resolve(loadComponent());
            }, 1000 * retryCount); // 递增延迟
          });
        }
        
        if (onError) onError(error);
        console.error(`组件加载失败: ${componentName}`, error);
        
        // 返回一个解析为默认导出为错误组件的模块
        return {
          default: () => errorFallback
        };
      });
    };

    return loadComponent();
  });

  return (props: any) => (
    <Suspense fallback={fallback}>
      <LazyComponent {...props} />
    </Suspense>
  );
};

/**
 * 预加载路由
 * @param path 路由路径
 */
export const preloadRoute = (path: string) => {
  const component = routeComponents[path];
  if (component && typeof component.preload === 'function') {
    console.log(`预加载路由: ${path}`);
    component.preload();
  }
};

// 从OptimizedRoutes.tsx导入路由组件映射
import { routeComponents } from './OptimizedRoutes';

/**
 * 获取当前用户角色组的路由
 * @param role 用户角色
 */
export const getRouteGroupByRole = (role: string): string[] => {
  switch (role) {
    case 'admin':
      return ROUTE_GROUPS.ADMIN;
    case 'reviewer':
      return ROUTE_GROUPS.REVIEWER;
    case 'superadmin':
      return ROUTE_GROUPS.SUPERADMIN;
    default:
      return ROUTE_GROUPS.PUBLIC;
  }
};

/**
 * 预加载用户角色相关路由
 * @param role 用户角色
 */
export const preloadRoleRoutes = (role: string) => {
  const routes = getRouteGroupByRole(role);
  
  // 使用requestIdleCallback在浏览器空闲时预加载
  if ('requestIdleCallback' in window) {
    window.requestIdleCallback(() => {
      routes.forEach(route => {
        preloadRoute(route);
      });
    });
  } else {
    // 降级为setTimeout
    setTimeout(() => {
      routes.forEach(route => {
        preloadRoute(route);
      });
    }, 2000);
  }
};

export default {
  SmartPreloader,
  enhancedLazyLoad,
  preloadRoute,
  preloadRoleRoutes
};
