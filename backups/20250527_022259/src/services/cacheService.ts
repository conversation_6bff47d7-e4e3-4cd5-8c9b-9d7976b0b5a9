/**
 * 缓存服务
 * 提供内存缓存和本地存储缓存功能
 * 优化版本：增加了性能监控、LRU缓存策略和批量操作
 */

// 缓存项接口
interface CacheItem<T> {
  data: T;
  expiry: number; // 过期时间戳
  createdAt: number; // 创建时间戳
  lastAccessed: number; // 最后访问时间戳
  accessCount: number; // 访问次数
}

// 缓存配置
interface CacheConfig {
  maxSize: number; // 最大缓存项数量
  defaultExpiry: number; // 默认过期时间（毫秒）
  cleanupInterval: number; // 清理间隔（毫秒）
  persistenceEnabled: boolean; // 是否启用持久化
  persistenceInterval: number; // 持久化间隔（毫秒）
}

// 默认缓存配置
const DEFAULT_CONFIG: CacheConfig = {
  maxSize: 100,
  defaultExpiry: 5 * 60 * 1000, // 默认5分钟
  cleanupInterval: 60 * 1000, // 1分钟
  persistenceEnabled: true,
  persistenceInterval: 60 * 1000, // 1分钟
};

// 缓存键前缀
const CONTENT_CACHE_KEY = 'content_cache';
const SETTINGS_CACHE_KEY = 'settings_cache';
const PERSISTENCE_KEY = 'app_cache_v2';

// 内存缓存
const memoryCache: Record<string, CacheItem<any>> = {};

// 缓存统计
const cacheStats = {
  hits: 0,
  misses: 0,
  sets: 0,
  deletes: 0,
  cleanups: 0,
  size: 0,
};

// 缓存配置
let cacheConfig: CacheConfig = { ...DEFAULT_CONFIG };

// 清理定时器
let cleanupTimer: number | null = null;
let persistenceTimer: number | null = null;

/**
 * 初始化缓存服务
 * @param config 缓存配置
 */
export const initCacheService = (config: Partial<CacheConfig> = {}): void => {
  cacheConfig = { ...DEFAULT_CONFIG, ...config };

  // 从本地存储加载缓存
  if (cacheConfig.persistenceEnabled) {
    loadFromStorage();
    startPersistenceTimer();
  }

  // 启动清理定时器
  startCleanupTimer();
};

/**
 * 设置内存缓存
 * @param key 缓存键
 * @param data 缓存数据
 * @param expiry 过期时间（毫秒）
 * @returns 是否设置成功
 */
export const setMemoryCache = <T>(key: string, data: T, expiry: number = cacheConfig.defaultExpiry): boolean => {
  // 如果缓存已满，清理最旧的项
  if (Object.keys(memoryCache).length >= cacheConfig.maxSize && !memoryCache[key]) {
    cleanLRUCache(1);
  }

  const now = Date.now();

  memoryCache[key] = {
    data,
    expiry: now + expiry,
    createdAt: now,
    lastAccessed: now,
    accessCount: 0
  };

  cacheStats.sets++;
  cacheStats.size = Object.keys(memoryCache).length;

  return true;
};

/**
 * 获取内存缓存
 * @param key 缓存键
 * @returns 缓存数据，如果不存在或已过期则返回null
 */
export const getMemoryCache = <T>(key: string): T | null => {
  const item = memoryCache[key];

  if (!item) {
    cacheStats.misses++;
    return null;
  }

  // 检查是否过期
  if (Date.now() > item.expiry) {
    delete memoryCache[key];
    cacheStats.misses++;
    cacheStats.size = Object.keys(memoryCache).length;
    return null;
  }

  // 更新访问统计
  item.lastAccessed = Date.now();
  item.accessCount++;

  cacheStats.hits++;
  return item.data;
};

/**
 * 清除内存缓存
 * @param key 缓存键，如果不提供则清除所有缓存
 * @returns 清除的缓存项数量
 */
export const clearMemoryCache = (key?: string): number => {
  if (key) {
    const deleted = key in memoryCache ? 1 : 0;
    delete memoryCache[key];
    cacheStats.deletes += deleted;
    cacheStats.size = Object.keys(memoryCache).length;
    return deleted;
  } else {
    const count = Object.keys(memoryCache).length;
    Object.keys(memoryCache).forEach(k => delete memoryCache[k]);
    cacheStats.deletes += count;
    cacheStats.size = 0;
    return count;
  }
};

/**
 * 获取缓存，如果不存在则通过回调函数获取并缓存
 * @param key 缓存键
 * @param callback 回调函数，用于获取数据
 * @param expiry 过期时间（毫秒）
 * @returns 缓存数据或回调函数的返回值
 */
export const getOrSetMemoryCache = async <T>(
  key: string,
  callback: () => Promise<T>,
  expiry: number = cacheConfig.defaultExpiry
): Promise<T> => {
  const cachedData = getMemoryCache<T>(key);

  if (cachedData !== null) {
    return cachedData;
  }

  const data = await callback();
  setMemoryCache(key, data, expiry);
  return data;
};

/**
 * 设置本地存储缓存
 * @param key 缓存键
 * @param data 缓存数据
 * @param expiry 过期时间（毫秒）
 * @returns 是否设置成功
 */
export const setLocalCache = <T>(key: string, data: T, expiry: number = cacheConfig.defaultExpiry): boolean => {
  const now = Date.now();

  const item: CacheItem<T> = {
    data,
    expiry: now + expiry,
    createdAt: now,
    lastAccessed: now,
    accessCount: 0
  };

  try {
    localStorage.setItem(key, JSON.stringify(item));
    return true;
  } catch (error) {
    console.error('设置本地缓存失败:', error);

    // 如果是存储空间不足，尝试清理一些缓存
    if (error instanceof DOMException && error.name === 'QuotaExceededError') {
      try {
        // 清理本地存储中的过期项
        cleanExpiredLocalCache();

        // 再次尝试设置
        localStorage.setItem(key, JSON.stringify(item));
        return true;
      } catch (retryError) {
        console.error('重试设置本地缓存失败:', retryError);
      }
    }

    return false;
  }
};

/**
 * 获取本地存储缓存
 * @param key 缓存键
 * @returns 缓存数据，如果不存在或已过期则返回null
 */
export const getLocalCache = <T>(key: string): T | null => {
  try {
    const itemStr = localStorage.getItem(key);

    if (!itemStr) {
      return null;
    }

    const item: CacheItem<T> = JSON.parse(itemStr);

    // 检查是否过期
    if (Date.now() > item.expiry) {
      localStorage.removeItem(key);
      return null;
    }

    // 更新访问统计
    item.lastAccessed = Date.now();
    item.accessCount++;

    // 保存更新后的统计信息
    try {
      localStorage.setItem(key, JSON.stringify(item));
    } catch (error) {
      // 忽略更新统计信息的错误
    }

    return item.data;
  } catch (error) {
    console.error('获取本地缓存失败:', error);
    return null;
  }
};

/**
 * 清除本地存储缓存
 * @param key 缓存键，如果不提供则清除所有缓存
 * @returns 是否清除成功
 */
export const clearLocalCache = (key?: string): boolean => {
  try {
    if (key) {
      localStorage.removeItem(key);
    } else {
      // 只清除我们的缓存项，而不是所有本地存储
      const keysToRemove: string[] = [];

      for (let i = 0; i < localStorage.length; i++) {
        const k = localStorage.key(i);
        if (k && (k.startsWith(CONTENT_CACHE_KEY) || k.startsWith(SETTINGS_CACHE_KEY) || k === PERSISTENCE_KEY)) {
          keysToRemove.push(k);
        }
      }

      keysToRemove.forEach(k => localStorage.removeItem(k));
    }

    return true;
  } catch (error) {
    console.error('清除本地缓存失败:', error);
    return false;
  }
};

/**
 * 获取或设置本地存储缓存
 * @param key 缓存键
 * @param callback 回调函数，用于获取数据
 * @param expiry 过期时间（毫秒）
 * @returns 缓存数据或回调函数的返回值
 */
export const getOrSetLocalCache = async <T>(
  key: string,
  callback: () => Promise<T>,
  expiry: number = cacheConfig.defaultExpiry
): Promise<T> => {
  const cachedData = getLocalCache<T>(key);

  if (cachedData !== null) {
    return cachedData;
  }

  const data = await callback();
  setLocalCache(key, data, expiry);
  return data;
};

/**
 * 清理过期的本地存储缓存
 * @returns 清理的缓存项数量
 */
export const cleanExpiredLocalCache = (): number => {
  let count = 0;
  const now = Date.now();
  const keysToCheck: string[] = [];

  // 收集所有可能的缓存键
  for (let i = 0; i < localStorage.length; i++) {
    const key = localStorage.key(i);
    if (key) {
      keysToCheck.push(key);
    }
  }

  // 检查并清理过期项
  keysToCheck.forEach(key => {
    try {
      const itemStr = localStorage.getItem(key);
      if (itemStr) {
        const item = JSON.parse(itemStr);
        if (item && item.expiry && item.expiry < now) {
          localStorage.removeItem(key);
          count++;
        }
      }
    } catch (error) {
      // 忽略解析错误
    }
  });

  return count;
};

/**
 * 清理过期的内存缓存
 * @returns 清理的缓存项数量
 */
export const cleanExpiredCache = (): number => {
  const now = Date.now();
  let count = 0;

  Object.entries(memoryCache).forEach(([key, item]) => {
    if (item.expiry < now) {
      delete memoryCache[key];
      count++;
    }
  });

  cacheStats.cleanups++;
  cacheStats.size = Object.keys(memoryCache).length;

  return count;
};

/**
 * 清理最近最少使用的缓存项
 * @param count 要清理的数量
 * @returns 清理的缓存项数量
 */
export const cleanLRUCache = (count: number = 1): number => {
  const entries = Object.entries(memoryCache)
    .sort((a, b) => {
      // 首先按访问次数排序
      const accessDiff = a[1].accessCount - b[1].accessCount;
      if (accessDiff !== 0) return accessDiff;

      // 然后按最后访问时间排序
      return a[1].lastAccessed - b[1].lastAccessed;
    });

  let cleaned = 0;

  for (let i = 0; i < Math.min(count, entries.length); i++) {
    delete memoryCache[entries[i][0]];
    cleaned++;
  }

  cacheStats.size = Object.keys(memoryCache).length;

  return cleaned;
};

/**
 * 从本地存储加载缓存
 */
const loadFromStorage = (): void => {
  if (typeof localStorage === 'undefined') {
    return;
  }

  try {
    const data = localStorage.getItem(PERSISTENCE_KEY);

    if (data) {
      const parsed = JSON.parse(data);

      // 只加载未过期的项
      const now = Date.now();

      Object.entries(parsed).forEach(([key, item]: [string, any]) => {
        if (item.expiry > now) {
          memoryCache[key] = item;
        }
      });

      cacheStats.size = Object.keys(memoryCache).length;
    }
  } catch (error) {
    console.error('从本地存储加载缓存失败:', error);
  }
};

/**
 * 保存缓存到本地存储
 */
const saveToStorage = (): void => {
  if (typeof localStorage === 'undefined' || !cacheConfig.persistenceEnabled) {
    return;
  }

  try {
    // 清理过期缓存
    cleanExpiredCache();

    localStorage.setItem(PERSISTENCE_KEY, JSON.stringify(memoryCache));
  } catch (error) {
    console.error('保存缓存到本地存储失败:', error);

    // 如果是存储空间不足，尝试清理一些缓存
    if (error instanceof DOMException && error.name === 'QuotaExceededError') {
      try {
        // 清理本地存储中的过期项
        cleanExpiredLocalCache();

        // 清理一些内存缓存项
        cleanLRUCache(Math.ceil(Object.keys(memoryCache).length * 0.2)); // 清理20%

        // 再次尝试保存
        localStorage.setItem(PERSISTENCE_KEY, JSON.stringify(memoryCache));
      } catch (retryError) {
        console.error('重试保存缓存到本地存储失败:', retryError);
      }
    }
  }
};

/**
 * 启动清理定时器
 */
const startCleanupTimer = (): void => {
  if (cleanupTimer !== null) {
    return;
  }

  cleanupTimer = window.setInterval(() => {
    cleanExpiredCache();
  }, cacheConfig.cleanupInterval);
};

/**
 * 启动持久化定时器
 */
const startPersistenceTimer = (): void => {
  if (persistenceTimer !== null || !cacheConfig.persistenceEnabled) {
    return;
  }

  persistenceTimer = window.setInterval(() => {
    saveToStorage();
  }, cacheConfig.persistenceInterval);
};

/**
 * 停止所有定时器
 */
export const stopTimers = (): void => {
  if (cleanupTimer !== null) {
    clearInterval(cleanupTimer);
    cleanupTimer = null;
  }

  if (persistenceTimer !== null) {
    clearInterval(persistenceTimer);
    persistenceTimer = null;
  }
};

/**
 * 获取缓存统计信息
 * @returns 缓存统计信息
 */
export const getCacheStats = (): typeof cacheStats & { hitRate: number } => {
  const totalAccess = cacheStats.hits + cacheStats.misses;
  const hitRate = totalAccess > 0 ? cacheStats.hits / totalAccess : 0;

  return {
    ...cacheStats,
    hitRate,
  };
};

/**
 * 缓存内容列表
 * @param queryParams 查询参数
 * @param data 内容数据
 * @param expiry 过期时间（毫秒）
 * @returns 是否缓存成功
 */
export const cacheContentList = (
  queryParams: string,
  data: any,
  expiry: number = cacheConfig.defaultExpiry
): boolean => {
  const cacheKey = `${CONTENT_CACHE_KEY}_${queryParams || 'default'}`;
  return setMemoryCache(cacheKey, data, expiry);
};

/**
 * 获取缓存的内容列表
 * @param queryParams 查询参数
 * @returns 缓存的内容数据，如果不存在或已过期则返回null
 */
export const getCachedContentList = (queryParams: string): any | null => {
  const cacheKey = `${CONTENT_CACHE_KEY}_${queryParams || 'default'}`;
  return getMemoryCache(cacheKey);
};

/**
 * 获取或缓存内容列表
 * @param queryParams 查询参数
 * @param callback 回调函数，用于获取数据
 * @param expiry 过期时间（毫秒）
 * @returns 缓存数据或回调函数的返回值
 */
export const getOrCacheContentList = async (
  queryParams: string,
  callback: () => Promise<any>,
  expiry: number = cacheConfig.defaultExpiry
): Promise<any> => {
  const cacheKey = `${CONTENT_CACHE_KEY}_${queryParams || 'default'}`;
  return getOrSetMemoryCache(cacheKey, callback, expiry);
};

/**
 * 缓存内容详情
 * @param id 内容ID
 * @param data 内容数据
 * @param expiry 过期时间（毫秒）
 * @returns 是否缓存成功
 */
export const cacheContentDetail = (
  id: string,
  data: any,
  expiry: number = cacheConfig.defaultExpiry
): boolean => {
  const cacheKey = `${CONTENT_CACHE_KEY}_detail_${id}`;
  return setMemoryCache(cacheKey, data, expiry);
};

/**
 * 获取缓存的内容详情
 * @param id 内容ID
 * @returns 缓存的内容数据，如果不存在或已过期则返回null
 */
export const getCachedContentDetail = (id: string): any | null => {
  const cacheKey = `${CONTENT_CACHE_KEY}_detail_${id}`;
  return getMemoryCache(cacheKey);
};

/**
 * 获取或缓存内容详情
 * @param id 内容ID
 * @param callback 回调函数，用于获取数据
 * @param expiry 过期时间（毫秒）
 * @returns 缓存数据或回调函数的返回值
 */
export const getOrCacheContentDetail = async (
  id: string,
  callback: () => Promise<any>,
  expiry: number = cacheConfig.defaultExpiry
): Promise<any> => {
  const cacheKey = `${CONTENT_CACHE_KEY}_detail_${id}`;
  return getOrSetMemoryCache(cacheKey, callback, expiry);
};

/**
 * 缓存审核设置
 * @param data 设置数据
 * @param expiry 过期时间（毫秒）
 * @returns 是否缓存成功
 */
export const cacheReviewSettings = (
  data: any,
  expiry: number = 30 * 60 * 1000 // 30分钟
): boolean => {
  const memoryResult = setMemoryCache(SETTINGS_CACHE_KEY, data, expiry);
  const localResult = setLocalCache(SETTINGS_CACHE_KEY, data, expiry);
  return memoryResult && localResult;
};

/**
 * 获取缓存的审核设置
 * @returns 缓存的设置数据，如果不存在或已过期则返回null
 */
export const getCachedReviewSettings = (): any | null => {
  // 先尝试从内存缓存获取
  const memoryData = getMemoryCache(SETTINGS_CACHE_KEY);
  if (memoryData) {
    return memoryData;
  }

  // 再尝试从本地存储获取
  const localData = getLocalCache(SETTINGS_CACHE_KEY);

  // 如果从本地存储获取到了，也放入内存缓存
  if (localData) {
    setMemoryCache(SETTINGS_CACHE_KEY, localData);
  }

  return localData;
};

/**
 * 获取或缓存审核设置
 * @param callback 回调函数，用于获取数据
 * @param expiry 过期时间（毫秒）
 * @returns 缓存数据或回调函数的返回值
 */
export const getOrCacheReviewSettings = async (
  callback: () => Promise<any>,
  expiry: number = 30 * 60 * 1000 // 30分钟
): Promise<any> => {
  // 先尝试从缓存获取
  const cachedData = getCachedReviewSettings();

  if (cachedData !== null) {
    return cachedData;
  }

  // 获取新数据
  const data = await callback();

  // 缓存数据
  cacheReviewSettings(data, expiry);

  return data;
};

/**
 * 清除所有审核相关缓存
 * @returns 清除的缓存项数量
 */
export const clearReviewCache = (): number => {
  let count = 0;

  // 清除内存中以CONTENT_CACHE_KEY开头的所有缓存
  Object.keys(memoryCache).forEach(key => {
    if (key.startsWith(CONTENT_CACHE_KEY)) {
      delete memoryCache[key];
      count++;
    }
  });

  // 清除设置缓存
  if (SETTINGS_CACHE_KEY in memoryCache) {
    clearMemoryCache(SETTINGS_CACHE_KEY);
    count++;
  }

  clearLocalCache(SETTINGS_CACHE_KEY);

  cacheStats.size = Object.keys(memoryCache).length;

  return count;
};

// 初始化缓存服务
initCacheService();
