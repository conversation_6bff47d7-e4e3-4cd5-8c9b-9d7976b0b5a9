/**
 * 权限定义模块
 *
 * 定义系统中的角色和权限
 */

// 角色类型
export type Role = 'superadmin' | 'admin' | 'reviewer' | 'user';

// 权限类型
export enum Permission {
  // 仪表盘权限
  DASHBOARD_PERSONAL = 'dashboard:personal',   // 个人仪表盘
  DASHBOARD_SYSTEM = 'dashboard:system',       // 系统概览
  DASHBOARD_SECURITY = 'dashboard:security',   // 安全监控

  // 内容管理权限
  CONTENT_REVIEW = 'content:review',           // 内容审核
  STORY_REVIEW = 'story:review',               // 故事审核
  QUICK_REVIEW = 'quick:review',               // 快速审核
  TAG_MANAGEMENT = 'tag:management',           // 标签管理

  // 数据管理权限
  QUESTIONNAIRE_VIEW = 'questionnaire:view',   // 问卷回复查看
  QUESTIONNAIRE_EDIT = 'questionnaire:edit',   // 问卷回复编辑
  DATA_ANALYSIS = 'data:analysis',             // 数据分析
  DATA_EXPORT = 'data:export',                 // 数据导出

  // 用户管理权限
  USER_VIEW = 'user:view',                     // 用户查看
  USER_MANAGEMENT = 'user:management',         // 用户管理
  REVIEWER_MANAGEMENT = 'reviewer:management', // 审核员管理
  ADMIN_MANAGEMENT = 'admin:management',       // 管理员管理
  ROLE_MANAGEMENT = 'role:management',         // 角色权限管理

  // 系统设置权限
  SETTINGS_PERSONAL = 'settings:personal',     // 个人设置
  DEIDENTIFICATION = 'settings:deidentification', // 内容脱敏设置
  SECURITY_SETTINGS = 'settings:security',     // 安全设置
  SYSTEM_CONFIG = 'settings:system',           // 系统配置

  // 安全与审计权限
  SECURITY_MONITOR = 'security:monitor',       // 安全监控
  SECURITY_LOGS = 'security:logs',             // 安全日志
  ADMIN_AUDIT = 'security:admin-audit',        // 管理员审计
  LOGIN_RECORDS = 'security:login-records',    // 登录记录
}

// 角色权限映射
export const rolePermissions: Record<Role, Permission[]> = {
  // 超级管理员拥有所有权限
  superadmin: Object.values(Permission),

  // 管理员权限
  admin: [
    // 仪表盘权限
    Permission.DASHBOARD_PERSONAL,
    Permission.DASHBOARD_SYSTEM,

    // 内容管理权限
    Permission.CONTENT_REVIEW,
    Permission.STORY_REVIEW,
    Permission.QUICK_REVIEW,
    Permission.TAG_MANAGEMENT,

    // 数据管理权限
    Permission.QUESTIONNAIRE_VIEW,
    Permission.QUESTIONNAIRE_EDIT,
    Permission.DATA_ANALYSIS,
    Permission.DATA_EXPORT,

    // 用户管理权限
    Permission.USER_VIEW,
    Permission.USER_MANAGEMENT,
    Permission.REVIEWER_MANAGEMENT,

    // 系统设置权限
    Permission.SETTINGS_PERSONAL,
    Permission.DEIDENTIFICATION,
  ],

  // 审核员权限
  reviewer: [
    // 仪表盘权限
    Permission.DASHBOARD_PERSONAL,

    // 内容管理权限
    Permission.CONTENT_REVIEW,
    Permission.STORY_REVIEW,
    Permission.QUICK_REVIEW,

    // 数据管理权限
    Permission.QUESTIONNAIRE_VIEW,

    // 系统设置权限
    Permission.SETTINGS_PERSONAL,
  ],

  // 普通用户权限
  user: [],
};

/**
 * 检查用户是否拥有指定权限
 *
 * @param userRole 用户角色
 * @param permission 需要检查的权限
 * @returns 是否拥有权限
 */
export function hasPermission(userRole: Role, permission: Permission): boolean {
  return rolePermissions[userRole]?.includes(permission) || false;
}

/**
 * 检查用户是否拥有指定权限中的任意一个
 *
 * @param userRole 用户角色
 * @param permissions 需要检查的权限列表
 * @returns 是否拥有任意一个权限
 */
export function hasAnyPermission(userRole: Role, permissions: Permission[]): boolean {
  return permissions.some(permission => hasPermission(userRole, permission));
}

/**
 * 检查用户是否拥有指定权限中的所有权限
 *
 * @param userRole 用户角色
 * @param permissions 需要检查的权限列表
 * @returns 是否拥有所有权限
 */
export function hasAllPermissions(userRole: Role, permissions: Permission[]): boolean {
  return permissions.every(permission => hasPermission(userRole, permission));
}
