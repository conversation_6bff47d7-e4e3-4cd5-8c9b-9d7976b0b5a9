import { useEffect } from 'react';
import { Link, useLocation, useNavigate } from 'react-router-dom';
import { Button } from '@/components/ui/button';
import { CheckCircle } from 'lucide-react';

interface LocationState {
  responseId?: number;
  verified?: boolean;
}

export default function SubmitSuccessPage() {
  const location = useLocation();
  const navigate = useNavigate();
  const state = location.state as LocationState;
  
  // If no state is provided, redirect to home page
  useEffect(() => {
    if (!state || !state.responseId) {
      navigate('/', { replace: true });
    }
  }, [state, navigate]);
  
  // If no state is provided, show loading
  if (!state || !state.responseId) {
    return <div>Loading...</div>;
  }
  
  return (
    <div className="max-w-md mx-auto text-center py-12">
      <div className="flex justify-center mb-6">
        <CheckCircle className="h-16 w-16 text-green-500" />
      </div>
      
      <h1 className="text-2xl font-bold mb-4">提交成功！</h1>
      
      <div className="bg-white rounded-lg shadow-md p-6 mb-8">
        <p className="text-gray-700 mb-4">
          感谢您参与大学生就业问卷调查。您的反馈对我们了解大学生就业现状非常重要。
        </p>
        
        <div className="bg-gray-50 p-4 rounded-md mb-4">
          <p className="text-sm text-gray-600 mb-2">提交编号</p>
          <p className="font-medium">{state.responseId}</p>
        </div>
        
        <div className="bg-gray-50 p-4 rounded-md">
          <p className="text-sm text-gray-600 mb-2">验证状态</p>
          <p className="font-medium">
            {state.verified ? (
              <span className="text-green-600">已验证</span>
            ) : (
              <span className="text-gray-600">匿名提交</span>
            )}
          </p>
        </div>
      </div>
      
      <div className="space-y-4">
        <Link to="/visualization">
          <Button className="w-full">
            查看数据可视化
          </Button>
        </Link>
        
        <Link to="/story-wall">
          <Button variant="outline" className="w-full">
            浏览故事墙
          </Button>
        </Link>
        
        <Link to="/">
          <Button variant="link" className="w-full">
            返回首页
          </Button>
        </Link>
      </div>
    </div>
  );
}
