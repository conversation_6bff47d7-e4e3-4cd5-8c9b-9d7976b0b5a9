{"id": "438c294e-2da2-45e3-9cdd-aa4f59085576", "name": "initial-state", "description": "初始测试数据状态", "createdAt": "2025-05-23T07:11:16.917Z", "version": "1.0", "checksum": "83900c5d1a7fb18e430bfa5c2c64839a", "data": {"users": [{"id": 4, "email": "<EMAIL>", "emailVerified": true, "verificationCode": null, "verificationExpiresAt": null, "ipAddress": null, "userAgent": null, "role": "reviewer", "username": "reviewer1", "name": "审核员1", "passwordHash": "$2b$10$example.hash.for.testing.purposes.only", "lastLoginAt": "2025-05-19T06:59:32.426Z", "createdAt": "2025-05-23T07:11:16.861Z", "updatedAt": "2025-05-23T07:11:16.862Z", "isTestData": true, "testDataVersion": "1.0", "testDataSet": "role-testing"}, {"id": 5, "email": "<EMAIL>", "emailVerified": true, "verificationCode": null, "verificationExpiresAt": null, "ipAddress": null, "userAgent": null, "role": "reviewer", "username": "reviewer2", "name": "审核员2", "passwordHash": "$2b$10$example.hash.for.testing.purposes.only", "lastLoginAt": "2025-05-22T02:53:05.734Z", "createdAt": "2025-05-23T07:11:16.863Z", "updatedAt": "2025-05-23T07:11:16.864Z", "isTestData": true, "testDataVersion": "1.0", "testDataSet": "role-testing"}, {"id": 6, "email": "<EMAIL>", "emailVerified": true, "verificationCode": null, "verificationExpiresAt": null, "ipAddress": null, "userAgent": null, "role": "reviewer", "username": "reviewer3", "name": "审核员3", "passwordHash": "$2b$10$example.hash.for.testing.purposes.only", "lastLoginAt": "2025-05-17T01:09:57.859Z", "createdAt": "2025-05-23T07:11:16.864Z", "updatedAt": "2025-05-23T07:11:16.864Z", "isTestData": true, "testDataVersion": "1.0", "testDataSet": "role-testing"}, {"id": 7, "email": "<EMAIL>", "emailVerified": true, "verificationCode": null, "verificationExpiresAt": null, "ipAddress": null, "userAgent": null, "role": "reviewer", "username": "reviewer4", "name": "审核员4", "passwordHash": "$2b$10$example.hash.for.testing.purposes.only", "lastLoginAt": "2025-05-21T11:18:11.991Z", "createdAt": "2025-05-23T07:11:16.865Z", "updatedAt": "2025-05-23T07:11:16.865Z", "isTestData": true, "testDataVersion": "1.0", "testDataSet": "role-testing"}, {"id": 8, "email": "<EMAIL>", "emailVerified": true, "verificationCode": null, "verificationExpiresAt": null, "ipAddress": null, "userAgent": null, "role": "reviewer", "username": "reviewer5", "name": "审核员5", "passwordHash": "$2b$10$example.hash.for.testing.purposes.only", "lastLoginAt": "2025-05-22T06:20:22.158Z", "createdAt": "2025-05-23T07:11:16.865Z", "updatedAt": "2025-05-23T07:11:16.866Z", "isTestData": true, "testDataVersion": "1.0", "testDataSet": "role-testing"}, {"id": 9, "email": "<EMAIL>", "emailVerified": true, "verificationCode": null, "verificationExpiresAt": null, "ipAddress": null, "userAgent": null, "role": "admin", "username": "admin1", "name": "管理员1", "passwordHash": "$2b$10$example.hash.for.testing.purposes.only", "lastLoginAt": "2025-05-23T00:19:01.421Z", "createdAt": "2025-05-23T07:11:16.866Z", "updatedAt": "2025-05-23T07:11:16.867Z", "isTestData": true, "testDataVersion": "1.0", "testDataSet": "role-testing"}, {"id": 10, "email": "<EMAIL>", "emailVerified": true, "verificationCode": null, "verificationExpiresAt": null, "ipAddress": null, "userAgent": null, "role": "admin", "username": "admin2", "name": "管理员2", "passwordHash": "$2b$10$example.hash.for.testing.purposes.only", "lastLoginAt": "2025-05-21T13:23:07.514Z", "createdAt": "2025-05-23T07:11:16.867Z", "updatedAt": "2025-05-23T07:11:16.868Z", "isTestData": true, "testDataVersion": "1.0", "testDataSet": "role-testing"}, {"id": 11, "email": "<EMAIL>", "emailVerified": true, "verificationCode": null, "verificationExpiresAt": null, "ipAddress": null, "userAgent": null, "role": "admin", "username": "admin3", "name": "管理员3", "passwordHash": "$2b$10$example.hash.for.testing.purposes.only", "lastLoginAt": "2025-05-22T11:59:15.797Z", "createdAt": "2025-05-23T07:11:16.868Z", "updatedAt": "2025-05-23T07:11:16.868Z", "isTestData": true, "testDataVersion": "1.0", "testDataSet": "role-testing"}, {"id": 12, "email": "<EMAIL>", "emailVerified": false, "verificationCode": null, "verificationExpiresAt": null, "ipAddress": null, "userAgent": null, "role": "user", "username": "user1", "name": "用户1", "passwordHash": "$2b$10$example.hash.for.testing.purposes.only", "lastLoginAt": "2025-05-19T13:47:10.850Z", "createdAt": "2025-05-18T18:05:38.403Z", "updatedAt": "2025-05-23T07:11:16.869Z", "isTestData": true, "testDataVersion": "1.0", "testDataSet": "role-testing"}, {"id": 13, "email": "<EMAIL>", "emailVerified": true, "verificationCode": null, "verificationExpiresAt": null, "ipAddress": null, "userAgent": null, "role": "user", "username": "user2", "name": "用户2", "passwordHash": "$2b$10$example.hash.for.testing.purposes.only", "lastLoginAt": "2025-05-20T11:46:50.791Z", "createdAt": "2025-05-11T10:11:29.867Z", "updatedAt": "2025-05-23T07:11:16.870Z", "isTestData": true, "testDataVersion": "1.0", "testDataSet": "role-testing"}, {"id": 14, "email": "<EMAIL>", "emailVerified": false, "verificationCode": null, "verificationExpiresAt": null, "ipAddress": null, "userAgent": null, "role": "user", "username": "user3", "name": "用户3", "passwordHash": "$2b$10$example.hash.for.testing.purposes.only", "lastLoginAt": null, "createdAt": "2025-05-09T22:36:09.607Z", "updatedAt": "2025-05-23T07:11:16.870Z", "isTestData": true, "testDataVersion": "1.0", "testDataSet": "role-testing"}, {"id": 15, "email": "<EMAIL>", "emailVerified": true, "verificationCode": null, "verificationExpiresAt": null, "ipAddress": null, "userAgent": null, "role": "user", "username": "user4", "name": "用户4", "passwordHash": "$2b$10$example.hash.for.testing.purposes.only", "lastLoginAt": "2025-05-22T11:09:10.156Z", "createdAt": "2025-04-26T22:41:39.004Z", "updatedAt": "2025-05-23T07:11:16.871Z", "isTestData": true, "testDataVersion": "1.0", "testDataSet": "role-testing"}, {"id": 16, "email": "<EMAIL>", "emailVerified": true, "verificationCode": null, "verificationExpiresAt": null, "ipAddress": null, "userAgent": null, "role": "user", "username": "user5", "name": "用户5", "passwordHash": "$2b$10$example.hash.for.testing.purposes.only", "lastLoginAt": null, "createdAt": "2025-05-07T21:14:56.026Z", "updatedAt": "2025-05-23T07:11:16.872Z", "isTestData": true, "testDataVersion": "1.0", "testDataSet": "role-testing"}, {"id": 17, "email": "<EMAIL>", "emailVerified": true, "verificationCode": null, "verificationExpiresAt": null, "ipAddress": null, "userAgent": null, "role": "user", "username": "user6", "name": "用户6", "passwordHash": "$2b$10$example.hash.for.testing.purposes.only", "lastLoginAt": "2025-05-23T06:08:35.713Z", "createdAt": "2025-05-16T03:12:46.627Z", "updatedAt": "2025-05-23T07:11:16.873Z", "isTestData": true, "testDataVersion": "1.0", "testDataSet": "role-testing"}, {"id": 18, "email": "<EMAIL>", "emailVerified": true, "verificationCode": null, "verificationExpiresAt": null, "ipAddress": null, "userAgent": null, "role": "user", "username": "user7", "name": "用户7", "passwordHash": "$2b$10$example.hash.for.testing.purposes.only", "lastLoginAt": "2025-05-23T01:51:23.717Z", "createdAt": "2025-05-14T18:05:09.270Z", "updatedAt": "2025-05-23T07:11:16.873Z", "isTestData": true, "testDataVersion": "1.0", "testDataSet": "role-testing"}, {"id": 19, "email": "<EMAIL>", "emailVerified": true, "verificationCode": null, "verificationExpiresAt": null, "ipAddress": null, "userAgent": null, "role": "user", "username": "user8", "name": "用户8", "passwordHash": "$2b$10$example.hash.for.testing.purposes.only", "lastLoginAt": null, "createdAt": "2025-04-25T12:32:28.731Z", "updatedAt": "2025-05-23T07:11:16.874Z", "isTestData": true, "testDataVersion": "1.0", "testDataSet": "role-testing"}, {"id": 20, "email": "<EMAIL>", "emailVerified": false, "verificationCode": null, "verificationExpiresAt": null, "ipAddress": null, "userAgent": null, "role": "user", "username": "user9", "name": "用户9", "passwordHash": "$2b$10$example.hash.for.testing.purposes.only", "lastLoginAt": null, "createdAt": "2025-04-24T09:35:22.397Z", "updatedAt": "2025-05-23T07:11:16.875Z", "isTestData": true, "testDataVersion": "1.0", "testDataSet": "role-testing"}, {"id": 21, "email": "<EMAIL>", "emailVerified": true, "verificationCode": null, "verificationExpiresAt": null, "ipAddress": null, "userAgent": null, "role": "user", "username": "user10", "name": "用户10", "passwordHash": "$2b$10$example.hash.for.testing.purposes.only", "lastLoginAt": "2025-05-20T07:16:45.054Z", "createdAt": "2025-04-24T13:39:11.958Z", "updatedAt": "2025-05-23T07:11:16.875Z", "isTestData": true, "testDataVersion": "1.0", "testDataSet": "role-testing"}], "questionnaires": [{"id": 51, "userId": 20, "sequenceNumber": "TEST1747984276876001", "isAnonymous": false, "ipAddress": "************", "submittedById": null, "educationLevel": "硕士", "major": "电子工程", "graduationYear": 2021, "region": "二线城市", "expectedPosition": "测试职位1", "expectedSalaryRange": "10k-15k", "expectedWorkHours": 59, "expectedVacationDays": 13, "employmentStatus": "已就业", "currentIndustry": null, "currentPosition": "当前职位1", "monthlySalary": null, "jobSatisfaction": "满意", "unemploymentDuration": "1个月以内", "unemploymentReason": null, "jobHuntingDifficulty": 2, "regretMajor": true, "preferredMajor": null, "careerChangeIntention": true, "careerChangeTarget": null, "adviceForStudents": "这是第1份问卷的学生建议内容，包含了实用的就业指导。", "observationOnEmployment": "这是第1份问卷的就业观察，反映了当前就业市场的真实情况。", "tags": "[\"大专\",\"已就业\"]", "status": "verified", "createdAt": "2025-05-03T14:58:38.768Z", "updatedAt": "2025-05-23T07:11:16.877Z", "isTestData": true, "testDataVersion": "1.0", "testDataSet": "role-testing"}, {"id": 52, "userId": 14, "sequenceNumber": "TEST1747984276877002", "isAnonymous": false, "ipAddress": "*************", "submittedById": null, "educationLevel": "高中/中专", "major": "经济学", "graduationYear": 2022, "region": "省会城市", "expectedPosition": "测试职位2", "expectedSalaryRange": "20k+", "expectedWorkHours": 54, "expectedVacationDays": 17, "employmentStatus": "待业中", "currentIndustry": "制造业", "currentPosition": null, "monthlySalary": 5415, "jobSatisfaction": "满意", "unemploymentDuration": null, "unemploymentReason": null, "jobHuntingDifficulty": null, "regretMajor": false, "preferredMajor": null, "careerChangeIntention": false, "careerChangeTarget": null, "adviceForStudents": "这是第2份问卷的学生建议内容，包含了实用的就业指导。", "observationOnEmployment": "这是第2份问卷的就业观察，反映了当前就业市场的真实情况。", "tags": "[\"本科\",\"已就业\"]", "status": "pending", "createdAt": "2025-05-04T19:41:34.123Z", "updatedAt": "2025-05-23T07:11:16.878Z", "isTestData": true, "testDataVersion": "1.0", "testDataSet": "role-testing"}, {"id": 53, "userId": 12, "sequenceNumber": "TEST1747984276878003", "isAnonymous": false, "ipAddress": "*************", "submittedById": null, "educationLevel": "博士", "major": "教育学", "graduationYear": 2023, "region": "海外", "expectedPosition": "测试职位3", "expectedSalaryRange": "15k-20k", "expectedWorkHours": 57, "expectedVacationDays": 11, "employmentStatus": "已就业", "currentIndustry": "医疗", "currentPosition": "当前职位3", "monthlySalary": null, "jobSatisfaction": null, "unemploymentDuration": null, "unemploymentReason": null, "jobHuntingDifficulty": null, "regretMajor": true, "preferredMajor": "管理学", "careerChangeIntention": false, "careerChangeTarget": null, "adviceForStudents": "这是第3份问卷的学生建议内容，包含了实用的就业指导。", "observationOnEmployment": "这是第3份问卷的就业观察，反映了当前就业市场的真实情况。", "tags": "[\"大专\",\"自由职业\"]", "status": "pending", "createdAt": "2025-05-04T11:22:09.703Z", "updatedAt": "2025-05-23T07:11:16.879Z", "isTestData": true, "testDataVersion": "1.0", "testDataSet": "role-testing"}, {"id": 54, "userId": 20, "sequenceNumber": "TEST1747984276879004", "isAnonymous": false, "ipAddress": "*************", "submittedById": null, "educationLevel": "硕士", "major": "医学", "graduationYear": 2024, "region": "三四线城市", "expectedPosition": "测试职位4", "expectedSalaryRange": "15k-20k", "expectedWorkHours": 59, "expectedVacationDays": 11, "employmentStatus": "创业", "currentIndustry": null, "currentPosition": null, "monthlySalary": null, "jobSatisfaction": null, "unemploymentDuration": null, "unemploymentReason": null, "jobHuntingDifficulty": 4, "regretMajor": false, "preferredMajor": "电子工程", "careerChangeIntention": false, "careerChangeTarget": null, "adviceForStudents": "这是第4份问卷的学生建议内容，包含了实用的就业指导。", "observationOnEmployment": "这是第4份问卷的就业观察，反映了当前就业市场的真实情况。", "tags": "[\"高中/中专\",\"自由职业\"]", "status": "verified", "createdAt": "2025-05-04T22:09:59.758Z", "updatedAt": "2025-05-23T07:11:16.879Z", "isTestData": true, "testDataVersion": "1.0", "testDataSet": "role-testing"}, {"id": 55, "userId": 20, "sequenceNumber": "TEST1747984276880005", "isAnonymous": false, "ipAddress": "*************", "submittedById": null, "educationLevel": "高中/中专", "major": "法学", "graduationYear": 2021, "region": "北上广深", "expectedPosition": "测试职位5", "expectedSalaryRange": "10k-15k", "expectedWorkHours": 47, "expectedVacationDays": 9, "employmentStatus": "待业中", "currentIndustry": "制造业", "currentPosition": "当前职位5", "monthlySalary": null, "jobSatisfaction": null, "unemploymentDuration": null, "unemploymentReason": null, "jobHuntingDifficulty": null, "regretMajor": false, "preferredMajor": null, "careerChangeIntention": true, "careerChangeTarget": null, "adviceForStudents": "这是第5份问卷的学生建议内容，包含了实用的就业指导。", "observationOnEmployment": "这是第5份问卷的就业观察，反映了当前就业市场的真实情况。", "tags": "[\"大专\",\"创业\"]", "status": "verified", "createdAt": "2025-04-26T19:49:57.939Z", "updatedAt": "2025-05-23T07:11:16.880Z", "isTestData": true, "testDataVersion": "1.0", "testDataSet": "role-testing"}, {"id": 56, "userId": 21, "sequenceNumber": "TEST1747984276880006", "isAnonymous": false, "ipAddress": "*************", "submittedById": null, "educationLevel": "高中/中专", "major": "电子工程", "graduationYear": 2021, "region": "北上广深", "expectedPosition": "测试职位6", "expectedSalaryRange": "5k-10k", "expectedWorkHours": 46, "expectedVacationDays": 13, "employmentStatus": "已就业", "currentIndustry": "IT/互联网", "currentPosition": "当前职位6", "monthlySalary": null, "jobSatisfaction": "满意", "unemploymentDuration": null, "unemploymentReason": null, "jobHuntingDifficulty": null, "regretMajor": true, "preferredMajor": "医学", "careerChangeIntention": true, "careerChangeTarget": "目标行业", "adviceForStudents": "这是第6份问卷的学生建议内容，包含了实用的就业指导。", "observationOnEmployment": "这是第6份问卷的就业观察，反映了当前就业市场的真实情况。", "tags": "[\"高中/中专\",\"自由职业\"]", "status": "pending", "createdAt": "2025-05-12T11:01:04.676Z", "updatedAt": "2025-05-23T07:11:16.881Z", "isTestData": true, "testDataVersion": "1.0", "testDataSet": "role-testing"}, {"id": 57, "userId": 13, "sequenceNumber": "TEST1747984276881007", "isAnonymous": false, "ipAddress": "************", "submittedById": null, "educationLevel": "博士", "major": "法学", "graduationYear": 2022, "region": "二线城市", "expectedPosition": "测试职位7", "expectedSalaryRange": "15k-20k", "expectedWorkHours": 55, "expectedVacationDays": 9, "employmentStatus": "创业", "currentIndustry": null, "currentPosition": "当前职位7", "monthlySalary": null, "jobSatisfaction": "满意", "unemploymentDuration": "1个月以内", "unemploymentReason": null, "jobHuntingDifficulty": 4, "regretMajor": false, "preferredMajor": null, "careerChangeIntention": true, "careerChangeTarget": "目标行业", "adviceForStudents": "这是第7份问卷的学生建议内容，包含了实用的就业指导。", "observationOnEmployment": "这是第7份问卷的就业观察，反映了当前就业市场的真实情况。", "tags": "[\"大专\",\"已就业\"]", "status": "normal", "createdAt": "2025-05-23T04:59:22.590Z", "updatedAt": "2025-05-23T07:11:16.882Z", "isTestData": true, "testDataVersion": "1.0", "testDataSet": "role-testing"}, {"id": 58, "userId": 13, "sequenceNumber": "TEST1747984276882008", "isAnonymous": false, "ipAddress": "*************", "submittedById": null, "educationLevel": "硕士", "major": "医学", "graduationYear": 2020, "region": "三四线城市", "expectedPosition": "测试职位8", "expectedSalaryRange": "5k-10k", "expectedWorkHours": 44, "expectedVacationDays": 15, "employmentStatus": "自由职业", "currentIndustry": "文化/传媒", "currentPosition": null, "monthlySalary": 12229, "jobSatisfaction": "一般", "unemploymentDuration": "1-3个月", "unemploymentReason": null, "jobHuntingDifficulty": 4, "regretMajor": true, "preferredMajor": null, "careerChangeIntention": false, "careerChangeTarget": null, "adviceForStudents": "这是第8份问卷的学生建议内容，包含了实用的就业指导。", "observationOnEmployment": "这是第8份问卷的就业观察，反映了当前就业市场的真实情况。", "tags": "[\"高中/中专\",\"自由职业\"]", "status": "verified", "createdAt": "2025-05-01T01:11:18.494Z", "updatedAt": "2025-05-23T07:11:16.882Z", "isTestData": true, "testDataVersion": "1.0", "testDataSet": "role-testing"}, {"id": 59, "userId": 13, "sequenceNumber": "TEST1747984276882009", "isAnonymous": false, "ipAddress": "************", "submittedById": null, "educationLevel": "大专", "major": "电子工程", "graduationYear": 2020, "region": "省会城市", "expectedPosition": "测试职位9", "expectedSalaryRange": "15k-20k", "expectedWorkHours": 57, "expectedVacationDays": 10, "employmentStatus": "已就业", "currentIndustry": "金融", "currentPosition": "当前职位9", "monthlySalary": null, "jobSatisfaction": "很满意", "unemploymentDuration": null, "unemploymentReason": null, "jobHuntingDifficulty": null, "regretMajor": true, "preferredMajor": null, "careerChangeIntention": true, "careerChangeTarget": null, "adviceForStudents": "这是第9份问卷的学生建议内容，包含了实用的就业指导。", "observationOnEmployment": "这是第9份问卷的就业观察，反映了当前就业市场的真实情况。", "tags": "[\"硕士\",\"待业中\"]", "status": "normal", "createdAt": "2025-05-15T23:01:36.737Z", "updatedAt": "2025-05-23T07:11:16.883Z", "isTestData": true, "testDataVersion": "1.0", "testDataSet": "role-testing"}, {"id": 60, "userId": 18, "sequenceNumber": "TEST1747984276883010", "isAnonymous": false, "ipAddress": "*************", "submittedById": null, "educationLevel": "硕士", "major": "教育学", "graduationYear": 2022, "region": "二线城市", "expectedPosition": "测试职位10", "expectedSalaryRange": "15k-20k", "expectedWorkHours": 59, "expectedVacationDays": 7, "employmentStatus": "创业", "currentIndustry": "文化/传媒", "currentPosition": null, "monthlySalary": null, "jobSatisfaction": null, "unemploymentDuration": "3-6个月", "unemploymentReason": null, "jobHuntingDifficulty": 2, "regretMajor": false, "preferredMajor": "法学", "careerChangeIntention": false, "careerChangeTarget": "目标行业", "adviceForStudents": "这是第10份问卷的学生建议内容，包含了实用的就业指导。", "observationOnEmployment": "这是第10份问卷的就业观察，反映了当前就业市场的真实情况。", "tags": "[\"本科\",\"其他\"]", "status": "pending", "createdAt": "2025-05-04T04:30:29.610Z", "updatedAt": "2025-05-23T07:11:16.884Z", "isTestData": true, "testDataVersion": "1.0", "testDataSet": "role-testing"}, {"id": 61, "userId": 20, "sequenceNumber": "TEST1747984276884011", "isAnonymous": false, "ipAddress": "************", "submittedById": null, "educationLevel": "硕士", "major": "电子工程", "graduationYear": 2023, "region": "县城或乡镇", "expectedPosition": "测试职位11", "expectedSalaryRange": "10k-15k", "expectedWorkHours": 42, "expectedVacationDays": 14, "employmentStatus": "待业中", "currentIndustry": "医疗", "currentPosition": "当前职位11", "monthlySalary": null, "jobSatisfaction": null, "unemploymentDuration": null, "unemploymentReason": null, "jobHuntingDifficulty": 3, "regretMajor": false, "preferredMajor": null, "careerChangeIntention": false, "careerChangeTarget": null, "adviceForStudents": "这是第11份问卷的学生建议内容，包含了实用的就业指导。", "observationOnEmployment": "这是第11份问卷的就业观察，反映了当前就业市场的真实情况。", "tags": "[\"本科\",\"已就业\"]", "status": "normal", "createdAt": "2025-05-10T20:00:08.053Z", "updatedAt": "2025-05-23T07:11:16.884Z", "isTestData": true, "testDataVersion": "1.0", "testDataSet": "role-testing"}, {"id": 62, "userId": 14, "sequenceNumber": "TEST1747984276885012", "isAnonymous": false, "ipAddress": "*************", "submittedById": null, "educationLevel": "高中/中专", "major": "经济学", "graduationYear": 2024, "region": "北上广深", "expectedPosition": "测试职位12", "expectedSalaryRange": "15k-20k", "expectedWorkHours": 47, "expectedVacationDays": 16, "employmentStatus": "自由职业", "currentIndustry": null, "currentPosition": "当前职位12", "monthlySalary": null, "jobSatisfaction": "很不满意", "unemploymentDuration": null, "unemploymentReason": null, "jobHuntingDifficulty": 2, "regretMajor": false, "preferredMajor": null, "careerChangeIntention": true, "careerChangeTarget": null, "adviceForStudents": "这是第12份问卷的学生建议内容，包含了实用的就业指导。", "observationOnEmployment": "这是第12份问卷的就业观察，反映了当前就业市场的真实情况。", "tags": "[\"博士\",\"自由职业\"]", "status": "normal", "createdAt": "2025-05-09T04:04:40.664Z", "updatedAt": "2025-05-23T07:11:16.885Z", "isTestData": true, "testDataVersion": "1.0", "testDataSet": "role-testing"}, {"id": 63, "userId": 20, "sequenceNumber": "TEST1747984276885013", "isAnonymous": false, "ipAddress": "*************", "submittedById": null, "educationLevel": "本科", "major": "计算机科学", "graduationYear": 2020, "region": "县城或乡镇", "expectedPosition": "测试职位13", "expectedSalaryRange": "5k-10k", "expectedWorkHours": 59, "expectedVacationDays": 10, "employmentStatus": "自由职业", "currentIndustry": "金融", "currentPosition": null, "monthlySalary": 5171, "jobSatisfaction": "很满意", "unemploymentDuration": null, "unemploymentReason": null, "jobHuntingDifficulty": null, "regretMajor": false, "preferredMajor": null, "careerChangeIntention": true, "careerChangeTarget": null, "adviceForStudents": "这是第13份问卷的学生建议内容，包含了实用的就业指导。", "observationOnEmployment": "这是第13份问卷的就业观察，反映了当前就业市场的真实情况。", "tags": "[\"大专\",\"其他\"]", "status": "verified", "createdAt": "2025-05-09T08:32:10.616Z", "updatedAt": "2025-05-23T07:11:16.886Z", "isTestData": true, "testDataVersion": "1.0", "testDataSet": "role-testing"}, {"id": 64, "userId": 18, "sequenceNumber": "TEST1747984276886014", "isAnonymous": false, "ipAddress": "************2", "submittedById": null, "educationLevel": "大专", "major": "法学", "graduationYear": 2023, "region": "二线城市", "expectedPosition": "测试职位14", "expectedSalaryRange": "10k-15k", "expectedWorkHours": 53, "expectedVacationDays": 12, "employmentStatus": "自由职业", "currentIndustry": "制造业", "currentPosition": null, "monthlySalary": 18892, "jobSatisfaction": "很不满意", "unemploymentDuration": null, "unemploymentReason": null, "jobHuntingDifficulty": null, "regretMajor": false, "preferredMajor": "计算机科学", "careerChangeIntention": true, "careerChangeTarget": null, "adviceForStudents": "这是第14份问卷的学生建议内容，包含了实用的就业指导。", "observationOnEmployment": "这是第14份问卷的就业观察，反映了当前就业市场的真实情况。", "tags": "[\"大专\",\"自由职业\"]", "status": "pending", "createdAt": "2025-05-06T15:16:59.922Z", "updatedAt": "2025-05-23T07:11:16.887Z", "isTestData": true, "testDataVersion": "1.0", "testDataSet": "role-testing"}, {"id": 65, "userId": 19, "sequenceNumber": "TEST1747984276887015", "isAnonymous": false, "ipAddress": "************", "submittedById": null, "educationLevel": "本科", "major": "法学", "graduationYear": 2020, "region": "北上广深", "expectedPosition": "测试职位15", "expectedSalaryRange": "10k-15k", "expectedWorkHours": 42, "expectedVacationDays": 16, "employmentStatus": "创业", "currentIndustry": null, "currentPosition": null, "monthlySalary": null, "jobSatisfaction": "很不满意", "unemploymentDuration": "6个月以上", "unemploymentReason": null, "jobHuntingDifficulty": null, "regretMajor": false, "preferredMajor": "法学", "careerChangeIntention": true, "careerChangeTarget": null, "adviceForStudents": "这是第15份问卷的学生建议内容，包含了实用的就业指导。", "observationOnEmployment": "这是第15份问卷的就业观察，反映了当前就业市场的真实情况。", "tags": "[\"本科\",\"已就业\"]", "status": "normal", "createdAt": "2025-05-14T05:51:55.012Z", "updatedAt": "2025-05-23T07:11:16.887Z", "isTestData": true, "testDataVersion": "1.0", "testDataSet": "role-testing"}, {"id": 66, "userId": 13, "sequenceNumber": "TEST1747984276887016", "isAnonymous": false, "ipAddress": "*************", "submittedById": null, "educationLevel": "大专", "major": "医学", "graduationYear": 2021, "region": "三四线城市", "expectedPosition": "测试职位16", "expectedSalaryRange": "15k-20k", "expectedWorkHours": 58, "expectedVacationDays": 12, "employmentStatus": "自由职业", "currentIndustry": "政府/事业单位", "currentPosition": "当前职位16", "monthlySalary": null, "jobSatisfaction": "一般", "unemploymentDuration": null, "unemploymentReason": null, "jobHuntingDifficulty": null, "regretMajor": true, "preferredMajor": null, "careerChangeIntention": false, "careerChangeTarget": null, "adviceForStudents": "这是第16份问卷的学生建议内容，包含了实用的就业指导。", "observationOnEmployment": "这是第16份问卷的就业观察，反映了当前就业市场的真实情况。", "tags": "[\"大专\",\"待业中\"]", "status": "verified", "createdAt": "2025-04-24T02:08:03.770Z", "updatedAt": "2025-05-23T07:11:16.888Z", "isTestData": true, "testDataVersion": "1.0", "testDataSet": "role-testing"}, {"id": 67, "userId": 21, "sequenceNumber": "TEST1747984276888017", "isAnonymous": false, "ipAddress": "************", "submittedById": null, "educationLevel": "大专", "major": "电子工程", "graduationYear": 2021, "region": "省会城市", "expectedPosition": "测试职位17", "expectedSalaryRange": "15k-20k", "expectedWorkHours": 48, "expectedVacationDays": 17, "employmentStatus": "其他", "currentIndustry": "IT/互联网", "currentPosition": null, "monthlySalary": 7342, "jobSatisfaction": "一般", "unemploymentDuration": null, "unemploymentReason": "测试失业原因", "jobHuntingDifficulty": null, "regretMajor": false, "preferredMajor": "经济学", "careerChangeIntention": true, "careerChangeTarget": null, "adviceForStudents": "这是第17份问卷的学生建议内容，包含了实用的就业指导。", "observationOnEmployment": "这是第17份问卷的就业观察，反映了当前就业市场的真实情况。", "tags": "[\"硕士\",\"待业中\"]", "status": "verified", "createdAt": "2025-05-14T18:48:43.702Z", "updatedAt": "2025-05-23T07:11:16.889Z", "isTestData": true, "testDataVersion": "1.0", "testDataSet": "role-testing"}, {"id": 68, "userId": 21, "sequenceNumber": "TEST1747984276889018", "isAnonymous": false, "ipAddress": "***********", "submittedById": null, "educationLevel": "硕士", "major": "管理学", "graduationYear": 2021, "region": "三四线城市", "expectedPosition": "测试职位18", "expectedSalaryRange": "10k-15k", "expectedWorkHours": 53, "expectedVacationDays": 8, "employmentStatus": "待业中", "currentIndustry": "政府/事业单位", "currentPosition": "当前职位18", "monthlySalary": null, "jobSatisfaction": "很不满意", "unemploymentDuration": null, "unemploymentReason": null, "jobHuntingDifficulty": null, "regretMajor": true, "preferredMajor": null, "careerChangeIntention": true, "careerChangeTarget": "目标行业", "adviceForStudents": "这是第18份问卷的学生建议内容，包含了实用的就业指导。", "observationOnEmployment": "这是第18份问卷的就业观察，反映了当前就业市场的真实情况。", "tags": "[\"硕士\",\"其他\"]", "status": "normal", "createdAt": "2025-04-30T05:13:03.439Z", "updatedAt": "2025-05-23T07:11:16.890Z", "isTestData": true, "testDataVersion": "1.0", "testDataSet": "role-testing"}, {"id": 69, "userId": 14, "sequenceNumber": "TEST1747984276891019", "isAnonymous": false, "ipAddress": "*************", "submittedById": null, "educationLevel": "本科", "major": "医学", "graduationYear": 2020, "region": "三四线城市", "expectedPosition": "测试职位19", "expectedSalaryRange": "20k+", "expectedWorkHours": 54, "expectedVacationDays": 7, "employmentStatus": "自由职业", "currentIndustry": null, "currentPosition": "当前职位19", "monthlySalary": 20183, "jobSatisfaction": null, "unemploymentDuration": null, "unemploymentReason": null, "jobHuntingDifficulty": null, "regretMajor": false, "preferredMajor": null, "careerChangeIntention": true, "careerChangeTarget": null, "adviceForStudents": "这是第19份问卷的学生建议内容，包含了实用的就业指导。", "observationOnEmployment": "这是第19份问卷的就业观察，反映了当前就业市场的真实情况。", "tags": "[\"高中/中专\",\"待业中\"]", "status": "verified", "createdAt": "2025-05-05T03:27:12.853Z", "updatedAt": "2025-05-23T07:11:16.892Z", "isTestData": true, "testDataVersion": "1.0", "testDataSet": "role-testing"}, {"id": 70, "userId": 13, "sequenceNumber": "TEST1747984276892020", "isAnonymous": false, "ipAddress": "*************", "submittedById": null, "educationLevel": "本科", "major": "电子工程", "graduationYear": 2020, "region": "省会城市", "expectedPosition": "测试职位20", "expectedSalaryRange": "15k-20k", "expectedWorkHours": 53, "expectedVacationDays": 13, "employmentStatus": "待业中", "currentIndustry": "医疗", "currentPosition": null, "monthlySalary": null, "jobSatisfaction": "不满意", "unemploymentDuration": null, "unemploymentReason": null, "jobHuntingDifficulty": null, "regretMajor": true, "preferredMajor": null, "careerChangeIntention": true, "careerChangeTarget": null, "adviceForStudents": "这是第20份问卷的学生建议内容，包含了实用的就业指导。", "observationOnEmployment": "这是第20份问卷的就业观察，反映了当前就业市场的真实情况。", "tags": "[\"高中/中专\",\"已就业\"]", "status": "normal", "createdAt": "2025-05-09T00:16:14.961Z", "updatedAt": "2025-05-23T07:11:16.893Z", "isTestData": true, "testDataVersion": "1.0", "testDataSet": "role-testing"}, {"id": 71, "userId": null, "sequenceNumber": "TEST1747984276893021", "isAnonymous": true, "ipAddress": "************1", "submittedById": "anon_aba3e17d-7e50-4606-b4e1-08a69c1b2849", "educationLevel": "本科", "major": "教育学", "graduationYear": 2020, "region": "北上广深", "expectedPosition": "测试职位21", "expectedSalaryRange": "5k-10k", "expectedWorkHours": 58, "expectedVacationDays": 14, "employmentStatus": "其他", "currentIndustry": "政府/事业单位", "currentPosition": "当前职位21", "monthlySalary": 10336, "jobSatisfaction": "很满意", "unemploymentDuration": null, "unemploymentReason": null, "jobHuntingDifficulty": null, "regretMajor": true, "preferredMajor": "管理学", "careerChangeIntention": false, "careerChangeTarget": null, "adviceForStudents": "这是第21份问卷的学生建议内容，包含了实用的就业指导。", "observationOnEmployment": "这是第21份问卷的就业观察，反映了当前就业市场的真实情况。", "tags": "[\"博士\",\"创业\"]", "status": "verified", "createdAt": "2025-05-20T16:06:28.173Z", "updatedAt": "2025-05-23T07:11:16.894Z", "isTestData": true, "testDataVersion": "1.0", "testDataSet": "role-testing"}, {"id": 72, "userId": null, "sequenceNumber": "TEST1747984276894022", "isAnonymous": true, "ipAddress": "*************", "submittedById": "anon_20ab31ac-c71f-4425-a9f0-6ed9caa73342", "educationLevel": "本科", "major": "计算机科学", "graduationYear": 2021, "region": "县城或乡镇", "expectedPosition": "测试职位22", "expectedSalaryRange": "15k-20k", "expectedWorkHours": 54, "expectedVacationDays": 11, "employmentStatus": "创业", "currentIndustry": null, "currentPosition": null, "monthlySalary": null, "jobSatisfaction": null, "unemploymentDuration": "6个月以上", "unemploymentReason": null, "jobHuntingDifficulty": 3, "regretMajor": true, "preferredMajor": null, "careerChangeIntention": true, "careerChangeTarget": null, "adviceForStudents": "这是第22份问卷的学生建议内容，包含了实用的就业指导。", "observationOnEmployment": "这是第22份问卷的就业观察，反映了当前就业市场的真实情况。", "tags": "[\"博士\",\"创业\"]", "status": "normal", "createdAt": "2025-04-24T08:10:19.574Z", "updatedAt": "2025-05-23T07:11:16.894Z", "isTestData": true, "testDataVersion": "1.0", "testDataSet": "role-testing"}, {"id": 73, "userId": null, "sequenceNumber": "TEST1747984276894023", "isAnonymous": true, "ipAddress": "*************", "submittedById": "anon_ccb5c424-e3a8-42cf-bfa3-79c0c708aea2", "educationLevel": "高中/中专", "major": "管理学", "graduationYear": 2022, "region": "海外", "expectedPosition": "测试职位23", "expectedSalaryRange": "15k-20k", "expectedWorkHours": 40, "expectedVacationDays": 10, "employmentStatus": "已就业", "currentIndustry": "教育", "currentPosition": null, "monthlySalary": null, "jobSatisfaction": "很不满意", "unemploymentDuration": null, "unemploymentReason": null, "jobHuntingDifficulty": null, "regretMajor": true, "preferredMajor": "经济学", "careerChangeIntention": true, "careerChangeTarget": null, "adviceForStudents": "这是第23份问卷的学生建议内容，包含了实用的就业指导。", "observationOnEmployment": "这是第23份问卷的就业观察，反映了当前就业市场的真实情况。", "tags": "[\"大专\",\"创业\"]", "status": "normal", "createdAt": "2025-05-22T13:19:28.400Z", "updatedAt": "2025-05-23T07:11:16.895Z", "isTestData": true, "testDataVersion": "1.0", "testDataSet": "role-testing"}, {"id": 74, "userId": null, "sequenceNumber": "TEST1747984276895024", "isAnonymous": true, "ipAddress": "************", "submittedById": "anon_c78de00b-c869-43c0-9b8a-10540df3c31e", "educationLevel": "博士", "major": "计算机科学", "graduationYear": 2022, "region": "省会城市", "expectedPosition": "测试职位24", "expectedSalaryRange": "15k-20k", "expectedWorkHours": 54, "expectedVacationDays": 8, "employmentStatus": "已就业", "currentIndustry": null, "currentPosition": null, "monthlySalary": null, "jobSatisfaction": null, "unemploymentDuration": null, "unemploymentReason": null, "jobHuntingDifficulty": null, "regretMajor": false, "preferredMajor": null, "careerChangeIntention": true, "careerChangeTarget": "目标行业", "adviceForStudents": "这是第24份问卷的学生建议内容，包含了实用的就业指导。", "observationOnEmployment": "这是第24份问卷的就业观察，反映了当前就业市场的真实情况。", "tags": "[\"博士\",\"创业\"]", "status": "verified", "createdAt": "2025-04-29T02:32:10.139Z", "updatedAt": "2025-05-23T07:11:16.896Z", "isTestData": true, "testDataVersion": "1.0", "testDataSet": "role-testing"}, {"id": 75, "userId": null, "sequenceNumber": "TEST1747984276896025", "isAnonymous": true, "ipAddress": "************", "submittedById": "anon_d9328d6e-be9c-44c4-bb89-eb9314d014f4", "educationLevel": "博士", "major": "计算机科学", "graduationYear": 2023, "region": "省会城市", "expectedPosition": "测试职位25", "expectedSalaryRange": "15k-20k", "expectedWorkHours": 58, "expectedVacationDays": 7, "employmentStatus": "自由职业", "currentIndustry": "教育", "currentPosition": "当前职位25", "monthlySalary": 11567, "jobSatisfaction": "满意", "unemploymentDuration": "3-6个月", "unemploymentReason": null, "jobHuntingDifficulty": null, "regretMajor": false, "preferredMajor": null, "careerChangeIntention": false, "careerChangeTarget": null, "adviceForStudents": "这是第25份问卷的学生建议内容，包含了实用的就业指导。", "observationOnEmployment": "这是第25份问卷的就业观察，反映了当前就业市场的真实情况。", "tags": "[\"本科\",\"其他\"]", "status": "verified", "createdAt": "2025-04-28T06:36:36.668Z", "updatedAt": "2025-05-23T07:11:16.896Z", "isTestData": true, "testDataVersion": "1.0", "testDataSet": "role-testing"}, {"id": 76, "userId": null, "sequenceNumber": "TEST1747984276896026", "isAnonymous": true, "ipAddress": "*************", "submittedById": "anon_e76f3792-62a5-4259-8246-a58d9acfabf1", "educationLevel": "大专", "major": "计算机科学", "graduationYear": 2023, "region": "省会城市", "expectedPosition": "测试职位26", "expectedSalaryRange": "20k+", "expectedWorkHours": 53, "expectedVacationDays": 16, "employmentStatus": "待业中", "currentIndustry": null, "currentPosition": "当前职位26", "monthlySalary": 16538, "jobSatisfaction": "一般", "unemploymentDuration": null, "unemploymentReason": null, "jobHuntingDifficulty": 1, "regretMajor": false, "preferredMajor": null, "careerChangeIntention": true, "careerChangeTarget": null, "adviceForStudents": "这是第26份问卷的学生建议内容，包含了实用的就业指导。", "observationOnEmployment": "这是第26份问卷的就业观察，反映了当前就业市场的真实情况。", "tags": "[\"高中/中专\",\"已就业\"]", "status": "normal", "createdAt": "2025-05-08T06:48:17.442Z", "updatedAt": "2025-05-23T07:11:16.897Z", "isTestData": true, "testDataVersion": "1.0", "testDataSet": "role-testing"}, {"id": 77, "userId": null, "sequenceNumber": "TEST1747984276897027", "isAnonymous": true, "ipAddress": "************", "submittedById": "anon_13600f7d-65e2-4cf6-9363-8b0b64215f14", "educationLevel": "高中/中专", "major": "计算机科学", "graduationYear": 2020, "region": "三四线城市", "expectedPosition": "测试职位27", "expectedSalaryRange": "5k-10k", "expectedWorkHours": 42, "expectedVacationDays": 5, "employmentStatus": "自由职业", "currentIndustry": null, "currentPosition": null, "monthlySalary": 11058, "jobSatisfaction": null, "unemploymentDuration": null, "unemploymentReason": null, "jobHuntingDifficulty": null, "regretMajor": true, "preferredMajor": null, "careerChangeIntention": false, "careerChangeTarget": null, "adviceForStudents": "这是第27份问卷的学生建议内容，包含了实用的就业指导。", "observationOnEmployment": "这是第27份问卷的就业观察，反映了当前就业市场的真实情况。", "tags": "[\"大专\",\"创业\"]", "status": "normal", "createdAt": "2025-05-07T14:39:07.155Z", "updatedAt": "2025-05-23T07:11:16.897Z", "isTestData": true, "testDataVersion": "1.0", "testDataSet": "role-testing"}, {"id": 78, "userId": null, "sequenceNumber": "TEST1747984276897028", "isAnonymous": true, "ipAddress": "*************", "submittedById": "anon_dfbda21a-d366-4cff-8951-efa6bbcef0f5", "educationLevel": "博士", "major": "法学", "graduationYear": 2023, "region": "三四线城市", "expectedPosition": "测试职位28", "expectedSalaryRange": "15k-20k", "expectedWorkHours": 46, "expectedVacationDays": 12, "employmentStatus": "其他", "currentIndustry": "医疗", "currentPosition": "当前职位28", "monthlySalary": 10866, "jobSatisfaction": "一般", "unemploymentDuration": null, "unemploymentReason": null, "jobHuntingDifficulty": null, "regretMajor": false, "preferredMajor": "电子工程", "careerChangeIntention": true, "careerChangeTarget": "目标行业", "adviceForStudents": "这是第28份问卷的学生建议内容，包含了实用的就业指导。", "observationOnEmployment": "这是第28份问卷的就业观察，反映了当前就业市场的真实情况。", "tags": "[\"大专\",\"待业中\"]", "status": "normal", "createdAt": "2025-05-03T22:42:45.323Z", "updatedAt": "2025-05-23T07:11:16.898Z", "isTestData": true, "testDataVersion": "1.0", "testDataSet": "role-testing"}, {"id": 79, "userId": null, "sequenceNumber": "TEST1747984276898029", "isAnonymous": true, "ipAddress": "*************", "submittedById": "anon_c3a6a029-c91a-407e-849f-2828b3464d8c", "educationLevel": "本科", "major": "教育学", "graduationYear": 2020, "region": "三四线城市", "expectedPosition": "测试职位29", "expectedSalaryRange": "10k-15k", "expectedWorkHours": 44, "expectedVacationDays": 6, "employmentStatus": "自由职业", "currentIndustry": "金融", "currentPosition": "当前职位29", "monthlySalary": 24013, "jobSatisfaction": "不满意", "unemploymentDuration": null, "unemploymentReason": null, "jobHuntingDifficulty": null, "regretMajor": true, "preferredMajor": null, "careerChangeIntention": true, "careerChangeTarget": null, "adviceForStudents": "这是第29份问卷的学生建议内容，包含了实用的就业指导。", "observationOnEmployment": "这是第29份问卷的就业观察，反映了当前就业市场的真实情况。", "tags": "[\"大专\",\"待业中\"]", "status": "normal", "createdAt": "2025-05-21T07:03:29.323Z", "updatedAt": "2025-05-23T07:11:16.899Z", "isTestData": true, "testDataVersion": "1.0", "testDataSet": "role-testing"}, {"id": 80, "userId": null, "sequenceNumber": "TEST1747984276899030", "isAnonymous": true, "ipAddress": "************0", "submittedById": "anon_0e21ba1e-d21a-4d15-90cb-2c2f53fff8fc", "educationLevel": "博士", "major": "经济学", "graduationYear": 2020, "region": "二线城市", "expectedPosition": "测试职位30", "expectedSalaryRange": "20k+", "expectedWorkHours": 41, "expectedVacationDays": 10, "employmentStatus": "待业中", "currentIndustry": "文化/传媒", "currentPosition": null, "monthlySalary": 19624, "jobSatisfaction": "不满意", "unemploymentDuration": null, "unemploymentReason": null, "jobHuntingDifficulty": null, "regretMajor": true, "preferredMajor": "计算机科学", "careerChangeIntention": true, "careerChangeTarget": null, "adviceForStudents": "这是第30份问卷的学生建议内容，包含了实用的就业指导。", "observationOnEmployment": "这是第30份问卷的就业观察，反映了当前就业市场的真实情况。", "tags": "[\"大专\",\"创业\"]", "status": "verified", "createdAt": "2025-05-20T05:04:23.499Z", "updatedAt": "2025-05-23T07:11:16.899Z", "isTestData": true, "testDataVersion": "1.0", "testDataSet": "role-testing"}], "stories": [{"id": 11, "userId": 13, "isAnonymous": false, "title": "测试故事1: 转行经历", "content": "转行对我来说是一个重大决定。从原来的行业到现在的工作，我经历了很多困难但也收获了很多。 这是第1个测试故事的详细内容，包含了丰富的职场经验和人生感悟。故事内容需要经过审核才能发布，当前状态为待审核。", "author": "用户2", "ipAddress": "*************", "likes": 98, "dislikes": 17, "tags": "[\"实习体验\",\"测试数据\",\"大专\"]", "category": "职场感悟", "educationLevel": "本科", "industry": "制造业", "status": "pending", "submittedById": null, "createdAt": "2025-05-10T17:21:47.033Z", "updatedAt": "2025-05-23T07:11:16.901Z", "isTestData": true, "testDataVersion": "1.0", "testDataSet": "role-testing"}, {"id": 12, "userId": null, "isAnonymous": true, "title": "测试故事2: 实习体验", "content": "实习期间的经历让我对职场有了更深的理解，也帮助我明确了未来的职业方向。 这是第2个测试故事的详细内容，包含了丰富的职场经验和人生感悟。故事内容需要经过审核才能发布，当前状态为待审核。", "author": "匿名用户", "ipAddress": "***********6", "likes": 61, "dislikes": 19, "tags": "[\"转行经历\",\"测试数据\",\"大专\"]", "category": "求职经历", "educationLevel": "大专", "industry": "制造业", "status": "pending", "submittedById": "anon_d10bcefe-d9c8-4eac-912d-5a7255a63095", "createdAt": "2025-05-11T21:12:12.409Z", "updatedAt": "2025-05-23T07:11:16.901Z", "isTestData": true, "testDataVersion": "1.0", "testDataSet": "role-testing"}, {"id": 13, "userId": 19, "isAnonymous": false, "title": "测试故事3: 转行经历", "content": "实习期间的经历让我对职场有了更深的理解，也帮助我明确了未来的职业方向。 这是第3个测试故事的详细内容，包含了丰富的职场经验和人生感悟。故事内容需要经过审核才能发布，当前状态为待审核。", "author": "用户8", "ipAddress": "************", "likes": 97, "dislikes": 1, "tags": "[\"实习体验\",\"测试数据\",\"本科\"]", "category": "创业故事", "educationLevel": "大专", "industry": "医疗", "status": "pending", "submittedById": null, "createdAt": "2025-05-17T03:40:16.130Z", "updatedAt": "2025-05-23T07:11:16.902Z", "isTestData": true, "testDataVersion": "1.0", "testDataSet": "role-testing"}, {"id": 14, "userId": 7, "isAnonymous": false, "title": "测试故事4: 求职经历", "content": "转行对我来说是一个重大决定。从原来的行业到现在的工作，我经历了很多困难但也收获了很多。 这是第4个测试故事的详细内容，包含了丰富的职场经验和人生感悟。故事内容需要经过审核才能发布，当前状态为待审核。", "author": "审核员4", "ipAddress": "*************", "likes": 14, "dislikes": 4, "tags": "[\"职场感悟\",\"测试数据\",\"本科\"]", "category": "求职经历", "educationLevel": "本科", "industry": "文化/传媒", "status": "pending", "submittedById": null, "createdAt": "2025-05-09T13:35:52.748Z", "updatedAt": "2025-05-23T07:11:16.902Z", "isTestData": true, "testDataVersion": "1.0", "testDataSet": "role-testing"}, {"id": 15, "userId": null, "isAnonymous": true, "title": "测试故事5: 职场感悟", "content": "创业的道路虽然艰辛，但每一次挫折都是成长的机会。我想分享一些创业路上的心得体会。 这是第5个测试故事的详细内容，包含了丰富的职场经验和人生感悟。故事内容需要经过审核才能发布，当前状态为待审核。", "author": "匿名用户", "ipAddress": "************8", "likes": 84, "dislikes": 12, "tags": "[\"转行经历\",\"测试数据\",\"硕士\"]", "category": "职场感悟", "educationLevel": "大专", "industry": "医疗", "status": "pending", "submittedById": "anon_075ae996-b5a7-4313-b6d5-971f276f79a5", "createdAt": "2025-05-16T23:09:40.906Z", "updatedAt": "2025-05-23T07:11:16.903Z", "isTestData": true, "testDataVersion": "1.0", "testDataSet": "role-testing"}, {"id": 16, "userId": null, "isAnonymous": true, "title": "测试故事6: 实习体验", "content": "创业的道路虽然艰辛，但每一次挫折都是成长的机会。我想分享一些创业路上的心得体会。 这是第6个测试故事的详细内容，包含了丰富的职场经验和人生感悟。故事内容需要经过审核才能发布，当前状态为待审核。", "author": "匿名用户", "ipAddress": "*************", "likes": 32, "dislikes": 2, "tags": "[\"实习体验\",\"测试数据\",\"博士\"]", "category": "职场感悟", "educationLevel": "博士", "industry": "金融", "status": "pending", "submittedById": "anon_11768ae5-848b-47a7-91d8-cc41b3a5f9d0", "createdAt": "2025-05-22T10:49:45.228Z", "updatedAt": "2025-05-23T07:11:16.903Z", "isTestData": true, "testDataVersion": "1.0", "testDataSet": "role-testing"}, {"id": 17, "userId": 11, "isAnonymous": false, "title": "测试故事7: 创业故事", "content": "我的求职之路充满了挑战和机遇。从最初的迷茫到最终找到理想工作，这个过程让我成长了很多。 这是第7个测试故事的详细内容，包含了丰富的职场经验和人生感悟。故事内容需要经过审核才能发布，当前状态为待审核。", "author": "管理员3", "ipAddress": "************", "likes": 32, "dislikes": 8, "tags": "[\"创业故事\",\"测试数据\",\"硕士\"]", "category": "实习体验", "educationLevel": "高中/中专", "industry": "制造业", "status": "pending", "submittedById": null, "createdAt": "2025-05-08T07:46:15.172Z", "updatedAt": "2025-05-23T07:11:16.904Z", "isTestData": true, "testDataVersion": "1.0", "testDataSet": "role-testing"}, {"id": 18, "userId": null, "isAnonymous": true, "title": "测试故事8: 求职经历", "content": "我的求职之路充满了挑战和机遇。从最初的迷茫到最终找到理想工作，这个过程让我成长了很多。 这是第8个测试故事的详细内容，包含了丰富的职场经验和人生感悟。故事内容需要经过审核才能发布，当前状态为待审核。", "author": "匿名用户", "ipAddress": "*************", "likes": 64, "dislikes": 10, "tags": "[\"求职经历\",\"测试数据\",\"高中/中专\"]", "category": "求职经历", "educationLevel": "博士", "industry": "制造业", "status": "pending", "submittedById": "anon_a0d770fb-028e-44aa-bfde-270ec03b8e82", "createdAt": "2025-05-17T23:54:03.704Z", "updatedAt": "2025-05-23T07:11:16.904Z", "isTestData": true, "testDataVersion": "1.0", "testDataSet": "role-testing"}, {"id": 19, "userId": 18, "isAnonymous": false, "title": "测试故事9: 求职经历", "content": "创业的道路虽然艰辛，但每一次挫折都是成长的机会。我想分享一些创业路上的心得体会。 这是第9个测试故事的详细内容，包含了丰富的职场经验和人生感悟。故事内容需要经过审核才能发布，当前状态为待审核。", "author": "用户7", "ipAddress": "************", "likes": 60, "dislikes": 13, "tags": "[\"创业故事\",\"测试数据\",\"高中/中专\"]", "category": "转行经历", "educationLevel": "高中/中专", "industry": "IT/互联网", "status": "pending", "submittedById": null, "createdAt": "2025-05-19T13:48:46.685Z", "updatedAt": "2025-05-23T07:11:16.905Z", "isTestData": true, "testDataVersion": "1.0", "testDataSet": "role-testing"}, {"id": 20, "userId": 16, "isAnonymous": false, "title": "测试故事10: 实习体验", "content": "在职场中，我学会了如何与同事协作，如何处理工作压力，这些经验对我来说非常宝贵。 这是第10个测试故事的详细内容，包含了丰富的职场经验和人生感悟。故事内容需要经过审核才能发布，当前状态为待审核。", "author": "用户5", "ipAddress": "*************", "likes": 58, "dislikes": 15, "tags": "[\"创业故事\",\"测试数据\",\"硕士\"]", "category": "实习体验", "educationLevel": "博士", "industry": "金融", "status": "pending", "submittedById": null, "createdAt": "2025-05-12T06:45:06.665Z", "updatedAt": "2025-05-23T07:11:16.906Z", "isTestData": true, "testDataVersion": "1.0", "testDataSet": "role-testing"}, {"id": 21, "userId": 6, "isAnonymous": false, "title": "测试故事11: 职场感悟", "content": "创业的道路虽然艰辛，但每一次挫折都是成长的机会。我想分享一些创业路上的心得体会。 这是第11个测试故事的详细内容，包含了丰富的职场经验和人生感悟。故事内容需要经过审核才能发布，当前状态为待审核。", "author": "审核员3", "ipAddress": "*************", "likes": 82, "dislikes": 2, "tags": "[\"实习体验\",\"测试数据\",\"硕士\"]", "category": "求职经历", "educationLevel": "硕士", "industry": "IT/互联网", "status": "pending", "submittedById": null, "createdAt": "2025-05-15T11:43:17.290Z", "updatedAt": "2025-05-23T07:11:16.907Z", "isTestData": true, "testDataVersion": "1.0", "testDataSet": "role-testing"}, {"id": 22, "userId": null, "isAnonymous": true, "title": "测试故事12: 职场感悟", "content": "实习期间的经历让我对职场有了更深的理解，也帮助我明确了未来的职业方向。 这是第12个测试故事的详细内容，包含了丰富的职场经验和人生感悟。故事内容需要经过审核才能发布，当前状态为待审核。", "author": "匿名用户", "ipAddress": "************", "likes": 32, "dislikes": 15, "tags": "[\"创业故事\",\"测试数据\",\"本科\"]", "category": "转行经历", "educationLevel": "高中/中专", "industry": "医疗", "status": "pending", "submittedById": "anon_de9536de-65d8-4f9e-ad1e-8dcfbb7d1d25", "createdAt": "2025-05-13T07:17:54.911Z", "updatedAt": "2025-05-23T07:11:16.908Z", "isTestData": true, "testDataVersion": "1.0", "testDataSet": "role-testing"}, {"id": 23, "userId": 17, "isAnonymous": false, "title": "测试故事13: 转行经历", "content": "我的求职之路充满了挑战和机遇。从最初的迷茫到最终找到理想工作，这个过程让我成长了很多。 这是第13个测试故事的详细内容，包含了丰富的职场经验和人生感悟。故事内容需要经过审核才能发布，当前状态为待审核。", "author": "用户6", "ipAddress": "************", "likes": 2, "dislikes": 10, "tags": "[\"转行经历\",\"测试数据\",\"硕士\"]", "category": "实习体验", "educationLevel": "硕士", "industry": "教育", "status": "pending", "submittedById": null, "createdAt": "2025-05-19T09:41:38.373Z", "updatedAt": "2025-05-23T07:11:16.908Z", "isTestData": true, "testDataVersion": "1.0", "testDataSet": "role-testing"}, {"id": 24, "userId": 7, "isAnonymous": false, "title": "测试故事14: 转行经历", "content": "创业的道路虽然艰辛，但每一次挫折都是成长的机会。我想分享一些创业路上的心得体会。 这是第14个测试故事的详细内容，包含了丰富的职场经验和人生感悟。故事内容需要经过审核才能发布，当前状态为待审核。", "author": "审核员4", "ipAddress": "************", "likes": 54, "dislikes": 18, "tags": "[\"实习体验\",\"测试数据\",\"高中/中专\"]", "category": "求职经历", "educationLevel": "本科", "industry": "制造业", "status": "pending", "submittedById": null, "createdAt": "2025-05-14T08:20:54.727Z", "updatedAt": "2025-05-23T07:11:16.909Z", "isTestData": true, "testDataVersion": "1.0", "testDataSet": "role-testing"}, {"id": 25, "userId": null, "isAnonymous": true, "title": "测试故事15: 实习体验", "content": "创业的道路虽然艰辛，但每一次挫折都是成长的机会。我想分享一些创业路上的心得体会。 这是第15个测试故事的详细内容，包含了丰富的职场经验和人生感悟。故事内容需要经过审核才能发布，当前状态为待审核。", "author": "匿名用户", "ipAddress": "************", "likes": 38, "dislikes": 11, "tags": "[\"职场感悟\",\"测试数据\",\"博士\"]", "category": "职场感悟", "educationLevel": "本科", "industry": "IT/互联网", "status": "pending", "submittedById": "anon_5ea8c651-1399-4e4d-9a41-86c9c50d359c", "createdAt": "2025-05-23T03:15:18.712Z", "updatedAt": "2025-05-23T07:11:16.909Z", "isTestData": true, "testDataVersion": "1.0", "testDataSet": "role-testing"}, {"id": 26, "userId": null, "isAnonymous": true, "title": "测试故事16: 转行经历", "content": "实习期间的经历让我对职场有了更深的理解，也帮助我明确了未来的职业方向。 这是第16个测试故事的详细内容，包含了丰富的职场经验和人生感悟。故事内容需要经过审核才能发布，当前状态为待审核。", "author": "匿名用户", "ipAddress": "*************", "likes": 60, "dislikes": 19, "tags": "[\"求职经历\",\"测试数据\",\"高中/中专\"]", "category": "转行经历", "educationLevel": "本科", "industry": "文化/传媒", "status": "pending", "submittedById": "anon_e7c14617-2e02-442c-be7c-d39e6438e5d7", "createdAt": "2025-05-17T13:20:18.550Z", "updatedAt": "2025-05-23T07:11:16.910Z", "isTestData": true, "testDataVersion": "1.0", "testDataSet": "role-testing"}, {"id": 27, "userId": null, "isAnonymous": true, "title": "测试故事17: 创业故事", "content": "我的求职之路充满了挑战和机遇。从最初的迷茫到最终找到理想工作，这个过程让我成长了很多。 这是第17个测试故事的详细内容，包含了丰富的职场经验和人生感悟。故事内容需要经过审核才能发布，当前状态为待审核。", "author": "匿名用户", "ipAddress": "************", "likes": 23, "dislikes": 1, "tags": "[\"转行经历\",\"测试数据\",\"高中/中专\"]", "category": "创业故事", "educationLevel": "硕士", "industry": "医疗", "status": "pending", "submittedById": "anon_e14af959-8643-4338-b2ee-0fe832f3ca3d", "createdAt": "2025-05-22T09:27:23.874Z", "updatedAt": "2025-05-23T07:11:16.910Z", "isTestData": true, "testDataVersion": "1.0", "testDataSet": "role-testing"}, {"id": 28, "userId": null, "isAnonymous": true, "title": "测试故事18: 转行经历", "content": "实习期间的经历让我对职场有了更深的理解，也帮助我明确了未来的职业方向。 这是第18个测试故事的详细内容，包含了丰富的职场经验和人生感悟。故事内容需要经过审核才能发布，当前状态为待审核。", "author": "匿名用户", "ipAddress": "*************", "likes": 40, "dislikes": 5, "tags": "[\"求职经历\",\"测试数据\",\"大专\"]", "category": "职场感悟", "educationLevel": "本科", "industry": "文化/传媒", "status": "pending", "submittedById": "anon_312af1da-5dee-42b5-bb90-07fc3879c34a", "createdAt": "2025-05-17T01:58:27.061Z", "updatedAt": "2025-05-23T07:11:16.911Z", "isTestData": true, "testDataVersion": "1.0", "testDataSet": "role-testing"}, {"id": 29, "userId": null, "isAnonymous": true, "title": "测试故事19: 职场感悟", "content": "创业的道路虽然艰辛，但每一次挫折都是成长的机会。我想分享一些创业路上的心得体会。 这是第19个测试故事的详细内容，包含了丰富的职场经验和人生感悟。故事内容需要经过审核才能发布，当前状态为待审核。", "author": "匿名用户", "ipAddress": "************", "likes": 43, "dislikes": 14, "tags": "[\"求职经历\",\"测试数据\",\"本科\"]", "category": "求职经历", "educationLevel": "博士", "industry": "IT/互联网", "status": "pending", "submittedById": "anon_2aa77ecd-8a66-4598-8556-ce2425bb4dd8", "createdAt": "2025-05-20T01:12:24.255Z", "updatedAt": "2025-05-23T07:11:16.911Z", "isTestData": true, "testDataVersion": "1.0", "testDataSet": "role-testing"}, {"id": 30, "userId": 6, "isAnonymous": false, "title": "测试故事20: 创业故事", "content": "转行对我来说是一个重大决定。从原来的行业到现在的工作，我经历了很多困难但也收获了很多。 这是第20个测试故事的详细内容，包含了丰富的职场经验和人生感悟。故事内容需要经过审核才能发布，当前状态为待审核。", "author": "审核员3", "ipAddress": "*************", "likes": 2, "dislikes": 13, "tags": "[\"职场感悟\",\"测试数据\",\"本科\"]", "category": "求职经历", "educationLevel": "硕士", "industry": "金融", "status": "pending", "submittedById": null, "createdAt": "2025-05-18T08:48:23.029Z", "updatedAt": "2025-05-23T07:11:16.912Z", "isTestData": true, "testDataVersion": "1.0", "testDataSet": "role-testing"}, {"id": 31, "userId": null, "isAnonymous": true, "title": "测试故事21: 求职经历", "content": "在职场中，我学会了如何与同事协作，如何处理工作压力，这些经验对我来说非常宝贵。 这是第21个测试故事的详细内容，包含了丰富的职场经验和人生感悟。故事内容需要经过审核才能发布，当前状态为待审核。", "author": "匿名用户", "ipAddress": "************", "likes": 56, "dislikes": 2, "tags": "[\"实习体验\",\"测试数据\",\"高中/中专\"]", "category": "转行经历", "educationLevel": "高中/中专", "industry": "IT/互联网", "status": "pending", "submittedById": "anon_dc2f4dfe-258a-4aaf-96db-b09320fdb9bc", "createdAt": "2025-05-16T00:38:46.346Z", "updatedAt": "2025-05-23T07:11:16.912Z", "isTestData": true, "testDataVersion": "1.0", "testDataSet": "role-testing"}, {"id": 32, "userId": null, "isAnonymous": true, "title": "测试故事22: 实习体验", "content": "转行对我来说是一个重大决定。从原来的行业到现在的工作，我经历了很多困难但也收获了很多。 这是第22个测试故事的详细内容，包含了丰富的职场经验和人生感悟。故事内容需要经过审核才能发布，当前状态为待审核。", "author": "匿名用户", "ipAddress": "************", "likes": 11, "dislikes": 9, "tags": "[\"创业故事\",\"测试数据\",\"博士\"]", "category": "职场感悟", "educationLevel": "大专", "industry": "制造业", "status": "pending", "submittedById": "anon_c716b978-8a13-425e-a98e-75de0269fd03", "createdAt": "2025-05-12T14:55:19.872Z", "updatedAt": "2025-05-23T07:11:16.913Z", "isTestData": true, "testDataVersion": "1.0", "testDataSet": "role-testing"}, {"id": 33, "userId": null, "isAnonymous": true, "title": "测试故事23: 创业故事", "content": "转行对我来说是一个重大决定。从原来的行业到现在的工作，我经历了很多困难但也收获了很多。 这是第23个测试故事的详细内容，包含了丰富的职场经验和人生感悟。故事内容需要经过审核才能发布，当前状态为待审核。", "author": "匿名用户", "ipAddress": "***********", "likes": 6, "dislikes": 10, "tags": "[\"实习体验\",\"测试数据\",\"大专\"]", "category": "职场感悟", "educationLevel": "本科", "industry": "金融", "status": "pending", "submittedById": "anon_e7fd9eef-a374-4f31-94de-7e2d3847c2bf", "createdAt": "2025-05-12T04:40:48.307Z", "updatedAt": "2025-05-23T07:11:16.913Z", "isTestData": true, "testDataVersion": "1.0", "testDataSet": "role-testing"}, {"id": 34, "userId": 14, "isAnonymous": false, "title": "测试故事24: 创业故事", "content": "在职场中，我学会了如何与同事协作，如何处理工作压力，这些经验对我来说非常宝贵。 这是第24个测试故事的详细内容，包含了丰富的职场经验和人生感悟。故事内容需要经过审核才能发布，当前状态为待审核。", "author": "用户3", "ipAddress": "*************", "likes": 67, "dislikes": 19, "tags": "[\"创业故事\",\"测试数据\",\"博士\"]", "category": "职场感悟", "educationLevel": "高中/中专", "industry": "医疗", "status": "pending", "submittedById": null, "createdAt": "2025-05-19T17:03:42.877Z", "updatedAt": "2025-05-23T07:11:16.914Z", "isTestData": true, "testDataVersion": "1.0", "testDataSet": "role-testing"}, {"id": 35, "userId": 5, "isAnonymous": false, "title": "测试故事25: 转行经历", "content": "创业的道路虽然艰辛，但每一次挫折都是成长的机会。我想分享一些创业路上的心得体会。 这是第25个测试故事的详细内容，包含了丰富的职场经验和人生感悟。故事内容需要经过审核才能发布，当前状态为待审核。", "author": "审核员2", "ipAddress": "************", "likes": 58, "dislikes": 1, "tags": "[\"职场感悟\",\"测试数据\",\"硕士\"]", "category": "职场感悟", "educationLevel": "博士", "industry": "医疗", "status": "pending", "submittedById": null, "createdAt": "2025-05-09T14:50:22.767Z", "updatedAt": "2025-05-23T07:11:16.914Z", "isTestData": true, "testDataVersion": "1.0", "testDataSet": "role-testing"}, {"id": 36, "userId": null, "isAnonymous": true, "title": "测试故事26: 创业故事", "content": "我的求职之路充满了挑战和机遇。从最初的迷茫到最终找到理想工作，这个过程让我成长了很多。 这是第26个测试故事的详细内容，包含了丰富的职场经验和人生感悟。故事内容需要经过审核才能发布，当前状态为待审核。", "author": "匿名用户", "ipAddress": "*************", "likes": 78, "dislikes": 2, "tags": "[\"求职经历\",\"测试数据\",\"博士\"]", "category": "转行经历", "educationLevel": "硕士", "industry": "文化/传媒", "status": "pending", "submittedById": "anon_d9427719-10d3-47c3-8790-2f8af69fd740", "createdAt": "2025-05-08T12:26:50.104Z", "updatedAt": "2025-05-23T07:11:16.914Z", "isTestData": true, "testDataVersion": "1.0", "testDataSet": "role-testing"}, {"id": 37, "userId": null, "isAnonymous": true, "title": "测试故事27: 实习体验", "content": "在职场中，我学会了如何与同事协作，如何处理工作压力，这些经验对我来说非常宝贵。 这是第27个测试故事的详细内容，包含了丰富的职场经验和人生感悟。故事内容需要经过审核才能发布，当前状态为待审核。", "author": "匿名用户", "ipAddress": "*************", "likes": 29, "dislikes": 15, "tags": "[\"创业故事\",\"测试数据\",\"大专\"]", "category": "实习体验", "educationLevel": "博士", "industry": "制造业", "status": "pending", "submittedById": "anon_39c97bbc-7f27-4352-bb54-d4f7bd336f0f", "createdAt": "2025-05-10T10:53:40.878Z", "updatedAt": "2025-05-23T07:11:16.915Z", "isTestData": true, "testDataVersion": "1.0", "testDataSet": "role-testing"}, {"id": 38, "userId": 13, "isAnonymous": false, "title": "测试故事28: 职场感悟", "content": "在职场中，我学会了如何与同事协作，如何处理工作压力，这些经验对我来说非常宝贵。 这是第28个测试故事的详细内容，包含了丰富的职场经验和人生感悟。故事内容需要经过审核才能发布，当前状态为待审核。", "author": "用户2", "ipAddress": "************", "likes": 78, "dislikes": 11, "tags": "[\"创业故事\",\"测试数据\",\"大专\"]", "category": "实习体验", "educationLevel": "高中/中专", "industry": "IT/互联网", "status": "pending", "submittedById": null, "createdAt": "2025-05-21T10:41:06.646Z", "updatedAt": "2025-05-23T07:11:16.915Z", "isTestData": true, "testDataVersion": "1.0", "testDataSet": "role-testing"}, {"id": 39, "userId": 12, "isAnonymous": false, "title": "测试故事29: 实习体验", "content": "我的求职之路充满了挑战和机遇。从最初的迷茫到最终找到理想工作，这个过程让我成长了很多。 这是第29个测试故事的详细内容，包含了丰富的职场经验和人生感悟。故事内容需要经过审核才能发布，当前状态为待审核。", "author": "用户1", "ipAddress": "*************", "likes": 53, "dislikes": 1, "tags": "[\"实习体验\",\"测试数据\",\"高中/中专\"]", "category": "实习体验", "educationLevel": "高中/中专", "industry": "金融", "status": "pending", "submittedById": null, "createdAt": "2025-05-22T03:16:06.978Z", "updatedAt": "2025-05-23T07:11:16.916Z", "isTestData": true, "testDataVersion": "1.0", "testDataSet": "role-testing"}, {"id": 40, "userId": null, "isAnonymous": true, "title": "测试故事30: 求职经历", "content": "创业的道路虽然艰辛，但每一次挫折都是成长的机会。我想分享一些创业路上的心得体会。 这是第30个测试故事的详细内容，包含了丰富的职场经验和人生感悟。故事内容需要经过审核才能发布，当前状态为待审核。", "author": "匿名用户", "ipAddress": "*************", "likes": 14, "dislikes": 12, "tags": "[\"创业故事\",\"测试数据\",\"博士\"]", "category": "实习体验", "educationLevel": "博士", "industry": "IT/互联网", "status": "pending", "submittedById": "anon_12a08476-45a7-41c6-86ff-01f78341641a", "createdAt": "2025-05-21T23:46:02.087Z", "updatedAt": "2025-05-23T07:11:16.916Z", "isTestData": true, "testDataVersion": "1.0", "testDataSet": "role-testing"}], "votes": []}}