/**
 * 测试数据服务
 *
 * 提供测试数据管理相关的 API 调用
 */

import { apiClient } from './apiClient';

// 测试数据统计类型
export interface TestDataStats {
  users: {
    total: number;
    normalUsers: number;
    usersWithUuid: number;
    reviewers: number;
    admins: number;
    superAdmins: number;
  };
  content: {
    questionnaires: number;
    stories: number;
    pendingReview: number;
    approved: number;
    rejected: number;
  };
  lastGenerated: string;
  isLoaded: boolean;
}

// 导入测试数据参数
export interface ImportTestDataParams {
  userCount?: number;
  reviewerCount?: number;
  adminCount?: number;
  questionnaireCount?: number;
  storyCount?: number;
  uuidPercentage?: number;
}

// 导入测试数据进度
export interface ImportProgress {
  step: string;
  progress: number;
  details: string;
}

// 默认测试数据统计
const defaultTestDataStats: TestDataStats = {
  users: {
    total: 64,
    normalUsers: 50,
    usersWithUuid: 30,
    reviewers: 10,
    admins: 3,
    superAdmins: 1
  },
  content: {
    questionnaires: 50,
    stories: 50,
    pendingReview: 35,
    approved: 45,
    rejected: 20
  },
  lastGenerated: new Date().toISOString(),
  isLoaded: false
};

/**
 * 获取测试数据统计
 * @returns 测试数据统计
 */
export async function getTestDataStats() {
  try {
    // 检查是否为开发环境
    if (import.meta.env.DEV && import.meta.env.VITE_USE_MOCK === 'true') {
      console.log('使用模拟数据获取测试数据统计');

      // 模拟网络延迟
      await new Promise(resolve => setTimeout(resolve, 1000));

      return {
        success: true,
        data: {
          ...defaultTestDataStats,
          isLoaded: Math.random() > 0.5 // 随机模拟是否已加载测试数据
        }
      };
    }

    // 生产环境使用真实API
    const response = await apiClient.get('/admin/test-data/stats');
    return response.data;
  } catch (error) {
    console.error('获取测试数据统计失败:', error);
    throw error;
  }
}

/**
 * 导入测试数据
 * @param params 导入参数
 * @param progressCallback 进度回调函数
 * @returns 导入结果
 */
export async function importTestData(
  params: ImportTestDataParams = {},
  progressCallback?: (progress: ImportProgress) => void
) {
  try {
    // 检查是否为开发环境
    if (import.meta.env.DEV && import.meta.env.VITE_USE_MOCK === 'true') {
      console.log('使用模拟数据导入测试数据');

      // 模拟导入过程
      const steps = [
        { step: '准备导入', progress: 10, details: '正在准备导入测试数据...' },
        { step: '创建用户', progress: 30, details: '正在创建测试用户...' },
        { step: '创建问卷', progress: 50, details: '正在创建测试问卷...' },
        { step: '创建故事', progress: 70, details: '正在创建测试故事...' },
        { step: '完成导入', progress: 100, details: '测试数据导入完成' }
      ];

      for (const step of steps) {
        // 模拟网络延迟
        await new Promise(resolve => setTimeout(resolve, 800));
        
        if (progressCallback) {
          progressCallback(step);
        }
      }

      return {
        success: true,
        message: '测试数据导入成功',
        data: {
          ...defaultTestDataStats,
          isLoaded: true,
          lastGenerated: new Date().toISOString()
        }
      };
    }

    // 生产环境使用真实API
    const response = await apiClient.post('/admin/test-data/import', params);
    return response.data;
  } catch (error) {
    console.error('导入测试数据失败:', error);
    throw error;
  }
}

/**
 * 清除测试数据
 * @param progressCallback 进度回调函数
 * @returns 清除结果
 */
export async function clearTestData(
  progressCallback?: (progress: ImportProgress) => void
) {
  try {
    // 检查是否为开发环境
    if (import.meta.env.DEV && import.meta.env.VITE_USE_MOCK === 'true') {
      console.log('使用模拟数据清除测试数据');

      // 模拟清除过程
      const steps = [
        { step: '准备清除', progress: 10, details: '正在准备清除测试数据...' },
        { step: '清除故事', progress: 30, details: '正在清除测试故事...' },
        { step: '清除问卷', progress: 50, details: '正在清除测试问卷...' },
        { step: '清除用户', progress: 70, details: '正在清除测试用户...' },
        { step: '完成清除', progress: 100, details: '测试数据清除完成' }
      ];

      for (const step of steps) {
        // 模拟网络延迟
        await new Promise(resolve => setTimeout(resolve, 800));
        
        if (progressCallback) {
          progressCallback(step);
        }
      }

      return {
        success: true,
        message: '测试数据清除成功',
        data: {
          ...defaultTestDataStats,
          users: {
            total: 0,
            normalUsers: 0,
            usersWithUuid: 0,
            reviewers: 0,
            admins: 0,
            superAdmins: 0
          },
          content: {
            questionnaires: 0,
            stories: 0,
            pendingReview: 0,
            approved: 0,
            rejected: 0
          },
          isLoaded: false,
          lastGenerated: ''
        }
      };
    }

    // 生产环境使用真实API
    const response = await apiClient.delete('/admin/test-data');
    return response.data;
  } catch (error) {
    console.error('清除测试数据失败:', error);
    throw error;
  }
}

/**
 * 获取测试数据状态
 * @returns 测试数据状态
 */
export async function getTestDataStatus() {
  try {
    // 检查是否为开发环境
    if (import.meta.env.DEV && import.meta.env.VITE_USE_MOCK === 'true') {
      console.log('使用模拟数据获取测试数据状态');

      // 模拟网络延迟
      await new Promise(resolve => setTimeout(resolve, 500));

      // 随机模拟测试数据状态
      const status = ['loaded', 'not_loaded', 'unknown'][Math.floor(Math.random() * 3)];

      return {
        success: true,
        data: {
          status
        }
      };
    }

    // 生产环境使用真实API
    const response = await apiClient.get('/admin/test-data/status');
    return response.data;
  } catch (error) {
    console.error('获取测试数据状态失败:', error);
    throw error;
  }
}

/**
 * 获取测试数据详情
 * @returns 测试数据详情
 */
export async function getTestDataDetails() {
  try {
    // 检查是否为开发环境
    if (import.meta.env.DEV && import.meta.env.VITE_USE_MOCK === 'true') {
      console.log('使用模拟数据获取测试数据详情');

      // 模拟网络延迟
      await new Promise(resolve => setTimeout(resolve, 1000));

      return {
        success: true,
        data: {
          users: [
            { id: 1, email: '<EMAIL>', name: '测试用户1', role: 'user', hasUuid: true },
            { id: 2, email: '<EMAIL>', name: '测试用户2', role: 'user', hasUuid: true },
            // ... 更多用户
          ],
          reviewers: [
            { id: 51, email: '<EMAIL>', name: '测试审核员1', role: 'reviewer' },
            { id: 52, email: '<EMAIL>', name: '测试审核员2', role: 'reviewer' },
            // ... 更多审核员
          ],
          admins: [
            { id: 61, email: '<EMAIL>', name: '测试管理员1', role: 'admin' },
            { id: 62, email: '<EMAIL>', name: '测试管理员2', role: 'admin' },
            // ... 更多管理员
          ],
          questionnaires: [
            { id: 1, sequenceNumber: 'QUEST001', status: 'pending' },
            { id: 2, sequenceNumber: 'QUEST002', status: 'pending' },
            // ... 更多问卷
          ],
          stories: [
            { id: 1, sequenceNumber: 'STORY001', status: 'pending' },
            { id: 2, sequenceNumber: 'STORY002', status: 'pending' },
            // ... 更多故事
          ]
        }
      };
    }

    // 生产环境使用真实API
    const response = await apiClient.get('/admin/test-data/details');
    return response.data;
  } catch (error) {
    console.error('获取测试数据详情失败:', error);
    throw error;
  }
}
