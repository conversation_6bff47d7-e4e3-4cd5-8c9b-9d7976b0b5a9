#!/bin/bash
# auto-fix-cors.sh - 自动修复常见CORS问题

echo "🔧 CORS自动修复工具"

BACKEND_FILE="backend/index.js"
BACKUP_FILE="backend/index.js.backup.$(date +%Y%m%d_%H%M%S)"

# 检查后端文件是否存在
if [ ! -f "$BACKEND_FILE" ]; then
    echo "❌ 错误: 找不到后端文件 $BACKEND_FILE"
    exit 1
fi

echo "📁 后端文件: $BACKEND_FILE"

# 创建备份
echo "💾 创建备份: $BACKUP_FILE"
cp "$BACKEND_FILE" "$BACKUP_FILE"

# 检查是否存在CORS配置问题
echo ""
echo "🔍 检查CORS配置..."

CORS_ISSUES=0

# 1. 检查 return true 问题
if grep -q "return true" "$BACKEND_FILE"; then
    echo "❌ 发现问题: 使用了 'return true'"
    CORS_ISSUES=$((CORS_ISSUES + 1))
    
    echo "🔧 修复: 将 'return true' 替换为 'return origin'"
    
    # 使用sed进行替换
    if [[ "$OSTYPE" == "darwin"* ]]; then
        # macOS
        sed -i '' 's/return true;/return origin;/g' "$BACKEND_FILE"
    else
        # Linux
        sed -i 's/return true;/return origin;/g' "$BACKEND_FILE"
    fi
    
    echo "✅ 已修复 'return true' 问题"
fi

# 2. 检查是否缺少origin处理
if ! grep -q "if (!origin)" "$BACKEND_FILE"; then
    echo "⚠️  建议: 添加无origin请求的处理"
    echo "   在CORS配置中添加: if (!origin) return origin;"
fi

# 3. 检查是否有子域名匹配
if ! grep -q "\.match(" "$BACKEND_FILE"; then
    echo "⚠️  建议: 考虑添加子域名匹配规则"
    echo "   例如: origin.match(/^https:\/\/[a-f0-9]{8}\.your-domain\.pages\.dev$/)"
fi

echo ""
echo "📊 修复总结:"

if [ $CORS_ISSUES -eq 0 ]; then
    echo "✅ 未发现需要自动修复的CORS问题"
    echo "🗑️  删除备份文件"
    rm "$BACKUP_FILE"
else
    echo "✅ 已修复 $CORS_ISSUES 个CORS问题"
    echo "💾 备份文件保存在: $BACKUP_FILE"
    
    echo ""
    echo "🔍 修复后的配置预览:"
    echo "----------------------------------------"
    grep -A 20 -B 5 "cors({" "$BACKEND_FILE" | head -30
    echo "----------------------------------------"
    
    echo ""
    echo "🚀 建议下一步:"
    echo "1. 检查修复后的配置是否正确"
    echo "2. 运行测试: npm test (如果有)"
    echo "3. 部署后端: cd backend && npx wrangler deploy"
    echo "4. 运行CORS诊断: ./scripts/cors-diagnose.sh API_URL FRONTEND_URL"
fi

echo ""
echo "🛠️  其他可用工具:"
echo "   ./scripts/pre-deploy-check.sh - 部署前检查"
echo "   ./scripts/cors-diagnose.sh - CORS诊断"
echo "   浏览器控制台运行 scripts/browser-cors-test.js"
