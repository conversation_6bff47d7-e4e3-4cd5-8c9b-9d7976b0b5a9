import React, { useEffect, useState } from 'react';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import {
  Shield,
  Lock,
  AlertTriangle,
  User,
  Key,
  RefreshCw,
  LogOut
} from 'lucide-react';
import { permissionManager, permissionUtils, type UserRole } from '@/utils/permissions';
import { auditLogger } from '@/utils/auditLog';

export interface PermissionGuardProps {
  children: React.ReactNode;
  requiredPermissions?: string[];
  requiredRole?: UserRole;
  allowedRoles?: UserRole[];
  requireAll?: boolean; // 是否需要所有权限
  fallback?: React.ReactNode;
  onUnauthorized?: () => void;
  showDetails?: boolean; // 是否显示详细的权限信息
}

/**
 * 权限保护组件
 *
 * 特性：
 * - 细粒度权限控制
 * - 角色验证
 * - 权限缓存
 * - 审计日志记录
 * - 友好的错误提示
 */
const PermissionGuard: React.FC<PermissionGuardProps> = ({
  children,
  requiredPermissions = [],
  requiredRole,
  allowedRoles = [],
  requireAll = true,
  fallback,
  onUnauthorized,
  showDetails = false
}) => {
  const [hasAccess, setHasAccess] = useState<boolean | null>(null);
  const [userInfo, setUserInfo] = useState<{
    userId: string;
    role: UserRole;
    permissions: string[];
  } | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [accessDeniedReason, setAccessDeniedReason] = useState<string>('');

  // 检查权限 - try catch错误处理
  const checkPermissions = async () => {
    setIsLoading(true);

    try {
      const userId = localStorage.getItem('userId');
      const userRole = localStorage.getItem('userRole') as UserRole;

      if (!userId || !userRole) {
        setHasAccess(false);
        setAccessDeniedReason('用户未登录');
        await logAccessAttempt(false, '用户未登录');
        return;
      }

      // 获取用户权限
      const userPermissions = permissionManager.getUserAllPermissions(userId);
      setUserInfo({
        userId,
        role: userRole,
        permissions: userPermissions
      });

      // 检查角色权限
      let roleCheck = true;
      let roleReason = '';

      if (requiredRole) {
        roleCheck = userRole === requiredRole;
        if (!roleCheck) {
          roleReason = `需要角色: ${requiredRole}，当前角色: ${userRole}`;
        }
      }

      if (allowedRoles.length > 0) {
        roleCheck = allowedRoles.includes(userRole);
        if (!roleCheck) {
          roleReason = `需要角色: ${allowedRoles.join(', ')}，当前角色: ${userRole}`;
        }
      }

      // 检查具体权限
      let permissionCheck = true;
      let permissionReason = '';

      if (requiredPermissions.length > 0) {
        if (requireAll) {
          permissionCheck = permissionManager.hasAllPermissions(userId, requiredPermissions);
          if (!permissionCheck) {
            const missingPermissions = requiredPermissions.filter(
              perm => !permissionManager.hasPermission(userId, perm)
            );
            permissionReason = `缺少权限: ${missingPermissions.join(', ')}`;
          }
        } else {
          permissionCheck = permissionManager.hasAnyPermission(userId, requiredPermissions);
          if (!permissionCheck) {
            permissionReason = `需要以下任一权限: ${requiredPermissions.join(', ')}`;
          }
        }
      }

      const hasFullAccess = roleCheck && permissionCheck;
      setHasAccess(hasFullAccess);

      if (!hasFullAccess) {
        const reason = [roleReason, permissionReason].filter(Boolean).join('; ');
        setAccessDeniedReason(reason);
        await logAccessAttempt(false, reason);
        onUnauthorized?.();
      } else {
        await logAccessAttempt(true, '权限验证通过');
      }
    } catch (error) {
      console.error('权限检查失败:', error);
      setHasAccess(false);
      setAccessDeniedReason('权限检查失败');
      await logAccessAttempt(false, '权限检查失败');
    } finally {
      setIsLoading(false);
    }
  };

  // 记录访问尝试
  const logAccessAttempt = async (success: boolean, reason: string) => {
    const userId = localStorage.getItem('userId') || 'unknown';
    const userRole = localStorage.getItem('userRole') || 'unknown';

    await auditLogger.log({
      userId,
      userRole,
      action: 'read',
      resource: 'permission_check',
      description: `权限验证${success ? '成功' : '失败'}: ${reason}`,
      level: success ? 'info' : 'warn',
      success,
      details: {
        requiredPermissions,
        requiredRole,
        allowedRoles,
        requireAll,
        reason
      },
      tags: ['permission', 'access_control']
    });
  };

  // 刷新权限
  const refreshPermissions = () => {
    checkPermissions();
  };

  // 登出
  const handleLogout = () => {
    localStorage.removeItem('userId');
    localStorage.removeItem('userRole');
    localStorage.removeItem('adminToken');
    localStorage.removeItem('superadminToken');
    window.location.href = '/login';
  };

  // 初始权限检查
  useEffect(() => {
    checkPermissions();
  }, [requiredPermissions, requiredRole, allowedRoles, requireAll]);

  // 加载状态
  if (isLoading) {
    return (
      <div className="flex items-center justify-center p-8">
        <div className="flex items-center gap-2">
          <RefreshCw className="h-5 w-5 animate-spin" />
          <span>验证权限中...</span>
        </div>
      </div>
    );
  }

  // 权限验证失败
  if (hasAccess === false) {
    if (fallback) {
      return <>{fallback}</>;
    }

    return (
      <div className="container mx-auto py-8 px-4">
        <Card className="max-w-2xl mx-auto">
          <CardHeader>
            <CardTitle className="flex items-center gap-2 text-red-600">
              <Shield className="h-6 w-6" />
              访问被拒绝
            </CardTitle>
            <CardDescription>
              您没有足够的权限访问此资源
            </CardDescription>
          </CardHeader>

          <CardContent className="space-y-6">
            {/* 错误信息 */}
            <Alert variant="destructive">
              <AlertTriangle className="h-4 w-4" />
              <AlertTitle>权限不足</AlertTitle>
              <AlertDescription>{accessDeniedReason}</AlertDescription>
            </Alert>

            {/* 用户信息 */}
            {userInfo && (
              <div className="space-y-4">
                <h3 className="font-medium flex items-center gap-2">
                  <User className="h-4 w-4" />
                  当前用户信息
                </h3>
                <div className="bg-gray-50 p-4 rounded-md space-y-2">
                  <div className="flex items-center justify-between">
                    <span className="text-sm text-gray-600">用户ID:</span>
                    <span className="font-mono text-sm">{userInfo.userId}</span>
                  </div>
                  <div className="flex items-center justify-between">
                    <span className="text-sm text-gray-600">角色:</span>
                    <Badge variant="outline">{userInfo.role}</Badge>
                  </div>
                  {showDetails && (
                    <div className="space-y-2">
                      <span className="text-sm text-gray-600">当前权限:</span>
                      <div className="flex flex-wrap gap-1">
                        {userInfo.permissions.length > 0 ? (
                          userInfo.permissions.map(permission => (
                            <Badge key={permission} variant="secondary" className="text-xs">
                              {permission}
                            </Badge>
                          ))
                        ) : (
                          <span className="text-xs text-gray-400">无权限</span>
                        )}
                      </div>
                    </div>
                  )}
                </div>
              </div>
            )}

            {/* 所需权限 */}
            {showDetails && (requiredPermissions.length > 0 || requiredRole || allowedRoles.length > 0) && (
              <div className="space-y-4">
                <h3 className="font-medium flex items-center gap-2">
                  <Key className="h-4 w-4" />
                  所需权限
                </h3>
                <div className="bg-blue-50 p-4 rounded-md space-y-3">
                  {(requiredRole || allowedRoles.length > 0) && (
                    <div>
                      <span className="text-sm text-gray-600 block mb-1">所需角色:</span>
                      <div className="flex flex-wrap gap-1">
                        {requiredRole && (
                          <Badge variant="outline" className="text-blue-600">
                            {requiredRole}
                          </Badge>
                        )}
                        {allowedRoles.map(role => (
                          <Badge key={role} variant="outline" className="text-blue-600">
                            {role}
                          </Badge>
                        ))}
                      </div>
                    </div>
                  )}

                  {requiredPermissions.length > 0 && (
                    <div>
                      <span className="text-sm text-gray-600 block mb-1">
                        所需权限 ({requireAll ? '需要全部' : '需要任一'}):
                      </span>
                      <div className="flex flex-wrap gap-1">
                        {requiredPermissions.map(permission => (
                          <Badge key={permission} variant="outline" className="text-blue-600">
                            {permission}
                          </Badge>
                        ))}
                      </div>
                    </div>
                  )}
                </div>
              </div>
            )}

            {/* 操作按钮 */}
            <div className="flex gap-3">
              <Button onClick={refreshPermissions} variant="outline">
                <RefreshCw className="h-4 w-4 mr-2" />
                刷新权限
              </Button>
              <Button onClick={handleLogout} variant="outline">
                <LogOut className="h-4 w-4 mr-2" />
                重新登录
              </Button>
              <Button onClick={() => window.history.back()} variant="outline">
                返回上页
              </Button>
            </div>

            {/* 帮助信息 */}
            <Alert>
              <Lock className="h-4 w-4" />
              <AlertTitle>需要帮助？</AlertTitle>
              <AlertDescription>
                如果您认为这是一个错误，请联系系统管理员获取相应权限。
                请提供您的用户ID和所需访问的功能信息。
              </AlertDescription>
            </Alert>
          </CardContent>
        </Card>
      </div>
    );
  }

  // 权限验证通过，渲染子组件
  return <>{children}</>;
};

// 权限检查Hook
export const usePermissions = () => {
  const [userPermissions, setUserPermissions] = useState<string[]>([]);
  const [userRole, setUserRole] = useState<UserRole | null>(null);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    const loadPermissions = () => {
      const userId = localStorage.getItem('userId');
      const role = localStorage.getItem('userRole') as UserRole;

      if (userId && role) {
        const permissions = permissionManager.getUserAllPermissions(userId);
        setUserPermissions(permissions);
        setUserRole(role);
      }

      setIsLoading(false);
    };

    loadPermissions();
  }, []);

  const hasPermission = (permission: string): boolean => {
    return userPermissions.includes(permission);
  };

  const hasAnyPermission = (permissions: string[]): boolean => {
    return permissions.some(permission => userPermissions.includes(permission));
  };

  const hasAllPermissions = (permissions: string[]): boolean => {
    return permissions.every(permission => userPermissions.includes(permission));
  };

  const hasRole = (role: UserRole): boolean => {
    return userRole === role;
  };

  const hasAnyRole = (roles: UserRole[]): boolean => {
    return userRole ? roles.includes(userRole) : false;
  };

  return {
    userPermissions,
    userRole,
    isLoading,
    hasPermission,
    hasAnyPermission,
    hasAllPermissions,
    hasRole,
    hasAnyRole
  };
};

export default PermissionGuard;
