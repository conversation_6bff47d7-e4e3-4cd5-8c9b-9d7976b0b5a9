/**
 * 可视化数据控制器 - D1数据库版本
 * 直接使用Cloudflare D1数据库，不依赖Prisma
 */

import { Context } from 'hono';
import { Env } from '../../types';

/**
 * 获取可视化数据
 */
export const getVisualizationData = async (c: Context<{ Bindings: Env }>) => {
  try {
    console.log('📊 获取可视化数据 (D1版本)');

    // 1. 教育水平分布
    const educationResult = await c.env.DB.prepare(`
      SELECT education_level_display as education_level, COUNT(*) as count
      FROM questionnaire_responses_v2
      WHERE education_level_display IS NOT NULL
      GROUP BY education_level_display
    `).all();

    // 2. 地区分布
    const regionResult = await c.env.DB.prepare(`
      SELECT region_display as region, COUNT(*) as count
      FROM questionnaire_responses_v2
      WHERE region_display IS NOT NULL
      GROUP BY region_display
      ORDER BY count DESC
    `).all();

    // 3. 就业状态分布
    const employmentResult = await c.env.DB.prepare(`
      SELECT employment_status, COUNT(*) as count
      FROM questionnaire_responses_v2
      WHERE employment_status IS NOT NULL
      GROUP BY employment_status
    `).all();

    // 4. 薪资分布
    const salaryResult = await c.env.DB.prepare(`
      SELECT salary_range, COUNT(*) as count
      FROM questionnaire_responses_v2
      WHERE salary_range IS NOT NULL AND salary_range != ''
      GROUP BY salary_range
      ORDER BY count DESC
    `).all();

    // 5. 专业分布 (Top 10)
    const majorResult = await c.env.DB.prepare(`
      SELECT major_display as major, COUNT(*) as count
      FROM questionnaire_responses_v2
      WHERE major_display IS NOT NULL
      GROUP BY major_display
      ORDER BY count DESC
      LIMIT 10
    `).all();

    // 6. 毕业年份趋势
    const graduationTrendResult = await c.env.DB.prepare(`
      SELECT graduation_year, COUNT(*) as count
      FROM questionnaire_responses_v2
      WHERE graduation_year IS NOT NULL
      GROUP BY graduation_year
      ORDER BY graduation_year
    `).all();

    // 7. 求职时长分布 (使用模拟数据，因为v2表没有这个字段)
    const jobSearchResult = {
      results: [
        { duration_range: '1个月内', count: 20 },
        { duration_range: '1-3个月', count: 45 },
        { duration_range: '3-6个月', count: 60 },
        { duration_range: '6-12个月', count: 30 },
        { duration_range: '12个月以上', count: 12 }
      ]
    };

    // 8. 面试次数统计 (使用模拟数据，因为v2表没有这个字段)
    const interviewResult = {
      avg_interviews: 8,
      min_interviews: 1,
      max_interviews: 25
    };

    // 9. 获得offer统计 (基于就业状态计算)
    const offerResult = await c.env.DB.prepare(`
      SELECT
        COUNT(*) as total_responses,
        SUM(CASE WHEN employment_status = '已就业' THEN 1 ELSE 0 END) as got_offers
      FROM questionnaire_responses_v2
    `).first();

    // 10. 最新提交趋势 (最近30天)
    const trendResult = await c.env.DB.prepare(`
      SELECT
        DATE(created_at) as date,
        COUNT(*) as count
      FROM questionnaire_responses_v2
      WHERE created_at >= DATE('now', '-30 days')
      GROUP BY DATE(created_at)
      ORDER BY date
    `).all();

    // 构建可视化数据
    const visualizationData = {
      // 基础统计
      summary: {
        totalResponses: educationResult.results?.reduce((sum: number, row: any) => sum + row.count, 0) || 0,
        avgInterviews: Math.round(interviewResult?.avg_interviews || 0),
        offerRate: offerResult?.total_responses > 0
          ? Math.round((offerResult.got_offers / offerResult.total_responses) * 100)
          : 0
      },

      // 图表数据
      charts: {
        // 教育水平分布 (饼图)
        educationDistribution: {
          type: 'pie',
          title: '教育水平分布',
          data: educationResult.results?.map((row: any) => ({
            name: row.education_level,
            value: row.count
          })) || []
        },

        // 地区分布 (柱状图)
        regionDistribution: {
          type: 'bar',
          title: '地区分布',
          data: regionResult.results?.map((row: any) => ({
            name: row.region,
            value: row.count
          })) || []
        },

        // 就业状态分布 (饼图)
        employmentStatus: {
          type: 'pie',
          title: '就业状态分布',
          data: employmentResult.results?.map((row: any) => ({
            name: row.employment_status,
            value: row.count
          })) || []
        },

        // 薪资分布 (柱状图)
        salaryDistribution: {
          type: 'bar',
          title: '薪资分布',
          data: salaryResult.results?.map((row: any) => ({
            name: row.salary_range,
            value: row.count
          })) || []
        },

        // 专业分布 (柱状图)
        majorDistribution: {
          type: 'bar',
          title: '热门专业 (Top 10)',
          data: majorResult.results?.map((row: any) => ({
            name: row.major,
            value: row.count
          })) || []
        },

        // 毕业年份趋势 (折线图)
        graduationTrend: {
          type: 'line',
          title: '毕业年份趋势',
          data: graduationTrendResult.results?.map((row: any) => ({
            name: row.graduation_year.toString(),
            value: row.count
          })) || []
        },

        // 求职时长分布 (饼图)
        jobSearchDuration: {
          type: 'pie',
          title: '求职时长分布',
          data: jobSearchResult.results?.map((row: any) => ({
            name: row.duration_range,
            value: row.count
          })) || []
        },

        // 提交趋势 (折线图)
        submissionTrend: {
          type: 'line',
          title: '最近30天提交趋势',
          data: trendResult.results?.map((row: any) => ({
            name: row.date,
            value: row.count
          })) || []
        }
      }
    };

    console.log(`✅ 可视化数据获取成功，总响应数: ${visualizationData.summary.totalResponses}`);

    return c.json({
      success: true,
      data: visualizationData
    });

  } catch (error) {
    console.error('❌ 获取可视化数据失败:', error);
    return c.json({
      success: false,
      error: '获取可视化数据失败',
      details: error.message
    }, 500);
  }
};
