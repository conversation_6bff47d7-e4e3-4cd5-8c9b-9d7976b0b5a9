#!/usr/bin/env node

/**
 * 简单的API测试服务器
 * 用于验证前端API调用
 */

const http = require('http');
const url = require('url');

const PORT = 8787;

// 统一的问卷数据配置
const QUESTIONNAIRE_CONFIG = {
  totalResponses: 50,  // 统一使用50作为总数
  verifiedCount: 35,
  anonymousCount: 30,
  employedCount: 40,
  unemployedCount: 10
};

// 统一的教育水平数据
const EDUCATION_LEVELS = [
  { name: '本科', count: 25 },
  { name: '硕士', count: 15 },
  { name: '大专', count: 8 },
  { name: '博士', count: 2 }
];

// 统一的地区数据
const REGIONS = [
  { name: '北上广深', count: 20 },
  { name: '省会城市', count: 15 },
  { name: '二线城市', count: 10 },
  { name: '其他', count: 5 }
];

// 统一的行业数据
const INDUSTRIES = [
  { name: 'IT/互联网', count: 18 },
  { name: '金融', count: 12 },
  { name: '教育', count: 8 },
  { name: '制造业', count: 7 },
  { name: '其他', count: 5 }
];

// 模拟故事数据
const mockStories = [
  {
    id: 1,
    sequenceNumber: "S00001",
    title: "我的求职经历分享",
    content: "作为一名计算机专业的毕业生，我想分享一下我的求职经历...",
    author: { id: 1, name: "匿名用户" },
    isAnonymous: true,
    createdAt: "2024-05-20T10:00:00Z",
    tags: ["求职经验", "计算机专业", "毕业生"],
    likes: 15,
    comments: 3
  },
  {
    id: 2,
    sequenceNumber: "S00002",
    title: "转行到IT行业的心得",
    content: "从传统行业转到IT行业，这个过程充满了挑战...",
    author: { id: 2, name: "用户2" },
    isAnonymous: false,
    createdAt: "2024-05-21T14:30:00Z",
    tags: ["转行经验", "IT行业", "职业规划"],
    likes: 23,
    comments: 7
  },
  {
    id: 3,
    sequenceNumber: "S00003",
    title: "实习期间的成长感悟",
    content: "在实习的这几个月里，我学到了很多课堂上学不到的东西...",
    author: { id: 3, name: "匿名用户" },
    isAnonymous: true,
    createdAt: "2024-05-22T09:15:00Z",
    tags: ["实习经验", "职场成长", "学习心得"],
    likes: 18,
    comments: 5
  }
];

// 可视化数据（使用统一配置）
const mockVisualizationData = {
  success: true,
  stats: {
    totalCount: QUESTIONNAIRE_CONFIG.totalResponses,
    verifiedCount: QUESTIONNAIRE_CONFIG.verifiedCount,
    anonymousCount: QUESTIONNAIRE_CONFIG.anonymousCount,
    employedCount: QUESTIONNAIRE_CONFIG.employedCount,
    unemployedCount: QUESTIONNAIRE_CONFIG.unemployedCount,
    averageUnemploymentDuration: "2.5个月",
    mostCommonEducation: "本科",
    mostCommonIndustry: "IT/互联网",
    educationLevels: EDUCATION_LEVELS,
    regions: REGIONS,
    industries: INDUSTRIES
  }
};

// 处理CORS
function setCORSHeaders(res) {
  res.setHeader('Access-Control-Allow-Origin', '*');
  res.setHeader('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS');
  res.setHeader('Access-Control-Allow-Headers', 'Content-Type, Authorization');
}

// 发送JSON响应
function sendJSON(res, data, statusCode = 200) {
  setCORSHeaders(res);
  res.writeHead(statusCode, { 'Content-Type': 'application/json' });
  res.end(JSON.stringify(data));
}

// 路由处理
function handleRequest(req, res) {
  const parsedUrl = url.parse(req.url, true);
  const path = parsedUrl.pathname;
  const method = req.method;

  console.log(`${method} ${path}`);

  // 处理OPTIONS请求
  if (method === 'OPTIONS') {
    setCORSHeaders(res);
    res.writeHead(200);
    res.end();
    return;
  }

  // 健康检查
  if (path === '/health') {
    sendJSON(res, { status: 'ok', timestamp: new Date().toISOString() });
    return;
  }

  // 故事列表API
  if (path === '/api/story/list' && method === 'GET') {
    const page = parseInt(parsedUrl.query.page) || 1;
    const pageSize = parseInt(parsedUrl.query.pageSize) || 6;
    const sortBy = parsedUrl.query.sortBy || 'latest';

    sendJSON(res, {
      success: true,
      stories: mockStories,
      totalPages: 1,
      page: page,
      pageSize: pageSize,
      totalItems: mockStories.length,
      popularTags: [
        { tag: "求职经验", count: 15 },
        { tag: "实习经验", count: 8 },
        { tag: "转行经验", count: 6 },
        { tag: "职场成长", count: 12 }
      ]
    });
    return;
  }

  // 可视化数据API
  if (path === '/api/visualization/data' && method === 'GET') {
    sendJSON(res, mockVisualizationData);
    return;
  }

  // 问卷实时统计API（用于问卷页面显示）
  if (path === '/api/questionnaire/stats' && method === 'GET') {
    // 使用统一的数据源，确保与可视化页面数据一致
    sendJSON(res, {
      success: true,
      statistics: {
        totalResponses: QUESTIONNAIRE_CONFIG.totalResponses,
        educationLevels: EDUCATION_LEVELS,
        regions: REGIONS,
        industries: INDUSTRIES,
        lastUpdated: new Date().toISOString()
      }
    });
    return;
  }

  // 问卷详细统计API（用于管理后台）
  if (path === '/api/questionnaire/detailed-stats' && method === 'GET') {
    sendJSON(res, {
      success: true,
      stats: {
        totalResponses: QUESTIONNAIRE_CONFIG.totalResponses,
        verifiedResponses: QUESTIONNAIRE_CONFIG.verifiedCount,
        anonymousResponses: QUESTIONNAIRE_CONFIG.anonymousCount,
        recentResponses: 12
      }
    });
    return;
  }

  // 管理员登录API
  if (path === '/api/admin/login' && method === 'POST') {
    let body = '';
    req.on('data', chunk => {
      body += chunk.toString();
    });
    req.on('end', () => {
      try {
        const { username, password } = JSON.parse(body);

        // 定义管理员用户
        const adminUsers = [
          {
            username: 'admin',
            password: 'admin123',
            role: 'admin',
            name: '管理员',
            id: 1
          },
          {
            username: 'reviewer',
            password: 'reviewer123',
            role: 'reviewer',
            name: '审核员',
            id: 2
          },
          {
            username: 'superadmin',
            password: 'super123',
            role: 'superadmin',
            name: '超级管理员',
            id: 3
          }
        ];

        // 查找匹配的用户
        const user = adminUsers.find(u => u.username === username && u.password === password);

        if (user) {
          sendJSON(res, {
            success: true,
            token: `mock-${user.role}-token-${Date.now()}`,
            user: {
              id: user.id,
              username: user.username,
              name: user.name,
              role: user.role
            }
          });
        } else {
          sendJSON(res, {
            success: false,
            error: 'Invalid credentials'
          }, 401);
        }
      } catch (error) {
        sendJSON(res, {
          success: false,
          error: 'Invalid JSON'
        }, 400);
      }
    });
    return;
  }

  // 获取待审核故事API
  if (path === '/api/admin/stories/pending' && method === 'GET') {
    sendJSON(res, {
      success: true,
      stories: [
        {
          id: 1,
          title: "待审核的故事标题1",
          content: "这是一个待审核的故事内容...",
          author: { id: 1, name: "匿名用户" },
          createdAt: "2024-01-15T10:30:00Z",
          status: "pending",
          tags: ["本科", "计算机"],
          category: "就业经历"
        },
        {
          id: 2,
          title: "待审核的故事标题2",
          content: "另一个待审核的故事内容...",
          author: { id: 2, name: "匿名用户" },
          createdAt: "2024-01-15T11:00:00Z",
          status: "pending",
          tags: ["硕士", "金融"],
          category: "求职经历"
        }
      ],
      total: 2
    });
    return;
  }

  // 获取已审核故事API
  if (path === '/api/admin/stories/reviewed' && method === 'GET') {
    sendJSON(res, {
      success: true,
      stories: [
        {
          id: 3,
          title: "已审核的故事标题1",
          content: "这是一个已审核的故事内容...",
          author: { id: 3, name: "匿名用户" },
          createdAt: "2024-01-14T10:30:00Z",
          status: "approved",
          tags: ["本科", "互联网"],
          category: "就业经历",
          reviewedAt: "2024-01-14T12:00:00Z",
          reviewedBy: "reviewer"
        }
      ],
      total: 1
    });
    return;
  }

  // 获取标签列表API
  if (path === '/api/admin/tags' && method === 'GET') {
    sendJSON(res, {
      success: true,
      tags: [
        { id: 1, name: "本科", count: 25 },
        { id: 2, name: "硕士", count: 18 },
        { id: 3, name: "博士", count: 7 },
        { id: 4, name: "计算机", count: 20 },
        { id: 5, name: "金融", count: 15 },
        { id: 6, name: "互联网", count: 22 },
        { id: 7, name: "就业经历", count: 30 },
        { id: 8, name: "求职经历", count: 20 }
      ]
    });
    return;
  }



  // 审核通过内容API
  if (path.match(/^\/api\/admin\/review\/(.+)\/approve$/) && method === 'POST') {
    const contentId = path.match(/^\/api\/admin\/review\/(.+)\/approve$/)[1];

    sendJSON(res, {
      success: true,
      message: '内容审核通过',
      contentId: contentId,
      status: 'approved'
    });
    return;
  }

  // 拒绝内容API
  if (path.match(/^\/api\/admin\/review\/(.+)\/reject$/) && method === 'POST') {
    const contentId = path.match(/^\/api\/admin\/review\/(.+)\/reject$/)[1];

    sendJSON(res, {
      success: true,
      message: '内容已被拒绝',
      contentId: contentId,
      status: 'rejected'
    });
    return;
  }

  // 编辑并通过内容API
  if (path.match(/^\/api\/admin\/review\/(.+)\/edit$/) && method === 'PUT') {
    const contentId = path.match(/^\/api\/admin\/review\/(.+)\/edit$/)[1];

    sendJSON(res, {
      success: true,
      message: '内容已编辑并通过审核',
      contentId: contentId,
      status: 'approved'
    });
    return;
  }



  // 审核模板API
  if (path === '/api/admin/review/templates' && method === 'GET') {
    sendJSON(res, {
      success: true,
      templates: [
        {
          id: 1,
          name: "标准审核模板",
          description: "用于一般内容审核的标准模板",
          criteria: ["内容真实性", "语言规范性", "信息完整性"],
          isDefault: true
        },
        {
          id: 2,
          name: "快速审核模板",
          description: "用于快速审核的简化模板",
          criteria: ["基本合规性"],
          isDefault: false
        }
      ]
    });
    return;
  }

  // 审核统计API
  if (path === '/api/admin/review/stats' && method === 'GET') {
    sendJSON(res, {
      success: true,
      stats: {
        pending: 23,
        approved: 156,
        rejected: 45,
        total: 224,
        avgDuration: 150, // 秒
        daily: [
          { date: '2025-01-23', total: 12, approved: 9, rejected: 3 },
          { date: '2025-01-22', total: 15, approved: 12, rejected: 3 },
          { date: '2025-01-21', total: 18, approved: 14, rejected: 4 }
        ]
      }
    });
    return;
  }

  // 待审核内容列表API
  if (path === '/api/admin/review/pending' && method === 'GET') {
    const pageSize = parseInt(parsedUrl.query.pageSize) || 10;
    const page = parseInt(parsedUrl.query.page) || 1;
    const type = parsedUrl.query.type || 'all';

    // 生成模拟的待审核内容
    const allContents = [];

    // 生成故事类型的内容
    for (let i = 1; i <= 10; i++) {
      allContents.push({
        id: `story-${i}`,
        sequenceNumber: `S${String(i).padStart(5, '0')}`,
        type: 'story',
        title: `待审核故事 ${i}`,
        content: `这是一个待审核的故事内容，描述了毕业生的就业经历和感受。故事 ${i}。`,
        originalContent: JSON.stringify({
          title: `待审核故事 ${i}`,
          content: `这是一个待审核的故事内容，描述了毕业生的就业经历和感受。故事 ${i}。`,
          tags: ['本科', '计算机', '求职经历'],
          isAnonymous: i % 2 === 0
        }),
        status: 'pending',
        flags: i % 4 === 0 ? ['sensitive'] : [],
        createdAt: new Date(Date.now() - (i * 24 * 60 * 60 * 1000)).toISOString(),
        updatedAt: new Date(Date.now() - (i * 24 * 60 * 60 * 1000)).toISOString()
      });
    }

    // 生成问卷心声类型的内容
    for (let i = 1; i <= 8; i++) {
      allContents.push({
        id: `questionnaire-${i}`,
        sequenceNumber: `Q${String(i).padStart(5, '0')}`,
        type: 'questionnaire',
        title: `问卷心声 ${i}`,
        content: `希望能够找到一份符合专业的工作，为社会做出贡献。这是来自问卷第6步的心声内容 ${i}。`,
        originalContent: JSON.stringify({
          adviceForStudents: i % 2 === 0 ? `对学弟学妹的建议：要多实习，积累经验，提前做好职业规划。内容 ${i}` : null,
          observationOnEmployment: i % 3 === 0 ? `当前就业形势比较严峻，需要做好充分准备，保持积极心态。观察 ${i}` : null,
          userId: `user-${i}`
        }),
        status: 'pending',
        flags: i % 5 === 0 ? ['sensitive'] : [],
        createdAt: new Date(Date.now() - (i * 12 * 60 * 60 * 1000)).toISOString(),
        updatedAt: new Date(Date.now() - (i * 12 * 60 * 60 * 1000)).toISOString()
      });
    }

    // 根据类型过滤内容
    let filteredContents = allContents;
    if (type === 'story') {
      filteredContents = allContents.filter(content => content.type === 'story');
    } else if (type === 'questionnaire') {
      filteredContents = allContents.filter(content => content.type === 'questionnaire');
    }

    const startIndex = (page - 1) * pageSize;
    const endIndex = startIndex + pageSize;
    const paginatedContents = filteredContents.slice(startIndex, endIndex);

    sendJSON(res, {
      success: true,
      pendingContents: paginatedContents,
      data: paginatedContents, // 兼容不同的前端期望
      total: filteredContents.length,
      pagination: {
        page: page,
        pageSize: pageSize,
        total: filteredContents.length,
        totalPages: Math.ceil(filteredContents.length / pageSize)
      }
    });
    return;
  }

  // 审核员绩效API
  if (path === '/api/admin/review/performance' && method === 'GET') {
    const timeRange = parsedUrl.query.timeRange || 'week';
    sendJSON(res, {
      success: true,
      performance: {
        timeRange: timeRange,
        reviewCount: 45,
        accuracy: 0.96,
        averageTime: "2.3分钟",
        efficiency: "高"
      }
    });
    return;
  }

  // 去标识化配置API
  if (path === '/api/admin/deidentification/config' && method === 'GET') {
    sendJSON(res, {
      success: true,
      config: {
        enableAutoDeidentification: true,
        sensitiveFields: ["name", "email", "phone", "idCard"],
        maskingRules: {
          name: "***",
          email: "***@***.com",
          phone: "***-****-****"
        }
      }
    });
    return;
  }

  // 获取单条待审核内容详情API（放在最后，避免路径冲突）
  if (path.match(/^\/api\/admin\/review\/(.+)$/) && method === 'GET' && !path.includes('pending') && !path.includes('stats') && !path.includes('templates') && !path.includes('performance')) {
    const contentId = path.match(/^\/api\/admin\/review\/(.+)$/)[1];

    // 模拟返回内容详情
    const mockContent = {
      id: contentId,
      type: 'story',
      sequenceNumber: 'S24070001',
      title: '我的求职经历分享',
      content: '作为一名计算机专业的应届毕业生，我想分享一下自己的求职经历...',
      originalContent: JSON.stringify({
        title: '我的求职经历分享',
        content: '作为一名计算机专业的应届毕业生，我想分享一下自己的求职经历...',
        tags: ['本科', '计算机', '求职经历'],
        isAnonymous: false
      }),
      userId: 'user-123',
      userName: '张同学',
      status: 'pending',
      flags: [],
      createdAt: '2024-07-15T10:30:00Z',
      updatedAt: '2024-07-15T10:30:00Z',
      originIp: '*************',
      reviewLogs: []
    };

    sendJSON(res, {
      success: true,
      pendingContent: mockContent
    });
    return;
  }

  // 审核员仪表盘统计数据API
  if (path === '/api/reviewer/dashboard/stats' && method === 'GET') {
    // 获取审核员ID（从Authorization header或查询参数）
    const authHeader = req.headers.authorization;
    const reviewerId = authHeader ? authHeader.replace('Bearer ', '') : 'reviewer-123';

    // TODO: 这里应该从真实数据库查询审核员的实际统计数据
    // 当前使用模拟数据，但结构已为真实数据做好准备

    // 模拟从数据库查询的结果（实际应该是SQL查询）
    const mockDatabaseQuery = {
      // SELECT COUNT(*) FROM reviews WHERE reviewer_id = ? AND action = 'approve' AND content_type = 'story'
      approvedStories: Math.floor(Math.random() * 30) + 15, // 15-45之间的随机数
      // SELECT COUNT(*) FROM reviews WHERE reviewer_id = ? AND action = 'reject' AND content_type = 'story'
      rejectedStories: Math.floor(Math.random() * 10) + 3,  // 3-13之间的随机数
      // SELECT COUNT(*) FROM reviews WHERE reviewer_id = ? AND action = 'approve' AND content_type = 'voice'
      approvedVoices: Math.floor(Math.random() * 20) + 8,   // 8-28之间的随机数
      // SELECT COUNT(*) FROM reviews WHERE reviewer_id = ? AND action = 'reject' AND content_type = 'voice'
      rejectedVoices: Math.floor(Math.random() * 5) + 1     // 1-6之间的随机数
    };

    const { approvedStories, rejectedStories, approvedVoices, rejectedVoices } = mockDatabaseQuery;
    const totalReviewed = approvedStories + rejectedStories + approvedVoices + rejectedVoices;
    const reviewRate = approvedStories + rejectedStories > 0
      ? Math.round((approvedStories / (approvedStories + rejectedStories)) * 100)
      : 0;

    sendJSON(res, {
      success: true,
      approvedStories: approvedStories,
      rejectedStories: rejectedStories,
      approvedVoices: approvedVoices,
      rejectedVoices: rejectedVoices,
      totalReviewed: totalReviewed,
      reviewRate: reviewRate,
      reviewerId: reviewerId, // 添加审核员ID用于调试
      dataSource: 'mock_database_simulation', // 标识数据来源
      lastUpdated: new Date().toISOString()
    });
    return;
  }

  // 审核员待审核内容列表API
  if (path === '/api/reviewer/pending-content' && method === 'GET') {
    const page = parseInt(parsedUrl.query.page) || 1;
    const limit = parseInt(parsedUrl.query.limit) || 10;

    // 模拟待审核故事
    const stories = [];
    for (let i = 1; i <= 15; i++) {
      stories.push({
        id: i,
        title: `待审核故事 ${i}`,
        content: `这是一个待审核的故事内容，描述了毕业生的就业经历和感受。故事 ${i}。`,
        created_at: new Date(Date.now() - (i * 2 * 60 * 60 * 1000)).toISOString(),
        is_anonymous: i % 2 === 0
      });
    }

    // 模拟待审核问卷心声
    const voices = [];
    for (let i = 1; i <= 12; i++) {
      voices.push({
        id: i,
        content: `希望能够找到一份符合专业的工作，为社会做出贡献。这是来自问卷第6步的心声内容 ${i}。`,
        created_at: new Date(Date.now() - (i * 3 * 60 * 60 * 1000)).toISOString(),
        is_anonymous: i % 3 === 0
      });
    }

    sendJSON(res, {
      success: true,
      stories: stories.slice(0, limit),
      voices: voices.slice(0, limit),
      totalStories: stories.length,
      totalVoices: voices.length
    });
    return;
  }

  // 审核故事API
  if (path.match(/^\/api\/reviewer\/review\/story\/(\d+)$/) && method === 'POST') {
    const storyId = path.match(/^\/api\/reviewer\/review\/story\/(\d+)$/)[1];

    let body = '';
    req.on('data', chunk => {
      body += chunk.toString();
    });
    req.on('end', () => {
      try {
        const { action, reason } = JSON.parse(body);

        sendJSON(res, {
          success: true,
          message: `故事已${action === 'approve' ? '批准' : '拒绝'}`,
          storyId: storyId,
          action: action
        });
      } catch (error) {
        sendJSON(res, {
          success: false,
          error: 'Invalid JSON'
        }, 400);
      }
    });
    return;
  }

  // 审核问卷心声API
  if (path.match(/^\/api\/reviewer\/review\/voice\/(\d+)$/) && method === 'POST') {
    const voiceId = path.match(/^\/api\/reviewer\/review\/voice\/(\d+)$/)[1];

    let body = '';
    req.on('data', chunk => {
      body += chunk.toString();
    });
    req.on('end', () => {
      try {
        const { action, reason } = JSON.parse(body);

        sendJSON(res, {
          success: true,
          message: `问卷心声已${action === 'approve' ? '批准' : '拒绝'}`,
          voiceId: voiceId,
          action: action
        });
      } catch (error) {
        sendJSON(res, {
          success: false,
          error: 'Invalid JSON'
        }, 400);
      }
    });
    return;
  }

  // 获取批量审核故事API（每次5条）
  if (path === '/api/reviewer/batch/stories' && method === 'GET') {
    const stories = [];
    for (let i = 1; i <= 5; i++) {
      stories.push({
        id: i,
        sequenceNumber: `S${String(i).padStart(5, '0')}`,
        title: `待审核故事 ${i}`,
        content: `这是一个待审核的故事内容，描述了毕业生的就业经历和感受。故事 ${i}。内容包含了求职过程中的挑战、收获和感悟，希望能够为其他同学提供参考和帮助。`,
        author: i % 2 === 0 ? '匿名用户' : `用户${i}`,
        isAnonymous: i % 2 === 0,
        createdAt: new Date(Date.now() - (i * 2 * 60 * 60 * 1000)).toISOString(),
        tags: ['本科', '计算机', '求职经历'],
        category: '就业经历'
      });
    }

    sendJSON(res, {
      success: true,
      stories: stories,
      total: stories.length,
      batchId: `batch-${Date.now()}`
    });
    return;
  }

  // 获取批量审核问卷心声API（每次5条）
  if (path === '/api/reviewer/batch/voices' && method === 'GET') {
    const voices = [];
    for (let i = 1; i <= 5; i++) {
      voices.push({
        id: i,
        sequenceNumber: `Q${String(i).padStart(5, '0')}`,
        content: `希望能够找到一份符合专业的工作，为社会做出贡献。这是来自问卷第6步的心声内容 ${i}。我认为当前的就业形势虽然有挑战，但只要做好充分准备，保持积极心态，一定能够找到合适的工作机会。`,
        author: i % 3 === 0 ? '匿名用户' : `用户${i}`,
        isAnonymous: i % 3 === 0,
        createdAt: new Date(Date.now() - (i * 3 * 60 * 60 * 1000)).toISOString(),
        questionnaireId: `questionnaire-${i}`,
        step: 6
      });
    }

    sendJSON(res, {
      success: true,
      voices: voices,
      total: voices.length,
      batchId: `batch-${Date.now()}`
    });
    return;
  }

  // 批量提交审核结果API
  if (path === '/api/reviewer/batch/submit' && method === 'POST') {
    let body = '';
    req.on('data', chunk => {
      body += chunk.toString();
    });
    req.on('end', () => {
      try {
        const { batchId, results, type } = JSON.parse(body);

        // 模拟处理审核结果
        const processedResults = results.map(result => ({
          id: result.id,
          action: result.action,
          reason: result.reason || '',
          reviewedAt: new Date().toISOString(),
          reviewerId: 'reviewer-123' // 模拟审核员ID
        }));

        sendJSON(res, {
          success: true,
          message: `成功提交${results.length}条${type === 'stories' ? '故事' : '问卷心声'}审核结果`,
          batchId: batchId,
          processedCount: processedResults.length,
          results: processedResults
        });
      } catch (error) {
        sendJSON(res, {
          success: false,
          error: 'Invalid JSON'
        }, 400);
      }
    });
    return;
  }

  // 生成测试数据API
  if (path === '/api/admin/generate-test-data' && method === 'POST') {
    // 生成50个问卷响应的模拟数据
    const questionnaires = [];
    const educationLevels = ['本科', '硕士', '博士', '专科'];
    const industries = ['互联网', '金融', '教育', '制造业', '服务业', '医疗', '政府机关', '其他'];
    const regions = ['北京', '上海', '广州', '深圳', '杭州', '南京', '成都', '武汉', '西安', '其他'];

    for (let i = 1; i <= 50; i++) {
      const hasUUID = i <= 10; // 前10个有UUID，后40个匿名
      questionnaires.push({
        id: i,
        uuid: hasUUID ? `user-${i}` : null,
        isAnonymous: !hasUUID,
        educationLevel: educationLevels[Math.floor(Math.random() * educationLevels.length)],
        industry: industries[Math.floor(Math.random() * industries.length)],
        region: regions[Math.floor(Math.random() * regions.length)],
        expectedSalary: Math.floor(Math.random() * 20000) + 5000,
        actualSalary: Math.floor(Math.random() * 18000) + 4000,
        unemploymentDuration: Math.floor(Math.random() * 12),
        careerChange: Math.random() > 0.5,
        createdAt: new Date(Date.now() - Math.random() * 30 * 24 * 60 * 60 * 1000).toISOString(),
        responses: {
          step1: { basicInfo: 'completed' },
          step2: { education: 'completed' },
          step3: { employment: 'completed' },
          step4: { salary: 'completed' },
          step5: { experience: 'completed' },
          step6: { story: i % 3 === 0 ? '我的就业故事...' : null }
        }
      });
    }

    sendJSON(res, {
      success: true,
      message: '成功生成50个测试问卷数据',
      data: {
        totalGenerated: 50,
        withUUID: 10,
        anonymous: 40,
        questionnaires: questionnaires
      }
    });
    return;
  }

  // 404处理
  sendJSON(res, {
    success: false,
    error: 'API endpoint not found',
    path: path,
    method: method
  }, 404);
}

// 创建服务器
const server = http.createServer(handleRequest);

server.listen(PORT, () => {
  console.log(`🚀 API测试服务器启动成功！`);
  console.log(`📍 地址: http://localhost:${PORT}`);
  console.log(`🔍 健康检查: http://localhost:${PORT}/health`);
  console.log(`📊 故事列表: http://localhost:${PORT}/api/story/list`);
  console.log(`📈 可视化数据: http://localhost:${PORT}/api/visualization/data`);
  console.log(`📋 问卷统计: http://localhost:${PORT}/api/questionnaire/stats`);
  console.log(`\n按 Ctrl+C 停止服务器`);
});

// 优雅关闭
process.on('SIGINT', () => {
  console.log('\n🛑 正在关闭服务器...');
  server.close(() => {
    console.log('✅ 服务器已关闭');
    process.exit(0);
  });
});
