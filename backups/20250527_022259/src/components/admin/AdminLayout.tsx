import React from 'react';
import { Link, useLocation } from 'react-router-dom';
import ReviewNotifications from './ReviewNotifications';
import '../../styles/admin/admin-layout.css';

interface AdminLayoutProps {
  children: React.ReactNode;
}

const AdminLayout: React.FC<AdminLayoutProps> = ({ children }) => {
  const location = useLocation();

  // 检查当前路径是否匹配
  const isActive = (path: string) => {
    return location.pathname === path;
  };

  return (
    <div className="admin-layout">
      <div className="sidebar">
        <div className="sidebar-header">
          <h2>管理后台</h2>
        </div>

        <ul className="sidebar-menu">
          <li className={isActive('/admin/dashboard') ? 'active' : ''}>
            <Link to="/admin/dashboard">仪表盘</Link>
          </li>
          <li className={isActive('/admin/content-review') ? 'active' : ''}>
            <Link to="/admin/content-review">内容审核</Link>
          </li>
          <li className={isActive('/admin/story-review') ? 'active' : ''}>
            <Link to="/admin/story-review">故事审核</Link>
          </li>
          <li className={isActive('/admin/tag-management') ? 'active' : ''}>
            <Link to="/admin/tag-management">标签管理</Link>
          </li>
          <li className={isActive('/admin/questionnaire-responses') ? 'active' : ''}>
            <Link to="/admin/questionnaire-responses">问卷回复</Link>
          </li>
          <li className={isActive('/admin/data-analysis') ? 'active' : ''}>
            <Link to="/admin/data-analysis">数据分析</Link>
          </li>
          <li className={isActive('/admin/user-management') ? 'active' : ''}>
            <Link to="/admin/user-management">用户管理</Link>
          </li>
          <li className={isActive('/admin/security-settings') ? 'active' : ''}>
            <Link to="/admin/security-settings">安全设置</Link>
          </li>
          <li className={isActive('/admin/deidentification-settings') ? 'active' : ''}>
            <Link to="/admin/deidentification-settings">内容脱敏</Link>
          </li>
          <li className={isActive('/admin/security-logs') ? 'active' : ''}>
            <Link to="/admin/security-logs">安全日志</Link>
          </li>
        </ul>
      </div>

      <div className="main-content">
        {children}
      </div>
    </div>
  );
};

export default AdminLayout;
