import React, { useState, useEffect } from 'react';
import { 
  Card, 
  Form, 
  Input, 
  Button, 
  Switch, 
  Select, 
  InputNumber, 
  Divider, 
  message, 
  Alert, 
  Spin, 
  Typography, 
  Space, 
  Tabs,
  Tooltip,
  Collapse
} from 'antd';
import { 
  SaveOutlined, 
  ReloadOutlined, 
  QuestionCircleOutlined, 
  ApiOutlined, 
  DatabaseOutlined, 
  SecurityScanOutlined,
  SettingOutlined,
  GlobalOutlined,
  ClockCircleOutlined
} from '@ant-design/icons';
import { fetchData } from '../../../services/unifiedDataService';

const { TabPane } = Tabs;
const { Title, Text, Paragraph } = Typography;
const { Option } = Select;
const { Panel } = Collapse;

// 系统设置组件
const SystemSettings: React.FC = () => {
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);
  const [settings, setSettings] = useState<any>({});
  const [form] = Form.useForm();
  const [apiForm] = Form.useForm();
  const [databaseForm] = Form.useForm();
  const [securityForm] = Form.useForm();
  const [generalForm] = Form.useForm();

  // 加载系统设置
  const loadSettings = async () => {
    setLoading(true);
    setError(null);

    try {
      const response = await fetchData(
        'admin/settings',
        'getSystemSettings',
        {},
        {
          method: 'GET',
          headers: {
            Authorization: `Bearer ${localStorage.getItem('authToken')}`,
          },
        }
      );

      if (response.success) {
        setSettings(response.settings || {});
        
        // 设置表单初始值
        const { api, database, security, general } = response.settings || {};
        
        apiForm.setFieldsValue(api || {});
        databaseForm.setFieldsValue(database || {});
        securityForm.setFieldsValue(security || {});
        generalForm.setFieldsValue(general || {});
      } else {
        setError(response.error || '获取系统设置失败');
      }
    } catch (err) {
      setError('获取系统设置时发生错误');
      console.error('获取系统设置错误:', err);
    } finally {
      setLoading(false);
    }
  };

  // 首次加载
  useEffect(() => {
    loadSettings();
  }, []);

  // 保存API设置
  const handleSaveApiSettings = async (values: any) => {
    try {
      const response = await fetchData(
        'admin/settings/api',
        'updateApiSettings',
        values,
        {
          method: 'PUT',
          headers: {
            Authorization: `Bearer ${localStorage.getItem('authToken')}`,
          },
        }
      );
      
      if (response.success) {
        message.success('API设置已保存');
        
        // 重新加载设置
        loadSettings();
      } else {
        message.error(response.error || '保存API设置失败');
      }
    } catch (err) {
      console.error('保存API设置错误:', err);
      message.error('保存API设置时发生错误');
    }
  };

  // 保存数据库设置
  const handleSaveDatabaseSettings = async (values: any) => {
    try {
      const response = await fetchData(
        'admin/settings/database',
        'updateDatabaseSettings',
        values,
        {
          method: 'PUT',
          headers: {
            Authorization: `Bearer ${localStorage.getItem('authToken')}`,
          },
        }
      );
      
      if (response.success) {
        message.success('数据库设置已保存');
        
        // 重新加载设置
        loadSettings();
      } else {
        message.error(response.error || '保存数据库设置失败');
      }
    } catch (err) {
      console.error('保存数据库设置错误:', err);
      message.error('保存数据库设置时发生错误');
    }
  };

  // 保存安全设置
  const handleSaveSecuritySettings = async (values: any) => {
    try {
      const response = await fetchData(
        'admin/settings/security',
        'updateSecuritySettings',
        values,
        {
          method: 'PUT',
          headers: {
            Authorization: `Bearer ${localStorage.getItem('authToken')}`,
          },
        }
      );
      
      if (response.success) {
        message.success('安全设置已保存');
        
        // 重新加载设置
        loadSettings();
      } else {
        message.error(response.error || '保存安全设置失败');
      }
    } catch (err) {
      console.error('保存安全设置错误:', err);
      message.error('保存安全设置时发生错误');
    }
  };

  // 保存通用设置
  const handleSaveGeneralSettings = async (values: any) => {
    try {
      const response = await fetchData(
        'admin/settings/general',
        'updateGeneralSettings',
        values,
        {
          method: 'PUT',
          headers: {
            Authorization: `Bearer ${localStorage.getItem('authToken')}`,
          },
        }
      );
      
      if (response.success) {
        message.success('通用设置已保存');
        
        // 重新加载设置
        loadSettings();
      } else {
        message.error(response.error || '保存通用设置失败');
      }
    } catch (err) {
      console.error('保存通用设置错误:', err);
      message.error('保存通用设置时发生错误');
    }
  };

  return (
    <div className="system-settings">
      <Spin spinning={loading}>
        {error && (
          <Alert
            message="错误"
            description={error}
            type="error"
            showIcon
            style={{ marginBottom: 16 }}
          />
        )}
        
        <Tabs defaultActiveKey="general">
          <TabPane 
            tab={
              <span>
                <SettingOutlined />
                通用设置
              </span>
            } 
            key="general"
          >
            <Card>
              <Form
                form={generalForm}
                layout="vertical"
                onFinish={handleSaveGeneralSettings}
              >
                <Title level={4}>通用设置</Title>
                
                <Form.Item
                  name="siteName"
                  label="站点名称"
                  rules={[{ required: true, message: '请输入站点名称' }]}
                >
                  <Input placeholder="请输入站点名称" />
                </Form.Item>
                
                <Form.Item
                  name="siteDescription"
                  label="站点描述"
                >
                  <Input.TextArea rows={3} placeholder="请输入站点描述" />
                </Form.Item>
                
                <Form.Item
                  name="contactEmail"
                  label="联系邮箱"
                  rules={[
                    { required: true, message: '请输入联系邮箱' },
                    { type: 'email', message: '请输入有效的邮箱地址' }
                  ]}
                >
                  <Input placeholder="请输入联系邮箱" />
                </Form.Item>
                
                <Divider />
                
                <Form.Item
                  name="maintenanceMode"
                  label="维护模式"
                  valuePropName="checked"
                >
                  <Switch />
                </Form.Item>
                
                <Form.Item
                  name="maintenanceMessage"
                  label="维护消息"
                >
                  <Input.TextArea rows={3} placeholder="请输入维护消息" />
                </Form.Item>
                
                <Divider />
                
                <Form.Item
                  name="defaultLanguage"
                  label="默认语言"
                >
                  <Select>
                    <Option value="zh-CN">简体中文</Option>
                    <Option value="en-US">English (US)</Option>
                  </Select>
                </Form.Item>
                
                <Form.Item
                  name="defaultTimezone"
                  label="默认时区"
                >
                  <Select>
                    <Option value="Asia/Shanghai">中国标准时间 (UTC+8)</Option>
                    <Option value="UTC">协调世界时 (UTC)</Option>
                    <Option value="America/New_York">美国东部时间 (UTC-5/4)</Option>
                  </Select>
                </Form.Item>
                
                <Form.Item>
                  <Space>
                    <Button type="primary" htmlType="submit" icon={<SaveOutlined />}>
                      保存设置
                    </Button>
                    <Button icon={<ReloadOutlined />} onClick={loadSettings}>
                      重置
                    </Button>
                  </Space>
                </Form.Item>
              </Form>
            </Card>
          </TabPane>
          
          <TabPane 
            tab={
              <span>
                <ApiOutlined />
                API设置
              </span>
            } 
            key="api"
          >
            <Card>
              <Form
                form={apiForm}
                layout="vertical"
                onFinish={handleSaveApiSettings}
              >
                <Title level={4}>API设置</Title>
                
                <Form.Item
                  name="baseUrl"
                  label="API基础URL"
                  rules={[{ required: true, message: '请输入API基础URL' }]}
                >
                  <Input placeholder="请输入API基础URL" />
                </Form.Item>
                
                <Form.Item
                  name="version"
                  label="API版本"
                  rules={[{ required: true, message: '请输入API版本' }]}
                >
                  <Input placeholder="请输入API版本" />
                </Form.Item>
                
                <Divider />
                
                <Form.Item
                  name="timeout"
                  label={
                    <span>
                      请求超时（毫秒）
                      <Tooltip title="API请求的超时时间">
                        <QuestionCircleOutlined style={{ marginLeft: 4 }} />
                      </Tooltip>
                    </span>
                  }
                  rules={[{ required: true, message: '请输入请求超时' }]}
                >
                  <InputNumber min={1000} max={60000} style={{ width: '100%' }} />
                </Form.Item>
                
                <Form.Item
                  name="retryCount"
                  label="重试次数"
                  rules={[{ required: true, message: '请输入重试次数' }]}
                >
                  <InputNumber min={0} max={5} style={{ width: '100%' }} />
                </Form.Item>
                
                <Form.Item
                  name="retryDelay"
                  label="重试延迟（毫秒）"
                  rules={[{ required: true, message: '请输入重试延迟' }]}
                >
                  <InputNumber min={100} max={10000} style={{ width: '100%' }} />
                </Form.Item>
                
                <Divider />
                
                <Form.Item
                  name="useMock"
                  label="使用模拟数据"
                  valuePropName="checked"
                >
                  <Switch />
                </Form.Item>
                
                <Form.Item
                  name="mockDelay"
                  label="模拟延迟（毫秒）"
                >
                  <InputNumber min={0} max={5000} style={{ width: '100%' }} />
                </Form.Item>
                
                <Form.Item
                  name="fallbackToMock"
                  label="API失败时回退到模拟数据"
                  valuePropName="checked"
                >
                  <Switch />
                </Form.Item>
                
                <Divider />
                
                <Form.Item
                  name="logApiCalls"
                  label="记录API调用"
                  valuePropName="checked"
                >
                  <Switch />
                </Form.Item>
                
                <Form.Item>
                  <Space>
                    <Button type="primary" htmlType="submit" icon={<SaveOutlined />}>
                      保存设置
                    </Button>
                    <Button icon={<ReloadOutlined />} onClick={loadSettings}>
                      重置
                    </Button>
                  </Space>
                </Form.Item>
              </Form>
            </Card>
          </TabPane>
          
          <TabPane 
            tab={
              <span>
                <DatabaseOutlined />
                数据库设置
              </span>
            } 
            key="database"
          >
            <Card>
              <Form
                form={databaseForm}
                layout="vertical"
                onFinish={handleSaveDatabaseSettings}
              >
                <Title level={4}>数据库设置</Title>
                
                <Form.Item
                  name="autoBackup"
                  label="启用自动备份"
                  valuePropName="checked"
                >
                  <Switch />
                </Form.Item>
                
                <Form.Item
                  name="backupInterval"
                  label="备份间隔（小时）"
                >
                  <InputNumber min={1} max={168} style={{ width: '100%' }} />
                </Form.Item>
                
                <Form.Item
                  name="maxBackups"
                  label="最大备份数量"
                >
                  <InputNumber min={1} max={100} style={{ width: '100%' }} />
                </Form.Item>
                
                <Form.Item
                  name="includeDataInBackup"
                  label="备份包含数据"
                  valuePropName="checked"
                >
                  <Switch />
                </Form.Item>
                
                <Divider />
                
                <Form.Item
                  name="cacheDuration"
                  label="缓存持续时间（秒）"
                >
                  <InputNumber min={0} max={86400} style={{ width: '100%' }} />
                </Form.Item>
                
                <Form.Item
                  name="useCache"
                  label="使用数据缓存"
                  valuePropName="checked"
                >
                  <Switch />
                </Form.Item>
                
                <Form.Item>
                  <Space>
                    <Button type="primary" htmlType="submit" icon={<SaveOutlined />}>
                      保存设置
                    </Button>
                    <Button icon={<ReloadOutlined />} onClick={loadSettings}>
                      重置
                    </Button>
                  </Space>
                </Form.Item>
              </Form>
            </Card>
          </TabPane>
          
          <TabPane 
            tab={
              <span>
                <SecurityScanOutlined />
                安全设置
              </span>
            } 
            key="security"
          >
            <Card>
              <Form
                form={securityForm}
                layout="vertical"
                onFinish={handleSaveSecuritySettings}
              >
                <Title level={4}>安全设置</Title>
                
                <Form.Item
                  name="jwtExpiration"
                  label="JWT令牌过期时间（分钟）"
                  rules={[{ required: true, message: '请输入JWT令牌过期时间' }]}
                >
                  <InputNumber min={5} max={1440} style={{ width: '100%' }} />
                </Form.Item>
                
                <Form.Item
                  name="refreshTokenExpiration"
                  label="刷新令牌过期时间（天）"
                  rules={[{ required: true, message: '请输入刷新令牌过期时间' }]}
                >
                  <InputNumber min={1} max={30} style={{ width: '100%' }} />
                </Form.Item>
                
                <Divider />
                
                <Form.Item
                  name="maxLoginAttempts"
                  label="最大登录尝试次数"
                >
                  <InputNumber min={1} max={10} style={{ width: '100%' }} />
                </Form.Item>
                
                <Form.Item
                  name="lockoutDuration"
                  label="锁定时间（分钟）"
                >
                  <InputNumber min={5} max={1440} style={{ width: '100%' }} />
                </Form.Item>
                
                <Divider />
                
                <Form.Item
                  name="passwordMinLength"
                  label="密码最小长度"
                >
                  <InputNumber min={6} max={32} style={{ width: '100%' }} />
                </Form.Item>
                
                <Form.Item
                  name="passwordRequireUppercase"
                  label="密码需要大写字母"
                  valuePropName="checked"
                >
                  <Switch />
                </Form.Item>
                
                <Form.Item
                  name="passwordRequireNumbers"
                  label="密码需要数字"
                  valuePropName="checked"
                >
                  <Switch />
                </Form.Item>
                
                <Form.Item
                  name="passwordRequireSpecialChars"
                  label="密码需要特殊字符"
                  valuePropName="checked"
                >
                  <Switch />
                </Form.Item>
                
                <Divider />
                
                <Form.Item
                  name="enableCors"
                  label="启用CORS"
                  valuePropName="checked"
                >
                  <Switch />
                </Form.Item>
                
                <Form.Item
                  name="allowedOrigins"
                  label="允许的来源"
                >
                  <Input.TextArea rows={3} placeholder="请输入允许的来源，每行一个" />
                </Form.Item>
                
                <Form.Item>
                  <Space>
                    <Button type="primary" htmlType="submit" icon={<SaveOutlined />}>
                      保存设置
                    </Button>
                    <Button icon={<ReloadOutlined />} onClick={loadSettings}>
                      重置
                    </Button>
                  </Space>
                </Form.Item>
              </Form>
            </Card>
          </TabPane>
        </Tabs>
      </Spin>
    </div>
  );
};

export default SystemSettings;
