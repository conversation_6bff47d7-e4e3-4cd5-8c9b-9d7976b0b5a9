/**
 * 指纹服务
 */

import { FingerprintingConfig } from '../types';

/**
 * 简单的浏览器指纹生成函数
 */
function generateSimpleFingerprint(): string {
  const components = [
    navigator.userAgent,
    navigator.language,
    new Date().getTimezoneOffset(),
    screen.width + 'x' + screen.height + 'x' + screen.colorDepth,
    navigator.hardwareConcurrency,
    navigator.deviceMemory,
    navigator.platform,
    !!navigator.cookieEnabled,
    !!navigator.doNotTrack,
    typeof window.indexedDB !== 'undefined',
    typeof window.sessionStorage !== 'undefined',
    typeof window.localStorage !== 'undefined',
    typeof window.openDatabase !== 'undefined',
    !!window.WebSocket,
    !!window.Worker,
    !!window.HTMLCanvasElement
  ];

  // 创建一个简单的哈希
  let hash = 0;
  const str = components.join('###');
  for (let i = 0; i < str.length; i++) {
    hash = ((hash << 5) - hash) + str.charCodeAt(i);
    hash |= 0; // 转换为32位整数
  }

  return hash.toString(16);
}

/**
 * 指纹服务类
 */
export class FingerprintService {
  private config: FingerprintingConfig;
  private fingerprint: string | null = null;
  private fpPromise: Promise<string> | null = null;

  /**
   * 构造函数
   */
  constructor() {
    this.config = {
      enabled: false,
      storageTime: 0
    };
  }

  /**
   * 初始化服务
   * @param config 指纹配置
   */
  public initialize(config: FingerprintingConfig): void {
    this.config = { ...config };
    
    // 如果启用，生成或加载指纹
    if (this.config.enabled) {
      this.loadOrGenerateFingerprint();
    }
  }

  /**
   * 更新配置
   * @param config 指纹配置
   */
  public updateConfig(config: FingerprintingConfig): void {
    const wasEnabled = this.config.enabled;
    this.config = { ...this.config, ...config };
    
    // 如果启用状态变更为启用，生成或加载指纹
    if (!wasEnabled && this.config.enabled) {
      this.loadOrGenerateFingerprint();
    }
  }

  /**
   * 获取指纹
   */
  public getFingerprint(): string | null {
    return this.fingerprint;
  }

  /**
   * 加载或生成指纹
   */
  private async loadOrGenerateFingerprint(): Promise<void> {
    // 如果已有指纹或正在生成，直接返回
    if (this.fingerprint || this.fpPromise) {
      return;
    }
    
    // 尝试从本地存储加载指纹
    const storedFp = localStorage.getItem('security_fingerprint');
    const storedTime = localStorage.getItem('security_fingerprint_time');
    
    if (storedFp && storedTime) {
      const time = parseInt(storedTime, 10);
      const now = Date.now();
      
      // 检查指纹是否过期
      if (now - time < this.config.storageTime * 24 * 60 * 60 * 1000) {
        this.fingerprint = storedFp;
        return;
      }
    }
    
    // 生成新指纹
    this.fpPromise = this.generateFingerprint();
    this.fingerprint = await this.fpPromise;
    this.fpPromise = null;
    
    // 存储指纹
    localStorage.setItem('security_fingerprint', this.fingerprint);
    localStorage.setItem('security_fingerprint_time', Date.now().toString());
  }

  /**
   * 生成指纹
   */
  private async generateFingerprint(): Promise<string> {
    try {
      // 尝试加载 FingerprintJS
      if (typeof window.FingerprintJS !== 'undefined') {
        const fp = await window.FingerprintJS.load();
        const result = await fp.get();
        return result.visitorId;
      }
      
      // 如果没有 FingerprintJS，使用简单的指纹生成
      return generateSimpleFingerprint();
    } catch (error) {
      console.error('Error generating fingerprint:', error);
      return generateSimpleFingerprint();
    }
  }

  /**
   * 加载 FingerprintJS 脚本
   */
  private loadFingerprintScript(): Promise<void> {
    return new Promise((resolve, reject) => {
      // 检查脚本是否已加载
      if (document.querySelector('script[src*="fingerprintjs"]')) {
        resolve();
        return;
      }
      
      // 创建脚本元素
      const script = document.createElement('script');
      script.src = 'https://cdn.jsdelivr.net/npm/@fingerprintjs/fingerprintjs@3/dist/fp.min.js';
      script.async = true;
      
      // 添加事件监听器
      script.onload = () => resolve();
      script.onerror = () => reject(new Error('Failed to load FingerprintJS'));
      
      // 添加到文档
      document.head.appendChild(script);
    });
  }
}

// 为 FingerprintJS 声明全局类型
declare global {
  interface Window {
    FingerprintJS?: {
      load: () => Promise<{
        get: () => Promise<{
          visitorId: string;
        }>;
      }>;
    };
  }
}
