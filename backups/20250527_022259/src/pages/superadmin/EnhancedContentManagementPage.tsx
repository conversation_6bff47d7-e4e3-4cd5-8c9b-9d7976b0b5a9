import React, { useState, useEffect } from 'react';
import SuperAdminLayout from '@/components/layouts/SuperAdminLayout';
import AdvancedSearchPanel from '@/components/admin/AdvancedSearchPanel';
import BatchOperationPanel from '@/components/admin/BatchOperationPanel';
import ExportPanel from '@/components/admin/ExportPanel';
import ContentReviewPanel from '@/components/admin/ContentReviewPanel';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Separator } from '@/components/ui/separator';
import { 
  Search, 
  CheckSquare, 
  Download, 
  FileText, 
  BarChart3,
  Filter,
  Settings,
  RefreshCw,
  Eye,
  EyeOff
} from 'lucide-react';
import { useToast } from '@/components/ui/use-toast';
import {
  AdvancedSearchFilters,
  SearchResult,
  PendingContent,
  advancedSearchContents
} from '@/services/contentManagementService';

/**
 * 增强版内容管理页面
 * 
 * 集成了所有高级功能：
 * - 高级搜索和过滤
 * - 批量操作管理
 * - 数据导出功能
 * - 内容审核面板
 */
const EnhancedContentManagementPage: React.FC = () => {
  const { toast } = useToast();
  const [activeTab, setActiveTab] = useState('search');
  const [isLoading, setIsLoading] = useState(false);
  const [searchResults, setSearchResults] = useState<SearchResult<PendingContent> | null>(null);
  const [currentFilters, setCurrentFilters] = useState<AdvancedSearchFilters>({});
  const [selectedItems, setSelectedItems] = useState<PendingContent[]>([]);
  const [showAdvancedFeatures, setShowAdvancedFeatures] = useState(true);

  // 统计数据
  const [stats, setStats] = useState({
    totalPending: 0,
    totalSelected: 0,
    totalFiltered: 0,
    lastUpdated: new Date().toISOString()
  });

  // 执行高级搜索
  const handleAdvancedSearch = async (filters: AdvancedSearchFilters) => {
    setIsLoading(true);
    setCurrentFilters(filters);
    
    try {
      const response = await advancedSearchContents(filters, {
        page: 1,
        pageSize: 50
      });
      
      if (response.success) {
        setSearchResults(response.data);
        setStats(prev => ({
          ...prev,
          totalFiltered: response.data.pagination.total,
          lastUpdated: new Date().toISOString()
        }));
        
        toast({
          title: '搜索完成',
          description: `找到 ${response.data.pagination.total} 个结果`,
        });
      } else {
        throw new Error(response.error || '搜索失败');
      }
    } catch (error) {
      console.error('高级搜索失败:', error);
      toast({
        title: '搜索失败',
        description: error instanceof Error ? error.message : '执行搜索时发生错误',
        variant: 'destructive',
      });
    } finally {
      setIsLoading(false);
    }
  };

  // 重置搜索
  const handleResetSearch = () => {
    setCurrentFilters({});
    setSearchResults(null);
    setSelectedItems([]);
    setStats(prev => ({
      ...prev,
      totalFiltered: 0,
      totalSelected: 0,
      lastUpdated: new Date().toISOString()
    }));
  };

  // 处理选择变化
  const handleSelectionChange = (items: PendingContent[]) => {
    setSelectedItems(items);
    setStats(prev => ({
      ...prev,
      totalSelected: items.length,
      lastUpdated: new Date().toISOString()
    }));
  };

  // 操作完成后刷新
  const handleOperationComplete = () => {
    // 重新执行搜索以获取最新数据
    if (Object.keys(currentFilters).length > 0) {
      handleAdvancedSearch(currentFilters);
    }
    
    // 清空选择
    setSelectedItems([]);
  };

  // 获取当前显示的内容列表
  const getCurrentItems = (): PendingContent[] => {
    return searchResults?.items || [];
  };

  return (
    <SuperAdminLayout>
      <div className="container mx-auto py-6 space-y-6">
        {/* 页面头部 */}
        <div className="flex justify-between items-start">
          <div>
            <h1 className="text-3xl font-bold">增强版内容管理</h1>
            <p className="text-gray-500 mt-2">
              使用高级搜索、批量操作和导出功能管理内容
            </p>
          </div>
          
          <div className="flex items-center gap-4">
            <Button
              variant="outline"
              size="sm"
              onClick={() => setShowAdvancedFeatures(!showAdvancedFeatures)}
            >
              {showAdvancedFeatures ? <EyeOff className="h-4 w-4 mr-2" /> : <Eye className="h-4 w-4 mr-2" />}
              {showAdvancedFeatures ? '隐藏高级功能' : '显示高级功能'}
            </Button>
            
            <Button
              variant="outline"
              size="sm"
              onClick={() => handleOperationComplete()}
              disabled={isLoading}
            >
              <RefreshCw className={`h-4 w-4 mr-2 ${isLoading ? 'animate-spin' : ''}`} />
              刷新数据
            </Button>
          </div>
        </div>

        {/* 统计概览 */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          <Card>
            <CardContent className="p-4">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-500">待审核总数</p>
                  <p className="text-2xl font-bold">{stats.totalPending}</p>
                </div>
                <FileText className="h-8 w-8 text-blue-500" />
              </div>
            </CardContent>
          </Card>
          
          <Card>
            <CardContent className="p-4">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-500">搜索结果</p>
                  <p className="text-2xl font-bold">{stats.totalFiltered}</p>
                </div>
                <Search className="h-8 w-8 text-green-500" />
              </div>
            </CardContent>
          </Card>
          
          <Card>
            <CardContent className="p-4">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-500">已选择</p>
                  <p className="text-2xl font-bold">{stats.totalSelected}</p>
                </div>
                <CheckSquare className="h-8 w-8 text-orange-500" />
              </div>
            </CardContent>
          </Card>
          
          <Card>
            <CardContent className="p-4">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-500">最后更新</p>
                  <p className="text-sm text-gray-600">
                    {new Date(stats.lastUpdated).toLocaleTimeString()}
                  </p>
                </div>
                <BarChart3 className="h-8 w-8 text-purple-500" />
              </div>
            </CardContent>
          </Card>
        </div>

        {/* 主要功能区域 */}
        {showAdvancedFeatures ? (
          <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
            <TabsList className="grid w-full grid-cols-4">
              <TabsTrigger value="search" className="flex items-center gap-2">
                <Search className="h-4 w-4" />
                高级搜索
              </TabsTrigger>
              <TabsTrigger value="batch" className="flex items-center gap-2">
                <CheckSquare className="h-4 w-4" />
                批量操作
              </TabsTrigger>
              <TabsTrigger value="export" className="flex items-center gap-2">
                <Download className="h-4 w-4" />
                数据导出
              </TabsTrigger>
              <TabsTrigger value="review" className="flex items-center gap-2">
                <FileText className="h-4 w-4" />
                内容审核
              </TabsTrigger>
            </TabsList>

            <TabsContent value="search" className="space-y-6">
              <AdvancedSearchPanel
                onSearch={handleAdvancedSearch}
                onReset={handleResetSearch}
                isLoading={isLoading}
                searchResults={searchResults}
              />
              
              {/* 搜索结果展示 */}
              {searchResults && (
                <Card>
                  <CardHeader>
                    <CardTitle>搜索结果</CardTitle>
                    <CardDescription>
                      共找到 {searchResults.pagination.total} 个结果
                    </CardDescription>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-4">
                      {searchResults.items.map((item) => (
                        <div key={item.id} className="border rounded-lg p-4">
                          <div className="flex items-start justify-between">
                            <div className="flex-1">
                              <div className="flex items-center gap-2 mb-2">
                                <Badge variant="outline">{item.type}</Badge>
                                <Badge variant={
                                  item.status === 'pending' ? 'secondary' :
                                  item.status === 'approved' ? 'default' :
                                  'destructive'
                                }>
                                  {item.status}
                                </Badge>
                                {item.priority && (
                                  <Badge variant="outline">
                                    优先级: {item.priority}
                                  </Badge>
                                )}
                              </div>
                              <p className="text-sm text-gray-600 line-clamp-2">
                                {typeof item.originalContent === 'string' 
                                  ? item.originalContent 
                                  : item.sanitizedContent || '无内容'}
                              </p>
                              <p className="text-xs text-gray-400 mt-2">
                                创建时间: {new Date(item.createdAt).toLocaleString()}
                              </p>
                            </div>
                            <div className="flex items-center gap-2">
                              <input
                                type="checkbox"
                                checked={selectedItems.some(selected => selected.id === item.id)}
                                onChange={(e) => {
                                  if (e.target.checked) {
                                    setSelectedItems([...selectedItems, item]);
                                  } else {
                                    setSelectedItems(selectedItems.filter(selected => selected.id !== item.id));
                                  }
                                }}
                                className="rounded"
                              />
                            </div>
                          </div>
                        </div>
                      ))}
                    </div>
                  </CardContent>
                </Card>
              )}
            </TabsContent>

            <TabsContent value="batch" className="space-y-6">
              <BatchOperationPanel
                selectedItems={selectedItems}
                onSelectionChange={handleSelectionChange}
                allItems={getCurrentItems()}
                onOperationComplete={handleOperationComplete}
              />
            </TabsContent>

            <TabsContent value="export" className="space-y-6">
              <ExportPanel
                currentFilters={currentFilters}
                selectedItems={selectedItems}
              />
            </TabsContent>

            <TabsContent value="review" className="space-y-6">
              <ContentReviewPanel />
            </TabsContent>
          </Tabs>
        ) : (
          // 简化视图，只显示内容审核
          <ContentReviewPanel />
        )}

        {/* 快速操作栏 */}
        {selectedItems.length > 0 && (
          <div className="fixed bottom-6 left-1/2 transform -translate-x-1/2 bg-white border border-gray-200 rounded-lg shadow-lg p-4">
            <div className="flex items-center gap-4">
              <span className="text-sm font-medium">
                已选择 {selectedItems.length} 个项目
              </span>
              <Separator orientation="vertical" className="h-6" />
              <div className="flex gap-2">
                <Button size="sm" onClick={() => setActiveTab('batch')}>
                  批量操作
                </Button>
                <Button size="sm" variant="outline" onClick={() => setActiveTab('export')}>
                  导出选中
                </Button>
                <Button 
                  size="sm" 
                  variant="ghost" 
                  onClick={() => setSelectedItems([])}
                >
                  清空选择
                </Button>
              </div>
            </div>
          </div>
        )}
      </div>
    </SuperAdminLayout>
  );
};

export default EnhancedContentManagementPage;
