/**
 * 错误边界组件
 * 
 * 用于捕获子组件树中的 JavaScript 错误，记录错误并显示备用 UI
 */

import React, { Component } from 'react';
import { Alert, AlertTitle, Button, Typography, Box, Paper } from '@mui/material';
import { ErrorOutline, Refresh } from '@mui/icons-material';

class ErrorBoundary extends Component {
  constructor(props) {
    super(props);
    this.state = { 
      hasError: false,
      error: null,
      errorInfo: null
    };
  }

  static getDerivedStateFromError(error) {
    // 更新 state 使下一次渲染能够显示降级 UI
    return { hasError: true, error };
  }

  componentDidCatch(error, errorInfo) {
    // 你同样可以将错误日志上报给服务器
    console.error('ErrorBoundary caught an error', error, errorInfo);
    this.setState({ errorInfo });
    
    // 如果有错误报告服务，可以在这里上报
    if (this.props.onError) {
      this.props.onError(error, errorInfo);
    }
  }

  handleReset = () => {
    this.setState({ 
      hasError: false,
      error: null,
      errorInfo: null
    });
  }

  handleReload = () => {
    window.location.reload();
  }

  render() {
    if (this.state.hasError) {
      // 你可以自定义降级 UI
      return (
        <Paper 
          elevation={3}
          sx={{ 
            p: 3, 
            m: 2, 
            maxWidth: '800px', 
            mx: 'auto',
            backgroundColor: '#fff8f8',
            border: '1px solid #ffcdd2'
          }}
        >
          <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
            <ErrorOutline color="error" sx={{ fontSize: 32, mr: 1 }} />
            <Typography variant="h5" component="h2" color="error">
              {this.props.fallbackTitle || '出错了'}
            </Typography>
          </Box>
          
          <Alert severity="error" sx={{ mb: 2 }}>
            <AlertTitle>错误信息</AlertTitle>
            {this.state.error && this.state.error.toString()}
          </Alert>
          
          {this.props.showDetails && this.state.errorInfo && (
            <Box sx={{ mt: 2, mb: 2 }}>
              <Typography variant="subtitle1" component="h3" gutterBottom>
                组件堆栈信息:
              </Typography>
              <Box 
                component="pre" 
                sx={{ 
                  p: 2, 
                  backgroundColor: '#f5f5f5', 
                  borderRadius: 1,
                  overflow: 'auto',
                  fontSize: '0.875rem',
                  maxHeight: '200px'
                }}
              >
                {this.state.errorInfo.componentStack}
              </Box>
            </Box>
          )}
          
          <Box sx={{ mt: 3, display: 'flex', gap: 2 }}>
            <Button 
              variant="outlined" 
              color="primary" 
              onClick={this.handleReset}
              startIcon={<Refresh />}
            >
              尝试恢复
            </Button>
            <Button 
              variant="contained" 
              color="primary" 
              onClick={this.handleReload}
            >
              刷新页面
            </Button>
            {this.props.actionButton}
          </Box>
          
          {this.props.children && (
            <Box sx={{ mt: 3, p: 2, backgroundColor: '#f5f5f5', borderRadius: 1 }}>
              <Typography variant="body2" color="textSecondary" gutterBottom>
                备用内容:
              </Typography>
              {this.props.fallback}
            </Box>
          )}
        </Paper>
      );
    }

    // 正常渲染子组件
    return this.props.children;
  }
}

export default ErrorBoundary;
