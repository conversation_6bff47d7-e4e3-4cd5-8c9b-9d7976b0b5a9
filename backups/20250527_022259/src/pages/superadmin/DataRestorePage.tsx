import React, { useState, useEffect } from 'react';
import SuperAdminLayout from '@/components/layouts/SuperAdminLayout';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { useToast } from '@/components/ui/use-toast';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Checkbox } from '@/components/ui/checkbox';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import {
  RefreshCw, Upload, Download, Database, FileText, 
  AlertTriangle, CheckCircle, Clock, Info, Play
} from 'lucide-react';
import {
  Table, TableBody, TableCaption, TableCell, TableHead,
  TableHeader, TableRow
} from '@/components/ui/table';
import {
  Dialog, DialogContent, DialogDescription, DialogFooter,
  Di<PERSON>Header, DialogTitle, DialogTrigger
} from '@/components/ui/dialog';

interface BackupFile {
  id: string;
  name: string;
  type: 'full' | 'incremental' | 'differential';
  size: string;
  createdAt: string;
  status: 'completed' | 'corrupted' | 'partial';
  location: string;
  description: string;
}

/**
 * 数据恢复页面
 * 
 * 提供数据恢复功能，包括备份文件管理和恢复操作
 */
const DataRestorePage: React.FC = () => {
  const { toast } = useToast();
  const [isLoading, setIsLoading] = useState(true);
  const [backups, setBackups] = useState<BackupFile[]>([]);
  const [selectedBackup, setSelectedBackup] = useState<BackupFile | null>(null);
  const [isRestoreDialogOpen, setIsRestoreDialogOpen] = useState(false);
  const [isRestoreInProgress, setIsRestoreInProgress] = useState(false);
  const [restoreProgress, setRestoreProgress] = useState(0);
  const [restoreOptions, setRestoreOptions] = useState({
    restoreDatabase: true,
    restoreFiles: true,
    restoreConfigs: true,
    overwriteExisting: false,
    createBackupBeforeRestore: true
  });

  // 模拟备份数据
  const mockBackups: BackupFile[] = [
    {
      id: '1',
      name: 'Full Backup - 2023-06-20',
      type: 'full',
      size: '1.5 GB',
      createdAt: '2023-06-20 02:00:00',
      status: 'completed',
      location: '本地存储',
      description: '完整系统备份，包含所有数据和配置'
    },
    {
      id: '2',
      name: 'Full Backup - 2023-06-19',
      type: 'full',
      size: '1.4 GB',
      createdAt: '2023-06-19 02:00:00',
      status: 'completed',
      location: '云存储',
      description: '完整系统备份，包含所有数据和配置'
    },
    {
      id: '3',
      name: 'Incremental Backup - 2023-06-18',
      type: 'incremental',
      size: '250 MB',
      createdAt: '2023-06-18 02:00:00',
      status: 'completed',
      location: '本地存储',
      description: '增量备份，包含最新变更'
    },
    {
      id: '4',
      name: 'Full Backup - 2023-06-17',
      type: 'full',
      size: '1.3 GB',
      createdAt: '2023-06-17 02:00:00',
      status: 'partial',
      location: '本地存储',
      description: '部分完成的备份，可能缺少某些文件'
    },
    {
      id: '5',
      name: 'Differential Backup - 2023-06-16',
      type: 'differential',
      size: '450 MB',
      createdAt: '2023-06-16 02:00:00',
      status: 'corrupted',
      location: '云存储',
      description: '备份文件已损坏，无法使用'
    }
  ];

  useEffect(() => {
    // 模拟数据加载
    setTimeout(() => {
      setBackups(mockBackups);
      setIsLoading(false);
    }, 1000);
  }, []);

  const handleRestore = async () => {
    if (!selectedBackup) return;

    setIsRestoreInProgress(true);
    setRestoreProgress(0);

    try {
      // 模拟恢复过程
      for (let i = 0; i <= 100; i += 10) {
        await new Promise(resolve => setTimeout(resolve, 500));
        setRestoreProgress(i);
      }

      toast({
        title: '恢复完成',
        description: `系统已成功恢复到备份点: ${selectedBackup.name}`,
      });

      setIsRestoreDialogOpen(false);
    } catch (error) {
      toast({
        variant: 'destructive',
        title: '恢复失败',
        description: '数据恢复过程中发生错误，请稍后再试',
      });
    } finally {
      setIsRestoreInProgress(false);
      setRestoreProgress(0);
    }
  };

  const getBackupTypeLabel = (type: string) => {
    switch (type) {
      case 'full':
        return '完整备份';
      case 'incremental':
        return '增量备份';
      case 'differential':
        return '差异备份';
      default:
        return type;
    }
  };

  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'completed':
        return <Badge className="bg-green-100 text-green-800">完整</Badge>;
      case 'partial':
        return <Badge className="bg-yellow-100 text-yellow-800">部分</Badge>;
      case 'corrupted':
        return <Badge className="bg-red-100 text-red-800">损坏</Badge>;
      default:
        return <Badge className="bg-gray-100 text-gray-800">{status}</Badge>;
    }
  };

  const canRestore = (backup: BackupFile) => {
    return backup.status === 'completed' || backup.status === 'partial';
  };

  return (
    <SuperAdminLayout>
      <div className="container mx-auto py-6">
        <div className="flex justify-between items-center mb-6">
          <div className="flex items-center">
            <RefreshCw className="h-6 w-6 mr-2" />
            <h1 className="text-2xl font-bold">数据恢复</h1>
          </div>
          <div className="flex items-center space-x-2">
            <Button variant="outline">
              <Upload className="mr-2 h-4 w-4" />
              上传备份文件
            </Button>
            <Button variant="outline">
              <Download className="mr-2 h-4 w-4" />
              下载备份
            </Button>
          </div>
        </div>

        {/* 恢复警告 */}
        <Alert className="mb-6">
          <AlertTriangle className="h-4 w-4" />
          <AlertTitle>重要提醒</AlertTitle>
          <AlertDescription>
            数据恢复操作将覆盖当前系统数据。建议在恢复前创建当前系统的备份，以防止数据丢失。
            请确保选择正确的备份文件，并仔细检查恢复选项。
          </AlertDescription>
        </Alert>

        {/* 备份文件列表 */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <Database className="h-5 w-5 mr-2" />
              可用备份文件
            </CardTitle>
            <CardDescription>
              选择要恢复的备份文件。只有状态为"完整"或"部分"的备份可以用于恢复。
            </CardDescription>
          </CardHeader>
          <CardContent>
            {isLoading ? (
              <div className="flex items-center justify-center py-8">
                <RefreshCw className="h-6 w-6 animate-spin mr-2" />
                加载备份文件...
              </div>
            ) : (
              <Table>
                <TableCaption>
                  共找到 {backups.length} 个备份文件
                </TableCaption>
                <TableHeader>
                  <TableRow>
                    <TableHead>备份名称</TableHead>
                    <TableHead>类型</TableHead>
                    <TableHead>大小</TableHead>
                    <TableHead>创建时间</TableHead>
                    <TableHead>状态</TableHead>
                    <TableHead>位置</TableHead>
                    <TableHead>操作</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {backups.map((backup) => (
                    <TableRow key={backup.id}>
                      <TableCell>
                        <div>
                          <div className="font-medium">{backup.name}</div>
                          <div className="text-sm text-muted-foreground">{backup.description}</div>
                        </div>
                      </TableCell>
                      <TableCell>{getBackupTypeLabel(backup.type)}</TableCell>
                      <TableCell>{backup.size}</TableCell>
                      <TableCell>{backup.createdAt}</TableCell>
                      <TableCell>{getStatusBadge(backup.status)}</TableCell>
                      <TableCell>{backup.location}</TableCell>
                      <TableCell>
                        <Dialog open={isRestoreDialogOpen} onOpenChange={setIsRestoreDialogOpen}>
                          <DialogTrigger asChild>
                            <Button
                              variant="outline"
                              size="sm"
                              disabled={!canRestore(backup)}
                              onClick={() => setSelectedBackup(backup)}
                            >
                              <Play className="mr-2 h-4 w-4" />
                              恢复
                            </Button>
                          </DialogTrigger>
                          <DialogContent className="sm:max-w-[500px]">
                            <DialogHeader>
                              <DialogTitle>确认数据恢复</DialogTitle>
                              <DialogDescription>
                                您即将恢复系统到以下备份点。此操作不可逆，请确认您的选择。
                              </DialogDescription>
                            </DialogHeader>

                            {selectedBackup && (
                              <div className="space-y-4">
                                <div className="p-4 border rounded-lg">
                                  <h4 className="font-medium">{selectedBackup.name}</h4>
                                  <p className="text-sm text-muted-foreground">{selectedBackup.description}</p>
                                  <div className="mt-2 text-sm">
                                    <span className="font-medium">创建时间:</span> {selectedBackup.createdAt}
                                  </div>
                                  <div className="text-sm">
                                    <span className="font-medium">大小:</span> {selectedBackup.size}
                                  </div>
                                </div>

                                <div className="space-y-3">
                                  <Label className="text-base font-medium">恢复选项</Label>
                                  
                                  <div className="flex items-center space-x-2">
                                    <Checkbox
                                      id="restore-database"
                                      checked={restoreOptions.restoreDatabase}
                                      onCheckedChange={(checked) =>
                                        setRestoreOptions(prev => ({ ...prev, restoreDatabase: !!checked }))
                                      }
                                    />
                                    <label htmlFor="restore-database" className="text-sm">
                                      恢复数据库
                                    </label>
                                  </div>

                                  <div className="flex items-center space-x-2">
                                    <Checkbox
                                      id="restore-files"
                                      checked={restoreOptions.restoreFiles}
                                      onCheckedChange={(checked) =>
                                        setRestoreOptions(prev => ({ ...prev, restoreFiles: !!checked }))
                                      }
                                    />
                                    <label htmlFor="restore-files" className="text-sm">
                                      恢复文件
                                    </label>
                                  </div>

                                  <div className="flex items-center space-x-2">
                                    <Checkbox
                                      id="restore-configs"
                                      checked={restoreOptions.restoreConfigs}
                                      onCheckedChange={(checked) =>
                                        setRestoreOptions(prev => ({ ...prev, restoreConfigs: !!checked }))
                                      }
                                    />
                                    <label htmlFor="restore-configs" className="text-sm">
                                      恢复配置文件
                                    </label>
                                  </div>

                                  <div className="flex items-center space-x-2">
                                    <Checkbox
                                      id="create-backup"
                                      checked={restoreOptions.createBackupBeforeRestore}
                                      onCheckedChange={(checked) =>
                                        setRestoreOptions(prev => ({ ...prev, createBackupBeforeRestore: !!checked }))
                                      }
                                    />
                                    <label htmlFor="create-backup" className="text-sm">
                                      恢复前创建当前系统备份
                                    </label>
                                  </div>

                                  <div className="flex items-center space-x-2">
                                    <Checkbox
                                      id="overwrite-existing"
                                      checked={restoreOptions.overwriteExisting}
                                      onCheckedChange={(checked) =>
                                        setRestoreOptions(prev => ({ ...prev, overwriteExisting: !!checked }))
                                      }
                                    />
                                    <label htmlFor="overwrite-existing" className="text-sm">
                                      覆盖现有文件
                                    </label>
                                  </div>
                                </div>

                                {isRestoreInProgress && (
                                  <div className="space-y-2">
                                    <div className="flex items-center justify-between">
                                      <span className="text-sm">恢复进度</span>
                                      <span className="text-sm">{restoreProgress}%</span>
                                    </div>
                                    <Progress value={restoreProgress} className="h-2" />
                                  </div>
                                )}
                              </div>
                            )}

                            <DialogFooter>
                              <Button
                                variant="outline"
                                onClick={() => setIsRestoreDialogOpen(false)}
                                disabled={isRestoreInProgress}
                              >
                                取消
                              </Button>
                              <Button
                                onClick={handleRestore}
                                disabled={isRestoreInProgress}
                              >
                                {isRestoreInProgress ? (
                                  <>
                                    <RefreshCw className="mr-2 h-4 w-4 animate-spin" />
                                    恢复中...
                                  </>
                                ) : (
                                  <>
                                    <Play className="mr-2 h-4 w-4" />
                                    开始恢复
                                  </>
                                )}
                              </Button>
                            </DialogFooter>
                          </DialogContent>
                        </Dialog>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            )}
          </CardContent>
        </Card>
      </div>
    </SuperAdminLayout>
  );
};

export default DataRestorePage;
