import { useState, useEffect } from 'react';

/**
 * 自定义钩子，用于检测媒体查询
 * @param query 媒体查询字符串，例如 '(max-width: 768px)'
 * @returns 布尔值，表示媒体查询是否匹配
 */
export function useMediaQuery(query: string): boolean {
  // 初始状态基于当前窗口
  const getMatches = (): boolean => {
    // 在服务器端渲染时，没有 window 对象
    if (typeof window !== 'undefined') {
      return window.matchMedia(query).matches;
    }
    return false;
  };

  const [matches, setMatches] = useState<boolean>(getMatches());

  // 监听媒体查询变化
  useEffect(() => {
    // 获取媒体查询对象
    const mediaQuery = window.matchMedia(query);
    
    // 定义回调函数
    const handleChange = () => {
      setMatches(mediaQuery.matches);
    };

    // 添加监听器
    if (mediaQuery.addEventListener) {
      mediaQuery.addEventListener('change', handleChange);
    } else {
      // 兼容旧版浏览器
      mediaQuery.addListener(handleChange);
    }

    // 初始检查
    handleChange();

    // 清理函数
    return () => {
      if (mediaQuery.removeEventListener) {
        mediaQuery.removeEventListener('change', handleChange);
      } else {
        // 兼容旧版浏览器
        mediaQuery.removeListener(handleChange);
      }
    };
  }, [query]);

  return matches;
}

/**
 * 预定义的媒体查询断点
 */
export const breakpoints = {
  sm: '(min-width: 640px)',
  md: '(min-width: 768px)',
  lg: '(min-width: 1024px)',
  xl: '(min-width: 1280px)',
  '2xl': '(min-width: 1536px)',
  
  // 移动优先的断点
  mobile: '(max-width: 767px)',
  tablet: '(min-width: 768px) and (max-width: 1023px)',
  desktop: '(min-width: 1024px)',
  
  // 方向断点
  portrait: '(orientation: portrait)',
  landscape: '(orientation: landscape)',
  
  // 深色模式
  dark: '(prefers-color-scheme: dark)',
  light: '(prefers-color-scheme: light)',
  
  // 减少动画
  reducedMotion: '(prefers-reduced-motion: reduce)',
};

/**
 * 使用预定义断点的媒体查询钩子
 * @param breakpoint 预定义断点名称
 * @returns 布尔值，表示媒体查询是否匹配
 */
export function useBreakpoint(breakpoint: keyof typeof breakpoints): boolean {
  return useMediaQuery(breakpoints[breakpoint]);
}

export default useMediaQuery;
