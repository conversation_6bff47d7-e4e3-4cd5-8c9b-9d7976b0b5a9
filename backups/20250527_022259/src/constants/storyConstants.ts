/**
 * 故事墙相关常量定义
 */

// 故事标签定义
export const STORY_TAGS = [
  { id: 'all', label: '全部' },
  
  // 求职相关标签 (job, blue)
  { id: 'job-hunting', label: '求职故事', category: 'job', color: 'blue' },
  { id: 'interview', label: '面试经验', category: 'job', color: 'blue' },
  { id: 'resume', label: '简历技巧', category: 'job', color: 'blue' },
  { id: 'job-search', label: '找工作', category: 'job', color: 'blue' },
  { id: 'salary', label: '薪资谈判', category: 'job', color: 'blue' },
  { id: 'offer', label: 'Offer选择', category: 'job', color: 'blue' },

  // 学历相关标签 (education, green)
  { id: 'bachelor', label: '本科经验', category: 'education', color: 'green' },
  { id: 'master', label: '硕士经验', category: 'education', color: 'green' },
  { id: 'phd', label: '博士经验', category: 'education', color: 'green' },
  { id: 'overseas-edu', label: '海外学历', category: 'education', color: 'green' },
  { id: 'continuing-edu', label: '继续教育', category: 'education', color: 'green' },
  { id: 'self-taught', label: '自学成才', category: 'education', color: 'green' },

  // 行业相关标签 (industry, purple)
  { id: 'it-industry', label: 'IT行业', category: 'industry', color: 'purple' },
  { id: 'finance', label: '金融行业', category: 'industry', color: 'purple' },
  { id: 'education-industry', label: '教育行业', category: 'industry', color: 'purple' },
  { id: 'healthcare', label: '医疗行业', category: 'industry', color: 'purple' },
  { id: 'manufacturing', label: '制造业', category: 'industry', color: 'purple' },
  { id: 'service', label: '服务业', category: 'industry', color: 'purple' },

  // 经验相关标签 (experience, yellow)
  { id: 'career-change', label: '转行经历', category: 'experience', color: 'yellow' },
  { id: 'work-life', label: '工作生活', category: 'experience', color: 'yellow' },
  { id: 'advice', label: '建议分享', category: 'experience', color: 'yellow' },
  { id: 'internship', label: '实习经历', category: 'experience', color: 'yellow' },
  { id: 'overseas', label: '海外就业', category: 'experience', color: 'yellow' },
  { id: 'startup', label: '创业经历', category: 'experience', color: 'yellow' },
  { id: 'remote-work', label: '远程工作', category: 'experience', color: 'yellow' },
  { id: 'freelance', label: '自由职业', category: 'experience', color: 'yellow' },

  // 其他标签 (other, gray)
  { id: 'success', label: '成功故事', category: 'other', color: 'gray' },
  { id: 'challenge', label: '挑战经历', category: 'other', color: 'gray' },
  { id: 'inspiration', label: '励志故事', category: 'other', color: 'gray' },
];

// 故事分类
export const STORY_CATEGORIES = [
  { id: 'all', label: '全部分类' },
  { id: 'success', label: '成功故事' },
  { id: 'challenge', label: '挑战经历' },
  { id: 'advice', label: '建议分享' },
  { id: 'experience', label: '经验分享' },
];

// 教育水平
export const EDUCATION_LEVELS = [
  { id: 'all', label: '全部学历' },
  { id: 'high-school', label: '高中及以下' },
  { id: 'college', label: '专科' },
  { id: 'bachelor', label: '本科' },
  { id: 'master', label: '硕士' },
  { id: 'phd', label: '博士' },
];

// 行业领域
export const INDUSTRIES = [
  { id: 'all', label: '全部行业' },
  { id: 'it', label: '互联网/IT' },
  { id: 'finance', label: '金融' },
  { id: 'education', label: '教育' },
  { id: 'healthcare', label: '医疗' },
  { id: 'manufacturing', label: '制造业' },
  { id: 'service', label: '服务业' },
  { id: 'government', label: '政府/公共服务' },
  { id: 'media', label: '媒体/文化' },
];

// 热门搜索词
export const POPULAR_SEARCHES = [
  '实习经验',
  '面试技巧',
  '薪资谈判',
  '职业规划',
  '转行经历',
  '海外就业',
];

// 标签分类标签
export const TAG_CATEGORY_LABELS: Record<string, string> = {
  'job': '求职相关',
  'education': '学历相关',
  'industry': '行业相关',
  'experience': '经验相关',
  'other': '其他标签'
};

// 标签颜色映射
export const TAG_CATEGORY_COLORS: Record<string, string> = {
  'job': 'blue',
  'education': 'green',
  'industry': 'purple',
  'experience': 'yellow',
  'other': 'gray'
};

// 类型定义
export interface Tag {
  id: string;
  label: string;
  category?: string;
  color?: string;
}

export interface Category {
  id: string;
  label: string;
}

export interface TagWithCount extends Tag {
  count: number;
}
