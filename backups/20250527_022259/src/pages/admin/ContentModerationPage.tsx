import React, { useState } from 'react';
import AdminLayout from '@/components/layouts/AdminLayout';
import { Tabs, TabsContent, <PERSON><PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Textarea } from '@/components/ui/textarea';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import { useToast } from '@/components/ui/use-toast';
import { Shield, AlertTriangle, FileText, BarChart, History, TrendingUp, User } from 'lucide-react';
import ContentModerationPanel from '@/components/admin/ContentModerationPanel';
import ModerationHistoryTable from '@/components/admin/ModerationHistoryTable';
import ModerationStatsDashboard from '@/components/admin/ModerationStatsDashboard';
import ReviewerPerformanceDashboard from '@/components/admin/ReviewerPerformanceDashboard';
import ContentQualityTrendsDashboard from '@/components/admin/ContentQualityTrendsDashboard';
import { ContentModerationResult } from '@/hooks/useContentModeration';

/**
 * 内容审核管理页面
 *
 * 用于管理员进行内容审核、查看审核历史和统计数据
 */
const ContentModerationPage: React.FC = () => {
  const { toast } = useToast();
  const [activeTab, setActiveTab] = useState<string>('moderate');
  const [content, setContent] = useState<string>('');
  const [contentType, setContentType] = useState<string>('story');
  const [moderationResult, setModerationResult] = useState<ContentModerationResult | null>(null);

  // 处理内容审核结果
  const handleModerationResult = (result: ContentModerationResult) => {
    setModerationResult(result);

    // 显示通知
    if (result.suggestedAction === 'approve') {
      toast({
        title: '内容审核完成',
        description: '内容安全，建议通过',
        variant: 'default',
      });
    } else if (result.suggestedAction === 'reject') {
      toast({
        title: '内容审核完成',
        description: '内容可能不适合发布，建议拒绝',
        variant: 'destructive',
      });
    } else {
      toast({
        title: '内容审核完成',
        description: '内容需要人工审核',
        variant: 'warning',
      });
    }
  };

  return (
    <AdminLayout>
      <div className="container mx-auto py-6">
        <div className="flex items-center justify-between mb-6">
          <div>
            <h1 className="text-3xl font-bold">内容审核管理</h1>
            <p className="text-muted-foreground">
              使用AI审核内容、查看审核历史和统计数据
            </p>
          </div>
        </div>

        <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
          <TabsList className="grid w-full grid-cols-5">
            <TabsTrigger value="moderate">
              <Shield className="w-4 h-4 mr-2" />
              内容审核
            </TabsTrigger>
            <TabsTrigger value="history">
              <History className="w-4 h-4 mr-2" />
              审核历史
            </TabsTrigger>
            <TabsTrigger value="stats">
              <BarChart className="w-4 h-4 mr-2" />
              审核统计
            </TabsTrigger>
            <TabsTrigger value="reviewer">
              <User className="w-4 h-4 mr-2" />
              审核员绩效
            </TabsTrigger>
            <TabsTrigger value="trends">
              <TrendingUp className="w-4 h-4 mr-2" />
              质量趋势
            </TabsTrigger>
          </TabsList>

          <TabsContent value="moderate" className="mt-6">
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              <div className="md:col-span-2">
                <ContentModerationPanel
                  onResult={handleModerationResult}
                />
              </div>

              <div>
                <Card>
                  <CardHeader>
                    <CardTitle className="flex items-center">
                      <FileText className="w-5 h-5 mr-2" />
                      审核指南
                    </CardTitle>
                    <CardDescription>
                      内容审核的最佳实践和注意事项
                    </CardDescription>
                  </CardHeader>
                  <CardContent>
                    <Alert className="mb-4">
                      <AlertTriangle className="h-4 w-4" />
                      <AlertTitle>注意</AlertTitle>
                      <AlertDescription>
                        AI审核结果仅供参考，最终决定应由人工审核员做出。
                      </AlertDescription>
                    </Alert>

                    <h3 className="text-sm font-medium mb-2">审核内容类型</h3>
                    <ul className="list-disc list-inside text-sm space-y-1 mb-4">
                      <li>故事：用户分享的就业故事</li>
                      <li>问卷：用户提交的问卷回复</li>
                      <li>评论：用户对故事的评论</li>
                      <li>个人资料：用户的个人信息</li>
                      <li>反馈：用户提交的系统反馈</li>
                    </ul>

                    <h3 className="text-sm font-medium mb-2">审核标准</h3>
                    <ul className="list-disc list-inside text-sm space-y-1">
                      <li>政治敏感内容</li>
                      <li>暴力或仇恨言论</li>
                      <li>色情或不适当的性内容</li>
                      <li>个人隐私信息</li>
                      <li>广告或垃圾信息</li>
                      <li>其他不适合公开发布的内容</li>
                    </ul>
                  </CardContent>
                </Card>
              </div>
            </div>
          </TabsContent>

          <TabsContent value="history" className="mt-6">
            <ModerationHistoryTable />
          </TabsContent>

          <TabsContent value="stats" className="mt-6">
            <ModerationStatsDashboard />
          </TabsContent>

          <TabsContent value="reviewer" className="mt-6">
            <ReviewerPerformanceDashboard />
          </TabsContent>

          <TabsContent value="trends" className="mt-6">
            <ContentQualityTrendsDashboard />
          </TabsContent>
        </Tabs>
      </div>
    </AdminLayout>
  );
};

export default ContentModerationPage;
