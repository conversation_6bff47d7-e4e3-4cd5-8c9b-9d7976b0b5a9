import React, { useState, useEffect } from 'react';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import { Ta<PERSON>, <PERSON>bs<PERSON>ontent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Switch } from '@/components/ui/switch';
import { Slider } from '@/components/ui/slider';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import { useToast } from '@/components/ui/use-toast';
import { 
  Shield, 
  Save, 
  RefreshCw,
  AlertTriangle,
  CheckCircle,
  XCircle,
  Settings,
  Bell
} from 'lucide-react';
import { useApi } from '@/hooks/useApi';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import * as z from 'zod';

// 自动审核配置验证模式
const autoModerationConfigSchema = z.object({
  enabled: z.boolean().default(true),
  confidenceThreshold: z.number().min(0).max(1).default(0.7),
  autoApproveThreshold: z.number().min(0).max(1).default(0.9),
  autoRejectThreshold: z.number().min(0).max(1).default(0.9),
  severityThresholds: z.object({
    low: z.number().min(0).max(1).default(0.7),
    medium: z.number().min(0).max(1).default(0.8),
    high: z.number().min(0).max(1).default(0.9)
  }),
  notifyAdmin: z.boolean().default(true),
  notifyUser: z.boolean().default(true),
  logResults: z.boolean().default(true)
});

// 自动审核配置类型
type AutoModerationConfig = z.infer<typeof autoModerationConfigSchema>;

// 自动审核测试验证模式
const autoModerationTestSchema = z.object({
  content: z.string().min(1, '内容不能为空'),
  contentType: z.enum(['story', 'questionnaire', 'comment', 'profile', 'feedback'])
});

// 自动审核测试类型
type AutoModerationTest = z.infer<typeof autoModerationTestSchema>;

interface AutoModerationConfigPanelProps {
  className?: string;
}

/**
 * 自动审核配置面板组件
 * 
 * 用于配置和测试自动审核功能
 */
const AutoModerationConfigPanel: React.FC<AutoModerationConfigPanelProps> = ({ className }) => {
  const { toast } = useToast();
  const api = useApi();
  const [isLoading, setIsLoading] = useState<boolean>(true);
  const [isSaving, setIsSaving] = useState<boolean>(false);
  const [isTesting, setIsTesting] = useState<boolean>(false);
  const [testResult, setTestResult] = useState<any>(null);
  
  // 创建表单
  const form = useForm<AutoModerationConfig>({
    resolver: zodResolver(autoModerationConfigSchema),
    defaultValues: {
      enabled: true,
      confidenceThreshold: 0.7,
      autoApproveThreshold: 0.9,
      autoRejectThreshold: 0.9,
      severityThresholds: {
        low: 0.7,
        medium: 0.8,
        high: 0.9
      },
      notifyAdmin: true,
      notifyUser: true,
      logResults: true
    }
  });
  
  // 创建测试表单
  const testForm = useForm<AutoModerationTest>({
    resolver: zodResolver(autoModerationTestSchema),
    defaultValues: {
      content: '',
      contentType: 'story'
    }
  });
  
  // 加载配置
  const loadConfig = async () => {
    setIsLoading(true);
    
    try {
      // 发送请求
      const response = await api.get('/admin/auto-moderation/config');
      
      if (response.success) {
        // 更新表单值
        form.reset(response.config);
      } else {
        toast({
          title: '加载失败',
          description: response.error || '无法加载自动审核配置',
          variant: 'destructive'
        });
      }
    } catch (error) {
      console.error('加载自动审核配置失败:', error);
      toast({
        title: '加载失败',
        description: '服务器错误，请稍后再试',
        variant: 'destructive'
      });
    } finally {
      setIsLoading(false);
    }
  };
  
  // 保存配置
  const saveConfig = async (values: AutoModerationConfig) => {
    setIsSaving(true);
    
    try {
      // 发送请求
      const response = await api.post('/admin/auto-moderation/config', values);
      
      if (response.success) {
        toast({
          title: '保存成功',
          description: '自动审核配置已更新',
          variant: 'default'
        });
        
        // 更新表单值
        form.reset(response.config);
      } else {
        toast({
          title: '保存失败',
          description: response.error || '无法保存自动审核配置',
          variant: 'destructive'
        });
      }
    } catch (error) {
      console.error('保存自动审核配置失败:', error);
      toast({
        title: '保存失败',
        description: '服务器错误，请稍后再试',
        variant: 'destructive'
      });
    } finally {
      setIsSaving(false);
    }
  };
  
  // 重置配置
  const resetConfig = async () => {
    setIsLoading(true);
    
    try {
      // 发送请求
      const response = await api.post('/admin/auto-moderation/config/reset');
      
      if (response.success) {
        toast({
          title: '重置成功',
          description: '自动审核配置已重置为默认值',
          variant: 'default'
        });
        
        // 更新表单值
        form.reset(response.config);
      } else {
        toast({
          title: '重置失败',
          description: response.error || '无法重置自动审核配置',
          variant: 'destructive'
        });
      }
    } catch (error) {
      console.error('重置自动审核配置失败:', error);
      toast({
        title: '重置失败',
        description: '服务器错误，请稍后再试',
        variant: 'destructive'
      });
    } finally {
      setIsLoading(false);
    }
  };
  
  // 测试自动审核
  const testAutoModeration = async (values: AutoModerationTest) => {
    setIsTesting(true);
    setTestResult(null);
    
    try {
      // 构建测试内容
      let testContent: any = { content: values.content };
      
      // 根据内容类型添加必要的字段
      switch (values.contentType) {
        case 'story':
          testContent = {
            title: '测试标题',
            content: values.content,
            isAnonymous: true
          };
          break;
        case 'questionnaire':
          testContent = {
            graduationYear: 2023,
            major: '计算机科学',
            educationLevel: '本科',
            employmentStatus: 'employed',
            jobTitle: '软件工程师',
            adviceForStudents: values.content
          };
          break;
        case 'comment':
          testContent = {
            content: values.content,
            parentId: '123',
            parentType: 'story'
          };
          break;
        case 'profile':
          testContent = {
            username: '测试用户',
            bio: values.content
          };
          break;
        case 'feedback':
          testContent = {
            content: values.content,
            category: '功能建议'
          };
          break;
      }
      
      // 发送请求
      const response = await api.post('/admin/auto-moderation/test', {
        content: testContent,
        contentType: values.contentType
      });
      
      if (response.success) {
        setTestResult(response.result);
      } else {
        toast({
          title: '测试失败',
          description: response.error || '无法测试自动审核',
          variant: 'destructive'
        });
      }
    } catch (error) {
      console.error('测试自动审核失败:', error);
      toast({
        title: '测试失败',
        description: '服务器错误，请稍后再试',
        variant: 'destructive'
      });
    } finally {
      setIsTesting(false);
    }
  };
  
  // 组件加载时加载配置
  useEffect(() => {
    loadConfig();
  }, []);
  
  // 渲染操作建议标签
  const renderActionBadge = (action: string) => {
    switch (action) {
      case 'approve':
        return (
          <div className="flex items-center text-green-600">
            <CheckCircle className="w-4 h-4 mr-1" />
            通过
          </div>
        );
      case 'reject':
        return (
          <div className="flex items-center text-red-600">
            <XCircle className="w-4 h-4 mr-1" />
            拒绝
          </div>
        );
      case 'review':
        return (
          <div className="flex items-center text-yellow-600">
            <AlertTriangle className="w-4 h-4 mr-1" />
            人工审核
          </div>
        );
      default:
        return action;
    }
  };
  
  return (
    <Card className={className}>
      <CardHeader>
        <CardTitle className="flex items-center">
          <Shield className="w-5 h-5 mr-2" />
          自动审核配置
        </CardTitle>
        <CardDescription>
          配置自动审核功能的参数和行为
        </CardDescription>
      </CardHeader>
      <CardContent>
        <Tabs defaultValue="config" className="w-full">
          <TabsList className="grid w-full grid-cols-2">
            <TabsTrigger value="config">
              <Settings className="w-4 h-4 mr-2" />
              配置
            </TabsTrigger>
            <TabsTrigger value="test">
              <AlertTriangle className="w-4 h-4 mr-2" />
              测试
            </TabsTrigger>
          </TabsList>
          
          <TabsContent value="config" className="mt-4">
            {isLoading ? (
              <div className="flex items-center justify-center py-8">
                <RefreshCw className="w-6 h-6 animate-spin text-primary" />
              </div>
            ) : (
              <Form {...form}>
                <form onSubmit={form.handleSubmit(saveConfig)} className="space-y-6">
                  <FormField
                    control={form.control}
                    name="enabled"
                    render={({ field }) => (
                      <FormItem className="flex flex-row items-center justify-between rounded-lg border p-4">
                        <div className="space-y-0.5">
                          <FormLabel className="text-base">启用自动审核</FormLabel>
                          <FormDescription>
                            开启后，系统将自动审核新提交的内容
                          </FormDescription>
                        </div>
                        <FormControl>
                          <Switch
                            checked={field.value}
                            onCheckedChange={field.onChange}
                          />
                        </FormControl>
                      </FormItem>
                    )}
                  />
                  
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <FormField
                      control={form.control}
                      name="confidenceThreshold"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>置信度阈值</FormLabel>
                          <FormDescription>
                            低于此值的审核结果将被标记为需要人工审核
                          </FormDescription>
                          <FormControl>
                            <div className="space-y-2">
                              <Slider
                                value={[field.value]}
                                min={0}
                                max={1}
                                step={0.01}
                                onValueChange={(value) => field.onChange(value[0])}
                              />
                              <div className="flex justify-between">
                                <span>{Math.round(field.value * 100)}%</span>
                                <Input
                                  type="number"
                                  value={field.value}
                                  onChange={(e) => field.onChange(parseFloat(e.target.value))}
                                  min={0}
                                  max={1}
                                  step={0.01}
                                  className="w-20"
                                />
                              </div>
                            </div>
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                    
                    <FormField
                      control={form.control}
                      name="autoApproveThreshold"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>自动通过阈值</FormLabel>
                          <FormDescription>
                            高于此值的安全内容将被自动通过
                          </FormDescription>
                          <FormControl>
                            <div className="space-y-2">
                              <Slider
                                value={[field.value]}
                                min={0}
                                max={1}
                                step={0.01}
                                onValueChange={(value) => field.onChange(value[0])}
                              />
                              <div className="flex justify-between">
                                <span>{Math.round(field.value * 100)}%</span>
                                <Input
                                  type="number"
                                  value={field.value}
                                  onChange={(e) => field.onChange(parseFloat(e.target.value))}
                                  min={0}
                                  max={1}
                                  step={0.01}
                                  className="w-20"
                                />
                              </div>
                            </div>
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                    
                    <FormField
                      control={form.control}
                      name="autoRejectThreshold"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>自动拒绝阈值</FormLabel>
                          <FormDescription>
                            高于此值的不安全内容将被自动拒绝
                          </FormDescription>
                          <FormControl>
                            <div className="space-y-2">
                              <Slider
                                value={[field.value]}
                                min={0}
                                max={1}
                                step={0.01}
                                onValueChange={(value) => field.onChange(value[0])}
                              />
                              <div className="flex justify-between">
                                <span>{Math.round(field.value * 100)}%</span>
                                <Input
                                  type="number"
                                  value={field.value}
                                  onChange={(e) => field.onChange(parseFloat(e.target.value))}
                                  min={0}
                                  max={1}
                                  step={0.01}
                                  className="w-20"
                                />
                              </div>
                            </div>
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  </div>
                  
                  <div className="space-y-4">
                    <h3 className="text-lg font-medium">严重程度阈值</h3>
                    <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                      <FormField
                        control={form.control}
                        name="severityThresholds.low"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>低严重度阈值</FormLabel>
                            <FormControl>
                              <div className="space-y-2">
                                <Slider
                                  value={[field.value]}
                                  min={0}
                                  max={1}
                                  step={0.01}
                                  onValueChange={(value) => field.onChange(value[0])}
                                />
                                <div className="flex justify-between">
                                  <span>{Math.round(field.value * 100)}%</span>
                                  <Input
                                    type="number"
                                    value={field.value}
                                    onChange={(e) => field.onChange(parseFloat(e.target.value))}
                                    min={0}
                                    max={1}
                                    step={0.01}
                                    className="w-20"
                                  />
                                </div>
                              </div>
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                      
                      <FormField
                        control={form.control}
                        name="severityThresholds.medium"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>中严重度阈值</FormLabel>
                            <FormControl>
                              <div className="space-y-2">
                                <Slider
                                  value={[field.value]}
                                  min={0}
                                  max={1}
                                  step={0.01}
                                  onValueChange={(value) => field.onChange(value[0])}
                                />
                                <div className="flex justify-between">
                                  <span>{Math.round(field.value * 100)}%</span>
                                  <Input
                                    type="number"
                                    value={field.value}
                                    onChange={(e) => field.onChange(parseFloat(e.target.value))}
                                    min={0}
                                    max={1}
                                    step={0.01}
                                    className="w-20"
                                  />
                                </div>
                              </div>
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                      
                      <FormField
                        control={form.control}
                        name="severityThresholds.high"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>高严重度阈值</FormLabel>
                            <FormControl>
                              <div className="space-y-2">
                                <Slider
                                  value={[field.value]}
                                  min={0}
                                  max={1}
                                  step={0.01}
                                  onValueChange={(value) => field.onChange(value[0])}
                                />
                                <div className="flex justify-between">
                                  <span>{Math.round(field.value * 100)}%</span>
                                  <Input
                                    type="number"
                                    value={field.value}
                                    onChange={(e) => field.onChange(parseFloat(e.target.value))}
                                    min={0}
                                    max={1}
                                    step={0.01}
                                    className="w-20"
                                  />
                                </div>
                              </div>
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                    </div>
                  </div>
                  
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                    <FormField
                      control={form.control}
                      name="notifyAdmin"
                      render={({ field }) => (
                        <FormItem className="flex flex-row items-center justify-between rounded-lg border p-4">
                          <div className="space-y-0.5">
                            <FormLabel>通知管理员</FormLabel>
                            <FormDescription>
                              审核结果通知管理员
                            </FormDescription>
                          </div>
                          <FormControl>
                            <Switch
                              checked={field.value}
                              onCheckedChange={field.onChange}
                            />
                          </FormControl>
                        </FormItem>
                      )}
                    />
                    
                    <FormField
                      control={form.control}
                      name="notifyUser"
                      render={({ field }) => (
                        <FormItem className="flex flex-row items-center justify-between rounded-lg border p-4">
                          <div className="space-y-0.5">
                            <FormLabel>通知用户</FormLabel>
                            <FormDescription>
                              审核结果通知提交用户
                            </FormDescription>
                          </div>
                          <FormControl>
                            <Switch
                              checked={field.value}
                              onCheckedChange={field.onChange}
                            />
                          </FormControl>
                        </FormItem>
                      )}
                    />
                    
                    <FormField
                      control={form.control}
                      name="logResults"
                      render={({ field }) => (
                        <FormItem className="flex flex-row items-center justify-between rounded-lg border p-4">
                          <div className="space-y-0.5">
                            <FormLabel>记录结果</FormLabel>
                            <FormDescription>
                              记录审核结果到历史记录
                            </FormDescription>
                          </div>
                          <FormControl>
                            <Switch
                              checked={field.value}
                              onCheckedChange={field.onChange}
                            />
                          </FormControl>
                        </FormItem>
                      )}
                    />
                  </div>
                  
                  <div className="flex justify-between">
                    <Button
                      type="button"
                      variant="outline"
                      onClick={resetConfig}
                      disabled={isLoading || isSaving}
                    >
                      重置为默认值
                    </Button>
                    <Button
                      type="submit"
                      disabled={isLoading || isSaving}
                    >
                      {isSaving ? (
                        <>
                          <RefreshCw className="w-4 h-4 mr-2 animate-spin" />
                          保存中...
                        </>
                      ) : (
                        <>
                          <Save className="w-4 h-4 mr-2" />
                          保存配置
                        </>
                      )}
                    </Button>
                  </div>
                </form>
              </Form>
            )}
          </TabsContent>
          
          <TabsContent value="test" className="mt-4">
            <div className="space-y-6">
              <Alert>
                <AlertTriangle className="h-4 w-4" />
                <AlertTitle>测试说明</AlertTitle>
                <AlertDescription>
                  此功能用于测试自动审核效果，不会实际提交内容。测试结果仅供参考，实际审核可能会有所不同。
                </AlertDescription>
              </Alert>
              
              <form onSubmit={testForm.handleSubmit(testAutoModeration)} className="space-y-4">
                <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
                  <div className="md:col-span-3">
                    <label className="text-sm font-medium mb-1 block">测试内容</label>
                    <Textarea
                      placeholder="输入要测试的内容..."
                      className="min-h-[100px]"
                      {...testForm.register('content')}
                    />
                    {testForm.formState.errors.content && (
                      <p className="text-sm text-red-500 mt-1">
                        {testForm.formState.errors.content.message}
                      </p>
                    )}
                  </div>
                  
                  <div>
                    <label className="text-sm font-medium mb-1 block">内容类型</label>
                    <select
                      className="w-full rounded-md border border-input bg-background px-3 py-2"
                      {...testForm.register('contentType')}
                    >
                      <option value="story">故事</option>
                      <option value="questionnaire">问卷</option>
                      <option value="comment">评论</option>
                      <option value="profile">个人资料</option>
                      <option value="feedback">反馈</option>
                    </select>
                  </div>
                </div>
                
                <div className="flex justify-end">
                  <Button
                    type="submit"
                    disabled={isTesting}
                  >
                    {isTesting ? (
                      <>
                        <RefreshCw className="w-4 h-4 mr-2 animate-spin" />
                        测试中...
                      </>
                    ) : (
                      <>
                        <AlertTriangle className="w-4 h-4 mr-2" />
                        测试审核
                      </>
                    )}
                  </Button>
                </div>
              </form>
              
              {testResult && (
                <div className="mt-6 border rounded-lg p-4">
                  <h3 className="text-lg font-medium mb-4">测试结果</h3>
                  
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <h4 className="text-sm font-medium mb-1">执行操作</h4>
                      <div className="text-lg font-semibold">
                        {renderActionBadge(testResult.action)}
                      </div>
                    </div>
                    
                    <div>
                      <h4 className="text-sm font-medium mb-1">内容安全性</h4>
                      <div className="text-lg font-semibold">
                        {testResult.moderationResult.isSafe ? (
                          <div className="flex items-center text-green-600">
                            <CheckCircle className="w-4 h-4 mr-1" />
                            安全
                          </div>
                        ) : (
                          <div className="flex items-center text-red-600">
                            <XCircle className="w-4 h-4 mr-1" />
                            不安全
                          </div>
                        )}
                      </div>
                    </div>
                    
                    <div>
                      <h4 className="text-sm font-medium mb-1">置信度</h4>
                      <div className="text-lg font-semibold">
                        {Math.round(testResult.moderationResult.confidence * 100)}%
                      </div>
                    </div>
                    
                    <div>
                      <h4 className="text-sm font-medium mb-1">建议操作</h4>
                      <div className="text-lg font-semibold">
                        {renderActionBadge(testResult.moderationResult.suggestedAction)}
                      </div>
                    </div>
                    
                    {testResult.moderationResult.issues.length > 0 && (
                      <div className="md:col-span-2">
                        <h4 className="text-sm font-medium mb-1">发现的问题</h4>
                        <ul className="list-disc list-inside">
                          {testResult.moderationResult.issues.map((issue: string, index: number) => (
                            <li key={index} className="text-sm">{issue}</li>
                          ))}
                        </ul>
                      </div>
                    )}
                    
                    <div className="md:col-span-2">
                      <h4 className="text-sm font-medium mb-1">解释</h4>
                      <p className="text-sm">{testResult.moderationResult.explanation}</p>
                    </div>
                  </div>
                </div>
              )}
            </div>
          </TabsContent>
        </Tabs>
      </CardContent>
    </Card>
  );
};

export default AutoModerationConfigPanel;
