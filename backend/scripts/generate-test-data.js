/**
 * 测试数据生成脚本
 * 
 * 此脚本用于生成测试数据并将其写入数据库，包括：
 * - 普通用户（50个，其中30个拥有UUID）
 * - 审核员（10个）
 * - 管理员（3个）
 * - 问卷（50份，未审核状态）
 * - 故事墙内容（50条，未审核状态）
 */

const { v4: uuidv4 } = require('uuid');
const bcrypt = require('bcryptjs');
const { PrismaClient } = require('@prisma/client');
const prisma = new PrismaClient();

// 配置项
const CONFIG = {
  normalUsers: 50,
  usersWithUuid: 30,
  reviewers: 10,
  admins: 3,
  questionnaires: 50,
  stories: 50,
  salt: 10, // bcrypt salt rounds
};

// 用户名和密码生成
const generateCredentials = (prefix, index) => {
  return {
    username: `${prefix}${index}`,
    password: bcrypt.hashSync(`${prefix}${index}123`, CONFIG.salt),
  };
};

// 生成普通用户数据
const generateNormalUsers = async () => {
  console.log('生成普通用户数据...');
  const users = [];

  for (let i = 1; i <= CONFIG.normalUsers; i++) {
    const { username, password } = generateCredentials('user', i);
    const hasUuid = i <= CONFIG.usersWithUuid;
    
    users.push({
      username,
      password,
      email: `user${i}@example.com`,
      role: 'USER',
      status: 'ACTIVE',
      uuid: hasUuid ? uuidv4() : null,
      createdAt: new Date(Date.now() - Math.floor(Math.random() * 30) * 24 * 60 * 60 * 1000),
      profile: {
        create: {
          fullName: `测试用户 ${i}`,
          gender: Math.random() > 0.5 ? 'MALE' : 'FEMALE',
          age: 18 + Math.floor(Math.random() * 40),
          education: ['HIGH_SCHOOL', 'BACHELOR', 'MASTER', 'PHD'][Math.floor(Math.random() * 4)],
          major: ['计算机科学', '电子工程', '机械工程', '经济学', '管理学', '医学', '法学', '文学'][Math.floor(Math.random() * 8)],
          graduationYear: 2010 + Math.floor(Math.random() * 14),
          province: ['北京', '上海', '广东', '江苏', '浙江', '四川', '湖北', '湖南'][Math.floor(Math.random() * 8)],
          city: ['北京', '上海', '广州', '深圳', '杭州', '成都', '武汉', '长沙'][Math.floor(Math.random() * 8)],
        }
      }
    });
  }

  // 批量创建用户
  await prisma.user.createMany({
    data: users,
    skipDuplicates: true,
  });

  console.log(`已创建 ${users.length} 个普通用户`);
};

// 生成审核员数据
const generateReviewers = async () => {
  console.log('生成审核员数据...');
  const reviewers = [];

  for (let i = 1; i <= CONFIG.reviewers; i++) {
    const { username, password } = generateCredentials('reviewer', i);
    
    reviewers.push({
      username,
      password,
      email: `reviewer${i}@example.com`,
      role: 'REVIEWER',
      status: 'ACTIVE',
      createdAt: new Date(Date.now() - Math.floor(Math.random() * 30) * 24 * 60 * 60 * 1000),
      profile: {
        create: {
          fullName: `审核员 ${i}`,
          gender: Math.random() > 0.5 ? 'MALE' : 'FEMALE',
          age: 25 + Math.floor(Math.random() * 20),
        }
      }
    });
  }

  // 批量创建审核员
  await prisma.user.createMany({
    data: reviewers,
    skipDuplicates: true,
  });

  console.log(`已创建 ${reviewers.length} 个审核员`);
};

// 生成管理员数据
const generateAdmins = async () => {
  console.log('生成管理员数据...');
  const admins = [];

  // 创建普通管理员
  for (let i = 1; i <= CONFIG.admins; i++) {
    const { username, password } = generateCredentials('admin', i);
    
    admins.push({
      username,
      password,
      email: `admin${i}@example.com`,
      role: 'ADMIN',
      status: 'ACTIVE',
      createdAt: new Date(Date.now() - Math.floor(Math.random() * 30) * 24 * 60 * 60 * 1000),
      profile: {
        create: {
          fullName: `管理员 ${i}`,
          gender: Math.random() > 0.5 ? 'MALE' : 'FEMALE',
          age: 30 + Math.floor(Math.random() * 20),
        }
      }
    });
  }

  // 创建超级管理员
  admins.push({
    username: 'superadmin',
    password: bcrypt.hashSync('superadmin123', CONFIG.salt),
    email: '<EMAIL>',
    role: 'SUPER_ADMIN',
    status: 'ACTIVE',
    createdAt: new Date(Date.now() - 60 * 24 * 60 * 60 * 1000), // 60天前
    profile: {
      create: {
        fullName: '超级管理员',
        gender: 'MALE',
        age: 35,
      }
    }
  });

  // 批量创建管理员
  await prisma.user.createMany({
    data: admins,
    skipDuplicates: true,
  });

  console.log(`已创建 ${admins.length} 个管理员（包括超级管理员）`);
};

// 生成问卷数据
const generateQuestionnaires = async () => {
  console.log('生成问卷数据...');
  const questionnaires = [];
  
  // 获取所有普通用户
  const users = await prisma.user.findMany({
    where: { role: 'USER' },
    select: { id: true, uuid: true }
  });

  for (let i = 1; i <= CONFIG.questionnaires; i++) {
    const user = users[Math.floor(Math.random() * users.length)];
    const submissionDate = new Date(Date.now() - Math.floor(Math.random() * 30) * 24 * 60 * 60 * 1000);
    
    questionnaires.push({
      userId: user.id,
      uuid: user.uuid,
      status: ['PENDING', 'APPROVED', 'REJECTED'][Math.floor(Math.random() * 3)],
      submissionDate,
      data: {
        education: ['本科', '硕士', '博士'][Math.floor(Math.random() * 3)],
        major: ['计算机科学', '电子工程', '机械工程', '经济学', '管理学', '医学', '法学', '文学'][Math.floor(Math.random() * 8)],
        graduationYear: 2010 + Math.floor(Math.random() * 14),
        employmentStatus: ['已就业', '未就业', '继续深造'][Math.floor(Math.random() * 3)],
        jobTitle: ['软件工程师', '产品经理', '数据分析师', '市场专员', '人力资源', '财务分析师'][Math.floor(Math.random() * 6)],
        industry: ['互联网', '金融', '教育', '医疗', '制造业', '服务业'][Math.floor(Math.random() * 6)],
        salary: 5000 + Math.floor(Math.random() * 15000),
        jobSatisfaction: 1 + Math.floor(Math.random() * 5),
        adviceToJuniors: `作为一名${['计算机科学', '电子工程', '机械工程', '经济学', '管理学', '医学', '法学', '文学'][Math.floor(Math.random() * 8)]}专业的毕业生，我建议高三学子在选择专业时要考虑自己的兴趣和就业前景。大学期间要多参加实践活动，提升自己的实际能力。`,
        employmentObservation: `目前就业环境${['良好', '一般', '严峻'][Math.floor(Math.random() * 3)]}，${['互联网', '金融', '教育', '医疗', '制造业', '服务业'][Math.floor(Math.random() * 6)]}行业发展前景较好，但竞争也很激烈。建议在校期间多积累实习经验，提升自己的竞争力。`
      },
      createdAt: submissionDate,
      updatedAt: submissionDate,
    });
  }

  // 批量创建问卷
  await prisma.questionnaire.createMany({
    data: questionnaires,
    skipDuplicates: true,
  });

  console.log(`已创建 ${questionnaires.length} 份问卷`);
};

// 生成故事墙数据
const generateStories = async () => {
  console.log('生成故事墙数据...');
  const stories = [];
  
  // 获取所有普通用户
  const users = await prisma.user.findMany({
    where: { role: 'USER' },
    select: { id: true }
  });

  for (let i = 1; i <= CONFIG.stories; i++) {
    const user = users[Math.floor(Math.random() * users.length)];
    const submissionDate = new Date(Date.now() - Math.floor(Math.random() * 30) * 24 * 60 * 60 * 1000);
    
    stories.push({
      userId: user.id,
      title: `我的就业故事 #${i}`,
      content: `我是一名${['计算机科学', '电子工程', '机械工程', '经济学', '管理学', '医学', '法学', '文学'][Math.floor(Math.random() * 8)]}专业的毕业生，毕业后进入了${['互联网', '金融', '教育', '医疗', '制造业', '服务业'][Math.floor(Math.random() * 6)]}行业工作。

刚开始工作时面临了很多挑战，但通过不断学习和努力，现在已经适应了工作环境，并取得了一定的成绩。

我想分享的经验是：${['要保持学习的热情', '多与行业前辈交流', '不要害怕失败', '坚持自己的职业规划', '工作中要善于团队合作'][Math.floor(Math.random() * 5)]}。希望我的故事能给即将毕业的学弟学妹一些启发。`,
      tags: JSON.stringify([
        ['互联网', '金融', '教育', '医疗', '制造业', '服务业'][Math.floor(Math.random() * 6)],
        ['职场经验', '求职技巧', '行业分析', '个人成长', '职业规划'][Math.floor(Math.random() * 5)]
      ]),
      status: ['PENDING', 'APPROVED', 'REJECTED'][Math.floor(Math.random() * 3)],
      likes: Math.floor(Math.random() * 100),
      views: Math.floor(Math.random() * 500),
      createdAt: submissionDate,
      updatedAt: submissionDate,
    });
  }

  // 批量创建故事
  await prisma.story.createMany({
    data: stories,
    skipDuplicates: true,
  });

  console.log(`已创建 ${stories.length} 条故事`);
};

// 主函数
const main = async () => {
  try {
    console.log('开始生成测试数据...');
    
    /*
     * 清空现有数据（可选）
     * await prisma.questionnaire.deleteMany({});
     * await prisma.story.deleteMany({});
     * await prisma.profile.deleteMany({});
     * await prisma.user.deleteMany({});
     */
    
    // 生成数据
    await generateNormalUsers();
    await generateReviewers();
    await generateAdmins();
    await generateQuestionnaires();
    await generateStories();
    
    console.log('测试数据生成完成！');
  } catch (error) {
    console.error('生成测试数据时出错:', error);
  } finally {
    await prisma.$disconnect();
  }
};

// 执行主函数
main();
