interface AnswerStatsProps {
  options: {
    label: string;
    percentage: number;
    count?: number;
    isSelected?: boolean;
  }[];
  title: string;
  isLoading?: boolean;
}

export default function AnswerStats({ options, title, isLoading = false }: AnswerStatsProps) {
  return (
    <div className="bg-white rounded-lg shadow-md p-4 border border-gray-100">
      <div className="flex items-center justify-between mb-3">
        <h4 className="text-sm font-medium text-gray-700">{title}</h4>
        {isLoading && (
          <div className="w-4 h-4 border-2 border-blue-200 border-t-blue-600 rounded-full animate-spin"></div>
        )}
      </div>

      <div className="space-y-3">
        {options.length > 0 ? (
          options.map((option, index) => (
            <div key={index} className="space-y-1">
              <div className="flex justify-between text-xs">
                <span className={option.isSelected ? "font-medium text-primary" : "text-gray-600"}>
                  {option.label}
                  {option.count !== undefined && (
                    <span className="ml-1 text-gray-400">({option.count})</span>
                  )}
                </span>
                <span className={option.isSelected ? "font-medium text-primary" : "text-gray-600"}>
                  {option.percentage}%
                </span>
              </div>

              <div className="w-full bg-gray-100 rounded-full h-2">
                <div
                  className={`h-2 rounded-full transition-all duration-300 ${
                    option.isSelected ? 'bg-primary' : 'bg-gray-300'
                  }`}
                  style={{ width: `${option.percentage}%` }}
                />
              </div>
            </div>
          ))
        ) : (
          <div className="text-center text-gray-400 py-4">
            暂无数据
          </div>
        )}
      </div>

      <div className="mt-4 text-xs text-gray-500 text-center">
        {isLoading ? '正在更新统计数据...' :
         options.length > 0 ? '基于实时数据统计，仅供参考' : '等待数据加载...'}
      </div>
    </div>
  );
}
