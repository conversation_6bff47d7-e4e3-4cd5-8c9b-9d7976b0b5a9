import { PrismaClient } from '@prisma/client';

// Initialize Prisma client
const prisma = new PrismaClient();

/**
 * Filter sensitive words from text
 * @param text Text to filter
 * @returns Filtered text
 */
export async function filterSensitiveWords(text: string): Promise<string> {
  if (!text) {
    return text;
  }
  
  try {
    // Get all sensitive words from database
    const sensitiveWords = await prisma.sensitiveWord.findMany({
      orderBy: {
        level: 'desc', // Process higher level (more sensitive) words first
      },
    });
    
    // If no sensitive words, return original text
    if (sensitiveWords.length === 0) {
      return text;
    }
    
    // Replace sensitive words with asterisks
    let filteredText = text;
    
    for (const { word, level } of sensitiveWords) {
      // Create a regular expression to match the word
      // Use word boundary for whole word matching
      const regex = new RegExp(`\\b${word}\\b`, 'gi');
      
      // Replace with asterisks based on level
      if (level === 3) {
        // Level 3 (high): Replace entire text with warning
        if (regex.test(filteredText)) {
          return '[内容包含违规信息]';
        }
      } else if (level === 2) {
        // Level 2 (medium): Replace word with asterisks
        filteredText = filteredText.replace(regex, '*'.repeat(word.length));
      } else {
        // Level 1 (low): Replace word with first character + asterisks
        filteredText = filteredText.replace(regex, (match) => {
          return match.charAt(0) + '*'.repeat(match.length - 1);
        });
      }
    }
    
    return filteredText;
  } catch (error) {
    console.error('Error filtering sensitive words:', error);
    return text; // Return original text on error
  }
}

/**
 * Check if text contains sensitive words
 * @param text Text to check
 * @returns True if text contains sensitive words
 */
export async function containsSensitiveWords(text: string): Promise<boolean> {
  if (!text) {
    return false;
  }
  
  try {
    // Get all sensitive words from database
    const sensitiveWords = await prisma.sensitiveWord.findMany();
    
    // If no sensitive words, return false
    if (sensitiveWords.length === 0) {
      return false;
    }
    
    // Check if text contains any sensitive words
    for (const { word } of sensitiveWords) {
      // Create a regular expression to match the word
      // Use word boundary for whole word matching
      const regex = new RegExp(`\\b${word}\\b`, 'gi');
      
      if (regex.test(text)) {
        return true;
      }
    }
    
    return false;
  } catch (error) {
    console.error('Error checking sensitive words:', error);
    return false; // Return false on error
  }
}
