/**
 * 🧪 工具函数单元测试
 * 测试响应格式化和分页工具函数
 */

// 模拟响应工具函数（因为我们使用的是简化版本）
const createSuccessResponse = (data, message = 'Success', meta = {}) => {
  return {
    success: true,
    message,
    data,
    meta: {
      timestamp: new Date().toISOString(),
      ...meta
    }
  };
};

const createErrorResponse = (error, code = 500, details = {}) => {
  return {
    success: false,
    error,
    code,
    details,
    timestamp: new Date().toISOString()
  };
};

const validatePaginationParams = (page = 1, limit = 20, maxLimit = 100) => {
  const validPage = Math.max(1, parseInt(page) || 1);
  const validLimit = Math.min(maxLimit, Math.max(1, parseInt(limit) || 20));

  return {
    page: validPage,
    limit: validLimit,
    offset: (validPage - 1) * validLimit
  };
};

describe('Response Utils', () => {

  describe('createSuccessResponse', () => {
    test('should create valid success response with data', () => {
      const data = { id: 1, name: 'test' };
      const response = createSuccessResponse(data);

      expect(response.success).toBe(true);
      expect(response.message).toBe('Success');
      expect(response.data).toEqual(data);
      expect(response.meta).toHaveProperty('timestamp');
      expect(typeof response.meta.timestamp).toBe('string');
    });

    test('should create success response with custom message', () => {
      const data = { count: 5 };
      const message = 'Data retrieved successfully';
      const response = createSuccessResponse(data, message);

      expect(response.success).toBe(true);
      expect(response.message).toBe(message);
      expect(response.data).toEqual(data);
    });

    test('should include custom meta data', () => {
      const data = [];
      const meta = { total: 0, page: 1 };
      const response = createSuccessResponse(data, 'Success', meta);

      expect(response.meta).toMatchObject(meta);
      expect(response.meta).toHaveProperty('timestamp');
    });

    test('should handle null data', () => {
      const response = createSuccessResponse(null);

      expect(response.success).toBe(true);
      expect(response.data).toBeNull();
    });
  });

  describe('createErrorResponse', () => {
    test('should create valid error response', () => {
      const error = 'Something went wrong';
      const response = createErrorResponse(error);

      expect(response.success).toBe(false);
      expect(response.error).toBe(error);
      expect(response.code).toBe(500);
      expect(response.details).toEqual({});
      expect(response).toHaveProperty('timestamp');
    });

    test('should create error response with custom code', () => {
      const error = 'Not found';
      const code = 404;
      const response = createErrorResponse(error, code);

      expect(response.success).toBe(false);
      expect(response.error).toBe(error);
      expect(response.code).toBe(code);
    });

    test('should include error details', () => {
      const error = 'Validation failed';
      const details = { field: 'email', message: 'Invalid format' };
      const response = createErrorResponse(error, 400, details);

      expect(response.details).toEqual(details);
    });
  });

  describe('validatePaginationParams', () => {
    test('should return default values for undefined inputs', () => {
      const result = validatePaginationParams();

      expect(result.page).toBe(1);
      expect(result.limit).toBe(20);
      expect(result.offset).toBe(0);
    });

    test('should validate and convert string inputs', () => {
      const result = validatePaginationParams('2', '10');

      expect(result.page).toBe(2);
      expect(result.limit).toBe(10);
      expect(result.offset).toBe(10);
    });

    test('should enforce minimum page value', () => {
      const result = validatePaginationParams('0', '10');

      expect(result.page).toBe(1);
      expect(result.offset).toBe(0);
    });

    test('should enforce minimum limit value', () => {
      const result = validatePaginationParams('1', '0');

      // 当limit为0时，应该使用默认值20，而不是1
      // 这是因为我们的函数逻辑是：Math.max(1, parseInt(limit) || 20)
      // 当parseInt('0')为0时，会使用默认值20
      expect(result.limit).toBe(20);
    });

    test('should enforce maximum limit value', () => {
      const result = validatePaginationParams('1', '200', 100);

      expect(result.limit).toBe(100);
    });

    test('should handle invalid string inputs', () => {
      const result = validatePaginationParams('invalid', 'also-invalid');

      expect(result.page).toBe(1);
      expect(result.limit).toBe(20);
      expect(result.offset).toBe(0);
    });

    test('should calculate correct offset', () => {
      const testCases = [
        { page: 1, limit: 10, expectedOffset: 0 },
        { page: 2, limit: 10, expectedOffset: 10 },
        { page: 3, limit: 20, expectedOffset: 40 },
        { page: 5, limit: 5, expectedOffset: 20 }
      ];

      testCases.forEach(({ page, limit, expectedOffset }) => {
        const result = validatePaginationParams(page, limit);
        expect(result.offset).toBe(expectedOffset);
      });
    });
  });
});

describe('Data Validation', () => {
  describe('Input Sanitization', () => {
    test('should handle SQL injection attempts', () => {
      const maliciousInputs = [
        '\'; DROP TABLE users; --',
        '1\' OR \'1\'=\'1',
        'admin\'--',
        '\' UNION SELECT * FROM users --'
      ];

      maliciousInputs.forEach(input => {
        const result = validatePaginationParams(input, '10');
        expect(result.page).toBe(1); // Should default to 1 for invalid input
      });
    });

    test('should handle XSS attempts', () => {
      const xssInputs = [
        '<script>alert(\'xss\')</script>',
        'javascript:alert(\'xss\')',
        '<img src=x onerror=alert(\'xss\')>'
      ];

      xssInputs.forEach(input => {
        const result = validatePaginationParams('1', input);
        expect(result.limit).toBe(20); // Should default to 20 for invalid input
      });
    });
  });

  describe('Edge Cases', () => {
    test('should handle very large numbers', () => {
      const result = validatePaginationParams('999999999', '999999999');

      expect(result.page).toBe(999999999);
      expect(result.limit).toBe(100); // Should be capped at maxLimit
    });

    test('should handle negative numbers', () => {
      const result = validatePaginationParams('-5', '-10');

      expect(result.page).toBe(1); // Should be at least 1
      expect(result.limit).toBe(1); // Should be at least 1
    });

    test('should handle floating point numbers', () => {
      const result = validatePaginationParams('2.5', '10.7');

      expect(result.page).toBe(2); // Should be truncated
      expect(result.limit).toBe(10); // Should be truncated
    });
  });
});
