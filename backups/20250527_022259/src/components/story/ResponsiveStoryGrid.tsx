import { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { 
  ThumbsUp, 
  Calendar, 
  User, 
  Share2, 
  Flame, 
  Clock,
  Heart,
  MessageCircle,
  Bookmark,
  ExternalLink
} from 'lucide-react';
import { cn } from '@/lib/utils';
import MobileStoryCard from './MobileStoryCard';

interface Story {
  id: number;
  title: string;
  content: string;
  author: string;
  likes: number;
  createdAt: string;
  category: string;
  educationLevel: string;
  industry: string;
  tags?: string[];
}

interface ResponsiveStoryGridProps {
  stories: Story[];
  loading?: boolean;
  sortBy?: 'latest' | 'popular';
  onStoryClick?: (story: Story) => void;
  onStoryShare?: (story: Story) => void;
  onStoryLike?: (story: Story) => void;
  onStoryBookmark?: (story: Story) => void;
  className?: string;
  emptyMessage?: string;
  emptyDescription?: string;
}

export default function ResponsiveStoryGrid({
  stories,
  loading = false,
  sortBy = 'latest',
  onStoryClick,
  onStoryShare,
  onStoryLike,
  onStoryBookmark,
  className,
  emptyMessage = "暂无故事",
  emptyDescription = "还没有符合条件的故事，试试调整筛选条件吧"
}: ResponsiveStoryGridProps) {
  const [isMobile, setIsMobile] = useState(false);

  // 检测屏幕尺寸
  useEffect(() => {
    const checkMobile = () => {
      setIsMobile(window.innerWidth < 1024); // lg breakpoint
    };

    checkMobile();
    window.addEventListener('resize', checkMobile);
    return () => window.removeEventListener('resize', checkMobile);
  }, []);

  // 格式化日期
  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString('zh-CN', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    });
  };

  // 处理故事点击
  const handleStoryClick = (story: Story) => {
    onStoryClick?.(story);
  };

  // 处理分享
  const handleShare = (story: Story, event: React.MouseEvent) => {
    event.stopPropagation();
    onStoryShare?.(story);
  };

  // 处理点赞
  const handleLike = (story: Story, event: React.MouseEvent) => {
    event.stopPropagation();
    onStoryLike?.(story);
  };

  // 处理收藏
  const handleBookmark = (story: Story, event: React.MouseEvent) => {
    event.stopPropagation();
    onStoryBookmark?.(story);
  };

  // 加载状态
  if (loading) {
    return (
      <div className={cn("space-y-4", className)}>
        {Array.from({ length: 6 }).map((_, index) => (
          <Card key={index} className="animate-pulse">
            <CardHeader>
              <div className="h-4 bg-gray-200 rounded w-3/4"></div>
              <div className="flex gap-2 mt-2">
                <div className="h-5 bg-gray-200 rounded w-16"></div>
                <div className="h-5 bg-gray-200 rounded w-16"></div>
                <div className="h-5 bg-gray-200 rounded w-16"></div>
              </div>
            </CardHeader>
            <CardContent>
              <div className="space-y-2">
                <div className="h-3 bg-gray-200 rounded"></div>
                <div className="h-3 bg-gray-200 rounded w-5/6"></div>
                <div className="h-3 bg-gray-200 rounded w-4/6"></div>
              </div>
              <div className="flex justify-between items-center mt-4">
                <div className="h-4 bg-gray-200 rounded w-24"></div>
                <div className="h-8 bg-gray-200 rounded w-16"></div>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>
    );
  }

  // 空状态
  if (stories.length === 0) {
    return (
      <div className={cn("text-center py-12", className)}>
        <div className="max-w-md mx-auto">
          <div className="w-24 h-24 mx-auto mb-4 bg-gray-100 rounded-full flex items-center justify-center">
            <MessageCircle className="h-12 w-12 text-gray-400" />
          </div>
          <h3 className="text-lg font-medium text-gray-900 mb-2">
            {emptyMessage}
          </h3>
          <p className="text-gray-500 text-sm">
            {emptyDescription}
          </p>
        </div>
      </div>
    );
  }

  // 移动端布局
  if (isMobile) {
    return (
      <div className={cn("space-y-4", className)}>
        {stories.map((story) => (
          <MobileStoryCard
            key={story.id}
            story={story}
            onShare={onStoryShare}
            onLike={onStoryLike}
            onBookmark={onStoryBookmark}
            onViewDetails={onStoryClick}
          />
        ))}
      </div>
    );
  }

  // 桌面端布局
  return (
    <div className={cn("grid grid-cols-1 md:grid-cols-2 xl:grid-cols-3 gap-6", className)}>
      {stories.map((story) => (
        <Card
          key={story.id}
          className="cursor-pointer transition-all duration-200 hover:shadow-lg hover:-translate-y-1 group"
          onClick={() => handleStoryClick(story)}
        >
          <CardHeader>
            <CardTitle className="text-lg line-clamp-2 flex items-start gap-2">
              <span className="flex-1">{story.title}</span>
              {sortBy === 'popular' && (
                <Flame className="h-5 w-5 text-orange-500 flex-shrink-0 mt-0.5" title="热门故事" />
              )}
              {sortBy === 'latest' && (
                <Clock className="h-5 w-5 text-blue-500 flex-shrink-0 mt-0.5" title="最新故事" />
              )}
            </CardTitle>
            <div className="flex flex-wrap gap-2">
              <Badge variant="outline" className="text-xs">
                {story.category}
              </Badge>
              <Badge variant="outline" className="text-xs">
                {story.educationLevel}
              </Badge>
              <Badge variant="outline" className="text-xs">
                {story.industry}
              </Badge>
            </div>
          </CardHeader>
          <CardContent>
            <p className="text-gray-600 text-sm line-clamp-3 mb-4">
              {story.content}
            </p>
            
            <div className="flex items-center justify-between text-sm text-gray-500">
              <div className="flex items-center gap-4">
                <div className="flex items-center gap-1">
                  <User className="h-4 w-4" />
                  <span>{story.author}</span>
                </div>
                <div className="flex items-center gap-1">
                  <Calendar className="h-4 w-4" />
                  <span>{formatDate(story.createdAt)}</span>
                </div>
              </div>
              
              <div className="flex items-center gap-2">
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={(e) => handleLike(story, e)}
                  className="h-8 px-2 gap-1 opacity-0 group-hover:opacity-100 transition-opacity"
                >
                  <Heart className="h-4 w-4" />
                  <span>{story.likes}</span>
                </Button>
                
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={(e) => handleShare(story, e)}
                  className="h-8 w-8 p-0 opacity-0 group-hover:opacity-100 transition-opacity"
                >
                  <Share2 className="h-4 w-4" />
                </Button>
              </div>
            </div>
          </CardContent>
        </Card>
      ))}
    </div>
  );
}
