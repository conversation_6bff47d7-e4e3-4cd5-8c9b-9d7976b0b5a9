/**
 * 模拟数据
 * 
 * 这个文件包含所有模拟数据，用于开发和测试阶段。
 * 在生产环境中，这些数据将被真实API数据替代。
 */

import { 
  QuestionnaireResponse, 
  FilterOptions, 
  PaginationInfo, 
  TagItem,
  DeidentificationConfig
} from '@/types/api';

// 模拟问卷回复数据
const mockResponses: QuestionnaireResponse[] = Array.from({ length: 100 }, (_, index) => ({
  id: index + 1,
  sequenceNumber: `R${String(index + 1).padStart(5, '0')}`,
  isAnonymous: index % 3 === 0,
  educationLevel: ['本科', '硕士', '博士', '专科'][index % 4],
  major: ['计算机科学', '软件工程', '电子工程', '通信工程', '人工智能'][index % 5],
  graduationYear: 2020 + (index % 5),
  region: ['北京', '上海', '广州', '深圳', '杭州', '成都'][index % 6],
  employmentStatus: ['已就业', '待业', '自由职业', '创业'][index % 4],
  industry: ['互联网', '金融', '教育', '医疗', '制造业'][index % 5],
  position: ['软件工程师', '产品经理', '数据分析师', '设计师', '运营'][index % 5],
  salary: ['5k以下', '5k-10k', '10k-15k', '15k-20k', '20k-30k', '30k以上'][index % 6],
  jobSatisfaction: String((index % 5) + 1),
  unemploymentDuration: ['0-3个月', '3-6个月', '6-12个月', '1年以上'][index % 4],
  careerChangeIntention: index % 2 === 0,
  challenges: `面临的挑战 ${index + 1}`,
  suggestions: `建议 ${index + 1}`,
  createdAt: new Date(Date.now() - (index * 24 * 60 * 60 * 1000)).toISOString(),
  updatedAt: new Date(Date.now() - (index * 12 * 60 * 60 * 1000)).toISOString(),
  ipAddress: `192.168.1.${index % 255}`,
  tags: index % 3 === 0 ? ['重要', '已验证'] : index % 3 === 1 ? ['待处理'] : [],
  status: index % 3 === 0 ? 'verified' : index % 3 === 1 ? 'pending' : 'normal'
}));

// 模拟标签数据
const mockTags: TagItem[] = [
  { id: '1', name: '重要', color: 'red', priority: 1, category: 'status', count: 35, createdAt: '2023-01-01T00:00:00Z', updatedAt: '2023-01-01T00:00:00Z' },
  { id: '2', name: '已验证', color: 'green', priority: 2, category: 'status', count: 42, createdAt: '2023-01-02T00:00:00Z', updatedAt: '2023-01-02T00:00:00Z' },
  { id: '3', name: '待处理', color: 'yellow', priority: 3, category: 'status', count: 28, createdAt: '2023-01-03T00:00:00Z', updatedAt: '2023-01-03T00:00:00Z' },
  { id: '4', name: '计算机', color: 'blue', priority: 4, category: 'major', count: 56, createdAt: '2023-01-04T00:00:00Z', updatedAt: '2023-01-04T00:00:00Z' },
  { id: '5', name: '软件', color: 'blue', priority: 5, category: 'major', count: 48, createdAt: '2023-01-05T00:00:00Z', updatedAt: '2023-01-05T00:00:00Z' },
  { id: '6', name: '北京', color: 'purple', priority: 6, category: 'region', count: 32, createdAt: '2023-01-06T00:00:00Z', updatedAt: '2023-01-06T00:00:00Z' },
  { id: '7', name: '上海', color: 'purple', priority: 7, category: 'region', count: 29, createdAt: '2023-01-07T00:00:00Z', updatedAt: '2023-01-07T00:00:00Z' },
  { id: '8', name: '高薪', color: 'orange', priority: 8, category: 'salary', count: 18, createdAt: '2023-01-08T00:00:00Z', updatedAt: '2023-01-08T00:00:00Z' },
  { id: '9', name: '低薪', color: 'orange', priority: 9, category: 'salary', count: 22, createdAt: '2023-01-09T00:00:00Z', updatedAt: '2023-01-09T00:00:00Z' },
  { id: '10', name: '满意度高', color: 'cyan', priority: 10, category: 'satisfaction', count: 15, createdAt: '2023-01-10T00:00:00Z', updatedAt: '2023-01-10T00:00:00Z' }
];

// 模拟故事数据
const mockStories = Array.from({ length: 50 }, (_, index) => ({
  id: index + 1,
  title: `故事标题 ${index + 1}`,
  content: `这是故事内容 ${index + 1}，描述了毕业生的就业经历和感受。`,
  author: index % 3 === 0 ? '匿名用户' : `用户${index + 1}`,
  createdAt: new Date(Date.now() - (index * 24 * 60 * 60 * 1000)).toISOString(),
  likes: Math.floor(Math.random() * 100),
  dislikes: Math.floor(Math.random() * 20),
  tags: [
    mockTags[index % 10].name,
    mockTags[(index + 2) % 10].name
  ],
  category: ['求职经历', '职场感悟', '转行经验', '创业故事'][index % 4],
  educationLevel: ['本科', '硕士', '博士', '专科'][index % 4],
  industry: ['互联网', '金融', '教育', '医疗', '制造业'][index % 5],
  metadata: index % 5 === 0 ? {
    deidentified: true,
    deidentificationLevel: ['low', 'medium', 'high'][index % 3],
    deidentificationTimestamp: new Date().toISOString(),
    originalTitle: `原始标题 ${index + 1}`,
    originalContent: `原始内容 ${index + 1}`
  } : undefined
}));

// 模拟统计数据
const mockStatistics = {
  totalResponses: 8742,
  verifiedResponses: 6521,
  anonymousResponses: 2221,
  responsesByDate: [
    { date: '2023-01', count: 1245 },
    { date: '2023-02', count: 1356 },
    { date: '2023-03', count: 1478 },
    { date: '2023-04', count: 1589 },
    { date: '2023-05', count: 1632 },
    { date: '2023-06', count: 1442 }
  ],
  educationLevels: [
    { name: '本科', count: 4521 },
    { name: '硕士', count: 2356 },
    { name: '博士', count: 987 },
    { name: '专科', count: 878 }
  ],
  regions: [
    { name: '北京', count: 1856 },
    { name: '上海', count: 1742 },
    { name: '广州', count: 1245 },
    { name: '深圳', count: 1189 },
    { name: '杭州', count: 987 },
    { name: '成都', count: 856 },
    { name: '其他', count: 867 }
  ],
  industries: [
    { name: '互联网', count: 3245 },
    { name: '金融', count: 1856 },
    { name: '教育', count: 1245 },
    { name: '医疗', count: 987 },
    { name: '制造业', count: 756 },
    { name: '其他', count: 653 }
  ],
  employmentStatus: [
    { name: '已就业', count: 6521 },
    { name: '待业', count: 1542 },
    { name: '自由职业', count: 456 },
    { name: '创业', count: 223 }
  ],
  salaryRanges: [
    { range: '5k以下', count: 456 },
    { range: '5k-10k', count: 1245 },
    { range: '10k-15k', count: 2356 },
    { range: '15k-20k', count: 1856 },
    { range: '20k-30k', count: 1542 },
    { range: '30k以上', count: 1287 }
  ],
  jobSatisfaction: [
    { level: 1, count: 456 },
    { level: 2, count: 987 },
    { level: 3, count: 2356 },
    { level: 4, count: 3245 },
    { level: 5, count: 1698 }
  ],
  careerChangeIntention: [
    { intention: true, count: 3245 },
    { intention: false, count: 5497 }
  ]
};

// 模拟脱敏配置
const mockDeidentificationConfig: DeidentificationConfig = {
  enabled: true,
  level: 'medium',
  aiProvider: 'openai',
  model: 'gpt-3.5-turbo',
  preserveSemantics: true,
  applyToStories: true,
  applyToQuestionnaires: true,
  adminCanViewOriginal: true
};

// 获取问卷回复列表
export const getMockResponses = (
  page = 1, 
  pageSize = 10, 
  filters: FilterOptions = {}
) => {
  // 应用筛选条件
  let filteredResponses = [...mockResponses];
  
  if (filters.search) {
    const searchLower = filters.search.toLowerCase();
    filteredResponses = filteredResponses.filter(response => 
      response.sequenceNumber.toLowerCase().includes(searchLower) ||
      response.major?.toLowerCase().includes(searchLower) ||
      response.region?.toLowerCase().includes(searchLower) ||
      response.industry?.toLowerCase().includes(searchLower) ||
      response.position?.toLowerCase().includes(searchLower)
    );
  }
  
  if (filters.educationLevel) {
    filteredResponses = filteredResponses.filter(response => 
      response.educationLevel === filters.educationLevel
    );
  }
  
  if (filters.employmentStatus) {
    filteredResponses = filteredResponses.filter(response => 
      response.employmentStatus === filters.employmentStatus
    );
  }
  
  if (filters.region) {
    filteredResponses = filteredResponses.filter(response => 
      response.region === filters.region
    );
  }
  
  if (filters.isAnonymous !== undefined) {
    filteredResponses = filteredResponses.filter(response => 
      response.isAnonymous === filters.isAnonymous
    );
  }
  
  if (filters.status) {
    filteredResponses = filteredResponses.filter(response => 
      response.status === filters.status
    );
  }
  
  // 计算分页
  const totalItems = filteredResponses.length;
  const totalPages = Math.ceil(totalItems / pageSize);
  const startIndex = (page - 1) * pageSize;
  const endIndex = startIndex + pageSize;
  const paginatedResponses = filteredResponses.slice(startIndex, endIndex);
  
  // 构建分页信息
  const pagination: PaginationInfo = {
    currentPage: page,
    totalPages,
    pageSize,
    totalItems
  };
  
  return {
    success: true,
    responses: paginatedResponses,
    pagination
  };
};

// 获取单个问卷回复
export const getMockResponse = (id: number) => {
  const response = mockResponses.find(r => r.id === id);
  
  if (!response) {
    return {
      success: false,
      error: '未找到问卷回复'
    };
  }
  
  return {
    success: true,
    response
  };
};

// 获取标签列表
export const getMockTags = () => {
  return {
    success: true,
    tags: mockTags
  };
};

// 获取故事列表
export const getMockStories = (
  page = 1,
  sortBy: 'latest' | 'popular' = 'latest',
  tag?: string,
  options?: any
) => {
  // 应用筛选条件
  let filteredStories = [...mockStories];
  
  if (tag) {
    filteredStories = filteredStories.filter(story => 
      story.tags.includes(tag)
    );
  }
  
  if (options?.category) {
    filteredStories = filteredStories.filter(story => 
      story.category === options.category
    );
  }
  
  if (options?.educationLevel) {
    filteredStories = filteredStories.filter(story => 
      story.educationLevel === options.educationLevel
    );
  }
  
  if (options?.industry) {
    filteredStories = filteredStories.filter(story => 
      story.industry === options.industry
    );
  }
  
  // 排序
  if (sortBy === 'latest') {
    filteredStories.sort((a, b) => 
      new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime()
    );
  } else if (sortBy === 'popular') {
    filteredStories.sort((a, b) => 
      (b.likes - b.dislikes) - (a.likes - a.dislikes)
    );
  }
  
  // 计算分页
  const pageSize = options?.limit || 10;
  const totalItems = filteredStories.length;
  const totalPages = Math.ceil(totalItems / pageSize);
  const startIndex = (page - 1) * pageSize;
  const endIndex = startIndex + pageSize;
  const paginatedStories = filteredStories.slice(startIndex, endIndex);
  
  // 获取热门标签
  const tagCounts: Record<string, number> = {};
  filteredStories.forEach(story => {
    story.tags.forEach(tag => {
      tagCounts[tag] = (tagCounts[tag] || 0) + 1;
    });
  });
  
  const popularTags = Object.entries(tagCounts)
    .map(([tag, count]) => ({ tag, count }))
    .sort((a, b) => b.count - a.count)
    .slice(0, 10);
  
  return {
    success: true,
    stories: paginatedStories,
    totalPages,
    currentPage: page,
    popularTags
  };
};

// 获取统计数据
export const getMockStatistics = (filters: Record<string, any> = {}) => {
  // 在实际应用中，这里应该根据筛选条件过滤统计数据
  return {
    success: true,
    statistics: mockStatistics
  };
};

// 获取脱敏配置
export const getMockDeidentificationConfig = () => {
  return {
    success: true,
    config: mockDeidentificationConfig
  };
};
