/**
 * 审计日志服务
 * 记录敏感操作的日志
 */

import { PrismaClient } from '@prisma/client';
import { Env } from '../types';
import { getPrismaClient } from '../utils/prisma';

// 操作类型枚举
export enum AuditAction {
  // 内容审核相关
  CONTENT_APPROVE = 'content_approve',
  CONTENT_REJECT = 'content_reject',
  CONTENT_EDIT = 'content_edit',
  CONTENT_DELETE = 'content_delete',
  BATCH_APPROVE = 'batch_approve',
  BATCH_REJECT = 'batch_reject',

  // 用户管理相关
  USER_CREATE = 'user_create',
  USER_UPDATE = 'user_update',
  USER_DELETE = 'user_delete',
  USER_ROLE_CHANGE = 'user_role_change',

  // 系统设置相关
  SETTING_UPDATE = 'setting_update',

  // 安全相关
  LOGIN_SUCCESS = 'login_success',
  LOGIN_FAILURE = 'login_failure',
  PASSWORD_CHANGE = 'password_change',
  PERMISSION_CHANGE = 'permission_change',
  PERMISSION_USE = 'permission_use',
  PERMISSION_DENIED = 'permission_denied',

  // 数据相关
  DATA_EXPORT = 'data_export',
  DATA_IMPORT = 'data_import',
  DATA_DELETE = 'data_delete',

  // 风险操作
  SECURITY_ALERT = 'security_alert',
  RISK_OPERATION = 'risk_operation',

  // 其他
  OTHER = 'other'
}

// 操作严重性枚举
export enum AuditSeverity {
  INFO = 'info',
  WARNING = 'warning',
  ERROR = 'error',
  CRITICAL = 'critical'
}

// 审计日志接口
export interface AuditLog {
  userId: string;
  action: AuditAction;
  resourceType: string;
  resourceId?: string;
  details?: any;
  ipAddress?: string;
  userAgent?: string;
  severity?: AuditSeverity;
}

/**
 * 审计日志服务
 */
export class AuditLogService {
  private static instance: AuditLogService;
  private isInitialized: boolean = false;

  /**
   * 获取单例实例
   */
  public static getInstance(): AuditLogService {
    if (!AuditLogService.instance) {
      AuditLogService.instance = new AuditLogService();
    }
    return AuditLogService.instance;
  }

  /**
   * 初始化服务
   */
  public async initialize(): Promise<void> {
    if (this.isInitialized) {
      return;
    }

    this.isInitialized = true;
  }

  /**
   * 记录审计日志
   * @param log 审计日志
   * @param env 环境变量
   */
  public async log(log: AuditLog, env: Env): Promise<void> {
    try {
      const prisma = getPrismaClient(env);

      // 创建审计日志
      await prisma.auditLog.create({
        data: {
          userId: log.userId,
          action: log.action,
          resourceType: log.resourceType,
          resourceId: log.resourceId,
          details: log.details,
          ipAddress: log.ipAddress,
          userAgent: log.userAgent,
          severity: log.severity || AuditSeverity.INFO
        }
      });

      console.log(`Audit log created: ${log.action} on ${log.resourceType}${log.resourceId ? `/${log.resourceId}` : ''} by ${log.userId}`);
    } catch (error) {
      console.error('Error creating audit log:', error);
      // 日志记录失败不应影响主要业务流程
    }
  }

  /**
   * 获取审计日志
   * @param filters 过滤条件
   * @param pagination 分页参数
   * @param env 环境变量
   */
  public async getLogs(
    filters: {
      userId?: string;
      action?: AuditAction;
      resourceType?: string;
      resourceId?: string;
      severity?: AuditSeverity;
      startDate?: Date;
      endDate?: Date;
    },
    pagination: {
      page: number;
      pageSize: number;
    },
    env: Env
  ): Promise<{ logs: any[]; total: number; page: number; pageSize: number; totalPages: number }> {
    try {
      const prisma = getPrismaClient(env);

      // 构建查询条件
      const where: any = {};

      if (filters.userId) {
        where.userId = filters.userId;
      }

      if (filters.action) {
        where.action = filters.action;
      }

      if (filters.resourceType) {
        where.resourceType = filters.resourceType;
      }

      if (filters.resourceId) {
        where.resourceId = filters.resourceId;
      }

      if (filters.severity) {
        where.severity = filters.severity;
      }

      if (filters.startDate || filters.endDate) {
        where.createdAt = {};

        if (filters.startDate) {
          where.createdAt.gte = filters.startDate;
        }

        if (filters.endDate) {
          where.createdAt.lte = filters.endDate;
        }
      }

      // 计算分页
      const skip = (pagination.page - 1) * pagination.pageSize;

      // 获取总数
      const total = await prisma.auditLog.count({ where });

      // 获取日志
      const logs = await prisma.auditLog.findMany({
        where,
        orderBy: {
          createdAt: 'desc'
        },
        skip,
        take: pagination.pageSize
      });

      return {
        logs,
        total,
        page: pagination.page,
        pageSize: pagination.pageSize,
        totalPages: Math.ceil(total / pagination.pageSize)
      };
    } catch (error) {
      console.error('Error getting audit logs:', error);
      throw error;
    }
  }

  /**
   * 获取单条审计日志
   * @param id 日志ID
   * @param env 环境变量
   */
  public async getLog(id: string, env: Env): Promise<any> {
    try {
      const prisma = getPrismaClient(env);

      // 获取日志
      const log = await prisma.auditLog.findUnique({
        where: {
          id
        }
      });

      return log;
    } catch (error) {
      console.error('Error getting audit log:', error);
      throw error;
    }
  }

  /**
   * 删除审计日志
   * @param id 日志ID
   * @param env 环境变量
   */
  public async deleteLog(id: string, env: Env): Promise<void> {
    try {
      const prisma = getPrismaClient(env);

      // 删除日志
      await prisma.auditLog.delete({
        where: {
          id
        }
      });
    } catch (error) {
      console.error('Error deleting audit log:', error);
      throw error;
    }
  }

  /**
   * 清理过期日志
   * @param days 保留天数
   * @param env 环境变量
   */
  public async cleanupLogs(days: number, env: Env): Promise<number> {
    try {
      const prisma = getPrismaClient(env);

      // 计算截止日期
      const cutoffDate = new Date();
      cutoffDate.setDate(cutoffDate.getDate() - days);

      // 删除过期日志
      const result = await prisma.auditLog.deleteMany({
        where: {
          createdAt: {
            lt: cutoffDate
          }
        }
      });

      return result.count;
    } catch (error) {
      console.error('Error cleaning up audit logs:', error);
      throw error;
    }
  }
}
