/**
 * 数据预加载服务
 * 提前加载可能需要的数据，提高用户体验
 */

import { logInfo, logError } from '@/lib/logger';
import {
  getPendingContents,
  getReviewSettings,
  getReviewStats,
  getReviewTemplates,
  getReviewerPerformance
} from './dataService';
import {
  cacheContentList,
  cacheSettings,
  getCacheStats
} from './advancedCacheService';

// 预加载状态
let isPreloading = false;
let preloadComplete = false;
let preloadError: Error | null = null;

/**
 * 预加载审核相关数据
 * @param priority 优先级（1-5，1最高）
 */
export const preloadReviewData = async (priority: number = 3): Promise<void> => {
  // 如果已经在预加载或已完成，则不重复执行
  if (isPreloading || preloadComplete) {
    return;
  }

  isPreloading = true;
  preloadError = null;

  try {
    logInfo('开始预加载审核数据', { priority });

    // 根据优先级决定预加载顺序和内容
    const tasks: Promise<any>[] = [];

    // 优先级1：审核设置（最重要）
    if (priority <= 1) {
      tasks.push(
        getReviewSettings()
          .then(data => {
            logInfo('预加载审核设置完成');
            return data;
          })
          .catch(error => {
            logError('预加载审核设置失败', error);
            throw error;
          })
      );
    }

    // 优先级2：待审核内容（重要）
    if (priority <= 2) {
      tasks.push(
        getPendingContents()
          .then(data => {
            logInfo('预加载待审核内容完成');
            return data;
          })
          .catch(error => {
            logError('预加载待审核内容失败', error);
            throw error;
          })
      );
    }

    // 优先级3：审核模板（中等重要）
    if (priority <= 3) {
      tasks.push(
        getReviewTemplates()
          .then(data => {
            logInfo('预加载审核模板完成');
            return data;
          })
          .catch(error => {
            logError('预加载审核模板失败', error);
            // 非关键数据，不抛出错误
            return null;
          })
      );
    }

    // 优先级4：审核统计（次要）
    if (priority <= 4) {
      tasks.push(
        getReviewStats()
          .then(data => {
            logInfo('预加载审核统计完成');
            return data;
          })
          .catch(error => {
            logError('预加载审核统计失败', error);
            // 非关键数据，不抛出错误
            return null;
          })
      );
    }

    // 优先级5：审核员绩效（可选）
    if (priority <= 5) {
      tasks.push(
        getReviewerPerformance()
          .then(data => {
            logInfo('预加载审核员绩效完成');
            return data;
          })
          .catch(error => {
            logError('预加载审核员绩效失败', error);
            // 非关键数据，不抛出错误
            return null;
          })
      );
    }

    // 执行预加载任务
    await Promise.all(tasks);

    // 预加载完成
    preloadComplete = true;
    logInfo('预加载审核数据完成', getCacheStats());
  } catch (error) {
    preloadError = error as Error;
    logError('预加载审核数据失败', error);
  } finally {
    isPreloading = false;
  }
};

/**
 * 预加载特定页面的数据
 * @param page 页面路径
 */
export const preloadPageData = async (page: string): Promise<void> => {
  try {
    logInfo('预加载页面数据', { page });

    switch (page) {
      case '/admin/content-review':
        // 预加载内容审核页面数据
        await preloadReviewData(2); // 优先级2
        break;

      case '/admin/dashboard':
        // 预加载仪表盘页面数据
        await Promise.all([
          getReviewStats(),
          getReviewerPerformance()
        ]);
        break;

      default:
        // 默认预加载
        await preloadReviewData(5); // 最低优先级
        break;
    }

    logInfo('预加载页面数据完成', { page });
  } catch (error) {
    logError('预加载页面数据失败', error);
  }
};

/**
 * 预加载下一页数据
 * @param currentPage 当前页码
 * @param pageSize 每页大小
 */
export const preloadNextPageData = async (currentPage: number, pageSize: number): Promise<void> => {
  try {
    logInfo('预加载下一页数据', { currentPage, pageSize });

    // 构建下一页查询参数
    const nextPage = currentPage + 1;
    const queryParams = new URLSearchParams();
    queryParams.append('page', nextPage.toString());
    queryParams.append('pageSize', pageSize.toString());

    // 预加载下一页数据
    const response = await getPendingContents(queryParams.toString());

    if (response.success && response.pendingContents.length > 0) {
      logInfo('预加载下一页数据完成', { nextPage, itemCount: response.pendingContents.length });
    } else {
      logInfo('下一页没有数据', { nextPage });
    }
  } catch (error) {
    logError('预加载下一页数据失败', error);
  }
};

/**
 * 获取预加载状态
 */
export const getPreloadStatus = (): { isPreloading: boolean; complete: boolean; error: Error | null } => {
  return {
    isPreloading,
    complete: preloadComplete,
    error: preloadError
  };
};

/**
 * 重置预加载状态
 */
export const resetPreloadStatus = (): void => {
  isPreloading = false;
  preloadComplete = false;
  preloadError = null;
};
