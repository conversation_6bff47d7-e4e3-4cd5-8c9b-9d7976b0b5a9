const http = require('http');
const url = require('url');
const sqlite3 = require('sqlite3').verbose();
const path = require('path');

const PORT = 8788;

// 连接到SQLite数据库 (v2.0 优化版)
const dbPath = path.join(__dirname, 'database.db');
const db = new sqlite3.Database(dbPath, (err) => {
  if (err) {
    console.error('Error opening database:', err.message);
  } else {
    console.log('Connected to SQLite database v2.0:', dbPath);
  }
});

// CORS headers
const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
  'Access-Control-Allow-Headers': 'Content-Type, Authorization'
};

// 发送JSON响应
function sendJSON(res, data, statusCode = 200) {
  res.writeHead(statusCode, {
    'Content-Type': 'application/json',
    ...corsHeaders
  });
  res.end(JSON.stringify(data));
}

// 处理OPTIONS请求
function handleOptions(res) {
  res.writeHead(200, corsHeaders);
  res.end();
}

// 辅助函数：获取请求体
function getRequestBody(req) {
  return new Promise((resolve, reject) => {
    let body = '';
    req.on('data', chunk => {
      body += chunk.toString();
    });
    req.on('end', () => {
      resolve(body);
    });
    req.on('error', err => {
      reject(err);
    });
  });
}

// 主请求处理函数
async function handleRequest(req, res) {
  const parsedUrl = url.parse(req.url, true);
  const path = parsedUrl.pathname;
  const method = req.method;

  console.log(`${method} ${path}`);

  // 处理OPTIONS请求
  if (method === 'OPTIONS') {
    handleOptions(res);
    return;
  }

  // 健康检查
  if (path === '/health' && method === 'GET') {
    sendJSON(res, {
      status: 'ok',
      database: 'connected',
      timestamp: new Date().toISOString()
    });
    return;
  }

  // 可视化数据API
  if (path === '/api/visualization/data' && method === 'GET') {
    try {
      // 获取基础统计
      const totalCount = await new Promise((resolve, reject) => {
        db.get("SELECT COUNT(*) as count FROM QuestionnaireResponse", (err, row) => {
          if (err) reject(err);
          else resolve(row.count);
        });
      });

      const employedCount = await new Promise((resolve, reject) => {
        db.get("SELECT COUNT(*) as count FROM QuestionnaireResponse WHERE employmentStatus = '已就业'", (err, row) => {
          if (err) reject(err);
          else resolve(row.count);
        });
      });

      const verifiedCount = await new Promise((resolve, reject) => {
        db.get("SELECT COUNT(*) as count FROM QuestionnaireResponse WHERE isAnonymous = 0", (err, row) => {
          if (err) reject(err);
          else resolve(row.count);
        });
      });

      // 获取教育水平分布
      const educationLevels = await new Promise((resolve, reject) => {
        db.all("SELECT educationLevel as name, COUNT(*) as count FROM QuestionnaireResponse WHERE educationLevel IS NOT NULL GROUP BY educationLevel", (err, rows) => {
          if (err) reject(err);
          else resolve(rows);
        });
      });

      // 获取地区分布
      const regions = await new Promise((resolve, reject) => {
        db.all("SELECT region as name, COUNT(*) as count FROM QuestionnaireResponse WHERE region IS NOT NULL GROUP BY region", (err, rows) => {
          if (err) reject(err);
          else resolve(rows);
        });
      });

      // 获取行业分布
      const industries = await new Promise((resolve, reject) => {
        db.all("SELECT currentIndustry as name, COUNT(*) as count FROM QuestionnaireResponse WHERE currentIndustry IS NOT NULL GROUP BY currentIndustry", (err, rows) => {
          if (err) reject(err);
          else resolve(rows);
        });
      });

      // 获取薪资分布
      const expectedSalaries = await new Promise((resolve, reject) => {
        db.all("SELECT expectedSalaryRange as range, COUNT(*) as count FROM QuestionnaireResponse WHERE expectedSalaryRange IS NOT NULL GROUP BY expectedSalaryRange", (err, rows) => {
          if (err) reject(err);
          else resolve(rows);
        });
      });

      // 获取失业时长分布
      const unemploymentDurations = await new Promise((resolve, reject) => {
        db.all("SELECT unemploymentDuration as duration, COUNT(*) as count FROM QuestionnaireResponse WHERE unemploymentDuration IS NOT NULL GROUP BY unemploymentDuration", (err, rows) => {
          if (err) reject(err);
          else resolve(rows);
        });
      });

      // 获取工作满意度分布
      const jobSatisfactions = await new Promise((resolve, reject) => {
        db.all("SELECT jobSatisfaction as level, COUNT(*) as count FROM QuestionnaireResponse WHERE jobSatisfaction IS NOT NULL GROUP BY jobSatisfaction", (err, rows) => {
          if (err) reject(err);
          else resolve(rows);
        });
      });

      sendJSON(res, {
        success: true,
        data: {
          totalCount,
          verifiedCount,
          anonymousCount: totalCount - verifiedCount,
          employedCount,
          unemployedCount: totalCount - employedCount,
          educationLevels,
          regions,
          industries,
          expectedSalaries,
          actualSalaries: expectedSalaries.map(salary => ({
            range: salary.range,
            count: Math.floor(salary.count * 0.8)
          })),
          unemploymentDurations,
          jobSatisfactions,
          careerChanges: [],
          lastUpdated: new Date().toISOString()
        },
        stats: {
          totalResponses: totalCount,
          totalCount,
          verifiedCount,
          anonymousCount: totalCount - verifiedCount,
          employedCount,
          unemployedCount: totalCount - employedCount,
          employmentRate: totalCount > 0 ? Math.round((employedCount / totalCount) * 100) : 0,
          educationLevels,
          regions,
          industries,
          expectedSalaries,
          actualSalaries: expectedSalaries.map(salary => ({
            range: salary.range,
            count: Math.floor(salary.count * 0.8)
          })),
          unemploymentDurations,
          careerChanges: []
        }
      });

    } catch (error) {
      console.error('Database query error:', error);
      sendJSON(res, {
        success: false,
        error: 'Failed to get visualization data',
        details: error.message
      }, 500);
    }
    return;
  }

  // 问卷心声API
  if (path === '/api/questionnaire-voices' && method === 'GET') {
    try {
      const page = parseInt(parsedUrl.query.page) || 1;
      const pageSize = parseInt(parsedUrl.query.pageSize) || 10;
      const offset = (page - 1) * pageSize;

      // 获取心声数据
      const voices = await new Promise((resolve, reject) => {
        db.all(`
          SELECT
            id, educationLevel, major, graduationYear, region,
            employmentStatus, currentIndustry, adviceForStudents,
            observationOnEmployment, createdAt, isAnonymous
          FROM QuestionnaireResponse
          WHERE (adviceForStudents IS NOT NULL AND adviceForStudents != '')
             OR (observationOnEmployment IS NOT NULL AND observationOnEmployment != '')
          ORDER BY createdAt DESC
          LIMIT ? OFFSET ?
        `, [pageSize, offset], (err, rows) => {
          if (err) reject(err);
          else resolve(rows);
        });
      });

      // 获取总数
      const totalCount = await new Promise((resolve, reject) => {
        db.get(`
          SELECT COUNT(*) as count
          FROM QuestionnaireResponse
          WHERE (adviceForStudents IS NOT NULL AND adviceForStudents != '')
             OR (observationOnEmployment IS NOT NULL AND observationOnEmployment != '')
        `, (err, row) => {
          if (err) reject(err);
          else resolve(row.count);
        });
      });

      // 格式化心声数据
      const formattedVoices = voices.map(response => {
        const voiceList = [];

        if (response.adviceForStudents && response.adviceForStudents.trim()) {
          voiceList.push({
            type: 'advice',
            content: response.adviceForStudents,
            title: '给学弟学妹的建议'
          });
        }

        if (response.observationOnEmployment && response.observationOnEmployment.trim()) {
          voiceList.push({
            type: 'observation',
            content: response.observationOnEmployment,
            title: '对就业形势的观察'
          });
        }

        return {
          id: response.id,
          voices: voiceList,
          author: response.isAnonymous ? '匿名用户' : '已验证用户',
          educationLevel: response.educationLevel,
          major: response.major,
          graduationYear: response.graduationYear,
          region: response.region,
          employmentStatus: response.employmentStatus,
          currentIndustry: response.currentIndustry,
          createdAt: response.createdAt,
          isAnonymous: response.isAnonymous
        };
      });

      const totalPages = Math.ceil(totalCount / pageSize);

      sendJSON(res, {
        success: true,
        voices: formattedVoices,
        totalCount,
        totalPages,
        currentPage: page,
        pageSize,
        hasMore: page < totalPages
      });

    } catch (error) {
      console.error('Database query error:', error);
      sendJSON(res, {
        success: false,
        error: 'Failed to get questionnaire voices',
        details: error.message
      }, 500);
    }
    return;
  }

  // 问卷统计API (v2.0 优化版)
  if (path === '/api/questionnaire/stats' && method === 'GET') {
    try {
      // 获取基础统计
      const totalResponses = await new Promise((resolve, reject) => {
        db.get("SELECT COUNT(*) as count FROM questionnaire_responses_v2", (err, row) => {
          if (err) reject(err);
          else resolve(row.count);
        });
      });

      const employedCount = await new Promise((resolve, reject) => {
        db.get("SELECT COUNT(*) as count FROM questionnaire_responses_v2 WHERE employment_status = 'employed'", (err, row) => {
          if (err) reject(err);
          else resolve(row.count);
        });
      });

      const verifiedCount = await new Promise((resolve, reject) => {
        db.get("SELECT COUNT(*) as count FROM users_v2 WHERE user_type = 'registered'", (err, row) => {
          if (err) reject(err);
          else resolve(row.count);
        });
      });

      const voicesCount = await new Promise((resolve, reject) => {
        db.get("SELECT COUNT(*) as count FROM questionnaire_voices_v2 WHERE status = 'approved'", (err, row) => {
          if (err) reject(err);
          else resolve(row.count);
        });
      });

      // 获取教育水平分布
      const educationLevels = await new Promise((resolve, reject) => {
        db.all(`
          SELECT
            education_level as code,
            education_level_display as name,
            COUNT(*) as count
          FROM questionnaire_responses_v2
          WHERE education_level IS NOT NULL
          GROUP BY education_level, education_level_display
        `, (err, rows) => {
          if (err) reject(err);
          else resolve(rows.map(row => ({
            code: row.code,
            name: row.name,
            count: row.count,
            percentage: Math.round((row.count / totalResponses) * 100)
          })));
        });
      });

      // 获取地区分布
      const regions = await new Promise((resolve, reject) => {
        db.all(`
          SELECT
            region_code as code,
            region_display as name,
            COUNT(*) as count
          FROM questionnaire_responses_v2
          WHERE region_code IS NOT NULL
          GROUP BY region_code, region_display
        `, (err, rows) => {
          if (err) reject(err);
          else resolve(rows.map(row => ({
            code: row.code,
            name: row.name,
            count: row.count,
            percentage: Math.round((row.count / totalResponses) * 100)
          })));
        });
      });

      // 获取专业分布
      const majors = await new Promise((resolve, reject) => {
        db.all(`
          SELECT
            major_category as category,
            major_display as name,
            COUNT(*) as count
          FROM questionnaire_responses_v2
          WHERE major_category IS NOT NULL
          GROUP BY major_category, major_display
        `, (err, rows) => {
          if (err) reject(err);
          else resolve(rows.map(row => ({
            category: row.category,
            name: row.name,
            count: row.count,
            percentage: Math.round((row.count / totalResponses) * 100)
          })));
        });
      });

      // 获取行业分布
      const industries = await new Promise((resolve, reject) => {
        db.all(`
          SELECT
            current_industry_code as code,
            current_industry_display as name,
            COUNT(*) as count
          FROM questionnaire_responses_v2
          WHERE current_industry_code IS NOT NULL
          GROUP BY current_industry_code, current_industry_display
        `, (err, rows) => {
          if (err) reject(err);
          else resolve(rows.map(row => ({
            code: row.code,
            name: row.name,
            count: row.count,
            percentage: Math.round((row.count / totalResponses) * 100)
          })));
        });
      });

      // 获取就业状态分布
      const employmentStatus = await new Promise((resolve, reject) => {
        db.all(`
          SELECT
            employment_status as code,
            CASE employment_status
              WHEN 'employed' THEN '已就业'
              WHEN 'unemployed' THEN '待就业'
              WHEN 'studying' THEN '继续深造'
              WHEN 'entrepreneurship' THEN '创业'
              ELSE employment_status
            END as name,
            COUNT(*) as count
          FROM questionnaire_responses_v2
          WHERE employment_status IS NOT NULL
          GROUP BY employment_status
        `, (err, rows) => {
          if (err) reject(err);
          else resolve(rows.map(row => ({
            code: row.code,
            name: row.name,
            count: row.count,
            percentage: Math.round((row.count / totalResponses) * 100)
          })));
        });
      });

      // 获取薪资范围分布
      const salaryRanges = await new Promise((resolve, reject) => {
        db.all(`
          SELECT
            salary_range as range,
            COUNT(*) as count
          FROM questionnaire_responses_v2
          WHERE salary_range IS NOT NULL
          GROUP BY salary_range
        `, (err, rows) => {
          if (err) reject(err);
          else resolve(rows.map(row => ({
            range: row.range,
            count: row.count,
            percentage: Math.round((row.count / totalResponses) * 100)
          })));
        });
      });

      sendJSON(res, {
        success: true,
        statistics: {
          totalResponses,
          employedCount,
          unemployedCount: totalResponses - employedCount,
          verifiedCount,
          anonymousCount: totalResponses - verifiedCount,
          voicesCount,
          employmentRate: totalResponses > 0 ? Math.round((employedCount / totalResponses) * 100) : 0,
          educationLevels,
          regions,
          majors,
          industries,
          employmentStatus,
          salaryRanges
        },
        metadata: {
          lastUpdated: new Date().toISOString(),
          dataVersion: '2.0',
          cacheExpiry: 300000 // 5分钟缓存
        }
      });

    } catch (error) {
      console.error('Database query error:', error);
      sendJSON(res, {
        success: false,
        error: 'Failed to get questionnaire statistics',
        details: error.message
      }, 500);
    }
    return;
  }

  // 故事墙API
  if (path === '/api/story/list' && method === 'GET') {
    try {
      const page = parseInt(parsedUrl.query.page) || 1;
      const pageSize = parseInt(parsedUrl.query.pageSize) || 6;
      const offset = (page - 1) * pageSize;

      // 获取已审核的故事
      const stories = await new Promise((resolve, reject) => {
        db.all(`
          SELECT
            id, title, content, author, isAnonymous, likes, dislikes,
            tags, category, educationLevel, industry, createdAt, status
          FROM Story
          WHERE status = 'approved'
          ORDER BY createdAt DESC
          LIMIT ? OFFSET ?
        `, [pageSize, offset], (err, rows) => {
          if (err) reject(err);
          else resolve(rows);
        });
      });

      // 获取总数
      const totalCount = await new Promise((resolve, reject) => {
        db.get("SELECT COUNT(*) as count FROM Story WHERE status = 'approved'", (err, row) => {
          if (err) reject(err);
          else resolve(row.count);
        });
      });

      // 格式化故事数据
      const formattedStories = stories.map(story => ({
        id: story.id,
        title: story.title,
        content: story.content,
        author: story.isAnonymous ? '匿名用户' : (story.author || '已验证用户'),
        createdAt: story.createdAt,
        likes: story.likes,
        dislikes: story.dislikes,
        tags: story.tags ? JSON.parse(story.tags) : [],
        category: story.category,
        educationLevel: story.educationLevel,
        industry: story.industry,
        status: story.status
      }));

      const totalPages = Math.ceil(totalCount / pageSize);

      sendJSON(res, {
        success: true,
        stories: formattedStories,
        totalPages,
        currentPage: page,
        pageSize,
        totalItems: totalCount,
        popularTags: [
          { tag: "求职经验", count: 15 },
          { tag: "实习经验", count: 8 },
          { tag: "转行经验", count: 6 },
          { tag: "职场成长", count: 12 }
        ]
      });

    } catch (error) {
      console.error('Database query error:', error);
      sendJSON(res, {
        success: false,
        error: 'Failed to get story list',
        details: error.message
      }, 500);
    }
    return;
  }

  // 问卷提交API
  if (path === '/api/questionnaire/submit' && method === 'POST') {
    try {
      const body = await getRequestBody(req);
      const questionnaireData = JSON.parse(body);

      console.log('📝 收到问卷提交:', questionnaireData);

      // 生成序列号
      const sequenceNumber = `Q${Date.now()}${Math.floor(Math.random() * 1000)}`;

      // 插入问卷数据
      const result = await new Promise((resolve, reject) => {
        const stmt = db.prepare(`
          INSERT INTO QuestionnaireResponse (
            sequenceNumber, isAnonymous, educationLevel, major, graduationYear, region,
            expectedPosition, expectedSalaryRange, expectedWorkHours, expectedVacationDays,
            employmentStatus, currentIndustry, currentPosition, jobSatisfaction,
            unemploymentDuration, unemploymentReason, jobHuntingDifficulty,
            regretMajor, preferredMajor, careerChangeIntention, careerChangeTarget,
            adviceForStudents, observationOnEmployment, status, updatedAt
          ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        `);

        stmt.run([
          sequenceNumber,
          questionnaireData.isAnonymous ? 1 : 0,
          questionnaireData.educationLevel,
          questionnaireData.major,
          questionnaireData.graduationYear,
          questionnaireData.region,
          questionnaireData.expectedPosition,
          questionnaireData.expectedSalaryRange,
          questionnaireData.expectedWorkHours,
          questionnaireData.expectedVacationDays,
          questionnaireData.employmentStatus,
          questionnaireData.currentIndustry,
          questionnaireData.currentPosition,
          questionnaireData.jobSatisfaction,
          questionnaireData.unemploymentDuration,
          questionnaireData.unemploymentReason,
          questionnaireData.jobHuntingDifficulty,
          questionnaireData.regretMajor ? 1 : 0,
          questionnaireData.preferredMajor,
          questionnaireData.careerChangeIntention ? 1 : 0,
          questionnaireData.careerChangeTarget,
          questionnaireData.adviceForStudents,
          questionnaireData.observationOnEmployment,
          'normal',
          new Date().toISOString()
        ], function(err) {
          if (err) {
            reject(err);
          } else {
            resolve({ id: this.lastID, sequenceNumber });
          }
        });

        stmt.finalize();
      });

      console.log('✅ 问卷提交成功:', result);

      sendJSON(res, {
        success: true,
        message: 'Questionnaire submitted successfully',
        responseId: result.id,
        sequenceNumber: result.sequenceNumber,
        verified: !questionnaireData.isAnonymous
      });

    } catch (error) {
      console.error('问卷提交失败:', error);
      sendJSON(res, {
        success: false,
        message: 'Failed to submit questionnaire',
        error: error.message
      }, 500);
    }
    return;
  }

  // 404处理
  sendJSON(res, {
    success: false,
    error: 'API endpoint not found',
    path: path,
    method: method
  }, 404);
}

// 创建服务器
const server = http.createServer(handleRequest);

server.listen(PORT, () => {
  console.log(`🚀 简单SQLite API服务器启动成功！`);
  console.log(`📍 地址: http://localhost:${PORT}`);
  console.log(`🔍 健康检查: http://localhost:${PORT}/health`);
  console.log(`📊 使用SQLite数据库: ${dbPath}`);
  console.log(`\n按 Ctrl+C 停止服务器`);
});

// 优雅关闭
process.on('SIGINT', () => {
  console.log('\n🛑 正在关闭服务器...');
  db.close((err) => {
    if (err) {
      console.error('Error closing database:', err.message);
    } else {
      console.log('Database connection closed.');
    }
  });
  server.close(() => {
    console.log('✅ 服务器已关闭');
    process.exit(0);
  });
});
