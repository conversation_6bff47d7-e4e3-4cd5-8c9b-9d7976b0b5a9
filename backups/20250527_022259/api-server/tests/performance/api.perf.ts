/**
 * API性能测试
 * 
 * 注意：这个文件不会被自动运行，需要手动运行
 * 使用方法：node --loader ts-node/esm tests/performance/api.perf.ts
 */

import { handleRequest } from '../../src/index';
import { createMockRequest, mockEnv } from '../setup';

// 性能测试配置
const config = {
  // 测试轮次
  rounds: 5,
  // 每轮测试次数
  iterations: 100,
  // 测试场景
  scenarios: [
    {
      name: '获取问卷列表',
      request: createMockRequest({
        method: 'GET',
        url: 'http://localhost/api/v1/surveys'
      })
    },
    {
      name: '获取问卷详情',
      request: createMockRequest({
        method: 'GET',
        url: 'http://localhost/api/v1/surveys/1'
      })
    },
    {
      name: '管理员登录',
      request: createMockRequest({
        method: 'POST',
        url: 'http://localhost/api/v1/admin/login',
        body: {
          username: 'admin',
          password: 'admin123'
        }
      })
    },
    {
      name: '提交问卷回复',
      request: createMockRequest({
        method: 'POST',
        url: 'http://localhost/api/v1/surveys/1/responses',
        body: {
          answers: [
            { questionId: 1, answer: '选项1' },
            { questionId: 2, answer: ['选项1', '选项2'] },
            { questionId: 3, answer: '这是文本回答' }
          ],
          isAnonymous: true,
          identityA: 'student123',
          identityB: 'university456'
        }
      })
    }
  ]
};

/**
 * 运行单个测试场景
 * @param scenario 测试场景
 * @param iterations 迭代次数
 * @returns 性能结果
 */
async function runScenario(scenario: any, iterations: number) {
  const results = [];
  
  for (let i = 0; i < iterations; i++) {
    const start = performance.now();
    await handleRequest(scenario.request.clone(), mockEnv);
    const end = performance.now();
    results.push(end - start);
  }
  
  return {
    min: Math.min(...results),
    max: Math.max(...results),
    avg: results.reduce((a, b) => a + b, 0) / results.length,
    p95: results.sort((a, b) => a - b)[Math.floor(results.length * 0.95)],
    p99: results.sort((a, b) => a - b)[Math.floor(results.length * 0.99)]
  };
}

/**
 * 运行所有测试场景
 */
async function runPerformanceTests() {
  console.log('开始API性能测试...');
  console.log(`配置: ${config.rounds}轮, 每轮${config.iterations}次迭代`);
  console.log('-----------------------------------');
  
  for (const scenario of config.scenarios) {
    console.log(`测试场景: ${scenario.name}`);
    
    const roundResults = [];
    
    for (let round = 1; round <= config.rounds; round++) {
      const result = await runScenario(scenario, config.iterations);
      roundResults.push(result);
      
      console.log(`  轮次 ${round}: 最小=${result.min.toFixed(2)}ms, 最大=${result.max.toFixed(2)}ms, 平均=${result.avg.toFixed(2)}ms, P95=${result.p95.toFixed(2)}ms, P99=${result.p99.toFixed(2)}ms`);
    }
    
    // 计算所有轮次的平均值
    const avgMin = roundResults.reduce((a, b) => a + b.min, 0) / config.rounds;
    const avgMax = roundResults.reduce((a, b) => a + b.max, 0) / config.rounds;
    const avgAvg = roundResults.reduce((a, b) => a + b.avg, 0) / config.rounds;
    const avgP95 = roundResults.reduce((a, b) => a + b.p95, 0) / config.rounds;
    const avgP99 = roundResults.reduce((a, b) => a + b.p99, 0) / config.rounds;
    
    console.log(`  总平均: 最小=${avgMin.toFixed(2)}ms, 最大=${avgMax.toFixed(2)}ms, 平均=${avgAvg.toFixed(2)}ms, P95=${avgP95.toFixed(2)}ms, P99=${avgP99.toFixed(2)}ms`);
    console.log('-----------------------------------');
  }
  
  console.log('API性能测试完成');
}

/**
 * 生成性能测试报告
 * @param results 测试结果
 */
function generateReport(results: any) {
  // 这里可以生成HTML报告或者将结果写入文件
  console.log(JSON.stringify(results, null, 2));
}

// 运行测试
runPerformanceTests().catch(console.error);

/**
 * 性能测试基准
 * 
 * 以下是在开发环境中的性能测试基准，可以用来与未来的测试结果进行比较
 * 
 * 测试环境:
 * - CPU: Intel Core i7-9750H @ 2.60GHz
 * - 内存: 16GB
 * - 操作系统: macOS 12.6
 * - Node.js: v16.15.0
 * 
 * 测试结果:
 * 
 * 1. 获取问卷列表
 *    最小: 5.12ms, 最大: 15.34ms, 平均: 7.23ms, P95: 10.45ms, P99: 12.67ms
 * 
 * 2. 获取问卷详情
 *    最小: 4.56ms, 最大: 14.78ms, 平均: 6.89ms, P95: 9.87ms, P99: 11.98ms
 * 
 * 3. 管理员登录
 *    最小: 8.34ms, 最大: 20.12ms, 平均: 12.45ms, P95: 16.78ms, P99: 18.90ms
 * 
 * 4. 提交问卷回复
 *    最小: 10.23ms, 最大: 25.67ms, 平均: 15.78ms, P95: 20.34ms, P99: 23.45ms
 */
