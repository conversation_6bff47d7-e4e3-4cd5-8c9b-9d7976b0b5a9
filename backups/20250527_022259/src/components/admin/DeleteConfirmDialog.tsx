import { useState } from 'react';
import { useToast } from '@/components/ui/use-toast';
import { Button } from '@/components/ui/button';
import { 
  Dialog, 
  DialogContent, 
  DialogDescription, 
  DialogFooter, 
  DialogHeader, 
  DialogTitle 
} from '@/components/ui/dialog';
import { AlertTriangle, Loader2 } from 'lucide-react';

// 组件属性类型
interface DeleteConfirmDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onConfirm: () => Promise<void>;
  title?: string;
  description?: string;
  itemName?: string;
  isBulkDelete?: boolean;
  count?: number;
}

export default function DeleteConfirmDialog({
  open,
  onOpenChange,
  onConfirm,
  title = '确认删除',
  description = '此操作不可撤销，删除后数据将无法恢复。',
  itemName = '记录',
  isBulkDelete = false,
  count = 0,
}: DeleteConfirmDialogProps) {
  const { toast } = useToast();
  const [isDeleting, setIsDeleting] = useState(false);
  
  // 处理确认删除
  const handleConfirm = async () => {
    try {
      setIsDeleting(true);
      await onConfirm();
      
      toast({
        title: '删除成功',
        description: isBulkDelete 
          ? `已成功删除 ${count} 条${itemName}`
          : `${itemName}已成功删除`,
      });
      
      onOpenChange(false);
    } catch (error) {
      console.error('删除错误:', error);
      toast({
        variant: 'destructive',
        title: '删除失败',
        description: '服务器错误，请稍后再试',
      });
    } finally {
      setIsDeleting(false);
    }
  };
  
  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-md">
        <DialogHeader>
          <div className="flex items-center gap-2">
            <AlertTriangle className="h-5 w-5 text-red-500" />
            <DialogTitle>{title}</DialogTitle>
          </div>
          <DialogDescription>
            {isBulkDelete 
              ? `您确定要删除选中的 ${count} 条${itemName}吗？${description}`
              : `您确定要删除此${itemName}吗？${description}`
            }
          </DialogDescription>
        </DialogHeader>
        
        <DialogFooter className="gap-2 sm:gap-0">
          <Button
            type="button"
            variant="outline"
            onClick={() => onOpenChange(false)}
            disabled={isDeleting}
          >
            取消
          </Button>
          <Button
            type="button"
            variant="destructive"
            onClick={handleConfirm}
            disabled={isDeleting}
          >
            {isDeleting && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
            {isDeleting ? '删除中...' : '确认删除'}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
