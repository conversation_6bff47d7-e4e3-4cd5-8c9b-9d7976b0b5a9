/**
 * 问卷服务
 * 
 * 提供问卷相关的功能
 */

import { fetchData } from './unifiedDataService';

/**
 * 获取问卷列表
 * @param params 请求参数
 * @returns 问卷列表
 */
export async function getSurveys(params: any = {}) {
  return fetchData(
    '/surveys',
    'getSurveysMock',
    params,
    { method: 'GET' }
  );
}

/**
 * 获取问卷详情
 * @param id 问卷ID
 * @returns 问卷详情
 */
export async function getSurveyDetail(id: number) {
  return fetchData(
    `/surveys/${id}`,
    'getSurveyDetailMock',
    { id },
    { method: 'GET' }
  );
}

/**
 * 获取问卷回复列表
 * @param params 请求参数
 * @returns 问卷回复列表
 */
export async function getSurveyResponses(params: any = {}) {
  return fetchData(
    '/survey-responses',
    'getSurveyResponsesMock',
    params,
    { method: 'GET' }
  );
}

/**
 * 获取问卷回复详情
 * @param id 问卷回复ID
 * @returns 问卷回复详情
 */
export async function getSurveyResponseDetail(id: number) {
  return fetchData(
    `/survey-responses/${id}`,
    'getSurveyResponseDetailMock',
    { id },
    { method: 'GET' }
  );
}

/**
 * 创建问卷
 * @param surveyData 问卷数据
 * @returns 创建结果
 */
export async function createSurvey(surveyData: {
  title: string;
  description: string;
  questions: Array<{
    type: 'radio' | 'checkbox' | 'text';
    question: string;
    options?: string[];
    required: boolean;
  }>;
}) {
  return fetchData(
    '/surveys',
    'createSurveyMock',
    surveyData,
    { method: 'POST' }
  );
}

/**
 * 更新问卷
 * @param id 问卷ID
 * @param surveyData 问卷数据
 * @returns 更新结果
 */
export async function updateSurvey(
  id: number,
  surveyData: {
    title?: string;
    description?: string;
    questions?: Array<{
      type: 'radio' | 'checkbox' | 'text';
      question: string;
      options?: string[];
      required: boolean;
    }>;
    status?: 'active' | 'inactive';
  }
) {
  return fetchData(
    `/surveys/${id}`,
    'updateSurveyMock',
    { id, ...surveyData },
    { method: 'PUT' }
  );
}

/**
 * 删除问卷
 * @param id 问卷ID
 * @returns 删除结果
 */
export async function deleteSurvey(id: number) {
  return fetchData(
    `/surveys/${id}`,
    'deleteSurveyMock',
    { id },
    { method: 'DELETE' }
  );
}

/**
 * 提交问卷回复
 * @param surveyId 问卷ID
 * @param responseData 回复数据
 * @returns 提交结果
 */
export async function submitSurveyResponse(
  surveyId: number,
  responseData: {
    answers: Array<{
      questionId: number;
      answer: string | string[];
    }>;
    isAnonymous: boolean;
    identityA: string;
    identityB: string;
  }
) {
  return fetchData(
    `/surveys/${surveyId}/responses`,
    'submitSurveyResponseMock',
    { surveyId, ...responseData },
    { method: 'POST' }
  );
}

/**
 * 审核问卷回复
 * @param id 问卷回复ID
 * @param status 审核状态
 * @param reason 拒绝原因
 * @returns 审核结果
 */
export async function moderateSurveyResponse(
  id: number,
  status: 'verified' | 'rejected',
  reason?: string
) {
  return fetchData(
    `/admin/survey-responses/${id}/moderate`,
    'moderateSurveyResponseMock',
    { id, status, reason },
    { method: 'POST' }
  );
}
