/**
 * 测试数据重置管理器
 * 
 * 支持智能重置、快照恢复、状态回滚等功能
 */

const { PrismaClient } = require('@prisma/client');
const fs = require('fs').promises;
const path = require('path');
const { TestDataManager } = require('./test-data-manager');

const prisma = new PrismaClient();

/**
 * 测试数据重置管理器
 */
class TestDataResetManager extends TestDataManager {
  
  /**
   * 重置到初始状态
   */
  async resetToInitialState() {
    console.log('🔄 重置测试数据到初始状态...');
    
    try {
      // 重置故事状态
      await this.resetStoryStates();
      
      // 重置用户状态
      await this.resetUserStates();
      
      // 重置投票数据
      await this.resetVoteData();
      
      // 重置问卷状态
      await this.resetQuestionnaireStates();
      
      console.log('✅ 测试数据已重置到初始状态');
      
      // 创建重置后快照
      await this.createSnapshot('reset-to-initial', '重置到初始状态后的快照');
      
      return await this.getStatus();
    } catch (error) {
      console.error('❌ 重置失败:', error);
      throw error;
    }
  }

  /**
   * 重置故事状态
   */
  async resetStoryStates() {
    console.log('📖 重置故事审核状态...');
    
    // 将所有测试故事重置为待审核状态
    const result = await prisma.story.updateMany({
      where: { 
        isTestData: true 
      },
      data: {
        status: 'pending',
        likes: 0,
        dislikes: 0,
        updatedAt: new Date()
      }
    });
    
    console.log(`✅ 重置了 ${result.count} 个故事的状态`);
    return result;
  }

  /**
   * 重置用户状态
   */
  async resetUserStates() {
    console.log('👥 重置用户状态...');
    
    // 重置用户登录状态和活动数据
    const result = await prisma.user.updateMany({
      where: { 
        isTestData: true 
      },
      data: {
        lastLoginAt: null,
        updatedAt: new Date()
      }
    });
    
    console.log(`✅ 重置了 ${result.count} 个用户的状态`);
    return result;
  }

  /**
   * 重置投票数据
   */
  async resetVoteData() {
    console.log('🗳️ 清除投票数据...');
    
    // 删除所有测试相关的投票
    const result = await prisma.vote.deleteMany({
      where: { 
        isTestData: true 
      }
    });
    
    console.log(`✅ 清除了 ${result.count} 条投票记录`);
    return result;
  }

  /**
   * 重置问卷状态
   */
  async resetQuestionnaireStates() {
    console.log('📋 重置问卷状态...');
    
    // 重置问卷状态为正常
    const result = await prisma.questionnaireResponse.updateMany({
      where: { 
        isTestData: true 
      },
      data: {
        status: 'normal',
        updatedAt: new Date()
      }
    });
    
    console.log(`✅ 重置了 ${result.count} 份问卷的状态`);
    return result;
  }

  /**
   * 智能重置 - 只重置被修改的数据
   */
  async smartReset() {
    console.log('🧠 执行智能重置...');
    
    try {
      // 检测被修改的数据
      const modifiedData = await this.detectModifiedData();
      
      console.log('🔍 检测到修改的数据:', modifiedData);
      
      // 只重置被修改的数据
      if (modifiedData.stories.length > 0) {
        await this.resetSpecificStories(modifiedData.stories);
      }
      
      if (modifiedData.users.length > 0) {
        await this.resetSpecificUsers(modifiedData.users);
      }
      
      if (modifiedData.questionnaires.length > 0) {
        await this.resetSpecificQuestionnaires(modifiedData.questionnaires);
      }
      
      console.log('✅ 智能重置完成');
      return modifiedData;
    } catch (error) {
      console.error('❌ 智能重置失败:', error);
      throw error;
    }
  }

  /**
   * 检测被修改的数据
   */
  async detectModifiedData() {
    // 检测状态不是初始状态的故事
    const modifiedStories = await prisma.story.findMany({
      where: {
        isTestData: true,
        OR: [
          { status: { not: 'pending' } },
          { likes: { gt: 0 } },
          { dislikes: { gt: 0 } }
        ]
      },
      select: { id: true, title: true, status: true, likes: true, dislikes: true }
    });

    // 检测有登录记录的用户
    const modifiedUsers = await prisma.user.findMany({
      where: {
        isTestData: true,
        lastLoginAt: { not: null }
      },
      select: { id: true, username: true, lastLoginAt: true }
    });

    // 检测状态被修改的问卷
    const modifiedQuestionnaires = await prisma.questionnaireResponse.findMany({
      where: {
        isTestData: true,
        status: { not: 'normal' }
      },
      select: { id: true, sequenceNumber: true, status: true }
    });

    return {
      stories: modifiedStories,
      users: modifiedUsers,
      questionnaires: modifiedQuestionnaires
    };
  }

  /**
   * 重置特定故事
   */
  async resetSpecificStories(storyIds) {
    console.log(`📖 重置 ${storyIds.length} 个特定故事...`);
    
    const result = await prisma.story.updateMany({
      where: {
        id: { in: storyIds.map(s => s.id) }
      },
      data: {
        status: 'pending',
        likes: 0,
        dislikes: 0,
        updatedAt: new Date()
      }
    });
    
    console.log(`✅ 重置了 ${result.count} 个故事`);
    return result;
  }

  /**
   * 重置特定用户
   */
  async resetSpecificUsers(userIds) {
    console.log(`👥 重置 ${userIds.length} 个特定用户...`);
    
    const result = await prisma.user.updateMany({
      where: {
        id: { in: userIds.map(u => u.id) }
      },
      data: {
        lastLoginAt: null,
        updatedAt: new Date()
      }
    });
    
    console.log(`✅ 重置了 ${result.count} 个用户`);
    return result;
  }

  /**
   * 重置特定问卷
   */
  async resetSpecificQuestionnaires(questionnaireIds) {
    console.log(`📋 重置 ${questionnaireIds.length} 份特定问卷...`);
    
    const result = await prisma.questionnaireResponse.updateMany({
      where: {
        id: { in: questionnaireIds.map(q => q.id) }
      },
      data: {
        status: 'normal',
        updatedAt: new Date()
      }
    });
    
    console.log(`✅ 重置了 ${result.count} 份问卷`);
    return result;
  }

  /**
   * 从快照恢复
   */
  async restoreFromSnapshot(snapshotName) {
    console.log(`📸 从快照恢复: ${snapshotName}`);
    
    try {
      // 查找快照文件
      const snapshotFiles = await fs.readdir(this.snapshotDir);
      const snapshotFile = snapshotFiles.find(file => file.startsWith(snapshotName));
      
      if (!snapshotFile) {
        throw new Error(`未找到快照: ${snapshotName}`);
      }
      
      const snapshotPath = path.join(this.snapshotDir, snapshotFile);
      const snapshotData = JSON.parse(await fs.readFile(snapshotPath, 'utf8'));
      
      console.log(`📂 加载快照: ${snapshotData.name} (${snapshotData.createdAt})`);
      
      // 清除现有测试数据
      await this.clearTestData();
      
      // 恢复数据
      await this.restoreData(snapshotData.data);
      
      console.log('✅ 快照恢复完成');
      return snapshotData;
    } catch (error) {
      console.error('❌ 快照恢复失败:', error);
      throw error;
    }
  }

  /**
   * 恢复数据
   */
  async restoreData(data) {
    console.log('🔄 恢复数据...');
    
    // 恢复用户
    if (data.users && data.users.length > 0) {
      for (const user of data.users) {
        const { id, ...userData } = user;
        await prisma.user.create({ data: userData });
      }
      console.log(`✅ 恢复了 ${data.users.length} 个用户`);
    }
    
    // 恢复问卷
    if (data.questionnaires && data.questionnaires.length > 0) {
      for (const questionnaire of data.questionnaires) {
        const { id, ...questionnaireData } = questionnaire;
        await prisma.questionnaireResponse.create({ data: questionnaireData });
      }
      console.log(`✅ 恢复了 ${data.questionnaires.length} 份问卷`);
    }
    
    // 恢复故事
    if (data.stories && data.stories.length > 0) {
      for (const story of data.stories) {
        const { id, ...storyData } = story;
        await prisma.story.create({ data: storyData });
      }
      console.log(`✅ 恢复了 ${data.stories.length} 个故事`);
    }
    
    // 恢复投票
    if (data.votes && data.votes.length > 0) {
      for (const vote of data.votes) {
        const { id, ...voteData } = vote;
        await prisma.vote.create({ data: voteData });
      }
      console.log(`✅ 恢复了 ${data.votes.length} 条投票`);
    }
  }

  /**
   * 列出所有快照
   */
  async listSnapshots() {
    try {
      await this.initDirectories();
      const files = await fs.readdir(this.snapshotDir);
      const snapshots = [];
      
      for (const file of files) {
        if (file.endsWith('.json')) {
          const filePath = path.join(this.snapshotDir, file);
          const content = JSON.parse(await fs.readFile(filePath, 'utf8'));
          snapshots.push({
            file,
            name: content.name,
            description: content.description,
            createdAt: content.createdAt,
            checksum: content.checksum
          });
        }
      }
      
      return snapshots.sort((a, b) => new Date(b.createdAt) - new Date(a.createdAt));
    } catch (error) {
      console.error('❌ 获取快照列表失败:', error);
      return [];
    }
  }

  /**
   * 删除旧快照
   */
  async cleanupOldSnapshots(maxSnapshots = 10) {
    console.log(`🧹 清理旧快照 (保留最新 ${maxSnapshots} 个)...`);
    
    try {
      const snapshots = await this.listSnapshots();
      
      if (snapshots.length > maxSnapshots) {
        const toDelete = snapshots.slice(maxSnapshots);
        
        for (const snapshot of toDelete) {
          const filePath = path.join(this.snapshotDir, snapshot.file);
          await fs.unlink(filePath);
          console.log(`🗑️ 删除快照: ${snapshot.name}`);
        }
        
        console.log(`✅ 清理了 ${toDelete.length} 个旧快照`);
      } else {
        console.log('✅ 无需清理快照');
      }
    } catch (error) {
      console.error('❌ 清理快照失败:', error);
    }
  }
}

module.exports = { TestDataResetManager };
