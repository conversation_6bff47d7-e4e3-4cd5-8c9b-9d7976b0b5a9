/**
 * 真实数据库API验证测试脚本
 * 验证用户管理功能是否正确连接到真实数据库
 */

const http = require('http');

// 测试配置
const API_BASE = 'http://localhost:8789';

// 发送HTTP请求的工具函数
function makeRequest(method, path, data = null) {
  return new Promise((resolve, reject) => {
    const url = new URL(API_BASE + path);
    
    const options = {
      hostname: url.hostname,
      port: url.port,
      path: url.pathname + url.search,
      method: method,
      headers: {
        'Content-Type': 'application/json'
      }
    };

    const req = http.request(options, (res) => {
      let responseData = '';
      
      res.on('data', (chunk) => {
        responseData += chunk;
      });
      
      res.on('end', () => {
        try {
          const jsonData = JSON.parse(responseData);
          resolve({
            status: res.statusCode,
            data: jsonData
          });
        } catch (error) {
          resolve({
            status: res.statusCode,
            data: responseData,
            error: `JSON解析错误: ${error.message}`
          });
        }
      });
    });

    req.on('error', (error) => {
      reject(error);
    });

    req.setTimeout(5000, () => {
      req.destroy();
      reject(new Error('请求超时'));
    });

    if (data) {
      req.write(JSON.stringify(data));
    }
    
    req.end();
  });
}

// 运行真实数据库API测试
async function runRealDatabaseTests() {
  console.log('🧪 开始真实数据库API验证测试...\n');
  
  const testResults = {
    passed: 0,
    failed: 0,
    total: 0
  };

  // 测试函数
  async function test(name, testFn) {
    testResults.total++;
    console.log(`🔍 测试: ${name}`);
    
    try {
      await testFn();
      testResults.passed++;
      console.log(`✅ ${name} - 通过\n`);
    } catch (error) {
      testResults.failed++;
      console.log(`❌ ${name} - 失败: ${error.message}\n`);
    }
  }

  // 1. 测试健康检查
  await test('健康检查', async () => {
    const response = await makeRequest('GET', '/health');
    if (response.status !== 200) {
      throw new Error(`状态码错误: ${response.status}`);
    }
    if (!response.data.status || response.data.status !== 'ok') {
      throw new Error('健康检查失败');
    }
    console.log('   💚 服务状态正常');
  });

  // 2. 测试获取用户列表
  await test('获取用户列表', async () => {
    const response = await makeRequest('GET', '/api/admin/users?page=1&pageSize=5');
    if (response.status !== 200) {
      throw new Error(`状态码错误: ${response.status}`);
    }
    if (!response.data.success) {
      throw new Error(response.data.error || '获取用户列表失败');
    }
    const users = response.data.data.users;
    const total = response.data.data.pagination.total;
    console.log(`   📊 获取到 ${users.length} 个用户，总计 ${total} 个用户`);
    
    // 验证数据结构
    if (users.length > 0) {
      const user = users[0];
      if (!user.id || !user.email || !user.role) {
        throw new Error('用户数据结构不完整');
      }
      console.log(`   👤 示例用户: ${user.email} (${user.role})`);
    }
  });

  // 3. 测试按角色筛选
  await test('按角色筛选用户', async () => {
    const roles = ['user', 'admin', 'reviewer', 'superadmin'];
    
    for (const role of roles) {
      const response = await makeRequest('GET', `/api/admin/users?role=${role}`);
      if (response.status !== 200) {
        throw new Error(`获取${role}角色用户失败: ${response.status}`);
      }
      const count = response.data.data.pagination.total;
      console.log(`   👑 ${role}: ${count} 个用户`);
    }
  });

  // 4. 测试搜索功能
  await test('用户搜索功能', async () => {
    const response = await makeRequest('GET', '/api/admin/users?search=test');
    if (response.status !== 200) {
      throw new Error(`搜索失败: ${response.status}`);
    }
    const count = response.data.data.pagination.total;
    console.log(`   🔍 搜索"test"找到 ${count} 个用户`);
  });

  // 5. 测试角色统计
  await test('角色统计', async () => {
    const response = await makeRequest('GET', '/api/admin/roles');
    if (response.status !== 200) {
      throw new Error(`获取角色统计失败: ${response.status}`);
    }
    if (!response.data.success) {
      throw new Error(response.data.error || '获取角色统计失败');
    }
    const roles = response.data.data;
    console.log('   📈 角色统计:');
    roles.forEach(role => {
      console.log(`      ${role.name}: ${role.userCount} 个用户`);
    });
  });

  // 6. 测试操作日志
  await test('操作日志查询', async () => {
    const response = await makeRequest('GET', '/api/admin/operation-logs?pageSize=5');
    if (response.status !== 200) {
      throw new Error(`获取操作日志失败: ${response.status}`);
    }
    if (!response.data.success) {
      throw new Error(response.data.error || '获取操作日志失败');
    }
    const logs = response.data.data.logs;
    const total = response.data.data.pagination.total;
    console.log(`   📝 获取到 ${logs.length} 条日志，总计 ${total} 条`);
    
    if (logs.length > 0) {
      const log = logs[0];
      console.log(`   📋 最新日志: ${log.adminName} ${log.action} ${log.target}`);
    }
  });

  // 7. 测试权限配置
  await test('权限配置查询', async () => {
    const response = await makeRequest('GET', '/api/admin/permissions');
    if (response.status !== 200) {
      throw new Error(`获取权限配置失败: ${response.status}`);
    }
    if (!response.data.success) {
      throw new Error(response.data.error || '获取权限配置失败');
    }
    const permissions = response.data.data;
    console.log('   🔐 权限配置:');
    Object.entries(permissions).forEach(([role, perms]) => {
      console.log(`      ${role}: ${perms.join(', ')}`);
    });
  });

  // 8. 测试创建用户
  await test('创建用户', async () => {
    const testUser = {
      username: 'api_test_user',
      email: '<EMAIL>',
      name: 'API测试用户',
      password: 'password123',
      role: 'user'
    };

    const response = await makeRequest('POST', '/api/admin/users', testUser);
    if (response.status !== 201) {
      throw new Error(`创建用户失败: ${response.status} - ${response.data.error || '未知错误'}`);
    }
    if (!response.data.success) {
      throw new Error(response.data.error || '创建用户失败');
    }
    
    const newUser = response.data.data;
    console.log(`   ✨ 创建用户成功: ID=${newUser.id}, 邮箱=${newUser.email}`);
    
    // 保存用户ID用于后续测试
    global.testUserId = newUser.id;
  });

  // 9. 测试更新用户
  await test('更新用户', async () => {
    if (!global.testUserId) {
      throw new Error('没有可更新的测试用户');
    }

    const updateData = {
      role: 'reviewer',
      name: 'API测试用户-已更新'
    };

    const response = await makeRequest('PUT', `/api/admin/users/${global.testUserId}`, updateData);
    if (response.status !== 200) {
      throw new Error(`更新用户失败: ${response.status}`);
    }
    if (!response.data.success) {
      throw new Error(response.data.error || '更新用户失败');
    }
    
    const updatedUser = response.data.data;
    console.log(`   🔄 更新用户成功: 角色=${updatedUser.role}, 姓名=${updatedUser.name}`);
  });

  // 10. 测试删除用户
  await test('删除用户', async () => {
    if (!global.testUserId) {
      throw new Error('没有可删除的测试用户');
    }

    const response = await makeRequest('DELETE', `/api/admin/users/${global.testUserId}`);
    if (response.status !== 200) {
      throw new Error(`删除用户失败: ${response.status}`);
    }
    if (!response.data.success) {
      throw new Error(response.data.error || '删除用户失败');
    }
    
    console.log('   🗑️ 删除用户成功');
  });

  // 生成测试报告
  console.log('📋 真实数据库API测试报告');
  console.log('=' * 50);
  console.log(`✅ 通过: ${testResults.passed}/${testResults.total}`);
  console.log(`❌ 失败: ${testResults.failed}/${testResults.total}`);
  console.log(`📊 成功率: ${Math.round((testResults.passed / testResults.total) * 100)}%`);
  
  if (testResults.passed === testResults.total) {
    console.log('\n🎉 所有测试通过！真实数据库API功能正常。');
    console.log('\n✨ 验证结果:');
    console.log('- ✅ 数据库连接正常');
    console.log('- ✅ 用户CRUD操作正常');
    console.log('- ✅ 搜索和筛选功能正常');
    console.log('- ✅ 角色权限管理正常');
    console.log('- ✅ 操作日志记录正常');
    console.log('- ✅ 前端可以正常调用API');
  } else {
    console.log('\n⚠️ 部分测试失败，请检查API实现。');
  }
  
  console.log('\n🌐 前端测试建议:');
  console.log('1. 访问 http://localhost:5178/superadmin/user-management');
  console.log('2. 测试用户列表显示');
  console.log('3. 测试搜索和筛选功能');
  console.log('4. 测试用户创建、编辑、删除功能');
  console.log('5. 访问 http://localhost:5178/superadmin/operation-logs 查看操作日志');
}

// 启动测试
runRealDatabaseTests().catch(console.error);
