/**
 * 内容审核控制器
 */

import { Context } from 'hono';
import { PrismaClient } from '@prisma/client';
import { ReviewService, ReviewStatus, ReviewAction, ContentType } from '../../services/reviewService';
import { AuditLogService, AuditAction, AuditSeverity } from '../../services/auditLogService';
import { getPrismaClient } from '../../utils/prisma';
import { Env } from '../../types';

/**
 * 获取待审核内容列表
 */
export const getPendingContents = async (c: Context<{ Bindings: Env }>) => {
  try {
    // 获取查询参数
    const type = c.req.query('type') as ContentType || undefined;
    const page = parseInt(c.req.query('page') || '1', 10);
    const pageSize = parseInt(c.req.query('pageSize') || '10', 10);
    const flag = c.req.query('flag') || undefined;

    // 计算分页
    const skip = (page - 1) * pageSize;

    // 构建查询条件
    const where: any = {
      status: ReviewStatus.PENDING
    };

    if (type) {
      where.type = type;
    }

    if (flag) {
      where.flags = {
        has: flag
      };
    }

    // 获取待审核内容
    const prisma = getPrismaClient(c.env);

    // 获取总数
    const total = await prisma.pendingContent.count({
      where
    });

    // 获取数据
    const pendingContents = await prisma.pendingContent.findMany({
      where,
      orderBy: {
        createdAt: 'desc'
      },
      skip,
      take: pageSize
    });

    // 返回结果
    return c.json({
      success: true,
      pendingContents,
      pagination: {
        page,
        pageSize,
        total,
        totalPages: Math.ceil(total / pageSize)
      }
    });
  } catch (error) {
    console.error('Error getting pending contents:', error);
    return c.json({
      success: false,
      error: '获取待审核内容失败'
    }, 500);
  }
};

/**
 * 获取单条待审核内容详情
 */
export const getPendingContent = async (c: Context<{ Bindings: Env }>) => {
  try {
    // 获取内容ID
    const id = c.req.param('id');

    // 获取待审核内容
    const prisma = getPrismaClient(c.env);
    const pendingContent = await prisma.pendingContent.findUnique({
      where: {
        id
      },
      include: {
        reviewLogs: {
          orderBy: {
            timestamp: 'desc'
          }
        }
      }
    });

    // 检查是否存在
    if (!pendingContent) {
      return c.json({
        success: false,
        error: '待审核内容不存在'
      }, 404);
    }

    // 返回结果
    return c.json({
      success: true,
      pendingContent
    });
  } catch (error) {
    console.error('Error getting pending content:', error);
    return c.json({
      success: false,
      error: '获取待审核内容详情失败'
    }, 500);
  }
};

/**
 * 审核通过内容
 */
export const approveContent = async (c: Context<{ Bindings: Env }>) => {
  try {
    // 获取内容ID
    const id = c.req.param('id');

    // 获取请求数据
    const data = await c.req.json();
    const { reviewNotes } = data;

    // 获取审核员信息
    const user = c.get('user');
    const reviewerId = user.username || user.id || 'unknown';

    // 获取客户端信息
    const ipAddress = c.req.headers.get('CF-Connecting-IP') || c.req.headers.get('X-Forwarded-For') || 'unknown';
    const userAgent = c.req.headers.get('User-Agent') || 'unknown';

    // 获取待审核内容
    const prisma = getPrismaClient(c.env);
    const pendingContent = await prisma.pendingContent.findUnique({
      where: {
        id
      }
    });

    // 检查是否存在
    if (!pendingContent) {
      return c.json({
        success: false,
        error: '待审核内容不存在'
      }, 404);
    }

    // 检查是否已审核
    if (pendingContent.status !== ReviewStatus.PENDING) {
      return c.json({
        success: false,
        error: '内容已审核'
      }, 400);
    }

    // 更新待审核内容状态
    await prisma.pendingContent.update({
      where: {
        id
      },
      data: {
        status: ReviewStatus.APPROVED,
        reviewerId,
        reviewedAt: new Date(),
        reviewNotes
      }
    });

    // 创建审核日志
    await prisma.reviewLog.create({
      data: {
        reviewerId,
        contentId: id,
        action: ReviewAction.APPROVE,
        reviewNotes,
        ipAddress,
        userAgent
      }
    });

    // 记录审计日志
    try {
      const auditLogService = AuditLogService.getInstance();
      if (!auditLogService.isInitialized) {
        await auditLogService.initialize();
      }

      await auditLogService.log({
        userId: reviewerId,
        action: AuditAction.CONTENT_APPROVE,
        resourceType: 'pendingContent',
        resourceId: id,
        details: {
          contentType: pendingContent.type,
          reviewNotes
        },
        ipAddress,
        userAgent,
        severity: AuditSeverity.INFO
      }, c.env);
    } catch (auditError) {
      console.error('Error creating audit log:', auditError);
      // 审计日志失败不影响主流程
    }

    // 将内容写入正式表
    let result;
    if (pendingContent.type === ContentType.STORY) {
      // 处理故事内容
      const storyData = pendingContent.sanitizedContent || pendingContent.originalContent;

      const story = await prisma.story.create({
        data: {
          userId: storyData.userId,
          isAnonymous: storyData.isAnonymous,
          title: storyData.title,
          content: storyData.content,
          ipAddress: pendingContent.originIp,
          status: 'approved'
        }
      });

      result = {
        storyId: story.id
      };
    } else if (pendingContent.type === ContentType.QUESTIONNAIRE) {
      // 处理问卷内容
      const questionnaireData = pendingContent.sanitizedContent || pendingContent.originalContent;

      const response = await prisma.questionnaireResponse.create({
        data: {
          userId: questionnaireData.userId,
          isAnonymous: questionnaireData.isAnonymous,
          // 其他问卷字段...
        }
      });

      result = {
        responseId: response.id
      };
    }

    // 返回结果
    return c.json({
      success: true,
      message: '审核通过',
      ...result
    });
  } catch (error) {
    console.error('Error approving content:', error);
    return c.json({
      success: false,
      error: '审核通过失败'
    }, 500);
  }
};

/**
 * 编辑并通过内容
 */
export const editContent = async (c: Context<{ Bindings: Env }>) => {
  try {
    // 获取内容ID
    const id = c.req.param('id');

    // 获取请求数据
    const data = await c.req.json();
    const { content, reviewNotes } = data;

    // 获取审核员信息
    const user = c.get('user');
    const reviewerId = user.username || user.id || 'unknown';

    // 获取客户端信息
    const ipAddress = c.req.headers.get('CF-Connecting-IP') || c.req.headers.get('X-Forwarded-For') || 'unknown';
    const userAgent = c.req.headers.get('User-Agent') || 'unknown';

    // 获取待审核内容
    const prisma = getPrismaClient(c.env);
    const pendingContent = await prisma.pendingContent.findUnique({
      where: {
        id
      }
    });

    // 检查是否存在
    if (!pendingContent) {
      return c.json({
        success: false,
        error: '待审核内容不存在'
      }, 404);
    }

    // 检查是否已审核
    if (pendingContent.status !== ReviewStatus.PENDING) {
      return c.json({
        success: false,
        error: '内容已审核'
      }, 400);
    }

    // 计算差异
    const originalContent = pendingContent.originalContent;
    const diff = {
      original: originalContent,
      edited: content
    };

    // 更新待审核内容
    await prisma.pendingContent.update({
      where: {
        id
      },
      data: {
        status: ReviewStatus.EDITED,
        sanitizedContent: content,
        reviewerId,
        reviewedAt: new Date(),
        reviewNotes
      }
    });

    // 创建审核日志
    await prisma.reviewLog.create({
      data: {
        reviewerId,
        contentId: id,
        action: ReviewAction.EDIT,
        diff,
        reviewNotes,
        ipAddress,
        userAgent
      }
    });

    // 记录审计日志
    try {
      const auditLogService = AuditLogService.getInstance();
      if (!auditLogService.isInitialized) {
        await auditLogService.initialize();
      }

      await auditLogService.log({
        userId: reviewerId,
        action: AuditAction.CONTENT_EDIT,
        resourceType: 'pendingContent',
        resourceId: id,
        details: {
          contentType: pendingContent.type,
          reviewNotes,
          diff
        },
        ipAddress,
        userAgent,
        severity: AuditSeverity.WARNING // 编辑操作级别更高
      }, c.env);
    } catch (auditError) {
      console.error('Error creating audit log:', auditError);
      // 审计日志失败不影响主流程
    }

    // 将内容写入正式表
    let result;
    if (pendingContent.type === ContentType.STORY) {
      // 处理故事内容
      const story = await prisma.story.create({
        data: {
          userId: content.userId,
          isAnonymous: content.isAnonymous,
          title: content.title,
          content: content.content,
          ipAddress: pendingContent.originIp,
          status: 'approved'
        }
      });

      result = {
        storyId: story.id
      };
    } else if (pendingContent.type === ContentType.QUESTIONNAIRE) {
      // 处理问卷内容
      const response = await prisma.questionnaireResponse.create({
        data: {
          userId: content.userId,
          isAnonymous: content.isAnonymous,
          // 其他问卷字段...
        }
      });

      result = {
        responseId: response.id
      };
    }

    // 返回结果
    return c.json({
      success: true,
      message: '编辑并通过',
      ...result
    });
  } catch (error) {
    console.error('Error editing content:', error);
    return c.json({
      success: false,
      error: '编辑内容失败'
    }, 500);
  }
};

/**
 * 拒绝内容
 */
export const rejectContent = async (c: Context<{ Bindings: Env }>) => {
  try {
    // 获取内容ID
    const id = c.req.param('id');

    // 获取请求数据
    const data = await c.req.json();
    const { reason } = data;

    // 获取审核员信息
    const user = c.get('user');
    const reviewerId = user.username || user.id || 'unknown';

    // 获取客户端信息
    const ipAddress = c.req.headers.get('CF-Connecting-IP') || c.req.headers.get('X-Forwarded-For') || 'unknown';
    const userAgent = c.req.headers.get('User-Agent') || 'unknown';

    // 获取待审核内容
    const prisma = getPrismaClient(c.env);
    const pendingContent = await prisma.pendingContent.findUnique({
      where: {
        id
      }
    });

    // 检查是否存在
    if (!pendingContent) {
      return c.json({
        success: false,
        error: '待审核内容不存在'
      }, 404);
    }

    // 检查是否已审核
    if (pendingContent.status !== ReviewStatus.PENDING) {
      return c.json({
        success: false,
        error: '内容已审核'
      }, 400);
    }

    // 更新待审核内容状态
    await prisma.pendingContent.update({
      where: {
        id
      },
      data: {
        status: ReviewStatus.REJECTED,
        reviewerId,
        reviewedAt: new Date(),
        reviewNotes: reason
      }
    });

    // 创建审核日志
    await prisma.reviewLog.create({
      data: {
        reviewerId,
        contentId: id,
        action: ReviewAction.REJECT,
        reviewNotes: reason,
        ipAddress,
        userAgent
      }
    });

    // 记录审计日志
    try {
      const auditLogService = AuditLogService.getInstance();
      if (!auditLogService.isInitialized) {
        await auditLogService.initialize();
      }

      await auditLogService.log({
        userId: reviewerId,
        action: AuditAction.CONTENT_REJECT,
        resourceType: 'pendingContent',
        resourceId: id,
        details: {
          contentType: pendingContent.type,
          reason
        },
        ipAddress,
        userAgent,
        severity: AuditSeverity.WARNING
      }, c.env);
    } catch (auditError) {
      console.error('Error creating audit log:', auditError);
      // 审计日志失败不影响主流程
    }

    // 返回结果
    return c.json({
      success: true,
      message: '已拒绝内容'
    });
  } catch (error) {
    console.error('Error rejecting content:', error);
    return c.json({
      success: false,
      error: '拒绝内容失败'
    }, 500);
  }
};

/**
 * 批量操作内容
 */
export const batchOperation = async (c: Context<{ Bindings: Env }>) => {
  try {
    const { ids, action, reason, reviewNotes } = await c.req.json();

    // 验证参数
    if (!ids || !Array.isArray(ids) || ids.length === 0) {
      return c.json({
        success: false,
        error: '内容ID列表是必需的'
      }, 400);
    }

    if (!action || !['approve', 'reject'].includes(action)) {
      return c.json({
        success: false,
        error: '操作类型无效，必须是 approve 或 reject'
      }, 400);
    }

    if (action === 'reject' && !reason) {
      return c.json({
        success: false,
        error: '拒绝操作需要提供拒绝原因'
      }, 400);
    }

    // 获取用户信息
    const user = c.get('user');
    const reviewerId = user.username || user.id || 'unknown';

    // 获取客户端信息
    const ipAddress = c.req.headers.get('CF-Connecting-IP') || c.req.headers.get('X-Forwarded-For') || 'unknown';
    const userAgent = c.req.headers.get('User-Agent') || 'unknown';

    // 初始化审计日志服务
    const auditLogService = AuditLogService.getInstance();
    if (!auditLogService.isInitialized) {
      await auditLogService.initialize();
    }

    // 执行批量操作
    const results = [];
    const prisma = getPrismaClient(c.env);

    for (const id of ids) {
      try {
        // 获取待审核内容
        const pendingContent = await prisma.pendingContent.findUnique({
          where: { id }
        });

        if (!pendingContent) {
          results.push({
            id,
            success: false,
            error: '内容不存在'
          });
          continue;
        }

        // 检查是否已审核
        if (pendingContent.status !== ReviewStatus.PENDING) {
          results.push({
            id,
            success: false,
            error: '内容已审核'
          });
          continue;
        }

        if (action === 'approve') {
          // 更新待审核内容状态
          await prisma.pendingContent.update({
            where: { id },
            data: {
              status: ReviewStatus.APPROVED,
              reviewerId,
              reviewedAt: new Date(),
              reviewNotes: reviewNotes || '批量通过'
            }
          });

          // 创建审核日志
          await prisma.reviewLog.create({
            data: {
              reviewerId,
              contentId: id,
              action: ReviewAction.APPROVE,
              reviewNotes: reviewNotes || '批量通过',
              ipAddress,
              userAgent
            }
          });

          // 将内容写入正式表
          if (pendingContent.type === ContentType.STORY) {
            // 处理故事内容
            const storyData = pendingContent.sanitizedContent || pendingContent.originalContent;

            await prisma.story.create({
              data: {
                userId: storyData.userId,
                isAnonymous: storyData.isAnonymous,
                title: storyData.title,
                content: storyData.content,
                ipAddress: pendingContent.originIp,
                status: 'approved'
              }
            });
          } else if (pendingContent.type === ContentType.QUESTIONNAIRE) {
            // 处理问卷内容
            const questionnaireData = pendingContent.sanitizedContent || pendingContent.originalContent;

            await prisma.questionnaireResponse.create({
              data: {
                userId: questionnaireData.userId,
                isAnonymous: questionnaireData.isAnonymous,
                // 其他问卷字段...
              }
            });
          }

          // 记录审计日志
          await auditLogService.log({
            userId: reviewerId,
            action: AuditAction.CONTENT_APPROVE,
            resourceType: 'pendingContent',
            resourceId: id,
            details: {
              contentType: pendingContent.type,
              batchOperation: true,
              reviewNotes: reviewNotes || '批量通过'
            },
            ipAddress,
            userAgent,
            severity: AuditSeverity.INFO
          }, c.env);

          results.push({
            id,
            success: true
          });
        } else if (action === 'reject') {
          // 更新待审核内容状态
          await prisma.pendingContent.update({
            where: { id },
            data: {
              status: ReviewStatus.REJECTED,
              reviewerId,
              reviewedAt: new Date(),
              reviewNotes: reason
            }
          });

          // 创建审核日志
          await prisma.reviewLog.create({
            data: {
              reviewerId,
              contentId: id,
              action: ReviewAction.REJECT,
              reviewNotes: reason,
              ipAddress,
              userAgent
            }
          });

          // 记录审计日志
          await auditLogService.log({
            userId: reviewerId,
            action: AuditAction.CONTENT_REJECT,
            resourceType: 'pendingContent',
            resourceId: id,
            details: {
              contentType: pendingContent.type,
              batchOperation: true,
              reason
            },
            ipAddress,
            userAgent,
            severity: AuditSeverity.INFO
          }, c.env);

          results.push({
            id,
            success: true
          });
        }
      } catch (error) {
        console.error(`批量操作内容错误 (ID: ${id}):`, error);
        results.push({
          id,
          success: false,
          error: '操作失败'
        });
      }
    }

    // 记录批量操作审计日志
    await auditLogService.log({
      userId: reviewerId,
      action: action === 'approve' ? AuditAction.BATCH_APPROVE : AuditAction.BATCH_REJECT,
      resourceType: 'pendingContent',
      details: {
        totalCount: ids.length,
        successCount: results.filter(r => r.success).length,
        failCount: results.filter(r => !r.success).length,
        action
      },
      ipAddress,
      userAgent,
      severity: AuditSeverity.WARNING
    }, c.env);

    return c.json({
      success: true,
      message: `批量${action === 'approve' ? '通过' : '拒绝'}操作已完成`,
      results
    });
  } catch (error) {
    console.error('批量操作内容错误:', error);
    return c.json({
      success: false,
      error: '批量操作内容失败'
    }, 500);
  }
};
