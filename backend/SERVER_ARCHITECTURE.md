# 服务器架构说明

## 问题分析

项目中存在多个服务器文件，导致端口混乱和数据不一致：

### 发现的服务器文件
- `test-server-simple.js` (8787) - 当前使用的测试服务器
- `unified-api-server.js` (8790) - 统一API服务器
- `api-server-v2.js` (8788) - API服务器v2
- `api-proxy-server.js` (8789) - API代理服务器
- 其他测试服务器文件...

### 数据库问题
- Prisma连接到 `./prisma/prisma/dev.db` (之前只有5条记录)
- 实际数据在 `./prisma/dev.db` (152条记录)
- 已修复：复制了完整数据库到Prisma期望的位置

## 解决方案

### 1. 统一服务器架构
**推荐使用单一服务器**：`test-server-simple.js` (端口8787)

### 2. 前端代理配置
```typescript
// frontend/vite.config.ts
proxy: {
  '/api': {
    target: 'http://localhost:8787',  // 统一使用8787
    changeOrigin: true,
  },
}
```

### 3. 数据库配置
```env
# backend/.env
DATABASE_URL="file:./prisma/dev.db"
```

### 4. 清理不必要的服务器
建议删除或归档以下文件：
- `api-server-v2.js`
- `api-proxy-server.js` 
- `unified-api-server.js`
- 其他测试服务器

## 当前状态

✅ **已修复**：
- 数据库连接问题（152条记录正确显示）
- 前端代理配置（指向8787端口）
- API统计数据（12个专业正确返回）

✅ **正在运行**：
- 前端：http://localhost:5173
- 后端：http://localhost:8787

## 验证命令

```bash
# 检查服务器状态
curl http://localhost:8787/health

# 检查统计数据
curl http://localhost:8787/api/questionnaire/stats | jq '.statistics.totalResponses'

# 检查专业数量
curl http://localhost:8787/api/questionnaire/stats | jq '.statistics.majors | length'
```

## 建议

1. **保持单一服务器**：只使用 `test-server-simple.js`
2. **统一端口**：所有环境都使用8787端口
3. **清理冗余文件**：删除不使用的服务器文件
4. **文档化配置**：明确记录端口和数据库配置
