import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { 
  Settings, Shield, Users, Database, Save, FileText, 
  Eye, UserCheck, BarChart2, Zap, Edit, Check, 
  RotateCcw, Plus, X, User, Activity, Lock
} from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardTitle, CardDescription } from '@/components/ui/card';
import { 
  DropdownMenu, DropdownMenuContent, DropdownMenuItem, 
  DropdownMenuLabel, DropdownMenuSeparator, DropdownMenuTrigger 
} from '@/components/ui/dropdown-menu';

// 定义快速访问项目类型
export interface QuickAccessItem {
  id: string;
  title: string;
  icon: string; // 图标名称
  path: string;
  description?: string;
}

// 默认快速访问项目
const defaultQuickAccessItems: QuickAccessItem[] = [
  {
    id: 'system-config',
    title: '系统配置',
    icon: 'Settings',
    path: '/superadmin/system-config',
    description: '配置系统参数'
  },
  {
    id: 'security',
    title: '安全监控',
    icon: 'Shield',
    path: '/superadmin/security',
    description: '查看安全状态'
  },
  {
    id: 'user-management',
    title: '用户管理',
    icon: 'Users',
    path: '/superadmin/user-management',
    description: '管理系统用户'
  },
  {
    id: 'test-data',
    title: '测试数据',
    icon: 'Database',
    path: '/superadmin/test-data-management',
    description: '管理测试数据'
  }
];

// 所有可用的快速访问项目
const allQuickAccessItems: QuickAccessItem[] = [
  ...defaultQuickAccessItems,
  {
    id: 'system-backup',
    title: '系统备份',
    icon: 'Save',
    path: '/superadmin/system-backup',
    description: '备份系统数据'
  },
  {
    id: 'security-logs',
    title: '安全日志',
    icon: 'FileText',
    path: '/superadmin/security-logs',
    description: '查看安全日志'
  },
  {
    id: 'content-review',
    title: '内容审核',
    icon: 'Eye',
    path: '/superadmin/content-review',
    description: '审核内容'
  },
  {
    id: 'documentation',
    title: '项目文档',
    icon: 'FileText',
    path: '/superadmin/documentation',
    description: '查看项目文档'
  },
  {
    id: 'role-management',
    title: '角色管理',
    icon: 'UserCheck',
    path: '/superadmin/role-management',
    description: '管理系统角色'
  },
  {
    id: 'data-analysis',
    title: '数据分析',
    icon: 'BarChart2',
    path: '/superadmin/data-analysis',
    description: '分析系统数据'
  },
  {
    id: 'system-monitor',
    title: '系统监控',
    icon: 'Activity',
    path: '/superadmin/system-monitor',
    description: '监控系统状态'
  },
  {
    id: 'security-settings',
    title: '安全设置',
    icon: 'Lock',
    path: '/superadmin/security-settings',
    description: '配置安全参数'
  }
];

// 快速访问卡片组件
const QuickAccessCard: React.FC<{
  title: string;
  icon: React.ReactNode;
  path: string;
  description?: string;
  onClick?: () => void;
}> = ({ title, icon, path, description, onClick }) => {
  const navigate = useNavigate();
  
  const handleClick = () => {
    if (onClick) {
      onClick();
    } else {
      navigate(path);
    }
  };
  
  return (
    <Card 
      className="hover:border-primary cursor-pointer transition-colors"
      onClick={handleClick}
    >
      <CardContent className="flex flex-col items-center justify-center p-4">
        <div className="text-primary mb-2">{icon}</div>
        <CardTitle className="text-center text-sm">{title}</CardTitle>
        {description && (
          <CardDescription className="text-center text-xs mt-1">
            {description}
          </CardDescription>
        )}
      </CardContent>
    </Card>
  );
};

// 获取图标组件
const getIconComponent = (iconName: string, size: number = 8) => {
  const icons: Record<string, React.ReactNode> = {
    Settings: <Settings className={`h-${size} w-${size}`} />,
    Shield: <Shield className={`h-${size} w-${size}`} />,
    Users: <Users className={`h-${size} w-${size}`} />,
    Database: <Database className={`h-${size} w-${size}`} />,
    Save: <Save className={`h-${size} w-${size}`} />,
    FileText: <FileText className={`h-${size} w-${size}`} />,
    Eye: <Eye className={`h-${size} w-${size}`} />,
    UserCheck: <UserCheck className={`h-${size} w-${size}`} />,
    BarChart2: <BarChart2 className={`h-${size} w-${size}`} />,
    User: <User className={`h-${size} w-${size}`} />,
    Activity: <Activity className={`h-${size} w-${size}`} />,
    Lock: <Lock className={`h-${size} w-${size}`} />
  };
  
  return icons[iconName] || <div className={`h-${size} w-${size}`} />;
};

/**
 * 快速访问组件
 * 
 * 显示常用功能的快速访问卡片，支持自定义
 */
const QuickAccess: React.FC = () => {
  // 从localStorage加载快速访问项目
  const [quickAccessItems, setQuickAccessItems] = useState<QuickAccessItem[]>(() => {
    try {
      const savedItems = localStorage.getItem('superadmin_quick_access');
      return savedItems ? JSON.parse(savedItems) : defaultQuickAccessItems;
    } catch (error) {
      console.error('加载快速访问项目失败:', error);
      return defaultQuickAccessItems;
    }
  });

  const [isCustomizing, setIsCustomizing] = useState(false);

  // 保存快速访问项目
  const saveQuickAccessItems = (items: QuickAccessItem[]) => {
    setQuickAccessItems(items);
    try {
      localStorage.setItem('superadmin_quick_access', JSON.stringify(items));
    } catch (error) {
      console.error('保存快速访问项目失败:', error);
    }
  };

  // 添加快速访问项目
  const addQuickAccessItem = (item: QuickAccessItem) => {
    const newItems = [...quickAccessItems, item];
    saveQuickAccessItems(newItems);
  };

  // 移除快速访问项目
  const removeQuickAccessItem = (itemId: string) => {
    const newItems = quickAccessItems.filter(item => item.id !== itemId);
    saveQuickAccessItems(newItems);
  };

  // 重置快速访问项目
  const resetQuickAccessItems = () => {
    saveQuickAccessItems(defaultQuickAccessItems);
  };

  return (
    <div className="mt-6">
      <div className="flex justify-between items-center mb-4">
        <h2 className="text-xl font-semibold flex items-center">
          <Zap className="h-5 w-5 mr-2 text-yellow-500" />
          快速访问
        </h2>
        <div className="flex space-x-2">
          <Button
            variant="outline"
            size="sm"
            onClick={() => setIsCustomizing(!isCustomizing)}
          >
            {isCustomizing ? (
              <>
                <Check className="h-4 w-4 mr-1" />
                完成
              </>
            ) : (
              <>
                <Edit className="h-4 w-4 mr-1" />
                自定义
              </>
            )}
          </Button>
          {isCustomizing && (
            <Button
              variant="outline"
              size="sm"
              onClick={resetQuickAccessItems}
            >
              <RotateCcw className="h-4 w-4 mr-1" />
              重置
            </Button>
          )}
        </div>
      </div>
      
      <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
        {quickAccessItems.map(item => (
          <div key={item.id} className="relative">
            <QuickAccessCard
              title={item.title}
              icon={getIconComponent(item.icon)}
              path={item.path}
              description={item.description}
            />
            {isCustomizing && (
              <Button
                variant="destructive"
                size="icon"
                className="absolute -top-2 -right-2 h-6 w-6 rounded-full"
                onClick={() => removeQuickAccessItem(item.id)}
              >
                <X className="h-3 w-3" />
              </Button>
            )}
          </div>
        ))}
        
        {isCustomizing && quickAccessItems.length < 8 && (
          <Card className="border-dashed border-2 hover:border-primary cursor-pointer transition-colors">
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <CardContent className="flex flex-col items-center justify-center p-4 h-full">
                  <Plus className="h-8 w-8 text-muted-foreground" />
                  <p className="text-sm text-muted-foreground mt-2">添加快速访问</p>
                </CardContent>
              </DropdownMenuTrigger>
              <DropdownMenuContent>
                <DropdownMenuLabel>选择要添加的项目</DropdownMenuLabel>
                <DropdownMenuSeparator />
                {allQuickAccessItems
                  .filter(item => !quickAccessItems.some(qi => qi.id === item.id))
                  .map(item => (
                    <DropdownMenuItem
                      key={item.id}
                      onClick={() => addQuickAccessItem(item)}
                    >
                      {getIconComponent(item.icon, 4)}
                      <span className="ml-2">{item.title}</span>
                    </DropdownMenuItem>
                  ))}
              </DropdownMenuContent>
            </DropdownMenu>
          </Card>
        )}
      </div>
    </div>
  );
};

export default QuickAccess;
