<template>
  <div class="error-dashboard">
    <div class="dashboard-header">
      <h2>错误分析仪表板</h2>
      <div class="dashboard-controls">
        <button class="refresh-button" @click="refreshDashboard">
          <i class="fas fa-sync-alt"></i> 刷新数据
        </button>
        <select v-model="refreshInterval" @change="setupAutoRefresh">
          <option :value="0">手动刷新</option>
          <option :value="30000">30秒</option>
          <option :value="60000">1分钟</option>
          <option :value="300000">5分钟</option>
        </select>
      </div>
    </div>
    
    <div v-if="loading" class="dashboard-loading">
      <div class="spinner"></div>
      <p>加载仪表板数据...</p>
    </div>
    
    <div v-else-if="error" class="dashboard-error">
      <h3>加载错误</h3>
      <p>{{ error }}</p>
      <button @click="refreshDashboard">重试</button>
    </div>
    
    <div v-else class="dashboard-content">
      <!-- 错误摘要卡片 -->
      <div class="summary-cards">
        <div class="summary-card total-errors">
          <div class="card-icon">
            <i class="fas fa-exclamation-circle"></i>
          </div>
          <div class="card-content">
            <div class="card-title">总错误数</div>
            <div class="card-value">{{ summary.totalErrors }}</div>
          </div>
        </div>
        
        <div class="summary-card critical-errors">
          <div class="card-icon">
            <i class="fas fa-skull-crossbones"></i>
          </div>
          <div class="card-content">
            <div class="card-title">严重错误</div>
            <div class="card-value">{{ summary.criticalErrors }}</div>
          </div>
        </div>
        
        <div class="summary-card anomalies">
          <div class="card-icon">
            <i class="fas fa-chart-line"></i>
          </div>
          <div class="card-content">
            <div class="card-title">检测到的异常</div>
            <div class="card-value">{{ summary.anomalies }}</div>
          </div>
        </div>
        
        <div class="summary-card clusters">
          <div class="card-icon">
            <i class="fas fa-object-group"></i>
          </div>
          <div class="card-content">
            <div class="card-title">错误聚类</div>
            <div class="card-value">{{ summary.clusters }}</div>
          </div>
        </div>
      </div>
      
      <!-- 错误严重性分布 -->
      <div class="severity-distribution">
        <h3>错误严重性分布</h3>
        <div class="severity-bars">
          <div class="severity-bar critical" :style="{ width: getSeverityWidth('critical') }">
            <span class="severity-label">严重</span>
            <span class="severity-count">{{ summary.criticalErrors }}</span>
          </div>
          <div class="severity-bar high" :style="{ width: getSeverityWidth('high') }">
            <span class="severity-label">高</span>
            <span class="severity-count">{{ summary.highErrors }}</span>
          </div>
          <div class="severity-bar medium" :style="{ width: getSeverityWidth('medium') }">
            <span class="severity-label">中</span>
            <span class="severity-count">{{ summary.mediumErrors }}</span>
          </div>
          <div class="severity-bar low" :style="{ width: getSeverityWidth('low') }">
            <span class="severity-label">低</span>
            <span class="severity-count">{{ summary.lowErrors }}</span>
          </div>
        </div>
      </div>
      
      <!-- 错误热图 -->
      <ErrorHeatmap 
        title="错误热图" 
        :dataUrl="apiBaseUrl + '/heatmap'"
        @view-all-errors="viewAllErrors"
      />
      
      <!-- 错误趋势图 -->
      <ErrorTimeChart 
        title="错误趋势" 
        :dataUrl="apiBaseUrl + '/timechart'"
      />
      
      <!-- 错误关联分析 -->
      <ErrorCorrelation 
        title="错误关联分析" 
        :dataUrl="apiBaseUrl + '/correlation'"
        @view-related-errors="viewRelatedErrors"
      />
      
      <!-- 最近错误 -->
      <div class="recent-errors">
        <h3>最近错误</h3>
        <div v-if="summary.recentErrors && summary.recentErrors.length > 0" class="error-list">
          <div 
            v-for="error in summary.recentErrors" 
            :key="error.id"
            class="error-item"
          >
            <div class="error-severity" :class="error.severity">{{ error.severity }}</div>
            <div class="error-details">
              <div class="error-message">{{ error.message }}</div>
              <div class="error-meta">
                <span v-if="error.errorCode" class="error-code">{{ error.errorCode }}</span>
                <span class="error-time">{{ formatTime(error.timestamp) }}</span>
              </div>
            </div>
            <button class="view-details-button" @click="viewErrorDetails(error)">
              <i class="fas fa-search"></i>
            </button>
          </div>
        </div>
        <div v-else class="no-errors">
          <p>没有最近的错误</p>
        </div>
      </div>
      
      <!-- 性能指标 -->
      <div class="performance-metrics">
        <h3>性能指标</h3>
        <div class="metrics-grid">
          <div class="metric-item">
            <div class="metric-label">平均处理时间</div>
            <div class="metric-value">{{ formatDuration(performance.avgProcessingTime) }}</div>
          </div>
          <div class="metric-item">
            <div class="metric-label">缓存命中率</div>
            <div class="metric-value">{{ formatPercentage(performance.cacheStats.hitRate) }}</div>
          </div>
          <div class="metric-item">
            <div class="metric-label">缓冲区大小</div>
            <div class="metric-value">{{ performance.bufferStats.bufferSize }}</div>
          </div>
          <div class="metric-item">
            <div class="metric-label">总处理日志</div>
            <div class="metric-value">{{ performance.totalLogs }}</div>
          </div>
        </div>
      </div>
    </div>
    
    <!-- 错误详情弹窗 -->
    <div v-if="showErrorDetails" class="error-details-modal">
      <div class="modal-content">
        <div class="modal-header">
          <h4>错误详情</h4>
          <button class="close-button" @click="closeErrorDetails">&times;</button>
        </div>
        <div class="modal-body">
          <div class="detail-item">
            <span class="detail-label">ID:</span>
            <span class="detail-value">{{ selectedError.id }}</span>
          </div>
          <div class="detail-item">
            <span class="detail-label">消息:</span>
            <span class="detail-value">{{ selectedError.message }}</span>
          </div>
          <div class="detail-item">
            <span class="detail-label">时间:</span>
            <span class="detail-value">{{ formatTime(selectedError.timestamp) }}</span>
          </div>
          <div class="detail-item">
            <span class="detail-label">严重性:</span>
            <span class="detail-value severity-badge" :class="selectedError.severity">
              {{ selectedError.severity }}
            </span>
          </div>
          <div v-if="selectedError.errorCode" class="detail-item">
            <span class="detail-label">错误代码:</span>
            <span class="detail-value">{{ selectedError.errorCode }}</span>
          </div>
          <div v-if="selectedError.source" class="detail-item">
            <span class="detail-label">来源:</span>
            <span class="detail-value">{{ selectedError.source }}</span>
          </div>
          <div v-if="selectedError.clientIp" class="detail-item">
            <span class="detail-label">客户端 IP:</span>
            <span class="detail-value">{{ selectedError.clientIp }}</span>
          </div>
          <div v-if="selectedError.stack" class="detail-section">
            <h5>堆栈跟踪</h5>
            <pre class="stack-trace">{{ selectedError.stack }}</pre>
          </div>
          <div v-if="selectedError.data" class="detail-section">
            <h5>附加数据</h5>
            <pre class="error-data">{{ JSON.stringify(selectedError.data, null, 2) }}</pre>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import ErrorHeatmap from './ErrorHeatmap.vue';
import ErrorTimeChart from './ErrorTimeChart.vue';
import ErrorCorrelation from './ErrorCorrelation.vue';

export default {
  name: 'ErrorDashboard',
  components: {
    ErrorHeatmap,
    ErrorTimeChart,
    ErrorCorrelation
  },
  props: {
    apiBaseUrl: {
      type: String,
      default: '/api/admin/error-visualization'
    }
  },
  data() {
    return {
      loading: true,
      error: null,
      summary: {
        totalErrors: 0,
        criticalErrors: 0,
        highErrors: 0,
        mediumErrors: 0,
        lowErrors: 0,
        anomalies: 0,
        clusters: 0,
        patterns: 0,
        recentErrors: []
      },
      performance: {
        totalLogs: 0,
        avgProcessingTime: 0,
        bufferStats: {
          bufferSize: 0
        },
        cacheStats: {
          hitRate: 0
        }
      },
      refreshInterval: 60000, // 默认1分钟刷新一次
      refreshTimer: null,
      showErrorDetails: false,
      selectedError: null
    };
  },
  mounted() {
    this.refreshDashboard();
    this.setupAutoRefresh();
  },
  beforeUnmount() {
    this.clearAutoRefresh();
  },
  methods: {
    async refreshDashboard() {
      this.loading = true;
      this.error = null;
      
      try {
        // 获取仪表板数据
        const response = await fetch(`${this.apiBaseUrl}/dashboard`);
        
        if (!response.ok) {
          throw new Error(`HTTP error! status: ${response.status}`);
        }
        
        const data = await response.json();
        
        if (!data.success) {
          throw new Error(data.error || '获取仪表板数据失败');
        }
        
        // 更新数据
        this.summary = data.dashboard.summary;
        this.performance = data.dashboard.performance;
      } catch (error) {
        console.error('Error fetching dashboard data:', error);
        this.error = '获取仪表板数据失败: ' + error.message;
      } finally {
        this.loading = false;
      }
    },
    
    setupAutoRefresh() {
      // 清除现有定时器
      this.clearAutoRefresh();
      
      // 如果刷新间隔大于0，设置新的定时器
      if (this.refreshInterval > 0) {
        this.refreshTimer = setInterval(() => {
          this.refreshDashboard();
        }, this.refreshInterval);
      }
    },
    
    clearAutoRefresh() {
      if (this.refreshTimer) {
        clearInterval(this.refreshTimer);
        this.refreshTimer = null;
      }
    },
    
    getSeverityWidth(severity) {
      const total = this.summary.totalErrors;
      if (total === 0) return '0%';
      
      let count = 0;
      switch (severity) {
        case 'critical':
          count = this.summary.criticalErrors;
          break;
        case 'high':
          count = this.summary.highErrors;
          break;
        case 'medium':
          count = this.summary.mediumErrors;
          break;
        case 'low':
          count = this.summary.lowErrors;
          break;
      }
      
      return `${(count / total) * 100}%`;
    },
    
    formatTime(timestamp) {
      const date = new Date(timestamp);
      return date.toLocaleString();
    },
    
    formatDuration(ms) {
      if (ms < 1) return '< 1ms';
      if (ms < 1000) return `${Math.round(ms)}ms`;
      return `${(ms / 1000).toFixed(2)}s`;
    },
    
    formatPercentage(value) {
      return `${Math.round(value * 100)}%`;
    },
    
    viewErrorDetails(error) {
      this.selectedError = error;
      this.showErrorDetails = true;
    },
    
    closeErrorDetails() {
      this.showErrorDetails = false;
      this.selectedError = null;
    },
    
    viewAllErrors(cell) {
      // 可以实现跳转到错误列表页面的逻辑
      console.log('View all errors:', cell);
    },
    
    viewRelatedErrors(node) {
      // 可以实现跳转到相关错误列表页面的逻辑
      console.log('View related errors:', node);
    }
  }
};
</script>

<style scoped>
.error-dashboard {
  padding: 20px;
  background-color: #f5f7fa;
  min-height: 100vh;
}

.dashboard-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.dashboard-header h2 {
  margin: 0;
  color: #333;
  font-size: 24px;
}

.dashboard-controls {
  display: flex;
  align-items: center;
}

.refresh-button {
  background-color: #3498db;
  color: white;
  border: none;
  border-radius: 4px;
  padding: 8px 15px;
  margin-right: 10px;
  cursor: pointer;
  display: flex;
  align-items: center;
}

.refresh-button i {
  margin-right: 5px;
}

.dashboard-controls select {
  padding: 7px 10px;
  border: 1px solid #ddd;
  border-radius: 4px;
  background-color: white;
}

.dashboard-loading, .dashboard-error {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  min-height: 300px;
  background-color: white;
  border-radius: 8px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  padding: 20px;
}

.spinner {
  border: 4px solid #f3f3f3;
  border-top: 4px solid #3498db;
  border-radius: 50%;
  width: 40px;
  height: 40px;
  animation: spin 1s linear infinite;
  margin-bottom: 15px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.dashboard-error h3 {
  color: #e74c3c;
  margin-top: 0;
}

.dashboard-error button {
  background-color: #3498db;
  color: white;
  border: none;
  border-radius: 4px;
  padding: 8px 15px;
  margin-top: 10px;
  cursor: pointer;
}

.summary-cards {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
  gap: 20px;
  margin-bottom: 20px;
}

.summary-card {
  background-color: white;
  border-radius: 8px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  padding: 20px;
  display: flex;
  align-items: center;
}

.card-icon {
  font-size: 24px;
  margin-right: 15px;
  width: 50px;
  height: 50px;
  border-radius: 50%;
  display: flex;
  justify-content: center;
  align-items: center;
  color: white;
}

.total-errors .card-icon {
  background-color: #3498db;
}

.critical-errors .card-icon {
  background-color: #e74c3c;
}

.anomalies .card-icon {
  background-color: #f39c12;
}

.clusters .card-icon {
  background-color: #2ecc71;
}

.card-content {
  flex: 1;
}

.card-title {
  font-size: 14px;
  color: #666;
  margin-bottom: 5px;
}

.card-value {
  font-size: 24px;
  font-weight: bold;
  color: #333;
}

.severity-distribution {
  background-color: white;
  border-radius: 8px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  padding: 20px;
  margin-bottom: 20px;
}

.severity-distribution h3 {
  margin-top: 0;
  margin-bottom: 15px;
  font-size: 18px;
  color: #333;
}

.severity-bars {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.severity-bar {
  height: 30px;
  border-radius: 4px;
  display: flex;
  align-items: center;
  padding: 0 10px;
  color: white;
  font-weight: bold;
  transition: width 0.5s ease;
  min-width: 60px;
}

.severity-bar.critical {
  background-color: #e74c3c;
}

.severity-bar.high {
  background-color: #e67e22;
}

.severity-bar.medium {
  background-color: #f1c40f;
  color: #333;
}

.severity-bar.low {
  background-color: #3498db;
}

.severity-label {
  flex: 1;
}

.recent-errors {
  background-color: white;
  border-radius: 8px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  padding: 20px;
  margin-bottom: 20px;
}

.recent-errors h3 {
  margin-top: 0;
  margin-bottom: 15px;
  font-size: 18px;
  color: #333;
}

.error-list {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.error-item {
  display: flex;
  align-items: center;
  padding: 10px;
  border-radius: 4px;
  background-color: #f9f9f9;
  border-left: 4px solid #ddd;
}

.error-item:hover {
  background-color: #f1f1f1;
}

.error-severity {
  padding: 3px 8px;
  border-radius: 4px;
  font-size: 12px;
  font-weight: bold;
  margin-right: 10px;
  min-width: 60px;
  text-align: center;
}

.error-severity.critical {
  background-color: #e74c3c;
  color: white;
}

.error-severity.high {
  background-color: #e67e22;
  color: white;
}

.error-severity.medium {
  background-color: #f1c40f;
}

.error-severity.low {
  background-color: #3498db;
  color: white;
}

.error-details {
  flex: 1;
}

.error-message {
  font-size: 14px;
  margin-bottom: 5px;
}

.error-meta {
  font-size: 12px;
  color: #666;
}

.error-code {
  background-color: #eee;
  padding: 2px 5px;
  border-radius: 3px;
  margin-right: 10px;
}

.view-details-button {
  background: none;
  border: none;
  color: #3498db;
  cursor: pointer;
  font-size: 16px;
}

.no-errors {
  text-align: center;
  padding: 20px;
  color: #666;
}

.performance-metrics {
  background-color: white;
  border-radius: 8px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  padding: 20px;
  margin-bottom: 20px;
}

.performance-metrics h3 {
  margin-top: 0;
  margin-bottom: 15px;
  font-size: 18px;
  color: #333;
}

.metrics-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
  gap: 15px;
}

.metric-item {
  background-color: #f9f9f9;
  border-radius: 4px;
  padding: 15px;
}

.metric-label {
  font-size: 14px;
  color: #666;
  margin-bottom: 5px;
}

.metric-value {
  font-size: 18px;
  font-weight: bold;
  color: #333;
}

.error-details-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
}

.modal-content {
  background-color: white;
  border-radius: 8px;
  width: 80%;
  max-width: 800px;
  max-height: 80vh;
  overflow-y: auto;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15px 20px;
  border-bottom: 1px solid #eee;
}

.modal-header h4 {
  margin: 0;
  font-size: 18px;
}

.close-button {
  background: none;
  border: none;
  font-size: 24px;
  cursor: pointer;
  color: #666;
}

.modal-body {
  padding: 20px;
}

.detail-item {
  margin-bottom: 10px;
}

.detail-label {
  font-weight: bold;
  margin-right: 5px;
}

.severity-badge {
  display: inline-block;
  padding: 3px 8px;
  border-radius: 4px;
  font-size: 12px;
}

.severity-badge.critical {
  background-color: #e74c3c;
  color: white;
}

.severity-badge.high {
  background-color: #e67e22;
  color: white;
}

.severity-badge.medium {
  background-color: #f1c40f;
}

.severity-badge.low {
  background-color: #3498db;
  color: white;
}

.detail-section {
  margin-top: 15px;
}

.detail-section h5 {
  margin-top: 0;
  margin-bottom: 10px;
  font-size: 16px;
  color: #333;
}

.stack-trace, .error-data {
  background-color: #f9f9f9;
  padding: 10px;
  border-radius: 4px;
  font-family: monospace;
  font-size: 12px;
  white-space: pre-wrap;
  overflow-x: auto;
}
</style>
