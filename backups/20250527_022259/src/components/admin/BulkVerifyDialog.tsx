import { useState } from 'react';
import { useToast } from '@/components/ui/use-toast';
import { Button } from '@/components/ui/button';
import { 
  Dialog, 
  DialogContent, 
  DialogDescription, 
  DialogFooter, 
  DialogHeader, 
  DialogTitle 
} from '@/components/ui/dialog';
import { 
  Select, 
  SelectContent, 
  SelectItem, 
  SelectTrigger, 
  SelectValue 
} from '@/components/ui/select';
import { Label } from '@/components/ui/label';
import { Loader2, CheckSquare, AlertTriangle } from 'lucide-react';
import { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group';

interface BulkVerifyDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onConfirm: (action: 'verify' | 'unverify', reason?: string) => Promise<void>;
  selectedCount: number;
}

export default function BulkVerifyDialog({
  open,
  onOpenChange,
  onConfirm,
  selectedCount,
}: BulkVerifyDialogProps) {
  const { toast } = useToast();
  const [isProcessing, setIsProcessing] = useState(false);
  const [action, setAction] = useState<'verify' | 'unverify'>('verify');
  const [reason, setReason] = useState<string>('');
  
  // 验证原因选项
  const verifyReasons = [
    '数据完整且准确',
    '已通过电话确认',
    '已通过邮件确认',
    '已通过其他方式确认',
    '系统自动验证通过',
  ];
  
  // 取消验证原因选项
  const unverifyReasons = [
    '数据不完整或不准确',
    '存在明显错误',
    '无法联系到用户',
    '用户要求取消验证',
    '系统检测到异常',
  ];
  
  // 处理确认
  const handleConfirm = async () => {
    try {
      setIsProcessing(true);
      await onConfirm(action, reason);
      
      // 重置状态
      setAction('verify');
      setReason('');
      
      // 关闭对话框
      onOpenChange(false);
      
      toast({
        title: action === 'verify' ? '批量验证成功' : '批量取消验证成功',
        description: `已${action === 'verify' ? '验证' : '取消验证'} ${selectedCount} 条数据`,
      });
    } catch (error) {
      console.error('批量验证错误:', error);
      toast({
        variant: 'destructive',
        title: '操作失败',
        description: '服务器错误，请稍后再试',
      });
    } finally {
      setIsProcessing(false);
    }
  };
  
  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-md">
        <DialogHeader>
          <DialogTitle>批量验证</DialogTitle>
          <DialogDescription>
            为选中的 {selectedCount} 条数据进行验证操作
          </DialogDescription>
        </DialogHeader>
        
        <div className="grid gap-4 py-4">
          {/* 操作选择 */}
          <div className="space-y-2">
            <Label>选择操作</Label>
            <RadioGroup 
              value={action} 
              onValueChange={(value) => setAction(value as 'verify' | 'unverify')}
              className="flex flex-col space-y-2"
            >
              <div className="flex items-center space-x-2">
                <RadioGroupItem value="verify" id="verify" />
                <Label htmlFor="verify" className="flex items-center">
                  <CheckSquare className="mr-2 h-4 w-4 text-green-500" />
                  批量验证
                </Label>
              </div>
              <div className="flex items-center space-x-2">
                <RadioGroupItem value="unverify" id="unverify" />
                <Label htmlFor="unverify" className="flex items-center">
                  <AlertTriangle className="mr-2 h-4 w-4 text-amber-500" />
                  批量取消验证
                </Label>
              </div>
            </RadioGroup>
          </div>
          
          {/* 原因选择 */}
          <div className="space-y-2">
            <Label>选择原因</Label>
            <Select
              value={reason}
              onValueChange={setReason}
            >
              <SelectTrigger>
                <SelectValue placeholder="选择操作原因" />
              </SelectTrigger>
              <SelectContent>
                {(action === 'verify' ? verifyReasons : unverifyReasons).map((r) => (
                  <SelectItem key={r} value={r}>
                    {r}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>
        </div>
        
        <DialogFooter>
          <Button
            type="button"
            variant="outline"
            onClick={() => onOpenChange(false)}
            disabled={isProcessing}
          >
            取消
          </Button>
          <Button 
            type="button" 
            onClick={handleConfirm}
            disabled={isProcessing || !reason}
            variant={action === 'verify' ? 'default' : 'secondary'}
          >
            {isProcessing && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
            {action === 'verify' ? '确认验证' : '确认取消验证'}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
