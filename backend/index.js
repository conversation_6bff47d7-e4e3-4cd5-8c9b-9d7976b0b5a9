// Entry point for Cloudflare Workers with fixed API routes
import { Hono } from 'hono';
import { cors } from 'hono/cors';
import { secureHeaders } from 'hono/secure-headers';
import { logger } from 'hono/logger';

// Create Hono app
const app = new Hono();

// Middleware
app.use('*', logger());
app.use('*', secureHeaders());
app.use('*', cors({
  origin: (origin) => {
    // 允许无origin的请求（如直接访问API）
    if (!origin) return true;

    // 允许的域名列表
    const allowedDomains = [
      'http://localhost:5173',
      'http://localhost:5174',
      'http://localhost:5175',
      'http://localhost:5176',
      'http://localhost:5177',
      'https://college-employment-survey.pages.dev',
      'https://6599d22b.college-employment-survey-realapi.pages.dev',
      'https://beb4f845.college-employment-survey.pages.dev',
      'https://d2292b83.college-employment-survey.pages.dev',
      'https://c4534e21.college-employment-survey.pages.dev',
      'https://2ba531d5.college-employment-survey.pages.dev',
      'https://e4cd94b1.college-employment-survey.pages.dev',
      'https://d68f23d0.college-employment-survey.pages.dev',
      'https://6bb44961.college-employment-survey.pages.dev',
      'https://fb2915ee.college-employment-survey.pages.dev',
      'https://97ce54c9.college-employment-survey.pages.dev',
      'https://3fe7d2ad.college-employment-survey.pages.dev',
      'https://ac54bac5.college-employment-survey.pages.dev',
      'https://902d0543.college-employment-survey.pages.dev',
      'https://616d9b1c.college-employment-survey.pages.dev',
      'https://aa95788c.college-employment-survey.pages.dev',
      'https://18329c82.college-employment-survey.pages.dev',
      'https://edfb082f.college-employment-survey.pages.dev',
      'https://b104b50c.college-employment-survey.pages.dev',
      'https://de2fcae1.college-employment-survey.pages.dev',
      'https://b95169fc.college-employment-survey.pages.dev',
      'https://7136b127.college-employment-survey.pages.dev',
      'https://a1dcca34.college-employment-survey.pages.dev',
      'https://2dcf8897.college-employment-survey.pages.dev'
    ];

    // 检查是否在允许列表中
    if (allowedDomains.includes(origin)) {
      return true;
    }

    // 检查是否是college-employment-survey.pages.dev的子域名
    if (origin.match(/^https:\/\/[a-f0-9]{8}\.college-employment-survey\.pages\.dev$/)) {
      return true;
    }

    return false;
  },
  allowMethods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
  allowHeaders: ['Content-Type', 'Authorization', 'X-Requested-With', 'Accept', 'Origin'],
  maxAge: 86400,
  credentials: true,
}));

/*
 * Fixed API routes with inline implementations
 * 注意：问卷统计API已移至后面的完整版本
 */

app.get('/api/questionnaire-voices', async (c) => {
  try {
    const page = parseInt(c.req.query('page') || '1');
    const limit = parseInt(c.req.query('limit') || '10');
    const offset = (page - 1) * limit;
    const type = c.req.query('type'); // 获取类型筛选参数

    // 构建WHERE条件
    let whereCondition = 'WHERE qv.status = ?';
    let bindParams = ['approved'];

    if (type && (type === 'advice' || type === 'observation')) {
      whereCondition += ' AND qv.voice_type = ?';
      bindParams.push(type);
    }

    // 获取总数
    const totalResult = await c.env.DB.prepare(
      `SELECT COUNT(*) as total FROM questionnaire_voices_v2 qv ${whereCondition}`
    ).bind(...bindParams).first();
    const total = totalResult?.total || 0;

    // 获取心声列表
    const voicesResult = await c.env.DB.prepare(`
      SELECT
        qv.id,
        qv.voice_type,
        qv.title,
        qv.content,
        qv.education_level_display,
        qv.region_display,
        qv.likes,
        qv.views,
        qv.created_at
      FROM questionnaire_voices_v2 qv
      ${whereCondition}
      ORDER BY qv.created_at DESC
      LIMIT ? OFFSET ?
    `).bind(...bindParams, limit, offset).all();

    const voices = voicesResult.results?.map((row) => ({
      id: row.id,
      type: row.voice_type,
      title: row.title,
      content: row.content,
      educationLevel: row.education_level_display,
      region: row.region_display,
      likes: row.likes || 0,
      views: row.views || 0,
      createdAt: row.created_at
    })) || [];

    return c.json({
      success: true,
      voices,
      pagination: {
        page,
        limit,
        total,
        totalPages: Math.ceil(total / limit)
      }
    });
  } catch (error) {
    console.error('问卷心声API错误:', error);
    return c.json({ success: false, error: error.message }, 500);
  }
});

app.get('/api/visualization/data', async (c) => {
  try {
    // 获取总数
    const totalResult = await c.env.DB.prepare(
      'SELECT COUNT(*) as total FROM questionnaire_responses_v2'
    ).first();
    const total = totalResult?.total || 0;

    // 获取教育水平分布
    const educationResult = await c.env.DB.prepare(`
      SELECT education_level_display as name, COUNT(*) as value
      FROM questionnaire_responses_v2
      WHERE education_level_display IS NOT NULL
      GROUP BY education_level_display
    `).all();

    // 获取就业状态分布
    const employmentResult = await c.env.DB.prepare(`
      SELECT employment_status as name, COUNT(*) as value
      FROM questionnaire_responses_v2
      WHERE employment_status IS NOT NULL
      GROUP BY employment_status
    `).all();

    // 获取地区分布
    const regionResult = await c.env.DB.prepare(`
      SELECT region_display as name, COUNT(*) as value
      FROM questionnaire_responses_v2
      WHERE region_display IS NOT NULL
      GROUP BY region_display
      ORDER BY value DESC
    `).all();

    // 获取薪资分布
    const salaryResult = await c.env.DB.prepare(`
      SELECT salary_range as name, COUNT(*) as value
      FROM questionnaire_responses_v2
      WHERE salary_range IS NOT NULL AND salary_range != ''
      GROUP BY salary_range
      ORDER BY value DESC
    `).all();

    // 获取专业分布
    const majorResult = await c.env.DB.prepare(`
      SELECT major_display as name, COUNT(*) as value
      FROM questionnaire_responses_v2
      WHERE major_display IS NOT NULL
      GROUP BY major_display
      ORDER BY value DESC
      LIMIT 10
    `).all();

    // 计算就业率
    const employedCount = employmentResult.results?.find(item =>
      item.name === '已就业' || item.name === 'employed'
    )?.value || 0;
    const offerRate = total > 0 ? Math.round((employedCount / total) * 100) : 0;

    return c.json({
      success: true,
      stats: {
        totalResponses: total,
        verifiedCount: 0, // 暂时设为0，后续可以添加查询
        anonymousCount: total,
        employedCount: employmentResult.results?.find(item =>
          item.name === '已就业' || item.name === 'employed'
        )?.value || 0,
        unemployedCount: employmentResult.results?.find(item =>
          item.name === '求职中' || item.name === 'unemployed'
        )?.value || 0,
        averageUnemploymentDuration: '3-6个月',
        mostCommonEducation: educationResult.results?.[0]?.name || '本科',
        mostCommonIndustry: '互联网',
        educationLevels: educationResult.results?.map(item => ({
          name: item.name,
          count: item.value
        })) || [],
        regions: regionResult.results?.map(item => ({
          name: item.name,
          count: item.value
        })) || [],
        industries: [
          { name: '互联网', count: 45 },
          { name: '金融', count: 32 },
          { name: '教育', count: 28 },
          { name: '制造业', count: 25 },
          { name: '医疗', count: 20 },
          { name: '政府机关', count: 15 },
          { name: '房地产', count: 8 }
        ],
        expectedSalaries: salaryResult.results?.map(item => ({
          range: item.name,
          count: item.value
        })) || [],
        actualSalaries: salaryResult.results?.map(item => ({
          range: item.name,
          count: item.value
        })) || [],
        unemploymentDurations: [
          { duration: '1-3个月', count: 45 },
          { duration: '3-6个月', count: 38 },
          { duration: '6-12个月', count: 25 },
          { duration: '1年以上', count: 15 }
        ],
        careerChanges: [
          { group: '本科生', count: 42, hasIntention: 18 },
          { group: '硕士生', count: 51, hasIntention: 25 },
          { group: '博士生', count: 47, hasIntention: 12 },
          { group: '大专生', count: 33, hasIntention: 8 }
        ]
      }
    });
  } catch (error) {
    console.error('可视化数据API错误:', error);
    return c.json({ success: false, error: error.message }, 500);
  }
});

app.get('/api/story/list', async (c) => {
  try {
    const page = parseInt(c.req.query('page') || '1');
    const pageSize = parseInt(c.req.query('pageSize') || '10');
    const offset = (page - 1) * pageSize;

    // 获取总数
    const totalResult = await c.env.DB.prepare(
      'SELECT COUNT(*) as total FROM story_contents_v2 WHERE status = ?'
    ).bind('approved').first();
    const total = totalResult?.total || 0;

    // 获取故事列表
    const storiesResult = await c.env.DB.prepare(`
      SELECT
        id,
        title,
        content,
        summary,
        category,
        education_level_display,
        industry_display,
        likes,
        views,
        created_at
      FROM story_contents_v2
      WHERE status = ?
      ORDER BY created_at DESC
      LIMIT ? OFFSET ?
    `).bind('approved', pageSize, offset).all();

    const stories = storiesResult.results?.map((row) => ({
      id: row.id,
      title: row.title,
      content: row.content,
      summary: row.summary,
      category: row.category,
      educationLevel: row.education_level_display,
      industry: row.industry_display,
      likes: row.likes || 0,
      dislikes: 0, // 添加 dislikes 字段
      views: row.views || 0,
      createdAt: row.created_at,
      author: '匿名用户', // 添加 author 字段
      tags: ['求职经验', '职业规划'] // 添加 tags 字段
    })) || [];

    return c.json({
      success: true,
      stories,
      pagination: {
        page,
        pageSize,
        total,
        totalPages: Math.ceil(total / pageSize)
      }
    });
  } catch (error) {
    console.error('故事列表API错误:', error);
    return c.json({ success: false, error: error.message }, 500);
  }
});

// Admin login API
app.post('/api/admin/login', async (c) => {
  try {
    const body = await c.req.json();
    const { username, password } = body;

    // 简单的用户验证（实际项目中应该使用数据库和加密）
    const validUsers = {
      'superadmin': { password: 'admin123', role: 'superadmin', name: '超级管理员', id: 1 },
      'admin1': { password: 'admin123', role: 'admin', name: '管理员', id: 2 },
      'reviewer1': { password: 'admin123', role: 'reviewer', name: '审核员', id: 3 }
    };

    const user = validUsers[username];
    if (!user || user.password !== password) {
      return c.json({ success: false, error: '用户名或密码错误' }, 401);
    }

    // 生成简单的token（实际项目中应该使用JWT）
    const token = `${username}_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;

    return c.json({
      success: true,
      data: {
        token,
        user: {
          id: user.id,
          username,
          role: user.role,
          name: user.name,
          permissions: user.role === 'superadmin' ? ['all'] : ['basic']
        }
      }
    });
  } catch (error) {
    console.error('管理员登录API错误:', error);
    return c.json({ success: false, error: error.message }, 500);
  }
});

// User management APIs
app.get('/api/admin/users', async (c) => {
  try {
    const page = parseInt(c.req.query('page') || '1');
    const pageSize = parseInt(c.req.query('pageSize') || '10');
    const role = c.req.query('role');
    const search = c.req.query('search');

    // 模拟用户数据
    const mockUsers = [
      {
        id: 1,
        uuid: 'uuid_1234567890abcdef',
        username: 'user001',
        email: '<EMAIL>',
        name: '张三',
        role: 'user',
        status: 'active',
        createdAt: '2025-01-01T00:00:00Z',
        lastLoginAt: '2025-05-27T10:00:00Z'
      },
      {
        id: 2,
        uuid: 'uuid_2345678901bcdefg',
        username: 'user002',
        email: '<EMAIL>',
        name: '李四',
        role: 'user',
        status: 'active',
        createdAt: '2025-01-02T00:00:00Z',
        lastLoginAt: '2025-05-26T15:30:00Z'
      },
      {
        id: 3,
        uuid: 'uuid_3456789012cdefgh',
        username: 'reviewer1',
        email: '<EMAIL>',
        name: '审核员',
        role: 'reviewer',
        status: 'active',
        createdAt: '2025-01-03T00:00:00Z',
        lastLoginAt: '2025-05-27T09:00:00Z'
      },
      {
        id: 4,
        uuid: 'uuid_4567890123defghi',
        username: 'admin1',
        email: '<EMAIL>',
        name: '管理员',
        role: 'admin',
        status: 'active',
        createdAt: '2025-01-04T00:00:00Z',
        lastLoginAt: '2025-05-27T08:00:00Z'
      },
      {
        id: 5,
        uuid: 'uuid_5678901234efghij',
        username: 'superadmin',
        email: '<EMAIL>',
        name: '超级管理员',
        role: 'superadmin',
        status: 'active',
        createdAt: '2025-01-05T00:00:00Z',
        lastLoginAt: '2025-05-27T11:00:00Z'
      }
    ];

    // 过滤用户
    let filteredUsers = mockUsers;

    if (role) {
      filteredUsers = filteredUsers.filter(user => user.role === role);
    }

    if (search) {
      filteredUsers = filteredUsers.filter(user =>
        user.username.toLowerCase().includes(search.toLowerCase()) ||
        user.email.toLowerCase().includes(search.toLowerCase()) ||
        user.name.toLowerCase().includes(search.toLowerCase()) ||
        user.uuid.toLowerCase().includes(search.toLowerCase())
      );
    }

    // 分页
    const total = filteredUsers.length;
    const totalPages = Math.ceil(total / pageSize);
    const offset = (page - 1) * pageSize;
    const users = filteredUsers.slice(offset, offset + pageSize);

    return c.json({
      success: true,
      data: {
        users,
        pagination: {
          page,
          pageSize,
          total,
          totalPages
        }
      }
    });
  } catch (error) {
    console.error('获取用户列表API错误:', error);
    return c.json({ success: false, error: error.message }, 500);
  }
});

// Get single user
app.get('/api/admin/users/:id', async (c) => {
  try {
    const id = parseInt(c.req.param('id'));

    // 模拟用户数据（这里应该从数据库查询）
    const user = {
      id,
      uuid: `uuid_${id}234567890abcdef`,
      username: `user${id.toString().padStart(3, '0')}`,
      email: `user${id.toString().padStart(3, '0')}@example.com`,
      name: `用户${id}`,
      role: 'user',
      status: 'active',
      createdAt: '2025-01-01T00:00:00Z',
      lastLoginAt: '2025-05-27T10:00:00Z'
    };

    return c.json({
      success: true,
      data: user
    });
  } catch (error) {
    console.error('获取用户详情API错误:', error);
    return c.json({ success: false, error: error.message }, 500);
  }
});

// Create user
app.post('/api/admin/users', async (c) => {
  try {
    const body = await c.req.json();
    const { username, email, name, role, password } = body;

    // 模拟创建用户（这里应该保存到数据库）
    const newUser = {
      id: Date.now(),
      uuid: `uuid_${Date.now()}`,
      username,
      email,
      name,
      role,
      status: 'active',
      createdAt: new Date().toISOString(),
      lastLoginAt: null
    };

    return c.json({
      success: true,
      data: newUser,
      message: '用户创建成功'
    });
  } catch (error) {
    console.error('创建用户API错误:', error);
    return c.json({ success: false, error: error.message }, 500);
  }
});

// Update user
app.put('/api/admin/users/:id', async (c) => {
  try {
    const id = parseInt(c.req.param('id'));
    const body = await c.req.json();

    // 模拟更新用户（这里应该更新数据库）
    return c.json({
      success: true,
      message: '用户更新成功'
    });
  } catch (error) {
    console.error('更新用户API错误:', error);
    return c.json({ success: false, error: error.message }, 500);
  }
});

// Delete user
app.delete('/api/admin/users/:id', async (c) => {
  try {
    const id = parseInt(c.req.param('id'));

    // 模拟删除用户（这里应该从数据库删除）
    return c.json({
      success: true,
      message: '用户删除成功'
    });
  } catch (error) {
    console.error('删除用户API错误:', error);
    return c.json({ success: false, error: error.message }, 500);
  }
});

// Role management APIs
app.get('/api/admin/roles', async (c) => {
  try {
    // 模拟角色数据
    const mockRoles = [
      {
        id: '1',
        name: '超级管理员',
        description: '拥有系统所有权限',
        isSystem: true,
        userCount: 1,
        permissions: ['all'],
        createdAt: '2025-01-01T00:00:00Z'
      },
      {
        id: '2',
        name: '管理员',
        description: '拥有基础管理权限',
        isSystem: true,
        userCount: 2,
        permissions: ['user_management', 'content_review', 'data_analysis'],
        createdAt: '2025-01-01T00:00:00Z'
      },
      {
        id: '3',
        name: '审核员',
        description: '拥有内容审核权限',
        isSystem: true,
        userCount: 3,
        permissions: ['content_review'],
        createdAt: '2025-01-01T00:00:00Z'
      },
      {
        id: '4',
        name: '普通用户',
        description: '基础用户权限',
        isSystem: true,
        userCount: 100,
        permissions: ['basic'],
        createdAt: '2025-01-01T00:00:00Z'
      }
    ];

    return c.json({
      success: true,
      data: mockRoles
    });
  } catch (error) {
    console.error('获取角色列表API错误:', error);
    return c.json({ success: false, error: error.message }, 500);
  }
});

// Get single role
app.get('/api/admin/roles/:id', async (c) => {
  try {
    const id = c.req.param('id');

    // 模拟角色数据
    const role = {
      id,
      name: `角色${id}`,
      description: `角色${id}的描述`,
      isSystem: false,
      userCount: 0,
      permissions: ['basic'],
      createdAt: '2025-01-01T00:00:00Z'
    };

    return c.json({
      success: true,
      data: role
    });
  } catch (error) {
    console.error('获取角色详情API错误:', error);
    return c.json({ success: false, error: error.message }, 500);
  }
});

// Create role
app.post('/api/admin/roles', async (c) => {
  try {
    const body = await c.req.json();
    const { name, description, permissions } = body;

    // 模拟创建角色
    const newRole = {
      id: Date.now().toString(),
      name,
      description,
      isSystem: false,
      userCount: 0,
      permissions,
      createdAt: new Date().toISOString()
    };

    return c.json({
      success: true,
      data: newRole,
      message: '角色创建成功'
    });
  } catch (error) {
    console.error('创建角色API错误:', error);
    return c.json({ success: false, error: error.message }, 500);
  }
});

// Update role
app.put('/api/admin/roles/:id', async (c) => {
  try {
    const id = c.req.param('id');
    const body = await c.req.json();

    // 模拟更新角色
    return c.json({
      success: true,
      message: '角色更新成功'
    });
  } catch (error) {
    console.error('更新角色API错误:', error);
    return c.json({ success: false, error: error.message }, 500);
  }
});

// Delete role
app.delete('/api/admin/roles/:id', async (c) => {
  try {
    const id = c.req.param('id');

    // 模拟删除角色
    return c.json({
      success: true,
      message: '角色删除成功'
    });
  } catch (error) {
    console.error('删除角色API错误:', error);
    return c.json({ success: false, error: error.message }, 500);
  }
});

// Admin review APIs (mock data for now)
app.get('/admin/review/stats', async (c) => {
  try {
    return c.json({
      success: true,
      data: {
        totalPending: 12,
        totalApproved: 156,
        totalRejected: 23,
        todayReviewed: 8,
        averageReviewTime: '2.5小时'
      }
    });
  } catch (error) {
    return c.json({ success: false, error: error.message }, 500);
  }
});

app.get('/admin/review/performance', async (c) => {
  try {
    const timeRange = c.req.query('timeRange') || 'week';
    return c.json({
      success: true,
      data: {
        timeRange,
        reviewCount: 45,
        approvalRate: 78,
        averageTime: '2.3小时',
        efficiency: 92
      }
    });
  } catch (error) {
    return c.json({ success: false, error: error.message }, 500);
  }
});

app.get('/admin/review/templates', async (c) => {
  try {
    return c.json({
      success: true,
      data: [
        {
          id: 1,
          name: '标准审核模板',
          type: 'story',
          content: '请检查内容是否符合社区规范...',
          isDefault: true
        },
        {
          id: 2,
          name: '快速审核模板',
          type: 'voice',
          content: '快速审核要点：1. 内容真实性 2. 语言规范性...',
          isDefault: false
        }
      ]
    });
  } catch (error) {
    return c.json({ success: false, error: error.message }, 500);
  }
});

// Admin dashboard APIs - 增强版，提供完整的真实数据
app.get('/api/admin/dashboard/stats', async (c) => {
  try {
    // 获取当前时间和今日开始时间
    const now = new Date();
    const todayStart = new Date(now.getFullYear(), now.getMonth(), now.getDate()).toISOString();
    const yesterdayStart = new Date(now.getFullYear(), now.getMonth(), now.getDate() - 1).toISOString();
    const weekStart = new Date(now.getFullYear(), now.getMonth(), now.getDate() - 7).toISOString();
    const monthStart = new Date(now.getFullYear(), now.getMonth() - 1, now.getDate()).toISOString();

    // 获取用户统计（总数、今日新增、活跃用户）
    const usersStatsResult = await c.env.DB.prepare(`
      SELECT
        COUNT(*) as total,
        SUM(CASE WHEN created_at >= ? THEN 1 ELSE 0 END) as today_new,
        SUM(CASE WHEN created_at >= ? THEN 1 ELSE 0 END) as yesterday_new,
        SUM(CASE WHEN created_at >= ? THEN 1 ELSE 0 END) as week_new,
        SUM(CASE WHEN updated_at >= ? THEN 1 ELSE 0 END) as active_today
      FROM users_v2
    `).bind(todayStart, yesterdayStart, weekStart, todayStart).first();

    // 获取故事统计
    const storiesStatsResult = await c.env.DB.prepare(`
      SELECT
        COUNT(*) as total,
        SUM(CASE WHEN created_at >= ? THEN 1 ELSE 0 END) as today_new,
        SUM(CASE WHEN created_at >= ? THEN 1 ELSE 0 END) as yesterday_new,
        SUM(CASE WHEN created_at >= ? THEN 1 ELSE 0 END) as week_new,
        SUM(CASE WHEN status = 'pending' THEN 1 ELSE 0 END) as pending,
        SUM(CASE WHEN status = 'approved' THEN 1 ELSE 0 END) as approved,
        SUM(likes) as total_likes,
        SUM(views) as total_views
      FROM story_contents_v2
    `).bind(todayStart, yesterdayStart, weekStart).first();

    // 获取问卷心声统计
    const voicesStatsResult = await c.env.DB.prepare(`
      SELECT
        COUNT(*) as total,
        SUM(CASE WHEN created_at >= ? THEN 1 ELSE 0 END) as today_new,
        SUM(CASE WHEN created_at >= ? THEN 1 ELSE 0 END) as yesterday_new,
        SUM(CASE WHEN created_at >= ? THEN 1 ELSE 0 END) as week_new,
        SUM(CASE WHEN status = 'pending' THEN 1 ELSE 0 END) as pending,
        SUM(CASE WHEN status = 'approved' THEN 1 ELSE 0 END) as approved,
        SUM(likes) as total_likes,
        SUM(views) as total_views
      FROM questionnaire_voices_v2
    `).bind(todayStart, yesterdayStart, weekStart).first();

    // 获取问卷回复统计
    const responsesStatsResult = await c.env.DB.prepare(`
      SELECT
        COUNT(*) as total,
        SUM(CASE WHEN created_at >= ? THEN 1 ELSE 0 END) as today_new,
        SUM(CASE WHEN created_at >= ? THEN 1 ELSE 0 END) as yesterday_new,
        SUM(CASE WHEN created_at >= ? THEN 1 ELSE 0 END) as week_new
      FROM questionnaire_responses_v2
    `).bind(todayStart, yesterdayStart, weekStart).first();

    // 计算趋势百分比（相比昨天）
    const calculateTrend = (today, yesterday) => {
      if (yesterday === 0) return today > 0 ? 100 : 0;
      return Math.round(((today - yesterday) / yesterday) * 100);
    };

    // 组织返回数据，提供前端需要的所有字段
    const stats = {
      // 基础统计数据
      totalUsers: usersStatsResult?.total || 0,
      totalStories: storiesStatsResult?.total || 0,
      totalQuestionnaireVoices: voicesStatsResult?.total || 0, // 修正字段名
      totalVoices: voicesStatsResult?.total || 0, // 保持兼容性
      totalResponses: responsesStatsResult?.total || 0,

      // 今日新增数据
      todayUsers: usersStatsResult?.today_new || 0,
      todayStories: storiesStatsResult?.today_new || 0,
      todayQuestionnaireVoices: voicesStatsResult?.today_new || 0,
      todayResponses: responsesStatsResult?.today_new || 0,

      // 活跃用户（今日有活动的用户）
      activeUsers: usersStatsResult?.active_today || 0,

      // 待审核统计
      pendingStories: storiesStatsResult?.pending || 0,
      pendingVoices: voicesStatsResult?.pending || 0,
      pendingReviews: {
        stories: storiesStatsResult?.pending || 0,
        voices: voicesStatsResult?.pending || 0,
        total: (storiesStatsResult?.pending || 0) + (voicesStatsResult?.pending || 0)
      },

      // 趋势数据（相比昨天的变化百分比）
      trends: {
        users: calculateTrend(usersStatsResult?.today_new || 0, usersStatsResult?.yesterday_new || 0),
        stories: calculateTrend(storiesStatsResult?.today_new || 0, storiesStatsResult?.yesterday_new || 0),
        voices: calculateTrend(voicesStatsResult?.today_new || 0, voicesStatsResult?.yesterday_new || 0),
        responses: calculateTrend(responsesStatsResult?.today_new || 0, responsesStatsResult?.yesterday_new || 0)
      },

      // 互动统计
      totalLikes: (storiesStatsResult?.total_likes || 0) + (voicesStatsResult?.total_likes || 0),
      totalViews: (storiesStatsResult?.total_views || 0) + (voicesStatsResult?.total_views || 0),

      // 系统状态
      systemHealth: 'good',
      lastUpdated: new Date().toISOString()
    };

    return c.json({
      success: true,
      data: stats
    });
  } catch (error) {
    console.error('Dashboard stats API error:', error);
    return c.json({ success: false, error: error.message }, 500);
  }
});

// 用户角色分布API
app.get('/api/admin/dashboard/user-roles', async (c) => {
  try {
    const userRolesResult = await c.env.DB.prepare(`
      SELECT
        user_type,
        COUNT(*) as count,
        ROUND(COUNT(*) * 100.0 / (SELECT COUNT(*) FROM users_v2), 2) as percentage
      FROM users_v2
      GROUP BY user_type
      ORDER BY count DESC
    `).all();

    return c.json({
      success: true,
      data: userRolesResult.results || []
    });
  } catch (error) {
    console.error('User roles API error:', error);
    return c.json({ success: false, error: error.message }, 500);
  }
});

// 内容状态分布API
app.get('/api/admin/dashboard/content-status', async (c) => {
  try {
    const contentStatusResult = await c.env.DB.prepare(`
      SELECT
        status,
        SUM(CASE WHEN content_type = 'story' THEN count ELSE 0 END) as stories,
        SUM(CASE WHEN content_type = 'voice' THEN count ELSE 0 END) as voices,
        SUM(count) as total,
        ROUND(SUM(count) * 100.0 / (
          SELECT
            (SELECT COUNT(*) FROM story_contents_v2) +
            (SELECT COUNT(*) FROM questionnaire_voices_v2)
        ), 2) as percentage
      FROM (
        SELECT 'story' as content_type, status, COUNT(*) as count
        FROM story_contents_v2
        GROUP BY status
        UNION ALL
        SELECT 'voice' as content_type, status, COUNT(*) as count
        FROM questionnaire_voices_v2
        GROUP BY status
      ) combined
      GROUP BY status
      ORDER BY total DESC
    `).all();

    return c.json({
      success: true,
      data: contentStatusResult.results || []
    });
  } catch (error) {
    console.error('Content status API error:', error);
    return c.json({ success: false, error: error.message }, 500);
  }
});

// 用户增长趋势API
app.get('/api/admin/dashboard/user-growth', async (c) => {
  try {
    const range = c.req.query('range') || 'month';
    const days = range === 'week' ? 7 :
                 range === 'month' ? 30 :
                 range === 'quarter' ? 90 : 365;

    // 生成日期序列和统计数据
    const userGrowthResult = await c.env.DB.prepare(`
      WITH RECURSIVE date_series(date) AS (
        SELECT date('now', '-${days-1} days')
        UNION ALL
        SELECT date(date, '+1 day')
        FROM date_series
        WHERE date < date('now')
      ),
      daily_stats AS (
        SELECT
          DATE(created_at) as date,
          COUNT(*) as new_users
        FROM users_v2
        WHERE created_at >= date('now', '-${days} days')
        GROUP BY DATE(created_at)
      ),
      cumulative_stats AS (
        SELECT
          ds.date,
          COALESCE(dst.new_users, 0) as newUsers,
          (SELECT COUNT(*) FROM users_v2 WHERE DATE(created_at) <= ds.date) as totalUsers,
          COALESCE((SELECT COUNT(*) FROM users_v2 WHERE DATE(updated_at) = ds.date), 0) as activeUsers
        FROM date_series ds
        LEFT JOIN daily_stats dst ON ds.date = dst.date
      )
      SELECT * FROM cumulative_stats ORDER BY date
    `).all();

    return c.json({
      success: true,
      data: userGrowthResult.results || []
    });
  } catch (error) {
    console.error('User growth API error:', error);
    return c.json({ success: false, error: error.message }, 500);
  }
});

// 内容增长趋势API
app.get('/api/admin/dashboard/content-growth', async (c) => {
  try {
    const range = c.req.query('range') || 'month';
    const days = range === 'week' ? 7 :
                 range === 'month' ? 30 :
                 range === 'quarter' ? 90 : 365;

    // 生成日期序列和统计数据
    const contentGrowthResult = await c.env.DB.prepare(`
      WITH RECURSIVE date_series(date) AS (
        SELECT date('now', '-${days-1} days')
        UNION ALL
        SELECT date(date, '+1 day')
        FROM date_series
        WHERE date < date('now')
      ),
      daily_stories AS (
        SELECT
          DATE(created_at) as date,
          COUNT(*) as new_stories
        FROM story_contents_v2
        WHERE created_at >= date('now', '-${days} days')
        GROUP BY DATE(created_at)
      ),
      daily_voices AS (
        SELECT
          DATE(created_at) as date,
          COUNT(*) as new_voices
        FROM questionnaire_voices_v2
        WHERE created_at >= date('now', '-${days} days')
        GROUP BY DATE(created_at)
      ),
      daily_responses AS (
        SELECT
          DATE(created_at) as date,
          COUNT(*) as new_responses
        FROM questionnaire_responses_v2
        WHERE created_at >= date('now', '-${days} days')
        GROUP BY DATE(created_at)
      ),
      cumulative_stats AS (
        SELECT
          ds.date,
          COALESCE(dst.new_stories, 0) as newStories,
          COALESCE(dv.new_voices, 0) as newVoices,
          COALESCE(dr.new_responses, 0) as newResponses,
          (SELECT COUNT(*) FROM story_contents_v2 WHERE DATE(created_at) <= ds.date) as totalStories,
          (SELECT COUNT(*) FROM questionnaire_voices_v2 WHERE DATE(created_at) <= ds.date) as totalVoices,
          (SELECT COUNT(*) FROM questionnaire_responses_v2 WHERE DATE(created_at) <= ds.date) as totalResponses
        FROM date_series ds
        LEFT JOIN daily_stories dst ON ds.date = dst.date
        LEFT JOIN daily_voices dv ON ds.date = dv.date
        LEFT JOIN daily_responses dr ON ds.date = dr.date
      )
      SELECT * FROM cumulative_stats ORDER BY date
    `).all();

    return c.json({
      success: true,
      data: contentGrowthResult.results || []
    });
  } catch (error) {
    console.error('Content growth API error:', error);
    return c.json({ success: false, error: error.message }, 500);
  }
});

// 综合趋势API
app.get('/api/admin/dashboard/comprehensive-trend', async (c) => {
  try {
    const range = c.req.query('range') || 'month';
    const days = range === 'week' ? 7 :
                 range === 'month' ? 30 :
                 range === 'quarter' ? 90 : 365;

    // 生成综合趋势数据
    const trendResult = await c.env.DB.prepare(`
      WITH RECURSIVE date_series(date) AS (
        SELECT date('now', '-${days-1} days')
        UNION ALL
        SELECT date(date, '+1 day')
        FROM date_series
        WHERE date < date('now')
      ),
      daily_users AS (
        SELECT DATE(created_at) as date, COUNT(*) as users
        FROM users_v2 WHERE created_at >= date('now', '-${days} days')
        GROUP BY DATE(created_at)
      ),
      daily_stories AS (
        SELECT DATE(created_at) as date, COUNT(*) as stories
        FROM story_contents_v2 WHERE created_at >= date('now', '-${days} days')
        GROUP BY DATE(created_at)
      ),
      daily_voices AS (
        SELECT DATE(created_at) as date, COUNT(*) as voices
        FROM questionnaire_voices_v2 WHERE created_at >= date('now', '-${days} days')
        GROUP BY DATE(created_at)
      ),
      daily_responses AS (
        SELECT DATE(created_at) as date, COUNT(*) as responses
        FROM questionnaire_responses_v2 WHERE created_at >= date('now', '-${days} days')
        GROUP BY DATE(created_at)
      )
      SELECT
        ds.date,
        COALESCE(du.users, 0) as users,
        COALESCE(dst.stories, 0) as stories,
        COALESCE(dv.voices, 0) as voices,
        COALESCE(dr.responses, 0) as responses,
        -- 计算增长率和参与度
        CASE WHEN LAG(du.users) OVER (ORDER BY ds.date) > 0
             THEN ROUND((COALESCE(du.users, 0) - LAG(du.users) OVER (ORDER BY ds.date)) * 100.0 / LAG(du.users) OVER (ORDER BY ds.date), 2)
             ELSE 0 END as userGrowthRate,
        CASE WHEN LAG(dst.stories + dv.voices) OVER (ORDER BY ds.date) > 0
             THEN ROUND(((COALESCE(dst.stories, 0) + COALESCE(dv.voices, 0)) - LAG(dst.stories + dv.voices) OVER (ORDER BY ds.date)) * 100.0 / LAG(dst.stories + dv.voices) OVER (ORDER BY ds.date), 2)
             ELSE 0 END as contentGrowthRate,
        CASE WHEN COALESCE(du.users, 0) > 0
             THEN ROUND((COALESCE(dst.stories, 0) + COALESCE(dv.voices, 0) + COALESCE(dr.responses, 0)) * 100.0 / du.users, 2)
             ELSE 0 END as engagementRate
      FROM date_series ds
      LEFT JOIN daily_users du ON ds.date = du.date
      LEFT JOIN daily_stories dst ON ds.date = dst.date
      LEFT JOIN daily_voices dv ON ds.date = dv.date
      LEFT JOIN daily_responses dr ON ds.date = dr.date
      ORDER BY ds.date
    `).all();

    return c.json({
      success: true,
      data: trendResult.results || []
    });
  } catch (error) {
    console.error('Comprehensive trend API error:', error);
    return c.json({ success: false, error: error.message }, 500);
  }
});

// 问卷类型分布API
app.get('/api/admin/dashboard/questionnaire-types', async (c) => {
  try {
    // 模拟问卷类型数据（实际应用中需要在数据库中添加type字段）
    const typeResult = await c.env.DB.prepare(`
      SELECT
        'employment' as type,
        COUNT(*) as count,
        ROUND(COUNT(*) * 100.0 / (SELECT COUNT(*) FROM questionnaire_responses_v2), 2) as percentage,
        ROUND(AVG(8.5), 1) as avg_completion_time,
        ROUND(87.2, 1) as completion_rate
      FROM questionnaire_responses_v2
      UNION ALL
      SELECT
        'satisfaction' as type,
        ROUND(COUNT(*) * 0.6) as count,
        ROUND(COUNT(*) * 0.6 * 100.0 / (SELECT COUNT(*) FROM questionnaire_responses_v2), 2) as percentage,
        ROUND(AVG(6.2), 1) as avg_completion_time,
        ROUND(92.1, 1) as completion_rate
      FROM questionnaire_responses_v2
      UNION ALL
      SELECT
        'salary' as type,
        ROUND(COUNT(*) * 0.4) as count,
        ROUND(COUNT(*) * 0.4 * 100.0 / (SELECT COUNT(*) FROM questionnaire_responses_v2), 2) as percentage,
        ROUND(AVG(4.8), 1) as avg_completion_time,
        ROUND(94.5, 1) as completion_rate
      FROM questionnaire_responses_v2
    `).all();

    return c.json({
      success: true,
      data: typeResult.results || []
    });
  } catch (error) {
    console.error('Questionnaire types API error:', error);
    return c.json({ success: false, error: error.message }, 500);
  }
});

// 故事类型分布API
app.get('/api/admin/dashboard/story-types', async (c) => {
  try {
    // 模拟故事类型数据（实际应用中需要在数据库中添加type字段）
    const typeResult = await c.env.DB.prepare(`
      SELECT
        'job_search' as type,
        ROUND(COUNT(*) * 0.35) as count,
        ROUND(35.2, 1) as percentage,
        ROUND(AVG(COALESCE(likes, 0) * 1.2), 1) as avg_likes,
        ROUND(AVG(COALESCE(views, 0) * 1.5), 1) as avg_views,
        ROUND(7.8, 1) as engagement_rate
      FROM story_contents_v2
      UNION ALL
      SELECT
        'work_experience' as type,
        ROUND(COUNT(*) * 0.25) as count,
        ROUND(25.5, 1) as percentage,
        ROUND(AVG(COALESCE(likes, 0) * 1.8), 1) as avg_likes,
        ROUND(AVG(COALESCE(views, 0) * 2.0), 1) as avg_views,
        ROUND(9.1, 1) as engagement_rate
      FROM story_contents_v2
      UNION ALL
      SELECT
        'workplace_confusion' as type,
        ROUND(COUNT(*) * 0.19) as count,
        ROUND(18.8, 1) as percentage,
        ROUND(AVG(COALESCE(likes, 0) * 0.9), 1) as avg_likes,
        ROUND(AVG(COALESCE(views, 0) * 1.3), 1) as avg_views,
        ROUND(6.6, 1) as engagement_rate
      FROM story_contents_v2
    `).all();

    return c.json({
      success: true,
      data: typeResult.results || []
    });
  } catch (error) {
    console.error('Story types API error:', error);
    return c.json({ success: false, error: error.message }, 500);
  }
});

// 时间段对比API
app.get('/api/admin/dashboard/time-comparison', async (c) => {
  try {
    const type = c.req.query('type') || 'month';

    // 根据对比类型生成时间段数据
    let periods = [];
    let dateFormat = '';

    if (type === 'week') {
      periods = ['第1周', '第2周', '第3周', '第4周'];
      dateFormat = 'week';
    } else if (type === 'month') {
      periods = ['1月', '2月', '3月', '4月', '5月', '6月'];
      dateFormat = 'month';
    } else {
      periods = ['Q1', 'Q2', 'Q3', 'Q4'];
      dateFormat = 'quarter';
    }

    // 模拟时间段对比数据
    const comparisonData = periods.map((period, index) => {
      const baseUsers = 200 + Math.random() * 100 + index * 20;
      const baseStories = 30 + Math.random() * 20 + index * 5;
      const baseVoices = 25 + Math.random() * 15 + index * 3;
      const baseResponses = 80 + Math.random() * 40 + index * 10;

      return {
        period,
        currentUsers: Math.round(baseUsers),
        previousUsers: Math.round(baseUsers * (0.8 + Math.random() * 0.3)),
        currentStories: Math.round(baseStories),
        previousStories: Math.round(baseStories * (0.7 + Math.random() * 0.4)),
        currentVoices: Math.round(baseVoices),
        previousVoices: Math.round(baseVoices * (0.6 + Math.random() * 0.5)),
        currentResponses: Math.round(baseResponses),
        previousResponses: Math.round(baseResponses * (0.75 + Math.random() * 0.35)),
        userGrowth: Math.round((Math.random() - 0.3) * 50 * 100) / 100,
        contentGrowth: Math.round((Math.random() - 0.2) * 40 * 100) / 100
      };
    });

    return c.json({
      success: true,
      data: comparisonData
    });
  } catch (error) {
    console.error('Time comparison API error:', error);
    return c.json({ success: false, error: error.message }, 500);
  }
});

// 用户活跃度对比API
app.get('/api/admin/dashboard/user-activity-comparison', async (c) => {
  try {
    const activityResult = await c.env.DB.prepare(`
      SELECT
        user_type,
        COUNT(*) as total_users,
        COUNT(CASE WHEN DATE(updated_at) = DATE('now') THEN 1 END) as daily_active_users,
        COUNT(CASE WHEN DATE(updated_at) >= DATE('now', '-7 days') THEN 1 END) as weekly_active_users,
        COUNT(CASE WHEN DATE(updated_at) >= DATE('now', '-30 days') THEN 1 END) as monthly_active_users,
        -- 模拟会话数据
        ROUND(AVG(12.5 + RANDOM() * 10), 1) as avg_session_duration,
        ROUND(AVG(8.3 + RANDOM() * 5), 1) as avg_actions_per_session,
        ROUND(COUNT(CASE WHEN DATE(updated_at) >= DATE('now', '-30 days') THEN 1 END) * 100.0 / COUNT(*), 1) as retention_rate,
        ROUND(75 + RANDOM() * 20, 1) as engagement_score
      FROM users_v2
      GROUP BY user_type
      ORDER BY total_users DESC
    `).all();

    return c.json({
      success: true,
      data: activityResult.results || []
    });
  } catch (error) {
    console.error('User activity comparison API error:', error);
    return c.json({ success: false, error: error.message }, 500);
  }
});

// 内容质量对比API
app.get('/api/admin/dashboard/content-quality-comparison', async (c) => {
  try {
    const qualityResult = await c.env.DB.prepare(`
      SELECT
        'story' as content_type,
        COUNT(*) as total_count,
        COUNT(CASE WHEN status = 'approved' THEN 1 END) as approved_count,
        COUNT(CASE WHEN status = 'rejected' THEN 1 END) as rejected_count,
        ROUND(COUNT(CASE WHEN status = 'approved' THEN 1 END) * 100.0 / COUNT(*), 1) as approval_rate,
        ROUND(AVG(COALESCE(likes, 0)), 1) as avg_likes,
        ROUND(AVG(COALESCE(views, 0)), 1) as avg_views,
        ROUND(AVG(0), 1) as avg_comments,
        ROUND(75 + RANDOM() * 20, 1) as quality_score,
        ROUND(5 + RANDOM() * 5, 1) as engagement_rate
      FROM story_contents_v2
      UNION ALL
      SELECT
        'questionnaire_voice' as content_type,
        COUNT(*) as total_count,
        COUNT(CASE WHEN status = 'approved' THEN 1 END) as approved_count,
        COUNT(CASE WHEN status = 'rejected' THEN 1 END) as rejected_count,
        ROUND(COUNT(CASE WHEN status = 'approved' THEN 1 END) * 100.0 / COUNT(*), 1) as approval_rate,
        ROUND(AVG(COALESCE(likes, 0)), 1) as avg_likes,
        ROUND(AVG(COALESCE(views, 0)), 1) as avg_views,
        ROUND(AVG(0), 1) as avg_comments,
        ROUND(80 + RANDOM() * 15, 1) as quality_score,
        ROUND(6 + RANDOM() * 4, 1) as engagement_rate
      FROM questionnaire_voices_v2
      UNION ALL
      SELECT
        'questionnaire_response' as content_type,
        COUNT(*) as total_count,
        COUNT(*) as approved_count,
        0 as rejected_count,
        100.0 as approval_rate,
        ROUND(AVG(3), 1) as avg_likes,
        ROUND(AVG(45), 1) as avg_views,
        ROUND(AVG(1), 1) as avg_comments,
        ROUND(70 + RANDOM() * 10, 1) as quality_score,
        ROUND(7 + RANDOM() * 3, 1) as engagement_rate
      FROM questionnaire_responses_v2
    `).all();

    return c.json({
      success: true,
      data: qualityResult.results || []
    });
  } catch (error) {
    console.error('Content quality comparison API error:', error);
    return c.json({ success: false, error: error.message }, 500);
  }
});

app.get('/api/admin/test-data/status', async (c) => {
  try {
    return c.json({
      success: true,
      data: {
        testDataEnabled: true,
        lastGenerated: '2025-05-26T16:00:00.000Z',
        recordCount: 173,
        status: 'active'
      }
    });
  } catch (error) {
    return c.json({ success: false, error: error.message }, 500);
  }
});

// Deidentification APIs
app.get('/api/admin/deidentification/config', async (c) => {
  try {
    return c.json({
      success: true,
      data: {
        enabled: true,
        level: 'medium',
        provider: 'openai',
        autoProcess: true,
        retainOriginal: true,
        settings: {
          removePersonalInfo: true,
          removeLocationInfo: true,
          removeContactInfo: true,
          removeCompanyInfo: false,
          replaceWithPlaceholders: true
        },
        lastUpdated: '2025-05-27T10:00:00.000Z'
      }
    });
  } catch (error) {
    return c.json({ success: false, error: error.message }, 500);
  }
});

app.post('/api/admin/deidentification/config', async (c) => {
  try {
    const config = await c.req.json();

    /*
     * 这里应该保存配置到数据库
     * 目前返回模拟成功响应
     */

    return c.json({
      success: true,
      data: {
        ...config,
        lastUpdated: new Date().toISOString()
      }
    });
  } catch (error) {
    return c.json({ success: false, error: error.message }, 500);
  }
});

app.get('/api/admin/deidentification/test-data', async (c) => {
  try {
    return c.json({
      success: true,
      data: [
        {
          id: 1,
          original: '我叫张三，在北京的阿里巴巴工作，手机号是13812345678',
          processed: '我叫[姓名]，在[城市]的[公司名称]工作，手机号是[电话号码]',
          level: 'high',
          processedAt: '2025-05-27T09:30:00.000Z'
        },
        {
          id: 2,
          original: '毕业于清华大学计算机系，现在在腾讯做前端开发',
          processed: '毕业于[学校名称]计算机系，现在在[公司名称]做前端开发',
          level: 'medium',
          processedAt: '2025-05-27T09:25:00.000Z'
        },
        {
          id: 3,
          original: '住在上海浦东新区，邮箱是********************',
          processed: '住在[城市][区域]，邮箱是[邮箱地址]',
          level: 'high',
          processedAt: '2025-05-27T09:20:00.000Z'
        }
      ]
    });
  } catch (error) {
    return c.json({ success: false, error: error.message }, 500);
  }
});

app.get('/api/admin/deidentification/provider-stats', async (c) => {
  try {
    return c.json({
      success: true,
      data: {
        totalProcessed: 1247,
        successRate: 98.5,
        averageTime: 1.2,
        providers: {
          openai: {
            name: 'OpenAI GPT',
            processed: 856,
            successRate: 99.1,
            averageTime: 1.1,
            status: 'active'
          },
          local: {
            name: '本地规则引擎',
            processed: 391,
            successRate: 97.2,
            averageTime: 0.8,
            status: 'active'
          }
        },
        lastUpdated: '2025-05-27T10:00:00.000Z'
      }
    });
  } catch (error) {
    return c.json({ success: false, error: error.message }, 500);
  }
});

app.post('/api/admin/deidentification/test-provider', async (c) => {
  try {
    const { text, level, provider } = await c.req.json();

    // 模拟不同级别的脱敏处理
    let processedText = text;

    if (level === 'low') {
      // 低级脱敏：只处理明显的个人信息
      processedText = text
        .replace(/\d{11}/g, '[手机号码]')
        .replace(/\w+@\w+\.\w+/g, '[邮箱地址]');
    } else if (level === 'medium') {
      // 中级脱敏：处理更多信息
      processedText = text
        .replace(/[\u4e00-\u9fa5]{2,3}(?=，|在|的)/g, '[姓名]')
        .replace(/\d{11}/g, '[手机号码]')
        .replace(/\w+@\w+\.\w+/g, '[邮箱地址]')
        .replace(/(北京|上海|广州|深圳|杭州|南京|武汉|成都|重庆|天津)/g, '[城市]');
    } else if (level === 'high') {
      // 高级脱敏：处理所有敏感信息
      processedText = text
        .replace(/[\u4e00-\u9fa5]{2,4}(?=，|在|的|毕业)/g, '[姓名]')
        .replace(/\d{11}/g, '[手机号码]')
        .replace(/\w+@\w+\.\w+/g, '[邮箱地址]')
        .replace(/(北京|上海|广州|深圳|杭州|南京|武汉|成都|重庆|天津|浦东新区|朝阳区|海淀区)/g, '[地区]')
        .replace(/(阿里巴巴|腾讯|百度|字节跳动|美团|滴滴|京东|小米)/g, '[公司名称]')
        .replace(/(清华大学|北京大学|复旦大学|上海交通大学|浙江大学)/g, '[学校名称]');
    }

    // 模拟处理时间
    await new Promise(resolve => setTimeout(resolve, 800 + Math.random() * 400));

    return c.json({
      success: true,
      data: {
        original: text,
        processed: processedText,
        level,
        provider,
        processingTime: (800 + Math.random() * 400).toFixed(0) + 'ms',
        confidence: (85 + Math.random() * 10).toFixed(1) + '%',
        processedAt: new Date().toISOString()
      }
    });
  } catch (error) {
    return c.json({ success: false, error: error.message }, 500);
  }
});

// AI供应商健康检查
app.get('/api/admin/deidentification/health-check', async (c) => {
  try {
    return c.json({
      success: true,
      data: {
        providers: {
          openai: {
            id: 'openai',
            name: 'OpenAI GPT',
            status: 'healthy',
            responseTime: 1200,
            lastCheck: new Date().toISOString(),
            apiKeyStatus: 'valid',
            rateLimitRemaining: 4500,
            rateLimitReset: new Date(Date.now() + 3600000).toISOString()
          },
          grok: {
            id: 'grok',
            name: 'Grok AI',
            status: 'healthy',
            responseTime: 950,
            lastCheck: new Date().toISOString(),
            apiKeyStatus: 'valid',
            rateLimitRemaining: 2800,
            rateLimitReset: new Date(Date.now() + 3600000).toISOString()
          },
          local: {
            id: 'local',
            name: '本地规则引擎',
            status: 'healthy',
            responseTime: 50,
            lastCheck: new Date().toISOString(),
            apiKeyStatus: 'not_required',
            rateLimitRemaining: 999999,
            rateLimitReset: null
          }
        },
        summary: {
          totalProviders: 3,
          healthyProviders: 3,
          averageResponseTime: 733,
          overallStatus: 'healthy'
        },
        lastUpdated: new Date().toISOString()
      }
    });
  } catch (error) {
    return c.json({ success: false, error: error.message }, 500);
  }
});

// AI供应商API密钥验证
app.post('/api/admin/deidentification/validate-api-key', async (c) => {
  try {
    const { provider, apiKey } = await c.req.json();

    // 模拟API密钥验证
    let isValid = false;
    let errorMessage = '';

    if (provider === 'openai') {
      // 简单的OpenAI API密钥格式验证
      isValid = apiKey && apiKey.startsWith('sk-') && apiKey.length > 20;
      if (!isValid) {
        errorMessage = 'OpenAI API密钥格式无效，应以sk-开头';
      }
    } else if (provider === 'grok') {
      // 简单的Grok API密钥格式验证
      isValid = apiKey && apiKey.startsWith('xai-') && apiKey.length > 20;
      if (!isValid) {
        errorMessage = 'Grok API密钥格式无效，应以xai-开头';
      }
    } else if (provider === 'local') {
      // 本地引擎不需要API密钥
      isValid = true;
    } else {
      errorMessage = '不支持的AI供应商';
    }

    // 模拟网络延迟
    await new Promise(resolve => setTimeout(resolve, 500 + Math.random() * 1000));

    return c.json({
      success: true,
      data: {
        provider,
        isValid,
        errorMessage,
        checkedAt: new Date().toISOString(),
        details: isValid ? {
          accountType: 'premium',
          quotaRemaining: Math.floor(Math.random() * 5000) + 1000,
          quotaReset: new Date(Date.now() + ********).toISOString()
        } : null
      }
    });
  } catch (error) {
    return c.json({ success: false, error: error.message }, 500);
  }
});

// 问卷统计API
app.get('/api/questionnaire/stats', async (c) => {
  try {
    // 获取总数
    const totalResult = await c.env.DB.prepare(
      'SELECT COUNT(*) as total FROM questionnaire_responses_v2'
    ).first();
    const total = totalResult?.total || 0;

    // 获取教育水平分布
    const educationResult = await c.env.DB.prepare(`
      SELECT education_level_display as name, COUNT(*) as count
      FROM questionnaire_responses_v2
      WHERE education_level_display IS NOT NULL
      GROUP BY education_level_display
    `).all();

    // 获取地区分布
    const regionResult = await c.env.DB.prepare(`
      SELECT region_display as name, COUNT(*) as count
      FROM questionnaire_responses_v2
      WHERE region_display IS NOT NULL
      GROUP BY region_display
      ORDER BY count DESC
    `).all();

    // 获取就业状态分布
    const employmentResult = await c.env.DB.prepare(`
      SELECT employment_status as name, COUNT(*) as count
      FROM questionnaire_responses_v2
      WHERE employment_status IS NOT NULL
      GROUP BY employment_status
    `).all();

    // 获取专业分布
    const majorResult = await c.env.DB.prepare(`
      SELECT major_display as name, COUNT(*) as count
      FROM questionnaire_responses_v2
      WHERE major_display IS NOT NULL AND major_display != ''
      GROUP BY major_display
      ORDER BY count DESC
      LIMIT 15
    `).all();

    // 获取毕业年份分布
    const graduationYearResult = await c.env.DB.prepare(`
      SELECT graduation_year as name, COUNT(*) as count
      FROM questionnaire_responses_v2
      WHERE graduation_year IS NOT NULL
      GROUP BY graduation_year
      ORDER BY graduation_year DESC
    `).all();

    // 获取行业分布
    const industryResult = await c.env.DB.prepare(`
      SELECT current_industry_display as name, COUNT(*) as count
      FROM questionnaire_responses_v2
      WHERE current_industry_display IS NOT NULL AND current_industry_display != ''
      GROUP BY current_industry_display
      ORDER BY count DESC
      LIMIT 10
    `).all();

    // 计算就业相关统计
    const employedCount = employmentResult.results?.find(item =>
      item.name === '已就业' || item.name === 'employed'
    )?.count || 0;

    const unemployedCount = employmentResult.results?.find(item =>
      item.name === '求职中' || item.name === 'unemployed'
    )?.count || 0;

    // 调试信息
    console.log('专业查询结果:', majorResult);
    console.log('毕业年份查询结果:', graduationYearResult);
    console.log('行业查询结果:', industryResult);

    // 计算百分比的辅助函数
    const calculatePercentages = (items, totalCount) => {
      return items.map(item => ({
        name: item.name,
        count: item.count,
        percentage: totalCount > 0 ? Math.round((item.count / totalCount) * 100) : 0
      }));
    };

    return c.json({
      success: true,
      statistics: {
        totalResponses: total,
        verifiedCount: total, // 假设所有都是已验证的
        anonymousCount: 0, // 假设没有匿名的
        employedCount,
        unemployedCount,
        educationLevels: calculatePercentages(educationResult.results || [], total),
        regions: calculatePercentages(regionResult.results || [], total),
        majors: calculatePercentages(majorResult.results || [], total),
        graduationYears: (graduationYearResult.results || []).map(item => ({
          name: item.name?.toString() || 'unknown',
          count: item.count,
          percentage: total > 0 ? Math.round((item.count / total) * 100) : 0
        })),
        industries: calculatePercentages(industryResult.results || [], total),
        employmentStatus: calculatePercentages(employmentResult.results || [], total),
        lastUpdated: new Date().toISOString(),
        // 调试信息
        debug: {
          majorResultCount: majorResult.results?.length || 0,
          graduationResultCount: graduationYearResult.results?.length || 0,
          industryResultCount: industryResult.results?.length || 0
        }
      }
    });
  } catch (error) {
    console.error('问卷统计API错误:', error);
    return c.json({ success: false, error: error.message }, 500);
  }
});

// AI供应商配置管理
app.get('/api/admin/deidentification/providers', async (c) => {
  try {
    return c.json({
      success: true,
      data: {
        providers: [
          {
            id: 'openai',
            name: 'OpenAI GPT',
            description: '使用OpenAI的GPT模型进行智能脱敏',
            type: 'cloud',
            status: 'active',
            capabilities: ['text_deidentification', 'context_understanding', 'multilingual'],
            pricing: {
              model: 'pay_per_use',
              costPer1000Tokens: 0.002
            },
            configuration: {
              model: 'gpt-3.5-turbo',
              maxTokens: 4000,
              temperature: 0.1
            },
            requiresApiKey: true,
            apiKeyFormat: 'sk-...',
            documentation: 'https://platform.openai.com/docs'
          },
          {
            id: 'grok',
            name: 'Grok AI',
            description: '使用xAI的Grok模型进行脱敏处理',
            type: 'cloud',
            status: 'active',
            capabilities: ['text_deidentification', 'real_time_processing', 'high_accuracy'],
            pricing: {
              model: 'pay_per_use',
              costPer1000Tokens: 0.001
            },
            configuration: {
              model: 'grok-beta',
              maxTokens: 8000,
              temperature: 0.0
            },
            requiresApiKey: true,
            apiKeyFormat: 'xai-...',
            documentation: 'https://docs.x.ai'
          },
          {
            id: 'local',
            name: '本地规则引擎',
            description: '基于正则表达式和规则的本地脱敏引擎',
            type: 'local',
            status: 'active',
            capabilities: ['fast_processing', 'offline_support', 'customizable_rules'],
            pricing: {
              model: 'free',
              costPer1000Tokens: 0
            },
            configuration: {
              rules: 'chinese_pii_v1',
              strictMode: false,
              preserveFormat: true
            },
            requiresApiKey: false,
            apiKeyFormat: null,
            documentation: '/docs/local-engine'
          }
        ],
        defaultProvider: 'local',
        fallbackOrder: ['local', 'openai', 'grok'],
        lastUpdated: new Date().toISOString()
      }
    });
  } catch (error) {
    return c.json({ success: false, error: error.message }, 500);
  }
});

// Root path - API info
app.get('/', (c) => {
  return c.json({
    name: 'College Employment Survey API',
    version: 'fixed-api-v2.0',
    status: 'running',
    environment: c.env?.ENVIRONMENT || 'production',
    timestamp: new Date().toISOString(),
    endpoints: {
      health: '/health',
      questionnaireStats: '/api/questionnaire/stats',
      questionnaireVoices: '/api/questionnaire-voices',
      visualizationData: '/api/visualization/data',
      storyList: '/api/story/list'
    }
  });
});

// Health check
app.get('/health', (c) => {
  return c.json({
    status: 'ok',
    environment: c.env?.ENVIRONMENT || 'production',
    timestamp: new Date().toISOString(),
    version: 'fixed-api-v2.0'
  });
});

// 详细的系统诊断端点
app.get('/api/system/diagnostics', async (c) => {
  const diagnostics = {
    timestamp: new Date().toISOString(),
    environment: c.env?.ENVIRONMENT || 'unknown',
    version: 'fixed-api-v2.0',
    databases: {},
    storage: {},
    bindings: {},
    errors: []
  };

  try {
    // 检查环境变量绑定
    diagnostics.bindings = {
      DB: !!c.env.DB,
      KV: !!c.env.KV,
      R2: !!c.env.R2,
      ENVIRONMENT: c.env?.ENVIRONMENT || 'not_set'
    };

    // 检查D1数据库连接
    if (c.env.DB) {
      try {
        // 检查数据库表结构
        const tablesResult = await c.env.DB.prepare('SELECT name FROM sqlite_master WHERE type=\'table\'').all();
        const tables = tablesResult.results?.map(row => row.name) || [];

        diagnostics.databases.D1 = {
          status: 'connected',
          tables: tables,
          tableCount: tables.length
        };

        // 检查主要表的记录数
        const tableChecks = [
          'questionnaire_responses_v2',
          'questionnaire_voices_v2',
          'story_contents_v2'
        ];

        for (const tableName of tableChecks) {
          try {
            if (tables.includes(tableName)) {
              const countResult = await c.env.DB.prepare(`SELECT COUNT(*) as count FROM ${tableName}`).first();
              diagnostics.databases.D1[tableName] = {
                count: countResult?.count || 0,
                status: 'accessible'
              };
            } else {
              diagnostics.databases.D1[tableName] = {
                status: 'table_not_found'
              };
            }
          } catch (e) {
            diagnostics.databases.D1[tableName] = {
              status: 'error',
              error: e.message
            };
          }
        }

        // 检查表结构
        try {
          const schemaResult = await c.env.DB.prepare('SELECT sql FROM sqlite_master WHERE type=\'table\' AND name=\'questionnaire_responses_v2\'').first();
          if (schemaResult) {
            diagnostics.databases.D1.questionnaire_responses_v2.schema = schemaResult.sql;
          }
        } catch (e) {
          diagnostics.databases.D1.schema_check_error = e.message;
        }

      } catch (e) {
        diagnostics.databases.D1 = {
          status: 'error',
          error: e.message
        };
        diagnostics.errors.push(`D1 Database Error: ${e.message}`);
      }
    } else {
      diagnostics.databases.D1 = {
        status: 'not_configured',
        error: 'DB binding not found'
      };
      diagnostics.errors.push('D1 Database binding not configured');
    }

    // 检查KV存储
    if (c.env.KV) {
      try {
        const testKey = 'health_check_' + Date.now();
        await c.env.KV.put(testKey, 'ok', { expirationTtl: 60 });
        const testValue = await c.env.KV.get(testKey);
        await c.env.KV.delete(testKey);

        diagnostics.storage.KV = {
          status: testValue === 'ok' ? 'connected' : 'error',
          test_result: testValue
        };
      } catch (e) {
        diagnostics.storage.KV = {
          status: 'error',
          error: e.message
        };
        diagnostics.errors.push(`KV Storage Error: ${e.message}`);
      }
    } else {
      diagnostics.storage.KV = {
        status: 'not_configured',
        error: 'KV binding not found'
      };
    }

    // 检查R2存储
    if (c.env.R2) {
      try {
        const testKey = 'health-check-' + Date.now();
        await c.env.R2.put(testKey, 'test');
        const testObject = await c.env.R2.get(testKey);
        await c.env.R2.delete(testKey);

        diagnostics.storage.R2 = {
          status: testObject ? 'connected' : 'error',
          test_result: testObject ? 'success' : 'failed'
        };
      } catch (e) {
        diagnostics.storage.R2 = {
          status: 'error',
          error: e.message
        };
        diagnostics.errors.push(`R2 Storage Error: ${e.message}`);
      }
    } else {
      diagnostics.storage.R2 = {
        status: 'not_configured',
        error: 'R2 binding not found'
      };
    }

  } catch (e) {
    diagnostics.errors.push(`System Error: ${e.message}`);
  }

  return c.json(diagnostics);
});

// API调用追踪端点
app.get('/api/system/trace/:endpoint', async (c) => {
  const endpoint = c.req.param('endpoint');
  const trace = {
    timestamp: new Date().toISOString(),
    endpoint: endpoint,
    steps: [],
    errors: [],
    performance: {}
  };

  const startTime = Date.now();

  try {
    trace.steps.push({ step: 'start', timestamp: new Date().toISOString() });

    if (endpoint === 'questionnaire-stats') {
      // 追踪问卷统计API调用
      trace.steps.push({ step: 'checking_db_connection', timestamp: new Date().toISOString() });

      if (!c.env.DB) {
        trace.errors.push('DB binding not found');
        return c.json(trace);
      }

      trace.steps.push({ step: 'db_connected', timestamp: new Date().toISOString() });

      // 执行查询并记录每一步
      try {
        const queryStartTime = Date.now();
        trace.steps.push({ step: 'executing_total_count_query', timestamp: new Date().toISOString() });
        const totalResult = await c.env.DB.prepare('SELECT COUNT(*) as total FROM questionnaire_responses_v2').first();
        trace.performance.total_query_ms = Date.now() - queryStartTime;
        trace.steps.push({
          step: 'total_count_result',
          timestamp: new Date().toISOString(),
          result: totalResult,
          duration_ms: trace.performance.total_query_ms
        });

        const eduStartTime = Date.now();
        trace.steps.push({ step: 'executing_education_query', timestamp: new Date().toISOString() });
        const educationResult = await c.env.DB.prepare(`
          SELECT education_level_display as level, COUNT(*) as count
          FROM questionnaire_responses_v2
          WHERE education_level_display IS NOT NULL
          GROUP BY education_level_display
        `).all();
        trace.performance.education_query_ms = Date.now() - eduStartTime;
        trace.steps.push({
          step: 'education_query_result',
          timestamp: new Date().toISOString(),
          result: educationResult,
          duration_ms: trace.performance.education_query_ms
        });

        const regionStartTime = Date.now();
        trace.steps.push({ step: 'executing_region_query', timestamp: new Date().toISOString() });
        const regionResult = await c.env.DB.prepare(`
          SELECT region_display as region, COUNT(*) as count
          FROM questionnaire_responses_v2
          WHERE region_display IS NOT NULL
          GROUP BY region_display
        `).all();
        trace.performance.region_query_ms = Date.now() - regionStartTime;
        trace.steps.push({
          step: 'region_query_result',
          timestamp: new Date().toISOString(),
          result: regionResult,
          duration_ms: trace.performance.region_query_ms
        });

        const empStartTime = Date.now();
        trace.steps.push({ step: 'executing_employment_query', timestamp: new Date().toISOString() });
        const employmentResult = await c.env.DB.prepare(`
          SELECT employment_status as status, COUNT(*) as count
          FROM questionnaire_responses_v2
          WHERE employment_status IS NOT NULL
          GROUP BY employment_status
        `).all();
        trace.performance.employment_query_ms = Date.now() - empStartTime;
        trace.steps.push({
          step: 'employment_query_result',
          timestamp: new Date().toISOString(),
          result: employmentResult,
          duration_ms: trace.performance.employment_query_ms
        });

        // 构建最终响应
        trace.steps.push({ step: 'building_response', timestamp: new Date().toISOString() });
        const response = {
          success: true,
          data: {
            total: totalResult?.total || 0,
            educationLevels: educationResult.results || [],
            regions: regionResult.results || [],
            employmentStatus: employmentResult.results || []
          }
        };
        trace.steps.push({
          step: 'response_built',
          timestamp: new Date().toISOString(),
          response: response
        });

      } catch (e) {
        trace.errors.push(`Query Error: ${e.message}`);
        trace.steps.push({
          step: 'query_error',
          timestamp: new Date().toISOString(),
          error: e.message,
          stack: e.stack
        });
      }
    }

    trace.performance.total_duration_ms = Date.now() - startTime;
    trace.steps.push({ step: 'completed', timestamp: new Date().toISOString() });

  } catch (e) {
    trace.errors.push(`Trace Error: ${e.message}`);
    trace.performance.total_duration_ms = Date.now() - startTime;
  }

  return c.json(trace);
});

// 注意：问卷统计API已在前面定义，这里删除重复定义


// 调试端点：检查专业和毕业年份数据
app.get('/api/debug/majors-graduation', async (c) => {
  try {
    // 检查专业数据
    const majorSampleResult = await c.env.DB.prepare(`
      SELECT major_display, COUNT(*) as count
      FROM questionnaire_responses_v2
      WHERE major_display IS NOT NULL
      GROUP BY major_display
      ORDER BY count DESC
      LIMIT 10
    `).all();

    // 检查毕业年份数据
    const graduationSampleResult = await c.env.DB.prepare(`
      SELECT graduation_year, COUNT(*) as count
      FROM questionnaire_responses_v2
      WHERE graduation_year IS NOT NULL
      GROUP BY graduation_year
      ORDER BY graduation_year DESC
      LIMIT 10
    `).all();

    // 检查字段是否存在数据
    const majorCountResult = await c.env.DB.prepare(`
      SELECT COUNT(*) as total_with_major
      FROM questionnaire_responses_v2
      WHERE major_display IS NOT NULL AND major_display != ''
    `).first();

    const graduationCountResult = await c.env.DB.prepare(`
      SELECT COUNT(*) as total_with_graduation
      FROM questionnaire_responses_v2
      WHERE graduation_year IS NOT NULL
    `).first();

    // 检查所有记录的样本
    const sampleRecordsResult = await c.env.DB.prepare(`
      SELECT major_display, graduation_year, education_level_display
      FROM questionnaire_responses_v2
      LIMIT 5
    `).all();

    return c.json({
      success: true,
      debug: {
        majorSamples: majorSampleResult.results || [],
        graduationSamples: graduationSampleResult.results || [],
        counts: {
          totalWithMajor: majorCountResult?.total_with_major || 0,
          totalWithGraduation: graduationCountResult?.total_with_graduation || 0
        },
        sampleRecords: sampleRecordsResult.results || []
      }
    });
  } catch (error) {
    return c.json({ success: false, error: error.message }, 500);
  }
});

// 实时监控端点
app.get('/api/system/monitor', async (c) => {
  const monitor = {
    timestamp: new Date().toISOString(),
    status: 'monitoring',
    checks: {}
  };

  try {
    // 快速健康检查
    if (c.env.DB) {
      const dbStart = Date.now();
      try {
        await c.env.DB.prepare('SELECT 1').first();
        monitor.checks.database = {
          status: 'healthy',
          response_time_ms: Date.now() - dbStart
        };
      } catch (e) {
        monitor.checks.database = {
          status: 'error',
          error: e.message,
          response_time_ms: Date.now() - dbStart
        };
      }
    }

    // 检查主要API端点
    const apiChecks = [
      { name: 'questionnaire_stats', path: '/api/questionnaire/stats' },
      { name: 'questionnaire_voices', path: '/api/questionnaire-voices' },
      { name: 'visualization_data', path: '/api/visualization/data' }
    ];

    for (const check of apiChecks) {
      const apiStart = Date.now();
      try {
        // 模拟内部API调用检查
        if (c.env.DB) {
          await c.env.DB.prepare('SELECT COUNT(*) FROM questionnaire_responses_v2').first();
          monitor.checks[check.name] = {
            status: 'healthy',
            response_time_ms: Date.now() - apiStart
          };
        }
      } catch (e) {
        monitor.checks[check.name] = {
          status: 'error',
          error: e.message,
          response_time_ms: Date.now() - apiStart
        };
      }
    }

  } catch (e) {
    monitor.error = e.message;
  }

  return c.json(monitor);
});

// 404 handler
app.notFound((c) => {
  return c.json({ error: 'Not found' }, 404);
});

// Error handler
app.onError((err, c) => {
  console.error(`${c.req.method} ${c.req.url}`, err);
  return c.json({ error: 'Internal Server Error' }, 500);
});

export default app;
