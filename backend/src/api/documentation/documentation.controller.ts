/**
 * 文档管理控制器
 *
 * 提供项目文档的读取、同步和管理功能
 * 基于项目文档管理规范实现
 */

import { Context } from 'hono';
import { Env } from '../../types';

// 文档项接口
interface ProjectDocument {
  id: string;
  title: string;
  description: string;
  filePath: string;
  category: string;
  type: 'markdown' | 'pdf' | 'word' | 'text';
  tags: string[];
  lastUpdated: string;
  size: string;
  author: string;
  version: string;
  status: 'published' | 'archived';
  accessLevel: 'public' | 'internal' | 'restricted' | 'confidential';
  isLocal: boolean;
  createdBy: 'augment' | 'user';
  syncStatus: 'synced' | 'pending' | 'error';
  versions: DocumentVersion[];
  content?: string;
}

interface DocumentVersion {
  version: string;
  date: string;
  author: string;
  changes: string;
  filePath: string;
  size: string;
}

interface SyncStatus {
  lastSync: string;
  status: 'success' | 'error' | 'pending';
  totalDocs: number;
  syncedDocs: number;
  errorDocs: number;
}

// Cloudflare Workers环境下使用模拟数据

/**
 * 获取文档列表
 */
export const getDocuments = async (c: Context<{ Bindings: Env }>) => {
  try {
    console.log('📚 获取文档列表...');

    // Cloudflare Workers环境下始终使用模拟数据
    console.log('🎭 使用模拟文档数据');
    return c.json({
      success: true,
      data: getMockDocuments(),
      syncStatus: getMockSyncStatus()
    });
  } catch (error) {
    console.error('❌ 获取文档列表失败:', error);

    // 回退到模拟数据
    return c.json({
      success: true,
      data: getMockDocuments(),
      syncStatus: getMockSyncStatus(),
      fallback: true
    });
  }
};

/**
 * 获取单个文档详情
 */
export const getDocument = async (c: Context<{ Bindings: Env }>) => {
  try {
    const documentId = c.req.param('id');
    console.log(`📄 获取文档详情: ${documentId}`);

    // Cloudflare Workers环境下使用模拟数据
    const mockDoc = getMockDocuments().find(d => d.id === documentId);
    if (!mockDoc) {
      return c.json({ success: false, error: '文档不存在' }, 404);
    }
    return c.json({ success: true, data: mockDoc });
  } catch (error) {
    console.error('❌ 获取文档详情失败:', error);
    return c.json({ success: false, error: error.message }, 500);
  }
};

/**
 * 同步文档
 */
export const syncDocuments = async (c: Context<{ Bindings: Env }>) => {
  try {
    console.log('🔄 开始同步文档...');

    // Cloudflare Workers环境下使用模拟数据
    const documents = getMockDocuments();
    const syncStatus = getMockSyncStatus();

    console.log(`✅ 同步完成，共 ${documents.length} 个文档`);

    return c.json({
      success: true,
      message: '文档同步完成',
      syncStatus,
      documentsCount: documents.length
    });
  } catch (error) {
    console.error('❌ 文档同步失败:', error);
    return c.json({ success: false, error: error.message }, 500);
  }
};

// 已移除文件系统相关函数，使用模拟数据

// 文件系统相关函数已移除，Cloudflare Workers环境使用模拟数据

/**
 * 获取模拟文档数据
 */
function getMockDocuments(): ProjectDocument[] {
  return [
    {
      id: '1',
      title: '项目文档管理规则',
      description: '定义项目文档管理的规则、流程和标准',
      filePath: '/docs/documentation-management-rules.md',
      category: '项目概述',
      type: 'markdown',
      tags: ['文档', '规则', '流程'],
      lastUpdated: '2024-05-23',
      size: '12.8 KB',
      author: '项目团队',
      version: '1.0.0',
      status: 'published',
      accessLevel: 'internal',
      isLocal: true,
      createdBy: 'augment',
      syncStatus: 'synced',
      versions: [{
        version: '1.0.0',
        date: '2024-05-23',
        author: '项目团队',
        changes: '初始版本',
        filePath: '/docs/documentation-management-rules.md',
        size: '12.8 KB'
      }]
    }
    // 可以添加更多模拟数据...
  ];
}

/**
 * 获取模拟同步状态
 */
function getMockSyncStatus(): SyncStatus {
  return {
    lastSync: new Date().toISOString().slice(0, 19).replace('T', ' '),
    status: 'success',
    totalDocs: 7,
    syncedDocs: 7,
    errorDocs: 0
  };
}
