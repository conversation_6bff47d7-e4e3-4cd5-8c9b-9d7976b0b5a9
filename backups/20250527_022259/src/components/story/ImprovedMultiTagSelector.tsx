import { useState, useEffect, useRef } from 'react';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Ta<PERSON>, TabsContent, Ta<PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import { ScrollArea } from '@/components/ui/scroll-area';
import { 
  X, 
  Check, 
  ChevronDown, 
  ChevronUp, 
  Search, 
  Tag as TagIcon,
  Plus,
  Sparkles
} from 'lucide-react';
import {
  Collapsible,
  CollapsibleContent,
  CollapsibleTrigger,
} from '@/components/ui/collapsible.tsx';
import { cn } from '@/lib/utils';
import { getTagRecommendations } from '@/lib/api';

interface Tag {
  id: string;
  label: string;
  category?: string;
  color?: string;
}

interface TagCategory {
  id: string;
  label: string;
  tags: Tag[];
  color?: string;
}

interface ImprovedMultiTagSelectorProps {
  tags: Tag[];
  selectedTags: string[];
  onTagsChange: (tags: string[]) => void;
  maxTags?: number;
  className?: string;
  content?: string; // 用于推荐标签的内容
  showRecommendations?: boolean;
}

export default function ImprovedMultiTagSelector({
  tags,
  selectedTags,
  onTagsChange,
  maxTags = 5,
  className,
  content = '',
  showRecommendations = false,
}: ImprovedMultiTagSelectorProps) {
  const [isOpen, setIsOpen] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');
  const [activeCategory, setActiveCategory] = useState('all');
  const [recommendedTags, setRecommendedTags] = useState<string[]>([]);
  const [isLoadingRecommendations, setIsLoadingRecommendations] = useState(false);
  const searchInputRef = useRef<HTMLInputElement>(null);
  
  // 将标签按类别分组
  const categorizedTags: TagCategory[] = [];
  const categoryColors: Record<string, string> = {
    'job': 'blue',
    'education': 'green',
    'industry': 'purple',
    'experience': 'yellow',
    'other': 'gray'
  };

  // 创建"全部"类别
  categorizedTags.push({
    id: 'all',
    label: '全部标签',
    tags: tags,
    color: 'gray'
  });

  // 按类别分组其他标签
  const categories = [...new Set(tags.filter(tag => tag.category).map(tag => tag.category))];
  categories.forEach(category => {
    if (category) {
      categorizedTags.push({
        id: category,
        label: getCategoryLabel(category),
        tags: tags.filter(tag => tag.category === category),
        color: categoryColors[category as string] || 'gray'
      });
    }
  });

  // 获取类别显示名称
  function getCategoryLabel(categoryId: string): string {
    const categoryLabels: Record<string, string> = {
      'job': '求职相关',
      'education': '学历相关',
      'industry': '行业相关',
      'experience': '经验相关',
      'other': '其他标签'
    };
    return categoryLabels[categoryId] || categoryId;
  }

  // 切换标签选择状态
  const toggleTag = (tagId: string) => {
    if (selectedTags.includes(tagId)) {
      // 移除标签
      onTagsChange(selectedTags.filter(id => id !== tagId));
    } else {
      // 添加标签，但要检查是否超过最大数量
      if (selectedTags.length < maxTags) {
        onTagsChange([...selectedTags, tagId]);
      }
    }
  };

  // 清除所有选中的标签
  const clearTags = () => {
    onTagsChange([]);
  };
  
  // 获取标签推荐
  const fetchTagRecommendations = async () => {
    if (!content || !showRecommendations) return;
    
    try {
      setIsLoadingRecommendations(true);
      const response = await getTagRecommendations(content);
      
      if (response.success) {
        setRecommendedTags(response.tags);
      }
    } catch (error) {
      console.error('获取标签推荐失败:', error);
    } finally {
      setIsLoadingRecommendations(false);
    }
  };
  
  // 当内容变化时获取标签推荐
  useEffect(() => {
    if (content && showRecommendations) {
      fetchTagRecommendations();
    }
  }, [content, showRecommendations]);
  
  // 当打开选择器时，聚焦搜索框
  useEffect(() => {
    if (isOpen && searchInputRef.current) {
      setTimeout(() => {
        searchInputRef.current?.focus();
      }, 100);
    }
  }, [isOpen]);
  
  // 筛选标签
  const getFilteredTags = (categoryId: string) => {
    let filteredTags = categoryId === 'all' 
      ? tags 
      : tags.filter(tag => tag.category === categoryId);
    
    if (searchQuery) {
      const query = searchQuery.toLowerCase();
      filteredTags = filteredTags.filter(tag => 
        tag.label.toLowerCase().includes(query) || 
        tag.id.toLowerCase().includes(query)
      );
    }
    
    return filteredTags;
  };
  
  // 渲染标签颜色
  const getTagColor = (tag: Tag) => {
    if (tag.color) return tag.color;
    if (tag.category) return categoryColors[tag.category] || 'gray';
    return 'gray';
  };
  
  // 渲染标签徽章
  const renderTagBadge = (tag: Tag, isSelected: boolean = false) => {
    const color = getTagColor(tag);
    
    return (
      <Badge
        key={tag.id}
        variant={isSelected ? "default" : "outline"}
        className={cn(
          "cursor-pointer flex items-center gap-1 transition-all",
          {
            "bg-blue-100 text-blue-800 hover:bg-blue-200": color === 'blue' && !isSelected,
            "bg-green-100 text-green-800 hover:bg-green-200": color === 'green' && !isSelected,
            "bg-purple-100 text-purple-800 hover:bg-purple-200": color === 'purple' && !isSelected,
            "bg-yellow-100 text-yellow-800 hover:bg-yellow-200": color === 'yellow' && !isSelected,
            "bg-red-100 text-red-800 hover:bg-red-200": color === 'red' && !isSelected,
            "bg-pink-100 text-pink-800 hover:bg-pink-200": color === 'pink' && !isSelected,
            "bg-indigo-100 text-indigo-800 hover:bg-indigo-200": color === 'indigo' && !isSelected,
            "bg-gray-100 text-gray-800 hover:bg-gray-200": color === 'gray' && !isSelected,
          }
        )}
        onClick={() => toggleTag(tag.id)}
      >
        {tag.label}
        {isSelected ? (
          <X className="h-3 w-3" />
        ) : (
          <Plus className="h-3 w-3" />
        )}
      </Badge>
    );
  };

  return (
    <div className={cn("w-full", className)}>
      <div className="flex flex-wrap gap-2 mb-2">
        {selectedTags.length > 0 ? (
          <>
            {selectedTags.map(tagId => {
              const tag = tags.find(t => t.id === tagId);
              if (!tag) return null;
              return renderTagBadge(tag, true);
            })}
            
            <Button
              variant="ghost"
              size="sm"
              className="h-6 px-2 text-xs"
              onClick={clearTags}
            >
              清除全部
            </Button>
          </>
        ) : (
          <span className="text-sm text-gray-500">未选择标签</span>
        )}
      </div>

      <Collapsible open={isOpen} onOpenChange={setIsOpen}>
        <CollapsibleTrigger asChild>
          <Button
            variant="outline"
            size="sm"
            className="w-full flex justify-between items-center"
          >
            <span>选择标签 ({selectedTags.length}/{maxTags})</span>
            {isOpen ? (
              <ChevronUp className="h-4 w-4" />
            ) : (
              <ChevronDown className="h-4 w-4" />
            )}
          </Button>
        </CollapsibleTrigger>
        <CollapsibleContent>
          <div className="mt-2 border rounded-md p-4 space-y-4">
            {/* 搜索框 */}
            <div className="relative">
              <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
              <Input
                ref={searchInputRef}
                placeholder="搜索标签..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="pl-8"
              />
            </div>
            
            {/* 标签推荐 */}
            {showRecommendations && recommendedTags.length > 0 && (
              <div className="space-y-2">
                <div className="flex items-center gap-2">
                  <Sparkles className="h-4 w-4 text-amber-500" />
                  <Label className="text-sm font-medium">推荐标签</Label>
                </div>
                <div className="flex flex-wrap gap-2">
                  {recommendedTags.map(tagId => {
                    const tag = tags.find(t => t.id === tagId);
                    if (!tag) return null;
                    return renderTagBadge(tag, selectedTags.includes(tagId));
                  })}
                </div>
              </div>
            )}
            
            {/* 标签分类选项卡 */}
            <Tabs value={activeCategory} onValueChange={setActiveCategory}>
              <TabsList className="w-full flex overflow-x-auto">
                {categorizedTags.map(category => (
                  <TabsTrigger 
                    key={category.id} 
                    value={category.id}
                    className="flex-1"
                  >
                    {category.label}
                  </TabsTrigger>
                ))}
              </TabsList>
              
              {categorizedTags.map(category => (
                <TabsContent key={category.id} value={category.id} className="mt-2">
                  <ScrollArea className="h-[200px] pr-4">
                    <div className="flex flex-wrap gap-2">
                      {getFilteredTags(category.id).length > 0 ? (
                        getFilteredTags(category.id).map(tag => 
                          renderTagBadge(tag, selectedTags.includes(tag.id))
                        )
                      ) : (
                        <div className="w-full flex flex-col items-center justify-center py-8 text-muted-foreground">
                          <TagIcon className="h-8 w-8 mb-2" />
                          <p>未找到匹配的标签</p>
                        </div>
                      )}
                    </div>
                  </ScrollArea>
                </TabsContent>
              ))}
            </Tabs>

            {selectedTags.length >= maxTags && (
              <p className="text-xs text-amber-600">
                已达到最大标签数量 ({maxTags})
              </p>
            )}
          </div>
        </CollapsibleContent>
      </Collapsible>
    </div>
  );
}
