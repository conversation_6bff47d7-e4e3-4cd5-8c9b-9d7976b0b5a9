<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>大学生就业现状调研与匿名社交展示平台 - 角色功能验收测试报告</title>
  <style>
    body { font-family: Arial, sans-serif; line-height: 1.6; max-width: 1200px; margin: 0 auto; padding: 20px; }
    h1 { color: #333; text-align: center; }
    .summary { display: flex; justify-content: space-around; margin: 20px 0; background: #f5f5f5; padding: 15px; border-radius: 5px; }
    .summary-item { text-align: center; }
    .summary-item .count { font-size: 24px; font-weight: bold; }
    .test-item { margin-bottom: 30px; border: 1px solid #ddd; border-radius: 5px; padding: 15px; }
    .test-item h3 { margin-top: 0; display: flex; align-items: center; }
    .test-item.passed { border-left: 5px solid #4CAF50; }
    .test-item.failed { border-left: 5px solid #F44336; }
    .test-item.skipped { border-left: 5px solid #FF9800; }
    .status-badge { display: inline-block; padding: 3px 8px; border-radius: 3px; color: white; margin-left: 10px; font-size: 14px; }
    .status-badge.passed { background-color: #4CAF50; }
    .status-badge.failed { background-color: #F44336; }
    .status-badge.skipped { background-color: #FF9800; }
    .screenshot { max-width: 100%; border: 1px solid #ddd; margin-top: 15px; }
  </style>
</head>
<body>
  <h1>大学生就业现状调研与匿名社交展示平台 - 角色功能验收测试报告</h1>

  <div class="summary">
    <div class="summary-item">
      <div class="count">8</div>
      <div>总测试数</div>
    </div>
    <div class="summary-item">
      <div class="count" style="color: #4CAF50">7</div>
      <div>通过</div>
    </div>
    <div class="summary-item">
      <div class="count" style="color: #F44336">1</div>
      <div>失败</div>
    </div>
    <div class="summary-item">
      <div class="count" style="color: #FF9800">0</div>
      <div>跳过</div>
    </div>
  </div>

  <h2>测试详情</h2>

  <div class="test-list">
    <div class="test-item passed">
      <h3>1. admin 登录页面加载 <span class="status-badge passed">通过</span></h3>
      
      <img src="admin-login.png" alt="admin 登录页面加载" class="screenshot">
    </div>
    <div class="test-item passed">
      <h3>2. admin 登录成功 <span class="status-badge passed">通过</span></h3>
      <p>成功访问 admin 仪表盘</p>
      <img src="admin-dashboard.png" alt="admin 登录成功" class="screenshot">
    </div>
    <div class="test-item passed">
      <h3>3. admin 仪表盘加载 <span class="status-badge passed">通过</span></h3>
      <p>仪表盘页面加载正常，没有显示错误信息</p>
    </div>
    <div class="test-item passed">
      <h3>4. reviewer 登录页面加载 <span class="status-badge passed">通过</span></h3>
      
      <img src="reviewer-login.png" alt="reviewer 登录页面加载" class="screenshot">
    </div>
    <div class="test-item passed">
      <h3>5. reviewer 登录成功 <span class="status-badge passed">通过</span></h3>
      <p>成功访问 reviewer 仪表盘</p>
      <img src="reviewer-dashboard.png" alt="reviewer 登录成功" class="screenshot">
    </div>
    <div class="test-item passed">
      <h3>6. reviewer 仪表盘加载 <span class="status-badge passed">通过</span></h3>
      <p>仪表盘页面加载正常，没有显示错误信息</p>
    </div>
    <div class="test-item passed">
      <h3>7. superadmin 登录页面加载 <span class="status-badge passed">通过</span></h3>
      
      <img src="superadmin-login.png" alt="superadmin 登录页面加载" class="screenshot">
    </div>
    <div class="test-item failed">
      <h3>8. superadmin 登录成功 <span class="status-badge failed">失败</span></h3>
      <p>未能访问 superadmin 仪表盘，当前URL: http://localhost:5173/admin/login</p>
    </div>
  </div>
</body>
</html>