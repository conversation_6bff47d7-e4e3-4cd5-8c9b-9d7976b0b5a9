-- 创建新的表结构 (v2版本)
-- 这些表结构与本地数据库保持一致

-- 创建问卷回复表 v2
CREATE TABLE IF NOT EXISTS questionnaire_responses_v2 (
    id TEXT PRIMARY KEY,
    user_id TEXT,
    session_id TEXT,
    education_level TEXT,
    education_level_display TEXT,
    major_category TEXT,
    major_display TEXT,
    graduation_year INTEGER,
    region_code TEXT,
    region_display TEXT,
    employment_status TEXT,
    current_industry_code TEXT,
    current_industry_display TEXT,
    salary_range TEXT,
    advice_content TEXT,
    observation_content TEXT,
    status TEXT DEFAULT 'submitted' CHECK (status IN ('submitted', 'processed', 'archived')),
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
);

-- 创建问卷心声表 v2
CREATE TABLE IF NOT EXISTS questionnaire_voices_v2 (
    id TEXT PRIMARY KEY,
    source_response_id TEXT,
    user_id TEXT,
    voice_type TEXT CHECK (voice_type IN ('advice', 'observation')),
    title TEXT,
    content TEXT,
    education_level TEXT,
    education_level_display TEXT,
    region_code TEXT,
    region_display TEXT,
    status TEXT DEFAULT 'pending' CHECK (status IN ('pending', 'approved', 'rejected', 'archived')),
    likes INTEGER DEFAULT 0,
    views INTEGER DEFAULT 0,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (source_response_id) REFERENCES questionnaire_responses_v2(id)
);

-- 创建故事内容表 v2
CREATE TABLE IF NOT EXISTS story_contents_v2 (
    id TEXT PRIMARY KEY,
    user_id TEXT,
    title TEXT NOT NULL,
    content TEXT NOT NULL,
    summary TEXT,
    category TEXT,
    education_level TEXT,
    education_level_display TEXT,
    industry_code TEXT,
    industry_display TEXT,
    story_type TEXT DEFAULT 'experience',
    is_anonymous BOOLEAN DEFAULT 1,
    status TEXT DEFAULT 'pending' CHECK (status IN ('pending', 'approved', 'rejected', 'archived')),
    likes INTEGER DEFAULT 0,
    dislikes INTEGER DEFAULT 0,
    views INTEGER DEFAULT 0,
    quality_score REAL DEFAULT 0.0,
    word_count INTEGER DEFAULT 0,
    reading_time INTEGER DEFAULT 0,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
);

-- 创建用户表 v2 (如果需要)
CREATE TABLE IF NOT EXISTS users_v2 (
    id TEXT PRIMARY KEY,
    identity_a TEXT,
    identity_b TEXT,
    user_type TEXT DEFAULT 'anonymous' CHECK (user_type IN ('anonymous', 'registered', 'reviewer', 'admin', 'superadmin')),
    email TEXT,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(identity_a, identity_b)
);

-- 创建索引以提高查询性能
CREATE INDEX IF NOT EXISTS idx_questionnaire_responses_v2_status ON questionnaire_responses_v2(status);
CREATE INDEX IF NOT EXISTS idx_questionnaire_responses_v2_created_at ON questionnaire_responses_v2(created_at);
CREATE INDEX IF NOT EXISTS idx_questionnaire_responses_v2_education ON questionnaire_responses_v2(education_level_display);
CREATE INDEX IF NOT EXISTS idx_questionnaire_responses_v2_region ON questionnaire_responses_v2(region_display);
CREATE INDEX IF NOT EXISTS idx_questionnaire_responses_v2_employment ON questionnaire_responses_v2(employment_status);

CREATE INDEX IF NOT EXISTS idx_questionnaire_voices_v2_status ON questionnaire_voices_v2(status);
CREATE INDEX IF NOT EXISTS idx_questionnaire_voices_v2_created_at ON questionnaire_voices_v2(created_at);
CREATE INDEX IF NOT EXISTS idx_questionnaire_voices_v2_type ON questionnaire_voices_v2(voice_type);

CREATE INDEX IF NOT EXISTS idx_story_contents_v2_status ON story_contents_v2(status);
CREATE INDEX IF NOT EXISTS idx_story_contents_v2_created_at ON story_contents_v2(created_at);
CREATE INDEX IF NOT EXISTS idx_story_contents_v2_category ON story_contents_v2(category);
