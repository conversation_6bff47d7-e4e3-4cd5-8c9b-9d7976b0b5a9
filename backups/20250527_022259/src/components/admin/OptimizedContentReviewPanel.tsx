import React, { useState, useEffect, useCallback, useMemo } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { Skeleton } from '@/components/ui/skeleton';
import VirtualScroll, { VirtualScrollItem } from '@/components/ui/virtual-scroll';
import { useToast } from '@/components/ui/use-toast';
import {
  Search,
  Filter,
  RefreshCw,
  CheckCircle,
  X,
  Clock,
  AlertTriangle,
  Eye,
  MoreHorizontal
} from 'lucide-react';
import {
  getContentsPending,
  approveContentNew,
  rejectContentNew,
  type PendingContent,
  type PaginationParams
} from '@/services/contentManagementService';
import { contentCache, cacheUtils } from '@/utils/cache';
import { debounce, chunkProcessor } from '@/utils/bigDataOptimization';
import { performanceMonitor } from '@/utils/performance';
import { measureComponentRender } from '@/utils/performance';

interface OptimizedContentReviewPanelProps {
  onSelectionChange?: (selectedItems: PendingContent[]) => void;
  selectedItems?: PendingContent[];
  className?: string;
}

/**
 * 优化版内容审核面板
 *
 * 性能优化特性：
 * - 虚拟滚动支持大数据量
 * - 智能缓存减少API调用
 * - 分片处理批量操作
 * - 防抖搜索优化
 * - 内存使用监控
 */
const OptimizedContentReviewPanel: React.FC<OptimizedContentReviewPanelProps> = ({
  onSelectionChange,
  selectedItems = [],
  className = ''
}) => {
  const { toast } = useToast();

  // 状态管理
  const [contents, setContents] = useState<PendingContent[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [hasNextPage, setHasNextPage] = useState(true);
  const [isLoadingMore, setIsLoadingMore] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');
  const [filters, setFilters] = useState<any>({});
  const [currentPage, setCurrentPage] = useState(1);
  const [error, setError] = useState<string | null>(null);

  // 性能监控
  const [performanceStats, setPerformanceStats] = useState({
    renderTime: 0,
    cacheHitRate: 0,
    memoryUsage: 0
  });

  // 分页配置
  const pageSize = 50;
  const itemHeight = 120;
  const containerHeight = 600;

  // 防抖搜索
  const debouncedSearch = useMemo(
    () => debounce((query: string) => {
      setCurrentPage(1);
      setContents([]);
      loadContents(1, { search: query });
    }, 300),
    []
  );

  // 生成缓存键
  const generateCacheKey = useCallback((page: number, params: any = {}) => {
    return cacheUtils.generateKey('contents-pending', {
      page,
      pageSize,
      ...params,
      ...filters
    });
  }, [filters]);

  // 加载内容数据
  const loadContents = useCallback(async (
    page: number = 1,
    additionalParams: any = {},
    append: boolean = false
  ) => {
    const startTime = performance.now();

    try {
      setIsLoading(!append);
      setError(null);

      const params: PaginationParams = {
        page,
        pageSize,
        ...additionalParams,
        ...filters
      };

      const cacheKey = generateCacheKey(page, additionalParams);

      // 尝试从缓存获取
      let response = contentCache.get(cacheKey);
      let fromCache = true;

      if (!response) {
        fromCache = false;
        response = await getContentsPending(params);

        if (response.success) {
          // 缓存成功响应
          contentCache.set(cacheKey, response, {
            ttl: 2 * 60 * 1000, // 2分钟缓存
            tags: ['contents', 'pending']
          });
        }
      }

      if (response.success) {
        const newContents = response.contents || [];

        if (append) {
          setContents(prev => [...prev, ...newContents]);
        } else {
          setContents(newContents);
        }

        setHasNextPage(
          response.pagination ?
          response.pagination.page < response.pagination.totalPages :
          false
        );

        // 更新性能统计
        const renderTime = performance.now() - startTime;
        setPerformanceStats(prev => ({
          ...prev,
          renderTime,
          cacheHitRate: fromCache ? 1 : 0
        }));

        performanceMonitor.recordMetric('content-load', renderTime);
      } else {
        throw new Error(response.error || '加载内容失败');
      }
    } catch (error) {
      console.error('加载内容失败:', error);
      setError(error instanceof Error ? error.message : '加载内容失败');
      toast({
        title: '加载失败',
        description: error instanceof Error ? error.message : '加载内容时发生错误',
        variant: 'destructive',
      });
    } finally {
      setIsLoading(false);
      setIsLoadingMore(false);
    }
  }, [filters, generateCacheKey, toast]);

  // 加载更多数据
  const loadNextPage = useCallback(async () => {
    if (!hasNextPage || isLoadingMore) return;

    setIsLoadingMore(true);
    const nextPage = currentPage + 1;
    setCurrentPage(nextPage);

    await loadContents(nextPage, { search: searchQuery }, true);
  }, [hasNextPage, isLoadingMore, currentPage, loadContents, searchQuery]);

  // 处理搜索
  const handleSearch = useCallback((query: string) => {
    setSearchQuery(query);
    debouncedSearch(query);
  }, [debouncedSearch]);

  // 批量审核操作
  const handleBatchApprove = useCallback(async (items: PendingContent[]) => {
    if (items.length === 0) return;

    try {
      setIsLoading(true);

      // 使用分片处理大批量操作
      await chunkProcessor.process(
        items,
        async (chunk) => {
          const promises = chunk.map(item =>
            approveContentNew(item.id, { reviewNotes: '批量通过' })
          );
          return Promise.all(promises);
        },
        {
          chunkSize: 10,
          delay: 100,
          onProgress: (processed, total) => {
            console.log(`批量审核进度: ${processed}/${total}`);
          }
        }
      );

      // 清除相关缓存
      cacheUtils.updateStrategy.invalidate('contents-pending');

      // 重新加载数据
      await loadContents(1);

      toast({
        title: '批量操作成功',
        description: `已批准 ${items.length} 个内容`,
      });
    } catch (error) {
      console.error('批量审核失败:', error);
      toast({
        title: '批量操作失败',
        description: error instanceof Error ? error.message : '批量审核时发生错误',
        variant: 'destructive',
      });
    } finally {
      setIsLoading(false);
    }
  }, [loadContents, toast]);

  // 单个审核操作
  const handleSingleApprove = useCallback(async (item: PendingContent) => {
    try {
      const response = await approveContentNew(item.id, { reviewNotes: '审核通过' });

      if (response.success) {
        // 从列表中移除已审核项目
        setContents(prev => prev.filter(content => content.id !== item.id));

        // 清除相关缓存
        cacheUtils.updateStrategy.invalidate('contents-pending');

        toast({
          title: '审核成功',
          description: '内容已通过审核',
        });
      } else {
        throw new Error(response.error || '审核失败');
      }
    } catch (error) {
      console.error('审核失败:', error);
      toast({
        title: '审核失败',
        description: error instanceof Error ? error.message : '审核时发生错误',
        variant: 'destructive',
      });
    }
  }, [toast]);

  const handleSingleReject = useCallback(async (item: PendingContent) => {
    try {
      const response = await rejectContentNew(item.id, {
        reason: '不符合规范',
        reviewNotes: '审核拒绝'
      });

      if (response.success) {
        // 从列表中移除已审核项目
        setContents(prev => prev.filter(content => content.id !== item.id));

        // 清除相关缓存
        cacheUtils.updateStrategy.invalidate('contents-pending');

        toast({
          title: '审核成功',
          description: '内容已被拒绝',
        });
      } else {
        throw new Error(response.error || '审核失败');
      }
    } catch (error) {
      console.error('审核失败:', error);
      toast({
        title: '审核失败',
        description: error instanceof Error ? error.message : '审核时发生错误',
        variant: 'destructive',
      });
    }
  }, [toast]);

  // 渲染单个内容项
  const renderContentItem = useCallback(({ item, index, style }: {
    item: VirtualScrollItem;
    index: number;
    style: React.CSSProperties;
  }) => {
    const content = item.data as PendingContent;
    const isSelected = selectedItems.some(selected => selected.id === content.id);

    return (
      <div style={style} className="p-2">
        <Card className={`transition-all duration-200 hover:shadow-md ${isSelected ? 'ring-2 ring-blue-500' : ''}`}>
          <CardContent className="p-4">
            <div className="flex items-start justify-between">
              <div className="flex-1 min-w-0">
                <div className="flex items-center gap-2 mb-2">
                  <Badge variant="outline">{content.type}</Badge>
                  <Badge variant={
                    content.status === 'pending' ? 'secondary' :
                    content.status === 'approved' ? 'default' :
                    'destructive'
                  }>
                    {content.status}
                  </Badge>
                  {content.priority && content.priority > 3 && (
                    <Badge variant="destructive">
                      <AlertTriangle className="h-3 w-3 mr-1" />
                      高优先级
                    </Badge>
                  )}
                </div>

                <p className="text-sm text-gray-600 line-clamp-2 mb-2">
                  {typeof content.originalContent === 'string'
                    ? content.originalContent
                    : content.sanitizedContent || '无内容'}
                </p>

                <div className="flex items-center gap-4 text-xs text-gray-400">
                  <span>创建: {new Date(content.createdAt).toLocaleString()}</span>
                  {content.aiConfidence && (
                    <span>AI置信度: {content.aiConfidence}%</span>
                  )}
                </div>
              </div>

              <div className="flex items-center gap-2 ml-4">
                <input
                  type="checkbox"
                  checked={isSelected}
                  onChange={(e) => {
                    if (e.target.checked) {
                      onSelectionChange?.([...selectedItems, content]);
                    } else {
                      onSelectionChange?.(selectedItems.filter(item => item.id !== content.id));
                    }
                  }}
                  className="rounded"
                />

                <Button
                  size="sm"
                  variant="outline"
                  onClick={() => handleSingleApprove(content)}
                  className="text-green-600 hover:text-green-700"
                >
                  <CheckCircle className="h-4 w-4" />
                </Button>

                <Button
                  size="sm"
                  variant="outline"
                  onClick={() => handleSingleReject(content)}
                  className="text-red-600 hover:text-red-700"
                >
                  <X className="h-4 w-4" />
                </Button>

                <Button size="sm" variant="ghost">
                  <Eye className="h-4 w-4" />
                </Button>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }, [selectedItems, onSelectionChange, handleSingleApprove, handleSingleReject]);

  // 转换数据为虚拟滚动格式
  const virtualScrollItems: VirtualScrollItem[] = useMemo(() => {
    return contents.map((content, index) => ({
      id: content.id,
      height: itemHeight,
      data: content
    }));
  }, [contents]);

  // 初始加载
  useEffect(() => {
    loadContents(1);
  }, []);

  // 监控内存使用
  useEffect(() => {
    const interval = setInterval(() => {
      if ('memory' in performance) {
        const memory = (performance as any).memory;
        setPerformanceStats(prev => ({
          ...prev,
          memoryUsage: memory.usedJSHeapSize / 1024 / 1024 // MB
        }));
      }
    }, 5000);

    return () => clearInterval(interval);
  }, []);

  return (
    <div className={`space-y-4 ${className}`}>
      {/* 控制面板 */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <div>
              <CardTitle className="flex items-center gap-2">
                <Eye className="h-5 w-5" />
                优化版内容审核
              </CardTitle>
              <CardDescription>
                高性能大数据量内容审核面板
              </CardDescription>
            </div>

            <div className="flex items-center gap-2">
              <Badge variant="outline">
                缓存命中率: {(performanceStats.cacheHitRate * 100).toFixed(1)}%
              </Badge>
              <Badge variant="outline">
                内存: {performanceStats.memoryUsage.toFixed(1)}MB
              </Badge>
              <Button
                variant="outline"
                size="sm"
                onClick={() => loadContents(1)}
                disabled={isLoading}
              >
                <RefreshCw className={`h-4 w-4 mr-2 ${isLoading ? 'animate-spin' : ''}`} />
                刷新
              </Button>
            </div>
          </div>
        </CardHeader>

        <CardContent>
          <div className="flex gap-4">
            <div className="flex-1">
              <Input
                placeholder="搜索内容..."
                value={searchQuery}
                onChange={(e) => handleSearch(e.target.value)}
                className="w-full"
              />
            </div>

            <Button variant="outline">
              <Filter className="h-4 w-4 mr-2" />
              过滤
            </Button>

            {selectedItems.length > 0 && (
              <Button onClick={() => handleBatchApprove(selectedItems)}>
                批量通过 ({selectedItems.length})
              </Button>
            )}
          </div>
        </CardContent>
      </Card>

      {/* 内容列表 */}
      <Card>
        <CardContent className="p-0">
          {error ? (
            <div className="p-8 text-center">
              <AlertTriangle className="h-12 w-12 mx-auto mb-4 text-red-500" />
              <p className="text-red-600 mb-4">{error}</p>
              <Button onClick={() => loadContents(1)}>重试</Button>
            </div>
          ) : (
            <VirtualScroll
              items={virtualScrollItems}
              itemHeight={itemHeight}
              containerHeight={containerHeight}
              hasNextPage={hasNextPage}
              isNextPageLoading={isLoadingMore}
              loadNextPage={loadNextPage}
              renderItem={renderContentItem}
              overscanCount={5}
              threshold={10}
              className="border-0"
              emptyComponent={
                <div className="text-center py-12">
                  <Clock className="h-12 w-12 mx-auto mb-4 text-gray-400" />
                  <p className="text-gray-500">暂无待审核内容</p>
                </div>
              }
              loadingComponent={
                <Card className="p-4 m-2">
                  <Skeleton className="h-4 w-3/4 mb-2" />
                  <Skeleton className="h-4 w-1/2 mb-2" />
                  <Skeleton className="h-4 w-2/3" />
                </Card>
              }
            />
          )}
        </CardContent>
      </Card>

      {/* 性能统计 */}
      <Card>
        <CardContent className="p-4">
          <div className="flex items-center justify-between text-sm text-gray-500">
            <span>已加载 {contents.length} 个项目</span>
            <span>渲染时间: {performanceStats.renderTime.toFixed(2)}ms</span>
            <span>
              {isLoadingMore && (
                <>
                  <RefreshCw className="h-4 w-4 inline mr-1 animate-spin" />
                  加载中...
                </>
              )}
            </span>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default OptimizedContentReviewPanel;
