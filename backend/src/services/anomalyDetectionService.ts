/**
 * 异常操作检测服务
 * 检测和预警异常操作行为
 */

import { Env } from '../types';
import { AuditLogService, AuditAction, AuditSeverity } from './auditLogService';
import { getPrismaClient } from '../utils/prisma';

// 异常类型
export enum AnomalyType {
  UNUSUAL_TIME = 'unusual_time',                 // 异常时间
  UNUSUAL_LOCATION = 'unusual_location',         // 异常位置
  UNUSUAL_VOLUME = 'unusual_volume',             // 异常数量
  UNUSUAL_FREQUENCY = 'unusual_frequency',       // 异常频率
  UNUSUAL_PATTERN = 'unusual_pattern',           // 异常模式
  UNUSUAL_RESOURCE = 'unusual_resource',         // 异常资源
  UNUSUAL_PERMISSION = 'unusual_permission',     // 异常权限
  UNUSUAL_SEQUENCE = 'unusual_sequence',         // 异常序列
  UNUSUAL_FAILURE_RATE = 'unusual_failure_rate', // 异常失败率
  UNUSUAL_USER_AGENT = 'unusual_user_agent'      // 异常用户代理
}

// 异常严重性
export enum AnomalySeverity {
  LOW = 'low',         // 低严重性
  MEDIUM = 'medium',   // 中严重性
  HIGH = 'high',       // 高严重性
  CRITICAL = 'critical' // 极高严重性
}

// 异常检测结果
export interface AnomalyDetectionResult {
  isAnomaly: boolean;
  anomalyType?: AnomalyType;
  severity?: AnomalySeverity;
  confidence: number;
  description?: string;
  details?: any;
}

/**
 * 异常操作检测服务
 */
export class AnomalyDetectionService {
  private static instance: AnomalyDetectionService;
  private auditLogService: AuditLogService;
  
  private constructor() {
    this.auditLogService = AuditLogService.getInstance();
  }
  
  /**
   * 获取异常检测服务实例
   */
  public static getInstance(): AnomalyDetectionService {
    if (!AnomalyDetectionService.instance) {
      AnomalyDetectionService.instance = new AnomalyDetectionService();
    }
    return AnomalyDetectionService.instance;
  }
  
  /**
   * 检测操作频率异常
   * @param userId 用户ID
   * @param action 操作类型
   * @param timeWindowMinutes 时间窗口（分钟）
   * @param thresholdCount 阈值次数
   * @param env 环境变量
   */
  public async detectFrequencyAnomaly(
    userId: string,
    action: AuditAction,
    timeWindowMinutes: number = 5,
    thresholdCount: number = 10,
    env: Env
  ): Promise<AnomalyDetectionResult> {
    try {
      // 获取时间窗口内的操作次数
      const startDate = new Date(Date.now() - timeWindowMinutes * 60 * 1000);
      
      const result = await this.auditLogService.getLogs(
        {
          userId,
          action,
          startDate
        },
        { page: 1, pageSize: 100 },
        env
      );
      
      const operationCount = result.logs.length;
      
      // 检查是否超过阈值
      if (operationCount >= thresholdCount) {
        // 计算置信度
        const confidence = Math.min(1, (operationCount / thresholdCount));
        
        // 确定严重性
        let severity: AnomalySeverity;
        if (operationCount >= thresholdCount * 3) {
          severity = AnomalySeverity.CRITICAL;
        } else if (operationCount >= thresholdCount * 2) {
          severity = AnomalySeverity.HIGH;
        } else if (operationCount >= thresholdCount * 1.5) {
          severity = AnomalySeverity.MEDIUM;
        } else {
          severity = AnomalySeverity.LOW;
        }
        
        return {
          isAnomaly: true,
          anomalyType: AnomalyType.UNUSUAL_FREQUENCY,
          severity,
          confidence,
          description: `用户在 ${timeWindowMinutes} 分钟内执行了 ${operationCount} 次 ${action} 操作，超过阈值 ${thresholdCount}`,
          details: {
            userId,
            action,
            timeWindowMinutes,
            thresholdCount,
            operationCount
          }
        };
      }
      
      return {
        isAnomaly: false,
        confidence: 0
      };
    } catch (error) {
      console.error('Error detecting frequency anomaly:', error);
      return {
        isAnomaly: false,
        confidence: 0
      };
    }
  }
  
  /**
   * 检测操作时间异常
   * @param timestamp 操作时间戳
   * @param userId 用户ID
   * @param env 环境变量
   */
  public async detectTimeAnomaly(
    timestamp: Date,
    userId: string,
    env: Env
  ): Promise<AnomalyDetectionResult> {
    try {
      const hour = timestamp.getHours();
      
      // 检查是否在非工作时间
      const isNonWorkingHour = hour < 7 || hour > 22;
      
      if (!isNonWorkingHour) {
        return {
          isAnomaly: false,
          confidence: 0
        };
      }
      
      // 获取用户过去30天在此时间段的操作历史
      const prisma = getPrismaClient(env);
      
      const thirtyDaysAgo = new Date(Date.now() - 30 * 24 * 60 * 60 * 1000);
      
      // 查询用户在此时间段的历史操作
      const historicalOperations = await prisma.auditLog.count({
        where: {
          userId,
          createdAt: {
            gte: thirtyDaysAgo
          },
          AND: [
            {
              OR: [
                { createdAt: { gte: new Date(new Date().setHours(0, 0, 0, 0)) } },
                { createdAt: { lt: new Date(new Date().setHours(7, 0, 0, 0)) } }
              ]
            },
            {
              OR: [
                { createdAt: { gte: new Date(new Date().setHours(22, 0, 0, 0)) } },
                { createdAt: { lt: new Date(new Date().setHours(23, 59, 59, 999)) } }
              ]
            }
          ]
        }
      });
      
      // 如果用户有在此时间段操作的历史，则不视为异常
      if (historicalOperations > 5) {
        return {
          isAnomaly: false,
          confidence: 0
        };
      }
      
      // 确定严重性
      let severity: AnomalySeverity;
      let confidence: number;
      
      if (hour >= 0 && hour < 4) {
        severity = AnomalySeverity.HIGH;
        confidence = 0.8;
      } else if (hour >= 4 && hour < 6) {
        severity = AnomalySeverity.MEDIUM;
        confidence = 0.6;
      } else {
        severity = AnomalySeverity.LOW;
        confidence = 0.4;
      }
      
      return {
        isAnomaly: true,
        anomalyType: AnomalyType.UNUSUAL_TIME,
        severity,
        confidence,
        description: `用户在非常规工作时间 (${hour}:${timestamp.getMinutes()}) 执行操作`,
        details: {
          userId,
          timestamp,
          hour,
          historicalOperations
        }
      };
    } catch (error) {
      console.error('Error detecting time anomaly:', error);
      return {
        isAnomaly: false,
        confidence: 0
      };
    }
  }
  
  /**
   * 检测操作位置异常
   * @param ipAddress IP地址
   * @param userId 用户ID
   * @param env 环境变量
   */
  public async detectLocationAnomaly(
    ipAddress: string,
    userId: string,
    env: Env
  ): Promise<AnomalyDetectionResult> {
    try {
      // 获取用户过去30天的IP地址历史
      const prisma = getPrismaClient(env);
      
      const thirtyDaysAgo = new Date(Date.now() - 30 * 24 * 60 * 60 * 1000);
      
      // 查询用户的历史IP地址
      const historicalIPs = await prisma.auditLog.groupBy({
        by: ['ipAddress'],
        where: {
          userId,
          createdAt: {
            gte: thirtyDaysAgo
          },
          ipAddress: {
            not: null
          }
        },
        _count: {
          ipAddress: true
        }
      });
      
      // 检查当前IP是否在历史记录中
      const ipHistory = historicalIPs.find(ip => ip.ipAddress === ipAddress);
      
      if (ipHistory && ipHistory._count.ipAddress > 5) {
        return {
          isAnomaly: false,
          confidence: 0
        };
      }
      
      // 如果是新IP地址，则视为异常
      return {
        isAnomaly: true,
        anomalyType: AnomalyType.UNUSUAL_LOCATION,
        severity: AnomalySeverity.MEDIUM,
        confidence: 0.7,
        description: `用户使用新的IP地址 ${ipAddress} 执行操作`,
        details: {
          userId,
          ipAddress,
          historicalIPs
        }
      };
    } catch (error) {
      console.error('Error detecting location anomaly:', error);
      return {
        isAnomaly: false,
        confidence: 0
      };
    }
  }
  
  /**
   * 记录异常操作
   * @param userId 用户ID
   * @param action 操作类型
   * @param resourceType 资源类型
   * @param resourceId 资源ID
   * @param anomaly 异常检测结果
   * @param clientInfo 客户端信息
   * @param env 环境变量
   */
  public async logAnomaly(
    userId: string,
    action: AuditAction,
    resourceType: string,
    resourceId: string | undefined,
    anomaly: AnomalyDetectionResult,
    clientInfo: {
      ipAddress: string;
      userAgent: string;
    },
    env: Env
  ): Promise<void> {
    try {
      // 确定审计严重性
      let severity: AuditSeverity;
      switch (anomaly.severity) {
        case AnomalySeverity.CRITICAL:
          severity = AuditSeverity.CRITICAL;
          break;
        case AnomalySeverity.HIGH:
          severity = AuditSeverity.ERROR;
          break;
        case AnomalySeverity.MEDIUM:
          severity = AuditSeverity.WARNING;
          break;
        default:
          severity = AuditSeverity.INFO;
          break;
      }
      
      // 记录审计日志
      await this.auditLogService.log({
        userId,
        action: AuditAction.SECURITY_ALERT,
        resourceType,
        resourceId,
        details: {
          originalAction: action,
          anomaly
        },
        ipAddress: clientInfo.ipAddress,
        userAgent: clientInfo.userAgent,
        severity
      }, env);
    } catch (error) {
      console.error('Error logging anomaly:', error);
      // 忽略错误，继续处理
    }
  }
}
