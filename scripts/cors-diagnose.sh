#!/bin/bash
# cors-diagnose.sh - CORS问题自动诊断工具
# 用法: ./cors-diagnose.sh <API_BASE_URL> <FRONTEND_DOMAIN>

API_BASE="$1"
FRONTEND_DOMAIN="$2"

if [ -z "$API_BASE" ] || [ -z "$FRONTEND_DOMAIN" ]; then
  echo "用法: $0 <API_BASE_URL> <FRONTEND_DOMAIN>"
  echo "示例: $0 https://college-employment-survey.aibook2099.workers.dev https://a9017891.college-employment-survey.pages.dev"
  exit 1
fi

echo "🔍 CORS诊断开始..."
echo "API: $API_BASE"
echo "前端: $FRONTEND_DOMAIN"
echo ""

# 1. 测试OPTIONS预检请求
echo "1️⃣ 测试OPTIONS预检请求..."
OPTIONS_RESPONSE=$(curl -s -X OPTIONS \
  -H "Origin: $FRONTEND_DOMAIN" \
  -H "Access-Control-Request-Method: GET" \
  -H "Access-Control-Request-Headers: Content-Type" \
  "$API_BASE/api/system/monitor" \
  -w "HTTPSTATUS:%{http_code}" \
  -D /tmp/cors_headers.txt)

STATUS=$(echo $OPTIONS_RESPONSE | grep -o "HTTPSTATUS:[0-9]*" | cut -d: -f2)
ALLOW_ORIGIN=$(grep -i "access-control-allow-origin" /tmp/cors_headers.txt | cut -d: -f2- | tr -d '\r\n ')

echo "   状态码: $STATUS"
echo "   Allow-Origin: '$ALLOW_ORIGIN'"

if [ "$STATUS" = "204" ] || [ "$STATUS" = "200" ]; then
  if [ "$ALLOW_ORIGIN" = "$FRONTEND_DOMAIN" ]; then
    echo "   ✅ OPTIONS预检正常"
  elif [ "$ALLOW_ORIGIN" = "true" ]; then
    echo "   ❌ CORS配置错误: 返回了布尔值而不是域名"
    echo "   🔧 修复建议: 修改origin函数返回具体域名"
  else
    echo "   ❌ CORS配置错误: Origin不匹配"
    echo "   🔧 修复建议: 检查域名是否在允许列表中"
  fi
else
  echo "   ❌ OPTIONS请求失败"
fi

# 2. 测试实际GET请求
echo ""
echo "2️⃣ 测试实际GET请求..."
GET_RESPONSE=$(curl -s -H "Origin: $FRONTEND_DOMAIN" \
  "$API_BASE/api/system/monitor" \
  -w "HTTPSTATUS:%{http_code}" \
  -D /tmp/get_headers.txt)

GET_STATUS=$(echo $GET_RESPONSE | grep -o "HTTPSTATUS:[0-9]*" | cut -d: -f2)
GET_ALLOW_ORIGIN=$(grep -i "access-control-allow-origin" /tmp/get_headers.txt | cut -d: -f2- | tr -d '\r\n ')

echo "   状态码: $GET_STATUS"
echo "   Allow-Origin: '$GET_ALLOW_ORIGIN'"

if [ "$GET_STATUS" = "200" ]; then
  echo "   ✅ GET请求正常"
else
  echo "   ❌ GET请求失败"
fi

# 3. 测试多个端点
echo ""
echo "3️⃣ 测试多个API端点..."
ENDPOINTS=(
  "/api/questionnaire/stats"
  "/api/questionnaire-voices"
  "/api/visualization/data"
  "/api/story/list"
)

for endpoint in "${ENDPOINTS[@]}"; do
  echo "   测试: $endpoint"
  ENDPOINT_STATUS=$(curl -s -H "Origin: $FRONTEND_DOMAIN" \
    "$API_BASE$endpoint" \
    -w "%{http_code}" \
    -o /dev/null)
  
  if [ "$ENDPOINT_STATUS" = "200" ]; then
    echo "     ✅ 正常 ($ENDPOINT_STATUS)"
  else
    echo "     ❌ 异常 ($ENDPOINT_STATUS)"
  fi
done

# 4. 生成诊断报告
echo ""
echo "📋 诊断报告:"
if [ "$ALLOW_ORIGIN" = "$FRONTEND_DOMAIN" ] && [ "$GET_ALLOW_ORIGIN" = "$FRONTEND_DOMAIN" ]; then
  echo "✅ CORS配置正常，问题可能在其他地方"
elif [ "$ALLOW_ORIGIN" = "true" ] || [ "$GET_ALLOW_ORIGIN" = "true" ]; then
  echo "❌ 发现问题: CORS返回布尔值"
  echo "🔧 修复方法:"
  echo "   1. 修改后端CORS配置"
  echo "   2. 将 'return true' 改为 'return origin'"
  echo "   3. 重新部署后端"
  echo ""
  echo "📝 具体修复代码:"
  echo "   // ❌ 错误"
  echo "   origin: (origin) => allowedDomains.includes(origin) ? true : false"
  echo ""
  echo "   // ✅ 正确"
  echo "   origin: (origin) => allowedDomains.includes(origin) ? origin : false"
else
  echo "❌ 发现问题: 域名不在CORS允许列表中"
  echo "🔧 修复方法:"
  echo "   1. 添加 '$FRONTEND_DOMAIN' 到CORS允许列表"
  echo "   2. 重新部署后端"
fi

# 清理临时文件
rm -f /tmp/cors_headers.txt /tmp/get_headers.txt

echo ""
echo "🎯 建议下一步:"
echo "   1. 如果CORS正常，运行浏览器环境测试"
echo "   2. 如果CORS异常，按照修复建议操作"
echo "   3. 修复后重新运行此脚本验证"
