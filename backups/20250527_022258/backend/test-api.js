const express = require('express');
const cors = require('cors');
const { PrismaClient } = require('@prisma/client');

const app = express();
const prisma = new PrismaClient();
const PORT = 8787;

// 中间件
app.use(cors({
  origin: [
    'http://localhost:5173',
    'http://localhost:5174',
    'http://localhost:5175',
    'http://localhost:5176'
  ],
  credentials: true
}));
app.use(express.json());

// 请求日志
app.use((req, res, next) => {
  console.log(`${new Date().toISOString()} ${req.method} ${req.url}`);
  next();
});

// 健康检查
app.get('/health', (req, res) => {
  console.log('✅ 健康检查请求');
  res.json({ status: 'ok', environment: 'development' });
});

// 管理员登录API
app.post('/api/admin/login', async (req, res) => {
  console.log('🔐 管理员登录请求');
  try {
    const { username, password } = req.body;
    console.log('📝 登录信息:', { username, password: '***' });

    if (!username || !password) {
      return res.status(400).json({
        success: false,
        message: '用户名和密码不能为空'
      });
    }

    // 查找用户
    const user = await prisma.user.findFirst({
      where: {
        OR: [
          { username: username },
          { email: username }
        ]
      }
    });

    if (!user) {
      console.log('❌ 用户不存在:', username);
      return res.status(401).json({
        success: false,
        message: '用户名或密码错误'
      });
    }

    // 简单密码验证（实际应用中应该使用bcrypt）
    if (password !== 'admin123') {
      console.log('❌ 密码错误');
      return res.status(401).json({
        success: false,
        message: '用户名或密码错误'
      });
    }

    // 检查用户角色权限
    if (!['admin', 'superadmin', 'reviewer'].includes(user.role)) {
      console.log('❌ 权限不足:', user.role);
      return res.status(403).json({
        success: false,
        message: '您没有权限访问管理系统'
      });
    }

    console.log('✅ 登录成功:', { id: user.id, username: user.username, role: user.role });

    // 返回用户信息和token
    res.json({
      success: true,
      data: {
        user: {
          id: user.id,
          username: user.username,
          email: user.email,
          name: user.name,
          role: user.role,
          loginTime: new Date().toISOString()
        },
        token: `mock_token_${user.id}_${Date.now()}`,
        expiresIn: 86400 // 24小时
      },
      message: '登录成功'
    });
  } catch (error) {
    console.error('❌ 登录失败:', error);
    res.status(500).json({
      success: false,
      message: '登录失败，请稍后重试',
      error: error.message
    });
  }
});

// 超级管理员仪表盘统计
app.get('/api/admin/dashboard/stats', async (req, res) => {
  console.log('📊 仪表盘统计请求');
  try {
    // 获取今日开始时间
    const today = new Date();
    today.setHours(0, 0, 0, 0);

    const [
      userCount,
      storyCount,
      responseCount,
      pendingStories,
      todayUsers,
      todayStories,
      todayResponses,
      questionnaireVoicesCount,
      todayQuestionnaireVoices
    ] = await Promise.all([
      prisma.user.count(),
      prisma.story.count(),
      prisma.questionnaireResponse.count(),
      prisma.story.count({ where: { status: 'pending' } }),
      prisma.user.count({ where: { createdAt: { gte: today } } }),
      prisma.story.count({ where: { createdAt: { gte: today } } }),
      prisma.questionnaireResponse.count({ where: { createdAt: { gte: today } } }),
      // 问卷心声：有建议或观察内容的问卷
      prisma.questionnaireResponse.count({
        where: {
          OR: [
            { adviceForStudents: { not: null } },
            { observationOnEmployment: { not: null } }
          ]
        }
      }),
      // 今日问卷心声
      prisma.questionnaireResponse.count({
        where: {
          createdAt: { gte: today },
          OR: [
            { adviceForStudents: { not: null } },
            { observationOnEmployment: { not: null } }
          ]
        }
      })
    ]);

    console.log('📈 数据库查询结果:', {
      userCount,
      storyCount,
      responseCount,
      pendingStories,
      todayUsers,
      todayStories,
      todayResponses,
      questionnaireVoicesCount,
      todayQuestionnaireVoices
    });

    const result = {
      success: true,
      data: {
        totalUsers: userCount,
        activeUsers: Math.floor(userCount * 0.8), // 假设80%活跃
        totalStories: storyCount,
        pendingStories: pendingStories,
        totalResponses: responseCount,
        todayUsers: todayUsers,
        todayStories: todayStories,
        todayResponses: todayResponses,
        totalQuestionnaireVoices: questionnaireVoicesCount,
        todayQuestionnaireVoices: todayQuestionnaireVoices,
        securityAlerts: 0,
        testDataStatus: 'loaded'
      }
    };

    console.log('✅ 返回结果:', JSON.stringify(result, null, 2));
    res.json(result);
  } catch (error) {
    console.error('❌ 获取仪表盘统计失败:', error);
    res.status(500).json({
      success: false,
      message: '获取统计数据失败',
      error: error.message
    });
  }
});

// 测试数据统计
app.get('/api/admin/test-data/stats', async (req, res) => {
  console.log('📈 测试数据统计请求');
  try {
    const [userStats, responseCount, storyCount, storyStats] = await Promise.all([
      prisma.user.groupBy({
        by: ['role'],
        _count: { id: true }
      }),
      prisma.questionnaireResponse.count(),
      prisma.story.count(),
      prisma.story.groupBy({
        by: ['status'],
        _count: { id: true }
      })
    ]);

    // 处理用户统计
    const roleMap = {};
    userStats.forEach(stat => {
      roleMap[stat.role] = stat._count.id;
    });

    // 处理故事状态统计
    const statusMap = {};
    storyStats.forEach(stat => {
      statusMap[stat.status] = stat._count.id;
    });

    console.log('📈 数据库查询结果:', { userStats, responseCount, storyCount, storyStats });

    res.json({
      success: true,
      data: {
        users: {
          total: userStats.reduce((sum, stat) => sum + stat._count.id, 0),
          normalUsers: roleMap.user || 0,
          usersWithUuid: roleMap.user || 0,
          reviewers: roleMap.reviewer || 0,
          admins: roleMap.admin || 0,
          superAdmins: roleMap.superadmin || 0
        },
        content: {
          questionnaires: responseCount,
          stories: storyCount,
          comments: 0,
          pendingReview: statusMap.pending || 0,
          approved: statusMap.approved || 0,
          rejected: statusMap.rejected || 0
        },
        system: {
          databaseSize: '356 KB',
          lastBackup: new Date().toISOString(),
          totalFiles: 0
        },
        lastGenerated: new Date().toISOString()
      }
    });
  } catch (error) {
    console.error('❌ 获取测试数据统计失败:', error);
    res.status(500).json({
      success: false,
      message: '获取测试数据统计失败',
      error: error.message
    });
  }
});

// 测试数据状态
app.get('/api/admin/test-data/status', (req, res) => {
  console.log('🔍 测试数据状态请求');
  res.json({
    success: true,
    data: {
      isLoaded: true
    }
  });
});

// 导入测试数据
app.post('/api/admin/test-data/import', (req, res) => {
  console.log('📥 导入测试数据请求');
  res.json({
    success: true,
    data: {
      imported: 100
    }
  });
});

// 清除测试数据
app.delete('/api/admin/test-data/clear', (req, res) => {
  console.log('🗑️ 清除测试数据请求');
  res.json({
    success: true,
    data: {
      deleted: 50
    }
  });
});

// 用户管理API
app.get('/api/admin/users', async (req, res) => {
  console.log('👥 获取用户列表请求');
  try {
    const { page = 1, pageSize = 10, search, role, status } = req.query;

    // 构建查询条件
    const where = {};
    if (search) {
      // SQLite不支持mode: 'insensitive'，使用LIKE进行模糊搜索
      const searchLower = search.toLowerCase();
      where.OR = [
        { username: { contains: search } },
        { email: { contains: search } },
        { name: { contains: search } }
      ];
    }
    if (role) {
      where.role = role;
    }

    const [users, total] = await Promise.all([
      prisma.user.findMany({
        where,
        skip: (parseInt(page) - 1) * parseInt(pageSize),
        take: parseInt(pageSize),
        select: {
          id: true,
          username: true,
          email: true,
          name: true,
          role: true,
          emailVerified: true,
          createdAt: true,
          updatedAt: true
        },
        orderBy: { createdAt: 'desc' }
      }),
      prisma.user.count({ where })
    ]);

    console.log('📈 用户查询结果:', { total, count: users.length });

    res.json({
      success: true,
      data: {
        users: users.map(user => ({
          id: user.id,
          uuid: user.id, // 使用id作为uuid
          username: user.username,
          email: user.email,
          name: user.name,
          role: user.role,
          status: user.emailVerified ? 'active' : 'inactive',
          createdAt: user.createdAt,
          lastLoginAt: user.updatedAt,
          lastLoginIp: '*************',
          submissionCount: 0,
          storyCount: 0,
          questionnaireCount: 0
        })),
        total,
        page: parseInt(page),
        limit: parseInt(pageSize),
        totalPages: Math.ceil(total / parseInt(pageSize))
      }
    });
  } catch (error) {
    console.error('❌ 获取用户列表失败:', error);
    res.status(500).json({
      success: false,
      message: '获取用户列表失败',
      error: error.message
    });
  }
});

// 创建用户API
app.post('/api/admin/users', async (req, res) => {
  console.log('👤 创建用户请求');
  try {
    const { username, email, name, role, password } = req.body;

    // 验证必填字段
    if (!username || !email || !name || !role) {
      return res.status(400).json({
        success: false,
        message: '用户名、邮箱、姓名和角色不能为空'
      });
    }

    // 检查用户名是否已存在
    const existingUser = await prisma.user.findFirst({
      where: {
        OR: [
          { username: username },
          { email: email }
        ]
      }
    });

    if (existingUser) {
      return res.status(400).json({
        success: false,
        message: '用户名或邮箱已存在'
      });
    }

    // 创建用户
    const newUser = await prisma.user.create({
      data: {
        username,
        email,
        name,
        role,
        emailVerified: true, // 管理员创建的用户默认已验证
        createdAt: new Date(),
        updatedAt: new Date()
      }
    });

    console.log('✅ 用户创建成功:', { id: newUser.id, username: newUser.username });

    res.json({
      success: true,
      data: {
        id: newUser.id,
        uuid: newUser.id,
        username: newUser.username,
        email: newUser.email,
        name: newUser.name,
        role: newUser.role,
        status: 'active',
        createdAt: newUser.createdAt
      },
      message: '用户创建成功'
    });
  } catch (error) {
    console.error('❌ 创建用户失败:', error);
    res.status(500).json({
      success: false,
      message: '创建用户失败',
      error: error.message
    });
  }
});

// 更新用户API
app.put('/api/admin/users/:id', async (req, res) => {
  console.log('✏️ 更新用户请求:', req.params.id);
  try {
    const { id } = req.params;
    const { username, email, name, role } = req.body;

    // 检查用户是否存在
    const existingUser = await prisma.user.findUnique({
      where: { id: parseInt(id) }
    });

    if (!existingUser) {
      return res.status(404).json({
        success: false,
        message: '用户不存在'
      });
    }

    // 检查用户名和邮箱是否被其他用户使用
    if (username || email) {
      const conflictUser = await prisma.user.findFirst({
        where: {
          AND: [
            { id: { not: parseInt(id) } },
            {
              OR: [
                username ? { username: username } : {},
                email ? { email: email } : {}
              ].filter(obj => Object.keys(obj).length > 0)
            }
          ]
        }
      });

      if (conflictUser) {
        return res.status(400).json({
          success: false,
          message: '用户名或邮箱已被其他用户使用'
        });
      }
    }

    // 更新用户
    const updatedUser = await prisma.user.update({
      where: { id: parseInt(id) },
      data: {
        ...(username && { username }),
        ...(email && { email }),
        ...(name && { name }),
        ...(role && { role }),
        updatedAt: new Date()
      }
    });

    console.log('✅ 用户更新成功:', { id: updatedUser.id, username: updatedUser.username });

    res.json({
      success: true,
      data: {
        id: updatedUser.id,
        uuid: updatedUser.id,
        username: updatedUser.username,
        email: updatedUser.email,
        name: updatedUser.name,
        role: updatedUser.role,
        status: updatedUser.emailVerified ? 'active' : 'inactive',
        createdAt: updatedUser.createdAt,
        updatedAt: updatedUser.updatedAt
      },
      message: '用户更新成功'
    });
  } catch (error) {
    console.error('❌ 更新用户失败:', error);
    res.status(500).json({
      success: false,
      message: '更新用户失败',
      error: error.message
    });
  }
});

// 删除用户API
app.delete('/api/admin/users/:id', async (req, res) => {
  console.log('🗑️ 删除用户请求:', req.params.id);
  try {
    const { id } = req.params;

    // 检查用户是否存在
    const existingUser = await prisma.user.findUnique({
      where: { id: parseInt(id) }
    });

    if (!existingUser) {
      return res.status(404).json({
        success: false,
        message: '用户不存在'
      });
    }

    // 防止删除超级管理员
    if (existingUser.role === 'superadmin') {
      return res.status(403).json({
        success: false,
        message: '不能删除超级管理员账户'
      });
    }

    // 删除用户
    await prisma.user.delete({
      where: { id: parseInt(id) }
    });

    console.log('✅ 用户删除成功:', { id: parseInt(id), username: existingUser.username });

    res.json({
      success: true,
      message: '用户删除成功'
    });
  } catch (error) {
    console.error('❌ 删除用户失败:', error);
    res.status(500).json({
      success: false,
      message: '删除用户失败',
      error: error.message
    });
  }
});

// 角色管理API
app.get('/api/admin/roles', async (req, res) => {
  console.log('🔐 获取角色列表请求');
  try {
    // 获取每个角色的用户数量
    const userCounts = await prisma.user.groupBy({
      by: ['role'],
      _count: { id: true }
    });

    const roleCountMap = {};
    userCounts.forEach(count => {
      roleCountMap[count.role] = count._count.id;
    });

    // 返回预定义的角色列表
    const roles = [
      {
        id: '1',
        name: 'superadmin',
        description: '超级管理员，拥有所有权限',
        permissions: [
          { id: 'USER_CREATE', name: '创建用户', description: '允许创建新用户' },
          { id: 'USER_READ', name: '查看用户', description: '允许查看用户信息' },
          { id: 'USER_UPDATE', name: '更新用户', description: '允许更新用户信息' },
          { id: 'USER_DELETE', name: '删除用户', description: '允许删除用户' },
          { id: 'ROLE_MANAGE', name: '角色管理', description: '允许管理角色和权限' },
          { id: 'SYSTEM_CONFIG', name: '系统配置', description: '允许修改系统配置' }
        ],
        userCount: roleCountMap.superadmin || 0
      },
      {
        id: '2',
        name: 'admin',
        description: '管理员，拥有大部分管理权限',
        permissions: [
          { id: 'USER_CREATE', name: '创建用户', description: '允许创建新用户' },
          { id: 'USER_READ', name: '查看用户', description: '允许查看用户信息' },
          { id: 'USER_UPDATE', name: '更新用户', description: '允许更新用户信息' },
          { id: 'CONTENT_MODERATE', name: '内容审核', description: '允许审核内容' }
        ],
        userCount: roleCountMap.admin || 0
      },
      {
        id: '3',
        name: 'reviewer',
        description: '审核员，负责内容审核',
        permissions: [
          { id: 'CONTENT_MODERATE', name: '内容审核', description: '允许审核内容' },
          { id: 'USER_READ', name: '查看用户', description: '允许查看用户信息' }
        ],
        userCount: roleCountMap.reviewer || 0
      },
      {
        id: '4',
        name: 'user',
        description: '普通用户',
        permissions: [
          { id: 'CONTENT_CREATE', name: '创建内容', description: '允许创建内容' },
          { id: 'CONTENT_READ', name: '查看内容', description: '允许查看内容' }
        ],
        userCount: roleCountMap.user || 0
      }
    ];

    res.json({
      success: true,
      data: roles
    });
  } catch (error) {
    console.error('❌ 获取角色列表失败:', error);
    res.status(500).json({
      success: false,
      message: '获取角色列表失败',
      error: error.message
    });
  }
});

// 安全监控API
app.get('/api/admin/security/events', async (req, res) => {
  console.log('🔒 获取安全事件请求');
  try {
    const { page = 1, limit = 10, severity, type } = req.query;

    // 模拟安全事件数据
    const mockEvents = [
      {
        id: '1',
        timestamp: new Date(Date.now() - 5 * 60000).toISOString(),
        type: 'login_failure',
        severity: 'high',
        source: '登录系统',
        ip: '*************',
        userId: 'user_123',
        username: 'testuser',
        details: '连续5次登录失败，账户已被临时锁定',
        status: 'active'
      },
      {
        id: '2',
        timestamp: new Date(Date.now() - 15 * 60000).toISOString(),
        type: 'suspicious_activity',
        severity: 'medium',
        source: 'API网关',
        ip: '*************',
        userId: 'user_456',
        username: 'reviewer1',
        details: '异常API调用频率，可能存在自动化攻击',
        status: 'investigating'
      },
      {
        id: '3',
        timestamp: new Date(Date.now() - 25 * 60000).toISOString(),
        type: 'permission_violation',
        severity: 'critical',
        source: '权限系统',
        ip: '*************',
        userId: 'user_789',
        username: 'admin1',
        details: '尝试访问超出权限范围的资源',
        status: 'resolved'
      },
      {
        id: '4',
        timestamp: new Date(Date.now() - 30 * 60000).toISOString(),
        type: 'data_access',
        severity: 'critical',
        source: '数据库',
        ip: '*************',
        userId: 'system',
        username: 'system',
        details: '异常数据访问模式，可能是数据泄露尝试',
        status: 'investigating'
      },
      {
        id: '5',
        timestamp: new Date(Date.now() - 60 * 60000).toISOString(),
        type: 'system_error',
        severity: 'low',
        source: '应用服务器',
        details: '服务器内存使用率异常高',
        status: 'resolved'
      }
    ];

    // 应用过滤器
    let filteredEvents = mockEvents;
    if (severity) {
      filteredEvents = filteredEvents.filter(event => event.severity === severity);
    }
    if (type) {
      filteredEvents = filteredEvents.filter(event => event.type === type);
    }

    // 分页
    const startIndex = (parseInt(page) - 1) * parseInt(limit);
    const endIndex = startIndex + parseInt(limit);
    const paginatedEvents = filteredEvents.slice(startIndex, endIndex);

    res.json({
      success: true,
      data: {
        events: paginatedEvents,
        total: filteredEvents.length,
        page: parseInt(page),
        limit: parseInt(limit),
        totalPages: Math.ceil(filteredEvents.length / parseInt(limit))
      }
    });
  } catch (error) {
    console.error('❌ 获取安全事件失败:', error);
    res.status(500).json({
      success: false,
      message: '获取安全事件失败',
      error: error.message
    });
  }
});

// 系统资源监控API
app.get('/api/admin/security/resources', async (req, res) => {
  console.log('📊 获取系统资源请求');
  try {
    // 模拟系统资源数据
    const resources = {
      cpu: {
        usage: Math.floor(Math.random() * 30) + 20, // 20-50%
        cores: 8,
        temperature: Math.floor(Math.random() * 20) + 45 // 45-65°C
      },
      memory: {
        usage: Math.floor(Math.random() * 40) + 30, // 30-70%
        total: 16384, // 16GB
        available: Math.floor(Math.random() * 8000) + 4000 // 4-12GB
      },
      disk: {
        usage: Math.floor(Math.random() * 30) + 40, // 40-70%
        total: 512000, // 512GB
        available: Math.floor(Math.random() * 200000) + 150000 // 150-350GB
      },
      network: {
        inbound: Math.floor(Math.random() * 100) + 50, // 50-150 Mbps
        outbound: Math.floor(Math.random() * 80) + 30, // 30-110 Mbps
        connections: Math.floor(Math.random() * 500) + 200 // 200-700 connections
      },
      timestamp: new Date().toISOString()
    };

    res.json({
      success: true,
      data: resources
    });
  } catch (error) {
    console.error('❌ 获取系统资源失败:', error);
    res.status(500).json({
      success: false,
      message: '获取系统资源失败',
      error: error.message
    });
  }
});

// 安全统计API
app.get('/api/admin/security/stats', async (req, res) => {
  console.log('📈 获取安全统计请求');
  try {
    // 模拟安全统计数据
    const stats = {
      securityScore: Math.floor(Math.random() * 20) + 75, // 75-95
      threatLevel: ['low', 'medium', 'high'][Math.floor(Math.random() * 3)],
      activeThreats: Math.floor(Math.random() * 5) + 1, // 1-5
      resolvedThreats: Math.floor(Math.random() * 20) + 10, // 10-30
      todayEvents: Math.floor(Math.random() * 50) + 20, // 20-70
      weeklyEvents: Math.floor(Math.random() * 200) + 100, // 100-300
      monthlyEvents: Math.floor(Math.random() * 800) + 400, // 400-1200
      loginAttempts: Math.floor(Math.random() * 1000) + 500, // 500-1500
      failedLogins: Math.floor(Math.random() * 50) + 10, // 10-60
      activeSessions: Math.floor(Math.random() * 100) + 50 // 50-150
    };

    res.json({
      success: true,
      data: stats
    });
  } catch (error) {
    console.error('❌ 获取安全统计失败:', error);
    res.status(500).json({
      success: false,
      message: '获取安全统计失败',
      error: error.message
    });
  }
});

// 审计日志API
app.get('/api/admin/audit-logs', async (req, res) => {
  console.log('📋 获取审计日志请求');
  try {
    const { page = 1, limit = 10, username, role, action, resource, status, riskLevel, startDate, endDate } = req.query;

    // 模拟审计日志数据
    const mockAuditLogs = [
      {
        id: '1',
        userId: '101',
        username: 'admin1',
        role: 'admin',
        action: 'login',
        resource: 'system',
        status: 'success',
        timestamp: '2023-05-20T08:30:45Z',
        ip: '*************',
        userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
        riskLevel: 'low'
      },
      {
        id: '2',
        userId: '102',
        username: 'admin2',
        role: 'admin',
        action: 'update',
        resource: 'user',
        resourceId: '201',
        details: '修改用户角色: user -> reviewer',
        status: 'success',
        timestamp: '2023-05-20T09:15:22Z',
        ip: '*************',
        userAgent: 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36',
        riskLevel: 'medium'
      },
      {
        id: '3',
        userId: '103',
        username: 'superadmin1',
        role: 'superadmin',
        action: 'delete',
        resource: 'user',
        resourceId: '202',
        details: '删除用户',
        status: 'success',
        timestamp: '2023-05-20T10:05:18Z',
        ip: '*************',
        userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
        riskLevel: 'high'
      },
      {
        id: '4',
        userId: '101',
        username: 'admin1',
        role: 'admin',
        action: 'update',
        resource: 'system_settings',
        details: '修改系统设置: 内容审核阈值',
        status: 'failure',
        timestamp: '2023-05-20T11:30:45Z',
        ip: '*************',
        userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
        riskLevel: 'critical'
      },
      {
        id: '5',
        userId: '103',
        username: 'superadmin1',
        role: 'superadmin',
        action: 'create',
        resource: 'role',
        resourceId: '301',
        details: '创建新角色: 数据分析师',
        status: 'success',
        timestamp: '2023-05-20T13:45:12Z',
        ip: '*************',
        userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
        riskLevel: 'medium'
      }
    ];

    // 应用过滤器
    let filteredLogs = mockAuditLogs;

    if (username) {
      filteredLogs = filteredLogs.filter(log =>
        log.username.toLowerCase().includes(username.toLowerCase())
      );
    }

    if (role) {
      filteredLogs = filteredLogs.filter(log => log.role === role);
    }

    if (action) {
      filteredLogs = filteredLogs.filter(log =>
        log.action.toLowerCase().includes(action.toLowerCase())
      );
    }

    if (resource) {
      filteredLogs = filteredLogs.filter(log =>
        log.resource.toLowerCase().includes(resource.toLowerCase())
      );
    }

    if (status) {
      filteredLogs = filteredLogs.filter(log => log.status === status);
    }

    if (riskLevel) {
      filteredLogs = filteredLogs.filter(log => log.riskLevel === riskLevel);
    }

    if (startDate) {
      const start = new Date(startDate);
      filteredLogs = filteredLogs.filter(log => new Date(log.timestamp) >= start);
    }

    if (endDate) {
      const end = new Date(endDate);
      filteredLogs = filteredLogs.filter(log => new Date(log.timestamp) <= end);
    }

    // 分页
    const startIndex = (parseInt(page) - 1) * parseInt(limit);
    const endIndex = startIndex + parseInt(limit);
    const paginatedLogs = filteredLogs.slice(startIndex, endIndex);

    console.log('📈 审计日志查询结果:', { total: filteredLogs.length, count: paginatedLogs.length });

    res.json({
      success: true,
      data: {
        logs: paginatedLogs,
        total: filteredLogs.length,
        page: parseInt(page),
        limit: parseInt(limit),
        totalPages: Math.ceil(filteredLogs.length / parseInt(limit))
      }
    });
  } catch (error) {
    console.error('❌ 获取审计日志失败:', error);
    res.status(500).json({
      success: false,
      message: '获取审计日志失败',
      error: error.message
    });
  }
});

// 审计日志详情API
app.get('/api/admin/audit-logs/:id', async (req, res) => {
  console.log('📋 获取审计日志详情请求:', req.params.id);
  try {
    const { id } = req.params;

    // 模拟审计日志详情
    const mockLog = {
      id: id,
      userId: '101',
      username: 'admin1',
      role: 'admin',
      action: 'update',
      resource: 'user',
      resourceId: '201',
      details: '修改用户角色: user -> reviewer',
      status: 'success',
      timestamp: '2023-05-20T09:15:22Z',
      ip: '*************',
      userAgent: 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
      riskLevel: 'medium',
      sessionId: 'sess_' + Date.now(),
      requestId: 'req_' + Date.now(),
      duration: 245,
      requestBody: JSON.stringify({ role: 'reviewer' }),
      responseCode: 200
    };

    console.log('✅ 审计日志详情获取成功:', { id });

    res.json({
      success: true,
      data: mockLog
    });
  } catch (error) {
    console.error('❌ 获取审计日志详情失败:', error);
    res.status(500).json({
      success: false,
      message: '获取审计日志详情失败',
      error: error.message
    });
  }
});

// 导出审计日志API
app.post('/api/admin/audit-logs/export', async (req, res) => {
  console.log('📤 导出审计日志请求');
  try {
    const { username, role, action, resource, status, riskLevel, startDate, endDate } = req.body;

    // 模拟导出处理
    await new Promise(resolve => setTimeout(resolve, 1000));

    const exportData = {
      downloadUrl: '/mock-exports/audit-logs.csv',
      fileName: `audit_logs_${new Date().toISOString().split('T')[0]}.csv`,
      fileSize: '2.5 MB',
      recordCount: 150
    };

    console.log('✅ 审计日志导出成功:', exportData);

    res.json({
      success: true,
      data: exportData,
      message: '审计日志导出成功'
    });
  } catch (error) {
    console.error('❌ 导出审计日志失败:', error);
    res.status(500).json({
      success: false,
      message: '导出审计日志失败',
      error: error.message
    });
  }
});

// 故事列表API
app.get('/api/story/list', async (req, res) => {
  console.log('📚 获取故事列表请求');
  try {
    const { page = 1, pageSize = 6, sortBy = 'latest', tag, category, search, educationLevel, industry } = req.query;

    // 构建查询条件 - 只显示已审核通过的故事
    const where = { status: 'approved' };

    // 添加搜索条件
    if (search) {
      where.OR = [
        { title: { contains: search } },
        { content: { contains: search } }
      ];
    }

    // 添加其他筛选条件
    if (category) {
      where.category = category;
    }

    if (educationLevel) {
      where.educationLevel = educationLevel;
    }

    if (industry) {
      where.industry = industry;
    }

    // 标签筛选
    if (tag) {
      where.tags = { has: tag };
    }

    // 排序条件
    const orderBy = sortBy === 'popular' ? { likes: 'desc' } : { createdAt: 'desc' };

    const [stories, total] = await Promise.all([
      prisma.story.findMany({
        where,
        skip: (parseInt(page) - 1) * parseInt(pageSize),
        take: parseInt(pageSize),
        orderBy,
        include: {
          user: {
            select: {
              id: true,
              username: true,
              name: true
            }
          }
        }
      }),
      prisma.story.count({ where })
    ]);

    // 获取热门标签
    const popularTags = [
      { tag: "求职经验", count: 15 },
      { tag: "实习经验", count: 8 },
      { tag: "转行经验", count: 6 },
      { tag: "职场成长", count: 12 },
      { tag: "面试技巧", count: 10 },
      { tag: "简历优化", count: 7 }
    ];

    console.log('📈 故事查询结果:', { total, count: stories.length });

    res.json({
      success: true,
      stories: stories.map(story => ({
        id: story.id,
        title: story.title,
        content: story.content,
        contentPreview: story.content ? story.content.substring(0, 200) + '...' : '',
        tags: story.tags || [],
        category: story.category,
        educationLevel: story.educationLevel,
        industry: story.industry,
        likes: story.likes || 0,
        dislikes: story.dislikes || 0,
        views: story.views || 0,
        isAnonymous: story.isAnonymous,
        author: story.isAnonymous ? null : {
          id: story.user?.id,
          username: story.user?.username,
          name: story.user?.name
        },
        createdAt: story.createdAt,
        updatedAt: story.updatedAt
      })),
      totalPages: Math.ceil(total / parseInt(pageSize)),
      currentPage: parseInt(page),
      totalItems: total,
      popularTags
    });
  } catch (error) {
    console.error('❌ 获取故事列表失败:', error);
    res.status(500).json({
      success: false,
      message: '获取故事列表失败',
      error: error.message
    });
  }
});

// 数据管理API
app.get('/api/admin/data/tasks', async (req, res) => {
  console.log('📋 获取数据任务请求');
  try {
    const { page = 1, limit = 10, type, status } = req.query;

    // 模拟数据任务
    const mockTasks = [
      {
        id: '1',
        type: 'export',
        name: '用户数据导出',
        dataType: 'users',
        status: 'completed',
        progress: 100,
        createdAt: '2024-01-15 14:30:00',
        completedAt: '2024-01-15 14:32:15',
        fileSize: '2.5 MB',
        recordCount: 1250,
        createdBy: '管理员',
        downloadUrl: '/api/admin/data/download/users_export_20240115.csv'
      },
      {
        id: '2',
        type: 'import',
        name: '问卷数据导入',
        dataType: 'questionnaires',
        status: 'running',
        progress: 65,
        createdAt: '2024-01-15 15:00:00',
        recordCount: 500,
        errorCount: 12,
        createdBy: '数据分析师'
      },
      {
        id: '3',
        type: 'export',
        name: '审计日志导出',
        dataType: 'audit_logs',
        status: 'failed',
        progress: 45,
        createdAt: '2024-01-15 13:15:00',
        errorCount: 1,
        errorMessage: '数据库连接超时',
        createdBy: '安全管理员'
      },
      {
        id: '4',
        type: 'import',
        name: '故事数据导入',
        dataType: 'stories',
        status: 'pending',
        progress: 0,
        createdAt: '2024-01-15 16:00:00',
        recordCount: 200,
        createdBy: '内容管理员'
      }
    ];

    // 应用过滤器
    let filteredTasks = mockTasks;
    if (type) {
      filteredTasks = filteredTasks.filter(task => task.type === type);
    }
    if (status) {
      filteredTasks = filteredTasks.filter(task => task.status === status);
    }

    // 分页
    const startIndex = (parseInt(page) - 1) * parseInt(limit);
    const endIndex = startIndex + parseInt(limit);
    const paginatedTasks = filteredTasks.slice(startIndex, endIndex);

    res.json({
      success: true,
      data: {
        tasks: paginatedTasks,
        total: filteredTasks.length,
        page: parseInt(page),
        limit: parseInt(limit),
        totalPages: Math.ceil(filteredTasks.length / parseInt(limit))
      }
    });
  } catch (error) {
    console.error('❌ 获取数据任务失败:', error);
    res.status(500).json({
      success: false,
      message: '获取数据任务失败',
      error: error.message
    });
  }
});

// 创建导出任务API
app.post('/api/admin/data/export', async (req, res) => {
  console.log('📤 创建导出任务请求');
  try {
    const { dataType, format = 'csv', filters = {} } = req.body;

    if (!dataType) {
      return res.status(400).json({
        success: false,
        message: '数据类型不能为空'
      });
    }

    // 模拟创建导出任务
    const newTask = {
      id: Date.now().toString(),
      type: 'export',
      name: `${dataType}数据导出`,
      dataType,
      format,
      status: 'pending',
      progress: 0,
      createdAt: new Date().toLocaleString(),
      createdBy: '当前用户',
      filters
    };

    console.log('✅ 导出任务创建成功:', { id: newTask.id, dataType });

    res.json({
      success: true,
      data: newTask,
      message: '导出任务创建成功'
    });
  } catch (error) {
    console.error('❌ 创建导出任务失败:', error);
    res.status(500).json({
      success: false,
      message: '创建导出任务失败',
      error: error.message
    });
  }
});

// 创建导入任务API
app.post('/api/admin/data/import', async (req, res) => {
  console.log('📥 创建导入任务请求');
  try {
    const { dataType, fileName, fileSize } = req.body;

    if (!dataType || !fileName) {
      return res.status(400).json({
        success: false,
        message: '数据类型和文件名不能为空'
      });
    }

    // 模拟创建导入任务
    const newTask = {
      id: Date.now().toString(),
      type: 'import',
      name: `${dataType}数据导入`,
      dataType,
      fileName,
      fileSize,
      status: 'pending',
      progress: 0,
      createdAt: new Date().toLocaleString(),
      createdBy: '当前用户'
    };

    console.log('✅ 导入任务创建成功:', { id: newTask.id, dataType, fileName });

    res.json({
      success: true,
      data: newTask,
      message: '导入任务创建成功'
    });
  } catch (error) {
    console.error('❌ 创建导入任务失败:', error);
    res.status(500).json({
      success: false,
      message: '创建导入任务失败',
      error: error.message
    });
  }
});

// 404处理
app.use((req, res) => {
  console.log(`❌ 404: ${req.method} ${req.url}`);
  res.status(404).json({
    success: false,
    error: 'API endpoint not found',
    path: req.originalUrl,
    method: req.method
  });
});

// 错误处理
app.use((err, req, res, next) => {
  console.error('❌ 服务器错误:', err);
  res.status(500).json({
    success: false,
    error: 'Internal server error',
    message: err.message
  });
});

// 初始化测试数据
async function initTestData() {
  try {
    // 检查是否已有故事数据
    const storyCount = await prisma.story.count();
    if (storyCount === 0) {
      console.log('🌱 初始化测试故事数据...');

      const testStories = [
        {
          title: "从计算机专业到产品经理的转行之路",
          content: "大学学的是计算机科学，毕业后在一家互联网公司做了两年开发。虽然技术能力不错，但我发现自己更喜欢与人沟通，思考产品逻辑。经过深思熟虑，我决定转行做产品经理。转行过程中遇到了很多挑战，比如缺乏产品经验、需要重新学习商业知识等。但通过不断学习和实践，现在我已经成功转型，在一家知名互联网公司担任高级产品经理。",
          tags: '["转行经验", "产品经理", "计算机专业"]',
          category: "职业转换",
          educationLevel: "本科",
          industry: "互联网",
          status: "approved",
          isAnonymous: true,
          likes: 15
        },
        {
          title: "应届生求职经验分享：如何在激烈竞争中脱颖而出",
          content: "作为一名应届毕业生，求职过程充满了挑战。我投了上百份简历，参加了几十场面试，最终收到了心仪公司的offer。在这个过程中，我总结了几个关键点：1. 简历要突出亮点，量化成果；2. 面试前要充分准备，了解公司和岗位；3. 保持积极心态，从每次失败中学习。希望我的经验能帮助到其他求职的同学。",
          tags: '["求职经验", "应届生", "面试技巧"]',
          category: "求职指南",
          educationLevel: "本科",
          industry: "金融",
          status: "approved",
          isAnonymous: false,
          author: "小明同学",
          likes: 23
        },
        {
          title: "实习经历让我重新认识职场",
          content: "大三暑假在一家外企实习了三个月，这段经历让我对职场有了全新的认识。实习期间，我不仅学到了专业技能，更重要的是学会了如何与同事协作、如何处理工作压力、如何平衡工作与生活。虽然实习工资不高，但这段经历为我后来的正式求职打下了坚实基础。建议学弟学妹们一定要重视实习机会。",
          tags: '["实习经验", "职场成长", "外企"]',
          category: "实习体验",
          educationLevel: "本科",
          industry: "制造业",
          status: "approved",
          isAnonymous: true,
          likes: 8
        },
        {
          title: "毕业三年，我从月薪3K到年薪30W的成长历程",
          content: "刚毕业时在一家小公司做销售，月薪只有3000元。那时候觉得前途渺茫，但我没有放弃。通过不断学习新技能、积累客户资源、提升业务能力，三年后我跳槽到一家大公司，年薪达到了30万。这个过程中最重要的是保持学习的心态和坚持不懈的努力。每个人的起点可能不同，但只要努力，都能创造属于自己的成功。",
          tags: '["职场成长", "薪资提升", "销售"]',
          category: "职业发展",
          educationLevel: "专科",
          industry: "销售",
          status: "approved",
          isAnonymous: false,
          author: "奋斗青年",
          likes: 45
        },
        {
          title: "选择考研还是直接工作？我的思考和建议",
          content: "大四面临选择：考研还是工作？经过深思熟虑，我选择了直接工作。虽然身边很多同学都在考研，但我认为实践经验同样重要。现在工作两年了，我觉得这个选择是对的。工作让我更快地了解社会，积累了宝贵的实践经验。当然，考研也有其价值，关键是要根据自己的情况和目标来选择。",
          tags: '["考研vs工作", "职业规划", "人生选择"]',
          category: "职业规划",
          educationLevel: "本科",
          industry: "教育",
          status: "approved",
          isAnonymous: true,
          likes: 12
        },
        {
          title: "疫情期间的求职经历：挑战与机遇并存",
          content: "疫情期间求职确实比较困难，很多公司都在缩招。但我发现这也是一个机会，可以更好地提升自己。我利用这段时间学习了新技能，完善了简历，还参加了一些线上培训。最终在疫情后期找到了满意的工作。这段经历让我明白，困难时期往往也是成长的最好时机。",
          tags: '["疫情求职", "逆境成长", "在线学习"]',
          category: "特殊时期",
          educationLevel: "硕士",
          industry: "互联网",
          status: "approved",
          isAnonymous: true,
          likes: 18
        }
      ];

      for (const story of testStories) {
        await prisma.story.create({
          data: {
            ...story,
            createdAt: new Date(Date.now() - Math.random() * 30 * 24 * 60 * 60 * 1000), // 随机过去30天内的时间
            isTestData: true,
            testDataVersion: "1.0",
            testDataSet: "initial"
          }
        });
      }

      console.log(`✅ 已创建 ${testStories.length} 条测试故事数据`);
    } else {
      console.log(`📚 数据库中已有 ${storyCount} 条故事数据`);
    }
  } catch (error) {
    console.error('❌ 初始化测试数据失败:', error);
  }
}

// 启动服务器
const server = app.listen(PORT, async () => {
  console.log(`🚀 测试API服务器运行在 http://localhost:${PORT}`);
  console.log(`📊 健康检查: http://localhost:${PORT}/health`);
  console.log(`📊 仪表盘统计: http://localhost:${PORT}/api/admin/dashboard/stats`);

  // 初始化测试数据
  await initTestData();
});

// 优雅关闭
process.on('SIGINT', () => {
  console.log('\n正在关闭服务器...');
  server.close(() => {
    console.log('服务器已关闭');
    process.exit(0);
  });
});

// 处理未捕获的异常
process.on('uncaughtException', (err) => {
  console.error('未捕获的异常:', err);
  process.exit(1);
});

process.on('unhandledRejection', (reason, promise) => {
  console.error('未处理的Promise拒绝:', reason);
  process.exit(1);
});
