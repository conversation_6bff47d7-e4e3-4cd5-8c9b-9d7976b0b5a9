import { Hono } from 'hono';
import { z } from 'zod';
import { zValidator } from '@hono/zod-validator';
import { PrismaClient } from '@prisma/client';
import { adminAuth } from '../middlewares/adminAuth';
import { getPrismaClient } from '../utils/prisma';
import { mockTags, shouldUseMockData } from '../mock';

// 创建标签路由
const app = new Hono();

// 标签创建验证模式
const createTagSchema = z.object({
  name: z.string().min(1).max(50),
  color: z.string().min(1).max(20),
  priority: z.number().int().min(0).max(100),
  category: z.string().optional(),
  parentId: z.string().nullable().optional(),
});

// 标签更新验证模式
const updateTagSchema = z.object({
  name: z.string().min(1).max(50).optional(),
  color: z.string().min(1).max(20).optional(),
  priority: z.number().int().min(0).max(100).optional(),
  category: z.string().optional(),
  parentId: z.string().nullable().optional(),
});

// 获取所有标签（公开接口）
app.get('/', async (c) => {
  try {
    // 检查是否应该使用模拟数据
    if (shouldUseMockData(c.env)) {
      console.log('Development mode: Returning mock tags');
      return c.json({
        success: true,
        data: mockTags,
      });
    }

    // 使用适配的Prisma客户端
    const prisma = getPrismaClient(c.env);

    // 获取所有标签
    const tags = await prisma.tag.findMany({
      orderBy: {
        priority: 'desc',
      },
    });

    // 注意：使用模拟客户端时不需要断开连接
    if (typeof prisma.$disconnect === 'function') {
      await prisma.$disconnect();
    }

    return c.json({
      success: true,
      data: tags,
    });
  } catch (error) {
    console.error('Error getting tags:', error);
    return c.json({ success: false, error: 'Failed to get tags' }, 500);
  }
});

// 获取标签统计（公开接口）
app.get('/stats', async (c) => {
  try {
    console.log('Getting tag statistics...');

    // 获取查询参数
    const category = c.req.query('category');
    const limit = parseInt(c.req.query('limit') || '20');
    const sortBy = c.req.query('sortBy') || 'count'; // count, name, priority

    // 检查是否应该使用模拟数据
    if (shouldUseMockData(c.env)) {
      console.log('Development mode: Returning mock tag stats');

      // 模拟标签统计数据
      const mockTagStats = [
        { id: 'job-hunting', name: '求职故事', color: 'blue', category: 'job', count: 156 },
        { id: 'interview', name: '面试经验', color: 'blue', category: 'job', count: 134 },
        { id: 'internship', name: '实习经历', color: 'yellow', category: 'experience', count: 98 },
        { id: 'career-change', name: '转行经历', color: 'yellow', category: 'experience', count: 87 },
        { id: 'salary', name: '薪资谈判', color: 'blue', category: 'job', count: 76 },
        { id: 'bachelor', name: '本科经验', color: 'green', category: 'education', count: 65 },
        { id: 'it-industry', name: 'IT行业', color: 'purple', category: 'industry', count: 54 },
        { id: 'resume', name: '简历技巧', color: 'blue', category: 'job', count: 43 },
        { id: 'master', name: '硕士经验', color: 'green', category: 'education', count: 38 },
        { id: 'work-life', name: '工作生活', color: 'yellow', category: 'experience', count: 32 },
      ];

      // 应用分类过滤
      let filteredTags = mockTagStats;
      if (category && category !== 'all') {
        filteredTags = mockTagStats.filter(tag => tag.category === category);
      }

      // 应用排序
      if (sortBy === 'count') {
        filteredTags.sort((a, b) => b.count - a.count);
      } else if (sortBy === 'name') {
        filteredTags.sort((a, b) => a.name.localeCompare(b.name));
      }

      // 应用限制
      filteredTags = filteredTags.slice(0, limit);

      return c.json({
        success: true,
        tags: filteredTags,
        meta: {
          category: category || 'all',
          limit,
          sortBy,
          count: filteredTags.length,
        },
      });
    }

    // 使用适配的Prisma客户端
    const prisma = getPrismaClient(c.env);

    // 构建查询条件
    const where: any = {};
    if (category && category !== 'all') {
      where.category = category;
    }

    // 构建排序条件
    let orderBy: any = {};
    switch (sortBy) {
      case 'count':
        orderBy = { usageCount: 'desc' };
        break;
      case 'name':
        orderBy = { name: 'asc' };
        break;
      case 'priority':
        orderBy = [{ priority: 'desc' }, { usageCount: 'desc' }];
        break;
      default:
        orderBy = { usageCount: 'desc' };
    }

    // 获取标签统计
    const tags = await prisma.tag.findMany({
      where,
      orderBy,
      take: limit,
      select: {
        id: true,
        name: true,
        color: true,
        category: true,
        usageCount: true,
        priority: true,
        createdAt: true,
        updatedAt: true,
      },
    });

    // 格式化数据
    const formattedTags = tags.map(tag => ({
      id: tag.id.toString(),
      name: tag.name,
      color: tag.color,
      category: tag.category,
      count: tag.usageCount,
      priority: tag.priority,
      createdAt: tag.createdAt,
      updatedAt: tag.updatedAt,
    }));

    // 注意：使用模拟客户端时不需要断开连接
    if (typeof prisma.$disconnect === 'function') {
      await prisma.$disconnect();
    }

    console.log(`Found ${formattedTags.length} tags`);

    return c.json({
      success: true,
      tags: formattedTags,
      meta: {
        category: category || 'all',
        limit,
        sortBy,
        count: formattedTags.length,
      },
    });
  } catch (error) {
    console.error('Error getting tag stats:', error);
    return c.json({ success: false, error: 'Failed to get tag statistics' }, 500);
  }
});

// 获取所有标签（管理员接口）
app.get('/admin', adminAuth, async (c) => {
  try {
    // 检查是否应该使用模拟数据
    if (shouldUseMockData(c.env)) {
      console.log('Development mode: Returning mock tags');
      return c.json({
        success: true,
        tags: mockTags,
      });
    }

    // 使用适配的Prisma客户端
    const prisma = getPrismaClient(c.env);

    // 获取所有标签
    const tags = await prisma.tag.findMany({
      orderBy: {
        priority: 'desc',
      },
    });

    // 注意：使用模拟客户端时不需要断开连接
    if (typeof prisma.$disconnect === 'function') {
      await prisma.$disconnect();
    }

    return c.json({
      success: true,
      tags,
    });
  } catch (error) {
    console.error('Error getting tags:', error);
    return c.json({ success: false, error: 'Failed to get tags' }, 500);
  }
});

// 创建标签
app.post('/', adminAuth, zValidator('json', createTagSchema), async (c) => {
  const data = c.req.valid('json');

  try {
    // 检查是否应该使用模拟数据
    if (shouldUseMockData(c.env)) {
      console.log('Development mode: Creating mock tag');

      // 创建模拟标签
      const mockTag = {
        id: `tag-${Date.now()}`,
        name: data.name,
        color: data.color,
        priority: data.priority,
        category: data.category || 'other',
        parentId: data.parentId || null,
        count: 0,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
      };

      return c.json({
        success: true,
        tag: mockTag,
      });
    }

    // 使用适配的Prisma客户端
    const prisma = getPrismaClient(c.env);

    // 创建标签
    const tag = await prisma.tag.create({
      data: {
        name: data.name,
        color: data.color,
        priority: data.priority,
        category: data.category,
        parentId: data.parentId,
      },
    });

    // 注意：使用模拟客户端时不需要断开连接
    if (typeof prisma.$disconnect === 'function') {
      await prisma.$disconnect();
    }

    return c.json({
      success: true,
      tag,
    });
  } catch (error) {
    console.error('Error creating tag:', error);
    return c.json({ success: false, error: 'Failed to create tag' }, 500);
  }
});

// 更新标签
app.put('/:id', adminAuth, zValidator('json', updateTagSchema), async (c) => {
  const { id } = c.req.param();
  const data = c.req.valid('json');

  try {
    // 检查是否应该使用模拟数据
    if (shouldUseMockData(c.env)) {
      console.log(`Development mode: Updating mock tag ${id}`);

      // 查找模拟标签
      const tagIndex = mockTags.findIndex(tag => tag.id === id);

      if (tagIndex === -1) {
        return c.json({ success: false, error: 'Tag not found' }, 404);
      }

      // 更新模拟标签
      const updatedTag = {
        ...mockTags[tagIndex],
        ...data,
        updatedAt: new Date().toISOString(),
      };

      return c.json({
        success: true,
        tag: updatedTag,
      });
    }

    // 使用适配的Prisma客户端
    const prisma = getPrismaClient(c.env);

    // 更新标签
    const tag = await prisma.tag.update({
      where: { id },
      data,
    });

    // 注意：使用模拟客户端时不需要断开连接
    if (typeof prisma.$disconnect === 'function') {
      await prisma.$disconnect();
    }

    return c.json({
      success: true,
      tag,
    });
  } catch (error) {
    console.error('Error updating tag:', error);
    return c.json({ success: false, error: 'Failed to update tag' }, 500);
  }
});

// 删除标签
app.delete('/:id', adminAuth, async (c) => {
  const { id } = c.req.param();

  try {
    // 检查是否应该使用模拟数据
    if (shouldUseMockData(c.env)) {
      console.log(`Development mode: Deleting mock tag ${id}`);

      // 查找模拟标签
      const tagIndex = mockTags.findIndex(tag => tag.id === id);

      if (tagIndex === -1) {
        return c.json({ success: false, error: 'Tag not found' }, 404);
      }

      return c.json({
        success: true,
        message: 'Tag deleted successfully',
      });
    }

    // 使用适配的Prisma客户端
    const prisma = getPrismaClient(c.env);

    // 删除标签
    await prisma.tag.delete({
      where: { id },
    });

    // 注意：使用模拟客户端时不需要断开连接
    if (typeof prisma.$disconnect === 'function') {
      await prisma.$disconnect();
    }

    return c.json({
      success: true,
      message: 'Tag deleted successfully',
    });
  } catch (error) {
    console.error('Error deleting tag:', error);
    return c.json({ success: false, error: 'Failed to delete tag' }, 500);
  }
});

export { app as tags };
