import { useState, Suspense, lazy } from 'react';
import { useQuery } from '@tanstack/react-query';
import { Link } from 'react-router-dom';
import { getVisualizationData, VisualizationFilters } from '@/lib/api';
import { hybridApiService } from '@/services/apiServiceV2';
import { useApiDebug, DataStructures } from '@/hooks/useApiDebug';
import ApiDebugPanel from '@/components/debug/ApiDebugPanel';
import DataIntegrationPanel from '@/components/debug/DataIntegrationPanel';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import ChartFilters from '@/components/visualization/ChartFilters';
import { Skeleton } from '@/components/ui/skeleton';
import { BarChart3, TrendingUp } from 'lucide-react';

// 导入组件级代码分割工具
import { createLazyComponent } from '@/utils/componentSplitting';

// 懒加载图表组件
const DashboardSummary = createLazyComponent(
  () => import('@/components/visualization/DashboardSummary'),
  { name: 'DashboardSummary' }
);

const EducationDistributionChart = createLazyComponent(
  () => import('@/components/visualization/charts/EducationDistributionChart'),
  { name: 'EducationDistributionChart' }
);

const IndustryDistributionChart = createLazyComponent(
  () => import('@/components/visualization/charts/IndustryDistributionChart'),
  { name: 'IndustryDistributionChart' }
);

const SalaryComparisonChart = createLazyComponent(
  () => import('@/components/visualization/charts/SalaryComparisonChart'),
  { name: 'SalaryComparisonChart' }
);

const RegionDistributionChart = createLazyComponent(
  () => import('@/components/visualization/charts/RegionDistributionChart'),
  { name: 'RegionDistributionChart' }
);

const UnemploymentDurationChart = createLazyComponent(
  () => import('@/components/visualization/charts/UnemploymentDurationChart'),
  { name: 'UnemploymentDurationChart' }
);

const CareerChangeChart = createLazyComponent(
  () => import('@/components/visualization/charts/CareerChangeChart'),
  { name: 'CareerChangeChart' }
);

const VerificationDistributionChart = createLazyComponent(
  () => import('@/components/visualization/charts/VerificationDistributionChart'),
  { name: 'VerificationDistributionChart' }
);

// 预加载所有图表组件
const preloadAllCharts = () => {
  EducationDistributionChart.preload();
  IndustryDistributionChart.preload();
  SalaryComparisonChart.preload();
  RegionDistributionChart.preload();
  UnemploymentDurationChart.preload();
  CareerChangeChart.preload();
  VerificationDistributionChart.preload();
  DashboardSummary.preload();
};

export default function VisualizationPage() {
  // State for filters
  const [filters, setFilters] = useState<VisualizationFilters>({});
  const [dataType, setDataType] = useState<'all' | 'verified'>('all');

  // API调试配置
  const apiDebug = useApiDebug({
    pageName: '数据可视化页面',
    requests: [
      {
        id: 'visualization-data-v2',
        name: '可视化数据 (v2.0)',
        endpoint: '/api/v2/visualization/data',
        method: 'GET',
        expectedStructure: DataStructures.visualizationData,
        testFunction: async () => {
          const response = await hybridApiService.getVisualizationData();
          return response;
        }
      },
      {
        id: 'visualization-data-v1',
        name: '可视化数据 (v1.0)',
        endpoint: '/api/visualization/data',
        method: 'GET',
        expectedStructure: DataStructures.visualizationData,
        testFunction: async () => {
          const response = await getVisualizationData({
            ...filters,
            verified: dataType === 'verified' ? true : undefined
          });
          return response;
        }
      }
    ]
  });

  // 使用混合API数据 (优先v2.0，降级到v1.0)
  const { data, isLoading, error } = useQuery(
    ['visualizationData', filters, dataType],
    async () => {
      try {
        // 优先使用v2.0 API
        return await hybridApiService.getVisualizationData();
      } catch (error) {
        console.warn('混合API失败，使用原始API:', error);
        // 降级到原始API
        return await getVisualizationData({
          ...filters,
          verified: dataType === 'verified' ? true : undefined
        });
      }
    },
    {
      refetchOnWindowFocus: false,
      staleTime: 5 * 60 * 1000, // 5 minutes
    }
  );

  // Handle filter changes
  const handleFilterChange = (newFilters: VisualizationFilters) => {
    setFilters(newFilters);

    // 当用户应用筛选器时，预加载图表组件
    if ('requestIdleCallback' in window) {
      (window as any).requestIdleCallback(() => {
        preloadAllCharts();
      });
    } else {
      setTimeout(preloadAllCharts, 1000);
    }
  };

  // Handle data type change
  const handleDataTypeChange = (value: string) => {
    setDataType(value as 'all' | 'verified');

    // 当用户切换数据类型时，预加载图表组件
    if ('requestIdleCallback' in window) {
      (window as any).requestIdleCallback(() => {
        preloadAllCharts();
      });
    } else {
      setTimeout(preloadAllCharts, 1000);
    }
  };

  return (
    <div className="container mx-auto py-8">


      <div className="flex flex-col md:flex-row justify-between items-start md:items-center mb-8">
        <div>
          <h1 className="text-3xl font-bold mb-2">大学生就业数据可视化</h1>
          <p className="text-gray-600">
            通过图表直观展示各学历群体的就业差异与趋势，帮助您了解当前就业市场状况。
          </p>
        </div>

        <div className="mt-4 md:mt-0 flex gap-4">
          <Link to="/advanced-analysis">
            <Button variant="outline" className="flex items-center gap-2">
              <TrendingUp className="h-4 w-4" />
              高级分析
            </Button>
          </Link>
        </div>
      </div>

      <Tabs defaultValue="all" onValueChange={handleDataTypeChange} className="mb-8">
        <div className="flex justify-between items-center mb-4">
          <TabsList>
            <TabsTrigger value="all">全部数据</TabsTrigger>
            <TabsTrigger value="verified">已验证数据</TabsTrigger>
          </TabsList>

          <ChartFilters onFilterChange={handleFilterChange} />
        </div>

        <TabsContent value="all" className="mt-0">
          {isLoading ? (
            <DashboardSkeleton />
          ) : error ? (
            <div className="text-center py-12">
              <p className="text-red-500">加载数据失败，请稍后重试</p>
            </div>
          ) : (
            <DashboardContent data={data} dataType="全部数据" />
          )}
        </TabsContent>

        <TabsContent value="verified" className="mt-0">
          {isLoading ? (
            <DashboardSkeleton />
          ) : error ? (
            <div className="text-center py-12">
              <p className="text-red-500">加载数据失败，请稍后重试</p>
            </div>
          ) : (
            <DashboardContent data={data} dataType="已验证数据" />
          )}
        </TabsContent>
      </Tabs>
    </div>
  );
}

// 图表加载占位符
const ChartLoadingFallback = () => (
  <div className="flex justify-center items-center h-80">
    <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
  </div>
);

// Dashboard content component
function DashboardContent({ data, dataType }: { data: any, dataType: string }) {
  if (!data || !data.success) {
    return (
      <div className="text-center py-12">
        <p className="text-gray-500">暂无数据</p>
      </div>
    );
  }

  // 适配数据结构 - 后端返回的数据在 data.stats 中
  const stats = data.stats || data.data || {};

  return (
    <>
      <Suspense fallback={<Skeleton className="h-40 w-full" />}>
        <DashboardSummary data={data} dataType={dataType} />
      </Suspense>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
        <Card>
          <CardHeader>
            <CardTitle>学历分布</CardTitle>
            <CardDescription>各学历层次人数占比</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="h-80">
              <Suspense fallback={<ChartLoadingFallback />}>
                <EducationDistributionChart data={stats.educationLevels || []} />
              </Suspense>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>就业行业分布</CardTitle>
            <CardDescription>各行业就业人数占比</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="h-80">
              <Suspense fallback={<ChartLoadingFallback />}>
                <IndustryDistributionChart data={stats.industries || []} />
              </Suspense>
            </div>
          </CardContent>
        </Card>
      </div>

      <div className="grid grid-cols-1 gap-6 mb-6">
        <Card>
          <CardHeader>
            <CardTitle>期望薪资 vs 实际薪资</CardTitle>
            <CardDescription>期望与现实的薪资对比</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="h-80">
              <Suspense fallback={<ChartLoadingFallback />}>
                <SalaryComparisonChart
                  expectedData={stats.expectedSalaries || []}
                  actualData={stats.actualSalaries || []}
                />
              </Suspense>
            </div>
          </CardContent>
        </Card>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
        <Card>
          <CardHeader>
            <CardTitle>地区就业热度</CardTitle>
            <CardDescription>各地区就业人数分布</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="h-80">
              <Suspense fallback={<ChartLoadingFallback />}>
                <RegionDistributionChart data={stats.regions || []} />
              </Suspense>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>失业时长趋势</CardTitle>
            <CardDescription>失业时间分布情况</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="h-80">
              <Suspense fallback={<ChartLoadingFallback />}>
                <UnemploymentDurationChart data={stats.unemploymentDurations || []} />
              </Suspense>
            </div>
          </CardContent>
        </Card>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <Card>
          <CardHeader>
            <CardTitle>转专业意向度</CardTitle>
            <CardDescription>不同群体的转专业意愿</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="h-80">
              <Suspense fallback={<ChartLoadingFallback />}>
                <CareerChangeChart data={stats.careerChanges || []} />
              </Suspense>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>数据验证分布</CardTitle>
            <CardDescription>已验证与匿名数据占比</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="h-80">
              <Suspense fallback={<ChartLoadingFallback />}>
                <VerificationDistributionChart
                  verified={stats.verifiedCount || 0}
                  anonymous={stats.anonymousCount || 0}
                />
              </Suspense>
            </div>
          </CardContent>
        </Card>
      </div>
    </>
  );
}

// Skeleton loader for dashboard
function DashboardSkeleton() {
  return (
    <>
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-6">
        {[1, 2, 3].map((i) => (
          <Card key={i}>
            <CardHeader>
              <Skeleton className="h-6 w-1/2" />
              <Skeleton className="h-4 w-3/4" />
            </CardHeader>
            <CardContent>
              <Skeleton className="h-20 w-full" />
            </CardContent>
          </Card>
        ))}
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
        {[1, 2].map((i) => (
          <Card key={i}>
            <CardHeader>
              <Skeleton className="h-6 w-1/2" />
              <Skeleton className="h-4 w-3/4" />
            </CardHeader>
            <CardContent>
              <Skeleton className="h-80 w-full" />
            </CardContent>
          </Card>
        ))}
      </div>

      <div className="grid grid-cols-1 gap-6 mb-6">
        <Card>
          <CardHeader>
            <Skeleton className="h-6 w-1/2" />
            <Skeleton className="h-4 w-3/4" />
          </CardHeader>
          <CardContent>
            <Skeleton className="h-80 w-full" />
          </CardContent>
        </Card>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        {[1, 2].map((i) => (
          <Card key={i}>
            <CardHeader>
              <Skeleton className="h-6 w-1/2" />
              <Skeleton className="h-4 w-3/4" />
            </CardHeader>
            <CardContent>
              <Skeleton className="h-80 w-full" />
            </CardContent>
          </Card>
        ))}
      </div>
    </>
  );
}
