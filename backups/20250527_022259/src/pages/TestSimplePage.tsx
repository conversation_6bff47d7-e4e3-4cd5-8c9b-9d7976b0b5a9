import React from 'react';

/**
 * 简单测试页面
 * 用于验证路由和组件加载是否正常
 */
const TestSimplePage: React.FC = () => {
  return (
    <div style={{ padding: '20px', textAlign: 'center' }}>
      <h1>测试页面</h1>
      <p>如果您能看到这个页面，说明路由和组件加载正常。</p>
      <p>当前时间: {new Date().toLocaleString()}</p>
      
      <div style={{ marginTop: '20px' }}>
        <h2>测试链接</h2>
        <ul style={{ listStyle: 'none', padding: 0 }}>
          <li><a href="/reviewer/login">审核员登录</a></li>
          <li><a href="/reviewer/dashboard">审核员仪表盘</a></li>
          <li><a href="/admin/login">管理员登录</a></li>
          <li><a href="/">首页</a></li>
        </ul>
      </div>
    </div>
  );
};

export default TestSimplePage;
