import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, Card<PERSON>ooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Ta<PERSON>, <PERSON>bsContent, Ta<PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import { Switch } from '@/components/ui/switch';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Label } from '@/components/ui/label';
import { Separator } from '@/components/ui/separator';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import { AlertCircle, CheckCircle2, Info, Loader2 } from 'lucide-react';
import { useToast } from '@/components/ui/use-toast';
import { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group';
import { Progress } from '@/components/ui/progress';
import { Badge } from '@/components/ui/badge';

// 脱敏配置类型
interface DeidentificationConfig {
  enabled: boolean;
  level: 'low' | 'medium' | 'high';
  aiProvider: 'openai' | 'mock';
  apiKey?: string;
  model?: string;
  preserveSemantics: boolean;
  applyToStories: boolean;
  applyToQuestionnaires: boolean;
  adminCanViewOriginal: boolean;
}

// 测试脱敏结果类型
interface TestResult {
  original: string;
  sanitized: string;
  modified: boolean;
  warning?: string;
  changes?: {
    changeCount: number;
    changeRatio: number;
    examples: Array<{ original: string; sanitized: string }>;
  };
  config?: {
    enabled: boolean;
    level: string;
    aiProvider: string;
  };
}

const DeidentificationSettings: React.FC = () => {
  const { toast } = useToast();
  const [config, setConfig] = useState<DeidentificationConfig>({
    enabled: false,
    level: 'medium',
    aiProvider: 'mock',
    model: 'gpt-4o',
    preserveSemantics: true,
    applyToStories: true,
    applyToQuestionnaires: false,
    adminCanViewOriginal: true
  });

  const [loading, setLoading] = useState(false);
  const [testLoading, setTestLoading] = useState(false);
  const [testContent, setTestContent] = useState('我是复旦大学2022届的李明，在阿里巴巴实习，被裁员了很绝望。');
  const [testResult, setTestResult] = useState<TestResult | null>(null);
  const [activeTab, setActiveTab] = useState('general');

  // 批量处理状态
  const [batchProcessing, setBatchProcessing] = useState(false);
  const [batchStatus, setBatchStatus] = useState<'idle' | 'processing' | 'completed' | 'error'>('idle');
  const [batchResults, setBatchResults] = useState<any>(null);
  const [batchProgress, setBatchProgress] = useState(0);
  const [batchFilter, setBatchFilter] = useState({
    contentType: 'story',
    status: 'all',
    tags: []
  });

  // 加载配置
  useEffect(() => {
    const fetchConfig = async () => {
      try {
        setLoading(true);

        // 检查是否为开发环境且使用模拟数据
        if (import.meta.env.DEV && import.meta.env.VITE_USE_MOCK === 'true') {
          console.log('使用模拟数据获取脱敏配置');

          // 模拟网络延迟
          await new Promise(resolve => setTimeout(resolve, 500));

          // 使用默认配置
          const mockConfig = {
            enabled: true,
            level: 'medium',
            aiProvider: 'mock',
            model: 'gpt-4o',
            preserveSemantics: true,
            applyToStories: true,
            applyToQuestionnaires: false,
            adminCanViewOriginal: true
          };

          setConfig(mockConfig);
          return;
        }

        const response = await fetch('/api/admin/deidentification/config', {
          headers: {
            'Authorization': `Bearer ${localStorage.getItem('adminToken')}`
          }
        });

        if (response.ok) {
          const data = await response.json();
          if (data.success && data.config) {
            setConfig(data.config);
          }
        } else {
          console.error('Error fetching deidentification config, status:', response.status);
          toast({
            title: '加载配置失败',
            description: '无法获取脱敏配置，使用默认设置',
            variant: 'destructive',
          });
        }
      } catch (error) {
        console.error('Error fetching deidentification config:', error);
        toast({
          title: '加载配置失败',
          description: '发生错误，使用默认设置',
          variant: 'destructive',
        });
      } finally {
        setLoading(false);
      }
    };

    fetchConfig();
  }, [toast]);

  // 保存配置
  const saveConfig = async () => {
    try {
      setLoading(true);

      // 检查是否为开发环境且使用模拟数据
      if (import.meta.env.DEV && import.meta.env.VITE_USE_MOCK === 'true') {
        console.log('使用模拟数据保存脱敏配置');

        // 模拟网络延迟
        await new Promise(resolve => setTimeout(resolve, 500));

        toast({
          title: '保存成功',
          description: '脱敏配置已更新（模拟数据）',
          variant: 'default',
        });

        return;
      }

      const response = await fetch('/api/admin/deidentification/config', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${localStorage.getItem('adminToken')}`
        },
        body: JSON.stringify({ config })
      });

      if (response.ok) {
        const data = await response.json();
        if (data.success) {
          toast({
            title: '保存成功',
            description: '脱敏配置已更新',
            variant: 'default',
          });
        }
      } else {
        console.error('Error saving deidentification config, status:', response.status);
        toast({
          title: '保存失败',
          description: '无法保存脱敏配置',
          variant: 'destructive',
        });
      }
    } catch (error) {
      console.error('Error saving deidentification config:', error);
      toast({
        title: '保存失败',
        description: '发生错误，请稍后重试',
        variant: 'destructive',
      });
    } finally {
      setLoading(false);
    }
  };

  // 测试脱敏
  const testDeidentification = async () => {
    if (!testContent.trim()) {
      toast({
        title: '请输入测试内容',
        description: '测试内容不能为空',
        variant: 'destructive',
      });
      return;
    }

    try {
      setTestLoading(true);

      // 检查是否为开发环境且使用模拟数据
      if (import.meta.env.DEV && import.meta.env.VITE_USE_MOCK === 'true') {
        console.log('使用模拟数据测试脱敏');

        // 模拟网络延迟
        await new Promise(resolve => setTimeout(resolve, 500));

        // 简单的模拟脱敏处理
        const sanitized = testContent
          .replace(/我是([^，。,.!?！？\s]{1,4})/g, '我是某位学生')
          .replace(/北京大学|清华大学|复旦大学|上海交通大学/g, '某高校')
          .replace(/阿里巴巴|腾讯|百度|字节跳动/g, '某公司');

        const modified = testContent !== sanitized;

        setTestResult({
          original: testContent,
          sanitized,
          modified,
          changes: modified ? {
            changeCount: 1,
            changeRatio: 0.2,
            examples: [
              {
                original: testContent,
                sanitized
              }
            ]
          } : undefined,
          config: {
            enabled: config.enabled,
            level: config.level,
            aiProvider: config.aiProvider
          }
        });

        return;
      }

      const response = await fetch('/api/admin/deidentification/test', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${localStorage.getItem('adminToken')}`
        },
        body: JSON.stringify({
          content: testContent,
          contentType: 'story'
        })
      });

      if (response.ok) {
        const data = await response.json();
        if (data.success) {
          setTestResult({
            original: data.original,
            sanitized: data.sanitized,
            modified: data.modified,
            warning: data.warning,
            changes: data.changes,
            config: data.config
          });
        }
      } else {
        console.error('Error testing deidentification, status:', response.status);
        toast({
          title: '测试失败',
          description: '无法测试脱敏功能',
          variant: 'destructive',
        });
      }
    } catch (error) {
      console.error('Error testing deidentification:', error);
      toast({
        title: '测试失败',
        description: '发生错误，请稍后重试',
        variant: 'destructive',
      });
    } finally {
      setTestLoading(false);
    }
  };

  // 处理配置变更
  const handleConfigChange = (key: keyof DeidentificationConfig, value: any) => {
    setConfig(prev => ({ ...prev, [key]: value }));
  };

  // 批量处理脱敏
  const startBatchProcessing = async () => {
    if (!config.enabled) {
      toast({
        title: '脱敏功能未启用',
        description: '请先在基本设置中启用脱敏功能',
        variant: 'destructive',
      });
      return;
    }

    if (batchFilter.contentType === 'story' && !config.applyToStories) {
      toast({
        title: '脱敏功能未应用于故事墙内容',
        description: '请先在基本设置中启用故事墙内容脱敏',
        variant: 'destructive',
      });
      return;
    }

    if (batchFilter.contentType === 'questionnaire' && !config.applyToQuestionnaires) {
      toast({
        title: '脱敏功能未应用于问卷回复',
        description: '请先在基本设置中启用问卷回复脱敏',
        variant: 'destructive',
      });
      return;
    }

    try {
      setBatchProcessing(true);
      setBatchStatus('processing');
      setBatchProgress(10);

      // 检查是否为开发环境且使用模拟数据
      if (import.meta.env.DEV && import.meta.env.VITE_USE_MOCK === 'true') {
        console.log('使用模拟数据进行批量脱敏处理');

        // 模拟进度更新
        setBatchProgress(30);
        await new Promise(resolve => setTimeout(resolve, 500));

        setBatchProgress(60);
        await new Promise(resolve => setTimeout(resolve, 500));

        setBatchProgress(100);

        // 创建模拟结果
        const mockResults = {
          success: true,
          processedCount: 10,
          totalCount: 10,
          results: Array(5).fill(0).map((_, i) => ({
            id: `story-${i + 1}`,
            modified: i % 2 === 0,
            original: `这是原始内容 ${i + 1}`,
            sanitized: `这是脱敏后的内容 ${i + 1}`
          }))
        };

        setBatchStatus('completed');
        setBatchResults(mockResults);

        toast({
          title: '批量处理完成',
          description: `已处理 ${mockResults.processedCount} 条内容，共 ${mockResults.totalCount} 条（模拟数据）`,
          variant: 'default',
        });

        setBatchProcessing(false);
        return;
      }

      const response = await fetch('/api/admin/deidentification/batch', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${localStorage.getItem('adminToken')}`
        },
        body: JSON.stringify({
          contentType: batchFilter.contentType,
          filter: {
            status: batchFilter.status,
            tags: batchFilter.tags.length > 0 ? batchFilter.tags : undefined
          }
        })
      });

      setBatchProgress(70);

      if (response.ok) {
        const data = await response.json();
        setBatchProgress(100);

        if (data.success) {
          setBatchStatus('completed');
          setBatchResults(data);

          toast({
            title: '批量处理完成',
            description: `已处理 ${data.processedCount} 条内容，共 ${data.totalCount} 条`,
            variant: 'default',
          });
        } else {
          setBatchStatus('error');

          toast({
            title: '批量处理失败',
            description: data.error || '发生未知错误',
            variant: 'destructive',
          });
        }
      } else {
        setBatchStatus('error');
        console.error('Error batch processing deidentification, status:', response.status);
        toast({
          title: '批量处理失败',
          description: '服务器返回错误',
          variant: 'destructive',
        });
      }
    } catch (error) {
      console.error('Error batch processing deidentification:', error);
      setBatchStatus('error');

      toast({
        title: '批量处理失败',
        description: '发生错误，请稍后重试',
        variant: 'destructive',
      });
    } finally {
      setBatchProcessing(false);
    }
  };

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <CardTitle>内容脱敏与防溯源保护</CardTitle>
          <CardDescription>
            配置系统如何处理用户生成内容中的个人可识别信息，保护用户隐私
          </CardDescription>
        </CardHeader>

        <CardContent>
          <Tabs value={activeTab} onValueChange={setActiveTab}>
            <TabsList className="grid w-full grid-cols-4">
              <TabsTrigger value="general">基本设置</TabsTrigger>
              <TabsTrigger value="advanced">高级设置</TabsTrigger>
              <TabsTrigger value="test">测试脱敏</TabsTrigger>
              <TabsTrigger value="batch">批量处理</TabsTrigger>
            </TabsList>

            <TabsContent value="general" className="space-y-4 mt-4">
              <div className="flex items-center justify-between">
                <div>
                  <Label htmlFor="enable-deidentification" className="text-base">启用内容脱敏</Label>
                  <p className="text-sm text-muted-foreground">
                    自动处理用户提交的内容，去除或替换可能暴露身份的信息
                  </p>
                </div>
                <Switch
                  id="enable-deidentification"
                  checked={config.enabled}
                  onCheckedChange={(checked) => handleConfigChange('enabled', checked)}
                />
              </div>

              <Separator className="my-4" />

              {config.enabled && (
                <>
                  <div className="space-y-4">
                    <div>
                      <Label htmlFor="deidentification-level">脱敏级别</Label>
                      <Select
                        value={config.level}
                        onValueChange={(value) => handleConfigChange('level', value)}
                      >
                        <SelectTrigger id="deidentification-level" className="mt-1">
                          <SelectValue placeholder="选择脱敏级别" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="low">低 - 仅替换明显的个人信息</SelectItem>
                          <SelectItem value="medium">中 - 替换大部分可识别信息</SelectItem>
                          <SelectItem value="high">高 - 最大程度脱敏和泛化</SelectItem>
                        </SelectContent>
                      </Select>
                      <p className="text-xs text-muted-foreground mt-1">
                        {config.level === 'low' && '仅替换明显的个人身份信息，如具体姓名、精确地点等'}
                        {config.level === 'medium' && '替换所有可能暴露身份的信息，包括人名、地点、学校、公司等'}
                        {config.level === 'high' && '最大程度脱敏，包括特定经历的泛化处理，确保内容无法被用于识别个人'}
                      </p>
                    </div>

                    <div className="space-y-2">
                      <Label>应用范围</Label>
                      <div className="flex items-center space-x-2">
                        <Switch
                          id="apply-to-stories"
                          checked={config.applyToStories}
                          onCheckedChange={(checked) => handleConfigChange('applyToStories', checked)}
                        />
                        <Label htmlFor="apply-to-stories">应用于故事墙内容</Label>
                      </div>

                      <div className="flex items-center space-x-2">
                        <Switch
                          id="apply-to-questionnaires"
                          checked={config.applyToQuestionnaires}
                          onCheckedChange={(checked) => handleConfigChange('applyToQuestionnaires', checked)}
                        />
                        <Label htmlFor="apply-to-questionnaires">应用于问卷回复</Label>
                      </div>
                    </div>

                    <div className="flex items-center space-x-2">
                      <Switch
                        id="admin-view-original"
                        checked={config.adminCanViewOriginal}
                        onCheckedChange={(checked) => handleConfigChange('adminCanViewOriginal', checked)}
                      />
                      <div>
                        <Label htmlFor="admin-view-original">管理员可查看原始内容</Label>
                        <p className="text-xs text-muted-foreground">
                          允许管理员查看脱敏前的原始内容（仅用于审核目的）
                        </p>
                      </div>
                    </div>
                  </div>
                </>
              )}
            </TabsContent>

            <TabsContent value="advanced" className="space-y-4 mt-4">
              <div>
                <Label htmlFor="ai-provider">AI 提供商</Label>
                <Select
                  value={config.aiProvider}
                  onValueChange={(value) => handleConfigChange('aiProvider', value as 'openai' | 'mock')}
                >
                  <SelectTrigger id="ai-provider" className="mt-1">
                    <SelectValue placeholder="选择 AI 提供商" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="mock">模拟脱敏（本地规则）</SelectItem>
                    <SelectItem value="openai">OpenAI</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              {config.aiProvider === 'openai' && (
                <>
                  <div>
                    <Label htmlFor="api-key">API 密钥</Label>
                    <Input
                      id="api-key"
                      type="password"
                      value={config.apiKey || ''}
                      onChange={(e) => handleConfigChange('apiKey', e.target.value)}
                      className="mt-1"
                      placeholder="sk-..."
                    />
                    <p className="text-xs text-muted-foreground mt-1">
                      API 密钥仅存储在服务器端，不会暴露给前端
                    </p>
                  </div>

                  <div>
                    <Label htmlFor="model">模型</Label>
                    <Select
                      value={config.model || 'gpt-4o'}
                      onValueChange={(value) => handleConfigChange('model', value)}
                    >
                      <SelectTrigger id="model" className="mt-1">
                        <SelectValue placeholder="选择模型" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="gpt-4o">GPT-4o</SelectItem>
                        <SelectItem value="gpt-4-turbo">GPT-4 Turbo</SelectItem>
                        <SelectItem value="gpt-3.5-turbo">GPT-3.5 Turbo</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                </>
              )}

              <div className="flex items-center space-x-2">
                <Switch
                  id="preserve-semantics"
                  checked={config.preserveSemantics}
                  onCheckedChange={(checked) => handleConfigChange('preserveSemantics', checked)}
                />
                <div>
                  <Label htmlFor="preserve-semantics">保留语义</Label>
                  <p className="text-xs text-muted-foreground">
                    在脱敏过程中尽量保留内容的原始语义和表达方式
                  </p>
                </div>
              </div>
            </TabsContent>

            <TabsContent value="test" className="space-y-4 mt-4">
              <div>
                <Label htmlFor="test-content">测试内容</Label>
                <Textarea
                  id="test-content"
                  value={testContent}
                  onChange={(e) => setTestContent(e.target.value)}
                  className="mt-1 min-h-[100px]"
                  placeholder="输入包含个人信息的测试内容..."
                />
              </div>

              <Button
                onClick={testDeidentification}
                disabled={testLoading || !config.enabled}
              >
                {testLoading ? '处理中...' : '测试脱敏'}
              </Button>

              {!config.enabled && (
                <Alert variant="warning">
                  <AlertCircle className="h-4 w-4" />
                  <AlertTitle>脱敏功能未启用</AlertTitle>
                  <AlertDescription>
                    请先在基本设置中启用脱敏功能
                  </AlertDescription>
                </Alert>
              )}

              {testResult && (
                <div className="space-y-4 mt-4">
                  {testResult.warning && (
                    <Alert variant="warning">
                      <AlertCircle className="h-4 w-4" />
                      <AlertTitle>警告</AlertTitle>
                      <AlertDescription>{testResult.warning}</AlertDescription>
                    </Alert>
                  )}

                  <Alert variant={testResult.modified ? "success" : "info"}>
                    {testResult.modified ? (
                      <CheckCircle2 className="h-4 w-4" />
                    ) : (
                      <Info className="h-4 w-4" />
                    )}
                    <AlertTitle>
                      {testResult.modified ? '内容已脱敏' : '内容未变化'}
                    </AlertTitle>
                    <AlertDescription>
                      {testResult.modified
                        ? `脱敏处理成功，内容中的个人信息已被替换。使用${testResult.config?.aiProvider === 'openai' ? 'OpenAI' : '本地规则'}进行${
                            testResult.config?.level === 'low' ? '低级别' :
                            testResult.config?.level === 'medium' ? '中级别' : '高级别'
                          }脱敏。`
                        : '内容未检测到需要脱敏的个人信息，或脱敏级别过低'}
                    </AlertDescription>
                  </Alert>

                  {testResult.changes && testResult.modified && (
                    <div className="space-y-2">
                      <h4 className="text-sm font-medium">变化统计</h4>
                      <div className="grid grid-cols-2 gap-2 text-sm">
                        <div className="flex items-center space-x-2">
                          <span className="font-medium">变化句子数:</span>
                          <span>{testResult.changes.changeCount}</span>
                        </div>
                        <div className="flex items-center space-x-2">
                          <span className="font-medium">变化比例:</span>
                          <span>{(testResult.changes.changeRatio * 100).toFixed(0)}%</span>
                        </div>
                      </div>

                      {testResult.changes.examples.length > 0 && (
                        <div className="mt-2">
                          <h4 className="text-sm font-medium mb-2">变化示例</h4>
                          <div className="space-y-2">
                            {testResult.changes.examples.map((example, index) => (
                              <div key={index} className="grid grid-cols-2 gap-2 text-xs">
                                <div className="p-2 border rounded bg-red-50">
                                  <span className="font-medium text-red-600">原始: </span>
                                  {example.original}
                                </div>
                                <div className="p-2 border rounded bg-green-50">
                                  <span className="font-medium text-green-600">脱敏: </span>
                                  {example.sanitized}
                                </div>
                              </div>
                            ))}
                          </div>
                        </div>
                      )}
                    </div>
                  )}

                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <Label>原始内容</Label>
                      <div className="p-3 border rounded-md mt-1 bg-muted overflow-auto max-h-40">
                        {testResult.original}
                      </div>
                    </div>

                    <div>
                      <Label>脱敏后内容</Label>
                      <div className="p-3 border rounded-md mt-1 bg-muted overflow-auto max-h-40">
                        {testResult.sanitized}
                      </div>
                    </div>
                  </div>
                </div>
              )}
            </TabsContent>

            <TabsContent value="batch" className="space-y-4 mt-4">
              <div className="space-y-4">
                <Alert variant={config.enabled ? "info" : "warning"}>
                  <Info className="h-4 w-4" />
                  <AlertTitle>批量处理说明</AlertTitle>
                  <AlertDescription>
                    批量处理功能可以对已有内容进行脱敏处理。处理过程可能需要一些时间，请耐心等待。
                    {!config.enabled && ' 请先在基本设置中启用脱敏功能。'}
                  </AlertDescription>
                </Alert>

                <div>
                  <Label>内容类型</Label>
                  <RadioGroup
                    value={batchFilter.contentType}
                    onValueChange={(value) => setBatchFilter({...batchFilter, contentType: value})}
                    className="flex space-x-4 mt-2"
                  >
                    <div className="flex items-center space-x-2">
                      <RadioGroupItem value="story" id="content-type-story" />
                      <Label htmlFor="content-type-story">故事墙内容</Label>
                    </div>
                    <div className="flex items-center space-x-2">
                      <RadioGroupItem value="questionnaire" id="content-type-questionnaire" />
                      <Label htmlFor="content-type-questionnaire">问卷回复</Label>
                    </div>
                  </RadioGroup>
                </div>

                <div>
                  <Label>内容状态</Label>
                  <RadioGroup
                    value={batchFilter.status}
                    onValueChange={(value) => setBatchFilter({...batchFilter, status: value})}
                    className="flex space-x-4 mt-2"
                  >
                    <div className="flex items-center space-x-2">
                      <RadioGroupItem value="all" id="status-all" />
                      <Label htmlFor="status-all">全部</Label>
                    </div>
                    <div className="flex items-center space-x-2">
                      <RadioGroupItem value="pending" id="status-pending" />
                      <Label htmlFor="status-pending">待审核</Label>
                    </div>
                    <div className="flex items-center space-x-2">
                      <RadioGroupItem value="approved" id="status-approved" />
                      <Label htmlFor="status-approved">已通过</Label>
                    </div>
                  </RadioGroup>
                </div>

                <Button
                  onClick={startBatchProcessing}
                  disabled={batchProcessing || !config.enabled}
                  className="mt-2"
                >
                  {batchProcessing ? (
                    <>
                      <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                      处理中...
                    </>
                  ) : '开始批量处理'}
                </Button>

                {batchStatus === 'processing' && (
                  <div className="space-y-2">
                    <Label>处理进度</Label>
                    <Progress value={batchProgress} className="w-full" />
                  </div>
                )}

                {batchResults && batchStatus === 'completed' && (
                  <div className="space-y-4 mt-4">
                    <Alert variant="success">
                      <CheckCircle2 className="h-4 w-4" />
                      <AlertTitle>处理完成</AlertTitle>
                      <AlertDescription>
                        已处理 {batchResults.processedCount} 条内容，共 {batchResults.totalCount} 条
                        {batchResults.limitApplied && '（已应用50条处理限制）'}
                      </AlertDescription>
                    </Alert>

                    <div className="space-y-2">
                      <h4 className="text-sm font-medium">处理结果统计</h4>
                      <div className="flex space-x-4">
                        <Badge variant="outline" className="text-green-600 bg-green-50">
                          已修改: {batchResults.results.filter(r => r.modified).length}
                        </Badge>
                        <Badge variant="outline" className="text-blue-600 bg-blue-50">
                          未变化: {batchResults.results.filter(r => r.modified === false).length}
                        </Badge>
                        <Badge variant="outline" className="text-red-600 bg-red-50">
                          失败: {batchResults.results.filter(r => r.error).length}
                        </Badge>
                      </div>
                    </div>

                    {batchResults.results.length > 0 && (
                      <div className="space-y-2">
                        <h4 className="text-sm font-medium">处理详情（最多显示5条）</h4>
                        <div className="space-y-2 max-h-60 overflow-auto">
                          {batchResults.results.slice(0, 5).map((result, index) => (
                            <div key={index} className="p-3 border rounded-md text-sm">
                              <div className="flex justify-between items-center">
                                <span className="font-medium">ID: {result.id}</span>
                                {result.modified ? (
                                  <Badge className="bg-green-100 text-green-800 hover:bg-green-100">已修改</Badge>
                                ) : result.error ? (
                                  <Badge className="bg-red-100 text-red-800 hover:bg-red-100">处理失败</Badge>
                                ) : (
                                  <Badge className="bg-blue-100 text-blue-800 hover:bg-blue-100">未变化</Badge>
                                )}
                              </div>

                              {result.error && (
                                <div className="mt-2 text-red-600 text-xs">
                                  错误: {result.errorDetails || result.error}
                                </div>
                              )}

                              {result.modified && (
                                <div className="grid grid-cols-2 gap-2 mt-2 text-xs">
                                  <div className="p-2 border rounded bg-red-50">
                                    <span className="font-medium text-red-600">原始: </span>
                                    {result.original.length > 100 ?
                                      result.original.substring(0, 100) + '...' :
                                      result.original}
                                  </div>
                                  <div className="p-2 border rounded bg-green-50">
                                    <span className="font-medium text-green-600">脱敏: </span>
                                    {result.sanitized.length > 100 ?
                                      result.sanitized.substring(0, 100) + '...' :
                                      result.sanitized}
                                  </div>
                                </div>
                              )}
                            </div>
                          ))}
                        </div>
                      </div>
                    )}
                  </div>
                )}

                {batchStatus === 'error' && (
                  <Alert variant="destructive">
                    <AlertCircle className="h-4 w-4" />
                    <AlertTitle>处理失败</AlertTitle>
                    <AlertDescription>
                      批量处理过程中发生错误，请稍后重试
                    </AlertDescription>
                  </Alert>
                )}
              </div>
            </TabsContent>
          </Tabs>
        </CardContent>

        <CardFooter className="flex justify-between">
          <Button variant="outline" onClick={() => window.location.reload()}>
            重置
          </Button>
          <Button onClick={saveConfig} disabled={loading}>
            {loading ? '保存中...' : '保存配置'}
          </Button>
        </CardFooter>
      </Card>
    </div>
  );
};

export default DeidentificationSettings;
