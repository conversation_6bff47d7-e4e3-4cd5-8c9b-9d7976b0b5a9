/**
 * API缓存服务
 * 提供内存缓存和KV存储缓存功能
 */

const { recordCacheAccess } = require('./performance');

// 缓存配置
const DEFAULT_TTL = 5 * 60; // 默认5分钟（秒）
const MAX_MEMORY_ITEMS = 1000; // 最大内存缓存项数

// 内存缓存
const memoryCache = new Map();

// 缓存统计
const cacheStats = {
  memoryHits: 0,
  memoryMisses: 0,
  kvHits: 0,
  kvMisses: 0,
  memorySize: 0,
  evictions: 0,
};

/**
 * 生成缓存键
 * @param {string} namespace 命名空间
 * @param {string} key 键
 * @returns {string} 缓存键
 */
function generateCacheKey(namespace, key) {
  return `${namespace}:${key}`;
}

/**
 * 设置内存缓存
 * @param {string} namespace 命名空间
 * @param {string} key 键
 * @param {*} value 值
 * @param {number} ttl 过期时间（秒）
 */
function setMemoryCache(namespace, key, value, ttl = DEFAULT_TTL) {
  const cacheKey = generateCacheKey(namespace, key);
  
  // 如果缓存已满，清理最旧的项
  if (memoryCache.size >= MAX_MEMORY_ITEMS && !memoryCache.has(cacheKey)) {
    evictOldestItem();
  }
  
  const now = Math.floor(Date.now() / 1000);
  
  memoryCache.set(cacheKey, {
    value,
    expiry: now + ttl,
    createdAt: now,
    lastAccessed: now,
    accessCount: 0,
  });
  
  cacheStats.memorySize = memoryCache.size;
}

/**
 * 获取内存缓存
 * @param {string} namespace 命名空间
 * @param {string} key 键
 * @returns {*} 缓存值，如果不存在或已过期则返回null
 */
function getMemoryCache(namespace, key) {
  const cacheKey = generateCacheKey(namespace, key);
  const item = memoryCache.get(cacheKey);
  
  if (!item) {
    cacheStats.memoryMisses++;
    recordCacheAccess(false);
    return null;
  }
  
  const now = Math.floor(Date.now() / 1000);
  
  // 检查是否过期
  if (item.expiry < now) {
    memoryCache.delete(cacheKey);
    cacheStats.memoryMisses++;
    recordCacheAccess(false);
    cacheStats.memorySize = memoryCache.size;
    return null;
  }
  
  // 更新访问统计
  item.lastAccessed = now;
  item.accessCount++;
  
  cacheStats.memoryHits++;
  recordCacheAccess(true);
  
  return item.value;
}

/**
 * 删除内存缓存
 * @param {string} namespace 命名空间
 * @param {string} key 键
 * @returns {boolean} 是否删除成功
 */
function deleteMemoryCache(namespace, key) {
  const cacheKey = generateCacheKey(namespace, key);
  const result = memoryCache.delete(cacheKey);
  cacheStats.memorySize = memoryCache.size;
  return result;
}

/**
 * 清理命名空间下的所有内存缓存
 * @param {string} namespace 命名空间
 * @returns {number} 清理的缓存项数量
 */
function clearNamespaceMemoryCache(namespace) {
  let count = 0;
  const prefix = `${namespace}:`;
  
  for (const key of memoryCache.keys()) {
    if (key.startsWith(prefix)) {
      memoryCache.delete(key);
      count++;
    }
  }
  
  cacheStats.memorySize = memoryCache.size;
  return count;
}

/**
 * 清理所有内存缓存
 */
function clearAllMemoryCache() {
  memoryCache.clear();
  cacheStats.memorySize = 0;
}

/**
 * 清理过期的内存缓存
 * @returns {number} 清理的缓存项数量
 */
function cleanExpiredMemoryCache() {
  let count = 0;
  const now = Math.floor(Date.now() / 1000);
  
  for (const [key, item] of memoryCache.entries()) {
    if (item.expiry < now) {
      memoryCache.delete(key);
      count++;
    }
  }
  
  cacheStats.memorySize = memoryCache.size;
  return count;
}

/**
 * 驱逐最旧的缓存项
 * @param {number} count 要驱逐的数量
 * @returns {number} 实际驱逐的数量
 */
function evictOldestItem(count = 1) {
  // 按最后访问时间和访问次数排序
  const entries = [...memoryCache.entries()].sort((a, b) => {
    // 首先按访问次数排序
    const accessDiff = a[1].accessCount - b[1].accessCount;
    if (accessDiff !== 0) return accessDiff;
    
    // 然后按最后访问时间排序
    return a[1].lastAccessed - b[1].lastAccessed;
  });
  
  let evicted = 0;
  for (let i = 0; i < Math.min(count, entries.length); i++) {
    memoryCache.delete(entries[i][0]);
    evicted++;
  }
  
  cacheStats.evictions += evicted;
  cacheStats.memorySize = memoryCache.size;
  
  return evicted;
}

/**
 * 设置KV缓存
 * @param {Object} env 环境对象
 * @param {string} namespace 命名空间
 * @param {string} key 键
 * @param {*} value 值
 * @param {number} ttl 过期时间（秒）
 * @returns {Promise<boolean>} 是否设置成功
 */
async function setKVCache(env, namespace, key, value, ttl = DEFAULT_TTL) {
  if (!env.CACHE_KV) {
    return false;
  }
  
  const cacheKey = generateCacheKey(namespace, key);
  
  try {
    await env.CACHE_KV.put(
      cacheKey, 
      JSON.stringify(value), 
      { expirationTtl: ttl }
    );
    return true;
  } catch (error) {
    console.error('设置KV缓存失败:', error);
    return false;
  }
}

/**
 * 获取KV缓存
 * @param {Object} env 环境对象
 * @param {string} namespace 命名空间
 * @param {string} key 键
 * @returns {Promise<*>} 缓存值，如果不存在则返回null
 */
async function getKVCache(env, namespace, key) {
  if (!env.CACHE_KV) {
    cacheStats.kvMisses++;
    recordCacheAccess(false);
    return null;
  }
  
  const cacheKey = generateCacheKey(namespace, key);
  
  try {
    const cachedValue = await env.CACHE_KV.get(cacheKey, { type: 'json' });
    
    if (cachedValue === null) {
      cacheStats.kvMisses++;
      recordCacheAccess(false);
      return null;
    }
    
    cacheStats.kvHits++;
    recordCacheAccess(true);
    
    return cachedValue;
  } catch (error) {
    console.error('获取KV缓存失败:', error);
    cacheStats.kvMisses++;
    recordCacheAccess(false);
    return null;
  }
}

/**
 * 删除KV缓存
 * @param {Object} env 环境对象
 * @param {string} namespace 命名空间
 * @param {string} key 键
 * @returns {Promise<boolean>} 是否删除成功
 */
async function deleteKVCache(env, namespace, key) {
  if (!env.CACHE_KV) {
    return false;
  }
  
  const cacheKey = generateCacheKey(namespace, key);
  
  try {
    await env.CACHE_KV.delete(cacheKey);
    return true;
  } catch (error) {
    console.error('删除KV缓存失败:', error);
    return false;
  }
}

/**
 * 获取缓存（先检查内存缓存，然后检查KV缓存）
 * @param {Object} env 环境对象
 * @param {string} namespace 命名空间
 * @param {string} key 键
 * @returns {Promise<*>} 缓存值，如果不存在则返回null
 */
async function getCache(env, namespace, key) {
  // 先检查内存缓存
  const memoryValue = getMemoryCache(namespace, key);
  if (memoryValue !== null) {
    return memoryValue;
  }
  
  // 然后检查KV缓存
  const kvValue = await getKVCache(env, namespace, key);
  
  // 如果KV缓存存在，也放入内存缓存
  if (kvValue !== null) {
    setMemoryCache(namespace, key, kvValue);
  }
  
  return kvValue;
}

/**
 * 设置缓存（同时设置内存缓存和KV缓存）
 * @param {Object} env 环境对象
 * @param {string} namespace 命名空间
 * @param {string} key 键
 * @param {*} value 值
 * @param {number} ttl 过期时间（秒）
 * @returns {Promise<boolean>} 是否设置成功
 */
async function setCache(env, namespace, key, value, ttl = DEFAULT_TTL) {
  // 设置内存缓存
  setMemoryCache(namespace, key, value, ttl);
  
  // 设置KV缓存
  return await setKVCache(env, namespace, key, value, ttl);
}

/**
 * 删除缓存（同时删除内存缓存和KV缓存）
 * @param {Object} env 环境对象
 * @param {string} namespace 命名空间
 * @param {string} key 键
 * @returns {Promise<boolean>} 是否删除成功
 */
async function deleteCache(env, namespace, key) {
  // 删除内存缓存
  const memoryResult = deleteMemoryCache(namespace, key);
  
  // 删除KV缓存
  const kvResult = await deleteKVCache(env, namespace, key);
  
  return memoryResult || kvResult;
}

/**
 * 获取缓存统计信息
 * @returns {Object} 缓存统计信息
 */
function getCacheStats() {
  const totalMemoryAccesses = cacheStats.memoryHits + cacheStats.memoryMisses;
  const totalKVAccesses = cacheStats.kvHits + cacheStats.kvMisses;
  
  return {
    ...cacheStats,
    memoryHitRate: totalMemoryAccesses > 0 ? cacheStats.memoryHits / totalMemoryAccesses : 0,
    kvHitRate: totalKVAccesses > 0 ? cacheStats.kvHits / totalKVAccesses : 0,
    totalHits: cacheStats.memoryHits + cacheStats.kvHits,
    totalMisses: cacheStats.memoryMisses + cacheStats.kvMisses,
    totalHitRate: (totalMemoryAccesses + totalKVAccesses) > 0 
      ? (cacheStats.memoryHits + cacheStats.kvHits) / (totalMemoryAccesses + totalKVAccesses) 
      : 0
  };
}

// 定期清理过期缓存
setInterval(cleanExpiredMemoryCache, 60 * 1000); // 每分钟清理一次

module.exports = {
  setMemoryCache,
  getMemoryCache,
  deleteMemoryCache,
  clearNamespaceMemoryCache,
  clearAllMemoryCache,
  setKVCache,
  getKVCache,
  deleteKVCache,
  getCache,
  setCache,
  deleteCache,
  getCacheStats,
  cleanExpiredMemoryCache
};
