/**
 * 错误处理服务
 * 
 * 提供全局错误处理、日志记录和错误报告功能
 */

// 错误严重程度级别
export enum ErrorSeverity {
  INFO = 'info',
  WARNING = 'warning',
  ERROR = 'error',
  CRITICAL = 'critical'
}

// 错误上下文接口
export interface ErrorContext {
  component?: string;
  action?: string;
  userId?: string;
  url?: string;
  additionalData?: Record<string, any>;
}

// 错误日志接口
export interface ErrorLog {
  message: string;
  stack?: string;
  severity: ErrorSeverity;
  timestamp: string;
  context: ErrorContext;
}

/**
 * 错误处理服务
 */
class ErrorService {
  private logs: ErrorLog[] = [];
  private maxLogs: number = 100;
  private isInitialized: boolean = false;

  /**
   * 初始化错误处理服务
   */
  public initialize(): void {
    if (this.isInitialized) {
      return;
    }

    // 设置全局错误处理
    window.addEventListener('error', this.handleGlobalError);
    window.addEventListener('unhandledrejection', this.handleUnhandledRejection);

    // 覆盖控制台错误方法
    const originalConsoleError = console.error;
    console.error = (...args: any[]) => {
      originalConsoleError.apply(console, args);
      this.logError(args.join(' '), ErrorSeverity.ERROR);
    };

    this.isInitialized = true;
    console.log('错误处理服务已初始化');
  }

  /**
   * 处理全局错误
   */
  private handleGlobalError = (event: ErrorEvent): void => {
    this.logError(
      event.message || '未知错误',
      ErrorSeverity.ERROR,
      event.error?.stack,
      {
        url: window.location.href,
        component: 'global'
      }
    );
  };

  /**
   * 处理未捕获的 Promise 拒绝
   */
  private handleUnhandledRejection = (event: PromiseRejectionEvent): void => {
    const error = event.reason;
    this.logError(
      error?.message || '未处理的 Promise 拒绝',
      ErrorSeverity.ERROR,
      error?.stack,
      {
        url: window.location.href,
        component: 'promise'
      }
    );
  };

  /**
   * 记录错误
   */
  public logError(
    message: string,
    severity: ErrorSeverity = ErrorSeverity.ERROR,
    stack?: string,
    context: ErrorContext = {}
  ): void {
    const errorLog: ErrorLog = {
      message,
      stack,
      severity,
      timestamp: new Date().toISOString(),
      context: {
        ...context,
        url: context.url || window.location.href
      }
    };

    // 添加到本地日志
    this.logs.unshift(errorLog);
    
    // 限制日志数量
    if (this.logs.length > this.maxLogs) {
      this.logs = this.logs.slice(0, this.maxLogs);
    }

    // 在开发环境中打印错误
    if (import.meta.env.DEV) {
      console.group('错误日志');
      console.log('消息:', errorLog.message);
      console.log('严重程度:', errorLog.severity);
      console.log('时间戳:', errorLog.timestamp);
      console.log('上下文:', errorLog.context);
      if (errorLog.stack) {
        console.log('堆栈:', errorLog.stack);
      }
      console.groupEnd();
    }

    // 在生产环境中可以将错误发送到服务器
    if (import.meta.env.PROD) {
      // TODO: 实现错误上报逻辑
      // this.sendErrorToServer(errorLog);
    }
  }

  /**
   * 获取所有错误日志
   */
  public getLogs(): ErrorLog[] {
    return [...this.logs];
  }

  /**
   * 清除所有错误日志
   */
  public clearLogs(): void {
    this.logs = [];
  }
}

// 创建单例实例
export const errorService = new ErrorService();

export default errorService;
