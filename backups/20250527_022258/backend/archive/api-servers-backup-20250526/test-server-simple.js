/**
 * 简单的测试服务器
 * 用于验证数据转换和API功能
 */

const express = require('express');
const cors = require('cors');
const { PrismaClient } = require('@prisma/client');

const app = express();
const prisma = new PrismaClient();
const PORT = 8787;

// 中间件
app.use(cors({
  origin: [
    'http://localhost:5173',
    'http://localhost:5174',
    'http://localhost:5175',
    'http://localhost:5176',
    'http://localhost:5177'
  ],
  credentials: true
}));
app.use(express.json());

// 数据转换服务 (简化版)
class DataTransformService {
  static transformQuestionnaireToFrontend(response) {
    return {
      id: response.id,
      sequenceNumber: response.sequenceNumber,
      isAnonymous: response.isAnonymous,
      educationLevel: response.educationLevel || undefined,
      major: response.major || undefined,
      graduationYear: response.graduationYear || undefined,
      region: response.region || undefined,
      employmentStatus: response.employmentStatus || undefined,
      industry: response.currentIndustry || undefined,
      position: response.currentPosition || undefined,
      salary: response.monthlySalary ? response.monthlySalary.toString() : undefined,
      jobSatisfaction: response.jobSatisfaction || undefined,
      unemploymentDuration: response.unemploymentDuration || undefined,
      careerChangeIntention: response.careerChangeIntention || undefined,
      challenges: response.observationOnEmployment || undefined,
      suggestions: response.adviceForStudents || undefined,
      createdAt: response.createdAt.toISOString(),
      updatedAt: response.updatedAt?.toISOString(),
      ipAddress: response.ipAddress || undefined,
      tags: response.tags ? JSON.parse(response.tags) : undefined,
      status: response.status
    };
  }

  static transformStoryToFrontend(story) {
    let author = story.author || '匿名用户';
    if (!story.isAnonymous && story.user) {
      author = story.user.name || story.user.username || story.user.email;
    }

    return {
      id: story.id,
      title: story.title,
      content: story.content,
      author: author,
      createdAt: story.createdAt.toISOString(),
      likes: story.likes,
      dislikes: story.dislikes,
      tags: story.tags ? JSON.parse(story.tags) : [],
      category: story.category || undefined,
      educationLevel: story.educationLevel || undefined,
      industry: story.industry || undefined
    };
  }
}

// 健康检查
app.get('/health', (req, res) => {
  res.json({ status: 'ok', environment: 'development' });
});

// 管理员登录
app.post('/api/admin/login', async (req, res) => {
  try {
    const { username, password } = req.body;

    console.log('管理员登录尝试:', username);

    // 简单的硬编码验证（实际应用中应该使用数据库和加密）
    if (username === 'admin' && password === 'admin123') {
      const token = 'mock-jwt-token-' + Date.now();

      res.json({
        success: true,
        token: token,
        user: {
          id: 1,
          username: 'admin',
          role: 'admin'
        }
      });
    } else {
      res.status(401).json({
        success: false,
        error: '用户名或密码错误'
      });
    }

  } catch (error) {
    console.error('管理员登录时发生错误:', error);
    res.status(500).json({
      success: false,
      error: '登录失败'
    });
  }
});

// 待审核故事
app.get('/api/admin/stories/pending', async (req, res) => {
  try {
    console.log('获取待审核故事');

    const page = parseInt(req.query.page || '1');
    const pageSize = parseInt(req.query.pageSize || '10');
    const skip = (page - 1) * pageSize;

    // 获取待审核的内容
    const pendingContents = await prisma.pendingContent.findMany({
      where: {
        status: 'pending',
        type: 'story'
      },
      skip,
      take: pageSize,
      orderBy: { createdAt: 'desc' }
    });

    const total = await prisma.pendingContent.count({
      where: {
        status: 'pending',
        type: 'story'
      }
    });

    res.json({
      success: true,
      data: pendingContents,
      pagination: {
        page,
        pageSize,
        total,
        totalPages: Math.ceil(total / pageSize)
      }
    });

  } catch (error) {
    console.error('获取待审核故事时发生错误:', error);
    res.status(500).json({
      success: false,
      error: '获取待审核故事失败'
    });
  }
});

// 文档列表
app.get('/api/documentation', async (req, res) => {
  try {
    console.log('获取文档列表');

    // 模拟文档数据
    const mockDocuments = [
      {
        id: 1,
        title: '项目概述',
        content: '这是一个大学生就业调研项目...',
        category: 'overview',
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString()
      },
      {
        id: 2,
        title: 'API文档',
        content: '本项目提供的API接口说明...',
        category: 'api',
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString()
      }
    ];

    res.json({
      success: true,
      data: mockDocuments
    });

  } catch (error) {
    console.error('获取文档列表时发生错误:', error);
    res.status(500).json({
      success: false,
      error: '获取文档列表失败'
    });
  }
});

// 标签列表
app.get('/api/tags', async (req, res) => {
  try {
    console.log('获取标签列表');

    // 模拟标签数据
    const mockTags = [
      { id: 1, name: '就业困难', color: '#ff6b6b', priority: 10 },
      { id: 2, name: '求职经验', color: '#4ecdc4', priority: 9 },
      { id: 3, name: '职场新人', color: '#45b7d1', priority: 8 },
      { id: 4, name: '专业选择', color: '#96ceb4', priority: 7 },
      { id: 5, name: '实习经历', color: '#feca57', priority: 6 },
      { id: 6, name: '考研经历', color: '#ff9ff3', priority: 5 },
      { id: 7, name: '创业故事', color: '#54a0ff', priority: 4 },
      { id: 8, name: '转行经历', color: '#5f27cd', priority: 3 }
    ];

    res.json({
      success: true,
      data: mockTags
    });

  } catch (error) {
    console.error('获取标签列表时发生错误:', error);
    res.status(500).json({
      success: false,
      error: '获取标签列表失败'
    });
  }
});

// 问卷统计
app.get('/api/questionnaire/stats', async (req, res) => {
  try {
    console.log('获取问卷统计数据');

    const totalResponses = await prisma.questionnaireResponse.count();
    const verifiedResponses = await prisma.questionnaireResponse.count({
      where: { status: 'verified' }
    });
    const anonymousResponses = await prisma.questionnaireResponse.count({
      where: { isAnonymous: true }
    });

    const educationLevels = await prisma.questionnaireResponse.groupBy({
      by: ['educationLevel'],
      _count: { educationLevel: true },
      where: { educationLevel: { not: null } }
    });

    const regions = await prisma.questionnaireResponse.groupBy({
      by: ['region'],
      _count: { region: true },
      where: { region: { not: null } }
    });

    const employmentStatus = await prisma.questionnaireResponse.groupBy({
      by: ['employmentStatus'],
      _count: { employmentStatus: true },
      where: { employmentStatus: { not: null } }
    });

    // 计算百分比的辅助函数
    const calculatePercentages = (items, total) => {
      return items.map(item => ({
        code: item.name.toLowerCase().replace(/[\/\s]/g, '_'),
        name: item.name,
        count: item.count,
        percentage: total > 0 ? Math.round((item.count / total) * 100) : 0
      }));
    };

    // 格式化教育水平数据
    const formattedEducationLevels = calculatePercentages(
      educationLevels.map(level => ({
        name: level.educationLevel || '未知',
        count: level._count.educationLevel,
      })),
      totalResponses
    );

    // 格式化地区数据
    const formattedRegions = calculatePercentages(
      regions.map(region => ({
        name: region.region || '未知',
        count: region._count.region,
      })),
      totalResponses
    );

    // 格式化就业状态数据
    const formattedEmploymentStatus = calculatePercentages(
      employmentStatus.map(status => ({
        name: status.employmentStatus || '未知',
        count: status._count.employmentStatus,
      })),
      totalResponses
    );

    // 查询专业分布
    const majors = await prisma.questionnaireResponse.groupBy({
      by: ['major'],
      _count: { major: true },
      where: { major: { not: null, not: '' } }
    });

    // 查询行业分布
    const industries = await prisma.questionnaireResponse.groupBy({
      by: ['currentIndustry'],
      _count: { currentIndustry: true },
      where: { currentIndustry: { not: null, not: '' } }
    });

    // 查询薪资范围分布（需要从monthlySalary转换为范围）
    const salaryData = await prisma.questionnaireResponse.findMany({
      where: { monthlySalary: { not: null } },
      select: { monthlySalary: true }
    });

    // 将薪资转换为范围
    const salaryRanges = {};
    salaryData.forEach(item => {
      const salary = item.monthlySalary;
      let range;
      if (salary < 3000) range = '3000以下';
      else if (salary < 5000) range = '3000-5000';
      else if (salary < 8000) range = '5000-8000';
      else if (salary < 12000) range = '8000-12000';
      else if (salary < 20000) range = '12000-20000';
      else range = '20000以上';

      salaryRanges[range] = (salaryRanges[range] || 0) + 1;
    });

    // 格式化专业数据
    const formattedMajors = calculatePercentages(
      majors.map(major => ({
        name: major.major || '未知',
        count: major._count.major,
      })),
      totalResponses
    );

    // 格式化行业数据
    const formattedIndustries = calculatePercentages(
      industries.map(industry => ({
        name: industry.currentIndustry || '未知',
        count: industry._count.currentIndustry,
      })),
      totalResponses
    );

    // 查询毕业年份分布
    const graduationYears = await prisma.questionnaireResponse.groupBy({
      by: ['graduationYear'],
      _count: { graduationYear: true },
      where: { graduationYear: { not: null } }
    });

    // 格式化薪资范围数据
    const formattedSalaryRanges = calculatePercentages(
      Object.entries(salaryRanges).map(([range, count]) => ({
        name: range,
        count: count,
      })),
      totalResponses
    );

    // 格式化毕业年份数据
    const formattedGraduationYears = calculatePercentages(
      graduationYears.map(year => ({
        name: year.graduationYear ? year.graduationYear.toString() : '未知',
        count: year._count.graduationYear,
      })),
      totalResponses
    );

    const statistics = {
      totalResponses,
      verifiedCount: verifiedResponses,
      anonymousCount: anonymousResponses,
      educationLevels: formattedEducationLevels,
      regions: formattedRegions,
      majors: formattedMajors,
      industries: formattedIndustries,
      employmentStatus: formattedEmploymentStatus,
      salaryRanges: formattedSalaryRanges,
      graduationYears: formattedGraduationYears
    };

    res.json({
      success: true,
      statistics: statistics,
      metadata: {
        lastUpdated: new Date().toISOString(),
        dataVersion: '1.0.0',
        cacheExpiry: 30000
      }
    });

  } catch (error) {
    console.error('获取问卷统计时发生错误:', error);
    res.status(500).json({
      success: false,
      error: '获取统计数据失败'
    });
  }
});

// 实时统计API (for QuestionnaireRealtimeStats component)
app.get('/api/questionnaire/realtime-stats', async (req, res) => {
  try {
    console.log('获取实时统计数据');

    const totalSubmissions = await prisma.questionnaireResponse.count();

    const educationLevels = await prisma.questionnaireResponse.groupBy({
      by: ['educationLevel'],
      _count: { educationLevel: true },
      where: { educationLevel: { not: null } }
    });

    const regions = await prisma.questionnaireResponse.groupBy({
      by: ['region'],
      _count: { region: true },
      where: { region: { not: null } }
    });

    const employmentStatus = await prisma.questionnaireResponse.groupBy({
      by: ['employmentStatus'],
      _count: { employmentStatus: true },
      where: { employmentStatus: { not: null } }
    });

    // 查询专业分布
    const majors = await prisma.questionnaireResponse.groupBy({
      by: ['major'],
      _count: { major: true },
      where: { major: { not: null, not: '' } }
    });

    // 查询行业分布
    const industries = await prisma.questionnaireResponse.groupBy({
      by: ['currentIndustry'],
      _count: { currentIndustry: true },
      where: { currentIndustry: { not: null, not: '' } }
    });

    // 查询毕业年份分布
    const graduationYears = await prisma.questionnaireResponse.groupBy({
      by: ['graduationYear'],
      _count: { graduationYear: true },
      where: { graduationYear: { not: null } }
    });

    // 计算百分比的辅助函数
    const calculatePercentages = (items, total) => {
      return items.map(item => ({
        code: item.name.toLowerCase().replace(/[\/\s]/g, '_'),
        name: item.name,
        count: item.count,
        percentage: total > 0 ? Math.round((item.count / total) * 100) : 0
      }));
    };

    // 格式化数据
    const formattedEducationLevels = calculatePercentages(
      educationLevels.map(level => ({
        name: level.educationLevel || '未知',
        count: level._count.educationLevel,
      })),
      totalSubmissions
    );

    const formattedRegions = calculatePercentages(
      regions.map(region => ({
        name: region.region || '未知',
        count: region._count.region,
      })),
      totalSubmissions
    );

    const formattedEmploymentStatus = calculatePercentages(
      employmentStatus.map(status => ({
        name: status.employmentStatus || '未知',
        count: status._count.employmentStatus,
      })),
      totalSubmissions
    );

    // 格式化专业数据
    const formattedMajors = calculatePercentages(
      majors.map(major => ({
        name: major.major || '未知',
        count: major._count.major,
      })),
      totalSubmissions
    );

    // 格式化行业数据
    const formattedIndustries = calculatePercentages(
      industries.map(industry => ({
        name: industry.currentIndustry || '未知',
        count: industry._count.currentIndustry,
      })),
      totalSubmissions
    );

    // 格式化毕业年份数据
    const formattedGraduationYears = calculatePercentages(
      graduationYears.map(year => ({
        name: year.graduationYear ? year.graduationYear.toString() : '未知',
        count: year._count.graduationYear,
      })),
      totalSubmissions
    );

    res.json({
      success: true,
      totalSubmissions,
      questionStats: {
        education_level: formattedEducationLevels,
        region: formattedRegions,
        employment_status: formattedEmploymentStatus,
        major: formattedMajors,
        industry: formattedIndustries,
        graduation_year: formattedGraduationYears
      },
      metadata: {
        lastUpdated: new Date().toISOString(),
        refreshInterval: 30000
      }
    });

  } catch (error) {
    console.error('获取实时统计数据时发生错误:', error);
    res.status(500).json({
      success: false,
      error: '获取实时统计数据失败'
    });
  }
});

// 问卷列表
app.get('/api/questionnaire', async (req, res) => {
  try {
    const page = parseInt(req.query.page || '1');
    const pageSize = parseInt(req.query.pageSize || '10');
    const skip = (page - 1) * pageSize;

    const filters = {};
    if (req.query.educationLevel) filters.educationLevel = req.query.educationLevel;
    if (req.query.employmentStatus) filters.employmentStatus = req.query.employmentStatus;
    if (req.query.region) filters.region = req.query.region;
    if (req.query.isAnonymous !== undefined) filters.isAnonymous = req.query.isAnonymous === 'true';

    const responses = await prisma.questionnaireResponse.findMany({
      where: filters,
      skip,
      take: pageSize,
      orderBy: { createdAt: 'desc' },
      include: {
        user: {
          select: {
            id: true,
            email: true,
            name: true,
            username: true
          }
        }
      }
    });

    const total = await prisma.questionnaireResponse.count({ where: filters });

    const frontendResponses = responses.map(response =>
      DataTransformService.transformQuestionnaireToFrontend(response)
    );

    res.json({
      success: true,
      data: frontendResponses,
      pagination: {
        page,
        pageSize,
        total,
        totalPages: Math.ceil(total / pageSize)
      }
    });

  } catch (error) {
    console.error('获取问卷回复列表时发生错误:', error);
    res.status(500).json({
      success: false,
      error: '获取问卷回复列表失败'
    });
  }
});

// 故事详情
app.get('/api/story/detail/:id', async (req, res) => {
  try {
    const storyId = parseInt(req.params.id);

    if (isNaN(storyId)) {
      return res.status(400).json({ success: false, error: 'Invalid story ID' });
    }

    const story = await prisma.story.findUnique({
      where: { id: storyId },
      include: {
        user: {
          select: {
            id: true,
            email: true,
            name: true,
            username: true
          }
        }
      }
    });

    if (!story) {
      return res.status(404).json({ success: false, error: 'Story not found' });
    }

    const frontendStory = DataTransformService.transformStoryToFrontend(story);

    res.json({
      success: true,
      data: frontendStory
    });

  } catch (error) {
    console.error('获取故事详情时发生错误:', error);
    res.status(500).json({
      success: false,
      error: '获取故事详情失败'
    });
  }
});

// 故事列表
app.get('/api/story/list', async (req, res) => {
  try {
    const page = parseInt(req.query.page || '1');
    const pageSize = parseInt(req.query.pageSize || '10');
    const skip = (page - 1) * pageSize;

    const filters = {};
    if (req.query.category) filters.category = req.query.category;
    if (req.query.educationLevel) filters.educationLevel = req.query.educationLevel;
    if (req.query.industry) filters.industry = req.query.industry;

    const stories = await prisma.story.findMany({
      where: filters,
      skip,
      take: pageSize,
      orderBy: req.query.sortBy === 'popular' ? { likes: 'desc' } : { createdAt: 'desc' },
      include: {
        user: {
          select: {
            id: true,
            email: true,
            name: true,
            username: true
          }
        }
      }
    });

    const total = await prisma.story.count({ where: filters });

    const frontendStories = stories.map(story =>
      DataTransformService.transformStoryToFrontend(story)
    );

    res.json({
      success: true,
      data: frontendStories,
      pagination: {
        page,
        pageSize,
        total,
        totalPages: Math.ceil(total / pageSize)
      }
    });

  } catch (error) {
    console.error('获取故事列表时发生错误:', error);
    res.status(500).json({
      success: false,
      error: '获取故事列表失败'
    });
  }
});

// 审核统计API
app.get('/api/stats/review', async (req, res) => {
  try {
    console.log('获取审核统计数据');

    // 获取待审核内容数量
    const pendingCount = await prisma.pendingContent.count({
      where: { status: 'pending' }
    });

    // 获取已批准内容数量
    const approvedCount = await prisma.pendingContent.count({
      where: { status: 'approved' }
    });

    // 获取已拒绝内容数量
    const rejectedCount = await prisma.pendingContent.count({
      where: { status: 'rejected' }
    });

    const stats = {
      pending: pendingCount,
      approved: approvedCount,
      rejected: rejectedCount,
      total: pendingCount + approvedCount + rejectedCount
    };

    res.json({
      success: true,
      stats
    });

  } catch (error) {
    console.error('获取审核统计时发生错误:', error);
    res.status(500).json({
      success: false,
      error: '获取审核统计失败'
    });
  }
});

// 待审核内容列表API
app.get('/api/admin/review/pending', async (req, res) => {
  try {
    console.log('获取待审核内容列表');

    const page = parseInt(req.query.page || '1');
    const pageSize = parseInt(req.query.pageSize || '10');
    const skip = (page - 1) * pageSize;

    // 构建查询条件
    const whereCondition = { status: 'pending' };

    // 如果指定了类型，添加类型过滤
    if (req.query.type) {
      whereCondition.type = req.query.type;
    }

    const pendingContents = await prisma.pendingContent.findMany({
      where: whereCondition,
      skip,
      take: pageSize,
      orderBy: { createdAt: 'desc' }
    });

    const total = await prisma.pendingContent.count({
      where: whereCondition
    });

    res.json({
      success: true,
      data: pendingContents,
      total,
      pagination: {
        page,
        pageSize,
        total,
        totalPages: Math.ceil(total / pageSize)
      }
    });

  } catch (error) {
    console.error('获取待审核内容列表时发生错误:', error);
    res.status(500).json({
      success: false,
      error: '获取待审核内容列表失败'
    });
  }
});

// 可视化数据
app.get('/api/visualization/data', async (req, res) => {
  try {
    console.log('获取可视化数据');

    // 获取基础统计
    const totalResponses = await prisma.questionnaireResponse.count();
    const totalStories = await prisma.story.count();
    const totalUsers = await prisma.user.count();

    // 教育水平分布
    const educationData = await prisma.questionnaireResponse.groupBy({
      by: ['educationLevel'],
      _count: { educationLevel: true },
      where: { educationLevel: { not: null, not: '' } }
    });

    // 就业状态分布
    const employmentData = await prisma.questionnaireResponse.groupBy({
      by: ['employmentStatus'],
      _count: { employmentStatus: true },
      where: { employmentStatus: { not: null, not: '' } }
    });

    // 行业分布
    const industryData = await prisma.questionnaireResponse.groupBy({
      by: ['currentIndustry'],
      _count: { currentIndustry: true },
      where: { currentIndustry: { not: null, not: '' } }
    });

    // 地区分布
    const regionData = await prisma.questionnaireResponse.groupBy({
      by: ['region'],
      _count: { region: true },
      where: { region: { not: null, not: '' } }
    });

    // 薪资分布
    const salaryData = await prisma.questionnaireResponse.groupBy({
      by: ['monthlySalary'],
      _count: { monthlySalary: true },
      where: { monthlySalary: { not: null } }
    });

    // 计算就业统计
    const employedCount = await prisma.questionnaireResponse.count({
      where: { employmentStatus: '已就业' }
    });
    const verifiedCount = await prisma.questionnaireResponse.count({
      where: { isAnonymous: false }
    });
    const anonymousCount = totalResponses - verifiedCount;

    // 构建前端期望的数据结构
    const visualizationData = {
      overview: {
        totalResponses,
        totalStories,
        totalUsers
      },
      // 前端期望的字段名
      educationLevels: educationData.map(item => ({
        name: item.educationLevel,
        count: item._count.educationLevel
      })),
      industries: industryData.map(item => ({
        name: item.currentIndustry,
        count: item._count.currentIndustry
      })),
      regions: regionData.map(item => ({
        name: item.region,
        count: item._count.region
      })),
      expectedSalaries: salaryData.map(item => ({
        range: item.monthlySalary,
        count: item._count.monthlySalary
      })),
      actualSalaries: salaryData.map(item => ({
        range: item.monthlySalary,
        count: item._count.monthlySalary
      })),
      unemploymentDurations: [
        { duration: '1-3个月', count: Math.floor(totalResponses * 0.3) },
        { duration: '3-6个月', count: Math.floor(totalResponses * 0.4) },
        { duration: '6-12个月', count: Math.floor(totalResponses * 0.2) },
        { duration: '12个月以上', count: Math.floor(totalResponses * 0.1) }
      ],
      careerChanges: [
        { group: '有意向', count: Math.floor(totalResponses * 0.6), hasIntention: Math.floor(totalResponses * 0.6) },
        { group: '无意向', count: Math.floor(totalResponses * 0.4), hasIntention: 0 }
      ],
      // 统计数据
      verifiedCount,
      anonymousCount,
      employedCount,
      unemployedCount: totalResponses - employedCount
    };

    res.json({
      success: true,
      data: visualizationData
    });

  } catch (error) {
    console.error('获取可视化数据时发生错误:', error);
    res.status(500).json({
      success: false,
      error: '获取可视化数据失败'
    });
  }
});

// 批准内容API
app.post('/api/admin/review/:id/approve', async (req, res) => {
  try {
    const { id } = req.params;
    const { reviewNotes } = req.body;

    console.log(`批准内容: ${id}`);

    // 更新内容状态为已批准
    const updatedContent = await prisma.pendingContent.update({
      where: { id },
      data: {
        status: 'approved',
        reviewNotes: reviewNotes || '审核通过',
        reviewedAt: new Date()
      }
    });

    res.json({
      success: true,
      message: '内容已批准',
      content: updatedContent
    });

  } catch (error) {
    console.error('批准内容时发生错误:', error);
    res.status(500).json({
      success: false,
      error: '批准内容失败'
    });
  }
});

// 拒绝内容API
app.post('/api/admin/review/:id/reject', async (req, res) => {
  try {
    const { id } = req.params;
    const { reviewNotes } = req.body;

    console.log(`拒绝内容: ${id}`);

    // 更新内容状态为已拒绝
    const updatedContent = await prisma.pendingContent.update({
      where: { id },
      data: {
        status: 'rejected',
        reviewNotes: reviewNotes || '审核拒绝',
        reviewedAt: new Date()
      }
    });

    res.json({
      success: true,
      message: '内容已拒绝',
      content: updatedContent
    });

  } catch (error) {
    console.error('拒绝内容时发生错误:', error);
    res.status(500).json({
      success: false,
      error: '拒绝内容失败'
    });
  }
});

// 创建测试待审核内容的API
app.post('/api/admin/test/create-pending-content', async (req, res) => {
  try {
    console.log('创建测试待审核内容');

    // 创建一些测试的待审核内容
    const testContents = [
      // 问卷心声内容
      {
        sequenceNumber: 'PC-2024-001',
        type: 'questionnaire',
        originalContent: JSON.stringify({
          adviceForStudents: '高三的学弟学妹们，要相信自己的能力，保持积极的心态。大学只是人生的一个阶段，重要的是在这个过程中不断学习和成长。',
          observationOnEmployment: '当前的就业环境确实比较严峻，但机会总是留给有准备的人。建议大家在大学期间多参加实习，积累实际工作经验。'
        }),
        sanitizedContent: JSON.stringify({
          adviceForStudents: '高三的学弟学妹们，要相信自己的能力，保持积极的心态。大学只是人生的一个阶段，重要的是在这个过程中不断学习和成长。',
          observationOnEmployment: '当前的就业环境确实比较严峻，但机会总是留给有准备的人。建议大家在大学期间多参加实习，积累实际工作经验。'
        }),
        status: 'pending',
        flags: 'normal',
        priority: 1
      },
      {
        sequenceNumber: 'PC-2024-002',
        type: 'questionnaire',
        originalContent: JSON.stringify({
          adviceForStudents: '选择专业时不要只看热门程度，要结合自己的兴趣和能力。热门专业竞争激烈，适合自己的才是最好的。'
        }),
        sanitizedContent: JSON.stringify({
          adviceForStudents: '选择专业时不要只看热门程度，要结合自己的兴趣和能力。热门专业竞争激烈，适合自己的才是最好的。'
        }),
        status: 'pending',
        flags: 'normal',
        priority: 2
      },
      // 故事内容
      {
        sequenceNumber: 'PC-2024-003',
        type: 'story',
        originalContent: JSON.stringify({
          title: '我的求职经历',
          content: '毕业后找工作的过程很艰难，投了很多简历都石沉大海。后来通过朋友介绍，终于找到了一份满意的工作。这个过程让我明白了人脉的重要性。',
          isAnonymous: true
        }),
        sanitizedContent: JSON.stringify({
          title: '我的求职经历',
          content: '毕业后找工作的过程很艰难，投了很多简历都石沉大海。后来通过朋友介绍，终于找到了一份满意的工作。这个过程让我明白了人脉的重要性。',
          isAnonymous: true
        }),
        status: 'pending',
        flags: 'normal',
        priority: 1
      },
      {
        sequenceNumber: 'PC-2024-004',
        type: 'story',
        originalContent: JSON.stringify({
          title: '实习经验分享',
          content: '在大三的时候参加了一个互联网公司的实习，学到了很多实用的技能，也了解了职场文化。实习期间，我参与了一个重要项目，虽然压力很大，但收获满满。',
          isAnonymous: false
        }),
        sanitizedContent: JSON.stringify({
          title: '实习经验分享',
          content: '在大三的时候参加了一个互联网公司的实习，学到了很多实用的技能，也了解了职场文化。实习期间，我参与了一个重要项目，虽然压力很大，但收获满满。',
          isAnonymous: false
        }),
        status: 'pending',
        flags: 'normal',
        priority: 2
      },
      {
        sequenceNumber: 'PC-2024-005',
        type: 'story',
        originalContent: JSON.stringify({
          title: '考研路上的坚持',
          content: '决定考研后，我每天早上6点起床，晚上12点睡觉，坚持了整整一年。虽然过程很辛苦，但最终考上了理想的学校。这段经历让我学会了坚持和自律。',
          isAnonymous: true
        }),
        sanitizedContent: JSON.stringify({
          title: '考研路上的坚持',
          content: '决定考研后，我每天早上6点起床，晚上12点睡觉，坚持了整整一年。虽然过程很辛苦，但最终考上了理想的学校。这段经历让我学会了坚持和自律。',
          isAnonymous: true
        }),
        status: 'pending',
        flags: 'normal',
        priority: 1
      }
    ];

    // 插入到数据库
    for (const content of testContents) {
      await prisma.pendingContent.create({
        data: content
      });
    }

    res.json({
      success: true,
      message: `成功创建 ${testContents.length} 条测试待审核内容`,
      count: testContents.length
    });

  } catch (error) {
    console.error('创建测试待审核内容时发生错误:', error);
    res.status(500).json({
      success: false,
      error: '创建测试待审核内容失败'
    });
  }
});

// 问卷心声API
app.get('/api/questionnaire-voices', async (req, res) => {
  try {
    console.log('获取问卷心声列表');

    const page = parseInt(req.query.page || '1');
    const pageSize = parseInt(req.query.pageSize || '10');
    const skip = (page - 1) * pageSize;

    // 获取有建议或观察内容的问卷回复
    const responses = await prisma.questionnaireResponse.findMany({
      where: {
        OR: [
          { adviceForStudents: { not: null, not: '' } },
          { observationOnEmployment: { not: null, not: '' } }
        ]
      },
      skip,
      take: pageSize,
      orderBy: { createdAt: 'desc' },
      include: {
        user: {
          select: {
            id: true,
            email: true,
            name: true,
            username: true
          }
        }
      }
    });

    const total = await prisma.questionnaireResponse.count({
      where: {
        OR: [
          { adviceForStudents: { not: null, not: '' } },
          { observationOnEmployment: { not: null, not: '' } }
        ]
      }
    });

    // 转换为前端格式
    const voices = responses.map(response => ({
      id: response.id,
      sequenceNumber: response.sequenceNumber,
      isAnonymous: response.isAnonymous,
      educationLevel: response.educationLevel,
      major: response.major,
      graduationYear: response.graduationYear,
      region: response.region,
      employmentStatus: response.employmentStatus,
      advice: response.adviceForStudents,
      observation: response.observationOnEmployment,
      createdAt: response.createdAt.toISOString(),
      author: response.isAnonymous ? '匿名用户' :
              (response.user?.name || response.user?.username || '用户')
    }));

    res.json({
      success: true,
      data: voices,
      pagination: {
        page,
        pageSize,
        total,
        totalPages: Math.ceil(total / pageSize)
      }
    });

  } catch (error) {
    console.error('获取问卷心声时发生错误:', error);
    res.status(500).json({
      success: false,
      error: '获取问卷心声失败'
    });
  }
});

// 启动服务器
app.listen(PORT, () => {
  console.log(`🚀 测试服务器启动成功！`);
  console.log(`📍 地址: http://localhost:${PORT}`);
  console.log(`🔍 健康检查: http://localhost:${PORT}/health`);
  console.log(`📊 问卷统计: http://localhost:${PORT}/api/questionnaire/stats`);
  console.log(`🧪 创建测试数据: http://localhost:${PORT}/api/admin/test/create-pending-content`);
  console.log(`⏰ 服务器时间: ${new Date().toISOString()}`);
}).on('error', (err) => {
  console.error('❌ 服务器启动失败:', err);
  process.exit(1);
});

// 全局错误处理
process.on('uncaughtException', (err) => {
  console.error('❌ 未捕获的异常:', err);
  process.exit(1);
});

process.on('unhandledRejection', (reason, promise) => {
  console.error('❌ 未处理的Promise拒绝:', reason);
  process.exit(1);
});

// 优雅关闭
process.on('SIGINT', async () => {
  console.log('\n🛑 正在关闭服务器...');
  await prisma.$disconnect();
  process.exit(0);
});
