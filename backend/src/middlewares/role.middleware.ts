/**
 * 角色权限中间件
 */

import { Context, Next } from 'hono';
import { Env } from '../types';

export interface RoleMiddlewareOptions {
  allowedRoles: string[];
  requireAuth?: boolean;
}

/**
 * 角色权限检查中间件
 */
export function roleMiddleware(options: RoleMiddlewareOptions) {
  return async (c: Context<{ Bindings: Env }>, next: Next) => {
    const { allowedRoles, requireAuth = true } = options;

    // 如果不需要认证，直接通过
    if (!requireAuth) {
      await next();
      return;
    }

    try {
      // 从请求头获取Authorization token
      const authHeader = c.req.header('Authorization');
      if (!authHeader || !authHeader.startsWith('Bearer ')) {
        return c.json({ error: '未提供认证令牌' }, 401);
      }

      const token = authHeader.substring(7);
      
      // 这里应该验证JWT token并获取用户信息
      // 简化版本，实际应该使用JWT验证
      let userRole = 'user'; // 默认角色
      
      // 简单的token解析（实际应该使用JWT库）
      try {
        // 这里应该使用JWT验证逻辑
        // const payload = jwt.verify(token, c.env.JWT_SECRET);
        // userRole = payload.role;
        
        // 临时硬编码一些测试token
        if (token === 'superadmin-token') {
          userRole = 'superadmin';
        } else if (token === 'admin-token') {
          userRole = 'admin';
        } else if (token === 'reviewer-token') {
          userRole = 'reviewer';
        }
      } catch (error) {
        return c.json({ error: '无效的认证令牌' }, 401);
      }

      // 检查用户角色是否在允许的角色列表中
      if (!allowedRoles.includes(userRole)) {
        return c.json({ 
          error: '权限不足', 
          required: allowedRoles,
          current: userRole 
        }, 403);
      }

      // 将用户信息添加到上下文中
      c.set('userRole', userRole);
      c.set('user', { role: userRole });

      await next();
    } catch (error) {
      console.error('角色权限检查错误:', error);
      return c.json({ error: '权限验证失败' }, 500);
    }
  };
}

/**
 * 预定义的角色权限中间件
 */
export const requireSuperAdmin = roleMiddleware({ 
  allowedRoles: ['superadmin'] 
});

export const requireAdmin = roleMiddleware({ 
  allowedRoles: ['superadmin', 'admin'] 
});

export const requireReviewer = roleMiddleware({ 
  allowedRoles: ['superadmin', 'admin', 'reviewer'] 
});

export const requireUser = roleMiddleware({ 
  allowedRoles: ['superadmin', 'admin', 'reviewer', 'user'] 
});
