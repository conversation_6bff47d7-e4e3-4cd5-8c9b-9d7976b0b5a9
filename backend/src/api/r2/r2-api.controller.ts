/**
 * R2 API 控制器
 *
 * 提供从 R2 存储获取数据的 API
 */

import { Context } from 'hono';
import { R2StorageService } from '../../services/r2StorageService';

// Define environment interface
interface Env {
  R2_BUCKET: R2Bucket;
}

/**
 * 从 R2 获取故事列表
 */
export const getStoryListFromR2 = async (c: Context<{ Bindings: Env }>) => {
  try {
    console.log('Getting story list from R2...');
    
    // 获取查询参数
    const page = parseInt(c.req.query('page') || '1');
    const pageSize = parseInt(c.req.query('pageSize') || '6');
    const sortBy = c.req.query('sortBy') || 'latest';
    const tag = c.req.query('tag');
    const category = c.req.query('category');
    const search = c.req.query('search');
    const educationLevel = c.req.query('educationLevel');
    const industry = c.req.query('industry');
    
    // 创建 R2 存储服务
    const r2StorageService = new R2StorageService(c.env);
    
    // 从 R2 获取故事列表
    let stories = await r2StorageService.getStories();
    
    // 只显示已审核的故事
    stories = stories.filter(story => story.status === 'approved');
    
    // 应用筛选条件
    if (tag) {
      stories = stories.filter(story => story.tags.includes(tag));
    }
    
    if (category) {
      stories = stories.filter(story => story.category === category);
    }
    
    if (educationLevel) {
      stories = stories.filter(story => story.educationLevel === educationLevel);
    }
    
    if (industry) {
      stories = stories.filter(story => story.industry === industry);
    }
    
    if (search) {
      const searchLower = search.toLowerCase();
      stories = stories.filter(story =>
        story.title.toLowerCase().includes(searchLower) ||
        story.content.toLowerCase().includes(searchLower)
      );
    }
    
    // 应用排序
    if (sortBy === 'latest') {
      stories.sort((a, b) => new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime());
    } else if (sortBy === 'popular') {
      stories.sort((a, b) => b.likes - a.likes);
    }
    
    // 计算分页
    const totalCount = stories.length;
    const totalPages = Math.ceil(totalCount / pageSize);
    const startIndex = (page - 1) * pageSize;
    const endIndex = startIndex + pageSize;
    const paginatedStories = stories.slice(startIndex, endIndex);
    
    // 获取热门标签
    const popularTags = await r2StorageService.getPopularTags();
    
    return c.json({
      success: true,
      stories: paginatedStories,
      totalPages,
      currentPage: page,
      popularTags,
    });
  } catch (error) {
    console.error('Error getting story list from R2:', error);
    return c.json({
      success: false,
      error: 'Failed to get story list from R2',
    }, 500);
  }
};

/**
 * 从 R2 获取问卷统计数据
 */
export const getQuestionnaireStatsFromR2 = async (c: Context<{ Bindings: Env }>) => {
  try {
    console.log('Getting questionnaire stats from R2...');
    
    // 创建 R2 存储服务
    const r2StorageService = new R2StorageService(c.env);
    
    // 从 R2 获取统计数据
    const stats = await r2StorageService.getStatistics();
    
    if (!stats) {
      return c.json({
        success: false,
        error: 'No statistics data found in R2',
      }, 404);
    }
    
    return c.json({
      success: true,
      stats,
    });
  } catch (error) {
    console.error('Error getting questionnaire stats from R2:', error);
    return c.json({
      success: false,
      error: 'Failed to get questionnaire stats from R2',
    }, 500);
  }
};

/**
 * 从 R2 获取可视化数据
 */
export const getVisualizationDataFromR2 = async (c: Context<{ Bindings: Env }>) => {
  try {
    console.log('Getting visualization data from R2...');
    
    // 创建 R2 存储服务
    const r2StorageService = new R2StorageService(c.env);
    
    // 从 R2 获取可视化数据
    const data = await r2StorageService.getVisualizationData();
    
    if (!data) {
      return c.json({
        success: false,
        error: 'No visualization data found in R2',
      }, 404);
    }
    
    return c.json(data);
  } catch (error) {
    console.error('Error getting visualization data from R2:', error);
    return c.json({
      success: false,
      error: 'Failed to get visualization data from R2',
    }, 500);
  }
};
