import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { StatisticsData } from '../StatisticsPanel';
import { Separator } from '@/components/ui/separator';
import { Badge } from '@/components/ui/badge';
import { 
  DollarSign, 
  TrendingUp, 
  TrendingDown, 
  GraduationCap, 
  Building, 
  MapPin 
} from 'lucide-react';

// 导入图表库
import { 
  BarChart, 
  Bar, 
  XAxis, 
  YAxis, 
  CartesianGrid, 
  Tooltip, 
  Legend, 
  ResponsiveContainer,
  LineChart,
  Line,
  PieChart,
  Pie,
  Cell,
  ScatterChart,
  Scatter,
  ZAxis,
  Radar,
  RadarChart,
  PolarGrid,
  PolarAngleAxis,
  PolarRadiusAxis
} from 'recharts';

interface SalaryAnalysisProps {
  data: StatisticsData;
}

// 图表颜色
const COLORS = ['#0088FE', '#00C49F', '#FFBB28', '#FF8042', '#8884d8', '#82ca9d'];

export default function SalaryAnalysis({ data }: SalaryAnalysisProps) {
  // 格式化薪资
  const formattedSalary = data.overview.averageSalary.toLocaleString('zh-CN');
  
  // 准备薪资范围数据
  const salaryRangeData = data.salaryRanges.labels.map((label, index) => ({
    name: label,
    value: data.salaryRanges.data[index]
  }));
  
  // 准备学历与薪资关联数据
  const salaryByEducationData = data.salaryByEducation.labels.map((label, index) => ({
    name: label,
    value: data.salaryByEducation.data[index]
  }));
  
  // 准备行业与薪资关联数据
  const salaryByIndustryData = data.salaryByIndustry.labels.map((label, index) => ({
    name: label,
    value: data.salaryByIndustry.data[index]
  }));
  
  // 准备薪资与满意度散点图数据（模拟数据，实际应从API获取）
  const salarySatisfactionData = [
    { x: 5000, y: 3.2, z: 10 },
    { x: 6000, y: 3.5, z: 15 },
    { x: 7000, y: 3.8, z: 20 },
    { x: 8000, y: 4.0, z: 25 },
    { x: 9000, y: 4.2, z: 20 },
    { x: 10000, y: 4.3, z: 15 },
    { x: 12000, y: 4.5, z: 10 },
    { x: 15000, y: 4.7, z: 5 },
    { x: 18000, y: 4.8, z: 3 },
    { x: 20000, y: 4.9, z: 2 },
  ];
  
  // 准备地区薪资雷达图数据（模拟数据，实际应从API获取）
  const regionSalaryData = [
    { subject: '北京', A: 15000, fullMark: 20000 },
    { subject: '上海', A: 14500, fullMark: 20000 },
    { subject: '广州', A: 12000, fullMark: 20000 },
    { subject: '深圳', A: 13000, fullMark: 20000 },
    { subject: '杭州', A: 11000, fullMark: 20000 },
    { subject: '成都', A: 9000, fullMark: 20000 },
  ];
  
  return (
    <div className="space-y-4">
      {/* 薪资概览 */}
      <Card>
        <CardHeader>
          <CardTitle>薪资概览</CardTitle>
          <CardDescription>毕业生薪资水平分析</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="flex flex-col md:flex-row items-center justify-between gap-4 mb-6">
            <div className="flex items-center gap-4">
              <div className="p-4 bg-blue-50 rounded-full">
                <DollarSign className="h-8 w-8 text-blue-500" />
              </div>
              <div>
                <p className="text-sm text-muted-foreground">平均薪资</p>
                <h3 className="text-2xl font-bold">{formattedSalary}元/月</h3>
              </div>
            </div>
            
            <div className="flex items-center gap-4">
              <div className="p-4 bg-green-50 rounded-full">
                <TrendingUp className="h-8 w-8 text-green-500" />
              </div>
              <div>
                <p className="text-sm text-muted-foreground">同比增长</p>
                <h3 className="text-2xl font-bold">+8.5%</h3>
              </div>
            </div>
            
            <div className="flex items-center gap-4">
              <div className="p-4 bg-amber-50 rounded-full">
                <GraduationCap className="h-8 w-8 text-amber-500" />
              </div>
              <div>
                <p className="text-sm text-muted-foreground">最高学历薪资</p>
                <h3 className="text-2xl font-bold">18,500元/月</h3>
              </div>
            </div>
          </div>
          
          <Separator className="my-6" />
          
          <div className="h-80">
            <ResponsiveContainer width="100%" height="100%">
              <BarChart
                data={salaryRangeData}
                margin={{ top: 5, right: 30, left: 20, bottom: 5 }}
              >
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis dataKey="name" />
                <YAxis />
                <Tooltip formatter={(value) => [`${value}人`, '人数']} />
                <Legend />
                <Bar dataKey="value" fill="#0088FE" name="人数" />
              </BarChart>
            </ResponsiveContainer>
          </div>
        </CardContent>
      </Card>
      
      {/* 学历与薪资关联分析 */}
      <Card>
        <CardHeader>
          <CardTitle>学历与薪资关联分析</CardTitle>
          <CardDescription>不同学历层次的薪资水平</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="h-80">
            <ResponsiveContainer width="100%" height="100%">
              <BarChart
                data={salaryByEducationData}
                margin={{ top: 5, right: 30, left: 20, bottom: 5 }}
              >
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis dataKey="name" />
                <YAxis />
                <Tooltip formatter={(value) => [`${value}元/月`, '平均薪资']} />
                <Legend />
                <Bar dataKey="value" fill="#00C49F" name="平均薪资" />
              </BarChart>
            </ResponsiveContainer>
          </div>
        </CardContent>
      </Card>
      
      {/* 行业与薪资关联分析 */}
      <Card>
        <CardHeader>
          <CardTitle>行业与薪资关联分析</CardTitle>
          <CardDescription>不同行业的薪资水平</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="h-80">
            <ResponsiveContainer width="100%" height="100%">
              <BarChart
                data={salaryByIndustryData}
                layout="vertical"
                margin={{ top: 5, right: 30, left: 20, bottom: 5 }}
              >
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis type="number" />
                <YAxis dataKey="name" type="category" width={100} />
                <Tooltip formatter={(value) => [`${value}元/月`, '平均薪资']} />
                <Legend />
                <Bar dataKey="value" fill="#FFBB28" name="平均薪资" />
              </BarChart>
            </ResponsiveContainer>
          </div>
        </CardContent>
      </Card>
      
      {/* 薪资与满意度关联分析 */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <Card>
          <CardHeader>
            <CardTitle>薪资与满意度关联分析</CardTitle>
            <CardDescription>薪资水平与工作满意度的关系</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="h-80">
              <ResponsiveContainer width="100%" height="100%">
                <ScatterChart
                  margin={{ top: 20, right: 20, bottom: 20, left: 20 }}
                >
                  <CartesianGrid />
                  <XAxis 
                    type="number" 
                    dataKey="x" 
                    name="薪资" 
                    unit="元/月" 
                    domain={['auto', 'auto']} 
                  />
                  <YAxis 
                    type="number" 
                    dataKey="y" 
                    name="满意度" 
                    unit="分" 
                    domain={[0, 5]} 
                  />
                  <ZAxis 
                    type="number" 
                    dataKey="z" 
                    range={[60, 400]} 
                    name="数量" 
                    unit="人" 
                  />
                  <Tooltip 
                    cursor={{ strokeDasharray: '3 3' }} 
                    formatter={(value, name, props) => {
                      if (name === '薪资') return [`${value}元/月`, name];
                      if (name === '满意度') return [`${value}分`, name];
                      if (name === '数量') return [`${value}人`, name];
                      return [value, name];
                    }}
                  />
                  <Legend />
                  <Scatter 
                    name="薪资-满意度分布" 
                    data={salarySatisfactionData} 
                    fill="#8884d8" 
                  />
                </ScatterChart>
              </ResponsiveContainer>
            </div>
          </CardContent>
        </Card>
        
        <Card>
          <CardHeader>
            <CardTitle>地区薪资水平</CardTitle>
            <CardDescription>不同地区的薪资水平比较</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="h-80">
              <ResponsiveContainer width="100%" height="100%">
                <RadarChart outerRadius={90} data={regionSalaryData}>
                  <PolarGrid />
                  <PolarAngleAxis dataKey="subject" />
                  <PolarRadiusAxis angle={30} domain={[0, 20000]} />
                  <Radar 
                    name="平均薪资" 
                    dataKey="A" 
                    stroke="#8884d8" 
                    fill="#8884d8" 
                    fillOpacity={0.6} 
                  />
                  <Legend />
                  <Tooltip formatter={(value) => [`${value}元/月`, '平均薪资']} />
                </RadarChart>
              </ResponsiveContainer>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
