/**
 * 模拟数据服务
 *
 * 提供与前端期望格式匹配的模拟数据
 */

/**
 * 获取问卷统计数据的模拟数据
 * 返回与前端 QuestionnaireStats 接口匹配的数据结构
 */
export function getMockQuestionnaireStats() {
  return {
    success: true,
    statistics: {
      totalResponses: 125,
      verifiedCount: 75,
      anonymousCount: 50,
      educationLevels: [
        { code: 'undergraduate', name: '本科', count: 65, percentage: 52 },
        { code: 'master', name: '硕士', count: 35, percentage: 28 },
        { code: 'associate', name: '大专', count: 15, percentage: 12 },
        { code: 'phd', name: '博士', count: 8, percentage: 6 },
        { code: 'high_school', name: '高中/中专', count: 2, percentage: 2 }
      ],
      regions: [
        { code: 'tier1_cities', name: '北上广深', count: 45, percentage: 36 },
        { code: 'provincial_capitals', name: '省会城市', count: 30, percentage: 24 },
        { code: 'tier2_cities', name: '二线城市', count: 25, percentage: 20 },
        { code: 'tier3_cities', name: '三四线城市', count: 20, percentage: 16 },
        { code: 'county_town', name: '县城或乡镇', count: 5, percentage: 4 }
      ],
      majors: [
        { category: 'engineering', name: '工学', count: 40, percentage: 32 },
        { category: 'economics', name: '经济学', count: 25, percentage: 20 },
        { category: 'management', name: '管理学', count: 20, percentage: 16 },
        { category: 'literature', name: '文学', count: 15, percentage: 12 },
        { category: 'science', name: '理学', count: 12, percentage: 10 },
        { category: 'law', name: '法学', count: 8, percentage: 6 },
        { category: 'education', name: '教育学', count: 5, percentage: 4 }
      ],
      industries: [
        { code: 'it_internet', name: 'IT/互联网', count: 35, percentage: 28 },
        { code: 'finance', name: '金融', count: 20, percentage: 16 },
        { code: 'education', name: '教育', count: 15, percentage: 12 },
        { code: 'manufacturing', name: '制造业', count: 12, percentage: 10 },
        { code: 'healthcare', name: '医疗', count: 10, percentage: 8 },
        { code: 'government', name: '政府/事业单位', count: 8, percentage: 6 },
        { code: 'service', name: '服务业', count: 8, percentage: 6 },
        { code: 'other', name: '其他', count: 17, percentage: 14 }
      ],
      employmentStatus: [
        { code: 'employed', name: '已就业', count: 85, percentage: 68 },
        { code: 'unemployed', name: '待业中', count: 25, percentage: 20 },
        { code: 'freelance', name: '自由职业', count: 10, percentage: 8 },
        { code: 'further_education', name: '继续深造', count: 5, percentage: 4 }
      ],
      salaryRanges: [
        { range: '5k以下', count: 15, percentage: 12 },
        { range: '5k-10k', count: 35, percentage: 28 },
        { range: '10k-15k', count: 30, percentage: 24 },
        { range: '15k-20k', count: 20, percentage: 16 },
        { range: '20k-30k', count: 15, percentage: 12 },
        { range: '30k以上', count: 10, percentage: 8 }
      ]
    },
    metadata: {
      lastUpdated: new Date().toISOString(),
      dataVersion: '1.0.0',
      cacheExpiry: 30000
    }
  };
}

/**
 * 获取实时统计数据的模拟数据
 * 返回与 QuestionnaireRealtimeStats 组件期望的数据结构
 */
export function getMockRealtimeStats() {
  return {
    success: true,
    totalSubmissions: 125,
    questionStats: {
      education_level: [
        { code: 'undergraduate', name: '本科', count: 65, percentage: 52 },
        { code: 'master', name: '硕士', count: 35, percentage: 28 },
        { code: 'associate', name: '大专', count: 15, percentage: 12 },
        { code: 'phd', name: '博士', count: 8, percentage: 6 },
        { code: 'high_school', name: '高中/中专', count: 2, percentage: 2 }
      ],
      region: [
        { code: 'tier1_cities', name: '北上广深', count: 45, percentage: 36 },
        { code: 'provincial_capitals', name: '省会城市', count: 30, percentage: 24 },
        { code: 'tier2_cities', name: '二线城市', count: 25, percentage: 20 },
        { code: 'tier3_cities', name: '三四线城市', count: 20, percentage: 16 },
        { code: 'county_town', name: '县城或乡镇', count: 5, percentage: 4 }
      ],
      employment_status: [
        { code: 'employed', name: '已就业', count: 85, percentage: 68 },
        { code: 'unemployed', name: '待业中', count: 25, percentage: 20 },
        { code: 'freelance', name: '自由职业', count: 10, percentage: 8 },
        { code: 'further_education', name: '继续深造', count: 5, percentage: 4 }
      ]
    },
    metadata: {
      lastUpdated: new Date().toISOString(),
      refreshInterval: 30000
    }
  };
}

/**
 * 获取模拟问卷回复数据
 */
export function getMockResponses() {
  return [];
}

/**
 * 获取模拟可视化数据
 */
export function getMockVisualizationData() {
  return {
    charts: [],
    statistics: {}
  };
}

/**
 * 获取模拟用户数据
 */
export function getMockUsers() {
  return [];
}

/**
 * 获取模拟问卷回复数据
 */
export function getMockQuestionnaireResponses() {
  return [];
}

/**
 * 获取模拟故事数据
 */
export function getMockStories() {
  return [];
}

/**
 * 获取模拟待审核故事数据
 */
export function getMockPendingStories() {
  return [];
}

/**
 * 获取模拟统计数据
 */
export function getMockStatistics() {
  return {
    totalUsers: 0,
    totalStories: 0,
    totalResponses: 0
  };
}