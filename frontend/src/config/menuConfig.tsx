import React from 'react';
import {
  LayoutDashboard,
  BarChart,
  BarChart2,
  Activity,
  Shield,
  Users,
  UserCheck,
  Users as UsersPlus,
  User,
  FileText,
  BookOpen,
  Zap,
  Tag,
  FileQuestion,
  Database,
  EyeOff,
  Settings,
  Eye,
  Clock,
  Lock,
  Save,
  RefreshCw,
  LineChart,
  PieChart,
  AlertTriangle,
  Bell,
  MessageSquare,
  TestTube,
  Quote,
  Search,
  CheckSquare,
  Download,
  Monitor
} from 'lucide-react';

export interface MenuItem {
  title: string;
  path?: string;
  icon?: React.ReactNode;
  children?: MenuItem[];
}

export interface MenuGroup {
  title: string;
  icon?: React.ReactNode;
  items: MenuItem[];
}

/**
 * 审核员菜单配置
 */
export const reviewerMenuGroups: MenuGroup[] = [
  {
    title: '仪表盘',
    items: [
      { title: '审核仪表盘', path: '/reviewer/dashboard', icon: <BarChart2 className="h-4 w-4 mr-2" /> }
    ]
  },
  {
    title: '内容审核',
    items: [
      { title: '问卷心声', path: '/reviewer/questionnaire-voices', icon: <Quote className="h-4 w-4 mr-2" /> },
      { title: '故事审核', path: '/reviewer/story-review', icon: <BookOpen className="h-4 w-4 mr-2" /> },
      { title: '快速审核', path: '/reviewer/quick-review', icon: <Zap className="h-4 w-4 mr-2" /> }
    ]
  },
  {
    title: '设置',
    items: [
      { title: '个人设置', path: '/reviewer/settings', icon: <Settings className="h-4 w-4 mr-2" /> }
    ]
  }
];

/**
 * 管理员菜单配置
 */
export const adminMenuGroups: MenuGroup[] = [
  {
    title: '仪表盘',
    items: [
      { title: '管理仪表盘', path: '/admin/dashboard', icon: <BarChart2 className="h-4 w-4 mr-2" /> }
    ]
  },
  {
    title: '内容管理',
    items: [
      { title: '内容审核', path: '/admin/content-review', icon: <FileText className="h-4 w-4 mr-2" /> },
      { title: '评论审核', path: '/admin/comment-review', icon: <MessageSquare className="h-4 w-4 mr-2" /> },
      { title: '故事审核', path: '/admin/story-review', icon: <BookOpen className="h-4 w-4 mr-2" /> },
      { title: '快速审核', path: '/admin/quick-review', icon: <Zap className="h-4 w-4 mr-2" /> },
      { title: '标签管理', path: '/admin/tag-management', icon: <Tag className="h-4 w-4 mr-2" /> },
      { title: '内容脱敏', path: '/admin/deidentification-settings', icon: <EyeOff className="h-4 w-4 mr-2" /> }
    ]
  },
  {
    title: '数据管理',
    items: [
      { title: '问卷回复', path: '/admin/questionnaire-responses', icon: <FileQuestion className="h-4 w-4 mr-2" /> },
      { title: '数据分析', path: '/admin/data-analysis', icon: <Database className="h-4 w-4 mr-2" /> }
    ]
  },
  {
    title: '用户管理',
    items: [
      { title: '用户管理', path: '/admin/user-management', icon: <Users className="h-4 w-4 mr-2" /> },
      { title: '审核员管理', path: '/admin/reviewer-management', icon: <UserCheck className="h-4 w-4 mr-2" /> }
    ]
  },
  {
    title: '系统设置',
    items: [
      { title: '安全设置', path: '/admin/security-settings', icon: <Lock className="h-4 w-4 mr-2" /> },
      { title: '个人设置', path: '/admin/settings', icon: <Settings className="h-4 w-4 mr-2" /> }
    ]
  }
];

/**
 * 超级管理员菜单配置
 */
export const superAdminMenuGroups: MenuGroup[] = [
  {
    title: '仪表盘',
    icon: <LayoutDashboard className="h-5 w-5" />,
    items: [
      { title: '系统概览', path: '/superadmin/dashboard', icon: <LayoutDashboard className="h-4 w-4 mr-2" /> },
      { title: '平台统计', path: '/superadmin/platform-overview', icon: <BarChart className="h-4 w-4 mr-2" /> },
      { title: '系统健康状态', path: '/data-monitor', icon: <Activity className="h-4 w-4 mr-2" /> }
    ]
  },
  {
    title: '用户管理',
    icon: <Users className="h-5 w-5" />,
    items: [
      { title: '普通用户管理', path: '/superadmin/user-management', icon: <Users className="h-4 w-4 mr-2" /> },
      { title: '审核员管理', path: '/superadmin/reviewer-management', icon: <UserCheck className="h-4 w-4 mr-2" /> },
      { title: '角色管理', path: '/superadmin/role-management', icon: <UserCheck className="h-4 w-4 mr-2" /> },
      { title: '管理员管理', path: '/superadmin/admin-management', icon: <Shield className="h-4 w-4 mr-2" /> },
      { title: '批量用户管理', path: '/superadmin/user-batch', icon: <UsersPlus className="h-4 w-4 mr-2" /> }
    ]
  },
  {
    title: '内容管理',
    icon: <FileText className="h-5 w-5" />,
    items: [
      { title: '标签管理', path: '/superadmin/tag-management', icon: <Tag className="h-4 w-4 mr-2" /> },
      { title: '内容脱敏', path: '/superadmin/deidentification-settings', icon: <EyeOff className="h-4 w-4 mr-2" /> }
    ]
  },
  {
    title: '数据分析',
    icon: <LineChart className="h-5 w-5" />,
    items: [
      { title: '高级数据分析', path: '/superadmin/enhanced-data-analysis', icon: <PieChart className="h-4 w-4 mr-2" /> },
      { title: '数据管理中心', path: '/superadmin/data-management', icon: <Database className="h-4 w-4 mr-2" /> }
    ]
  },
  {
    title: '安全监控',
    icon: <Shield className="h-5 w-5" />,
    items: [
      { title: '操作日志', path: '/superadmin/operation-logs', icon: <Activity className="h-4 w-4 mr-2" /> }
    ]
  },
  {
    title: '系统管理',
    icon: <Settings className="h-5 w-5" />,
    items: [
      { title: '系统配置', path: '/superadmin/system-config', icon: <Settings className="h-4 w-4 mr-2" /> },
      { title: '性能优化', path: '/superadmin/performance-optimization', icon: <Zap className="h-4 w-4 mr-2" /> },
      { title: '系统备份', path: '/superadmin/system-backup', icon: <Save className="h-4 w-4 mr-2" /> },
      { title: '数据恢复', path: '/superadmin/data-restore', icon: <RefreshCw className="h-4 w-4 mr-2" /> },
      { title: '测试数据管理', path: '/superadmin/test-data-management', icon: <Database className="h-4 w-4 mr-2" /> },
      { title: '个人设置', path: '/superadmin/settings', icon: <User className="h-4 w-4 mr-2" /> }
    ]
  },
  {
    title: '内测工具',
    icon: <TestTube className="h-5 w-5" />,
    items: [
      { title: '数据监测', path: '/data-monitor', icon: <Monitor className="h-4 w-4 mr-2" /> },
      { title: '测试数据生成器', path: '/test-data-generator', icon: <TestTube className="h-4 w-4 mr-2" /> }
    ]
  },
  {
    title: '文档中心',
    icon: <FileText className="h-5 w-5" />,
    items: [
      { title: '项目文档', path: '/superadmin/documentation', icon: <FileText className="h-4 w-4 mr-2" /> }
    ]
  }
];
