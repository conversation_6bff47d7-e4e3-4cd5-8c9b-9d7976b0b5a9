/**
 * 性能测试面板组件
 * 
 * 用于在开发环境中测试性能
 */

import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import {
  configurePerformanceTest,
  startPerformanceTest,
  getAllTestResults,
  getLatestTestResult,
  clearTestResults,
  generatePerformanceReport
} from '@/utils/performanceTest';

// 测试路由列表
const TEST_ROUTES = [
  { name: '首页', path: '/' },
  { name: '问卷页面', path: '/questionnaire' },
  { name: '可视化页面', path: '/visualization' },
  { name: '故事墙', path: '/story-wall' },
  { name: '管理员仪表盘', path: '/admin/dashboard' },
  { name: '审核员仪表盘', path: '/reviewer/dashboard' },
  { name: '超级管理员仪表盘', path: '/superadmin/dashboard' }
];

/**
 * 性能测试面板组件
 */
export default function PerformanceTestPanel() {
  const [isOpen, setIsOpen] = useState(false);
  const [selectedRoute, setSelectedRoute] = useState('/');
  const [testName, setTestName] = useState('性能测试');
  const [iterations, setIterations] = useState(3);
  const [isTesting, setIsTesting] = useState(false);
  const [testResults, setTestResults] = useState<any[]>([]);
  const [report, setReport] = useState<string>('');
  const navigate = useNavigate();

  // 加载测试结果
  useEffect(() => {
    setTestResults(getAllTestResults());
  }, []);

  // 处理开始测试
  const handleStartTest = () => {
    setIsTesting(true);
    
    // 配置测试
    configurePerformanceTest({
      name: testName,
      description: `路由: ${selectedRoute}, 迭代次数: ${iterations}`,
      route: selectedRoute,
      iterations,
      logToConsole: true,
      exportResults: false,
      autoRun: false,
      includeNetworkRequests: true,
      includeResourceLoading: true,
      includeComponentRendering: true,
      includeWebVitals: true
    });
    
    // 开始测试
    startPerformanceTest();
    
    // 导航到测试路由
    navigate(selectedRoute);
    
    // 模拟测试完成
    setTimeout(() => {
      setIsTesting(false);
      setTestResults(getAllTestResults());
      
      // 生成报告
      const generatedReport = generatePerformanceReport();
      setReport(generatedReport);
    }, 5000);
  };

  // 处理清除结果
  const handleClearResults = () => {
    clearTestResults();
    setTestResults([]);
    setReport('');
  };

  // 处理导出报告
  const handleExportReport = () => {
    const blob = new Blob([report], { type: 'text/markdown' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `performance-report-${new Date().toISOString().replace(/[:.]/g, '-')}.md`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
  };

  // 如果不是开发环境，不显示面板
  if (process.env.NODE_ENV !== 'development') {
    return null;
  }

  return (
    <div className="fixed bottom-4 right-4 z-50">
      {/* 折叠按钮 */}
      <button
        className="bg-blue-500 hover:bg-blue-600 text-white p-2 rounded-full shadow-lg"
        onClick={() => setIsOpen(!isOpen)}
      >
        {isOpen ? '×' : '📊'}
      </button>

      {/* 测试面板 */}
      {isOpen && (
        <div className="bg-white rounded-lg shadow-xl p-4 mt-2 w-96 max-h-[80vh] overflow-auto">
          <h2 className="text-lg font-bold mb-4">性能测试工具</h2>

          {/* 测试配置 */}
          <div className="space-y-4 mb-4">
            <div>
              <label className="block text-sm font-medium mb-1">测试名称</label>
              <input
                type="text"
                value={testName}
                onChange={(e) => setTestName(e.target.value)}
                className="w-full p-2 border rounded"
              />
            </div>

            <div>
              <label className="block text-sm font-medium mb-1">测试路由</label>
              <select
                value={selectedRoute}
                onChange={(e) => setSelectedRoute(e.target.value)}
                className="w-full p-2 border rounded"
              >
                {TEST_ROUTES.map((route) => (
                  <option key={route.path} value={route.path}>
                    {route.name} ({route.path})
                  </option>
                ))}
              </select>
            </div>

            <div>
              <label className="block text-sm font-medium mb-1">测试次数</label>
              <input
                type="number"
                min="1"
                max="10"
                value={iterations}
                onChange={(e) => setIterations(parseInt(e.target.value))}
                className="w-full p-2 border rounded"
              />
            </div>

            <div className="flex space-x-2">
              <button
                onClick={handleStartTest}
                disabled={isTesting}
                className="bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded disabled:opacity-50"
              >
                {isTesting ? '测试中...' : '开始测试'}
              </button>
              <button
                onClick={handleClearResults}
                className="bg-gray-500 hover:bg-gray-600 text-white px-4 py-2 rounded"
              >
                清除结果
              </button>
            </div>
          </div>

          {/* 测试结果 */}
          {testResults.length > 0 && (
            <div>
              <h3 className="text-md font-bold mb-2">测试结果</h3>
              <div className="space-y-2">
                {testResults.map((result, index) => (
                  <div key={index} className="border p-2 rounded">
                    <p className="font-medium">{result.name}</p>
                    <p className="text-sm text-gray-600">
                      {new Date(result.timestamp).toLocaleString()}
                    </p>
                    {result.firstContentfulPaint && (
                      <p className="text-sm">
                        FCP: {result.firstContentfulPaint.toFixed(2)}ms
                      </p>
                    )}
                    {result.largestContentfulPaint && (
                      <p className="text-sm">
                        LCP: {result.largestContentfulPaint.toFixed(2)}ms
                      </p>
                    )}
                  </div>
                ))}
              </div>
            </div>
          )}

          {/* 性能报告 */}
          {report && (
            <div className="mt-4">
              <div className="flex justify-between items-center mb-2">
                <h3 className="text-md font-bold">性能报告</h3>
                <button
                  onClick={handleExportReport}
                  className="text-sm bg-green-500 hover:bg-green-600 text-white px-2 py-1 rounded"
                >
                  导出报告
                </button>
              </div>
              <pre className="bg-gray-100 p-2 rounded text-xs overflow-auto max-h-60">
                {report}
              </pre>
            </div>
          )}
        </div>
      )}
    </div>
  );
}
