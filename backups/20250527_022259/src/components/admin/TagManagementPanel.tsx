import { useState, useEffect } from 'react';
import { useToast } from '@/components/ui/use-toast';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle
} from '@/components/ui/dialog';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import {
  Plus,
  Edit,
  Trash2,
  MoreVertical,
  Tag,
  Search,
  RefreshCw,
  ArrowUpDown,
  AlertCircle
} from 'lucide-react';

// 导入通用组件
import LoadingSpinner from '@/components/ui/loading-spinner';
import ErrorDisplay from '@/components/ui/error-display';

// 导入数据服务
import { getAllTags, createTag, updateTag, deleteTag } from '@/services/dataService';

// 标签类型
export interface TagItem {
  id: string;
  name: string;
  color: string;
  priority: number;
  category?: string;
  parentId?: string;
  count: number;
  createdAt: string;
  updatedAt: string;
}

// 标签分类
const TAG_CATEGORIES = [
  { id: 'job', name: '求职相关' },
  { id: 'education', name: '学历相关' },
  { id: 'industry', name: '行业相关' },
  { id: 'experience', name: '经验相关' },
  { id: 'other', name: '其他' }
];

// 标签颜色
const TAG_COLORS = [
  { id: 'blue', name: '蓝色', value: '#3b82f6' },
  { id: 'green', name: '绿色', value: '#10b981' },
  { id: 'red', name: '红色', value: '#ef4444' },
  { id: 'yellow', name: '黄色', value: '#f59e0b' },
  { id: 'purple', name: '紫色', value: '#8b5cf6' },
  { id: 'pink', name: '粉色', value: '#ec4899' },
  { id: 'indigo', name: '靛蓝', value: '#6366f1' },
  { id: 'gray', name: '灰色', value: '#6b7280' }
];

export default function TagManagementPanel() {
  const { toast } = useToast();
  const [tags, setTags] = useState<TagItem[]>([]);
  const [filteredTags, setFilteredTags] = useState<TagItem[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [searchQuery, setSearchQuery] = useState('');
  const [sortField, setSortField] = useState<keyof TagItem>('priority');
  const [sortDirection, setSortDirection] = useState<'asc' | 'desc'>('asc');
  const [selectedCategory, setSelectedCategory] = useState<string>('all');

  // 编辑标签状态
  const [isTagDialogOpen, setIsTagDialogOpen] = useState(false);
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);
  const [currentTag, setCurrentTag] = useState<TagItem | null>(null);
  const [tagForm, setTagForm] = useState({
    name: '',
    color: 'blue',
    priority: 0,
    category: 'other',
    parentId: ''
  });

  // 获取标签数据
  const fetchTags = async () => {
    try {
      setIsLoading(true);
      setError(null);

      const response = await getAllTags();

      if (response.success) {
        setTags(response.tags);
        applyFilters(response.tags, searchQuery, selectedCategory);
      } else {
        setError(response.error || '获取标签失败');
      }
    } catch (error) {
      console.error('获取标签错误:', error);
      setError('服务器错误，请稍后再试');
    } finally {
      setIsLoading(false);
    }
  };

  // 组件加载时获取标签数据
  useEffect(() => {
    fetchTags();
  }, []);

  // 应用筛选和排序
  const applyFilters = (tagList: TagItem[], query: string, category: string) => {
    let result = [...tagList];

    // 应用搜索筛选
    if (query) {
      const lowerQuery = query.toLowerCase();
      result = result.filter(tag =>
        tag.name.toLowerCase().includes(lowerQuery) ||
        tag.id.toLowerCase().includes(lowerQuery)
      );
    }

    // 应用分类筛选
    if (category && category !== 'all') {
      result = result.filter(tag => tag.category === category);
    }

    // 应用排序
    result.sort((a, b) => {
      const aValue = a[sortField];
      const bValue = b[sortField];

      if (typeof aValue === 'string' && typeof bValue === 'string') {
        return sortDirection === 'asc'
          ? aValue.localeCompare(bValue)
          : bValue.localeCompare(aValue);
      }

      if (typeof aValue === 'number' && typeof bValue === 'number') {
        return sortDirection === 'asc' ? aValue - bValue : bValue - aValue;
      }

      return 0;
    });

    setFilteredTags(result);
  };

  // 当筛选条件变化时重新应用筛选
  useEffect(() => {
    applyFilters(tags, searchQuery, selectedCategory);
  }, [tags, searchQuery, selectedCategory, sortField, sortDirection]);

  // 处理排序变更
  const handleSort = (field: keyof TagItem) => {
    if (field === sortField) {
      setSortDirection(sortDirection === 'asc' ? 'desc' : 'asc');
    } else {
      setSortField(field);
      setSortDirection('asc');
    }
  };

  // 打开创建标签对话框
  const handleOpenCreateDialog = () => {
    setCurrentTag(null);
    setTagForm({
      name: '',
      color: 'blue',
      priority: 0,
      category: 'other',
      parentId: ''
    });
    setIsTagDialogOpen(true);
  };

  // 打开编辑标签对话框
  const handleOpenEditDialog = (tag: TagItem) => {
    setCurrentTag(tag);
    setTagForm({
      name: tag.name,
      color: tag.color,
      priority: tag.priority,
      category: tag.category || 'other',
      parentId: tag.parentId || ''
    });
    setIsTagDialogOpen(true);
  };

  // 打开删除标签对话框
  const handleOpenDeleteDialog = (tag: TagItem) => {
    setCurrentTag(tag);
    setIsDeleteDialogOpen(true);
  };

  // 处理表单变更
  const handleFormChange = (field: string, value: string | number) => {
    setTagForm(prev => ({
      ...prev,
      [field]: value
    }));
  };

  // 处理保存标签
  const handleSaveTag = async () => {
    try {
      if (!tagForm.name.trim()) {
        toast({
          variant: 'destructive',
          title: '保存失败',
          description: '标签名称不能为空',
        });
        return;
      }

      if (currentTag) {
        // 更新标签
        const response = await updateTag(currentTag.id, {
          name: tagForm.name,
          color: tagForm.color,
          priority: tagForm.priority,
          category: tagForm.category,
          parentId: tagForm.parentId || null
        });

        if (response.success) {
          toast({
            title: '更新成功',
            description: '标签已更新',
          });

          // 更新本地数据
          setTags(prev => prev.map(tag =>
            tag.id === currentTag.id ? { ...tag, ...response.tag } : tag
          ));
        } else {
          toast({
            variant: 'destructive',
            title: '更新失败',
            description: response.error || '更新标签失败',
          });
        }
      } else {
        // 创建标签
        const response = await createTag({
          name: tagForm.name,
          color: tagForm.color,
          priority: tagForm.priority,
          category: tagForm.category,
          parentId: tagForm.parentId || null
        });

        if (response.success) {
          toast({
            title: '创建成功',
            description: '标签已创建',
          });

          // 更新本地数据
          setTags(prev => [...prev, response.tag]);
        } else {
          toast({
            variant: 'destructive',
            title: '创建失败',
            description: response.error || '创建标签失败',
          });
        }
      }

      // 关闭对话框
      setIsTagDialogOpen(false);
    } catch (error) {
      console.error('保存标签错误:', error);
      toast({
        variant: 'destructive',
        title: '保存失败',
        description: '服务器错误，请稍后再试',
      });
    }
  };

  // 处理删除标签
  const handleDeleteTag = async () => {
    try {
      if (!currentTag) return;

      const response = await deleteTag(currentTag.id);

      if (response.success) {
        toast({
          title: '删除成功',
          description: '标签已删除',
        });

        // 更新本地数据
        setTags(prev => prev.filter(tag => tag.id !== currentTag.id));
      } else {
        toast({
          variant: 'destructive',
          title: '删除失败',
          description: response.error || '删除标签失败',
        });
      }

      // 关闭对话框
      setIsDeleteDialogOpen(false);
    } catch (error) {
      console.error('删除标签错误:', error);
      toast({
        variant: 'destructive',
        title: '删除失败',
        description: '服务器错误，请稍后再试',
      });
    }
  };

  // 渲染标签颜色预览
  const renderColorPreview = (color: string) => {
    const colorObj = TAG_COLORS.find(c => c.id === color);
    return (
      <div
        className="w-4 h-4 rounded-full"
        style={{ backgroundColor: colorObj?.value || '#6b7280' }}
      />
    );
  };

  // 渲染加载状态
  if (isLoading && tags.length === 0) {
    return (
      <div className="flex justify-center items-center py-12">
        <LoadingSpinner size="lg" text="加载标签数据中..." />
      </div>
    );
  }

  // 渲染错误状态
  if (error && tags.length === 0) {
    return (
      <ErrorDisplay
        title="加载数据失败"
        message={error}
        onRetry={fetchTags}
      />
    );
  }

  return (
    <div className="space-y-4">
      <div className="flex flex-col md:flex-row justify-between items-start md:items-center gap-4">
        <div>
          <h3 className="text-lg font-medium">标签管理</h3>
          <p className="text-sm text-gray-500">
            管理故事墙的标签系统，包括创建、编辑和删除标签
          </p>
        </div>

        <div className="flex flex-wrap gap-2">
          <Button
            variant="outline"
            size="sm"
            onClick={fetchTags}
            className="flex items-center gap-1"
          >
            <RefreshCw className="h-4 w-4" />
            刷新
          </Button>

          <Button
            size="sm"
            onClick={handleOpenCreateDialog}
            className="flex items-center gap-1"
          >
            <Plus className="h-4 w-4" />
            创建标签
          </Button>
        </div>
      </div>

      {/* 筛选工具栏 */}
      <Card>
        <CardContent className="p-4">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className="space-y-1">
              <Label htmlFor="search">搜索标签</Label>
              <div className="relative">
                <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
                <Input
                  id="search"
                  placeholder="搜索标签名称..."
                  className="pl-8"
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                />
              </div>
            </div>

            <div className="space-y-1">
              <Label htmlFor="category">标签分类</Label>
              <Select
                value={selectedCategory}
                onValueChange={setSelectedCategory}
              >
                <SelectTrigger id="category">
                  <SelectValue placeholder="选择分类" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">全部分类</SelectItem>
                  {TAG_CATEGORIES.map((category) => (
                    <SelectItem key={category.id} value={category.id}>
                      {category.name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* 标签列表 */}
      <Card>
        <CardHeader className="p-4">
          <CardTitle>标签列表</CardTitle>
          <CardDescription>
            共 {filteredTags.length} 个标签
            {searchQuery && ` (搜索: "${searchQuery}")`}
            {selectedCategory !== 'all' && ` (分类: "${TAG_CATEGORIES.find(c => c.id === selectedCategory)?.name || selectedCategory}")`}
          </CardDescription>
        </CardHeader>
        <CardContent className="p-0">
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead className="w-12">颜色</TableHead>
                <TableHead>
                  <Button
                    variant="ghost"
                    className="flex items-center gap-1 p-0 h-auto font-medium"
                    onClick={() => handleSort('name')}
                  >
                    标签名称
                    {sortField === 'name' && (
                      <ArrowUpDown className="h-4 w-4" />
                    )}
                  </Button>
                </TableHead>
                <TableHead>分类</TableHead>
                <TableHead>
                  <Button
                    variant="ghost"
                    className="flex items-center gap-1 p-0 h-auto font-medium"
                    onClick={() => handleSort('priority')}
                  >
                    优先级
                    {sortField === 'priority' && (
                      <ArrowUpDown className="h-4 w-4" />
                    )}
                  </Button>
                </TableHead>
                <TableHead>
                  <Button
                    variant="ghost"
                    className="flex items-center gap-1 p-0 h-auto font-medium"
                    onClick={() => handleSort('count')}
                  >
                    使用次数
                    {sortField === 'count' && (
                      <ArrowUpDown className="h-4 w-4" />
                    )}
                  </Button>
                </TableHead>
                <TableHead className="text-right">操作</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {filteredTags.length === 0 ? (
                <TableRow>
                  <TableCell colSpan={6} className="text-center py-8">
                    {searchQuery || selectedCategory !== 'all' ? (
                      <div className="flex flex-col items-center justify-center text-muted-foreground">
                        <Search className="h-8 w-8 mb-2" />
                        <p>未找到匹配的标签</p>
                      </div>
                    ) : (
                      <div className="flex flex-col items-center justify-center text-muted-foreground">
                        <Tag className="h-8 w-8 mb-2" />
                        <p>暂无标签数据</p>
                        <Button
                          variant="link"
                          className="mt-2"
                          onClick={handleOpenCreateDialog}
                        >
                          创建第一个标签
                        </Button>
                      </div>
                    )}
                  </TableCell>
                </TableRow>
              ) : (
                filteredTags.map((tag) => (
                  <TableRow key={tag.id}>
                    <TableCell>{renderColorPreview(tag.color)}</TableCell>
                    <TableCell>
                      <div className="flex items-center gap-2">
                        <Badge
                          variant="outline"
                          style={{
                            borderColor: TAG_COLORS.find(c => c.id === tag.color)?.value || '#6b7280',
                            color: TAG_COLORS.find(c => c.id === tag.color)?.value || '#6b7280'
                          }}
                        >
                          {tag.name}
                        </Badge>
                        {tag.parentId && (
                          <Badge variant="secondary" className="text-xs">
                            子标签
                          </Badge>
                        )}
                      </div>
                    </TableCell>
                    <TableCell>
                      {TAG_CATEGORIES.find(c => c.id === tag.category)?.name || '未分类'}
                    </TableCell>
                    <TableCell>{tag.priority}</TableCell>
                    <TableCell>{tag.count}</TableCell>
                    <TableCell className="text-right">
                      <DropdownMenu>
                        <DropdownMenuTrigger asChild>
                          <Button variant="ghost" size="icon">
                            <MoreVertical className="h-4 w-4" />
                          </Button>
                        </DropdownMenuTrigger>
                        <DropdownMenuContent align="end">
                          <DropdownMenuLabel>操作</DropdownMenuLabel>
                          <DropdownMenuSeparator />
                          <DropdownMenuItem onClick={() => handleOpenEditDialog(tag)}>
                            <Edit className="h-4 w-4 mr-2" />
                            编辑
                          </DropdownMenuItem>
                          <DropdownMenuItem
                            onClick={() => handleOpenDeleteDialog(tag)}
                            className="text-red-600"
                          >
                            <Trash2 className="h-4 w-4 mr-2" />
                            删除
                          </DropdownMenuItem>
                        </DropdownMenuContent>
                      </DropdownMenu>
                    </TableCell>
                  </TableRow>
                ))
              )}
            </TableBody>
          </Table>
        </CardContent>
      </Card>

      {/* 标签编辑对话框 */}
      <Dialog open={isTagDialogOpen} onOpenChange={setIsTagDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>{currentTag ? '编辑标签' : '创建标签'}</DialogTitle>
            <DialogDescription>
              {currentTag ? '修改标签信息' : '创建一个新的标签'}
            </DialogDescription>
          </DialogHeader>

          <div className="grid gap-4 py-4">
            <div className="grid gap-2">
              <Label htmlFor="name">标签名称</Label>
              <Input
                id="name"
                value={tagForm.name}
                onChange={(e) => handleFormChange('name', e.target.value)}
                placeholder="输入标签名称"
              />
            </div>

            <div className="grid gap-2">
              <Label htmlFor="color">标签颜色</Label>
              <Select
                value={tagForm.color}
                onValueChange={(value) => handleFormChange('color', value)}
              >
                <SelectTrigger id="color" className="flex items-center gap-2">
                  {renderColorPreview(tagForm.color)}
                  <SelectValue placeholder="选择颜色" />
                </SelectTrigger>
                <SelectContent>
                  {TAG_COLORS.map((color) => (
                    <SelectItem key={color.id} value={color.id}>
                      <div className="flex items-center gap-2">
                        <div
                          className="w-4 h-4 rounded-full"
                          style={{ backgroundColor: color.value }}
                        />
                        <span>{color.name}</span>
                      </div>
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            <div className="grid gap-2">
              <Label htmlFor="category">标签分类</Label>
              <Select
                value={tagForm.category}
                onValueChange={(value) => handleFormChange('category', value)}
              >
                <SelectTrigger id="category">
                  <SelectValue placeholder="选择分类" />
                </SelectTrigger>
                <SelectContent>
                  {TAG_CATEGORIES.map((category) => (
                    <SelectItem key={category.id} value={category.id}>
                      {category.name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            <div className="grid gap-2">
              <Label htmlFor="priority">优先级</Label>
              <Input
                id="priority"
                type="number"
                value={tagForm.priority}
                onChange={(e) => handleFormChange('priority', parseInt(e.target.value) || 0)}
                placeholder="输入优先级"
              />
              <p className="text-xs text-muted-foreground">
                优先级越高，在标签列表中排序越靠前
              </p>
            </div>

            <div className="grid gap-2">
              <Label htmlFor="parentId">父标签</Label>
              <Select
                value={tagForm.parentId}
                onValueChange={(value) => handleFormChange('parentId', value)}
              >
                <SelectTrigger id="parentId">
                  <SelectValue placeholder="选择父标签（可选）" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="">无父标签</SelectItem>
                  {tags
                    .filter(tag => !tag.parentId && (!currentTag || tag.id !== currentTag.id))
                    .map((tag) => (
                      <SelectItem key={tag.id} value={tag.id}>
                        {tag.name}
                      </SelectItem>
                    ))
                  }
                </SelectContent>
              </Select>
              <p className="text-xs text-muted-foreground">
                选择父标签可以创建标签层次结构
              </p>
            </div>
          </div>

          <DialogFooter>
            <Button variant="outline" onClick={() => setIsTagDialogOpen(false)}>
              取消
            </Button>
            <Button onClick={handleSaveTag}>
              保存
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* 删除确认对话框 */}
      <Dialog open={isDeleteDialogOpen} onOpenChange={setIsDeleteDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>删除标签</DialogTitle>
            <DialogDescription>
              确定要删除标签 "{currentTag?.name}" 吗？此操作不可撤销。
            </DialogDescription>
          </DialogHeader>

          <DialogFooter>
            <Button variant="outline" onClick={() => setIsDeleteDialogOpen(false)}>
              取消
            </Button>
            <Button variant="destructive" onClick={handleDeleteTag}>
              删除
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
}
