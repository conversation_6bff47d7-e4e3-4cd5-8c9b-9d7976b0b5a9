/**
 * 问卷处理程序测试
 */

import { describe, it, expect, beforeEach, vi } from 'vitest';
import { 
  handleGetSurveys, 
  handleGetSurveyDetail,
  handleCreateSurvey,
  handleSubmitSurveyResponse
} from '../../src/handlers/survey';
import { mockEnv, createMockRequest, parseResponseJson, createAuthHeader } from '../setup';
import { 
  resetAllMocks, 
  mockVerifyToken, 
  createMockSurvey, 
  createMockQuestion,
  mockDbQueryResult
} from '../utils';

describe('问卷处理程序', () => {
  beforeEach(() => {
    resetAllMocks();
    vi.clearAllMocks();
  });

  describe('handleGetSurveys', () => {
    it('应该返回问卷列表', async () => {
      // 模拟数据库查询结果
      const mockSurveys = [
        createMockSurvey(1),
        createMockSurvey(2),
        createMockSurvey(3)
      ];

      mockDbQueryResult(mockEnv.DB, {
        'SELECT * FROM surveys': { multiple: mockSurveys },
        'SELECT COUNT': { single: { total: mockSurveys.length } }
      });

      // 准备请求
      const request = createMockRequest({
        method: 'GET',
        url: 'http://localhost/api/v1/surveys'
      });

      // 执行处理程序
      const response = await handleGetSurveys(request, mockEnv);
      
      // 验证响应
      expect(response.status).toBe(200);
      
      const data = await parseResponseJson(response);
      expect(data.success).toBe(true);
      expect(data.surveys).toHaveLength(3);
      expect(data.total).toBe(3);
      expect(data.page).toBe(1);
      expect(data.pageSize).toBe(10);
      expect(data.totalPages).toBe(1);
    });

    it('应该支持分页和筛选', async () => {
      // 模拟数据库查询结果
      const mockSurveys = [createMockSurvey(1)];
      mockDbQueryResult(mockEnv.DB, {
        'SELECT * FROM surveys': { multiple: mockSurveys },
        'SELECT COUNT': { single: { total: 1 } }
      });

      // 准备请求
      const request = createMockRequest({
        method: 'GET',
        url: 'http://localhost/api/v1/surveys?status=active&page=2&pageSize=5'
      });

      // 执行处理程序
      const response = await handleGetSurveys(request, mockEnv);
      
      // 验证响应
      expect(response.status).toBe(200);
      
      const data = await parseResponseJson(response);
      expect(data.success).toBe(true);
      expect(data.page).toBe(2);
      expect(data.pageSize).toBe(5);
    });

    it('应该在发生异常时返回500错误', async () => {
      // 模拟数据库查询抛出异常
      mockEnv.DB.prepare = vi.fn().mockImplementation(() => {
        throw new Error('模拟数据库异常');
      });

      // 准备请求
      const request = createMockRequest({
        method: 'GET',
        url: 'http://localhost/api/v1/surveys'
      });

      // 执行处理程序
      const response = await handleGetSurveys(request, mockEnv);
      
      // 验证响应
      expect(response.status).toBe(500);
      
      const data = await parseResponseJson(response);
      expect(data.success).toBe(false);
      expect(data.error).toBe('服务器错误');
    });
  });

  describe('handleGetSurveyDetail', () => {
    it('应该返回问卷详情', async () => {
      // 模拟数据库查询结果
      const mockSurvey = createMockSurvey(1);
      const mockQuestions = [
        createMockQuestion(1, 1),
        createMockQuestion(2, 1),
        createMockQuestion(3, 1)
      ];

      mockDbQueryResult(mockEnv.DB, {
        'SELECT * FROM surveys': { single: mockSurvey },
        'SELECT * FROM questions': { multiple: mockQuestions }
      });

      // 准备请求
      const request = createMockRequest({
        method: 'GET',
        url: 'http://localhost/api/v1/surveys/1'
      });

      // 执行处理程序
      const response = await handleGetSurveyDetail(request, mockEnv, '1');
      
      // 验证响应
      expect(response.status).toBe(200);
      
      const data = await parseResponseJson(response);
      expect(data.success).toBe(true);
      expect(data.survey).toBeDefined();
      expect(data.survey.id).toBe(1);
      expect(data.survey.questions).toHaveLength(3);
    });

    it('应该在问卷不存在时返回404错误', async () => {
      // 模拟数据库查询结果
      mockDbQueryResult(mockEnv.DB, {
        'SELECT * FROM surveys': { single: null }
      });

      // 准备请求
      const request = createMockRequest({
        method: 'GET',
        url: 'http://localhost/api/v1/surveys/999'
      });

      // 执行处理程序
      const response = await handleGetSurveyDetail(request, mockEnv, '999');
      
      // 验证响应
      expect(response.status).toBe(404);
      
      const data = await parseResponseJson(response);
      expect(data.success).toBe(false);
      expect(data.error).toBe('问卷不存在');
    });
  });

  describe('handleCreateSurvey', () => {
    beforeEach(() => {
      // 模拟认证
      mockVerifyToken(true, {
        sub: '1',
        role: 'admin',
        permissions: ['manage_content']
      });
    });

    it('应该成功创建问卷', async () => {
      // 模拟数据库查询结果
      const mockResult = { success: true, meta: { last_row_id: 1 } };
      mockEnv.DB.prepare = vi.fn().mockReturnValue({
        bind: vi.fn().mockReturnThis(),
        run: vi.fn().mockResolvedValue(mockResult)
      });

      // 准备请求
      const request = createMockRequest({
        method: 'POST',
        url: 'http://localhost/api/v1/surveys',
        headers: createAuthHeader('admin'),
        body: {
          title: '测试问卷',
          description: '这是一个测试问卷',
          questions: [
            {
              type: 'radio',
              question: '测试问题1',
              options: ['选项1', '选项2'],
              required: true
            },
            {
              type: 'text',
              question: '测试问题2',
              required: false
            }
          ]
        }
      });

      // 执行处理程序
      const response = await handleCreateSurvey(request, mockEnv);
      
      // 验证响应
      expect(response.status).toBe(201);
      
      const data = await parseResponseJson(response);
      expect(data.success).toBe(true);
      expect(data.surveyId).toBeDefined();
      expect(data.message).toBe('问卷创建成功');
    });

    it('应该在未授权时返回401错误', async () => {
      // 准备请求（没有认证头）
      const request = createMockRequest({
        method: 'POST',
        url: 'http://localhost/api/v1/surveys',
        body: {
          title: '测试问卷',
          description: '这是一个测试问卷',
          questions: []
        }
      });

      // 执行处理程序
      const response = await handleCreateSurvey(request, mockEnv);
      
      // 验证响应
      expect(response.status).toBe(401);
      
      const data = await parseResponseJson(response);
      expect(data.success).toBe(false);
      expect(data.error).toBe('未授权');
    });

    it('应该在令牌无效时返回401错误', async () => {
      // 模拟认证失败
      mockVerifyToken(false);

      // 准备请求
      const request = createMockRequest({
        method: 'POST',
        url: 'http://localhost/api/v1/surveys',
        headers: createAuthHeader('admin'),
        body: {
          title: '测试问卷',
          description: '这是一个测试问卷',
          questions: []
        }
      });

      // 执行处理程序
      const response = await handleCreateSurvey(request, mockEnv);
      
      // 验证响应
      expect(response.status).toBe(401);
      
      const data = await parseResponseJson(response);
      expect(data.success).toBe(false);
      expect(data.error).toBe('无效的令牌');
    });

    it('应该在没有权限时返回403错误', async () => {
      // 模拟认证为审核员（没有创建问卷的权限）
      mockVerifyToken(true, {
        sub: '1',
        role: 'reviewer',
        permissions: ['review_content']
      });

      // 准备请求
      const request = createMockRequest({
        method: 'POST',
        url: 'http://localhost/api/v1/surveys',
        headers: createAuthHeader('reviewer'),
        body: {
          title: '测试问卷',
          description: '这是一个测试问卷',
          questions: []
        }
      });

      // 执行处理程序
      const response = await handleCreateSurvey(request, mockEnv);
      
      // 验证响应
      expect(response.status).toBe(403);
      
      const data = await parseResponseJson(response);
      expect(data.success).toBe(false);
      expect(data.error).toBe('没有权限');
    });

    it('应该在缺少必要参数时返回400错误', async () => {
      // 准备请求（缺少描述）
      const request = createMockRequest({
        method: 'POST',
        url: 'http://localhost/api/v1/surveys',
        headers: createAuthHeader('admin'),
        body: {
          title: '测试问卷',
          questions: []
        }
      });

      // 执行处理程序
      const response = await handleCreateSurvey(request, mockEnv);
      
      // 验证响应
      expect(response.status).toBe(400);
      
      const data = await parseResponseJson(response);
      expect(data.success).toBe(false);
      expect(data.error).toBe('缺少必要参数');
    });
  });

  describe('handleSubmitSurveyResponse', () => {
    it('应该成功提交问卷回复', async () => {
      // 模拟数据库查询结果
      mockDbQueryResult(mockEnv.DB, {
        'SELECT * FROM surveys': { single: createMockSurvey(1) },
        'SELECT * FROM survey_responses': { single: null }
      });

      const mockResult = { success: true, meta: { last_row_id: 1 } };
      mockEnv.DB.prepare = vi.fn().mockImplementation((sql) => {
        if (sql.includes('SELECT')) {
          if (sql.includes('surveys')) {
            return {
              bind: vi.fn().mockReturnThis(),
              first: vi.fn().mockResolvedValue(createMockSurvey(1))
            };
          } else {
            return {
              bind: vi.fn().mockReturnThis(),
              first: vi.fn().mockResolvedValue(null)
            };
          }
        } else {
          return {
            bind: vi.fn().mockReturnThis(),
            run: vi.fn().mockResolvedValue(mockResult)
          };
        }
      });

      // 准备请求
      const request = createMockRequest({
        method: 'POST',
        url: 'http://localhost/api/v1/surveys/1/responses',
        body: {
          answers: [
            {
              questionId: 1,
              answer: '选项1'
            },
            {
              questionId: 2,
              answer: ['选项1', '选项3']
            },
            {
              questionId: 3,
              answer: '这是文本回答'
            }
          ],
          isAnonymous: true,
          identityA: 'student123',
          identityB: 'university456'
        }
      });

      // 执行处理程序
      const response = await handleSubmitSurveyResponse(request, mockEnv, '1');
      
      // 验证响应
      expect(response.status).toBe(201);
      
      const data = await parseResponseJson(response);
      expect(data.success).toBe(true);
      expect(data.responseId).toBeDefined();
      expect(data.message).toBe('问卷提交成功，等待审核');
    });

    it('应该在问卷不存在时返回404错误', async () => {
      // 模拟数据库查询结果
      mockDbQueryResult(mockEnv.DB, {
        'SELECT * FROM surveys': { single: null }
      });

      // 准备请求
      const request = createMockRequest({
        method: 'POST',
        url: 'http://localhost/api/v1/surveys/999/responses',
        body: {
          answers: [{ questionId: 1, answer: '选项1' }],
          isAnonymous: true,
          identityA: 'student123',
          identityB: 'university456'
        }
      });

      // 执行处理程序
      const response = await handleSubmitSurveyResponse(request, mockEnv, '999');
      
      // 验证响应
      expect(response.status).toBe(404);
      
      const data = await parseResponseJson(response);
      expect(data.success).toBe(false);
      expect(data.error).toBe('问卷不存在或已关闭');
    });

    it('应该在已提交过问卷时返回400错误', async () => {
      // 模拟数据库查询结果
      mockDbQueryResult(mockEnv.DB, {
        'SELECT * FROM surveys': { single: createMockSurvey(1) },
        'SELECT * FROM survey_responses': { single: { id: 1 } }
      });

      // 准备请求
      const request = createMockRequest({
        method: 'POST',
        url: 'http://localhost/api/v1/surveys/1/responses',
        body: {
          answers: [{ questionId: 1, answer: '选项1' }],
          isAnonymous: true,
          identityA: 'student123',
          identityB: 'university456'
        }
      });

      // 执行处理程序
      const response = await handleSubmitSurveyResponse(request, mockEnv, '1');
      
      // 验证响应
      expect(response.status).toBe(400);
      
      const data = await parseResponseJson(response);
      expect(data.success).toBe(false);
      expect(data.error).toBe('您已提交过该问卷');
    });

    it('应该在缺少必要参数时返回400错误', async () => {
      // 准备请求（缺少identityB）
      const request = createMockRequest({
        method: 'POST',
        url: 'http://localhost/api/v1/surveys/1/responses',
        body: {
          answers: [{ questionId: 1, answer: '选项1' }],
          isAnonymous: true,
          identityA: 'student123'
        }
      });

      // 执行处理程序
      const response = await handleSubmitSurveyResponse(request, mockEnv, '1');
      
      // 验证响应
      expect(response.status).toBe(400);
      
      const data = await parseResponseJson(response);
      expect(data.success).toBe(false);
      expect(data.error).toBe('缺少必要参数');
    });
  });
});
