/**
 * 验证码验证服务
 * 
 * 负责验证前端提交的验证码令牌
 */

import { CaptchaConfig, ValidationResult } from '../types';

/**
 * 验证码验证服务类
 */
export class CaptchaVerifier {
  private config: CaptchaConfig;

  /**
   * 构造函数
   */
  constructor() {
    this.config = {
      enabled: false,
      type: 'none',
      triggerThreshold: 0
    };
  }

  /**
   * 初始化服务
   * @param config 验证码配置
   */
  public initialize(config: CaptchaConfig): void {
    this.config = { ...config };
  }

  /**
   * 更新配置
   * @param config 验证码配置
   */
  public updateConfig(config: CaptchaConfig): void {
    this.config = { ...this.config, ...config };
  }

  /**
   * 验证 Turnstile 令牌
   * @param token 验证码令牌
   * @param clientIp 客户端 IP
   * @param env 环境变量
   */
  public async verifyCaptcha(
    token: string | undefined,
    clientIp: string,
    env: any
  ): Promise<ValidationResult> {
    // 如果未启用验证码，直接返回有效
    if (!this.config.enabled) {
      return { valid: true };
    }

    // 如果未提供令牌，返回无效
    if (!token) {
      return { 
        valid: false, 
        error: '验证码验证失败，请重试' 
      };
    }

    // 验证 Turnstile 令牌
    if (this.config.type === 'turnstile') {
      try {
        // 获取密钥
        const secretKey = this.config.secretKey || env.TURNSTILE_SECRET_KEY;
        
        if (!secretKey) {
          console.error('Turnstile secret key not configured');
          return { 
            valid: false, 
            error: '验证码服务配置错误' 
          };
        }

        // 验证令牌
        const result = await this.verifyTurnstileToken(token, secretKey, clientIp);
        
        if (!result.success) {
          console.warn('Turnstile verification failed:', result.error);
          return { 
            valid: false, 
            error: '验证码验证失败，请重试' 
          };
        }

        return { valid: true };
      } catch (error) {
        console.error('Error verifying Turnstile token:', error);
        return { 
          valid: false, 
          error: '验证码验证过程中发生错误' 
        };
      }
    }

    // 自定义验证码类型
    if (this.config.type === 'custom') {
      // 实现自定义验证码验证逻辑
      return { valid: true };
    }

    return { valid: true };
  }

  /**
   * 验证 Turnstile 令牌
   * @param token 验证码令牌
   * @param secretKey 密钥
   * @param clientIp 客户端 IP
   */
  private async verifyTurnstileToken(
    token: string,
    secretKey: string,
    clientIp: string
  ): Promise<{ success: boolean; error?: string }> {
    try {
      const formData = new FormData();
      formData.append('secret', secretKey);
      formData.append('response', token);
      formData.append('remoteip', clientIp);

      const response = await fetch('https://challenges.cloudflare.com/turnstile/v0/siteverify', {
        method: 'POST',
        body: formData
      });

      const result = await response.json();

      if (!result.success) {
        return {
          success: false,
          error: result['error-codes']?.join(', ') || 'Unknown error'
        };
      }

      return { success: true };
    } catch (error) {
      console.error('Error verifying Turnstile token:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error'
      };
    }
  }
}
