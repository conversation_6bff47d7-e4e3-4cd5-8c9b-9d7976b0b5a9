/**
 * 测试数据服务
 *
 * 提供测试数据的生成、导入和清除功能
 * 适配Cloudflare Workers环境（不使用Node.js的fs模块）
 */

import { Env } from '../types';
import { v4 as uuidv4 } from 'uuid';
import CryptoJS from 'crypto-js';

// 生成UUID
function generateUUID() {
  return uuidv4();
}

// 生成哈希密码
function hashPassword(password: string) {
  return CryptoJS.SHA256(password).toString(CryptoJS.enc.Hex);
}

// 生成用户数据
function generateUsers() {
  const users = [];

  // 生成50个普通用户，其中30个有UUID
  for (let i = 1; i <= 50; i++) {
    const hasUUID = i <= 30;
    users.push({
      email: `user${i}@example.com`,
      name: `测试用户${i}`,
      password: hashPassword('password123'),
      role: 'user',
      uuid: hasUUID ? generateUUID() : null,
      isTestData: true
    });
  }

  // 生成10个审核员
  for (let i = 1; i <= 10; i++) {
    users.push({
      email: `reviewer${i}@example.com`,
      name: `测试审核员${i}`,
      password: hashPassword('password123'),
      role: 'reviewer',
      uuid: generateUUID(),
      isTestData: true
    });
  }

  // 生成3个管理员
  for (let i = 1; i <= 3; i++) {
    users.push({
      email: `admin${i}@example.com`,
      name: `测试管理员${i}`,
      password: hashPassword('password123'),
      role: 'admin',
      uuid: generateUUID(),
      isTestData: true
    });
  }

  return users;
}

// 生成问卷数据
function generateQuestionnaires(userIds: Record<string, number>) {
  const questionnaires = [];

  for (let i = 1; i <= 50; i++) {
    const userEmail = `user${(i % 50) + 1}@example.com`;
    const userId = userIds[userEmail];

    if (!userId) continue;

    questionnaires.push({
      sequenceNumber: `QUEST${String(i).padStart(3, '0')}`,
      type: 'questionnaire',
      originalContent: JSON.stringify({
        userId: userId,
        educationLevel: ['本科', '硕士', '博士', '大专'][Math.floor(Math.random() * 4)],
        major: ['计算机科学', '电子工程', '经济学', '管理学', '法学'][Math.floor(Math.random() * 5)],
        graduationYear: 2020 + Math.floor(Math.random() * 4),
        region: ['北京', '上海', '广州', '深圳', '杭州'][Math.floor(Math.random() * 5)],
        expectedPosition: `测试职位${i}`,
        expectedSalaryRange: ['5k-10k', '10k-15k', '15k-20k', '20k+'][Math.floor(Math.random() * 4)],
        adviceForStudents: `这是第${i}份问卷的建议内容，需要审核。`,
        observationOnEmployment: `这是第${i}份问卷的就业观察，需要审核。`
      }),
      sanitizedContent: '{}',
      status: 'pending',
      priority: Math.floor(Math.random() * 3) + 1,
      originIp: '127.0.0.1',
      userAgent: 'Test Data Generator',
      isTestData: true
    });
  }

  return questionnaires;
}

// 生成故事数据
function generateStories(userIds: Record<string, number>) {
  const stories = [];

  for (let i = 1; i <= 50; i++) {
    const userEmail = `user${(i % 50) + 1}@example.com`;
    const userId = userIds[userEmail];

    if (!userId) continue;

    stories.push({
      sequenceNumber: `STORY${String(i).padStart(3, '0')}`,
      type: 'story',
      originalContent: JSON.stringify({
        userId: userId,
        title: `测试故事标题 ${i}`,
        content: `这是第${i}个测试故事的内容。这个故事讲述了一个大学生求职的经历，包含了一些挑战和成功。这个内容需要经过审核才能显示在故事墙上。`,
        tags: ['求职', '面试', '实习'].slice(0, Math.floor(Math.random() * 3) + 1)
      }),
      sanitizedContent: '{}',
      status: 'pending',
      priority: Math.floor(Math.random() * 3) + 1,
      originIp: '127.0.0.1',
      userAgent: 'Test Data Generator',
      isTestData: true
    });
  }

  return stories;
}

/**
 * 导入测试数据
 */
export async function importTestData(env: Env) {
  try {
    console.log('开始导入测试数据...');

    // 获取数据库连接
    const db = env.DB;

    // 生成用户数据
    const users = generateUsers();
    console.log(`生成了 ${users.length} 个用户`);

    // 导入用户数据
    const userIds: Record<string, number> = {};
    for (const userData of users) {
      try {
        // 检查用户是否已存在
        let existingUser;
        try {
          existingUser = await db.prepare(
            'SELECT id FROM users WHERE email = ?'
          ).bind(userData.email).first();
        } catch (error) {
          console.log('尝试使用大写表名');
          existingUser = await db.prepare(
            'SELECT id FROM Users WHERE email = ?'
          ).bind(userData.email).first();
        }

        if (existingUser) {
          console.log(`用户 ${userData.email} 已存在，跳过`);
          userIds[userData.email] = existingUser.id;
          continue;
        }

        // 创建新用户
        let result;
        try {
          result = await db.prepare(
            'INSERT INTO users (email, name, password, role, uuid, is_test_data) VALUES (?, ?, ?, ?, ?, ?) RETURNING id'
          ).bind(
            userData.email,
            userData.name,
            userData.password,
            userData.role,
            userData.uuid,
            userData.isTestData ? 1 : 0
          ).first();
        } catch (error) {
          console.log('尝试使用大写表名和字段名');
          result = await db.prepare(
            'INSERT INTO Users (email, name, password, role, uuid, isTestData) VALUES (?, ?, ?, ?, ?, ?) RETURNING id'
          ).bind(
            userData.email,
            userData.name,
            userData.password,
            userData.role,
            userData.uuid,
            userData.isTestData ? 1 : 0
          ).first();
        }

        if (result && result.id) {
          userIds[userData.email] = result.id;
        }

        console.log(`用户 ${userData.email} 创建成功`);
      } catch (error) {
        console.error(`导入用户 ${userData.email} 失败:`, error);
      }
    }

    // 生成问卷数据
    const questionnaires = generateQuestionnaires(userIds);
    console.log(`生成了 ${questionnaires.length} 份问卷`);

    // 导入问卷数据
    for (const questionnaireData of questionnaires) {
      try {
        // 创建待审核内容
        try {
          await db.prepare(
            'INSERT INTO pending_content (sequence_number, type, original_content, sanitized_content, status, priority, origin_ip, user_agent, is_test_data) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)'
          ).bind(
            questionnaireData.sequenceNumber,
            questionnaireData.type,
            questionnaireData.originalContent,
            questionnaireData.sanitizedContent,
            questionnaireData.status,
            questionnaireData.priority,
            questionnaireData.originIp,
            questionnaireData.userAgent,
            questionnaireData.isTestData ? 1 : 0
          ).run();
        } catch (error) {
          console.log('尝试使用大写表名和字段名');
          await db.prepare(
            'INSERT INTO PendingContent (sequenceNumber, type, originalContent, sanitizedContent, status, priority, originIp, userAgent, isTestData) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)'
          ).bind(
            questionnaireData.sequenceNumber,
            questionnaireData.type,
            questionnaireData.originalContent,
            questionnaireData.sanitizedContent,
            questionnaireData.status,
            questionnaireData.priority,
            questionnaireData.originIp,
            questionnaireData.userAgent,
            questionnaireData.isTestData ? 1 : 0
          ).run();
        }

        console.log(`问卷 ${questionnaireData.sequenceNumber} 创建成功`);
      } catch (error) {
        console.error(`导入问卷 ${questionnaireData.sequenceNumber} 失败:`, error);
      }
    }

    // 生成故事数据
    const stories = generateStories(userIds);
    console.log(`生成了 ${stories.length} 条故事`);

    // 导入故事数据
    for (const storyData of stories) {
      try {
        // 创建待审核内容
        try {
          await db.prepare(
            'INSERT INTO pending_content (sequence_number, type, original_content, sanitized_content, status, priority, origin_ip, user_agent, is_test_data) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)'
          ).bind(
            storyData.sequenceNumber,
            storyData.type,
            storyData.originalContent,
            storyData.sanitizedContent,
            storyData.status,
            storyData.priority,
            storyData.originIp,
            storyData.userAgent,
            storyData.isTestData ? 1 : 0
          ).run();
        } catch (error) {
          console.log('尝试使用大写表名和字段名');
          await db.prepare(
            'INSERT INTO PendingContent (sequenceNumber, type, originalContent, sanitizedContent, status, priority, originIp, userAgent, isTestData) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)'
          ).bind(
            storyData.sequenceNumber,
            storyData.type,
            storyData.originalContent,
            storyData.sanitizedContent,
            storyData.status,
            storyData.priority,
            storyData.originIp,
            storyData.userAgent,
            storyData.isTestData ? 1 : 0
          ).run();
        }

        console.log(`故事 ${storyData.sequenceNumber} 创建成功`);
      } catch (error) {
        console.error(`导入故事 ${storyData.sequenceNumber} 失败:`, error);
      }
    }

    console.log('所有测试数据导入完成！');

    return { success: true, message: '测试数据导入成功' };
  } catch (error) {
    console.error('导入测试数据失败:', error);
    return { success: false, error: '导入测试数据失败' };
  }
}

/**
 * 清除测试数据
 */
export async function clearTestData(env: Env) {
  try {
    console.log('开始清除测试数据...');

    // 获取数据库连接
    const db = env.DB;

    // 删除待审核内容
    let deletedPendingContent;
    try {
      deletedPendingContent = await db.prepare(
        'DELETE FROM pending_content WHERE is_test_data = 1'
      ).run();
    } catch (error) {
      console.log('尝试使用大写表名');
      deletedPendingContent = await db.prepare(
        'DELETE FROM PendingContent WHERE isTestData = 1'
      ).run();
    }
    console.log(`已删除 ${deletedPendingContent.count} 条待审核内容`);

    // 删除问卷
    let deletedQuestionnaires;
    try {
      deletedQuestionnaires = await db.prepare(
        'DELETE FROM questionnaire_responses WHERE is_test_data = 1'
      ).run();
    } catch (error) {
      console.log('尝试使用大写表名');
      deletedQuestionnaires = await db.prepare(
        'DELETE FROM QuestionnaireResponse WHERE isTestData = 1'
      ).run();
    }
    console.log(`已删除 ${deletedQuestionnaires.count} 份问卷`);

    // 删除故事
    let deletedStories;
    try {
      deletedStories = await db.prepare(
        'DELETE FROM stories WHERE is_test_data = 1'
      ).run();
    } catch (error) {
      console.log('尝试使用大写表名');
      deletedStories = await db.prepare(
        'DELETE FROM Story WHERE isTestData = 1'
      ).run();
    }
    console.log(`已删除 ${deletedStories.count} 条故事`);

    // 删除用户
    let deletedUsers;
    try {
      deletedUsers = await db.prepare(
        'DELETE FROM users WHERE is_test_data = 1'
      ).run();
    } catch (error) {
      console.log('尝试使用大写表名');
      deletedUsers = await db.prepare(
        'DELETE FROM Users WHERE isTestData = 1'
      ).run();
    }
    console.log(`已删除 ${deletedUsers.count} 个用户`);

    console.log('所有测试数据清除完成！');

    return { success: true, message: '测试数据清除成功' };
  } catch (error) {
    console.error('清除测试数据失败:', error);
    return { success: false, error: '清除测试数据失败' };
  }
}

/**
 * 获取测试数据状态
 */
export async function getTestDataStatus(env: Env) {
  try {
    // 获取数据库连接
    const db = env.DB;

    // 获取测试数据统计
    // 尝试不同的表名大小写
    let userCount;
    try {
      userCount = await db.prepare(
        'SELECT COUNT(*) as count FROM users WHERE is_test_data = 1'
      ).first();
    } catch (error) {
      console.log('尝试使用大写表名');
      userCount = await db.prepare(
        'SELECT COUNT(*) as count FROM Users WHERE isTestData = 1'
      ).first();
    }

    let pendingContentCount;
    try {
      pendingContentCount = await db.prepare(
        'SELECT COUNT(*) as count FROM pending_content WHERE is_test_data = 1'
      ).first();
    } catch (error) {
      console.log('尝试使用大写表名');
      pendingContentCount = await db.prepare(
        'SELECT COUNT(*) as count FROM PendingContent WHERE isTestData = 1'
      ).first();
    }

    let questionnaireCount;
    try {
      questionnaireCount = await db.prepare(
        'SELECT COUNT(*) as count FROM questionnaire_responses WHERE is_test_data = 1'
      ).first();
    } catch (error) {
      console.log('尝试使用大写表名');
      questionnaireCount = await db.prepare(
        'SELECT COUNT(*) as count FROM QuestionnaireResponse WHERE isTestData = 1'
      ).first();
    }

    let storyCount;
    try {
      storyCount = await db.prepare(
        'SELECT COUNT(*) as count FROM stories WHERE is_test_data = 1'
      ).first();
    } catch (error) {
      console.log('尝试使用大写表名');
      storyCount = await db.prepare(
        'SELECT COUNT(*) as count FROM Story WHERE isTestData = 1'
      ).first();
    }

    return {
      success: true,
      data: {
        userCount: userCount?.count || 0,
        pendingContentCount: pendingContentCount?.count || 0,
        questionnaireCount: questionnaireCount?.count || 0,
        storyCount: storyCount?.count || 0,
        hasTestData: (userCount?.count || 0) > 0 ||
                    (pendingContentCount?.count || 0) > 0 ||
                    (questionnaireCount?.count || 0) > 0 ||
                    (storyCount?.count || 0) > 0
      }
    };
  } catch (error) {
    console.error('获取测试数据状态失败:', error);
    return { success: false, error: '获取测试数据状态失败' };
  }
}
