import React, { useState, useEffect } from 'react';
import { Link, useLocation, useNavigate } from 'react-router-dom';
import { useAuth } from '@/contexts/AuthContext';
import { Button } from '@/components/ui/button';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import {
  DropdownMenu, DropdownMenuContent, DropdownMenuItem,
  DropdownMenuLabel, DropdownMenuSeparator, DropdownMenuTrigger
} from '@/components/ui/dropdown-menu';
import { LogOut, Bell, Settings, ChevronLeft, Menu, User } from 'lucide-react';
import { useToast } from '@/components/ui/use-toast';
import EnhancedSidebarMenu from '../common/EnhancedSidebarMenu';
import { superAdminMenuGroups } from '@/config/menuConfig';
import '../../styles/admin/admin-layout.css';

interface SuperAdminLayoutProps {
  children: React.ReactNode;
}

/**
 * 增强版超级管理员专用布局组件
 *
 * 显示所有菜单项，包括超级管理员专属功能
 * 支持侧边栏折叠功能
 */
const SuperAdminLayout: React.FC<SuperAdminLayoutProps> = ({ children }) => {
  const location = useLocation();
  const navigate = useNavigate();
  const { toast } = useToast();
  const { user, logout } = useAuth();
  const [pendingCount, setPendingCount] = useState<number>(0);

  // 从localStorage加载侧边栏折叠状态
  const [sidebarCollapsed, setSidebarCollapsed] = useState<boolean>(() => {
    try {
      const savedState = localStorage.getItem('superadmin_sidebar_collapsed');
      return savedState === 'true';
    } catch (error) {
      console.error('加载侧边栏状态失败:', error);
      return false;
    }
  });

  // 获取待审核内容数量和初始化菜单状态
  useEffect(() => {
    // 模拟获取待审核内容数量
    setPendingCount(Math.floor(Math.random() * 10) + 1);

    // 标记菜单已初始化，防止路由变化时重置菜单状态
    if (!sessionStorage.getItem('superadmin_menu_initialized')) {
      sessionStorage.setItem('superadmin_menu_initialized', 'true');
    }
  }, []);

  // 检查当前路径是否为管理员路径，如果是则重定向到超级管理员仪表盘
  useEffect(() => {
    if (user?.role === 'superadmin' && location.pathname.startsWith('/admin/') && location.pathname !== '/admin/login') {
      console.log('SuperAdminLayout - 检测到超级管理员在管理员页面，重定向到超级管理员仪表盘');
      navigate('/superadmin/dashboard', { replace: true });
    }
  }, [location.pathname, user?.role, navigate]);

  // 处理登出
  const handleLogout = () => {
    logout();

    toast({
      title: '已登出',
      description: '您已成功退出超级管理员账号',
    });

    // 使用window.location.href进行页面跳转，确保页面刷新
    window.location.href = '/admin/login';
  };

  // 处理侧边栏折叠
  const toggleSidebar = () => {
    const newState = !sidebarCollapsed;
    setSidebarCollapsed(newState);

    // 保存到localStorage
    try {
      localStorage.setItem('superadmin_sidebar_collapsed', newState.toString());
    } catch (error) {
      console.error('保存侧边栏状态失败:', error);
    }
  };

  return (
    <div className="admin-layout">
      <header className="admin-header">
        <div className="logo">
          <h1>就业调查管理系统</h1>
        </div>
        <div className="user-info">
          <div className="flex items-center">
            <Bell className="h-5 w-5 mr-2" />
            <span className="bg-red-500 text-white rounded-full px-2 py-0.5 text-xs mr-4">
              {pendingCount}
            </span>
          </div>
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="ghost" className="flex items-center">
                <Avatar className="h-8 w-8 mr-2">
                  <AvatarImage src="/avatars/admin.png" alt={user?.name} />
                  <AvatarFallback>{user?.name?.charAt(0) || 'A'}</AvatarFallback>
                </Avatar>
                <span className="mr-1">{user?.name || '超级管理员'}</span>
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent className="w-56" align="end">
              <DropdownMenuLabel className="font-normal">
                <div className="flex flex-col space-y-1">
                  <p className="text-sm font-medium leading-none">{user?.name || '超级管理员'}</p>
                  <p className="text-xs leading-none text-muted-foreground">{user?.email || '<EMAIL>'}</p>
                </div>
              </DropdownMenuLabel>
              <DropdownMenuSeparator />
              <DropdownMenuItem asChild>
                <Link to="/superadmin/settings">
                  <Settings className="mr-2 h-4 w-4" />
                  <span>个人设置</span>
                </Link>
              </DropdownMenuItem>
              <DropdownMenuSeparator />
              <DropdownMenuItem onClick={handleLogout}>
                <LogOut className="mr-2 h-4 w-4" />
                <span>退出登录</span>
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        </div>
      </header>

      <div className="admin-content">
        <div className={`sidebar ${sidebarCollapsed ? 'collapsed' : ''}`}>
          <div className="sidebar-header">
            <h2>{sidebarCollapsed ? '' : '超级管理员后台'}</h2>
            <Button
              variant="ghost"
              size="sm"
              onClick={toggleSidebar}
              className="sidebar-toggle"
            >
              {sidebarCollapsed ? <Menu className="h-4 w-4" /> : <ChevronLeft className="h-4 w-4" />}
            </Button>
          </div>

          <EnhancedSidebarMenu
            groups={superAdminMenuGroups}
            role="superadmin"
            collapsed={sidebarCollapsed}
          />
        </div>

        <div className={`main-content ${sidebarCollapsed ? 'expanded' : ''}`}>
          {children}
        </div>
      </div>
    </div>
  );
};

export default SuperAdminLayout;
