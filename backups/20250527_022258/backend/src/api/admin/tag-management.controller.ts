import { Context } from 'hono';
import { Env } from '../../types';
import { KVStorageService } from '../../services/kvStorageService';
import { WebSocketType, WebSocketMessageType } from '../../services/websocket.service';
import config from '../../config';

/**
 * 获取标签列表
 */
export const getTags = async (c: Context<{ Bindings: Env }>) => {
  try {
    // 获取标签列表
    const kvStorage = new KVStorageService(c.env);
    const tags = await kvStorage.getTags();

    return c.json({
      success: true,
      tags,
    });
  } catch (error) {
    console.error('获取标签列表失败', error);
    return c.json({
      success: false,
      error: '服务器错误，请稍后再试',
    }, 500);
  }
};

/**
 * 创建标签
 */
export const createTag = async (c: Context<{ Bindings: Env }>) => {
  try {
    const data = await c.req.json();

    // 验证数据
    if (!data.name) {
      return c.json({
        success: false,
        error: '标签名称不能为空',
      }, 400);
    }

    // 生成标签ID
    const id = data.name
      .toLowerCase()
      .replace(/\s+/g, '-')
      .replace(/[^a-z0-9-]/g, '');

    // 检查ID是否已存在
    const kvStorage = new KVStorageService(c.env);
    const existingTags = await kvStorage.getTags();

    if (existingTags.some(tag => tag.id === id)) {
      return c.json({
        success: false,
        error: '标签ID已存在',
      }, 400);
    }

    // 创建标签
    const newTag = {
      id,
      name: data.name,
      color: data.color || 'blue',
      priority: data.priority || 0,
      category: data.category || 'other',
      parentId: data.parentId || null,
      count: 0,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
    };

    // 保存标签
    const updatedTags = [...existingTags, newTag];
    await kvStorage.saveTags(updatedTags);

    // 添加审计日志
    console.log('管理员创建标签', {
      adminId: c.get('user')?.username,
      action: 'createTag',
      tagData: newTag,
    });

    // 发送WebSocket通知
    const webSocketService = c.get('webSocketService');
    if (webSocketService) {
      webSocketService.broadcast(WebSocketType.STORY_WALL, {
        type: WebSocketMessageType.DATA_UPDATE,
        data: {
          action: 'create',
          entityType: 'tag',
          entityId: id,
          entity: newTag,
        },
      });
    }

    return c.json({
      success: true,
      tag: newTag,
    });
  } catch (error) {
    console.error('创建标签失败', error);
    return c.json({
      success: false,
      error: '服务器错误，请稍后再试',
    }, 500);
  }
};

/**
 * 更新标签
 */
export const updateTag = async (c: Context<{ Bindings: Env }>) => {
  try {
    const { id } = c.req.param();
    const data = await c.req.json();

    // 验证数据
    if (!id) {
      return c.json({
        success: false,
        error: '标签ID不能为空',
      }, 400);
    }

    // 获取标签列表
    const kvStorage = new KVStorageService(c.env);
    const tags = await kvStorage.getTags();

    // 查找标签
    const tagIndex = tags.findIndex(tag => tag.id === id);

    if (tagIndex === -1) {
      return c.json({
        success: false,
        error: '标签不存在',
      }, 404);
    }

    // 更新标签
    const updatedTag = {
      ...tags[tagIndex],
      name: data.name || tags[tagIndex].name,
      color: data.color || tags[tagIndex].color,
      priority: data.priority !== undefined ? data.priority : tags[tagIndex].priority,
      category: data.category || tags[tagIndex].category,
      parentId: data.parentId !== undefined ? data.parentId : tags[tagIndex].parentId,
      updatedAt: new Date().toISOString(),
    };

    // 保存标签
    tags[tagIndex] = updatedTag;
    await kvStorage.saveTags(tags);

    // 添加审计日志
    console.log('管理员更新标签', {
      adminId: c.get('user')?.username,
      action: 'updateTag',
      tagId: id,
      tagData: updatedTag,
    });

    // 发送WebSocket通知
    const webSocketService = c.get('webSocketService');
    if (webSocketService) {
      webSocketService.broadcast(WebSocketType.STORY_WALL, {
        type: WebSocketMessageType.DATA_UPDATE,
        data: {
          action: 'update',
          entityType: 'tag',
          entityId: id,
          entity: updatedTag,
        },
      });
    }

    return c.json({
      success: true,
      tag: updatedTag,
    });
  } catch (error) {
    console.error('更新标签失败', error);
    return c.json({
      success: false,
      error: '服务器错误，请稍后再试',
    }, 500);
  }
};

/**
 * 删除标签
 */
export const deleteTag = async (c: Context<{ Bindings: Env }>) => {
  try {
    const { id } = c.req.param();

    // 验证数据
    if (!id) {
      return c.json({
        success: false,
        error: '标签ID不能为空',
      }, 400);
    }

    // 获取标签列表
    const kvStorage = new KVStorageService(c.env);
    const tags = await kvStorage.getTags();

    // 查找标签
    const tagIndex = tags.findIndex(tag => tag.id === id);

    if (tagIndex === -1) {
      return c.json({
        success: false,
        error: '标签不存在',
      }, 404);
    }

    // 检查是否有子标签
    const hasChildren = tags.some(tag => tag.parentId === id);

    if (hasChildren) {
      return c.json({
        success: false,
        error: '无法删除有子标签的标签，请先删除子标签',
      }, 400);
    }

    // 删除标签
    const deletedTag = tags[tagIndex];
    tags.splice(tagIndex, 1);
    await kvStorage.saveTags(tags);

    // 添加审计日志
    console.log('管理员删除标签', {
      adminId: c.get('user')?.username,
      action: 'deleteTag',
      tagId: id,
    });

    // 发送WebSocket通知
    const webSocketService = c.get('webSocketService');
    if (webSocketService) {
      webSocketService.broadcast(WebSocketType.STORY_WALL, {
        type: WebSocketMessageType.DATA_UPDATE,
        data: {
          action: 'delete',
          entityType: 'tag',
          entityId: id,
        },
      });
    }

    return c.json({
      success: true,
    });
  } catch (error) {
    console.error('删除标签失败', error);
    return c.json({
      success: false,
      error: '服务器错误，请稍后再试',
    }, 500);
  }
};

/**
 * 获取标签推荐
 */
export const getTagRecommendations = async (c: Context<{ Bindings: Env }>) => {
  try {
    const { content } = await c.req.json();

    if (!content) {
      return c.json({
        success: false,
        error: '内容不能为空',
      }, 400);
    }

    // 获取所有标签
    const kvStorage = new KVStorageService(c.env);
    const allTags = await kvStorage.getTags();

    // 简单的关键词匹配算法
    const contentLower = content.toLowerCase();
    const recommendedTags = allTags
      .filter(tag => {
        // 检查标签名称或相关关键词是否出现在内容中
        return contentLower.includes(tag.name.toLowerCase());
      })
      .sort((a, b) => b.priority - a.priority || b.count - a.count)
      .slice(0, 5)
      .map(tag => tag.id);

    return c.json({
      success: true,
      tags: recommendedTags,
    });
  } catch (error) {
    console.error('获取标签推荐失败', error);
    return c.json({
      success: false,
      error: '服务器错误，请稍后再试',
    }, 500);
  }
};

// ==================== 新增功能 ====================

/**
 * 获取单个标签详情
 */
export const getTagById = async (c: Context<{ Bindings: Env }>) => {
  try {
    const { id } = c.req.param();

    if (!id) {
      return c.json({
        success: false,
        error: '标签ID不能为空',
      }, 400);
    }

    // 获取标签列表
    const kvStorage = new KVStorageService(c.env);
    const tags = await kvStorage.getTags();

    // 查找标签
    const tag = tags.find(tag => tag.id === id);

    if (!tag) {
      return c.json({
        success: false,
        error: '标签不存在',
      }, 404);
    }

    return c.json({
      success: true,
      tag,
    });
  } catch (error) {
    console.error('获取标签详情失败', error);
    return c.json({
      success: false,
      error: '服务器错误，请稍后再试',
    }, 500);
  }
};

/**
 * 获取标签使用统计
 */
export const getTagStats = async (c: Context<{ Bindings: Env }>) => {
  try {
    const { id } = c.req.param();

    if (!id) {
      return c.json({
        success: false,
        error: '标签ID不能为空',
      }, 400);
    }

    // 这里应该从数据库获取真实的使用统计
    // 目前返回模拟数据
    const stats = {
      tagId: id,
      totalUsage: Math.floor(Math.random() * 1000),
      recentUsage: Math.floor(Math.random() * 100),
      usageByType: {
        story: Math.floor(Math.random() * 500),
        questionnaire: Math.floor(Math.random() * 300),
        comment: Math.floor(Math.random() * 200)
      },
      usageTrend: Array.from({ length: 7 }, (_, i) => ({
        date: new Date(Date.now() - i * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
        count: Math.floor(Math.random() * 50)
      })).reverse()
    };

    return c.json({
      success: true,
      stats,
    });
  } catch (error) {
    console.error('获取标签统计失败', error);
    return c.json({
      success: false,
      error: '服务器错误，请稍后再试',
    }, 500);
  }
};

/**
 * 批量操作标签
 */
export const batchOperateTags = async (c: Context<{ Bindings: Env }>) => {
  try {
    const { action, tagIds, data } = await c.req.json();

    if (!action || !Array.isArray(tagIds) || tagIds.length === 0) {
      return c.json({
        success: false,
        error: '操作类型和标签ID列表不能为空',
      }, 400);
    }

    const kvStorage = new KVStorageService(c.env);
    const tags = await kvStorage.getTags();

    let processed = 0;
    let failed = 0;

    switch (action) {
      case 'delete':
        for (const tagId of tagIds) {
          const tagIndex = tags.findIndex(tag => tag.id === tagId);
          if (tagIndex !== -1) {
            // 检查是否有子标签
            const hasChildren = tags.some(tag => tag.parentId === tagId);
            if (!hasChildren) {
              tags.splice(tagIndex, 1);
              processed++;
            } else {
              failed++;
            }
          } else {
            failed++;
          }
        }
        break;

      case 'updateCategory':
        if (!data.category) {
          return c.json({
            success: false,
            error: '新分类不能为空',
          }, 400);
        }
        for (const tagId of tagIds) {
          const tagIndex = tags.findIndex(tag => tag.id === tagId);
          if (tagIndex !== -1) {
            tags[tagIndex].category = data.category;
            tags[tagIndex].updatedAt = new Date().toISOString();
            processed++;
          } else {
            failed++;
          }
        }
        break;

      default:
        return c.json({
          success: false,
          error: '不支持的操作类型',
        }, 400);
    }

    // 保存更新后的标签
    await kvStorage.saveTags(tags);

    // 添加审计日志
    console.log('管理员批量操作标签', {
      adminId: c.get('user')?.username,
      action: 'batchOperateTags',
      operation: action,
      tagIds,
      processed,
      failed,
    });

    return c.json({
      success: true,
      processed,
      failed,
      message: `批量操作完成，成功处理 ${processed} 个，失败 ${failed} 个`,
    });
  } catch (error) {
    console.error('批量操作标签失败', error);
    return c.json({
      success: false,
      error: '服务器错误，请稍后再试',
    }, 500);
  }
};

/**
 * 获取标签分类列表
 */
export const getTagCategories = async (c: Context<{ Bindings: Env }>) => {
  try {
    // 预定义的标签分类
    const categories = [
      { value: 'general', label: '通用标签', description: '通用的标签分类' },
      { value: 'emotion', label: '情感标签', description: '表达情感和感受的标签' },
      { value: 'topic', label: '话题标签', description: '特定话题和主题的标签' },
      { value: 'quality', label: '质量标签', description: '内容质量相关的标签' },
      { value: 'system', label: '系统标签', description: '系统自动生成的标签' }
    ];

    return c.json({
      success: true,
      categories,
    });
  } catch (error) {
    console.error('获取标签分类失败', error);
    return c.json({
      success: false,
      error: '服务器错误，请稍后再试',
    }, 500);
  }
};

/**
 * 创建标签分类
 */
export const createTagCategory = async (c: Context<{ Bindings: Env }>) => {
  try {
    const { value, label, description } = await c.req.json();

    if (!value || !label) {
      return c.json({
        success: false,
        error: '分类值和标签不能为空',
      }, 400);
    }

    // 这里应该保存到数据库
    // 目前只返回成功响应
    const newCategory = {
      value,
      label,
      description: description || '',
      createdAt: new Date().toISOString(),
    };

    console.log('管理员创建标签分类', {
      adminId: c.get('user')?.username,
      action: 'createTagCategory',
      category: newCategory,
    });

    return c.json({
      success: true,
      category: newCategory,
    });
  } catch (error) {
    console.error('创建标签分类失败', error);
    return c.json({
      success: false,
      error: '服务器错误，请稍后再试',
    }, 500);
  }
};

/**
 * 更新标签分类
 */
export const updateTagCategory = async (c: Context<{ Bindings: Env }>) => {
  try {
    const { category } = c.req.param();
    const { label, description } = await c.req.json();

    if (!category) {
      return c.json({
        success: false,
        error: '分类值不能为空',
      }, 400);
    }

    // 这里应该更新数据库中的分类
    // 目前只返回成功响应
    const updatedCategory = {
      value: category,
      label: label || category,
      description: description || '',
      updatedAt: new Date().toISOString(),
    };

    console.log('管理员更新标签分类', {
      adminId: c.get('user')?.username,
      action: 'updateTagCategory',
      category: updatedCategory,
    });

    return c.json({
      success: true,
      category: updatedCategory,
    });
  } catch (error) {
    console.error('更新标签分类失败', error);
    return c.json({
      success: false,
      error: '服务器错误，请稍后再试',
    }, 500);
  }
};

/**
 * 删除标签分类
 */
export const deleteTagCategory = async (c: Context<{ Bindings: Env }>) => {
  try {
    const { category } = c.req.param();

    if (!category) {
      return c.json({
        success: false,
        error: '分类值不能为空',
      }, 400);
    }

    // 检查是否有标签使用此分类
    const kvStorage = new KVStorageService(c.env);
    const tags = await kvStorage.getTags();
    const hasTagsInCategory = tags.some(tag => tag.category === category);

    if (hasTagsInCategory) {
      return c.json({
        success: false,
        error: '无法删除包含标签的分类，请先移动或删除相关标签',
      }, 400);
    }

    // 这里应该从数据库删除分类
    // 目前只返回成功响应

    console.log('管理员删除标签分类', {
      adminId: c.get('user')?.username,
      action: 'deleteTagCategory',
      category,
    });

    return c.json({
      success: true,
    });
  } catch (error) {
    console.error('删除标签分类失败', error);
    return c.json({
      success: false,
      error: '服务器错误，请稍后再试',
    }, 500);
  }
};

/**
 * 获取标签使用统计分析
 */
export const getTagUsageAnalytics = async (c: Context<{ Bindings: Env }>) => {
  try {
    // 这里应该从数据库获取真实的统计数据
    // 目前返回模拟数据
    const analytics = {
      totalTags: Math.floor(Math.random() * 100) + 50,
      activeTags: Math.floor(Math.random() * 80) + 30,
      unusedTags: Math.floor(Math.random() * 20) + 5,
      topUsedTags: Array.from({ length: 10 }, (_, i) => ({
        id: `tag-${i + 1}`,
        name: `标签${i + 1}`,
        usageCount: Math.floor(Math.random() * 500) + 100,
        category: ['general', 'emotion', 'topic'][Math.floor(Math.random() * 3)]
      })),
      categoryStats: [
        { category: 'general', count: Math.floor(Math.random() * 30) + 10 },
        { category: 'emotion', count: Math.floor(Math.random() * 25) + 8 },
        { category: 'topic', count: Math.floor(Math.random() * 20) + 5 },
        { category: 'quality', count: Math.floor(Math.random() * 15) + 3 },
        { category: 'system', count: Math.floor(Math.random() * 10) + 2 }
      ],
      usageTrend: Array.from({ length: 30 }, (_, i) => ({
        date: new Date(Date.now() - i * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
        count: Math.floor(Math.random() * 100) + 20
      })).reverse()
    };

    return c.json({
      success: true,
      analytics,
    });
  } catch (error) {
    console.error('获取标签使用统计分析失败', error);
    return c.json({
      success: false,
      error: '服务器错误，请稍后再试',
    }, 500);
  }
};

/**
 * 获取标签趋势分析
 */
export const getTagTrends = async (c: Context<{ Bindings: Env }>) => {
  try {
    const { period = 'month', category } = c.req.query();

    // 这里应该从数据库获取真实的趋势数据
    // 目前返回模拟数据
    const trends = {
      period,
      category,
      emergingTags: Array.from({ length: 5 }, (_, i) => ({
        id: `emerging-${i + 1}`,
        name: `新兴标签${i + 1}`,
        growthRate: Math.floor(Math.random() * 200) + 50,
        currentUsage: Math.floor(Math.random() * 100) + 10
      })),
      decliningTags: Array.from({ length: 5 }, (_, i) => ({
        id: `declining-${i + 1}`,
        name: `衰退标签${i + 1}`,
        declineRate: Math.floor(Math.random() * 50) + 10,
        currentUsage: Math.floor(Math.random() * 50) + 5
      })),
      stableTags: Array.from({ length: 10 }, (_, i) => ({
        id: `stable-${i + 1}`,
        name: `稳定标签${i + 1}`,
        averageUsage: Math.floor(Math.random() * 200) + 50,
        variance: Math.floor(Math.random() * 20) + 5
      }))
    };

    return c.json({
      success: true,
      trends,
    });
  } catch (error) {
    console.error('获取标签趋势分析失败', error);
    return c.json({
      success: false,
      error: '服务器错误，请稍后再试',
    }, 500);
  }
};

/**
 * 获取标签关联分析
 */
export const getTagCorrelations = async (c: Context<{ Bindings: Env }>) => {
  try {
    // 这里应该从数据库分析标签之间的关联关系
    // 目前返回模拟数据
    const correlations = {
      strongCorrelations: Array.from({ length: 10 }, (_, i) => ({
        tag1: `标签${i + 1}`,
        tag2: `标签${i + 2}`,
        correlation: (Math.random() * 0.5 + 0.5).toFixed(3), // 0.5-1.0
        coOccurrence: Math.floor(Math.random() * 100) + 20
      })),
      weakCorrelations: Array.from({ length: 10 }, (_, i) => ({
        tag1: `标签${i + 11}`,
        tag2: `标签${i + 12}`,
        correlation: (Math.random() * 0.3 + 0.2).toFixed(3), // 0.2-0.5
        coOccurrence: Math.floor(Math.random() * 50) + 5
      })),
      tagClusters: Array.from({ length: 5 }, (_, i) => ({
        clusterId: i + 1,
        tags: Array.from({ length: Math.floor(Math.random() * 5) + 3 }, (_, j) => `集群${i + 1}标签${j + 1}`),
        coherence: (Math.random() * 0.4 + 0.6).toFixed(3) // 0.6-1.0
      }))
    };

    return c.json({
      success: true,
      correlations,
    });
  } catch (error) {
    console.error('获取标签关联分析失败', error);
    return c.json({
      success: false,
      error: '服务器错误，请稍后再试',
    }, 500);
  }
};