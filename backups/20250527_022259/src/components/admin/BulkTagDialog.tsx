import { useState } from 'react';
import { useToast } from '@/components/ui/use-toast';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { 
  Dialog, 
  DialogContent, 
  DialogDescription, 
  DialogFooter, 
  DialogHeader, 
  DialogTitle 
} from '@/components/ui/dialog';
import { 
  Select, 
  SelectContent, 
  SelectItem, 
  SelectTrigger, 
  SelectValue 
} from '@/components/ui/select';
import { Label } from '@/components/ui/label';
import { Loader2, Tag, X } from 'lucide-react';
import { Badge } from '@/components/ui/badge';

interface BulkTagDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onConfirm: (tags: string[]) => Promise<void>;
  selectedCount: number;
  existingTags?: string[];
}

export default function BulkTagDialog({
  open,
  onOpenChange,
  onConfirm,
  selectedCount,
  existingTags = [],
}: BulkTagDialogProps) {
  const { toast } = useToast();
  const [isProcessing, setIsProcessing] = useState(false);
  const [selectedTags, setSelectedTags] = useState<string[]>([]);
  const [newTag, setNewTag] = useState('');
  
  // 预定义的标签选项
  const predefinedTags = [
    '重要', '待处理', '已处理', '已验证', '需跟进',
    '高优先级', '中优先级', '低优先级',
    '有效数据', '无效数据', '异常数据',
    ...existingTags.filter(tag => !predefinedTags.includes(tag))
  ];
  
  // 处理添加新标签
  const handleAddTag = () => {
    if (!newTag.trim()) return;
    
    // 检查标签是否已存在
    if (selectedTags.includes(newTag.trim())) {
      toast({
        variant: 'destructive',
        title: '标签已存在',
        description: '请输入一个不同的标签',
      });
      return;
    }
    
    setSelectedTags([...selectedTags, newTag.trim()]);
    setNewTag('');
  };
  
  // 处理删除标签
  const handleRemoveTag = (tag: string) => {
    setSelectedTags(selectedTags.filter(t => t !== tag));
  };
  
  // 处理选择预定义标签
  const handleSelectTag = (tag: string) => {
    if (selectedTags.includes(tag)) return;
    setSelectedTags([...selectedTags, tag]);
  };
  
  // 处理确认
  const handleConfirm = async () => {
    if (selectedTags.length === 0) {
      toast({
        variant: 'destructive',
        title: '请选择至少一个标签',
        description: '您需要选择或创建至少一个标签',
      });
      return;
    }
    
    try {
      setIsProcessing(true);
      await onConfirm(selectedTags);
      
      // 重置状态
      setSelectedTags([]);
      setNewTag('');
      
      // 关闭对话框
      onOpenChange(false);
      
      toast({
        title: '标签添加成功',
        description: `已为 ${selectedCount} 条数据添加标签`,
      });
    } catch (error) {
      console.error('添加标签错误:', error);
      toast({
        variant: 'destructive',
        title: '添加标签失败',
        description: '服务器错误，请稍后再试',
      });
    } finally {
      setIsProcessing(false);
    }
  };
  
  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-md">
        <DialogHeader>
          <DialogTitle>批量添加标签</DialogTitle>
          <DialogDescription>
            为选中的 {selectedCount} 条数据添加标签
          </DialogDescription>
        </DialogHeader>
        
        <div className="grid gap-4 py-4">
          {/* 已选标签 */}
          {selectedTags.length > 0 && (
            <div className="space-y-2">
              <Label>已选标签</Label>
              <div className="flex flex-wrap gap-2">
                {selectedTags.map(tag => (
                  <Badge key={tag} variant="secondary" className="flex items-center gap-1">
                    <Tag className="h-3 w-3" />
                    {tag}
                    <button 
                      onClick={() => handleRemoveTag(tag)}
                      className="ml-1 rounded-full hover:bg-muted p-0.5"
                    >
                      <X className="h-3 w-3" />
                      <span className="sr-only">移除</span>
                    </button>
                  </Badge>
                ))}
              </div>
            </div>
          )}
          
          {/* 添加新标签 */}
          <div className="grid gap-2">
            <Label htmlFor="newTag">添加新标签</Label>
            <div className="flex gap-2">
              <Input
                id="newTag"
                value={newTag}
                onChange={(e) => setNewTag(e.target.value)}
                placeholder="输入新标签"
                className="flex-1"
                onKeyDown={(e) => {
                  if (e.key === 'Enter') {
                    e.preventDefault();
                    handleAddTag();
                  }
                }}
              />
              <Button type="button" onClick={handleAddTag}>添加</Button>
            </div>
          </div>
          
          {/* 预定义标签 */}
          <div className="grid gap-2">
            <Label>选择预定义标签</Label>
            <div className="flex flex-wrap gap-2">
              {predefinedTags
                .filter(tag => !selectedTags.includes(tag))
                .map(tag => (
                  <Badge 
                    key={tag} 
                    variant="outline" 
                    className="cursor-pointer hover:bg-secondary"
                    onClick={() => handleSelectTag(tag)}
                  >
                    <Tag className="mr-1 h-3 w-3" />
                    {tag}
                  </Badge>
                ))
              }
            </div>
          </div>
        </div>
        
        <DialogFooter>
          <Button
            type="button"
            variant="outline"
            onClick={() => onOpenChange(false)}
            disabled={isProcessing}
          >
            取消
          </Button>
          <Button 
            type="button" 
            onClick={handleConfirm}
            disabled={isProcessing || selectedTags.length === 0}
          >
            {isProcessing && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
            确认添加
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
