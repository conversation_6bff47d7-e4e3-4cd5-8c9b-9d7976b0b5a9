import { Hono } from 'hono';
import { PrismaClient } from '@prisma/client';
import { zValidator } from '@hono/zod-validator';
import { z } from 'zod';
import { adminAuth } from '../middlewares/adminAuth';
import type { Env } from '../types';
import { getPrismaClient } from '../utils/prisma';
import { mockStatistics, shouldUseMockData } from '../mock';

// 创建Hono应用实例
const app = new Hono<{ Bindings: Env }>();

// 定义过滤器schema
const analyticsFilterSchema = z.object({
  startDate: z.string().optional(),
  endDate: z.string().optional(),
  educationLevel: z.string().optional(),
  region: z.string().optional(),
  employmentStatus: z.string().optional(),
  graduationYear: z.number().optional(),
});

// 获取问卷数据统计
app.get('/stats', async (c) => {
  try {
    console.log('Development mode: Returning mock analytics stats');

    // 返回模拟数据
    return c.json({
      success: true,
      stats: {
        totalResponses: 8742,
        verifiedResponses: 6521,
        anonymousResponses: 2221,
        responsesByDate: [
          { date: '2025-01', count: 1245 },
          { date: '2025-02', count: 1356 },
          { date: '2025-03', count: 1478 },
          { date: '2025-04', count: 1589 },
          { date: '2025-05', count: 1632 },
          { date: '2025-06', count: 1442 }
        ],
        educationLevels: [
          { level: '本科', count: 5842 },
          { level: '硕士', count: 2341 },
          { level: '博士', count: 559 }
        ],
        regions: [
          { region: '北京', count: 1245 },
          { region: '上海', count: 1356 },
          { region: '广州', count: 987 },
          { region: '深圳', count: 876 },
          { region: '杭州', count: 765 },
          { region: '南京', count: 654 },
          { region: '武汉', count: 543 },
          { region: '成都', count: 432 },
          { region: '西安', count: 321 },
          { region: '其他', count: 1563 }
        ],
        employmentStatus: [
          { status: '全职', count: 5842 },
          { status: '兼职', count: 1245 },
          { status: '自由职业', count: 876 },
          { status: '创业', count: 543 },
          { status: '待业', count: 236 }
        ],
        industries: [
          { industry: '互联网/IT', count: 2341 },
          { industry: '金融', count: 1245 },
          { industry: '教育', count: 987 },
          { industry: '医疗', count: 876 },
          { industry: '制造业', count: 765 },
          { industry: '服务业', count: 654 },
          { industry: '其他', count: 1874 }
        ],
        salaryRanges: [
          { range: '5k以下', count: 543 },
          { range: '5k-10k', count: 1245 },
          { range: '10k-15k', count: 1874 },
          { range: '15k-20k', count: 2341 },
          { range: '20k-30k', count: 1563 },
          { range: '30k以上', count: 1176 }
        ],
        jobSatisfaction: [
          { level: '非常满意', count: 1245 },
          { level: '满意', count: 3214 },
          { level: '一般', count: 2341 },
          { level: '不满意', count: 1176 },
          { level: '非常不满意', count: 766 }
        ],
        careerChangeIntention: [
          { intention: '有意向', count: 3214 },
          { intention: '无意向', count: 5528 }
        ]
      }
    });
  } catch (error) {
    console.error('Error getting analytics stats:', error);
    return c.json({ success: false, error: 'Failed to get analytics stats' }, 500);
  }
});

// 导出数据API
app.get('/export', adminAuth, zValidator('query', analyticsFilterSchema), async (c) => {
  try {
    // 获取查询参数
    const filters = c.req.valid('query');
    const format = c.req.query('format') || 'csv';

    // 检查是否应该使用模拟数据
    if (shouldUseMockData(c.env)) {
      console.log('Development mode: Returning mock export URL');

      return c.json({
        success: true,
        exportUrl: `${c.env.BASE_URL}/mock-exports/questionnaire-data.${format}`,
        expiresAt: new Date(Date.now() + 24 * 60 * 60 * 1000).toISOString(),
      });
    }

    // 生产环境实现
    // 这里需要实现实际的数据导出逻辑，生成文件并返回下载URL

    return c.json({
      success: true,
      exportUrl: `${c.env.BASE_URL}/exports/questionnaire-data-${Date.now()}.${format}`,
      expiresAt: new Date(Date.now() + 24 * 60 * 60 * 1000).toISOString(),
    });
  } catch (error) {
    console.error('Error exporting data:', error);
    return c.json({ success: false, error: 'Failed to export data' }, 500);
  }
});

export { app as analytics };
