import React, { useState, useEffect } from 'react';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { Tabs, Ta<PERSON>Content, Ta<PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import { Button } from '@/components/ui/button';
import { useToast } from '@/components/ui/use-toast';
import { 
  BarChart, 
  Bar, 
  LineChart, 
  Line, 
  PieChart, 
  Pie, 
  Cell, 
  XAxis, 
  YAxis, 
  CartesianGrid, 
  Tooltip, 
  Legend, 
  ResponsiveContainer 
} from 'recharts';
import { 
  Calendar, 
  CheckCircle, 
  Clock, 
  Download, 
  Filter, 
  RefreshCw, 
  XCircle 
} from 'lucide-react';
import { useApi } from '@/hooks/useApi';
import { DateRangePicker } from '@/components/ui/date-range-picker';
import { format } from 'date-fns';

/**
 * 自动审核统计仪表板组件
 */
const AutoModerationStatsDashboard: React.FC = () => {
  const { toast } = useToast();
  const api = useApi();
  const [isLoading, setIsLoading] = useState<boolean>(true);
  const [stats, setStats] = useState<any>(null);
  const [dateRange, setDateRange] = useState<{ from: Date; to: Date } | undefined>({
    from: new Date(new Date().setDate(new Date().getDate() - 30)),
    to: new Date()
  });
  const [activeTab, setActiveTab] = useState<string>('overview');
  
  // 颜色配置
  const COLORS = {
    approve: '#10b981',
    reject: '#ef4444',
    review: '#f59e0b',
    story: '#3b82f6',
    questionnaire: '#8b5cf6',
    comment: '#ec4899',
    profile: '#6366f1',
    feedback: '#14b8a6',
    low: '#84cc16',
    medium: '#f59e0b',
    high: '#ef4444'
  };
  
  // 加载统计数据
  const loadStats = async () => {
    setIsLoading(true);
    
    try {
      // 构建查询参数
      const params = new URLSearchParams();
      if (dateRange?.from) {
        params.append('startDate', format(dateRange.from, 'yyyy-MM-dd'));
      }
      if (dateRange?.to) {
        params.append('endDate', format(dateRange.to, 'yyyy-MM-dd'));
      }
      
      // 发送请求
      const response = await api.get(`/admin/auto-moderation/stats?${params.toString()}`);
      
      if (response.success) {
        setStats(response.stats);
      } else {
        toast({
          title: '加载失败',
          description: response.error || '无法加载审核统计数据',
          variant: 'destructive'
        });
      }
    } catch (error) {
      console.error('加载审核统计数据失败:', error);
      toast({
        title: '加载失败',
        description: '服务器错误，请稍后再试',
        variant: 'destructive'
      });
    } finally {
      setIsLoading(false);
    }
  };
  
  // 导出统计数据
  const exportStats = () => {
    if (!stats) return;
    
    try {
      // 创建CSV内容
      const csvContent = [
        // 表头
        ['日期', '总数', '通过', '拒绝', '人工审核'].join(','),
        // 数据行
        ...stats.daily.map((item: any) => [
          item.date,
          item.count,
          item.approvedCount || 0,
          item.rejectedCount || 0,
          item.reviewCount || 0
        ].join(','))
      ].join('\n');
      
      // 创建Blob
      const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
      
      // 创建下载链接
      const link = document.createElement('a');
      const url = URL.createObjectURL(blob);
      link.setAttribute('href', url);
      link.setAttribute('download', `auto-moderation-stats-${format(new Date(), 'yyyy-MM-dd')}.csv`);
      link.style.visibility = 'hidden';
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
    } catch (error) {
      console.error('导出统计数据失败:', error);
      toast({
        title: '导出失败',
        description: '导出统计数据失败，请稍后再试',
        variant: 'destructive'
      });
    }
  };
  
  // 组件加载时加载统计数据
  useEffect(() => {
    loadStats();
  }, [dateRange]);
  
  // 渲染加载状态
  if (isLoading) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>自动审核统计</CardTitle>
          <CardDescription>加载中...</CardDescription>
        </CardHeader>
        <CardContent className="flex items-center justify-center py-10">
          <RefreshCw className="w-8 h-8 animate-spin text-primary" />
        </CardContent>
      </Card>
    );
  }
  
  // 渲染统计仪表板
  return (
    <div className="space-y-4">
      <div className="flex flex-col md:flex-row justify-between gap-4">
        <Card className="flex-1">
          <CardHeader className="pb-2">
            <CardTitle className="text-2xl font-bold">
              {stats?.total || 0}
            </CardTitle>
            <CardDescription>总审核数</CardDescription>
          </CardHeader>
        </Card>
        <Card className="flex-1">
          <CardHeader className="pb-2">
            <CardTitle className="text-2xl font-bold text-green-500">
              {stats?.approved || 0}
            </CardTitle>
            <CardDescription>自动通过</CardDescription>
          </CardHeader>
        </Card>
        <Card className="flex-1">
          <CardHeader className="pb-2">
            <CardTitle className="text-2xl font-bold text-red-500">
              {stats?.rejected || 0}
            </CardTitle>
            <CardDescription>自动拒绝</CardDescription>
          </CardHeader>
        </Card>
        <Card className="flex-1">
          <CardHeader className="pb-2">
            <CardTitle className="text-2xl font-bold text-yellow-500">
              {stats?.review || 0}
            </CardTitle>
            <CardDescription>人工审核</CardDescription>
          </CardHeader>
        </Card>
      </div>
      
      <div className="flex justify-between items-center">
        <DateRangePicker
          value={dateRange}
          onChange={setDateRange}
        />
        <div className="flex gap-2">
          <Button
            variant="outline"
            size="sm"
            onClick={loadStats}
          >
            <RefreshCw className="w-4 h-4 mr-2" />
            刷新
          </Button>
          <Button
            variant="outline"
            size="sm"
            onClick={exportStats}
            disabled={!stats}
          >
            <Download className="w-4 h-4 mr-2" />
            导出
          </Button>
        </div>
      </div>
      
      <Tabs value={activeTab} onValueChange={setActiveTab}>
        <TabsList className="grid w-full grid-cols-3">
          <TabsTrigger value="overview">概览</TabsTrigger>
          <TabsTrigger value="trends">趋势</TabsTrigger>
          <TabsTrigger value="details">详情</TabsTrigger>
        </TabsList>
        
        <TabsContent value="overview" className="space-y-4 mt-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <Card>
              <CardHeader>
                <CardTitle>审核结果分布</CardTitle>
                <CardDescription>按审核结果统计</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="h-[300px]">
                  <ResponsiveContainer width="100%" height="100%">
                    <PieChart>
                      <Pie
                        data={[
                          { name: '自动通过', value: stats?.approved || 0, color: COLORS.approve },
                          { name: '自动拒绝', value: stats?.rejected || 0, color: COLORS.reject },
                          { name: '人工审核', value: stats?.review || 0, color: COLORS.review }
                        ]}
                        cx="50%"
                        cy="50%"
                        labelLine={false}
                        outerRadius={80}
                        fill="#8884d8"
                        dataKey="value"
                        label={({ name, percent }) => `${name}: ${(percent * 100).toFixed(0)}%`}
                      >
                        {[
                          { name: '自动通过', value: stats?.approved || 0, color: COLORS.approve },
                          { name: '自动拒绝', value: stats?.rejected || 0, color: COLORS.reject },
                          { name: '人工审核', value: stats?.review || 0, color: COLORS.review }
                        ].map((entry, index) => (
                          <Cell key={`cell-${index}`} fill={entry.color} />
                        ))}
                      </Pie>
                      <Tooltip />
                      <Legend />
                    </PieChart>
                  </ResponsiveContainer>
                </div>
              </CardContent>
            </Card>
            
            <Card>
              <CardHeader>
                <CardTitle>内容类型分布</CardTitle>
                <CardDescription>按内容类型统计</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="h-[300px]">
                  <ResponsiveContainer width="100%" height="100%">
                    <PieChart>
                      <Pie
                        data={stats?.byType || []}
                        cx="50%"
                        cy="50%"
                        labelLine={false}
                        outerRadius={80}
                        fill="#8884d8"
                        dataKey="count"
                        nameKey="contentType"
                        label={({ name, percent }) => `${name}: ${(percent * 100).toFixed(0)}%`}
                      >
                        {(stats?.byType || []).map((entry: any, index: number) => (
                          <Cell 
                            key={`cell-${index}`} 
                            fill={COLORS[entry.contentType as keyof typeof COLORS] || '#999'} 
                          />
                        ))}
                      </Pie>
                      <Tooltip />
                      <Legend />
                    </PieChart>
                  </ResponsiveContainer>
                </div>
              </CardContent>
            </Card>
          </div>
          
          <Card>
            <CardHeader>
              <CardTitle>严重程度分布</CardTitle>
              <CardDescription>按严重程度统计</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="h-[300px]">
                <ResponsiveContainer width="100%" height="100%">
                  <BarChart
                    data={stats?.bySeverity || []}
                    margin={{ top: 20, right: 30, left: 20, bottom: 5 }}
                  >
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis dataKey="severity" />
                    <YAxis />
                    <Tooltip />
                    <Legend />
                    <Bar dataKey="count" name="数量">
                      {(stats?.bySeverity || []).map((entry: any, index: number) => (
                        <Cell 
                          key={`cell-${index}`} 
                          fill={COLORS[entry.severity as keyof typeof COLORS] || '#999'} 
                        />
                      ))}
                    </Bar>
                  </BarChart>
                </ResponsiveContainer>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
        
        <TabsContent value="trends" className="space-y-4 mt-4">
          <Card>
            <CardHeader>
              <CardTitle>每日审核趋势</CardTitle>
              <CardDescription>按日期统计</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="h-[400px]">
                <ResponsiveContainer width="100%" height="100%">
                  <LineChart
                    data={stats?.daily || []}
                    margin={{ top: 20, right: 30, left: 20, bottom: 5 }}
                  >
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis dataKey="date" />
                    <YAxis />
                    <Tooltip />
                    <Legend />
                    <Line 
                      type="monotone" 
                      dataKey="count" 
                      name="总数" 
                      stroke="#3b82f6" 
                      activeDot={{ r: 8 }} 
                    />
                    <Line 
                      type="monotone" 
                      dataKey="approvedCount" 
                      name="通过" 
                      stroke="#10b981" 
                    />
                    <Line 
                      type="monotone" 
                      dataKey="rejectedCount" 
                      name="拒绝" 
                      stroke="#ef4444" 
                    />
                    <Line 
                      type="monotone" 
                      dataKey="reviewCount" 
                      name="人工审核" 
                      stroke="#f59e0b" 
                    />
                  </LineChart>
                </ResponsiveContainer>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
        
        <TabsContent value="details" className="space-y-4 mt-4">
          <Card>
            <CardHeader>
              <CardTitle>详细统计数据</CardTitle>
              <CardDescription>按日期查看详细数据</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="overflow-x-auto">
                <table className="w-full border-collapse">
                  <thead>
                    <tr className="border-b">
                      <th className="py-2 px-4 text-left">日期</th>
                      <th className="py-2 px-4 text-right">总数</th>
                      <th className="py-2 px-4 text-right">通过</th>
                      <th className="py-2 px-4 text-right">拒绝</th>
                      <th className="py-2 px-4 text-right">人工审核</th>
                    </tr>
                  </thead>
                  <tbody>
                    {(stats?.daily || []).map((item: any, index: number) => (
                      <tr key={index} className="border-b hover:bg-muted/50">
                        <td className="py-2 px-4">{item.date}</td>
                        <td className="py-2 px-4 text-right">{item.count}</td>
                        <td className="py-2 px-4 text-right text-green-500">{item.approvedCount || 0}</td>
                        <td className="py-2 px-4 text-right text-red-500">{item.rejectedCount || 0}</td>
                        <td className="py-2 px-4 text-right text-yellow-500">{item.reviewCount || 0}</td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
};

export default AutoModerationStatsDashboard;
