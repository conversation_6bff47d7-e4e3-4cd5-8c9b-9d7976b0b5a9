/**
 * 🚀 优化版问卷控制器
 * 专注于性能优化的问卷统计API
 */

import { Context } from 'hono';
import { Env } from '../../types';

// 缓存配置
const CACHE_TTL = 5 * 60 * 1000; // 5分钟缓存
const cache = new Map<string, { data: any; timestamp: number }>();

/**
 * 🚀 优化版问卷统计API
 * 主要优化策略：
 * 1. 使用单个复杂查询替代多个简单查询
 * 2. 实施内存缓存
 * 3. 减少数据传输量
 * 4. 优化数据结构
 */
export const getQuestionnaireStatsOptimized = async (c: Context<{ Bindings: Env }>) => {
  const startTime = Date.now();
  
  try {
    console.log('🚀 获取优化版问卷统计数据');

    // 检查缓存
    const cacheKey = 'questionnaire_stats';
    const cached = cache.get(cacheKey);
    
    if (cached && (Date.now() - cached.timestamp) < CACHE_TTL) {
      console.log(`✅ 缓存命中，响应时间: ${Date.now() - startTime}ms`);
      return c.json({
        ...cached.data,
        meta: {
          ...cached.data.meta,
          cached: true,
          responseTime: Date.now() - startTime
        }
      });
    }

    // 使用单个优化查询获取所有统计数据
    const statsQuery = `
      WITH base_stats AS (
        SELECT 
          COUNT(*) as total_responses,
          COUNT(CASE WHEN is_anonymous = 0 THEN 1 END) as verified_count,
          COUNT(CASE WHEN is_anonymous = 1 THEN 1 END) as anonymous_count,
          COUNT(CASE WHEN employment_status_display = '已就业' THEN 1 END) as employed_count,
          COUNT(CASE WHEN employment_status_display = '未就业' THEN 1 END) as unemployed_count
        FROM questionnaire_responses_v2
      ),
      major_stats AS (
        SELECT 
          major_display as name, 
          COUNT(*) as count
        FROM questionnaire_responses_v2
        WHERE major_display IS NOT NULL AND major_display != ''
        GROUP BY major_display
        ORDER BY count DESC
        LIMIT 15
      ),
      graduation_stats AS (
        SELECT 
          graduation_year as name, 
          COUNT(*) as count
        FROM questionnaire_responses_v2
        WHERE graduation_year IS NOT NULL
        GROUP BY graduation_year
        ORDER BY graduation_year DESC
        LIMIT 10
      ),
      education_stats AS (
        SELECT 
          education_level_display as name, 
          COUNT(*) as count
        FROM questionnaire_responses_v2
        WHERE education_level_display IS NOT NULL
        GROUP BY education_level_display
        ORDER BY count DESC
      )
      SELECT 
        'base' as type,
        total_responses,
        verified_count,
        anonymous_count,
        employed_count,
        unemployed_count,
        NULL as name,
        NULL as count
      FROM base_stats
      
      UNION ALL
      
      SELECT 
        'major' as type,
        NULL as total_responses,
        NULL as verified_count,
        NULL as anonymous_count,
        NULL as employed_count,
        NULL as unemployed_count,
        name,
        count
      FROM major_stats
      
      UNION ALL
      
      SELECT 
        'graduation' as type,
        NULL as total_responses,
        NULL as verified_count,
        NULL as anonymous_count,
        NULL as employed_count,
        NULL as unemployed_count,
        name,
        count
      FROM graduation_stats
      
      UNION ALL
      
      SELECT 
        'education' as type,
        NULL as total_responses,
        NULL as verified_count,
        NULL as anonymous_count,
        NULL as employed_count,
        NULL as unemployed_count,
        name,
        count
      FROM education_stats
    `;

    const queryStartTime = Date.now();
    const result = await c.env.DB.prepare(statsQuery).all();
    const queryTime = Date.now() - queryStartTime;

    console.log(`📊 数据库查询完成，耗时: ${queryTime}ms`);

    // 处理查询结果
    const processStartTime = Date.now();
    const statistics = processQueryResults(result.results || []);
    const processTime = Date.now() - processStartTime;

    console.log(`🔄 数据处理完成，耗时: ${processTime}ms`);

    // 构建响应数据
    const responseData = {
      success: true,
      message: '统计数据获取成功',
      meta: {
        statistics,
        timestamp: new Date().toISOString(),
        cached: false,
        responseTime: Date.now() - startTime,
        queryTime,
        processTime,
        version: 'optimized-v1.0'
      }
    };

    // 更新缓存
    cache.set(cacheKey, {
      data: responseData,
      timestamp: Date.now()
    });

    console.log(`✅ 优化版统计数据获取成功，总响应时间: ${Date.now() - startTime}ms`);
    return c.json(responseData);

  } catch (error) {
    console.error('❌ 获取优化版统计数据失败:', error);
    return c.json({
      success: false,
      error: '获取统计数据失败',
      details: error.message,
      meta: {
        responseTime: Date.now() - startTime,
        version: 'optimized-v1.0'
      }
    }, 500);
  }
};

/**
 * 处理查询结果
 */
function processQueryResults(results: any[]): any {
  const statistics = {
    totalResponses: 0,
    verifiedCount: 0,
    anonymousCount: 0,
    employedCount: 0,
    unemployedCount: 0,
    majors: [] as Array<{ name: string; count: number }>,
    graduationYears: [] as Array<{ name: string; count: number }>,
    educationLevels: [] as Array<{ name: string; count: number }>,
    lastUpdated: new Date().toISOString()
  };

  // 分类处理结果
  for (const row of results) {
    switch (row.type) {
      case 'base':
        statistics.totalResponses = row.total_responses || 0;
        statistics.verifiedCount = row.verified_count || 0;
        statistics.anonymousCount = row.anonymous_count || 0;
        statistics.employedCount = row.employed_count || 0;
        statistics.unemployedCount = row.unemployed_count || 0;
        break;
      
      case 'major':
        if (row.name && row.count) {
          statistics.majors.push({
            name: row.name,
            count: row.count
          });
        }
        break;
      
      case 'graduation':
        if (row.name && row.count) {
          statistics.graduationYears.push({
            name: row.name.toString(),
            count: row.count
          });
        }
        break;
      
      case 'education':
        if (row.name && row.count) {
          statistics.educationLevels.push({
            name: row.name,
            count: row.count
          });
        }
        break;
    }
  }

  return statistics;
}

/**
 * 🔄 轻量级实时统计API
 * 只返回最基本的统计信息，用于实时更新
 */
export const getQuestionnaireRealtimeStatsOptimized = async (c: Context<{ Bindings: Env }>) => {
  const startTime = Date.now();
  
  try {
    console.log('⚡ 获取轻量级实时统计');

    // 检查缓存
    const cacheKey = 'realtime_stats';
    const cached = cache.get(cacheKey);
    
    if (cached && (Date.now() - cached.timestamp) < (CACHE_TTL / 5)) { // 更短的缓存时间
      console.log(`✅ 实时统计缓存命中，响应时间: ${Date.now() - startTime}ms`);
      return c.json({
        ...cached.data,
        meta: {
          ...cached.data.meta,
          cached: true,
          responseTime: Date.now() - startTime
        }
      });
    }

    // 轻量级查询 - 只获取基本计数
    const lightQuery = `
      SELECT 
        COUNT(*) as total,
        COUNT(CASE WHEN DATE(created_at) = DATE('now') THEN 1 END) as today,
        COUNT(CASE WHEN DATE(created_at) >= DATE('now', '-7 days') THEN 1 END) as week,
        COUNT(CASE WHEN DATE(created_at) >= DATE('now', '-30 days') THEN 1 END) as month
      FROM questionnaire_responses_v2
    `;

    const queryStartTime = Date.now();
    const result = await c.env.DB.prepare(lightQuery).first();
    const queryTime = Date.now() - queryStartTime;

    const responseData = {
      success: true,
      data: {
        total: result?.total || 0,
        today: result?.today || 0,
        week: result?.week || 0,
        month: result?.month || 0
      },
      meta: {
        timestamp: new Date().toISOString(),
        cached: false,
        responseTime: Date.now() - startTime,
        queryTime,
        version: 'realtime-optimized-v1.0'
      }
    };

    // 更新缓存
    cache.set(cacheKey, {
      data: responseData,
      timestamp: Date.now()
    });

    console.log(`✅ 实时统计获取成功，响应时间: ${Date.now() - startTime}ms`);
    return c.json(responseData);

  } catch (error) {
    console.error('❌ 获取实时统计失败:', error);
    return c.json({
      success: false,
      error: '获取实时统计失败',
      details: error.message,
      meta: {
        responseTime: Date.now() - startTime,
        version: 'realtime-optimized-v1.0'
      }
    }, 500);
  }
};

/**
 * 🧹 清理缓存
 */
export const clearStatsCache = () => {
  cache.clear();
  console.log('🧹 统计缓存已清理');
};

/**
 * 📊 获取缓存状态
 */
export const getCacheStatus = () => {
  const status = {
    size: cache.size,
    keys: Array.from(cache.keys()),
    entries: Array.from(cache.entries()).map(([key, value]) => ({
      key,
      age: Date.now() - value.timestamp,
      expired: (Date.now() - value.timestamp) > CACHE_TTL
    }))
  };
  
  console.log('📊 缓存状态:', status);
  return status;
};
