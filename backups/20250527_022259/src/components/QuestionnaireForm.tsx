import React, { useState, useEffect } from 'react';
import { SecurityModule } from '../lib/security';

// 问卷表单组件
const QuestionnaireForm: React.FC = () => {
  // 表单数据状态
  const [formData, setFormData] = useState({
    name: '',
    email: '',
    age: '',
    gender: '',
    education: '',
    major: '',
    graduation_year: '',
    employment_status: '',
    industry: '',
  });

  // 表单状态
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [isSuccess, setIsSuccess] = useState(false);

  // 初始化安全模块
  useEffect(() => {
    SecurityModule.initialize({
      protectionLevel: 2, // 使用标准防护等级
      captcha: {
        siteKey: import.meta.env.VITE_TURNSTILE_SITE_KEY || '1x00000000000000000000AA' // 使用环境变量或默认值
      }
    });
  }, []);

  // 处理表单字段变化
  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>) => {
    const { name, value } = e.target;
    
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
    
    // 记录字段变化（用于行为分析）
    if (SecurityModule) {
      (SecurityModule as any).behaviorTrackingService?.recordFieldChange(name, value);
    }
  };

  // 处理单选按钮变化
  const handleRadioChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
    
    // 记录字段变化（用于行为分析）
    if (SecurityModule) {
      (SecurityModule as any).behaviorTrackingService?.recordFieldChange(name, value);
    }
  };

  // 处理表单提交
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setError(null);
    setIsSubmitting(true);
    
    try {
      // 验证表单
      const validationResult = await SecurityModule.validateForm(formData);
      if (!validationResult.valid) {
        setError(validationResult.error || '提交失败，请重试');
        setIsSubmitting(false);
        return;
      }
      
      // 获取安全元数据
      const securityMetadata = SecurityModule.getSecurityMetadata();
      
      // 构建提交数据
      const submitData = {
        ...formData,
        _security: securityMetadata
      };
      
      // 模拟 API 请求
      console.log('提交数据:', submitData);
      
      // 模拟延迟
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      // 设置成功状态
      setIsSuccess(true);
    } catch (error) {
      console.error('提交错误:', error);
      setError('提交过程中发生错误，请稍后重试');
    } finally {
      setIsSubmitting(false);
    }
  };

  // 如果提交成功，显示成功消息
  if (isSuccess) {
    return (
      <div className="questionnaire-success">
        <h2>提交成功！</h2>
        <p>感谢您参与本次调查。</p>
        <button 
          onClick={() => {
            setIsSuccess(false);
            setFormData({
              name: '',
              email: '',
              age: '',
              gender: '',
              education: '',
              major: '',
              graduation_year: '',
              employment_status: '',
              industry: '',
            });
          }}
          className="button"
        >
          再次填写
        </button>
      </div>
    );
  }

  return (
    <div className="questionnaire-form">
      <h1>大学毕业生就业调查问卷</h1>
      
      <form onSubmit={handleSubmit}>
        <div className="form-section">
          <h2>基本信息</h2>
          
          <div className="form-group">
            <label htmlFor="name">姓名</label>
            <input
              type="text"
              id="name"
              name="name"
              value={formData.name}
              onChange={handleChange}
              required
            />
          </div>
          
          <div className="form-group">
            <label htmlFor="email">邮箱</label>
            <input
              type="email"
              id="email"
              name="email"
              value={formData.email}
              onChange={handleChange}
              required
            />
          </div>
          
          <div className="form-group">
            <label htmlFor="age">年龄</label>
            <input
              type="number"
              id="age"
              name="age"
              value={formData.age}
              onChange={handleChange}
              min="18"
              max="60"
              required
            />
          </div>
          
          <div className="form-group">
            <label>性别</label>
            <div className="radio-group">
              <label className="radio-label">
                <input
                  type="radio"
                  name="gender"
                  value="male"
                  checked={formData.gender === 'male'}
                  onChange={handleRadioChange}
                  required
                />
                男
              </label>
              <label className="radio-label">
                <input
                  type="radio"
                  name="gender"
                  value="female"
                  checked={formData.gender === 'female'}
                  onChange={handleRadioChange}
                />
                女
              </label>
              <label className="radio-label">
                <input
                  type="radio"
                  name="gender"
                  value="other"
                  checked={formData.gender === 'other'}
                  onChange={handleRadioChange}
                />
                其他
              </label>
            </div>
          </div>
        </div>
        
        <div className="form-section">
          <h2>教育背景</h2>
          
          <div className="form-group">
            <label htmlFor="education">最高学历</label>
            <select
              id="education"
              name="education"
              value={formData.education}
              onChange={handleChange}
              required
            >
              <option value="">请选择</option>
              <option value="high_school">高中/中专</option>
              <option value="college">大专</option>
              <option value="bachelor">本科</option>
              <option value="master">硕士</option>
              <option value="phd">博士</option>
            </select>
          </div>
          
          <div className="form-group">
            <label htmlFor="major">专业</label>
            <input
              type="text"
              id="major"
              name="major"
              value={formData.major}
              onChange={handleChange}
              required
            />
          </div>
          
          <div className="form-group">
            <label htmlFor="graduation_year">毕业年份</label>
            <select
              id="graduation_year"
              name="graduation_year"
              value={formData.graduation_year}
              onChange={handleChange}
              required
            >
              <option value="">请选择</option>
              <option value="2024">2024</option>
              <option value="2023">2023</option>
              <option value="2022">2022</option>
              <option value="2021">2021</option>
              <option value="2020">2020</option>
              <option value="earlier">2020年前</option>
            </select>
          </div>
        </div>
        
        <div className="form-section">
          <h2>就业情况</h2>
          
          <div className="form-group">
            <label htmlFor="employment_status">当前就业状态</label>
            <select
              id="employment_status"
              name="employment_status"
              value={formData.employment_status}
              onChange={handleChange}
              required
            >
              <option value="">请选择</option>
              <option value="employed">已就业</option>
              <option value="unemployed">待业中</option>
              <option value="further_education">继续深造</option>
              <option value="entrepreneurship">自主创业</option>
              <option value="other">其他</option>
            </select>
          </div>
          
          <div className="form-group">
            <label htmlFor="industry">行业</label>
            <select
              id="industry"
              name="industry"
              value={formData.industry}
              onChange={handleChange}
            >
              <option value="">请选择</option>
              <option value="it">互联网/IT</option>
              <option value="finance">金融</option>
              <option value="education">教育</option>
              <option value="healthcare">医疗健康</option>
              <option value="manufacturing">制造业</option>
              <option value="service">服务业</option>
              <option value="government">政府/事业单位</option>
              <option value="other">其他</option>
            </select>
          </div>
        </div>
        
        {/* 安全组件（验证码等） */}
        <div className="security-components">
          {SecurityModule.renderSecurityComponents({ theme: 'light' })}
        </div>
        
        {/* 错误消息 */}
        {error && (
          <div className="error-message">
            {error}
          </div>
        )}
        
        {/* 提交按钮 */}
        <button 
          type="submit" 
          className="submit-button"
          disabled={isSubmitting}
        >
          {isSubmitting ? '提交中...' : '提交问卷'}
        </button>
      </form>
    </div>
  );
};

export default QuestionnaireForm;
