import { useState, useEffect } from 'react';
import { Search, X, ChevronDown, ChevronUp } from 'lucide-react';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '@/components/ui/popover.tsx';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import {
  Collapsible,
  CollapsibleContent,
  CollapsibleTrigger,
} from '@/components/ui/collapsible.tsx';

// 热门搜索词
const POPULAR_SEARCHES = [
  '实习经验',
  '面试技巧',
  '薪资谈判',
  '职业规划',
  '转行经历',
  '海外就业',
];

interface AdvancedSearchProps {
  searchQuery: string;
  onSearchChange: (query: string) => void;
  onSearch: (e: React.FormEvent) => void;
  selectedCategory: string;
  onCategoryChange: (category: string) => void;
  selectedEducationLevel: string;
  onEducationLevelChange: (level: string) => void;
  selectedIndustry: string;
  onIndustryChange: (industry: string) => void;
  onClearFilters: () => void;
  isFilterActive: boolean;
  categories: Array<{ id: string; label: string }>;
  educationLevels: Array<{ id: string; label: string }>;
  industries: Array<{ id: string; label: string }>;
}

export default function AdvancedSearch({
  searchQuery,
  onSearchChange,
  onSearch,
  selectedCategory,
  onCategoryChange,
  selectedEducationLevel,
  onEducationLevelChange,
  selectedIndustry,
  onIndustryChange,
  onClearFilters,
  isFilterActive,
  categories,
  educationLevels,
  industries,
}: AdvancedSearchProps) {
  const [isAdvancedOpen, setIsAdvancedOpen] = useState(false);
  const [recentSearches, setRecentSearches] = useState<string[]>([]);

  // 加载最近搜索记录
  useEffect(() => {
    const saved = localStorage.getItem('recentSearches');
    if (saved) {
      try {
        setRecentSearches(JSON.parse(saved));
      } catch (e) {
        console.error('Failed to parse recent searches', e);
      }
    }
  }, []);

  // 保存搜索记录
  const saveSearch = (query: string) => {
    if (!query.trim()) return;

    const newSearches = [
      query,
      ...recentSearches.filter(s => s !== query),
    ].slice(0, 5);

    setRecentSearches(newSearches);
    localStorage.setItem('recentSearches', JSON.stringify(newSearches));
  };

  // 处理搜索提交
  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (searchQuery.trim()) {
      saveSearch(searchQuery);
    }
    onSearch(e);
  };

  // 选择搜索建议
  const selectSuggestion = (suggestion: string) => {
    onSearchChange(suggestion);
    saveSearch(suggestion);
    // 自动提交搜索
    const formEvent = { preventDefault: () => {} } as React.FormEvent;
    onSearch(formEvent);
  };

  return (
    <div className="w-full space-y-4">
      <form onSubmit={handleSubmit} className="flex gap-2">
        <div className="relative flex-1">
          <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-gray-500" />
          <Input
            type="search"
            placeholder="搜索故事..."
            className="pl-9"
            value={searchQuery}
            onChange={(e) => onSearchChange(e.target.value)}
          />

          {/* 搜索建议 */}
          {searchQuery && (
            <Popover open={true}>
              <PopoverContent
                className="w-full p-0"
                align="start"
                sideOffset={5}
              >
                <div className="py-2">
                  <div className="px-2 py-1.5 text-sm font-medium text-gray-500">
                    搜索建议
                  </div>
                  <div className="mt-1">
                    {POPULAR_SEARCHES.filter(s =>
                      s.toLowerCase().includes(searchQuery.toLowerCase())
                    ).slice(0, 5).map((suggestion) => (
                      <div
                        key={suggestion}
                        className="px-2 py-1.5 text-sm hover:bg-gray-100 cursor-pointer"
                        onClick={() => selectSuggestion(suggestion)}
                      >
                        {suggestion}
                      </div>
                    ))}
                    {recentSearches.filter(s =>
                      s.toLowerCase().includes(searchQuery.toLowerCase()) &&
                      !POPULAR_SEARCHES.includes(s)
                    ).slice(0, 3).map((suggestion) => (
                      <div
                        key={suggestion}
                        className="px-2 py-1.5 text-sm hover:bg-gray-100 cursor-pointer flex items-center"
                        onClick={() => selectSuggestion(suggestion)}
                      >
                        <span className="text-gray-400 mr-1">最近:</span> {suggestion}
                      </div>
                    ))}
                  </div>
                </div>
              </PopoverContent>
            </Popover>
          )}
        </div>
        <Button type="submit">搜索</Button>
        <Button
          type="button"
          variant="outline"
          onClick={() => setIsAdvancedOpen(!isAdvancedOpen)}
          className="flex items-center gap-1"
        >
          高级
          {isAdvancedOpen ? (
            <ChevronUp className="h-4 w-4" />
          ) : (
            <ChevronDown className="h-4 w-4" />
          )}
        </Button>
      </form>

      <Collapsible open={isAdvancedOpen} onOpenChange={setIsAdvancedOpen}>
        <CollapsibleContent>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4 p-4 border rounded-lg">
            <div>
              <label className="text-sm font-medium mb-1 block">分类</label>
              <Select value={selectedCategory} onValueChange={onCategoryChange}>
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  {categories.map((category) => (
                    <SelectItem key={category.id} value={category.id}>
                      {category.label}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
            <div>
              <label className="text-sm font-medium mb-1 block">学历</label>
              <Select value={selectedEducationLevel} onValueChange={onEducationLevelChange}>
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  {educationLevels.map((level) => (
                    <SelectItem key={level.id} value={level.id}>
                      {level.label}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
            <div>
              <label className="text-sm font-medium mb-1 block">行业</label>
              <Select value={selectedIndustry} onValueChange={onIndustryChange}>
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  {industries.map((industry) => (
                    <SelectItem key={industry.id} value={industry.id}>
                      {industry.label}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
            <div className="md:col-span-3 flex justify-end">
              <Button
                variant="outline"
                size="sm"
                onClick={onClearFilters}
                disabled={!isFilterActive}
              >
                清除筛选
              </Button>
            </div>
          </div>
        </CollapsibleContent>
      </Collapsible>

      {/* 活跃筛选条件 */}
      {isFilterActive && (
        <div className="flex flex-wrap gap-2">
          {selectedCategory !== 'all' && (
            <Badge variant="secondary" className="flex items-center gap-1">
              分类: {categories.find(c => c.id === selectedCategory)?.label}
              <X
                className="h-3 w-3 cursor-pointer"
                onClick={() => onCategoryChange('all')}
              />
            </Badge>
          )}

          {selectedEducationLevel !== 'all' && (
            <Badge variant="secondary" className="flex items-center gap-1">
              学历: {educationLevels.find(e => e.id === selectedEducationLevel)?.label}
              <X
                className="h-3 w-3 cursor-pointer"
                onClick={() => onEducationLevelChange('all')}
              />
            </Badge>
          )}

          {selectedIndustry !== 'all' && (
            <Badge variant="secondary" className="flex items-center gap-1">
              行业: {industries.find(i => i.id === selectedIndustry)?.label}
              <X
                className="h-3 w-3 cursor-pointer"
                onClick={() => onIndustryChange('all')}
              />
            </Badge>
          )}

          {searchQuery && (
            <Badge variant="secondary" className="flex items-center gap-1">
              搜索: {searchQuery}
              <X
                className="h-3 w-3 cursor-pointer"
                onClick={() => onSearchChange('')}
              />
            </Badge>
          )}
        </div>
      )}
    </div>
  );
}
