import React, { useState, useEffect } from 'react';
import SuperAdminLayout from '@/components/layouts/SuperAdminLayout';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import {
  Tag,
  Plus,
  Edit,
  Trash2,
  Search,
  Hash,
  TrendingUp,
  Users,
  BarChart3,
  RefreshCw
} from 'lucide-react';
import { useToast } from '@/components/ui/use-toast';
import { getTags, createTag, updateTag, deleteTag } from '@/services/contentManagementService';
import { STORY_TAGS } from '@/constants/storyConstants';

// 标签名称映射 - 将数据库字段名转换为中文显示名
const TAG_DISPLAY_NAMES: Record<string, string> = {
  // 学历标签
  'bachelor': '本科',
  'master': '硕士',
  'phd': '博士',
  'associate': '专科',
  'high_school': '高中',

  // 行业标签
  'finance': '金融行业',
  'it_industry': 'IT互联网行业',
  'education': '教育行业',
  'healthcare': '医疗健康',
  'manufacturing': '制造业',
  'consulting': '咨询行业',
  'government': '政府机关',
  'nonprofit': '非营利组织',

  // 经历标签
  'career_change': '转行经历',
  'internship': '实习经历',
  'job_hunting': '求职经历',
  'interview': '面试相关',
  'offer': 'Offer选择',
  'startup': '创业经历',
  'freelance': '自由职业',

  // 技能标签
  'programming': '编程技能',
  'design': '设计技能',
  'marketing': '市场营销',
  'sales': '销售技能',
  'management': '管理技能',
  'communication': '沟通技能',
  'language': '语言技能',

  // 地区标签
  'beijing': '北京',
  'shanghai': '上海',
  'guangzhou': '广州',
  'shenzhen': '深圳',
  'hangzhou': '杭州',
  'nanjing': '南京',
  'chengdu': '成都',
  'wuhan': '武汉',
  'xian': '西安',
  'overseas': '海外',

  // 其他标签
  'remote_work': '远程工作',
  'work_life_balance': '工作生活平衡',
  'salary_negotiation': '薪资谈判',
  'workplace_culture': '职场文化',
  'professional_development': '职业发展',
  'networking': '人脉建设',
  'stress_management': '压力管理',
  'time_management': '时间管理',

  // 补充缺失的标签映射（从截图中看到的）
  'career_planning': '职业规划',
  'challenge': '挑战经历',
  'education_advice': '教育建议',
  'industry_insight': '行业洞察',
  'inspiration': '励志故事',

  // 更多可能的标签
  'job_search': '求职搜索',
  'resume': '简历相关',
  'networking_tips': '人脉技巧',
  'skill_development': '技能发展',
  'work_experience': '工作经验',
  'career_advice': '职业建议',
  'success_story': '成功故事',
  'failure_lesson': '失败教训',
  'industry_trend': '行业趋势',
  'workplace_tips': '职场技巧',

  // 从最新截图补充的标签
  'salary': '薪资相关',
  'success': '成功故事',
  'work_life': '工作生活',

  // 常见的英文标签变体
  'interview_tips': '面试技巧',
  'resume_writing': '简历写作',
  'professional_growth': '职业成长',
  'leadership': '领导力',
  'teamwork': '团队合作',
  'communication_skills': '沟通技巧',
  'problem_solving': '问题解决',
  'project_management': '项目管理'
};

// 智能标签名称转换函数
const smartTagNameConversion = (tagName: string): string => {
  // 1. 首先检查精确匹配
  if (TAG_DISPLAY_NAMES[tagName]) {
    return TAG_DISPLAY_NAMES[tagName];
  }

  // 2. 处理下划线分隔的复合词
  if (tagName.includes('_')) {
    const parts = tagName.split('_');
    const convertedParts = parts.map(part => {
      // 检查每个部分是否有映射
      if (TAG_DISPLAY_NAMES[part]) {
        return TAG_DISPLAY_NAMES[part];
      }
      // 常见英文词汇的简单转换
      const commonWords: Record<string, string> = {
        'work': '工作',
        'life': '生活',
        'job': '工作',
        'career': '职业',
        'skill': '技能',
        'management': '管理',
        'development': '发展',
        'experience': '经验',
        'advice': '建议',
        'tips': '技巧',
        'planning': '规划',
        'growth': '成长',
        'balance': '平衡',
        'success': '成功',
        'story': '故事',
        'interview': '面试',
        'resume': '简历',
        'salary': '薪资',
        'networking': '人脉',
        'leadership': '领导力',
        'teamwork': '团队合作',
        'communication': '沟通',
        'problem': '问题',
        'solving': '解决',
        'time': '时间',
        'project': '项目'
      };
      return commonWords[part] || part;
    });
    return convertedParts.join('');
  }

  // 3. 如果都没有匹配，返回原始名称
  return tagName;
};

// 获取标签显示名称
const getTagDisplayName = (tag: TagData): string => {
  // 优先使用数据库中的display_name字段
  if (tag.display_name) {
    return tag.display_name;
  }

  // 使用智能转换函数
  const convertedName = smartTagNameConversion(tag.name);

  // 调试：如果转换后的名称和原始名称相同，说明没有找到映射
  if (convertedName === tag.name && !TAG_DISPLAY_NAMES[tag.name]) {
    console.warn(`🏷️ 未找到标签映射: "${tag.name}" - 建议添加中文映射`);
  }

  return convertedName;
};

// 颜色名称转换为十六进制
const getColorHex = (colorName: string): string => {
  const colorMap: Record<string, string> = {
    'blue': '#3B82F6',
    'green': '#10B981',
    'purple': '#8B5CF6',
    'yellow': '#F59E0B',
    'gray': '#6B7280',
    'red': '#EF4444',
    'pink': '#EC4899',
    'indigo': '#6366F1',
  };
  return colorMap[colorName] || '#3B82F6';
};

interface TagData {
  id: string;
  name: string;
  display_name: string;
  description: string;
  color: string;
  category: string;
  application: string;
  usageCount: number;
  createdAt: string;
  isSystem: boolean;
}

/**
 * 超级管理员标签管理页面
 *
 * 管理系统中的所有标签，包括：
 * - 创建、编辑、删除标签
 * - 标签分类管理
 * - 标签使用统计
 * - 标签颜色管理
 */
const SuperAdminTagManagementPage: React.FC = () => {
  const { toast } = useToast();
  const [tags, setTags] = useState<TagData[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedCategory, setSelectedCategory] = useState('all');
  const [selectedApplication, setSelectedApplication] = useState('all');
  const [isCreateDialogOpen, setIsCreateDialogOpen] = useState(false);
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false);
  const [editingTag, setEditingTag] = useState<TagData | null>(null);
  const [newTag, setNewTag] = useState({
    name: '',
    description: '',
    color: '#3B82F6',
    category: 'general',
    application: 'general'
  });

  // 标签分类
  const categories = [
    { value: 'all', label: '全部分类' },
    { value: 'job', label: '求职相关' },
    { value: 'education', label: '教育相关' },
    { value: 'industry', label: '行业相关' },
    { value: 'experience', label: '经验相关' },
    { value: 'career', label: '职业发展' },
    { value: 'advice', label: '建议分享' },
    { value: 'skill', label: '技能发展' },
    { value: 'workplace', label: '职场文化' },
    { value: 'other', label: '其他标签' },
    { value: 'general', label: '通用标签' }
  ];

  // 应用场景
  const applications = [
    { value: 'all', label: '全部应用' },
    { value: 'story_wall', label: '故事墙' },
    { value: 'questionnaire', label: '问卷系统' },
    { value: 'general', label: '通用标签' }
  ];

  // 预定义颜色
  const colors = [
    '#3B82F6', '#EF4444', '#10B981', '#F59E0B',
    '#8B5CF6', '#EC4899', '#06B6D4', '#84CC16'
  ];

  // 真实API数据加载
  useEffect(() => {
    const loadTags = async () => {
      setIsLoading(true);
      try {
        // 使用统一的API服务获取标签列表
        const response = await getTags();

        if (response.success && response.data) {
          // 转换API数据格式为组件所需格式
          const formattedTags: TagData[] = response.data.map((tag: any) => ({
            id: tag.id.toString(),
            name: tag.name,
            description: tag.description || '',
            color: tag.color || '#3B82F6',
            category: tag.category || 'general',
            application: tag.application || 'general',
            usageCount: tag.usage_count || 0,
            createdAt: tag.created_at ? new Date(tag.created_at).toISOString().split('T')[0] : new Date().toISOString().split('T')[0],
            isSystem: tag.is_system || false
          }));

          setTags(formattedTags);
        } else {
          throw new Error(response.error || '获取标签列表失败');
        }
      } catch (error) {
        console.error('加载标签失败:', error);
        toast({
          title: '加载失败',
          description: error instanceof Error ? error.message : '无法加载标签列表',
          variant: 'destructive',
        });

        // 如果API失败，使用备用数据
        const fallbackTags: TagData[] = [
          {
            id: 'fallback-1',
            name: '通用',
            description: '通用标签',
            color: '#3B82F6',
            category: 'general',
            usageCount: 0,
            createdAt: new Date().toISOString().split('T')[0],
            isSystem: false
          }
        ];
        setTags(fallbackTags);
      } finally {
        setIsLoading(false);
      }
    };

    loadTags();
  }, [toast]);

  // 过滤标签
  const filteredTags = tags.filter(tag => {
    const matchesSearch = tag.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         tag.description.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesCategory = selectedCategory === 'all' || tag.category === selectedCategory;
    const matchesApplication = selectedApplication === 'all' || tag.application === selectedApplication;
    return matchesSearch && matchesCategory && matchesApplication;
  });

  // 创建标签
  const handleCreateTag = async () => {
    try {
      // 使用统一的API服务创建标签
      const response = await createTag(newTag);

      if (response.success && response.data) {
        // 转换API返回的标签数据格式
        const createdTag: TagData = {
          id: response.data.id.toString(),
          name: response.data.name,
          description: response.data.description || '',
          color: response.data.color || '#3B82F6',
          category: response.data.category || 'general',
          usageCount: response.data.usageCount || 0,
          createdAt: response.data.createdAt ? new Date(response.data.createdAt).toISOString().split('T')[0] : new Date().toISOString().split('T')[0],
          isSystem: response.data.isSystem || false
        };

        setTags([...tags, createdTag]);
        setIsCreateDialogOpen(false);
        setNewTag({
          name: '',
          description: '',
          color: '#3B82F6',
          category: 'general',
          application: 'general'
        });

        toast({
          title: '创建成功',
          description: '标签已创建',
        });
      } else {
        throw new Error(response.error || '创建标签失败');
      }
    } catch (error) {
      console.error('创建标签失败:', error);
      toast({
        title: '创建失败',
        description: error instanceof Error ? error.message : '创建标签时发生错误',
        variant: 'destructive',
      });
    }
  };

  // 编辑标签
  const handleEditTag = async () => {
    if (!editingTag) return;

    try {
      // 使用统一的API服务更新标签
      const response = await updateTag(parseInt(editingTag.id), {
        name: editingTag.name,
        description: editingTag.description,
        color: editingTag.color,
        category: editingTag.category
      });

      if (response.success) {
        // 更新本地状态
        const updatedTags = tags.map(tag =>
          tag.id === editingTag.id ? {
            ...editingTag,
            updatedAt: new Date().toISOString().split('T')[0]
          } : tag
        );

        setTags(updatedTags);
        setIsEditDialogOpen(false);
        setEditingTag(null);

        toast({
          title: '更新成功',
          description: '标签已更新',
        });
      } else {
        throw new Error(response.error || '更新标签失败');
      }
    } catch (error) {
      console.error('更新标签失败:', error);
      toast({
        title: '更新失败',
        description: error instanceof Error ? error.message : '更新标签时发生错误',
        variant: 'destructive',
      });
    }
  };

  // 删除标签
  const handleDeleteTag = async (tagId: string) => {
    try {
      // 使用统一的API服务删除标签
      const response = await deleteTag(parseInt(tagId));

      if (response.success) {
        // 更新本地状态
        const updatedTags = tags.filter(tag => tag.id !== tagId);
        setTags(updatedTags);

        toast({
          title: '删除成功',
          description: '标签已删除',
        });
      } else {
        throw new Error(response.error || '删除标签失败');
      }
    } catch (error) {
      console.error('删除标签失败:', error);
      toast({
        title: '删除失败',
        description: error instanceof Error ? error.message : '删除标签时发生错误',
        variant: 'destructive',
      });
    }
  };

  const getCategoryName = (category: string) => {
    return categories.find(c => c.value === category)?.label || category;
  };

  return (
    <SuperAdminLayout>
      <div className="container mx-auto py-6">
        <div className="flex justify-between items-center mb-6">
          <div>
            <h1 className="text-2xl font-bold flex items-center gap-2">
              <Tag className="h-6 w-6" />
              标签管理
            </h1>
            <p className="text-gray-500 mt-1">管理系统中的所有标签和分类</p>
          </div>
          <div className="flex gap-2">
            <Button
              variant="outline"
              onClick={async () => {
                try {
                  // 批量更新标签显示名称
                  let updatedCount = 0;

                  for (const tag of tags) {
                    const convertedName = smartTagNameConversion(tag.name);

                    // 如果转换后的名称不同于原始名称，且没有display_name，则更新
                    if (convertedName !== tag.name && !tag.display_name) {
                      try {
                        await updateTag(parseInt(tag.id), {
                          name: tag.name,
                          description: tag.description,
                          color: tag.color,
                          category: tag.category,
                          // 注意：这里需要API支持display_name字段
                          display_name: convertedName
                        });
                        updatedCount++;
                      } catch (error) {
                        console.error(`更新标签显示名称失败: ${tag.name}`, error);
                      }
                    }
                  }

                  toast({
                    title: '批量更新完成',
                    description: `成功更新 ${updatedCount} 个标签的显示名称`,
                  });

                  // 重新加载标签列表
                  window.location.reload();
                } catch (error) {
                  toast({
                    title: '批量更新失败',
                    description: '批量更新过程中发生错误',
                    variant: 'destructive',
                  });
                }
              }}
            >
              <Edit className="mr-2 h-4 w-4" />
              批量更新显示名称
            </Button>
            <Button
              variant="outline"
              onClick={async () => {
                try {
                  // 同步前端标签到数据库
                  const tagsToSync = STORY_TAGS.filter(tag => tag.id !== 'all');
                  let syncedCount = 0;

                  for (const frontendTag of tagsToSync) {
                    try {
                      const existingTags = await getTags({ search: frontendTag.id });
                      const exists = existingTags.data?.some((tag: any) => tag.name === frontendTag.id);

                      if (!exists) {
                        await createTag({
                          name: frontendTag.id,
                          description: `${frontendTag.label} - 从前端常量同步`,
                          color: getColorHex(frontendTag.color || 'blue'),
                          category: frontendTag.category || 'other',
                        });
                        syncedCount++;
                      }
                    } catch (error) {
                      console.error(`同步标签失败: ${frontendTag.id}`, error);
                    }
                  }

                  toast({
                    title: '同步完成',
                    description: `成功同步 ${syncedCount} 个标签`,
                  });

                  // 重新加载标签列表
                  window.location.reload();
                } catch (error) {
                  toast({
                    title: '同步失败',
                    description: '标签同步过程中发生错误',
                    variant: 'destructive',
                  });
                }
              }}
            >
              <RefreshCw className="mr-2 h-4 w-4" />
              同步前端标签
            </Button>
            <Dialog open={isCreateDialogOpen} onOpenChange={setIsCreateDialogOpen}>
              <DialogTrigger asChild>
                <Button>
                  <Plus className="mr-2 h-4 w-4" />
                  创建标签
                </Button>
              </DialogTrigger>
              <DialogContent>
              <DialogHeader>
                <DialogTitle>创建新标签</DialogTitle>
                <DialogDescription>
                  创建一个新的标签用于内容分类
                </DialogDescription>
              </DialogHeader>
              <div className="space-y-4">
                <div>
                  <Label htmlFor="name">标签名称</Label>
                  <Input
                    id="name"
                    value={newTag.name}
                    onChange={(e) => setNewTag({ ...newTag, name: e.target.value })}
                    placeholder="输入标签名称"
                  />
                </div>
                <div>
                  <Label htmlFor="description">描述</Label>
                  <Textarea
                    id="description"
                    value={newTag.description}
                    onChange={(e) => setNewTag({ ...newTag, description: e.target.value })}
                    placeholder="输入标签描述"
                  />
                </div>
                <div>
                  <Label htmlFor="application">应用场景</Label>
                  <Select value={newTag.application} onValueChange={(value) => setNewTag({ ...newTag, application: value })}>
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      {applications.slice(1).map(application => (
                        <SelectItem key={application.value} value={application.value}>
                          {application.label}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
                <div>
                  <Label htmlFor="category">分类</Label>
                  <Select value={newTag.category} onValueChange={(value) => setNewTag({ ...newTag, category: value })}>
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      {categories.slice(1).map(category => (
                        <SelectItem key={category.value} value={category.value}>
                          {category.label}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
                <div>
                  <Label htmlFor="color">颜色</Label>
                  <div className="flex gap-2 mt-2">
                    {colors.map(color => (
                      <button
                        key={color}
                        className={`w-8 h-8 rounded-full border-2 ${
                          newTag.color === color ? 'border-gray-800' : 'border-gray-300'
                        }`}
                        style={{ backgroundColor: color }}
                        onClick={() => setNewTag({ ...newTag, color })}
                      />
                    ))}
                  </div>
                </div>
              </div>
              <DialogFooter>
                <Button variant="outline" onClick={() => setIsCreateDialogOpen(false)}>
                  取消
                </Button>
                <Button onClick={handleCreateTag} disabled={!newTag.name.trim()}>
                  创建
                </Button>
              </DialogFooter>
            </DialogContent>
            </Dialog>
          </div>
        </div>

        {/* 统计卡片 */}
        <div className="grid grid-cols-4 gap-4 mb-6">
          <Card>
            <CardContent className="p-4">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm text-gray-500">总标签数</p>
                  <p className="text-2xl font-bold">{tags.length}</p>
                </div>
                <Hash className="h-8 w-8 text-blue-500" />
              </div>
            </CardContent>
          </Card>
          <Card>
            <CardContent className="p-4">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm text-gray-500">总使用次数</p>
                  <p className="text-2xl font-bold">{tags.reduce((sum, tag) => sum + tag.usageCount, 0)}</p>
                </div>
                <TrendingUp className="h-8 w-8 text-green-500" />
              </div>
            </CardContent>
          </Card>
          <Card>
            <CardContent className="p-4">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm text-gray-500">系统标签</p>
                  <p className="text-2xl font-bold">{tags.filter(tag => tag.isSystem).length}</p>
                </div>
                <Users className="h-8 w-8 text-purple-500" />
              </div>
            </CardContent>
          </Card>
          <Card>
            <CardContent className="p-4">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm text-gray-500">分类数量</p>
                  <p className="text-2xl font-bold">{new Set(tags.map(tag => tag.category)).size}</p>
                </div>
                <BarChart3 className="h-8 w-8 text-orange-500" />
              </div>
            </CardContent>
          </Card>
        </div>

        {/* 搜索和过滤 */}
        <Card className="mb-6">
          <CardContent className="p-4">
            <div className="flex gap-4">
              <div className="relative flex-1">
                <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
                <Input
                  placeholder="搜索标签..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-8"
                />
              </div>
              <Select value={selectedApplication} onValueChange={setSelectedApplication}>
                <SelectTrigger className="w-[200px]">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  {applications.map(application => (
                    <SelectItem key={application.value} value={application.value}>
                      {application.label}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
              <Select value={selectedCategory} onValueChange={setSelectedCategory}>
                <SelectTrigger className="w-[200px]">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  {categories.map(category => (
                    <SelectItem key={category.value} value={category.value}>
                      {category.label}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
          </CardContent>
        </Card>

        {/* 标签列表 */}
        <Card>
          <CardHeader>
            <CardTitle>标签列表</CardTitle>
            <CardDescription>
              共 {filteredTags.length} 个标签
            </CardDescription>
          </CardHeader>
          <CardContent>
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>标签</TableHead>
                  <TableHead>应用场景</TableHead>
                  <TableHead>分类</TableHead>
                  <TableHead>描述</TableHead>
                  <TableHead>使用次数</TableHead>
                  <TableHead>创建时间</TableHead>
                  <TableHead>类型</TableHead>
                  <TableHead className="text-right">操作</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {filteredTags.map((tag) => (
                  <TableRow key={tag.id}>
                    <TableCell>
                      <Badge style={{ backgroundColor: tag.color, color: 'white' }}>
                        {getTagDisplayName(tag)}
                      </Badge>
                    </TableCell>
                    <TableCell>
                      <Badge variant="outline">
                        {applications.find(app => app.value === tag.application)?.label || tag.application}
                      </Badge>
                    </TableCell>
                    <TableCell>{getCategoryName(tag.category)}</TableCell>
                    <TableCell className="max-w-xs truncate">{tag.description}</TableCell>
                    <TableCell>{tag.usageCount}</TableCell>
                    <TableCell>{tag.createdAt}</TableCell>
                    <TableCell>
                      {tag.isSystem ? (
                        <Badge variant="secondary">系统</Badge>
                      ) : (
                        <Badge variant="outline">自定义</Badge>
                      )}
                    </TableCell>
                    <TableCell className="text-right">
                      <div className="flex justify-end gap-2">
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => {
                            setEditingTag(tag);
                            setIsEditDialogOpen(true);
                          }}
                        >
                          <Edit className="h-4 w-4" />
                        </Button>
                        {!tag.isSystem && (
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => handleDeleteTag(tag.id)}
                            className="text-red-600 hover:text-red-700"
                          >
                            <Trash2 className="h-4 w-4" />
                          </Button>
                        )}
                      </div>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </CardContent>
        </Card>

        {/* 编辑对话框 */}
        <Dialog open={isEditDialogOpen} onOpenChange={setIsEditDialogOpen}>
          <DialogContent>
            <DialogHeader>
              <DialogTitle>编辑标签</DialogTitle>
              <DialogDescription>
                修改标签信息
              </DialogDescription>
            </DialogHeader>
            {editingTag && (
              <div className="space-y-4">
                <div>
                  <Label htmlFor="edit-name">标签名称</Label>
                  <Input
                    id="edit-name"
                    value={editingTag.name}
                    onChange={(e) => setEditingTag({ ...editingTag, name: e.target.value })}
                  />
                </div>
                <div>
                  <Label htmlFor="edit-description">描述</Label>
                  <Textarea
                    id="edit-description"
                    value={editingTag.description}
                    onChange={(e) => setEditingTag({ ...editingTag, description: e.target.value })}
                  />
                </div>
                <div>
                  <Label htmlFor="edit-application">应用场景</Label>
                  <Select
                    value={editingTag.application}
                    onValueChange={(value) => setEditingTag({ ...editingTag, application: value })}
                  >
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      {applications.slice(1).map(application => (
                        <SelectItem key={application.value} value={application.value}>
                          {application.label}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
                <div>
                  <Label htmlFor="edit-category">分类</Label>
                  <Select
                    value={editingTag.category}
                    onValueChange={(value) => setEditingTag({ ...editingTag, category: value })}
                  >
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      {categories.slice(1).map(category => (
                        <SelectItem key={category.value} value={category.value}>
                          {category.label}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
                <div>
                  <Label htmlFor="edit-color">颜色</Label>
                  <div className="flex gap-2 mt-2">
                    {colors.map(color => (
                      <button
                        key={color}
                        className={`w-8 h-8 rounded-full border-2 ${
                          editingTag.color === color ? 'border-gray-800' : 'border-gray-300'
                        }`}
                        style={{ backgroundColor: color }}
                        onClick={() => setEditingTag({ ...editingTag, color })}
                      />
                    ))}
                  </div>
                </div>
              </div>
            )}
            <DialogFooter>
              <Button variant="outline" onClick={() => setIsEditDialogOpen(false)}>
                取消
              </Button>
              <Button onClick={handleEditTag}>
                保存
              </Button>
            </DialogFooter>
          </DialogContent>
        </Dialog>
      </div>
    </SuperAdminLayout>
  );
};

export default SuperAdminTagManagementPage;
