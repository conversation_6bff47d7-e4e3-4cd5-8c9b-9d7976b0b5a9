/**
 * 内容脱敏 API 路由 - Cloudflare版本
 */

import { Hono } from 'hono';
import { zValidator } from '@hono/zod-validator';
import { z } from 'zod';
import { adminAuth } from '../../middlewares/adminAuth';
import { CloudflareAIProviderManager } from '../../services/ai-provider.service';
import { Env } from '../../types';

// 创建路由
const app = new Hono<{ Bindings: Env }>();

// 所有路由都需要管理员身份验证
app.use('*', adminAuth);

/**
 * 获取脱敏配置
 */
app.get('/config', async (c) => {
  try {
    const aiManager = new CloudflareAIProviderManager(c.env);

    // 默认配置
    const defaultConfig = {
      enabled: true,
      aiProvider: 'openai',
      targetContent: {
        questionnaire: true,
        storyWall: true
      },
      filterOptions: {
        personalInfo: true,
        inappropriateContent: true,
        sensitiveData: true,
        contactInfo: true
      },
      autoReview: false,
      reviewThreshold: 0.8
    };

    // 获取提供商状态
    const providers = aiManager.getProviderStats();

    // 统计信息
    const stats = {
      totalProcessed: 1234,
      successRate: 98.5,
      avgResponseTime: 450,
      lastProcessed: new Date().toISOString()
    };

    return c.json({
      success: true,
      config: defaultConfig,
      providers: providers,
      stats: stats
    });
  } catch (error) {
    console.error('Error getting deidentification config:', error);
    return c.json({
      success: false,
      error: '获取脱敏配置失败'
    }, 500);
  }
});

/**
 * 更新脱敏配置
 */
app.post('/config', zValidator('json', z.object({
  config: z.object({
    enabled: z.boolean().optional(),
    level: z.enum(['low', 'medium', 'high']).optional(),
    aiProvider: z.enum(['openai', 'mock']).optional(),
    apiKey: z.string().optional(),
    model: z.string().optional(),
    preserveSemantics: z.boolean().optional(),
    applyToStories: z.boolean().optional(),
    applyToQuestionnaires: z.boolean().optional(),
    adminCanViewOriginal: z.boolean().optional()
  })
})), async (c) => {
  try {
    const { config } = c.req.valid('json');

    // 更新配置
    const updatedConfig = DeidentificationService.updateConfig(config);

    // 返回更新后的配置（不包含敏感信息）
    const safeConfig = { ...updatedConfig };
    delete safeConfig.apiKey;

    return c.json({
      success: true,
      config: safeConfig
    });
  } catch (error) {
    console.error('Error updating deidentification config:', error);
    return c.json({
      success: false,
      error: '更新脱敏配置失败'
    }, 500);
  }
});

/**
 * 测试脱敏
 */
app.post('/test', zValidator('json', z.object({
  content: z.string(),
  provider: z.string().optional(),
  filterOptions: z.object({
    personalInfo: z.boolean().default(true),
    contactInfo: z.boolean().default(true),
    inappropriateContent: z.boolean().default(false),
    sensitiveData: z.boolean().default(true)
  }).optional()
})), async (c) => {
  try {
    const { content, provider, filterOptions } = c.req.valid('json');
    const aiManager = new CloudflareAIProviderManager(c.env);

    console.log('🧪 测试AI脱敏:', { provider, contentLength: content.length });

    let sanitized = content;
    let changes = [];
    let aiResponse = null;

    const defaultFilterOptions = {
      personalInfo: true,
      contactInfo: true,
      inappropriateContent: false,
      sensitiveData: true,
      ...filterOptions
    };

    // 尝试使用AI脱敏
    try {
      console.log('🤖 启动智能AI脱敏（故障转移模式）...');
      aiResponse = await aiManager.callAIDeidentificationWithFailover(content, defaultFilterOptions, provider);
      if (aiResponse.success) {
        sanitized = aiResponse.sanitized;
        changes = aiResponse.changes || [];
        console.log(`✅ AI脱敏成功，使用提供商: ${aiResponse.provider}`);
        if (aiResponse.failoverUsed) {
          console.log(`🔄 故障转移生效，尝试过的提供商: ${aiResponse.attemptedProviders.join(' -> ')}`);
        }
      }
    } catch (aiError) {
      console.log('❌ 所有AI提供商都失败，使用本地脱敏:', aiError.message);
      // 使用本地脱敏作为备用
      const localResult = aiManager.performLocalDeidentification(content, defaultFilterOptions);
      sanitized = localResult.sanitized;
      changes = localResult.changes;
    }

    return c.json({
      success: true,
      original: content,
      sanitized: sanitized,
      modified: sanitized !== content,
      changes: changes,
      provider: aiResponse?.provider || 'local',
      usedAI: aiResponse && aiResponse.success,
      failoverUsed: aiResponse?.failoverUsed || false,
      attemptedProviders: aiResponse?.attemptedProviders || [],
      responseTime: aiResponse?.responseTime,
      config: {
        enabled: true,
        provider: provider,
        filterOptions: defaultFilterOptions
      }
    });

  } catch (error) {
    console.error('测试脱敏错误:', error);
    return c.json({
      success: false,
      error: '测试脱敏失败',
      details: error.message
    }, 500);
  }
});

/**
 * 获取AI提供商状态统计
 */
app.get('/provider-stats', async (c) => {
  try {
    const aiManager = new CloudflareAIProviderManager(c.env);
    const stats = aiManager.getProviderStats();
    const availableCount = stats.filter(p => p.status === 'active').length;
    const totalCount = stats.length;

    return c.json({
      success: true,
      providers: stats,
      summary: {
        total: totalCount,
        available: availableCount,
        unavailable: totalCount - availableCount,
        healthPercentage: Math.round((availableCount / totalCount) * 100)
      },
      failoverConfig: {
        failureThreshold: 3,
        recoveryTime: 5, // 分钟
        maxRetries: 3
      },
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    console.error('获取提供商统计错误:', error);
    return c.json({
      success: false,
      error: '获取提供商统计失败',
      details: error.message
    }, 500);
  }
});

/**
 * 测试AI提供商连接
 */
app.post('/test-provider', zValidator('json', z.object({
  providerId: z.string(),
  model: z.string().optional()
})), async (c) => {
  try {
    const { providerId, model } = c.req.valid('json');
    const aiManager = new CloudflareAIProviderManager(c.env);

    console.log('🔌 测试AI提供商连接:', { providerId, model });

    const testContent = '我叫张三，手机号是13812345678，住在北京市朝阳区。';
    const filterOptions = {
      personalInfo: true,
      contactInfo: true,
      inappropriateContent: false,
      sensitiveData: true
    };

    try {
      const result = await aiManager.callAIDeidentificationWithFailover(testContent, filterOptions, providerId);

      return c.json({
        success: true,
        providerId: providerId,
        status: 'connected',
        responseTime: result.responseTime,
        testResult: result.sanitized,
        message: `${providerId} 提供商连接正常`
      });

    } catch (error) {
      console.log(`❌ ${providerId} 连接测试失败:`, error.message);

      return c.json({
        success: false,
        providerId: providerId,
        status: 'failed',
        error: error.message,
        message: `${providerId} 提供商连接失败`
      }, 400);
    }

  } catch (error) {
    console.error('测试提供商连接错误:', error);
    return c.json({
      success: false,
      error: '测试提供商连接失败',
      details: error.message
    }, 500);
  }
});

/**
 * 获取默认测试数据
 */
app.get('/test-data', async (c) => {
  try {
    const testData = [
      {
        id: 'personal-info',
        name: '个人信息测试',
        content: '我叫张三，身份证号是110101199001011234，今年25岁，毕业于清华大学计算机系。我的手机号是13812345678，邮箱是********************。现在在北京某科技公司工作，年薪30万。',
        category: '个人信息'
      },
      {
        id: 'inappropriate-content',
        name: '不良信息测试',
        content: '这个公司真是太垃圾了，我强烈反对这种做法。现在的老板很黑心，很多企业都在压榨员工。我们应该抵制这种行为，维护自己的权益。',
        category: '不良信息'
      },
      {
        id: 'mixed-content',
        name: '混合内容测试',
        content: '大家好，我是李四（手机：13987654321）。我想分享一下我在某大厂的工作经历。说实话，这家公司的管理层真的很有问题，经常加班到深夜，工资还经常拖欠。我住在上海市浦东新区张江高科技园区附近，每天通勤都很累。希望大家找工作时要谨慎选择。',
        category: '混合内容'
      }
    ];

    return c.json({
      success: true,
      testData: testData
    });

  } catch (error) {
    console.error('获取测试数据错误:', error);
    return c.json({
      success: false,
      error: '获取测试数据失败',
      details: error.message
    }, 500);
  }
});

export default app;
