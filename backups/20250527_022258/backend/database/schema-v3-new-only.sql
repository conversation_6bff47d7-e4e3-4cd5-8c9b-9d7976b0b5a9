-- 数据库结构修正脚本 v3.0 - 仅新增内容
-- 只创建不存在的表和插入配置数据

-- 1. 创建内容标签关联表（如果不存在）
CREATE TABLE IF NOT EXISTS content_tags_v2 (
  content_id TEXT NOT NULL,              -- 内容ID
  tag_id TEXT NOT NULL,                  -- 标签ID
  content_type TEXT NOT NULL CHECK (content_type IN ('story', 'voice')),
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (content_id, tag_id),
  FOREIGN KEY (tag_id) REFERENCES tags_v2(id) ON DELETE CASCADE
);

-- 2. 创建审核记录表（如果不存在）
CREATE TABLE IF NOT EXISTS review_records_v2 (
  id TEXT PRIMARY KEY,                    -- review_xxx
  reviewer_id TEXT NOT NULL,             -- 审核员ID
  content_id TEXT NOT NULL,              -- 内容ID
  content_type TEXT NOT NULL CHECK (content_type IN ('voice', 'story')),
  action TEXT NOT NULL CHECK (action IN ('approved', 'rejected', 'pending')),
  reason TEXT,                           -- 审核理由
  review_time INTEGER DEFAULT 0,         -- 审核耗时(秒)
  ai_pre_review_result TEXT,             -- AI预审结果
  quality_score REAL DEFAULT 0.0,       -- 内容质量评分
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  FOREIGN KEY (reviewer_id) REFERENCES users_v2(id)
);

-- 3. 创建问卷统计缓存表（优化性能）
CREATE TABLE IF NOT EXISTS questionnaire_stats_cache_v2 (
  id TEXT PRIMARY KEY,
  question_key TEXT NOT NULL,            -- 问题标识
  option_key TEXT NOT NULL,              -- 选项标识
  option_display TEXT NOT NULL,          -- 选项显示名
  count INTEGER DEFAULT 0,               -- 选择次数
  percentage REAL DEFAULT 0.0,           -- 选择比例
  total_responses INTEGER DEFAULT 0,     -- 总回复数
  last_updated TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  UNIQUE(question_key, option_key)
);

-- 4. 插入系统默认配置
INSERT OR IGNORE INTO system_config_v2 (key, value, description, category) VALUES
('ai_review_enabled', 'true', 'AI审核开关', 'review'),
('anti_script_enabled', 'true', '防脚本刷问卷开关', 'security'),
('max_tags_per_story', '5', '每个故事最大标签数', 'content'),
('questionnaire_total_count', '0', '问卷总提交数', 'statistics'),
('review_batch_size', '10', '审核批次大小', 'review'),
('anonymous_identity_expiry_days', '365', '匿名身份有效期(天)', 'user');

-- 5. 插入系统默认标签
INSERT OR IGNORE INTO tags_v2 (id, name, display_name, category, description, is_system) VALUES
('tag_job_search', '求职经验', '求职经验', '经验分享', '求职过程中的经验和技巧', true),
('tag_career_planning', '职业规划', '职业规划', '规划发展', '职业发展规划和思考', true),
('tag_interview', '面试经验', '面试经验', '经验分享', '面试相关的经验分享', true),
('tag_workplace', '职场感悟', '职场感悟', '感悟思考', '职场生活的感悟和思考', true),
('tag_industry_insight', '行业观察', '行业观察', '行业分析', '对特定行业的观察和分析', true),
('tag_skill_development', '技能提升', '技能提升', '学习成长', '技能学习和提升经验', true),
('tag_entrepreneurship', '创业故事', '创业故事', '创业经历', '创业相关的故事和经验', true),
('tag_life_balance', '工作生活平衡', '工作生活平衡', '生活方式', '工作与生活平衡的思考', true);

-- 6. 创建索引优化查询性能
CREATE INDEX IF NOT EXISTS idx_content_tags_v2_content ON content_tags_v2(content_id, content_type);
CREATE INDEX IF NOT EXISTS idx_content_tags_v2_tag ON content_tags_v2(tag_id);
CREATE INDEX IF NOT EXISTS idx_review_records_v2_reviewer ON review_records_v2(reviewer_id, created_at);
CREATE INDEX IF NOT EXISTS idx_review_records_v2_content ON review_records_v2(content_id, content_type);
CREATE INDEX IF NOT EXISTS idx_questionnaire_stats_cache_v2_question ON questionnaire_stats_cache_v2(question_key);

-- 7. 创建视图简化查询
CREATE VIEW IF NOT EXISTS v_story_with_tags AS
SELECT 
  s.*,
  GROUP_CONCAT(t.name, ',') as tag_names,
  GROUP_CONCAT(t.id, ',') as tag_ids
FROM story_contents_v2 s
LEFT JOIN content_tags_v2 ct ON s.id = ct.content_id AND ct.content_type = 'story'
LEFT JOIN tags_v2 t ON ct.tag_id = t.id
GROUP BY s.id;

CREATE VIEW IF NOT EXISTS v_voice_with_tags AS
SELECT 
  v.*,
  GROUP_CONCAT(t.name, ',') as tag_names,
  GROUP_CONCAT(t.id, ',') as tag_ids
FROM questionnaire_voices_v2 v
LEFT JOIN content_tags_v2 ct ON v.id = ct.content_id AND ct.content_type = 'voice'
LEFT JOIN tags_v2 t ON ct.tag_id = t.id
GROUP BY v.id;

-- 8. 创建审核员工作量统计视图
CREATE VIEW IF NOT EXISTS v_reviewer_stats AS
SELECT 
  r.reviewer_id,
  u.display_name as reviewer_name,
  COUNT(*) as total_reviews,
  COUNT(CASE WHEN r.action = 'approved' THEN 1 END) as approved_count,
  COUNT(CASE WHEN r.action = 'rejected' THEN 1 END) as rejected_count,
  AVG(r.review_time) as avg_review_time,
  AVG(r.quality_score) as avg_quality_score,
  DATE(r.created_at) as review_date
FROM review_records_v2 r
JOIN users_v2 u ON r.reviewer_id = u.id
GROUP BY r.reviewer_id, DATE(r.created_at);

-- 9. 更新统计信息
UPDATE system_config_v2 
SET value = (SELECT COUNT(*) FROM questionnaire_responses_v2)
WHERE key = 'questionnaire_total_count';

-- 10. 为现有故事添加一些测试标签
INSERT OR IGNORE INTO content_tags_v2 (content_id, tag_id, content_type)
SELECT 
  s.id,
  CASE 
    WHEN s.category = '求职经验' THEN 'tag_job_search'
    WHEN s.category = '职场感悟' THEN 'tag_workplace'
    WHEN s.category = '学习心得' THEN 'tag_skill_development'
    WHEN s.category = '转行经历' THEN 'tag_career_planning'
    WHEN s.category = '创业故事' THEN 'tag_entrepreneurship'
    ELSE 'tag_workplace'
  END,
  'story'
FROM story_contents_v2 s
WHERE s.status = 'approved';

-- 11. 为现有问卷心声添加标签
INSERT OR IGNORE INTO content_tags_v2 (content_id, tag_id, content_type)
SELECT 
  v.id,
  CASE 
    WHEN v.voice_type = 'advice' THEN 'tag_career_planning'
    WHEN v.voice_type = 'observation' THEN 'tag_industry_insight'
    ELSE 'tag_workplace'
  END,
  'voice'
FROM questionnaire_voices_v2 v
WHERE v.status = 'approved';

-- 12. 更新标签使用次数
UPDATE tags_v2 SET usage_count = (
  SELECT COUNT(*) FROM content_tags_v2 WHERE tag_id = tags_v2.id
);

-- 13. 初始化问卷统计缓存（示例数据）
INSERT OR IGNORE INTO questionnaire_stats_cache_v2 (id, question_key, option_key, option_display, count, percentage, total_responses) VALUES
('cache_edu_undergraduate', 'education_level', 'undergraduate', '本科', 30, 60.0, 50),
('cache_edu_master', 'education_level', 'master', '硕士', 15, 30.0, 50),
('cache_edu_associate', 'education_level', 'associate', '大专', 5, 10.0, 50),
('cache_region_tier1', 'region', 'tier1_cities', '北上广深', 20, 40.0, 50),
('cache_region_tier2', 'region', 'tier2_cities', '二线城市', 15, 30.0, 50),
('cache_region_tier3', 'region', 'tier3_cities', '三四线城市', 15, 30.0, 50),
('cache_employment_employed', 'employment_status', 'employed', '已就业', 35, 70.0, 50),
('cache_employment_unemployed', 'employment_status', 'unemployed', '待就业', 10, 20.0, 50),
('cache_employment_studying', 'employment_status', 'studying', '继续深造', 5, 10.0, 50);
