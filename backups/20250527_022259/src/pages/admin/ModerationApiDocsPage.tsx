import React, { useState, useEffect } from 'react';
import AdminLayout from '@/components/layouts/AdminLayout';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import { Button } from '@/components/ui/button';
import { useToast } from '@/components/ui/use-toast';
import { 
  AlertCircle, 
  Book, 
  Code, 
  Copy, 
  Download, 
  ExternalLink, 
  FileText, 
  Info, 
  Search 
} from 'lucide-react';
import { useApi } from '@/hooks/useApi';
import ReactMarkdown from 'react-markdown';
import { Prism as SyntaxHighlighter } from 'react-syntax-highlighter';
import { vscDarkPlus } from 'react-syntax-highlighter/dist/esm/styles/prism';

/**
 * 审核API文档页面
 */
const ModerationApiDocsPage: React.FC = () => {
  const { toast } = useToast();
  const api = useApi();
  const [isLoading, setIsLoading] = useState<boolean>(true);
  const [markdown, setMarkdown] = useState<string>('');
  const [activeTab, setActiveTab] = useState<string>('overview');
  const [searchTerm, setSearchTerm] = useState<string>('');
  
  // 加载API文档
  const loadApiDocs = async () => {
    setIsLoading(true);
    
    try {
      // 在实际应用中，这里应该从API获取文档内容
      // 这里为了演示，我们直接使用fetch获取markdown文件
      const response = await fetch('/api/docs/moderation-api.md');
      
      if (response.ok) {
        const text = await response.text();
        setMarkdown(text);
      } else {
        toast({
          title: '加载失败',
          description: '无法加载API文档',
          variant: 'destructive',
        });
      }
    } catch (error) {
      console.error('加载API文档失败:', error);
      toast({
        title: '加载失败',
        description: '服务器错误，请稍后再试',
        variant: 'destructive',
      });
    } finally {
      setIsLoading(false);
    }
  };
  
  // 下载API文档
  const downloadApiDocs = () => {
    if (!markdown) return;
    
    try {
      // 创建Blob
      const blob = new Blob([markdown], { type: 'text/markdown;charset=utf-8;' });
      
      // 创建下载链接
      const link = document.createElement('a');
      const url = URL.createObjectURL(blob);
      link.setAttribute('href', url);
      link.setAttribute('download', 'moderation-api-docs.md');
      link.style.visibility = 'hidden';
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      
      toast({
        title: '下载成功',
        description: 'API文档已下载',
        variant: 'default',
      });
    } catch (error) {
      console.error('下载API文档失败:', error);
      toast({
        title: '下载失败',
        description: '下载API文档失败，请稍后再试',
        variant: 'destructive',
      });
    }
  };
  
  // 复制代码块
  const copyCode = (code: string) => {
    navigator.clipboard.writeText(code).then(
      () => {
        toast({
          title: '复制成功',
          description: '代码已复制到剪贴板',
          variant: 'default',
        });
      },
      (err) => {
        console.error('复制失败:', err);
        toast({
          title: '复制失败',
          description: '无法复制代码，请手动选择并复制',
          variant: 'destructive',
        });
      }
    );
  };
  
  // 组件加载时加载API文档
  useEffect(() => {
    loadApiDocs();
  }, []);
  
  // 自定义渲染组件
  const components = {
    code({ node, inline, className, children, ...props }: any) {
      const match = /language-(\w+)/.exec(className || '');
      const code = String(children).replace(/\n$/, '');
      
      return !inline && match ? (
        <div className="relative">
          <Button
            variant="ghost"
            size="icon"
            className="absolute top-2 right-2 h-8 w-8 bg-background/80 hover:bg-background"
            onClick={() => copyCode(code)}
          >
            <Copy className="h-4 w-4" />
          </Button>
          <SyntaxHighlighter
            style={vscDarkPlus}
            language={match[1]}
            PreTag="div"
            {...props}
          >
            {code}
          </SyntaxHighlighter>
        </div>
      ) : (
        <code className={className} {...props}>
          {children}
        </code>
      );
    },
    h1: ({ children }: any) => <h1 className="text-3xl font-bold mb-4">{children}</h1>,
    h2: ({ children }: any) => <h2 className="text-2xl font-bold mt-8 mb-4 pb-2 border-b">{children}</h2>,
    h3: ({ children }: any) => <h3 className="text-xl font-bold mt-6 mb-3">{children}</h3>,
    h4: ({ children }: any) => <h4 className="text-lg font-bold mt-4 mb-2">{children}</h4>,
    p: ({ children }: any) => <p className="mb-4">{children}</p>,
    ul: ({ children }: any) => <ul className="list-disc pl-6 mb-4">{children}</ul>,
    ol: ({ children }: any) => <ol className="list-decimal pl-6 mb-4">{children}</ol>,
    li: ({ children }: any) => <li className="mb-1">{children}</li>,
    a: ({ href, children }: any) => (
      <a 
        href={href} 
        target="_blank" 
        rel="noopener noreferrer" 
        className="text-blue-500 hover:underline inline-flex items-center"
      >
        {children}
        <ExternalLink className="h-3 w-3 ml-1" />
      </a>
    ),
    table: ({ children }: any) => (
      <div className="overflow-x-auto mb-4">
        <table className="w-full border-collapse">{children}</table>
      </div>
    ),
    thead: ({ children }: any) => <thead className="bg-muted">{children}</thead>,
    tbody: ({ children }: any) => <tbody>{children}</tbody>,
    tr: ({ children }: any) => <tr className="border-b">{children}</tr>,
    th: ({ children }: any) => <th className="py-2 px-4 text-left font-medium">{children}</th>,
    td: ({ children }: any) => <td className="py-2 px-4">{children}</td>,
    blockquote: ({ children }: any) => (
      <blockquote className="border-l-4 border-muted pl-4 italic my-4">{children}</blockquote>
    ),
    hr: () => <hr className="my-6" />,
  };
  
  return (
    <AdminLayout>
      <div className="container mx-auto py-6">
        <div className="flex items-center justify-between mb-6">
          <div>
            <h1 className="text-3xl font-bold">审核 API 文档</h1>
            <p className="text-muted-foreground">
              内容审核系统的 API 接口文档
            </p>
          </div>
          <div className="flex gap-2">
            <Button
              variant="outline"
              onClick={loadApiDocs}
              disabled={isLoading}
            >
              <FileText className="w-4 h-4 mr-2" />
              刷新
            </Button>
            <Button
              variant="outline"
              onClick={downloadApiDocs}
              disabled={isLoading || !markdown}
            >
              <Download className="w-4 h-4 mr-2" />
              下载
            </Button>
          </div>
        </div>
        
        <Alert className="mb-6">
          <Info className="h-4 w-4" />
          <AlertTitle>API 文档说明</AlertTitle>
          <AlertDescription>
            本文档描述了内容审核系统的 API 接口，包括自动审核和人工审核相关的接口。请确保在使用 API 时遵循文档中的规范。
          </AlertDescription>
        </Alert>
        
        <Card>
          <CardHeader>
            <CardTitle>内容审核 API</CardTitle>
            <CardDescription>
              内容审核系统的 API 接口文档
            </CardDescription>
          </CardHeader>
          <CardContent>
            {isLoading ? (
              <div className="flex flex-col items-center justify-center py-12">
                <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary mb-4"></div>
                <p className="text-muted-foreground">加载文档中...</p>
              </div>
            ) : markdown ? (
              <div className="prose prose-sm max-w-none dark:prose-invert">
                <ReactMarkdown components={components}>
                  {markdown}
                </ReactMarkdown>
              </div>
            ) : (
              <div className="flex flex-col items-center justify-center py-12">
                <AlertCircle className="h-12 w-12 text-muted-foreground mb-4" />
                <p className="text-muted-foreground">无法加载文档</p>
                <Button
                  variant="outline"
                  size="sm"
                  className="mt-4"
                  onClick={loadApiDocs}
                >
                  重试
                </Button>
              </div>
            )}
          </CardContent>
        </Card>
      </div>
    </AdminLayout>
  );
};

export default ModerationApiDocsPage;
