import { useState, useEffect } from 'react';
import { <PERSON>, Card<PERSON>ontent, Card<PERSON><PERSON>er, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { Users, TrendingUp, Clock, RefreshCw } from 'lucide-react';
import { Button } from '@/components/ui/button';

interface RealtimeStatsData {
  success: boolean;
  statistics: {
    totalResponses: number;
    verifiedCount: number;
    anonymousCount: number;
    employedCount: number;
    unemployedCount: number;
    educationLevels: Array<{
      name: string;
      count: number;
    }>;
    regions: Array<{
      name: string;
      count: number;
    }>;
    industries: Array<{
      name: string;
      count: number;
    }>;
    majors: Array<{
      name: string;
      count: number;
    }>;
    graduationYears: Array<{
      name: string;
      count: number;
    }>;
    lastUpdated: string;
  };
}

interface QuestionnaireRealtimeStatsProps {
  className?: string;
  autoRefresh?: boolean;
  refreshInterval?: number;
  compact?: boolean;
}

export default function QuestionnaireRealtimeStats({
  className = '',
  autoRefresh = true,
  refreshInterval = 30000, // 30秒
  compact = false,
}: QuestionnaireRealtimeStatsProps) {
  const [stats, setStats] = useState<RealtimeStatsData | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [lastRefresh, setLastRefresh] = useState<Date>(new Date());

  // 检测紧凑模式
  const isCompact = compact || className.includes('compact-mode');

  // 获取实时统计数据
  const fetchRealtimeStats = async () => {
    try {
      setError(null);
      // 使用环境变量中的API基础URL
      const apiBaseUrl = import.meta.env.VITE_API_BASE_URL || 'https://college-employment-survey.aibook2099.workers.dev';
      const response = await fetch(`${apiBaseUrl}/api/questionnaire/stats`);

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const data = await response.json();

      if (data.success) {
        // 转换数据结构以适配组件期望的格式
        let transformedData = data;

        // 如果API返回的是标准格式，直接使用
        if (data.statistics) {
          console.log('使用标准API数据结构:', data.statistics);

          transformedData = {
            success: true,
            statistics: {
              totalResponses: data.statistics.totalResponses || 0,
              verifiedCount: data.statistics.verifiedCount || data.statistics.totalResponses || 0,
              anonymousCount: data.statistics.anonymousCount || 0,
              employedCount: data.statistics.employedCount || 0,
              educationLevels: data.statistics.educationLevels || [],
              regions: data.statistics.regions || [],
              industries: data.statistics.industries || [],
              majors: data.statistics.majors || [],
              graduationYears: data.statistics.graduationYears || [],
              employmentStatus: data.statistics.employmentStatus || []
            }
          };

          console.log('转换后的数据结构:', transformedData);
        }

        setStats(transformedData);
        setLastRefresh(new Date());
      } else {
        throw new Error(data.error || '获取统计数据失败');
      }
    } catch (err) {
      console.error('获取实时统计失败:', err);
      setError(err instanceof Error ? err.message : '获取统计数据失败');
    } finally {
      setLoading(false);
    }
  };

  // 手动刷新
  const handleManualRefresh = () => {
    setLoading(true);
    fetchRealtimeStats();
  };

  // 初始加载和自动刷新
  useEffect(() => {
    fetchRealtimeStats();

    if (autoRefresh) {
      const interval = setInterval(fetchRealtimeStats, refreshInterval);
      return () => clearInterval(interval);
    }
  }, [autoRefresh, refreshInterval]);

  // 格式化时间
  const formatTime = (date: Date) => {
    return date.toLocaleTimeString('zh-CN', {
      hour: '2-digit',
      minute: '2-digit',
      second: '2-digit'
    });
  };

  if (loading && !stats) {
    return (
      <Card className={className}>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Users className="h-5 w-5" />
            问卷参与统计
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="animate-pulse space-y-4">
            <div className="h-8 bg-gray-200 rounded w-1/3"></div>
            <div className="space-y-2">
              <div className="h-4 bg-gray-200 rounded"></div>
              <div className="h-4 bg-gray-200 rounded w-3/4"></div>
            </div>
          </div>
        </CardContent>
      </Card>
    );
  }

  if (error) {
    return (
      <Card className={className}>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Users className="h-5 w-5" />
            问卷参与统计
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="text-center py-4">
            <p className="text-red-600 mb-4">{error}</p>
            <Button onClick={handleManualRefresh} variant="outline" size="sm">
              <RefreshCw className="h-4 w-4 mr-2" />
              重试
            </Button>
          </div>
        </CardContent>
      </Card>
    );
  }

  if (!stats) {
    return null;
  }

  // 紧凑模式渲染
  if (isCompact) {
    return (
      <Card className={`${className} bg-gradient-to-r from-blue-50 to-indigo-50 border-blue-200`}>
        <CardContent className="p-4">
          <div className="flex items-center justify-between">
            {/* 左侧：总参与人数 */}
            <div className="flex items-center gap-4">
              <div className="flex items-center gap-2">
                <Users className="h-5 w-5 text-blue-600" />
                <span className="text-sm font-medium text-gray-700">问卷参与统计</span>
              </div>
              <div className="flex items-center gap-1">
                <span className="text-2xl font-bold text-blue-600">
                  {stats.statistics.totalResponses.toLocaleString()}
                </span>
                <span className="text-sm text-gray-600">人已参与</span>
              </div>
            </div>

            {/* 中间：教育水平分布（横向显示前3项） */}
            {stats.statistics.educationLevels && stats.statistics.educationLevels.length > 0 && (
              <div className="flex items-center gap-4">
                {stats.statistics.educationLevels.slice(0, 3).map((item, index) => {
                  const percentage = Math.round((item.count / stats.statistics.totalResponses) * 100);
                  return (
                    <div key={index} className="flex items-center gap-2">
                      <span className="text-sm text-gray-600">{item.name}</span>
                      <Badge variant="outline" className="text-xs">
                        {item.count}
                      </Badge>
                      <span className="text-xs text-gray-500">({percentage}%)</span>
                    </div>
                  );
                })}
                {stats.statistics.educationLevels.length > 3 && (
                  <span className="text-xs text-gray-400">+{stats.statistics.educationLevels.length - 3}项</span>
                )}
              </div>
            )}

            {/* 右侧：刷新控制 */}
            <div className="flex items-center gap-2">
              <Badge variant="secondary" className="text-xs">
                <Clock className="h-3 w-3 mr-1" />
                {formatTime(lastRefresh)}
              </Badge>
              <Button
                onClick={handleManualRefresh}
                variant="ghost"
                size="sm"
                disabled={loading}
              >
                <RefreshCw className={`h-4 w-4 ${loading ? 'animate-spin' : ''}`} />
              </Button>
            </div>
          </div>

          {/* 自动刷新提示 */}
          {autoRefresh && (
            <div className="text-xs text-gray-500 text-center mt-2 pt-2 border-t border-blue-100">
              数据每 {Math.round(refreshInterval / 1000)} 秒自动更新
            </div>
          )}
        </CardContent>
      </Card>
    );
  }

  // 完整模式渲染
  return (
    <Card className={className}>
      <CardHeader>
        <div className="flex items-center justify-between">
          <CardTitle className="flex items-center gap-2">
            <Users className="h-5 w-5" />
            问卷参与统计
          </CardTitle>
          <div className="flex items-center gap-2">
            <Badge variant="secondary" className="text-xs">
              <Clock className="h-3 w-3 mr-1" />
              {formatTime(lastRefresh)}
            </Badge>
            <Button
              onClick={handleManualRefresh}
              variant="ghost"
              size="sm"
              disabled={loading}
            >
              <RefreshCw className={`h-4 w-4 ${loading ? 'animate-spin' : ''}`} />
            </Button>
          </div>
        </div>
      </CardHeader>
      <CardContent className="space-y-6">
        {/* 总参与人数 */}
        <div className="text-center">
          <div className="text-3xl font-bold text-blue-600 mb-1">
            {stats.statistics.totalResponses.toLocaleString()}
          </div>
          <p className="text-sm text-gray-600">人已参与问卷调查</p>
          <div className="flex justify-center gap-4 mt-2 text-sm text-gray-500">
            <span>实名: {stats.statistics.verifiedCount}</span>
            <span>匿名: {stats.statistics.anonymousCount}</span>
            <span>已就业: {stats.statistics.employedCount}</span>
          </div>
        </div>

        {/* 教育水平分布 */}
        {stats.statistics.educationLevels && stats.statistics.educationLevels.length > 0 && (
          <div>
            <h4 className="font-medium mb-3 flex items-center gap-2">
              <TrendingUp className="h-4 w-4" />
              教育水平分布
            </h4>
            <div className="space-y-2">
              {stats.statistics.educationLevels.map((item, index) => {
                const percentage = Math.round((item.count / stats.statistics.totalResponses) * 100);
                return (
                  <div key={index} className="flex items-center justify-between">
                    <span className="text-sm">{item.name}</span>
                    <div className="flex items-center gap-2 min-w-[120px]">
                      <Progress value={percentage} className="w-16 h-2" />
                      <span className="text-xs text-gray-600 min-w-[40px]">
                        {percentage}%
                      </span>
                      <Badge variant="outline" className="text-xs min-w-[30px]">
                        {item.count}
                      </Badge>
                    </div>
                  </div>
                );
              })}
            </div>
          </div>
        )}

        {/* 地区分布 */}
        {stats.statistics.regions && stats.statistics.regions.length > 0 && (
          <div>
            <h4 className="font-medium mb-3">地区分布</h4>
            <div className="space-y-2">
              {stats.statistics.regions.map((item, index) => {
                const percentage = Math.round((item.count / stats.statistics.totalResponses) * 100);
                return (
                  <div key={index} className="flex items-center justify-between">
                    <span className="text-sm">{item.name}</span>
                    <div className="flex items-center gap-2 min-w-[120px]">
                      <Progress value={percentage} className="w-16 h-2" />
                      <span className="text-xs text-gray-600 min-w-[40px]">
                        {percentage}%
                      </span>
                      <Badge variant="outline" className="text-xs min-w-[30px]">
                        {item.count}
                      </Badge>
                    </div>
                  </div>
                );
              })}
            </div>
          </div>
        )}

        {/* 专业分布 */}
        {stats.statistics.majors && stats.statistics.majors.length > 0 && (
          <div>
            <h4 className="font-medium mb-3 flex items-center gap-2">
              <TrendingUp className="h-4 w-4" />
              专业分布
            </h4>
            <div className="space-y-2">
              {stats.statistics.majors.slice(0, 8).map((item, index) => {
                const percentage = Math.round((item.count / stats.statistics.totalResponses) * 100);
                return (
                  <div key={index} className="flex items-center justify-between">
                    <span className="text-sm">{item.name}</span>
                    <div className="flex items-center gap-2 min-w-[120px]">
                      <Progress value={percentage} className="w-16 h-2" />
                      <span className="text-xs text-gray-600 min-w-[40px]">
                        {percentage}%
                      </span>
                      <Badge variant="outline" className="text-xs min-w-[30px]">
                        {item.count}
                      </Badge>
                    </div>
                  </div>
                );
              })}
              {stats.statistics.majors.length > 8 && (
                <div className="text-xs text-gray-500 text-center pt-2">
                  还有 {stats.statistics.majors.length - 8} 个专业...
                </div>
              )}
            </div>
          </div>
        )}

        {/* 毕业年份分布 */}
        {stats.statistics.graduationYears && stats.statistics.graduationYears.length > 0 && (
          <div>
            <h4 className="font-medium mb-3">毕业年份分布</h4>
            <div className="space-y-2">
              {stats.statistics.graduationYears.map((item, index) => {
                const percentage = Math.round((item.count / stats.statistics.totalResponses) * 100);
                return (
                  <div key={index} className="flex items-center justify-between">
                    <span className="text-sm">{item.name}年</span>
                    <div className="flex items-center gap-2 min-w-[120px]">
                      <Progress value={percentage} className="w-16 h-2" />
                      <span className="text-xs text-gray-600 min-w-[40px]">
                        {percentage}%
                      </span>
                      <Badge variant="outline" className="text-xs min-w-[30px]">
                        {item.count}
                      </Badge>
                    </div>
                  </div>
                );
              })}
            </div>
          </div>
        )}

        {/* 自动刷新提示 */}
        {autoRefresh && (
          <div className="text-xs text-gray-500 text-center pt-2 border-t">
            数据每 {Math.round(refreshInterval / 1000)} 秒自动更新
          </div>
        )}
      </CardContent>
    </Card>
  );
}
