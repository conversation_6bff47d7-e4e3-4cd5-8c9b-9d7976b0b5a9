import { <PERSON><PERSON><PERSON>, Pie, Cell, ResponsiveContainer, Legend, Tooltip } from 'recharts';

interface VerificationDistributionChartProps {
  verified: number;
  anonymous: number;
}

export default function VerificationDistributionChart({ 
  verified, 
  anonymous 
}: VerificationDistributionChartProps) {
  // If no data, show placeholder
  if (verified === 0 && anonymous === 0) {
    return (
      <div className="h-full flex items-center justify-center">
        <p className="text-gray-500">暂无数据</p>
      </div>
    );
  }
  
  // Format data for pie chart
  const chartData = [
    { name: '已验证数据', value: verified },
    { name: '匿名数据', value: anonymous },
  ];
  
  // Colors for pie chart
  const COLORS = ['#00C49F', '#FFBB28'];
  
  // Calculate percentages
  const total = verified + anonymous;
  const verifiedPercentage = total > 0 ? (verified / total) * 100 : 0;
  const anonymousPercentage = total > 0 ? (anonymous / total) * 100 : 0;
  
  // Custom tooltip
  const CustomTooltip = ({ active, payload }: any) => {
    if (active && payload && payload.length) {
      return (
        <div className="bg-white p-2 border rounded shadow-sm">
          <p className="font-medium">{`${payload[0].name}`}</p>
          <p className="text-sm">{`数量: ${payload[0].value}`}</p>
          <p className="text-sm">{`占比: ${((payload[0].value / total) * 100).toFixed(1)}%`}</p>
        </div>
      );
    }
    return null;
  };
  
  return (
    <div className="h-full flex flex-col">
      <div className="flex-1">
        <ResponsiveContainer width="100%" height="100%">
          <PieChart>
            <Pie
              data={chartData}
              cx="50%"
              cy="50%"
              labelLine={false}
              outerRadius={70}
              fill="#8884d8"
              dataKey="value"
              label={({ name, percent }) => `${name} ${(percent * 100).toFixed(0)}%`}
            >
              {chartData.map((entry, index) => (
                <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
              ))}
            </Pie>
            <Tooltip content={<CustomTooltip />} />
            <Legend />
          </PieChart>
        </ResponsiveContainer>
      </div>
      
      <div className="mt-4 space-y-4">
        <div>
          <div className="flex justify-between text-sm mb-1">
            <span>已验证数据</span>
            <span>{verifiedPercentage.toFixed(1)}%</span>
          </div>
          <div className="h-2 bg-gray-200 rounded-full overflow-hidden">
            <div 
              className="h-full bg-green-500 rounded-full"
              style={{ width: `${verifiedPercentage}%` }}
            />
          </div>
          <div className="text-xs text-gray-500 mt-1">
            经过邮箱验证的数据，可信度较高
          </div>
        </div>
        
        <div>
          <div className="flex justify-between text-sm mb-1">
            <span>匿名数据</span>
            <span>{anonymousPercentage.toFixed(1)}%</span>
          </div>
          <div className="h-2 bg-gray-200 rounded-full overflow-hidden">
            <div 
              className="h-full bg-yellow-500 rounded-full"
              style={{ width: `${anonymousPercentage}%` }}
            />
          </div>
          <div className="text-xs text-gray-500 mt-1">
            未经验证的匿名提交数据
          </div>
        </div>
      </div>
    </div>
  );
}
