import React from 'react';
import SmartLayout from '@/components/layouts/SmartLayout';
import DataManagementPanel from '@/components/admin/DataManagementPanel';

const QuestionnaireResponsesPage: React.FC = () => {
  return (
    <SmartLayout allowedRoles={['admin', 'superadmin']}>
      <div className="container mx-auto py-6">
        <h1 className="text-2xl font-bold mb-6">问卷回复</h1>
        <p className="text-gray-500 mb-6">查看用户提交的问卷回复数据</p>
        <DataManagementPanel />
      </div>
    </SmartLayout>
  );
};

export default QuestionnaireResponsesPage;
