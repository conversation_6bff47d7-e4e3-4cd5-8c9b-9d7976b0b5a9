#!/usr/bin/env node

/**
 * 数据库初始化脚本 v2.0
 * 
 * 功能：
 * 1. 创建新的v2数据库结构
 * 2. 迁移现有数据（如果存在）
 * 3. 生成测试数据
 * 4. 验证数据完整性
 */

import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';
import { MigrationManager } from './migrate-to-v2.js';
import { TestDataGenerator } from './generate-test-data-v2.js';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// 配置选项
const CONFIG = {
  dbPath: './database.db',
  backupPath: './database-backup.db',
  testDataCount: 50,
  skipMigration: false,
  skipTestData: false,
  forceReset: false
};

// 解析命令行参数
function parseArguments() {
  const args = process.argv.slice(2);
  
  for (let i = 0; i < args.length; i++) {
    const arg = args[i];
    
    switch (arg) {
    case '--db':
    case '--database':
      CONFIG.dbPath = args[++i];
      break;
    case '--backup':
      CONFIG.backupPath = args[++i];
      break;
    case '--test-data':
      CONFIG.testDataCount = parseInt(args[++i]) || 50;
      break;
    case '--skip-migration':
      CONFIG.skipMigration = true;
      break;
    case '--skip-test-data':
      CONFIG.skipTestData = true;
      break;
    case '--force-reset':
      CONFIG.forceReset = true;
      break;
    case '--help':
    case '-h':
      showHelp();
      process.exit(0);
      break;
    }
  }
}

// 显示帮助信息
function showHelp() {
  console.log(`
数据库初始化脚本 v2.0

用法: node init-database-v2.js [选项]

选项:
  --db <path>           数据库文件路径 (默认: ./database.db)
  --backup <path>       备份文件路径 (默认: ./database-backup.db)
  --test-data <count>   测试数据数量 (默认: 50)
  --skip-migration      跳过数据迁移
  --skip-test-data      跳过测试数据生成
  --force-reset         强制重置数据库
  --help, -h            显示此帮助信息

示例:
  node init-database-v2.js --db ./my-database.db --test-data 100
  node init-database-v2.js --skip-migration --test-data 200
  node init-database-v2.js --force-reset
`);
}

// 备份现有数据库
async function backupDatabase() {
  if (fs.existsSync(CONFIG.dbPath)) {
    console.log(`备份现有数据库到: ${CONFIG.backupPath}`);
    fs.copyFileSync(CONFIG.dbPath, CONFIG.backupPath);
    console.log('备份完成');
  } else {
    console.log('未找到现有数据库，跳过备份');
  }
}

// 重置数据库
async function resetDatabase() {
  if (fs.existsSync(CONFIG.dbPath)) {
    console.log('删除现有数据库...');
    fs.unlinkSync(CONFIG.dbPath);
  }
  console.log('数据库重置完成');
}

// 检查数据库状态
async function checkDatabaseStatus() {
  if (!fs.existsSync(CONFIG.dbPath)) {
    console.log('数据库文件不存在，将创建新数据库');
    return { exists: false, hasV2Tables: false };
  }
  
  // 检查是否已有v2表结构
  const Database = (await import('better-sqlite3')).default;
  const db = new Database(CONFIG.dbPath);
  
  try {
    const tables = db.prepare(`
      SELECT name FROM sqlite_master 
      WHERE type='table' AND name LIKE '%_v2'
    `).all();
    
    const hasV2Tables = tables.length > 0;
    console.log(`数据库状态: 存在=${true}, V2表=${hasV2Tables}`);
    
    return { exists: true, hasV2Tables };
  } finally {
    db.close();
  }
}

// 执行数据迁移
async function performMigration() {
  if (CONFIG.skipMigration) {
    console.log('跳过数据迁移');
    return;
  }
  
  console.log('开始数据迁移...');
  const migrator = new MigrationManager(CONFIG.dbPath);
  
  try {
    await migrator.migrate();
    console.log('数据迁移完成');
  } finally {
    migrator.close();
  }
}

// 生成测试数据
async function generateTestData() {
  if (CONFIG.skipTestData) {
    console.log('跳过测试数据生成');
    return;
  }
  
  console.log(`开始生成测试数据 (${CONFIG.testDataCount} 个用户)...`);
  const generator = new TestDataGenerator(CONFIG.dbPath);
  
  try {
    await generator.generateTestDataSet({
      userCount: CONFIG.testDataCount,
      questionnaireRatio: 0.8,
      storyRatio: 0.3,
      registeredUserRatio: 0.2
    });
    console.log('测试数据生成完成');
  } finally {
    generator.close();
  }
}

// 验证数据完整性
async function validateDatabase() {
  console.log('验证数据库完整性...');
  
  const Database = (await import('better-sqlite3')).default;
  const db = new Database(CONFIG.dbPath);
  
  try {
    const tables = [
      'users_v2',
      'questionnaire_responses_v2', 
      'questionnaire_voices_v2',
      'story_contents_v2',
      'tags_v2',
      'content_tags_v2'
    ];
    
    const results = {};
    
    for (const table of tables) {
      try {
        const count = db.prepare(`SELECT COUNT(*) as count FROM ${table}`).get();
        results[table] = count.count;
        console.log(`  ${table}: ${count.count} 条记录`);
      } catch (error) {
        console.log(`  ${table}: 表不存在或查询失败`);
        results[table] = 0;
      }
    }
    
    // 检查数据关联完整性
    if (results.questionnaire_voices_v2 > 0) {
      const voicesWithSource = db.prepare(`
        SELECT COUNT(*) as count FROM questionnaire_voices_v2 v
        JOIN questionnaire_responses_v2 q ON v.source_response_id = q.id
      `).get();
      
      console.log(`  问卷心声关联检查: ${voicesWithSource.count}/${results.questionnaire_voices_v2} 条记录有效`);
    }
    
    // 检查索引
    const indexes = db.prepare(`
      SELECT name FROM sqlite_master 
      WHERE type='index' AND name LIKE 'idx_%_v2_%'
    `).all();
    
    console.log(`  索引数量: ${indexes.length}`);
    
    console.log('数据库验证完成');
    return results;
    
  } finally {
    db.close();
  }
}

// 生成初始化报告
async function generateReport(validationResults) {
  const report = {
    timestamp: new Date().toISOString(),
    config: CONFIG,
    results: validationResults,
    summary: {
      totalUsers: validationResults.users_v2 || 0,
      totalQuestionnaires: validationResults.questionnaire_responses_v2 || 0,
      totalVoices: validationResults.questionnaire_voices_v2 || 0,
      totalStories: validationResults.story_contents_v2 || 0,
      totalTags: validationResults.tags_v2 || 0
    }
  };
  
  const reportPath = `./database-init-report-${Date.now()}.json`;
  fs.writeFileSync(reportPath, JSON.stringify(report, null, 2));
  
  console.log(`\n初始化报告已保存到: ${reportPath}`);
  console.log('\n=== 初始化摘要 ===');
  console.log(`用户数量: ${report.summary.totalUsers}`);
  console.log(`问卷回复: ${report.summary.totalQuestionnaires}`);
  console.log(`问卷心声: ${report.summary.totalVoices}`);
  console.log(`故事内容: ${report.summary.totalStories}`);
  console.log(`系统标签: ${report.summary.totalTags}`);
}

// 主执行函数
async function main() {
  console.log('=== 数据库初始化脚本 v2.0 ===\n');
  
  // 解析命令行参数
  parseArguments();
  
  console.log('配置信息:');
  console.log(`  数据库路径: ${CONFIG.dbPath}`);
  console.log(`  备份路径: ${CONFIG.backupPath}`);
  console.log(`  测试数据数量: ${CONFIG.testDataCount}`);
  console.log(`  跳过迁移: ${CONFIG.skipMigration}`);
  console.log(`  跳过测试数据: ${CONFIG.skipTestData}`);
  console.log(`  强制重置: ${CONFIG.forceReset}`);
  console.log('');
  
  try {
    // 1. 检查数据库状态
    const dbStatus = await checkDatabaseStatus();
    
    // 2. 处理强制重置
    if (CONFIG.forceReset) {
      await backupDatabase();
      await resetDatabase();
    } else if (dbStatus.exists && !dbStatus.hasV2Tables) {
      // 3. 备份现有数据库（如果需要迁移）
      await backupDatabase();
    }
    
    // 4. 执行数据迁移
    await performMigration();
    
    // 5. 生成测试数据
    await generateTestData();
    
    // 6. 验证数据完整性
    const validationResults = await validateDatabase();
    
    // 7. 生成报告
    await generateReport(validationResults);
    
    console.log('\n🎉 数据库初始化成功完成！');
    
  } catch (error) {
    console.error('\n❌ 数据库初始化失败:', error.message);
    console.error(error.stack);
    process.exit(1);
  }
}

// 如果直接运行此脚本
if (import.meta.url === `file://${process.argv[1]}`) {
  main();
}

export { CONFIG, main };
