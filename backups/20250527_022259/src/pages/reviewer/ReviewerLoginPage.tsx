import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { useToast } from '@/components/ui/use-toast';
import { adminLogin } from '@/services/adminAuthService';

/**
 * 审核员登录页面
 * 
 * 专门用于审核员登录的简化页面
 */
const ReviewerLoginPage: React.FC = () => {
  const { toast } = useToast();
  const navigate = useNavigate();
  const [isLoading, setIsLoading] = useState(false);
  const [username, setUsername] = useState('reviewer');
  const [password, setPassword] = useState('reviewer123');

  // 处理登录
  const handleLogin = async () => {
    if (!username || !password) {
      toast({
        title: '请输入用户名和密码',
        variant: 'destructive',
      });
      return;
    }

    setIsLoading(true);

    try {
      // 先清除所有之前的登录信息
      localStorage.removeItem('adminToken');
      localStorage.removeItem('adminUser');
      sessionStorage.removeItem('redirectToRoleDashboard');

      // 等待一小段时间确保清除完成
      await new Promise(resolve => setTimeout(resolve, 100));

      // 调用登录服务
      const result = await adminLogin(username, password);

      if (result.success) {
        // 设置新的登录信息
        localStorage.setItem('adminToken', result.token);
        if (result.user) {
          localStorage.setItem('adminUser', JSON.stringify({
            ...result.user,
            role: 'reviewer', // 确保角色正确
            loginTime: new Date().toISOString() // 添加登录时间戳
          }));
        }

        // 设置重定向标志
        sessionStorage.setItem('redirectToRoleDashboard', 'true');
        console.log('设置审核员重定向标志');

        toast({
          title: '登录成功',
          description: `以审核员身份登录成功！`,
        });

        // 使用window.location直接跳转，确保页面刷新
        window.location.href = '/reviewer/dashboard';
      } else {
        toast({
          title: '登录失败',
          description: result.error || '用户名或密码错误',
          variant: 'destructive',
        });
      }
    } catch (error) {
      console.error('登录错误:', error);
      toast({
        title: '登录失败',
        description: '服务器错误，请稍后再试',
        variant: 'destructive',
      });
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="flex items-center justify-center min-h-screen bg-gray-100">
      <Card className="w-full max-w-md">
        <CardHeader className="space-y-1">
          <CardTitle className="text-2xl font-bold text-center">审核员登录</CardTitle>
          <CardDescription className="text-center">
            请输入您的审核员账号和密码
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="mb-4 bg-blue-50 border border-blue-200 rounded-md p-3">
            <h4 className="text-blue-700 font-medium text-sm mb-1">测试账号</h4>
            <p className="text-blue-600 text-xs">
              用户名: reviewer<br />
              密码: reviewer123
            </p>
          </div>
          <div className="space-y-4">
            <div className="space-y-2">
              <label className="text-sm font-medium">用户名</label>
              <Input
                value={username}
                onChange={(e) => setUsername(e.target.value)}
                placeholder="请输入用户名"
              />
            </div>
            <div className="space-y-2">
              <label className="text-sm font-medium">密码</label>
              <Input
                type="password"
                value={password}
                onChange={(e) => setPassword(e.target.value)}
                placeholder="请输入密码"
              />
            </div>
            <Button
              className="w-full"
              onClick={handleLogin}
              disabled={isLoading}
            >
              {isLoading ? '登录中...' : '登录'}
            </Button>
          </div>
        </CardContent>
        <CardFooter className="flex justify-center">
          <p className="text-sm text-gray-500">
            仅限审核员访问
          </p>
        </CardFooter>
      </Card>
    </div>
  );
};

export default ReviewerLoginPage;
