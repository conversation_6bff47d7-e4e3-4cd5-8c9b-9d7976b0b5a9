/**
 * CORS工具函数
 */

/**
 * 处理CORS预检请求
 * @param request 请求对象
 * @returns 响应对象
 */
export function handleCORS(request: Request): Response {
  // 获取请求头
  const headers = request.headers;
  
  // 获取请求方法
  const accessControlRequestMethod = headers.get('Access-Control-Request-Method');
  
  // 获取请求头
  const accessControlRequestHeaders = headers.get('Access-Control-Request-Headers');
  
  // 设置CORS头
  const corsHeaders = {
    'Access-Control-Allow-Origin': '*',
    'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
    'Access-Control-Allow-Headers': 'Content-Type, Authorization',
    'Access-Control-Max-Age': '86400' // 24小时
  };
  
  // 如果请求方法存在，添加到允许的方法中
  if (accessControlRequestMethod) {
    corsHeaders['Access-Control-Allow-Methods'] = accessControlRequestMethod;
  }
  
  // 如果请求头存在，添加到允许的头中
  if (accessControlRequestHeaders) {
    corsHeaders['Access-Control-Allow-Headers'] = accessControlRequestHeaders;
  }
  
  // 返回204 No Content
  return new Response(null, {
    status: 204,
    headers: corsHeaders
  });
}
