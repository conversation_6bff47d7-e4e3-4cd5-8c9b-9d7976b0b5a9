# Cloudflare 数据架构设计

基于 KV + D1 + R2 的多层数据存储策略

## 🎯 数据分层原则

### 1. **热数据 → KV** (高频读取，低延迟)
- 用户会话和认证信息
- 实时统计数据
- 缓存的查询结果
- 系统配置

### 2. **核心数据 → D1** (关系查询，事务支持)
- 用户基础信息
- 问卷结构和回复
- 审核日志
- 权限管理

### 3. **大文件 → R2** (大容量存储)
- 长文本内容
- 文件附件
- 数据备份
- 日志归档

## 📊 具体数据分配策略

### 🔥 **KV 存储设计**

#### 1. 用户会话管理
```typescript
// Key格式: session:{sessionId}
// TTL: 24小时
interface UserSession {
  userId: string;           // user_xxx
  anonymousId?: string;     // anon_xxx
  role: string;
  permissions: string[];
  lastActivity: number;
  ipAddress: string;
}

// 示例
KV.put("session:sess_abc123", JSON.stringify(userSession), { expirationTtl: 86400 });
```

#### 2. 实时统计缓存
```typescript
// Key格式: stats:{type}:{date}
// TTL: 1小时，定期更新
interface QuestionnaireStats {
  questionId: string;
  totalResponses: number;
  options: Array<{
    value: string;
    count: number;
    percentage: number;
  }>;
  lastUpdated: number;
}

// 示例
KV.put("stats:question:qitem_123:2024-01-15", JSON.stringify(stats), { expirationTtl: 3600 });
```

#### 3. 热门内容缓存
```typescript
// Key格式: hot:{type}:{timeframe}
// TTL: 30分钟
interface HotContent {
  stories: Array<{
    id: string;
    title: string;
    likes: number;
    views: number;
  }>;
  voices: Array<{
    id: string;
    title: string;
    type: string;
  }>;
  lastUpdated: number;
}

// 示例
KV.put("hot:content:daily", JSON.stringify(hotContent), { expirationTtl: 1800 });
```

#### 4. 系统配置
```typescript
// Key格式: config:{category}
// TTL: 永久，手动更新
interface SystemConfig {
  moderation: {
    autoApprove: boolean;
    sensitiveWords: string[];
  };
  features: {
    storySubmission: boolean;
    anonymousRegistration: boolean;
  };
  limits: {
    maxStoryLength: number;
    maxVoiceLength: number;
  };
}

// 示例
KV.put("config:system", JSON.stringify(config));
```

### 🗄️ **D1 存储设计**

#### 1. 核心用户数据
```sql
-- 用户基础信息（轻量级）
CREATE TABLE users (
  id TEXT PRIMARY KEY,              -- user_xxx
  anonymous_id TEXT UNIQUE,         -- anon_xxx
  email TEXT,
  role TEXT DEFAULT 'user',
  is_active BOOLEAN DEFAULT true,
  created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
  updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
);

-- 索引优化
CREATE INDEX idx_users_role ON users(role);
CREATE INDEX idx_users_anonymous_id ON users(anonymous_id);
CREATE INDEX idx_users_active ON users(is_active);
```

#### 2. 问卷结构数据
```sql
-- 问卷模板（相对稳定）
CREATE TABLE questionnaire_templates (
  id TEXT PRIMARY KEY,              -- quest_xxx
  title TEXT NOT NULL,
  version TEXT DEFAULT '1.0',
  is_active BOOLEAN DEFAULT true,
  created_at DATETIME DEFAULT CURRENT_TIMESTAMP
);

-- 题目定义
CREATE TABLE questions (
  id TEXT PRIMARY KEY,              -- qitem_xxx
  questionnaire_id TEXT NOT NULL,
  question_number INTEGER NOT NULL,
  question_text TEXT NOT NULL,
  question_type TEXT NOT NULL,      -- single, multiple, text
  options TEXT,                     -- JSON格式选项
  created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
  FOREIGN KEY (questionnaire_id) REFERENCES questionnaire_templates(id)
);

-- 高频查询索引
CREATE INDEX idx_questions_questionnaire ON questions(questionnaire_id);
CREATE INDEX idx_questions_type ON questions(question_type);
```

#### 3. 用户回复数据
```sql
-- 问卷回复记录
CREATE TABLE questionnaire_responses (
  id TEXT PRIMARY KEY,              -- resp_xxx
  user_id TEXT,
  questionnaire_id TEXT NOT NULL,
  session_id TEXT NOT NULL,         -- 关联会话
  completion_rate REAL DEFAULT 0,
  created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
  FOREIGN KEY (user_id) REFERENCES users(id),
  FOREIGN KEY (questionnaire_id) REFERENCES questionnaire_templates(id)
);

-- 用户答案（高频查询表）
CREATE TABLE question_answers (
  id TEXT PRIMARY KEY,              -- ans_xxx
  user_id TEXT NOT NULL,
  question_id TEXT NOT NULL,
  response_id TEXT NOT NULL,
  answer_value TEXT NOT NULL,       -- JSON格式答案
  created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
  FOREIGN KEY (user_id) REFERENCES users(id),
  FOREIGN KEY (question_id) REFERENCES questions(id),
  FOREIGN KEY (response_id) REFERENCES questionnaire_responses(id)
);

-- 关键索引
CREATE INDEX idx_answers_question ON question_answers(question_id);
CREATE INDEX idx_answers_user ON question_answers(user_id);
CREATE INDEX idx_answers_response ON question_answers(response_id);
```

#### 4. 审核和权限数据
```sql
-- 审核日志
CREATE TABLE review_logs (
  id TEXT PRIMARY KEY,              -- review_xxx
  reviewer_id TEXT NOT NULL,
  content_type TEXT NOT NULL,
  content_id TEXT NOT NULL,
  action TEXT NOT NULL,
  notes TEXT,
  created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
  FOREIGN KEY (reviewer_id) REFERENCES users(id)
);

-- 操作日志
CREATE TABLE operation_logs (
  id TEXT PRIMARY KEY,              -- op_xxx
  operator_id TEXT NOT NULL,
  operation_type TEXT NOT NULL,
  target_id TEXT,
  operation_data TEXT,              -- JSON格式
  created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
  FOREIGN KEY (operator_id) REFERENCES users(id)
);

-- 审核索引
CREATE INDEX idx_review_logs_reviewer ON review_logs(reviewer_id);
CREATE INDEX idx_review_logs_content ON review_logs(content_type, content_id);
CREATE INDEX idx_operation_logs_operator ON operation_logs(operator_id);
```

### 📁 **R2 存储设计**

#### 1. 长文本内容
```typescript
// 路径格式: content/{type}/{year}/{month}/{contentId}
interface StoredContent {
  id: string;                       // text_xxx
  type: 'story' | 'voice';
  title: string;
  content: string;                  // 完整文本内容
  metadata: {
    userId: string;
    version: number;
    wordCount: number;
    language: string;
    tags: string[];
  };
  audit: {
    createdAt: string;
    updatedAt: string;
    reviewStatus: string;
    reviewNotes?: string;
  };
}

// 示例路径
// content/story/2024/01/text_abc123.json
// content/voice/2024/01/voice_def456.json
```

#### 2. 数据备份
```typescript
// 路径格式: backups/{date}/{type}
// 每日自动备份关键数据
interface BackupData {
  timestamp: string;
  type: 'users' | 'responses' | 'content';
  data: any[];
  checksum: string;
}

// 示例路径
// backups/2024-01-15/users.json
// backups/2024-01-15/responses.json
```

#### 3. 文件附件
```typescript
// 路径格式: attachments/{userId}/{contentId}/{filename}
// 用户上传的图片、文档等
interface AttachmentMetadata {
  originalName: string;
  mimeType: string;
  size: number;
  uploadedAt: string;
  userId: string;
  contentId: string;
}

// 示例路径
// attachments/user_123/text_456/image.jpg
```

## 🔄 **数据流设计**

### 1. 问卷提交流程
```mermaid
graph TD
    A[用户提交问卷] --> B[D1: 保存回复记录]
    B --> C[D1: 保存答案数据]
    C --> D[R2: 保存长文本心声]
    D --> E[KV: 更新实时统计]
    E --> F[KV: 清除相关缓存]
```

### 2. 故事发布流程
```mermaid
graph TD
    A[用户发布故事] --> B[R2: 保存故事内容]
    B --> C[D1: 保存元数据记录]
    C --> D[KV: 更新热门内容]
    D --> E[审核队列处理]
```

### 3. 数据查询流程
```mermaid
graph TD
    A[API请求] --> B{检查KV缓存}
    B -->|命中| C[返回缓存数据]
    B -->|未命中| D[查询D1数据库]
    D --> E{需要长文本?}
    E -->|是| F[从R2获取内容]
    E -->|否| G[返回D1数据]
    F --> H[合并数据返回]
    G --> I[写入KV缓存]
    H --> I
```

## ⚡ **性能优化策略**

### 1. 缓存策略
```typescript
// 多层缓存设计
interface CacheStrategy {
  // L1: KV缓存 (1-10ms)
  hot: {
    userSessions: '24h',
    realtimeStats: '1h',
    hotContent: '30m',
    systemConfig: 'permanent'
  };
  
  // L2: D1查询 (10-50ms)
  warm: {
    userProfiles: 'on-demand',
    questionnaireStructure: 'on-demand',
    auditLogs: 'on-demand'
  };
  
  // L3: R2存储 (50-200ms)
  cold: {
    fullTextContent: 'on-demand',
    fileAttachments: 'on-demand',
    historicalBackups: 'on-demand'
  };
}
```

### 2. 查询优化
```typescript
// 智能查询路由
class DataRouter {
  async getQuestionStats(questionId: string) {
    // 1. 尝试KV缓存
    const cached = await KV.get(`stats:question:${questionId}`);
    if (cached) return JSON.parse(cached);
    
    // 2. 查询D1聚合数据
    const stats = await D1.prepare(`
      SELECT answer_value, COUNT(*) as count 
      FROM question_answers 
      WHERE question_id = ? 
      GROUP BY answer_value
    `).bind(questionId).all();
    
    // 3. 写入KV缓存
    await KV.put(`stats:question:${questionId}`, JSON.stringify(stats), {
      expirationTtl: 3600
    });
    
    return stats;
  }
  
  async getStoryContent(storyId: string) {
    // 1. 从D1获取元数据
    const metadata = await D1.prepare(`
      SELECT * FROM story_metadata WHERE id = ?
    `).bind(storyId).first();
    
    if (!metadata) return null;
    
    // 2. 从R2获取完整内容
    const contentPath = `content/story/${metadata.year}/${metadata.month}/${storyId}.json`;
    const content = await R2.get(contentPath);
    
    if (!content) return metadata; // 降级返回元数据
    
    // 3. 合并返回
    return {
      ...metadata,
      fullContent: await content.json()
    };
  }
}
```

### 3. 写入优化
```typescript
// 批量写入策略
class BatchWriter {
  private d1Batch: any[] = [];
  private kvBatch: Map<string, any> = new Map();
  
  async flushBatch() {
    // 1. 批量写入D1
    if (this.d1Batch.length > 0) {
      await D1.batch(this.d1Batch);
      this.d1Batch = [];
    }
    
    // 2. 批量写入KV
    if (this.kvBatch.size > 0) {
      const promises = Array.from(this.kvBatch.entries()).map(([key, value]) =>
        KV.put(key, JSON.stringify(value))
      );
      await Promise.all(promises);
      this.kvBatch.clear();
    }
  }
}
```

## 🚨 **风险控制**

### 1. 数据一致性
```typescript
// 分布式事务模拟
class DistributedTransaction {
  async submitQuestionnaire(data: QuestionnaireData) {
    const rollbackActions: (() => Promise<void>)[] = [];
    
    try {
      // 1. D1写入
      const responseId = await this.writeToD1(data);
      rollbackActions.push(() => this.deleteFromD1(responseId));
      
      // 2. R2写入
      if (data.longText) {
        await this.writeToR2(responseId, data.longText);
        rollbackActions.push(() => this.deleteFromR2(responseId));
      }
      
      // 3. KV更新
      await this.updateKVStats(data);
      rollbackActions.push(() => this.revertKVStats(data));
      
      return { success: true, responseId };
      
    } catch (error) {
      // 回滚操作
      for (const rollback of rollbackActions.reverse()) {
        try {
          await rollback();
        } catch (rollbackError) {
          console.error('Rollback failed:', rollbackError);
        }
      }
      throw error;
    }
  }
}
```

### 2. 降级策略
```typescript
// 服务降级
class ServiceDegrader {
  async getDataWithFallback(key: string) {
    try {
      // 优先KV
      return await KV.get(key);
    } catch (kvError) {
      console.warn('KV failed, falling back to D1');
      try {
        // 降级到D1
        return await this.getFromD1(key);
      } catch (d1Error) {
        console.error('D1 also failed, using static fallback');
        // 最终降级到静态数据
        return this.getStaticFallback(key);
      }
    }
  }
}
```

### 3. 监控告警
```typescript
// 性能监控
class PerformanceMonitor {
  async trackQuery(operation: string, fn: () => Promise<any>) {
    const start = Date.now();
    try {
      const result = await fn();
      const duration = Date.now() - start;
      
      // 记录性能指标
      await this.recordMetric(operation, duration, 'success');
      
      // 性能告警
      if (duration > 1000) {
        await this.sendAlert(`Slow query: ${operation} took ${duration}ms`);
      }
      
      return result;
    } catch (error) {
      const duration = Date.now() - start;
      await this.recordMetric(operation, duration, 'error');
      throw error;
    }
  }
}
```

## 📋 **实施建议**

### 立即执行
1. **数据分层迁移**: 按照上述策略重新分配数据
2. **缓存层实现**: 实现KV缓存策略
3. **查询路由**: 实现智能数据路由

### 分阶段优化
1. **第一阶段**: 基础分层和缓存
2. **第二阶段**: 性能监控和降级策略
3. **第三阶段**: 高级优化和自动扩展

这个架构设计如何？是否需要调整某些数据分配策略？
