import { useState, useEffect } from 'react';
import { Badge } from '@/components/ui/badge';
import { cn } from '@/lib/utils';

interface TagCloudProps {
  tags: Array<{ tag: string; count: number }>;
  selectedTags: string[];
  onTagSelect: (tag: string) => void;
  className?: string;
  maxTags?: number;
}

export default function TagCloud({
  tags,
  selectedTags,
  onTagSelect,
  className,
  maxTags = 20,
}: TagCloudProps) {
  const [displayTags, setDisplayTags] = useState<Array<{ tag: string; count: number; size: number }>>(
    []
  );

  useEffect(() => {
    if (tags.length === 0) return;

    // 限制标签数量
    const limitedTags = tags.slice(0, maxTags);

    // 找出最大和最小计数
    const maxCount = Math.max(...limitedTags.map((t) => t.count));
    const minCount = Math.min(...limitedTags.map((t) => t.count));
    
    // 计算每个标签的大小 (范围从 1 到 5)
    const tagSizes = limitedTags.map((tag) => {
      let size = 1;
      if (maxCount !== minCount) {
        // 将计数映射到 1-5 的范围
        size = 1 + Math.floor(((tag.count - minCount) / (maxCount - minCount)) * 4);
      }
      return {
        ...tag,
        size,
      };
    });

    // 随机排序标签以创建更自然的云效果
    const shuffledTags = [...tagSizes].sort(() => Math.random() - 0.5);
    
    setDisplayTags(shuffledTags);
  }, [tags, maxTags]);

  if (tags.length === 0) {
    return (
      <div className={cn("text-center py-4", className)}>
        <p className="text-sm text-gray-500">暂无标签数据</p>
      </div>
    );
  }

  return (
    <div className={cn("flex flex-wrap gap-2", className)}>
      {displayTags.map((tag) => (
        <Badge
          key={tag.tag}
          variant={selectedTags.includes(tag.tag) ? "default" : "outline"}
          className={cn(
            "cursor-pointer hover:bg-primary/90 hover:text-primary-foreground transition-colors",
            {
              "text-xs": tag.size === 1,
              "text-sm": tag.size === 2,
              "text-base": tag.size === 3,
              "text-lg": tag.size === 4,
              "text-xl": tag.size === 5,
            }
          )}
          onClick={() => onTagSelect(tag.tag)}
        >
          {tag.tag} ({tag.count})
        </Badge>
      ))}
    </div>
  );
}
