#!/usr/bin/env node

/**
 * 🗄️ 数据库优化器
 * 分析和优化数据库性能，包括索引创建、查询优化等
 */

const fs = require('fs');
const path = require('path');

class DatabaseOptimizer {
  constructor() {
    this.optimizations = [];
    this.indexAnalysis = {};
    this.queryAnalysis = {};
  }

  // 生成索引优化建议
  generateIndexOptimizations() {
    console.log('🔍 分析数据库索引需求...');

    const indexRecommendations = [
      {
        table: 'questionnaire_responses_v2',
        indexes: [
          {
            name: 'idx_questionnaire_created_at',
            columns: ['created_at'],
            type: 'btree',
            purpose: '优化时间范围查询（今日、本周、本月统计）',
            impact: 'high',
            sql: 'CREATE INDEX IF NOT EXISTS idx_questionnaire_created_at ON questionnaire_responses_v2(created_at);'
          },
          {
            name: 'idx_questionnaire_major_display',
            columns: ['major_display'],
            type: 'btree',
            purpose: '优化专业分布统计查询',
            impact: 'high',
            sql: 'CREATE INDEX IF NOT EXISTS idx_questionnaire_major_display ON questionnaire_responses_v2(major_display) WHERE major_display IS NOT NULL;'
          },
          {
            name: 'idx_questionnaire_employment_status',
            columns: ['employment_status_display'],
            type: 'btree',
            purpose: '优化就业状态统计查询',
            impact: 'medium',
            sql: 'CREATE INDEX IF NOT EXISTS idx_questionnaire_employment_status ON questionnaire_responses_v2(employment_status_display);'
          },
          {
            name: 'idx_questionnaire_education_level',
            columns: ['education_level_display'],
            type: 'btree',
            purpose: '优化教育水平统计查询',
            impact: 'medium',
            sql: 'CREATE INDEX IF NOT EXISTS idx_questionnaire_education_level ON questionnaire_responses_v2(education_level_display);'
          },
          {
            name: 'idx_questionnaire_graduation_year',
            columns: ['graduation_year'],
            type: 'btree',
            purpose: '优化毕业年份统计查询',
            impact: 'medium',
            sql: 'CREATE INDEX IF NOT EXISTS idx_questionnaire_graduation_year ON questionnaire_responses_v2(graduation_year);'
          },
          {
            name: 'idx_questionnaire_anonymous',
            columns: ['is_anonymous'],
            type: 'btree',
            purpose: '优化匿名/实名统计查询',
            impact: 'low',
            sql: 'CREATE INDEX IF NOT EXISTS idx_questionnaire_anonymous ON questionnaire_responses_v2(is_anonymous);'
          },
          {
            name: 'idx_questionnaire_composite_stats',
            columns: ['created_at', 'major_display', 'employment_status_display'],
            type: 'composite',
            purpose: '优化复合统计查询',
            impact: 'high',
            sql: 'CREATE INDEX IF NOT EXISTS idx_questionnaire_composite_stats ON questionnaire_responses_v2(created_at, major_display, employment_status_display);'
          }
        ]
      },
      {
        table: 'story_contents_v2',
        indexes: [
          {
            name: 'idx_story_created_at',
            columns: ['created_at'],
            type: 'btree',
            purpose: '优化故事列表时间排序',
            impact: 'high',
            sql: 'CREATE INDEX IF NOT EXISTS idx_story_created_at ON story_contents_v2(created_at DESC);'
          },
          {
            name: 'idx_story_status',
            columns: ['status'],
            type: 'btree',
            purpose: '优化故事状态过滤',
            impact: 'medium',
            sql: 'CREATE INDEX IF NOT EXISTS idx_story_status ON story_contents_v2(status);'
          },
          {
            name: 'idx_story_composite_list',
            columns: ['status', 'created_at'],
            type: 'composite',
            purpose: '优化故事列表查询（状态+时间）',
            impact: 'high',
            sql: 'CREATE INDEX IF NOT EXISTS idx_story_composite_list ON story_contents_v2(status, created_at DESC);'
          }
        ]
      }
    ];

    this.indexAnalysis = {
      recommendations: indexRecommendations,
      totalIndexes: indexRecommendations.reduce((sum, table) => sum + table.indexes.length, 0),
      highImpact: indexRecommendations.reduce((sum, table) => 
        sum + table.indexes.filter(idx => idx.impact === 'high').length, 0
      ),
      estimatedImprovement: '60-80%'
    };

    return indexRecommendations;
  }

  // 生成查询优化建议
  generateQueryOptimizations() {
    console.log('🚀 分析查询优化机会...');

    const queryOptimizations = [
      {
        category: 'Statistics Queries',
        optimizations: [
          {
            title: '合并统计查询',
            description: '将多个独立的COUNT查询合并为单个复杂查询',
            before: `
-- 原始方式：多个查询
SELECT COUNT(*) FROM questionnaire_responses_v2;
SELECT COUNT(*) FROM questionnaire_responses_v2 WHERE is_anonymous = 0;
SELECT COUNT(*) FROM questionnaire_responses_v2 WHERE employment_status_display = '已就业';
-- ... 更多查询`,
            after: `
-- 优化方式：单个查询
SELECT 
  COUNT(*) as total,
  COUNT(CASE WHEN is_anonymous = 0 THEN 1 END) as verified,
  COUNT(CASE WHEN employment_status_display = '已就业' THEN 1 END) as employed
FROM questionnaire_responses_v2;`,
            impact: 'high',
            estimatedImprovement: '70%'
          },
          {
            title: '使用CTE优化复杂统计',
            description: '使用公共表表达式(CTE)组织复杂的统计查询',
            before: `
-- 多个独立查询
SELECT major_display, COUNT(*) FROM questionnaire_responses_v2 GROUP BY major_display;
SELECT graduation_year, COUNT(*) FROM questionnaire_responses_v2 GROUP BY graduation_year;`,
            after: `
-- 使用CTE的单个查询
WITH major_stats AS (...),
     graduation_stats AS (...)
SELECT * FROM major_stats
UNION ALL
SELECT * FROM graduation_stats;`,
            impact: 'medium',
            estimatedImprovement: '40%'
          }
        ]
      },
      {
        category: 'Pagination Queries',
        optimizations: [
          {
            title: '优化分页查询',
            description: '使用基于游标的分页替代OFFSET',
            before: `
-- OFFSET分页（性能随页数下降）
SELECT * FROM story_contents_v2 
ORDER BY created_at DESC 
LIMIT 20 OFFSET 1000;`,
            after: `
-- 基于游标的分页（性能稳定）
SELECT * FROM story_contents_v2 
WHERE created_at < ?
ORDER BY created_at DESC 
LIMIT 20;`,
            impact: 'high',
            estimatedImprovement: '90% for deep pagination'
          }
        ]
      }
    ];

    this.queryAnalysis = {
      optimizations: queryOptimizations,
      totalOptimizations: queryOptimizations.reduce((sum, cat) => sum + cat.optimizations.length, 0),
      estimatedOverallImprovement: '50-70%'
    };

    return queryOptimizations;
  }

  // 生成缓存策略建议
  generateCacheStrategies() {
    console.log('💾 分析缓存策略...');

    return [
      {
        type: 'Application Cache',
        strategy: 'In-Memory Cache',
        targets: [
          {
            endpoint: '/api/questionnaire/stats',
            ttl: '5 minutes',
            reason: '统计数据变化不频繁',
            implementation: 'Map-based cache with TTL'
          },
          {
            endpoint: '/api/story/list',
            ttl: '2 minutes',
            reason: '故事列表更新相对频繁',
            implementation: 'LRU cache with pagination key'
          }
        ]
      },
      {
        type: 'Database Cache',
        strategy: 'Query Result Cache',
        targets: [
          {
            query: 'Complex statistics aggregations',
            ttl: '10 minutes',
            reason: '复杂聚合查询计算成本高',
            implementation: 'SQLite query cache or Redis'
          }
        ]
      },
      {
        type: 'CDN Cache',
        strategy: 'Edge Caching',
        targets: [
          {
            endpoint: 'Static statistics endpoints',
            ttl: '1 hour',
            reason: '统计数据可以容忍一定延迟',
            implementation: 'Cloudflare Cache API'
          }
        ]
      }
    ];
  }

  // 生成数据库优化SQL脚本
  generateOptimizationSQL() {
    console.log('📝 生成数据库优化SQL脚本...');

    const indexRecommendations = this.generateIndexOptimizations();
    
    let sql = `-- 🗄️ 数据库性能优化脚本
-- 生成时间: ${new Date().toISOString()}
-- 目标: 优化问卷统计和故事列表查询性能

-- ============================================
-- 索引优化
-- ============================================

`;

    indexRecommendations.forEach(table => {
      sql += `-- ${table.table} 表索引优化\n`;
      table.indexes.forEach(index => {
        sql += `-- ${index.purpose} (影响: ${index.impact})\n`;
        sql += `${index.sql}\n\n`;
      });
      sql += '\n';
    });

    sql += `-- ============================================
-- 查询优化示例
-- ============================================

-- 优化版统计查询（替代多个独立查询）
-- 预期性能提升: 70%
CREATE VIEW IF NOT EXISTS questionnaire_stats_optimized AS
WITH base_stats AS (
  SELECT 
    COUNT(*) as total_responses,
    COUNT(CASE WHEN is_anonymous = 0 THEN 1 END) as verified_count,
    COUNT(CASE WHEN is_anonymous = 1 THEN 1 END) as anonymous_count,
    COUNT(CASE WHEN employment_status_display = '已就业' THEN 1 END) as employed_count,
    COUNT(CASE WHEN employment_status_display = '未就业' THEN 1 END) as unemployed_count
  FROM questionnaire_responses_v2
)
SELECT * FROM base_stats;

-- ============================================
-- 性能监控查询
-- ============================================

-- 检查索引使用情况
-- EXPLAIN QUERY PLAN SELECT * FROM questionnaire_responses_v2 WHERE created_at >= date('now', '-7 days');

-- 检查表统计信息
-- SELECT name, sql FROM sqlite_master WHERE type='index' AND tbl_name='questionnaire_responses_v2';

-- ============================================
-- 维护建议
-- ============================================

-- 1. 定期运行 ANALYZE 命令更新统计信息
-- ANALYZE;

-- 2. 定期检查查询计划
-- 使用 EXPLAIN QUERY PLAN 分析慢查询

-- 3. 监控数据库大小和性能
-- 定期运行性能测试脚本
`;

    return sql;
  }

  // 生成性能监控脚本
  generatePerformanceMonitoringScript() {
    console.log('📊 生成性能监控脚本...');

    return `#!/usr/bin/env node

/**
 * 📊 数据库性能监控脚本
 * 监控关键查询的性能指标
 */

const { performance } = require('perf_hooks');

class DatabasePerformanceMonitor {
  constructor(db) {
    this.db = db;
    this.metrics = [];
  }

  async measureQuery(name, query, params = []) {
    const start = performance.now();
    
    try {
      const result = await this.db.prepare(query).bind(...params).all();
      const duration = performance.now() - start;
      
      this.metrics.push({
        name,
        duration: Math.round(duration * 100) / 100,
        success: true,
        resultCount: result.results?.length || 0,
        timestamp: new Date().toISOString()
      });
      
      return result;
    } catch (error) {
      const duration = performance.now() - start;
      
      this.metrics.push({
        name,
        duration: Math.round(duration * 100) / 100,
        success: false,
        error: error.message,
        timestamp: new Date().toISOString()
      });
      
      throw error;
    }
  }

  async runPerformanceTests() {
    console.log('🚀 开始数据库性能测试...');

    // 测试基础统计查询
    await this.measureQuery(
      'basic_count',
      'SELECT COUNT(*) as total FROM questionnaire_responses_v2'
    );

    // 测试复杂统计查询
    await this.measureQuery(
      'complex_stats',
      \`SELECT 
        COUNT(*) as total,
        COUNT(CASE WHEN is_anonymous = 0 THEN 1 END) as verified,
        COUNT(CASE WHEN employment_status_display = '已就业' THEN 1 END) as employed
      FROM questionnaire_responses_v2\`
    );

    // 测试分组查询
    await this.measureQuery(
      'major_grouping',
      \`SELECT major_display, COUNT(*) as count 
      FROM questionnaire_responses_v2 
      WHERE major_display IS NOT NULL 
      GROUP BY major_display 
      ORDER BY count DESC 
      LIMIT 15\`
    );

    // 测试时间范围查询
    await this.measureQuery(
      'time_range',
      \`SELECT COUNT(*) as count 
      FROM questionnaire_responses_v2 
      WHERE created_at >= date('now', '-7 days')\`
    );

    return this.generateReport();
  }

  generateReport() {
    const report = {
      timestamp: new Date().toISOString(),
      totalQueries: this.metrics.length,
      successfulQueries: this.metrics.filter(m => m.success).length,
      failedQueries: this.metrics.filter(m => !m.success).length,
      averageDuration: this.metrics.reduce((sum, m) => sum + m.duration, 0) / this.metrics.length,
      slowestQuery: this.metrics.reduce((slowest, current) => 
        current.duration > (slowest?.duration || 0) ? current : slowest, null),
      fastestQuery: this.metrics.reduce((fastest, current) => 
        current.duration < (fastest?.duration || Infinity) ? current : fastest, null),
      metrics: this.metrics
    };

    console.log('📊 性能测试报告:');
    console.log(\`总查询数: \${report.totalQueries}\`);
    console.log(\`成功查询: \${report.successfulQueries}\`);
    console.log(\`失败查询: \${report.failedQueries}\`);
    console.log(\`平均耗时: \${report.averageDuration.toFixed(2)}ms\`);
    
    if (report.slowestQuery) {
      console.log(\`最慢查询: \${report.slowestQuery.name} (\${report.slowestQuery.duration}ms)\`);
    }
    
    if (report.fastestQuery) {
      console.log(\`最快查询: \${report.fastestQuery.name} (\${report.fastestQuery.duration}ms)\`);
    }

    return report;
  }
}

module.exports = DatabasePerformanceMonitor;
`;
  }

  // 运行完整的数据库优化分析
  async runOptimizationAnalysis() {
    console.log('🚀 开始数据库优化分析...\n');

    // 生成各种优化建议
    const indexRecommendations = this.generateIndexOptimizations();
    const queryOptimizations = this.generateQueryOptimizations();
    const cacheStrategies = this.generateCacheStrategies();

    // 生成SQL脚本
    const optimizationSQL = this.generateOptimizationSQL();
    const monitoringScript = this.generatePerformanceMonitoringScript();

    // 保存文件
    const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
    
    // 保存SQL优化脚本
    const sqlFilename = `database-optimization-${timestamp}.sql`;
    fs.writeFileSync(sqlFilename, optimizationSQL);
    console.log(`📄 SQL优化脚本已保存: ${sqlFilename}`);

    // 保存性能监控脚本
    const monitorFilename = `database-monitor-${timestamp}.js`;
    fs.writeFileSync(monitorFilename, monitoringScript);
    console.log(`📄 性能监控脚本已保存: ${monitorFilename}`);

    // 生成完整报告
    const report = {
      timestamp: new Date().toISOString(),
      analysis: {
        indexes: this.indexAnalysis,
        queries: this.queryAnalysis,
        caching: cacheStrategies
      },
      files: {
        sqlOptimization: sqlFilename,
        performanceMonitoring: monitorFilename
      },
      summary: {
        totalRecommendations: indexRecommendations.reduce((sum, table) => sum + table.indexes.length, 0) +
                             queryOptimizations.reduce((sum, cat) => sum + cat.optimizations.length, 0),
        estimatedImprovement: '60-80%',
        implementationPriority: [
          '1. 创建高影响索引',
          '2. 实施查询优化',
          '3. 部署缓存策略',
          '4. 建立性能监控'
        ]
      }
    };

    // 保存分析报告
    const reportFilename = `database-optimization-report-${timestamp}.json`;
    fs.writeFileSync(reportFilename, JSON.stringify(report, null, 2));
    console.log(`📄 优化分析报告已保存: ${reportFilename}`);

    // 输出总结
    console.log('\n🎉 数据库优化分析完成!');
    console.log(`📊 总优化建议数: ${report.summary.totalRecommendations}`);
    console.log(`⚡ 预期性能提升: ${report.summary.estimatedImprovement}`);
    console.log('\n📋 实施优先级:');
    report.summary.implementationPriority.forEach((item, index) => {
      console.log(`   ${item}`);
    });

    return report;
  }
}

// 主函数
async function main() {
  const optimizer = new DatabaseOptimizer();
  await optimizer.runOptimizationAnalysis();
}

if (require.main === module) {
  main().catch(console.error);
}

module.exports = DatabaseOptimizer;
