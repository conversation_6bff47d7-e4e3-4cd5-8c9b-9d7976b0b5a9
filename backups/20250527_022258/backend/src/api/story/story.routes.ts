/**
 * 故事路由
 * 
 * 处理故事相关的路由
 */

import { Hono } from 'hono';
import { Env } from '../../types';
import * as storyController from './story.controller';
import { rateLimit } from '../../middlewares/rateLimit';
import { storyModeration } from '../../middlewares/contentModeration.middleware';

// 创建路由
const app = new Hono<{ Bindings: Env }>();

// 提交故事
app.post('/submit', 
  // 使用异步审核中间件
  storyModeration({ async: true }),
  ...storyController.submitStory
);

// 获取故事详情
app.get('/:id', storyController.getStoryById);

// 获取故事列表
app.get('/', storyController.getStories);

// 点赞故事
app.post('/:id/like', storyController.likeStory);

// 评论故事
app.post('/:id/comment', storyController.commentStory);

export default app;
