#!/bin/bash

# 部署脚本

set -e

ENVIRONMENT=${1:-staging}

echo "🚀 开始部署到 $ENVIRONMENT 环境..."

# 验证环境参数
if [[ ! "$ENVIRONMENT" =~ ^(staging|production)$ ]]; then
    echo "❌ 无效的环境参数: $ENVIRONMENT"
    echo "用法: ./scripts/deploy.sh <staging|production>"
    exit 1
fi

# 切换到对应环境配置
echo "🔧 配置 $ENVIRONMENT 环境..."
node scripts/env-manager.js switch $ENVIRONMENT

# 构建前端
if [ -d "frontend" ]; then
    echo "🎨 构建前端..."
    cd frontend

    # 安装依赖
    if [ ! -d "node_modules" ]; then
        echo "📦 安装前端依赖..."
        npm install
    fi

    # 构建
    echo "🔨 构建前端应用..."
    npm run build

    # 部署前端
    echo "🌐 部署前端到 Cloudflare Pages..."
    if [ "$ENVIRONMENT" = "production" ]; then
        wrangler pages deploy dist --project-name=college-employment-survey
    else
        wrangler pages deploy dist --project-name=college-employment-survey
    fi

    cd ..
fi

# 部署后端
if [ -d "backend" ]; then
    echo "🔥 部署后端..."
    cd backend

    # 部署到 Cloudflare Workers
    echo "🌐 部署后端到 Cloudflare Workers..."
    if [ "$ENVIRONMENT" = "production" ]; then
        wrangler deploy --env=production
    else
        wrangler deploy --env=staging
    fi

    cd ..
fi

echo ""
echo "🎉 部署完成！"
echo ""
echo "📍 访问地址:"
if [ "$ENVIRONMENT" = "production" ]; then
    echo "   前端: https://college-employment-survey.pages.dev"
    echo "   后端: https://college-employment-survey.aibook2099.workers.dev"
else
    echo "   前端: https://staging.college-employment-survey.pages.dev"
    echo "   后端: https://staging.college-employment-survey.aibook2099.workers.dev"
fi
echo ""
echo "🔧 后续操作:"
echo "   查看部署状态: wrangler pages deployment list"
echo "   查看日志: wrangler tail"
echo "   切换回本地环境: node scripts/env-manager.js switch local"
