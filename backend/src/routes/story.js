/**
 * 📖 故事模块路由
 * 包含故事列表、详情、投票等相关API
 */

import { Hono } from 'hono';
import { createSuccessResponse, createErrorResponse, wrapAsyncHandler } from '../utils/response.js';
import { validatePaginationParams } from '../utils/pagination.js';

const storyRouter = new Hono();

// 故事列表API
storyRouter.get('/list', wrapAsyncHandler(async (c) => {
  const { page, limit, offset } = validatePaginationParams(
    c.req.query('page'),
    c.req.query('pageSize'),
    20
  );

  // 获取总数
  const totalResult = await c.env.DB.prepare(
    'SELECT COUNT(*) as total FROM story_contents_v2 WHERE status = ?'
  ).bind('approved').first();
  const total = totalResult?.total || 0;

  // 获取故事列表
  const storiesResult = await c.env.DB.prepare(`
    SELECT
      id,
      title,
      content,
      summary,
      category,
      education_level_display,
      industry_display,
      likes,
      views,
      created_at
    FROM story_contents_v2
    WHERE status = ?
    ORDER BY created_at DESC
    LIMIT ? OFFSET ?
  `).bind('approved', limit, offset).all();

  const stories = storiesResult.results?.map(story => ({
    id: story.id,
    title: story.title,
    content: story.content,
    summary: story.summary,
    category: story.category,
    metadata: {
      educationLevel: story.education_level_display,
      industry: story.industry_display
    },
    stats: {
      likes: story.likes,
      views: story.views
    },
    createdAt: story.created_at
  })) || [];

  const pagination = {
    total,
    page,
    limit,
    pages: Math.ceil(total / limit),
    hasNext: page * limit < total,
    hasPrev: page > 1
  };

  return c.json(createSuccessResponse(stories, 'Stories retrieved successfully', { pagination }));
}));

// 故事详情API
storyRouter.get('/detail/:id', wrapAsyncHandler(async (c) => {
  const id = c.req.param('id');

  // 获取故事详情
  const storyResult = await c.env.DB.prepare(`
    SELECT
      id,
      title,
      content,
      summary,
      category,
      education_level_display,
      industry_display,
      region_display,
      likes,
      views,
      created_at,
      updated_at
    FROM story_contents_v2
    WHERE id = ? AND status = ?
  `).bind(id, 'approved').first();

  if (!storyResult) {
    return c.json(createErrorResponse('Story not found', 404), 404);
  }

  // 增加浏览次数
  await c.env.DB.prepare(`
    UPDATE story_contents_v2
    SET views = views + 1
    WHERE id = ?
  `).bind(id).run();

  const story = {
    id: storyResult.id,
    title: storyResult.title,
    content: storyResult.content,
    summary: storyResult.summary,
    category: storyResult.category,
    metadata: {
      educationLevel: storyResult.education_level_display,
      industry: storyResult.industry_display,
      region: storyResult.region_display
    },
    stats: {
      likes: storyResult.likes,
      views: storyResult.views + 1 // 包含本次浏览
    },
    createdAt: storyResult.created_at,
    updatedAt: storyResult.updated_at
  };

  return c.json(createSuccessResponse(story, 'Story retrieved successfully'));
}));

// 故事投票API
storyRouter.post('/vote', wrapAsyncHandler(async (c) => {
  const { storyId, voteType } = await c.req.json();

  if (!storyId || !voteType) {
    return c.json(createErrorResponse('Story ID and vote type are required', 400), 400);
  }

  if (!['like', 'dislike'].includes(voteType)) {
    return c.json(createErrorResponse('Invalid vote type', 400), 400);
  }

  // 检查故事是否存在
  const storyResult = await c.env.DB.prepare(`
    SELECT id, likes, dislikes
    FROM story_contents_v2
    WHERE id = ? AND status = ?
  `).bind(storyId, 'approved').first();

  if (!storyResult) {
    return c.json(createErrorResponse('Story not found', 404), 404);
  }

  // 更新投票数
  const updateField = voteType === 'like' ? 'likes' : 'dislikes';
  await c.env.DB.prepare(`
    UPDATE story_contents_v2
    SET ${updateField} = ${updateField} + 1
    WHERE id = ?
  `).bind(storyId).run();

  // 获取更新后的数据
  const updatedStoryResult = await c.env.DB.prepare(`
    SELECT likes, dislikes
    FROM story_contents_v2
    WHERE id = ?
  `).bind(storyId).first();

  const result = {
    storyId,
    voteType,
    newStats: {
      likes: updatedStoryResult.likes,
      dislikes: updatedStoryResult.dislikes
    }
  };

  return c.json(createSuccessResponse(result, 'Vote recorded successfully'));
}));

// 故事分类统计API
storyRouter.get('/categories/stats', wrapAsyncHandler(async (c) => {
  const categoriesResult = await c.env.DB.prepare(`
    SELECT
      category,
      COUNT(*) as count,
      SUM(likes) as total_likes,
      SUM(views) as total_views
    FROM story_contents_v2
    WHERE status = ? AND category IS NOT NULL
    GROUP BY category
    ORDER BY count DESC
  `).bind('approved').all();

  const categories = categoriesResult.results?.map(cat => ({
    name: cat.category,
    count: cat.count,
    totalLikes: cat.total_likes,
    totalViews: cat.total_views
  })) || [];

  return c.json(createSuccessResponse(categories, 'Category statistics retrieved successfully'));
}));

// 热门故事API
storyRouter.get('/trending', wrapAsyncHandler(async (c) => {
  const { limit } = validatePaginationParams(1, c.req.query('limit') || '10', 50);

  const trendingResult = await c.env.DB.prepare(`
    SELECT
      id,
      title,
      summary,
      category,
      likes,
      views,
      created_at,
      (likes * 2 + views * 0.1) as trending_score
    FROM story_contents_v2
    WHERE status = ?
    ORDER BY trending_score DESC, created_at DESC
    LIMIT ?
  `).bind('approved', limit).all();

  const trending = trendingResult.results?.map(story => ({
    id: story.id,
    title: story.title,
    summary: story.summary,
    category: story.category,
    stats: {
      likes: story.likes,
      views: story.views,
      trendingScore: Math.round(story.trending_score)
    },
    createdAt: story.created_at
  })) || [];

  return c.json(createSuccessResponse(trending, 'Trending stories retrieved successfully'));
}));

export default storyRouter;
