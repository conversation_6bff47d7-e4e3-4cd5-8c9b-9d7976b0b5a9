#!/bin/bash
# pre-deploy-check.sh - 部署前自动检查脚本

echo "🔍 部署前检查开始..."

# 配置
BACKEND_DIR="backend"
FRONTEND_DIR="frontend"
API_BASE="https://college-employment-survey.aibook2099.workers.dev"

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# 检查函数
check_pass() {
    echo -e "   ${GREEN}✅ $1${NC}"
}

check_fail() {
    echo -e "   ${RED}❌ $1${NC}"
    FAILED_CHECKS=$((FAILED_CHECKS + 1))
}

check_warn() {
    echo -e "   ${YELLOW}⚠️  $1${NC}"
}

FAILED_CHECKS=0

echo ""
echo "1️⃣ 检查项目结构..."

# 检查目录结构
if [ -d "$BACKEND_DIR" ]; then
    check_pass "后端目录存在"
else
    check_fail "后端目录不存在"
fi

if [ -d "$FRONTEND_DIR" ]; then
    check_pass "前端目录存在"
else
    check_fail "前端目录不存在"
fi

echo ""
echo "2️⃣ 检查后端配置..."

# 检查后端CORS配置
if [ -f "$BACKEND_DIR/index.js" ]; then
    check_pass "后端入口文件存在"
    
    # 检查CORS配置
    if grep -q "return true" "$BACKEND_DIR/index.js"; then
        check_fail "发现CORS配置错误: 使用了 'return true'"
        echo "      修复: 将 'return true' 改为 'return origin'"
    else
        check_pass "CORS配置检查通过"
    fi
    
    # 检查是否有origin函数
    if grep -q "origin: (origin)" "$BACKEND_DIR/index.js"; then
        check_pass "CORS origin函数存在"
    else
        check_warn "未找到CORS origin函数"
    fi
else
    check_fail "后端入口文件不存在"
fi

# 检查package.json
if [ -f "$BACKEND_DIR/package.json" ]; then
    check_pass "后端package.json存在"
else
    check_fail "后端package.json不存在"
fi

echo ""
echo "3️⃣ 检查前端配置..."

# 检查前端构建配置
if [ -f "$FRONTEND_DIR/package.json" ]; then
    check_pass "前端package.json存在"
else
    check_fail "前端package.json不存在"
fi

if [ -f "$FRONTEND_DIR/vite.config.ts" ]; then
    check_pass "Vite配置文件存在"
else
    check_warn "Vite配置文件不存在"
fi

echo ""
echo "4️⃣ 检查API连通性..."

# 检查API是否可达
if command -v curl >/dev/null 2>&1; then
    API_STATUS=$(curl -s -o /dev/null -w "%{http_code}" "$API_BASE/api/system/monitor" || echo "000")
    
    if [ "$API_STATUS" = "200" ]; then
        check_pass "API服务正常 ($API_STATUS)"
    elif [ "$API_STATUS" = "000" ]; then
        check_fail "API服务不可达"
    else
        check_warn "API服务异常 ($API_STATUS)"
    fi
else
    check_warn "curl命令不可用，跳过API检查"
fi

echo ""
echo "5️⃣ 检查依赖..."

# 检查后端依赖
if [ -f "$BACKEND_DIR/package.json" ]; then
    cd "$BACKEND_DIR"
    if [ -d "node_modules" ]; then
        check_pass "后端依赖已安装"
    else
        check_warn "后端依赖未安装，建议运行 npm install"
    fi
    cd ..
fi

# 检查前端依赖
if [ -f "$FRONTEND_DIR/package.json" ]; then
    cd "$FRONTEND_DIR"
    if [ -d "node_modules" ]; then
        check_pass "前端依赖已安装"
    else
        check_warn "前端依赖未安装，建议运行 npm install"
    fi
    cd ..
fi

echo ""
echo "6️⃣ 检查构建..."

# 检查前端构建
if [ -d "$FRONTEND_DIR/dist" ]; then
    check_pass "前端构建目录存在"
    
    # 检查构建文件
    if [ -f "$FRONTEND_DIR/dist/index.html" ]; then
        check_pass "前端构建文件存在"
    else
        check_warn "前端构建文件不完整"
    fi
else
    check_warn "前端未构建，建议运行 npm run build"
fi

echo ""
echo "📋 检查总结:"

if [ $FAILED_CHECKS -eq 0 ]; then
    echo -e "${GREEN}✅ 所有检查通过，可以安全部署${NC}"
    exit 0
else
    echo -e "${RED}❌ 发现 $FAILED_CHECKS 个问题，建议修复后再部署${NC}"
    echo ""
    echo "🔧 常见修复方法:"
    echo "1. CORS配置错误:"
    echo "   - 修改 backend/index.js 中的 CORS 配置"
    echo "   - 将 'return true' 改为 'return origin'"
    echo ""
    echo "2. 依赖问题:"
    echo "   - cd backend && npm install"
    echo "   - cd frontend && npm install"
    echo ""
    echo "3. 构建问题:"
    echo "   - cd frontend && npm run build"
    echo ""
    echo "4. API连通性问题:"
    echo "   - 检查网络连接"
    echo "   - 验证API服务状态"
    
    exit 1
fi
