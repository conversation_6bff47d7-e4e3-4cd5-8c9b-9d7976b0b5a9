-- 标签数据初始化脚本 (自动生成)
-- 生成时间: 2025-05-24T17:39:48.783Z

-- 1. 清理现有数据
DROP TABLE IF EXISTS story_tags;
DROP TABLE IF EXISTS tags;

-- 2. 创建标签表
CREATE TABLE IF NOT EXISTS tags (
  id TEXT PRIMARY KEY,
  name TEXT NOT NULL,
  color TEXT DEFAULT 'blue',
  priority INTEGER DEFAULT 0,
  category TEXT DEFAULT 'other',
  parent_id TEXT,
  count INTEGER DEFAULT 0,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  FOREIGN KEY (parent_id) REFERENCES tags(id)
);

-- 3. 创建故事标签关联表
CREATE TABLE IF NOT EXISTS story_tags (
  story_id INTEGER,
  tag_id TEXT,
  PRIMARY KEY (story_id, tag_id),
  FOREIGN KEY (story_id) REFERENCES stories(id) ON DELETE CASCADE,
  FOREIGN KEY (tag_id) REFERENCES tags(id) ON DELETE CASCADE
);

-- 4. 插入标签数据
INSERT OR REPLACE INTO tags (id, name, color, priority, category, count) VALUES ('job-hunting', '求职故事', 'blue', 10, 'job', 156);
INSERT OR REPLACE INTO tags (id, name, color, priority, category, count) VALUES ('interview', '面试经验', 'blue', 9, 'job', 134);
INSERT OR REPLACE INTO tags (id, name, color, priority, category, count) VALUES ('resume', '简历技巧', 'blue', 8, 'job', 43);
INSERT OR REPLACE INTO tags (id, name, color, priority, category, count) VALUES ('job-search', '找工作', 'blue', 7, 'job', 89);
INSERT OR REPLACE INTO tags (id, name, color, priority, category, count) VALUES ('salary', '薪资谈判', 'blue', 6, 'job', 76);
INSERT OR REPLACE INTO tags (id, name, color, priority, category, count) VALUES ('offer', 'Offer选择', 'blue', 5, 'job', 32);
INSERT OR REPLACE INTO tags (id, name, color, priority, category, count) VALUES ('bachelor', '本科经验', 'green', 4, 'education', 65);
INSERT OR REPLACE INTO tags (id, name, color, priority, category, count) VALUES ('master', '硕士经验', 'green', 3, 'education', 38);
INSERT OR REPLACE INTO tags (id, name, color, priority, category, count) VALUES ('phd', '博士经验', 'green', 2, 'education', 12);
INSERT OR REPLACE INTO tags (id, name, color, priority, category, count) VALUES ('overseas-edu', '海外学历', 'green', 1, 'education', 18);
INSERT OR REPLACE INTO tags (id, name, color, priority, category, count) VALUES ('continuing-edu', '继续教育', 'green', 10, 'education', 8);
INSERT OR REPLACE INTO tags (id, name, color, priority, category, count) VALUES ('self-taught', '自学成才', 'green', 9, 'education', 15);
INSERT OR REPLACE INTO tags (id, name, color, priority, category, count) VALUES ('it-industry', 'IT行业', 'purple', 8, 'industry', 54);
INSERT OR REPLACE INTO tags (id, name, color, priority, category, count) VALUES ('finance', '金融行业', 'purple', 7, 'industry', 25);
INSERT OR REPLACE INTO tags (id, name, color, priority, category, count) VALUES ('education-industry', '教育行业', 'purple', 6, 'industry', 16);
INSERT OR REPLACE INTO tags (id, name, color, priority, category, count) VALUES ('healthcare', '医疗行业', 'purple', 5, 'industry', 14);
INSERT OR REPLACE INTO tags (id, name, color, priority, category, count) VALUES ('manufacturing', '制造业', 'purple', 4, 'industry', 11);
INSERT OR REPLACE INTO tags (id, name, color, priority, category, count) VALUES ('service', '服务业', 'purple', 3, 'industry', 13);
INSERT OR REPLACE INTO tags (id, name, color, priority, category, count) VALUES ('career-change', '转行经历', 'yellow', 2, 'experience', 87);
INSERT OR REPLACE INTO tags (id, name, color, priority, category, count) VALUES ('work-life', '工作生活', 'yellow', 1, 'experience', 32);
INSERT OR REPLACE INTO tags (id, name, color, priority, category, count) VALUES ('advice', '建议分享', 'yellow', 10, 'experience', 29);
INSERT OR REPLACE INTO tags (id, name, color, priority, category, count) VALUES ('internship', '实习经历', 'yellow', 9, 'experience', 98);
INSERT OR REPLACE INTO tags (id, name, color, priority, category, count) VALUES ('overseas', '海外就业', 'yellow', 8, 'experience', 22);
INSERT OR REPLACE INTO tags (id, name, color, priority, category, count) VALUES ('startup', '创业经历', 'yellow', 7, 'experience', 18);
INSERT OR REPLACE INTO tags (id, name, color, priority, category, count) VALUES ('remote-work', '远程工作', 'yellow', 6, 'experience', 15);
INSERT OR REPLACE INTO tags (id, name, color, priority, category, count) VALUES ('freelance', '自由职业', 'yellow', 5, 'experience', 8);
INSERT OR REPLACE INTO tags (id, name, color, priority, category, count) VALUES ('success', '成功故事', 'gray', 4, 'other', 12);
INSERT OR REPLACE INTO tags (id, name, color, priority, category, count) VALUES ('challenge', '挑战经历', 'gray', 3, 'other', 10);
INSERT OR REPLACE INTO tags (id, name, color, priority, category, count) VALUES ('inspiration', '励志故事', 'gray', 2, 'other', 9);

-- 5. 基于关键词为现有故事添加标签

-- 为包含"求职", "找工作", "应聘"的故事添加"job-hunting"标签
INSERT OR IGNORE INTO story_tags (story_id, tag_id)
SELECT s.id, 'job-hunting'
FROM stories s
WHERE ((s.title LIKE '%求职%' OR s.content LIKE '%求职%') OR (s.title LIKE '%找工作%' OR s.content LIKE '%找工作%') OR (s.title LIKE '%应聘%' OR s.content LIKE '%应聘%'))
  AND s.status = 'approved';

-- 为包含"面试", "面谈"的故事添加"interview"标签
INSERT OR IGNORE INTO story_tags (story_id, tag_id)
SELECT s.id, 'interview'
FROM stories s
WHERE ((s.title LIKE '%面试%' OR s.content LIKE '%面试%') OR (s.title LIKE '%面谈%' OR s.content LIKE '%面谈%'))
  AND s.status = 'approved';

-- 为包含"实习", "实习生"的故事添加"internship"标签
INSERT OR IGNORE INTO story_tags (story_id, tag_id)
SELECT s.id, 'internship'
FROM stories s
WHERE ((s.title LIKE '%实习%' OR s.content LIKE '%实习%') OR (s.title LIKE '%实习生%' OR s.content LIKE '%实习生%'))
  AND s.status = 'approved';

-- 为包含"转行", "换行业"的故事添加"career-change"标签
INSERT OR IGNORE INTO story_tags (story_id, tag_id)
SELECT s.id, 'career-change'
FROM stories s
WHERE ((s.title LIKE '%转行%' OR s.content LIKE '%转行%') OR (s.title LIKE '%换行业%' OR s.content LIKE '%换行业%'))
  AND s.status = 'approved';

-- 为包含"薪资", "工资", "薪水", "待遇"的故事添加"salary"标签
INSERT OR IGNORE INTO story_tags (story_id, tag_id)
SELECT s.id, 'salary'
FROM stories s
WHERE ((s.title LIKE '%薪资%' OR s.content LIKE '%薪资%') OR (s.title LIKE '%工资%' OR s.content LIKE '%工资%') OR (s.title LIKE '%薪水%' OR s.content LIKE '%薪水%') OR (s.title LIKE '%待遇%' OR s.content LIKE '%待遇%'))
  AND s.status = 'approved';

-- 为包含"简历", "CV", "resume"的故事添加"resume"标签
INSERT OR IGNORE INTO story_tags (story_id, tag_id)
SELECT s.id, 'resume'
FROM stories s
WHERE ((s.title LIKE '%简历%' OR s.content LIKE '%简历%') OR (s.title LIKE '%CV%' OR s.content LIKE '%CV%') OR (s.title LIKE '%resume%' OR s.content LIKE '%resume%'))
  AND s.status = 'approved';

-- 为包含"本科", "学士"的故事添加"bachelor"标签
INSERT OR IGNORE INTO story_tags (story_id, tag_id)
SELECT s.id, 'bachelor'
FROM stories s
WHERE ((s.title LIKE '%本科%' OR s.content LIKE '%本科%') OR (s.title LIKE '%学士%' OR s.content LIKE '%学士%'))
  AND s.status = 'approved';

-- 为包含"硕士", "研究生", "master"的故事添加"master"标签
INSERT OR IGNORE INTO story_tags (story_id, tag_id)
SELECT s.id, 'master'
FROM stories s
WHERE ((s.title LIKE '%硕士%' OR s.content LIKE '%硕士%') OR (s.title LIKE '%研究生%' OR s.content LIKE '%研究生%') OR (s.title LIKE '%master%' OR s.content LIKE '%master%'))
  AND s.status = 'approved';

-- 为包含"博士", "PhD", "博导"的故事添加"phd"标签
INSERT OR IGNORE INTO story_tags (story_id, tag_id)
SELECT s.id, 'phd'
FROM stories s
WHERE ((s.title LIKE '%博士%' OR s.content LIKE '%博士%') OR (s.title LIKE '%PhD%' OR s.content LIKE '%PhD%') OR (s.title LIKE '%博导%' OR s.content LIKE '%博导%'))
  AND s.status = 'approved';

-- 为包含"IT", "程序", "开发", "编程", "软件"的故事添加"it-industry"标签
INSERT OR IGNORE INTO story_tags (story_id, tag_id)
SELECT s.id, 'it-industry'
FROM stories s
WHERE ((s.title LIKE '%IT%' OR s.content LIKE '%IT%') OR (s.title LIKE '%程序%' OR s.content LIKE '%程序%') OR (s.title LIKE '%开发%' OR s.content LIKE '%开发%') OR (s.title LIKE '%编程%' OR s.content LIKE '%编程%') OR (s.title LIKE '%软件%' OR s.content LIKE '%软件%'))
  AND s.status = 'approved';

-- 为包含"金融", "银行", "投资", "证券"的故事添加"finance"标签
INSERT OR IGNORE INTO story_tags (story_id, tag_id)
SELECT s.id, 'finance'
FROM stories s
WHERE ((s.title LIKE '%金融%' OR s.content LIKE '%金融%') OR (s.title LIKE '%银行%' OR s.content LIKE '%银行%') OR (s.title LIKE '%投资%' OR s.content LIKE '%投资%') OR (s.title LIKE '%证券%' OR s.content LIKE '%证券%'))
  AND s.status = 'approved';

-- 为包含"创业", "初创", "startup"的故事添加"startup"标签
INSERT OR IGNORE INTO story_tags (story_id, tag_id)
SELECT s.id, 'startup'
FROM stories s
WHERE ((s.title LIKE '%创业%' OR s.content LIKE '%创业%') OR (s.title LIKE '%初创%' OR s.content LIKE '%初创%') OR (s.title LIKE '%startup%' OR s.content LIKE '%startup%'))
  AND s.status = 'approved';

-- 为包含"远程", "在家办公", "remote"的故事添加"remote-work"标签
INSERT OR IGNORE INTO story_tags (story_id, tag_id)
SELECT s.id, 'remote-work'
FROM stories s
WHERE ((s.title LIKE '%远程%' OR s.content LIKE '%远程%') OR (s.title LIKE '%在家办公%' OR s.content LIKE '%在家办公%') OR (s.title LIKE '%remote%' OR s.content LIKE '%remote%'))
  AND s.status = 'approved';

-- 为包含"自由职业", "自由工作者", "freelance"的故事添加"freelance"标签
INSERT OR IGNORE INTO story_tags (story_id, tag_id)
SELECT s.id, 'freelance'
FROM stories s
WHERE ((s.title LIKE '%自由职业%' OR s.content LIKE '%自由职业%') OR (s.title LIKE '%自由工作者%' OR s.content LIKE '%自由工作者%') OR (s.title LIKE '%freelance%' OR s.content LIKE '%freelance%'))
  AND s.status = 'approved';

-- 为包含"海外", "国外", "出国"的故事添加"overseas"标签
INSERT OR IGNORE INTO story_tags (story_id, tag_id)
SELECT s.id, 'overseas'
FROM stories s
WHERE ((s.title LIKE '%海外%' OR s.content LIKE '%海外%') OR (s.title LIKE '%国外%' OR s.content LIKE '%国外%') OR (s.title LIKE '%出国%' OR s.content LIKE '%出国%'))
  AND s.status = 'approved';

-- 为包含"成功", "成就", "胜利"的故事添加"success"标签
INSERT OR IGNORE INTO story_tags (story_id, tag_id)
SELECT s.id, 'success'
FROM stories s
WHERE ((s.title LIKE '%成功%' OR s.content LIKE '%成功%') OR (s.title LIKE '%成就%' OR s.content LIKE '%成就%') OR (s.title LIKE '%胜利%' OR s.content LIKE '%胜利%'))
  AND s.status = 'approved';

-- 为包含"挑战", "困难", "难题"的故事添加"challenge"标签
INSERT OR IGNORE INTO story_tags (story_id, tag_id)
SELECT s.id, 'challenge'
FROM stories s
WHERE ((s.title LIKE '%挑战%' OR s.content LIKE '%挑战%') OR (s.title LIKE '%困难%' OR s.content LIKE '%困难%') OR (s.title LIKE '%难题%' OR s.content LIKE '%难题%'))
  AND s.status = 'approved';

-- 为包含"建议", "经验", "分享"的故事添加"advice"标签
INSERT OR IGNORE INTO story_tags (story_id, tag_id)
SELECT s.id, 'advice'
FROM stories s
WHERE ((s.title LIKE '%建议%' OR s.content LIKE '%建议%') OR (s.title LIKE '%经验%' OR s.content LIKE '%经验%') OR (s.title LIKE '%分享%' OR s.content LIKE '%分享%'))
  AND s.status = 'approved';

-- 6. 重新计算标签统计
UPDATE tags SET count = (
  SELECT COUNT(*)
  FROM story_tags st
  WHERE st.tag_id = tags.id
);

-- 7. 创建索引
CREATE INDEX IF NOT EXISTS idx_story_tags_story_id ON story_tags(story_id);
CREATE INDEX IF NOT EXISTS idx_story_tags_tag_id ON story_tags(tag_id);
CREATE INDEX IF NOT EXISTS idx_tags_category ON tags(category);
CREATE INDEX IF NOT EXISTS idx_tags_count ON tags(count DESC);

-- 8. 验证数据
SELECT 
  '标签统计验证' as info,
  COUNT(*) as total_tags,
  SUM(count) as total_tag_usages
FROM tags;

SELECT 
  category,
  COUNT(*) as tag_count,
  SUM(count) as usage_count
FROM tags
GROUP BY category
ORDER BY usage_count DESC;
