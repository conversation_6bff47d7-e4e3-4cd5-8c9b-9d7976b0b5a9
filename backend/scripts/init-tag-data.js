#!/usr/bin/env node

/**
 * 标签数据初始化脚本
 * 解决前端常量与数据库标签不匹配的问题
 */

const fs = require('fs');
const path = require('path');

// 前端标签常量定义
const STORY_TAGS = [
  // 求职相关标签 (job, blue)
  { id: 'job-hunting', label: '求职故事', category: 'job', color: 'blue', count: 156 },
  { id: 'interview', label: '面试经验', category: 'job', color: 'blue', count: 134 },
  { id: 'resume', label: '简历技巧', category: 'job', color: 'blue', count: 43 },
  { id: 'job-search', label: '找工作', category: 'job', color: 'blue', count: 89 },
  { id: 'salary', label: '薪资谈判', category: 'job', color: 'blue', count: 76 },
  { id: 'offer', label: 'Offer选择', category: 'job', color: 'blue', count: 32 },

  // 学历相关标签 (education, green)
  { id: 'bachelor', label: '本科经验', category: 'education', color: 'green', count: 65 },
  { id: 'master', label: '硕士经验', category: 'education', color: 'green', count: 38 },
  { id: 'phd', label: '博士经验', category: 'education', color: 'green', count: 12 },
  { id: 'overseas-edu', label: '海外学历', category: 'education', color: 'green', count: 18 },
  { id: 'continuing-edu', label: '继续教育', category: 'education', color: 'green', count: 8 },
  { id: 'self-taught', label: '自学成才', category: 'education', color: 'green', count: 15 },

  // 行业相关标签 (industry, purple)
  { id: 'it-industry', label: 'IT行业', category: 'industry', color: 'purple', count: 54 },
  { id: 'finance', label: '金融行业', category: 'industry', color: 'purple', count: 25 },
  { id: 'education-industry', label: '教育行业', category: 'industry', color: 'purple', count: 16 },
  { id: 'healthcare', label: '医疗行业', category: 'industry', color: 'purple', count: 14 },
  { id: 'manufacturing', label: '制造业', category: 'industry', color: 'purple', count: 11 },
  { id: 'service', label: '服务业', category: 'industry', color: 'purple', count: 13 },

  // 经验相关标签 (experience, yellow)
  { id: 'career-change', label: '转行经历', category: 'experience', color: 'yellow', count: 87 },
  { id: 'work-life', label: '工作生活', category: 'experience', color: 'yellow', count: 32 },
  { id: 'advice', label: '建议分享', category: 'experience', color: 'yellow', count: 29 },
  { id: 'internship', label: '实习经历', category: 'experience', color: 'yellow', count: 98 },
  { id: 'overseas', label: '海外就业', category: 'experience', color: 'yellow', count: 22 },
  { id: 'startup', label: '创业经历', category: 'experience', color: 'yellow', count: 18 },
  { id: 'remote-work', label: '远程工作', category: 'experience', color: 'yellow', count: 15 },
  { id: 'freelance', label: '自由职业', category: 'experience', color: 'yellow', count: 8 },

  // 其他标签 (other, gray)
  { id: 'success', label: '成功故事', category: 'other', color: 'gray', count: 12 },
  { id: 'challenge', label: '挑战经历', category: 'other', color: 'gray', count: 10 },
  { id: 'inspiration', label: '励志故事', category: 'other', color: 'gray', count: 9 },
];

// 关键词匹配规则
const KEYWORD_RULES = [
  { keywords: ['求职', '找工作', '应聘'], tagId: 'job-hunting' },
  { keywords: ['面试', '面谈'], tagId: 'interview' },
  { keywords: ['实习', '实习生'], tagId: 'internship' },
  { keywords: ['转行', '换行业'], tagId: 'career-change' },
  { keywords: ['薪资', '工资', '薪水', '待遇'], tagId: 'salary' },
  { keywords: ['简历', 'CV', 'resume'], tagId: 'resume' },
  { keywords: ['本科', '学士'], tagId: 'bachelor' },
  { keywords: ['硕士', '研究生', 'master'], tagId: 'master' },
  { keywords: ['博士', 'PhD', '博导'], tagId: 'phd' },
  { keywords: ['IT', '程序', '开发', '编程', '软件'], tagId: 'it-industry' },
  { keywords: ['金融', '银行', '投资', '证券'], tagId: 'finance' },
  { keywords: ['创业', '初创', 'startup'], tagId: 'startup' },
  { keywords: ['远程', '在家办公', 'remote'], tagId: 'remote-work' },
  { keywords: ['自由职业', '自由工作者', 'freelance'], tagId: 'freelance' },
  { keywords: ['海外', '国外', '出国'], tagId: 'overseas' },
  { keywords: ['成功', '成就', '胜利'], tagId: 'success' },
  { keywords: ['挑战', '困难', '难题'], tagId: 'challenge' },
  { keywords: ['建议', '经验', '分享'], tagId: 'advice' },
];

/**
 * 生成SQL脚本
 */
function generateSQL() {
  let sql = `-- 标签数据初始化脚本 (自动生成)
-- 生成时间: ${new Date().toISOString()}

-- 1. 清理现有数据
DROP TABLE IF EXISTS story_tags;
DROP TABLE IF EXISTS tags;

-- 2. 创建标签表
CREATE TABLE IF NOT EXISTS tags (
  id TEXT PRIMARY KEY,
  name TEXT NOT NULL,
  color TEXT DEFAULT 'blue',
  priority INTEGER DEFAULT 0,
  category TEXT DEFAULT 'other',
  parent_id TEXT,
  count INTEGER DEFAULT 0,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  FOREIGN KEY (parent_id) REFERENCES tags(id)
);

-- 3. 创建故事标签关联表
CREATE TABLE IF NOT EXISTS story_tags (
  story_id INTEGER,
  tag_id TEXT,
  PRIMARY KEY (story_id, tag_id),
  FOREIGN KEY (story_id) REFERENCES stories(id) ON DELETE CASCADE,
  FOREIGN KEY (tag_id) REFERENCES tags(id) ON DELETE CASCADE
);

-- 4. 插入标签数据
`;

  // 生成标签插入语句
  STORY_TAGS.forEach((tag, index) => {
    const priority = 10 - (index % 10); // 根据顺序设置优先级
    sql += `INSERT OR REPLACE INTO tags (id, name, color, priority, category, count) VALUES ('${tag.id}', '${tag.label}', '${tag.color}', ${priority}, '${tag.category}', ${tag.count});\n`;
  });

  sql += `
-- 5. 基于关键词为现有故事添加标签
`;

  // 生成关键词匹配语句
  KEYWORD_RULES.forEach(rule => {
    const conditions = rule.keywords.map(keyword => 
      `(s.title LIKE '%${keyword}%' OR s.content LIKE '%${keyword}%')`
    ).join(' OR ');
    
    sql += `
-- 为包含"${rule.keywords.join('", "')}"的故事添加"${rule.tagId}"标签
INSERT OR IGNORE INTO story_tags (story_id, tag_id)
SELECT s.id, '${rule.tagId}'
FROM stories s
WHERE (${conditions})
  AND s.status = 'approved';
`;
  });

  sql += `
-- 6. 重新计算标签统计
UPDATE tags SET count = (
  SELECT COUNT(*)
  FROM story_tags st
  WHERE st.tag_id = tags.id
);

-- 7. 创建索引
CREATE INDEX IF NOT EXISTS idx_story_tags_story_id ON story_tags(story_id);
CREATE INDEX IF NOT EXISTS idx_story_tags_tag_id ON story_tags(tag_id);
CREATE INDEX IF NOT EXISTS idx_tags_category ON tags(category);
CREATE INDEX IF NOT EXISTS idx_tags_count ON tags(count DESC);

-- 8. 验证数据
SELECT 
  '标签统计验证' as info,
  COUNT(*) as total_tags,
  SUM(count) as total_tag_usages
FROM tags;

SELECT 
  category,
  COUNT(*) as tag_count,
  SUM(count) as usage_count
FROM tags
GROUP BY category
ORDER BY usage_count DESC;
`;

  return sql;
}

/**
 * 生成JSON配置文件
 */
function generateJSON() {
  return {
    version: '1.0',
    generatedAt: new Date().toISOString(),
    tags: STORY_TAGS,
    keywordRules: KEYWORD_RULES,
    stats: {
      totalTags: STORY_TAGS.length,
      categories: [...new Set(STORY_TAGS.map(t => t.category))],
      totalUsages: STORY_TAGS.reduce((sum, tag) => sum + tag.count, 0)
    }
  };
}

/**
 * 主函数
 */
function main() {
  try {
    // 确保输出目录存在
    const outputDir = path.join(__dirname, '../data');
    if (!fs.existsSync(outputDir)) {
      fs.mkdirSync(outputDir, { recursive: true });
    }

    // 生成SQL脚本
    const sql = generateSQL();
    const sqlPath = path.join(outputDir, 'init-tags.sql');
    fs.writeFileSync(sqlPath, sql, 'utf8');
    console.log(`✅ SQL脚本已生成: ${sqlPath}`);

    // 生成JSON配置
    const json = generateJSON();
    const jsonPath = path.join(outputDir, 'tags-config.json');
    fs.writeFileSync(jsonPath, JSON.stringify(json, null, 2), 'utf8');
    console.log(`✅ JSON配置已生成: ${jsonPath}`);

    // 输出统计信息
    console.log('\n📊 标签统计:');
    console.log(`- 总标签数: ${STORY_TAGS.length}`);
    console.log(`- 分类数: ${[...new Set(STORY_TAGS.map(t => t.category))].length}`);
    console.log(`- 关键词规则: ${KEYWORD_RULES.length}`);
    console.log(`- 总使用次数: ${STORY_TAGS.reduce((sum, tag) => sum + tag.count, 0)}`);

    console.log('\n🎯 下一步操作:');
    console.log('1. 执行SQL脚本初始化数据库');
    console.log('2. 重启API服务器');
    console.log('3. 测试标签筛选功能');

  } catch (error) {
    console.error('❌ 生成失败:', error);
    process.exit(1);
  }
}

// 如果直接运行此脚本
if (require.main === module) {
  main();
}

module.exports = {
  generateSQL,
  generateJSON,
  STORY_TAGS,
  KEYWORD_RULES
};
