import React from 'react';
import SmartLayout from '@/components/layouts/SmartLayout';
import AnalyticsPanel from '@/components/admin/AnalyticsPanel';
import AdvancedDataVisualization from '@/components/admin/AdvancedDataVisualization';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';

const DataAnalysisPage: React.FC = () => {
  return (
    <SmartLayout allowedRoles={['admin', 'superadmin']}>
      <div className="container mx-auto py-6">
        <h1 className="text-2xl font-bold mb-6">数据分析</h1>
        <p className="text-gray-500 mb-6">分析问卷数据，生成统计图表和导出数据</p>

        <Tabs defaultValue="basic" className="space-y-6">
          <TabsList>
            <TabsTrigger value="basic">基础分析</TabsTrigger>
            <TabsTrigger value="advanced">高级可视化</TabsTrigger>
          </TabsList>

          <TabsContent value="basic">
            <AnalyticsPanel />
          </TabsContent>

          <TabsContent value="advanced">
            <AdvancedDataVisualization />
          </TabsContent>
        </Tabs>
      </div>
    </SmartLayout>
  );
};

export default DataAnalysisPage;
