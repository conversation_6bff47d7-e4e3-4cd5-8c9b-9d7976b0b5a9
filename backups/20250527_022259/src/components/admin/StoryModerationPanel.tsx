import { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { <PERSON>, CardContent, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { useToast } from '@/components/ui/use-toast';
import LoadingSpinner from '@/components/ui/loading-spinner';
import ErrorDisplay from '@/components/ui/error-display';

// 导入数据服务 - 使用新的API服务
import { getPendingStoriesNew, moderateStoryNew } from '@/services/dataService';

interface Story {
  id: number;
  sequenceNumber: string; // 添加顺序编号字段
  title: string;
  content: string;
  isAnonymous: boolean;
  createdAt: string;
  status: 'pending' | 'approved' | 'rejected';
}

export default function StoryModerationPanel() {
  const { toast } = useToast();
  const [stories, setStories] = useState<Story[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // 获取待审核的故事
  const fetchPendingStories = async () => {
    try {
      setIsLoading(true);
      setError(null);

      const data = await getPendingStoriesNew();

      if (data.success) {
        setStories(data.stories);
      } else {
        setError(data.error || '获取故事失败');
      }
    } catch (error) {
      console.error('获取故事错误:', error);
      setError('服务器错误，请稍后再试');
    } finally {
      setIsLoading(false);
    }
  };

  // 组件加载时获取故事
  useEffect(() => {
    fetchPendingStories();
  }, []);

  // 处理故事审核
  const handleModerateStory = async (storyId: number, status: 'approved' | 'rejected') => {
    try {
      const data = await moderateStoryNew(storyId, status, {
        reviewNotes: `故事${status === 'approved' ? '通过' : '拒绝'}审核`
      });

      if (data.success) {
        // 更新本地故事列表
        setStories(stories.filter(story => story.id !== storyId));

        // 显示成功消息
        toast({
          title: '审核成功',
          description: `故事已${status === 'approved' ? '通过' : '拒绝'}`,
        });
      } else {
        toast({
          title: '审核失败',
          description: data.error || '操作失败，请重试',
          variant: 'destructive',
        });
      }
    } catch (error) {
      console.error('审核故事错误:', error);
      toast({
        title: '审核失败',
        description: '服务器错误，请稍后再试',
        variant: 'destructive',
      });
    }
  };

  if (isLoading) {
    return (
      <div className="flex justify-center items-center py-12">
        <LoadingSpinner size="lg" text="加载待审核故事中..." />
      </div>
    );
  }

  if (error) {
    return (
      <ErrorDisplay
        title="加载故事失败"
        message={error}
        onRetry={fetchPendingStories}
      />
    );
  }

  if (stories.length === 0) {
    return (
      <div className="text-center p-8">
        <p className="text-gray-500">没有待审核的故事</p>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <h3 className="text-lg font-medium">待审核故事 ({stories.length})</h3>
        <Button variant="outline" onClick={fetchPendingStories}>
          刷新
        </Button>
      </div>

      <div className="grid gap-6">
        {stories.map((story) => (
          <Card key={story.id}>
            <CardHeader>
              <div className="flex justify-between items-start">
                <div>
                  <Badge variant="outline" className="mb-2">
                    编号: {story.sequenceNumber}
                  </Badge>
                  <CardTitle className="text-xl">{story.title}</CardTitle>
                </div>
                <Badge variant={story.isAnonymous ? "secondary" : "outline"}>
                  {story.isAnonymous ? '匿名' : '已验证'}
                </Badge>
              </div>
              <p className="text-sm text-gray-500">
                提交时间: {new Date(story.createdAt).toLocaleString('zh-CN')}
              </p>
            </CardHeader>
            <CardContent>
              <div className="whitespace-pre-wrap">{story.content}</div>
            </CardContent>
            <CardFooter className="flex justify-end space-x-2">
              <Button
                variant="destructive"
                onClick={() => handleModerateStory(story.id, 'rejected')}
              >
                拒绝
              </Button>
              <Button
                variant="default"
                onClick={() => handleModerateStory(story.id, 'approved')}
              >
                通过
              </Button>
            </CardFooter>
          </Card>
        ))}
      </div>
    </div>
  );
}
