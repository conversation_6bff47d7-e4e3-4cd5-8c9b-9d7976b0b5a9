import { useState, useEffect } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle
} from '@/components/ui/dialog';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue
} from '@/components/ui/select';
import { Checkbox } from '@/components/ui/checkbox';
import { Label } from '@/components/ui/label';
import { DatePicker } from '@/components/ui/date-picker';
import {
  Filter,
  X,
  Save,
  Plus,
  Trash2,
  Search,
  Tag,
  Calendar,
  BookOpen,
  Briefcase,
  MapPin,
  Star,
  Clock,
  FileText
} from 'lucide-react';
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '@/components/ui/popover';
import { Badge } from '@/components/ui/badge';
import { Slider } from '@/components/ui/slider';
import { Switch } from '@/components/ui/switch';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Separator } from '@/components/ui/separator';
import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from '@/components/ui/accordion';
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';

// 定义筛选条件类型
export interface AdvancedFilterOptions {
  // 基本筛选
  search: string;
  searchFields: string[];
  educationLevel: string[];
  employmentStatus: string[];
  region: string[];
  industry: string[];
  isAnonymous: boolean | null;
  hasTag: string[];

  // 数值范围筛选
  graduationYearRange: {
    min: number | null;
    max: number | null;
  };
  salaryRange: {
    min: number | null;
    max: number | null;
  };
  jobSatisfactionRange: {
    min: number | null;
    max: number | null;
  };

  // 日期范围筛选
  dateRange: {
    startDate: Date | null;
    endDate: Date | null;
  };

  // 逻辑组合
  logicOperator: 'AND' | 'OR';

  // 排序
  sortBy: string;
  sortOrder: 'asc' | 'desc';
}

// 保存的筛选条件类型
export interface SavedFilter {
  id: string;
  name: string;
  description?: string;
  filters: AdvancedFilterOptions;
  createdAt: string;
  isDefault?: boolean;
}

// 组件属性类型
interface AdvancedFilterDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  filters: AdvancedFilterOptions;
  onApplyFilters: (filters: AdvancedFilterOptions) => void;
  onResetFilters: () => void;
  savedFilters: SavedFilter[];
  onSaveFilter: (name: string, description: string, filters: AdvancedFilterOptions) => void;
  onDeleteSavedFilter?: (id: string) => void;
  onSetDefaultFilter?: (id: string) => void;
  availableTags?: string[];
}

// 默认筛选条件
const defaultFilters: AdvancedFilterOptions = {
  search: '',
  searchFields: [],
  educationLevel: [],
  employmentStatus: [],
  region: [],
  industry: [],
  isAnonymous: null,
  hasTag: [],
  graduationYearRange: {
    min: null,
    max: null,
  },
  salaryRange: {
    min: null,
    max: null,
  },
  jobSatisfactionRange: {
    min: null,
    max: null,
  },
  dateRange: {
    startDate: null,
    endDate: null,
  },
  logicOperator: 'AND',
  sortBy: 'createdAt',
  sortOrder: 'desc',
};

export default function AdvancedFilterDialog({
  open,
  onOpenChange,
  filters,
  onApplyFilters,
  onResetFilters,
  savedFilters,
  onSaveFilter,
  onDeleteSavedFilter,
  onSetDefaultFilter,
  availableTags = [],
}: AdvancedFilterDialogProps) {
  // 本地筛选状态
  const [localFilters, setLocalFilters] = useState<AdvancedFilterOptions>({...filters});
  const [activeTab, setActiveTab] = useState('basic');
  const [saveFilterName, setSaveFilterName] = useState('');
  const [saveFilterDescription, setSaveFilterDescription] = useState('');
  const [isSavePopoverOpen, setIsSavePopoverOpen] = useState(false);
  const [activeFilterSummary, setActiveFilterSummary] = useState<string[]>([]);

  // 当组件打开或filters变化时，更新本地筛选状态
  useEffect(() => {
    if (open) {
      setLocalFilters({...filters});
      updateFilterSummary({...filters});
    }
  }, [open, filters]);

  // 更新筛选条件摘要
  const updateFilterSummary = (currentFilters: AdvancedFilterOptions) => {
    const summary: string[] = [];

    if (currentFilters.search) {
      summary.push(`搜索: "${currentFilters.search}"`);
    }

    if (currentFilters.educationLevel.length > 0) {
      summary.push(`学历: ${currentFilters.educationLevel.join(', ')}`);
    }

    if (currentFilters.employmentStatus.length > 0) {
      summary.push(`就业状态: ${currentFilters.employmentStatus.join(', ')}`);
    }

    if (currentFilters.region.length > 0) {
      summary.push(`地区: ${currentFilters.region.join(', ')}`);
    }

    if (currentFilters.industry.length > 0) {
      summary.push(`行业: ${currentFilters.industry.join(', ')}`);
    }

    if (currentFilters.isAnonymous !== null) {
      summary.push(`提交类型: ${currentFilters.isAnonymous ? '匿名' : '已验证'}`);
    }

    if (currentFilters.hasTag.length > 0) {
      summary.push(`标签: ${currentFilters.hasTag.join(', ')}`);
    }

    if (currentFilters.graduationYearRange.min !== null || currentFilters.graduationYearRange.max !== null) {
      const minYear = currentFilters.graduationYearRange.min !== null ? currentFilters.graduationYearRange.min : '不限';
      const maxYear = currentFilters.graduationYearRange.max !== null ? currentFilters.graduationYearRange.max : '不限';
      summary.push(`毕业年份: ${minYear} - ${maxYear}`);
    }

    if (currentFilters.salaryRange.min !== null || currentFilters.salaryRange.max !== null) {
      const minSalary = currentFilters.salaryRange.min !== null ? `${currentFilters.salaryRange.min}元` : '不限';
      const maxSalary = currentFilters.salaryRange.max !== null ? `${currentFilters.salaryRange.max}元` : '不限';
      summary.push(`薪资范围: ${minSalary} - ${maxSalary}`);
    }

    if (currentFilters.dateRange.startDate || currentFilters.dateRange.endDate) {
      const startDate = currentFilters.dateRange.startDate
        ? currentFilters.dateRange.startDate.toLocaleDateString('zh-CN')
        : '不限';
      const endDate = currentFilters.dateRange.endDate
        ? currentFilters.dateRange.endDate.toLocaleDateString('zh-CN')
        : '不限';
      summary.push(`提交日期: ${startDate} - ${endDate}`);
    }

    if (currentFilters.sortBy) {
      const sortFieldMap: Record<string, string> = {
        'createdAt': '提交时间',
        'graduationYear': '毕业年份',
        'salary': '薪资',
        'jobSatisfaction': '工作满意度',
      };

      const sortField = sortFieldMap[currentFilters.sortBy] || currentFilters.sortBy;
      const sortDirection = currentFilters.sortOrder === 'asc' ? '升序' : '降序';
      summary.push(`排序: ${sortField} ${sortDirection}`);
    }

    setActiveFilterSummary(summary);
  };

  // 处理筛选条件变化
  const handleFilterChange = <K extends keyof AdvancedFilterOptions>(
    key: K,
    value: AdvancedFilterOptions[K]
  ) => {
    const updatedFilters = {
      ...localFilters,
      [key]: value,
    };
    setLocalFilters(updatedFilters);
    updateFilterSummary(updatedFilters);
  };

  // 处理数组类型筛选条件的变化
  const handleArrayFilterChange = <K extends 'educationLevel' | 'employmentStatus' | 'region' | 'industry' | 'hasTag' | 'searchFields'>(
    key: K,
    value: string,
    checked: boolean
  ) => {
    const currentValues = [...localFilters[key]];

    if (checked) {
      if (!currentValues.includes(value)) {
        currentValues.push(value);
      }
    } else {
      const index = currentValues.indexOf(value);
      if (index !== -1) {
        currentValues.splice(index, 1);
      }
    }

    handleFilterChange(key, currentValues);
  };

  // 处理范围类型筛选条件的变化
  const handleRangeFilterChange = <K extends 'graduationYearRange' | 'salaryRange' | 'jobSatisfactionRange'>(
    key: K,
    rangeKey: 'min' | 'max',
    value: number | null
  ) => {
    const updatedRange = {
      ...localFilters[key],
      [rangeKey]: value,
    };

    handleFilterChange(key, updatedRange);
  };

  // 处理日期范围变化
  const handleDateRangeChange = (key: 'startDate' | 'endDate', value: Date | null) => {
    const updatedDateRange = {
      ...localFilters.dateRange,
      [key]: value,
    };

    handleFilterChange('dateRange', updatedDateRange);
  };

  // 处理应用筛选
  const handleApplyFilters = () => {
    onApplyFilters(localFilters);
    onOpenChange(false);
  };

  // 处理重置筛选
  const handleResetFilters = () => {
    setLocalFilters({...defaultFilters});
    updateFilterSummary({...defaultFilters});
    onResetFilters();
  };

  // 处理保存筛选条件
  const handleSaveFilter = () => {
    if (saveFilterName.trim()) {
      onSaveFilter(saveFilterName.trim(), saveFilterDescription.trim(), localFilters);
      setSaveFilterName('');
      setSaveFilterDescription('');
      setIsSavePopoverOpen(false);
    }
  };

  // 处理加载已保存的筛选条件
  const handleLoadSavedFilter = (savedFilter: SavedFilter) => {
    setLocalFilters({...savedFilter.filters});
    updateFilterSummary({...savedFilter.filters});
  };

  // 学历选项
  const educationLevels = [
    { value: '高中/中专', label: '高中/中专' },
    { value: '大专', label: '大专' },
    { value: '本科', label: '本科' },
    { value: '硕士', label: '硕士' },
    { value: '博士', label: '博士' },
  ];

  // 就业状态选项
  const employmentStatuses = [
    { value: '已就业', label: '已就业' },
    { value: '待业中', label: '待业中' },
    { value: '继续深造', label: '继续深造' },
    { value: '其他', label: '其他' },
  ];

  // 行业选项
  const industries = [
    { value: 'IT/互联网', label: 'IT/互联网' },
    { value: '金融', label: '金融' },
    { value: '教育', label: '教育' },
    { value: '医疗', label: '医疗' },
    { value: '制造业', label: '制造业' },
    { value: '服务业', label: '服务业' },
    { value: '政府/事业单位', label: '政府/事业单位' },
    { value: '其他', label: '其他' },
  ];

  // 地区选项
  const regions = [
    { value: '北京', label: '北京' },
    { value: '上海', label: '上海' },
    { value: '广州', label: '广州' },
    { value: '深圳', label: '深圳' },
    { value: '杭州', label: '杭州' },
    { value: '南京', label: '南京' },
    { value: '武汉', label: '武汉' },
    { value: '成都', label: '成都' },
    { value: '重庆', label: '重庆' },
    { value: '西安', label: '西安' },
    { value: '天津', label: '天津' },
    { value: '苏州', label: '苏州' },
    { value: '郑州', label: '郑州' },
    { value: '长沙', label: '长沙' },
    { value: '东莞', label: '东莞' },
    { value: '沈阳', label: '沈阳' },
    { value: '青岛', label: '青岛' },
    { value: '合肥', label: '合肥' },
    { value: '佛山', label: '佛山' },
    { value: '其他', label: '其他' },
  ];

  // 搜索字段选项
  const searchFields = [
    { value: 'all', label: '全部字段' },
    { value: 'major', label: '专业' },
    { value: 'position', label: '职位' },
    { value: 'challenges', label: '就业挑战' },
    { value: 'suggestions', label: '建议' },
  ];

  // 排序字段选项
  const sortFields = [
    { value: 'createdAt', label: '提交时间' },
    { value: 'graduationYear', label: '毕业年份' },
    { value: 'salary', label: '薪资' },
    { value: 'jobSatisfaction', label: '工作满意度' },
  ];

  // 排序方向选项
  const sortDirections = [
    { value: 'asc', label: '升序' },
    { value: 'desc', label: '降序' },
  ];

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-4xl max-h-[90vh]">
        <DialogHeader>
          <DialogTitle>高级筛选</DialogTitle>
          <DialogDescription>
            设置筛选条件以查找特定的问卷回复数据
          </DialogDescription>
        </DialogHeader>

        <div className="flex flex-col md:flex-row gap-4">
          {/* 左侧筛选选项 */}
          <div className="md:w-3/4">
            <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
              <TabsList className="grid grid-cols-4">
                <TabsTrigger value="basic">基本筛选</TabsTrigger>
                <TabsTrigger value="advanced">高级筛选</TabsTrigger>
                <TabsTrigger value="range">范围筛选</TabsTrigger>
                <TabsTrigger value="sort">排序与逻辑</TabsTrigger>
              </TabsList>

              <ScrollArea className="h-[calc(90vh-250px)]">
                <TabsContent value="basic" className="space-y-4 p-1">
                  {/* 关键词搜索 */}
                  <div className="space-y-2">
                    <Label htmlFor="search">关键词搜索</Label>
                    <div className="flex gap-2">
                      <Input
                        id="search"
                        placeholder="搜索关键词..."
                        value={localFilters.search}
                        onChange={(e) => handleFilterChange('search', e.target.value)}
                        className="flex-1"
                      />
                      <Button
                        variant="outline"
                        size="icon"
                        onClick={() => handleFilterChange('search', '')}
                        disabled={!localFilters.search}
                      >
                        <X className="h-4 w-4" />
                      </Button>
                    </div>

                    <div className="pt-2">
                      <Label className="text-xs mb-1 block">搜索范围</Label>
                      <div className="grid grid-cols-2 gap-2">
                        {searchFields.map((field) => (
                          <div key={field.value} className="flex items-center space-x-2">
                            <Checkbox
                              id={`search-field-${field.value}`}
                              checked={
                                field.value === 'all'
                                  ? localFilters.searchFields.length === 0
                                  : localFilters.searchFields.includes(field.value)
                              }
                              onCheckedChange={(checked) => {
                                if (field.value === 'all') {
                                  handleFilterChange('searchFields', []);
                                } else {
                                  handleArrayFilterChange('searchFields', field.value, checked === true);
                                }
                              }}
                            />
                            <label
                              htmlFor={`search-field-${field.value}`}
                              className="text-sm font-normal leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
                            >
                              {field.label}
                            </label>
                          </div>
                        ))}
                      </div>
                    </div>
                  </div>

                  <Separator />

                  {/* 学历层次 */}
                  <div className="space-y-2">
                    <Label>学历层次</Label>
                    <div className="grid grid-cols-2 gap-2">
                      {educationLevels.map((level) => (
                        <div key={level.value} className="flex items-center space-x-2">
                          <Checkbox
                            id={`education-${level.value}`}
                            checked={localFilters.educationLevel.includes(level.value)}
                            onCheckedChange={(checked) =>
                              handleArrayFilterChange('educationLevel', level.value, checked === true)
                            }
                          />
                          <label
                            htmlFor={`education-${level.value}`}
                            className="text-sm font-normal leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
                          >
                            {level.label}
                          </label>
                        </div>
                      ))}
                    </div>
                  </div>

                  {/* 就业状态 */}
                  <div className="space-y-2">
                    <Label>就业状态</Label>
                    <div className="grid grid-cols-2 gap-2">
                      {employmentStatuses.map((status) => (
                        <div key={status.value} className="flex items-center space-x-2">
                          <Checkbox
                            id={`employment-${status.value}`}
                            checked={localFilters.employmentStatus.includes(status.value)}
                            onCheckedChange={(checked) =>
                              handleArrayFilterChange('employmentStatus', status.value, checked === true)
                            }
                          />
                          <label
                            htmlFor={`employment-${status.value}`}
                            className="text-sm font-normal leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
                          >
                            {status.label}
                          </label>
                        </div>
                      ))}
                    </div>
                  </div>

                  {/* 提交类型 */}
                  <div className="space-y-2">
                    <Label>提交类型</Label>
                    <div className="flex gap-4">
                      <div className="flex items-center space-x-2">
                        <Checkbox
                          id="anonymous-all"
                          checked={localFilters.isAnonymous === null}
                          onCheckedChange={() => handleFilterChange('isAnonymous', null)}
                        />
                        <label
                          htmlFor="anonymous-all"
                          className="text-sm font-normal leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
                        >
                          全部
                        </label>
                      </div>
                      <div className="flex items-center space-x-2">
                        <Checkbox
                          id="anonymous-true"
                          checked={localFilters.isAnonymous === true}
                          onCheckedChange={() => handleFilterChange('isAnonymous', true)}
                        />
                        <label
                          htmlFor="anonymous-true"
                          className="text-sm font-normal leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
                        >
                          匿名
                        </label>
                      </div>
                      <div className="flex items-center space-x-2">
                        <Checkbox
                          id="anonymous-false"
                          checked={localFilters.isAnonymous === false}
                          onCheckedChange={() => handleFilterChange('isAnonymous', false)}
                        />
                        <label
                          htmlFor="anonymous-false"
                          className="text-sm font-normal leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
                        >
                          已验证
                        </label>
                      </div>
                    </div>
                  </div>
                </TabsContent>

                <TabsContent value="advanced" className="space-y-4 p-1">
                  {/* 地区筛选 */}
                  <div className="space-y-2">
                    <Label>地区</Label>
                    <div className="grid grid-cols-3 gap-2">
                      {regions.map((region) => (
                        <div key={region.value} className="flex items-center space-x-2">
                          <Checkbox
                            id={`region-${region.value}`}
                            checked={localFilters.region.includes(region.value)}
                            onCheckedChange={(checked) =>
                              handleArrayFilterChange('region', region.value, checked === true)
                            }
                          />
                          <label
                            htmlFor={`region-${region.value}`}
                            className="text-sm font-normal leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
                          >
                            {region.label}
                          </label>
                        </div>
                      ))}
                    </div>
                  </div>

                  {/* 行业筛选 */}
                  <div className="space-y-2">
                    <Label>行业</Label>
                    <div className="grid grid-cols-2 gap-2">
                      {industries.map((industry) => (
                        <div key={industry.value} className="flex items-center space-x-2">
                          <Checkbox
                            id={`industry-${industry.value}`}
                            checked={localFilters.industry.includes(industry.value)}
                            onCheckedChange={(checked) =>
                              handleArrayFilterChange('industry', industry.value, checked === true)
                            }
                          />
                          <label
                            htmlFor={`industry-${industry.value}`}
                            className="text-sm font-normal leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
                          >
                            {industry.label}
                          </label>
                        </div>
                      ))}
                    </div>
                  </div>

                  <Separator />

                  {/* 标签筛选 */}
                  <div className="space-y-2">
                    <Label>标签筛选</Label>
                    {availableTags.length > 0 ? (
                      <div className="flex flex-wrap gap-2">
                        {availableTags.map((tag) => (
                          <Badge
                            key={tag}
                            variant={localFilters.hasTag.includes(tag) ? "default" : "outline"}
                            className="cursor-pointer"
                            onClick={() => {
                              handleArrayFilterChange(
                                'hasTag',
                                tag,
                                !localFilters.hasTag.includes(tag)
                              );
                            }}
                          >
                            <Tag className={`mr-1 h-3 w-3 ${localFilters.hasTag.includes(tag) ? 'text-primary-foreground' : ''}`} />
                            {tag}
                          </Badge>
                        ))}
                      </div>
                    ) : (
                      <p className="text-sm text-muted-foreground">暂无可用标签</p>
                    )}
                  </div>

                  {/* 提交日期范围 */}
                  <div className="space-y-2">
                    <Label>提交日期范围</Label>
                    <div className="grid grid-cols-2 gap-4">
                      <div>
                        <Label htmlFor="startDate" className="text-xs">开始日期</Label>
                        <DatePicker
                          selected={localFilters.dateRange.startDate}
                          onSelect={(date) => handleDateRangeChange('startDate', date)}
                          placeholder="开始日期"
                        />
                      </div>
                      <div>
                        <Label htmlFor="endDate" className="text-xs">结束日期</Label>
                        <DatePicker
                          selected={localFilters.dateRange.endDate}
                          onSelect={(date) => handleDateRangeChange('endDate', date)}
                          placeholder="结束日期"
                        />
                      </div>
                    </div>
                  </div>
                </TabsContent>

                <TabsContent value="range" className="space-y-4 p-1">
                  {/* 毕业年份范围 */}
                  <div className="space-y-2">
                    <Label>毕业年份范围</Label>
                    <div className="grid grid-cols-2 gap-4">
                      <div className="space-y-1">
                        <Label htmlFor="graduationYearMin" className="text-xs">最小值</Label>
                        <Input
                          id="graduationYearMin"
                          type="number"
                          placeholder="最小年份"
                          value={localFilters.graduationYearRange.min || ''}
                          onChange={(e) => {
                            const value = e.target.value ? parseInt(e.target.value) : null;
                            handleRangeFilterChange('graduationYearRange', 'min', value);
                          }}
                        />
                      </div>
                      <div className="space-y-1">
                        <Label htmlFor="graduationYearMax" className="text-xs">最大值</Label>
                        <Input
                          id="graduationYearMax"
                          type="number"
                          placeholder="最大年份"
                          value={localFilters.graduationYearRange.max || ''}
                          onChange={(e) => {
                            const value = e.target.value ? parseInt(e.target.value) : null;
                            handleRangeFilterChange('graduationYearRange', 'max', value);
                          }}
                        />
                      </div>
                    </div>
                  </div>

                  {/* 薪资范围 */}
                  <div className="space-y-2">
                    <Label>薪资范围 (元/月)</Label>
                    <div className="grid grid-cols-2 gap-4">
                      <div className="space-y-1">
                        <Label htmlFor="salaryMin" className="text-xs">最小值</Label>
                        <Input
                          id="salaryMin"
                          type="number"
                          placeholder="最小薪资"
                          value={localFilters.salaryRange.min || ''}
                          onChange={(e) => {
                            const value = e.target.value ? parseInt(e.target.value) : null;
                            handleRangeFilterChange('salaryRange', 'min', value);
                          }}
                        />
                      </div>
                      <div className="space-y-1">
                        <Label htmlFor="salaryMax" className="text-xs">最大值</Label>
                        <Input
                          id="salaryMax"
                          type="number"
                          placeholder="最大薪资"
                          value={localFilters.salaryRange.max || ''}
                          onChange={(e) => {
                            const value = e.target.value ? parseInt(e.target.value) : null;
                            handleRangeFilterChange('salaryRange', 'max', value);
                          }}
                        />
                      </div>
                    </div>
                  </div>

                  {/* 工作满意度范围 */}
                  <div className="space-y-2">
                    <Label>工作满意度范围 (1-5分)</Label>
                    <div className="grid grid-cols-2 gap-4">
                      <div className="space-y-1">
                        <Label htmlFor="jobSatisfactionMin" className="text-xs">最小值</Label>
                        <Input
                          id="jobSatisfactionMin"
                          type="number"
                          min="1"
                          max="5"
                          step="0.1"
                          placeholder="最小满意度"
                          value={localFilters.jobSatisfactionRange.min || ''}
                          onChange={(e) => {
                            const value = e.target.value ? parseFloat(e.target.value) : null;
                            handleRangeFilterChange('jobSatisfactionRange', 'min', value);
                          }}
                        />
                      </div>
                      <div className="space-y-1">
                        <Label htmlFor="jobSatisfactionMax" className="text-xs">最大值</Label>
                        <Input
                          id="jobSatisfactionMax"
                          type="number"
                          min="1"
                          max="5"
                          step="0.1"
                          placeholder="最大满意度"
                          value={localFilters.jobSatisfactionRange.max || ''}
                          onChange={(e) => {
                            const value = e.target.value ? parseFloat(e.target.value) : null;
                            handleRangeFilterChange('jobSatisfactionRange', 'max', value);
                          }}
                        />
                      </div>
                    </div>
                  </div>
                </TabsContent>

                <TabsContent value="sort" className="space-y-4 p-1">
                  {/* 排序设置 */}
                  <div className="space-y-2">
                    <Label>排序设置</Label>
                    <div className="grid grid-cols-2 gap-4">
                      <div className="space-y-1">
                        <Label htmlFor="sortBy" className="text-xs">排序字段</Label>
                        <Select
                          value={localFilters.sortBy}
                          onValueChange={(value) => handleFilterChange('sortBy', value)}
                        >
                          <SelectTrigger id="sortBy">
                            <SelectValue placeholder="选择排序字段" />
                          </SelectTrigger>
                          <SelectContent>
                            {sortFields.map((field) => (
                              <SelectItem key={field.value} value={field.value}>
                                {field.label}
                              </SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                      </div>
                      <div className="space-y-1">
                        <Label htmlFor="sortOrder" className="text-xs">排序方向</Label>
                        <Select
                          value={localFilters.sortOrder}
                          onValueChange={(value) => handleFilterChange('sortOrder', value as 'asc' | 'desc')}
                        >
                          <SelectTrigger id="sortOrder">
                            <SelectValue placeholder="选择排序方向" />
                          </SelectTrigger>
                          <SelectContent>
                            {sortDirections.map((direction) => (
                              <SelectItem key={direction.value} value={direction.value}>
                                {direction.label}
                              </SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                      </div>
                    </div>
                  </div>

                  <Separator />

                  {/* 逻辑运算符 */}
                  <div className="space-y-2">
                    <Label>筛选条件逻辑</Label>
                    <div className="flex gap-4">
                      <div className="flex items-center space-x-2">
                        <Checkbox
                          id="logic-and"
                          checked={localFilters.logicOperator === 'AND'}
                          onCheckedChange={() => handleFilterChange('logicOperator', 'AND')}
                        />
                        <label
                          htmlFor="logic-and"
                          className="text-sm font-normal leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
                        >
                          满足所有条件 (AND)
                        </label>
                      </div>
                      <div className="flex items-center space-x-2">
                        <Checkbox
                          id="logic-or"
                          checked={localFilters.logicOperator === 'OR'}
                          onCheckedChange={() => handleFilterChange('logicOperator', 'OR')}
                        />
                        <label
                          htmlFor="logic-or"
                          className="text-sm font-normal leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
                        >
                          满足任一条件 (OR)
                        </label>
                      </div>
                    </div>
                    <p className="text-xs text-muted-foreground mt-1">
                      选择"满足所有条件"时，只有同时满足所有筛选条件的数据才会显示。
                      选择"满足任一条件"时，满足任何一个筛选条件的数据都会显示。
                    </p>
                  </div>
                </TabsContent>
              </ScrollArea>
            </Tabs>
          </div>

          {/* 右侧筛选摘要和已保存的筛选条件 */}
          <div className="md:w-1/4 space-y-4">
            <Card>
              <CardHeader className="py-3">
                <CardTitle className="text-sm">当前筛选条件</CardTitle>
              </CardHeader>
              <CardContent className="py-0">
                {activeFilterSummary.length > 0 ? (
                  <ul className="text-xs space-y-1">
                    {activeFilterSummary.map((item, index) => (
                      <li key={index} className="flex items-start">
                        <span className="mr-1">•</span>
                        <span>{item}</span>
                      </li>
                    ))}
                  </ul>
                ) : (
                  <p className="text-xs text-muted-foreground">未设置筛选条件</p>
                )}
              </CardContent>
              <CardFooter className="pt-3 pb-1">
                <Button
                  variant="ghost"
                  size="sm"
                  className="text-xs h-7 px-2 w-full"
                  onClick={handleResetFilters}
                >
                  <X className="h-3 w-3 mr-1" />
                  清除所有筛选条件
                </Button>
              </CardFooter>
            </Card>

            {savedFilters.length > 0 && (
              <Card>
                <CardHeader className="py-3">
                  <CardTitle className="text-sm">已保存的筛选条件</CardTitle>
                </CardHeader>
                <CardContent className="py-0 max-h-[200px] overflow-y-auto">
                  <ul className="space-y-2">
                    {savedFilters.map((filter) => (
                      <li key={filter.id} className="text-xs">
                        <Button
                          variant="outline"
                          size="sm"
                          className="w-full justify-start h-auto py-1 px-2"
                          onClick={() => handleLoadSavedFilter(filter)}
                        >
                          <div className="flex flex-col items-start">
                            <span className="font-medium">{filter.name}</span>
                            {filter.description && (
                              <span className="text-muted-foreground text-[10px] truncate max-w-full">
                                {filter.description}
                              </span>
                            )}
                          </div>
                        </Button>
                      </li>
                    ))}
                  </ul>
                </CardContent>
              </Card>
            )}
          </div>
        </div>

        <DialogFooter className="flex justify-between">
          <div className="flex gap-2">
            <Button
              type="button"
              variant="destructive"
              onClick={handleResetFilters}
            >
              <X className="mr-2 h-4 w-4" />
              重置
            </Button>

            <Popover open={isSavePopoverOpen} onOpenChange={setIsSavePopoverOpen}>
              <PopoverTrigger asChild>
                <Button variant="outline">
                  <Save className="mr-2 h-4 w-4" />
                  保存
                </Button>
              </PopoverTrigger>
              <PopoverContent className="w-80">
                <div className="grid gap-4">
                  <div className="space-y-2">
                    <h4 className="font-medium leading-none">保存筛选条件</h4>
                    <p className="text-sm text-muted-foreground">
                      为当前筛选条件设置一个名称以便日后使用
                    </p>
                  </div>
                  <div className="grid gap-2">
                    <div className="grid gap-2">
                      <Label htmlFor="filterName">名称</Label>
                      <Input
                        id="filterName"
                        value={saveFilterName}
                        onChange={(e) => setSaveFilterName(e.target.value)}
                      />
                    </div>
                    <div className="grid gap-2">
                      <Label htmlFor="filterDescription">描述 (可选)</Label>
                      <Textarea
                        id="filterDescription"
                        value={saveFilterDescription}
                        onChange={(e) => setSaveFilterDescription(e.target.value)}
                        rows={2}
                      />
                    </div>
                  </div>
                  <Button
                    onClick={handleSaveFilter}
                    disabled={!saveFilterName.trim()}
                  >
                    保存筛选条件
                  </Button>
                </div>
              </PopoverContent>
            </Popover>
          </div>

          <Button onClick={handleApplyFilters}>
            <Filter className="mr-2 h-4 w-4" />
            应用筛选
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
