import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Collapsible, CollapsibleContent, CollapsibleTrigger } from '@/components/ui/collapsible';
import { ScrollArea } from '@/components/ui/scroll-area';
import { 
  Database, 
  Activity, 
  AlertTriangle, 
  CheckCircle, 
  XCircle, 
  Clock, 
  TrendingUp,
  Download,
  Trash2,
  RefreshCw,
  ChevronDown,
  ChevronRight,
  Zap,
  Shield,
  Monitor
} from 'lucide-react';
import { dataMonitor, MonitorEvent, MonitorEventType } from '@/services/dataIntegrationMonitor';

interface DataIntegrationPanelProps {
  pageName: string;
}

export const DataIntegrationPanel: React.FC<DataIntegrationPanelProps> = ({ pageName }) => {
  const [isExpanded, setIsExpanded] = useState(false);
  const [events, setEvents] = useState<MonitorEvent[]>([]);
  const [statistics, setStatistics] = useState<any>(null);
  const [selectedEvent, setSelectedEvent] = useState<MonitorEvent | null>(null);
  const [autoRefresh, setAutoRefresh] = useState(true);

  // 检查是否应该显示面板
  const shouldShow = import.meta.env.DEV || import.meta.env.VITE_SHOW_DEBUG_PANEL === 'true';

  useEffect(() => {
    if (!shouldShow) return;

    const updateData = () => {
      setEvents(dataMonitor.getAllEvents());
      setStatistics(dataMonitor.getStatistics());
    };

    updateData();

    let interval: NodeJS.Timeout;
    if (autoRefresh) {
      interval = setInterval(updateData, 2000); // 每2秒更新
    }

    return () => {
      if (interval) clearInterval(interval);
    };
  }, [autoRefresh, shouldShow]);

  if (!shouldShow) return null;

  // 过滤当前页面的事件
  const pageEvents = events.filter(event => event.page === pageName);
  const recentEvents = pageEvents.slice(-20); // 最近20个事件

  // 统计信息
  const pageStats = {
    totalEvents: pageEvents.length,
    successCount: pageEvents.filter(e => e.type === MonitorEventType.REQUEST_SUCCESS).length,
    errorCount: pageEvents.filter(e => e.type === MonitorEventType.REQUEST_ERROR).length,
    validationErrors: pageEvents.filter(e => 
      e.type === MonitorEventType.DATA_VALIDATION && 
      e.details.dataValidation && 
      !e.details.dataValidation.isValid
    ).length,
    avgResponseTime: pageEvents
      .filter(e => e.details.duration)
      .reduce((sum, e) => sum + (e.details.duration || 0), 0) / 
      Math.max(1, pageEvents.filter(e => e.details.duration).length)
  };

  const getEventIcon = (type: MonitorEventType) => {
    switch (type) {
      case MonitorEventType.REQUEST_SUCCESS:
        return <CheckCircle className="h-4 w-4 text-green-500" />;
      case MonitorEventType.REQUEST_ERROR:
        return <XCircle className="h-4 w-4 text-red-500" />;
      case MonitorEventType.DATA_VALIDATION:
        return <Shield className="h-4 w-4 text-blue-500" />;
      case MonitorEventType.DATABASE_OPERATION:
        return <Database className="h-4 w-4 text-purple-500" />;
      case MonitorEventType.PERFORMANCE_METRIC:
        return <TrendingUp className="h-4 w-4 text-orange-500" />;
      case MonitorEventType.ENVIRONMENT_CHECK:
        return <Monitor className="h-4 w-4 text-gray-500" />;
      default:
        return <Activity className="h-4 w-4 text-gray-400" />;
    }
  };

  const getEventColor = (type: MonitorEventType) => {
    switch (type) {
      case MonitorEventType.REQUEST_SUCCESS:
        return 'bg-green-100 text-green-800';
      case MonitorEventType.REQUEST_ERROR:
        return 'bg-red-100 text-red-800';
      case MonitorEventType.DATA_VALIDATION:
        return 'bg-blue-100 text-blue-800';
      case MonitorEventType.DATABASE_OPERATION:
        return 'bg-purple-100 text-purple-800';
      case MonitorEventType.PERFORMANCE_METRIC:
        return 'bg-orange-100 text-orange-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const handleExportData = () => {
    const data = dataMonitor.exportData();
    const blob = new Blob([data], { type: 'application/json' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `data-integration-log-${pageName}-${new Date().toISOString().split('T')[0]}.json`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
  };

  const handleClearEvents = () => {
    dataMonitor.clearEvents();
    setEvents([]);
    setSelectedEvent(null);
  };

  const formatTimestamp = (timestamp: string) => {
    return new Date(timestamp).toLocaleTimeString();
  };

  return (
    <Card className="mb-6 border-2 border-dashed border-purple-200 bg-purple-50/50">
      <Collapsible open={isExpanded} onOpenChange={setIsExpanded}>
        <CollapsibleTrigger asChild>
          <CardHeader className="cursor-pointer hover:bg-purple-100/50 transition-colors">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-2">
                <Database className="h-5 w-5 text-purple-600" />
                <CardTitle className="text-lg">数据对接监测 - {pageName}</CardTitle>
                {isExpanded ? <ChevronDown className="h-4 w-4" /> : <ChevronRight className="h-4 w-4" />}
              </div>
              <div className="flex items-center gap-2">
                <Badge variant="outline" className="bg-white">
                  {pageStats.successCount}/{pageStats.totalEvents} 成功
                </Badge>
                {pageStats.errorCount > 0 && (
                  <Badge variant="destructive">
                    {pageStats.errorCount} 错误
                  </Badge>
                )}
                {pageStats.validationErrors > 0 && (
                  <Badge variant="secondary">
                    {pageStats.validationErrors} 验证失败
                  </Badge>
                )}
              </div>
            </div>
            <CardDescription>
              监控前端与后端数据库的完整对接流程，包括请求验证、API连接、数据结构验证等
            </CardDescription>
          </CardHeader>
        </CollapsibleTrigger>

        <CollapsibleContent>
          <CardContent>
            {/* 统计概览 */}
            <div className="grid grid-cols-2 md:grid-cols-5 gap-4 mb-6">
              <div className="text-center p-3 bg-white rounded-lg border">
                <div className="text-2xl font-bold text-purple-600">{pageStats.totalEvents}</div>
                <div className="text-sm text-gray-600">总事件数</div>
              </div>
              <div className="text-center p-3 bg-white rounded-lg border">
                <div className="text-2xl font-bold text-green-600">{pageStats.successCount}</div>
                <div className="text-sm text-gray-600">成功请求</div>
              </div>
              <div className="text-center p-3 bg-white rounded-lg border">
                <div className="text-2xl font-bold text-red-600">{pageStats.errorCount}</div>
                <div className="text-sm text-gray-600">失败请求</div>
              </div>
              <div className="text-center p-3 bg-white rounded-lg border">
                <div className="text-2xl font-bold text-blue-600">{pageStats.validationErrors}</div>
                <div className="text-sm text-gray-600">验证错误</div>
              </div>
              <div className="text-center p-3 bg-white rounded-lg border">
                <div className="text-2xl font-bold text-orange-600">
                  {Math.round(pageStats.avgResponseTime)}ms
                </div>
                <div className="text-sm text-gray-600">平均响应</div>
              </div>
            </div>

            {/* 操作按钮 */}
            <div className="flex gap-2 mb-6">
              <Button
                onClick={() => setAutoRefresh(!autoRefresh)}
                variant={autoRefresh ? "default" : "outline"}
                size="sm"
              >
                <RefreshCw className={`h-4 w-4 mr-2 ${autoRefresh ? 'animate-spin' : ''}`} />
                {autoRefresh ? '自动刷新' : '手动刷新'}
              </Button>
              <Button onClick={handleExportData} variant="outline" size="sm">
                <Download className="h-4 w-4 mr-2" />
                导出数据
              </Button>
              <Button onClick={handleClearEvents} variant="outline" size="sm">
                <Trash2 className="h-4 w-4 mr-2" />
                清除日志
              </Button>
              <Button 
                onClick={() => dataMonitor.checkEnvironmentCompatibility(pageName)} 
                variant="outline" 
                size="sm"
              >
                <Monitor className="h-4 w-4 mr-2" />
                环境检查
              </Button>
            </div>

            {/* 事件列表和详情 */}
            <Tabs defaultValue="events" className="w-full">
              <TabsList>
                <TabsTrigger value="events">事件列表</TabsTrigger>
                <TabsTrigger value="details">事件详情</TabsTrigger>
                <TabsTrigger value="analysis">数据分析</TabsTrigger>
              </TabsList>

              <TabsContent value="events" className="mt-4">
                <ScrollArea className="h-96 w-full border rounded-lg">
                  <div className="p-4 space-y-2">
                    {recentEvents.length === 0 ? (
                      <div className="text-center py-8 text-gray-500">
                        暂无事件记录
                      </div>
                    ) : (
                      recentEvents.reverse().map((event) => (
                        <div
                          key={event.id}
                          className="flex items-center justify-between p-3 bg-white rounded-lg border hover:bg-gray-50 cursor-pointer"
                          onClick={() => setSelectedEvent(event)}
                        >
                          <div className="flex items-center gap-3">
                            {getEventIcon(event.type)}
                            <div>
                              <div className="font-medium">{event.component}</div>
                              <div className="text-sm text-gray-500">
                                {formatTimestamp(event.timestamp)}
                              </div>
                            </div>
                          </div>
                          <div className="flex items-center gap-2">
                            {event.details.duration && (
                              <Badge variant="outline">{event.details.duration}ms</Badge>
                            )}
                            <Badge className={getEventColor(event.type)}>
                              {event.type.replace('_', ' ')}
                            </Badge>
                          </div>
                        </div>
                      ))
                    )}
                  </div>
                </ScrollArea>
              </TabsContent>

              <TabsContent value="details" className="mt-4">
                {selectedEvent ? (
                  <EventDetails event={selectedEvent} />
                ) : (
                  <div className="text-center py-8 text-gray-500">
                    请在事件列表中选择一个事件查看详细信息
                  </div>
                )}
              </TabsContent>

              <TabsContent value="analysis" className="mt-4">
                <DataAnalysis events={pageEvents} />
              </TabsContent>
            </Tabs>
          </CardContent>
        </CollapsibleContent>
      </Collapsible>
    </Card>
  );
};

// 事件详情组件
const EventDetails: React.FC<{ event: MonitorEvent }> = ({ event }) => {
  return (
    <div className="space-y-4">
      <div className="grid grid-cols-2 gap-4">
        <div>
          <h4 className="font-medium mb-2">基本信息</h4>
          <div className="space-y-1 text-sm">
            <div><strong>事件ID:</strong> {event.id}</div>
            <div><strong>类型:</strong> {event.type}</div>
            <div><strong>组件:</strong> {event.component}</div>
            <div><strong>时间:</strong> {new Date(event.timestamp).toLocaleString()}</div>
          </div>
        </div>
        <div>
          <h4 className="font-medium mb-2">性能信息</h4>
          <div className="space-y-1 text-sm">
            {event.details.duration && (
              <div><strong>响应时间:</strong> {event.details.duration}ms</div>
            )}
            {event.details.endpoint && (
              <div><strong>端点:</strong> {event.details.endpoint}</div>
            )}
            {event.details.method && (
              <div><strong>方法:</strong> {event.details.method}</div>
            )}
          </div>
        </div>
      </div>

      {event.details.error && (
        <div className="p-3 bg-red-50 border border-red-200 rounded-lg">
          <h4 className="font-medium text-red-800 mb-2 flex items-center gap-2">
            <AlertTriangle className="h-4 w-4" />
            错误信息
          </h4>
          <pre className="text-sm text-red-700 whitespace-pre-wrap">{event.details.error}</pre>
        </div>
      )}

      {event.details.dataValidation && (
        <div className="p-3 bg-blue-50 border border-blue-200 rounded-lg">
          <h4 className="font-medium text-blue-800 mb-2">数据验证结果</h4>
          <div className="space-y-2 text-sm">
            <div className="flex items-center gap-2">
              {event.details.dataValidation.isValid ? (
                <CheckCircle className="h-4 w-4 text-green-500" />
              ) : (
                <XCircle className="h-4 w-4 text-red-500" />
              )}
              <span>{event.details.dataValidation.isValid ? '验证通过' : '验证失败'}</span>
            </div>
            {event.details.dataValidation.errors.length > 0 && (
              <div>
                <strong>错误:</strong>
                <ul className="list-disc list-inside ml-4">
                  {event.details.dataValidation.errors.map((error, index) => (
                    <li key={index} className="text-red-700">{error}</li>
                  ))}
                </ul>
              </div>
            )}
          </div>
        </div>
      )}

      {(event.details.requestData || event.details.responseData) && (
        <div className="space-y-4">
          {event.details.requestData && (
            <div>
              <h4 className="font-medium mb-2">请求数据</h4>
              <pre className="bg-gray-900 text-green-400 p-4 rounded-lg overflow-auto max-h-48 text-sm">
                {JSON.stringify(event.details.requestData, null, 2)}
              </pre>
            </div>
          )}
          {event.details.responseData && (
            <div>
              <h4 className="font-medium mb-2">响应数据</h4>
              <pre className="bg-gray-900 text-green-400 p-4 rounded-lg overflow-auto max-h-48 text-sm">
                {JSON.stringify(event.details.responseData, null, 2)}
              </pre>
            </div>
          )}
        </div>
      )}
    </div>
  );
};

// 数据分析组件
const DataAnalysis: React.FC<{ events: MonitorEvent[] }> = ({ events }) => {
  const analysis = {
    requestTrends: events.filter(e => e.details.duration).map(e => ({
      time: new Date(e.timestamp).getTime(),
      duration: e.details.duration || 0
    })),
    errorPatterns: events
      .filter(e => e.type === MonitorEventType.REQUEST_ERROR)
      .reduce((acc, e) => {
        const error = e.details.error || 'Unknown error';
        acc[error] = (acc[error] || 0) + 1;
        return acc;
      }, {} as Record<string, number>),
    validationIssues: events
      .filter(e => e.details.dataValidation && !e.details.dataValidation.isValid)
      .flatMap(e => e.details.dataValidation?.errors || [])
      .reduce((acc, error) => {
        acc[error] = (acc[error] || 0) + 1;
        return acc;
      }, {} as Record<string, number>)
  };

  return (
    <div className="space-y-6">
      <div>
        <h4 className="font-medium mb-4">错误模式分析</h4>
        {Object.keys(analysis.errorPatterns).length === 0 ? (
          <p className="text-gray-500">暂无错误记录</p>
        ) : (
          <div className="space-y-2">
            {Object.entries(analysis.errorPatterns).map(([error, count]) => (
              <div key={error} className="flex justify-between items-center p-2 bg-red-50 rounded">
                <span className="text-sm">{error}</span>
                <Badge variant="destructive">{count}</Badge>
              </div>
            ))}
          </div>
        )}
      </div>

      <div>
        <h4 className="font-medium mb-4">数据验证问题</h4>
        {Object.keys(analysis.validationIssues).length === 0 ? (
          <p className="text-gray-500">暂无验证问题</p>
        ) : (
          <div className="space-y-2">
            {Object.entries(analysis.validationIssues).map(([issue, count]) => (
              <div key={issue} className="flex justify-between items-center p-2 bg-yellow-50 rounded">
                <span className="text-sm">{issue}</span>
                <Badge variant="secondary">{count}</Badge>
              </div>
            ))}
          </div>
        )}
      </div>
    </div>
  );
};

export default DataIntegrationPanel;
