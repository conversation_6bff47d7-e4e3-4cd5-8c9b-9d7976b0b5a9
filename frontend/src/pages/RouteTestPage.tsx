import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { CheckCircle, XCircle, ExternalLink, RefreshCw } from 'lucide-react';

const RouteTestPage: React.FC = () => {
  const navigate = useNavigate();
  const [testResults, setTestResults] = useState<Record<string, 'pending' | 'success' | 'error'>>({});
  const [isAutoTesting, setIsAutoTesting] = useState(false);

  // 数据分析相关路由测试
  const dataAnalysisRoutes = [
    {
      name: '问卷回复',
      path: '/superadmin/questionnaire-responses',
      description: '查看和管理问卷回复数据',
      fullUrl: 'https://02b0ebf5.college-employment-survey.pages.dev/superadmin/questionnaire-responses'
    },
    {
      name: '数据分析',
      path: '/superadmin/data-analysis',
      description: '基础数据分析和图表展示',
      fullUrl: 'https://02b0ebf5.college-employment-survey.pages.dev/superadmin/data-analysis'
    },
    {
      name: '高级数据分析',
      path: '/superadmin/enhanced-data-analysis',
      description: '高级数据分析功能',
      fullUrl: 'https://02b0ebf5.college-employment-survey.pages.dev/superadmin/enhanced-data-analysis'
    },
    {
      name: '数据管理中心',
      path: '/superadmin/data-management',
      description: '数据管理和维护功能',
      fullUrl: 'https://02b0ebf5.college-employment-survey.pages.dev/superadmin/data-management'
    },
    {
      name: '数据测试中心',
      path: '/superadmin/data-testing',
      description: '数据测试和验证功能',
      fullUrl: 'https://02b0ebf5.college-employment-survey.pages.dev/superadmin/data-testing'
    }
  ];

  const testRoute = (path: string) => {
    try {
      navigate(path);
      return true;
    } catch (error) {
      console.error(`路由测试失败: ${path}`, error);
      return false;
    }
  };

  const openInNewTab = (url: string) => {
    window.open(url, '_blank');
  };

  const testRouteAvailability = async (route: any) => {
    try {
      setTestResults(prev => ({ ...prev, [route.path]: 'pending' }));

      // 使用fetch测试路由是否可访问
      const response = await fetch(route.fullUrl, {
        method: 'HEAD',
        mode: 'no-cors' // 避免CORS问题
      });

      // 对于no-cors模式，我们只能检查是否有网络错误
      setTestResults(prev => ({ ...prev, [route.path]: 'success' }));
      return true;
    } catch (error) {
      console.error(`路由测试失败: ${route.path}`, error);
      setTestResults(prev => ({ ...prev, [route.path]: 'error' }));
      return false;
    }
  };

  const runAllTests = async () => {
    setIsAutoTesting(true);

    for (const route of dataAnalysisRoutes) {
      await testRouteAvailability(route);
      // 添加延迟避免请求过快
      await new Promise(resolve => setTimeout(resolve, 500));
    }

    setIsAutoTesting(false);
  };

  const getStatusIcon = (status: 'pending' | 'success' | 'error' | undefined) => {
    switch (status) {
      case 'pending':
        return <RefreshCw className="h-4 w-4 animate-spin text-blue-500" />;
      case 'success':
        return <CheckCircle className="h-4 w-4 text-green-500" />;
      case 'error':
        return <XCircle className="h-4 w-4 text-red-500" />;
      default:
        return <div className="h-4 w-4 rounded-full border-2 border-gray-300" />;
    }
  };

  return (
    <div className="container mx-auto py-6">
      <div className="mb-6">
        <div className="flex items-center justify-between mb-4">
          <div>
            <h1 className="text-2xl font-bold mb-2">数据分析路由测试</h1>
            <p className="text-gray-600">测试数据分析栏目下各个子项的路由是否正常工作</p>
          </div>
          <Button
            onClick={runAllTests}
            disabled={isAutoTesting}
            className="flex items-center gap-2"
          >
            {isAutoTesting ? (
              <RefreshCw className="h-4 w-4 animate-spin" />
            ) : (
              <CheckCircle className="h-4 w-4" />
            )}
            {isAutoTesting ? '测试中...' : '自动测试所有路由'}
          </Button>
        </div>
      </div>

      <div className="grid gap-4">
        {dataAnalysisRoutes.map((route, index) => (
          <Card key={index} className="hover:shadow-md transition-shadow">
            <CardHeader>
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-3">
                  {getStatusIcon(testResults[route.path])}
                  <div>
                    <CardTitle className="text-lg">{route.name}</CardTitle>
                    <CardDescription>{route.description}</CardDescription>
                  </div>
                </div>
                <Badge variant="outline" className="font-mono text-xs">
                  {route.path}
                </Badge>
              </div>
            </CardHeader>
            <CardContent>
              <div className="flex gap-2">
                <Button
                  onClick={() => testRoute(route.path)}
                  className="flex items-center gap-2"
                >
                  <CheckCircle className="h-4 w-4" />
                  当前窗口测试
                </Button>
                <Button
                  variant="outline"
                  onClick={() => openInNewTab(route.fullUrl)}
                  className="flex items-center gap-2"
                >
                  <ExternalLink className="h-4 w-4" />
                  新窗口打开
                </Button>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      <Card className="mt-6">
        <CardHeader>
          <CardTitle>测试说明</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-2 text-sm text-gray-600">
            <p>• <strong>当前窗口测试</strong>：在当前窗口中导航到目标页面</p>
            <p>• <strong>新窗口打开</strong>：在新标签页中打开目标页面</p>
            <p>• 如果页面能正常加载，说明路由配置正确</p>
            <p>• 如果出现404或加载错误，说明路由配置有问题</p>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default RouteTestPage;
