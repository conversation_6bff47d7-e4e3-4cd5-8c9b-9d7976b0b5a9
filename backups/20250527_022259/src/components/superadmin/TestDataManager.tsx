import React, { useState, useEffect } from 'react';
import {
  Card,
  CardContent,
  Typography,
  Button,
  Grid,
  Box,
  Alert,
  CircularProgress,
  Divider,
  List,
  ListItem,
  ListItemIcon,
  ListItemText,
  Chip,
  LinearProgress,
  Paper,
  Tooltip,
  IconButton,
  Accordion,
  AccordionSummary,
  AccordionDetails
} from '@mui/material';
import {
  Backup as BackupIcon,
  Delete as DeleteIcon,
  Refresh as RefreshIcon,
  Person as PersonIcon,
  Assignment as AssignmentIcon,
  Comment as CommentIcon,
  AdminPanelSettings as AdminIcon,
  SupervisorAccount as ReviewerIcon,
  ExpandMore as ExpandMoreIcon,
  Info as InfoIcon,
  CheckCircle as CheckCircleIcon,
  Error as ErrorIcon,
  Warning as WarningIcon,
  Settings as SettingsIcon
} from '@mui/icons-material';
import { useSnackbar } from 'notistack';
import {
  getTestDataStats,
  importTestData,
  clearTestData,
  TestDataStats,
  ImportProgress
} from '../../services/testDataService';

/**
 * 测试数据管理组件
 *
 * 用于超级管理员页面，提供测试数据的导入和清除功能
 */
const TestDataManager: React.FC = () => {
  const { enqueueSnackbar } = useSnackbar();
  const [loading, setLoading] = useState<boolean>(false);
  const [status, setStatus] = useState<string>('');
  const [dataStats, setDataStats] = useState<TestDataStats | null>(null);
  const [operationProgress, setOperationProgress] = useState<number>(0);
  const [operationStatus, setOperationStatus] = useState<'idle' | 'success' | 'error' | 'warning' | 'progress'>('idle');
  const [lastOperation, setLastOperation] = useState<string>('');
  const [detailedLogs, setDetailedLogs] = useState<string[]>([]);
  const [showAdvancedOptions, setShowAdvancedOptions] = useState<boolean>(false);
  const [advancedOptions, setAdvancedOptions] = useState<ImportTestDataParams>({
    userCount: 50,
    reviewerCount: 10,
    adminCount: 3,
    questionnaireCount: 50,
    storyCount: 50,
    uuidPercentage: 60
  });

  // 获取当前测试数据统计
  const fetchDataStats = async () => {
    try {
      setLoading(true);
      setStatus('正在获取测试数据统计...');

      // 调用服务获取数据统计
      const response = await getTestDataStats();

      if (response.success) {
        setDataStats(response.data);
        setDetailedLogs(prev => [...prev, '测试数据统计获取成功']);
      } else {
        enqueueSnackbar(response.message || '获取测试数据统计失败', { variant: 'error' });
        setDetailedLogs(prev => [...prev, `测试数据统计获取失败: ${response.message || '未知错误'}`]);
      }
    } catch (error) {
      console.error('获取测试数据统计失败:', error);
      enqueueSnackbar('获取测试数据统计失败', { variant: 'error' });
      setDetailedLogs(prev => [...prev, `测试数据统计获取失败: ${error}`]);
    } finally {
      setLoading(false);
      setStatus('');
    }
  };

  // 处理导入进度回调
  const handleImportProgress = (progress: ImportProgress) => {
    setOperationStatus('progress');
    setOperationProgress(progress.progress);
    setDetailedLogs(prev => [...prev, `${progress.step}: ${progress.details}`]);
  };

  // 导入测试数据
  const handleImportTestData = async () => {
    try {
      setLoading(true);
      setStatus('正在导入测试数据...');
      setLastOperation('导入测试数据');
      setOperationStatus('progress');
      setOperationProgress(0);
      setDetailedLogs(prev => [...prev, '开始导入测试数据...']);

      // 调用服务导入测试数据
      const response = await importTestData(advancedOptions, handleImportProgress);

      if (response.success) {
        setOperationStatus('success');
        setDetailedLogs(prev => [...prev, '测试数据导入成功']);
        enqueueSnackbar('测试数据导入成功', { variant: 'success' });

        // 更新数据统计
        if (response.data) {
          setDataStats(response.data);
        } else {
          // 如果响应中没有数据统计，则重新获取
          fetchDataStats();
        }
      } else {
        setOperationStatus('error');
        setDetailedLogs(prev => [...prev, `测试数据导入失败: ${response.message || '未知错误'}`]);
        enqueueSnackbar(response.message || '测试数据导入失败', { variant: 'error' });
      }
    } catch (error) {
      console.error('导入测试数据失败:', error);
      setOperationStatus('error');
      setDetailedLogs(prev => [...prev, `测试数据导入失败: ${error}`]);
      enqueueSnackbar('导入测试数据失败', { variant: 'error' });
    } finally {
      setLoading(false);
      setStatus('');
    }
  };

  // 清除测试数据
  const handleClearTestData = async () => {
    if (!window.confirm('确定要清除所有测试数据吗？此操作不可逆！')) {
      return;
    }

    try {
      setLoading(true);
      setStatus('正在清除测试数据...');
      setLastOperation('清除测试数据');
      setOperationStatus('progress');
      setOperationProgress(0);
      setDetailedLogs(prev => [...prev, '开始清除测试数据...']);

      // 调用服务清除测试数据
      const response = await clearTestData(handleImportProgress);

      if (response.success) {
        setOperationStatus('success');
        setDetailedLogs(prev => [...prev, '测试数据清除成功']);
        enqueueSnackbar('测试数据清除成功', { variant: 'success' });

        // 更新数据统计
        if (response.data) {
          setDataStats(response.data);
        } else {
          // 如果响应中没有数据统计，则重新获取
          fetchDataStats();
        }
      } else {
        setOperationStatus('error');
        setDetailedLogs(prev => [...prev, `测试数据清除失败: ${response.message || '未知错误'}`]);
        enqueueSnackbar(response.message || '测试数据清除失败', { variant: 'error' });
      }
    } catch (error) {
      console.error('清除测试数据失败:', error);
      setOperationStatus('error');
      setDetailedLogs(prev => [...prev, `测试数据清除失败: ${error}`]);
      enqueueSnackbar('清除测试数据失败', { variant: 'error' });
    } finally {
      setLoading(false);
      setStatus('');
    }
  };

  // 组件加载时获取数据统计
  React.useEffect(() => {
    fetchDataStats();
  }, []);

  return (
    <Card>
      <CardContent>
        <Typography variant="h5" component="h2" gutterBottom>
          测试数据管理
        </Typography>
        <Typography variant="body2" color="textSecondary" paragraph>
          用于生成和管理测试数据，方便功能验证和演示。测试数据包括用户账号、问卷和故事墙内容等。
        </Typography>

        {/* 操作状态显示 */}
        {status && (
          <Alert
            severity={
              operationStatus === 'error' ? 'error' :
              operationStatus === 'warning' ? 'warning' :
              operationStatus === 'success' ? 'success' : 'info'
            }
            sx={{ my: 2 }}
            icon={
              operationStatus === 'error' ? <ErrorIcon /> :
              operationStatus === 'warning' ? <WarningIcon /> :
              operationStatus === 'success' ? <CheckCircleIcon /> :
              operationStatus === 'progress' ? <CircularProgress size={20} /> : null
            }
          >
            <Box sx={{ width: '100%' }}>
              <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
                <Typography variant="body1">{status}</Typography>
                {loading && operationStatus === 'progress' && (
                  <Typography variant="body2">{operationProgress}%</Typography>
                )}
              </Box>
              {loading && operationStatus === 'progress' && (
                <LinearProgress
                  variant="determinate"
                  value={operationProgress}
                  sx={{ mt: 1, mb: 1 }}
                />
              )}
            </Box>
          </Alert>
        )}

        {/* 操作日志 */}
        {detailedLogs.length > 0 && (
          <Accordion sx={{ mb: 2 }}>
            <AccordionSummary expandIcon={<ExpandMoreIcon />}>
              <Typography>操作日志</Typography>
            </AccordionSummary>
            <AccordionDetails>
              <Paper variant="outlined" sx={{ p: 2, maxHeight: '200px', overflow: 'auto' }}>
                {detailedLogs.map((log, index) => (
                  <Typography key={index} variant="body2" sx={{ mb: 0.5 }}>
                    {new Date().toLocaleTimeString()} - {log}
                  </Typography>
                ))}
              </Paper>
            </AccordionDetails>
          </Accordion>
        )}

        {dataStats && (
          <Box sx={{ my: 3 }}>
            <Typography variant="h6" gutterBottom>
              当前测试数据统计
            </Typography>
            <Grid container spacing={3}>
              <Grid item xs={12} md={6}>
                <Card variant="outlined">
                  <CardContent>
                    <Typography variant="subtitle1" gutterBottom>
                      用户数据
                    </Typography>
                    <List dense>
                      <ListItem>
                        <ListItemIcon>
                          <PersonIcon />
                        </ListItemIcon>
                        <ListItemText
                          primary="普通用户"
                          secondary={`共 ${dataStats.users.normalUsers} 个，其中 ${dataStats.users.usersWithUuid} 个拥有UUID`}
                        />
                      </ListItem>
                      <ListItem>
                        <ListItemIcon>
                          <ReviewerIcon />
                        </ListItemIcon>
                        <ListItemText
                          primary="审核员"
                          secondary={`共 ${dataStats.users.reviewers} 个`}
                        />
                      </ListItem>
                      <ListItem>
                        <ListItemIcon>
                          <AdminIcon />
                        </ListItemIcon>
                        <ListItemText
                          primary="管理员"
                          secondary={`共 ${dataStats.users.admins} 个普通管理员，${dataStats.users.superAdmins} 个超级管理员`}
                        />
                      </ListItem>
                    </List>
                  </CardContent>
                </Card>
              </Grid>
              <Grid item xs={12} md={6}>
                <Card variant="outlined">
                  <CardContent>
                    <Typography variant="subtitle1" gutterBottom>
                      内容数据
                    </Typography>
                    <List dense>
                      <ListItem>
                        <ListItemIcon>
                          <AssignmentIcon />
                        </ListItemIcon>
                        <ListItemText
                          primary="问卷"
                          secondary={`共 ${dataStats.content.questionnaires} 份`}
                        />
                      </ListItem>
                      <ListItem>
                        <ListItemIcon>
                          <CommentIcon />
                        </ListItemIcon>
                        <ListItemText
                          primary="故事墙"
                          secondary={`共 ${dataStats.content.stories} 条`}
                        />
                      </ListItem>
                      <ListItem>
                        <Box sx={{ display: 'flex', gap: 1 }}>
                          <Chip
                            label={`待审核: ${dataStats.content.pendingReview}`}
                            size="small"
                            color="warning"
                          />
                          <Chip
                            label={`已通过: ${dataStats.content.approved}`}
                            size="small"
                            color="success"
                          />
                          <Chip
                            label={`已拒绝: ${dataStats.content.rejected}`}
                            size="small"
                            color="error"
                          />
                        </Box>
                      </ListItem>
                    </List>
                  </CardContent>
                </Card>
              </Grid>
            </Grid>
            <Typography variant="caption" color="textSecondary" sx={{ mt: 1, display: 'block' }}>
              最后更新时间: {new Date(dataStats.lastGenerated).toLocaleString()}
            </Typography>
          </Box>
        )}

        <Divider sx={{ my: 3 }} />

        <Grid container spacing={2}>
          <Grid item xs={12} sm={3}>
            <Tooltip title="导入测试数据，包括用户、问卷和故事墙内容">
              <Button
                variant="contained"
                color="primary"
                startIcon={<BackupIcon />}
                fullWidth
                onClick={handleImportTestData}
                disabled={loading}
              >
                导入测试数据
              </Button>
            </Tooltip>
          </Grid>
          <Grid item xs={12} sm={3}>
            <Tooltip title="清除所有测试数据，不影响手动创建的数据">
              <Button
                variant="outlined"
                color="error"
                startIcon={<DeleteIcon />}
                fullWidth
                onClick={handleClearTestData}
                disabled={loading}
              >
                清除测试数据
              </Button>
            </Tooltip>
          </Grid>
          <Grid item xs={12} sm={3}>
            <Tooltip title="刷新测试数据统计信息">
              <Button
                variant="outlined"
                startIcon={<RefreshIcon />}
                fullWidth
                onClick={fetchDataStats}
                disabled={loading}
              >
                刷新统计
              </Button>
            </Tooltip>
          </Grid>
          <Grid item xs={12} sm={3}>
            <Tooltip title="高级选项">
              <Button
                variant="outlined"
                startIcon={<SettingsIcon />}
                fullWidth
                onClick={() => setShowAdvancedOptions(!showAdvancedOptions)}
                disabled={loading}
              >
                高级选项
              </Button>
            </Tooltip>
          </Grid>
        </Grid>

        {showAdvancedOptions && (
          <Box sx={{ mt: 3, p: 2, border: '1px dashed', borderColor: 'divider', borderRadius: 1 }}>
            <Typography variant="subtitle1" gutterBottom>
              高级导入选项
            </Typography>
            <Typography variant="body2" color="textSecondary" paragraph>
              自定义测试数据的生成参数。这些选项允许您控制生成的测试数据数量和类型。
            </Typography>
            <Grid container spacing={2}>
              <Grid item xs={12} sm={4}>
                <Typography variant="subtitle2" gutterBottom>
                  用户数据
                </Typography>
                <Box sx={{ mb: 2 }}>
                  <Typography variant="body2" gutterBottom>
                    普通用户数量
                  </Typography>
                  <Box sx={{ display: 'flex', alignItems: 'center' }}>
                    <Slider
                      value={advancedOptions.userCount || 50}
                      min={10}
                      max={200}
                      step={10}
                      onChange={(_, value) => setAdvancedOptions(prev => ({ ...prev, userCount: value as number }))}
                      sx={{ flexGrow: 1, mr: 2 }}
                    />
                    <Typography variant="body2">
                      {advancedOptions.userCount || 50}
                    </Typography>
                  </Box>
                </Box>
                <Box sx={{ mb: 2 }}>
                  <Typography variant="body2" gutterBottom>
                    审核员数量
                  </Typography>
                  <Box sx={{ display: 'flex', alignItems: 'center' }}>
                    <Slider
                      value={advancedOptions.reviewerCount || 10}
                      min={1}
                      max={50}
                      step={1}
                      onChange={(_, value) => setAdvancedOptions(prev => ({ ...prev, reviewerCount: value as number }))}
                      sx={{ flexGrow: 1, mr: 2 }}
                    />
                    <Typography variant="body2">
                      {advancedOptions.reviewerCount || 10}
                    </Typography>
                  </Box>
                </Box>
                <Box sx={{ mb: 2 }}>
                  <Typography variant="body2" gutterBottom>
                    管理员数量
                  </Typography>
                  <Box sx={{ display: 'flex', alignItems: 'center' }}>
                    <Slider
                      value={advancedOptions.adminCount || 3}
                      min={1}
                      max={20}
                      step={1}
                      onChange={(_, value) => setAdvancedOptions(prev => ({ ...prev, adminCount: value as number }))}
                      sx={{ flexGrow: 1, mr: 2 }}
                    />
                    <Typography variant="body2">
                      {advancedOptions.adminCount || 3}
                    </Typography>
                  </Box>
                </Box>
              </Grid>
              <Grid item xs={12} sm={4}>
                <Typography variant="subtitle2" gutterBottom>
                  内容数据
                </Typography>
                <Box sx={{ mb: 2 }}>
                  <Typography variant="body2" gutterBottom>
                    问卷数量
                  </Typography>
                  <Box sx={{ display: 'flex', alignItems: 'center' }}>
                    <Slider
                      value={advancedOptions.questionnaireCount || 50}
                      min={10}
                      max={200}
                      step={10}
                      onChange={(_, value) => setAdvancedOptions(prev => ({ ...prev, questionnaireCount: value as number }))}
                      sx={{ flexGrow: 1, mr: 2 }}
                    />
                    <Typography variant="body2">
                      {advancedOptions.questionnaireCount || 50}
                    </Typography>
                  </Box>
                </Box>
                <Box sx={{ mb: 2 }}>
                  <Typography variant="body2" gutterBottom>
                    故事数量
                  </Typography>
                  <Box sx={{ display: 'flex', alignItems: 'center' }}>
                    <Slider
                      value={advancedOptions.storyCount || 50}
                      min={10}
                      max={200}
                      step={10}
                      onChange={(_, value) => setAdvancedOptions(prev => ({ ...prev, storyCount: value as number }))}
                      sx={{ flexGrow: 1, mr: 2 }}
                    />
                    <Typography variant="body2">
                      {advancedOptions.storyCount || 50}
                    </Typography>
                  </Box>
                </Box>
              </Grid>
              <Grid item xs={12} sm={4}>
                <Typography variant="subtitle2" gutterBottom>
                  其他设置
                </Typography>
                <Box sx={{ mb: 2 }}>
                  <Typography variant="body2" gutterBottom>
                    UUID用户百分比
                  </Typography>
                  <Box sx={{ display: 'flex', alignItems: 'center' }}>
                    <Slider
                      value={advancedOptions.uuidPercentage || 60}
                      min={0}
                      max={100}
                      step={5}
                      onChange={(_, value) => setAdvancedOptions(prev => ({ ...prev, uuidPercentage: value as number }))}
                      sx={{ flexGrow: 1, mr: 2 }}
                    />
                    <Typography variant="body2">
                      {advancedOptions.uuidPercentage || 60}%
                    </Typography>
                  </Box>
                </Box>
              </Grid>
              <Grid item xs={12}>
                <Box sx={{ display: 'flex', justifyContent: 'flex-end', mt: 2 }}>
                  <Button
                    variant="outlined"
                    color="secondary"
                    onClick={() => setAdvancedOptions({
                      userCount: 50,
                      reviewerCount: 10,
                      adminCount: 3,
                      questionnaireCount: 50,
                      storyCount: 50,
                      uuidPercentage: 60
                    })}
                    sx={{ mr: 1 }}
                  >
                    重置默认值
                  </Button>
                  <Button
                    variant="contained"
                    color="primary"
                    onClick={handleImportTestData}
                    disabled={loading}
                  >
                    使用这些设置导入
                  </Button>
                </Box>
              </Grid>
            </Grid>
          </Box>
        )}

        {/* 操作状态指示器 */}
        <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'center', mt: 3 }}>
          <Typography variant="body2" color="textSecondary" sx={{ mr: 1 }}>
            系统状态:
          </Typography>
          {operationStatus === 'idle' && (
            <Chip
              label="空闲"
              size="small"
              color="default"
              icon={<InfoIcon />}
            />
          )}
          {operationStatus === 'progress' && (
            <Chip
              label="处理中"
              size="small"
              color="primary"
              icon={<CircularProgress size={16} />}
            />
          )}
          {operationStatus === 'success' && (
            <Chip
              label="操作成功"
              size="small"
              color="success"
              icon={<CheckCircleIcon />}
            />
          )}
          {operationStatus === 'error' && (
            <Chip
              label="操作失败"
              size="small"
              color="error"
              icon={<ErrorIcon />}
            />
          )}
          {operationStatus === 'warning' && (
            <Chip
              label="警告"
              size="small"
              color="warning"
              icon={<WarningIcon />}
            />
          )}

          <Typography variant="body2" color="textSecondary" sx={{ ml: 2, mr: 1 }}>
            最后操作:
          </Typography>
          <Typography variant="body2" fontWeight="medium">
            {lastOperation || '无'}
          </Typography>
        </Box>
      </CardContent>
    </Card>
  );
};

export default TestDataManager;
