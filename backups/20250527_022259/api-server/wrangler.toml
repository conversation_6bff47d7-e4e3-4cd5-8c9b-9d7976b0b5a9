name = "college-employment-survey-api"
main = "src/index.ts"
compatibility_date = "2023-05-18"

# 开发环境配置
[env.development]
kv_namespaces = [
  { binding = "COLLEGE_SURVEY_KV", id = "your-kv-id-here" }
]
d1_databases = [
  { binding = "DB", database_name = "college-survey-db", database_id = "your-d1-id-here" }
]

# 测试环境配置
[env.staging]
kv_namespaces = [
  { binding = "COLLEGE_SURVEY_KV", id = "your-kv-id-here" }
]
d1_databases = [
  { binding = "DB", database_name = "college-survey-db", database_id = "your-d1-id-here" }
]

# 生产环境配置
[env.production]
kv_namespaces = [
  { binding = "COLLEGE_SURVEY_KV", id = "your-kv-id-here" }
]
d1_databases = [
  { binding = "DB", database_name = "college-survey-db", database_id = "your-d1-id-here" }
]
