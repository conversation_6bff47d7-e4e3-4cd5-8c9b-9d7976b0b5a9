/**
 * 错误分析服务
 *
 * 负责错误聚类、异常检测和模式识别
 */

import { LogEntry } from './securityLogger';
import { ErrorAnalysisConfig } from '../types';

/**
 * 错误聚类结果
 */
interface ErrorCluster {
  id: string;
  name: string;
  count: number;
  firstSeen: number;
  lastSeen: number;
  errorCodes: string[];
  sources: string[];
  tags: string[];
  severity: 'low' | 'medium' | 'high' | 'critical';
  samples: LogEntry[];
  pattern?: string;
  similarityThreshold: number;
}

/**
 * 异常检测结果
 */
interface AnomalyDetectionResult {
  isAnomaly: boolean;
  score: number;
  reason?: string;
  threshold: number;
  baseline?: any;
  current?: any;
}

/**
 * 错误模式
 */
interface ErrorPattern {
  id: string;
  name: string;
  pattern: string;
  description: string;
  severity: 'low' | 'medium' | 'high' | 'critical';
  suggestedAction?: string;
  autoFixAvailable: boolean;
  matchCount: number;
  firstSeen: number;
  lastSeen: number;
}

/**
 * 错误分析服务类
 */
export class ErrorAnalyzer {
  private config: ErrorAnalysisConfig;
  private errorClusters: Map<string, ErrorCluster> = new Map();
  private errorPatterns: Map<string, ErrorPattern> = new Map();
  private errorFrequency: Map<string, number[]> = new Map();
  private lastAnalysisTime: number = 0;
  private anomalyBaselines: Map<string, any> = new Map();

  /**
   * 构造函数
   */
  constructor() {
    this.config = {
      enabled: true,
      clusteringEnabled: true,
      anomalyDetectionEnabled: true,
      patternRecognitionEnabled: true,
      similarityThreshold: 0.7,
      anomalyThreshold: 2.0,
      minClusterSize: 3,
      maxClusters: 50,
      maxSamplesPerCluster: 5,
      analysisInterval: 3600000, // 1小时
      baselineWindowDays: 7
    };

    // 初始化预定义错误模式
    this.initializeErrorPatterns();
  }

  /**
   * 初始化服务
   * @param config 错误分析配置
   */
  public initialize(config: Partial<ErrorAnalysisConfig>): void {
    this.config = { ...this.config, ...config };
  }

  /**
   * 更新配置
   * @param config 错误分析配置
   */
  public updateConfig(config: Partial<ErrorAnalysisConfig>): void {
    this.config = { ...this.config, ...config };
  }

  /**
   * 分析错误日志
   * @param logs 错误日志
   */
  public async analyzeErrors(logs: LogEntry[]): Promise<{
    clusters: ErrorCluster[];
    patterns: ErrorPattern[];
    anomalies: { [key: string]: AnomalyDetectionResult };
  }> {
    if (!this.config.enabled || logs.length === 0) {
      return { clusters: [], patterns: [], anomalies: {} };
    }

    const now = Date.now();
    const clusters: ErrorCluster[] = [];
    const patterns: ErrorPattern[] = [];
    const anomalies: { [key: string]: AnomalyDetectionResult } = {};

    // 只有在距离上次分析超过间隔时间才执行完整分析
    const shouldPerformFullAnalysis = now - this.lastAnalysisTime > this.config.analysisInterval;

    // 更新错误频率统计
    this.updateErrorFrequency(logs);

    // 执行错误聚类
    if (this.config.clusteringEnabled) {
      if (shouldPerformFullAnalysis) {
        await this.performClustering(logs);
      }
      clusters.push(...Array.from(this.errorClusters.values()));
    }

    // 执行模式识别
    if (this.config.patternRecognitionEnabled) {
      if (shouldPerformFullAnalysis) {
        await this.recognizePatterns(logs);
      }
      patterns.push(...Array.from(this.errorPatterns.values()));
    }

    // 执行异常检测
    if (this.config.anomalyDetectionEnabled) {
      const detectedAnomalies = await this.detectAnomalies(logs);
      Object.assign(anomalies, detectedAnomalies);
    }

    // 更新最后分析时间
    if (shouldPerformFullAnalysis) {
      this.lastAnalysisTime = now;
    }

    return { clusters, patterns, anomalies };
  }

  /**
   * 执行错误聚类
   * @param logs 错误日志
   */
  private async performClustering(logs: LogEntry[]): Promise<void> {
    // 如果日志数量太少，不执行聚类
    if (logs.length < this.config.minClusterSize) {
      return;
    }

    // 清理旧的聚类结果
    this.cleanupOldClusters();

    // 优化：预处理日志，按错误代码分组
    const logsByErrorCode: Map<string, LogEntry[]> = new Map();
    const logsWithoutCode: LogEntry[] = [];

    for (const log of logs) {
      // 跳过没有错误代码或消息的日志
      if (!log.errorCode && !log.message) {
        continue;
      }

      if (log.errorCode) {
        const logs = logsByErrorCode.get(log.errorCode) || [];
        logs.push(log);
        logsByErrorCode.set(log.errorCode, logs);
      } else {
        logsWithoutCode.push(log);
      }
    }

    // 首先处理有错误代码的日志 - 这些可以快速匹配
    for (const [errorCode, codeLogs] of logsByErrorCode.entries()) {
      // 查找匹配此错误代码的现有聚类
      const matchingClusters = Array.from(this.errorClusters.values())
        .filter(cluster => cluster.errorCodes.includes(errorCode));

      if (matchingClusters.length > 0) {
        // 如果找到匹配的聚类，将日志添加到第一个匹配的聚类
        for (const log of codeLogs) {
          this.addLogToCluster(log, matchingClusters[0]);
        }
      } else if (this.errorClusters.size < this.config.maxClusters) {
        // 如果没有匹配的聚类，为此错误代码创建一个新聚类
        this.createNewCluster(codeLogs[0]);

        // 将剩余的日志添加到新创建的聚类
        const newCluster = Array.from(this.errorClusters.values())
          .find(cluster => cluster.errorCodes.includes(errorCode));

        if (newCluster) {
          for (let i = 1; i < codeLogs.length; i++) {
            this.addLogToCluster(codeLogs[i], newCluster);
          }
        }
      }
    }

    // 然后处理没有错误代码的日志 - 这些需要基于内容相似度匹配
    // 使用批处理以减少计算量
    const batchSize = 20; // 每批处理的日志数量
    for (let i = 0; i < logsWithoutCode.length; i += batchSize) {
      const batch = logsWithoutCode.slice(i, i + batchSize);

      for (const log of batch) {
        // 尝试将日志添加到现有聚类
        let addedToCluster = false;
        for (const cluster of this.errorClusters.values()) {
          if (this.isLogSimilarToCluster(log, cluster)) {
            this.addLogToCluster(log, cluster);
            addedToCluster = true;
            break;
          }
        }

        // 如果没有添加到现有聚类，创建新聚类
        if (!addedToCluster && this.errorClusters.size < this.config.maxClusters) {
          this.createNewCluster(log);
        }
      }
    }

    // 移除太小的聚类
    this.removeSmallClusters();
  }

  /**
   * 判断日志是否与聚类相似
   * @param log 日志条目
   * @param cluster 错误聚类
   */
  private isLogSimilarToCluster(log: LogEntry, cluster: ErrorCluster): boolean {
    // 如果错误代码匹配，直接认为相似
    if (log.errorCode && cluster.errorCodes.includes(log.errorCode)) {
      return true;
    }

    // 如果有样本，计算消息相似度
    if (cluster.samples.length > 0 && log.message) {
      // 计算与样本的最大相似度
      const maxSimilarity = Math.max(
        ...cluster.samples.map(sample =>
          this.calculateStringSimilarity(sample.message || '', log.message || '')
        )
      );

      return maxSimilarity >= cluster.similarityThreshold;
    }

    return false;
  }

  /**
   * 计算字符串相似度 (优化的 Jaccard 相似度)
   * @param str1 字符串1
   * @param str2 字符串2
   */
  private calculateStringSimilarity(str1: string, str2: string): number {
    // 快速长度检查 - 如果长度差异太大，直接返回低相似度
    const lengthDiff = Math.abs(str1.length - str2.length) / Math.max(str1.length, str2.length);
    if (lengthDiff > 0.5) {
      return 0.1; // 返回低相似度值
    }

    // 快速内容检查 - 如果字符串包含另一个，给予较高的初始相似度
    if (str1.includes(str2) || str2.includes(str1)) {
      return 0.8;
    }

    // 提取关键词 - 只考虑长度 > 3 的单词，减少计算量
    const words1 = new Set(
      str1.toLowerCase()
        .split(/\W+/)
        .filter(w => w.length > 3)
    );

    const words2 = new Set(
      str2.toLowerCase()
        .split(/\W+/)
        .filter(w => w.length > 3)
    );

    // 如果关键词太少，回退到完整单词集
    if (words1.size < 2 || words2.size < 2) {
      const fullWords1 = new Set(str1.toLowerCase().split(/\W+/).filter(w => w.length > 0));
      const fullWords2 = new Set(str2.toLowerCase().split(/\W+/).filter(w => w.length > 0));

      // 计算交集大小
      const intersection = new Set([...fullWords1].filter(x => fullWords2.has(x)));

      // 计算并集大小
      const union = new Set([...fullWords1, ...fullWords2]);

      // 如果并集为空，返回0
      if (union.size === 0) {
        return 0;
      }

      // 返回 Jaccard 相似度
      return intersection.size / union.size;
    }

    // 计算交集大小
    const intersection = new Set([...words1].filter(x => words2.has(x)));

    // 计算并集大小
    const union = new Set([...words1, ...words2]);

    // 如果并集为空，返回0
    if (union.size === 0) {
      return 0;
    }

    // 返回加权 Jaccard 相似度 - 给予交集更高的权重
    return (intersection.size * 1.5) / (union.size + intersection.size * 0.5);
  }

  /**
   * 将日志添加到聚类
   * @param log 日志条目
   * @param cluster 错误聚类
   */
  private addLogToCluster(log: LogEntry, cluster: ErrorCluster): void {
    // 更新聚类信息
    cluster.count++;
    cluster.lastSeen = log.timestamp;

    // 添加错误代码
    if (log.errorCode && !cluster.errorCodes.includes(log.errorCode)) {
      cluster.errorCodes.push(log.errorCode);
    }

    // 添加来源
    if (log.source && !cluster.sources.includes(log.source)) {
      cluster.sources.push(log.source);
    }

    // 添加标签
    if (log.tags) {
      for (const tag of log.tags) {
        if (!cluster.tags.includes(tag)) {
          cluster.tags.push(tag);
        }
      }
    }

    // 更新严重性
    if (log.severity) {
      const severityLevels = ['low', 'medium', 'high', 'critical'];
      const currentIndex = severityLevels.indexOf(cluster.severity);
      const newIndex = severityLevels.indexOf(log.severity as any);

      if (newIndex > currentIndex) {
        cluster.severity = log.severity as any;
      }
    }

    // 添加样本
    if (cluster.samples.length < this.config.maxSamplesPerCluster) {
      cluster.samples.push(log);
    }
  }

  /**
   * 创建新聚类
   * @param log 日志条目
   */
  private createNewCluster(log: LogEntry): void {
    const clusterId = `cluster_${Date.now()}_${Math.random().toString(36).substring(2, 9)}`;

    const cluster: ErrorCluster = {
      id: clusterId,
      name: log.errorCode || log.message?.substring(0, 50) || 'Unknown Error',
      count: 1,
      firstSeen: log.timestamp,
      lastSeen: log.timestamp,
      errorCodes: log.errorCode ? [log.errorCode] : [],
      sources: log.source ? [log.source] : [],
      tags: log.tags || [],
      severity: log.severity as any || 'medium',
      samples: [log],
      similarityThreshold: this.config.similarityThreshold
    };

    this.errorClusters.set(clusterId, cluster);
  }

  /**
   * 清理旧的聚类结果
   */
  private cleanupOldClusters(): void {
    // 如果聚类数量超过最大值，移除最旧的聚类
    if (this.errorClusters.size > this.config.maxClusters) {
      const sortedClusters = Array.from(this.errorClusters.entries())
        .sort(([, a], [, b]) => a.lastSeen - b.lastSeen);

      const clustersToRemove = sortedClusters.slice(0, sortedClusters.length - this.config.maxClusters);

      for (const [id] of clustersToRemove) {
        this.errorClusters.delete(id);
      }
    }
  }

  /**
   * 移除太小的聚类
   */
  private removeSmallClusters(): void {
    for (const [id, cluster] of this.errorClusters.entries()) {
      if (cluster.count < this.config.minClusterSize) {
        this.errorClusters.delete(id);
      }
    }
  }

  /**
   * 识别错误模式
   * @param logs 错误日志
   */
  private async recognizePatterns(logs: LogEntry[]): Promise<void> {
    // 更新现有模式的匹配计数
    for (const pattern of this.errorPatterns.values()) {
      let matchCount = 0;
      let lastSeen = pattern.lastSeen;

      for (const log of logs) {
        if (this.doesLogMatchPattern(log, pattern)) {
          matchCount++;
          if (log.timestamp > lastSeen) {
            lastSeen = log.timestamp;
          }
        }
      }

      if (matchCount > 0) {
        pattern.matchCount += matchCount;
        pattern.lastSeen = lastSeen;
      }
    }
  }

  /**
   * 判断日志是否匹配模式
   * @param log 日志条目
   * @param pattern 错误模式
   */
  private doesLogMatchPattern(log: LogEntry, pattern: ErrorPattern): boolean {
    // 如果日志没有消息或错误代码，不匹配
    if (!log.message && !log.errorCode) {
      return false;
    }

    // 检查错误代码
    if (pattern.pattern.startsWith('code:') && log.errorCode) {
      const patternCode = pattern.pattern.substring(5).trim();
      return log.errorCode === patternCode;
    }

    // 检查消息模式
    if (log.message) {
      // 移除 'code:' 前缀
      const patternText = pattern.pattern.startsWith('code:')
        ? pattern.pattern.substring(5).trim()
        : pattern.pattern;

      // 尝试作为正则表达式匹配
      try {
        const regex = new RegExp(patternText, 'i');
        return regex.test(log.message);
      } catch (e) {
        // 如果不是有效的正则表达式，作为普通字符串匹配
        return log.message.toLowerCase().includes(patternText.toLowerCase());
      }
    }

    return false;
  }

  /**
   * 检测异常
   * @param logs 错误日志
   */
  private async detectAnomalies(logs: LogEntry[]): Promise<{ [key: string]: AnomalyDetectionResult }> {
    const anomalies: { [key: string]: AnomalyDetectionResult } = {};

    // 如果没有足够的数据，不执行异常检测
    if (logs.length === 0) {
      return anomalies;
    }

    // 计算当前时间窗口的错误频率
    const errorFrequencies = this.calculateErrorFrequencies(logs);

    // 检测错误频率异常
    anomalies.errorFrequency = this.detectFrequencyAnomaly(errorFrequencies);

    // 检测严重性分布异常
    anomalies.severityDistribution = this.detectSeverityDistributionAnomaly(logs);

    // 检测错误源分布异常
    anomalies.sourceDistribution = this.detectSourceDistributionAnomaly(logs);

    return anomalies;
  }

  /**
   * 计算错误频率
   * @param logs 错误日志
   */
  private calculateErrorFrequencies(logs: LogEntry[]): { [key: string]: number } {
    const frequencies: { [key: string]: number } = {
      total: logs.length,
      byErrorCode: {},
      bySource: {},
      bySeverity: {
        low: 0,
        medium: 0,
        high: 0,
        critical: 0
      }
    };

    // 计算各种分布
    for (const log of logs) {
      // 按错误代码
      if (log.errorCode) {
        frequencies.byErrorCode[log.errorCode] = (frequencies.byErrorCode[log.errorCode] || 0) + 1;
      }

      // 按来源
      if (log.source) {
        frequencies.bySource[log.source] = (frequencies.bySource[log.source] || 0) + 1;
      }

      // 按严重性
      if (log.severity) {
        frequencies.bySeverity[log.severity as keyof typeof frequencies.bySeverity] += 1;
      } else {
        frequencies.bySeverity.medium += 1;
      }
    }

    return frequencies;
  }

  /**
   * 更新错误频率统计
   * @param logs 错误日志
   */
  private updateErrorFrequency(logs: LogEntry[]): void {
    // 按小时统计错误数量
    const hourlyCount = new Map<string, number>();

    for (const log of logs) {
      const hour = new Date(log.timestamp).toISOString().substring(0, 13);
      hourlyCount.set(hour, (hourlyCount.get(hour) || 0) + 1);
    }

    // 更新错误频率统计
    for (const [hour, count] of hourlyCount.entries()) {
      if (!this.errorFrequency.has(hour)) {
        this.errorFrequency.set(hour, [count]);
      } else {
        this.errorFrequency.get(hour)!.push(count);
      }
    }

    // 清理旧数据
    const now = new Date();
    const cutoff = new Date(now.getTime() - this.config.baselineWindowDays * 24 * 60 * 60 * 1000);
    const cutoffHour = cutoff.toISOString().substring(0, 13);

    for (const hour of this.errorFrequency.keys()) {
      if (hour < cutoffHour) {
        this.errorFrequency.delete(hour);
      }
    }
  }

  /**
   * 检测频率异常
   * @param currentFrequencies 当前频率
   */
  private detectFrequencyAnomaly(currentFrequencies: { [key: string]: any }): AnomalyDetectionResult {
    // 如果没有基线数据，创建基线
    if (!this.anomalyBaselines.has('errorFrequency')) {
      this.anomalyBaselines.set('errorFrequency', {
        total: currentFrequencies.total,
        timestamp: Date.now()
      });

      return {
        isAnomaly: false,
        score: 0,
        threshold: this.config.anomalyThreshold,
        reason: 'Establishing baseline'
      };
    }

    // 获取基线数据
    const baseline = this.anomalyBaselines.get('errorFrequency');

    // 如果基线太旧，更新基线
    if (Date.now() - baseline.timestamp > 24 * 60 * 60 * 1000) {
      this.anomalyBaselines.set('errorFrequency', {
        total: currentFrequencies.total,
        timestamp: Date.now()
      });

      return {
        isAnomaly: false,
        score: 0,
        threshold: this.config.anomalyThreshold,
        reason: 'Updating baseline'
      };
    }

    // 计算异常分数
    const ratio = baseline.total > 0 ? currentFrequencies.total / baseline.total : 1;
    const score = Math.abs(ratio - 1);

    // 判断是否异常
    const isAnomaly = score > this.config.anomalyThreshold;

    // 构造异常结果
    const result: AnomalyDetectionResult = {
      isAnomaly,
      score,
      threshold: this.config.anomalyThreshold,
      baseline: baseline.total,
      current: currentFrequencies.total
    };

    // 添加异常原因
    if (isAnomaly) {
      result.reason = ratio > 1
        ? `Error frequency increased by ${Math.round((ratio - 1) * 100)}%`
        : `Error frequency decreased by ${Math.round((1 - ratio) * 100)}%`;
    }

    return result;
  }

  /**
   * 检测严重性分布异常
   * @param logs 错误日志
   */
  private detectSeverityDistributionAnomaly(logs: LogEntry[]): AnomalyDetectionResult {
    // 计算严重性分布
    const severityCounts = {
      low: 0,
      medium: 0,
      high: 0,
      critical: 0
    };

    for (const log of logs) {
      if (log.severity) {
        severityCounts[log.severity as keyof typeof severityCounts] += 1;
      } else {
        severityCounts.medium += 1;
      }
    }

    // 计算高严重性错误比例
    const totalErrors = logs.length;
    const highSeverityErrors = severityCounts.high + severityCounts.critical;
    const highSeverityRatio = totalErrors > 0 ? highSeverityErrors / totalErrors : 0;

    // 判断是否异常
    const isAnomaly = highSeverityRatio > 0.3; // 如果高严重性错误超过30%，认为是异常

    // 构造异常结果
    const result: AnomalyDetectionResult = {
      isAnomaly,
      score: highSeverityRatio,
      threshold: 0.3,
      current: severityCounts
    };

    // 添加异常原因
    if (isAnomaly) {
      result.reason = `High severity errors (${Math.round(highSeverityRatio * 100)}%) exceed threshold (30%)`;
    }

    return result;
  }

  /**
   * 检测错误源分布异常
   * @param logs 错误日志
   */
  private detectSourceDistributionAnomaly(logs: LogEntry[]): AnomalyDetectionResult {
    // 计算错误源分布
    const sourceCounts: { [key: string]: number } = {};

    for (const log of logs) {
      if (log.source) {
        sourceCounts[log.source] = (sourceCounts[log.source] || 0) + 1;
      }
    }

    // 找出最多错误的源
    let maxSource = '';
    let maxCount = 0;

    for (const [source, count] of Object.entries(sourceCounts)) {
      if (count > maxCount) {
        maxSource = source;
        maxCount = count;
      }
    }

    // 计算最多错误源的比例
    const totalErrors = logs.length;
    const maxSourceRatio = totalErrors > 0 ? maxCount / totalErrors : 0;

    // 判断是否异常
    const isAnomaly = maxSourceRatio > 0.7; // 如果单一源的错误超过70%，认为是异常

    // 构造异常结果
    const result: AnomalyDetectionResult = {
      isAnomaly,
      score: maxSourceRatio,
      threshold: 0.7,
      current: sourceCounts
    };

    // 添加异常原因
    if (isAnomaly && maxSource) {
      result.reason = `Source "${maxSource}" accounts for ${Math.round(maxSourceRatio * 100)}% of errors`;
    }

    return result;
  }

  /**
   * 初始化预定义错误模式
   */
  private initializeErrorPatterns(): void {
    const now = Date.now();

    const patterns: ErrorPattern[] = [
      {
        id: 'pattern_rate_limit',
        name: 'Rate Limit Exceeded',
        pattern: 'code:RATE_LIMIT_EXCEEDED',
        description: 'User has exceeded the rate limit for API requests',
        severity: 'medium',
        suggestedAction: 'Implement exponential backoff or increase rate limits',
        autoFixAvailable: true,
        matchCount: 0,
        firstSeen: now,
        lastSeen: now
      },
      {
        id: 'pattern_captcha_failed',
        name: 'CAPTCHA Verification Failed',
        pattern: 'code:CAPTCHA_VERIFICATION_FAILED',
        description: 'User failed to complete CAPTCHA verification',
        severity: 'medium',
        suggestedAction: 'Check CAPTCHA configuration or consider using a different CAPTCHA provider',
        autoFixAvailable: false,
        matchCount: 0,
        firstSeen: now,
        lastSeen: now
      },
      {
        id: 'pattern_honeypot',
        name: 'Honeypot Triggered',
        pattern: 'code:HONEYPOT_TRIGGERED',
        description: 'Bot detection honeypot field was filled',
        severity: 'high',
        suggestedAction: 'Block IP address or implement additional bot detection measures',
        autoFixAvailable: true,
        matchCount: 0,
        firstSeen: now,
        lastSeen: now
      },
      {
        id: 'pattern_suspicious',
        name: 'Suspicious Activity',
        pattern: 'code:SUSPICIOUS_ACTIVITY',
        description: 'Suspicious user behavior detected',
        severity: 'high',
        suggestedAction: 'Review user activity and consider implementing additional verification steps',
        autoFixAvailable: false,
        matchCount: 0,
        firstSeen: now,
        lastSeen: now
      },
      {
        id: 'pattern_storage_error',
        name: 'Storage Error',
        pattern: 'code:STORAGE_ERROR',
        description: 'Error accessing storage service',
        severity: 'high',
        suggestedAction: 'Check storage service configuration and connectivity',
        autoFixAvailable: true,
        matchCount: 0,
        firstSeen: now,
        lastSeen: now
      },
      {
        id: 'pattern_validation_error',
        name: 'Validation Error',
        pattern: 'code:VALIDATION_ERROR',
        description: 'Input validation failed',
        severity: 'low',
        suggestedAction: 'Review input validation rules and improve client-side validation',
        autoFixAvailable: false,
        matchCount: 0,
        firstSeen: now,
        lastSeen: now
      },
      {
        id: 'pattern_configuration_error',
        name: 'Configuration Error',
        pattern: 'code:CONFIGURATION_ERROR',
        description: 'System configuration error',
        severity: 'critical',
        suggestedAction: 'Review system configuration and fix invalid settings',
        autoFixAvailable: true,
        matchCount: 0,
        firstSeen: now,
        lastSeen: now
      }
    ];

    // 添加到错误模式映射
    for (const pattern of patterns) {
      this.errorPatterns.set(pattern.id, pattern);
    }
  }
}
