import React from 'react';
import { Permission, hasPermission, hasAnyPermission, hasAllPermissions } from '@/lib/permissions';
import { useAuth } from '@/hooks/useAuth';

interface PermissionCheckProps {
  /**
   * 需要检查的权限
   */
  permission?: Permission;
  
  /**
   * 需要检查的权限列表（任意一个）
   */
  anyPermissions?: Permission[];
  
  /**
   * 需要检查的权限列表（所有）
   */
  allPermissions?: Permission[];
  
  /**
   * 当用户拥有权限时渲染的内容
   */
  children: React.ReactNode;
  
  /**
   * 当用户没有权限时渲染的内容
   */
  fallback?: React.ReactNode;
}

/**
 * 权限检查组件
 * 
 * 根据用户权限决定是否渲染子组件
 */
export default function PermissionCheck({
  permission,
  anyPermissions,
  allPermissions,
  children,
  fallback = null
}: PermissionCheckProps) {
  const { user } = useAuth();
  
  if (!user || !user.role) {
    return <>{fallback}</>;
  }
  
  // 检查单个权限
  if (permission && !hasPermission(user.role, permission)) {
    return <>{fallback}</>;
  }
  
  // 检查任意权限
  if (anyPermissions && !hasAnyPermission(user.role, anyPermissions)) {
    return <>{fallback}</>;
  }
  
  // 检查所有权限
  if (allPermissions && !hasAllPermissions(user.role, allPermissions)) {
    return <>{fallback}</>;
  }
  
  return <>{children}</>;
}
