import React, { useState, useEffect, useRef, useCallback, useMemo } from 'react';
import { FixedSizeList as List, VariableSizeList, ListChildComponentProps } from 'react-window';
import { FixedSizeGrid as Grid } from 'react-window';
import InfiniteLoader from 'react-window-infinite-loader';
import { Skeleton } from '@/components/ui/skeleton';
import { Card } from '@/components/ui/card';

export interface VirtualScrollItem {
  id: string | number;
  height?: number;
  data?: any;
}

export interface VirtualScrollProps {
  items: VirtualScrollItem[];
  itemHeight?: number;
  containerHeight?: number;
  containerWidth?: string | number;
  hasNextPage?: boolean;
  isNextPageLoading?: boolean;
  loadNextPage?: () => Promise<void>;
  renderItem: (props: {
    item: VirtualScrollItem;
    index: number;
    style: React.CSSProperties;
    isScrolling?: boolean;
  }) => React.ReactNode;
  onItemsRendered?: (props: {
    visibleStartIndex: number;
    visibleStopIndex: number;
    overscanStartIndex: number;
    overscanStopIndex: number;
  }) => void;
  overscanCount?: number;
  threshold?: number;
  className?: string;
  emptyComponent?: React.ReactNode;
  loadingComponent?: React.ReactNode;
  errorComponent?: React.ReactNode;
  variableHeight?: boolean;
  estimatedItemSize?: number;
  lazy?: boolean; // 懒加载支持
}

/**
 * 高性能虚拟滚动组件
 *
 * 特性：
 * - 支持固定和可变高度
 * - 无限滚动加载
 * - 性能优化的渲染
 * - 自适应容器大小
 * - 滚动状态管理
 */
const VirtualScroll: React.FC<VirtualScrollProps> = ({
  items,
  itemHeight = 80,
  containerHeight = 600,
  containerWidth = '100%',
  hasNextPage = false,
  isNextPageLoading = false,
  loadNextPage,
  renderItem,
  onItemsRendered,
  overscanCount = 5,
  threshold = 15,
  className = '',
  emptyComponent,
  loadingComponent,
  errorComponent,
  variableHeight = false,
  estimatedItemSize = 80
}) => {
  const [isScrolling, setIsScrolling] = useState(false);
  const [scrollDirection, setScrollDirection] = useState<'forward' | 'backward'>('forward');
  const listRef = useRef<any>(null);
  const scrollTimeoutRef = useRef<NodeJS.Timeout>();
  const lastScrollTop = useRef(0);

  // 计算总项目数（包括加载占位符）
  const itemCount = hasNextPage ? items.length + 1 : items.length;

  // 检查项目是否已加载
  const isItemLoaded = useCallback((index: number) => {
    return !!items[index];
  }, [items]);

  // 处理滚动状态
  const handleScroll = useCallback(({ scrollTop }: { scrollTop: number }) => {
    setIsScrolling(true);
    setScrollDirection(scrollTop > lastScrollTop.current ? 'forward' : 'backward');
    lastScrollTop.current = scrollTop;

    // 清除之前的超时
    if (scrollTimeoutRef.current) {
      clearTimeout(scrollTimeoutRef.current);
    }

    // 设置滚动结束超时
    scrollTimeoutRef.current = setTimeout(() => {
      setIsScrolling(false);
    }, 150);
  }, []);

  // 获取项目高度（用于可变高度）
  const getItemSize = useCallback((index: number) => {
    const item = items[index];
    return item?.height || estimatedItemSize;
  }, [items, estimatedItemSize]);

  // 渲染单个项目
  const renderListItem = useCallback(({ index, style }: ListChildComponentProps) => {
    const item = items[index];

    // 如果项目未加载，显示加载占位符
    if (!item) {
      return (
        <div style={style} className="p-4">
          {loadingComponent || (
            <Card className="p-4">
              <Skeleton className="h-4 w-3/4 mb-2" />
              <Skeleton className="h-4 w-1/2 mb-2" />
              <Skeleton className="h-4 w-2/3" />
            </Card>
          )}
        </div>
      );
    }

    return renderItem({
      item,
      index,
      style,
      isScrolling
    });
  }, [items, renderItem, isScrolling, loadingComponent]);

  // 处理项目渲染回调
  const handleItemsRendered = useCallback((props: any) => {
    onItemsRendered?.(props);
  }, [onItemsRendered]);

  // 清理超时
  useEffect(() => {
    return () => {
      if (scrollTimeoutRef.current) {
        clearTimeout(scrollTimeoutRef.current);
      }
    };
  }, []);

  // 空状态处理
  if (items.length === 0 && !isNextPageLoading) {
    return (
      <div className={`flex items-center justify-center ${className}`} style={{ height: containerHeight }}>
        {emptyComponent || (
          <div className="text-center text-gray-500">
            <p className="text-lg mb-2">暂无数据</p>
            <p className="text-sm">没有找到任何项目</p>
          </div>
        )}
      </div>
    );
  }

  // 如果支持无限加载 - infinite无限滚动支持
  if (loadNextPage) {
    return (
      <div className={className}>
        <InfiniteLoader
          isItemLoaded={isItemLoaded}
          itemCount={itemCount}
          loadMoreItems={loadNextPage}
          threshold={threshold}
        >
          {({ onItemsRendered: onInfiniteItemsRendered, ref }) => {
            if (variableHeight) {
              return (
                <VariableSizeList
                  ref={(list) => {
                    ref(list);
                    listRef.current = list;
                  }}
                  height={containerHeight}
                  width={containerWidth}
                  itemCount={itemCount}
                  itemSize={getItemSize}
                  onItemsRendered={onInfiniteItemsRendered}
                  onScroll={handleScroll}
                  overscanCount={overscanCount}
                  estimatedItemSize={estimatedItemSize}
                >
                  {renderListItem}
                </VariableSizeList>
              );
            } else {
              return (
                <List
                  ref={(list) => {
                    ref(list);
                    listRef.current = list;
                  }}
                  height={containerHeight}
                  width={containerWidth}
                  itemCount={itemCount}
                  itemSize={itemHeight}
                  onItemsRendered={onInfiniteItemsRendered}
                  onScroll={handleScroll}
                  overscanCount={overscanCount}
                >
                  {renderListItem}
                </List>
              );
            }
          }}
        </InfiniteLoader>
      </div>
    );
  }

  // 普通虚拟滚动
  if (variableHeight) {
    return (
      <div className={className}>
        <VariableSizeList
          ref={listRef}
          height={containerHeight}
          width={containerWidth}
          itemCount={items.length}
          itemSize={getItemSize}
          onItemsRendered={handleItemsRendered}
          onScroll={handleScroll}
          overscanCount={overscanCount}
          estimatedItemSize={estimatedItemSize}
        >
          {renderListItem}
        </VariableSizeList>
      </div>
    );
  }

  return (
    <div className={className}>
      <List
        ref={listRef}
        height={containerHeight}
        width={containerWidth}
        itemCount={items.length}
        itemSize={itemHeight}
        onItemsRendered={handleItemsRendered}
        onScroll={handleScroll}
        overscanCount={overscanCount}
      >
        {renderListItem}
      </List>
    </div>
  );
};

// 虚拟网格组件
export interface VirtualGridProps {
  items: VirtualScrollItem[];
  columnCount: number;
  rowHeight: number;
  columnWidth: number;
  containerHeight: number;
  containerWidth?: string | number;
  renderItem: (props: {
    item: VirtualScrollItem;
    rowIndex: number;
    columnIndex: number;
    style: React.CSSProperties;
  }) => React.ReactNode;
  className?: string;
}

export const VirtualGrid: React.FC<VirtualGridProps> = ({
  items,
  columnCount,
  rowHeight,
  columnWidth,
  containerHeight,
  containerWidth = '100%',
  renderItem,
  className = ''
}) => {
  const rowCount = Math.ceil(items.length / columnCount);

  const renderGridItem = useCallback(({ columnIndex, rowIndex, style }: any) => {
    const itemIndex = rowIndex * columnCount + columnIndex;
    const item = items[itemIndex];

    if (!item) {
      return <div style={style} />;
    }

    return renderItem({
      item,
      rowIndex,
      columnIndex,
      style
    });
  }, [items, columnCount, renderItem]);

  return (
    <div className={className}>
      <Grid
        height={containerHeight}
        width={containerWidth}
        columnCount={columnCount}
        columnWidth={columnWidth}
        rowCount={rowCount}
        rowHeight={rowHeight}
      >
        {renderGridItem}
      </Grid>
    </div>
  );
};

// 虚拟表格组件
export interface VirtualTableColumn {
  key: string;
  title: string;
  width: number;
  render?: (value: any, item: VirtualScrollItem, index: number) => React.ReactNode;
}

export interface VirtualTableProps {
  items: VirtualScrollItem[];
  columns: VirtualTableColumn[];
  rowHeight?: number;
  containerHeight?: number;
  containerWidth?: string | number;
  headerHeight?: number;
  className?: string;
  onRowClick?: (item: VirtualScrollItem, index: number) => void;
}

export const VirtualTable: React.FC<VirtualTableProps> = ({
  items,
  columns,
  rowHeight = 50,
  containerHeight = 600,
  containerWidth = '100%',
  headerHeight = 40,
  className = '',
  onRowClick
}) => {
  const totalWidth = columns.reduce((sum, col) => sum + col.width, 0);

  const renderTableRow = useCallback(({ index, style }: ListChildComponentProps) => {
    const item = items[index];

    return (
      <div
        style={style}
        className={`flex border-b hover:bg-gray-50 ${onRowClick ? 'cursor-pointer' : ''}`}
        onClick={() => onRowClick?.(item, index)}
      >
        {columns.map((column) => (
          <div
            key={column.key}
            className="flex items-center px-4 py-2 border-r"
            style={{ width: column.width, minWidth: column.width }}
          >
            {column.render
              ? column.render(item.data?.[column.key], item, index)
              : item.data?.[column.key]
            }
          </div>
        ))}
      </div>
    );
  }, [items, columns, onRowClick]);

  return (
    <div className={className}>
      {/* 表头 */}
      <div
        className="flex bg-gray-100 border-b font-medium"
        style={{ height: headerHeight, width: totalWidth }}
      >
        {columns.map((column) => (
          <div
            key={column.key}
            className="flex items-center px-4 py-2 border-r"
            style={{ width: column.width, minWidth: column.width }}
          >
            {column.title}
          </div>
        ))}
      </div>

      {/* 表体 */}
      <List
        height={containerHeight - headerHeight}
        width={containerWidth}
        itemCount={items.length}
        itemSize={rowHeight}
        style={{ width: totalWidth }}
      >
        {renderTableRow}
      </List>
    </div>
  );
};

export default VirtualScroll;
