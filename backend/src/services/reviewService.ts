/**
 * 内容审核服务
 */

import { PrismaClient } from '@prisma/client';
import { DeidentificationService } from './deidentificationService';
import { ReviewAssignmentService } from './reviewAssignmentService';
import { filterSensitiveWords } from '../utils/sensitiveWordFilter';
import { SecurityModule } from '../security';
import { Env } from '../types';
import { getPrismaClient } from '../utils/prisma';

// 审核等级枚举
export enum ReviewLevel {
  LOW = 'low',       // 低级别：所有内容直接入库
  MEDIUM = 'medium', // 中级别：系统判定为敏感/可疑的需审核
  HIGH = 'high'      // 高级别：全部提交内容都需审核
}

// 内容类型枚举
export enum ContentType {
  QUESTIONNAIRE = 'questionnaire',
  STORY = 'story'
}

// 审核状态枚举
export enum ReviewStatus {
  PENDING = 'pending',
  APPROVED = 'approved',
  REJECTED = 'rejected',
  EDITED = 'edited'
}

// 审核操作枚举
export enum ReviewAction {
  APPROVE = 'approve',
  EDIT = 'edit',
  REJECT = 'reject'
}

// 审核服务类
export class ReviewService {
  private static instance: ReviewService;
  private reviewLevel: ReviewLevel = ReviewLevel.MEDIUM; // 默认中级别
  private initialized: boolean = false;

  /**
   * 获取单例实例
   */
  public static getInstance(): ReviewService {
    if (!ReviewService.instance) {
      ReviewService.instance = new ReviewService();
    }
    return ReviewService.instance;
  }

  /**
   * 初始化审核服务
   * @param level 审核等级
   */
  public async initialize(level?: ReviewLevel): Promise<void> {
    if (this.initialized) {
      return;
    }

    // 如果提供了审核等级，使用提供的等级
    if (level) {
      this.reviewLevel = level;
    } else {
      // 否则从数据库加载审核等级
      try {
        const prisma = new PrismaClient();
        const setting = await prisma.systemSetting.findUnique({
          where: { key: 'review_level' }
        });

        if (setting) {
          this.reviewLevel = setting.value as ReviewLevel;
        }

        await prisma.$disconnect();
      } catch (error) {
        console.error('Error loading review level from database:', error);
      }
    }

    this.initialized = true;
  }

  /**
   * 获取当前审核等级
   */
  public getReviewLevel(): ReviewLevel {
    return this.reviewLevel;
  }

  /**
   * 设置审核等级
   * @param level 审核等级
   * @param userId 更新者ID
   */
  public async setReviewLevel(level: ReviewLevel, userId: string): Promise<void> {
    try {
      const prisma = new PrismaClient();

      // 更新数据库中的审核等级
      await prisma.systemSetting.upsert({
        where: { key: 'review_level' },
        update: {
          value: level,
          updatedBy: userId,
          updatedAt: new Date()
        },
        create: {
          key: 'review_level',
          value: level,
          description: '内容审核等级',
          updatedBy: userId
        }
      });

      // 更新内存中的审核等级
      this.reviewLevel = level;

      await prisma.$disconnect();
    } catch (error) {
      console.error('Error setting review level:', error);
      throw error;
    }
  }

  /**
   * 处理内容提交
   * @param content 内容
   * @param type 内容类型
   * @param env 环境变量
   */
  public async processContent(content: any, type: ContentType, env: Env): Promise<any> {
    // 确保审核服务已初始化
    if (!this.initialized) {
      await this.initialize();
    }

    // 生成序列号
    const sequenceNumber = await this.generateSequenceNumber(type);

    // 获取客户端IP
    const clientIp = content.ipAddress || 'unknown';
    const userAgent = content.userAgent || 'unknown';

    // 检查内容是否可疑
    const isSuspicious = await this.checkIfSuspicious(content, type, env);

    // 根据审核等级决定处理方式
    switch (this.reviewLevel) {
      case ReviewLevel.HIGH:
        // 高级别：所有内容都需要审核
        return await this.createPendingContent(content, type, sequenceNumber, clientIp, userAgent, isSuspicious.flags);

      case ReviewLevel.MEDIUM:
        // 中级别：可疑内容需要审核，其他直接入库
        if (isSuspicious.suspicious) {
          return await this.createPendingContent(content, type, sequenceNumber, clientIp, userAgent, isSuspicious.flags);
        } else {
          return await this.approveContent(content, type, env);
        }

      case ReviewLevel.LOW:
        // 低级别：所有内容直接入库
        return await this.approveContent(content, type, env);

      default:
        throw new Error(`Unknown review level: ${this.reviewLevel}`);
    }
  }

  /**
   * 创建待审核内容
   * @param content 内容
   * @param type 内容类型
   * @param sequenceNumber 序列号
   * @param clientIp 客户端IP
   * @param userAgent 用户代理
   * @param flags 标记
   */
  private async createPendingContent(
    content: any,
    type: ContentType,
    sequenceNumber: string,
    clientIp: string,
    userAgent: string,
    flags: string[] = []
  ): Promise<any> {
    try {
      const prisma = new PrismaClient();

      // 脱敏内容
      const sanitizedContent = await this.sanitizeContent(content, type);

      // 计算优先级
      const priority = this.calculatePriority(flags, content);

      // 创建待审核内容
      const pendingContent = await prisma.pendingContent.create({
        data: {
          sequenceNumber,
          type,
          originalContent: content,
          sanitizedContent,
          status: ReviewStatus.PENDING,
          originIp: clientIp,
          userAgent,
          flags,
          priority
        }
      });

      // 自动分配给审核员
      let assignedTo = null;
      try {
        // 获取审核分配服务实例
        const assignmentService = ReviewAssignmentService.getInstance();

        // 确保审核分配服务已初始化
        if (!assignmentService.isInitialized) {
          await assignmentService.initialize();
        }

        // 分配内容
        assignedTo = await assignmentService.assignContent(pendingContent.id);

        if (assignedTo) {
          console.log(`Content ${pendingContent.id} assigned to reviewer ${assignedTo}`);
        } else {
          console.log(`No available reviewer to assign content ${pendingContent.id}`);
        }
      } catch (assignError) {
        console.error('Error assigning content to reviewer:', assignError);
        // 分配失败不影响内容创建
      }

      await prisma.$disconnect();

      return {
        success: true,
        message: '内容已提交，等待审核',
        pendingId: pendingContent.id,
        sequenceNumber,
        assignedTo
      };
    } catch (error) {
      console.error('Error creating pending content:', error);
      throw error;
    }
  }

  /**
   * 计算内容优先级
   * @param flags 标记
   * @param content 内容
   */
  private calculatePriority(flags: string[], content: any): number {
    let priority = 5; // 默认优先级

    // 根据标记调整优先级
    if (flags.includes('high-exposure')) {
      priority += 3;
    }

    if (flags.includes('sensitive-words')) {
      priority += 2;
    }

    if (flags.includes('suspicious-behavior')) {
      priority += 2;
    }

    // 根据内容长度调整优先级
    if (content.content && typeof content.content === 'string') {
      const contentLength = content.content.length;
      if (contentLength > 1000) {
        priority += 1; // 长内容优先级略高
      }
    }

    // 确保优先级在1-10范围内
    return Math.min(Math.max(priority, 1), 10);
  }

  /**
   * 批准内容（直接入库）
   * @param content 内容
   * @param type 内容类型
   * @param env 环境变量
   */
  private async approveContent(content: any, type: ContentType, env: Env): Promise<any> {
    try {
      const prisma = getPrismaClient(env);

      // 根据内容类型处理
      if (type === ContentType.STORY) {
        // 处理故事内容
        const story = await prisma.story.create({
          data: {
            userId: content.userId,
            isAnonymous: content.isAnonymous,
            title: content.title,
            content: content.content,
            ipAddress: content.ipAddress,
            status: 'approved'
          }
        });

        return {
          success: true,
          message: '故事已发布',
          storyId: story.id
        };
      } else if (type === ContentType.QUESTIONNAIRE) {
        // 处理问卷内容
        const response = await prisma.questionnaireResponse.create({
          data: {
            userId: content.userId,
            isAnonymous: content.isAnonymous,
            // 其他问卷字段...
          }
        });

        return {
          success: true,
          message: '问卷已提交',
          responseId: response.id
        };
      } else {
        throw new Error(`Unknown content type: ${type}`);
      }
    } catch (error) {
      console.error('Error approving content:', error);
      throw error;
    }
  }

  /**
   * 检查内容是否可疑
   * @param content 内容
   * @param type 内容类型
   * @param env 环境变量
   */
  private async checkIfSuspicious(content: any, type: ContentType, env: Env): Promise<{ suspicious: boolean, flags: string[] }> {
    const flags: string[] = [];

    // 1. 检查是否包含敏感词
    let hasSensitiveWords = false;
    if (type === ContentType.STORY) {
      const filteredTitle = await filterSensitiveWords(content.title);
      const filteredContent = await filterSensitiveWords(content.content);

      if (filteredTitle !== content.title || filteredContent !== content.content) {
        hasSensitiveWords = true;
        flags.push('sensitive-words');
      }
    } else if (type === ContentType.QUESTIONNAIRE) {
      // 检查问卷中的文本字段
      // ...
    }

    // 2. 检查AI脱敏是否标记为高暴露性内容
    let isHighExposure = false;
    if (DeidentificationService.isInitialized()) {
      const config = DeidentificationService.getConfig();

      if (config.enabled) {
        // 对内容进行脱敏检查
        let contentToCheck = '';

        if (type === ContentType.STORY) {
          contentToCheck = `${content.title}\n\n${content.content}`;
        } else if (type === ContentType.QUESTIONNAIRE) {
          // 构建问卷内容字符串
          // ...
        }

        const result = await DeidentificationService.checkExposureLevel(contentToCheck, type, env);

        if (result.highExposure) {
          isHighExposure = true;
          flags.push('high-exposure');
        }
      }
    }

    // 3. 检查行为分析是否标记为可疑
    let isBehaviorSuspicious = false;
    if (SecurityModule.isInitialized() && content._security) {
      const behaviorData = content._security.behavior;

      if (behaviorData && behaviorData.suspiciousScore > 50) {
        isBehaviorSuspicious = true;
        flags.push('suspicious-behavior');
      }
    }

    // 4. 检查IP/设备提交频次
    // 这部分需要访问数据库，暂时略过
    // ...

    // 综合判断
    const suspicious = hasSensitiveWords || isHighExposure || isBehaviorSuspicious;

    return { suspicious, flags };
  }

  /**
   * 生成序列号
   * @param type 内容类型
   */
  private async generateSequenceNumber(type: ContentType): Promise<string> {
    const prefix = type === ContentType.QUESTIONNAIRE ? 'Q' : 'S';
    const year = new Date().getFullYear().toString().substring(2);
    const month = (new Date().getMonth() + 1).toString().padStart(2, '0');

    try {
      const prisma = new PrismaClient();

      // 获取当前最大序列号
      const latestContent = await prisma.pendingContent.findFirst({
        where: {
          sequenceNumber: {
            startsWith: `${prefix}${year}${month}`
          }
        },
        orderBy: {
          sequenceNumber: 'desc'
        }
      });

      let sequence = 1;

      if (latestContent) {
        const latestSequence = parseInt(latestContent.sequenceNumber.substring(5));
        sequence = latestSequence + 1;
      }

      await prisma.$disconnect();

      return `${prefix}${year}${month}${sequence.toString().padStart(5, '0')}`;
    } catch (error) {
      console.error('Error generating sequence number:', error);

      // 如果出错，使用时间戳作为备用
      const timestamp = Date.now().toString().substring(6);
      return `${prefix}${year}${month}${timestamp}`;
    }
  }

  /**
   * 脱敏内容
   * @param content 内容
   * @param type 内容类型
   */
  private async sanitizeContent(content: any, type: ContentType): Promise<any> {
    // 创建内容的副本
    const sanitized = JSON.parse(JSON.stringify(content));

    // 根据内容类型处理
    if (type === ContentType.STORY) {
      // 脱敏标题和内容
      if (sanitized.title) {
        sanitized.title = await this.sanitizeText(sanitized.title);
      }

      if (sanitized.content) {
        sanitized.content = await this.sanitizeText(sanitized.content);
      }
    } else if (type === ContentType.QUESTIONNAIRE) {
      // 脱敏问卷中的文本字段
      // ...
    }

    return sanitized;
  }

  /**
   * 脱敏文本
   * @param text 文本
   */
  private async sanitizeText(text: string): Promise<string> {
    // 简单的脱敏处理，实际应用中可以使用更复杂的算法
    // 替换邮箱
    text = text.replace(/[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}/g, '[邮箱]');

    // 替换电话号码
    text = text.replace(/1[3-9]\d{9}/g, '[电话号码]');

    // 替换身份证号
    text = text.replace(/\d{17}[\dXx]/g, '[身份证号]');

    // 替换QQ号
    text = text.replace(/[1-9][0-9]{4,}/g, '[QQ号]');

    return text;
  }
}
