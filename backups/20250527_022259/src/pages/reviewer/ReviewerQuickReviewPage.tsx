import React, { useState, useEffect, useCallback, useRef } from 'react';
import { useNavigate } from 'react-router-dom';
import ReviewerLayout from '@/components/layouts/ReviewerLayout';
import { Card, CardContent, CardDescription, CardHeader, CardTitle, CardFooter } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Textarea } from '@/components/ui/textarea';
import { Progress } from '@/components/ui/progress';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from '@/components/ui/alert-dialog';
import {
  ThumbsUp, ThumbsDown, AlertTriangle, ArrowLeft, ArrowR<PERSON>,
  Clock, CheckCircle, XCircle, Loader2
} from 'lucide-react';
import { useToast } from '@/components/ui/use-toast';

// 模拟待审核内容
const mockItems = [
  {
    id: 3001,
    sequenceNumber: 'Q-2024-0101',
    title: '大学生活感悟',
    content: '大学四年转瞬即逝，我学到了很多知识，也结交了许多朋友。最重要的是，我学会了如何独立思考和解决问题。',
    type: 'story',
    createdAt: new Date(Date.now() - 3 * 24 * 60 * 60 * 1000).toISOString()
  },
  {
    id: 3002,
    sequenceNumber: 'Q-2024-0102',
    title: '实习经验',
    content: '在一家互联网公司实习的三个月，让我对行业有了更深入的了解。技术能力很重要，但沟通和团队协作同样不可或缺。',
    type: 'experience',
    createdAt: new Date(Date.now() - 2 * 24 * 60 * 60 * 1000).toISOString()
  },
  {
    id: 3003,
    sequenceNumber: 'Q-2024-0103',
    title: '求职建议',
    content: '找工作时，不要只关注薪资，也要考虑公司文化、发展空间和工作内容是否符合自己的兴趣和职业规划。',
    type: 'advice',
    createdAt: new Date(Date.now() - 1 * 24 * 60 * 60 * 1000).toISOString()
  },
  {
    id: 3004,
    sequenceNumber: 'Q-2024-0104',
    title: '考研心得',
    content: '考研需要长期的坚持和科学的规划。我每天保持8小时的学习时间，定期复习和模拟考试，最终成功考入了理想的学校。',
    type: 'story',
    createdAt: new Date(Date.now() - 12 * 60 * 60 * 1000).toISOString()
  },
  {
    id: 3005,
    sequenceNumber: 'Q-2024-0105',
    title: '职场适应',
    content: '刚进入职场时，要主动学习和请教，不懂就问，多观察优秀同事的工作方法，快速适应新环境。',
    type: 'advice',
    createdAt: new Date(Date.now() - 6 * 60 * 60 * 1000).toISOString()
  }
];

/**
 * 审核员快速审核页面
 *
 * 提供卡片式界面和键盘快捷键，实现高效审核
 */
const ReviewerQuickReviewPage: React.FC = () => {
  const { toast } = useToast();
  const navigate = useNavigate();
  const [items, setItems] = useState<any[]>([]);
  const [currentIndex, setCurrentIndex] = useState(0);
  const [isLoading, setIsLoading] = useState(true);
  const [rejectDialogOpen, setRejectDialogOpen] = useState(false);
  const [rejectReason, setRejectReason] = useState('');
  const [reviewStats, setReviewStats] = useState({
    approved: 0,
    rejected: 0,
    total: 0,
    averageTime: 0
  });
  const [reviewStartTime, setReviewStartTime] = useState<number | null>(null);

  // 加载数据
  useEffect(() => {
    const loadData = async () => {
      setIsLoading(true);

      // 模拟网络延迟
      await new Promise(resolve => setTimeout(resolve, 1000));

      setItems(mockItems);
      setReviewStats({
        approved: 0,
        rejected: 0,
        total: mockItems.length,
        averageTime: 0
      });

      setIsLoading(false);
      setReviewStartTime(Date.now());
    };

    loadData();
  }, []);

  // 获取当前项
  const currentItem = items[currentIndex];

  // 处理下一项
  const handleNext = useCallback(() => {
    if (currentIndex < items.length - 1) {
      setCurrentIndex(currentIndex + 1);
      setReviewStartTime(Date.now());
    } else {
      toast({
        title: '审核完成',
        description: '所有内容已审核完毕',
      });
    }
  }, [currentIndex, items.length, toast]);

  // 处理上一项
  const handlePrevious = useCallback(() => {
    if (currentIndex > 0) {
      setCurrentIndex(currentIndex - 1);
      setReviewStartTime(Date.now());
    }
  }, [currentIndex]);

  // 处理批准
  const handleApprove = useCallback(() => {
    if (!currentItem) return;

    // 计算审核时间
    const reviewTime = reviewStartTime ? (Date.now() - reviewStartTime) / 1000 : 0;

    // 更新统计数据
    setReviewStats(prev => ({
      ...prev,
      approved: prev.approved + 1,
      averageTime: prev.approved === 0
        ? reviewTime
        : (prev.averageTime * prev.approved + reviewTime) / (prev.approved + 1)
    }));

    // 显示提示
    toast({
      title: '已批准',
      description: `内容 "${currentItem.title}" 已批准`,
    });

    // 移除当前项并更新索引
    const newItems = [...items];
    newItems.splice(currentIndex, 1);
    setItems(newItems);

    // 如果删除的是最后一项，则索引减1
    if (currentIndex >= newItems.length) {
      setCurrentIndex(Math.max(0, newItems.length - 1));
    }

    // 重置审核开始时间
    setReviewStartTime(Date.now());
  }, [currentItem, currentIndex, items, reviewStartTime, toast]);

  // 处理拒绝
  const handleReject = useCallback(() => {
    if (!currentItem) return;
    setRejectDialogOpen(true);
  }, [currentItem]);

  // 处理拒绝确认
  const handleRejectConfirm = useCallback(() => {
    if (!currentItem) return;

    // 计算审核时间
    const reviewTime = reviewStartTime ? (Date.now() - reviewStartTime) / 1000 : 0;

    // 更新统计数据
    setReviewStats(prev => ({
      ...prev,
      rejected: prev.rejected + 1,
      averageTime: prev.rejected === 0
        ? reviewTime
        : (prev.averageTime * prev.rejected + reviewTime) / (prev.rejected + 1)
    }));

    // 显示提示
    toast({
      title: '已拒绝',
      description: `内容 "${currentItem.title}" 已拒绝`,
      variant: 'destructive',
    });

    // 移除当前项并更新索引
    const newItems = [...items];
    newItems.splice(currentIndex, 1);
    setItems(newItems);

    // 如果删除的是最后一项，则索引减1
    if (currentIndex >= newItems.length) {
      setCurrentIndex(Math.max(0, newItems.length - 1));
    }

    // 关闭对话框并重置
    setRejectDialogOpen(false);
    setRejectReason('');

    // 重置审核开始时间
    setReviewStartTime(Date.now());
  }, [currentItem, currentIndex, items, reviewStartTime, toast]);

  // 获取内容类型标签
  const getContentTypeLabel = (type: string) => {
    switch (type) {
      case 'story':
        return '故事';
      case 'experience':
        return '经验分享';
      case 'advice':
        return '求职建议';
      default:
        return '其他';
    }
  };

  // 设置键盘快捷键
  useEffect(() => {
    const handleKeyDown = (event: KeyboardEvent) => {
      switch (event.key) {
        case 'ArrowLeft':
          handlePrevious();
          break;
        case 'ArrowRight':
          handleNext();
          break;
        case 'ArrowUp':
          handleApprove();
          break;
        case 'ArrowDown':
          handleReject();
          break;
      }
    };

    window.addEventListener('keydown', handleKeyDown);
    return () => {
      window.removeEventListener('keydown', handleKeyDown);
    };
  }, [handlePrevious, handleNext, handleApprove, handleReject]);

  return (
    <ReviewerLayout>
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-2xl font-bold">快速审核</h1>
        <div className="flex items-center space-x-2">
          <Badge variant="outline" className="text-sm">
            <Clock className="h-3 w-3 mr-1" />
            平均审核时间: {reviewStats.averageTime.toFixed(1)}秒
          </Badge>
          <Badge variant="outline" className="text-sm">
            <CheckCircle className="h-3 w-3 mr-1" />
            已批准: {reviewStats.approved}
          </Badge>
          <Badge variant="outline" className="text-sm">
            <XCircle className="h-3 w-3 mr-1" />
            已拒绝: {reviewStats.rejected}
          </Badge>
        </div>
      </div>

      <div className="mb-4">
        <div className="flex justify-between text-sm text-muted-foreground mb-2">
          <span>审核进度</span>
          <span>{reviewStats.approved + reviewStats.rejected} / {reviewStats.total}</span>
        </div>
        <Progress value={(reviewStats.approved + reviewStats.rejected) / reviewStats.total * 100} />
      </div>

      {isLoading ? (
        <div className="flex justify-center items-center h-[400px]">
          <Loader2 className="h-8 w-8 animate-spin text-primary" />
          <span className="ml-2">加载中...</span>
        </div>
      ) : items.length === 0 ? (
        <Card>
          <CardContent className="flex flex-col items-center justify-center h-[400px]">
            <CheckCircle className="h-16 w-16 text-green-500 mb-4" />
            <h2 className="text-xl font-semibold mb-2">所有内容已审核完毕</h2>
            <p className="text-muted-foreground mb-4">您已完成所有待审核内容</p>
            <div className="flex space-x-4">
              <Button variant="outline" onClick={() => window.location.reload()}>
                刷新页面
              </Button>
              <Button onClick={() => navigate('/reviewer/dashboard')}>
                返回仪表盘
              </Button>
            </div>
          </CardContent>
        </Card>
      ) : (
        <div className="space-y-6">
          <div className="flex justify-center">
            <Card className="w-full max-w-3xl">
              <CardHeader>
                <div className="flex justify-between items-start">
                  <div>
                    <CardTitle>{currentItem.title}</CardTitle>
                    <CardDescription>
                      序列号: {currentItem.sequenceNumber} | 提交时间: {new Date(currentItem.createdAt).toLocaleString()}
                    </CardDescription>
                  </div>
                  <Badge variant="outline">
                    {getContentTypeLabel(currentItem.type)}
                  </Badge>
                </div>
              </CardHeader>
              <CardContent>
                <div className="border rounded-md p-4 min-h-[200px]">
                  {currentItem.content}
                </div>
              </CardContent>
              <CardFooter className="flex justify-between">
                <div className="flex space-x-2">
                  <Button
                    variant="outline"
                    onClick={handlePrevious}
                    disabled={currentIndex === 0}
                  >
                    <ArrowLeft className="mr-2 h-4 w-4" />
                    上一项
                  </Button>
                  <Button
                    variant="outline"
                    onClick={handleNext}
                    disabled={currentIndex === items.length - 1}
                  >
                    下一项
                    <ArrowRight className="ml-2 h-4 w-4" />
                  </Button>
                </div>
                <div className="flex space-x-2">
                  <Button
                    variant="outline"
                    onClick={handleReject}
                  >
                    <ThumbsDown className="mr-2 h-4 w-4" />
                    拒绝 (↓)
                  </Button>
                  <Button
                    onClick={handleApprove}
                  >
                    <ThumbsUp className="mr-2 h-4 w-4" />
                    批准 (↑)
                  </Button>
                </div>
              </CardFooter>
            </Card>
          </div>

          <Card>
            <CardHeader>
              <CardTitle>键盘快捷键</CardTitle>
              <CardDescription>
                使用键盘快捷键可以更高效地进行审核
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                <div className="flex items-center space-x-2">
                  <Badge variant="outline" className="px-2 py-1">↑</Badge>
                  <span>批准内容</span>
                </div>
                <div className="flex items-center space-x-2">
                  <Badge variant="outline" className="px-2 py-1">↓</Badge>
                  <span>拒绝内容</span>
                </div>
                <div className="flex items-center space-x-2">
                  <Badge variant="outline" className="px-2 py-1">←</Badge>
                  <span>上一项</span>
                </div>
                <div className="flex items-center space-x-2">
                  <Badge variant="outline" className="px-2 py-1">→</Badge>
                  <span>下一项</span>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      )}

      {/* 拒绝原因对话框 */}
      <AlertDialog open={rejectDialogOpen} onOpenChange={setRejectDialogOpen}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>拒绝内容</AlertDialogTitle>
            <AlertDialogDescription>
              请提供拒绝原因，这将帮助用户理解为什么他们的内容被拒绝。
            </AlertDialogDescription>
          </AlertDialogHeader>
          <div className="py-4">
            <Textarea
              placeholder="请输入拒绝原因..."
              value={rejectReason}
              onChange={(e) => setRejectReason(e.target.value)}
              className="min-h-[100px]"
            />
          </div>
          <AlertDialogFooter>
            <AlertDialogCancel>取消</AlertDialogCancel>
            <AlertDialogAction onClick={handleRejectConfirm}>确认拒绝</AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </ReviewerLayout>
  );
};

export default ReviewerQuickReviewPage;
