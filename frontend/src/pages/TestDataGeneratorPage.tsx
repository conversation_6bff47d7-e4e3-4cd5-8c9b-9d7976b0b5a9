import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Progress } from '@/components/ui/progress';
import { <PERSON><PERSON>, <PERSON>bsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { useToast } from '@/components/ui/use-toast';
import {
  TestTube,
  Database,
  Activity,
  Clock,
  CheckCircle,
  XCircle,
  AlertTriangle,
  BarChart3,
  Zap,
  RefreshCw,
  Info
} from 'lucide-react';
import { TestDataGeneratorService } from '@/services/testDataGeneratorService';

// 事件记录接口
interface TestEvent {
  id: string;
  timestamp: string;
  type: 'questionnaire' | 'story' | 'voice' | 'api_call';
  action: 'generate' | 'submit' | 'approve' | 'reject';
  status: 'success' | 'error' | 'warning';
  duration: number;
  details: {
    endpoint?: string;
    dataId?: string;
    errorMessage?: string;
    responseSize?: number;
  };
}

// 性能指标接口
interface PerformanceMetrics {
  apiResponseTime: number;
  successRate: number;
  errorRate: number;
  generationEfficiency: number;
  totalOperations: number;
}

// 数据统计接口
interface DataStats {
  questionnaires: { total: number; pending: number; approved: number };
  stories: { total: number; pending: number; approved: number };
  voices: { total: number; autoGenerated: number };
}

const TestDataGeneratorPage: React.FC = () => {
  const { toast } = useToast();

  // 环境检测
  const isAllowedTestEnvironment = () => {
    const hostname = window.location.hostname;

    // 允许的环境：
    // 1. 本地开发环境
    if (hostname.includes('localhost') || hostname.includes('127.0.0.1') || import.meta.env.DEV) {
      return true;
    }

    // 2. 测试/预发布环境
    if (hostname.includes('staging') || hostname.includes('test')) {
      return true;
    }

    // 3. Cloudflare Pages 预览环境（包含随机字符串的域名）
    if (hostname.includes('.pages.dev')) {
      return true;
    }

    // 4. 特定的测试域名（可以根据需要添加）
    const allowedTestDomains = [
      'test.college-employment-survey.com',
      'demo.college-employment-survey.com',
      // 可以添加更多测试域名
    ];

    if (allowedTestDomains.some(domain => hostname.includes(domain))) {
      return true;
    }

    return false;
  };

  // 状态管理
  const [events, setEvents] = useState<TestEvent[]>([]);
  const [metrics, setMetrics] = useState<PerformanceMetrics>({
    apiResponseTime: 0,
    successRate: 0,
    errorRate: 0,
    generationEfficiency: 0,
    totalOperations: 0
  });
  const [dataStats, setDataStats] = useState<DataStats>({
    questionnaires: { total: 0, pending: 0, approved: 0 },
    stories: { total: 0, pending: 0, approved: 0 },
    voices: { total: 0, autoGenerated: 0 }
  });
  const [isGeneratingQuestionnaire, setIsGeneratingQuestionnaire] = useState(false);
  const [isGeneratingStory, setIsGeneratingStory] = useState(false);
  const [selectedStatus, setSelectedStatus] = useState<'pending' | 'approved'>('approved');

  // 记录事件
  const recordEvent = (event: Omit<TestEvent, 'id' | 'timestamp'>) => {
    const newEvent: TestEvent = {
      id: crypto.randomUUID(),
      timestamp: new Date().toISOString(),
      ...event
    };

    setEvents(prev => [newEvent, ...prev.slice(0, 99)]); // 保留最近100条
    updateMetrics(newEvent);
  };

  // 更新性能指标
  const updateMetrics = (event: TestEvent) => {
    setMetrics(prev => {
      const newTotal = prev.totalOperations + 1;
      const successCount = events.filter(e => e.status === 'success').length + (event.status === 'success' ? 1 : 0);
      const errorCount = events.filter(e => e.status === 'error').length + (event.status === 'error' ? 1 : 0);

      return {
        apiResponseTime: event.duration,
        successRate: (successCount / newTotal) * 100,
        errorRate: (errorCount / newTotal) * 100,
        generationEfficiency: prev.generationEfficiency, // 将在实际生成时计算
        totalOperations: newTotal
      };
    });
  };

  // 生成随机问卷
  const generateQuestionnaire = async () => {
    setIsGeneratingQuestionnaire(true);
    const startTime = performance.now();

    try {
      // 调用真实API
      const result = await TestDataGeneratorService.generateAndSubmitQuestionnaire(selectedStatus);

      const duration = performance.now() - startTime;

      if (result.success) {
        recordEvent({
          type: 'questionnaire',
          action: 'generate',
          status: 'success',
          duration,
          details: {
            endpoint: '/api/questionnaire/submit',
            dataId: result.data?.id || `test_q_${Date.now()}`,
            responseSize: JSON.stringify(result.data || {}).length
          }
        });

        // 更新数据统计
        setDataStats(prev => ({
          ...prev,
          questionnaires: {
            ...prev.questionnaires,
            total: prev.questionnaires.total + 1,
            [selectedStatus]: prev.questionnaires[selectedStatus] + 1
          }
        }));

        // 显示成功提示
        toast({
          title: "问卷生成成功",
          description: `模拟用户流程: ${result.data?.submissionFlow || '问卷提交 -> 心声生成 -> 匿名提交'} | A+B: ${result.data?.anonymousIdentity?.identityA?.slice(-4)}***`,
          variant: "default",
        });
      } else {
        throw new Error(result.error || '提交失败');
      }

    } catch (error) {
      const duration = performance.now() - startTime;

      recordEvent({
        type: 'questionnaire',
        action: 'generate',
        status: 'error',
        duration,
        details: {
          endpoint: '/api/questionnaire/submit',
          errorMessage: error instanceof Error ? error.message : 'Unknown error'
        }
      });

      // 显示错误提示
      toast({
        title: "问卷生成失败",
        description: error instanceof Error ? error.message : '未知错误，请稍后重试',
        variant: "destructive",
      });
    } finally {
      setIsGeneratingQuestionnaire(false);
    }
  };

  // 生成随机故事
  const generateStory = async () => {
    setIsGeneratingStory(true);
    const startTime = performance.now();

    try {
      // 调用真实API
      const result = await TestDataGeneratorService.generateAndSubmitStory(selectedStatus);

      const duration = performance.now() - startTime;

      if (result.success) {
        recordEvent({
          type: 'story',
          action: 'generate',
          status: 'success',
          duration,
          details: {
            endpoint: '/api/story/submit',
            dataId: result.data?.id || `test_s_${Date.now()}`,
            responseSize: JSON.stringify(result.data || {}).length
          }
        });

        // 更新数据统计
        setDataStats(prev => ({
          ...prev,
          stories: {
            ...prev.stories,
            total: prev.stories.total + 1,
            [selectedStatus]: prev.stories[selectedStatus] + 1
          }
        }));

        // 显示成功提示
        toast({
          title: "故事生成成功",
          description: `模拟用户流程: ${result.data?.submissionFlow || '匿名注册 -> 内容创建 -> 故事发布'} | A+B: ${result.data?.anonymousIdentity?.identityA?.slice(-4)}***`,
          variant: "default",
        });
      } else {
        throw new Error(result.error || '提交失败');
      }

    } catch (error) {
      const duration = performance.now() - startTime;

      recordEvent({
        type: 'story',
        action: 'generate',
        status: 'error',
        duration,
        details: {
          endpoint: '/api/story/submit',
          errorMessage: error instanceof Error ? error.message : 'Unknown error'
        }
      });

      // 显示错误提示
      toast({
        title: "故事生成失败",
        description: error instanceof Error ? error.message : '未知错误，请稍后重试',
        variant: "destructive",
      });
    } finally {
      setIsGeneratingStory(false);
    }
  };

  // 格式化时间
  const formatTime = (timestamp: string) => {
    return new Date(timestamp).toLocaleTimeString();
  };

  // 获取状态图标
  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'success':
        return <CheckCircle className="h-4 w-4 text-green-500" />;
      case 'error':
        return <XCircle className="h-4 w-4 text-red-500" />;
      case 'warning':
        return <AlertTriangle className="h-4 w-4 text-yellow-500" />;
      default:
        return <Clock className="h-4 w-4 text-gray-500" />;
    }
  };

  return (
    <div className="container mx-auto px-4 py-8">
      {/* 页面标题 */}
      <div className="mb-8">
        <h1 className="text-3xl font-bold text-gray-900 mb-2">测试数据生成器</h1>
        <p className="text-gray-600">
          内测环境专用工具 - 模拟真实用户提交流程，生成完整测试数据
        </p>
        <div className="mt-2 text-sm text-blue-600">
          <div>• 问卷流程: 生成匿名身份 → 提交问卷 → 自动生成心声 → 完成匿名提交</div>
          <div>• 故事流程: 匿名注册UUID → 生成内容ID → 建立关联 → 发布故事</div>
        </div>
        {!isAllowedTestEnvironment() ? (
          <Alert className="mt-4" variant="destructive">
            <XCircle className="h-4 w-4" />
            <AlertDescription>
              ⚠️ 当前环境不在允许列表中！测试数据生成器已被禁用。
              <br />
              允许的环境：本地开发、staging/test 环境、*.pages.dev 预览环境
            </AlertDescription>
          </Alert>
        ) : (
          <Alert className="mt-4">
            <AlertTriangle className="h-4 w-4" />
            <AlertDescription>
              ✅ 当前环境：{window.location.hostname} - 测试数据生成器已启用
              <br />
              环境信息：DEV={String(import.meta.env.DEV)}, MODE={import.meta.env.MODE}
              <br />
              此工具仅在测试环境可用，生产环境将自动禁用
            </AlertDescription>
          </Alert>
        )}
      </div>

      {/* 业务流程说明 */}
      <Card className="mb-6">
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Info className="h-5 w-5" />
            业务流程说明
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-2 text-sm text-gray-600">
            <p><strong>问卷流程:</strong> 用户填写问卷 → 提交到后端 → 后端分配内容ID → 自动生成2条心声(各有独立内容ID) → 匿名身份(A+B)绑定所有相关内容</p>
            <p><strong>故事流程:</strong> 用户填写故事 → 提交到后端 → 后端分配内容ID → 匿名身份(A+B)绑定故事内容</p>
            <p><strong>A+B格式:</strong> A(11位数字) + B(4-6位数字)，生成唯一UUID绑定内容</p>
            <p><strong>内容ID分配:</strong> 由后端在提交时自动生成，前端不预先分配</p>
            <div className="mt-4 p-3 bg-green-50 border border-green-200 rounded-md">
              <p className="text-green-800"><strong>✅ 速率限制:</strong> 问卷和故事提交均为每分钟1次。适合测试使用，请合理安排提交频率。</p>
            </div>
          </div>
        </CardContent>
      </Card>

      <Tabs defaultValue="generator" className="space-y-6">
        <TabsList className="grid w-full grid-cols-4">
          <TabsTrigger value="generator">数据生成器</TabsTrigger>
          <TabsTrigger value="monitor">性能监控</TabsTrigger>
          <TabsTrigger value="events">事件日志</TabsTrigger>
          <TabsTrigger value="stats">数据统计</TabsTrigger>
        </TabsList>

        {/* 数据生成器标签页 */}
        <TabsContent value="generator" className="space-y-6">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {/* 问卷生成器 */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <TestTube className="h-5 w-5" />
                  问卷数据生成器
                </CardTitle>
                <CardDescription>
                  模拟真实用户提交问卷流程，包含匿名身份生成和心声自动创建
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="flex items-center gap-4">
                  <span className="text-sm font-medium">状态选择:</span>
                  <div className="flex gap-2">
                    <Button
                      variant={selectedStatus === 'pending' ? 'default' : 'outline'}
                      size="sm"
                      onClick={() => setSelectedStatus('pending')}
                    >
                      未审核
                    </Button>
                    <Button
                      variant={selectedStatus === 'approved' ? 'default' : 'outline'}
                      size="sm"
                      onClick={() => setSelectedStatus('approved')}
                    >
                      已通过
                    </Button>
                  </div>
                </div>

                <div className="flex gap-2">
                  <Button
                    onClick={generateQuestionnaire}
                    disabled={isGeneratingQuestionnaire}
                    className="flex-1"
                  >
                    {isGeneratingQuestionnaire ? (
                      <>
                        <RefreshCw className="h-4 w-4 mr-2 animate-spin" />
                        生成中...
                      </>
                    ) : (
                      '生成随机问卷'
                    )}
                  </Button>
                  <Button variant="outline" disabled={isGeneratingQuestionnaire}>
                    批量生成
                  </Button>
                </div>

                <div className="text-sm text-gray-600">
                  最近生成: {dataStats.questionnaires.total}条问卷
                </div>
              </CardContent>
            </Card>

            {/* 故事生成器 */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Database className="h-5 w-5" />
                  故事墙数据生成器
                </CardTitle>
                <CardDescription>
                  模拟真实用户发布故事流程，包含匿名注册和内容关联
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="flex items-center gap-4">
                  <span className="text-sm font-medium">状态选择:</span>
                  <div className="flex gap-2">
                    <Button
                      variant={selectedStatus === 'pending' ? 'default' : 'outline'}
                      size="sm"
                      onClick={() => setSelectedStatus('pending')}
                    >
                      未审核
                    </Button>
                    <Button
                      variant={selectedStatus === 'approved' ? 'default' : 'outline'}
                      size="sm"
                      onClick={() => setSelectedStatus('approved')}
                    >
                      已通过
                    </Button>
                  </div>
                </div>

                <div className="flex gap-2">
                  <Button
                    onClick={generateStory}
                    disabled={isGeneratingStory}
                    className="flex-1"
                  >
                    {isGeneratingStory ? (
                      <>
                        <RefreshCw className="h-4 w-4 mr-2 animate-spin" />
                        生成中...
                      </>
                    ) : (
                      '生成随机故事'
                    )}
                  </Button>
                  <Button variant="outline" disabled={isGeneratingStory}>
                    批量生成
                  </Button>
                </div>

                <div className="text-sm text-gray-600">
                  最近生成: {dataStats.stories.total}条故事
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        {/* 性能监控标签页 */}
        <TabsContent value="monitor" className="space-y-6">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            <Card>
              <CardHeader className="pb-2">
                <CardTitle className="text-sm font-medium">API响应时间</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{metrics.apiResponseTime.toFixed(0)}ms</div>
                <Progress value={Math.min(metrics.apiResponseTime / 20, 100)} className="mt-2" />
                <p className="text-xs text-gray-600 mt-1">目标: &lt;2000ms</p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="pb-2">
                <CardTitle className="text-sm font-medium">成功率</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{metrics.successRate.toFixed(1)}%</div>
                <Progress value={metrics.successRate} className="mt-2" />
                <p className="text-xs text-gray-600 mt-1">目标: &gt;95%</p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="pb-2">
                <CardTitle className="text-sm font-medium">错误率</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{metrics.errorRate.toFixed(1)}%</div>
                <Progress value={metrics.errorRate} className="mt-2" />
                <p className="text-xs text-gray-600 mt-1">目标: &lt;5%</p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="pb-2">
                <CardTitle className="text-sm font-medium">总操作数</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{metrics.totalOperations}</div>
                <div className="flex items-center mt-2">
                  <Activity className="h-4 w-4 text-green-500 mr-1" />
                  <span className="text-xs text-gray-600">实时监控</span>
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        {/* 事件日志标签页 */}
        <TabsContent value="events" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Activity className="h-5 w-5" />
                实时事件日志
              </CardTitle>
              <CardDescription>
                记录所有操作的详细信息和性能数据
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-2 max-h-96 overflow-y-auto">
                {events.length === 0 ? (
                  <div className="text-center text-gray-500 py-8">
                    暂无事件记录，开始生成数据以查看日志
                  </div>
                ) : (
                  events.map((event) => (
                    <div key={event.id} className="flex items-center gap-3 p-3 bg-gray-50 rounded-lg">
                      {getStatusIcon(event.status)}
                      <div className="flex-1">
                        <div className="flex items-center gap-2">
                          <span className="font-medium">
                            {event.type === 'questionnaire' ? '问卷' : '故事'}{event.action === 'generate' ? '生成' : '提交'}
                          </span>
                          <Badge variant={event.status === 'success' ? 'default' : 'destructive'}>
                            {event.status}
                          </Badge>
                          <span className="text-sm text-gray-500">
                            {event.duration.toFixed(0)}ms
                          </span>
                        </div>
                        <div className="text-sm text-gray-600">
                          {formatTime(event.timestamp)} - {event.details.endpoint}
                        </div>
                      </div>
                    </div>
                  ))
                )}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        {/* 数据统计标签页 */}
        <TabsContent value="stats" className="space-y-6">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <BarChart3 className="h-5 w-5" />
                  问卷数据
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-2">
                  <div className="flex justify-between">
                    <span>总数:</span>
                    <span className="font-medium">{dataStats.questionnaires.total}</span>
                  </div>
                  <div className="flex justify-between">
                    <span>待审核:</span>
                    <span className="font-medium">{dataStats.questionnaires.pending}</span>
                  </div>
                  <div className="flex justify-between">
                    <span>已通过:</span>
                    <span className="font-medium">{dataStats.questionnaires.approved}</span>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Database className="h-5 w-5" />
                  故事数据
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-2">
                  <div className="flex justify-between">
                    <span>总数:</span>
                    <span className="font-medium">{dataStats.stories.total}</span>
                  </div>
                  <div className="flex justify-between">
                    <span>待审核:</span>
                    <span className="font-medium">{dataStats.stories.pending}</span>
                  </div>
                  <div className="flex justify-between">
                    <span>已通过:</span>
                    <span className="font-medium">{dataStats.stories.approved}</span>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Zap className="h-5 w-5" />
                  心声数据
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-2">
                  <div className="flex justify-between">
                    <span>总数:</span>
                    <span className="font-medium">{dataStats.voices.total}</span>
                  </div>
                  <div className="flex justify-between">
                    <span>自动生成:</span>
                    <span className="font-medium">{dataStats.voices.autoGenerated}</span>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>
      </Tabs>
    </div>
  );
};

export default TestDataGeneratorPage;
