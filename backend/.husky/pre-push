#!/usr/bin/env sh
. "$(dirname -- "$0")/_/husky.sh"

echo "🚀 Running pre-push checks..."

# 运行完整的测试套件
echo "🧪 Running full test suite..."
npm run test:coverage

# 检查测试覆盖率
echo "📊 Checking test coverage..."
if [ -f "coverage/lcov.info" ]; then
  echo "✅ Coverage report generated"
else
  echo "⚠️  No coverage report found"
fi

# 运行代码重复检测
echo "🔍 Checking for code duplicates..."
if [ -f "scripts/detect-duplicates.js" ]; then
  node scripts/detect-duplicates.js
  if [ $? -ne 0 ]; then
    echo "⚠️  Code duplicates detected, but not blocking push"
  fi
fi

echo "✅ Pre-push checks completed!"
