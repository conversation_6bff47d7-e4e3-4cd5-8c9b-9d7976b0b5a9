/**
 * 蜜罐检测服务
 *
 * 负责检测蜜罐字段是否被填写，识别自动脚本
 */

import { HoneypotConfig, ValidationResult } from '../types';

/**
 * 蜜罐检测服务类
 */
export class HoneypotDetector {
  private config: HoneypotConfig;
  private honeypotFields: string[] = [
    'name_hp',
    'email_hp',
    'phone_hp',
    'address_hp',
    'website_hp',
    'company_hp',
    'message_hp',
    'subject_hp',
    'comment_hp',
    'username_hp'
  ];

  /**
   * 构造函数
   */
  constructor() {
    this.config = {
      enabled: false,
      fieldCount: 0,
      silentRejection: true
    };
  }

  /**
   * 初始化服务
   * @param config 蜜罐配置
   */
  public initialize(config: HoneypotConfig): void {
    this.config = { ...config };
  }

  /**
   * 更新配置
   * @param config 蜜罐配置
   */
  public updateConfig(config: HoneypotConfig): void {
    this.config = { ...this.config, ...config };
  }

  /**
   * 检查蜜罐字段
   * @param data 表单数据
   */
  public checkHoneypot(data: any): ValidationResult {
    // 如果未启用蜜罐，直接返回有效
    if (!this.config.enabled) {
      return { valid: true };
    }

    // 检查蜜罐字段是否被填写
    const filledFields = this.getFilledFields(data);

    if (filledFields.length > 0) {
      // 记录可疑活动
      console.warn('Honeypot fields filled:', filledFields);

      // 如果配置为静默拒绝，返回有效（但后端会忽略提交）
      if (this.config.silentRejection) {
        return {
          valid: true,
          silentRejection: true
        };
      }

      // 否则返回无效
      return {
        valid: false,
        error: '提交被拒绝，请重试'
      };
    }

    return { valid: true };
  }

  /**
   * 获取被填写的蜜罐字段
   * @param data 表单数据
   */
  public getFilledFields(data: any): string[] {
    if (!this.config.enabled || !data) {
      return [];
    }

    const filledFields: string[] = [];

    // 检查蜜罐字段是否被填写
    for (const field of this.honeypotFields) {
      if (data[field] && data[field].trim() !== '') {
        filledFields.push(field);
      }
    }

    return filledFields;
  }
}
