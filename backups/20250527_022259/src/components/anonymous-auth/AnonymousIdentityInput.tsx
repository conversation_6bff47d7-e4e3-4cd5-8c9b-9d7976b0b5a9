import React, { useState } from 'react';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Button } from '@/components/ui/button';
import { Checkbox } from '@/components/ui/checkbox';
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from '@/components/ui/tooltip';
import {
  Collapsible,
  CollapsibleContent,
  CollapsibleTrigger,
} from '@/components/ui/collapsible';
import { HelpCircle, ChevronDown, ChevronUp, Eye, EyeOff } from 'lucide-react';
import { cn } from '@/lib/utils';

interface AnonymousIdentityInputProps {
  onIdentityChange: (identityA: string, identityB: string, enabled: boolean) => void;
  className?: string;
}

/**
 * 匿名身份输入组件
 * 
 * 用于轻量级匿名身份验证功能，允许用户输入A（11位数字）和B（4或6位数字）
 */
export function AnonymousIdentityInput({ onIdentityChange, className }: AnonymousIdentityInputProps) {
  const [identityA, setIdentityA] = useState('');
  const [identityB, setIdentityB] = useState('');
  const [enabled, setEnabled] = useState(false);
  const [isOpen, setIsOpen] = useState(false);
  const [showB, setShowB] = useState(false);
  
  // 验证A是否有效（11位数字）
  const isValidA = /^\d{11}$/.test(identityA);
  
  // 验证B是否有效（4位或6位数字）
  const isValidB = /^\d{4}$/.test(identityB) || /^\d{6}$/.test(identityB);
  
  // 处理A值变化
  const handleAChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value.replace(/\D/g, '').slice(0, 11);
    setIdentityA(value);
    onIdentityChange(value, identityB, enabled);
  };
  
  // 处理B值变化
  const handleBChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value.replace(/\D/g, '').slice(0, 6);
    setIdentityB(value);
    onIdentityChange(identityA, value, enabled);
  };
  
  // 处理启用状态变化
  const handleEnabledChange = (checked: boolean) => {
    setEnabled(checked);
    onIdentityChange(identityA, identityB, checked);
    
    // 如果启用，自动展开
    if (checked && !isOpen) {
      setIsOpen(true);
    }
  };
  
  return (
    <div className={cn("space-y-2", className)}>
      <div className="flex items-center space-x-2">
        <Checkbox 
          id="enable-anonymous-identity" 
          checked={enabled}
          onCheckedChange={handleEnabledChange}
        />
        <Label 
          htmlFor="enable-anonymous-identity" 
          className="text-sm font-medium cursor-pointer"
        >
          启用匿名身份标识（可选）
        </Label>
        
        <TooltipProvider>
          <Tooltip>
            <TooltipTrigger asChild>
              <Button variant="ghost" size="icon" className="h-6 w-6">
                <HelpCircle className="h-4 w-4" />
              </Button>
            </TooltipTrigger>
            <TooltipContent className="max-w-xs">
              <p>匿名身份标识可以让您在保持匿名的同时管理自己的内容。</p>
              <p className="mt-1">A可以是您熟悉的11位数字（如手机号），B可以是4位或6位数字密码。</p>
              <p className="mt-1 text-yellow-500">请牢记您的A+B组合，系统不会存储原始值，忘记后将无法找回！</p>
            </TooltipContent>
          </Tooltip>
        </TooltipProvider>
      </div>
      
      <Collapsible open={isOpen} onOpenChange={setIsOpen}>
        <CollapsibleTrigger asChild>
          <Button 
            variant="ghost" 
            size="sm" 
            className="flex items-center p-0 h-6"
            disabled={!enabled}
          >
            {isOpen ? (
              <>
                <ChevronUp className="h-4 w-4 mr-1" />
                <span>隐藏详情</span>
              </>
            ) : (
              <>
                <ChevronDown className="h-4 w-4 mr-1" />
                <span>显示详情</span>
              </>
            )}
          </Button>
        </CollapsibleTrigger>
        
        <CollapsibleContent className="space-y-3 pt-2">
          <div className="space-y-1">
            <Label htmlFor="identity-a" className="text-sm">
              A值（11位数字，如手机号）
            </Label>
            <Input
              id="identity-a"
              type="text"
              inputMode="numeric"
              placeholder="请输入11位数字"
              value={identityA}
              onChange={handleAChange}
              disabled={!enabled}
              className={cn(
                identityA && !isValidA ? "border-red-500" : ""
              )}
            />
            {identityA && !isValidA && (
              <p className="text-xs text-red-500">请输入11位数字</p>
            )}
          </div>
          
          <div className="space-y-1">
            <Label htmlFor="identity-b" className="text-sm">
              B值（4位或6位数字密码）
            </Label>
            <div className="relative">
              <Input
                id="identity-b"
                type={showB ? "text" : "password"}
                inputMode="numeric"
                placeholder="请输入4位或6位数字"
                value={identityB}
                onChange={handleBChange}
                disabled={!enabled}
                className={cn(
                  identityB && !isValidB ? "border-red-500" : "",
                  "pr-10"
                )}
              />
              <Button
                type="button"
                variant="ghost"
                size="icon"
                className="absolute right-0 top-0 h-full"
                onClick={() => setShowB(!showB)}
                disabled={!enabled}
              >
                {showB ? <EyeOff className="h-4 w-4" /> : <Eye className="h-4 w-4" />}
              </Button>
            </div>
            {identityB && !isValidB && (
              <p className="text-xs text-red-500">请输入4位或6位数字</p>
            )}
          </div>
          
          <div className="text-xs text-muted-foreground">
            <p>请牢记您的A+B组合，系统不会存储原始值，忘记后将无法找回！</p>
            <p className="mt-1">启用此功能后，您可以使用相同的A+B组合查询和管理您的内容。</p>
          </div>
        </CollapsibleContent>
      </Collapsible>
    </div>
  );
}
