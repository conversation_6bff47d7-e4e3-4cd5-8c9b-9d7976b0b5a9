/**
 * API功能测试脚本
 * 验证v1.0和v2.0 API的功能正常性
 */

const axios = require('axios');

// 测试配置
const TEST_CONFIG = {
  v1_base_url: 'http://localhost:8788',
  v2_base_url: 'http://localhost:8789',
  timeout: 10000
};

// 测试结果收集
const testResults = {
  v1: { passed: 0, failed: 0, tests: [] },
  v2: { passed: 0, failed: 0, tests: [] },
  performance: { v1: [], v2: [] }
};

/**
 * 执行单个测试
 */
async function runTest(testName, testFn, version = 'v1') {
  const startTime = Date.now();
  
  try {
    const result = await testFn();
    const duration = Date.now() - startTime;
    
    testResults[version].passed++;
    testResults[version].tests.push({
      name: testName,
      status: 'PASSED',
      duration,
      result
    });
    
    testResults.performance[version].push({
      test: testName,
      duration
    });
    
    console.log(`✅ [${version.toUpperCase()}] ${testName} - ${duration}ms`);
    return { success: true, duration, result };
    
  } catch (error) {
    const duration = Date.now() - startTime;
    
    testResults[version].failed++;
    testResults[version].tests.push({
      name: testName,
      status: 'FAILED',
      duration,
      error: error.message
    });
    
    console.log(`❌ [${version.toUpperCase()}] ${testName} - ${error.message}`);
    return { success: false, duration, error: error.message };
  }
}

/**
 * V1.0 API测试
 */
const v1Tests = {
  // 健康检查
  healthCheck: async () => {
    const response = await axios.get(`${TEST_CONFIG.v1_base_url}/health`, {
      timeout: TEST_CONFIG.timeout
    });
    
    if (response.status !== 200) {
      throw new Error(`健康检查失败: ${response.status}`);
    }
    
    return { status: response.data.status, message: response.data.message };
  },

  // 数据可视化API
  visualizationData: async () => {
    const response = await axios.get(`${TEST_CONFIG.v1_base_url}/api/visualization/data`, {
      timeout: TEST_CONFIG.timeout
    });
    
    if (!response.data.success || !response.data.data) {
      throw new Error('可视化数据格式错误');
    }
    
    const data = response.data.data;
    if (typeof data.totalCount !== 'number') {
      throw new Error('totalCount字段缺失或类型错误');
    }
    
    return {
      totalCount: data.totalCount,
      hasEducationData: Array.isArray(data.educationLevel),
      hasEmploymentData: Array.isArray(data.employmentStatus)
    };
  },

  // 问卷心声API
  questionnaireVoices: async () => {
    const response = await axios.get(`${TEST_CONFIG.v1_base_url}/api/questionnaire-voices`, {
      timeout: TEST_CONFIG.timeout
    });
    
    if (!response.data.success) {
      throw new Error('问卷心声API调用失败');
    }
    
    return {
      totalCount: response.data.totalCount,
      dataCount: response.data.data?.length || 0
    };
  },

  // 故事列表API
  storyList: async () => {
    const response = await axios.get(`${TEST_CONFIG.v1_base_url}/api/story/list`, {
      timeout: TEST_CONFIG.timeout
    });
    
    if (!response.data.success) {
      throw new Error('故事列表API调用失败');
    }
    
    return {
      totalItems: response.data.totalItems,
      dataCount: response.data.data?.length || 0
    };
  }
};

/**
 * V2.0 API测试
 */
const v2Tests = {
  // 健康检查
  healthCheck: async () => {
    const response = await axios.get(`${TEST_CONFIG.v2_base_url}/health`, {
      timeout: TEST_CONFIG.timeout
    });
    
    if (response.status !== 200) {
      throw new Error(`健康检查失败: ${response.status}`);
    }
    
    return { 
      status: response.data.status, 
      message: response.data.message,
      database: response.data.database 
    };
  },

  // 数据库状态
  databaseStatus: async () => {
    const response = await axios.get(`${TEST_CONFIG.v2_base_url}/api/v2/database-status`, {
      timeout: TEST_CONFIG.timeout
    });
    
    if (!response.data.success || !response.data.data) {
      throw new Error('数据库状态API失败');
    }
    
    const tables = response.data.data.tables;
    const requiredTables = [
      'users_optimized',
      'content_metadata', 
      'questionnaire_responses_optimized',
      'questionnaire_voices'
    ];
    
    for (const table of requiredTables) {
      if (!tables[table] || typeof tables[table].count !== 'number') {
        throw new Error(`表 ${table} 数据异常`);
      }
    }
    
    return {
      version: response.data.data.version,
      tables: Object.keys(tables).length,
      totalRecords: Object.values(tables).reduce((sum, table) => sum + table.count, 0)
    };
  },

  // 问卷心声API v2.0
  questionnaireVoices: async () => {
    const response = await axios.get(`${TEST_CONFIG.v2_base_url}/api/v2/questionnaire-voices`, {
      params: { page: 1, pageSize: 10 },
      timeout: TEST_CONFIG.timeout
    });
    
    if (!response.data.success || !response.data.data) {
      throw new Error('问卷心声v2.0 API失败');
    }
    
    const data = response.data.data;
    if (!Array.isArray(data.voices) || !data.pagination) {
      throw new Error('问卷心声数据格式错误');
    }
    
    return {
      voicesCount: data.voices.length,
      totalCount: data.pagination.total,
      hasMetadata: data.voices.length > 0 && data.voices[0].metadata !== undefined
    };
  },

  // 故事列表API v2.0
  stories: async () => {
    const response = await axios.get(`${TEST_CONFIG.v2_base_url}/api/v2/stories`, {
      params: { page: 1, pageSize: 10 },
      timeout: TEST_CONFIG.timeout
    });
    
    if (!response.data.success || !response.data.data) {
      throw new Error('故事列表v2.0 API失败');
    }
    
    const data = response.data.data;
    if (!Array.isArray(data.stories) || !data.pagination) {
      throw new Error('故事数据格式错误');
    }
    
    return {
      storiesCount: data.stories.length,
      totalCount: data.pagination.total,
      hasContentPreview: data.stories.length > 0 && data.stories[0].contentPreview !== undefined
    };
  },

  // 问卷统计API v2.0
  questionnaireStats: async () => {
    const response = await axios.get(`${TEST_CONFIG.v2_base_url}/api/v2/questionnaire-stats`, {
      params: { questionId: 'qitem_education_level' },
      timeout: TEST_CONFIG.timeout
    });
    
    if (!response.data.success || !response.data.data) {
      throw new Error('问卷统计v2.0 API失败');
    }
    
    const data = response.data.data;
    if (!Array.isArray(data.options) || typeof data.totalResponses !== 'number') {
      throw new Error('统计数据格式错误');
    }
    
    return {
      questionId: data.questionId,
      totalResponses: data.totalResponses,
      optionsCount: data.options.length
    };
  }
};

/**
 * 执行所有测试
 */
async function runAllTests() {
  console.log('🚀 开始API功能测试...\n');
  
  // 测试V1.0 API
  console.log('📊 测试V1.0 API:');
  for (const [testName, testFn] of Object.entries(v1Tests)) {
    await runTest(testName, testFn, 'v1');
  }
  
  console.log('\n📊 测试V2.0 API:');
  for (const [testName, testFn] of Object.entries(v2Tests)) {
    await runTest(testName, testFn, 'v2');
  }
  
  // 生成测试报告
  generateTestReport();
}

/**
 * 生成测试报告
 */
function generateTestReport() {
  console.log('\n📋 测试报告');
  console.log('='.repeat(60));
  
  // V1.0 API结果
  console.log(`\n🔵 V1.0 API (${TEST_CONFIG.v1_base_url}):`);
  console.log(`✅ 通过: ${testResults.v1.passed}`);
  console.log(`❌ 失败: ${testResults.v1.failed}`);
  
  if (testResults.performance.v1.length > 0) {
    const avgTime = testResults.performance.v1.reduce((sum, test) => sum + test.duration, 0) / testResults.performance.v1.length;
    console.log(`⏱️  平均响应时间: ${Math.round(avgTime)}ms`);
  }
  
  // V2.0 API结果
  console.log(`\n🟢 V2.0 API (${TEST_CONFIG.v2_base_url}):`);
  console.log(`✅ 通过: ${testResults.v2.passed}`);
  console.log(`❌ 失败: ${testResults.v2.failed}`);
  
  if (testResults.performance.v2.length > 0) {
    const avgTime = testResults.performance.v2.reduce((sum, test) => sum + test.duration, 0) / testResults.performance.v2.length;
    console.log(`⏱️  平均响应时间: ${Math.round(avgTime)}ms`);
  }
  
  // 性能对比
  if (testResults.performance.v1.length > 0 && testResults.performance.v2.length > 0) {
    console.log('\n📈 性能对比:');
    
    const v1Avg = testResults.performance.v1.reduce((sum, test) => sum + test.duration, 0) / testResults.performance.v1.length;
    const v2Avg = testResults.performance.v2.reduce((sum, test) => sum + test.duration, 0) / testResults.performance.v2.length;
    
    const improvement = ((v1Avg - v2Avg) / v1Avg * 100);
    
    if (improvement > 0) {
      console.log(`🚀 V2.0比V1.0快 ${improvement.toFixed(1)}%`);
    } else {
      console.log(`⚠️  V2.0比V1.0慢 ${Math.abs(improvement).toFixed(1)}%`);
    }
  }
  
  // 失败详情
  const allFailures = [...testResults.v1.tests, ...testResults.v2.tests].filter(test => test.status === 'FAILED');
  if (allFailures.length > 0) {
    console.log('\n❌ 失败详情:');
    allFailures.forEach(test => {
      console.log(`- ${test.name}: ${test.error}`);
    });
  }
  
  // 总结
  const totalPassed = testResults.v1.passed + testResults.v2.passed;
  const totalFailed = testResults.v1.failed + testResults.v2.failed;
  const totalTests = totalPassed + totalFailed;
  
  console.log(`\n🎯 总结: ${totalPassed}/${totalTests} 测试通过 (${(totalPassed/totalTests*100).toFixed(1)}%)`);
  
  if (totalFailed === 0) {
    console.log('🎉 所有测试通过！');
  } else {
    console.log(`⚠️  ${totalFailed} 个测试失败，需要修复`);
  }
}

// 执行测试
if (require.main === module) {
  runAllTests()
    .catch(error => {
      console.error('测试执行失败:', error);
      process.exit(1);
    });
}

module.exports = { runAllTests, testResults };
