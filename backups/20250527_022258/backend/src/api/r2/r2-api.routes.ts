/**
 * R2 API 路由
 */

import { Hono } from 'hono';
import { 
  getStoryListFromR2, 
  getQuestionnaireStatsFromR2, 
  getVisualizationDataFromR2 
} from './r2-api.controller';

// Define environment interface
interface Env {
  R2_BUCKET: R2Bucket;
}

// Create Hono app
const app = new Hono<{ Bindings: Env }>();

// R2 API routes
app.get('/story/list', getStoryListFromR2);
app.get('/questionnaire/stats', getQuestionnaireStatsFromR2);
app.get('/visualization/data', getVisualizationDataFromR2);

export default app;
