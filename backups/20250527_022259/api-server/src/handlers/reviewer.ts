/**
 * 审核员管理相关处理程序
 */

import { verifyToken } from '../utils/auth';

/**
 * 处理获取审核员列表
 * @param request 请求对象
 * @param env 环境对象
 * @returns 响应对象
 */
export async function handleGetReviewers(request: Request, env: Env): Promise<Response> {
  try {
    // 验证管理员权限
    const authHeader = request.headers.get('Authorization');

    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return new Response(JSON.stringify({
        success: false,
        error: '未授权'
      }), {
        status: 401,
        headers: { 'Content-Type': 'application/json' }
      });
    }

    const token = authHeader.substring(7);
    const tokenResult = verifyToken(token, env.JWT_SECRET);

    if (!tokenResult.valid || !tokenResult.payload) {
      return new Response(JSON.stringify({
        success: false,
        error: '无效的令牌'
      }), {
        status: 401,
        headers: { 'Content-Type': 'application/json' }
      });
    }

    // 验证角色权限
    const role = tokenResult.payload.role;

    if (role !== 'admin' && role !== 'superadmin') {
      return new Response(JSON.stringify({
        success: false,
        error: '没有权限'
      }), {
        status: 403,
        headers: { 'Content-Type': 'application/json' }
      });
    }

    // 获取查询参数
    const url = new URL(request.url);
    const status = url.searchParams.get('status');
    const search = url.searchParams.get('search');
    const email = url.searchParams.get('email');
    const username = url.searchParams.get('username');
    const page = parseInt(url.searchParams.get('page') || '1', 10);
    const pageSize = parseInt(url.searchParams.get('pageSize') || '10', 10);

    // 构建查询
    let query = `
      SELECT u.*,
        COUNT(rr.id) as review_count,
        SUM(CASE WHEN rr.decision = 'approved' OR rr.decision = 'verified' THEN 1 ELSE 0 END) as approved_count,
        SUM(CASE WHEN rr.decision = 'rejected' THEN 1 ELSE 0 END) as rejected_count
      FROM users u
      LEFT JOIN review_records rr ON u.id = rr.reviewer_id
      WHERE u.role = 'reviewer'
    `;

    const params = [];

    // 添加状态过滤
    if (status) {
      query += ` AND u.status = ?`;
      params.push(status);
    }

    // 添加用户名过滤
    if (username) {
      query += ` AND u.username LIKE ?`;
      params.push(`%${username}%`);
    }

    // 添加邮箱过滤
    if (email) {
      query += ` AND u.email LIKE ?`;
      params.push(`%${email}%`);
    }

    // 添加搜索过滤
    if (search) {
      query += ` AND (u.username LIKE ? OR u.name LIKE ? OR u.email LIKE ?)`;
      params.push(`%${search}%`, `%${search}%`, `%${search}%`);
    }

    // 添加分组
    query += ` GROUP BY u.id`;

    // 添加排序
    query += ` ORDER BY u.created_at DESC`;

    // 添加分页
    query += ` LIMIT ? OFFSET ?`;
    params.push(pageSize.toString(), ((page - 1) * pageSize).toString());

    // 执行查询
    const reviewers = await env.DB.prepare(query).bind(...params).all();

    // 获取总数
    let countQuery = `
      SELECT COUNT(*) as total
      FROM users
      WHERE role = 'reviewer'
    `;

    const countParams = [];

    // 添加状态过滤
    if (status) {
      countQuery += ` AND status = ?`;
      countParams.push(status);
    }

    // 添加用户名过滤
    if (username) {
      countQuery += ` AND username LIKE ?`;
      countParams.push(`%${username}%`);
    }

    // 添加邮箱过滤
    if (email) {
      countQuery += ` AND email LIKE ?`;
      countParams.push(`%${email}%`);
    }

    // 添加搜索过滤
    if (search) {
      countQuery += ` AND (username LIKE ? OR name LIKE ? OR email LIKE ?)`;
      countParams.push(`%${search}%`, `%${search}%`, `%${search}%`);
    }

    const totalResult = await env.DB.prepare(countQuery).bind(...countParams).first();
    const total = totalResult ? (totalResult.total as number) : 0;

    // 获取审核员权限
    const reviewerIds = reviewers.results.map(reviewer => reviewer.id);
    let permissionsResult = { results: [] };

    if (reviewerIds.length > 0) {
      const permissionsQuery = `
        SELECT user_id, permission
        FROM permissions
        WHERE user_id IN (${reviewerIds.map(() => '?').join(',')})
      `;

      permissionsResult = await env.DB.prepare(permissionsQuery).bind(...reviewerIds).all();
    }

    // 组织权限数据
    const reviewerPermissions: Record<number, string[]> = {};

    for (const perm of permissionsResult.results) {
      if (!reviewerPermissions[perm.user_id]) {
        reviewerPermissions[perm.user_id] = [];
      }

      reviewerPermissions[perm.user_id].push(perm.permission);
    }

    // 组织响应数据
    const formattedReviewers = reviewers.results.map(reviewer => ({
      id: reviewer.id,
      username: reviewer.username,
      email: reviewer.email || '',
      name: reviewer.name,
      status: reviewer.status,
      lastLogin: reviewer.last_login,
      lastLoginIp: reviewer.last_login_ip || '',
      createdAt: reviewer.created_at,
      reviewCount: reviewer.review_count || 0,
      approvedCount: reviewer.approved_count || 0,
      rejectedCount: reviewer.rejected_count || 0,
      pendingCount: 0, // 暂不实现
      permissions: reviewerPermissions[reviewer.id] || []
    }));

    // 返回响应
    return new Response(JSON.stringify({
      success: true,
      reviewers: formattedReviewers,
      total,
      page,
      pageSize,
      totalPages: Math.ceil(total / pageSize)
    }), {
      status: 200,
      headers: { 'Content-Type': 'application/json' }
    });
  } catch (error) {
    console.error('获取审核员列表失败:', error);

    return new Response(JSON.stringify({
      success: false,
      error: '服务器错误'
    }), {
      status: 500,
      headers: { 'Content-Type': 'application/json' }
    });
  }
}

/**
 * 处理获取审核员详情
 * @param request 请求对象
 * @param env 环境对象
 * @param reviewerId 审核员ID
 * @returns 响应对象
 */
export async function handleGetReviewerDetail(request: Request, env: Env, reviewerId: string): Promise<Response> {
  try {
    // 验证管理员权限
    const authHeader = request.headers.get('Authorization');

    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return new Response(JSON.stringify({
        success: false,
        error: '未授权'
      }), {
        status: 401,
        headers: { 'Content-Type': 'application/json' }
      });
    }

    const token = authHeader.substring(7);
    const tokenResult = verifyToken(token, env.JWT_SECRET);

    if (!tokenResult.valid || !tokenResult.payload) {
      return new Response(JSON.stringify({
        success: false,
        error: '无效的令牌'
      }), {
        status: 401,
        headers: { 'Content-Type': 'application/json' }
      });
    }

    // 验证角色权限
    const role = tokenResult.payload.role;

    if (role !== 'admin' && role !== 'superadmin') {
      return new Response(JSON.stringify({
        success: false,
        error: '没有权限'
      }), {
        status: 403,
        headers: { 'Content-Type': 'application/json' }
      });
    }

    // 查询审核员
    const reviewer = await env.DB.prepare(`
      SELECT u.*,
        COUNT(rr.id) as review_count,
        SUM(CASE WHEN rr.decision = 'approved' OR rr.decision = 'verified' THEN 1 ELSE 0 END) as approved_count,
        SUM(CASE WHEN rr.decision = 'rejected' THEN 1 ELSE 0 END) as rejected_count
      FROM users u
      LEFT JOIN review_records rr ON u.id = rr.reviewer_id
      WHERE u.id = ? AND u.role = 'reviewer'
      GROUP BY u.id
    `).bind(reviewerId).first();

    if (!reviewer) {
      return new Response(JSON.stringify({
        success: false,
        error: '审核员不存在'
      }), {
        status: 404,
        headers: { 'Content-Type': 'application/json' }
      });
    }

    // 获取审核员权限
    const permissions = await env.DB.prepare(`
      SELECT permission
      FROM permissions
      WHERE user_id = ?
    `).bind(reviewerId).all();

    // 组织响应数据
    const formattedReviewer = {
      id: reviewer.id,
      username: reviewer.username,
      email: reviewer.email || '',
      name: reviewer.name,
      status: reviewer.status,
      lastLogin: reviewer.last_login,
      lastLoginIp: reviewer.last_login_ip || '',
      createdAt: reviewer.created_at,
      reviewCount: reviewer.review_count || 0,
      approvedCount: reviewer.approved_count || 0,
      rejectedCount: reviewer.rejected_count || 0,
      pendingCount: 0, // 暂不实现
      permissions: permissions.results.map(p => p.permission)
    };

    // 返回响应
    return new Response(JSON.stringify({
      success: true,
      reviewer: formattedReviewer
    }), {
      status: 200,
      headers: { 'Content-Type': 'application/json' }
    });
  } catch (error) {
    console.error('获取审核员详情失败:', error);

    return new Response(JSON.stringify({
      success: false,
      error: '服务器错误'
    }), {
      status: 500,
      headers: { 'Content-Type': 'application/json' }
    });
  }
}

/**
 * 处理添加审核员
 * @param request 请求对象
 * @param env 环境对象
 * @returns 响应对象
 */
export async function handleAddReviewer(request: Request, env: Env): Promise<Response> {
  try {
    // 验证管理员权限
    const authHeader = request.headers.get('Authorization');

    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return new Response(JSON.stringify({
        success: false,
        error: '未授权'
      }), {
        status: 401,
        headers: { 'Content-Type': 'application/json' }
      });
    }

    const token = authHeader.substring(7);
    const tokenResult = verifyToken(token, env.JWT_SECRET);

    if (!tokenResult.valid || !tokenResult.payload) {
      return new Response(JSON.stringify({
        success: false,
        error: '无效的令牌'
      }), {
        status: 401,
        headers: { 'Content-Type': 'application/json' }
      });
    }

    // 验证角色权限
    const role = tokenResult.payload.role;

    if (role !== 'admin' && role !== 'superadmin') {
      return new Response(JSON.stringify({
        success: false,
        error: '没有权限'
      }), {
        status: 403,
        headers: { 'Content-Type': 'application/json' }
      });
    }

    // 解析请求体
    const { username, password, name, email } = await request.json() as {
      username: string;
      password: string;
      name: string;
      email: string;
    };

    // 验证参数
    if (!username || !password || !name) {
      return new Response(JSON.stringify({
        success: false,
        error: '缺少必要参数'
      }), {
        status: 400,
        headers: { 'Content-Type': 'application/json' }
      });
    }

    // 检查用户名是否已存在
    const existingUser = await env.DB.prepare(`
      SELECT id FROM users WHERE username = ?
    `).bind(username).first();

    if (existingUser) {
      return new Response(JSON.stringify({
        success: false,
        error: '用户名已存在'
      }), {
        status: 400,
        headers: { 'Content-Type': 'application/json' }
      });
    }

    // 创建审核员
    const result = await env.DB.prepare(`
      INSERT INTO users (username, password, name, email, role, status)
      VALUES (?, ?, ?, ?, ?, ?)
    `).bind(
      username,
      password,
      name,
      email || null,
      'reviewer',
      'active'
    ).run();

    if (!result.success) {
      throw new Error('Failed to create reviewer');
    }

    // 获取新创建的审核员ID
    const reviewerId = result.meta.last_row_id;

    // 添加审核员权限
    await env.DB.prepare(`
      INSERT INTO permissions (user_id, permission)
      VALUES (?, ?)
    `).bind(reviewerId, 'review_content').run();

    // 返回响应
    return new Response(JSON.stringify({
      success: true,
      reviewerId,
      message: '审核员创建成功'
    }), {
      status: 201,
      headers: { 'Content-Type': 'application/json' }
    });
  } catch (error) {
    console.error('创建审核员失败:', error);

    return new Response(JSON.stringify({
      success: false,
      error: '服务器错误'
    }), {
      status: 500,
      headers: { 'Content-Type': 'application/json' }
    });
  }
}

/**
 * 处理更新审核员状态
 * @param request 请求对象
 * @param env 环境对象
 * @param reviewerId 审核员ID
 * @returns 响应对象
 */
export async function handleUpdateReviewerStatus(request: Request, env: Env, reviewerId: string): Promise<Response> {
  try {
    // 验证管理员权限
    const authHeader = request.headers.get('Authorization');

    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return new Response(JSON.stringify({
        success: false,
        error: '未授权'
      }), {
        status: 401,
        headers: { 'Content-Type': 'application/json' }
      });
    }

    const token = authHeader.substring(7);
    const tokenResult = verifyToken(token, env.JWT_SECRET);

    if (!tokenResult.valid || !tokenResult.payload) {
      return new Response(JSON.stringify({
        success: false,
        error: '无效的令牌'
      }), {
        status: 401,
        headers: { 'Content-Type': 'application/json' }
      });
    }

    // 验证角色权限
    const role = tokenResult.payload.role;

    if (role !== 'admin' && role !== 'superadmin') {
      return new Response(JSON.stringify({
        success: false,
        error: '没有权限'
      }), {
        status: 403,
        headers: { 'Content-Type': 'application/json' }
      });
    }

    // 解析请求体
    const { status, duration } = await request.json() as {
      status: string;
      duration?: string;
    };

    // 验证参数
    if (!status) {
      return new Response(JSON.stringify({
        success: false,
        error: '缺少必要参数'
      }), {
        status: 400,
        headers: { 'Content-Type': 'application/json' }
      });
    }

    // 验证状态
    if (status !== 'active' && status !== 'suspended' && status !== 'inactive') {
      return new Response(JSON.stringify({
        success: false,
        error: '无效的状态'
      }), {
        status: 400,
        headers: { 'Content-Type': 'application/json' }
      });
    }

    // 检查审核员是否存在
    const reviewer = await env.DB.prepare(`
      SELECT * FROM users
      WHERE id = ? AND role = 'reviewer'
    `).bind(reviewerId).first();

    if (!reviewer) {
      return new Response(JSON.stringify({
        success: false,
        error: '审核员不存在'
      }), {
        status: 404,
        headers: { 'Content-Type': 'application/json' }
      });
    }

    // 计算停用截止日期
    let suspendedUntil = null;

    if (status === 'suspended' && duration) {
      const now = new Date();

      switch (duration) {
        case '1day':
          now.setDate(now.getDate() + 1);
          break;
        case '1week':
          now.setDate(now.getDate() + 7);
          break;
        case '1month':
          now.setMonth(now.getMonth() + 1);
          break;
        case 'custom':
          // 自定义天数，暂不实现
          now.setDate(now.getDate() + 3);
          break;
      }

      suspendedUntil = now.toISOString();
    }

    // 更新审核员状态
    await env.DB.prepare(`
      UPDATE users
      SET status = ?, suspended_until = ?
      WHERE id = ?
    `).bind(status, suspendedUntil, reviewerId).run();

    // 返回响应
    return new Response(JSON.stringify({
      success: true,
      message: status === 'suspended'
        ? `审核员已停用至 ${new Date(suspendedUntil).toLocaleDateString()}`
        : '审核员状态已更新'
    }), {
      status: 200,
      headers: { 'Content-Type': 'application/json' }
    });
  } catch (error) {
    console.error('更新审核员状态失败:', error);

    return new Response(JSON.stringify({
      success: false,
      error: '服务器错误'
    }), {
      status: 500,
      headers: { 'Content-Type': 'application/json' }
    });
  }
}

/**
 * 处理删除审核员
 * @param request 请求对象
 * @param env 环境对象
 * @param reviewerId 审核员ID
 * @returns 响应对象
 */
export async function handleDeleteReviewer(request: Request, env: Env, reviewerId: string): Promise<Response> {
  try {
    // 验证管理员权限
    const authHeader = request.headers.get('Authorization');

    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return new Response(JSON.stringify({
        success: false,
        error: '未授权'
      }), {
        status: 401,
        headers: { 'Content-Type': 'application/json' }
      });
    }

    const token = authHeader.substring(7);
    const tokenResult = verifyToken(token, env.JWT_SECRET);

    if (!tokenResult.valid || !tokenResult.payload) {
      return new Response(JSON.stringify({
        success: false,
        error: '无效的令牌'
      }), {
        status: 401,
        headers: { 'Content-Type': 'application/json' }
      });
    }

    // 验证角色权限
    const role = tokenResult.payload.role;

    if (role !== 'admin' && role !== 'superadmin') {
      return new Response(JSON.stringify({
        success: false,
        error: '没有权限'
      }), {
        status: 403,
        headers: { 'Content-Type': 'application/json' }
      });
    }

    // 检查审核员是否存在
    const reviewer = await env.DB.prepare(`
      SELECT * FROM users
      WHERE id = ? AND role = 'reviewer'
    `).bind(reviewerId).first();

    if (!reviewer) {
      return new Response(JSON.stringify({
        success: false,
        error: '审核员不存在'
      }), {
        status: 404,
        headers: { 'Content-Type': 'application/json' }
      });
    }

    // 删除审核员权限
    await env.DB.prepare(`
      DELETE FROM permissions
      WHERE user_id = ?
    `).bind(reviewerId).run();

    // 删除审核员
    await env.DB.prepare(`
      DELETE FROM users
      WHERE id = ?
    `).bind(reviewerId).run();

    // 返回响应
    return new Response(JSON.stringify({
      success: true,
      message: '审核员已删除'
    }), {
      status: 200,
      headers: { 'Content-Type': 'application/json' }
    });
  } catch (error) {
    console.error('删除审核员失败:', error);

    return new Response(JSON.stringify({
      success: false,
      error: '服务器错误'
    }), {
      status: 500,
      headers: { 'Content-Type': 'application/json' }
    });
  }
}

/**
 * 处理重置审核员密码
 * @param request 请求对象
 * @param env 环境对象
 * @param reviewerId 审核员ID
 * @returns 响应对象
 */
export async function handleResetReviewerPassword(request: Request, env: Env, reviewerId: string): Promise<Response> {
  try {
    // 验证管理员权限
    const authHeader = request.headers.get('Authorization');

    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return new Response(JSON.stringify({
        success: false,
        error: '未授权'
      }), {
        status: 401,
        headers: { 'Content-Type': 'application/json' }
      });
    }

    const token = authHeader.substring(7);
    const tokenResult = verifyToken(token, env.JWT_SECRET);

    if (!tokenResult.valid || !tokenResult.payload) {
      return new Response(JSON.stringify({
        success: false,
        error: '无效的令牌'
      }), {
        status: 401,
        headers: { 'Content-Type': 'application/json' }
      });
    }

    // 验证角色权限
    const role = tokenResult.payload.role;

    if (role !== 'admin' && role !== 'superadmin') {
      return new Response(JSON.stringify({
        success: false,
        error: '没有权限'
      }), {
        status: 403,
        headers: { 'Content-Type': 'application/json' }
      });
    }

    // 检查审核员是否存在
    const reviewer = await env.DB.prepare(`
      SELECT * FROM users
      WHERE id = ? AND role = 'reviewer'
    `).bind(reviewerId).first();

    if (!reviewer) {
      return new Response(JSON.stringify({
        success: false,
        error: '审核员不存在'
      }), {
        status: 404,
        headers: { 'Content-Type': 'application/json' }
      });
    }

    // 生成新密码
    const newPassword = 'reviewer123';

    // 更新密码
    await env.DB.prepare(`
      UPDATE users
      SET password = ?
      WHERE id = ?
    `).bind(newPassword, reviewerId).run();

    // 返回响应
    return new Response(JSON.stringify({
      success: true,
      newPassword,
      message: '密码已重置'
    }), {
      status: 200,
      headers: { 'Content-Type': 'application/json' }
    });
  } catch (error) {
    console.error('重置审核员密码失败:', error);

    return new Response(JSON.stringify({
      success: false,
      error: '服务器错误'
    }), {
      status: 500,
      headers: { 'Content-Type': 'application/json' }
    });
  }
}