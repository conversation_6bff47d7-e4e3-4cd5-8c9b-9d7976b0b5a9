/**
 * API性能监控拦截器
 * 
 * 监控API调用性能并记录相关指标
 */

import PerformanceMonitoringService from '../monitoring/PerformanceMonitoringService';

// 请求计时映射
const requestTimings = new Map<string, number>();

// 生成请求ID
function generateRequestId(): string {
  return Math.random().toString(36).substring(2, 15) + Math.random().toString(36).substring(2, 15);
}

// 提取URL路径
function extractUrlPath(url: string): string {
  try {
    // 移除查询参数和哈希
    const urlObj = new URL(url);
    return urlObj.pathname;
  } catch (e) {
    // 如果不是有效URL，尝试提取路径部分
    const queryIndex = url.indexOf('?');
    const hashIndex = url.indexOf('#');
    
    if (queryIndex !== -1 && hashIndex !== -1) {
      return url.substring(0, Math.min(queryIndex, hashIndex));
    } else if (queryIndex !== -1) {
      return url.substring(0, queryIndex);
    } else if (hashIndex !== -1) {
      return url.substring(0, hashIndex);
    }
    
    return url;
  }
}

// 请求拦截器
export const requestInterceptor = (config: any) => {
  // 生成请求ID
  const requestId = generateRequestId();
  
  // 存储请求开始时间
  requestTimings.set(requestId, performance.now());
  
  // 将请求ID添加到请求配置中
  config.requestId = requestId;
  
  return config;
};

// 响应拦截器
export const responseInterceptor = (response: any) => {
  // 获取请求ID
  const requestId = response.config?.requestId;
  
  if (requestId && requestTimings.has(requestId)) {
    // 计算请求持续时间
    const startTime = requestTimings.get(requestId)!;
    const endTime = performance.now();
    const duration = endTime - startTime;
    
    // 清理请求计时
    requestTimings.delete(requestId);
    
    // 提取请求信息
    const url = extractUrlPath(response.config.url);
    const method = response.config.method?.toUpperCase() || 'GET';
    const status = response.status;
    
    // 记录API调用性能
    PerformanceMonitoringService.recordApiCall(url, method, duration, status);
  }
  
  return response;
};

// 错误拦截器
export const errorInterceptor = (error: any) => {
  // 获取请求ID
  const requestId = error.config?.requestId;
  
  if (requestId && requestTimings.has(requestId)) {
    // 计算请求持续时间
    const startTime = requestTimings.get(requestId)!;
    const endTime = performance.now();
    const duration = endTime - startTime;
    
    // 清理请求计时
    requestTimings.delete(requestId);
    
    // 提取请求信息
    const url = extractUrlPath(error.config.url);
    const method = error.config.method?.toUpperCase() || 'GET';
    const status = error.response?.status || 0;
    
    // 记录API调用性能
    PerformanceMonitoringService.recordApiCall(url, method, duration, status);
  }
  
  return Promise.reject(error);
};

// 为fetch API创建性能监控包装器
export function createFetchWithPerformanceMonitoring() {
  const originalFetch = window.fetch;
  
  window.fetch = async function(input: RequestInfo | URL, init?: RequestInit) {
    const startTime = performance.now();
    
    // 提取URL和方法
    let url: string;
    let method: string = init?.method?.toUpperCase() || 'GET';
    
    if (typeof input === 'string') {
      url = extractUrlPath(input);
    } else if (input instanceof URL) {
      url = extractUrlPath(input.toString());
    } else {
      url = extractUrlPath(input.url);
      method = input.method?.toUpperCase() || method;
    }
    
    try {
      // 执行原始fetch请求
      const response = await originalFetch(input, init);
      
      // 计算持续时间
      const endTime = performance.now();
      const duration = endTime - startTime;
      
      // 记录API调用性能
      PerformanceMonitoringService.recordApiCall(url, method, duration, response.status);
      
      return response;
    } catch (error) {
      // 计算持续时间
      const endTime = performance.now();
      const duration = endTime - startTime;
      
      // 记录API调用性能（错误）
      PerformanceMonitoringService.recordApiCall(url, method, duration, 0);
      
      throw error;
    }
  };
}

// 为XMLHttpRequest创建性能监控包装器
export function createXHRWithPerformanceMonitoring() {
  const originalXHROpen = XMLHttpRequest.prototype.open;
  const originalXHRSend = XMLHttpRequest.prototype.send;
  
  XMLHttpRequest.prototype.open = function(method: string, url: string | URL, ...args: any[]) {
    // 存储请求信息
    this.__performanceMonitoring = {
      method: method.toUpperCase(),
      url: typeof url === 'string' ? extractUrlPath(url) : extractUrlPath(url.toString()),
      startTime: 0
    };
    
    return originalXHROpen.apply(this, [method, url, ...args]);
  };
  
  XMLHttpRequest.prototype.send = function(...args: any[]) {
    if (this.__performanceMonitoring) {
      // 记录开始时间
      this.__performanceMonitoring.startTime = performance.now();
      
      // 添加加载完成事件监听器
      this.addEventListener('load', function() {
        if (this.__performanceMonitoring) {
          // 计算持续时间
          const endTime = performance.now();
          const duration = endTime - this.__performanceMonitoring.startTime;
          
          // 记录API调用性能
          PerformanceMonitoringService.recordApiCall(
            this.__performanceMonitoring.url,
            this.__performanceMonitoring.method,
            duration,
            this.status
          );
        }
      });
      
      // 添加错误事件监听器
      this.addEventListener('error', function() {
        if (this.__performanceMonitoring) {
          // 计算持续时间
          const endTime = performance.now();
          const duration = endTime - this.__performanceMonitoring.startTime;
          
          // 记录API调用性能（错误）
          PerformanceMonitoringService.recordApiCall(
            this.__performanceMonitoring.url,
            this.__performanceMonitoring.method,
            duration,
            0
          );
        }
      });
      
      // 添加超时事件监听器
      this.addEventListener('timeout', function() {
        if (this.__performanceMonitoring) {
          // 计算持续时间
          const endTime = performance.now();
          const duration = endTime - this.__performanceMonitoring.startTime;
          
          // 记录API调用性能（超时）
          PerformanceMonitoringService.recordApiCall(
            this.__performanceMonitoring.url,
            this.__performanceMonitoring.method,
            duration,
            0
          );
        }
      });
      
      // 添加中止事件监听器
      this.addEventListener('abort', function() {
        if (this.__performanceMonitoring) {
          // 计算持续时间
          const endTime = performance.now();
          const duration = endTime - this.__performanceMonitoring.startTime;
          
          // 记录API调用性能（中止）
          PerformanceMonitoringService.recordApiCall(
            this.__performanceMonitoring.url,
            this.__performanceMonitoring.method,
            duration,
            0
          );
        }
      });
    }
    
    return originalXHRSend.apply(this, args);
  };
}

// 初始化API性能监控
export function initApiPerformanceMonitoring() {
  // 为fetch API添加性能监控
  createFetchWithPerformanceMonitoring();
  
  // 为XMLHttpRequest添加性能监控
  createXHRWithPerformanceMonitoring();
  
  console.log('API性能监控已初始化');
}

export default {
  requestInterceptor,
  responseInterceptor,
  errorInterceptor,
  initApiPerformanceMonitoring
};
