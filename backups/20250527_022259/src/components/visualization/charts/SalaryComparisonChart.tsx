import { BarChart, Bar, XAxis, YAxis, CartesianGrid, Tooltip, Legend, ResponsiveContainer } from 'recharts';

interface SalaryComparisonChartProps {
  expectedData: Array<{ range: string; count: number }>;
  actualData: Array<{ range: string; count: number }>;
}

export default function SalaryComparisonChart({ 
  expectedData, 
  actualData 
}: SalaryComparisonChartProps) {
  // If no data, show placeholder
  if ((!expectedData || expectedData.length === 0) && (!actualData || actualData.length === 0)) {
    return (
      <div className="h-full flex items-center justify-center">
        <p className="text-gray-500">暂无数据</p>
      </div>
    );
  }
  
  // Combine data for comparison
  const salaryRanges = [
    '3000元以下',
    '3000-5000元',
    '5000-8000元',
    '8000-12000元',
    '12000-20000元',
    '20000元以上',
  ];
  
  // Create a map for expected data
  const expectedMap = new Map();
  expectedData?.forEach(item => {
    expectedMap.set(item.range, item.count);
  });
  
  // Create a map for actual data
  const actualMap = new Map();
  actualData?.forEach(item => {
    actualMap.set(item.range, item.count);
  });
  
  // Combine data
  const chartData = salaryRanges.map(range => ({
    range,
    expected: expectedMap.get(range) || 0,
    actual: actualMap.get(range) || 0,
  }));
  
  // Custom tooltip
  const CustomTooltip = ({ active, payload, label }: any) => {
    if (active && payload && payload.length) {
      const totalExpected = chartData.reduce((sum, item) => sum + item.expected, 0);
      const totalActual = chartData.reduce((sum, item) => sum + item.actual, 0);
      
      return (
        <div className="bg-white p-2 border rounded shadow-sm">
          <p className="font-medium">{`${label}`}</p>
          <p className="text-sm text-blue-600">{`期望: ${payload[0].value} 人 (${((payload[0].value / totalExpected) * 100).toFixed(1)}%)`}</p>
          <p className="text-sm text-green-600">{`实际: ${payload[1].value} 人 (${((payload[1].value / totalActual) * 100).toFixed(1)}%)`}</p>
        </div>
      );
    }
    return null;
  };
  
  return (
    <ResponsiveContainer width="100%" height="100%">
      <BarChart
        data={chartData}
        margin={{ top: 20, right: 30, left: 20, bottom: 5 }}
      >
        <CartesianGrid strokeDasharray="3 3" />
        <XAxis dataKey="range" />
        <YAxis />
        <Tooltip content={<CustomTooltip />} />
        <Legend />
        <Bar dataKey="expected" name="期望薪资" fill="#8884d8" />
        <Bar dataKey="actual" name="实际薪资" fill="#82ca9d" />
      </BarChart>
    </ResponsiveContainer>
  );
}
