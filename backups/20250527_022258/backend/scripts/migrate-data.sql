-- 数据迁移脚本：将现有数据迁移到优化表结构

BEGIN TRANSACTION;

-- 1. 迁移用户数据到 users_optimized
INSERT INTO users_optimized (
  id, email, email_verified, role, username, display_name, password_hash,
  anonymous_id, is_anonymous_registered, ip_address, last_login_at, is_active,
  created_at, updated_at, metadata, version, schema_version, is_test_data
)
SELECT 
  'user_' || substr(hex(randomblob(16)), 1, 8) || '-' || substr(hex(randomblob(16)), 9, 4) || '-4' || substr(hex(randomblob(16)), 13, 3) || '-' || substr('89ab', abs(random()) % 4 + 1, 1) || substr(hex(randomblob(16)), 17, 3) || '-' || substr(hex(randomblob(16)), 21, 12) as id,
  email,
  COALESCE(emailVerified, 0) as email_verified,
  COALESCE(role, 'user') as role,
  username,
  name as display_name,
  passwordHash as password_hash,
  CASE 
    WHEN email IS NOT NULL THEN 'anon_' || substr(hex(randomblob(4)), 1, 8)
    ELSE NULL 
  END as anonymous_id,
  CASE WHEN email IS NOT NULL THEN 1 ELSE 0 END as is_anonymous_registered,
  ipAddress as ip_address,
  lastLoginAt as last_login_at,
  1 as is_active,
  createdAt as created_at,
  updatedAt as updated_at,
  json_object('originalId', id, 'migrated', 1) as metadata,
  1 as version,
  '1.0' as schema_version,
  COALESCE(isTestData, 0) as is_test_data
FROM User
WHERE NOT EXISTS (
  SELECT 1 FROM users_optimized uo WHERE json_extract(uo.metadata, '$.originalId') = User.id
);

-- 2. 迁移故事数据到 content_metadata
INSERT INTO content_metadata (
  id, user_id, content_type, title, category, tags, status,
  likes, dislikes, views, word_count, created_at, updated_at,
  metadata, version, schema_version, is_deleted, is_test_data
)
SELECT 
  'text_' || substr(hex(randomblob(16)), 1, 8) || '-' || substr(hex(randomblob(16)), 9, 4) || '-4' || substr(hex(randomblob(16)), 13, 3) || '-' || substr('89ab', abs(random()) % 4 + 1, 1) || substr(hex(randomblob(16)), 17, 3) || '-' || substr(hex(randomblob(16)), 21, 12) as id,
  (SELECT uo.id FROM users_optimized uo WHERE json_extract(uo.metadata, '$.originalId') = Story.userId) as user_id,
  'story' as content_type,
  title,
  category,
  tags,
  status,
  likes,
  dislikes,
  0 as views,
  COALESCE(length(content), 0) as word_count,
  createdAt as created_at,
  updatedAt as updated_at,
  json_object(
    'originalId', id,
    'author', author,
    'isAnonymous', isAnonymous,
    'content', content,
    'educationLevel', educationLevel,
    'industry', industry,
    'migrated', 1
  ) as metadata,
  1 as version,
  '1.0' as schema_version,
  0 as is_deleted,
  COALESCE(isTestData, 0) as is_test_data
FROM Story
WHERE NOT EXISTS (
  SELECT 1 FROM content_metadata cm WHERE json_extract(cm.metadata, '$.originalId') = Story.id
);

-- 3. 迁移问卷回复数据到 questionnaire_responses_optimized
INSERT INTO questionnaire_responses_optimized (
  id, user_id, questionnaire_id, sequence_number, submission_session_id,
  ip_address, is_anonymous, status, completion_rate, created_at, updated_at,
  metadata, version, schema_version, is_test_data
)
SELECT 
  'resp_' || substr(hex(randomblob(16)), 1, 8) || '-' || substr(hex(randomblob(16)), 9, 4) || '-4' || substr(hex(randomblob(16)), 13, 3) || '-' || substr('89ab', abs(random()) % 4 + 1, 1) || substr(hex(randomblob(16)), 17, 3) || '-' || substr(hex(randomblob(16)), 21, 12) as id,
  (SELECT uo.id FROM users_optimized uo WHERE json_extract(uo.metadata, '$.originalId') = QuestionnaireResponse.userId) as user_id,
  'quest_default_employment_survey' as questionnaire_id,
  sequenceNumber as sequence_number,
  'session_' || substr(hex(randomblob(16)), 1, 8) || '-' || substr(hex(randomblob(16)), 9, 4) || '-4' || substr(hex(randomblob(16)), 13, 3) || '-' || substr('89ab', abs(random()) % 4 + 1, 1) || substr(hex(randomblob(16)), 17, 3) || '-' || substr(hex(randomblob(16)), 21, 12) as submission_session_id,
  ipAddress as ip_address,
  COALESCE(isAnonymous, 1) as is_anonymous,
  COALESCE(status, 'submitted') as status,
  1.0 as completion_rate,
  createdAt as created_at,
  updatedAt as updated_at,
  json_object(
    'originalId', id,
    'educationLevel', educationLevel,
    'major', major,
    'graduationYear', graduationYear,
    'region', region,
    'expectedPosition', expectedPosition,
    'expectedSalaryRange', expectedSalaryRange,
    'expectedWorkHours', expectedWorkHours,
    'expectedVacationDays', expectedVacationDays,
    'employmentStatus', employmentStatus,
    'currentIndustry', currentIndustry,
    'currentPosition', currentPosition,
    'monthlySalary', monthlySalary,
    'jobSatisfaction', jobSatisfaction,
    'unemploymentDuration', unemploymentDuration,
    'unemploymentReason', unemploymentReason,
    'jobHuntingDifficulty', jobHuntingDifficulty,
    'regretMajor', regretMajor,
    'preferredMajor', preferredMajor,
    'careerChangeIntention', careerChangeIntention,
    'careerChangeTarget', careerChangeTarget,
    'migrated', 1
  ) as metadata,
  1 as version,
  '1.0' as schema_version,
  COALESCE(isTestData, 0) as is_test_data
FROM QuestionnaireResponse
WHERE NOT EXISTS (
  SELECT 1 FROM questionnaire_responses_optimized qro WHERE json_extract(qro.metadata, '$.originalId') = QuestionnaireResponse.id
);

-- 4. 迁移问卷心声数据到 questionnaire_voices
-- 4.1 迁移建议类心声
INSERT INTO questionnaire_voices (
  id, response_id, user_id, submission_session_id, voice_type, title, content, content_id,
  status, is_public, is_anonymous, ip_address, created_at, updated_at,
  metadata, version, schema_version, is_test_data
)
SELECT 
  'voice_' || substr(hex(randomblob(16)), 1, 8) || '-' || substr(hex(randomblob(16)), 9, 4) || '-4' || substr(hex(randomblob(16)), 13, 3) || '-' || substr('89ab', abs(random()) % 4 + 1, 1) || substr(hex(randomblob(16)), 17, 3) || '-' || substr(hex(randomblob(16)), 21, 12) as id,
  qro.id as response_id,
  qro.user_id,
  qro.submission_session_id,
  'advice' as voice_type,
  '给学弟学妹的建议' as title,
  qr.adviceForStudents as content,
  'text_' || substr(hex(randomblob(16)), 1, 8) || '-' || substr(hex(randomblob(16)), 9, 4) || '-4' || substr(hex(randomblob(16)), 13, 3) || '-' || substr('89ab', abs(random()) % 4 + 1, 1) || substr(hex(randomblob(16)), 17, 3) || '-' || substr(hex(randomblob(16)), 21, 12) as content_id,
  'approved' as status,
  1 as is_public,
  COALESCE(qr.isAnonymous, 1) as is_anonymous,
  qr.ipAddress as ip_address,
  qr.createdAt as created_at,
  qr.updatedAt as updated_at,
  json_object('originalResponseId', qr.id, 'type', 'advice', 'migrated', 1) as metadata,
  1 as version,
  '1.0' as schema_version,
  COALESCE(qr.isTestData, 0) as is_test_data
FROM QuestionnaireResponse qr
JOIN questionnaire_responses_optimized qro ON json_extract(qro.metadata, '$.originalId') = qr.id
WHERE qr.adviceForStudents IS NOT NULL 
  AND trim(qr.adviceForStudents) != ''
  AND NOT EXISTS (
    SELECT 1 FROM questionnaire_voices qv 
    WHERE json_extract(qv.metadata, '$.originalResponseId') = qr.id 
    AND json_extract(qv.metadata, '$.type') = 'advice'
  );

-- 4.2 迁移观察类心声
INSERT INTO questionnaire_voices (
  id, response_id, user_id, submission_session_id, voice_type, title, content, content_id,
  status, is_public, is_anonymous, ip_address, created_at, updated_at,
  metadata, version, schema_version, is_test_data
)
SELECT 
  'voice_' || substr(hex(randomblob(16)), 1, 8) || '-' || substr(hex(randomblob(16)), 9, 4) || '-4' || substr(hex(randomblob(16)), 13, 3) || '-' || substr('89ab', abs(random()) % 4 + 1, 1) || substr(hex(randomblob(16)), 17, 3) || '-' || substr(hex(randomblob(16)), 21, 12) as id,
  qro.id as response_id,
  qro.user_id,
  qro.submission_session_id,
  'observation' as voice_type,
  '对就业形势的观察' as title,
  qr.observationOnEmployment as content,
  'text_' || substr(hex(randomblob(16)), 1, 8) || '-' || substr(hex(randomblob(16)), 9, 4) || '-4' || substr(hex(randomblob(16)), 13, 3) || '-' || substr('89ab', abs(random()) % 4 + 1, 1) || substr(hex(randomblob(16)), 17, 3) || '-' || substr(hex(randomblob(16)), 21, 12) as content_id,
  'approved' as status,
  1 as is_public,
  COALESCE(qr.isAnonymous, 1) as is_anonymous,
  qr.ipAddress as ip_address,
  qr.createdAt as created_at,
  qr.updatedAt as updated_at,
  json_object('originalResponseId', qr.id, 'type', 'observation', 'migrated', 1) as metadata,
  1 as version,
  '1.0' as schema_version,
  COALESCE(qr.isTestData, 0) as is_test_data
FROM QuestionnaireResponse qr
JOIN questionnaire_responses_optimized qro ON json_extract(qro.metadata, '$.originalId') = qr.id
WHERE qr.observationOnEmployment IS NOT NULL 
  AND trim(qr.observationOnEmployment) != ''
  AND NOT EXISTS (
    SELECT 1 FROM questionnaire_voices qv 
    WHERE json_extract(qv.metadata, '$.originalResponseId') = qr.id 
    AND json_extract(qv.metadata, '$.type') = 'observation'
  );

-- 5. 记录迁移完成状态
INSERT OR REPLACE INTO SystemConfig (id, key, value, description, createdAt, updatedAt) VALUES
('data_migration_v1_0', 'data_migration_v1_0', 
 json_object(
   'version', '1.0',
   'completed_at', datetime('now'),
   'status', 'completed',
   'migrated_tables', json_array('users_optimized', 'content_metadata', 'questionnaire_responses_optimized', 'questionnaire_voices'),
   'original_counts', json_object(
     'users', (SELECT COUNT(*) FROM User),
     'stories', (SELECT COUNT(*) FROM Story),
     'questionnaire_responses', (SELECT COUNT(*) FROM QuestionnaireResponse)
   ),
   'migrated_counts', json_object(
     'users_optimized', (SELECT COUNT(*) FROM users_optimized),
     'content_metadata', (SELECT COUNT(*) FROM content_metadata),
     'questionnaire_responses_optimized', (SELECT COUNT(*) FROM questionnaire_responses_optimized),
     'questionnaire_voices', (SELECT COUNT(*) FROM questionnaire_voices)
   )
 ),
 '数据迁移v1.0完成记录', 
 datetime('now'), 
 datetime('now')
);

COMMIT;

-- 验证迁移结果
SELECT 'Data migration completed. Summary:' as message;
SELECT 
  'Users: ' || (SELECT COUNT(*) FROM User) || ' → ' || (SELECT COUNT(*) FROM users_optimized) as users_migration,
  'Stories: ' || (SELECT COUNT(*) FROM Story) || ' → ' || (SELECT COUNT(*) FROM content_metadata WHERE content_type = 'story') as stories_migration,
  'Questionnaires: ' || (SELECT COUNT(*) FROM QuestionnaireResponse) || ' → ' || (SELECT COUNT(*) FROM questionnaire_responses_optimized) as questionnaires_migration,
  'Voices: ' || (SELECT COUNT(*) FROM questionnaire_voices) as voices_created;
