import { Context } from 'hono';
import { PrismaClient } from '@prisma/client';
import { getMockVisualizationData } from '../../services/mockDataService';
import { KVStorageService } from '../../services/kvStorageService';
import config from '../../config';

// Define environment interface
interface Env {
  DATABASE_URL: string;
  SURVEY_KV: KVNamespace;
}

// Initialize Prisma client
const prisma = new PrismaClient();

// Get visualization data
export const getVisualizationData = async (c: Context<{ Bindings: Env }>) => {
  try {
    // 检查是否使用模拟数据
    if (config.mockData.enabled) {
      console.log('Mock data enabled: Returning visualization data from KV storage');

      // 使用 KV 存储服务
      const kvStorage = new KVStorageService(c.env);
      const visualizationData = await kvStorage.getVisualizationData();

      if (!visualizationData) {
        console.log('No visualization data found in KV storage, returning mock data');
        const mockData = getMockVisualizationData();
        return c.json(mockData);
      }

      return c.json(visualizationData);
    }
    // Get query parameters for filtering
    const verified = c.req.query('verified') === 'true';
    const educationLevel = c.req.query('educationLevel');
    const region = c.req.query('region');
    const graduationYear = c.req.query('graduationYear')
      ? parseInt(c.req.query('graduationYear')!)
      : undefined;

    // Build filter object
    const filter: any = {};

    if (verified) {
      filter.isAnonymous = false;
    }

    if (educationLevel) {
      filter.educationLevel = educationLevel;
    }

    if (region) {
      filter.region = region;
    }

    if (graduationYear) {
      filter.graduationYear = graduationYear;
    }

    // Get total count
    const totalCount = await prisma.questionnaireResponse.count({
      where: filter,
    });

    // Get verified count
    const verifiedCount = await prisma.questionnaireResponse.count({
      where: {
        ...filter,
        isAnonymous: false,
      },
    });

    // Get anonymous count
    const anonymousCount = await prisma.questionnaireResponse.count({
      where: {
        ...filter,
        isAnonymous: true,
      },
    });

    // Get employed count
    const employedCount = await prisma.questionnaireResponse.count({
      where: {
        ...filter,
        employmentStatus: '已就业',
      },
    });

    // Get unemployed count
    const unemployedCount = await prisma.questionnaireResponse.count({
      where: {
        ...filter,
        employmentStatus: '待业中',
      },
    });

    // Get education level distribution
    const educationLevels = await prisma.questionnaireResponse.groupBy({
      by: ['educationLevel'],
      _count: true,
      where: {
        ...filter,
        educationLevel: {
          not: null,
        },
      },
    });

    // Get region distribution
    const regions = await prisma.questionnaireResponse.groupBy({
      by: ['region'],
      _count: true,
      where: {
        ...filter,
        region: {
          not: null,
        },
      },
    });

    // Get industry distribution
    const industries = await prisma.questionnaireResponse.groupBy({
      by: ['currentIndustry'],
      _count: true,
      where: {
        ...filter,
        currentIndustry: {
          not: null,
        },
      },
    });

    // Get expected salary distribution
    const expectedSalaries = await prisma.questionnaireResponse.groupBy({
      by: ['expectedSalaryRange'],
      _count: true,
      where: {
        ...filter,
        expectedSalaryRange: {
          not: null,
        },
      },
    });

    // Get unemployment duration distribution
    const unemploymentDurations = await prisma.questionnaireResponse.groupBy({
      by: ['unemploymentDuration'],
      _count: true,
      where: {
        ...filter,
        unemploymentDuration: {
          not: null,
        },
      },
    });

    // Get most common education level
    const mostCommonEducation = educationLevels.length > 0
      ? educationLevels.sort((a, b) => b._count - a._count)[0].educationLevel
      : null;

    // Get most common industry
    const mostCommonIndustry = industries.length > 0
      ? industries.sort((a, b) => b._count - a._count)[0].currentIndustry
      : null;

    // Get career change intention by education level
    const careerChanges = await Promise.all(
      ['高中/中专', '大专', '本科', '硕士', '博士'].map(async (level) => {
        const total = await prisma.questionnaireResponse.count({
          where: {
            ...filter,
            educationLevel: level,
          },
        });

        const hasIntention = await prisma.questionnaireResponse.count({
          where: {
            ...filter,
            educationLevel: level,
            careerChangeIntention: true,
          },
        });

        return {
          group: level,
          count: total,
          hasIntention,
        };
      })
    );

    // Calculate average unemployment duration
    let averageUnemploymentDuration = '未知';

    if (unemploymentDurations.length > 0) {
      const durationMap: Record<string, number> = {
        '3个月以内': 1.5,
        '3-6个月': 4.5,
        '6-12个月': 9,
        '1年以上': 18,
        '应届生尚未就业': 0,
      };

      const totalMonths = unemploymentDurations.reduce((sum, duration) => {
        const months = durationMap[duration.unemploymentDuration] || 0;
        return sum + (months * duration._count);
      }, 0);

      const totalResponses = unemploymentDurations.reduce((sum, duration) => {
        return sum + duration._count;
      }, 0);

      if (totalResponses > 0) {
        const avgMonths = totalMonths / totalResponses;
        averageUnemploymentDuration = `${avgMonths.toFixed(1)} 个月`;
      }
    }

    // Get actual salary data (mock for now)
    // In a real app, this would come from the database
    const actualSalaries = expectedSalaries.map(salary => ({
      range: salary.expectedSalaryRange,
      count: Math.floor(salary._count * 0.8), // 80% of expected for demo
    }));

    return c.json({
      success: true,
      data: {
        totalCount,
        verifiedCount,
        anonymousCount,
        employedCount,
        unemployedCount,
        averageUnemploymentDuration,
        mostCommonEducation,
        mostCommonIndustry,
        educationLevels: educationLevels.map(level => ({
          name: level.educationLevel,
          count: level._count,
        })),
        regions: regions.map(region => ({
          name: region.region,
          count: region._count,
        })),
        industries: industries.map(industry => ({
          name: industry.currentIndustry,
          count: industry._count,
        })),
        expectedSalaries: expectedSalaries.map(salary => ({
          range: salary.expectedSalaryRange,
          count: salary._count,
        })),
        actualSalaries,
        unemploymentDurations: unemploymentDurations.map(duration => ({
          duration: duration.unemploymentDuration,
          count: duration._count,
        })),
        careerChanges,
      },
    });
  } catch (error) {
    console.error('Error getting visualization data:', error);
    return c.json({ success: false, error: 'Failed to get visualization data' }, 500);
  }
};
