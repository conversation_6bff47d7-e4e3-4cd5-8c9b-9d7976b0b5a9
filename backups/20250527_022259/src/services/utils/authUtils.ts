/**
 * 认证相关工具函数
 */

/**
 * 生成模拟令牌
 * @param user 用户信息
 * @returns 模拟令牌
 */
export function generateToken(user: any): string {
  // 创建一个简单的模拟令牌，包含用户信息和时间戳
  const payload = {
    id: user.id || Math.floor(Math.random() * 1000),
    username: user.username,
    role: user.role,
    permissions: user.permissions,
    timestamp: Date.now()
  };
  
  // 将payload转换为Base64编码的字符串
  const base64Payload = btoa(JSON.stringify(payload));
  
  // 添加一个简单的签名（在实际应用中，应该使用更安全的方法）
  const signature = btoa(`${payload.username}:${payload.timestamp}`);
  
  // 返回模拟令牌
  return `mock_${base64Payload}.${signature}`;
}

/**
 * 解析模拟令牌
 * @param token 模拟令牌
 * @returns 解析后的用户信息
 */
export function parseToken(token: string): any {
  try {
    // 检查是否为模拟令牌
    if (!token.startsWith('mock_')) {
      throw new Error('Invalid token format');
    }
    
    // 提取payload部分
    const [base64Payload] = token.substring(5).split('.');
    
    // 解码payload
    const payload = JSON.parse(atob(base64Payload));
    
    return payload;
  } catch (error) {
    console.error('Failed to parse token:', error);
    return null;
  }
}

/**
 * 验证模拟令牌
 * @param token 模拟令牌
 * @returns 是否有效
 */
export function verifyToken(token: string): boolean {
  try {
    // 解析令牌
    const payload = parseToken(token);
    
    // 检查是否成功解析
    if (!payload) {
      return false;
    }
    
    // 检查令牌是否过期（模拟令牌24小时有效）
    const now = Date.now();
    const tokenTime = payload.timestamp;
    const tokenAge = now - tokenTime;
    const maxAge = 24 * 60 * 60 * 1000; // 24小时
    
    return tokenAge < maxAge;
  } catch (error) {
    console.error('Failed to verify token:', error);
    return false;
  }
}
