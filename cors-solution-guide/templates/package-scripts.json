{"scripts": {"// 开发相关": "", "dev": "concurrently \"npm run dev:backend\" \"npm run dev:frontend\"", "dev:backend": "cd backend && wrangler dev --port 8787", "dev:frontend": "cd frontend && npm run dev", "dev:cors-test": "npm run dev & sleep 5 && npm run test:cors:local", "// 构建相关": "", "build": "npm run build:frontend && npm run build:backend", "build:frontend": "cd frontend && npm run build", "build:backend": "cd backend && npm run build", "// 部署相关": "", "deploy": "npm run check:pre-deploy && npm run deploy:backend && npm run deploy:frontend && npm run test:cors:production", "deploy:backend": "cd backend && npx wrangler deploy", "deploy:frontend": "cd frontend && npm run build && npx wrangler pages deploy dist --project-name your-project-name", "deploy:staging": "npm run deploy:backend:staging && npm run deploy:frontend:staging", "deploy:backend:staging": "cd backend && npx wrangler deploy --env staging", "deploy:frontend:staging": "cd frontend && npm run build && npx wrangler pages deploy dist --project-name your-project-name-staging", "// CORS测试和诊断": "", "test:cors": "npm run test:cors:local && npm run test:cors:production", "test:cors:local": "./cors-solution-guide/scripts/cors-diagnose.sh http://localhost:8787 http://localhost:3000", "test:cors:staging": "./cors-solution-guide/scripts/cors-diagnose.sh https://your-api-staging.workers.dev https://staging.yourdomain.com", "test:cors:production": "./cors-solution-guide/scripts/cors-diagnose.sh https://your-api.workers.dev https://app.yourdomain.com", "// 检查和修复": "", "check:pre-deploy": "./cors-solution-guide/scripts/pre-deploy-check.sh", "check:cors": "./cors-solution-guide/scripts/cors-diagnose.sh", "fix:cors": "./cors-solution-guide/scripts/auto-fix-cors.sh", "// 维护相关": "", "clean": "npm run clean:frontend && npm run clean:backend", "clean:frontend": "cd frontend && rm -rf dist node_modules/.vite", "clean:backend": "cd backend && rm -rf dist .wrangler", "fresh-install": "npm run clean && npm install && cd frontend && npm install && cd ../backend && npm install", "// 日志和监控": "", "logs:backend": "cd backend && npx wrangler tail", "logs:backend:staging": "cd backend && npx wrangler tail --env staging", "// 工具脚本": "", "tools:cors-diagnose": "./cors-solution-guide/scripts/cors-diagnose.sh", "tools:browser-test": "echo '请在浏览器控制台运行 cors-solution-guide/scripts/browser-cors-test.js'", "tools:make-executable": "chmod +x cors-solution-guide/scripts/*.sh", "// 文档和帮助": "", "help": "echo '可用命令: dev, build, deploy, test:cors, check:pre-deploy, fix:cors'", "help:cors": "cat cors-solution-guide/README.md", "help:troubleshooting": "cat cors-solution-guide/docs/troubleshooting.md"}, "// 使用说明": {"开发流程": ["1. npm run dev - 启动开发环境", "2. npm run test:cors:local - 测试本地CORS配置", "3. npm run check:pre-deploy - 部署前检查", "4. npm run deploy - 部署到生产环境"], "CORS问题排查": ["1. npm run test:cors - 全面CORS测试", "2. npm run fix:cors - 自动修复常见问题", "3. npm run tools:cors-diagnose API_URL FRONTEND_URL - 详细诊断"], "紧急修复": ["1. npm run fix:cors - 自动修复", "2. npm run deploy:backend - 重新部署后端", "3. npm run test:cors:production - 验证修复"]}, "// 环境变量示例": {"development": {"NODE_ENV": "development", "ALLOWED_ORIGINS": "http://localhost:3000,http://localhost:5173"}, "staging": {"NODE_ENV": "staging", "ALLOWED_ORIGINS": "https://staging.yourdomain.com"}, "production": {"NODE_ENV": "production", "ALLOWED_ORIGINS": "https://app.yourdomain.com"}}, "// 推荐的devDependencies": {"concurrently": "^8.0.0", "@types/node": "^20.0.0", "typescript": "^5.0.0"}, "// Cloudflare配置示例": {"wrangler.toml": {"name": "your-project-api", "main": "src/index.ts", "compatibility_date": "2024-01-01", "env": {"staging": {"vars": {"ALLOWED_ORIGINS": "https://staging.yourdomain.com"}}, "production": {"vars": {"ALLOWED_ORIGINS": "https://app.yourdomain.com"}}}}}}