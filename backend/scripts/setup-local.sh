#!/bin/bash

# Setup script for local development

# Create directories if they don't exist
mkdir -p backend/postman

# Check if PostgreSQL is installed
if ! command -v psql &> /dev/null; then
    echo "PostgreSQL is not installed. Please install PostgreSQL and try again."
    exit 1
fi

# Check if database exists
if ! psql -lqt | cut -d \| -f 1 | grep -qw college_survey; then
    echo "Creating database 'college_survey'..."
    createdb college_survey
else
    echo "Database 'college_survey' already exists."
fi

# Check if .env.local exists
if [ ! -f backend/.env.local ]; then
    echo "Creating backend/.env.local..."
    cat > backend/.env.local << EOL
# Database URL for PostgreSQL
DATABASE_URL="postgresql://postgres:postgres@localhost:5432/college_survey?schema=public"

# Resend API Key (for email service)
RESEND_API_KEY="re_123456789"

# Encryption keys for sensitive data
ENCRYPTION_KEY="32_character_encryption_key_here_32"
ENCRYPTION_IV="16_char_iv_here"

# JWT Secret for authentication
JWT_SECRET="your_jwt_secret_key_here"

# Environment
ENVIRONMENT="development"
EOL
    echo "Created backend/.env.local"
else
    echo "backend/.env.local already exists."
fi

# Check if frontend .env.local exists
if [ ! -f frontend/.env.local ]; then
    echo "Creating frontend/.env.local..."
    cat > frontend/.env.local << EOL
# API URL for local development
VITE_API_URL="http://localhost:8787/api"

# Environment
VITE_ENVIRONMENT="development"
EOL
    echo "Created frontend/.env.local"
else
    echo "frontend/.env.local already exists."
fi

# Generate Prisma client
echo "Generating Prisma client..."
cd backend && npx prisma generate

# Run database migrations
echo "Running database migrations..."
cd backend && npx prisma migrate dev

# Seed the database
echo "Seeding the database..."
cd backend && npx prisma db seed

echo "Setup complete! You can now start the development servers:"
echo "  - Backend: cd backend && npm run dev"
echo "  - Frontend: cd frontend && npm run dev"
