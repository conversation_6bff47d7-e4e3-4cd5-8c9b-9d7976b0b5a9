/**
 * 🎯 Lint-staged配置文件
 * 对暂存的文件运行代码质量检查
 */

module.exports = {
  // JavaScript文件
  '*.js': [
    'eslint --fix',
    'prettier --write',
    'git add'
  ],
  
  // TypeScript文件
  '*.ts': [
    'eslint --fix',
    'prettier --write',
    'git add'
  ],
  
  // JSON文件
  '*.json': [
    'prettier --write',
    'git add'
  ],
  
  // Markdown文件
  '*.md': [
    'prettier --write',
    'git add'
  ],
  
  // YAML文件
  '*.{yml,yaml}': [
    'prettier --write',
    'git add'
  ],
  
  // 测试文件需要运行相关测试
  '*.test.js': [
    'eslint --fix',
    'prettier --write',
    'jest --findRelatedTests --passWithNoTests',
    'git add'
  ],
  
  // 配置文件
  '*.config.js': [
    'eslint --fix',
    'prettier --write',
    'git add'
  ]
};
