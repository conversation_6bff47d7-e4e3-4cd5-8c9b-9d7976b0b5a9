import React, { useState, useEffect } from 'react';
import SuperAdminLayout from '@/components/layouts/SuperAdminLayout';
import { Card, CardContent, CardHeader, CardTitle, CardDescription, CardFooter } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { Label } from '@/components/ui/label';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Switch } from '@/components/ui/switch';
import { Textarea } from '@/components/ui/textarea';
import { Slider } from '@/components/ui/slider';
import { useToast } from '@/components/ui/use-toast';
import {
  Save, RefreshCw, Server, Database, Shield,
  Mail, Cloud, AlertTriangle, Check, X,
  RotateCcw, Settings, Info, Download, Upload
} from 'lucide-react';
import {
  Dialog, DialogContent, DialogDescription, DialogFooter,
  DialogHeader, DialogTitle, DialogTrigger
} from '@/components/ui/dialog';
import { Separator } from '@/components/ui/separator';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import {
  getSystemConfig,
  updateSystemConfig,
  testEmailService,
  testStorageService,
  testAIContentReview,
  resetSystemConfig,
  exportSystemConfig,
  importSystemConfig,
  SystemConfig
} from '@/services/systemConfigService';
import SystemConfigImportExportDialog from '@/components/superadmin/SystemConfigImportExportDialog';

/**
 * 超级管理员系统配置页面
 *
 * 配置平台的基本设置、安全策略、审核策略和外部服务集成
 */
const SystemConfigPage: React.FC = () => {
  const { toast } = useToast();
  const [isLoading, setIsLoading] = useState(false);
  const [isSaving, setIsSaving] = useState(false);
  const [isTestingEmail, setIsTestingEmail] = useState(false);
  const [isTestingStorage, setIsTestingStorage] = useState(false);
  const [isTestingAI, setIsTestingAI] = useState(false);
  const [config, setConfig] = useState<SystemConfig>({
    // 基本设置
    siteName: '大学就业调查平台',
    siteDescription: '收集和分析大学生就业情况的平台',
    contactEmail: '<EMAIL>',
    maintenanceMode: false,
    maintenanceMessage: '系统正在维护中，请稍后再试。',

    // 安全设置
    maxLoginAttempts: 5,
    loginLockoutDuration: 30,
    passwordMinLength: 8,
    passwordRequireUppercase: true,
    passwordRequireNumbers: true,
    passwordRequireSymbols: false,
    sessionTimeout: 60,
    ipBlacklist: [],

    // 审核设置
    contentReviewEnabled: true,
    autoReviewEnabled: true,
    reviewSensitivityLevel: 'medium',
    reviewerNotifications: true,
    adminNotifications: true,

    // 外部服务
    emailServiceEnabled: true,
    emailServiceApiKey: 'resend_api_key_123456789',
    emailServiceSender: '<EMAIL>',
    storageServiceEnabled: true,
    storageServiceApiKey: 'storage_api_key_123456789',
    storageServiceBucket: 'college-survey-bucket',
    aiContentReviewEnabled: true,
    aiContentReviewApiKey: 'ai_api_key_123456789',
    aiContentReviewSensitivity: 75
  });
  const [isConfirmDialogOpen, setIsConfirmDialogOpen] = useState(false);
  const [isImportExportDialogOpen, setIsImportExportDialogOpen] = useState(false);
  const [pendingChanges, setPendingChanges] = useState<Partial<SystemConfig>>({});

  // 加载系统配置
  useEffect(() => {
    fetchSystemConfig();
  }, []);

  // 获取系统配置
  const fetchSystemConfig = async () => {
    try {
      setIsLoading(true);

      const response = await getSystemConfig();

      if (response.success) {
        setConfig(response.data);
        toast({
          title: '配置加载成功',
          description: '系统配置已成功加载'
        });
      } else {
        toast({
          title: '获取系统配置失败',
          description: response.message || '无法加载系统配置',
          variant: 'destructive'
        });
      }
    } catch (error) {
      console.error('获取系统配置失败:', error);
      toast({
        title: '获取系统配置失败',
        description: '服务器错误，请稍后再试',
        variant: 'destructive'
      });
    } finally {
      setIsLoading(false);
    }
  };

  // 保存系统配置
  const saveSystemConfig = async () => {
    try {
      setIsSaving(true);

      const response = await updateSystemConfig(pendingChanges);

      if (response.success) {
        // 更新配置
        setConfig(response.data);
        setPendingChanges({});

        toast({
          title: '保存成功',
          description: '系统配置已更新'
        });
      } else {
        toast({
          title: '保存失败',
          description: response.message || '无法更新系统配置',
          variant: 'destructive'
        });
      }
    } catch (error) {
      console.error('保存系统配置失败:', error);
      toast({
        title: '保存失败',
        description: '服务器错误，请稍后再试',
        variant: 'destructive'
      });
    } finally {
      setIsSaving(false);
    }
  };

  // 重置系统配置
  const handleResetConfig = async () => {
    try {
      setIsLoading(true);

      const response = await resetSystemConfig();

      if (response.success) {
        setConfig(response.data);
        setPendingChanges({});

        toast({
          title: '重置成功',
          description: '系统配置已恢复默认设置'
        });
      } else {
        toast({
          title: '重置失败',
          description: response.message || '无法重置系统配置',
          variant: 'destructive'
        });
      }
    } catch (error) {
      console.error('重置系统配置失败:', error);
      toast({
        title: '重置失败',
        description: '服务器错误，请稍后再试',
        variant: 'destructive'
      });
    } finally {
      setIsLoading(false);
    }
  };

  // 处理配置变更
  const handleConfigChange = <K extends keyof SystemConfig>(key: K, value: SystemConfig[K]) => {
    setPendingChanges(prev => ({ ...prev, [key]: value }));
  };

  // 测试邮件服务
  const handleTestEmailService = async () => {
    try {
      setIsTestingEmail(true);

      // 获取当前邮件服务配置
      const emailConfig = {
        emailServiceEnabled: getCurrentValue('emailServiceEnabled'),
        emailServiceApiKey: getCurrentValue('emailServiceApiKey'),
        emailServiceSender: getCurrentValue('emailServiceSender')
      };

      const response = await testEmailService(emailConfig);

      if (response.success) {
        toast({
          title: '测试成功',
          description: response.message || '邮件服务连接正常'
        });
      } else {
        toast({
          title: '测试失败',
          description: response.message || '无法连接到邮件服务',
          variant: 'destructive'
        });
      }
    } catch (error) {
      console.error('测试邮件服务失败:', error);
      toast({
        title: '测试失败',
        description: '服务器错误，请稍后再试',
        variant: 'destructive'
      });
    } finally {
      setIsTestingEmail(false);
    }
  };

  // 测试存储服务
  const handleTestStorageService = async () => {
    try {
      setIsTestingStorage(true);

      // 获取当前存储服务配置
      const storageConfig = {
        storageServiceEnabled: getCurrentValue('storageServiceEnabled'),
        storageServiceApiKey: getCurrentValue('storageServiceApiKey'),
        storageServiceBucket: getCurrentValue('storageServiceBucket')
      };

      const response = await testStorageService(storageConfig);

      if (response.success) {
        toast({
          title: '测试成功',
          description: response.message || '存储服务连接正常'
        });
      } else {
        toast({
          title: '测试失败',
          description: response.message || '无法连接到存储服务',
          variant: 'destructive'
        });
      }
    } catch (error) {
      console.error('测试存储服务失败:', error);
      toast({
        title: '测试失败',
        description: '服务器错误，请稍后再试',
        variant: 'destructive'
      });
    } finally {
      setIsTestingStorage(false);
    }
  };

  // 测试AI内容审查服务
  const handleTestAIContentReview = async () => {
    try {
      setIsTestingAI(true);

      // 获取当前AI内容审查服务配置
      const aiConfig = {
        aiContentReviewEnabled: getCurrentValue('aiContentReviewEnabled'),
        aiContentReviewApiKey: getCurrentValue('aiContentReviewApiKey'),
        aiContentReviewSensitivity: getCurrentValue('aiContentReviewSensitivity')
      };

      const response = await testAIContentReview(aiConfig);

      if (response.success) {
        toast({
          title: '测试成功',
          description: response.message || 'AI内容审查服务连接正常'
        });
      } else {
        toast({
          title: '测试失败',
          description: response.message || '无法连接到AI内容审查服务',
          variant: 'destructive'
        });
      }
    } catch (error) {
      console.error('测试AI内容审查服务失败:', error);
      toast({
        title: '测试失败',
        description: '服务器错误，请稍后再试',
        variant: 'destructive'
      });
    } finally {
      setIsTestingAI(false);
    }
  };

  // 处理导入成功
  const handleImportSuccess = (importedConfig: SystemConfig) => {
    setConfig(importedConfig);
    setPendingChanges({});

    toast({
      title: '导入成功',
      description: '系统配置已成功导入并应用'
    });
  };

  // 获取当前配置值
  const getCurrentValue = <K extends keyof SystemConfig>(key: K): SystemConfig[K] => {
    return key in pendingChanges ? pendingChanges[key] as SystemConfig[K] : config[key];
  };

  // 检查是否有未保存的更改
  const hasUnsavedChanges = Object.keys(pendingChanges).length > 0;

  return (
    <SuperAdminLayout>
      <div className="container mx-auto py-6">
        <div className="flex justify-between items-center mb-6">
          <div className="flex items-center">
            <Settings className="h-6 w-6 mr-2" />
            <h1 className="text-2xl font-bold">系统配置</h1>
          </div>
          <div className="flex items-center space-x-2">
            {hasUnsavedChanges && (
              <span className="text-yellow-500 text-sm mr-2 flex items-center">
                <Info className="h-4 w-4 mr-1" />
                有未保存的更改
              </span>
            )}
            <Button
              variant="outline"
              onClick={() => setPendingChanges({})}
              disabled={!hasUnsavedChanges || isSaving}
            >
              <RefreshCw className="mr-2 h-4 w-4" />
              撤销更改
            </Button>
            <Button
              variant="outline"
              onClick={() => {
                if (window.confirm('确定要恢复默认配置吗？这将丢失所有自定义设置。')) {
                  handleResetConfig();
                }
              }}
              disabled={isSaving || isLoading}
            >
              <RotateCcw className="mr-2 h-4 w-4" />
              恢复默认
            </Button>
            <Button
              variant="outline"
              onClick={() => setIsImportExportDialogOpen(true)}
              disabled={isSaving || isLoading}
            >
              <Download className="mr-2 h-4 w-4" />
              导入/导出
            </Button>
            <Button
              onClick={() => setIsConfirmDialogOpen(true)}
              disabled={!hasUnsavedChanges || isSaving}
            >
              <Save className="mr-2 h-4 w-4" />
              保存更改
              {isSaving && <RefreshCw className="ml-2 h-4 w-4 animate-spin" />}
            </Button>
          </div>
        </div>

        {isLoading && (
          <Alert className="mb-6">
            <RefreshCw className="h-4 w-4 animate-spin mr-2" />
            <AlertTitle>正在加载配置</AlertTitle>
            <AlertDescription>
              请稍候，系统正在加载配置信息...
            </AlertDescription>
          </Alert>
        )}

        <Tabs defaultValue="basic" className="space-y-6">
          <TabsList className="grid grid-cols-4 w-full">
            <TabsTrigger value="basic">基本设置</TabsTrigger>
            <TabsTrigger value="security">安全设置</TabsTrigger>
            <TabsTrigger value="review">审核设置</TabsTrigger>
            <TabsTrigger value="services">外部服务</TabsTrigger>
          </TabsList>

          {/* 基本设置 */}
          <TabsContent value="basic" className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle>基本信息</CardTitle>
                <CardDescription>
                  配置平台的基本信息
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="siteName">网站名称</Label>
                    <Input
                      id="siteName"
                      value={getCurrentValue('siteName')}
                      onChange={(e) => handleConfigChange('siteName', e.target.value)}
                    />
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="contactEmail">联系邮箱</Label>
                    <Input
                      id="contactEmail"
                      type="email"
                      value={getCurrentValue('contactEmail')}
                      onChange={(e) => handleConfigChange('contactEmail', e.target.value)}
                    />
                  </div>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="siteDescription">网站描述</Label>
                  <Textarea
                    id="siteDescription"
                    value={getCurrentValue('siteDescription')}
                    onChange={(e) => handleConfigChange('siteDescription', e.target.value)}
                    rows={3}
                  />
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>维护模式</CardTitle>
                <CardDescription>
                  启用维护模式将暂时关闭网站，只有管理员可以访问
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="flex items-center justify-between">
                  <Label htmlFor="maintenanceMode">启用维护模式</Label>
                  <Switch
                    id="maintenanceMode"
                    checked={getCurrentValue('maintenanceMode')}
                    onCheckedChange={(checked) => handleConfigChange('maintenanceMode', checked)}
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="maintenanceMessage">维护消息</Label>
                  <Textarea
                    id="maintenanceMessage"
                    value={getCurrentValue('maintenanceMessage')}
                    onChange={(e) => handleConfigChange('maintenanceMessage', e.target.value)}
                    rows={3}
                    disabled={!getCurrentValue('maintenanceMode')}
                  />
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          {/* 安全设置 */}
          <TabsContent value="security" className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle>登录安全</CardTitle>
                <CardDescription>
                  配置登录尝试和锁定策略
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="maxLoginAttempts">最大登录尝试次数</Label>
                    <div className="flex items-center space-x-2">
                      <Input
                        id="maxLoginAttempts"
                        type="number"
                        min="1"
                        max="10"
                        value={getCurrentValue('maxLoginAttempts')}
                        onChange={(e) => handleConfigChange('maxLoginAttempts', parseInt(e.target.value))}
                      />
                      <span className="text-sm text-muted-foreground">次</span>
                    </div>
                    <p className="text-xs text-muted-foreground">
                      超过此次数将锁定账号
                    </p>
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="loginLockoutDuration">账号锁定时长</Label>
                    <div className="flex items-center space-x-2">
                      <Input
                        id="loginLockoutDuration"
                        type="number"
                        min="5"
                        max="1440"
                        value={getCurrentValue('loginLockoutDuration')}
                        onChange={(e) => handleConfigChange('loginLockoutDuration', parseInt(e.target.value))}
                      />
                      <span className="text-sm text-muted-foreground">分钟</span>
                    </div>
                    <p className="text-xs text-muted-foreground">
                      账号锁定的持续时间
                    </p>
                  </div>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="sessionTimeout">会话超时时间</Label>
                  <div className="flex items-center space-x-2">
                    <Input
                      id="sessionTimeout"
                      type="number"
                      min="5"
                      max="1440"
                      value={getCurrentValue('sessionTimeout')}
                      onChange={(e) => handleConfigChange('sessionTimeout', parseInt(e.target.value))}
                    />
                    <span className="text-sm text-muted-foreground">分钟</span>
                  </div>
                  <p className="text-xs text-muted-foreground">
                    用户无操作后自动登出的时间
                  </p>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>密码策略</CardTitle>
                <CardDescription>
                  配置密码强度要求
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="space-y-2">
                  <Label htmlFor="passwordMinLength">密码最小长度</Label>
                  <div className="flex items-center space-x-2">
                    <Input
                      id="passwordMinLength"
                      type="number"
                      min="6"
                      max="20"
                      value={getCurrentValue('passwordMinLength')}
                      onChange={(e) => handleConfigChange('passwordMinLength', parseInt(e.target.value))}
                    />
                    <span className="text-sm text-muted-foreground">字符</span>
                  </div>
                </div>

                <div className="flex items-center justify-between">
                  <Label htmlFor="passwordRequireUppercase">要求包含大写字母</Label>
                  <Switch
                    id="passwordRequireUppercase"
                    checked={getCurrentValue('passwordRequireUppercase')}
                    onCheckedChange={(checked) => handleConfigChange('passwordRequireUppercase', checked)}
                  />
                </div>

                <div className="flex items-center justify-between">
                  <Label htmlFor="passwordRequireNumbers">要求包含数字</Label>
                  <Switch
                    id="passwordRequireNumbers"
                    checked={getCurrentValue('passwordRequireNumbers')}
                    onCheckedChange={(checked) => handleConfigChange('passwordRequireNumbers', checked)}
                  />
                </div>

                <div className="flex items-center justify-between">
                  <Label htmlFor="passwordRequireSymbols">要求包含特殊符号</Label>
                  <Switch
                    id="passwordRequireSymbols"
                    checked={getCurrentValue('passwordRequireSymbols')}
                    onCheckedChange={(checked) => handleConfigChange('passwordRequireSymbols', checked)}
                  />
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>IP黑名单</CardTitle>
                <CardDescription>
                  阻止特定IP地址访问系统
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="space-y-2">
                  <Label htmlFor="ipBlacklist">IP地址列表（每行一个）</Label>
                  <Textarea
                    id="ipBlacklist"
                    value={getCurrentValue('ipBlacklist').join('\n')}
                    onChange={(e) => handleConfigChange('ipBlacklist', e.target.value.split('\n').filter(ip => ip.trim()))}
                    rows={5}
                    placeholder="例如：***********"
                  />
                  <p className="text-xs text-muted-foreground">
                    这些IP地址将被阻止访问系统
                  </p>
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          {/* 审核设置 */}
          <TabsContent value="review" className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle>内容审核</CardTitle>
                <CardDescription>
                  配置内容审核策略
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="flex items-center justify-between">
                  <Label htmlFor="contentReviewEnabled">启用内容审核</Label>
                  <Switch
                    id="contentReviewEnabled"
                    checked={getCurrentValue('contentReviewEnabled')}
                    onCheckedChange={(checked) => handleConfigChange('contentReviewEnabled', checked)}
                  />
                </div>

                <div className="flex items-center justify-between">
                  <Label htmlFor="autoReviewEnabled">启用自动审核</Label>
                  <Switch
                    id="autoReviewEnabled"
                    checked={getCurrentValue('autoReviewEnabled')}
                    onCheckedChange={(checked) => handleConfigChange('autoReviewEnabled', checked)}
                    disabled={!getCurrentValue('contentReviewEnabled')}
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="reviewSensitivityLevel">审核敏感度</Label>
                  <Select
                    value={getCurrentValue('reviewSensitivityLevel')}
                    onValueChange={(value) => handleConfigChange('reviewSensitivityLevel', value as 'low' | 'medium' | 'high')}
                    disabled={!getCurrentValue('contentReviewEnabled')}
                  >
                    <SelectTrigger id="reviewSensitivityLevel">
                      <SelectValue placeholder="选择敏感度" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="low">低（仅过滤明显违规内容）</SelectItem>
                      <SelectItem value="medium">中（平衡过滤）</SelectItem>
                      <SelectItem value="high">高（严格过滤）</SelectItem>
                    </SelectContent>
                  </Select>
                  <p className="text-xs text-muted-foreground">
                    设置内容审核的敏感度级别
                  </p>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>通知设置</CardTitle>
                <CardDescription>
                  配置审核相关的通知
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="flex items-center justify-between">
                  <Label htmlFor="reviewerNotifications">审核员通知</Label>
                  <Switch
                    id="reviewerNotifications"
                    checked={getCurrentValue('reviewerNotifications')}
                    onCheckedChange={(checked) => handleConfigChange('reviewerNotifications', checked)}
                  />
                </div>
                <p className="text-xs text-muted-foreground">
                  当有新内容需要审核时，通知审核员
                </p>

                <Separator />

                <div className="flex items-center justify-between">
                  <Label htmlFor="adminNotifications">管理员通知</Label>
                  <Switch
                    id="adminNotifications"
                    checked={getCurrentValue('adminNotifications')}
                    onCheckedChange={(checked) => handleConfigChange('adminNotifications', checked)}
                  />
                </div>
                <p className="text-xs text-muted-foreground">
                  当有内容被拒绝或需要进一步审核时，通知管理员
                </p>
              </CardContent>
            </Card>
          </TabsContent>

          {/* 外部服务 */}
          <TabsContent value="services" className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle>邮件服务</CardTitle>
                <CardDescription>
                  配置用于发送通知和验证邮件的服务
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="flex items-center justify-between">
                  <Label htmlFor="emailServiceEnabled">启用邮件服务</Label>
                  <Switch
                    id="emailServiceEnabled"
                    checked={getCurrentValue('emailServiceEnabled')}
                    onCheckedChange={(checked) => handleConfigChange('emailServiceEnabled', checked)}
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="emailServiceApiKey">API密钥</Label>
                  <Input
                    id="emailServiceApiKey"
                    type="password"
                    value={getCurrentValue('emailServiceApiKey')}
                    onChange={(e) => handleConfigChange('emailServiceApiKey', e.target.value)}
                    disabled={!getCurrentValue('emailServiceEnabled')}
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="emailServiceSender">发件人邮箱</Label>
                  <Input
                    id="emailServiceSender"
                    type="email"
                    value={getCurrentValue('emailServiceSender')}
                    onChange={(e) => handleConfigChange('emailServiceSender', e.target.value)}
                    disabled={!getCurrentValue('emailServiceEnabled')}
                  />
                </div>

                <Button
                  variant="outline"
                  onClick={handleTestEmailService}
                  disabled={!getCurrentValue('emailServiceEnabled') || isTestingEmail}
                >
                  {isTestingEmail ? (
                    <>
                      <RefreshCw className="mr-2 h-4 w-4 animate-spin" />
                      测试中...
                    </>
                  ) : (
                    <>
                      <Mail className="mr-2 h-4 w-4" />
                      测试连接
                    </>
                  )}
                </Button>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>存储服务</CardTitle>
                <CardDescription>
                  配置用于存储文件和媒体的云存储服务
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="flex items-center justify-between">
                  <Label htmlFor="storageServiceEnabled">启用存储服务</Label>
                  <Switch
                    id="storageServiceEnabled"
                    checked={getCurrentValue('storageServiceEnabled')}
                    onCheckedChange={(checked) => handleConfigChange('storageServiceEnabled', checked)}
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="storageServiceApiKey">API密钥</Label>
                  <Input
                    id="storageServiceApiKey"
                    type="password"
                    value={getCurrentValue('storageServiceApiKey')}
                    onChange={(e) => handleConfigChange('storageServiceApiKey', e.target.value)}
                    disabled={!getCurrentValue('storageServiceEnabled')}
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="storageServiceBucket">存储桶名称</Label>
                  <Input
                    id="storageServiceBucket"
                    value={getCurrentValue('storageServiceBucket')}
                    onChange={(e) => handleConfigChange('storageServiceBucket', e.target.value)}
                    disabled={!getCurrentValue('storageServiceEnabled')}
                  />
                </div>

                <Button
                  variant="outline"
                  onClick={handleTestStorageService}
                  disabled={!getCurrentValue('storageServiceEnabled') || isTestingStorage}
                >
                  {isTestingStorage ? (
                    <>
                      <RefreshCw className="mr-2 h-4 w-4 animate-spin" />
                      测试中...
                    </>
                  ) : (
                    <>
                      <Cloud className="mr-2 h-4 w-4" />
                      测试连接
                    </>
                  )}
                </Button>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>AI内容审查服务</CardTitle>
                <CardDescription>
                  配置用于自动审查内容的AI服务
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="flex items-center justify-between">
                  <Label htmlFor="aiContentReviewEnabled">启用AI内容审查</Label>
                  <Switch
                    id="aiContentReviewEnabled"
                    checked={getCurrentValue('aiContentReviewEnabled')}
                    onCheckedChange={(checked) => handleConfigChange('aiContentReviewEnabled', checked)}
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="aiContentReviewApiKey">API密钥</Label>
                  <Input
                    id="aiContentReviewApiKey"
                    type="password"
                    value={getCurrentValue('aiContentReviewApiKey')}
                    onChange={(e) => handleConfigChange('aiContentReviewApiKey', e.target.value)}
                    disabled={!getCurrentValue('aiContentReviewEnabled')}
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="aiContentReviewSensitivity">敏感度 ({getCurrentValue('aiContentReviewSensitivity')}%)</Label>
                  <Slider
                    id="aiContentReviewSensitivity"
                    min={0}
                    max={100}
                    step={5}
                    value={[getCurrentValue('aiContentReviewSensitivity')]}
                    onValueChange={(value) => handleConfigChange('aiContentReviewSensitivity', value[0])}
                    disabled={!getCurrentValue('aiContentReviewEnabled')}
                  />
                  <div className="flex justify-between text-xs text-muted-foreground">
                    <span>宽松</span>
                    <span>平衡</span>
                    <span>严格</span>
                  </div>
                </div>

                <Button
                  variant="outline"
                  onClick={handleTestAIContentReview}
                  disabled={!getCurrentValue('aiContentReviewEnabled') || isTestingAI}
                >
                  {isTestingAI ? (
                    <>
                      <RefreshCw className="mr-2 h-4 w-4 animate-spin" />
                      测试中...
                    </>
                  ) : (
                    <>
                      <AlertTriangle className="mr-2 h-4 w-4" />
                      测试连接
                    </>
                  )}
                </Button>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>

        {/* 确认保存对话框 */}
        <Dialog open={isConfirmDialogOpen} onOpenChange={setIsConfirmDialogOpen}>
          <DialogContent>
            <DialogHeader>
              <DialogTitle>确认保存更改</DialogTitle>
              <DialogDescription>
                您确定要保存这些更改吗？这将影响系统的运行。
              </DialogDescription>
            </DialogHeader>

            <div className="py-4">
              <div className="space-y-4">
                <h3 className="text-sm font-medium">您修改了以下设置:</h3>
                <ul className="space-y-2">
                  {Object.entries(pendingChanges).map(([key, value]) => (
                    <li key={key} className="text-sm">
                      <span className="font-medium">{key}: </span>
                      <span className="text-muted-foreground">
                        {typeof value === 'boolean'
                          ? (value ? '启用' : '禁用')
                          : (Array.isArray(value)
                            ? value.join(', ')
                            : String(value))}
                      </span>
                    </li>
                  ))}
                </ul>
              </div>
            </div>

            <DialogFooter>
              <Button variant="outline" onClick={() => setIsConfirmDialogOpen(false)}>
                取消
              </Button>
              <Button
                onClick={() => {
                  setIsConfirmDialogOpen(false);
                  saveSystemConfig();
                }}
                disabled={isSaving}
              >
                {isSaving ? (
                  <>
                    <RefreshCw className="mr-2 h-4 w-4 animate-spin" />
                    保存中...
                  </>
                ) : (
                  <>
                    <Save className="mr-2 h-4 w-4" />
                    确认保存
                  </>
                )}
              </Button>
            </DialogFooter>
          </DialogContent>
        </Dialog>

        {/* 导入/导出对话框 */}
        <SystemConfigImportExportDialog
          open={isImportExportDialogOpen}
          onOpenChange={setIsImportExportDialogOpen}
          onImportSuccess={handleImportSuccess}
        />
      </div>
    </SuperAdminLayout>
  );
};

export default SystemConfigPage;
