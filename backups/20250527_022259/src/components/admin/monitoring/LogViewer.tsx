import React, { useState, useEffect } from 'react';
import {
  Card,
  Table,
  Select,
  Input,
  Button,
  Space,
  DatePicker,
  Tag,
  Drawer,
  Typography,
  Spin,
  Alert,
  Tooltip,
  Badge
} from 'antd';
import {
  ReloadOutlined,
  SearchOutlined,
  InfoCircleOutlined,
  WarningOutlined,
  CloseCircleOutlined,
  CheckCircleOutlined,
  DownloadOutlined,
  EyeOutlined
} from '@ant-design/icons';
import { fetchData } from '../../../services/unifiedDataService';
import dayjs from 'dayjs';
import { JsonView } from 'react-json-view-lite';
import 'react-json-view-lite/dist/index.css';

const { Option } = Select;
const { RangePicker } = DatePicker;
const { Title, Text, Paragraph } = Typography;

// 日志类型
type LogType = 'request' | 'error' | 'alert' | 'audit';

// 日志级别
type LogLevel = 'info' | 'warning' | 'error' | 'critical';

// 日志查看器组件
const LogViewer: React.FC = () => {
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);
  const [logs, setLogs] = useState<any[]>([]);
  const [logType, setLogType] = useState<LogType>('request');
  const [searchText, setSearchText] = useState<string>('');
  const [timeRange, setTimeRange] = useState<[dayjs.Dayjs, dayjs.Dayjs]>([
    dayjs().subtract(1, 'day'),
    dayjs(),
  ]);
  const [logLevel, setLogLevel] = useState<LogLevel | ''>('');
  const [pagination, setPagination] = useState({
    current: 1,
    pageSize: 10,
    total: 0,
  });
  const [detailVisible, setDetailVisible] = useState<boolean>(false);
  const [selectedLog, setSelectedLog] = useState<any>(null);

  // 加载日志数据
  const loadLogs = async (page = 1, pageSize = 10) => {
    setLoading(true);
    setError(null);

    try {
      const response = await fetchData(
        'admin/logs',
        'getLogs',
        {
          type: logType,
          level: logLevel || undefined,
          search: searchText || undefined,
          startTime: timeRange[0].toISOString(),
          endTime: timeRange[1].toISOString(),
          page,
          pageSize,
        },
        {
          method: 'GET',
          headers: {
            Authorization: `Bearer ${localStorage.getItem('authToken')}`,
          },
        }
      );

      if (response.success) {
        setLogs(response.logs || []);
        setPagination({
          ...pagination,
          current: page,
          pageSize,
          total: response.total || 0,
        });
      } else {
        setError(response.error || '获取日志失败');
      }
    } catch (err) {
      setError('获取日志时发生错误');
      console.error('获取日志错误:', err);
    } finally {
      setLoading(false);
    }
  };

  // 首次加载和筛选条件变化时加载数据
  useEffect(() => {
    loadLogs(1, pagination.pageSize);
  }, [logType, logLevel, timeRange]);

  // 处理表格变化
  const handleTableChange = (pagination: any) => {
    loadLogs(pagination.current, pagination.pageSize);
  };

  // 处理搜索
  const handleSearch = () => {
    loadLogs(1, pagination.pageSize);
  };

  // 处理查看日志详情
  const handleViewDetail = (log: any) => {
    setSelectedLog(log);
    setDetailVisible(true);
  };

  // 导出日志
  const handleExportLogs = () => {
    // 创建日志数据的 JSON 字符串
    const jsonString = JSON.stringify(logs, null, 2);

    // 创建 Blob 对象
    const blob = new Blob([jsonString], { type: 'application/json' });

    // 创建下载链接
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `logs_${logType}_${dayjs().format('YYYYMMDD_HHmmss')}.json`;

    // 触发下载
    document.body.appendChild(a);
    a.click();

    // 清理
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
  };

  // 获取日志级别标签
  const getLevelTag = (level: LogLevel) => {
    const levelConfig = {
      info: { color: 'blue', icon: <InfoCircleOutlined /> },
      warning: { color: 'orange', icon: <WarningOutlined /> },
      error: { color: 'red', icon: <CloseCircleOutlined /> },
      critical: { color: 'purple', icon: <CloseCircleOutlined /> },
    };

    const config = levelConfig[level] || levelConfig.info;

    return (
      <Tag color={config.color} icon={config.icon}>
        {level.toUpperCase()}
      </Tag>
    );
  };

  // 获取状态码标签
  const getStatusTag = (status: number) => {
    let color = 'green';
    let icon = <CheckCircleOutlined />;

    if (status >= 400 && status < 500) {
      color = 'orange';
      icon = <WarningOutlined />;
    } else if (status >= 500) {
      color = 'red';
      icon = <CloseCircleOutlined />;
    }

    return (
      <Tag color={color} icon={icon}>
        {status}
      </Tag>
    );
  };

  // 表格列定义
  const columns = [
    {
      title: '时间',
      dataIndex: 'timestamp',
      key: 'timestamp',
      render: (text: string) => dayjs(text).format('YYYY-MM-DD HH:mm:ss'),
      width: 180,
    },
    logType === 'request' && {
      title: '方法',
      dataIndex: 'method',
      key: 'method',
      width: 100,
      render: (text: string) => (
        <Tag color={
          text === 'GET' ? 'blue' :
          text === 'POST' ? 'green' :
          text === 'PUT' ? 'orange' :
          text === 'DELETE' ? 'red' : 'default'
        }>
          {text}
        </Tag>
      ),
    },
    logType === 'request' && {
      title: '路径',
      dataIndex: 'path',
      key: 'path',
      ellipsis: true,
    },
    logType === 'request' && {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      width: 100,
      render: (status: number) => getStatusTag(status),
    },
    logType === 'request' && {
      title: '耗时',
      dataIndex: 'duration',
      key: 'duration',
      width: 100,
      render: (duration: number) => `${duration} ms`,
    },
    (logType === 'error' || logType === 'alert') && {
      title: '级别',
      dataIndex: 'level',
      key: 'level',
      width: 100,
      render: (level: LogLevel) => getLevelTag(level),
    },
    (logType === 'error' || logType === 'alert') && {
      title: '消息',
      dataIndex: 'message',
      key: 'message',
      ellipsis: true,
    },
    logType === 'audit' && {
      title: '用户',
      dataIndex: 'username',
      key: 'username',
      width: 120,
    },
    logType === 'audit' && {
      title: '操作',
      dataIndex: 'action',
      key: 'action',
      ellipsis: true,
    },
    {
      title: 'IP地址',
      dataIndex: 'ip',
      key: 'ip',
      width: 130,
    },
    {
      title: '操作',
      key: 'action',
      width: 100,
      render: (_: any, record: any) => (
        <Button
          type="link"
          icon={<EyeOutlined />}
          onClick={() => handleViewDetail(record)}
        >
          详情
        </Button>
      ),
    },
  ].filter(Boolean);

  return (
    <div className="log-viewer">
      <Card>
        <div style={{ marginBottom: 16, display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
          <Title level={4}>系统日志</Title>
          <Space>
            <Button
              type="primary"
              icon={<DownloadOutlined />}
              onClick={handleExportLogs}
              disabled={logs.length === 0}
            >
              导出日志
            </Button>
          </Space>
        </div>

        {/* 筛选条件 */}
        <div style={{ marginBottom: 16 }}>
          <Space wrap>
            <Select
              value={logType}
              onChange={setLogType}
              style={{ width: 120 }}
            >
              <Option value="request">请求日志</Option>
              <Option value="error">错误日志</Option>
              <Option value="alert">告警日志</Option>
              <Option value="audit">审计日志</Option>
            </Select>

            {(logType === 'error' || logType === 'alert') && (
              <Select
                value={logLevel}
                onChange={setLogLevel}
                style={{ width: 120 }}
                placeholder="日志级别"
                allowClear
              >
                <Option value="info">信息</Option>
                <Option value="warning">警告</Option>
                <Option value="error">错误</Option>
                <Option value="critical">严重</Option>
              </Select>
            )}

            <RangePicker
              value={timeRange}
              onChange={(dates) => {
                if (dates) {
                  setTimeRange([dates[0]!, dates[1]!]);
                }
              }}
              showTime
              allowClear={false}
            />

            <Input
              placeholder="搜索关键词"
              value={searchText}
              onChange={(e) => setSearchText(e.target.value)}
              style={{ width: 200 }}
              onPressEnter={handleSearch}
              suffix={
                <Tooltip title="搜索日志内容">
                  <InfoCircleOutlined style={{ color: 'rgba(0,0,0,.45)' }} />
                </Tooltip>
              }
            />

            <Button
              type="primary"
              icon={<SearchOutlined />}
              onClick={handleSearch}
            >
              搜索
            </Button>

            <Button
              icon={<ReloadOutlined />}
              onClick={() => loadLogs(pagination.current, pagination.pageSize)}
              loading={loading}
            >
              刷新
            </Button>
          </Space>
        </div>

        {error && (
          <Alert
            message="错误"
            description={error}
            type="error"
            showIcon
            style={{ marginBottom: 16 }}
          />
        )}

        {/* 日志表格 */}
        <Table
          columns={columns}
          dataSource={logs}
          rowKey="id"
          pagination={pagination}
          onChange={handleTableChange}
          loading={loading}
          scroll={{ x: 'max-content' }}
        />

        {/* 日志详情抽屉 */}
        <Drawer
          title="日志详情"
          placement="right"
          width={600}
          onClose={() => setDetailVisible(false)}
          visible={detailVisible}
        >
          {selectedLog && (
            <div>
              <Paragraph>
                <Text strong>时间：</Text> {dayjs(selectedLog.timestamp).format('YYYY-MM-DD HH:mm:ss')}
              </Paragraph>

              {logType === 'request' && (
                <>
                  <Paragraph>
                    <Text strong>方法：</Text> {selectedLog.method}
                  </Paragraph>
                  <Paragraph>
                    <Text strong>路径：</Text> {selectedLog.path}
                  </Paragraph>
                  <Paragraph>
                    <Text strong>状态码：</Text> {getStatusTag(selectedLog.status)}
                  </Paragraph>
                  <Paragraph>
                    <Text strong>响应时间：</Text> {selectedLog.duration} ms
                  </Paragraph>
                  <Paragraph>
                    <Text strong>IP地址：</Text> {selectedLog.ip}
                  </Paragraph>
                  <Paragraph>
                    <Text strong>查询参数：</Text>
                  </Paragraph>
                  <JsonView data={selectedLog.query || {}} />
                  <Paragraph style={{ marginTop: 16 }}>
                    <Text strong>请求头：</Text>
                  </Paragraph>
                  <JsonView data={selectedLog.headers || {}} />
                </>
              )}

              {(logType === 'error' || logType === 'alert') && (
                <>
                  <Paragraph>
                    <Text strong>级别：</Text> {getLevelTag(selectedLog.level)}
                  </Paragraph>
                  <Paragraph>
                    <Text strong>消息：</Text> {selectedLog.message}
                  </Paragraph>
                  {selectedLog.error && (
                    <>
                      <Paragraph>
                        <Text strong>错误详情：</Text>
                      </Paragraph>
                      <JsonView data={selectedLog.error} />
                    </>
                  )}
                </>
              )}

              {logType === 'audit' && (
                <>
                  <Paragraph>
                    <Text strong>用户：</Text> {selectedLog.username}
                  </Paragraph>
                  <Paragraph>
                    <Text strong>角色：</Text> {selectedLog.role}
                  </Paragraph>
                  <Paragraph>
                    <Text strong>操作：</Text> {selectedLog.action}
                  </Paragraph>
                  <Paragraph>
                    <Text strong>目标：</Text> {selectedLog.target}
                  </Paragraph>
                  <Paragraph>
                    <Text strong>结果：</Text> {selectedLog.result === 'success' ? <Badge status="success" text="成功" /> : <Badge status="error" text="失败" />}
                  </Paragraph>
                  {selectedLog.details && (
                    <>
                      <Paragraph>
                        <Text strong>详细信息：</Text>
                      </Paragraph>
                      <JsonView data={selectedLog.details} />
                    </>
                  )}
                </>
              )}
            </div>
          )}
        </Drawer>
      </Card>
    </div>
  );
};

export default LogViewer;
