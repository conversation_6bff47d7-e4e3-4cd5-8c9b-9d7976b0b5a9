/**
 * 简化的大量测试数据生成脚本
 *
 * 功能：
 * 1. 生成50份随机问卷（包含建议和观察内容，已审核状态）
 * 2. 生成30份故事墙内容（匿名注册用户发布）
 * 3. 生成审核员和管理员测试账户
 */

import { execSync } from 'child_process';
import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// 数据库路径
const DB_PATH = path.join(__dirname, '../database.db');

// 随机数据生成器
const randomChoice = (arr) => arr[Math.floor(Math.random() * arr.length)];
const randomInt = (min, max) => Math.floor(Math.random() * (max - min + 1)) + min;
const randomDate = (start, end) => new Date(start.getTime() + Math.random() * (end.getTime() - start.getTime()));

// 生成UUID
const generateUUID = (prefix) => {
  const timestamp = Date.now();
  const random = Math.random().toString(36).substr(2, 9);
  return `${prefix}_${timestamp}_${random}`;
};

// 生成匿名用户AB值
const generateABValues = () => {
  const a = Math.random().toString().substr(2, 11); // 11位数字
  const b = Math.random().toString().substr(2, randomChoice([4, 6])); // 4位或6位数字
  return { a, b };
};

// 执行 SQL 文件
function executeSqlFile(filePath) {
  try {
    const command = `sqlite3 "${DB_PATH}" < "${filePath}"`;
    execSync(command, { stdio: 'pipe' });
    console.log(`  ✅ 执行SQL文件: ${path.basename(filePath)}`);
  } catch (error) {
    console.error(`  ❌ SQL文件执行失败: ${error.message}`);
    throw error;
  }
}

// 查询数据
function queryData(sql) {
  try {
    const command = `sqlite3 "${DB_PATH}" "${sql.replace(/"/g, '\\"')}"`;
    const result = execSync(command, { encoding: 'utf8' });
    return result.trim();
  } catch (error) {
    console.error('查询失败:', error.message);
    return '';
  }
}

// 基础数据配置
const educationLevels = [
  { code: 'undergraduate', display: '本科' },
  { code: 'master', display: '硕士' },
  { code: 'associate', display: '大专' },
  { code: 'phd', display: '博士' }
];

const regions = [
  { code: 'tier1_cities', display: '北上广深' },
  { code: 'provincial_capitals', display: '省会城市' },
  { code: 'tier2_cities', display: '二线城市' },
  { code: 'tier3_cities', display: '三四线城市' }
];

const majors = [
  '计算机科学与技术', '软件工程', '金融学', '市场营销', '机械工程',
  '电子信息工程', '国际贸易', '会计学', '英语', '法学', '医学', '建筑学'
];

const industries = [
  { code: 'technology', display: '互联网/IT' },
  { code: 'finance', display: '金融' },
  { code: 'service', display: '服务业' },
  { code: 'manufacturing', display: '制造业' },
  { code: 'education', display: '教育' }
];

const employmentStatuses = ['employed', 'unemployed', 'studying', 'entrepreneurship'];
const salaryRanges = ['3000-5000', '5000-8000', '8000-12000', '12000-20000', '20000-30000'];

// 建议内容模板
const adviceTemplates = [
  '建议学弟学妹们要注重实践能力的培养，多参加实习和项目经验。',
  '在大学期间一定要多参加社团活动，锻炼自己的组织协调能力。',
  '学好英语非常重要，这在求职时会是一个很大的加分项。',
  '要提前做好职业规划，明确自己的发展方向和目标。',
  '多关注行业动态，了解市场需求，这样能更好地规划自己的学习方向。',
  '不要只看重薪资，公司的发展前景和学习机会同样重要。',
  '面试前一定要做充分准备，包括简历优化、面试技巧和专业知识复习。',
  '要培养自己的沟通能力和团队协作精神，这在职场中非常重要。',
  '保持学习的心态，技术更新很快，要不断提升自己的专业技能。',
  '要有耐心，找工作是一个过程，不要因为一两次失败就放弃。'
];

// 观察内容模板
const observationTemplates = [
  '当前互联网行业的就业形势总体向好，特别是技术技能的需求在增长。',
  '疫情对就业市场产生了一定影响，但也催生了很多新的就业机会。',
  '现在企业更看重候选人的综合素质，不仅仅是专业技能。',
  '远程工作成为了新趋势，这为求职者提供了更多的选择。',
  '一线城市机会多但竞争激烈，二三线城市的发展机会也不错。',
  '新兴行业如人工智能、新能源等领域的就业前景很好。',
  '企业对应届毕业生的要求越来越高，需要有实际项目经验。',
  '职业教育和技能培训变得越来越重要，终身学习成为趋势。',
  '创业环境越来越好，很多年轻人选择自主创业。',
  '就业市场呈现多元化趋势，传统行业也在数字化转型。'
];

// 故事内容模板
const storyTemplates = [
  {
    title: '从迷茫到清晰：我的职业规划之路',
    content: '刚毕业的时候，我对未来充满了不确定性。经过了几个月的求职经历，我逐渐明确了自己的职业方向。现在回想起来，那段迷茫的时光其实是成长的必经之路。通过不断的尝试和反思，我找到了真正适合自己的道路。'
  },
  {
    title: '三次面试失败后，我终于找到了理想工作',
    content: '记得第一次面试时的紧张和兴奋，虽然没有成功，但每次面试都是一次宝贵的学习机会。第四次面试时，我终于成功了，那种喜悦至今难忘。失败教会了我如何更好地准备，如何展示自己的优势。'
  },
  {
    title: '在北京打拼的这些年',
    content: '从本科毕业后，我选择了北京作为我职业生涯的起点。这里的机会和挑战并存，虽然生活成本高，但成长的速度也很快。在这座城市里，我学会了独立，学会了坚持，也收获了珍贵的友谊和经验。'
  },
  {
    title: '转行的勇气与收获',
    content: '工作三年后，我发现自己对当前的工作失去了热情。经过深思熟虑，我决定转行。虽然过程艰难，但最终找到了真正适合自己的方向。转行让我重新认识了自己，也让我明白了什么是真正的职业满足感。'
  },
  {
    title: '实习经历改变了我的人生轨迹',
    content: '大三的那次实习经历让我对职场有了全新的认识。从一个懵懂的学生到能够独当一面的职场新人，这个转变让我受益匪浅。实习不仅让我掌握了专业技能，更重要的是培养了我的职业素养和工作态度。'
  }
];

// 主函数
async function generateLargeTestData() {
  console.log('🚀 开始生成大量测试数据...');
  console.log('======================================');

  try {
    console.log('📊 生成50份随机问卷数据...');
    await generateQuestionnaireData();

    console.log('📖 生成30份故事墙内容...');
    await generateStoryData();

    console.log('👥 生成审核员和管理员账户...');
    await generateReviewerAccounts();

    console.log('✅ 大量测试数据生成完成！');
    console.log('======================================');

    // 生成统计报告
    generateReport();

  } catch (error) {
    console.error('❌ 生成测试数据失败:', error);
    throw error;
  }
}

// 生成问卷数据
async function generateQuestionnaireData() {
  const sqlStatements = [];

  for (let i = 0; i < 50; i++) {
    const userId = generateUUID('anon');
    const questionnaireId = generateUUID('quest');
    const sessionId = generateUUID('sess');
    const { a, b } = generateABValues();

    const education = randomChoice(educationLevels);
    const region = randomChoice(regions);
    const major = randomChoice(majors);
    const industry = randomChoice(industries);
    const employmentStatus = randomChoice(employmentStatuses);
    const salaryRange = randomChoice(salaryRanges);

    const adviceContent = randomChoice(adviceTemplates).replace(/'/g, "''");
    const observationContent = randomChoice(observationTemplates).replace(/'/g, "''");

    const createdAt = randomDate(new Date(2024, 0, 1), new Date()).toISOString();

    // 插入用户
    sqlStatements.push(`INSERT OR IGNORE INTO users_v2 (id, user_type, display_name, identity_a, identity_b, created_at) VALUES ('${userId}', 'anonymous', NULL, '${a}', '${b}', '${createdAt}');`);

    // 插入问卷回复
    sqlStatements.push(`INSERT INTO questionnaire_responses_v2 (id, user_id, session_id, education_level, education_level_display, major_category, major_display, graduation_year, region_code, region_display, employment_status, current_industry_code, current_industry_display, salary_range, advice_content, observation_content, status, created_at) VALUES ('${questionnaireId}', '${userId}', '${sessionId}', '${education.code}', '${education.display}', '${major}', '${major}', ${randomInt(2020, 2024)}, '${region.code}', '${region.display}', '${employmentStatus}', '${industry.code}', '${industry.display}', '${salaryRange}', '${adviceContent}', '${observationContent}', 'processed', '${createdAt}');`);

    // 插入问卷心声（建议）
    const voiceId1 = generateUUID('voice');
    sqlStatements.push(`INSERT INTO questionnaire_voices_v2 (id, source_response_id, user_id, voice_type, title, content, education_level, education_level_display, region_code, region_display, status, likes, views, created_at) VALUES ('${voiceId1}', '${questionnaireId}', '${userId}', 'advice', '给高三学子的建议', '${adviceContent}', '${education.code}', '${education.display}', '${region.code}', '${region.display}', 'approved', ${randomInt(5, 50)}, ${randomInt(50, 500)}, '${createdAt}');`);

    // 插入问卷心声（观察）
    const voiceId2 = generateUUID('voice');
    sqlStatements.push(`INSERT INTO questionnaire_voices_v2 (id, source_response_id, user_id, voice_type, title, content, education_level, education_level_display, region_code, region_display, status, likes, views, created_at) VALUES ('${voiceId2}', '${questionnaireId}', '${userId}', 'observation', '对当前就业环境的观察', '${observationContent}', '${education.code}', '${education.display}', '${region.code}', '${region.display}', 'approved', ${randomInt(3, 30)}, ${randomInt(30, 300)}, '${createdAt}');`);
  }

  // 写入SQL文件并执行
  const sqlFile = path.join(__dirname, 'questionnaire-data.sql');
  fs.writeFileSync(sqlFile, sqlStatements.join('\n'));
  executeSqlFile(sqlFile);
  fs.unlinkSync(sqlFile);

  console.log('  ✅ 50份问卷数据生成完成');
}

// 生成故事数据
async function generateStoryData() {
  const sqlStatements = [];

  for (let i = 0; i < 30; i++) {
    const userId = generateUUID('user');
    const storyId = generateUUID('story');
    const { a, b } = generateABValues();

    const education = randomChoice(educationLevels);
    const industry = randomChoice(industries);
    const storyTemplate = randomChoice(storyTemplates);

    const createdAt = randomDate(new Date(2024, 0, 1), new Date()).toISOString();
    const wordCount = randomInt(200, 1000);
    const readingTime = Math.ceil(wordCount / 200);

    const title = storyTemplate.title.replace(/'/g, "''");
    const content = storyTemplate.content.replace(/'/g, "''");
    const summary = content.substring(0, 100).replace(/'/g, "''") + '...';

    // 插入匿名注册用户
    sqlStatements.push(`INSERT OR IGNORE INTO users_v2 (id, user_type, display_name, identity_a, identity_b, created_at) VALUES ('${userId}', 'registered', '匿名用户${i + 1}', '${a}', '${b}', '${createdAt}');`);

    // 插入故事内容
    sqlStatements.push(`INSERT INTO story_contents_v2 (id, user_id, title, content, summary, category, education_level, education_level_display, industry_code, industry_display, story_type, is_anonymous, status, likes, dislikes, views, quality_score, word_count, reading_time, created_at) VALUES ('${storyId}', '${userId}', '${title}', '${content}', '${summary}', '${randomChoice(['求职经验', '职场感悟', '学习心得', '转行经历', '创业故事'])}', '${education.code}', '${education.display}', '${industry.code}', '${industry.display}', 'experience', 1, 'approved', ${randomInt(10, 100)}, ${randomInt(0, 5)}, ${randomInt(100, 1000)}, ${(randomInt(35, 50) / 10).toFixed(1)}, ${wordCount}, ${readingTime}, '${createdAt}');`);
  }

  // 写入SQL文件并执行
  const sqlFile = path.join(__dirname, 'story-data.sql');
  fs.writeFileSync(sqlFile, sqlStatements.join('\n'));
  executeSqlFile(sqlFile);
  fs.unlinkSync(sqlFile);

  console.log('  ✅ 30份故事数据生成完成');
}

// 生成审核员账户
async function generateReviewerAccounts() {
  const sqlStatements = [];

  // 生成5个审核员账户
  for (let i = 1; i <= 5; i++) {
    const userId = generateUUID('reviewer');
    sqlStatements.push(`INSERT OR IGNORE INTO users_v2 (id, user_type, display_name, email, password_hash, created_at) VALUES ('${userId}', 'reviewer', '审核员${i}', 'reviewer${i}@test.com', 'hashed_password_${i}', '${new Date().toISOString()}');`);
  }

  // 生成3个管理员账户
  for (let i = 1; i <= 3; i++) {
    const userId = generateUUID('admin');
    sqlStatements.push(`INSERT OR IGNORE INTO users_v2 (id, user_type, display_name, email, password_hash, created_at) VALUES ('${userId}', 'admin', '管理员${i}', 'admin${i}@test.com', 'hashed_password_admin_${i}', '${new Date().toISOString()}');`);
  }

  // 写入SQL文件并执行
  const sqlFile = path.join(__dirname, 'accounts-data.sql');
  fs.writeFileSync(sqlFile, sqlStatements.join('\n'));
  executeSqlFile(sqlFile);
  fs.unlinkSync(sqlFile);

  console.log('  ✅ 审核员和管理员账户生成完成');
}

// 生成统计报告
function generateReport() {
  console.log('\n📊 数据生成统计报告:');
  console.log('================================');

  const tables = [
    'users_v2',
    'questionnaire_responses_v2',
    'questionnaire_voices_v2',
    'story_contents_v2'
  ];

  tables.forEach(table => {
    try {
      const count = queryData(`SELECT COUNT(*) FROM ${table}`);
      console.log(`${table}: ${count} 条记录`);
    } catch (error) {
      console.log(`${table}: 查询失败`);
    }
  });

  console.log('================================');
  console.log('🎉 测试数据生成完成！');
  console.log('');
  console.log('📋 数据说明:');
  console.log('- 50份问卷回复（已审核状态）');
  console.log('- 100条问卷心声（建议+观察，已审核状态）');
  console.log('- 30份故事墙内容（已审核状态）');
  console.log('- 5个审核员账户');
  console.log('- 3个管理员账户');
  console.log('');
  console.log('🔧 下一步: 重启API服务器以加载新数据');
}

// 如果直接运行此脚本
if (import.meta.url === `file://${process.argv[1]}`) {
  generateLargeTestData().catch(console.error);
}

export { generateLargeTestData };
