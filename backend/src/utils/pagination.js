/**
 * 🔧 分页工具函数库
 * 统一的分页逻辑，避免重复代码
 */

/**
 * 计算分页偏移量
 * @param {number} page - 页码（从1开始）
 * @param {number} limit - 每页数量
 * @returns {number} 偏移量
 */
export function calculateOffset(page, limit) {
  return (page - 1) * limit;
}

/**
 * 创建分页响应对象
 * @param {Array} data - 数据数组
 * @param {number} total - 总数量
 * @param {number} page - 当前页码
 * @param {number} limit - 每页数量
 * @returns {Object} 分页响应对象
 */
export function createPaginationResponse(data, total, page, limit) {
  return {
    data,
    pagination: {
      total,
      page,
      limit,
      pages: Math.ceil(total / limit),
      hasNext: page * limit < total,
      hasPrev: page > 1
    }
  };
}

/**
 * 验证分页参数
 * @param {number} page - 页码
 * @param {number} limit - 每页数量
 * @param {number} maxLimit - 最大每页数量
 * @returns {Object} 验证后的参数
 */
export function validatePaginationParams(page = 1, limit = 20, maxLimit = 100) {
  const validPage = Math.max(1, parseInt(page) || 1);
  const validLimit = Math.min(maxLimit, Math.max(1, parseInt(limit) || 20));
  
  return {
    page: validPage,
    limit: validLimit,
    offset: calculateOffset(validPage, validLimit)
  };
}

/**
 * 从URL查询参数中提取分页参数
 * @param {URL} url - URL对象
 * @param {Object} options - 选项
 * @returns {Object} 分页参数
 */
export function extractPaginationFromUrl(url, options = {}) {
  const { defaultLimit = 20, maxLimit = 100 } = options;
  
  const page = parseInt(url.searchParams.get('page')) || 1;
  const limit = parseInt(url.searchParams.get('limit')) || defaultLimit;
  
  return validatePaginationParams(page, limit, maxLimit);
}

/**
 * 创建分页元数据
 * @param {number} total - 总数量
 * @param {number} page - 当前页码
 * @param {number} limit - 每页数量
 * @returns {Object} 分页元数据
 */
export function createPaginationMeta(total, page, limit) {
  const totalPages = Math.ceil(total / limit);
  
  return {
    total,
    page,
    limit,
    totalPages,
    hasNext: page < totalPages,
    hasPrev: page > 1,
    isFirst: page === 1,
    isLast: page === totalPages
  };
}
