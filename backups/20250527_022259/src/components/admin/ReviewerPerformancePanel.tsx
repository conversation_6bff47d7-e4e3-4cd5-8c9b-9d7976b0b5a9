import React, { useState, useEffect } from 'react';
import { useToast } from '@/components/ui/use-toast';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { RefreshCw, Award, Clock, CheckCircle, XCircle, Edit, BarChart } from 'lucide-react';
import LoadingSpinner from '@/components/ui/loading-spinner';
import ErrorDisplay from '@/components/ui/error-display';
import { format } from 'date-fns';
import { zhCN } from 'date-fns/locale';

// 导入数据服务
import { getReviewerPerformance } from '@/services/dataService';

// 审核员绩效接口
interface ReviewerPerformance {
  reviewerId: string;
  reviewerName: string;
  totalReviewed: number;
  approved: number;
  rejected: number;
  edited: number;
  averageResponseTime: number; // 单位：分钟
  lastActive: string;
}

// 审核员绩效面板组件
export default function ReviewerPerformancePanel() {
  const { toast } = useToast();
  const [reviewers, setReviewers] = useState<ReviewerPerformance[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [timeRange, setTimeRange] = useState('week'); // 'day', 'week', 'month', 'year'
  
  // 获取审核员绩效数据
  const fetchReviewerPerformance = async () => {
    try {
      setIsLoading(true);
      setError(null);
      
      const response = await getReviewerPerformance(timeRange);
      
      if (response.success) {
        setReviewers(response.reviewers);
      } else {
        setError(response.error || '获取审核员绩效数据失败');
      }
    } catch (error) {
      console.error('获取审核员绩效数据错误:', error);
      setError('服务器错误，请稍后再试');
    } finally {
      setIsLoading(false);
    }
  };
  
  // 组件加载时获取审核员绩效数据
  useEffect(() => {
    fetchReviewerPerformance();
  }, [timeRange]);
  
  // 格式化响应时间
  const formatResponseTime = (minutes: number) => {
    if (minutes < 60) {
      return `${minutes.toFixed(1)} 分钟`;
    } else {
      const hours = minutes / 60;
      return `${hours.toFixed(1)} 小时`;
    }
  };
  
  // 渲染加载状态
  if (isLoading) {
    return (
      <div className="flex justify-center items-center py-12">
        <LoadingSpinner size="lg" text="加载审核员绩效数据中..." />
      </div>
    );
  }
  
  // 渲染错误状态
  if (error) {
    return (
      <ErrorDisplay 
        title="加载失败" 
        message={error} 
        onRetry={fetchReviewerPerformance} 
      />
    );
  }
  
  return (
    <div className="space-y-4">
      <div className="flex justify-between items-center">
        <h2 className="text-2xl font-bold flex items-center">
          <Award className="h-6 w-6 mr-2 text-amber-500" />
          审核员绩效
        </h2>
        
        <div className="flex items-center space-x-2">
          <Select
            value={timeRange}
            onValueChange={(value) => setTimeRange(value)}
          >
            <SelectTrigger className="w-[150px]">
              <SelectValue placeholder="时间范围" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="day">今日</SelectItem>
              <SelectItem value="week">本周</SelectItem>
              <SelectItem value="month">本月</SelectItem>
              <SelectItem value="year">今年</SelectItem>
            </SelectContent>
          </Select>
          
          <Button variant="outline" onClick={fetchReviewerPerformance}>
            <RefreshCw className="h-4 w-4 mr-2" />
            刷新
          </Button>
        </div>
      </div>
      
      {reviewers.length === 0 ? (
        <div className="flex flex-col items-center justify-center p-12 bg-muted/50 rounded-lg">
          <BarChart className="h-12 w-12 text-muted-foreground mb-4" />
          <h3 className="text-xl font-medium">暂无审核员绩效数据</h3>
          <p className="text-muted-foreground mt-2">在所选时间范围内没有审核活动</p>
        </div>
      ) : (
        <Card>
          <CardHeader>
            <CardTitle>审核员绩效统计</CardTitle>
            <CardDescription>
              {timeRange === 'day' && '今日'}
              {timeRange === 'week' && '本周'}
              {timeRange === 'month' && '本月'}
              {timeRange === 'year' && '今年'}
              审核员工作情况统计
            </CardDescription>
          </CardHeader>
          <CardContent>
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>审核员</TableHead>
                  <TableHead>总审核量</TableHead>
                  <TableHead>通过</TableHead>
                  <TableHead>拒绝</TableHead>
                  <TableHead>编辑</TableHead>
                  <TableHead>平均响应时间</TableHead>
                  <TableHead>最近活动</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {reviewers.map((reviewer) => (
                  <TableRow key={reviewer.reviewerId}>
                    <TableCell className="font-medium">{reviewer.reviewerName}</TableCell>
                    <TableCell>{reviewer.totalReviewed}</TableCell>
                    <TableCell>
                      <div className="flex items-center">
                        <CheckCircle className="h-4 w-4 mr-1 text-green-500" />
                        {reviewer.approved}
                      </div>
                    </TableCell>
                    <TableCell>
                      <div className="flex items-center">
                        <XCircle className="h-4 w-4 mr-1 text-red-500" />
                        {reviewer.rejected}
                      </div>
                    </TableCell>
                    <TableCell>
                      <div className="flex items-center">
                        <Edit className="h-4 w-4 mr-1 text-blue-500" />
                        {reviewer.edited}
                      </div>
                    </TableCell>
                    <TableCell>{formatResponseTime(reviewer.averageResponseTime)}</TableCell>
                    <TableCell>
                      <div className="flex items-center">
                        <Clock className="h-4 w-4 mr-1 text-muted-foreground" />
                        {format(new Date(reviewer.lastActive), 'yyyy-MM-dd HH:mm', { locale: zhCN })}
                      </div>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </CardContent>
        </Card>
      )}
    </div>
  );
}
