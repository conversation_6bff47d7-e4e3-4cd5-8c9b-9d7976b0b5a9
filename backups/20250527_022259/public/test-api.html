<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>API连接测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        .test-result {
            margin: 10px 0;
            padding: 10px;
            border-radius: 4px;
        }
        .success {
            background-color: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
        }
        .error {
            background-color: #f8d7da;
            border: 1px solid #f5c6cb;
            color: #721c24;
        }
        .loading {
            background-color: #d1ecf1;
            border: 1px solid #bee5eb;
            color: #0c5460;
        }
        pre {
            background-color: #f8f9fa;
            padding: 10px;
            border-radius: 4px;
            overflow-x: auto;
        }
        button {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background-color: #0056b3;
        }
    </style>
</head>
<body>
    <h1>API连接测试</h1>
    
    <div>
        <button onclick="testQuestionnaireStats()">测试问卷统计API</button>
        <button onclick="testVisualizationData()">测试可视化数据API</button>
        <button onclick="testDirectAPI()">直接测试后端API</button>
    </div>

    <div id="results"></div>

    <script>
        function addResult(title, status, data) {
            const resultsDiv = document.getElementById('results');
            const resultDiv = document.createElement('div');
            resultDiv.className = `test-result ${status}`;
            
            let content = `<h3>${title}</h3>`;
            if (status === 'loading') {
                content += '<p>正在测试...</p>';
            } else if (status === 'success') {
                content += '<p>✅ 成功</p>';
                if (data) {
                    content += `<pre>${JSON.stringify(data, null, 2)}</pre>`;
                }
            } else {
                content += '<p>❌ 失败</p>';
                if (data) {
                    content += `<pre>${data}</pre>`;
                }
            }
            
            resultDiv.innerHTML = content;
            resultsDiv.appendChild(resultDiv);
        }

        async function testQuestionnaireStats() {
            addResult('问卷统计API测试', 'loading');
            
            try {
                const response = await fetch('/api/questionnaire/stats');
                const data = await response.json();
                
                if (response.ok && data.success) {
                    addResult('问卷统计API测试', 'success', {
                        totalResponses: data.statistics.totalResponses,
                        verifiedCount: data.statistics.verifiedCount,
                        anonymousCount: data.statistics.anonymousCount,
                        educationLevels: data.statistics.educationLevels.slice(0, 3)
                    });
                } else {
                    addResult('问卷统计API测试', 'error', `API返回错误: ${JSON.stringify(data)}`);
                }
            } catch (error) {
                addResult('问卷统计API测试', 'error', `网络错误: ${error.message}`);
            }
        }

        async function testVisualizationData() {
            addResult('可视化数据API测试', 'loading');
            
            try {
                const response = await fetch('/api/visualization/data');
                const data = await response.json();
                
                if (response.ok && data.success) {
                    addResult('可视化数据API测试', 'success', {
                        totalResponses: data.stats.totalResponses,
                        educationLevels: data.stats.educationLevels.slice(0, 3),
                        regions: data.stats.regions.slice(0, 3)
                    });
                } else {
                    addResult('可视化数据API测试', 'error', `API返回错误: ${JSON.stringify(data)}`);
                }
            } catch (error) {
                addResult('可视化数据API测试', 'error', `网络错误: ${error.message}`);
            }
        }

        async function testDirectAPI() {
            addResult('直接后端API测试', 'loading');
            
            try {
                const response = await fetch('http://localhost:8788/api/questionnaire/stats');
                const data = await response.json();
                
                if (response.ok && data.success) {
                    addResult('直接后端API测试', 'success', {
                        totalResponses: data.statistics.totalResponses,
                        source: '直接连接到后端'
                    });
                } else {
                    addResult('直接后端API测试', 'error', `API返回错误: ${JSON.stringify(data)}`);
                }
            } catch (error) {
                addResult('直接后端API测试', 'error', `网络错误: ${error.message}`);
            }
        }

        // 页面加载时自动运行测试
        window.onload = function() {
            console.log('开始API连接测试...');
            setTimeout(testQuestionnaireStats, 500);
            setTimeout(testVisualizationData, 1000);
            setTimeout(testDirectAPI, 1500);
        };
    </script>
</body>
</html>
