/**
 * 常量定义
 */

/**
 * 内容类型枚举
 */
export enum ContentType {
  STORY = 'story',
  QUESTIONNAIRE = 'questionnaire',
  COMMENT = 'comment',
  PROFILE = 'profile',
  FEEDBACK = 'feedback'
}

/**
 * 审核状态枚举
 */
export enum ReviewStatus {
  PENDING = 'pending',
  APPROVED = 'approved',
  REJECTED = 'rejected',
  FLAGGED = 'flagged'
}

/**
 * 用户角色枚举
 */
export enum UserRole {
  USER = 'user',
  REVIEWER = 'reviewer',
  ADMIN = 'admin',
  SUPERADMIN = 'superadmin'
}

/**
 * 通知类型枚举
 */
export enum NotificationType {
  // 用户通知
  CONTENT_APPROVED = 'content-approve',
  CONTENT_REJECTED = 'content-reject',
  CONTENT_REVIEW = 'content-review',
  FEEDBACK_RESPONSE = 'feedback-response',
  APPEAL_APPROVED = 'appeal-approved',
  APPEAL_REJECTED = 'appeal-rejected',

  // 管理员通知
  ADMIN_CONTENT_APPROVED = 'admin-content-approve',
  ADMIN_CONTENT_REJECTED = 'admin-content-reject',
  ADMIN_CONTENT_REVIEW = 'admin-content-review',
  ADMIN_FEEDBACK_RECEIVED = 'admin-feedback-received',
  ADMIN_APPEAL_RECEIVED = 'admin-appeal-received',

  // 系统通知
  SYSTEM_ERROR = 'system-error',
  SYSTEM_WARNING = 'system-warning',
  SYSTEM_INFO = 'system-info'
}

/**
 * 审核优先级枚举
 */
export enum ReviewPriority {
  LOW = 1,
  MEDIUM = 2,
  HIGH = 3,
  URGENT = 4,
  CRITICAL = 5
}

/**
 * 内容严重程度枚举
 */
export enum ContentSeverity {
  LOW = 'low',
  MEDIUM = 'medium',
  HIGH = 'high'
}

/**
 * 内容质量枚举
 */
export enum ContentQuality {
  LOW = 'low',
  MEDIUM = 'medium',
  HIGH = 'high'
}

/**
 * 内容价值枚举
 */
export enum ContentValue {
  NONE = 'none',
  LOW = 'low',
  MEDIUM = 'medium',
  HIGH = 'high'
}

/**
 * 审核操作枚举
 */
export enum ReviewAction {
  APPROVE = 'approve',
  REJECT = 'reject',
  REVIEW = 'review'
}

/**
 * 系统配置键枚举
 */
export enum SystemConfigKey {
  AUTO_MODERATION_CONFIG = 'auto_moderation_config',
  CONTENT_MODERATION_PROMPT = 'content_moderation_prompt',
  STORY_MODERATION_PROMPT = 'story_moderation_prompt',
  QUESTIONNAIRE_MODERATION_PROMPT = 'questionnaire_moderation_prompt',
  COMMENT_MODERATION_PROMPT = 'comment_moderation_prompt',
  PROFILE_MODERATION_PROMPT = 'profile_moderation_prompt',
  FEEDBACK_MODERATION_PROMPT = 'feedback_moderation_prompt'
}

/**
 * 默认分页大小
 */
export const DEFAULT_PAGE_SIZE = 10;

/**
 * 最大分页大小
 */
export const MAX_PAGE_SIZE = 100;
