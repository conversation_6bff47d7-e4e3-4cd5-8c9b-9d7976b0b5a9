# 🚀 开发前规划指南

> **目标**: 在项目开始前制定正确的CORS策略，避免后期问题

## 📋 **项目架构评估**

### **1. 技术栈确认**
```yaml
后端框架: <PERSON><PERSON> (Cloudflare Workers)
前端框架: React/Vue/Angular (Cloudflare Pages)
部署平台: Cloudflare
域名策略: 子域名 or 独立域名
```

### **2. 域名规划**
```yaml
开发环境:
  - http://localhost:3000 (前端)
  - http://localhost:8787 (后端)

测试环境:
  - https://[hash].project.pages.dev (前端)
  - https://project.username.workers.dev (后端)

生产环境:
  - https://app.domain.com (前端)
  - https://api.domain.com (后端)
```

---

## 🎯 **CORS策略设计**

### **策略1: 明确域名列表 (推荐)**
```typescript
// 适用于: 域名数量有限且固定的项目
const allowedOrigins = [
  'http://localhost:3000',
  'http://localhost:5173',
  'https://app.domain.com',
  'https://project.username.workers.dev'
];

app.use('*', cors({
  origin: (origin) => {
    if (!origin) return origin;  // 允许无origin请求
    return allowedOrigins.includes(origin) ? origin : false;
  },
  allowMethods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
  allowHeaders: ['Content-Type', 'Authorization', 'X-Requested-With'],
  credentials: true,
  maxAge: 86400
}));
```

### **策略2: 正则表达式匹配 (灵活)**
```typescript
// 适用于: 有多个临时部署域名的项目
app.use('*', cors({
  origin: (origin) => {
    if (!origin) return origin;
    
    // 开发环境
    if (origin.startsWith('http://localhost:')) {
      return origin;
    }
    
    // 生产域名
    if (origin === 'https://app.domain.com') {
      return origin;
    }
    
    // Cloudflare Pages临时域名
    if (origin.match(/^https:\/\/[a-f0-9]{8}\.project\.pages\.dev$/)) {
      return origin;
    }
    
    return false;
  },
  // ... 其他配置
}));
```

### **策略3: 环境变量配置 (可维护)**
```typescript
// 适用于: 多环境部署的项目
const getAllowedOrigins = () => {
  const env = process.env.NODE_ENV || 'development';
  
  switch (env) {
    case 'development':
      return ['http://localhost:3000', 'http://localhost:5173'];
    case 'staging':
      return ['https://staging.domain.com'];
    case 'production':
      return ['https://app.domain.com'];
    default:
      return [];
  }
};
```

---

## 🛠️ **开发环境配置**

### **1. 本地开发配置**
```typescript
// backend/src/cors-config.ts
export const corsConfig = {
  origin: (origin: string) => {
    // 开发环境允许所有localhost
    if (process.env.NODE_ENV === 'development') {
      if (!origin || origin.startsWith('http://localhost:')) {
        return origin;
      }
    }
    
    // 生产环境严格检查
    const allowedOrigins = process.env.ALLOWED_ORIGINS?.split(',') || [];
    return allowedOrigins.includes(origin) ? origin : false;
  },
  allowMethods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
  allowHeaders: ['Content-Type', 'Authorization', 'X-Requested-With'],
  credentials: true,
  maxAge: 86400
};
```

### **2. 环境变量设置**
```bash
# .env.development
NODE_ENV=development
ALLOWED_ORIGINS=http://localhost:3000,http://localhost:5173

# .env.production
NODE_ENV=production
ALLOWED_ORIGINS=https://app.domain.com,https://admin.domain.com
```

### **3. Cloudflare Workers配置**
```toml
# wrangler.toml
[env.development.vars]
ALLOWED_ORIGINS = "http://localhost:3000,http://localhost:5173"

[env.production.vars]
ALLOWED_ORIGINS = "https://app.domain.com"
```

---

## 🔧 **工具集成规划**

### **1. 开发工具**
```json
// package.json
{
  "scripts": {
    "dev": "npm run dev:backend & npm run dev:frontend",
    "dev:backend": "cd backend && wrangler dev",
    "dev:frontend": "cd frontend && npm run dev",
    "test:cors": "./cors-solution-guide/scripts/cors-diagnose.sh",
    "check:deploy": "./cors-solution-guide/scripts/pre-deploy-check.sh"
  }
}
```

### **2. CI/CD集成**
```yaml
# .github/workflows/deploy.yml
name: Deploy
on:
  push:
    branches: [main]

jobs:
  deploy:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      
      - name: Pre-deploy Check
        run: ./cors-solution-guide/scripts/pre-deploy-check.sh
        
      - name: Deploy Backend
        run: cd backend && npx wrangler deploy
        
      - name: Deploy Frontend
        run: cd frontend && npm run build && npx wrangler pages deploy dist
        
      - name: Post-deploy CORS Test
        run: |
          sleep 30  # 等待部署生效
          ./cors-solution-guide/scripts/cors-diagnose.sh \
            ${{ secrets.API_URL }} \
            ${{ secrets.FRONTEND_URL }}
```

---

## 📊 **测试策略规划**

### **1. 单元测试**
```typescript
// tests/cors.test.ts
import { corsConfig } from '../src/cors-config';

describe('CORS Configuration', () => {
  test('should allow localhost in development', () => {
    process.env.NODE_ENV = 'development';
    const result = corsConfig.origin('http://localhost:3000');
    expect(result).toBe('http://localhost:3000');
  });
  
  test('should reject unauthorized origins', () => {
    const result = corsConfig.origin('https://malicious.com');
    expect(result).toBe(false);
  });
});
```

### **2. 集成测试**
```bash
# tests/integration/cors-test.sh
#!/bin/bash
API_BASE="http://localhost:8787"
FRONTEND_ORIGIN="http://localhost:3000"

echo "Testing CORS configuration..."

# 测试OPTIONS预检
curl -X OPTIONS \
  -H "Origin: $FRONTEND_ORIGIN" \
  -H "Access-Control-Request-Method: GET" \
  "$API_BASE/api/test" \
  -v
```

### **3. E2E测试**
```javascript
// tests/e2e/cors.spec.js
describe('CORS E2E Tests', () => {
  test('should allow API calls from frontend', async () => {
    await page.goto('http://localhost:3000');
    
    const response = await page.evaluate(async () => {
      const res = await fetch('http://localhost:8787/api/test');
      return res.ok;
    });
    
    expect(response).toBe(true);
  });
});
```

---

## 📋 **部署检查清单**

### **部署前检查**
- [ ] CORS配置使用正确的返回值类型 (string, not boolean)
- [ ] 所有目标域名已添加到允许列表
- [ ] 环境变量正确配置
- [ ] 本地测试通过
- [ ] 单元测试覆盖CORS逻辑

### **部署后验证**
- [ ] 运行 `cors-diagnose.sh` 脚本
- [ ] 浏览器环境测试通过
- [ ] 所有API端点可访问
- [ ] 错误日志中无CORS相关错误

---

## 🎯 **团队协作规范**

### **1. 代码审查要点**
```markdown
## CORS配置审查清单
- [ ] origin函数返回string而不是boolean
- [ ] 处理了无origin的请求情况
- [ ] 新域名已添加到允许列表
- [ ] 没有使用不安全的通配符配置
```

### **2. 文档维护**
```markdown
## 域名管理文档
### 当前活跃域名
- 生产: https://app.domain.com
- 测试: https://staging.domain.com
- 开发: http://localhost:3000

### 临时域名 (定期清理)
- https://abc123.project.pages.dev (2025-05-28创建)
- https://def456.project.pages.dev (2025-05-29创建)
```

### **3. 问题上报流程**
```markdown
## CORS问题上报模板
### 问题描述
- 错误信息: [具体错误]
- 发生时间: [时间]
- 影响范围: [用户/功能]

### 环境信息
- 前端域名: [域名]
- API域名: [域名]
- 浏览器: [类型和版本]

### 诊断结果
- 运行 cors-diagnose.sh 的输出
- 浏览器开发者工具截图
```

---

## 🔄 **维护计划**

### **定期维护任务**
- **每周**: 清理过期的临时域名
- **每月**: 审查CORS配置的有效性
- **每季度**: 更新最佳实践文档

### **监控指标**
- CORS相关错误率
- API调用成功率
- 新域名添加频率

### **改进计划**
- 自动化域名管理
- 实时CORS配置验证
- 可视化CORS状态监控
