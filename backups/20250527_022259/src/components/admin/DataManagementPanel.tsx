import { useState, useEffect } from 'react';
import { useToast } from '@/components/ui/use-toast';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow
} from '@/components/ui/table.tsx';
import { Badge } from '@/components/ui/badge';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue
} from '@/components/ui/select';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Checkbox } from '@/components/ui/checkbox';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu.tsx';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger
} from '@/components/ui/dialog';
import { ScrollArea } from '@/components/ui/scroll-area.tsx';
import {
  Edit,
  Trash2,
  MoreHorizontal,
  Filter,
  Download,
  Upload,
  CheckSquare,
  Search,
  RefreshCw,
  ChevronLeft,
  ChevronRight,
  AlertCircle
} from 'lucide-react';

// 导入通用组件
import LoadingSpinner from '@/components/ui/loading-spinner';
import ErrorDisplay from '@/components/ui/error-display';

// 导入数据服务
import {
  getResponses,
  getResponse,
  updateResponse,
  deleteResponse,
  bulkDeleteResponses,
  bulkAddTags,
  bulkEditResponses,
  bulkVerifyResponses
} from '@/services/dataService';

// 导入类型
import { QuestionnaireResponse } from '@/types/api';

// 导入自定义组件
import EditResponseDialog from './EditResponseDialog';
import DeleteConfirmDialog from './DeleteConfirmDialog';
import FilterDialog from './FilterDialog';
import AdvancedFilterDialog, { AdvancedFilterOptions, SavedFilter } from './AdvancedFilterDialog';
import BulkActionsMenu from './BulkActionsMenu';
import BulkTagDialog from './BulkTagDialog';
import BulkEditDialog from './BulkEditDialog';
import BulkVerifyDialog from './BulkVerifyDialog';

// 导入筛选服务
import {
  getSavedFilters,
  saveFilter,
  deleteFilter,
  setDefaultFilter,
  getDefaultFilter,
  getAvailableTags
} from '@/services/filterService';

// 定义问卷回复数据类型
interface QuestionnaireResponse {
  id: number;
  sequenceNumber: string;
  isAnonymous: boolean;
  educationLevel?: string;
  major?: string;
  graduationYear?: number;
  region?: string;
  employmentStatus?: string;
  industry?: string;
  position?: string;
  salary?: string;
  jobSatisfaction?: string;
  unemploymentDuration?: string;
  careerChangeIntention?: boolean;
  challenges?: string;
  suggestions?: string;
  createdAt: string;
  updatedAt?: string;
  ipAddress?: string;
  [key: string]: any;
}

// 定义筛选条件类型
interface FilterOptions {
  search: string;
  educationLevel: string;
  employmentStatus: string;
  region: string;
  dateRange: {
    startDate: Date | null;
    endDate: Date | null;
  };
  isAnonymous: boolean | null;
}

// 定义分页信息类型
interface PaginationInfo {
  currentPage: number;
  totalPages: number;
  pageSize: number;
  totalItems: number;
}

export default function DataManagementPanel() {
  const { toast } = useToast();

  // 状态管理
  const [responses, setResponses] = useState<QuestionnaireResponse[]>([]);
  const [selectedResponse, setSelectedResponse] = useState<QuestionnaireResponse | null>(null);
  const [selectedResponses, setSelectedResponses] = useState<number[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false);
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);
  const [isFilterDialogOpen, setIsFilterDialogOpen] = useState(false);
  const [isBulkTagDialogOpen, setIsBulkTagDialogOpen] = useState(false);
  const [isBulkEditDialogOpen, setIsBulkEditDialogOpen] = useState(false);
  const [isBulkVerifyDialogOpen, setIsBulkVerifyDialogOpen] = useState(false);
  const [activeTab, setActiveTab] = useState('all');

  // 筛选条件状态
  const [filters, setFilters] = useState<FilterOptions>({
    search: '',
    educationLevel: 'all',
    employmentStatus: 'all',
    region: 'all',
    dateRange: {
      startDate: null,
      endDate: null,
    },
    isAnonymous: null,
  });

  // 高级筛选条件状态
  const [advancedFilters, setAdvancedFilters] = useState<AdvancedFilterOptions>({
    search: '',
    searchFields: [],
    educationLevel: [],
    employmentStatus: [],
    region: [],
    industry: [],
    isAnonymous: null,
    hasTag: [],
    graduationYearRange: {
      min: null,
      max: null,
    },
    salaryRange: {
      min: null,
      max: null,
    },
    jobSatisfactionRange: {
      min: null,
      max: null,
    },
    dateRange: {
      startDate: null,
      endDate: null,
    },
    logicOperator: 'AND',
    sortBy: 'createdAt',
    sortOrder: 'desc',
  });

  // 保存的筛选条件
  const [savedFilters, setSavedFilters] = useState<SavedFilter[]>([]);

  // 可用标签
  const [availableTags, setAvailableTags] = useState<string[]>([]);

  // 是否使用高级筛选
  const [useAdvancedFilter, setUseAdvancedFilter] = useState(false);

  // 是否显示高级筛选对话框
  const [isAdvancedFilterDialogOpen, setIsAdvancedFilterDialogOpen] = useState(false);

  // 分页状态
  const [pagination, setPagination] = useState<PaginationInfo>({
    currentPage: 1,
    totalPages: 1,
    pageSize: 10,
    totalItems: 0,
  });

  // 编辑表单状态
  const [editForm, setEditForm] = useState<Partial<QuestionnaireResponse>>({});

  // 获取问卷回复数据
  const fetchResponses = async () => {
    try {
      setIsLoading(true);
      setError(null);

      // 准备筛选条件
      const apiFilters: any = {};

      if (useAdvancedFilter) {
        // 使用高级筛选条件
        if (advancedFilters.search) {
          apiFilters.search = advancedFilters.search;

          if (advancedFilters.searchFields.length > 0) {
            apiFilters.searchFields = advancedFilters.searchFields;
          }
        }

        if (advancedFilters.educationLevel.length > 0) {
          apiFilters.educationLevel = advancedFilters.educationLevel;
        }

        if (advancedFilters.employmentStatus.length > 0) {
          apiFilters.employmentStatus = advancedFilters.employmentStatus;
        }

        if (advancedFilters.region.length > 0) {
          apiFilters.region = advancedFilters.region;
        }

        if (advancedFilters.industry.length > 0) {
          apiFilters.industry = advancedFilters.industry;
        }

        if (advancedFilters.dateRange.startDate) {
          apiFilters.startDate = advancedFilters.dateRange.startDate.toISOString();
        }

        if (advancedFilters.dateRange.endDate) {
          apiFilters.endDate = advancedFilters.dateRange.endDate.toISOString();
        }

        if (advancedFilters.isAnonymous !== null) {
          apiFilters.isAnonymous = advancedFilters.isAnonymous;
        }

        if (advancedFilters.hasTag.length > 0) {
          apiFilters.tags = advancedFilters.hasTag;
        }

        if (advancedFilters.graduationYearRange.min !== null) {
          apiFilters.graduationYearMin = advancedFilters.graduationYearRange.min;
        }

        if (advancedFilters.graduationYearRange.max !== null) {
          apiFilters.graduationYearMax = advancedFilters.graduationYearRange.max;
        }

        if (advancedFilters.salaryRange.min !== null) {
          apiFilters.salaryMin = advancedFilters.salaryRange.min;
        }

        if (advancedFilters.salaryRange.max !== null) {
          apiFilters.salaryMax = advancedFilters.salaryRange.max;
        }

        if (advancedFilters.jobSatisfactionRange.min !== null) {
          apiFilters.jobSatisfactionMin = advancedFilters.jobSatisfactionRange.min;
        }

        if (advancedFilters.jobSatisfactionRange.max !== null) {
          apiFilters.jobSatisfactionMax = advancedFilters.jobSatisfactionRange.max;
        }

        // 设置逻辑运算符
        apiFilters.logicOperator = advancedFilters.logicOperator;

        // 设置排序
        apiFilters.sortBy = advancedFilters.sortBy;
        apiFilters.sortOrder = advancedFilters.sortOrder;
      } else {
        // 使用基本筛选条件
        if (filters.search) {
          apiFilters.search = filters.search;
        }

        if (filters.educationLevel !== 'all') {
          apiFilters.educationLevel = filters.educationLevel;
        }

        if (filters.employmentStatus !== 'all') {
          apiFilters.employmentStatus = filters.employmentStatus;
        }

        if (filters.region !== 'all') {
          apiFilters.region = filters.region;
        }

        if (filters.dateRange.startDate) {
          apiFilters.startDate = filters.dateRange.startDate.toISOString();
        }

        if (filters.dateRange.endDate) {
          apiFilters.endDate = filters.dateRange.endDate.toISOString();
        }

        if (filters.isAnonymous !== null) {
          apiFilters.isAnonymous = filters.isAnonymous;
        }
      }

      // 标签页筛选（无论是否使用高级筛选）
      if (activeTab !== 'all') {
        apiFilters.status = activeTab;
      }

      // 调用API获取数据
      const data = await getResponses(pagination.currentPage, pagination.pageSize, apiFilters);

      if (data.success) {
        setResponses(data.responses);
        setPagination({
          currentPage: data.pagination.currentPage,
          totalPages: data.pagination.totalPages,
          pageSize: data.pagination.pageSize,
          totalItems: data.pagination.totalItems,
        });
      } else {
        setError(data.error || '获取问卷回复失败');
      }
    } catch (error) {
      console.error('获取问卷回复错误:', error);
      setError('服务器错误，请稍后再试');
    } finally {
      setIsLoading(false);
    }
  };

  // 组件加载时获取问卷回复和初始化数据
  useEffect(() => {
    fetchResponses();

    // 加载保存的筛选条件
    const savedFiltersData = getSavedFilters();
    setSavedFilters(savedFiltersData);

    // 加载可用标签
    const tagsData = getAvailableTags();
    setAvailableTags(tagsData);
  }, []);

  // 当分页或标签页变化时重新获取数据
  useEffect(() => {
    fetchResponses();
  }, [pagination.currentPage, pagination.pageSize, activeTab]);

  // 当筛选条件变化时，重置到第一页并重新获取数据
  useEffect(() => {
    setPagination(prev => ({ ...prev, currentPage: 1 }));
    fetchResponses();
  }, [filters, useAdvancedFilter, useAdvancedFilter ? advancedFilters : null]);

  // 处理页面变化
  const handlePageChange = (newPage: number) => {
    setPagination(prev => ({ ...prev, currentPage: newPage }));
  };

  // 处理每页显示数量变化
  const handlePageSizeChange = (newSize: number) => {
    setPagination(prev => ({ ...prev, pageSize: newSize, currentPage: 1 }));
  };

  // 处理筛选条件变化
  const handleFilterChange = (newFilters: Partial<FilterOptions>) => {
    setFilters(prev => ({ ...prev, ...newFilters }));
    setUseAdvancedFilter(false);
  };

  // 处理高级筛选条件变化
  const handleAdvancedFilterChange = (newFilters: AdvancedFilterOptions) => {
    setAdvancedFilters(newFilters);
    setUseAdvancedFilter(true);
  };

  // 处理重置高级筛选
  const handleResetAdvancedFilters = () => {
    setAdvancedFilters({
      search: '',
      searchFields: [],
      educationLevel: [],
      employmentStatus: [],
      region: [],
      industry: [],
      isAnonymous: null,
      hasTag: [],
      graduationYearRange: {
        min: null,
        max: null,
      },
      salaryRange: {
        min: null,
        max: null,
      },
      jobSatisfactionRange: {
        min: null,
        max: null,
      },
      dateRange: {
        startDate: null,
        endDate: null,
      },
      logicOperator: 'AND',
      sortBy: 'createdAt',
      sortOrder: 'desc',
    });
    setUseAdvancedFilter(false);
  };

  // 处理保存筛选条件
  const handleSaveAdvancedFilter = (name: string, description: string, filters: AdvancedFilterOptions) => {
    try {
      const newFilter = saveFilter(name, description, filters);
      setSavedFilters(prev => [...prev, newFilter]);

      // 更新可用标签
      if (filters.hasTag.length > 0) {
        setAvailableTags(prev => {
          const newTags = [...prev];
          filters.hasTag.forEach(tag => {
            if (!newTags.includes(tag)) {
              newTags.push(tag);
            }
          });
          return newTags;
        });
      }

      toast({
        title: '保存成功',
        description: '筛选条件已保存',
      });
    } catch (error) {
      console.error('保存筛选条件错误:', error);
      toast({
        variant: 'destructive',
        title: '保存失败',
        description: '保存筛选条件时发生错误',
      });
    }
  };

  // 处理删除筛选条件
  const handleDeleteSavedFilter = (id: string) => {
    try {
      deleteFilter(id);
      setSavedFilters(prev => prev.filter(filter => filter.id !== id));

      toast({
        title: '删除成功',
        description: '筛选条件已删除',
      });
    } catch (error) {
      console.error('删除筛选条件错误:', error);
      toast({
        variant: 'destructive',
        title: '删除失败',
        description: '删除筛选条件时发生错误',
      });
    }
  };

  // 处理设置默认筛选条件
  const handleSetDefaultFilter = (id: string) => {
    try {
      setDefaultFilter(id);
      setSavedFilters(prev =>
        prev.map(filter => ({
          ...filter,
          isDefault: filter.id === id,
        }))
      );

      toast({
        title: '设置成功',
        description: '默认筛选条件已设置',
      });
    } catch (error) {
      console.error('设置默认筛选条件错误:', error);
      toast({
        variant: 'destructive',
        title: '设置失败',
        description: '设置默认筛选条件时发生错误',
      });
    }
  };

  // 处理选择/取消选择单个响应
  const handleSelectResponse = (id: number) => {
    setSelectedResponses(prev => {
      if (prev.includes(id)) {
        return prev.filter(responseId => responseId !== id);
      } else {
        return [...prev, id];
      }
    });
  };

  // 处理全选/取消全选
  const handleSelectAll = () => {
    if (selectedResponses.length === responses.length) {
      setSelectedResponses([]);
    } else {
      setSelectedResponses(responses.map(response => response.id));
    }
  };

  // 处理查看详情
  const handleViewDetails = (response: QuestionnaireResponse) => {
    setSelectedResponse(response);
  };

  // 处理编辑
  const handleEdit = (response: QuestionnaireResponse) => {
    setSelectedResponse(response);
    setEditForm(response);
    setIsEditDialogOpen(true);
  };

  // 处理保存编辑
  const handleSaveEdit = async (updatedResponse: QuestionnaireResponse) => {
    try {
      // 调用API更新数据
      const result = await updateResponse(updatedResponse.id, updatedResponse);

      if (result.success) {
        // 更新本地数据
        setResponses(prev =>
          prev.map(response =>
            response.id === updatedResponse.id ? updatedResponse : response
          )
        );

        toast({
          title: '保存成功',
          description: '问卷回复数据已更新',
        });
      } else {
        toast({
          variant: 'destructive',
          title: '保存失败',
          description: result.error || '更新数据时发生错误',
        });
      }
    } catch (error) {
      console.error('保存数据错误:', error);
      toast({
        variant: 'destructive',
        title: '保存失败',
        description: '服务器错误，请稍后再试',
      });
    }
  };

  // 处理删除
  const handleDelete = (response: QuestionnaireResponse) => {
    setSelectedResponse(response);
    setIsDeleteDialogOpen(true);
  };

  // 处理确认删除
  const handleConfirmDelete = async () => {
    if (!selectedResponse) return;

    try {
      // 调用API删除数据
      const result = await deleteResponse(selectedResponse.id);

      if (result.success) {
        // 更新本地数据
        setResponses(prev => prev.filter(response => response.id !== selectedResponse.id));
        setSelectedResponses(prev => prev.filter(id => id !== selectedResponse.id));

        toast({
          title: '删除成功',
          description: '问卷回复已成功删除',
        });
      } else {
        toast({
          variant: 'destructive',
          title: '删除失败',
          description: result.error || '删除数据时发生错误',
        });
      }
    } catch (error) {
      console.error('删除数据错误:', error);
      toast({
        variant: 'destructive',
        title: '删除失败',
        description: '服务器错误，请稍后再试',
      });
    }
  };

  // 处理批量删除
  const handleBulkDelete = () => {
    if (selectedResponses.length === 0) return;
    setIsDeleteDialogOpen(true);
  };

  // 处理确认批量删除
  const handleConfirmBulkDelete = async () => {
    if (selectedResponses.length === 0) return;

    try {
      // 调用API批量删除数据
      const result = await bulkDeleteResponses(selectedResponses);

      if (result.success) {
        // 更新本地数据
        setResponses(prev => prev.filter(response => !selectedResponses.includes(response.id)));
        setSelectedResponses([]);

        toast({
          title: '批量删除成功',
          description: `已成功删除 ${selectedResponses.length} 条问卷回复`,
        });
      } else {
        toast({
          variant: 'destructive',
          title: '批量删除失败',
          description: result.error || '删除数据时发生错误',
        });
      }
    } catch (error) {
      console.error('批量删除数据错误:', error);
      toast({
        variant: 'destructive',
        title: '批量删除失败',
        description: '服务器错误，请稍后再试',
      });
    }
  };

  // 处理导出
  const handleExport = async (format: string = 'csv') => {
    try {
      // 构建查询参数
      const queryParams = new URLSearchParams();

      if (filters.search) {
        queryParams.append('search', filters.search);
      }

      if (filters.educationLevel !== 'all') {
        queryParams.append('educationLevel', filters.educationLevel);
      }

      if (filters.employmentStatus !== 'all') {
        queryParams.append('employmentStatus', filters.employmentStatus);
      }

      if (filters.region !== 'all') {
        queryParams.append('region', filters.region);
      }

      if (filters.dateRange.startDate) {
        queryParams.append('startDate', filters.dateRange.startDate.toISOString());
      }

      if (filters.dateRange.endDate) {
        queryParams.append('endDate', filters.dateRange.endDate.toISOString());
      }

      if (filters.isAnonymous !== null) {
        queryParams.append('isAnonymous', filters.isAnonymous.toString());
      }

      if (activeTab !== 'all') {
        queryParams.append('status', activeTab);
      }

      if (selectedResponses.length > 0) {
        queryParams.append('ids', selectedResponses.join(','));
      }

      queryParams.append('format', format); // 设置导出格式

      // 调用API导出数据
      const token = localStorage.getItem('adminToken');
      const response = await fetch(
        `${import.meta.env.VITE_API_BASE_URL}/admin/responses/export?${queryParams.toString()}`,
        {
          headers: {
            'Authorization': `Bearer ${token}`,
          },
        }
      );

      const data = await response.json();

      if (data.success) {
        // 打开下载链接
        window.open(data.downloadUrl, '_blank');

        toast({
          title: '导出成功',
          description: `数据已导出为${format.toUpperCase()}格式，链接有效期24小时`,
        });
      } else {
        toast({
          variant: 'destructive',
          title: '导出失败',
          description: data.error || '导出数据失败',
        });
      }
    } catch (error) {
      console.error('导出数据错误:', error);
      toast({
        variant: 'destructive',
        title: '导出失败',
        description: '服务器错误，请稍后再试',
      });
    }
  };

  // 处理批量标记
  const handleBulkTag = () => {
    if (selectedResponses.length === 0) return;
    setIsBulkTagDialogOpen(true);
  };

  // 处理确认批量标记
  const handleConfirmBulkTag = async (tags: string[]) => {
    if (selectedResponses.length === 0 || tags.length === 0) return;

    try {
      // 调用API批量添加标签
      const result = await bulkAddTags(selectedResponses, tags);

      if (result.success) {
        // 刷新数据
        await fetchResponses();

        toast({
          title: '批量添加标签成功',
          description: `已为 ${selectedResponses.length} 条数据添加标签`,
        });
      } else {
        toast({
          variant: 'destructive',
          title: '批量添加标签失败',
          description: result.error || '添加标签时发生错误',
        });
      }
    } catch (error) {
      console.error('批量添加标签错误:', error);
      toast({
        variant: 'destructive',
        title: '批量添加标签失败',
        description: '服务器错误，请稍后再试',
      });
    }
  };

  // 处理批量编辑
  const handleBulkEdit = () => {
    if (selectedResponses.length === 0) return;
    setIsBulkEditDialogOpen(true);
  };

  // 处理确认批量编辑
  const handleConfirmBulkEdit = async (fields: Record<string, any>) => {
    if (selectedResponses.length === 0 || Object.keys(fields).length === 0) return;

    try {
      // 调用API批量编辑数据
      const result = await bulkEditResponses(selectedResponses, fields);

      if (result.success) {
        // 刷新数据
        await fetchResponses();

        toast({
          title: '批量编辑成功',
          description: `已成功编辑 ${selectedResponses.length} 条数据`,
        });
      } else {
        toast({
          variant: 'destructive',
          title: '批量编辑失败',
          description: result.error || '编辑数据时发生错误',
        });
      }
    } catch (error) {
      console.error('批量编辑错误:', error);
      toast({
        variant: 'destructive',
        title: '批量编辑失败',
        description: '服务器错误，请稍后再试',
      });
    }
  };

  // 处理批量验证
  const handleBulkVerify = () => {
    if (selectedResponses.length === 0) return;
    setIsBulkVerifyDialogOpen(true);
  };

  // 处理确认批量验证
  const handleConfirmBulkVerify = async (action: 'verify' | 'unverify', reason: string) => {
    if (selectedResponses.length === 0 || !reason) return;

    try {
      // 调用API批量验证数据
      const result = await bulkVerifyResponses(selectedResponses, action, reason);

      if (result.success) {
        // 刷新数据
        await fetchResponses();

        toast({
          title: action === 'verify' ? '批量验证成功' : '批量取消验证成功',
          description: `已${action === 'verify' ? '验证' : '取消验证'} ${selectedResponses.length} 条数据`,
        });
      } else {
        toast({
          variant: 'destructive',
          title: '操作失败',
          description: result.error || '处理数据时发生错误',
        });
      }
    } catch (error) {
      console.error('批量验证错误:', error);
      toast({
        variant: 'destructive',
        title: '操作失败',
        description: '服务器错误，请稍后再试',
      });
    }
  };

  // 渲染加载状态
  if (isLoading && responses.length === 0) {
    return (
      <div className="space-y-4">
        <div className="flex flex-col md:flex-row justify-between items-start md:items-center gap-4">
          <div>
            <h3 className="text-lg font-medium">问卷数据管理</h3>
            <p className="text-sm text-gray-500">
              加载数据中...
            </p>
          </div>
        </div>

        <div className="flex justify-center items-center py-12">
          <LoadingSpinner size="lg" text="加载问卷回复数据中..." />
        </div>
      </div>
    );
  }

  // 渲染错误状态
  if (error && responses.length === 0) {
    return (
      <div className="space-y-4">
        <div className="flex flex-col md:flex-row justify-between items-start md:items-center gap-4">
          <div>
            <h3 className="text-lg font-medium">问卷数据管理</h3>
            <p className="text-sm text-gray-500">
              加载失败
            </p>
          </div>
        </div>

        <ErrorDisplay
          title="加载数据失败"
          message={error}
          onRetry={fetchResponses}
        />
      </div>
    );
  }

  return (
    <div className="space-y-4">
      <div className="flex flex-col md:flex-row justify-between items-start md:items-center gap-4">
        <div>
          <h3 className="text-lg font-medium">问卷数据管理</h3>
          <p className="text-sm text-gray-500">
            共 {pagination.totalItems} 条数据，当前显示第 {pagination.currentPage} 页
          </p>
        </div>

        <div className="flex flex-wrap gap-2">
          <div className="flex gap-1">
            <Button
              variant="outline"
              size="sm"
              onClick={() => setIsFilterDialogOpen(true)}
              className="flex items-center gap-1"
            >
              <Filter className="h-4 w-4" />
              筛选
              {!useAdvancedFilter && (filters.search || filters.educationLevel !== 'all' || filters.employmentStatus !== 'all' ||
                filters.region !== 'all' || filters.dateRange.startDate || filters.dateRange.endDate ||
                filters.isAnonymous !== null) && (
                <Badge variant="secondary" className="ml-1 h-5 w-5 p-0 flex items-center justify-center">
                  !
                </Badge>
              )}
            </Button>

            <Button
              variant={useAdvancedFilter ? "default" : "outline"}
              size="sm"
              onClick={() => setIsAdvancedFilterDialogOpen(true)}
              className="flex items-center gap-1"
            >
              <Search className="h-4 w-4" />
              高级筛选
              {useAdvancedFilter && (
                <Badge variant="secondary" className="ml-1 h-5 w-5 p-0 flex items-center justify-center">
                  !
                </Badge>
              )}
            </Button>
          </div>

          <Button
            variant="outline"
            size="sm"
            onClick={() => handleExport()}
            className="flex items-center gap-1"
          >
            <Download className="h-4 w-4" />
            导出
          </Button>

          <Button
            variant="outline"
            size="sm"
            onClick={fetchResponses}
            className="flex items-center gap-1"
            disabled={isLoading}
          >
            {isLoading ? (
              <LoadingSpinner size="sm" />
            ) : (
              <RefreshCw className="h-4 w-4" />
            )}
            刷新
          </Button>

          {/* 批量操作菜单 */}
          <BulkActionsMenu
            selectedCount={selectedResponses.length}
            onExport={handleExport}
            onBulkTag={handleBulkTag}
            onBulkEdit={handleBulkEdit}
            onBulkVerify={handleBulkVerify}
            onBulkDelete={handleBulkDelete}
            disabled={isLoading}
          />
        </div>
      </div>

      {/* 标签页 */}
      <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
        <TabsList>
          <TabsTrigger value="all">全部数据</TabsTrigger>
          <TabsTrigger value="verified">已验证</TabsTrigger>
          <TabsTrigger value="anonymous">匿名</TabsTrigger>
        </TabsList>
      </Tabs>

      {/* 数据表格 */}
      <div className="rounded-md border">
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead className="w-12">
                <Checkbox
                  checked={selectedResponses.length === responses.length && responses.length > 0}
                  onCheckedChange={handleSelectAll}
                  aria-label="全选"
                />
              </TableHead>
              <TableHead>编号</TableHead>
              <TableHead>提交类型</TableHead>
              <TableHead>学历</TableHead>
              <TableHead>专业</TableHead>
              <TableHead>毕业年份</TableHead>
              <TableHead>地区</TableHead>
              <TableHead>就业状态</TableHead>
              <TableHead>提交时间</TableHead>
              <TableHead className="text-right">操作</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {isLoading ? (
              <TableRow>
                <TableCell colSpan={10} className="h-24 text-center">
                  <div className="flex justify-center items-center h-full">
                    <LoadingSpinner size="sm" text="加载中..." />
                  </div>
                </TableCell>
              </TableRow>
            ) : responses.length === 0 ? (
              <TableRow>
                <TableCell colSpan={10} className="h-24 text-center">
                  没有找到数据
                </TableCell>
              </TableRow>
            ) : (
              responses.map((response) => (
                <TableRow key={response.id}>
                  <TableCell>
                    <Checkbox
                      checked={selectedResponses.includes(response.id)}
                      onCheckedChange={() => handleSelectResponse(response.id)}
                      aria-label={`选择记录 ${response.id}`}
                    />
                  </TableCell>
                  <TableCell>
                    <Badge variant="outline">
                      {response.sequenceNumber}
                    </Badge>
                  </TableCell>
                  <TableCell>
                    <Badge variant={response.isAnonymous ? "secondary" : "outline"}>
                      {response.isAnonymous ? '匿名' : '已验证'}
                    </Badge>
                  </TableCell>
                  <TableCell>{response.educationLevel || '-'}</TableCell>
                  <TableCell>{response.major || '-'}</TableCell>
                  <TableCell>{response.graduationYear || '-'}</TableCell>
                  <TableCell>{response.region || '-'}</TableCell>
                  <TableCell>{response.employmentStatus || '-'}</TableCell>
                  <TableCell>{new Date(response.createdAt).toLocaleString('zh-CN')}</TableCell>
                  <TableCell className="text-right">
                    <DropdownMenu>
                      <DropdownMenuTrigger asChild>
                        <Button variant="ghost" size="sm">
                          <MoreHorizontal className="h-4 w-4" />
                          <span className="sr-only">操作</span>
                        </Button>
                      </DropdownMenuTrigger>
                      <DropdownMenuContent align="end">
                        <DropdownMenuLabel>操作</DropdownMenuLabel>
                        <DropdownMenuItem onClick={() => handleViewDetails(response)}>
                          查看详情
                        </DropdownMenuItem>
                        <DropdownMenuItem onClick={() => handleEdit(response)}>
                          编辑
                        </DropdownMenuItem>
                        <DropdownMenuSeparator />
                        <DropdownMenuItem
                          onClick={() => handleDelete(response)}
                          className="text-red-600"
                        >
                          删除
                        </DropdownMenuItem>
                      </DropdownMenuContent>
                    </DropdownMenu>
                  </TableCell>
                </TableRow>
              ))
            )}
          </TableBody>
        </Table>
      </div>

      {/* 分页控制 */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-2">
          <p className="text-sm text-gray-500">
            每页显示
          </p>
          <Select
            value={pagination.pageSize.toString()}
            onValueChange={(value) => handlePageSizeChange(parseInt(value))}
          >
            <SelectTrigger className="w-16">
              <SelectValue placeholder={pagination.pageSize.toString()} />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="10">10</SelectItem>
              <SelectItem value="20">20</SelectItem>
              <SelectItem value="50">50</SelectItem>
              <SelectItem value="100">100</SelectItem>
            </SelectContent>
          </Select>
          <p className="text-sm text-gray-500">
            条记录
          </p>
        </div>

        <div className="flex items-center gap-2">
          <Button
            variant="outline"
            size="sm"
            onClick={() => handlePageChange(pagination.currentPage - 1)}
            disabled={pagination.currentPage === 1}
          >
            <ChevronLeft className="h-4 w-4" />
            上一页
          </Button>

          <p className="text-sm">
            第 {pagination.currentPage} 页，共 {pagination.totalPages} 页
          </p>

          <Button
            variant="outline"
            size="sm"
            onClick={() => handlePageChange(pagination.currentPage + 1)}
            disabled={pagination.currentPage === pagination.totalPages}
          >
            下一页
            <ChevronRight className="h-4 w-4" />
          </Button>
        </div>
      </div>

      {/* 编辑对话框 */}
      <EditResponseDialog
        response={selectedResponse}
        open={isEditDialogOpen}
        onOpenChange={setIsEditDialogOpen}
        onSave={handleSaveEdit}
      />

      {/* 删除确认对话框 */}
      <DeleteConfirmDialog
        open={isDeleteDialogOpen}
        onOpenChange={setIsDeleteDialogOpen}
        onConfirm={selectedResponses.length > 1 ? handleConfirmBulkDelete : handleConfirmDelete}
        title={selectedResponses.length > 1 ? '批量删除' : '删除记录'}
        description="此操作不可撤销，删除后数据将无法恢复。"
        itemName="问卷回复"
        isBulkDelete={selectedResponses.length > 1}
        count={selectedResponses.length}
      />

      {/* 基本筛选对话框 */}
      <FilterDialog
        open={isFilterDialogOpen}
        onOpenChange={setIsFilterDialogOpen}
        filters={filters}
        onApplyFilters={handleFilterChange}
        onResetFilters={() => {
          setFilters({
            search: '',
            educationLevel: 'all',
            employmentStatus: 'all',
            region: 'all',
            dateRange: {
              startDate: null,
              endDate: null,
            },
            isAnonymous: null,
          });
        }}
      />

      {/* 高级筛选对话框 */}
      <AdvancedFilterDialog
        open={isAdvancedFilterDialogOpen}
        onOpenChange={setIsAdvancedFilterDialogOpen}
        filters={advancedFilters}
        onApplyFilters={handleAdvancedFilterChange}
        onResetFilters={handleResetAdvancedFilters}
        savedFilters={savedFilters}
        onSaveFilter={handleSaveAdvancedFilter}
        onDeleteSavedFilter={handleDeleteSavedFilter}
        onSetDefaultFilter={handleSetDefaultFilter}
        availableTags={availableTags}
      />

      {/* 查看详情对话框 */}
      <Dialog open={!!selectedResponse && !isEditDialogOpen && !isDeleteDialogOpen} onOpenChange={(open) => {
        if (!open) setSelectedResponse(null);
      }}>
        <DialogContent className="max-w-3xl max-h-[90vh]">
          <DialogHeader>
            <DialogTitle>问卷回复详情</DialogTitle>
            <DialogDescription>
              查看问卷回复的详细信息
            </DialogDescription>
          </DialogHeader>

          {selectedResponse && (
            <ScrollArea className="max-h-[calc(90vh-180px)] pr-4">
              <div className="space-y-6">
                <div className="flex justify-between items-center">
                  <div>
                    <Badge variant="outline" className="mb-2">
                      编号: {selectedResponse.sequenceNumber}
                    </Badge>
                    <p className="text-sm text-gray-500">
                      ID: {selectedResponse.id}
                    </p>
                  </div>
                  <Badge variant={selectedResponse.isAnonymous ? "secondary" : "outline"}>
                    {selectedResponse.isAnonymous ? '匿名' : '已验证'}
                  </Badge>
                </div>

                {/* 显示标签（如果有） */}
                {selectedResponse.tags && selectedResponse.tags.length > 0 && (
                  <div>
                    <h3 className="text-sm font-medium mb-2">标签</h3>
                    <div className="flex flex-wrap gap-2">
                      {selectedResponse.tags.map((tag: string, index: number) => (
                        <Badge key={index} variant="secondary">
                          <Tag className="mr-1 h-3 w-3" />
                          {tag}
                        </Badge>
                      ))}
                    </div>
                  </div>
                )}

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <h3 className="text-sm font-medium">基本信息</h3>
                    <div className="mt-2 space-y-2">
                      <div className="grid grid-cols-3">
                        <span className="text-sm text-gray-500">学历层次:</span>
                        <span className="col-span-2">{selectedResponse.educationLevel || '-'}</span>
                      </div>
                      <div className="grid grid-cols-3">
                        <span className="text-sm text-gray-500">专业:</span>
                        <span className="col-span-2">{selectedResponse.major || '-'}</span>
                      </div>
                      <div className="grid grid-cols-3">
                        <span className="text-sm text-gray-500">毕业年份:</span>
                        <span className="col-span-2">{selectedResponse.graduationYear || '-'}</span>
                      </div>
                      <div className="grid grid-cols-3">
                        <span className="text-sm text-gray-500">地区:</span>
                        <span className="col-span-2">{selectedResponse.region || '-'}</span>
                      </div>
                    </div>
                  </div>

                  <div>
                    <h3 className="text-sm font-medium">就业信息</h3>
                    <div className="mt-2 space-y-2">
                      <div className="grid grid-cols-3">
                        <span className="text-sm text-gray-500">就业状态:</span>
                        <span className="col-span-2">{selectedResponse.employmentStatus || '-'}</span>
                      </div>
                      <div className="grid grid-cols-3">
                        <span className="text-sm text-gray-500">行业:</span>
                        <span className="col-span-2">{selectedResponse.industry || '-'}</span>
                      </div>
                      <div className="grid grid-cols-3">
                        <span className="text-sm text-gray-500">职位:</span>
                        <span className="col-span-2">{selectedResponse.position || '-'}</span>
                      </div>
                      <div className="grid grid-cols-3">
                        <span className="text-sm text-gray-500">薪资:</span>
                        <span className="col-span-2">{selectedResponse.salary || '-'}</span>
                      </div>
                    </div>
                  </div>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <h3 className="text-sm font-medium">其他信息</h3>
                    <div className="mt-2 space-y-2">
                      <div className="grid grid-cols-3">
                        <span className="text-sm text-gray-500">工作满意度:</span>
                        <span className="col-span-2">{selectedResponse.jobSatisfaction || '-'}</span>
                      </div>
                      <div className="grid grid-cols-3">
                        <span className="text-sm text-gray-500">失业时长:</span>
                        <span className="col-span-2">{selectedResponse.unemploymentDuration || '-'}</span>
                      </div>
                      <div className="grid grid-cols-3">
                        <span className="text-sm text-gray-500">转行意向:</span>
                        <span className="col-span-2">{selectedResponse.careerChangeIntention ? '是' : '否'}</span>
                      </div>
                    </div>
                  </div>

                  <div>
                    <h3 className="text-sm font-medium">提交信息</h3>
                    <div className="mt-2 space-y-2">
                      <div className="grid grid-cols-3">
                        <span className="text-sm text-gray-500">提交时间:</span>
                        <span className="col-span-2">{new Date(selectedResponse.createdAt).toLocaleString('zh-CN')}</span>
                      </div>
                      {selectedResponse.updatedAt && (
                        <div className="grid grid-cols-3">
                          <span className="text-sm text-gray-500">更新时间:</span>
                          <span className="col-span-2">{new Date(selectedResponse.updatedAt).toLocaleString('zh-CN')}</span>
                        </div>
                      )}
                      {selectedResponse.ipAddress && (
                        <div className="grid grid-cols-3">
                          <span className="text-sm text-gray-500">IP地址:</span>
                          <span className="col-span-2">{selectedResponse.ipAddress}</span>
                        </div>
                      )}
                    </div>
                  </div>
                </div>

                {(selectedResponse.challenges || selectedResponse.suggestions) && (
                  <div>
                    <h3 className="text-sm font-medium">详细内容</h3>
                    <div className="mt-2 space-y-4">
                      {selectedResponse.challenges && (
                        <div>
                          <h4 className="text-xs font-medium text-gray-500">就业挑战:</h4>
                          <p className="mt-1 text-sm whitespace-pre-wrap">{selectedResponse.challenges}</p>
                        </div>
                      )}
                      {selectedResponse.suggestions && (
                        <div>
                          <h4 className="text-xs font-medium text-gray-500">建议:</h4>
                          <p className="mt-1 text-sm whitespace-pre-wrap">{selectedResponse.suggestions}</p>
                        </div>
                      )}
                    </div>
                  </div>
                )}
              </div>
            </ScrollArea>
          )}

          <DialogFooter>
            <div className="flex justify-between w-full">
              <Button
                variant="destructive"
                onClick={() => {
                  if (selectedResponse) {
                    handleDelete(selectedResponse);
                  }
                }}
              >
                <Trash2 className="mr-2 h-4 w-4" />
                删除
              </Button>

              <div className="flex gap-2">
                <Button
                  variant="outline"
                  onClick={() => setSelectedResponse(null)}
                >
                  关闭
                </Button>
                <Button
                  onClick={() => {
                    if (selectedResponse) {
                      handleEdit(selectedResponse);
                    }
                  }}
                >
                  <Edit className="mr-2 h-4 w-4" />
                  编辑
                </Button>
              </div>
            </div>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* 批量标记对话框 */}
      <BulkTagDialog
        open={isBulkTagDialogOpen}
        onOpenChange={setIsBulkTagDialogOpen}
        onConfirm={handleConfirmBulkTag}
        selectedCount={selectedResponses.length}
        existingTags={[]}
      />

      {/* 批量编辑对话框 */}
      <BulkEditDialog
        open={isBulkEditDialogOpen}
        onOpenChange={setIsBulkEditDialogOpen}
        onConfirm={handleConfirmBulkEdit}
        selectedCount={selectedResponses.length}
      />

      {/* 批量验证对话框 */}
      <BulkVerifyDialog
        open={isBulkVerifyDialogOpen}
        onOpenChange={setIsBulkVerifyDialogOpen}
        onConfirm={handleConfirmBulkVerify}
        selectedCount={selectedResponses.length}
      />
    </div>
  );
}
