// 优化后的数据库结构设计 v2.0
// 基于用户需求重新设计UUID体系和权限管理
// 优化命名规范、问卷结构、故事内容管理

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "sqlite"
  url      = env("DATABASE_URL")
}

// ========== 用户身份管理 ==========

// 用户表 - 统一管理所有用户类型
model User {
  id                   String                @id // user_xxxxxxxx-xxxx-xxxx-xxxx-xxxxxxxxxxxx
  email                String?               @unique
  emailVerified        Boolean               @default(false)
  verificationCode     String?
  verificationExpiresAt DateTime?

  // 用户类型和权限
  userType             String                @default("anonymous") // anonymous, registered, reviewer, admin, superadmin
  role                 String                @default("user") // user, reviewer, admin, superadmin
  username             String?               // 用户名，管理员和审核员需要
  displayName          String?               // 显示名称
  passwordHash         String?               // 密码哈希

  // 匿名用户标识
  anonymousId          String?               @unique // anon_xxxxxxxx 匿名注册用户的登录标识
  isAnonymousRegistered Boolean              @default(false) // 是否为匿名注册用户

  // 审计信息
  ipAddress            String?
  userAgent            String?
  lastLoginAt          DateTime?
  isActive             Boolean               @default(true)
  createdAt            DateTime              @default(now())
  updatedAt            DateTime              @updatedAt
  createdBy            String?               // 创建者ID（用于管理员创建的账户）

  // 测试数据标识
  isTestData           Boolean               @default(false)
  testDataVersion      String?
  testDataSet          String?

  // 关联关系
  questionnaireResponses QuestionnaireResponse[]
  questionnaireVoices  QuestionnaireVoice[]
  storyContents        StoryContent[]        // 故事内容
  questionAnswers      QuestionAnswer[]      // 问卷答案
  reviewLogs           ReviewLog[]
  operationLogs        OperationLog[]
  createdUsers         User[]                @relation("UserCreator")
  creator              User?                 @relation("UserCreator", fields: [createdBy], references: [id])

  @@index([userType])
  @@index([role])
  @@index([anonymousId])
  @@index([isActive])
}

// ========== 问卷系统 ==========

// 问卷模板表
model QuestionnaireTemplate {
  id                   String                @id // quest_xxxxxxxx-xxxx-xxxx-xxxx-xxxxxxxxxxxx
  title                String                // 问卷标题
  description          String?               // 问卷描述
  version              String                @default("1.0") // 问卷版本
  isActive             Boolean               @default(true)

  createdAt            DateTime              @default(now())
  updatedAt            DateTime              @updatedAt

  // 关联关系
  questions            Question[]            // 问卷题目
  responses            QuestionnaireResponse[] // 问卷回复

  @@index([isActive])
  @@index([version])
}

// 题目表
model Question {
  id                   String                @id // qitem_xxxxxxxx-xxxx-xxxx-xxxx-xxxxxxxxxxxx
  questionnaireId      String                // 关联问卷
  questionNumber       Int                   // 题目序号
  questionText         String                // 题目文本
  questionType         String                // single, multiple, text, number
  isRequired           Boolean               @default(true)

  // 题目配置
  options              String?               // JSON格式的选项配置
  validation           String?               // JSON格式的验证规则

  createdAt            DateTime              @default(now())
  updatedAt            DateTime              @updatedAt

  // 关联关系
  questionnaire        QuestionnaireTemplate @relation(fields: [questionnaireId], references: [id])
  answers              QuestionAnswer[]      // 用户答案
  stats                QuestionnaireStats[]  // 统计数据

  @@index([questionnaireId])
  @@index([questionNumber])
  @@index([questionType])
}

// 用户答案表 - 高频查询表
model QuestionAnswer {
  id                   String                @id // ans_xxxxxxxx-xxxx-xxxx-xxxx-xxxxxxxxxxxx
  userId               String                // 用户ID
  questionId           String                // 题目ID
  responseId           String                // 问卷回复ID

  // 答案内容
  answerType           String                // single, multiple, text, number
  answerValue          String                // 答案值（JSON格式存储）
  answerText           String?               // 文本答案
  answerNumber         Float?                // 数字答案

  createdAt            DateTime              @default(now())

  // 关联关系
  user                 User                  @relation(fields: [userId], references: [id])
  question             Question              @relation(fields: [questionId], references: [id])
  response             QuestionnaireResponse @relation(fields: [responseId], references: [id])

  // 防止重复答题
  @@unique([userId, questionId, responseId])
  @@index([questionId]) // 高频查询：按题目统计
  @@index([responseId])
  @@index([answerType])
}

// 问卷回复表 - 问卷提交记录
model QuestionnaireResponse {
  id                   String                @id // resp_xxxxxxxx-xxxx-xxxx-xxxx-xxxxxxxxxxxx
  userId               String?               // 关联用户
  questionnaireId      String                // 关联问卷模板
  sequenceNumber       String                @unique // 前端序列号
  submissionSessionId  String                // 提交会话ID，用于关联心声

  // 基础信息
  ipAddress            String?
  userAgent            String?
  isAnonymous          Boolean               @default(true)

  // 状态管理
  status               String                @default("submitted") // submitted, verified, flagged
  completionRate       Float                 @default(0) // 完成率

  createdAt            DateTime              @default(now())
  updatedAt            DateTime              @updatedAt

  // 测试数据标识
  isTestData           Boolean               @default(false)
  testDataVersion      String?
  testDataSet          String?

  // 关联关系
  user                 User?                 @relation(fields: [userId], references: [id])
  questionnaire        QuestionnaireTemplate @relation(fields: [questionnaireId], references: [id])
  answers              QuestionAnswer[]      // 用户答案
  voices               QuestionnaireVoice[]  // 关联的心声内容

  @@index([submissionSessionId])
  @@index([status])
  @@index([questionnaireId])
  @@index([userId])
  @@index([createdAt])
}

// 问卷心声表 - 独立的心声内容管理
model QuestionnaireVoice {
  id                   String                @id // voice_xxxxxxxx-xxxx-xxxx-xxxx-xxxxxxxxxxxx
  responseId           String                // 关联问卷回复
  userId               String?               // 关联用户
  submissionSessionId  String                // 提交会话ID

  // 心声内容
  voiceType            String                // advice, observation
  title                String                // 心声标题
  content              String                // 心声内容
  contentId            String                // text_xxxxxxxx 文本内容ID

  // 审核状态
  status               String                @default("pending") // pending, approved, rejected
  moderationNotes      String?               // 审核备注
  reviewedBy           String?               // 审核员ID
  reviewedAt           DateTime?             // 审核时间

  // 展示控制
  isPublic             Boolean               @default(true) // 是否公开展示
  isAnonymous          Boolean               @default(true)

  // 审计信息
  ipAddress            String?
  userAgent            String?
  createdAt            DateTime              @default(now())
  updatedAt            DateTime              @updatedAt

  // 测试数据标识
  isTestData           Boolean               @default(false)
  testDataVersion      String?
  testDataSet          String?

  // 关联关系
  response             QuestionnaireResponse @relation(fields: [responseId], references: [id])
  user                 User?                 @relation(fields: [userId], references: [id])

  @@index([status])
  @@index([voiceType])
  @@index([submissionSessionId])
  @@index([isPublic])
  @@index([contentId]) // 文本内容ID索引
}

// 故事内容表 - 独立的故事文本管理
model StoryContent {
  id                   String                @id // text_xxxxxxxx-xxxx-xxxx-xxxx-xxxxxxxxxxxx (txid)
  userId               String                // 关联用户

  // 故事内容
  title                String
  content              String
  category             String?               // 故事分类
  tags                 String?               // JSON字符串

  // 作者信息
  author               String?               // 显示名称
  isAnonymous          Boolean               @default(true)
  educationLevel       String?
  industry             String?

  // 版本管理
  version              Int                   @default(1) // 内容版本
  isLatestVersion      Boolean               @default(true) // 是否为最新版本
  parentContentId      String?               // 父内容ID（用于版本追踪）

  // 互动数据
  likes                Int                   @default(0)
  dislikes             Int                   @default(0)
  viewCount            Int                   @default(0)
  shareCount           Int                   @default(0)

  // 审核状态
  status               String                @default("pending") // pending, approved, rejected
  moderationNotes      String?
  reviewedBy           String?
  reviewedAt           DateTime?

  // 展示控制
  isPublic             Boolean               @default(true)
  isDeleted            Boolean               @default(false) // 软删除

  // 审计信息
  ipAddress            String?
  userAgent            String?
  createdAt            DateTime              @default(now())
  updatedAt            DateTime              @updatedAt

  // 测试数据标识
  isTestData           Boolean               @default(false)
  testDataVersion      String?
  testDataSet          String?

  // 关联关系
  user                 User                  @relation(fields: [userId], references: [id])
  votes                Vote[]
  parentContent        StoryContent?         @relation("ContentVersions", fields: [parentContentId], references: [id])
  childVersions        StoryContent[]        @relation("ContentVersions")

  @@index([status])
  @@index([category])
  @@index([userId])
  @@index([isLatestVersion])
  @@index([isPublic])
  @@index([isDeleted])
  @@index([createdAt])
}


// ========== 辅助表 ==========

// 投票表
model Vote {
  id                   String                @id // vote_xxxxxxxx-xxxx-xxxx-xxxx-xxxxxxxxxxxx
  storyContentId       String                // 关联故事内容
  voterType            String                @default("anonymous") // anonymous, registered
  voterId              String?               // 用户ID（如果是注册用户）
  ipAddress            String                // IP地址（匿名投票标识）
  voteType             String                // like, dislike

  createdAt            DateTime              @default(now())

  // 测试数据标识
  isTestData           Boolean               @default(false)
  testDataVersion      String?
  testDataSet          String?

  // 关联关系
  storyContent         StoryContent          @relation(fields: [storyContentId], references: [id])

  // 防止重复投票
  @@unique([storyContentId, ipAddress])
  @@index([storyContentId])
  @@index([voteType])
}

// 系统配置表
model SystemConfig {
  id                   String                @id // config_xxxxxxxx-xxxx-xxxx-xxxx-xxxxxxxxxxxx
  key                  String                @unique
  value                String                // JSON格式配置值
  description          String?
  category             String                @default("general") // general, moderation, feature, migration

  updatedBy            String?               // 更新者ID
  createdAt            DateTime              @default(now())
  updatedAt            DateTime              @updatedAt

  @@index([category])
}

// 敏感词表
model SensitiveWord {
  id                   String                @id // word_xxxxxxxx-xxxx-xxxx-xxxx-xxxxxxxxxxxx
  word                 String                @unique
  level                Int                   @default(1) // 1: low, 2: medium, 3: high
  category             String                @default("general") // general, political, violence, etc.

  isActive             Boolean               @default(true)
  createdAt            DateTime              @default(now())
  updatedAt            DateTime              @updatedAt

  @@index([level])
  @@index([category])
  @@index([isActive])
}

// 标签表
model Tag {
  id                   String                @id // tag_xxxxxxxx-xxxx-xxxx-xxxx-xxxxxxxxxxxx
  name                 String                @unique
  description          String?
  color                String                @default("#3B82F6")
  category             String                @default("general") // general, emotion, topic, quality

  usageCount           Int                   @default(0)
  isSystem             Boolean               @default(false)
  isActive             Boolean               @default(true)

  createdAt            DateTime              @default(now())
  updatedAt            DateTime              @updatedAt

  // 测试数据标识
  isTestData           Boolean               @default(false)
  testDataVersion      String?
  testDataSet          String?

  @@index([category])
  @@index([isSystem])
  @@index([isActive])
  @@index([usageCount])
}

// ========== 统计视图表 ==========

// 问卷统计视图 - 高频查询优化
model QuestionnaireStats {
  id                   String                @id // stats_xxxxxxxx-xxxx-xxxx-xxxx-xxxxxxxxxxxx
  questionId           String                // 题目ID
  optionValue          String                // 选项值
  optionText           String?               // 选项文本

  // 统计数据
  totalCount           Int                   @default(0) // 总选择次数
  percentage           Float                 @default(0) // 百分比

  // 时间维度
  statDate             DateTime              @default(now()) // 统计日期
  lastUpdated          DateTime              @default(now()) @updatedAt

  // 关联关系
  question             Question              @relation(fields: [questionId], references: [id])

  // 唯一约束
  @@unique([questionId, optionValue, statDate])
  @@index([questionId])
  @@index([statDate])
}
