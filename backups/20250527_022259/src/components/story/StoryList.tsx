import { useState, useEffect } from 'react';
import { useQuery } from '@tanstack/react-query';
import { useNavigate } from 'react-router-dom';
import StoryCard from './StoryCard';
import { Button } from '@/components/ui/button';
import { Skeleton } from '@/components/ui/skeleton';
import { getStories, GetStoriesOptions } from '@/lib/api';
import { ChevronLeft, ChevronRight, AlertCircle, Loader2 } from 'lucide-react';
import { cn } from '@/lib/utils';
import { motion, AnimatePresence } from 'framer-motion';

interface StoryListProps {
  sortBy: 'latest' | 'popular';
  tag?: string;
  tags?: string[]; // 支持多标签
  options?: GetStoriesOptions;
  onPopularTagsLoaded?: (tags: Array<{ tag: string; count: number }>) => void;
  layout?: 'grid' | 'list'; // 布局模式
  compact?: boolean; // 紧凑模式
  className?: string;
  searchQuery?: string; // 搜索关键词
}

export default function StoryList({
  sortBy,
  tag,
  tags = [],
  options,
  onPopularTagsLoaded,
  layout = 'grid',
  compact = false,
  className,
  searchQuery = ''
}: StoryListProps) {
  const navigate = useNavigate();
  const [page, setPage] = useState(1);

  // 合并标签
  const allTags = tag ? [tag, ...tags] : tags;
  const mergedOptions = {
    ...options,
    tags: allTags.length > 0 ? allTags : undefined
  };

  // Reset page when sort or tag or options change
  useEffect(() => {
    setPage(1);
  }, [sortBy, tag, tags.join(','), JSON.stringify(options)]);

  // Fetch stories
  const { data, isLoading, error, isFetching } = useQuery(
    ['stories', page, sortBy, tag, tags.join(','), JSON.stringify(mergedOptions), searchQuery],
    () => getStories(page, sortBy, tag, mergedOptions),
    {
      keepPreviousData: true,
      onSuccess: (data) => {
        // Pass popular tags to parent component if available
        if (data.popularTags && onPopularTagsLoaded) {
          onPopularTagsLoaded(data.popularTags);
        }
      },
      staleTime: 60000, // 1分钟内不重新获取数据
    }
  );

  // Handle pagination
  const handlePreviousPage = () => {
    setPage(prev => Math.max(prev - 1, 1));
    // 滚动到顶部
    window.scrollTo({ top: 0, behavior: 'smooth' });
  };

  const handleNextPage = () => {
    if (data && page < data.totalPages) {
      setPage(prev => prev + 1);
      // 滚动到顶部
      window.scrollTo({ top: 0, behavior: 'smooth' });
    }
  };

  // Render loading state
  if (isLoading && page === 1) {
    return (
      <div className={cn(
        layout === 'grid'
          ? "grid grid-cols-1 md:grid-cols-2 gap-6"
          : "space-y-6",
        className
      )}>
        {Array.from({ length: compact ? 3 : 6 }).map((_, index) => (
          <div key={index} className="border rounded-lg p-6 space-y-4">
            <Skeleton className="h-6 w-3/4" />
            <div className="flex gap-1">
              <Skeleton className="h-5 w-16" />
              <Skeleton className="h-5 w-16" />
            </div>
            <Skeleton className="h-4 w-full" />
            <Skeleton className="h-4 w-full" />
            <Skeleton className="h-4 w-2/3" />
            <div className="flex justify-between items-center pt-4">
              <Skeleton className="h-4 w-20" />
              <div className="flex space-x-2">
                <Skeleton className="h-8 w-16" />
                <Skeleton className="h-8 w-16" />
              </div>
            </div>
          </div>
        ))}
      </div>
    );
  }

  // Render error state
  if (error) {
    return (
      <div className="text-center py-12 border rounded-lg bg-red-50">
        <AlertCircle className="h-12 w-12 text-red-500 mx-auto mb-4" />
        <h3 className="text-lg font-medium mb-2 text-red-700">加载故事失败</h3>
        <p className="text-gray-600 mb-4">
          发生错误，请稍后重试或联系管理员
        </p>
        <Button
          onClick={() => window.location.reload()}
          variant="outline"
        >
          刷新页面
        </Button>
      </div>
    );
  }

  // Render empty state
  if (data && data.stories.length === 0) {
    return (
      <div className="text-center py-12 border rounded-lg bg-gray-50">
        <h3 className="text-lg font-medium mb-2">暂无故事</h3>
        <p className="text-gray-500 mb-4">
          {allTags.length > 0
            ? '该标签下暂无故事，请尝试其他标签'
            : searchQuery
              ? `没有找到包含"${searchQuery}"的故事`
              : '暂无故事，成为第一个分享故事的人吧！'
          }
        </p>
        <Button onClick={() => {}}>分享故事</Button>
      </div>
    );
  }

  return (
    <div className={className}>
      <AnimatePresence mode="wait">
        <motion.div
          key={`${page}-${sortBy}-${allTags.join('-')}-${searchQuery}`}
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          exit={{ opacity: 0 }}
          transition={{ duration: 0.3 }}
        >
          <div className={cn(
            layout === 'grid'
              ? "grid grid-cols-1 md:grid-cols-2 gap-6"
              : "space-y-6"
          )}>
            {data?.stories.map((story, index) => (
              <StoryCard
                key={story.id}
                story={story}
                onClick={() => navigate(`/story/${story.id}`)}
                searchQuery={searchQuery}
                compact={compact}
                featured={index === 0 && page === 1 && sortBy === 'popular'}
                className={layout === 'list' ? "w-full" : ""}
              />
            ))}
          </div>
        </motion.div>
      </AnimatePresence>

      {isFetching && page > 1 && (
        <div className="flex justify-center my-4">
          <Loader2 className="h-6 w-6 animate-spin text-primary" />
        </div>
      )}

      {data && data.totalPages > 1 && (
        <div className="flex justify-center items-center gap-4 mt-8">
          <Button
            variant="outline"
            onClick={handlePreviousPage}
            disabled={page === 1 || isFetching}
            className="flex items-center"
          >
            <ChevronLeft className="h-4 w-4 mr-1" />
            上一页
          </Button>
          <span className="text-sm">
            第 {page} 页，共 {data.totalPages} 页
          </span>
          <Button
            variant="outline"
            onClick={handleNextPage}
            disabled={page === data.totalPages || isFetching}
            className="flex items-center"
          >
            下一页
            <ChevronRight className="h-4 w-4 ml-1" />
          </Button>
        </div>
      )}
    </div>
  );
}
