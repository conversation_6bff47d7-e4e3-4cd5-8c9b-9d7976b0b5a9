/**
 * 行为跟踪服务
 */

import { BehaviorAnalysisConfig, ValidationResult } from '../types';

/**
 * 行为数据接口
 */
interface BehaviorData {
  startTime: number;
  mouseMovements: number;
  mouseClicks: number;
  keyPresses: number;
  fieldChanges: Record<string, { value: string; time: number }>;
  focusBlurEvents: number;
  scrollEvents: number;
  timeOnPage: number;
  lastActivityTime: number;
}

/**
 * 行为跟踪服务类
 */
export class BehaviorTrackingService {
  private config: BehaviorAnalysisConfig;
  private behaviorData: BehaviorData;
  private eventListeners: { [key: string]: EventListener } = {};
  private initialized: boolean = false;

  /**
   * 构造函数
   */
  constructor() {
    this.config = {
      enabled: false,
      minCompletionTime: 0,
      trackMouseMovements: false,
      trackKeyboardEvents: false,
      suspiciousScoreThreshold: 100
    };

    this.behaviorData = {
      startTime: Date.now(),
      mouseMovements: 0,
      mouseClicks: 0,
      keyPresses: 0,
      fieldChanges: {},
      focusBlurEvents: 0,
      scrollEvents: 0,
      timeOnPage: 0,
      lastActivityTime: Date.now()
    };
  }

  /**
   * 初始化服务
   * @param config 行为分析配置
   */
  public initialize(config: BehaviorAnalysisConfig): void {
    this.config = { ...config };
    
    // 如果已初始化且启用状态未变，不重复初始化
    if (this.initialized && this.config.enabled === this.initialized) {
      return;
    }
    
    // 重置行为数据
    this.resetBehaviorData();
    
    // 如果启用，添加事件监听器
    if (this.config.enabled) {
      this.addEventListeners();
    } else {
      this.removeEventListeners();
    }
    
    this.initialized = this.config.enabled;
  }

  /**
   * 更新配置
   * @param config 行为分析配置
   */
  public updateConfig(config: BehaviorAnalysisConfig): void {
    const wasEnabled = this.config.enabled;
    this.config = { ...this.config, ...config };
    
    // 如果启用状态变更，添加或移除事件监听器
    if (wasEnabled !== this.config.enabled) {
      if (this.config.enabled) {
        this.resetBehaviorData();
        this.addEventListeners();
      } else {
        this.removeEventListeners();
      }
      
      this.initialized = this.config.enabled;
    }
  }

  /**
   * 获取行为元数据
   */
  public getMetadata(): any {
    // 更新页面停留时间
    this.behaviorData.timeOnPage = Date.now() - this.behaviorData.startTime;
    
    return {
      completionTime: this.behaviorData.timeOnPage,
      mouseMovements: this.behaviorData.mouseMovements,
      mouseClicks: this.behaviorData.mouseClicks,
      keyPresses: this.behaviorData.keyPresses,
      fieldChanges: Object.keys(this.behaviorData.fieldChanges).length,
      focusBlurEvents: this.behaviorData.focusBlurEvents,
      scrollEvents: this.behaviorData.scrollEvents
    };
  }

  /**
   * 验证行为数据
   */
  public validateBehavior(): ValidationResult {
    if (!this.config.enabled) {
      return { valid: true };
    }
    
    // 计算可疑分数
    const suspiciousScore = this.calculateSuspiciousScore();
    
    // 如果可疑分数超过阈值，返回无效
    if (suspiciousScore >= this.config.suspiciousScoreThreshold) {
      return { 
        valid: false, 
        error: '提交被拒绝，请重试',
        suspiciousScore
      };
    }
    
    return { valid: true, suspiciousScore };
  }

  /**
   * 记录字段变化
   * @param fieldName 字段名称
   * @param value 字段值
   */
  public recordFieldChange(fieldName: string, value: string): void {
    if (!this.config.enabled) {
      return;
    }
    
    this.behaviorData.fieldChanges[fieldName] = {
      value,
      time: Date.now()
    };
    
    this.behaviorData.lastActivityTime = Date.now();
  }

  /**
   * 重置行为数据
   */
  private resetBehaviorData(): void {
    this.behaviorData = {
      startTime: Date.now(),
      mouseMovements: 0,
      mouseClicks: 0,
      keyPresses: 0,
      fieldChanges: {},
      focusBlurEvents: 0,
      scrollEvents: 0,
      timeOnPage: 0,
      lastActivityTime: Date.now()
    };
  }

  /**
   * 添加事件监听器
   */
  private addEventListeners(): void {
    // 鼠标移动
    if (this.config.trackMouseMovements) {
      this.eventListeners.mousemove = this.handleMouseMove.bind(this);
      document.addEventListener('mousemove', this.eventListeners.mousemove);
    }
    
    // 鼠标点击
    this.eventListeners.mousedown = this.handleMouseClick.bind(this);
    document.addEventListener('mousedown', this.eventListeners.mousedown);
    
    // 键盘事件
    if (this.config.trackKeyboardEvents) {
      this.eventListeners.keydown = this.handleKeyPress.bind(this);
      document.addEventListener('keydown', this.eventListeners.keydown);
    }
    
    // 焦点事件
    this.eventListeners.focus = this.handleFocusBlur.bind(this);
    document.addEventListener('focus', this.eventListeners.focus, true);
    this.eventListeners.blur = this.handleFocusBlur.bind(this);
    document.addEventListener('blur', this.eventListeners.blur, true);
    
    // 滚动事件
    this.eventListeners.scroll = this.handleScroll.bind(this);
    document.addEventListener('scroll', this.eventListeners.scroll);
  }

  /**
   * 移除事件监听器
   */
  private removeEventListeners(): void {
    // 移除所有事件监听器
    Object.entries(this.eventListeners).forEach(([event, listener]) => {
      document.removeEventListener(event as keyof DocumentEventMap, listener);
    });
    
    // 清空事件监听器对象
    this.eventListeners = {};
  }

  /**
   * 处理鼠标移动
   */
  private handleMouseMove(): void {
    this.behaviorData.mouseMovements++;
    this.behaviorData.lastActivityTime = Date.now();
  }

  /**
   * 处理鼠标点击
   */
  private handleMouseClick(): void {
    this.behaviorData.mouseClicks++;
    this.behaviorData.lastActivityTime = Date.now();
  }

  /**
   * 处理键盘按键
   */
  private handleKeyPress(): void {
    this.behaviorData.keyPresses++;
    this.behaviorData.lastActivityTime = Date.now();
  }

  /**
   * 处理焦点和失焦事件
   */
  private handleFocusBlur(): void {
    this.behaviorData.focusBlurEvents++;
    this.behaviorData.lastActivityTime = Date.now();
  }

  /**
   * 处理滚动事件
   */
  private handleScroll(): void {
    this.behaviorData.scrollEvents++;
    this.behaviorData.lastActivityTime = Date.now();
  }

  /**
   * 计算可疑分数
   */
  private calculateSuspiciousScore(): number {
    let score = 0;
    
    // 更新页面停留时间
    this.behaviorData.timeOnPage = Date.now() - this.behaviorData.startTime;
    
    // 检查完成时间
    if (this.behaviorData.timeOnPage < this.config.minCompletionTime) {
      // 完成时间过短，增加可疑分数
      const timeRatio = this.behaviorData.timeOnPage / this.config.minCompletionTime;
      score += Math.round(30 * (1 - timeRatio));
    }
    
    // 检查鼠标移动
    if (this.config.trackMouseMovements && this.behaviorData.mouseMovements < 10) {
      // 鼠标移动过少，增加可疑分数
      score += Math.max(0, 10 - this.behaviorData.mouseMovements);
    }
    
    // 检查键盘事件
    if (this.config.trackKeyboardEvents && this.behaviorData.keyPresses < 5) {
      // 键盘事件过少，增加可疑分数
      score += Math.max(0, 5 - this.behaviorData.keyPresses) * 2;
    }
    
    // 检查字段变化
    const fieldChangeCount = Object.keys(this.behaviorData.fieldChanges).length;
    if (fieldChangeCount < 3) {
      // 字段变化过少，增加可疑分数
      score += Math.max(0, 3 - fieldChangeCount) * 5;
    }
    
    // 检查焦点事件
    if (this.behaviorData.focusBlurEvents < 2) {
      // 焦点事件过少，增加可疑分数
      score += Math.max(0, 2 - this.behaviorData.focusBlurEvents) * 5;
    }
    
    return score;
  }
}
