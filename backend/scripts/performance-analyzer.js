#!/usr/bin/env node

/**
 * 🚀 性能分析器
 * 分析API响应时间、数据库查询性能和系统瓶颈
 */

const fs = require('fs');
const path = require('path');

class PerformanceAnalyzer {
  constructor() {
    this.baseUrl = process.env.API_BASE_URL || 'https://college-employment-survey.aibook2099.workers.dev';
    this.results = {
      timestamp: new Date().toISOString(),
      baseUrl: this.baseUrl,
      endpoints: {},
      summary: {},
      recommendations: []
    };
  }

  // 测试单个端点性能
  async testEndpoint(endpoint, options = {}) {
    const {
      method = 'GET',
      body = null,
      iterations = 10,
      warmup = 2
    } = options;

    console.log(`🔍 测试端点: ${method} ${endpoint}`);

    const url = `${this.baseUrl}${endpoint}`;
    const times = [];
    const errors = [];

    // 预热请求
    for (let i = 0; i < warmup; i++) {
      try {
        await this.makeRequest(url, method, body);
      } catch (error) {
        // 忽略预热错误
      }
    }

    // 正式测试
    for (let i = 0; i < iterations; i++) {
      try {
        const startTime = Date.now();
        const response = await this.makeRequest(url, method, body);
        const endTime = Date.now();
        
        const duration = endTime - startTime;
        times.push(duration);

        // 检查响应大小
        const responseSize = JSON.stringify(response).length;
        
        console.log(`  测试 ${i + 1}/${iterations}: ${duration}ms (${responseSize} bytes)`);
      } catch (error) {
        errors.push(error.message);
        console.log(`  测试 ${i + 1}/${iterations}: 错误 - ${error.message}`);
      }
    }

    // 计算统计数据
    const stats = this.calculateStats(times);
    
    this.results.endpoints[endpoint] = {
      method,
      iterations,
      successCount: times.length,
      errorCount: errors.length,
      errors: errors.slice(0, 3), // 只保留前3个错误
      ...stats
    };

    return stats;
  }

  // 发送HTTP请求
  async makeRequest(url, method, body) {
    const options = {
      method,
      headers: {
        'Content-Type': 'application/json',
        'User-Agent': 'Performance-Analyzer/1.0'
      }
    };

    if (body) {
      options.body = typeof body === 'string' ? body : JSON.stringify(body);
    }

    const response = await fetch(url, options);
    
    if (!response.ok) {
      throw new Error(`HTTP ${response.status}: ${response.statusText}`);
    }

    return await response.json();
  }

  // 计算统计数据
  calculateStats(times) {
    if (times.length === 0) {
      return {
        min: 0,
        max: 0,
        avg: 0,
        median: 0,
        p95: 0,
        p99: 0,
        stdDev: 0
      };
    }

    const sorted = times.slice().sort((a, b) => a - b);
    const sum = times.reduce((a, b) => a + b, 0);
    const avg = sum / times.length;

    // 计算标准差
    const variance = times.reduce((acc, time) => acc + Math.pow(time - avg, 2), 0) / times.length;
    const stdDev = Math.sqrt(variance);

    return {
      min: Math.min(...times),
      max: Math.max(...times),
      avg: Math.round(avg * 100) / 100,
      median: sorted[Math.floor(sorted.length / 2)],
      p95: sorted[Math.floor(sorted.length * 0.95)],
      p99: sorted[Math.floor(sorted.length * 0.99)],
      stdDev: Math.round(stdDev * 100) / 100
    };
  }

  // 运行完整的性能测试套件
  async runPerformanceTests() {
    console.log('🚀 开始性能分析...\n');

    // 核心API端点
    const endpoints = [
      { path: '/', name: 'API信息' },
      { path: '/health', name: '健康检查' },
      { path: '/api/system/health', name: '系统健康检查' },
      { path: '/api/questionnaire/stats', name: '问卷统计' },
      { path: '/api/story/list?page=1&pageSize=10', name: '故事列表（小）' },
      { path: '/api/story/list?page=1&pageSize=50', name: '故事列表（大）' },
      { path: '/api/story/list?page=1&pageSize=100', name: '故事列表（最大）' }
    ];

    for (const endpoint of endpoints) {
      try {
        await this.testEndpoint(endpoint.path, {
          iterations: 15,
          warmup: 3
        });
        console.log('');
      } catch (error) {
        console.error(`❌ 测试 ${endpoint.name} 失败:`, error.message);
        console.log('');
      }
    }

    // 并发测试
    await this.runConcurrencyTests();

    // 生成分析报告
    this.generateAnalysis();
    this.generateRecommendations();
  }

  // 并发测试
  async runConcurrencyTests() {
    console.log('🔄 运行并发测试...');

    const endpoint = '/api/questionnaire/stats';
    const concurrencyLevels = [1, 5, 10, 20];

    for (const concurrency of concurrencyLevels) {
      console.log(`  测试并发级别: ${concurrency}`);
      
      const promises = [];
      const startTime = Date.now();

      for (let i = 0; i < concurrency; i++) {
        promises.push(this.makeRequest(`${this.baseUrl}${endpoint}`, 'GET'));
      }

      try {
        await Promise.all(promises);
        const totalTime = Date.now() - startTime;
        const avgTime = totalTime / concurrency;

        console.log(`    总时间: ${totalTime}ms, 平均: ${avgTime.toFixed(2)}ms`);

        if (!this.results.concurrency) {
          this.results.concurrency = {};
        }
        
        this.results.concurrency[concurrency] = {
          totalTime,
          avgTime: Math.round(avgTime * 100) / 100,
          throughput: Math.round((concurrency / totalTime) * 1000 * 100) / 100 // requests/second
        };
      } catch (error) {
        console.log(`    错误: ${error.message}`);
      }
    }
    console.log('');
  }

  // 生成性能分析
  generateAnalysis() {
    console.log('📊 生成性能分析...');

    const endpoints = Object.entries(this.results.endpoints);
    
    if (endpoints.length === 0) {
      this.results.summary = { status: 'no_data' };
      return;
    }

    // 计算总体统计
    const allAvgTimes = endpoints.map(([_, stats]) => stats.avg).filter(t => t > 0);
    const allP95Times = endpoints.map(([_, stats]) => stats.p95).filter(t => t > 0);

    this.results.summary = {
      totalEndpoints: endpoints.length,
      avgResponseTime: Math.round((allAvgTimes.reduce((a, b) => a + b, 0) / allAvgTimes.length) * 100) / 100,
      avgP95ResponseTime: Math.round((allP95Times.reduce((a, b) => a + b, 0) / allP95Times.length) * 100) / 100,
      fastestEndpoint: this.findFastestEndpoint(endpoints),
      slowestEndpoint: this.findSlowestEndpoint(endpoints),
      performanceGrade: this.calculatePerformanceGrade(allAvgTimes)
    };
  }

  // 找到最快的端点
  findFastestEndpoint(endpoints) {
    let fastest = null;
    let minTime = Infinity;

    for (const [path, stats] of endpoints) {
      if (stats.avg > 0 && stats.avg < minTime) {
        minTime = stats.avg;
        fastest = { path, time: stats.avg };
      }
    }

    return fastest;
  }

  // 找到最慢的端点
  findSlowestEndpoint(endpoints) {
    let slowest = null;
    let maxTime = 0;

    for (const [path, stats] of endpoints) {
      if (stats.avg > maxTime) {
        maxTime = stats.avg;
        slowest = { path, time: stats.avg };
      }
    }

    return slowest;
  }

  // 计算性能等级
  calculatePerformanceGrade(avgTimes) {
    if (avgTimes.length === 0) return 'unknown';

    const overallAvg = avgTimes.reduce((a, b) => a + b, 0) / avgTimes.length;

    if (overallAvg < 100) return 'excellent';
    if (overallAvg < 200) return 'good';
    if (overallAvg < 500) return 'fair';
    if (overallAvg < 1000) return 'poor';
    return 'critical';
  }

  // 生成优化建议
  generateRecommendations() {
    console.log('💡 生成优化建议...');

    const recommendations = [];
    const summary = this.results.summary;

    // 基于平均响应时间的建议
    if (summary.avgResponseTime > 500) {
      recommendations.push({
        category: 'Response Time',
        priority: 'high',
        title: '响应时间过慢',
        description: `平均响应时间 ${summary.avgResponseTime}ms 超过推荐值 (< 200ms)`,
        actions: [
          '优化数据库查询',
          '添加缓存层',
          '减少API响应数据量',
          '优化算法复杂度'
        ]
      });
    }

    // 基于P95响应时间的建议
    if (summary.avgP95ResponseTime > 1000) {
      recommendations.push({
        category: 'Reliability',
        priority: 'medium',
        title: 'P95响应时间不稳定',
        description: `P95响应时间 ${summary.avgP95ResponseTime}ms 表明存在性能波动`,
        actions: [
          '分析慢查询日志',
          '优化数据库索引',
          '实施连接池',
          '监控系统资源使用'
        ]
      });
    }

    // 基于并发性能的建议
    if (this.results.concurrency) {
      const concurrencyData = Object.values(this.results.concurrency);
      const throughputDecline = this.analyzeThroughputDecline(concurrencyData);
      
      if (throughputDecline > 50) {
        recommendations.push({
          category: 'Concurrency',
          priority: 'medium',
          title: '并发性能下降',
          description: `高并发下吞吐量下降 ${throughputDecline.toFixed(1)}%`,
          actions: [
            '优化数据库连接池配置',
            '实施请求限流',
            '添加负载均衡',
            '优化锁竞争'
          ]
        });
      }
    }

    // 端点特定建议
    Object.entries(this.results.endpoints).forEach(([path, stats]) => {
      if (stats.avg > 1000) {
        recommendations.push({
          category: 'Endpoint Optimization',
          priority: 'high',
          title: `慢端点: ${path}`,
          description: `端点 ${path} 平均响应时间 ${stats.avg}ms`,
          actions: [
            '分析该端点的具体查询',
            '考虑数据预计算',
            '实施端点级缓存',
            '优化数据序列化'
          ]
        });
      }
    });

    // 通用优化建议
    recommendations.push({
      category: 'General Optimization',
      priority: 'low',
      title: '通用性能优化',
      description: '持续性能改进建议',
      actions: [
        '定期运行性能测试',
        '监控生产环境性能指标',
        '建立性能预算',
        '实施性能回归测试'
      ]
    });

    this.results.recommendations = recommendations;
  }

  // 分析吞吐量下降
  analyzeThroughputDecline(concurrencyData) {
    if (concurrencyData.length < 2) return 0;

    const firstThroughput = concurrencyData[0].throughput;
    const lastThroughput = concurrencyData[concurrencyData.length - 1].throughput;

    return ((firstThroughput - lastThroughput) / firstThroughput) * 100;
  }

  // 保存结果
  saveResults() {
    const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
    const filename = `performance-report-${timestamp}.json`;
    
    fs.writeFileSync(filename, JSON.stringify(this.results, null, 2));
    console.log(`📄 性能报告已保存: ${filename}`);

    // 生成HTML报告
    this.generateHTMLReport(filename.replace('.json', '.html'));
  }

  // 生成HTML报告
  generateHTMLReport(filename) {
    const html = `
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>性能分析报告</title>
    <style>
        body { font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif; margin: 0; padding: 20px; background: #f5f5f5; }
        .container { max-width: 1200px; margin: 0 auto; background: white; border-radius: 8px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        .header { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 30px; border-radius: 8px 8px 0 0; }
        .content { padding: 30px; }
        .metric-card { display: inline-block; background: #f8f9fa; border-radius: 8px; padding: 20px; margin: 10px; text-align: center; min-width: 150px; }
        .metric-value { font-size: 2em; font-weight: bold; margin-bottom: 5px; }
        .excellent { color: #28a745; }
        .good { color: #17a2b8; }
        .fair { color: #ffc107; }
        .poor { color: #fd7e14; }
        .critical { color: #dc3545; }
        .section { margin: 30px 0; }
        .section h2 { color: #333; border-bottom: 2px solid #eee; padding-bottom: 10px; }
        .endpoint-table { width: 100%; border-collapse: collapse; margin: 20px 0; }
        .endpoint-table th, .endpoint-table td { padding: 12px; text-align: left; border-bottom: 1px solid #ddd; }
        .endpoint-table th { background-color: #f8f9fa; font-weight: 600; }
        .recommendation { background: #f8f9fa; border-left: 4px solid #007bff; padding: 15px; margin: 10px 0; border-radius: 4px; }
        .high { border-left-color: #dc3545; }
        .medium { border-left-color: #ffc107; }
        .low { border-left-color: #28a745; }
        .chart { margin: 20px 0; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🚀 性能分析报告</h1>
            <p>基准URL: ${this.results.baseUrl}</p>
            <p>生成时间: ${new Date(this.results.timestamp).toLocaleString('zh-CN')}</p>
        </div>
        
        <div class="content">
            <div class="section">
                <h2>📊 总体性能指标</h2>
                <div class="metric-card">
                    <div class="metric-value ${this.results.summary.performanceGrade}">${this.results.summary.avgResponseTime || 'N/A'}ms</div>
                    <div>平均响应时间</div>
                </div>
                <div class="metric-card">
                    <div class="metric-value">${this.results.summary.avgP95ResponseTime || 'N/A'}ms</div>
                    <div>P95响应时间</div>
                </div>
                <div class="metric-card">
                    <div class="metric-value ${this.results.summary.performanceGrade}">${this.results.summary.performanceGrade || 'unknown'}</div>
                    <div>性能等级</div>
                </div>
                <div class="metric-card">
                    <div class="metric-value">${this.results.summary.totalEndpoints || 0}</div>
                    <div>测试端点数</div>
                </div>
            </div>

            <div class="section">
                <h2>🎯 端点性能详情</h2>
                <table class="endpoint-table">
                    <thead>
                        <tr>
                            <th>端点</th>
                            <th>平均时间</th>
                            <th>最小时间</th>
                            <th>最大时间</th>
                            <th>P95时间</th>
                            <th>成功率</th>
                        </tr>
                    </thead>
                    <tbody>
                        ${Object.entries(this.results.endpoints).map(([path, stats]) => `
                            <tr>
                                <td><code>${path}</code></td>
                                <td>${stats.avg}ms</td>
                                <td>${stats.min}ms</td>
                                <td>${stats.max}ms</td>
                                <td>${stats.p95}ms</td>
                                <td>${((stats.successCount / (stats.successCount + stats.errorCount)) * 100).toFixed(1)}%</td>
                            </tr>
                        `).join('')}
                    </tbody>
                </table>
            </div>

            ${this.results.concurrency ? `
            <div class="section">
                <h2>🔄 并发性能</h2>
                <table class="endpoint-table">
                    <thead>
                        <tr>
                            <th>并发数</th>
                            <th>总时间</th>
                            <th>平均时间</th>
                            <th>吞吐量 (req/s)</th>
                        </tr>
                    </thead>
                    <tbody>
                        ${Object.entries(this.results.concurrency).map(([level, stats]) => `
                            <tr>
                                <td>${level}</td>
                                <td>${stats.totalTime}ms</td>
                                <td>${stats.avgTime}ms</td>
                                <td>${stats.throughput}</td>
                            </tr>
                        `).join('')}
                    </tbody>
                </table>
            </div>
            ` : ''}

            <div class="section">
                <h2>💡 优化建议</h2>
                ${this.results.recommendations.map(rec => `
                    <div class="recommendation ${rec.priority}">
                        <h4>${rec.title}</h4>
                        <p>${rec.description}</p>
                        <ul>
                            ${rec.actions.map(action => `<li>${action}</li>`).join('')}
                        </ul>
                    </div>
                `).join('')}
            </div>
        </div>
    </div>
</body>
</html>`;

    fs.writeFileSync(filename, html);
    console.log(`📄 HTML报告已生成: ${filename}`);
  }

  // 运行分析
  async run() {
    try {
      await this.runPerformanceTests();
      this.saveResults();
      
      console.log('\n🎉 性能分析完成!');
      console.log(`📊 总体性能等级: ${this.results.summary.performanceGrade || 'unknown'}`);
      console.log(`⚡ 平均响应时间: ${this.results.summary.avgResponseTime || 'N/A'}ms`);
      
      if (this.results.summary.slowestEndpoint) {
        console.log(`🐌 最慢端点: ${this.results.summary.slowestEndpoint.path} (${this.results.summary.slowestEndpoint.time}ms)`);
      }
      
      if (this.results.summary.fastestEndpoint) {
        console.log(`⚡ 最快端点: ${this.results.summary.fastestEndpoint.path} (${this.results.summary.fastestEndpoint.time}ms)`);
      }
      
    } catch (error) {
      console.error('❌ 性能分析失败:', error);
      process.exit(1);
    }
  }
}

// 主函数
async function main() {
  const analyzer = new PerformanceAnalyzer();
  await analyzer.run();
}

if (require.main === module) {
  main().catch(console.error);
}

module.exports = PerformanceAnalyzer;
