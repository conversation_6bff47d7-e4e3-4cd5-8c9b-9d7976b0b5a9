#!/usr/bin/env node

/**
 * 测试数据管理命令行工具
 * 
 * 使用方法:
 * node test-data-cli.js generate [dataset]
 * node test-data-cli.js reset [--smart]
 * node test-data-cli.js snapshot <name> [description]
 * node test-data-cli.js restore <snapshot-name>
 * node test-data-cli.js status
 * node test-data-cli.js list-snapshots
 * node test-data-cli.js clean
 */

const { TestDataManager } = require('./test-data-manager');
const { TestDataResetManager } = require('./test-data-reset');

/**
 * 命令行工具类
 */
class TestDataCLI {
  constructor() {
    this.manager = new TestDataManager();
    this.resetManager = new TestDataResetManager();
  }

  /**
   * 解析命令行参数
   */
  parseArgs() {
    const args = process.argv.slice(2);
    const command = args[0];
    const options = {};
    const params = [];

    for (let i = 1; i < args.length; i++) {
      const arg = args[i];
      if (arg.startsWith('--')) {
        const key = arg.slice(2);
        const nextArg = args[i + 1];
        if (nextArg && !nextArg.startsWith('--')) {
          options[key] = nextArg;
          i++; // 跳过下一个参数
        } else {
          options[key] = true;
        }
      } else {
        params.push(arg);
      }
    }

    return { command, params, options };
  }

  /**
   * 显示帮助信息
   */
  showHelp() {
    console.log(`
🧪 测试数据管理工具

使用方法:
  node test-data-cli.js <command> [options]

命令:
  generate [dataset]              生成测试数据
    dataset: role-testing (默认) | audit-testing
    
  reset [--smart]                 重置测试数据
    --smart: 智能重置，只重置被修改的数据
    
  snapshot <name> [description]   创建快照
    name: 快照名称
    description: 快照描述
    
  restore <snapshot-name>         从快照恢复
    snapshot-name: 要恢复的快照名称
    
  status                          查看测试数据状态
  
  list-snapshots                  列出所有快照
  
  clean                           清除所有测试数据
  
  cleanup-snapshots [--max=10]   清理旧快照
    --max: 保留的最大快照数量

示例:
  node test-data-cli.js generate role-testing
  node test-data-cli.js reset --smart
  node test-data-cli.js snapshot initial-state "初始测试数据"
  node test-data-cli.js restore initial-state
  node test-data-cli.js status
`);
  }

  /**
   * 执行命令
   */
  async execute() {
    const { command, params, options } = this.parseArgs();

    try {
      switch (command) {
      case 'generate':
        await this.handleGenerate(params[0] || 'role-testing');
        break;
          
      case 'reset':
        await this.handleReset(options.smart);
        break;
          
      case 'snapshot':
        await this.handleSnapshot(params[0], params[1]);
        break;
          
      case 'restore':
        await this.handleRestore(params[0]);
        break;
          
      case 'status':
        await this.handleStatus();
        break;
          
      case 'list-snapshots':
        await this.handleListSnapshots();
        break;
          
      case 'clean':
        await this.handleClean();
        break;
          
      case 'cleanup-snapshots':
        await this.handleCleanupSnapshots(options.max || 10);
        break;
          
      case 'help':
      case '--help':
      case '-h':
        this.showHelp();
        break;
          
      default:
        console.log(`❌ 未知命令: ${command}`);
        this.showHelp();
        process.exit(1);
      }
    } catch (error) {
      console.error('❌ 执行失败:', error.message);
      process.exit(1);
    }
  }

  /**
   * 处理生成命令
   */
  async handleGenerate(dataset) {
    console.log(`🚀 生成测试数据集: ${dataset}`);
    
    const startTime = Date.now();
    const results = await this.manager.generateTestData(dataset);
    const duration = Date.now() - startTime;
    
    console.log('\n📊 生成结果:');
    console.log(`  用户: ${results.users.total} (审核员: ${results.users.reviewers}, 管理员: ${results.users.admins}, 普通用户: ${results.users.regularUsers})`);
    console.log(`  问卷: ${results.questionnaires.total} (实名: ${results.questionnaires.withUserId}, 匿名: ${results.questionnaires.anonymous})`);
    console.log(`  故事: ${results.stories.total} (实名: ${results.stories.withUserId}, 匿名: ${results.stories.anonymous}, 待审核: ${results.stories.pendingReview})`);
    console.log(`  耗时: ${duration}ms`);
    console.log('\n✅ 测试数据生成完成！');
  }

  /**
   * 处理重置命令
   */
  async handleReset(smart = false) {
    if (smart) {
      console.log('🧠 执行智能重置...');
      const modifiedData = await this.resetManager.smartReset();
      
      console.log('\n📊 重置结果:');
      console.log(`  故事: ${modifiedData.stories.length}`);
      console.log(`  用户: ${modifiedData.users.length}`);
      console.log(`  问卷: ${modifiedData.questionnaires.length}`);
    } else {
      console.log('🔄 执行完全重置...');
      await this.resetManager.resetToInitialState();
    }
    
    console.log('\n✅ 重置完成！');
  }

  /**
   * 处理快照命令
   */
  async handleSnapshot(name, description = '') {
    if (!name) {
      console.log('❌ 请提供快照名称');
      return;
    }
    
    console.log(`📸 创建快照: ${name}`);
    const snapshot = await this.manager.createSnapshot(name, description);
    
    console.log('\n📊 快照信息:');
    console.log(`  ID: ${snapshot.id}`);
    console.log(`  名称: ${snapshot.name}`);
    console.log(`  描述: ${snapshot.description}`);
    console.log(`  创建时间: ${snapshot.createdAt}`);
    console.log(`  校验和: ${snapshot.checksum}`);
    console.log('\n✅ 快照创建完成！');
  }

  /**
   * 处理恢复命令
   */
  async handleRestore(snapshotName) {
    if (!snapshotName) {
      console.log('❌ 请提供快照名称');
      return;
    }
    
    console.log(`📸 从快照恢复: ${snapshotName}`);
    const snapshot = await this.resetManager.restoreFromSnapshot(snapshotName);
    
    console.log('\n📊 恢复信息:');
    console.log(`  快照: ${snapshot.name}`);
    console.log(`  描述: ${snapshot.description}`);
    console.log(`  创建时间: ${snapshot.createdAt}`);
    console.log('\n✅ 快照恢复完成！');
  }

  /**
   * 处理状态命令
   */
  async handleStatus() {
    console.log('📊 测试数据状态:');
    
    const status = await this.manager.getStatus();
    
    console.log(`  用户数量: ${status.users}`);
    console.log(`  问卷数量: ${status.questionnaires}`);
    console.log(`  故事数量: ${status.stories}`);
    console.log(`  待审核故事: ${status.pendingStories}`);
    console.log(`  是否有测试数据: ${status.hasTestData ? '是' : '否'}`);
    
    if (status.hasTestData) {
      // 获取详细统计
      const detailedStatus = await this.getDetailedStatus();
      console.log('\n📈 详细统计:');
      console.log(`  审核员: ${detailedStatus.reviewers}`);
      console.log(`  管理员: ${detailedStatus.admins}`);
      console.log(`  普通用户: ${detailedStatus.regularUsers}`);
      console.log(`  匿名问卷: ${detailedStatus.anonymousQuestionnaires}`);
      console.log(`  匿名故事: ${detailedStatus.anonymousStories}`);
    }
  }

  /**
   * 获取详细状态
   */
  async getDetailedStatus() {
    const { PrismaClient } = require('@prisma/client');
    const prisma = new PrismaClient();
    
    try {
      const [
        reviewers,
        admins,
        regularUsers,
        anonymousQuestionnaires,
        anonymousStories
      ] = await Promise.all([
        prisma.user.count({ where: { isTestData: true, role: 'reviewer' } }),
        prisma.user.count({ where: { isTestData: true, role: 'admin' } }),
        prisma.user.count({ where: { isTestData: true, role: 'user' } }),
        prisma.questionnaireResponse.count({ where: { isTestData: true, isAnonymous: true } }),
        prisma.story.count({ where: { isTestData: true, isAnonymous: true } })
      ]);
      
      return {
        reviewers,
        admins,
        regularUsers,
        anonymousQuestionnaires,
        anonymousStories
      };
    } finally {
      await prisma.$disconnect();
    }
  }

  /**
   * 处理列出快照命令
   */
  async handleListSnapshots() {
    console.log('📸 快照列表:');
    
    const snapshots = await this.resetManager.listSnapshots();
    
    if (snapshots.length === 0) {
      console.log('  暂无快照');
      return;
    }
    
    snapshots.forEach((snapshot, index) => {
      console.log(`\n  ${index + 1}. ${snapshot.name}`);
      console.log(`     描述: ${snapshot.description || '无描述'}`);
      console.log(`     创建时间: ${snapshot.createdAt}`);
      console.log(`     文件: ${snapshot.file}`);
      console.log(`     校验和: ${snapshot.checksum.slice(0, 8)}...`);
    });
    
    console.log(`\n📊 总计: ${snapshots.length} 个快照`);
  }

  /**
   * 处理清理命令
   */
  async handleClean() {
    console.log('🗑️ 清除所有测试数据...');
    
    await this.manager.clearTestData();
    
    console.log('✅ 测试数据清除完成！');
  }

  /**
   * 处理清理快照命令
   */
  async handleCleanupSnapshots(maxSnapshots) {
    console.log(`🧹 清理旧快照 (保留 ${maxSnapshots} 个)...`);
    
    await this.resetManager.cleanupOldSnapshots(parseInt(maxSnapshots));
    
    console.log('✅ 快照清理完成！');
  }
}

// 执行命令行工具
if (require.main === module) {
  const cli = new TestDataCLI();
  cli.execute().catch(error => {
    console.error('❌ 执行失败:', error);
    process.exit(1);
  });
}

module.exports = { TestDataCLI };
