import { useEffect, useState } from 'react';
import { z } from 'zod';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { 
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage 
} from '@/components/ui/form';
import { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group';
import { Textarea } from '@/components/ui/textarea';
import QuestionItem from '../QuestionItem';
import AnswerStats from '../AnswerStats';
import { QuestionnaireData } from '@/lib/api';

// Define the form schema
const formSchema = z.object({
  unemploymentDuration: z.string().min(1, { message: '请选择失业时长' }),
  unemploymentReason: z.string().min(1, { message: '请填写失业原因' }).max(500, { message: '字数不能超过500' }),
  jobHuntingDifficulty: z.string().min(1, { message: '请选择求职难度' }),
});

// Mock statistics data
const mockStats = {
  unemploymentDuration: [
    { label: '3个月以内', percentage: 30 },
    { label: '3-6个月', percentage: 25 },
    { label: '6-12个月', percentage: 20 },
    { label: '1年以上', percentage: 15 },
    { label: '应届生尚未就业', percentage: 10 },
  ],
  jobHuntingDifficulty: [
    { label: '非常容易', percentage: 5 },
    { label: '比较容易', percentage: 15 },
    { label: '一般', percentage: 30 },
    { label: '比较困难', percentage: 35 },
    { label: '非常困难', percentage: 15 },
  ],
  unemploymentReasons: [
    { label: '行业不景气', percentage: 25 },
    { label: '专业不对口', percentage: 20 },
    { label: '薪资不满意', percentage: 15 },
    { label: '竞争激烈', percentage: 25 },
    { label: '其他', percentage: 15 },
  ],
};

interface UnemploymentStatusModuleProps {
  data: Partial<QuestionnaireData>;
  onUpdate: (data: Partial<QuestionnaireData>) => void;
}

export default function UnemploymentStatusModule({ 
  data, 
  onUpdate 
}: UnemploymentStatusModuleProps) {
  // Set up form with default values from data
  const form = useForm<z.infer<typeof formSchema>>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      unemploymentDuration: data.unemploymentDuration || '',
      unemploymentReason: data.unemploymentReason || '',
      jobHuntingDifficulty: data.jobHuntingDifficulty?.toString() || '',
    },
  });
  
  // Track selected options for stats display
  const [selectedOptions, setSelectedOptions] = useState({
    unemploymentDuration: data.unemploymentDuration || '',
    jobHuntingDifficulty: data.jobHuntingDifficulty?.toString() || '',
  });
  
  // Update stats when form values change
  useEffect(() => {
    const subscription = form.watch((value) => {
      setSelectedOptions({
        unemploymentDuration: value.unemploymentDuration || '',
        jobHuntingDifficulty: value.jobHuntingDifficulty || '',
      });
      
      // Update parent component with form values
      if (value.unemploymentDuration && value.unemploymentReason && value.jobHuntingDifficulty) {
        onUpdate({
          unemploymentDuration: value.unemploymentDuration,
          unemploymentReason: value.unemploymentReason,
          jobHuntingDifficulty: parseInt(value.jobHuntingDifficulty),
        });
      }
    });
    
    return () => subscription.unsubscribe();
  }, [form, onUpdate]);
  
  // Prepare stats data with selected indicators
  const durationStats = mockStats.unemploymentDuration.map(stat => ({
    ...stat,
    isSelected: stat.label === selectedOptions.unemploymentDuration,
  }));
  
  const difficultyStats = mockStats.jobHuntingDifficulty.map(stat => ({
    ...stat,
    isSelected: stat.label === selectedOptions.jobHuntingDifficulty,
  }));
  
  return (
    <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
      <div className="md:col-span-2">
        <Form {...form}>
          <form className="space-y-6">
            <QuestionItem
              number={1}
              title="您目前的失业时长是?"
              required
            >
              <FormField
                control={form.control}
                name="unemploymentDuration"
                render={({ field }) => (
                  <FormItem>
                    <FormControl>
                      <RadioGroup
                        onValueChange={field.onChange}
                        defaultValue={field.value}
                        className="flex flex-col space-y-2"
                      >
                        <FormItem className="flex items-center space-x-3 space-y-0">
                          <FormControl>
                            <RadioGroupItem value="3个月以内" />
                          </FormControl>
                          <FormLabel className="font-normal">3个月以内</FormLabel>
                        </FormItem>
                        <FormItem className="flex items-center space-x-3 space-y-0">
                          <FormControl>
                            <RadioGroupItem value="3-6个月" />
                          </FormControl>
                          <FormLabel className="font-normal">3-6个月</FormLabel>
                        </FormItem>
                        <FormItem className="flex items-center space-x-3 space-y-0">
                          <FormControl>
                            <RadioGroupItem value="6-12个月" />
                          </FormControl>
                          <FormLabel className="font-normal">6-12个月</FormLabel>
                        </FormItem>
                        <FormItem className="flex items-center space-x-3 space-y-0">
                          <FormControl>
                            <RadioGroupItem value="1年以上" />
                          </FormControl>
                          <FormLabel className="font-normal">1年以上</FormLabel>
                        </FormItem>
                        <FormItem className="flex items-center space-x-3 space-y-0">
                          <FormControl>
                            <RadioGroupItem value="应届生尚未就业" />
                          </FormControl>
                          <FormLabel className="font-normal">应届生尚未就业</FormLabel>
                        </FormItem>
                      </RadioGroup>
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </QuestionItem>
            
            <QuestionItem
              number={2}
              title="您认为目前求职的主要困难是什么?"
              required
            >
              <FormField
                control={form.control}
                name="unemploymentReason"
                render={({ field }) => (
                  <FormItem>
                    <FormControl>
                      <Textarea
                        placeholder="请描述您在求职过程中遇到的主要困难..."
                        className="resize-none"
                        {...field}
                      />
                    </FormControl>
                    <FormMessage />
                    <div className="text-xs text-gray-500 mt-1">
                      {field.value?.length || 0}/500
                    </div>
                  </FormItem>
                )}
              />
            </QuestionItem>
            
            <QuestionItem
              number={3}
              title="您认为当前求职的难度是? (1-5分)"
              required
            >
              <FormField
                control={form.control}
                name="jobHuntingDifficulty"
                render={({ field }) => (
                  <FormItem>
                    <FormControl>
                      <RadioGroup
                        onValueChange={field.onChange}
                        defaultValue={field.value}
                        className="flex space-x-4"
                      >
                        <FormItem className="flex flex-col items-center space-y-1">
                          <FormControl>
                            <RadioGroupItem value="1" />
                          </FormControl>
                          <FormLabel className="font-normal">1分<br/>非常容易</FormLabel>
                        </FormItem>
                        <FormItem className="flex flex-col items-center space-y-1">
                          <FormControl>
                            <RadioGroupItem value="2" />
                          </FormControl>
                          <FormLabel className="font-normal">2分<br/>比较容易</FormLabel>
                        </FormItem>
                        <FormItem className="flex flex-col items-center space-y-1">
                          <FormControl>
                            <RadioGroupItem value="3" />
                          </FormControl>
                          <FormLabel className="font-normal">3分<br/>一般</FormLabel>
                        </FormItem>
                        <FormItem className="flex flex-col items-center space-y-1">
                          <FormControl>
                            <RadioGroupItem value="4" />
                          </FormControl>
                          <FormLabel className="font-normal">4分<br/>比较困难</FormLabel>
                        </FormItem>
                        <FormItem className="flex flex-col items-center space-y-1">
                          <FormControl>
                            <RadioGroupItem value="5" />
                          </FormControl>
                          <FormLabel className="font-normal">5分<br/>非常困难</FormLabel>
                        </FormItem>
                      </RadioGroup>
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </QuestionItem>
          </form>
        </Form>
      </div>
      
      <div className="space-y-6">
        <AnswerStats 
          title="失业时长分布" 
          options={durationStats} 
        />
        
        <AnswerStats 
          title="求职难度分布" 
          options={difficultyStats} 
        />
        
        <AnswerStats 
          title="常见失业原因" 
          options={mockStats.unemploymentReasons} 
        />
      </div>
    </div>
  );
}
