import React, { useEffect } from 'react';
import { useNavigate, useLocation } from 'react-router-dom';
import { useAuth } from '@/contexts/AuthContext';

/**
 * 角色重定向组件
 *
 * 根据用户角色和当前路径，将用户重定向到正确的仪表盘
 */
const RoleRedirector: React.FC = () => {
  const { user, isAuthenticated } = useAuth();
  const navigate = useNavigate();
  const location = useLocation();

  useEffect(() => {
    // 检查是否需要重定向
    const shouldRedirect = sessionStorage.getItem('redirectToRoleDashboard');

    console.log('RoleRedirector - 检查状态:', {
      isAuthenticated,
      userRole: user?.role,
      currentPath: location.pathname,
      shouldRedirect
    });

    // 如果用户未认证，且访问的是需要认证的页面，重定向到登录页面
    if (!isAuthenticated || !user) {
      const protectedPaths = ['/admin/', '/reviewer/', '/superadmin/'];
      const isProtectedPath = protectedPaths.some(path => location.pathname.startsWith(path));

      if (isProtectedPath && location.pathname !== '/admin/login') {
        // 在清除登录信息之前，先检查 localStorage 中是否真的没有有效的登录信息
        const token = localStorage.getItem('adminToken');
        const userJson = localStorage.getItem('adminUser');

        if (token && userJson) {
          try {
            const userData = JSON.parse(userJson);
            if (userData && userData.role) {
              console.log('RoleRedirector - localStorage中有有效登录信息，等待useAuth更新状态');
              // 有有效的登录信息，等待 useAuth hook 更新状态
              return;
            }
          } catch (e) {
            console.error('RoleRedirector - 解析用户数据失败:', e);
          }
        }

        console.log('RoleRedirector - 用户未认证，访问受保护页面，重定向到登录页面');
        // 清除可能存在的无效登录信息
        localStorage.removeItem('adminToken');
        localStorage.removeItem('adminUser');
        sessionStorage.removeItem('redirectToRoleDashboard');
        navigate('/admin/login', { replace: true });
        return;
      }
      return;
    }

    // 用户已认证的情况
    console.log('RoleRedirector - 用户已认证，角色:', user.role);
    console.log('RoleRedirector - 当前路径:', location.pathname);
    console.log('RoleRedirector - 重定向标志:', shouldRedirect);
    console.log('RoleRedirector - 登录时间:', user.loginTime);

    // 获取当前时间和登录时间的差值（毫秒）
    const loginTime = user.loginTime ? new Date(user.loginTime).getTime() : 0;
    const currentTime = new Date().getTime();
    const timeDiff = currentTime - loginTime;

    // 如果登录时间超过24小时，清除登录信息并重定向到登录页面
    if (loginTime && timeDiff > 24 * 60 * 60 * 1000) {
      console.log('RoleRedirector - 登录已过期，重定向到登录页面');
      localStorage.removeItem('adminToken');
      localStorage.removeItem('adminUser');
      sessionStorage.removeItem('redirectToRoleDashboard');
      navigate('/admin/login', { replace: true });
      return;
    }

    // 如果在登录页面但已经登录，重定向到对应的仪表盘
    if (location.pathname === '/admin/login') {
      console.log('RoleRedirector - 用户已登录但在登录页面，重定向到对应仪表盘');

      // 根据用户角色重定向到相应的仪表盘
      if (user.role === 'superadmin') {
        console.log('RoleRedirector - 重定向到超级管理员仪表盘');
        navigate('/superadmin/dashboard', { replace: true });
        return;
      } else if (user.role === 'admin') {
        console.log('RoleRedirector - 重定向到管理员仪表盘');
        navigate('/admin/dashboard', { replace: true });
        return;
      } else if (user.role === 'reviewer') {
        console.log('RoleRedirector - 重定向到审核员仪表盘');
        navigate('/reviewer/dashboard', { replace: true });
        return;
      }
    }

    // 如果有重定向标志或者在管理入口路径，执行重定向
    if (shouldRedirect || location.pathname === '/admin') {
      console.log('RoleRedirector - 需要重定向，用户角色:', user.role);

      // 清除重定向标志
      sessionStorage.removeItem('redirectToRoleDashboard');

      // 根据用户角色重定向到相应的仪表盘
      if (user.role === 'superadmin') {
        console.log('RoleRedirector - 重定向到超级管理员仪表盘');
        navigate('/superadmin/dashboard', { replace: true });
      } else if (user.role === 'admin') {
        console.log('RoleRedirector - 重定向到管理员仪表盘');
        navigate('/admin/dashboard', { replace: true });
      } else if (user.role === 'reviewer') {
        console.log('RoleRedirector - 重定向到审核员仪表盘');
        navigate('/reviewer/dashboard', { replace: true });
      }
      return;
    }

    // 检查用户是否在错误的路径上
    // 例如，审核员访问管理员或超级管理员页面
    if (user.role === 'reviewer' &&
        (location.pathname.startsWith('/admin/') || location.pathname.startsWith('/superadmin/')) &&
        location.pathname !== '/admin/login') {
      console.log('RoleRedirector - 审核员访问管理员页面，重定向到审核员仪表盘');
      navigate('/reviewer/dashboard', { replace: true });
      return;
    }

    // 管理员访问超级管理员页面
    if (user.role === 'admin' &&
        location.pathname.startsWith('/superadmin/') &&
        location.pathname !== '/admin/login') {
      console.log('RoleRedirector - 管理员访问超级管理员页面，重定向到管理员仪表盘');
      navigate('/admin/dashboard', { replace: true });
      return;
    }

    // 超级管理员访问审核员页面
    if (user.role === 'superadmin' &&
        location.pathname.startsWith('/reviewer/') &&
        location.pathname !== '/admin/login') {
      console.log('RoleRedirector - 超级管理员访问审核员页面，重定向到超级管理员仪表盘');
      navigate('/superadmin/dashboard', { replace: true });
      return;
    }
  }, [isAuthenticated, user, navigate, location.pathname]);

  // 这个组件不渲染任何内容
  return null;
};

export default RoleRedirector;
