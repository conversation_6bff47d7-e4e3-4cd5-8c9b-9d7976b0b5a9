/**
 * UUID服务
 * 
 * 提供UUID生成和验证功能
 */

import { fetchData } from './unifiedDataService';

/**
 * 生成UUID
 * @param identityA 身份A
 * @param identityB 身份B
 * @returns 生成结果
 */
export async function generateUUID(identityA: string, identityB: string) {
  return fetchData(
    '/uuid/generate',
    'generateUUIDMock',
    { identityA, identityB },
    { method: 'POST' }
  );
}

/**
 * 验证UUID
 * @param identityA 身份A
 * @param identityB 身份B
 * @param uuid UUID（可选）
 * @returns 验证结果
 */
export async function verifyUUID(identityA: string, identityB: string, uuid?: string) {
  return fetchData(
    '/uuid/verify',
    'verifyUUIDMock',
    { identityA, identityB, uuid },
    { method: 'POST' }
  );
}

/**
 * 获取UUID信息
 * @param uuid UUID
 * @returns UUID信息
 */
export async function getUUIDInfo(uuid: string) {
  return fetchData(
    `/uuid/${uuid}`,
    'getUUIDInfoMock',
    { uuid },
    { method: 'GET' }
  );
}
