import { Context, Next } from 'hono';
import { verifyJWT } from '../utils/jwt';
import { Env } from '../types';

/**
 * 通用认证中间件
 * 验证请求头中的JWT令牌，不检查角色
 */
export const authMiddleware = async (c: Context<{ Bindings: Env }>, next: Next) => {
  // 开发环境下的简化认证
  if (c.env.ENVIRONMENT === 'development') {
    console.warn('Development mode: Bypassing authentication');

    // 在开发环境中，我们可以模拟一个用户
    c.set('user', { username: 'user', role: 'user' });

    await next();
    return;
  }

  // 获取Authorization头
  const authHeader = c.req.header('Authorization');

  if (!authHeader || !authHeader.startsWith('Bearer ')) {
    return c.json({ success: false, error: 'Unauthorized' }, 401);
  }

  // 提取token
  const token = authHeader.substring(7);

  try {
    // 验证token
    const payload = await verifyJWT(token, c.env.JWT_SECRET);

    if (!payload || !payload.role) {
      return c.json({ success: false, error: 'Unauthorized' }, 401);
    }

    // 将用户信息添加到请求上下文
    c.set('user', payload);

    await next();
  } catch (error) {
    console.error('JWT验证错误:', error);

    // 在开发环境中，即使出错也允许访问
    if (c.env.ENVIRONMENT === 'development') {
      console.warn('Development mode: JWT verification failed, but allowing access');
      c.set('user', { username: 'user', role: 'user' });
      await next();
      return;
    }

    return c.json({ success: false, error: 'Unauthorized' }, 401);
  }
};

/**
 * 管理员认证中间件
 * 验证请求头中的JWT令牌，确保用户具有管理员或超级管理员权限
 */
export const adminAuthMiddleware = async (c: Context<{ Bindings: Env }>, next: Next) => {
  // 开发环境下的简化认证
  if (c.env.ENVIRONMENT === 'development') {
    console.warn('Development mode: Bypassing admin authentication');

    // 在开发环境中，我们可以模拟一个管理员用户
    c.set('user', { username: 'admin', role: 'admin' });

    await next();
    return;
  }

  // 获取Authorization头
  const authHeader = c.req.header('Authorization');

  if (!authHeader || !authHeader.startsWith('Bearer ')) {
    return c.json({ success: false, error: 'Unauthorized' }, 401);
  }

  // 提取token
  const token = authHeader.substring(7);

  try {
    // 验证token
    const payload = await verifyJWT(token, c.env.JWT_SECRET);

    // 检查用户角色是否为管理员或超级管理员
    if (!payload || (payload.role !== 'admin' && payload.role !== 'superadmin')) {
      return c.json({ success: false, error: 'Forbidden: Admin role required' }, 403);
    }

    // 将用户信息添加到请求上下文
    c.set('user', payload);

    await next();
  } catch (error) {
    console.error('JWT验证错误:', error);

    // 在开发环境中，即使出错也允许访问
    if (c.env.ENVIRONMENT === 'development') {
      console.warn('Development mode: JWT verification failed, but allowing access');
      c.set('user', { username: 'admin', role: 'admin' });
      await next();
      return;
    }

    return c.json({ success: false, error: 'Unauthorized' }, 401);
  }
};
