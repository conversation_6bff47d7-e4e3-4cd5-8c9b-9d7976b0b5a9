/**
 * 🚀 模块化主应用文件
 * 整合所有路由模块和中间件
 */

import { Hono } from 'hono';
import { logger } from 'hono/logger';
import { secureHeaders } from 'hono/secure-headers';

// 导入中间件
import { createEnvironmentCorsMiddleware } from './middleware/cors.js';
import { createRateLimitMiddleware } from './middleware/auth.js';

// 导入路由模块
import questionnaireRouter from './routes/questionnaire.js';
import storyRouter from './routes/story.js';
import adminRouter from './routes/admin.js';
import deidentificationRouter from './routes/deidentification.js';
import systemRouter from './routes/system.js';

// 导入工具函数
import { createSuccessResponse, createErrorResponse, createHealthResponse } from './utils/response.js';

// 创建主应用
const app = new Hono();

// 全局中间件
app.use('*', logger());

// 简化的CORS配置 - 直接支持所有college-employment-survey.pages.dev域名
app.use('*', (c, next) => {
  const origin = c.req.header('Origin');
  console.log('CORS Middleware - Origin:', origin, 'Method:', c.req.method);

  // 设置CORS头
  if (origin) {
    // 检查是否是允许的域名
    const allowedDomains = [
      'https://college-employment-survey.pages.dev',
      'https://a1dcca34.college-employment-survey.pages.dev',
      'https://7136b127.college-employment-survey.pages.dev'
    ];

    const isAllowed = allowedDomains.includes(origin) ||
                     origin.match(/^https:\/\/[a-f0-9]{8}\.college-employment-survey\.pages\.dev$/);

    console.log('CORS Check - Origin allowed:', isAllowed);

    if (isAllowed) {
      c.header('Access-Control-Allow-Origin', origin);
      c.header('Access-Control-Allow-Credentials', 'true');
      c.header('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS, PATCH');
      c.header('Access-Control-Allow-Headers', 'Content-Type, Authorization, X-Requested-With, Accept, Origin');
      c.header('Access-Control-Max-Age', '86400');
      console.log('CORS Headers set for origin:', origin);
    }
  }

  // 处理OPTIONS预检请求
  if (c.req.method === 'OPTIONS') {
    console.log('Handling OPTIONS request');
    return c.text('', 200);
  }

  return next();
});

// 全局速率限制（可选）
app.use('/api/*', createRateLimitMiddleware({
  windowMs: 15 * 60 * 1000, // 15分钟
  maxRequests: 1000, // 每个IP每15分钟最多1000次请求
  message: 'Too many requests, please try again later'
}));

// 根路径 - API信息
app.get('/', (c) => {
  const info = {
    name: 'College Employment Survey API',
    version: 'v3.0-modular',
    status: 'running',
    environment: c.env?.ENVIRONMENT || 'production',
    timestamp: new Date().toISOString(),
    architecture: 'modular',
    modules: [
      'questionnaire',
      'story',
      'admin',
      'deidentification',
      'system'
    ],
    endpoints: {
      // 问卷模块
      questionnaireStats: '/api/questionnaire/stats',
      questionnaireVoices: '/api/questionnaire/voices',
      visualizationData: '/api/questionnaire/visualization/data',

      // 故事模块
      storyList: '/api/story/list',
      storyDetail: '/api/story/detail/:id',
      storyVote: '/api/story/vote',

      // 管理员模块
      adminDashboard: '/api/admin/dashboard/stats',
      adminReview: '/api/admin/review/pending',

      // 脱敏模块
      deidentificationConfig: '/api/admin/deidentification/config',
      deidentificationProcess: '/api/admin/deidentification/process',

      // 系统模块
      systemHealth: '/api/system/health',
      systemInfo: '/api/system/info'
    },
    documentation: {
      swagger: '/api/docs',
      postman: '/api/postman-collection'
    }
  };

  return c.json(createSuccessResponse(info, 'API information retrieved successfully'));
});

// 健康检查端点
app.get('/health', (c) => {
  const checks = {
    database: 'healthy',
    memory: 'healthy',
    modules: 'loaded'
  };

  return c.json(createHealthResponse('ok', checks, 'v3.0-modular'));
});

// 注册路由模块
app.route('/api/questionnaire', questionnaireRouter);
app.route('/api/story', storyRouter);
app.route('/api/admin', adminRouter);
app.route('/api/admin/deidentification', deidentificationRouter);
app.route('/api/system', systemRouter);

// 向后兼容的路由（保持旧API端点工作）
app.route('/api/visualization', questionnaireRouter); // 重定向到问卷模块

// API文档端点（占位符）
app.get('/api/docs', (c) => {
  const docs = {
    openapi: '3.0.0',
    info: {
      title: 'College Employment Survey API',
      version: 'v3.0-modular',
      description: 'Modular API for college employment survey system'
    },
    servers: [
      {
        url: 'https://college-employment-survey.aibook2099.workers.dev',
        description: 'Production server'
      }
    ],
    paths: {
      '/api/questionnaire/stats': {
        get: {
          summary: 'Get questionnaire statistics',
          tags: ['Questionnaire']
        }
      },
      '/api/story/list': {
        get: {
          summary: 'Get story list',
          tags: ['Story']
        }
      },
      '/api/admin/dashboard/stats': {
        get: {
          summary: 'Get admin dashboard statistics',
          tags: ['Admin']
        }
      }
    }
  };

  return c.json(createSuccessResponse(docs, 'API documentation retrieved successfully'));
});

// Postman集合端点（占位符）
app.get('/api/postman-collection', (c) => {
  const collection = {
    info: {
      name: 'College Employment Survey API',
      description: 'Postman collection for the modular API',
      schema: 'https://schema.getpostman.com/json/collection/v2.1.0/collection.json'
    },
    item: [
      {
        name: 'Questionnaire',
        item: [
          {
            name: 'Get Statistics',
            request: {
              method: 'GET',
              header: [],
              url: {
                raw: '{{baseUrl}}/api/questionnaire/stats',
                host: ['{{baseUrl}}'],
                path: ['api', 'questionnaire', 'stats']
              }
            }
          }
        ]
      }
    ],
    variable: [
      {
        key: 'baseUrl',
        value: 'https://college-employment-survey.aibook2099.workers.dev'
      }
    ]
  };

  return c.json(createSuccessResponse(collection, 'Postman collection retrieved successfully'));
});

// 404处理器
app.notFound((c) => {
  return c.json(createErrorResponse('Endpoint not found', 404, {
    path: c.req.path,
    method: c.req.method,
    availableEndpoints: [
      '/api/questionnaire/stats',
      '/api/story/list',
      '/api/admin/dashboard/stats',
      '/api/system/health'
    ]
  }), 404);
});

// 全局错误处理器
app.onError((err, c) => {
  console.error(`${c.req.method} ${c.req.url}`, err);

  // 根据错误类型返回不同的响应
  if (err.name === 'ValidationError') {
    return c.json(createErrorResponse('Validation failed', 400, {
      details: err.message
    }), 400);
  }

  if (err.name === 'DatabaseError') {
    return c.json(createErrorResponse('Database error', 500, {
      details: process.env.NODE_ENV === 'development' ? err.message : 'Internal database error'
    }), 500);
  }

  // 默认错误响应
  return c.json(createErrorResponse(
    process.env.NODE_ENV === 'development' ? err.message : 'Internal server error',
    500
  ), 500);
});

// 导出应用
export default app;
