import React, { useState, useRef } from 'react';
import SuperAdminLayout from '@/components/layouts/SuperAdminLayout';
import { Card, CardContent, CardHeader, CardTitle, CardDescription, CardFooter } from '@/components/ui/card';
import { But<PERSON> } from '@/components/ui/button';
import { Ta<PERSON>, <PERSON>bs<PERSON>ontent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Switch } from '@/components/ui/switch';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { useToast } from '@/components/ui/use-toast';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Checkbox } from '@/components/ui/checkbox';
import { Textarea } from '@/components/ui/textarea';
import {
  Upload, Download, FileText, Users, UserPlus as UserPlusIcon, UserCheck,
  UserX, RefreshCw, CheckCircle, AlertCircle, X, Check,
  FileDown, FilePlus, FileUp, Trash2, Eye, EyeOff
} from 'lucide-react';
import {
  Table, TableBody, TableCaption, TableCell, TableHead,
  TableHeader, TableRow
} from '@/components/ui/table';
import {
  Dialog, DialogContent, DialogDescription, DialogFooter,
  DialogHeader, DialogTitle, DialogTrigger
} from '@/components/ui/dialog';

// 用户类型
interface User {
  id: string;
  username: string;
  email: string;
  role: 'reviewer' | 'admin' | 'superadmin';
  status: 'active' | 'inactive' | 'pending';
  createdAt: string;
  lastLogin?: string;
}

// 导入结果类型
interface ImportResult {
  success: number;
  failed: number;
  errors: Array<{
    line: number;
    message: string;
    data: string;
  }>;
  users: User[];
}

/**
 * 用户批量导入/导出页面
 *
 * 提供用户批量导入和导出功能
 */
const UserBatchPage: React.FC = () => {
  const { toast } = useToast();
  const [isImporting, setIsImporting] = useState(false);
  const [isExporting, setIsExporting] = useState(false);
  const [importProgress, setImportProgress] = useState(0);
  const [importResult, setImportResult] = useState<ImportResult | null>(null);
  const [importFormat, setImportFormat] = useState('csv');
  const [exportFormat, setExportFormat] = useState('csv');
  const [includeInactiveUsers, setIncludeInactiveUsers] = useState(false);
  const [includeLoginHistory, setIncludeLoginHistory] = useState(false);
  const [csvData, setCsvData] = useState('');
  const [previewUsers, setPreviewUsers] = useState<User[]>([]);
  const [isPreviewDialogOpen, setIsPreviewDialogOpen] = useState(false);
  const fileInputRef = useRef<HTMLInputElement>(null);

  // 模拟用户数据
  const mockUsers: User[] = [
    {
      id: '1',
      username: 'reviewer1',
      email: '<EMAIL>',
      role: 'reviewer',
      status: 'active',
      createdAt: '2023-05-15 10:30:00',
      lastLogin: '2023-06-18 14:25:10'
    },
    {
      id: '2',
      username: 'admin1',
      email: '<EMAIL>',
      role: 'admin',
      status: 'active',
      createdAt: '2023-04-20 09:15:00',
      lastLogin: '2023-06-19 11:05:22'
    },
    {
      id: '3',
      username: 'superadmin',
      email: '<EMAIL>',
      role: 'superadmin',
      status: 'active',
      createdAt: '2023-01-10 08:00:00',
      lastLogin: '2023-06-19 16:30:45'
    },
    {
      id: '4',
      username: 'reviewer2',
      email: '<EMAIL>',
      role: 'reviewer',
      status: 'inactive',
      createdAt: '2023-05-25 11:20:00',
      lastLogin: '2023-06-10 09:15:30'
    },
    {
      id: '5',
      username: 'reviewer3',
      email: '<EMAIL>',
      role: 'reviewer',
      status: 'pending',
      createdAt: '2023-06-05 14:45:00'
    }
  ];

  // 处理文件上传
  const handleFileUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (!file) return;

    const reader = new FileReader();
    reader.onload = (e) => {
      const content = e.target?.result as string;
      setCsvData(content);

      // 解析 CSV 数据预览
      const lines = content.split('\n');
      const headers = lines[0].split(',');

      const users: User[] = [];
      for (let i = 1; i < Math.min(lines.length, 6); i++) {
        if (!lines[i].trim()) continue;

        const values = lines[i].split(',');
        users.push({
          id: `preview-${i}`,
          username: values[0] || '',
          email: values[1] || '',
          role: (values[2] as any) || 'reviewer',
          status: (values[3] as any) || 'pending',
          createdAt: new Date().toISOString().replace('T', ' ').substring(0, 19)
        });
      }

      setPreviewUsers(users);
      setIsPreviewDialogOpen(true);
    };

    reader.readAsText(file);
  };

  // 开始导入
  const startImport = async () => {
    try {
      setIsImporting(true);
      setImportProgress(0);

      // 模拟导入进度
      const interval = setInterval(() => {
        setImportProgress(prev => {
          if (prev >= 100) {
            clearInterval(interval);
            return 100;
          }
          return prev + 5;
        });
      }, 200);

      // 模拟导入完成
      setTimeout(() => {
        clearInterval(interval);
        setImportProgress(100);

        // 模拟导入结果
        const result: ImportResult = {
          success: 8,
          failed: 2,
          errors: [
            {
              line: 3,
              message: '邮箱格式无效',
              data: 'reviewer3,reviewer3@invalid,reviewer,active'
            },
            {
              line: 7,
              message: '角色无效，必须是 reviewer、admin 或 superadmin',
              data: 'reviewer7,<EMAIL>,invalid_role,active'
            }
          ],
          users: mockUsers.slice(0, 3)
        };

        setImportResult(result);

        setTimeout(() => {
          setIsImporting(false);
          toast({
            title: '导入完成',
            description: `成功导入 ${result.success} 个用户，失败 ${result.failed} 个用户`
          });
        }, 1000);
      }, 4000);
    } catch (error) {
      console.error('导入失败:', error);
      setIsImporting(false);
      toast({
        variant: 'destructive',
        title: '导入失败',
        description: '无法完成用户导入，请稍后再试'
      });
    }
  };

  // 开始导出
  const startExport = async () => {
    try {
      setIsExporting(true);

      // 模拟导出过程
      await new Promise(resolve => setTimeout(resolve, 2000));

      // 生成 CSV 数据
      let csvContent = 'username,email,role,status,createdAt,lastLogin\n';

      const usersToExport = includeInactiveUsers
        ? mockUsers
        : mockUsers.filter(user => user.status === 'active');

      usersToExport.forEach(user => {
        csvContent += `${user.username},${user.email},${user.role},${user.status},${user.createdAt}`;
        if (includeLoginHistory && user.lastLogin) {
          csvContent += `,${user.lastLogin}`;
        } else {
          csvContent += ',';
        }
        csvContent += '\n';
      });

      // 创建下载链接
      const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
      const url = URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.href = url;
      link.setAttribute('download', `users_export_${new Date().toISOString().split('T')[0]}.csv`);
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);

      setIsExporting(false);
      toast({
        title: '导出成功',
        description: `已导出 ${usersToExport.length} 个用户数据`
      });
    } catch (error) {
      console.error('导出失败:', error);
      setIsExporting(false);
      toast({
        variant: 'destructive',
        title: '导出失败',
        description: '无法完成用户导出，请稍后再试'
      });
    }
  };

  // 获取用户角色标签
  const getRoleBadge = (role: string) => {
    switch (role) {
      case 'reviewer':
        return <Badge className="bg-blue-100 text-blue-800">审核员</Badge>;
      case 'admin':
        return <Badge className="bg-purple-100 text-purple-800">管理员</Badge>;
      case 'superadmin':
        return <Badge className="bg-red-100 text-red-800">超级管理员</Badge>;
      default:
        return <Badge className="bg-gray-100 text-gray-800">{role}</Badge>;
    }
  };

  // 获取用户状态标签
  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'active':
        return <Badge className="bg-green-100 text-green-800">活跃</Badge>;
      case 'inactive':
        return <Badge className="bg-yellow-100 text-yellow-800">未激活</Badge>;
      case 'pending':
        return <Badge className="bg-orange-100 text-orange-800">待审核</Badge>;
      default:
        return <Badge className="bg-gray-100 text-gray-800">{status}</Badge>;
    }
  };

  // 获取 CSV 模板
  const getCSVTemplate = () => {
    return 'username,email,role,status\nreviewer1,<EMAIL>,reviewer,active\nadmin1,<EMAIL>,admin,active';
  };

  return (
    <SuperAdminLayout>
      <div className="container mx-auto py-6">
        <div className="flex justify-between items-center mb-6">
          <h1 className="text-2xl font-bold">用户批量管理</h1>
        </div>

        <Tabs defaultValue="import" className="space-y-6">
          <TabsList>
            <TabsTrigger value="import">批量导入</TabsTrigger>
            <TabsTrigger value="export">批量导出</TabsTrigger>
            <TabsTrigger value="template">导入模板</TabsTrigger>
          </TabsList>

          {/* 批量导入 */}
          <TabsContent value="import" className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle>上传用户数据</CardTitle>
                <CardDescription>
                  上传包含用户数据的文件进行批量导入
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div className="space-y-2">
                    <Label htmlFor="import-format">文件格式</Label>
                    <Select
                      value={importFormat}
                      onValueChange={setImportFormat}
                    >
                      <SelectTrigger id="import-format">
                        <SelectValue placeholder="选择文件格式" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="csv">CSV 文件</SelectItem>
                        <SelectItem value="excel">Excel 文件</SelectItem>
                        <SelectItem value="json">JSON 文件</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>

                  <div className="space-y-2">
                    <Label>上传文件</Label>
                    <div className="border-2 border-dashed rounded-md p-6 flex flex-col items-center justify-center">
                      <Upload className="h-8 w-8 text-muted-foreground mb-2" />
                      <p className="text-sm text-muted-foreground mb-1">
                        拖放文件到此处，或点击上传
                      </p>
                      <p className="text-xs text-muted-foreground mb-4">
                        支持 {importFormat.toUpperCase()} 格式，最大文件大小 5MB
                      </p>
                      <input
                        ref={fileInputRef}
                        type="file"
                        accept={
                          importFormat === 'csv' ? '.csv' :
                          importFormat === 'excel' ? '.xlsx,.xls' : '.json'
                        }
                        className="hidden"
                        onChange={handleFileUpload}
                      />
                      <Button
                        variant="outline"
                        onClick={() => fileInputRef.current?.click()}
                      >
                        <FileUp className="mr-2 h-4 w-4" />
                        选择文件
                      </Button>
                    </div>
                  </div>

                  <div className="space-y-2">
                    <Label>导入选项</Label>
                    <div className="space-y-2">
                      <div className="flex items-center space-x-2">
                        <Checkbox id="update-existing" />
                        <label
                          htmlFor="update-existing"
                          className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
                        >
                          更新已存在的用户
                        </label>
                      </div>
                      <div className="flex items-center space-x-2">
                        <Checkbox id="send-welcome" />
                        <label
                          htmlFor="send-welcome"
                          className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
                        >
                          发送欢迎邮件给新用户
                        </label>
                      </div>
                      <div className="flex items-center space-x-2">
                        <Checkbox id="skip-validation" />
                        <label
                          htmlFor="skip-validation"
                          className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
                        >
                          跳过邮箱验证
                        </label>
                      </div>
                    </div>
                  </div>
                </div>
              </CardContent>
              <CardFooter>
                <Button
                  className="w-full"
                  onClick={() => fileInputRef.current?.click()}
                  disabled={isImporting}
                >
                  <Upload className="mr-2 h-4 w-4" />
                  上传并预览
                </Button>
              </CardFooter>
            </Card>

            {isImporting && (
              <Card>
                <CardHeader>
                  <CardTitle>导入进行中</CardTitle>
                  <CardDescription>
                    正在导入用户数据，请稍候
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    <div>
                      <div className="flex justify-between mb-1">
                        <span className="text-sm font-medium">导入进度</span>
                        <span className="text-sm text-muted-foreground">{importProgress}%</span>
                      </div>
                      <Progress value={importProgress} className="h-2" />
                    </div>

                    <div className="flex items-center text-sm text-muted-foreground">
                      <RefreshCw className="h-4 w-4 mr-1 animate-spin" />
                      <span>正在导入用户数据，请稍候...</span>
                    </div>
                  </div>
                </CardContent>
              </Card>
            )}

            {importResult && (
              <Card>
                <CardHeader>
                  <CardTitle>导入结果</CardTitle>
                  <CardDescription>
                    用户数据导入结果
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                      <div className="p-4 border rounded-md bg-green-50">
                        <div className="flex items-center">
                          <CheckCircle className="h-5 w-5 text-green-500 mr-2" />
                          <span className="font-medium">成功导入</span>
                        </div>
                        <div className="text-2xl font-bold mt-2">{importResult.success}</div>
                      </div>

                      <div className="p-4 border rounded-md bg-red-50">
                        <div className="flex items-center">
                          <AlertCircle className="h-5 w-5 text-red-500 mr-2" />
                          <span className="font-medium">导入失败</span>
                        </div>
                        <div className="text-2xl font-bold mt-2">{importResult.failed}</div>
                      </div>

                      <div className="p-4 border rounded-md bg-blue-50">
                        <div className="flex items-center">
                          <Users className="h-5 w-5 text-blue-500 mr-2" />
                          <span className="font-medium">总用户数</span>
                        </div>
                        <div className="text-2xl font-bold mt-2">{importResult.success + importResult.failed}</div>
                      </div>
                    </div>

                    {importResult.errors.length > 0 && (
                      <div className="space-y-2">
                        <h3 className="text-sm font-medium">错误详情</h3>
                        <div className="rounded-md border">
                          <Table>
                            <TableHeader>
                              <TableRow>
                                <TableHead>行号</TableHead>
                                <TableHead>错误信息</TableHead>
                                <TableHead>数据</TableHead>
                              </TableRow>
                            </TableHeader>
                            <TableBody>
                              {importResult.errors.map((error, index) => (
                                <TableRow key={index}>
                                  <TableCell>{error.line}</TableCell>
                                  <TableCell className="text-red-500">{error.message}</TableCell>
                                  <TableCell>
                                    <code className="text-xs bg-muted p-1 rounded">{error.data}</code>
                                  </TableCell>
                                </TableRow>
                              ))}
                            </TableBody>
                          </Table>
                        </div>
                      </div>
                    )}

                    <div className="space-y-2">
                      <h3 className="text-sm font-medium">导入的用户</h3>
                      <div className="rounded-md border">
                        <Table>
                          <TableHeader>
                            <TableRow>
                              <TableHead>用户名</TableHead>
                              <TableHead>邮箱</TableHead>
                              <TableHead>角色</TableHead>
                              <TableHead>状态</TableHead>
                            </TableRow>
                          </TableHeader>
                          <TableBody>
                            {importResult.users.map((user) => (
                              <TableRow key={user.id}>
                                <TableCell>{user.username}</TableCell>
                                <TableCell>{user.email}</TableCell>
                                <TableCell>{getRoleBadge(user.role)}</TableCell>
                                <TableCell>{getStatusBadge(user.status)}</TableCell>
                              </TableRow>
                            ))}
                          </TableBody>
                        </Table>
                      </div>
                    </div>
                  </div>
                </CardContent>
                <CardFooter>
                  <Button
                    variant="outline"
                    className="w-full"
                    onClick={() => setImportResult(null)}
                  >
                    <X className="mr-2 h-4 w-4" />
                    关闭结果
                  </Button>
                </CardFooter>
              </Card>
            )}
          </TabsContent>

          {/* 批量导出 */}
          <TabsContent value="export" className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle>导出用户数据</CardTitle>
                <CardDescription>
                  导出系统中的用户数据
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div className="space-y-2">
                    <Label htmlFor="export-format">导出格式</Label>
                    <Select
                      value={exportFormat}
                      onValueChange={setExportFormat}
                    >
                      <SelectTrigger id="export-format">
                        <SelectValue placeholder="选择导出格式" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="csv">CSV 文件</SelectItem>
                        <SelectItem value="excel">Excel 文件</SelectItem>
                        <SelectItem value="json">JSON 文件</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>

                  <div className="space-y-2">
                    <Label>导出选项</Label>
                    <div className="space-y-2">
                      <div className="flex items-center justify-between">
                        <div className="flex items-center space-x-2">
                          <Checkbox
                            id="include-inactive"
                            checked={includeInactiveUsers}
                            onCheckedChange={(checked) => setIncludeInactiveUsers(!!checked)}
                          />
                          <label
                            htmlFor="include-inactive"
                            className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
                          >
                            包含未激活用户
                          </label>
                        </div>
                        <Badge className="bg-yellow-100 text-yellow-800">
                          {mockUsers.filter(u => u.status !== 'active').length} 个用户
                        </Badge>
                      </div>

                      <div className="flex items-center justify-between">
                        <div className="flex items-center space-x-2">
                          <Checkbox
                            id="include-login-history"
                            checked={includeLoginHistory}
                            onCheckedChange={(checked) => setIncludeLoginHistory(!!checked)}
                          />
                          <label
                            htmlFor="include-login-history"
                            className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
                          >
                            包含登录历史
                          </label>
                        </div>
                      </div>

                      <div className="flex items-center justify-between">
                        <div className="flex items-center space-x-2">
                          <Checkbox id="include-activity-data" />
                          <label
                            htmlFor="include-activity-data"
                            className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
                          >
                            包含活动数据
                          </label>
                        </div>
                      </div>
                    </div>
                  </div>

                  <div className="space-y-2">
                    <Label>用户筛选</Label>
                    <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                      <div>
                        <Label htmlFor="filter-role" className="text-xs">角色</Label>
                        <Select defaultValue="all">
                          <SelectTrigger id="filter-role">
                            <SelectValue placeholder="选择角色" />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="all">全部角色</SelectItem>
                            <SelectItem value="reviewer">审核员</SelectItem>
                            <SelectItem value="admin">管理员</SelectItem>
                            <SelectItem value="superadmin">超级管理员</SelectItem>
                          </SelectContent>
                        </Select>
                      </div>

                      <div>
                        <Label htmlFor="filter-status" className="text-xs">状态</Label>
                        <Select defaultValue="all">
                          <SelectTrigger id="filter-status">
                            <SelectValue placeholder="选择状态" />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="all">全部状态</SelectItem>
                            <SelectItem value="active">活跃</SelectItem>
                            <SelectItem value="inactive">未激活</SelectItem>
                            <SelectItem value="pending">待审核</SelectItem>
                          </SelectContent>
                        </Select>
                      </div>

                      <div>
                        <Label htmlFor="filter-date" className="text-xs">注册日期</Label>
                        <Select defaultValue="all">
                          <SelectTrigger id="filter-date">
                            <SelectValue placeholder="选择日期范围" />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="all">全部时间</SelectItem>
                            <SelectItem value="today">今天</SelectItem>
                            <SelectItem value="week">本周</SelectItem>
                            <SelectItem value="month">本月</SelectItem>
                            <SelectItem value="year">今年</SelectItem>
                          </SelectContent>
                        </Select>
                      </div>
                    </div>
                  </div>
                </div>
              </CardContent>
              <CardFooter>
                <Button
                  className="w-full"
                  onClick={startExport}
                  disabled={isExporting}
                >
                  {isExporting ? (
                    <>
                      <RefreshCw className="mr-2 h-4 w-4 animate-spin" />
                      导出中...
                    </>
                  ) : (
                    <>
                      <Download className="mr-2 h-4 w-4" />
                      导出用户数据
                    </>
                  )}
                </Button>
              </CardFooter>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>用户数据预览</CardTitle>
                <CardDescription>
                  将要导出的用户数据预览
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="rounded-md border">
                  <Table>
                    <TableHeader>
                      <TableRow>
                        <TableHead>用户名</TableHead>
                        <TableHead>邮箱</TableHead>
                        <TableHead>角色</TableHead>
                        <TableHead>状态</TableHead>
                        <TableHead>注册时间</TableHead>
                        {includeLoginHistory && <TableHead>最后登录</TableHead>}
                      </TableRow>
                    </TableHeader>
                    <TableBody>
                      {mockUsers
                        .filter(user => includeInactiveUsers || user.status === 'active')
                        .map((user) => (
                          <TableRow key={user.id}>
                            <TableCell>{user.username}</TableCell>
                            <TableCell>{user.email}</TableCell>
                            <TableCell>{getRoleBadge(user.role)}</TableCell>
                            <TableCell>{getStatusBadge(user.status)}</TableCell>
                            <TableCell>{user.createdAt}</TableCell>
                            {includeLoginHistory && (
                              <TableCell>{user.lastLogin || '-'}</TableCell>
                            )}
                          </TableRow>
                        ))}
                    </TableBody>
                  </Table>
                </div>

                <div className="mt-4 text-sm text-muted-foreground">
                  显示 {mockUsers.filter(user => includeInactiveUsers || user.status === 'active').length} 个用户（共 {mockUsers.length} 个）
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          {/* 导入模板 */}
          <TabsContent value="template" className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle>导入模板</CardTitle>
                <CardDescription>
                  下载用户导入模板，按照模板格式填写用户数据
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div className="p-4 border rounded-md">
                    <h3 className="text-sm font-medium mb-2">CSV 模板</h3>
                    <p className="text-sm text-muted-foreground mb-4">
                      CSV 格式的用户导入模板，可以使用 Excel 或文本编辑器打开编辑
                    </p>
                    <div className="bg-muted p-3 rounded-md mb-4 overflow-x-auto">
                      <pre className="text-xs">{getCSVTemplate()}</pre>
                    </div>
                    <Button variant="outline" size="sm">
                      <FileDown className="mr-2 h-4 w-4" />
                      下载 CSV 模板
                    </Button>
                  </div>

                  <div className="p-4 border rounded-md">
                    <h3 className="text-sm font-medium mb-2">Excel 模板</h3>
                    <p className="text-sm text-muted-foreground mb-4">
                      Excel 格式的用户导入模板，包含数据验证和格式说明
                    </p>
                    <Button variant="outline" size="sm">
                      <FileDown className="mr-2 h-4 w-4" />
                      下载 Excel 模板
                    </Button>
                  </div>

                  <div className="p-4 border rounded-md">
                    <h3 className="text-sm font-medium mb-2">JSON 模板</h3>
                    <p className="text-sm text-muted-foreground mb-4">
                      JSON 格式的用户导入模板，适合开发人员使用
                    </p>
                    <div className="bg-muted p-3 rounded-md mb-4 overflow-x-auto">
                      <pre className="text-xs">{`[
  {
    "username": "reviewer1",
    "email": "<EMAIL>",
    "role": "reviewer",
    "status": "active"
  },
  {
    "username": "admin1",
    "email": "<EMAIL>",
    "role": "admin",
    "status": "active"
  }
]`}</pre>
                    </div>
                    <Button variant="outline" size="sm">
                      <FileDown className="mr-2 h-4 w-4" />
                      下载 JSON 模板
                    </Button>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>字段说明</CardTitle>
                <CardDescription>
                  用户导入模板字段说明
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="rounded-md border">
                  <Table>
                    <TableHeader>
                      <TableRow>
                        <TableHead>字段名</TableHead>
                        <TableHead>说明</TableHead>
                        <TableHead>是否必填</TableHead>
                        <TableHead>有效值</TableHead>
                      </TableRow>
                    </TableHeader>
                    <TableBody>
                      <TableRow>
                        <TableCell className="font-medium">username</TableCell>
                        <TableCell>用户名</TableCell>
                        <TableCell>是</TableCell>
                        <TableCell>字母、数字、下划线，长度 3-20 个字符</TableCell>
                      </TableRow>
                      <TableRow>
                        <TableCell className="font-medium">email</TableCell>
                        <TableCell>邮箱</TableCell>
                        <TableCell>是</TableCell>
                        <TableCell>有效的邮箱地址</TableCell>
                      </TableRow>
                      <TableRow>
                        <TableCell className="font-medium">role</TableCell>
                        <TableCell>角色</TableCell>
                        <TableCell>是</TableCell>
                        <TableCell>reviewer, admin, superadmin</TableCell>
                      </TableRow>
                      <TableRow>
                        <TableCell className="font-medium">status</TableCell>
                        <TableCell>状态</TableCell>
                        <TableCell>否</TableCell>
                        <TableCell>active, inactive, pending（默认为 pending）</TableCell>
                      </TableRow>
                      <TableRow>
                        <TableCell className="font-medium">password</TableCell>
                        <TableCell>密码</TableCell>
                        <TableCell>否</TableCell>
                        <TableCell>如不提供，将生成随机密码</TableCell>
                      </TableRow>
                    </TableBody>
                  </Table>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>导入说明</CardTitle>
                <CardDescription>
                  用户批量导入注意事项
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div className="p-4 border rounded-md bg-blue-50">
                    <div className="flex items-start">
                      <FileText className="h-5 w-5 text-blue-500 mr-2 mt-0.5" />
                      <div>
                        <h4 className="font-medium text-blue-800">文件格式</h4>
                        <ul className="text-sm text-blue-700 space-y-1 list-disc pl-4 mt-1">
                          <li>CSV 文件应使用 UTF-8 编码</li>
                          <li>Excel 文件支持 .xlsx 和 .xls 格式</li>
                          <li>JSON 文件应为有效的 JSON 数组</li>
                        </ul>
                      </div>
                    </div>
                  </div>

                  <div className="p-4 border rounded-md bg-yellow-50">
                    <div className="flex items-start">
                      <AlertCircle className="h-5 w-5 text-yellow-500 mr-2 mt-0.5" />
                      <div>
                        <h4 className="font-medium text-yellow-800">导入限制</h4>
                        <ul className="text-sm text-yellow-700 space-y-1 list-disc pl-4 mt-1">
                          <li>单次导入最多支持 1000 个用户</li>
                          <li>用户名和邮箱不能重复</li>
                          <li>超级管理员角色需要特殊权限</li>
                        </ul>
                      </div>
                    </div>
                  </div>

                  <div className="p-4 border rounded-md bg-green-50">
                    <div className="flex items-start">
                      <CheckCircle className="h-5 w-5 text-green-500 mr-2 mt-0.5" />
                      <div>
                        <h4 className="font-medium text-green-800">最佳实践</h4>
                        <ul className="text-sm text-green-700 space-y-1 list-disc pl-4 mt-1">
                          <li>先下载模板，按照模板格式填写数据</li>
                          <li>导入前先检查数据格式是否正确</li>
                          <li>建议分批导入大量用户数据</li>
                          <li>导入完成后检查导入结果</li>
                        </ul>
                      </div>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>

        {/* 预览对话框 */}
        <Dialog open={isPreviewDialogOpen} onOpenChange={setIsPreviewDialogOpen}>
          <DialogContent className="max-w-4xl">
            <DialogHeader>
              <DialogTitle>导入预览</DialogTitle>
              <DialogDescription>
                以下是从文件中解析的用户数据预览
              </DialogDescription>
            </DialogHeader>

            <div className="py-4">
              <div className="rounded-md border">
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>用户名</TableHead>
                      <TableHead>邮箱</TableHead>
                      <TableHead>角色</TableHead>
                      <TableHead>状态</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {previewUsers.map((user) => (
                      <TableRow key={user.id}>
                        <TableCell>{user.username}</TableCell>
                        <TableCell>{user.email}</TableCell>
                        <TableCell>{getRoleBadge(user.role)}</TableCell>
                        <TableCell>{getStatusBadge(user.status)}</TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </div>

              <div className="mt-4 p-3 bg-yellow-50 border border-yellow-200 rounded-md">
                <div className="flex items-start">
                  <AlertCircle className="h-5 w-5 text-yellow-500 mr-2 mt-0.5" />
                  <div>
                    <h4 className="font-medium text-yellow-800">注意</h4>
                    <p className="text-sm text-yellow-700">
                      这只是数据预览，实际导入可能会有所不同。请确保数据格式正确，并且用户名和邮箱不重复。
                    </p>
                  </div>
                </div>
              </div>
            </div>

            <DialogFooter>
              <Button variant="outline" onClick={() => setIsPreviewDialogOpen(false)}>
                取消
              </Button>
              <Button
                variant="default"
                onClick={() => {
                  setIsPreviewDialogOpen(false);
                  startImport();
                }}
              >
                开始导入
              </Button>
            </DialogFooter>
          </DialogContent>
        </Dialog>
      </div>
    </SuperAdminLayout>
  );
};

export default UserBatchPage;
