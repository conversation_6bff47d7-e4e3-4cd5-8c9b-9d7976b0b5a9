import React, { useState, useEffect } from 'react';
import { useLocation, useNavigate } from 'react-router-dom';
import { Button } from '@/components/ui/button';
import { LogOut, User, Bell, ChevronLeft, Menu } from 'lucide-react';
import { useToast } from '@/components/ui/use-toast';
import { useAuth } from '@/contexts/AuthContext';
import SidebarMenu from '../common/SidebarMenu';
import { adminMenuGroups } from '@/config/menuConfig';
import '../../styles/admin/admin-layout.css';

interface AdminLayoutProps {
  children: React.ReactNode;
}

/**
 * 管理员专用布局组件
 *
 * 只显示管理员可访问的菜单项
 */
const AdminLayout: React.FC<AdminLayoutProps> = ({ children }) => {
  const location = useLocation();
  const navigate = useNavigate();
  const { toast } = useToast();
  const { user } = useAuth();
  const [userName, setUserName] = useState<string>('管理员');
  const [pendingCount, setPendingCount] = useState<number>(0);
  // 从localStorage加载侧边栏折叠状态
  const [sidebarCollapsed, setSidebarCollapsed] = useState<boolean>(() => {
    try {
      const savedState = localStorage.getItem('admin_sidebar_collapsed');
      return savedState === 'true';
    } catch (error) {
      console.error('加载侧边栏状态失败:', error);
      return false;
    }
  });

  // 管理员布局不需要管理菜单分组折叠状态，因为这个功能已经在SidebarMenu组件中实现

  // 检查超级管理员访问管理员页面的情况，重定向到对应的超级管理员页面
  useEffect(() => {
    if (user?.role === 'superadmin') {
      console.log('AdminLayout - 检测到超级管理员访问管理员页面，重定向到超级管理员页面');

      // 管理员页面到超级管理员页面的映射
      const redirectMap: { [key: string]: string } = {
        '/admin/dashboard': '/superadmin/dashboard',
        '/admin/content-review': '/superadmin/content-review',
        '/admin/comment-review': '/superadmin/comment-review',
        '/admin/story-review': '/superadmin/story-review',
        '/admin/quick-review': '/superadmin/quick-review',
        '/admin/questionnaire-responses': '/superadmin/questionnaire-responses',
        '/admin/data-analysis': '/superadmin/data-analysis',
        '/admin/user-management': '/superadmin/user-management',
        '/admin/reviewer-management': '/superadmin/user-management',
        '/admin/settings': '/superadmin/settings',
        '/admin/security-settings': '/superadmin/security-settings'
      };

      const targetPath = redirectMap[location.pathname] || '/superadmin/dashboard';
      console.log(`AdminLayout - 重定向: ${location.pathname} → ${targetPath}`);
      navigate(targetPath, { replace: true });
      return;
    }
  }, [location.pathname, user?.role, navigate]);

  // 获取用户信息和初始化菜单状态
  useEffect(() => {
    // 获取用户信息
    const userJson = localStorage.getItem('adminUser');
    if (userJson) {
      try {
        const user = JSON.parse(userJson);
        setUserName(user.name || '管理员');
      } catch (error) {
        console.error('解析用户信息失败:', error);
      }
    }

    // 模拟获取待审核内容数量
    setPendingCount(Math.floor(Math.random() * 10) + 1);

    // 标记菜单已初始化，防止路由变化时重置菜单状态
    sessionStorage.setItem('admin_menu_initialized', 'true');
  }, []);

  // 处理登出
  const handleLogout = () => {
    localStorage.removeItem('adminToken');
    localStorage.removeItem('adminUser');

    toast({
      title: '已登出',
      description: '您已成功退出管理员账号',
    });

    // 使用window.location.href进行页面跳转，确保页面刷新
    window.location.href = '/admin/login';
  };

  // 处理侧边栏折叠
  const toggleSidebar = () => {
    const newState = !sidebarCollapsed;
    setSidebarCollapsed(newState);

    // 保存到localStorage
    try {
      localStorage.setItem('admin_sidebar_collapsed', newState.toString());
    } catch (error) {
      console.error('保存侧边栏状态失败:', error);
    }
  };

  // 管理员布局不需要处理菜单组折叠，因为这个功能已经在SidebarMenu组件中实现

  return (
    <div className="admin-layout">
      <header className="admin-header">
        <div className="logo">
          <h1>就业调查管理系统</h1>
        </div>
        <div className="user-info">
          <div className="flex items-center">
            <Bell className="h-5 w-5 mr-2" />
            <span className="bg-red-500 text-white rounded-full px-2 py-0.5 text-xs mr-4">
              {pendingCount}
            </span>
          </div>
          <div className="flex items-center ml-4">
            <User className="h-4 w-4 mr-2" />
            <span className="mr-4">{userName} (管理员)</span>
            <Button
              variant="ghost"
              size="sm"
              onClick={handleLogout}
              className="flex items-center"
            >
              <LogOut className="h-4 w-4 mr-1" />
              登出
            </Button>
          </div>
        </div>
      </header>

      <div className="admin-content">
        <div className={`sidebar ${sidebarCollapsed ? 'collapsed' : ''}`}>
          <div className="sidebar-header">
            <h2>{sidebarCollapsed ? '' : '管理员后台'}</h2>
            <Button
              variant="ghost"
              size="sm"
              onClick={toggleSidebar}
              className="sidebar-toggle"
            >
              {sidebarCollapsed ? <Menu className="h-4 w-4" /> : <ChevronLeft className="h-4 w-4" />}
            </Button>
          </div>

          <SidebarMenu
            groups={adminMenuGroups}
            role="admin"
            collapsed={sidebarCollapsed}
          />
        </div>

        <div className="main-content">
          {children}
        </div>
      </div>
    </div>
  );
};

export default AdminLayout;
