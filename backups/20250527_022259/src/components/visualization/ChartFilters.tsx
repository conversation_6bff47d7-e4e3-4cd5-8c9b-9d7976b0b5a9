import { useState, useEffect } from 'react';
import { 
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue
} from '@/components/ui/select';
import { Button } from '@/components/ui/button';
import { VisualizationFilters } from '@/lib/api';
import { Filter, X } from 'lucide-react';

interface ChartFiltersProps {
  onFilterChange: (filters: VisualizationFilters) => void;
}

export default function ChartFilters({ onFilterChange }: ChartFiltersProps) {
  const [filters, setFilters] = useState<VisualizationFilters>({});
  const [isOpen, setIsOpen] = useState(false);
  
  // Education level options
  const educationLevels = [
    { value: '高中/中专', label: '高中/中专' },
    { value: '大专', label: '大专' },
    { value: '本科', label: '本科' },
    { value: '硕士', label: '硕士' },
    { value: '博士', label: '博士' },
  ];
  
  // Region options
  const regions = [
    { value: '北上广深', label: '北上广深' },
    { value: '省会城市', label: '省会城市' },
    { value: '二线城市', label: '二线城市' },
    { value: '三四线城市', label: '三四线城市' },
    { value: '县城或乡镇', label: '县城或乡镇' },
    { value: '海外', label: '海外' },
  ];
  
  // Graduation year options
  const graduationYears = [
    { value: '2023', label: '2023年' },
    { value: '2022', label: '2022年' },
    { value: '2021', label: '2021年' },
    { value: '2020', label: '2020年' },
    { value: '2019', label: '2019年' },
    { value: '2018', label: '2018年及以前' },
  ];
  
  // Update parent component when filters change
  useEffect(() => {
    onFilterChange(filters);
  }, [filters, onFilterChange]);
  
  // Handle filter changes
  const handleFilterChange = (key: string, value: string | null) => {
    if (value === null) {
      const newFilters = { ...filters };
      delete newFilters[key as keyof VisualizationFilters];
      setFilters(newFilters);
    } else {
      setFilters(prev => ({
        ...prev,
        [key]: key === 'graduationYear' ? parseInt(value) : value
      }));
    }
  };
  
  // Clear all filters
  const handleClearFilters = () => {
    setFilters({});
  };
  
  // Toggle filter panel
  const toggleFilterPanel = () => {
    setIsOpen(!isOpen);
  };
  
  // Count active filters
  const activeFilterCount = Object.keys(filters).length;
  
  return (
    <div className="relative">
      <Button 
        variant="outline" 
        size="sm" 
        onClick={toggleFilterPanel}
        className="flex items-center gap-2"
      >
        <Filter className="h-4 w-4" />
        筛选
        {activeFilterCount > 0 && (
          <span className="ml-1 rounded-full bg-primary text-primary-foreground w-5 h-5 flex items-center justify-center text-xs">
            {activeFilterCount}
          </span>
        )}
      </Button>
      
      {isOpen && (
        <div className="absolute right-0 top-10 z-10 mt-2 w-72 rounded-md bg-white p-4 shadow-lg border">
          <div className="flex justify-between items-center mb-4">
            <h3 className="font-medium">筛选条件</h3>
            <Button 
              variant="ghost" 
              size="sm" 
              onClick={toggleFilterPanel}
              className="h-8 w-8 p-0"
            >
              <X className="h-4 w-4" />
            </Button>
          </div>
          
          <div className="space-y-4">
            <div>
              <label className="text-sm font-medium mb-1 block">学历</label>
              <Select
                value={filters.educationLevel || ''}
                onValueChange={(value) => handleFilterChange('educationLevel', value || null)}
              >
                <SelectTrigger>
                  <SelectValue placeholder="全部学历" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="">全部学历</SelectItem>
                  {educationLevels.map((level) => (
                    <SelectItem key={level.value} value={level.value}>
                      {level.label}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
            
            <div>
              <label className="text-sm font-medium mb-1 block">地区</label>
              <Select
                value={filters.region || ''}
                onValueChange={(value) => handleFilterChange('region', value || null)}
              >
                <SelectTrigger>
                  <SelectValue placeholder="全部地区" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="">全部地区</SelectItem>
                  {regions.map((region) => (
                    <SelectItem key={region.value} value={region.value}>
                      {region.label}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
            
            <div>
              <label className="text-sm font-medium mb-1 block">毕业年份</label>
              <Select
                value={filters.graduationYear?.toString() || ''}
                onValueChange={(value) => handleFilterChange('graduationYear', value || null)}
              >
                <SelectTrigger>
                  <SelectValue placeholder="全部年份" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="">全部年份</SelectItem>
                  {graduationYears.map((year) => (
                    <SelectItem key={year.value} value={year.value}>
                      {year.label}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
            
            <div className="pt-2">
              <Button 
                variant="outline" 
                size="sm" 
                onClick={handleClearFilters}
                className="w-full"
                disabled={activeFilterCount === 0}
              >
                清除全部筛选
              </Button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
