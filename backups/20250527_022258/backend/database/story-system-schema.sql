-- =====================================================
-- 用户认证故事发布系统数据表设计 (基于A+B匿名身份验证)
-- =====================================================

-- 1. 用户表 (支持A+B匿名身份验证)
CREATE TABLE users (
  id TEXT PRIMARY KEY,                    -- user_xxx
  uuid TEXT UNIQUE NOT NULL,             -- 用户UUID (SHA-256哈希生成)

  -- 认证方式 (支持两种)
  auth_type TEXT NOT NULL CHECK (auth_type IN ('anonymous', 'email')), -- 认证类型

  -- A+B匿名身份验证 (主要方式)
  identity_a_hash TEXT,                  -- A值哈希 (11位数字，如手机号)
  identity_b_hash TEXT,                  -- B值哈希 (4位或6位密码)
  anonymous_uuid TEXT,                   -- A+B组合生成的UUID

  -- 邮箱认证 (可选方式)
  email TEXT UNIQUE,                     -- 邮箱 (可选)
  password_hash TEXT,                    -- 密码哈希 (可选)
  email_verified BOOLEAN DEFAULT FALSE,
  email_verified_at TIMESTAMP,

  -- 用户档案
  display_name TEXT,                     -- 显示名称 (可选)
  avatar_url TEXT,                       -- 头像URL
  education_level TEXT,                  -- 学历背景
  education_level_display TEXT,
  industry_code TEXT,                    -- 行业领域
  industry_display TEXT,
  graduation_year INTEGER,              -- 毕业年份
  location TEXT,                         -- 所在地区
  bio TEXT,                             -- 个人简介

  -- 账户状态
  status TEXT DEFAULT 'active' CHECK (status IN ('active', 'inactive', 'suspended')),
  is_anonymous BOOLEAN DEFAULT TRUE,     -- 是否匿名用户

  -- 统计信息
  stories_count INTEGER DEFAULT 0,      -- 发布故事数
  questionnaire_count INTEGER DEFAULT 0, -- 问卷提交数

  -- 时间戳
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  last_login_at TIMESTAMP,

  -- 约束条件
  CONSTRAINT check_auth_fields CHECK (
    (auth_type = 'anonymous' AND identity_a_hash IS NOT NULL AND identity_b_hash IS NOT NULL) OR
    (auth_type = 'email' AND email IS NOT NULL AND password_hash IS NOT NULL)
  )
);

-- 2. 故事表 (重新设计)
CREATE TABLE stories (
  id TEXT PRIMARY KEY,                   -- story_xxx
  user_uuid TEXT NOT NULL,              -- 关联用户UUID

  -- 基本内容
  title TEXT NOT NULL,                  -- 标题 (5-50字符)
  content TEXT NOT NULL,                -- 内容 (20-2000字符)
  summary TEXT,                         -- 摘要 (自动生成或手动)

  -- 分类维度
  category TEXT NOT NULL,               -- 主分类 (就业经历/转行经历/创业经历等)
  subcategory TEXT,                     -- 子分类
  story_type TEXT DEFAULT 'experience', -- 故事类型 (experience/advice/observation)

  -- 用户背景维度 (用于搜索和筛选)
  education_level TEXT,                 -- 学历背景
  education_level_display TEXT,
  industry_code TEXT,                   -- 行业领域
  industry_display TEXT,
  graduation_year INTEGER,             -- 毕业年份
  work_experience_years INTEGER,       -- 工作经验年数
  location TEXT,                        -- 地区

  -- 发布设置
  is_anonymous BOOLEAN DEFAULT FALSE,   -- 是否匿名发布
  author_name TEXT,                     -- 署名 (非匿名时显示)

  -- 审核状态
  status TEXT DEFAULT 'pending' CHECK (status IN ('draft', 'pending', 'approved', 'rejected', 'archived')),
  reviewed_by TEXT,                     -- 审核员ID
  reviewed_at TIMESTAMP,
  review_notes TEXT,                    -- 审核备注

  -- 互动数据
  views INTEGER DEFAULT 0,
  likes INTEGER DEFAULT 0,
  dislikes INTEGER DEFAULT 0,
  shares INTEGER DEFAULT 0,
  comments_count INTEGER DEFAULT 0,
  bookmarks_count INTEGER DEFAULT 0,

  -- 质量和推荐
  quality_score REAL DEFAULT 0.0,      -- 质量评分
  trending_score REAL DEFAULT 0.0,     -- 热度评分
  featured BOOLEAN DEFAULT FALSE,       -- 是否精选

  -- 元数据
  word_count INTEGER,                   -- 字数统计
  reading_time INTEGER,                -- 预估阅读时间(分钟)
  language TEXT DEFAULT 'zh-CN',       -- 语言

  -- 时间戳
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  published_at TIMESTAMP,              -- 发布时间

  FOREIGN KEY (user_uuid) REFERENCES users(uuid)
);

-- 3. 标签表 (分层设计)
CREATE TABLE tags (
  id TEXT PRIMARY KEY,                  -- tag_xxx
  name TEXT NOT NULL UNIQUE,           -- 标签名称
  display_name TEXT NOT NULL,          -- 显示名称
  category TEXT NOT NULL,              -- 标签分类 (industry/skill/topic/location等)
  parent_id TEXT,                      -- 父标签ID (支持层级)
  description TEXT,                    -- 标签描述
  color TEXT DEFAULT '#3B82F6',        -- 标签颜色
  icon TEXT,                           -- 图标

  -- 统计和状态
  usage_count INTEGER DEFAULT 0,       -- 使用次数
  is_system BOOLEAN DEFAULT FALSE,     -- 是否系统标签
  is_featured BOOLEAN DEFAULT FALSE,   -- 是否推荐标签
  status TEXT DEFAULT 'active' CHECK (status IN ('active', 'inactive', 'deprecated')),

  -- 搜索权重
  search_weight REAL DEFAULT 1.0,      -- 搜索权重

  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,

  FOREIGN KEY (parent_id) REFERENCES tags(id)
);

-- 4. 故事标签关联表
CREATE TABLE story_tags (
  id TEXT PRIMARY KEY,
  story_id TEXT NOT NULL,              -- 故事ID
  tag_id TEXT NOT NULL,                -- 标签ID
  relevance_score REAL DEFAULT 1.0,    -- 相关度评分
  is_primary BOOLEAN DEFAULT FALSE,    -- 是否主要标签
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,

  UNIQUE(story_id, tag_id),
  FOREIGN KEY (story_id) REFERENCES stories(id) ON DELETE CASCADE,
  FOREIGN KEY (tag_id) REFERENCES tags(id)
);

-- 5. 用户认证会话表 (支持A+B认证)
CREATE TABLE user_sessions (
  id TEXT PRIMARY KEY,                  -- session_xxx
  user_uuid TEXT NOT NULL,             -- 用户UUID
  auth_type TEXT NOT NULL,             -- 认证类型 (anonymous/email)

  -- 会话信息
  token_hash TEXT NOT NULL,            -- 会话令牌哈希
  session_data TEXT,                   -- 会话数据 (JSON格式)

  -- 设备和网络信息
  device_info TEXT,                    -- 设备信息
  ip_address TEXT,                     -- IP地址
  user_agent TEXT,                     -- 用户代理
  fingerprint TEXT,                    -- 浏览器指纹

  -- 时间管理
  expires_at TIMESTAMP NOT NULL,       -- 过期时间
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  last_used_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,

  -- 安全标记
  is_suspicious BOOLEAN DEFAULT FALSE, -- 是否可疑会话
  security_level TEXT DEFAULT 'normal' CHECK (security_level IN ('low', 'normal', 'high')),

  FOREIGN KEY (user_uuid) REFERENCES users(uuid) ON DELETE CASCADE
);

-- 6. 故事互动表
CREATE TABLE story_interactions (
  id TEXT PRIMARY KEY,
  story_id TEXT NOT NULL,              -- 故事ID
  user_uuid TEXT,                      -- 用户UUID (可为空，支持匿名)
  interaction_type TEXT NOT NULL CHECK (interaction_type IN ('view', 'like', 'dislike', 'share', 'bookmark')),
  ip_address TEXT,                     -- IP地址 (匿名用户)
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,

  UNIQUE(story_id, user_uuid, interaction_type),
  FOREIGN KEY (story_id) REFERENCES stories(id) ON DELETE CASCADE,
  FOREIGN KEY (user_uuid) REFERENCES users(uuid) ON DELETE SET NULL
);

-- =====================================================
-- 索引设计 (优化查询性能)
-- =====================================================

-- 用户表索引
CREATE INDEX idx_users_email ON users(email);
CREATE INDEX idx_users_uuid ON users(uuid);
CREATE INDEX idx_users_status ON users(status, created_at DESC);
CREATE INDEX idx_users_industry ON users(industry_code, education_level);

-- 故事表索引 (支持多维度搜索)
CREATE INDEX idx_stories_user ON stories(user_uuid, status);
CREATE INDEX idx_stories_status ON stories(status, created_at DESC);
CREATE INDEX idx_stories_category ON stories(category, subcategory);
CREATE INDEX idx_stories_education ON stories(education_level, industry_code);
CREATE INDEX idx_stories_trending ON stories(trending_score DESC, featured DESC);
CREATE INDEX idx_stories_search ON stories(status, title, content);
CREATE INDEX idx_stories_location ON stories(location, graduation_year);
CREATE INDEX idx_stories_published ON stories(published_at DESC, status);

-- 标签表索引
CREATE INDEX idx_tags_category ON tags(category, status);
CREATE INDEX idx_tags_usage ON tags(usage_count DESC, is_featured DESC);
CREATE INDEX idx_tags_parent ON tags(parent_id, status);

-- 故事标签关联索引
CREATE INDEX idx_story_tags_story ON story_tags(story_id);
CREATE INDEX idx_story_tags_tag ON story_tags(tag_id, relevance_score DESC);
CREATE INDEX idx_story_tags_primary ON story_tags(is_primary, tag_id);

-- 会话表索引
CREATE INDEX idx_sessions_user ON user_sessions(user_uuid, expires_at);
CREATE INDEX idx_sessions_token ON user_sessions(token_hash);
CREATE INDEX idx_sessions_expires ON user_sessions(expires_at);

-- 互动表索引
CREATE INDEX idx_interactions_story ON story_interactions(story_id, interaction_type);
CREATE INDEX idx_interactions_user ON story_interactions(user_uuid, created_at DESC);
CREATE INDEX idx_interactions_type ON story_interactions(interaction_type, created_at DESC);

-- =====================================================
-- 视图设计 (简化查询)
-- =====================================================

-- 故事详情视图 (包含标签和用户信息)
CREATE VIEW v_story_details AS
SELECT
  s.*,
  u.display_name as author_display_name,
  u.avatar_url as author_avatar,
  u.education_level as author_education,
  u.industry_display as author_industry,
  GROUP_CONCAT(t.display_name, ',') as tag_names,
  GROUP_CONCAT(t.id, ',') as tag_ids,
  GROUP_CONCAT(t.color, ',') as tag_colors
FROM stories s
LEFT JOIN users u ON s.user_uuid = u.uuid
LEFT JOIN story_tags st ON s.id = st.story_id
LEFT JOIN tags t ON st.tag_id = t.id AND t.status = 'active'
GROUP BY s.id;

-- 用户故事统计视图
CREATE VIEW v_user_story_stats AS
SELECT
  u.uuid,
  u.display_name,
  COUNT(s.id) as total_stories,
  COUNT(CASE WHEN s.status = 'approved' THEN 1 END) as published_stories,
  COUNT(CASE WHEN s.status = 'pending' THEN 1 END) as pending_stories,
  SUM(s.views) as total_views,
  SUM(s.likes) as total_likes,
  AVG(s.quality_score) as avg_quality_score
FROM users u
LEFT JOIN stories s ON u.uuid = s.user_uuid
GROUP BY u.uuid, u.display_name;

-- 热门标签视图
CREATE VIEW v_popular_tags AS
SELECT
  t.*,
  COUNT(st.story_id) as story_count,
  COUNT(CASE WHEN s.status = 'approved' THEN 1 END) as approved_story_count
FROM tags t
LEFT JOIN story_tags st ON t.id = st.tag_id
LEFT JOIN stories s ON st.story_id = s.id
WHERE t.status = 'active'
GROUP BY t.id
ORDER BY t.usage_count DESC, approved_story_count DESC;
