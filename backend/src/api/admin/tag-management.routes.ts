import { Hono } from 'hono';
import { Env } from '../../types';
import { adminAuthMiddleware } from '../../middlewares/auth.middleware';
import { reviewerAuthMiddleware } from '../../middlewares/reviewerAuth.middleware';
import { superAdminAuthMiddleware } from '../../middlewares/superAdminAuth.middleware';
import {
  getTags,
  createTag,
  updateTag,
  deleteTag,
  getTagById,
  getTagStats,
  batchOperateTags,
  getTagCategories,
  createTagCategory,
  updateTagCategory,
  deleteTagCategory,
  getTagUsageAnalytics,
  getTagTrends,
  getTagCorrelations
} from './tag-management.controller';

// 创建路由
const tagRouter = new Hono<{ Bindings: Env }>();

// 通用认证中间件 - 允许审核员、管理员和超级管理员访问
const anyRoleAuthMiddleware = async (c: any, next: any) => {
  // 尝试超级管理员认证
  try {
    await superAdminAuthMiddleware(c, () => {});
    await next();
    return;
  } catch (error) {
    // 超级管理员认证失败，继续尝试管理员认证
  }

  // 尝试管理员认证
  try {
    await adminAuthMiddleware(c, () => {});
    await next();
    return;
  } catch (error) {
    // 管理员认证失败，继续尝试审核员认证
  }

  // 尝试审核员认证
  try {
    await reviewerAuthMiddleware(c, next);
    return;
  } catch (error) {
    // 所有认证都失败
    return c.json({ success: false, error: 'Unauthorized' }, 401);
  }
};

// 管理员或超级管理员认证中间件
const adminOrSuperAdminAuthMiddleware = async (c: any, next: any) => {
  // 尝试超级管理员认证
  try {
    await superAdminAuthMiddleware(c, () => {});
    await next();
    return;
  } catch (error) {
    // 超级管理员认证失败，尝试管理员认证
  }

  // 尝试管理员认证
  try {
    await adminAuthMiddleware(c, next);
    return;
  } catch (error) {
    // 所有认证都失败
    return c.json({ success: false, error: 'Unauthorized' }, 401);
  }
};

// ==================== 标签查询 ====================

// 获取标签列表（允许审核员、管理员和超级管理员访问）
tagRouter.get('/', anyRoleAuthMiddleware, getTags);

// 获取单个标签详情（允许审核员、管理员和超级管理员访问）
tagRouter.get('/:id', anyRoleAuthMiddleware, getTagById);

// 获取标签使用统计（允许审核员、管理员和超级管理员访问）
tagRouter.get('/:id/stats', anyRoleAuthMiddleware, getTagStats);

// ==================== 标签管理 ====================

// 创建标签（允许管理员和超级管理员访问）
tagRouter.post('/', adminOrSuperAdminAuthMiddleware, createTag);

// 更新标签（允许管理员和超级管理员访问）
tagRouter.put('/:id', adminOrSuperAdminAuthMiddleware, updateTag);

// 删除标签（允许管理员和超级管理员访问）
tagRouter.delete('/:id', adminOrSuperAdminAuthMiddleware, deleteTag);

// 批量操作标签（允许管理员和超级管理员访问）
tagRouter.post('/batch', adminOrSuperAdminAuthMiddleware, batchOperateTags);

// ==================== 标签分类管理 ====================

// 获取标签分类列表（允许审核员、管理员和超级管理员访问）
tagRouter.get('/categories/list', anyRoleAuthMiddleware, getTagCategories);

// 创建标签分类（允许管理员和超级管理员访问）
tagRouter.post('/categories', adminOrSuperAdminAuthMiddleware, createTagCategory);

// 更新标签分类（允许管理员和超级管理员访问）
tagRouter.put('/categories/:category', adminOrSuperAdminAuthMiddleware, updateTagCategory);

// 删除标签分类（允许管理员和超级管理员访问）
tagRouter.delete('/categories/:category', adminOrSuperAdminAuthMiddleware, deleteTagCategory);

// ==================== 标签统计和分析 ====================

// 获取标签使用统计（允许管理员和超级管理员访问）
tagRouter.get('/analytics/usage', adminOrSuperAdminAuthMiddleware, getTagUsageAnalytics);

// 获取标签趋势分析（允许管理员和超级管理员访问）
tagRouter.get('/analytics/trends', adminOrSuperAdminAuthMiddleware, getTagTrends);

// 获取标签关联分析（允许管理员和超级管理员访问）
tagRouter.get('/analytics/correlations', adminOrSuperAdminAuthMiddleware, getTagCorrelations);

export default tagRouter;
