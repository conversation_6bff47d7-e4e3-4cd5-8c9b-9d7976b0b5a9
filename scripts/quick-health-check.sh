#!/bin/bash

# 大学生就业调研系统 - 快速健康检查脚本
# 用于快速诊断系统状态和常见问题

set -e

# 配置
API_BASE="https://college-employment-survey.aibook2099.workers.dev"
FRONTEND_URL="https://college-employment-survey.pages.dev"

# 颜色输出
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 输出函数
log_info() {
    echo -e "${BLUE}ℹ️  $1${NC}"
}

log_success() {
    echo -e "${GREEN}✅ $1${NC}"
}

log_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

log_error() {
    echo -e "${RED}❌ $1${NC}"
}

# 检查命令是否存在
check_command() {
    if ! command -v $1 &> /dev/null; then
        log_error "$1 命令未找到，请安装后重试"
        exit 1
    fi
}

# 检查API端点
check_endpoint() {
    local endpoint=$1
    local expected_status=$2
    local description=$3
    
    log_info "检查 $description..."
    
    local status=$(curl -s -o /dev/null -w "%{http_code}" "$API_BASE$endpoint" 2>/dev/null || echo "000")
    
    if [ "$status" = "$expected_status" ]; then
        log_success "$description - 状态码 $status (正常)"
        return 0
    elif [ "$status" = "000" ]; then
        log_error "$description - 连接失败"
        return 1
    else
        log_warning "$description - 状态码 $status (期望 $expected_status)"
        return 1
    fi
}

# 测试管理员登录
test_admin_login() {
    local username=$1
    local password=$2
    local role_name=$3
    
    log_info "测试 $role_name 登录..."
    
    local response=$(curl -s -X POST "$API_BASE/api/admin/login" \
        -H "Content-Type: application/json" \
        -d "{\"username\":\"$username\",\"password\":\"$password\"}" 2>/dev/null)
    
    if echo "$response" | jq -e '.success' > /dev/null 2>&1; then
        local token=$(echo "$response" | jq -r '.data.token')
        local role=$(echo "$response" | jq -r '.data.user.role')
        log_success "$role_name 登录成功 (角色: $role)"
        echo "$token"
        return 0
    else
        log_error "$role_name 登录失败"
        echo "$response" | jq . 2>/dev/null || echo "$response"
        return 1
    fi
}

# 测试受保护的API
test_protected_api() {
    local token=$1
    local endpoint=$2
    local description=$3
    
    log_info "测试 $description..."
    
    local response=$(curl -s -X GET "$API_BASE$endpoint" \
        -H "Authorization: Bearer $token" 2>/dev/null)
    
    if echo "$response" | jq -e '.success' > /dev/null 2>&1; then
        log_success "$description - API调用成功"
        return 0
    else
        log_error "$description - API调用失败"
        echo "$response" | jq . 2>/dev/null || echo "$response"
        return 1
    fi
}

# 主检查流程
main() {
    echo "🔍 大学生就业调研系统 - 快速健康检查"
    echo "=================================================="
    
    # 检查必要工具
    log_info "检查必要工具..."
    check_command "curl"
    check_command "jq"
    
    # 1. 基础连通性检查
    echo ""
    log_info "📡 基础连通性检查"
    echo "----------------------------------------"
    
    check_endpoint "/health" "200" "健康检查端点"
    check_endpoint "/api/admin/login" "405" "管理员登录端点"
    check_endpoint "/api/admin/deidentification/config" "401" "去标识化配置端点"
    check_endpoint "/api/admin/deidentification/test-data" "401" "测试数据端点"
    check_endpoint "/api/admin/deidentification/validate-api-key" "401" "API密钥验证端点"
    
    # 2. 管理员登录测试
    echo ""
    log_info "🔐 管理员登录测试"
    echo "----------------------------------------"
    
    # 测试各种管理员角色
    admin_token=$(test_admin_login "admin1" "admin123" "管理员")
    reviewer_token=$(test_admin_login "reviewer1" "admin123" "审核员")
    superadmin_token=$(test_admin_login "superadmin" "admin123" "超级管理员")
    
    # 3. 受保护API测试
    if [ ! -z "$superadmin_token" ]; then
        echo ""
        log_info "🛡️  受保护API测试"
        echo "----------------------------------------"
        
        test_protected_api "$superadmin_token" "/api/admin/deidentification/config" "去标识化配置"
        test_protected_api "$superadmin_token" "/api/admin/deidentification/test-data" "测试数据获取"
        test_protected_api "$superadmin_token" "/api/admin/deidentification/provider-stats" "提供商统计"
    else
        log_warning "跳过受保护API测试 (superadmin登录失败)"
    fi
    
    # 4. 前端页面检查
    echo ""
    log_info "🌐 前端页面检查"
    echo "----------------------------------------"
    
    local frontend_status=$(curl -s -o /dev/null -w "%{http_code}" "$FRONTEND_URL" 2>/dev/null || echo "000")
    if [ "$frontend_status" = "200" ]; then
        log_success "前端页面可访问"
    else
        log_error "前端页面访问失败 (状态码: $frontend_status)"
    fi
    
    # 5. 总结
    echo ""
    log_info "📊 检查总结"
    echo "----------------------------------------"
    
    if [ ! -z "$admin_token" ] && [ ! -z "$reviewer_token" ] && [ ! -z "$superadmin_token" ]; then
        log_success "所有管理员角色登录正常"
    else
        log_warning "部分管理员角色登录异常"
    fi
    
    echo ""
    log_info "🔧 如果发现问题，请参考 TROUBLESHOOTING_GUIDE.md 进行修复"
    echo "=================================================="
}

# 运行主函数
main "$@"
