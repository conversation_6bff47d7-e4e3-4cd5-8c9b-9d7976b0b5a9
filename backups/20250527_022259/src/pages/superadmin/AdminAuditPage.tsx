import React, { useState, useEffect } from 'react';
import SuperAdminLayout from '@/components/layouts/SuperAdminLayout';
import { Card, CardContent, CardDescription, CardHeader, CardTitle, CardFooter } from '@/components/ui/card';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Badge } from '@/components/ui/badge';
import { Pagination, PaginationContent, PaginationItem, PaginationLink, PaginationNext, PaginationPrevious } from '@/components/ui/pagination-ui';
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { useToast } from '@/components/ui/use-toast';
import {
  Search,
  Filter,
  Download,
  Eye,
  AlertTriangle,
  Clock,
  User,
  FileText,
  Settings,
  Shield,
  RefreshCw,
  Info,
  Calendar,
  Activity,
  Database
} from 'lucide-react';
import { Permission } from '@/lib/permissions';
import { usePermission } from '@/hooks/usePermission';
import { getAuditLogs, getAuditLogDetails, exportAuditLogs, AuditLog, AuditLogSearchParams } from '@/services/adminAuditService';

/**
 * 管理员审计日志页面
 *
 * 超级管理员专属功能，用于查看管理员的操作记录
 */
export default function AdminAuditPage() {
  const { toast } = useToast();
  const { checkPermission } = usePermission();
  const [auditLogs, setAuditLogs] = useState<AuditLog[]>([]);
  const [selectedLog, setSelectedLog] = useState<AuditLog | null>(null);
  const [isDetailsDialogOpen, setIsDetailsDialogOpen] = useState(false);
  const [isLoading, setIsLoading] = useState(true);
  const [isExporting, setIsExporting] = useState(false);
  const [totalLogs, setTotalLogs] = useState(0);
  const [totalPages, setTotalPages] = useState(1);
  const [searchTerm, setSearchTerm] = useState('');
  const [currentPage, setCurrentPage] = useState(1);
  const [filterSeverity, setFilterSeverity] = useState<string>('all');
  const [filterAction, setFilterAction] = useState<string>('all');
  const [dateRange, setDateRange] = useState<{ start: string; end: string }>({
    start: '',
    end: ''
  });

  // 检查权限
  const canViewAuditLogs = checkPermission(Permission.AUDIT_VIEW);
  const canExportAuditLogs = checkPermission(Permission.AUDIT_EXPORT);
  const canDeleteAuditLogs = checkPermission(Permission.AUDIT_DELETE);

  // 搜索参数
  const [searchParams, setSearchParams] = useState<AuditLogSearchParams>({
    page: 1,
    limit: 10
  });

  // 获取审计日志
  const fetchAuditLogs = async () => {
    if (!canViewAuditLogs) {
      toast({
        title: '权限不足',
        description: '您没有查看审计日志的权限',
        variant: 'destructive'
      });
      return;
    }

    try {
      setIsLoading(true);

      // 构建搜索参数
      const params: AuditLogSearchParams = {
        page: currentPage,
        limit: 10
      };

      // 添加搜索条件
      if (searchTerm) {
        params.username = searchTerm;
      }

      // 添加筛选条件
      if (filterSeverity !== 'all') {
        params.riskLevel = filterSeverity as any;
      }

      if (filterAction !== 'all') {
        params.action = filterAction;
      }

      // 添加日期范围
      if (dateRange.start) {
        params.startDate = dateRange.start;
      }

      if (dateRange.end) {
        params.endDate = dateRange.end;
      }

      const response = await getAuditLogs(params);

      if (response.success) {
        setAuditLogs(response.data.logs);
        setTotalLogs(response.data.total);
        setTotalPages(response.data.totalPages);
      } else {
        toast({
          variant: 'destructive',
          title: '获取审计日志失败',
          description: response.message || '无法加载审计日志'
        });
      }
    } catch (error) {
      console.error('获取审计日志失败:', error);
      toast({
        variant: 'destructive',
        title: '获取审计日志失败',
        description: '服务器错误，请稍后再试'
      });
    } finally {
      setIsLoading(false);
    }
  };

  // 组件加载时获取审计日志
  useEffect(() => {
    fetchAuditLogs();
  }, [currentPage, filterSeverity, filterAction, dateRange]);

  // 查看日志详情
  const handleViewDetails = async (log: AuditLog) => {
    try {
      setIsLoading(true);
      const response = await getAuditLogDetails(log.id);

      if (response.success) {
        setSelectedLog(response.data);
        setIsDetailsDialogOpen(true);
      } else {
        toast({
          variant: 'destructive',
          title: '获取日志详情失败',
          description: response.message || '无法加载日志详情'
        });
      }
    } catch (error) {
      console.error('获取日志详情失败:', error);
      toast({
        variant: 'destructive',
        title: '获取日志详情失败',
        description: '服务器错误，请稍后再试'
      });
    } finally {
      setIsLoading(false);
    }
  };

  // 处理搜索
  const handleSearch = () => {
    setCurrentPage(1);
    fetchAuditLogs();
  };

  // 处理导出
  const handleExport = async () => {
    if (!canExportAuditLogs) {
      toast({
        title: '权限不足',
        description: '您没有导出审计日志的权限',
        variant: 'destructive'
      });
      return;
    }

    try {
      setIsExporting(true);

      // 构建导出参数
      const params: AuditLogSearchParams = {};

      // 添加搜索条件
      if (searchTerm) {
        params.username = searchTerm;
      }

      // 添加筛选条件
      if (filterSeverity !== 'all') {
        params.riskLevel = filterSeverity as any;
      }

      if (filterAction !== 'all') {
        params.action = filterAction;
      }

      // 添加日期范围
      if (dateRange.start) {
        params.startDate = dateRange.start;
      }

      if (dateRange.end) {
        params.endDate = dateRange.end;
      }

      const response = await exportAuditLogs(params);

      if (response.success) {
        toast({
          title: '导出成功',
          description: '审计日志已成功导出'
        });

        // 如果有下载链接，则触发下载
        if (response.data?.downloadUrl) {
          window.open(response.data.downloadUrl, '_blank');
        }
      } else {
        toast({
          variant: 'destructive',
          title: '导出失败',
          description: response.message || '无法导出审计日志'
        });
      }
    } catch (error) {
      console.error('导出审计日志失败:', error);
      toast({
        variant: 'destructive',
        title: '导出失败',
        description: '服务器错误，请稍后再试'
      });
    } finally {
      setIsExporting(false);
    }
  };

  // 获取风险等级徽章
  const getRiskLevelBadge = (riskLevel: string) => {
    switch (riskLevel) {
      case 'low':
        return <Badge variant="outline" className="bg-green-50 text-green-700 border-green-200">低</Badge>;
      case 'medium':
        return <Badge variant="outline" className="bg-yellow-50 text-yellow-700 border-yellow-200">中</Badge>;
      case 'high':
        return <Badge variant="outline" className="bg-orange-50 text-orange-700 border-orange-200">高</Badge>;
      case 'critical':
        return <Badge variant="outline" className="bg-red-50 text-red-700 border-red-200">严重</Badge>;
      default:
        return <Badge variant="outline">未知</Badge>;
    }
  };

  // 获取状态徽章
  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'success':
        return <Badge variant="outline" className="bg-green-50 text-green-700 border-green-200">成功</Badge>;
      case 'failure':
        return <Badge variant="outline" className="bg-red-50 text-red-700 border-red-200">失败</Badge>;
      default:
        return <Badge variant="outline">未知</Badge>;
    }
  };

  // 获取操作图标
  const getActionIcon = (action: string) => {
    switch (action) {
      case 'create':
      case '创建':
        return <FileText className="h-4 w-4 text-green-500" />;
      case 'update':
      case '修改':
        return <Settings className="h-4 w-4 text-blue-500" />;
      case 'delete':
      case '删除':
        return <AlertTriangle className="h-4 w-4 text-red-500" />;
      case 'review':
      case 'approve':
      case 'reject':
      case '审核':
        return <Eye className="h-4 w-4 text-purple-500" />;
      case 'login':
      case '登录':
        return <User className="h-4 w-4 text-yellow-500" />;
      case 'export':
      case '导出':
        return <Download className="h-4 w-4 text-gray-500" />;
      case 'backup':
        return <Database className="h-4 w-4 text-blue-500" />;
      default:
        return <Shield className="h-4 w-4" />;
    }
  };

  return (
    <SuperAdminLayout>
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-2xl font-bold">管理员审计日志</h1>

        <div className="flex items-center space-x-2">
          <div className="relative">
            <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
            <Input
              type="search"
              placeholder="搜索审计日志..."
              className="pl-8 w-[250px]"
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              onKeyDown={(e) => e.key === 'Enter' && handleSearch()}
            />
          </div>

          <Button variant="outline" size="sm">
            <Filter className="mr-2 h-4 w-4" />
            筛选
          </Button>

          <Button
            variant="outline"
            size="sm"
            onClick={handleExport}
            disabled={!canExportAuditLogs}
          >
            <Download className="mr-2 h-4 w-4" />
            导出
          </Button>
        </div>
      </div>

      <Card>
        <CardHeader>
          <CardTitle>审计日志</CardTitle>
          <CardDescription>查看管理员的所有操作记录</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <div className="flex flex-wrap gap-4 mb-4">
              <div className="w-full md:w-auto">
                <Select value={filterSeverity} onValueChange={setFilterSeverity}>
                  <SelectTrigger className="w-[180px]">
                    <SelectValue placeholder="严重性" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">所有严重性</SelectItem>
                    <SelectItem value="low">低</SelectItem>
                    <SelectItem value="medium">中</SelectItem>
                    <SelectItem value="high">高</SelectItem>
                    <SelectItem value="critical">严重</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div className="w-full md:w-auto">
                <Select value={filterAction} onValueChange={setFilterAction}>
                  <SelectTrigger className="w-[180px]">
                    <SelectValue placeholder="操作类型" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">所有操作</SelectItem>
                    <SelectItem value="create">创建</SelectItem>
                    <SelectItem value="update">修改</SelectItem>
                    <SelectItem value="delete">删除</SelectItem>
                    <SelectItem value="review">审核</SelectItem>
                    <SelectItem value="login">登录</SelectItem>
                    <SelectItem value="export">导出</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div className="w-full md:w-auto">
                <Input
                  type="date"
                  value={dateRange.start}
                  onChange={(e) => setDateRange({ ...dateRange, start: e.target.value })}
                  className="w-[180px]"
                />
              </div>

              <div className="w-full md:w-auto">
                <Input
                  type="date"
                  value={dateRange.end}
                  onChange={(e) => setDateRange({ ...dateRange, end: e.target.value })}
                  className="w-[180px]"
                />
              </div>
            </div>

            <div className="rounded-md border">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead className="w-[100px]">严重性</TableHead>
                    <TableHead>管理员</TableHead>
                    <TableHead>操作</TableHead>
                    <TableHead>资源</TableHead>
                    <TableHead>IP地址</TableHead>
                    <TableHead className="w-[180px]">时间</TableHead>
                    <TableHead className="text-right">操作</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {isLoading ? (
                    <TableRow>
                      <TableCell colSpan={8} className="text-center py-8">
                        <div className="flex justify-center items-center">
                          <RefreshCw className="h-5 w-5 animate-spin mr-2" />
                          加载中...
                        </div>
                      </TableCell>
                    </TableRow>
                  ) : auditLogs.length === 0 ? (
                    <TableRow>
                      <TableCell colSpan={8} className="text-center py-8">
                        没有找到审计日志
                      </TableCell>
                    </TableRow>
                  ) : (
                    auditLogs.map((log) => (
                      <TableRow key={log.id}>
                        <TableCell>{getRiskLevelBadge(log.riskLevel)}</TableCell>
                        <TableCell>{log.username}</TableCell>
                        <TableCell>
                          <div className="flex items-center">
                            {getActionIcon(log.action)}
                            <span className="ml-2">{log.action}</span>
                          </div>
                        </TableCell>
                        <TableCell>
                          <span className="font-medium">{log.resource}</span>
                          {log.resourceId && (
                            <span className="text-muted-foreground ml-1 text-xs">#{log.resourceId}</span>
                          )}
                        </TableCell>
                        <TableCell>{log.ip || '-'}</TableCell>
                        <TableCell>{new Date(log.timestamp).toLocaleString()}</TableCell>
                        <TableCell>{getStatusBadge(log.status)}</TableCell>
                        <TableCell className="text-right">
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => handleViewDetails(log)}
                          >
                            <Info className="h-4 w-4" />
                          </Button>
                        </TableCell>
                      </TableRow>
                    ))
                  )}
                </TableBody>
              </Table>
            </div>

            <Pagination>
              <PaginationContent>
                <PaginationItem>
                  <PaginationPrevious
                    href="#"
                    onClick={(e) => {
                      e.preventDefault();
                      if (currentPage > 1) setCurrentPage(currentPage - 1);
                    }}
                  />
                </PaginationItem>
                {Array.from({ length: totalPages }, (_, i) => (
                  <PaginationItem key={i}>
                    <PaginationLink
                      href="#"
                      isActive={currentPage === i + 1}
                      onClick={(e) => {
                        e.preventDefault();
                        setCurrentPage(i + 1);
                      }}
                    >
                      {i + 1}
                    </PaginationLink>
                  </PaginationItem>
                ))}
                <PaginationItem>
                  <PaginationNext
                    href="#"
                    onClick={(e) => {
                      e.preventDefault();
                      if (currentPage < totalPages) setCurrentPage(currentPage + 1);
                    }}
                  />
                </PaginationItem>
              </PaginationContent>
            </Pagination>
          </div>
        </CardContent>
      </Card>

      {/* 日志详情对话框 */}
      <Dialog open={isDetailsDialogOpen} onOpenChange={setIsDetailsDialogOpen}>
        <DialogContent className="max-w-3xl">
          <DialogHeader>
            <DialogTitle className="flex items-center">
              <Info className="h-5 w-5 mr-2" />
              审计日志详情
            </DialogTitle>
            <DialogDescription>
              查看审计日志的详细信息
            </DialogDescription>
          </DialogHeader>

          {selectedLog && (
            <div className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <label className="text-sm font-medium flex items-center">
                    <User className="h-4 w-4 mr-1" />
                    用户信息
                  </label>
                  <div className="border rounded-md p-3">
                    <div className="grid grid-cols-2 gap-2">
                      <div>
                        <span className="text-sm text-muted-foreground">用户名:</span>
                        <p>{selectedLog.username}</p>
                      </div>
                      <div>
                        <span className="text-sm text-muted-foreground">用户ID:</span>
                        <p>{selectedLog.userId}</p>
                      </div>
                      <div>
                        <span className="text-sm text-muted-foreground">角色:</span>
                        <p>{selectedLog.role === 'admin' ? '管理员' : '超级管理员'}</p>
                      </div>
                    </div>
                  </div>
                </div>

                <div className="space-y-2">
                  <label className="text-sm font-medium flex items-center">
                    <Clock className="h-4 w-4 mr-1" />
                    时间信息
                  </label>
                  <div className="border rounded-md p-3">
                    <div>
                      <span className="text-sm text-muted-foreground">时间戳:</span>
                      <p>
                        {new Date(selectedLog.timestamp).toLocaleString('zh-CN', {
                          year: 'numeric',
                          month: '2-digit',
                          day: '2-digit',
                          hour: '2-digit',
                          minute: '2-digit',
                          second: '2-digit'
                        })}
                      </p>
                    </div>
                  </div>
                </div>

                <div className="space-y-2">
                  <label className="text-sm font-medium flex items-center">
                    <Activity className="h-4 w-4 mr-1" />
                    操作信息
                  </label>
                  <div className="border rounded-md p-3">
                    <div className="grid grid-cols-2 gap-2">
                      <div>
                        <span className="text-sm text-muted-foreground">操作:</span>
                        <p>{selectedLog.action}</p>
                      </div>
                      <div>
                        <span className="text-sm text-muted-foreground">状态:</span>
                        <p>{selectedLog.status === 'success' ? '成功' : '失败'}</p>
                      </div>
                      <div>
                        <span className="text-sm text-muted-foreground">风险等级:</span>
                        <p>{getRiskLevelBadge(selectedLog.riskLevel)}</p>
                      </div>
                    </div>
                  </div>
                </div>

                <div className="space-y-2">
                  <label className="text-sm font-medium flex items-center">
                    <Database className="h-4 w-4 mr-1" />
                    资源信息
                  </label>
                  <div className="border rounded-md p-3">
                    <div className="grid grid-cols-2 gap-2">
                      <div>
                        <span className="text-sm text-muted-foreground">资源类型:</span>
                        <p>{selectedLog.resource}</p>
                      </div>
                      {selectedLog.resourceId && (
                        <div>
                          <span className="text-sm text-muted-foreground">资源ID:</span>
                          <p>{selectedLog.resourceId}</p>
                        </div>
                      )}
                    </div>
                  </div>
                </div>
              </div>

              {selectedLog.details && (
                <div className="space-y-2">
                  <label className="text-sm font-medium flex items-center">
                    <FileText className="h-4 w-4 mr-1" />
                    详细信息
                  </label>
                  <div className="border rounded-md p-3">
                    <p>{selectedLog.details}</p>
                  </div>
                </div>
              )}

              <div className="space-y-2">
                <label className="text-sm font-medium flex items-center">
                  <Settings className="h-4 w-4 mr-1" />
                  系统信息
                </label>
                <div className="border rounded-md p-3">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-2">
                    {selectedLog.ip && (
                      <div>
                        <span className="text-sm text-muted-foreground">IP地址:</span>
                        <p>{selectedLog.ip}</p>
                      </div>
                    )}
                    {selectedLog.userAgent && (
                      <div>
                        <span className="text-sm text-muted-foreground">用户代理:</span>
                        <p className="text-xs break-all">{selectedLog.userAgent}</p>
                      </div>
                    )}
                  </div>
                </div>
              </div>
            </div>
          )}

          <DialogFooter>
            <Button onClick={() => setIsDetailsDialogOpen(false)}>
              关闭
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </SuperAdminLayout>
  );
}
