/**
 * 页面验证测试脚本
 * 验证前端页面是否能正常加载和获取数据
 */

const http = require('http');

// 测试API端点
const testEndpoints = [
  {
    name: '健康检查',
    url: 'http://localhost:8789/health',
    method: 'GET'
  },
  {
    name: '用户列表（普通用户）',
    url: 'http://localhost:8789/api/admin/users?role=user&page=1&pageSize=10',
    method: 'GET'
  },
  {
    name: '用户列表（管理员）',
    url: 'http://localhost:8789/api/admin/users?role=admin&page=1&pageSize=10',
    method: 'GET'
  },
  {
    name: '用户列表（超级管理员）',
    url: 'http://localhost:8789/api/admin/users?role=superadmin&page=1&pageSize=10',
    method: 'GET'
  },
  {
    name: '角色列表',
    url: 'http://localhost:8789/api/admin/roles',
    method: 'GET'
  }
];

// 测试单个端点
async function testEndpoint(endpoint) {
  return new Promise((resolve) => {
    const url = new URL(endpoint.url);
    
    const options = {
      hostname: url.hostname,
      port: url.port,
      path: url.pathname + url.search,
      method: endpoint.method,
      headers: {
        'Content-Type': 'application/json'
      }
    };

    const req = http.request(options, (res) => {
      let data = '';
      
      res.on('data', (chunk) => {
        data += chunk;
      });
      
      res.on('end', () => {
        try {
          const jsonData = JSON.parse(data);
          resolve({
            name: endpoint.name,
            status: res.statusCode,
            success: res.statusCode === 200,
            data: jsonData,
            error: null
          });
        } catch (error) {
          resolve({
            name: endpoint.name,
            status: res.statusCode,
            success: false,
            data: null,
            error: `JSON解析错误: ${error.message}`
          });
        }
      });
    });

    req.on('error', (error) => {
      resolve({
        name: endpoint.name,
        status: 0,
        success: false,
        data: null,
        error: `请求错误: ${error.message}`
      });
    });

    req.setTimeout(5000, () => {
      req.destroy();
      resolve({
        name: endpoint.name,
        status: 0,
        success: false,
        data: null,
        error: '请求超时'
      });
    });

    req.end();
  });
}

// 运行所有测试
async function runTests() {
  console.log('🧪 开始API端点验证测试...\n');
  
  const results = [];
  
  for (const endpoint of testEndpoints) {
    console.log(`🔍 测试: ${endpoint.name}`);
    const result = await testEndpoint(endpoint);
    results.push(result);
    
    if (result.success) {
      console.log(`✅ ${result.name} - 状态: ${result.status}`);
      
      // 显示关键数据信息
      if (result.data) {
        if (result.data.data && result.data.data.users) {
          console.log(`   📊 用户数量: ${result.data.data.users.length}`);
          if (result.data.data.pagination) {
            console.log(`   📄 分页信息: 第${result.data.data.pagination.page}页，共${result.data.data.pagination.total}条`);
          }
        } else if (Array.isArray(result.data.data)) {
          console.log(`   📊 角色数量: ${result.data.data.length}`);
        } else if (result.data.status) {
          console.log(`   💚 服务状态: ${result.data.status}`);
        }
      }
    } else {
      console.log(`❌ ${result.name} - 状态: ${result.status || '失败'}`);
      if (result.error) {
        console.log(`   🚨 错误: ${result.error}`);
      }
    }
    console.log('');
  }
  
  // 生成测试报告
  console.log('📋 测试报告');
  console.log('=' * 50);
  
  const successCount = results.filter(r => r.success).length;
  const totalCount = results.length;
  
  console.log(`✅ 成功: ${successCount}/${totalCount}`);
  console.log(`❌ 失败: ${totalCount - successCount}/${totalCount}`);
  console.log(`📊 成功率: ${Math.round((successCount / totalCount) * 100)}%`);
  
  if (successCount === totalCount) {
    console.log('\n🎉 所有API端点测试通过！前端页面应该能正常工作。');
  } else {
    console.log('\n⚠️  部分API端点测试失败，可能影响前端页面功能。');
  }
  
  // 前端页面验证建议
  console.log('\n🌐 前端页面验证建议:');
  console.log('1. 普通用户管理: http://localhost:5178/superadmin/user-management');
  console.log('2. 管理员管理: http://localhost:5178/superadmin/admin-management');
  console.log('3. 角色管理: http://localhost:5178/superadmin/role-management');
  console.log('4. 审核员管理: http://localhost:5178/superadmin/reviewer-management');
  
  console.log('\n🔧 如果页面显示"组件加载失败"，请检查:');
  console.log('- 前端服务是否正常运行 (http://localhost:5178)');
  console.log('- 后端API服务是否正常运行 (http://localhost:8789)');
  console.log('- 浏览器控制台是否有JavaScript错误');
  console.log('- 网络请求是否被CORS阻止');
}

// 启动测试
runTests().catch(console.error);
