import { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle
} from '@/components/ui/dialog';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue
} from '@/components/ui/select';
import { Checkbox } from '@/components/ui/checkbox';
import { Label } from '@/components/ui/label';
import { DatePicker } from '@/components/ui/date-picker';
import { Filter, X, Save } from 'lucide-react';
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '@/components/ui/popover';
import { Badge } from '@/components/ui/badge';

// 定义筛选条件类型
interface FilterOptions {
  search: string;
  educationLevel: string;
  employmentStatus: string;
  region: string;
  dateRange: {
    startDate: Date | null;
    endDate: Date | null;
  };
  isAnonymous: boolean | null;
}

// 组件属性类型
interface FilterDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  filters: FilterOptions;
  onApplyFilters: (filters: FilterOptions) => void;
  onResetFilters: () => void;
  savedFilters?: Array<{ name: string; filters: FilterOptions }>;
  onSaveFilter?: (name: string, filters: FilterOptions) => void;
}

export default function FilterDialog({
  open,
  onOpenChange,
  filters,
  onApplyFilters,
  onResetFilters,
  savedFilters = [],
  onSaveFilter,
}: FilterDialogProps) {
  // 本地筛选状态
  const [localFilters, setLocalFilters] = useState<FilterOptions>(filters);
  const [saveFilterName, setSaveFilterName] = useState('');
  const [isSavePopoverOpen, setIsSavePopoverOpen] = useState(false);

  // 重置为当前筛选条件
  const resetToCurrentFilters = () => {
    setLocalFilters(filters);
  };

  // 组件打开时重置本地筛选状态
  useState(() => {
    if (open) {
      resetToCurrentFilters();
    }
  });

  // 处理筛选条件变化
  const handleFilterChange = (key: keyof FilterOptions, value: any) => {
    setLocalFilters(prev => ({
      ...prev,
      [key]: value,
    }));
  };

  // 处理日期范围变化
  const handleDateRangeChange = (key: 'startDate' | 'endDate', value: Date | null) => {
    setLocalFilters(prev => ({
      ...prev,
      dateRange: {
        ...prev.dateRange,
        [key]: value,
      },
    }));
  };

  // 处理应用筛选
  const handleApplyFilters = () => {
    onApplyFilters(localFilters);
    onOpenChange(false);
  };

  // 处理重置筛选
  const handleResetFilters = () => {
    const resetFilters: FilterOptions = {
      search: '',
      educationLevel: 'all',
      employmentStatus: 'all',
      region: 'all',
      dateRange: {
        startDate: null,
        endDate: null,
      },
      isAnonymous: null,
    };

    setLocalFilters(resetFilters);
    onResetFilters();
    onOpenChange(false);
  };

  // 处理保存筛选条件
  const handleSaveFilter = () => {
    if (saveFilterName.trim() && onSaveFilter) {
      onSaveFilter(saveFilterName.trim(), localFilters);
      setSaveFilterName('');
      setIsSavePopoverOpen(false);
    }
  };

  // 处理加载已保存的筛选条件
  const handleLoadSavedFilter = (savedFilter: FilterOptions) => {
    setLocalFilters(savedFilter);
  };

  // 学历选项
  const educationLevels = [
    { value: 'all', label: '全部学历' },
    { value: '高中/中专', label: '高中/中专' },
    { value: '大专', label: '大专' },
    { value: '本科', label: '本科' },
    { value: '硕士', label: '硕士' },
    { value: '博士', label: '博士' },
  ];

  // 就业状态选项
  const employmentStatuses = [
    { value: 'all', label: '全部状态' },
    { value: '已就业', label: '已就业' },
    { value: '待业中', label: '待业中' },
    { value: '继续深造', label: '继续深造' },
    { value: '其他', label: '其他' },
  ];

  // 地区选项
  const regions = [
    { value: 'all', label: '全部地区' },
    { value: '北京', label: '北京' },
    { value: '上海', label: '上海' },
    { value: '广州', label: '广州' },
    { value: '深圳', label: '深圳' },
    { value: '杭州', label: '杭州' },
    { value: '南京', label: '南京' },
    { value: '武汉', label: '武汉' },
    { value: '成都', label: '成都' },
    { value: '重庆', label: '重庆' },
    { value: '西安', label: '西安' },
    { value: '其他', label: '其他' },
  ];

  // 匿名状态选项
  const anonymousOptions = [
    { value: null, label: '全部' },
    { value: true, label: '匿名' },
    { value: false, label: '已验证' },
  ];

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-md">
        <DialogHeader>
          <DialogTitle>高级筛选</DialogTitle>
          <DialogDescription>
            设置筛选条件以查找特定的问卷回复数据
          </DialogDescription>
        </DialogHeader>

        <div className="grid gap-4 py-4">
          <div className="grid gap-2">
            <Label htmlFor="search">关键词搜索</Label>
            <Input
              id="search"
              placeholder="搜索关键词..."
              value={localFilters.search}
              onChange={(e) => handleFilterChange('search', e.target.value)}
            />
          </div>

          <div className="grid grid-cols-2 gap-4">
            <div className="grid gap-2">
              <Label htmlFor="educationLevel">学历层次</Label>
              <Select
                value={localFilters.educationLevel}
                onValueChange={(value) => handleFilterChange('educationLevel', value)}
              >
                <SelectTrigger id="educationLevel">
                  <SelectValue placeholder="选择学历层次" />
                </SelectTrigger>
                <SelectContent>
                  {educationLevels.map((level) => (
                    <SelectItem key={level.value} value={level.value}>
                      {level.label}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            <div className="grid gap-2">
              <Label htmlFor="employmentStatus">就业状态</Label>
              <Select
                value={localFilters.employmentStatus}
                onValueChange={(value) => handleFilterChange('employmentStatus', value)}
              >
                <SelectTrigger id="employmentStatus">
                  <SelectValue placeholder="选择就业状态" />
                </SelectTrigger>
                <SelectContent>
                  {employmentStatuses.map((status) => (
                    <SelectItem key={status.value} value={status.value}>
                      {status.label}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
          </div>

          <div className="grid grid-cols-2 gap-4">
            <div className="grid gap-2">
              <Label htmlFor="region">地区</Label>
              <Select
                value={localFilters.region}
                onValueChange={(value) => handleFilterChange('region', value)}
              >
                <SelectTrigger id="region">
                  <SelectValue placeholder="选择地区" />
                </SelectTrigger>
                <SelectContent>
                  {regions.map((region) => (
                    <SelectItem key={region.value} value={region.value}>
                      {region.label}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            <div className="grid gap-2">
              <Label htmlFor="isAnonymous">提交类型</Label>
              <Select
                value={localFilters.isAnonymous === null ? 'null' : localFilters.isAnonymous.toString()}
                onValueChange={(value) => handleFilterChange('isAnonymous', value === 'null' ? null : value === 'true')}
              >
                <SelectTrigger id="isAnonymous">
                  <SelectValue placeholder="选择提交类型" />
                </SelectTrigger>
                <SelectContent>
                  {anonymousOptions.map((option) => (
                    <SelectItem
                      key={option.value === null ? 'null' : option.value.toString()}
                      value={option.value === null ? 'null' : option.value.toString()}
                    >
                      {option.label}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
          </div>

          <div className="grid gap-2">
            <Label>提交日期范围</Label>
            <div className="grid grid-cols-2 gap-4">
              <div>
                <Label htmlFor="startDate" className="text-xs">开始日期</Label>
                <DatePicker
                  selected={localFilters.dateRange.startDate}
                  onSelect={(date) => handleDateRangeChange('startDate', date)}
                  placeholder="开始日期"
                />
              </div>
              <div>
                <Label htmlFor="endDate" className="text-xs">结束日期</Label>
                <DatePicker
                  selected={localFilters.dateRange.endDate}
                  onSelect={(date) => handleDateRangeChange('endDate', date)}
                  placeholder="结束日期"
                />
              </div>
            </div>
          </div>

          {savedFilters.length > 0 && (
            <div className="grid gap-2">
              <Label>已保存的筛选条件</Label>
              <div className="flex flex-wrap gap-2">
                {savedFilters.map((filter, index) => (
                  <Badge
                    key={index}
                    variant="outline"
                    className="cursor-pointer"
                    onClick={() => handleLoadSavedFilter(filter.filters)}
                  >
                    {filter.name}
                  </Badge>
                ))}
              </div>
            </div>
          )}
        </div>

        <DialogFooter className="flex justify-between">
          <div className="flex gap-2">
            <Button
              type="button"
              variant="destructive"
              onClick={handleResetFilters}
            >
              <X className="mr-2 h-4 w-4" />
              重置
            </Button>

            {onSaveFilter && (
              <Popover open={isSavePopoverOpen} onOpenChange={setIsSavePopoverOpen}>
                <PopoverTrigger asChild>
                  <Button variant="outline">
                    <Save className="mr-2 h-4 w-4" />
                    保存
                  </Button>
                </PopoverTrigger>
                <PopoverContent className="w-80">
                  <div className="grid gap-4">
                    <div className="space-y-2">
                      <h4 className="font-medium leading-none">保存筛选条件</h4>
                      <p className="text-sm text-muted-foreground">
                        为当前筛选条件设置一个名称以便日后使用
                      </p>
                    </div>
                    <div className="grid gap-2">
                      <div className="grid grid-cols-3 items-center gap-4">
                        <Label htmlFor="filterName">名称</Label>
                        <Input
                          id="filterName"
                          value={saveFilterName}
                          onChange={(e) => setSaveFilterName(e.target.value)}
                          className="col-span-2"
                        />
                      </div>
                    </div>
                    <Button onClick={handleSaveFilter}>保存筛选条件</Button>
                  </div>
                </PopoverContent>
              </Popover>
            )}
          </div>

          <Button onClick={handleApplyFilters}>
            <Filter className="mr-2 h-4 w-4" />
            应用筛选
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
