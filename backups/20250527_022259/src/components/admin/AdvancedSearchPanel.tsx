import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Badge } from '@/components/ui/badge';
import { Checkbox } from '@/components/ui/checkbox';
import { Switch } from '@/components/ui/switch';
import { Separator } from '@/components/ui/separator';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Calendar } from '@/components/ui/calendar';
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { 
  Search, 
  Filter, 
  Save, 
  Trash2, 
  Calendar as CalendarIcon,
  Tag,
  User,
  Clock,
  Settings,
  X,
  Plus,
  ChevronDown,
  ChevronUp
} from 'lucide-react';
import { useToast } from '@/components/ui/use-toast';
import { format } from 'date-fns';
import { zhCN } from 'date-fns/locale';
import {
  AdvancedSearchFilters,
  getSearchSuggestions,
  saveSearchFilter,
  getSavedSearchFilters,
  type SearchResult,
  type PendingContent
} from '@/services/contentManagementService';

interface AdvancedSearchPanelProps {
  onSearch: (filters: AdvancedSearchFilters) => void;
  onReset: () => void;
  isLoading?: boolean;
  searchResults?: SearchResult<PendingContent>;
}

/**
 * 高级搜索面板组件
 * 
 * 提供强大的搜索和过滤功能，包括：
 * - 多维度过滤条件
 * - 保存和加载搜索条件
 * - 搜索建议和自动完成
 * - 实时搜索结果统计
 */
const AdvancedSearchPanel: React.FC<AdvancedSearchPanelProps> = ({
  onSearch,
  onReset,
  isLoading = false,
  searchResults
}) => {
  const { toast } = useToast();
  const [isExpanded, setIsExpanded] = useState(false);
  const [filters, setFilters] = useState<AdvancedSearchFilters>({});
  const [savedFilters, setSavedFilters] = useState<any[]>([]);
  const [isSaveDialogOpen, setIsSaveDialogOpen] = useState(false);
  const [saveFilterName, setSaveFilterName] = useState('');
  const [suggestions, setSuggestions] = useState<string[]>([]);
  const [recentSearches, setRecentSearches] = useState<string[]>([]);

  // 内容类型选项
  const contentTypes = [
    { value: 'story', label: '故事' },
    { value: 'questionnaire', label: '问卷心声' },
    { value: 'comment', label: '评论' },
    { value: 'profile', label: '个人资料' },
    { value: 'feedback', label: '反馈' }
  ];

  // 状态选项
  const statusOptions = [
    { value: 'pending', label: '待审核' },
    { value: 'approved', label: '已通过' },
    { value: 'rejected', label: '已拒绝' },
    { value: 'flagged', label: '已标记' }
  ];

  // 优先级选项
  const priorityOptions = [
    { value: 1, label: '低优先级' },
    { value: 2, label: '普通' },
    { value: 3, label: '中等' },
    { value: 4, label: '高优先级' },
    { value: 5, label: '紧急' }
  ];

  // 时间范围选项
  const timeRangeOptions = [
    { value: 'today', label: '今天' },
    { value: 'week', label: '本周' },
    { value: 'month', label: '本月' },
    { value: 'quarter', label: '本季度' },
    { value: 'year', label: '今年' },
    { value: 'custom', label: '自定义' }
  ];

  // 排序选项
  const sortOptions = [
    { value: 'createdAt', label: '创建时间' },
    { value: 'updatedAt', label: '更新时间' },
    { value: 'priority', label: '优先级' },
    { value: 'aiConfidence', label: 'AI置信度' },
    { value: 'contentLength', label: '内容长度' }
  ];

  // 加载保存的搜索条件
  useEffect(() => {
    const loadSavedFilters = async () => {
      try {
        const response = await getSavedSearchFilters();
        if (response.success) {
          setSavedFilters(response.data);
        }
      } catch (error) {
        console.error('加载保存的搜索条件失败:', error);
      }
    };

    loadSavedFilters();
  }, []);

  // 获取搜索建议
  const handleSearchSuggestions = async (query: string) => {
    if (query.length < 2) return;

    try {
      const response = await getSearchSuggestions(query);
      if (response.success) {
        setSuggestions(response.data.suggestions);
        setRecentSearches(response.data.recentSearches);
      }
    } catch (error) {
      console.error('获取搜索建议失败:', error);
    }
  };

  // 更新过滤条件
  const updateFilter = (key: keyof AdvancedSearchFilters, value: any) => {
    setFilters(prev => ({
      ...prev,
      [key]: value
    }));
  };

  // 添加关键词
  const addKeyword = (keyword: string) => {
    if (!keyword.trim()) return;
    
    const currentKeywords = filters.contentKeywords || [];
    if (!currentKeywords.includes(keyword)) {
      updateFilter('contentKeywords', [...currentKeywords, keyword]);
    }
  };

  // 移除关键词
  const removeKeyword = (keyword: string) => {
    const currentKeywords = filters.contentKeywords || [];
    updateFilter('contentKeywords', currentKeywords.filter(k => k !== keyword));
  };

  // 执行搜索
  const handleSearch = () => {
    onSearch(filters);
  };

  // 重置搜索
  const handleReset = () => {
    setFilters({});
    onReset();
  };

  // 保存搜索条件
  const handleSaveFilter = async () => {
    if (!saveFilterName.trim()) {
      toast({
        title: '保存失败',
        description: '请输入搜索条件名称',
        variant: 'destructive',
      });
      return;
    }

    try {
      const response = await saveSearchFilter(saveFilterName, filters);
      if (response.success) {
        toast({
          title: '保存成功',
          description: '搜索条件已保存',
        });
        setIsSaveDialogOpen(false);
        setSaveFilterName('');
        
        // 重新加载保存的搜索条件
        const savedResponse = await getSavedSearchFilters();
        if (savedResponse.success) {
          setSavedFilters(savedResponse.data);
        }
      }
    } catch (error) {
      toast({
        title: '保存失败',
        description: '保存搜索条件时发生错误',
        variant: 'destructive',
      });
    }
  };

  // 加载保存的搜索条件
  const loadSavedFilter = (savedFilter: any) => {
    setFilters(savedFilter.filters);
    toast({
      title: '加载成功',
      description: `已加载搜索条件: ${savedFilter.name}`,
    });
  };

  return (
    <Card className="w-full">
      <CardHeader>
        <div className="flex items-center justify-between">
          <div>
            <CardTitle className="flex items-center gap-2">
              <Search className="h-5 w-5" />
              高级搜索
            </CardTitle>
            <CardDescription>
              使用多维度过滤条件精确搜索内容
            </CardDescription>
          </div>
          <div className="flex items-center gap-2">
            <Button
              variant="outline"
              size="sm"
              onClick={() => setIsExpanded(!isExpanded)}
            >
              {isExpanded ? <ChevronUp className="h-4 w-4" /> : <ChevronDown className="h-4 w-4" />}
              {isExpanded ? '收起' : '展开'}
            </Button>
          </div>
        </div>
      </CardHeader>

      <CardContent className="space-y-6">
        {/* 基础搜索 */}
        <div className="space-y-4">
          <div className="flex gap-2">
            <div className="flex-1">
              <Input
                placeholder="输入关键词搜索..."
                onChange={(e) => handleSearchSuggestions(e.target.value)}
                onKeyPress={(e) => {
                  if (e.key === 'Enter') {
                    addKeyword(e.currentTarget.value);
                    e.currentTarget.value = '';
                  }
                }}
              />
            </div>
            <Button onClick={handleSearch} disabled={isLoading}>
              <Search className="h-4 w-4 mr-2" />
              搜索
            </Button>
            <Button variant="outline" onClick={handleReset}>
              重置
            </Button>
          </div>

          {/* 关键词标签 */}
          {filters.contentKeywords && filters.contentKeywords.length > 0 && (
            <div className="flex flex-wrap gap-2">
              {filters.contentKeywords.map((keyword, index) => (
                <Badge key={index} variant="secondary" className="flex items-center gap-1">
                  {keyword}
                  <X 
                    className="h-3 w-3 cursor-pointer" 
                    onClick={() => removeKeyword(keyword)}
                  />
                </Badge>
              ))}
            </div>
          )}

          {/* 搜索建议 */}
          {suggestions.length > 0 && (
            <div className="space-y-2">
              <Label className="text-sm text-gray-500">搜索建议:</Label>
              <div className="flex flex-wrap gap-2">
                {suggestions.map((suggestion, index) => (
                  <Badge 
                    key={index} 
                    variant="outline" 
                    className="cursor-pointer hover:bg-gray-100"
                    onClick={() => addKeyword(suggestion)}
                  >
                    <Plus className="h-3 w-3 mr-1" />
                    {suggestion}
                  </Badge>
                ))}
              </div>
            </div>
          )}
        </div>

        {/* 高级过滤条件 */}
        {isExpanded && (
          <Tabs defaultValue="basic" className="w-full">
            <TabsList className="grid w-full grid-cols-4">
              <TabsTrigger value="basic">基础过滤</TabsTrigger>
              <TabsTrigger value="content">内容过滤</TabsTrigger>
              <TabsTrigger value="user">用户过滤</TabsTrigger>
              <TabsTrigger value="advanced">高级选项</TabsTrigger>
            </TabsList>

            <TabsContent value="basic" className="space-y-4">
              {/* 内容类型 */}
              <div className="space-y-2">
                <Label>内容类型</Label>
                <div className="flex flex-wrap gap-2">
                  {contentTypes.map((type) => (
                    <div key={type.value} className="flex items-center space-x-2">
                      <Checkbox
                        id={`type-${type.value}`}
                        checked={filters.type?.includes(type.value) || false}
                        onCheckedChange={(checked) => {
                          const currentTypes = filters.type || [];
                          if (checked) {
                            updateFilter('type', [...currentTypes, type.value]);
                          } else {
                            updateFilter('type', currentTypes.filter(t => t !== type.value));
                          }
                        }}
                      />
                      <Label htmlFor={`type-${type.value}`} className="text-sm">
                        {type.label}
                      </Label>
                    </div>
                  ))}
                </div>
              </div>

              {/* 状态 */}
              <div className="space-y-2">
                <Label>状态</Label>
                <div className="flex flex-wrap gap-2">
                  {statusOptions.map((status) => (
                    <div key={status.value} className="flex items-center space-x-2">
                      <Checkbox
                        id={`status-${status.value}`}
                        checked={filters.status?.includes(status.value) || false}
                        onCheckedChange={(checked) => {
                          const currentStatuses = filters.status || [];
                          if (checked) {
                            updateFilter('status', [...currentStatuses, status.value]);
                          } else {
                            updateFilter('status', currentStatuses.filter(s => s !== status.value));
                          }
                        }}
                      />
                      <Label htmlFor={`status-${status.value}`} className="text-sm">
                        {status.label}
                      </Label>
                    </div>
                  ))}
                </div>
              </div>

              {/* 优先级 */}
              <div className="space-y-2">
                <Label>优先级</Label>
                <div className="flex flex-wrap gap-2">
                  {priorityOptions.map((priority) => (
                    <div key={priority.value} className="flex items-center space-x-2">
                      <Checkbox
                        id={`priority-${priority.value}`}
                        checked={filters.priority?.includes(priority.value) || false}
                        onCheckedChange={(checked) => {
                          const currentPriorities = filters.priority || [];
                          if (checked) {
                            updateFilter('priority', [...currentPriorities, priority.value]);
                          } else {
                            updateFilter('priority', currentPriorities.filter(p => p !== priority.value));
                          }
                        }}
                      />
                      <Label htmlFor={`priority-${priority.value}`} className="text-sm">
                        {priority.label}
                      </Label>
                    </div>
                  ))}
                </div>
              </div>

              {/* 时间范围 */}
              <div className="space-y-2">
                <Label>时间范围</Label>
                <Select 
                  value={filters.timeRange || ''} 
                  onValueChange={(value) => updateFilter('timeRange', value)}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="选择时间范围" />
                  </SelectTrigger>
                  <SelectContent>
                    {timeRangeOptions.map((option) => (
                      <SelectItem key={option.value} value={option.value}>
                        {option.label}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
            </TabsContent>

            <TabsContent value="content" className="space-y-4">
              {/* 内容长度 */}
              <div className="space-y-2">
                <Label>内容长度</Label>
                <div className="flex gap-2 items-center">
                  <Input
                    type="number"
                    placeholder="最小长度"
                    value={filters.contentLength?.min || ''}
                    onChange={(e) => updateFilter('contentLength', {
                      ...filters.contentLength,
                      min: e.target.value ? parseInt(e.target.value) : undefined
                    })}
                  />
                  <span>-</span>
                  <Input
                    type="number"
                    placeholder="最大长度"
                    value={filters.contentLength?.max || ''}
                    onChange={(e) => updateFilter('contentLength', {
                      ...filters.contentLength,
                      max: e.target.value ? parseInt(e.target.value) : undefined
                    })}
                  />
                </div>
              </div>

              {/* 是否有标记 */}
              <div className="flex items-center space-x-2">
                <Switch
                  id="has-flags"
                  checked={filters.hasFlags || false}
                  onCheckedChange={(checked) => updateFilter('hasFlags', checked)}
                />
                <Label htmlFor="has-flags">仅显示有标记的内容</Label>
              </div>
            </TabsContent>

            <TabsContent value="user" className="space-y-4">
              {/* 是否匿名 */}
              <div className="flex items-center space-x-2">
                <Switch
                  id="is-anonymous"
                  checked={filters.isAnonymous || false}
                  onCheckedChange={(checked) => updateFilter('isAnonymous', checked)}
                />
                <Label htmlFor="is-anonymous">仅显示匿名内容</Label>
              </div>

              {/* 审核员ID */}
              <div className="space-y-2">
                <Label>审核员</Label>
                <Input
                  placeholder="输入审核员ID"
                  value={filters.reviewerId?.join(',') || ''}
                  onChange={(e) => {
                    const ids = e.target.value.split(',').map(id => id.trim()).filter(Boolean);
                    updateFilter('reviewerId', ids.length > 0 ? ids : undefined);
                  }}
                />
              </div>
            </TabsContent>

            <TabsContent value="advanced" className="space-y-4">
              {/* 排序选项 */}
              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label>排序字段</Label>
                  <Select 
                    value={filters.sortBy || ''} 
                    onValueChange={(value) => updateFilter('sortBy', value)}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="选择排序字段" />
                    </SelectTrigger>
                    <SelectContent>
                      {sortOptions.map((option) => (
                        <SelectItem key={option.value} value={option.value}>
                          {option.label}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>

                <div className="space-y-2">
                  <Label>排序方向</Label>
                  <Select 
                    value={filters.sortOrder || ''} 
                    onValueChange={(value) => updateFilter('sortOrder', value)}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="选择排序方向" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="asc">升序</SelectItem>
                      <SelectItem value="desc">降序</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>

              {/* AI置信度 */}
              <div className="space-y-2">
                <Label>AI置信度</Label>
                <div className="flex gap-2 items-center">
                  <Input
                    type="number"
                    placeholder="最小值"
                    min="0"
                    max="100"
                    value={filters.aiConfidence?.min || ''}
                    onChange={(e) => updateFilter('aiConfidence', {
                      ...filters.aiConfidence,
                      min: e.target.value ? parseInt(e.target.value) : undefined
                    })}
                  />
                  <span>-</span>
                  <Input
                    type="number"
                    placeholder="最大值"
                    min="0"
                    max="100"
                    value={filters.aiConfidence?.max || ''}
                    onChange={(e) => updateFilter('aiConfidence', {
                      ...filters.aiConfidence,
                      max: e.target.value ? parseInt(e.target.value) : undefined
                    })}
                  />
                </div>
              </div>
            </TabsContent>
          </Tabs>
        )}

        {/* 搜索结果统计 */}
        {searchResults && (
          <div className="bg-gray-50 p-4 rounded-md">
            <div className="flex items-center justify-between">
              <div className="text-sm text-gray-600">
                找到 <span className="font-medium">{searchResults.pagination.total}</span> 个结果
              </div>
              {searchResults.aggregations && (
                <div className="flex gap-4 text-xs text-gray-500">
                  <span>平均处理时间: {searchResults.aggregations.averageProcessingTime}分钟</span>
                </div>
              )}
            </div>
            
            {searchResults.aggregations && (
              <div className="mt-2 flex flex-wrap gap-2">
                {Object.entries(searchResults.aggregations.totalByType).map(([type, count]) => (
                  <Badge key={type} variant="outline" className="text-xs">
                    {type}: {count}
                  </Badge>
                ))}
              </div>
            )}
          </div>
        )}

        {/* 操作按钮 */}
        <div className="flex justify-between items-center pt-4 border-t">
          <div className="flex gap-2">
            {/* 保存搜索条件 */}
            <Dialog open={isSaveDialogOpen} onOpenChange={setIsSaveDialogOpen}>
              <DialogTrigger asChild>
                <Button variant="outline" size="sm">
                  <Save className="h-4 w-4 mr-2" />
                  保存条件
                </Button>
              </DialogTrigger>
              <DialogContent>
                <DialogHeader>
                  <DialogTitle>保存搜索条件</DialogTitle>
                  <DialogDescription>
                    为当前搜索条件命名，以便后续快速使用
                  </DialogDescription>
                </DialogHeader>
                <div className="space-y-4">
                  <div>
                    <Label htmlFor="filter-name">条件名称</Label>
                    <Input
                      id="filter-name"
                      value={saveFilterName}
                      onChange={(e) => setSaveFilterName(e.target.value)}
                      placeholder="输入搜索条件名称"
                    />
                  </div>
                </div>
                <DialogFooter>
                  <Button variant="outline" onClick={() => setIsSaveDialogOpen(false)}>
                    取消
                  </Button>
                  <Button onClick={handleSaveFilter}>
                    保存
                  </Button>
                </DialogFooter>
              </DialogContent>
            </Dialog>

            {/* 加载保存的搜索条件 */}
            {savedFilters.length > 0 && (
              <Select onValueChange={(value) => {
                const savedFilter = savedFilters.find(f => f.id === value);
                if (savedFilter) loadSavedFilter(savedFilter);
              }}>
                <SelectTrigger className="w-[200px]">
                  <SelectValue placeholder="加载保存的条件" />
                </SelectTrigger>
                <SelectContent>
                  {savedFilters.map((filter) => (
                    <SelectItem key={filter.id} value={filter.id}>
                      {filter.name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            )}
          </div>
        </div>
      </CardContent>
    </Card>
  );
};

export default AdvancedSearchPanel;
