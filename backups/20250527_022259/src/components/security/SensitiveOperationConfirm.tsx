import React, { useState, useEffect } from 'react';
import { <PERSON><PERSON>, DialogContent, DialogDescription, Di<PERSON>Footer, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Checkbox } from '@/components/ui/checkbox';
import { Badge } from '@/components/ui/badge';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import { Progress } from '@/components/ui/progress';
import {
  AlertTriangle,
  Shield,
  Clock,
  User,
  Key,
  Eye,
  EyeOff,
  CheckCircle,
  XCircle
} from 'lucide-react';
import { useToast } from '@/components/ui/use-toast';
import { auditLogger } from '@/utils/auditLog';
import { permissionManager } from '@/utils/permissions';

export type SensitiveOperationType =
  | 'delete_user'
  | 'delete_content'
  | 'batch_delete'
  | 'change_permissions'
  | 'system_config'
  | 'data_export'
  | 'data_import'
  | 'backup_restore'
  | 'security_config';

export interface SensitiveOperation {
  type: SensitiveOperationType;
  title: string;
  description: string;
  riskLevel: 'low' | 'medium' | 'high' | 'critical';
  requiredPermissions: string[];
  requiresPassword?: boolean;
  requiresReason?: boolean;
  requiresApproval?: boolean;
  approvers?: string[];
  cooldownPeriod?: number; // 冷却期（秒）
  maxAttempts?: number;
  warningMessage?: string;
  confirmationText?: string;
  additionalChecks?: Array<{
    id: string;
    label: string;
    required: boolean;
  }>;
}

export interface SensitiveOperationConfirmProps {
  isOpen: boolean;
  operation: SensitiveOperation;
  targetResource?: {
    id: string;
    name: string;
    type: string;
  };
  onConfirm: (data: {
    password?: string;
    reason?: string;
    additionalData?: Record<string, any>;
  }) => Promise<void>;
  onCancel: () => void;
}

/**
 * 敏感操作确认组件
 *
 * 特性：
 * - 多级安全验证
 * - 密码确认
 * - 操作原因记录
 * - 权限验证
 * - 冷却期控制
 * - 审计日志记录
 */
const SensitiveOperationConfirm: React.FC<SensitiveOperationConfirmProps> = ({
  isOpen,
  operation,
  targetResource,
  onConfirm,
  onCancel
}) => {
  const { toast } = useToast();

  // 表单状态
  const [password, setPassword] = useState('');
  const [reason, setReason] = useState('');
  const [confirmationText, setConfirmationText] = useState('');
  const [additionalChecks, setAdditionalChecks] = useState<Record<string, boolean>>({});
  const [showPassword, setShowPassword] = useState(false);

  // 验证状态
  const [isValidating, setIsValidating] = useState(false);
  const [validationErrors, setValidationErrors] = useState<string[]>([]);
  const [attempts, setAttempts] = useState(0);
  const [cooldownRemaining, setCooldownRemaining] = useState(0);

  // 权限检查
  const [hasPermissions, setHasPermissions] = useState(false);
  const [permissionErrors, setPermissionErrors] = useState<string[]>([]);

  // 获取风险级别颜色
  const getRiskLevelColor = (level: string) => {
    switch (level) {
      case 'low':
        return 'text-green-600 bg-green-50 border-green-200';
      case 'medium':
        return 'text-yellow-600 bg-yellow-50 border-yellow-200';
      case 'high':
        return 'text-orange-600 bg-orange-50 border-orange-200';
      case 'critical':
        return 'text-red-600 bg-red-50 border-red-200';
      default:
        return 'text-gray-600 bg-gray-50 border-gray-200';
    }
  };

  // 获取风险级别图标
  const getRiskLevelIcon = (level: string) => {
    switch (level) {
      case 'low':
        return <CheckCircle className="h-4 w-4" />;
      case 'medium':
        return <Eye className="h-4 w-4" />;
      case 'high':
        return <AlertTriangle className="h-4 w-4" />;
      case 'critical':
        return <XCircle className="h-4 w-4" />;
      default:
        return <Shield className="h-4 w-4" />;
    }
  };

  // 检查权限
  useEffect(() => {
    const checkPermissions = () => {
      const userId = localStorage.getItem('userId');
      if (!userId) {
        setHasPermissions(false);
        setPermissionErrors(['用户未登录']);
        return;
      }

      const errors: string[] = [];
      let hasAllPermissions = true;

      operation.requiredPermissions.forEach(permission => {
        if (!permissionManager.hasPermission(userId, permission)) {
          hasAllPermissions = false;
          errors.push(`缺少权限: ${permission}`);
        }
      });

      setHasPermissions(hasAllPermissions);
      setPermissionErrors(errors);
    };

    if (isOpen) {
      checkPermissions();
    }
  }, [isOpen, operation.requiredPermissions]);

  // 冷却期倒计时
  useEffect(() => {
    let interval: NodeJS.Timeout;

    if (cooldownRemaining > 0) {
      interval = setInterval(() => {
        setCooldownRemaining(prev => {
          if (prev <= 1) {
            return 0;
          }
          return prev - 1;
        });
      }, 1000);
    }

    return () => {
      if (interval) clearInterval(interval);
    };
  }, [cooldownRemaining]);

  // 重置表单
  const resetForm = () => {
    setPassword('');
    setReason('');
    setConfirmationText('');
    setAdditionalChecks({});
    setValidationErrors([]);
    setAttempts(0);
    setCooldownRemaining(0);
  };

  // 验证表单
  const validateForm = (): boolean => {
    const errors: string[] = [];

    // 检查权限
    if (!hasPermissions) {
      errors.push('权限不足，无法执行此操作');
    }

    // 检查密码
    if (operation.requiresPassword && !password) {
      errors.push('请输入密码确认');
    }

    // 检查原因
    if (operation.requiresReason && !reason.trim()) {
      errors.push('请输入操作原因');
    }

    // 检查确认文本
    if (operation.confirmationText && confirmationText !== operation.confirmationText) {
      errors.push(`请输入确认文本: "${operation.confirmationText}"`);
    }

    // 检查附加验证
    if (operation.additionalChecks) {
      operation.additionalChecks.forEach(check => {
        if (check.required && !additionalChecks[check.id]) {
          errors.push(`请确认: ${check.label}`);
        }
      });
    }

    // 检查冷却期
    if (cooldownRemaining > 0) {
      errors.push(`操作冷却中，请等待 ${cooldownRemaining} 秒`);
    }

    // 检查尝试次数
    if (operation.maxAttempts && attempts >= operation.maxAttempts) {
      errors.push('尝试次数过多，请稍后再试');
    }

    setValidationErrors(errors);
    return errors.length === 0;
  };

  // 处理确认
  const handleConfirm = async () => {
    if (!validateForm()) {
      return;
    }

    setIsValidating(true);

    try {
      // 记录操作尝试
      const userId = localStorage.getItem('userId') || 'unknown';
      const userRole = localStorage.getItem('userRole') || 'unknown';

      await auditLogger.log({
        userId,
        userRole,
        action: 'system',
        resource: 'sensitive_operation',
        resourceId: targetResource?.id,
        description: `尝试执行敏感操作: ${operation.title}`,
        level: 'warn',
        success: true,
        details: {
          operationType: operation.type,
          riskLevel: operation.riskLevel,
          targetResource,
          hasPassword: !!password,
          hasReason: !!reason,
          attempts: attempts + 1
        },
        tags: ['sensitive_operation', 'security']
      });

      // 执行操作
      await onConfirm({
        password: password || undefined,
        reason: reason || undefined,
        additionalData: {
          confirmationText,
          additionalChecks,
          attempts: attempts + 1
        }
      });

      // 记录成功
      await auditLogger.log({
        userId,
        userRole,
        action: 'system',
        resource: 'sensitive_operation',
        resourceId: targetResource?.id,
        description: `成功执行敏感操作: ${operation.title}`,
        level: operation.riskLevel === 'critical' ? 'critical' : 'warn',
        success: true,
        details: {
          operationType: operation.type,
          riskLevel: operation.riskLevel,
          targetResource,
          reason
        },
        tags: ['sensitive_operation', 'security', 'success']
      });

      toast({
        title: '操作成功',
        description: '敏感操作已成功执行',
      });

      resetForm();
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : '操作失败';

      // 记录失败
      const userId = localStorage.getItem('userId') || 'unknown';
      const userRole = localStorage.getItem('userRole') || 'unknown';

      await auditLogger.logError(
        userId,
        userRole,
        'system',
        'sensitive_operation',
        errorMessage,
        {
          operationType: operation.type,
          riskLevel: operation.riskLevel,
          targetResource,
          attempts: attempts + 1
        }
      );

      setAttempts(prev => prev + 1);

      // 设置冷却期
      if (operation.cooldownPeriod && attempts >= 2) {
        setCooldownRemaining(operation.cooldownPeriod);
      }

      toast({
        title: '操作失败',
        description: errorMessage,
        variant: 'destructive',
      });
    } finally {
      setIsValidating(false);
    }
  };

  // 处理取消
  const handleCancel = () => {
    resetForm();
    onCancel();
  };

  // 处理附加检查变化
  const handleAdditionalCheckChange = (checkId: string, checked: boolean) => {
    setAdditionalChecks(prev => ({
      ...prev,
      [checkId]: checked
    }));
  };

  return (
    <Dialog open={isOpen} onOpenChange={handleCancel}>
      <DialogContent className="max-w-2xl">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Shield className="h-5 w-5 text-red-500" />
            敏感操作确认
          </DialogTitle>
          <DialogDescription>
            此操作需要额外的安全验证，请仔细确认后继续
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-6">
          {/* 操作信息 */}
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <h3 className="text-lg font-medium">{operation.title}</h3>
              <Badge className={`${getRiskLevelColor(operation.riskLevel)} border`}>
                {getRiskLevelIcon(operation.riskLevel)}
                <span className="ml-1">
                  {operation.riskLevel === 'low' && '低风险'}
                  {operation.riskLevel === 'medium' && '中风险'}
                  {operation.riskLevel === 'high' && '高风险'}
                  {operation.riskLevel === 'critical' && '极高风险'}
                </span>
              </Badge>
            </div>

            <p className="text-gray-600">{operation.description}</p>

            {targetResource && (
              <div className="bg-gray-50 p-3 rounded-md">
                <p className="text-sm">
                  <span className="font-medium">目标资源:</span> {targetResource.name} ({targetResource.type})
                </p>
              </div>
            )}
          </div>

          {/* 警告信息 */}
          {operation.warningMessage && (
            <Alert variant="destructive">
              <AlertTriangle className="h-4 w-4" />
              <AlertTitle>警告</AlertTitle>
              <AlertDescription>{operation.warningMessage}</AlertDescription>
            </Alert>
          )}

          {/* 权限检查 */}
          {permissionErrors.length > 0 && (
            <Alert variant="destructive">
              <XCircle className="h-4 w-4" />
              <AlertTitle>权限不足</AlertTitle>
              <AlertDescription>
                <ul className="list-disc list-inside space-y-1">
                  {permissionErrors.map((error, index) => (
                    <li key={index}>{error}</li>
                  ))}
                </ul>
              </AlertDescription>
            </Alert>
          )}

          {/* 验证错误 */}
          {validationErrors.length > 0 && (
            <Alert variant="destructive">
              <AlertTriangle className="h-4 w-4" />
              <AlertTitle>验证失败</AlertTitle>
              <AlertDescription>
                <ul className="list-disc list-inside space-y-1">
                  {validationErrors.map((error, index) => (
                    <li key={index}>{error}</li>
                  ))}
                </ul>
              </AlertDescription>
            </Alert>
          )}

          {/* 冷却期显示 */}
          {cooldownRemaining > 0 && (
            <Alert>
              <Clock className="h-4 w-4" />
              <AlertTitle>操作冷却中</AlertTitle>
              <AlertDescription>
                <div className="space-y-2">
                  <p>请等待 {cooldownRemaining} 秒后再试</p>
                  <Progress value={(operation.cooldownPeriod! - cooldownRemaining) / operation.cooldownPeriod! * 100} />
                </div>
              </AlertDescription>
            </Alert>
          )}

          {/* 表单字段 */}
          <div className="space-y-4">
            {/* 密码确认 */}
            {operation.requiresPassword && (
              <div className="space-y-2">
                <Label htmlFor="password">密码确认</Label>
                <div className="relative">
                  <Input
                    id="password"
                    type={showPassword ? 'text' : 'password'}
                    value={password}
                    onChange={(e) => setPassword(e.target.value)}
                    placeholder="请输入您的密码"
                    className="pr-10"
                  />
                  <Button
                    type="button"
                    variant="ghost"
                    size="sm"
                    className="absolute right-0 top-0 h-full px-3"
                    onClick={() => setShowPassword(!showPassword)}
                  >
                    {showPassword ? <EyeOff className="h-4 w-4" /> : <Eye className="h-4 w-4" />}
                  </Button>
                </div>
              </div>
            )}

            {/* 操作原因 */}
            {operation.requiresReason && (
              <div className="space-y-2">
                <Label htmlFor="reason">操作原因</Label>
                <Textarea
                  id="reason"
                  value={reason}
                  onChange={(e) => setReason(e.target.value)}
                  placeholder="请详细说明执行此操作的原因"
                  rows={3}
                />
              </div>
            )}

            {/* 确认文本 */}
            {operation.confirmationText && (
              <div className="space-y-2">
                <Label htmlFor="confirmation">确认文本</Label>
                <Input
                  id="confirmation"
                  value={confirmationText}
                  onChange={(e) => setConfirmationText(e.target.value)}
                  placeholder={`请输入: ${operation.confirmationText}`}
                />
                <p className="text-sm text-gray-500">
                  请准确输入确认文本: <code className="bg-gray-100 px-1 rounded">{operation.confirmationText}</code>
                </p>
              </div>
            )}

            {/* 附加检查 */}
            {operation.additionalChecks && operation.additionalChecks.length > 0 && (
              <div className="space-y-3">
                <Label>附加确认</Label>
                {operation.additionalChecks.map((check) => (
                  <div key={check.id} className="flex items-center space-x-2">
                    <Checkbox
                      id={check.id}
                      checked={additionalChecks[check.id] || false}
                      onCheckedChange={(checked) => handleAdditionalCheckChange(check.id, checked as boolean)}
                    />
                    <Label htmlFor={check.id} className="text-sm">
                      {check.label}
                      {check.required && <span className="text-red-500 ml-1">*</span>}
                    </Label>
                  </div>
                ))}
              </div>
            )}
          </div>

          {/* 尝试次数显示 */}
          {attempts > 0 && (
            <div className="text-sm text-gray-500">
              尝试次数: {attempts}{operation.maxAttempts && ` / ${operation.maxAttempts}`}
            </div>
          )}
        </div>

        <DialogFooter>
          <Button variant="outline" onClick={handleCancel}>
            取消
          </Button>
          <Button
            onClick={handleConfirm}
            disabled={!hasPermissions || isValidating || cooldownRemaining > 0}
            variant="destructive"
          >
            {isValidating ? '验证中...' : '确认执行'}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};

// 预定义的敏感操作配置
export const SENSITIVE_OPERATIONS: Record<SensitiveOperationType, SensitiveOperation> = {
  delete_user: {
    type: 'delete_user',
    title: '删除用户',
    description: '永久删除用户账户及其所有相关数据',
    riskLevel: 'critical',
    requiredPermissions: ['user.manage'],
    requiresPassword: true,
    requiresReason: true,
    confirmationText: 'DELETE USER',
    cooldownPeriod: 30,
    maxAttempts: 3,
    warningMessage: '此操作不可撤销，将永久删除用户的所有数据',
    additionalChecks: [
      { id: 'backup_confirmed', label: '我已确认数据已备份', required: true },
      { id: 'user_notified', label: '我已通知相关用户', required: true }
    ]
  },
  delete_content: {
    type: 'delete_content',
    title: '删除内容',
    description: '永久删除内容数据',
    riskLevel: 'high',
    requiredPermissions: ['content.delete'],
    requiresPassword: true,
    requiresReason: true,
    cooldownPeriod: 10,
    maxAttempts: 5,
    warningMessage: '删除的内容无法恢复'
  },
  batch_delete: {
    type: 'batch_delete',
    title: '批量删除',
    description: '批量删除多个项目',
    riskLevel: 'critical',
    requiredPermissions: ['content.delete', 'content.batch'],
    requiresPassword: true,
    requiresReason: true,
    confirmationText: 'BATCH DELETE',
    cooldownPeriod: 60,
    maxAttempts: 2,
    warningMessage: '批量删除操作影响范围大，请谨慎操作',
    additionalChecks: [
      { id: 'items_reviewed', label: '我已仔细检查所有待删除项目', required: true },
      { id: 'backup_confirmed', label: '我已确认数据已备份', required: true }
    ]
  },
  change_permissions: {
    type: 'change_permissions',
    title: '修改权限',
    description: '修改用户权限或角色',
    riskLevel: 'high',
    requiredPermissions: ['user.role'],
    requiresPassword: true,
    requiresReason: true,
    cooldownPeriod: 15,
    maxAttempts: 3,
    warningMessage: '权限修改可能影响系统安全'
  },
  system_config: {
    type: 'system_config',
    title: '系统配置',
    description: '修改系统核心配置',
    riskLevel: 'critical',
    requiredPermissions: ['system.config'],
    requiresPassword: true,
    requiresReason: true,
    confirmationText: 'MODIFY CONFIG',
    cooldownPeriod: 30,
    maxAttempts: 3,
    warningMessage: '系统配置修改可能影响整个系统的运行',
    additionalChecks: [
      { id: 'config_reviewed', label: '我已仔细检查配置参数', required: true },
      { id: 'rollback_plan', label: '我已准备回滚方案', required: true }
    ]
  },
  data_export: {
    type: 'data_export',
    title: '数据导出',
    description: '导出敏感数据',
    riskLevel: 'medium',
    requiredPermissions: ['data.export'],
    requiresReason: true,
    cooldownPeriod: 5,
    maxAttempts: 5,
    warningMessage: '导出的数据包含敏感信息，请妥善保管'
  },
  data_import: {
    type: 'data_import',
    title: '数据导入',
    description: '导入外部数据到系统',
    riskLevel: 'high',
    requiredPermissions: ['data.import'],
    requiresPassword: true,
    requiresReason: true,
    cooldownPeriod: 20,
    maxAttempts: 3,
    warningMessage: '数据导入可能覆盖现有数据',
    additionalChecks: [
      { id: 'data_validated', label: '我已验证导入数据的完整性', required: true },
      { id: 'backup_confirmed', label: '我已确认数据已备份', required: true }
    ]
  },
  backup_restore: {
    type: 'backup_restore',
    title: '备份恢复',
    description: '从备份恢复系统数据',
    riskLevel: 'critical',
    requiredPermissions: ['system.backup'],
    requiresPassword: true,
    requiresReason: true,
    confirmationText: 'RESTORE BACKUP',
    cooldownPeriod: 60,
    maxAttempts: 2,
    warningMessage: '备份恢复将覆盖当前所有数据',
    additionalChecks: [
      { id: 'backup_verified', label: '我已验证备份文件的完整性', required: true },
      { id: 'downtime_approved', label: '我已获得停机维护批准', required: true },
      { id: 'users_notified', label: '我已通知所有用户', required: true }
    ]
  },
  security_config: {
    type: 'security_config',
    title: '安全配置',
    description: '修改安全相关配置',
    riskLevel: 'critical',
    requiredPermissions: ['system.config', 'system.audit'],
    requiresPassword: true,
    requiresReason: true,
    confirmationText: 'SECURITY CONFIG',
    cooldownPeriod: 45,
    maxAttempts: 2,
    warningMessage: '安全配置修改可能影响系统安全防护',
    additionalChecks: [
      { id: 'security_reviewed', label: '我已通过安全团队审核', required: true },
      { id: 'test_completed', label: '我已在测试环境验证配置', required: true }
    ]
  }
};

export default SensitiveOperationConfirm;
