import { Hono } from 'hono';
import { z } from 'zod';
import { zValidator } from '@hono/zod-validator';
import { PrismaClient } from '@prisma/client';
import { generateJWT } from '../utils/jwt';
import { adminAuth } from '../middlewares/adminAuth';
import { getPrismaClient } from '../utils/prisma';
import { mockStories, mockTags, mockResponses, mockStatistics, shouldUseMockData } from '../mock';

// Define environment interface
interface Env {
  SURVEY_KV: KVNamespace;
  DATABASE_URL: string;
  JWT_SECRET: string;
}

// Create Hono app
const app = new Hono<{ Bindings: Env }>();

// Define validation schema for admin login
const loginSchema = z.object({
  username: z.string(),
  password: z.string(),
});

// Define validation schema for story moderation
const moderateStorySchema = z.object({
  storyId: z.number(),
  status: z.enum(['approved', 'rejected']),
});

// Admin login
app.post('/login', zValidator('json', loginSchema), async (c) => {
  const { username, password } = c.req.valid('json');

  // Define admin users with different roles
  const adminUsers = [
    {
      username: 'admin1',
      password: 'admin123',
      role: 'admin',
      name: '管理员',
      id: 1,
      permissions: ['content_review', 'user_management', 'data_analysis']
    },
    {
      username: 'reviewer1',
      password: 'admin123',
      role: 'reviewer',
      name: '审核员',
      id: 2,
      permissions: ['content_review']
    },
    {
      username: 'superadmin',
      password: 'admin123',
      role: 'superadmin',
      name: '超级管理员',
      id: 3,
      permissions: ['content_review', 'user_management', 'data_analysis', 'system_config', 'security_management']
    }
  ];

  // Find matching user
  const user = adminUsers.find(u => u.username === username && u.password === password);

  if (user) {
    // Generate JWT token
    const token = await generateJWT(
      { username: user.username, role: user.role, id: user.id },
      c.env.JWT_SECRET,
      '24h'
    );

    return c.json({
      success: true,
      data: {
        token,
        user: {
          id: user.id,
          username: user.username,
          name: user.name,
          role: user.role,
          permissions: user.permissions
        }
      },
      message: '登录成功'
    });
  }

  return c.json({ success: false, error: 'Invalid credentials' }, 401);
});

// Get pending stories for moderation
app.get('/stories/pending', adminAuth, async (c) => {
  try {
    // 强制在开发环境中返回模拟数据
    console.log('Development mode: Returning mock pending stories');

    // 模拟数据
    const mockStories = [
      {
        id: 1,
        sequenceNumber: 'S-00001', // 添加顺序编号，支持到99999
        title: '我的第一份工作经历',
        content: '毕业后，我找到了一份软件开发的工作。虽然薪资不高，但是学到了很多东西。',
        author: '张三',
        isAnonymous: false,
        createdAt: new Date(Date.now() - 3 * 86400000).toISOString(),
        status: 'pending',
      },
      {
        id: 2,
        sequenceNumber: 'S-00002', // 添加顺序编号，支持到99999
        title: '转行经历分享',
        content: '我从市场营销转到了产品经理，这是一段艰难但值得的旅程。',
        author: '匿名用户',
        isAnonymous: true,
        createdAt: new Date(Date.now() - 2 * 86400000).toISOString(),
        status: 'pending',
      },
      {
        id: 3,
        sequenceNumber: 'S-00003', // 添加顺序编号，支持到99999
        title: '实习生活的酸甜苦辣',
        content: '在大公司实习的半年，让我对职场有了全新的认识。',
        author: '李四',
        isAnonymous: false,
        createdAt: new Date(Date.now() - 1 * 86400000).toISOString(),
        status: 'pending',
      },
    ];

    return c.json({
      success: true,
      stories: mockStories,
    });

    /* 暂时注释掉Prisma相关代码
    try {
      // 使用适配的Prisma客户端
      const prisma = getPrismaClient(c.env);
    */

      /* 暂时注释掉Prisma相关代码
      // Get pending stories
      const stories = await prisma.story.findMany({
        where: {
          status: 'pending',
        },
        orderBy: {
          createdAt: 'desc',
        },
      });

      // 注意：使用模拟客户端时不需要断开连接
      if (typeof prisma.$disconnect === 'function') {
        await prisma.$disconnect();
      }

      return c.json({
        success: true,
        stories,
      });
    } catch (prismaError) {
      console.error('Prisma error:', prismaError);

      // 如果Prisma出错，在开发环境中返回模拟数据
      if (c.env.ENVIRONMENT === 'development') {
        console.log('Development mode: Prisma error, returning mock data');

        // 模拟数据
        const mockStories = [
          {
            id: 1,
            sequenceNumber: 'S-00001', // 添加顺序编号，支持到99999
            title: '我的第一份工作经历',
            content: '毕业后，我找到了一份软件开发的工作。虽然薪资不高，但是学到了很多东西。',
            author: '张三',
            isAnonymous: false,
            createdAt: new Date(Date.now() - 3 * 86400000).toISOString(),
            status: 'pending',
          },
          {
            id: 2,
            sequenceNumber: 'S-00002', // 添加顺序编号，支持到99999
            title: '转行经历分享',
            content: '我从市场营销转到了产品经理，这是一段艰难但值得的旅程。',
            author: '匿名用户',
            isAnonymous: true,
            createdAt: new Date(Date.now() - 2 * 86400000).toISOString(),
            status: 'pending',
          },
          {
            id: 3,
            sequenceNumber: 'S-003', // 添加顺序编号
            title: '实习生活的酸甜苦辣',
            content: '在大公司实习的半年，让我对职场有了全新的认识。',
            author: '李四',
            isAnonymous: false,
            createdAt: new Date(Date.now() - 1 * 86400000).toISOString(),
            status: 'pending',
          },
        ];

        return c.json({
          success: true,
          stories: mockStories,
        });
      }

      throw prismaError;
    }
  } catch (error) {
    console.error('Error getting pending stories:', error);

    // 在开发环境中，即使出错也返回模拟数据
    if (c.env.ENVIRONMENT === 'development') {
      console.log('Development mode: Error, returning mock data');

      // 模拟数据
      const mockStories = [
        {
          id: 1,
          sequenceNumber: 'S-001', // 添加顺序编号
          title: '我的第一份工作经历',
          content: '毕业后，我找到了一份软件开发的工作。虽然薪资不高，但是学到了很多东西。',
          author: '张三',
          isAnonymous: false,
          createdAt: new Date(Date.now() - 3 * 86400000).toISOString(),
          status: 'pending',
        },
        {
          id: 2,
          sequenceNumber: 'S-002', // 添加顺序编号
          title: '转行经历分享',
          content: '我从市场营销转到了产品经理，这是一段艰难但值得的旅程。',
          author: '匿名用户',
          isAnonymous: true,
          createdAt: new Date(Date.now() - 2 * 86400000).toISOString(),
          status: 'pending',
        },
        {
          id: 3,
          sequenceNumber: 'S-003', // 添加顺序编号
          title: '实习生活的酸甜苦辣',
          content: '在大公司实习的半年，让我对职场有了全新的认识。',
          author: '李四',
          isAnonymous: false,
          createdAt: new Date(Date.now() - 1 * 86400000).toISOString(),
          status: 'pending',
        },
      ];

      return c.json({
        success: true,
        stories: mockStories,
      });
    }
    */
  } catch (error) {
    console.error('Error getting pending stories:', error);
    return c.json({ success: false, error: 'Failed to get pending stories' }, 500);
  }
});

// Moderate story
app.post('/stories/moderate', adminAuth, zValidator('json', moderateStorySchema), async (c) => {
  const { storyId, status } = c.req.valid('json');

  try {
    // 在开发环境中模拟审核
    if (c.env.ENVIRONMENT === 'development') {
      console.log(`Development mode: Moderating story ${storyId} with status ${status}`);

      // 模拟数据
      const mockStory = {
        id: storyId,
        sequenceNumber: `S-${storyId.toString().padStart(3, '0')}`, // 添加顺序编号
        title: '模拟故事标题',
        content: '模拟故事内容',
        author: '模拟作者',
        isAnonymous: false,
        createdAt: new Date().toISOString(),
        status: status,
      };

      return c.json({
        success: true,
        message: `Story ${status}`,
        story: mockStory,
      });
    }

    try {
      // 使用适配的Prisma客户端
      const prisma = getPrismaClient(c.env);

      // Update story status
      const story = await prisma.story.update({
        where: {
          id: storyId,
        },
        data: {
          status,
        },
      });

      // 注意：使用模拟客户端时不需要断开连接
      if (typeof prisma.$disconnect === 'function') {
        await prisma.$disconnect();
      }

      return c.json({
        success: true,
        message: `Story ${status}`,
        story,
      });
    } catch (prismaError) {
      console.error('Prisma error:', prismaError);

      // 如果Prisma出错，在开发环境中返回模拟数据
      if (c.env.ENVIRONMENT === 'development') {
        console.log('Development mode: Prisma error, returning mock data');

        // 模拟数据
        const mockStory = {
          id: storyId,
          sequenceNumber: `S-${storyId.toString().padStart(3, '0')}`, // 添加顺序编号
          title: '模拟故事标题',
          content: '模拟故事内容',
          author: '模拟作者',
          isAnonymous: false,
          createdAt: new Date().toISOString(),
          status: status,
        };

        return c.json({
          success: true,
          message: `Story ${status}`,
          story: mockStory,
        });
      }

      throw prismaError;
    }
  } catch (error) {
    console.error('Error moderating story:', error);

    // 在开发环境中，即使出错也返回模拟数据
    if (c.env.ENVIRONMENT === 'development') {
      console.log('Development mode: Error, returning mock data');

      // 模拟数据
      const mockStory = {
        id: storyId,
        sequenceNumber: `S-${storyId.toString().padStart(3, '0')}`, // 添加顺序编号
        title: '模拟故事标题',
        content: '模拟故事内容',
        author: '模拟作者',
        isAnonymous: false,
        createdAt: new Date().toISOString(),
        status: status,
      };

      return c.json({
        success: true,
        message: `Story ${status}`,
        story: mockStory,
      });
    }

    return c.json({ success: false, error: 'Failed to moderate story' }, 500);
  }
});

// Get tags
app.get('/tags', adminAuth, async (c) => {
  try {
    // 在开发环境中返回模拟数据
    console.log('Development mode: Returning mock tags');
    return c.json({
      success: true,
      tags: mockTags,
    });
  } catch (error) {
    console.error('Error getting tags:', error);
    return c.json({ success: false, error: 'Failed to get tags' }, 500);
  }
});

// Get statistics
app.get('/statistics', adminAuth, async (c) => {
  try {
    console.log('Development mode: Returning mock statistics');
    return c.json({
      success: true,
      statistics: {
        totalResponses: 8742,
        totalStories: 1254,
        totalTags: 20,
        responsesByDay: [
          { date: '2025-05-01', count: 342 },
          { date: '2025-05-02', count: 389 },
          { date: '2025-05-03', count: 412 },
          { date: '2025-05-04', count: 378 },
          { date: '2025-05-05', count: 401 },
          { date: '2025-05-06', count: 425 },
          { date: '2025-05-07', count: 398 }
        ],
        responsesByGender: [
          { gender: '男', count: 4215 },
          { gender: '女', count: 4527 }
        ],
        responsesByAge: [
          { ageRange: '18-22', count: 3245 },
          { ageRange: '23-26', count: 4127 },
          { ageRange: '27-30', count: 1012 },
          { ageRange: '31+', count: 358 }
        ],
        responsesByEducation: [
          { education: '本科', count: 5842 },
          { education: '硕士', count: 2341 },
          { education: '博士', count: 559 }
        ],
        responsesByMajor: [
          { major: '计算机科学', count: 1842 },
          { major: '软件工程', count: 1523 },
          { major: '电子信息', count: 1124 },
          { major: '通信工程', count: 987 },
          { major: '数据科学', count: 845 },
          { major: '人工智能', count: 742 },
          { major: '其他', count: 1679 }
        ],
        topTags: [
          { id: 'tag-019', name: '互联网', count: 47 },
          { id: 'tag-011', name: '本科', count: 45 },
          { id: 'tag-001', name: '软件开发', count: 42 },
          { id: 'tag-015', name: '求职经验', count: 41 },
          { id: 'tag-014', name: '实习经历', count: 38 }
        ]
      }
    });
  } catch (error) {
    console.error('Error getting statistics:', error);
    return c.json({ success: false, error: 'Failed to get statistics' }, 500);
  }
});

// Get questionnaire responses
app.get('/responses', adminAuth, async (c) => {
  try {
    console.log('Development mode: Returning mock questionnaire responses');

    // 模拟数据
    const mockResponses = [
      {
        id: 1,
        sequenceNumber: 'Q-001', // 添加顺序编号
        isAnonymous: false,
        educationLevel: '本科',
        major: '计算机科学',
        graduationYear: 2022,
        region: '北京',
        employmentStatus: '全职',
        currentIndustry: '互联网',
        currentPosition: '软件工程师',
        jobSatisfaction: '满意',
        expectedSalaryRange: '15k-20k',
        createdAt: new Date(Date.now() - 5 * 86400000).toISOString(),
        updatedAt: new Date(Date.now() - 5 * 86400000).toISOString(),
        ipAddress: '***********',
      },
      {
        id: 2,
        sequenceNumber: 'Q-002', // 添加顺序编号
        isAnonymous: true,
        educationLevel: '硕士',
        major: '金融学',
        graduationYear: 2021,
        region: '上海',
        employmentStatus: '全职',
        currentIndustry: '金融',
        currentPosition: '分析师',
        jobSatisfaction: '一般',
        expectedSalaryRange: '20k-30k',
        createdAt: new Date(Date.now() - 3 * 86400000).toISOString(),
        updatedAt: new Date(Date.now() - 3 * 86400000).toISOString(),
        ipAddress: '***********',
      },
      {
        id: 3,
        sequenceNumber: 'Q-003', // 添加顺序编号
        isAnonymous: false,
        educationLevel: '博士',
        major: '生物工程',
        graduationYear: 2020,
        region: '广州',
        employmentStatus: '兼职',
        currentIndustry: '医疗',
        currentPosition: '研究员',
        jobSatisfaction: '非常满意',
        expectedSalaryRange: '30k以上',
        createdAt: new Date(Date.now() - 1 * 86400000).toISOString(),
        updatedAt: new Date(Date.now() - 1 * 86400000).toISOString(),
        ipAddress: '***********',
      },
    ];

    return c.json({
      success: true,
      responses: mockResponses,
    });
  } catch (error) {
    console.error('Error getting questionnaire responses:', error);
    return c.json({ success: false, error: 'Failed to get questionnaire responses' }, 500);
  }
});

// JWT token generation is now handled by the utility function

export { app as admin };
