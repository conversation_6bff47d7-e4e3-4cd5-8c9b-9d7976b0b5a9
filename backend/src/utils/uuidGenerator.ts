/**
 * UUID生成工具
 * 用于轻量级匿名身份验证功能
 *
 * 该工具根据用户提供的A（11位数字）和B（4或6位数字）生成唯一标识符
 */

import CryptoJS from 'crypto-js';

/**
 * 生成UUID
 * @param a 11位数字字符串（如手机号）
 * @param b 4位或6位数字密码
 * @param salt 加盐值（从环境变量获取）
 * @returns 生成的UUID字符串
 */
export function generateUUID(a: string, b: string, salt: string): string {
  // 验证输入
  if (!isValidA(a) || !isValidB(b)) {
    throw new Error('Invalid A or B value');
  }

  // 组合原始字符串
  const raw = `${a}_${b}`;

  // 加盐处理
  const salted = raw + salt;

  // 使用SHA-256哈希
  const hash = CryptoJS.SHA256(salted).toString(CryptoJS.enc.Hex);

  // 截取前32位作为UUID
  return hash.slice(0, 32);
}

/**
 * 验证A值是否有效
 * @param a 待验证的A值
 * @returns 是否有效
 */
export function isValidA(a: string): boolean {
  // A必须是11位数字
  return /^\d{11}$/.test(a);
}

/**
 * 验证B值是否有效
 * @param b 待验证的B值
 * @returns 是否有效
 */
export function isValidB(b: string): boolean {
  // B必须是4位或6位数字
  return /^\d{4}$/.test(b) || /^\d{6}$/.test(b);
}

/**
 * 验证UUID是否匹配
 * @param storedUUID 存储的UUID
 * @param a 用户输入的A值
 * @param b 用户输入的B值
 * @param salt 加盐值
 * @returns 是否匹配
 */
export function verifyUUID(storedUUID: string, a: string, b: string, salt: string): boolean {
  try {
    const generatedUUID = generateUUID(a, b, salt);
    return storedUUID === generatedUUID;
  } catch (error) {
    return false;
  }
}
