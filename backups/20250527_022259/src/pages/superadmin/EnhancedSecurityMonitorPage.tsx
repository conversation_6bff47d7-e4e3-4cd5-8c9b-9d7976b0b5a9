import React, { useState, useEffect, useRef } from 'react';
import SuperAdminLayout from '@/components/layouts/SuperAdminLayout';
import { Card, CardContent, CardHeader, CardTitle, CardDescription, CardFooter } from '@/components/ui/card';
import { But<PERSON> } from '@/components/ui/button';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Switch } from '@/components/ui/switch';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { useToast } from '@/components/ui/use-toast';
import {
  AlertTriangle, Shield, Activity, RefreshCw, Bell,
  Lock, UserX, Eye, Clock, Zap, Wifi, Server, Database,
  HardDrive, Cpu, BarChart, Download, Play, Pause
} from 'lucide-react';
import {
  Table, TableBody, TableCaption, TableCell, TableHead,
  TableHeader, TableRow
} from '@/components/ui/table';
import {
  Dialog, DialogContent, DialogDescription, DialogFooter,
  DialogHeader, DialogTitle, DialogTrigger
} from '@/components/ui/dialog';

// 安全事件类型
interface SecurityEvent {
  id: string;
  timestamp: string;
  type: 'login_failure' | 'brute_force' | 'suspicious_activity' | 'data_access' | 'system_error';
  severity: 'low' | 'medium' | 'high' | 'critical';
  source: string;
  ip?: string;
  userId?: string;
  username?: string;
  details: string;
  status: 'new' | 'investigating' | 'resolved' | 'false_positive';
}

// 系统状态类型
interface SystemStatus {
  cpu: number;
  memory: number;
  disk: number;
  network: number;
  activeUsers: number;
  requestsPerMinute: number;
  errorRate: number;
  securityScore: number;
}

/**
 * 增强安全监控页面
 *
 * 实时监控系统异常行为，包括登录失败、暴力破解、可疑活动等
 */
const EnhancedSecurityMonitorPage: React.FC = () => {
  const { toast } = useToast();
  const [isLoading, setIsLoading] = useState(true);
  const [isMonitoring, setIsMonitoring] = useState(true);
  const [events, setEvents] = useState<SecurityEvent[]>([]);
  const [systemStatus, setSystemStatus] = useState<SystemStatus>({
    cpu: 0,
    memory: 0,
    disk: 0,
    network: 0,
    activeUsers: 0,
    requestsPerMinute: 0,
    errorRate: 0,
    securityScore: 0
  });
  const [selectedEvent, setSelectedEvent] = useState<SecurityEvent | null>(null);
  const [isEventDetailsOpen, setIsEventDetailsOpen] = useState(false);
  const [alertSettings, setAlertSettings] = useState({
    emailAlerts: true,
    smsAlerts: false,
    pushNotifications: true,
    alertThreshold: 'medium'
  });
  const monitoringInterval = useRef<number | null>(null);

  // 模拟安全事件数据
  const mockEvents: SecurityEvent[] = [
    {
      id: '1',
      timestamp: new Date(Date.now() - 2 * 60000).toISOString(),
      type: 'login_failure',
      severity: 'medium',
      source: '登录系统',
      ip: '*************',
      userId: 'admin-2',
      username: 'admin2',
      details: '多次登录失败，可能是密码尝试攻击',
      status: 'new'
    },
    {
      id: '2',
      timestamp: new Date(Date.now() - 5 * 60000).toISOString(),
      type: 'brute_force',
      severity: 'high',
      source: '登录API',
      ip: '*************',
      details: '检测到暴力破解尝试，IP已被临时封禁',
      status: 'investigating'
    },
    {
      id: '3',
      timestamp: new Date(Date.now() - 15 * 60000).toISOString(),
      type: 'suspicious_activity',
      severity: 'medium',
      source: '用户管理',
      ip: '*************',
      userId: 'admin-1',
      username: 'admin1',
      details: '短时间内大量修改用户权限',
      status: 'resolved'
    },
    {
      id: '4',
      timestamp: new Date(Date.now() - 30 * 60000).toISOString(),
      type: 'data_access',
      severity: 'critical',
      source: '数据库',
      ip: '*************',
      userId: 'system',
      username: 'system',
      details: '异常数据访问模式，可能是数据泄露尝试',
      status: 'investigating'
    },
    {
      id: '5',
      timestamp: new Date(Date.now() - 60 * 60000).toISOString(),
      type: 'system_error',
      severity: 'low',
      source: '应用服务器',
      details: '服务器内存使用率异常高',
      status: 'resolved'
    }
  ];

  // 初始化和清理
  useEffect(() => {
    fetchSecurityEvents();
    fetchSystemStatus();

    // 启动实时监控
    startMonitoring();

    return () => {
      // 清理定时器
      if (monitoringInterval.current) {
        clearInterval(monitoringInterval.current);
      }
    };
  }, []);

  // 获取安全事件
  const fetchSecurityEvents = async () => {
    try {
      setIsLoading(true);

      // 模拟网络延迟
      await new Promise(resolve => setTimeout(resolve, 1000));

      // 使用模拟数据
      setEvents(mockEvents);

      setIsLoading(false);
    } catch (error) {
      console.error('获取安全事件失败:', error);
      toast({
        variant: 'destructive',
        title: '获取数据失败',
        description: '无法加载安全事件，请稍后再试'
      });
      setIsLoading(false);
    }
  };

  // 获取系统状态
  const fetchSystemStatus = async () => {
    try {
      // 模拟网络延迟
      await new Promise(resolve => setTimeout(resolve, 500));

      // 使用模拟数据
      setSystemStatus({
        cpu: Math.floor(Math.random() * 40) + 20, // 20-60%
        memory: Math.floor(Math.random() * 30) + 40, // 40-70%
        disk: Math.floor(Math.random() * 20) + 30, // 30-50%
        network: Math.floor(Math.random() * 50) + 20, // 20-70%
        activeUsers: Math.floor(Math.random() * 100) + 50, // 50-150
        requestsPerMinute: Math.floor(Math.random() * 200) + 100, // 100-300
        errorRate: Math.floor(Math.random() * 5), // 0-5%
        securityScore: Math.floor(Math.random() * 20) + 70 // 70-90
      });
    } catch (error) {
      console.error('获取系统状态失败:', error);
    }
  };

  // 启动实时监控
  const startMonitoring = () => {
    if (monitoringInterval.current) {
      clearInterval(monitoringInterval.current);
    }

    // 每10秒更新一次系统状态
    monitoringInterval.current = window.setInterval(() => {
      if (isMonitoring) {
        fetchSystemStatus();

        // 随机添加新事件
        if (Math.random() < 0.3) { // 30%概率添加新事件
          const newEvent: SecurityEvent = {
            id: `new-${Date.now()}`,
            timestamp: new Date().toISOString(),
            type: ['login_failure', 'brute_force', 'suspicious_activity', 'data_access', 'system_error'][Math.floor(Math.random() * 5)] as any,
            severity: ['low', 'medium', 'high', 'critical'][Math.floor(Math.random() * 4)] as any,
            source: ['登录系统', '用户管理', '数据库', '应用服务器', '文件系统'][Math.floor(Math.random() * 5)],
            ip: `192.168.1.${Math.floor(Math.random() * 100) + 100}`,
            details: '新检测到的安全事件',
            status: 'new'
          };

          setEvents(prev => [newEvent, ...prev]);

          // 如果是高危或严重事件，显示通知
          if (newEvent.severity === 'high' || newEvent.severity === 'critical') {
            toast({
              variant: 'destructive',
              title: '检测到新的安全事件',
              description: `${newEvent.severity === 'critical' ? '严重' : '高危'}安全事件: ${newEvent.details}`
            });
          }
        }
      }
    }, 10000);

    setIsMonitoring(true);
  };

  // 停止实时监控
  const stopMonitoring = () => {
    if (monitoringInterval.current) {
      clearInterval(monitoringInterval.current);
      monitoringInterval.current = null;
    }

    setIsMonitoring(false);
  };

  // 处理事件状态更新
  const handleUpdateEventStatus = (id: string, status: 'investigating' | 'resolved' | 'false_positive') => {
    setEvents(prev => prev.map(event =>
      event.id === id ? { ...event, status } : event
    ));

    if (selectedEvent && selectedEvent.id === id) {
      setSelectedEvent({ ...selectedEvent, status });
    }

    toast({
      title: '状态已更新',
      description: `事件状态已更新为 ${
        status === 'investigating' ? '调查中' :
        status === 'resolved' ? '已解决' : '误报'
      }`
    });
  };

  // 获取事件图标
  const getEventIcon = (type: string) => {
    switch (type) {
      case 'login_failure':
        return <UserX className="h-4 w-4" />;
      case 'brute_force':
        return <Lock className="h-4 w-4" />;
      case 'suspicious_activity':
        return <Eye className="h-4 w-4" />;
      case 'data_access':
        return <Database className="h-4 w-4" />;
      case 'system_error':
        return <Server className="h-4 w-4" />;
      default:
        return <AlertTriangle className="h-4 w-4" />;
    }
  };

  // 获取事件严重性颜色
  const getSeverityColor = (severity: string) => {
    switch (severity) {
      case 'low':
        return 'bg-blue-100 text-blue-800';
      case 'medium':
        return 'bg-yellow-100 text-yellow-800';
      case 'high':
        return 'bg-orange-100 text-orange-800';
      case 'critical':
        return 'bg-red-100 text-red-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  // 获取事件状态颜色
  const getStatusColor = (status: string) => {
    switch (status) {
      case 'new':
        return 'bg-red-100 text-red-800';
      case 'investigating':
        return 'bg-yellow-100 text-yellow-800';
      case 'resolved':
        return 'bg-green-100 text-green-800';
      case 'false_positive':
        return 'bg-gray-100 text-gray-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  // 获取系统状态颜色
  const getStatusIndicatorColor = (value: number) => {
    if (value < 50) return 'bg-green-500';
    if (value < 80) return 'bg-yellow-500';
    return 'bg-red-500';
  };

  return (
    <SuperAdminLayout>
      <div className="container mx-auto py-6">
        <div className="flex justify-between items-center mb-6">
          <h1 className="text-2xl font-bold">增强安全监控</h1>
          <div className="flex items-center space-x-2">
            {isMonitoring ? (
              <Button variant="outline" onClick={stopMonitoring}>
                <Pause className="mr-2 h-4 w-4" />
                暂停监控
              </Button>
            ) : (
              <Button variant="default" onClick={startMonitoring}>
                <Play className="mr-2 h-4 w-4" />
                开始监控
              </Button>
            )}
            <Button variant="outline" onClick={() => {
              fetchSecurityEvents();
              fetchSystemStatus();
            }}>
              <RefreshCw className={`mr-2 h-4 w-4 ${isLoading ? 'animate-spin' : ''}`} />
              刷新
            </Button>
          </div>
        </div>

        <Tabs defaultValue="dashboard" className="space-y-6">
          <TabsList>
            <TabsTrigger value="dashboard">安全仪表盘</TabsTrigger>
            <TabsTrigger value="events">安全事件</TabsTrigger>
            <TabsTrigger value="system">系统状态</TabsTrigger>
            <TabsTrigger value="settings">监控设置</TabsTrigger>
          </TabsList>

          {/* 安全仪表盘 */}
          <TabsContent value="dashboard" className="space-y-6">
            <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
              <Card>
                <CardHeader className="pb-2">
                  <CardTitle className="text-sm font-medium">安全评分</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold">{systemStatus.securityScore}/100</div>
                  <Progress
                    value={systemStatus.securityScore}
                    className="mt-2"
                    indicatorClassName={
                      systemStatus.securityScore >= 80 ? "bg-green-500" :
                      systemStatus.securityScore >= 60 ? "bg-yellow-500" : "bg-red-500"
                    }
                  />
                  <p className="text-xs text-muted-foreground mt-2">
                    {systemStatus.securityScore >= 80 ? "系统安全状态良好" :
                     systemStatus.securityScore >= 60 ? "系统安全状态一般" : "系统安全状态需要注意"}
                  </p>
                </CardContent>
              </Card>

              <Card>
                <CardHeader className="pb-2">
                  <CardTitle className="text-sm font-medium">活跃用户</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold">{systemStatus.activeUsers}</div>
                  <div className="flex items-center mt-2">
                    <Users className="h-4 w-4 mr-1 text-muted-foreground" />
                    <span className="text-xs text-muted-foreground">当前在线用户数</span>
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardHeader className="pb-2">
                  <CardTitle className="text-sm font-medium">请求频率</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold">{systemStatus.requestsPerMinute}/分钟</div>
                  <div className="flex items-center mt-2">
                    <Activity className="h-4 w-4 mr-1 text-muted-foreground" />
                    <span className="text-xs text-muted-foreground">系统请求频率</span>
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardHeader className="pb-2">
                  <CardTitle className="text-sm font-medium">错误率</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold">{systemStatus.errorRate}%</div>
                  <div className="flex items-center mt-2">
                    <AlertTriangle className={`h-4 w-4 mr-1 ${systemStatus.errorRate > 2 ? 'text-red-500' : 'text-muted-foreground'}`} />
                    <span className="text-xs text-muted-foreground">系统错误率</span>
                  </div>
                </CardContent>
              </Card>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <Card>
                <CardHeader>
                  <CardTitle>安全事件统计</CardTitle>
                  <CardDescription>按严重程度统计的安全事件</CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    <div className="flex justify-between items-center">
                      <div className="flex items-center">
                        <div className="w-3 h-3 rounded-full bg-red-500 mr-2"></div>
                        <span>严重</span>
                      </div>
                      <span className="font-medium">{events.filter(e => e.severity === 'critical').length}</span>
                    </div>
                    <div className="flex justify-between items-center">
                      <div className="flex items-center">
                        <div className="w-3 h-3 rounded-full bg-orange-500 mr-2"></div>
                        <span>高危</span>
                      </div>
                      <span className="font-medium">{events.filter(e => e.severity === 'high').length}</span>
                    </div>
                    <div className="flex justify-between items-center">
                      <div className="flex items-center">
                        <div className="w-3 h-3 rounded-full bg-yellow-500 mr-2"></div>
                        <span>中危</span>
                      </div>
                      <span className="font-medium">{events.filter(e => e.severity === 'medium').length}</span>
                    </div>
                    <div className="flex justify-between items-center">
                      <div className="flex items-center">
                        <div className="w-3 h-3 rounded-full bg-blue-500 mr-2"></div>
                        <span>低危</span>
                      </div>
                      <span className="font-medium">{events.filter(e => e.severity === 'low').length}</span>
                    </div>
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle>事件类型分布</CardTitle>
                  <CardDescription>按类型统计的安全事件</CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    <div className="flex justify-between items-center">
                      <div className="flex items-center">
                        <UserX className="h-4 w-4 mr-2 text-blue-500" />
                        <span>登录失败</span>
                      </div>
                      <span className="font-medium">{events.filter(e => e.type === 'login_failure').length}</span>
                    </div>
                    <div className="flex justify-between items-center">
                      <div className="flex items-center">
                        <Lock className="h-4 w-4 mr-2 text-red-500" />
                        <span>暴力破解</span>
                      </div>
                      <span className="font-medium">{events.filter(e => e.type === 'brute_force').length}</span>
                    </div>
                    <div className="flex justify-between items-center">
                      <div className="flex items-center">
                        <Eye className="h-4 w-4 mr-2 text-yellow-500" />
                        <span>可疑活动</span>
                      </div>
                      <span className="font-medium">{events.filter(e => e.type === 'suspicious_activity').length}</span>
                    </div>
                    <div className="flex justify-between items-center">
                      <div className="flex items-center">
                        <Database className="h-4 w-4 mr-2 text-purple-500" />
                        <span>数据访问</span>
                      </div>
                      <span className="font-medium">{events.filter(e => e.type === 'data_access').length}</span>
                    </div>
                    <div className="flex justify-between items-center">
                      <div className="flex items-center">
                        <Server className="h-4 w-4 mr-2 text-gray-500" />
                        <span>系统错误</span>
                      </div>
                      <span className="font-medium">{events.filter(e => e.type === 'system_error').length}</span>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>

            <Card>
              <CardHeader>
                <CardTitle>最近安全事件</CardTitle>
                <CardDescription>最近发生的安全事件</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="rounded-md border">
                  <Table>
                    <TableHeader>
                      <TableRow>
                        <TableHead>类型</TableHead>
                        <TableHead>严重程度</TableHead>
                        <TableHead>来源</TableHead>
                        <TableHead>时间</TableHead>
                        <TableHead>状态</TableHead>
                        <TableHead className="text-right">操作</TableHead>
                      </TableRow>
                    </TableHeader>
                    <TableBody>
                      {events.slice(0, 5).map((event) => (
                        <TableRow key={event.id}>
                          <TableCell>
                            <div className="flex items-center">
                              {getEventIcon(event.type)}
                              <span className="ml-2">
                                {event.type === 'login_failure' ? '登录失败' :
                                 event.type === 'brute_force' ? '暴力破解' :
                                 event.type === 'suspicious_activity' ? '可疑活动' :
                                 event.type === 'data_access' ? '数据访问' : '系统错误'}
                              </span>
                            </div>
                          </TableCell>
                          <TableCell>
                            <Badge className={getSeverityColor(event.severity)}>
                              {event.severity === 'low' ? '低危' :
                               event.severity === 'medium' ? '中危' :
                               event.severity === 'high' ? '高危' : '严重'}
                            </Badge>
                          </TableCell>
                          <TableCell>{event.source}</TableCell>
                          <TableCell>{new Date(event.timestamp).toLocaleString()}</TableCell>
                          <TableCell>
                            <Badge className={getStatusColor(event.status)}>
                              {event.status === 'new' ? '新事件' :
                               event.status === 'investigating' ? '调查中' :
                               event.status === 'resolved' ? '已解决' : '误报'}
                            </Badge>
                          </TableCell>
                          <TableCell className="text-right">
                            <Button
                              variant="ghost"
                              size="sm"
                              onClick={() => {
                                setSelectedEvent(event);
                                setIsEventDetailsOpen(true);
                              }}
                            >
                              <Eye className="h-4 w-4" />
                            </Button>
                          </TableCell>
                        </TableRow>
                      ))}
                    </TableBody>
                  </Table>
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          {/* 安全事件 */}
          <TabsContent value="events" className="space-y-6">
            <Card>
              <CardHeader>
                <div className="flex justify-between items-center">
                  <div>
                    <CardTitle>安全事件列表</CardTitle>
                    <CardDescription>
                      所有检测到的安全事件
                    </CardDescription>
                  </div>
                  <div className="flex items-center space-x-2">
                    <Select
                      value={events.length > 0 ? 'all' : 'all'}
                      onValueChange={() => {}}
                    >
                      <SelectTrigger className="w-[180px]">
                        <SelectValue placeholder="筛选事件" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="all">全部事件</SelectItem>
                        <SelectItem value="new">新事件</SelectItem>
                        <SelectItem value="investigating">调查中</SelectItem>
                        <SelectItem value="resolved">已解决</SelectItem>
                        <SelectItem value="false_positive">误报</SelectItem>
                      </SelectContent>
                    </Select>
                    <Button variant="outline" size="sm">
                      <Download className="mr-2 h-4 w-4" />
                      导出
                    </Button>
                  </div>
                </div>
              </CardHeader>
              <CardContent>
                <div className="rounded-md border">
                  <Table>
                    <TableHeader>
                      <TableRow>
                        <TableHead>类型</TableHead>
                        <TableHead>严重程度</TableHead>
                        <TableHead>来源</TableHead>
                        <TableHead>IP地址</TableHead>
                        <TableHead>时间</TableHead>
                        <TableHead>状态</TableHead>
                        <TableHead className="text-right">操作</TableHead>
                      </TableRow>
                    </TableHeader>
                    <TableBody>
                      {isLoading ? (
                        <TableRow>
                          <TableCell colSpan={7} className="text-center py-8">
                            <div className="flex justify-center items-center">
                              <RefreshCw className="h-6 w-6 animate-spin mr-2" />
                              <span>加载中...</span>
                            </div>
                          </TableCell>
                        </TableRow>
                      ) : events.length === 0 ? (
                        <TableRow>
                          <TableCell colSpan={7} className="text-center py-8">
                            未找到安全事件
                          </TableCell>
                        </TableRow>
                      ) : (
                        events.map((event) => (
                          <TableRow key={event.id}>
                            <TableCell>
                              <div className="flex items-center">
                                {getEventIcon(event.type)}
                                <span className="ml-2">
                                  {event.type === 'login_failure' ? '登录失败' :
                                   event.type === 'brute_force' ? '暴力破解' :
                                   event.type === 'suspicious_activity' ? '可疑活动' :
                                   event.type === 'data_access' ? '数据访问' : '系统错误'}
                                </span>
                              </div>
                            </TableCell>
                            <TableCell>
                              <Badge className={getSeverityColor(event.severity)}>
                                {event.severity === 'low' ? '低危' :
                                 event.severity === 'medium' ? '中危' :
                                 event.severity === 'high' ? '高危' : '严重'}
                              </Badge>
                            </TableCell>
                            <TableCell>{event.source}</TableCell>
                            <TableCell>{event.ip || '-'}</TableCell>
                            <TableCell>{new Date(event.timestamp).toLocaleString()}</TableCell>
                            <TableCell>
                              <Badge className={getStatusColor(event.status)}>
                                {event.status === 'new' ? '新事件' :
                                 event.status === 'investigating' ? '调查中' :
                                 event.status === 'resolved' ? '已解决' : '误报'}
                              </Badge>
                            </TableCell>
                            <TableCell className="text-right">
                              <div className="flex justify-end space-x-1">
                                <Button
                                  variant="ghost"
                                  size="sm"
                                  onClick={() => {
                                    setSelectedEvent(event);
                                    setIsEventDetailsOpen(true);
                                  }}
                                >
                                  <Eye className="h-4 w-4" />
                                </Button>

                                {event.status === 'new' && (
                                  <Button
                                    variant="ghost"
                                    size="sm"
                                    onClick={() => handleUpdateEventStatus(event.id, 'investigating')}
                                  >
                                    <Shield className="h-4 w-4 text-yellow-500" />
                                  </Button>
                                )}

                                {(event.status === 'new' || event.status === 'investigating') && (
                                  <Button
                                    variant="ghost"
                                    size="sm"
                                    onClick={() => handleUpdateEventStatus(event.id, 'resolved')}
                                  >
                                    <Check className="h-4 w-4 text-green-500" />
                                  </Button>
                                )}
                              </div>
                            </TableCell>
                          </TableRow>
                        ))
                      )}
                    </TableBody>
                  </Table>
                </div>
              </CardContent>
            </Card>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <Card>
                <CardHeader>
                  <CardTitle>事件时间线</CardTitle>
                  <CardDescription>安全事件发生的时间线</CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="space-y-6">
                    {events.slice(0, 5).map((event, index) => (
                      <div key={event.id} className="flex">
                        <div className="flex flex-col items-center mr-4">
                          <div className={`w-3 h-3 rounded-full ${
                            event.severity === 'critical' ? 'bg-red-500' :
                            event.severity === 'high' ? 'bg-orange-500' :
                            event.severity === 'medium' ? 'bg-yellow-500' : 'bg-blue-500'
                          }`}></div>
                          {index < 4 && <div className="w-0.5 h-full bg-gray-200 mt-1"></div>}
                        </div>
                        <div className="space-y-1">
                          <div className="flex items-center">
                            <span className="font-medium">{event.type === 'login_failure' ? '登录失败' :
                             event.type === 'brute_force' ? '暴力破解' :
                             event.type === 'suspicious_activity' ? '可疑活动' :
                             event.type === 'data_access' ? '数据访问' : '系统错误'}</span>
                            <Badge className={`ml-2 ${getSeverityColor(event.severity)}`}>
                              {event.severity === 'low' ? '低危' :
                               event.severity === 'medium' ? '中危' :
                               event.severity === 'high' ? '高危' : '严重'}
                            </Badge>
                          </div>
                          <p className="text-sm text-muted-foreground">{event.details}</p>
                          <div className="flex items-center text-xs text-muted-foreground">
                            <Clock className="h-3 w-3 mr-1" />
                            <span>{new Date(event.timestamp).toLocaleString()}</span>
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle>IP地址分析</CardTitle>
                  <CardDescription>可疑IP地址活动分析</CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    {Array.from(new Set(events.filter(e => e.ip).map(e => e.ip))).slice(0, 5).map((ip) => {
                      const ipEvents = events.filter(e => e.ip === ip);
                      const criticalCount = ipEvents.filter(e => e.severity === 'critical').length;
                      const highCount = ipEvents.filter(e => e.severity === 'high').length;
                      const totalCount = ipEvents.length;
                      const riskScore = (criticalCount * 3 + highCount * 2 + totalCount) / (totalCount || 1);

                      return (
                        <div key={ip} className="p-3 border rounded-md">
                          <div className="flex justify-between items-center">
                            <div className="font-medium">{ip}</div>
                            <Badge className={
                              riskScore >= 3 ? 'bg-red-100 text-red-800' :
                              riskScore >= 2 ? 'bg-orange-100 text-orange-800' :
                              'bg-yellow-100 text-yellow-800'
                            }>
                              {riskScore >= 3 ? '高风险' : riskScore >= 2 ? '中风险' : '低风险'}
                            </Badge>
                          </div>
                          <div className="mt-2 text-sm">
                            <div className="flex justify-between">
                              <span>事件数量:</span>
                              <span>{totalCount}</span>
                            </div>
                            <div className="flex justify-between">
                              <span>严重事件:</span>
                              <span>{criticalCount}</span>
                            </div>
                            <div className="flex justify-between">
                              <span>高危事件:</span>
                              <span>{highCount}</span>
                            </div>
                          </div>
                          <div className="mt-2 flex justify-end">
                            <Button variant="outline" size="sm">
                              <Shield className="mr-2 h-4 w-4" />
                              封禁IP
                            </Button>
                          </div>
                        </div>
                      );
                    })}
                  </div>
                </CardContent>
              </Card>
            </div>
          </TabsContent>

          {/* 系统状态 */}
          <TabsContent value="system" className="space-y-6">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <Card>
                <CardHeader>
                  <CardTitle>CPU 使用率</CardTitle>
                  <CardDescription>系统 CPU 使用情况</CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="flex justify-between items-center mb-2">
                    <span className="text-2xl font-bold">{systemStatus.cpu}%</span>
                    <Badge className={
                      systemStatus.cpu < 50 ? 'bg-green-100 text-green-800' :
                      systemStatus.cpu < 80 ? 'bg-yellow-100 text-yellow-800' :
                      'bg-red-100 text-red-800'
                    }>
                      {systemStatus.cpu < 50 ? '正常' :
                       systemStatus.cpu < 80 ? '偏高' : '过高'}
                    </Badge>
                  </div>
                  <Progress
                    value={systemStatus.cpu}
                    className="h-2"
                    indicatorClassName={
                      systemStatus.cpu < 50 ? "bg-green-500" :
                      systemStatus.cpu < 80 ? "bg-yellow-500" : "bg-red-500"
                    }
                  />
                  <div className="flex justify-between text-xs text-muted-foreground mt-1">
                    <span>0%</span>
                    <span>50%</span>
                    <span>100%</span>
                  </div>
                  <div className="mt-4 text-sm text-muted-foreground">
                    <div className="flex items-center">
                      <Cpu className="h-4 w-4 mr-2" />
                      <span>处理器: Intel Xeon E5-2680 v4 @ 2.40GHz</span>
                    </div>
                    <div className="flex items-center mt-1">
                      <Activity className="h-4 w-4 mr-2" />
                      <span>核心数: 8 核心 / 16 线程</span>
                    </div>
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle>内存使用率</CardTitle>
                  <CardDescription>系统内存使用情况</CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="flex justify-between items-center mb-2">
                    <span className="text-2xl font-bold">{systemStatus.memory}%</span>
                    <Badge className={
                      systemStatus.memory < 50 ? 'bg-green-100 text-green-800' :
                      systemStatus.memory < 80 ? 'bg-yellow-100 text-yellow-800' :
                      'bg-red-100 text-red-800'
                    }>
                      {systemStatus.memory < 50 ? '正常' :
                       systemStatus.memory < 80 ? '偏高' : '过高'}
                    </Badge>
                  </div>
                  <Progress
                    value={systemStatus.memory}
                    className="h-2"
                    indicatorClassName={
                      systemStatus.memory < 50 ? "bg-green-500" :
                      systemStatus.memory < 80 ? "bg-yellow-500" : "bg-red-500"
                    }
                  />
                  <div className="flex justify-between text-xs text-muted-foreground mt-1">
                    <span>0%</span>
                    <span>50%</span>
                    <span>100%</span>
                  </div>
                  <div className="mt-4 text-sm text-muted-foreground">
                    <div className="flex items-center">
                      <Server className="h-4 w-4 mr-2" />
                      <span>总内存: 32 GB DDR4</span>
                    </div>
                    <div className="flex items-center mt-1">
                      <Activity className="h-4 w-4 mr-2" />
                      <span>已使用: {Math.round(systemStatus.memory * 32 / 100)} GB / 32 GB</span>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <Card>
                <CardHeader>
                  <CardTitle>磁盘使用率</CardTitle>
                  <CardDescription>系统磁盘使用情况</CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="flex justify-between items-center mb-2">
                    <span className="text-2xl font-bold">{systemStatus.disk}%</span>
                    <Badge className={
                      systemStatus.disk < 50 ? 'bg-green-100 text-green-800' :
                      systemStatus.disk < 80 ? 'bg-yellow-100 text-yellow-800' :
                      'bg-red-100 text-red-800'
                    }>
                      {systemStatus.disk < 50 ? '正常' :
                       systemStatus.disk < 80 ? '偏高' : '过高'}
                    </Badge>
                  </div>
                  <Progress
                    value={systemStatus.disk}
                    className="h-2"
                    indicatorClassName={
                      systemStatus.disk < 50 ? "bg-green-500" :
                      systemStatus.disk < 80 ? "bg-yellow-500" : "bg-red-500"
                    }
                  />
                  <div className="flex justify-between text-xs text-muted-foreground mt-1">
                    <span>0%</span>
                    <span>50%</span>
                    <span>100%</span>
                  </div>
                  <div className="mt-4 text-sm text-muted-foreground">
                    <div className="flex items-center">
                      <HardDrive className="h-4 w-4 mr-2" />
                      <span>总容量: 1 TB SSD</span>
                    </div>
                    <div className="flex items-center mt-1">
                      <Activity className="h-4 w-4 mr-2" />
                      <span>已使用: {Math.round(systemStatus.disk * 10)} GB / 1000 GB</span>
                    </div>
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle>网络使用率</CardTitle>
                  <CardDescription>系统网络使用情况</CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="flex justify-between items-center mb-2">
                    <span className="text-2xl font-bold">{systemStatus.network}%</span>
                    <Badge className={
                      systemStatus.network < 50 ? 'bg-green-100 text-green-800' :
                      systemStatus.network < 80 ? 'bg-yellow-100 text-yellow-800' :
                      'bg-red-100 text-red-800'
                    }>
                      {systemStatus.network < 50 ? '正常' :
                       systemStatus.network < 80 ? '偏高' : '过高'}
                    </Badge>
                  </div>
                  <Progress
                    value={systemStatus.network}
                    className="h-2"
                    indicatorClassName={
                      systemStatus.network < 50 ? "bg-green-500" :
                      systemStatus.network < 80 ? "bg-yellow-500" : "bg-red-500"
                    }
                  />
                  <div className="flex justify-between text-xs text-muted-foreground mt-1">
                    <span>0%</span>
                    <span>50%</span>
                    <span>100%</span>
                  </div>
                  <div className="mt-4 text-sm text-muted-foreground">
                    <div className="flex items-center">
                      <Wifi className="h-4 w-4 mr-2" />
                      <span>带宽: 1 Gbps</span>
                    </div>
                    <div className="flex items-center mt-1">
                      <Activity className="h-4 w-4 mr-2" />
                      <span>当前流量: {Math.round(systemStatus.network * 10)} Mbps</span>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>

            <Card>
              <CardHeader>
                <CardTitle>系统性能历史</CardTitle>
                <CardDescription>过去24小时的系统性能历史记录</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="h-80 bg-muted rounded-md flex items-center justify-center">
                  <div className="text-center">
                    <BarChart className="h-10 w-10 mx-auto mb-2 text-muted-foreground" />
                    <p className="text-muted-foreground">系统性能历史图表</p>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>系统服务状态</CardTitle>
                <CardDescription>关键系统服务的运行状态</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div className="flex justify-between items-center p-3 border rounded-md">
                    <div className="flex items-center">
                      <div className="w-3 h-3 rounded-full bg-green-500 mr-2"></div>
                      <span className="font-medium">Web 服务器</span>
                    </div>
                    <Badge className="bg-green-100 text-green-800">正常运行</Badge>
                  </div>

                  <div className="flex justify-between items-center p-3 border rounded-md">
                    <div className="flex items-center">
                      <div className="w-3 h-3 rounded-full bg-green-500 mr-2"></div>
                      <span className="font-medium">数据库服务</span>
                    </div>
                    <Badge className="bg-green-100 text-green-800">正常运行</Badge>
                  </div>

                  <div className="flex justify-between items-center p-3 border rounded-md">
                    <div className="flex items-center">
                      <div className="w-3 h-3 rounded-full bg-green-500 mr-2"></div>
                      <span className="font-medium">缓存服务</span>
                    </div>
                    <Badge className="bg-green-100 text-green-800">正常运行</Badge>
                  </div>

                  <div className="flex justify-between items-center p-3 border rounded-md">
                    <div className="flex items-center">
                      <div className="w-3 h-3 rounded-full bg-yellow-500 mr-2"></div>
                      <span className="font-medium">消息队列</span>
                    </div>
                    <Badge className="bg-yellow-100 text-yellow-800">性能降低</Badge>
                  </div>

                  <div className="flex justify-between items-center p-3 border rounded-md">
                    <div className="flex items-center">
                      <div className="w-3 h-3 rounded-full bg-green-500 mr-2"></div>
                      <span className="font-medium">搜索服务</span>
                    </div>
                    <Badge className="bg-green-100 text-green-800">正常运行</Badge>
                  </div>
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          {/* 监控设置 */}
          <TabsContent value="settings" className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle>通知设置</CardTitle>
                <CardDescription>配置安全事件的通知方式</CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="flex items-center justify-between">
                  <div className="space-y-0.5">
                    <div className="font-medium">电子邮件通知</div>
                    <div className="text-sm text-muted-foreground">
                      通过电子邮件接收安全事件通知
                    </div>
                  </div>
                  <Switch
                    checked={alertSettings.emailAlerts}
                    onCheckedChange={(checked) =>
                      setAlertSettings(prev => ({ ...prev, emailAlerts: checked }))
                    }
                  />
                </div>

                <div className="flex items-center justify-between">
                  <div className="space-y-0.5">
                    <div className="font-medium">短信通知</div>
                    <div className="text-sm text-muted-foreground">
                      通过短信接收安全事件通知
                    </div>
                  </div>
                  <Switch
                    checked={alertSettings.smsAlerts}
                    onCheckedChange={(checked) =>
                      setAlertSettings(prev => ({ ...prev, smsAlerts: checked }))
                    }
                  />
                </div>

                <div className="flex items-center justify-between">
                  <div className="space-y-0.5">
                    <div className="font-medium">推送通知</div>
                    <div className="text-sm text-muted-foreground">
                      通过浏览器推送接收安全事件通知
                    </div>
                  </div>
                  <Switch
                    checked={alertSettings.pushNotifications}
                    onCheckedChange={(checked) =>
                      setAlertSettings(prev => ({ ...prev, pushNotifications: checked }))
                    }
                  />
                </div>

                <div className="pt-4">
                  <div className="font-medium mb-2">通知阈值</div>
                  <Select
                    value={alertSettings.alertThreshold}
                    onValueChange={(value) =>
                      setAlertSettings(prev => ({ ...prev, alertThreshold: value }))
                    }
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="选择通知阈值" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="low">全部事件（低危及以上）</SelectItem>
                      <SelectItem value="medium">中危及以上事件</SelectItem>
                      <SelectItem value="high">高危及以上事件</SelectItem>
                      <SelectItem value="critical">仅严重事件</SelectItem>
                    </SelectContent>
                  </Select>
                  <p className="text-sm text-muted-foreground mt-1">
                    选择需要接收通知的事件严重程度阈值
                  </p>
                </div>
              </CardContent>
              <CardFooter>
                <Button>保存通知设置</Button>
              </CardFooter>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>监控频率</CardTitle>
                <CardDescription>配置系统监控的频率</CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="space-y-2">
                  <div className="font-medium">系统状态刷新间隔</div>
                  <Select defaultValue="10">
                    <SelectTrigger>
                      <SelectValue placeholder="选择刷新间隔" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="5">5 秒</SelectItem>
                      <SelectItem value="10">10 秒</SelectItem>
                      <SelectItem value="30">30 秒</SelectItem>
                      <SelectItem value="60">1 分钟</SelectItem>
                      <SelectItem value="300">5 分钟</SelectItem>
                    </SelectContent>
                  </Select>
                  <p className="text-sm text-muted-foreground">
                    系统状态信息的自动刷新间隔
                  </p>
                </div>

                <div className="space-y-2">
                  <div className="font-medium">安全事件检查间隔</div>
                  <Select defaultValue="60">
                    <SelectTrigger>
                      <SelectValue placeholder="选择检查间隔" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="30">30 秒</SelectItem>
                      <SelectItem value="60">1 分钟</SelectItem>
                      <SelectItem value="300">5 分钟</SelectItem>
                      <SelectItem value="600">10 分钟</SelectItem>
                      <SelectItem value="1800">30 分钟</SelectItem>
                    </SelectContent>
                  </Select>
                  <p className="text-sm text-muted-foreground">
                    检查新安全事件的间隔
                  </p>
                </div>

                <div className="space-y-2">
                  <div className="font-medium">日志保留时间</div>
                  <Select defaultValue="30">
                    <SelectTrigger>
                      <SelectValue placeholder="选择保留时间" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="7">7 天</SelectItem>
                      <SelectItem value="14">14 天</SelectItem>
                      <SelectItem value="30">30 天</SelectItem>
                      <SelectItem value="90">90 天</SelectItem>
                      <SelectItem value="180">180 天</SelectItem>
                      <SelectItem value="365">365 天</SelectItem>
                    </SelectContent>
                  </Select>
                  <p className="text-sm text-muted-foreground">
                    安全事件日志的保留时间
                  </p>
                </div>
              </CardContent>
              <CardFooter>
                <Button>保存监控设置</Button>
              </CardFooter>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>自动响应规则</CardTitle>
                <CardDescription>配置安全事件的自动响应规则</CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="flex items-center justify-between">
                  <div className="space-y-0.5">
                    <div className="font-medium">自动封禁可疑 IP</div>
                    <div className="text-sm text-muted-foreground">
                      当检测到暴力破解尝试时，自动封禁来源 IP
                    </div>
                  </div>
                  <Switch defaultChecked />
                </div>

                <div className="flex items-center justify-between">
                  <div className="space-y-0.5">
                    <div className="font-medium">自动锁定账户</div>
                    <div className="text-sm text-muted-foreground">
                      当检测到多次登录失败时，自动锁定账户
                    </div>
                  </div>
                  <Switch defaultChecked />
                </div>

                <div className="flex items-center justify-between">
                  <div className="space-y-0.5">
                    <div className="font-medium">自动限制请求频率</div>
                    <div className="text-sm text-muted-foreground">
                      当检测到异常请求频率时，自动限制请求
                    </div>
                  </div>
                  <Switch defaultChecked />
                </div>

                <div className="flex items-center justify-between">
                  <div className="space-y-0.5">
                    <div className="font-medium">自动备份数据</div>
                    <div className="text-sm text-muted-foreground">
                      当检测到异常数据访问时，自动备份相关数据
                    </div>
                  </div>
                  <Switch defaultChecked />
                </div>
              </CardContent>
              <CardFooter>
                <Button>保存响应规则</Button>
              </CardFooter>
            </Card>
          </TabsContent>
        </Tabs>

        {/* 事件详情对话框 */}
        <Dialog open={isEventDetailsOpen} onOpenChange={setIsEventDetailsOpen}>
          <DialogContent>
            {selectedEvent && (
              <>
                <DialogHeader>
                  <DialogTitle>安全事件详情</DialogTitle>
                  <DialogDescription>
                    查看安全事件的详细信息
                  </DialogDescription>
                </DialogHeader>

                <div className="py-4">
                  <div className="flex items-center mb-4">
                    {getEventIcon(selectedEvent.type)}
                    <h3 className="text-lg font-medium ml-2">
                      {selectedEvent.type === 'login_failure' ? '登录失败' :
                       selectedEvent.type === 'brute_force' ? '暴力破解' :
                       selectedEvent.type === 'suspicious_activity' ? '可疑活动' :
                       selectedEvent.type === 'data_access' ? '数据访问' : '系统错误'}
                    </h3>
                    <Badge className={`ml-2 ${getSeverityColor(selectedEvent.severity)}`}>
                      {selectedEvent.severity === 'low' ? '低危' :
                       selectedEvent.severity === 'medium' ? '中危' :
                       selectedEvent.severity === 'high' ? '高危' : '严重'}
                    </Badge>
                    <Badge className={`ml-2 ${getStatusColor(selectedEvent.status)}`}>
                      {selectedEvent.status === 'new' ? '新事件' :
                       selectedEvent.status === 'investigating' ? '调查中' :
                       selectedEvent.status === 'resolved' ? '已解决' : '误报'}
                    </Badge>
                  </div>

                  <div className="space-y-3">
                    <div className="grid grid-cols-3 gap-2">
                      <span className="font-medium">时间:</span>
                      <span className="col-span-2">{new Date(selectedEvent.timestamp).toLocaleString()}</span>
                    </div>

                    <div className="grid grid-cols-3 gap-2">
                      <span className="font-medium">来源:</span>
                      <span className="col-span-2">{selectedEvent.source}</span>
                    </div>

                    <div className="grid grid-cols-3 gap-2">
                      <span className="font-medium">详情:</span>
                      <span className="col-span-2">{selectedEvent.details}</span>
                    </div>

                    {selectedEvent.ip && (
                      <div className="grid grid-cols-3 gap-2">
                        <span className="font-medium">IP地址:</span>
                        <span className="col-span-2">{selectedEvent.ip}</span>
                      </div>
                    )}

                    {selectedEvent.username && (
                      <div className="grid grid-cols-3 gap-2">
                        <span className="font-medium">用户:</span>
                        <span className="col-span-2">{selectedEvent.username}</span>
                      </div>
                    )}
                  </div>
                </div>

                <DialogFooter>
                  {selectedEvent.status === 'new' && (
                    <Button
                      variant="outline"
                      onClick={() => handleUpdateEventStatus(selectedEvent.id, 'investigating')}
                    >
                      <Eye className="mr-2 h-4 w-4" />
                      标记为调查中
                    </Button>
                  )}

                  {(selectedEvent.status === 'new' || selectedEvent.status === 'investigating') && (
                    <>
                      <Button
                        variant="outline"
                        onClick={() => handleUpdateEventStatus(selectedEvent.id, 'resolved')}
                      >
                        <Shield className="mr-2 h-4 w-4" />
                        标记为已解决
                      </Button>

                      <Button
                        variant="outline"
                        onClick={() => handleUpdateEventStatus(selectedEvent.id, 'false_positive')}
                      >
                        <AlertTriangle className="mr-2 h-4 w-4" />
                        标记为误报
                      </Button>
                    </>
                  )}

                  <Button variant="outline" onClick={() => setIsEventDetailsOpen(false)}>
                    关闭
                  </Button>
                </DialogFooter>
              </>
            )}
          </DialogContent>
        </Dialog>
      </div>
    </SuperAdminLayout>
  );
};

export default EnhancedSecurityMonitorPage;
