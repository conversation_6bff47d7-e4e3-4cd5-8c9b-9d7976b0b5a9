import { useState } from 'react';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Tabs, Ta<PERSON>Content, Ta<PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import { X, Tag as TagIcon } from 'lucide-react';
import { cn } from '@/lib/utils';
import { STORY_TAGS, TAG_CATEGORY_LABELS, TAG_CATEGORY_COLORS, type Tag } from '@/constants/storyConstants';

interface SimpleTagSelectorProps {
  selectedTag: string;
  onTagSelect: (tagId: string) => void;
  className?: string;
  showCategories?: boolean;
}

export default function SimpleTagSelector({
  selectedTag,
  onTagSelect,
  className,
  showCategories = true,
}: SimpleTagSelectorProps) {
  const [activeCategory, setActiveCategory] = useState('all');

  // 按分类分组标签
  const categorizedTags: Record<string, Tag[]> = {
    all: STORY_TAGS.filter(tag => tag.id !== 'all'),
    job: STORY_TAGS.filter(tag => tag.category === 'job'),
    education: STORY_TAGS.filter(tag => tag.category === 'education'),
    industry: STORY_TAGS.filter(tag => tag.category === 'industry'),
    experience: STORY_TAGS.filter(tag => tag.category === 'experience'),
    other: STORY_TAGS.filter(tag => tag.category === 'other'),
  };

  // 获取标签颜色类名
  const getTagColorClass = (tag: Tag, isSelected: boolean) => {
    if (isSelected) return '';
    
    const color = tag.color || TAG_CATEGORY_COLORS[tag.category || 'other'];
    
    switch (color) {
      case 'blue':
        return 'bg-blue-50 text-blue-700 border-blue-200 hover:bg-blue-100';
      case 'green':
        return 'bg-green-50 text-green-700 border-green-200 hover:bg-green-100';
      case 'purple':
        return 'bg-purple-50 text-purple-700 border-purple-200 hover:bg-purple-100';
      case 'yellow':
        return 'bg-yellow-50 text-yellow-700 border-yellow-200 hover:bg-yellow-100';
      case 'gray':
      default:
        return 'bg-gray-50 text-gray-700 border-gray-200 hover:bg-gray-100';
    }
  };

  // 渲染标签
  const renderTags = (tags: Tag[]) => {
    return (
      <div className="flex flex-wrap gap-2">
        {tags.length === 0 ? (
          <p className="text-sm text-gray-500 w-full text-center py-4">
            该分类下暂无标签
          </p>
        ) : (
          tags.map((tag) => {
            const isSelected = selectedTag === tag.id;
            
            return (
              <Badge
                key={tag.id}
                variant={isSelected ? "default" : "outline"}
                className={cn(
                  "cursor-pointer transition-all flex items-center gap-1",
                  !isSelected && getTagColorClass(tag, isSelected)
                )}
                onClick={() => onTagSelect(tag.id)}
              >
                <TagIcon className="h-3 w-3" />
                {tag.label}
                {isSelected && <X className="h-3 w-3" />}
              </Badge>
            );
          })
        )}
      </div>
    );
  };

  return (
    <div className={cn("space-y-4", className)}>
      {/* 当前选中标签 */}
      {selectedTag && selectedTag !== 'all' && (
        <div className="flex items-center gap-2">
          <span className="text-sm font-medium">当前标签:</span>
          <Badge variant="default" className="flex items-center gap-1">
            <TagIcon className="h-3 w-3" />
            {STORY_TAGS.find(t => t.id === selectedTag)?.label}
            <X 
              className="h-3 w-3 cursor-pointer" 
              onClick={() => onTagSelect('all')}
            />
          </Badge>
        </div>
      )}

      {/* 标签分类选择 */}
      {showCategories ? (
        <Tabs value={activeCategory} onValueChange={setActiveCategory}>
          <TabsList className="w-full flex overflow-x-auto">
            <TabsTrigger value="all">全部</TabsTrigger>
            <TabsTrigger value="job">{TAG_CATEGORY_LABELS.job}</TabsTrigger>
            <TabsTrigger value="education">{TAG_CATEGORY_LABELS.education}</TabsTrigger>
            <TabsTrigger value="industry">{TAG_CATEGORY_LABELS.industry}</TabsTrigger>
            <TabsTrigger value="experience">{TAG_CATEGORY_LABELS.experience}</TabsTrigger>
            <TabsTrigger value="other">{TAG_CATEGORY_LABELS.other}</TabsTrigger>
          </TabsList>
          
          {Object.entries(categorizedTags).map(([category, tags]) => (
            <TabsContent key={category} value={category} className="mt-4">
              {renderTags(tags)}
            </TabsContent>
          ))}
        </Tabs>
      ) : (
        // 简单模式：显示所有标签
        renderTags(categorizedTags.all)
      )}

      {/* 清除选择按钮 */}
      {selectedTag && selectedTag !== 'all' && (
        <div className="flex justify-center">
          <Button
            variant="outline"
            size="sm"
            onClick={() => onTagSelect('all')}
            className="flex items-center gap-1"
          >
            <X className="h-4 w-4" />
            清除标签选择
          </Button>
        </div>
      )}
    </div>
  );
}
