import { WebSocketService } from './services/websocket.service';

// Define environment interface
export interface Env {
  // Cloudflare Services
  DB: D1Database;
  SURVEY_KV: KVNamespace;
  R2_BUCKET: R2Bucket;

  // API Keys
  RESEND_API_KEY: string;
  OPENAI_API_KEY: string;
  GROK_API_KEY: string;

  // Database & Security
  DATABASE_URL: string;
  AES_SECRET_KEY: string;
  AES_IV: string;
  JWT_SECRET: string;
  UUID_SALT: string; // 轻量级匿名身份验证的盐值

  // Environment
  ENVIRONMENT: string;
  BASE_URL: string;

  // Encryption
  ENCRYPTION_KEY: string;
  ENCRYPTION_IV: string;
}

// 扩展Hono上下文
declare module 'hono' {
  interface ContextVariableMap {
    webSocketService: WebSocketService;
  }
}
