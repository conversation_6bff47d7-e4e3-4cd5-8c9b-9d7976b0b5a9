/**
 * API客户端配置
 *
 * 统一的HTTP客户端，支持请求拦截、响应处理、错误处理等
 */

// 简化版本，不依赖 axios，使用原生 fetch
interface ApiResponse<T = any> {
  data: T;
  status: number;
  statusText: string;
  headers: Record<string, string>;
}

// API基础配置 - 自动环境检测
const getApiBaseUrl = (): string => {
  const envUrl = import.meta.env.VITE_API_BASE_URL;

  // 如果明确设置了环境变量，使用环境变量
  if (envUrl) {
    return envUrl;
  }

  // 根据当前域名自动判断环境
  const hostname = window.location.hostname;

  if (hostname === 'localhost' || hostname === '127.0.0.1') {
    // 本地开发环境
    return 'http://localhost:8788';
  } else if (hostname.includes('staging')) {
    // 预发布环境
    return 'https://college-employment-survey.aibook2099.workers.dev';
  } else {
    // 生产环境
    return 'https://college-employment-survey.aibook2099.workers.dev';
  }
};

const API_BASE_URL = getApiBaseUrl();
const API_TIMEOUT = 10000; // 10秒超时

// 环境信息日志
console.log(`🌍 API环境配置:`, {
  baseUrl: API_BASE_URL,
  hostname: window.location.hostname,
  envVar: import.meta.env.VITE_API_BASE_URL,
  nodeEnv: import.meta.env.VITE_NODE_ENV,
});

/**
 * 基于 fetch 的 API 客户端
 */
class FetchApiClient {
  private baseURL: string;
  private timeout: number;

  constructor(baseURL: string, timeout: number) {
    this.baseURL = baseURL;
    this.timeout = timeout;
  }

  private async request<T = any>(
    url: string,
    options: RequestInit = {}
  ): Promise<ApiResponse<T>> {
    const requestId = `req_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    const fullUrl = url.startsWith('http') ? url : `${this.baseURL}${url}`;

    // 默认headers
    const headers: Record<string, string> = {
      'Content-Type': 'application/json',
      'X-Request-ID': requestId,
      ...((options.headers as Record<string, string>) || {}),
    };

    // 添加认证token
    const token = localStorage.getItem('auth_token');
    if (token) {
      headers.Authorization = `Bearer ${token}`;
    }

    // 创建AbortController用于超时控制
    const controller = new AbortController();
    const timeoutId = setTimeout(() => controller.abort(), this.timeout);

    const requestOptions: RequestInit = {
      ...options,
      headers,
      signal: controller.signal,
    };

    // 记录请求日志
    console.log(`🚀 API请求 [${requestId}]:`, {
      method: options.method || 'GET',
      url: fullUrl,
      headers,
      body: options.body,
      timestamp: new Date().toISOString()
    });

    try {
      const response = await fetch(fullUrl, requestOptions);
      clearTimeout(timeoutId);

      // 解析响应头
      const responseHeaders: Record<string, string> = {};
      response.headers.forEach((value, key) => {
        responseHeaders[key] = value;
      });

      // 解析响应数据
      let data: T;
      const contentType = response.headers.get('content-type');
      if (contentType && contentType.includes('application/json')) {
        data = await response.json();
      } else {
        data = (await response.text()) as unknown as T;
      }

      const apiResponse: ApiResponse<T> = {
        data,
        status: response.status,
        statusText: response.statusText,
        headers: responseHeaders,
      };

      if (response.ok) {
        // 记录成功响应日志
        console.log(`✅ API响应 [${requestId}]:`, {
          status: response.status,
          statusText: response.statusText,
          url: fullUrl,
          data,
          timestamp: new Date().toISOString()
        });

        return apiResponse;
      } else {
        // 处理HTTP错误状态码
        this.handleErrorStatus(response.status, fullUrl);
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }
    } catch (error: any) {
      clearTimeout(timeoutId);

      // 记录错误日志
      console.error(`❌ API错误 [${requestId}]:`, {
        url: fullUrl,
        message: error.message,
        timestamp: new Date().toISOString()
      });

      if (error.name === 'AbortError') {
        throw new Error('请求超时');
      }

      throw error;
    }
  }

  private handleErrorStatus(status: number, url: string): void {
    switch (status) {
      case 401:
        localStorage.removeItem('auth_token');
        if (window.location.pathname !== '/login') {
          console.warn('⚠️ 未授权，需要重新登录');
        }
        break;
      case 403:
        console.warn('⚠️ 访问被禁止，权限不足');
        break;
      case 404:
        console.warn('⚠️ 请求的资源未找到:', url);
        break;
      case 500:
        console.error('💥 服务器内部错误');
        break;
      default:
        console.error(`💥 HTTP错误 ${status}`);
    }
  }

  async get<T = any>(url: string, params?: Record<string, any>): Promise<ApiResponse<T>> {
    let fullUrl = url;
    if (params) {
      const searchParams = new URLSearchParams();
      Object.entries(params).forEach(([key, value]) => {
        if (value !== undefined && value !== null) {
          searchParams.append(key, String(value));
        }
      });
      fullUrl += `?${searchParams.toString()}`;
    }

    return this.request<T>(fullUrl, { method: 'GET' });
  }

  async post<T = any>(url: string, data?: any): Promise<ApiResponse<T>> {
    return this.request<T>(url, {
      method: 'POST',
      body: data ? JSON.stringify(data) : undefined,
    });
  }

  async put<T = any>(url: string, data?: any): Promise<ApiResponse<T>> {
    return this.request<T>(url, {
      method: 'PUT',
      body: data ? JSON.stringify(data) : undefined,
    });
  }

  async delete<T = any>(url: string): Promise<ApiResponse<T>> {
    return this.request<T>(url, { method: 'DELETE' });
  }

  async patch<T = any>(url: string, data?: any): Promise<ApiResponse<T>> {
    return this.request<T>(url, {
      method: 'PATCH',
      body: data ? JSON.stringify(data) : undefined,
    });
  }
}

// 创建API客户端实例
export const apiClient = new FetchApiClient(API_BASE_URL, API_TIMEOUT);

// 导出便捷方法
export const api = {
  get: <T = any>(url: string, params?: Record<string, any>): Promise<ApiResponse<T>> =>
    apiClient.get(url, params),

  post: <T = any>(url: string, data?: any): Promise<ApiResponse<T>> =>
    apiClient.post(url, data),

  put: <T = any>(url: string, data?: any): Promise<ApiResponse<T>> =>
    apiClient.put(url, data),

  delete: <T = any>(url: string): Promise<ApiResponse<T>> =>
    apiClient.delete(url),

  patch: <T = any>(url: string, data?: any): Promise<ApiResponse<T>> =>
    apiClient.patch(url, data),
};

// 健康检查方法
export const healthCheck = async (): Promise<boolean> => {
  try {
    const response = await apiClient.get('/health');
    return response.status === 200;
  } catch (error) {
    console.error('🏥 健康检查失败:', error);
    return false;
  }
};

// API连接测试方法
export const testApiConnection = async (): Promise<{
  connected: boolean;
  latency?: number;
  error?: string;
}> => {
  const startTime = Date.now();

  try {
    const response = await apiClient.get('/health');
    const latency = Date.now() - startTime;

    return {
      connected: true,
      latency,
    };
  } catch (error: any) {
    return {
      connected: false,
      error: error.message || '连接失败',
    };
  }
};

// 批量请求方法
export const batchRequest = async <T = any>(
  requests: Array<{ method: 'get' | 'post' | 'put' | 'delete'; url: string; data?: any }>
): Promise<Array<ApiResponse<T> | Error>> => {
  const promises = requests.map(({ method, url, data }) => {
    switch (method) {
      case 'get':
        return apiClient.get(url).catch(err => err);
      case 'post':
        return apiClient.post(url, data).catch(err => err);
      case 'put':
        return apiClient.put(url, data).catch(err => err);
      case 'delete':
        return apiClient.delete(url).catch(err => err);
      default:
        return Promise.resolve(new Error(`不支持的HTTP方法: ${method}`));
    }
  });

  return Promise.all(promises);
};

// 导出默认实例
export default apiClient;
