/**
 * 用户管理处理程序测试
 */

import { describe, it, expect, beforeEach, vi } from 'vitest';
import { 
  handleGetUsers, 
  handleCreateUser,
  handleUpdateUser,
  handleDeleteUser,
  handleResetUserPassword
} from '../../src/handlers/user';
import { mockEnv, createMockRequest, parseResponseJson, createAuthHeader } from '../setup';
import { 
  resetAllMocks, 
  mockVerifyToken, 
  createMockUser,
  mockDbQueryResult
} from '../utils';

describe('用户管理处理程序', () => {
  beforeEach(() => {
    resetAllMocks();
    vi.clearAllMocks();

    // 默认模拟超级管理员认证
    mockVerifyToken(true, {
      sub: '1',
      role: 'superadmin',
      permissions: ['manage_users', 'manage_reviewers', 'manage_content']
    });
  });

  describe('handleGetUsers', () => {
    it('应该返回用户列表', async () => {
      // 模拟数据库查询结果
      const mockUsers = [
        createMockUser('admin', 1),
        createMockUser('admin', 2),
        createMockUser('reviewer', 3)
      ];

      mockDbQueryResult(mockEnv.DB, {
        'SELECT * FROM users': { multiple: mockUsers },
        'SELECT COUNT': { single: { total: mockUsers.length } },
        'SELECT user_id': { multiple: [
          { user_id: 1, permission: 'manage_content' },
          { user_id: 1, permission: 'manage_reviewers' },
          { user_id: 2, permission: 'manage_content' }
        ]}
      });

      // 准备请求
      const request = createMockRequest({
        method: 'GET',
        url: 'http://localhost/api/v1/admin/users',
        headers: createAuthHeader('superadmin')
      });

      // 执行处理程序
      const response = await handleGetUsers(request, mockEnv);
      
      // 验证响应
      expect(response.status).toBe(200);
      
      const data = await parseResponseJson(response);
      expect(data.success).toBe(true);
      expect(data.users).toHaveLength(3);
      expect(data.total).toBe(3);
      expect(data.users[0].permissions).toHaveLength(2);
      expect(data.users[1].permissions).toHaveLength(1);
    });

    it('应该在未授权时返回401错误', async () => {
      // 准备请求（没有认证头）
      const request = createMockRequest({
        method: 'GET',
        url: 'http://localhost/api/v1/admin/users'
      });

      // 执行处理程序
      const response = await handleGetUsers(request, mockEnv);
      
      // 验证响应
      expect(response.status).toBe(401);
      
      const data = await parseResponseJson(response);
      expect(data.success).toBe(false);
      expect(data.error).toBe('未授权');
    });

    it('应该在令牌无效时返回401错误', async () => {
      // 模拟认证失败
      mockVerifyToken(false);

      // 准备请求
      const request = createMockRequest({
        method: 'GET',
        url: 'http://localhost/api/v1/admin/users',
        headers: createAuthHeader('superadmin')
      });

      // 执行处理程序
      const response = await handleGetUsers(request, mockEnv);
      
      // 验证响应
      expect(response.status).toBe(401);
      
      const data = await parseResponseJson(response);
      expect(data.success).toBe(false);
      expect(data.error).toBe('无效的令牌');
    });

    it('应该在没有超级管理员权限时返回403错误', async () => {
      // 模拟认证为普通管理员
      mockVerifyToken(true, {
        sub: '1',
        role: 'admin',
        permissions: ['manage_content']
      });

      // 准备请求
      const request = createMockRequest({
        method: 'GET',
        url: 'http://localhost/api/v1/admin/users',
        headers: createAuthHeader('admin')
      });

      // 执行处理程序
      const response = await handleGetUsers(request, mockEnv);
      
      // 验证响应
      expect(response.status).toBe(403);
      
      const data = await parseResponseJson(response);
      expect(data.success).toBe(false);
      expect(data.error).toBe('没有权限');
    });

    it('应该支持分页和筛选', async () => {
      // 模拟数据库查询结果
      const mockUsers = [createMockUser('admin', 1)];
      mockDbQueryResult(mockEnv.DB, {
        'SELECT * FROM users': { multiple: mockUsers },
        'SELECT COUNT': { single: { total: 1 } },
        'SELECT user_id': { multiple: [] }
      });

      // 准备请求
      const request = createMockRequest({
        method: 'GET',
        url: 'http://localhost/api/v1/admin/users?role=admin&status=active&page=2&pageSize=5',
        headers: createAuthHeader('superadmin')
      });

      // 执行处理程序
      const response = await handleGetUsers(request, mockEnv);
      
      // 验证响应
      expect(response.status).toBe(200);
      
      const data = await parseResponseJson(response);
      expect(data.success).toBe(true);
      expect(data.page).toBe(2);
      expect(data.pageSize).toBe(5);
    });
  });

  describe('handleCreateUser', () => {
    it('应该成功创建用户', async () => {
      // 模拟数据库查询结果
      mockDbQueryResult(mockEnv.DB, {
        'SELECT id FROM users': { single: null }
      });

      const mockResult = { success: true, meta: { last_row_id: 1 } };
      mockEnv.DB.prepare = vi.fn().mockImplementation((sql) => {
        if (sql.includes('SELECT')) {
          return {
            bind: vi.fn().mockReturnThis(),
            first: vi.fn().mockResolvedValue(null)
          };
        } else {
          return {
            bind: vi.fn().mockReturnThis(),
            run: vi.fn().mockResolvedValue(mockResult)
          };
        }
      });

      // 准备请求
      const request = createMockRequest({
        method: 'POST',
        url: 'http://localhost/api/v1/admin/users',
        headers: createAuthHeader('superadmin'),
        body: {
          username: 'newadmin',
          password: 'password123',
          name: '新管理员',
          role: 'admin',
          permissions: ['manage_content', 'manage_reviewers']
        }
      });

      // 执行处理程序
      const response = await handleCreateUser(request, mockEnv);
      
      // 验证响应
      expect(response.status).toBe(201);
      
      const data = await parseResponseJson(response);
      expect(data.success).toBe(true);
      expect(data.userId).toBeDefined();
      expect(data.message).toBe('用户创建成功');
    });

    it('应该在用户名已存在时返回400错误', async () => {
      // 模拟数据库查询结果
      mockDbQueryResult(mockEnv.DB, {
        'SELECT id FROM users': { single: { id: 1 } }
      });

      // 准备请求
      const request = createMockRequest({
        method: 'POST',
        url: 'http://localhost/api/v1/admin/users',
        headers: createAuthHeader('superadmin'),
        body: {
          username: 'existinguser',
          password: 'password123',
          name: '新管理员',
          role: 'admin',
          permissions: ['manage_content']
        }
      });

      // 执行处理程序
      const response = await handleCreateUser(request, mockEnv);
      
      // 验证响应
      expect(response.status).toBe(400);
      
      const data = await parseResponseJson(response);
      expect(data.success).toBe(false);
      expect(data.error).toBe('用户名已存在');
    });

    it('应该在角色无效时返回400错误', async () => {
      // 准备请求（无效的角色）
      const request = createMockRequest({
        method: 'POST',
        url: 'http://localhost/api/v1/admin/users',
        headers: createAuthHeader('superadmin'),
        body: {
          username: 'newuser',
          password: 'password123',
          name: '新用户',
          role: 'invalid',
          permissions: []
        }
      });

      // 执行处理程序
      const response = await handleCreateUser(request, mockEnv);
      
      // 验证响应
      expect(response.status).toBe(400);
      
      const data = await parseResponseJson(response);
      expect(data.success).toBe(false);
      expect(data.error).toBe('无效的角色');
    });

    it('应该在缺少必要参数时返回400错误', async () => {
      // 准备请求（缺少密码）
      const request = createMockRequest({
        method: 'POST',
        url: 'http://localhost/api/v1/admin/users',
        headers: createAuthHeader('superadmin'),
        body: {
          username: 'newuser',
          name: '新用户',
          role: 'admin'
        }
      });

      // 执行处理程序
      const response = await handleCreateUser(request, mockEnv);
      
      // 验证响应
      expect(response.status).toBe(400);
      
      const data = await parseResponseJson(response);
      expect(data.success).toBe(false);
      expect(data.error).toBe('缺少必要参数');
    });
  });

  describe('handleUpdateUser', () => {
    it('应该成功更新用户', async () => {
      // 模拟数据库查询结果
      mockDbQueryResult(mockEnv.DB, {
        'SELECT * FROM users': { single: createMockUser('admin', 1) }
      });

      // 准备请求
      const request = createMockRequest({
        method: 'PUT',
        url: 'http://localhost/api/v1/admin/users/1',
        headers: createAuthHeader('superadmin'),
        body: {
          name: '更新的管理员',
          role: 'admin',
          status: 'active',
          permissions: ['manage_content', 'manage_reviewers', 'view_statistics']
        }
      });

      // 执行处理程序
      const response = await handleUpdateUser(request, mockEnv, '1');
      
      // 验证响应
      expect(response.status).toBe(200);
      
      const data = await parseResponseJson(response);
      expect(data.success).toBe(true);
      expect(data.message).toBe('用户更新成功');
    });

    it('应该在用户不存在时返回404错误', async () => {
      // 模拟数据库查询结果
      mockDbQueryResult(mockEnv.DB, {
        'SELECT * FROM users': { single: null }
      });

      // 准备请求
      const request = createMockRequest({
        method: 'PUT',
        url: 'http://localhost/api/v1/admin/users/999',
        headers: createAuthHeader('superadmin'),
        body: {
          name: '更新的管理员'
        }
      });

      // 执行处理程序
      const response = await handleUpdateUser(request, mockEnv, '999');
      
      // 验证响应
      expect(response.status).toBe(404);
      
      const data = await parseResponseJson(response);
      expect(data.success).toBe(false);
      expect(data.error).toBe('用户不存在');
    });

    it('应该在角色无效时返回400错误', async () => {
      // 模拟数据库查询结果
      mockDbQueryResult(mockEnv.DB, {
        'SELECT * FROM users': { single: createMockUser('admin', 1) }
      });

      // 准备请求（无效的角色）
      const request = createMockRequest({
        method: 'PUT',
        url: 'http://localhost/api/v1/admin/users/1',
        headers: createAuthHeader('superadmin'),
        body: {
          role: 'invalid'
        }
      });

      // 执行处理程序
      const response = await handleUpdateUser(request, mockEnv, '1');
      
      // 验证响应
      expect(response.status).toBe(400);
      
      const data = await parseResponseJson(response);
      expect(data.success).toBe(false);
      expect(data.error).toBe('无效的角色');
    });
  });
});
