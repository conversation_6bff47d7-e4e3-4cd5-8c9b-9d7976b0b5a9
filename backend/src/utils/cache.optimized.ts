/**
 * 💾 优化版缓存工具
 * 提供多层缓存策略和智能缓存管理
 */

// 缓存接口定义
interface CacheEntry<T> {
  data: T;
  timestamp: number;
  ttl: number;
  hits: number;
  size: number;
}

interface CacheStats {
  totalEntries: number;
  totalSize: number;
  hitRate: number;
  missRate: number;
  oldestEntry: number;
  newestEntry: number;
}

/**
 * 🚀 智能缓存管理器
 * 支持TTL、LRU、大小限制等多种策略
 */
export class SmartCache<T> {
  private cache = new Map<string, CacheEntry<T>>();
  private accessOrder = new Map<string, number>(); // LRU tracking
  private stats = {
    hits: 0,
    misses: 0,
    sets: 0,
    deletes: 0,
    evictions: 0
  };

  constructor(
    private maxSize: number = 100,
    private maxMemory: number = 10 * 1024 * 1024, // 10MB
    private defaultTTL: number = 5 * 60 * 1000 // 5 minutes
  ) {}

  /**
   * 设置缓存项
   */
  set(key: string, data: T, ttl?: number): void {
    const now = Date.now();
    const entryTTL = ttl || this.defaultTTL;
    const size = this.calculateSize(data);

    // 检查是否需要清理空间
    this.evictIfNeeded(size);

    const entry: CacheEntry<T> = {
      data,
      timestamp: now,
      ttl: entryTTL,
      hits: 0,
      size
    };

    this.cache.set(key, entry);
    this.accessOrder.set(key, now);
    this.stats.sets++;

    console.log(`💾 缓存设置: ${key} (TTL: ${entryTTL}ms, 大小: ${size} bytes)`);
  }

  /**
   * 获取缓存项
   */
  get(key: string): T | null {
    const entry = this.cache.get(key);
    
    if (!entry) {
      this.stats.misses++;
      return null;
    }

    const now = Date.now();
    
    // 检查是否过期
    if (now - entry.timestamp > entry.ttl) {
      this.delete(key);
      this.stats.misses++;
      return null;
    }

    // 更新访问统计
    entry.hits++;
    this.accessOrder.set(key, now);
    this.stats.hits++;

    console.log(`✅ 缓存命中: ${key} (命中次数: ${entry.hits})`);
    return entry.data;
  }

  /**
   * 删除缓存项
   */
  delete(key: string): boolean {
    const deleted = this.cache.delete(key);
    this.accessOrder.delete(key);
    
    if (deleted) {
      this.stats.deletes++;
      console.log(`🗑️ 缓存删除: ${key}`);
    }
    
    return deleted;
  }

  /**
   * 清空缓存
   */
  clear(): void {
    const count = this.cache.size;
    this.cache.clear();
    this.accessOrder.clear();
    this.resetStats();
    console.log(`🧹 缓存清空: ${count} 项已删除`);
  }

  /**
   * 检查缓存项是否存在且未过期
   */
  has(key: string): boolean {
    const entry = this.cache.get(key);
    
    if (!entry) {
      return false;
    }

    const now = Date.now();
    if (now - entry.timestamp > entry.ttl) {
      this.delete(key);
      return false;
    }

    return true;
  }

  /**
   * 获取缓存统计信息
   */
  getStats(): CacheStats & typeof this.stats {
    const entries = Array.from(this.cache.values());
    const totalSize = entries.reduce((sum, entry) => sum + entry.size, 0);
    const timestamps = entries.map(entry => entry.timestamp);

    const hitRate = this.stats.hits + this.stats.misses > 0 ? 
      (this.stats.hits / (this.stats.hits + this.stats.misses)) * 100 : 0;

    return {
      ...this.stats,
      totalEntries: this.cache.size,
      totalSize,
      hitRate: Math.round(hitRate * 100) / 100,
      missRate: Math.round((100 - hitRate) * 100) / 100,
      oldestEntry: timestamps.length > 0 ? Math.min(...timestamps) : 0,
      newestEntry: timestamps.length > 0 ? Math.max(...timestamps) : 0
    };
  }

  /**
   * 清理过期项
   */
  cleanup(): number {
    const now = Date.now();
    let cleaned = 0;

    for (const [key, entry] of this.cache.entries()) {
      if (now - entry.timestamp > entry.ttl) {
        this.delete(key);
        cleaned++;
      }
    }

    if (cleaned > 0) {
      console.log(`🧹 清理过期缓存: ${cleaned} 项`);
    }

    return cleaned;
  }

  /**
   * 根据需要驱逐缓存项
   */
  private evictIfNeeded(newEntrySize: number): void {
    // 清理过期项
    this.cleanup();

    // 检查数量限制
    while (this.cache.size >= this.maxSize) {
      this.evictLRU();
    }

    // 检查内存限制
    const currentSize = Array.from(this.cache.values())
      .reduce((sum, entry) => sum + entry.size, 0);
    
    while (currentSize + newEntrySize > this.maxMemory && this.cache.size > 0) {
      this.evictLRU();
    }
  }

  /**
   * 驱逐最近最少使用的项
   */
  private evictLRU(): void {
    if (this.accessOrder.size === 0) return;

    // 找到最旧的访问时间
    let oldestKey = '';
    let oldestTime = Infinity;

    for (const [key, time] of this.accessOrder.entries()) {
      if (time < oldestTime) {
        oldestTime = time;
        oldestKey = key;
      }
    }

    if (oldestKey) {
      console.log(`🔄 LRU驱逐: ${oldestKey}`);
      this.delete(oldestKey);
      this.stats.evictions++;
    }
  }

  /**
   * 计算数据大小
   */
  private calculateSize(data: T): number {
    try {
      return JSON.stringify(data).length * 2; // 粗略估算UTF-16字符大小
    } catch {
      return 1024; // 默认1KB
    }
  }

  /**
   * 重置统计信息
   */
  private resetStats(): void {
    this.stats = {
      hits: 0,
      misses: 0,
      sets: 0,
      deletes: 0,
      evictions: 0
    };
  }
}

/**
 * 🎯 分层缓存管理器
 * 支持多层缓存策略（L1: 内存, L2: 持久化等）
 */
export class TieredCacheManager {
  private l1Cache: SmartCache<any>; // 内存缓存
  private l2Cache?: SmartCache<any>; // 可选的二级缓存

  constructor(
    l1Config = { maxSize: 100, maxMemory: 10 * 1024 * 1024, defaultTTL: 5 * 60 * 1000 },
    l2Config?: { maxSize: number; maxMemory: number; defaultTTL: number }
  ) {
    this.l1Cache = new SmartCache(l1Config.maxSize, l1Config.maxMemory, l1Config.defaultTTL);
    
    if (l2Config) {
      this.l2Cache = new SmartCache(l2Config.maxSize, l2Config.maxMemory, l2Config.defaultTTL);
    }
  }

  /**
   * 获取数据（先L1后L2）
   */
  async get<T>(key: string): Promise<T | null> {
    // 先尝试L1缓存
    let data = this.l1Cache.get(key);
    if (data !== null) {
      console.log(`🎯 L1缓存命中: ${key}`);
      return data as T;
    }

    // 尝试L2缓存
    if (this.l2Cache) {
      data = this.l2Cache.get(key);
      if (data !== null) {
        console.log(`🎯 L2缓存命中: ${key}`);
        // 提升到L1缓存
        this.l1Cache.set(key, data);
        return data as T;
      }
    }

    console.log(`❌ 缓存未命中: ${key}`);
    return null;
  }

  /**
   * 设置数据（同时设置L1和L2）
   */
  set<T>(key: string, data: T, ttl?: number): void {
    this.l1Cache.set(key, data, ttl);
    
    if (this.l2Cache) {
      // L2缓存使用更长的TTL
      const l2TTL = ttl ? ttl * 2 : undefined;
      this.l2Cache.set(key, data, l2TTL);
    }
  }

  /**
   * 删除数据（从所有层级）
   */
  delete(key: string): boolean {
    const l1Deleted = this.l1Cache.delete(key);
    const l2Deleted = this.l2Cache?.delete(key) || false;
    
    return l1Deleted || l2Deleted;
  }

  /**
   * 清空所有缓存
   */
  clear(): void {
    this.l1Cache.clear();
    this.l2Cache?.clear();
  }

  /**
   * 获取综合统计信息
   */
  getStats() {
    return {
      l1: this.l1Cache.getStats(),
      l2: this.l2Cache?.getStats() || null
    };
  }

  /**
   * 清理所有层级的过期项
   */
  cleanup(): { l1: number; l2: number } {
    const l1Cleaned = this.l1Cache.cleanup();
    const l2Cleaned = this.l2Cache?.cleanup() || 0;
    
    return { l1: l1Cleaned, l2: l2Cleaned };
  }
}

/**
 * 🔧 缓存装饰器
 * 为函数添加自动缓存功能
 */
export function cached<T extends (...args: any[]) => any>(
  cache: SmartCache<ReturnType<T>>,
  keyGenerator?: (...args: Parameters<T>) => string,
  ttl?: number
) {
  return function (target: any, propertyName: string, descriptor: PropertyDescriptor) {
    const method = descriptor.value;

    descriptor.value = async function (...args: Parameters<T>): Promise<ReturnType<T>> {
      // 生成缓存键
      const key = keyGenerator ? 
        keyGenerator(...args) : 
        `${propertyName}_${JSON.stringify(args)}`;

      // 尝试从缓存获取
      const cached = cache.get(key);
      if (cached !== null) {
        return cached;
      }

      // 执行原方法
      const result = await method.apply(this, args);

      // 缓存结果
      cache.set(key, result, ttl);

      return result;
    };

    return descriptor;
  };
}

// 全局缓存实例
export const globalCache = new TieredCacheManager(
  { maxSize: 200, maxMemory: 20 * 1024 * 1024, defaultTTL: 5 * 60 * 1000 }, // L1: 5分钟
  { maxSize: 1000, maxMemory: 100 * 1024 * 1024, defaultTTL: 30 * 60 * 1000 } // L2: 30分钟
);

// 专用缓存实例
export const statsCache = new SmartCache(
  50, // 最多50个统计缓存项
  5 * 1024 * 1024, // 5MB
  5 * 60 * 1000 // 5分钟TTL
);

export const storyCache = new SmartCache(
  100, // 最多100个故事缓存项
  10 * 1024 * 1024, // 10MB
  2 * 60 * 1000 // 2分钟TTL
);

/**
 * 🧹 缓存维护任务
 * 定期清理过期缓存
 */
export function startCacheMaintenance(intervalMs: number = 60000): NodeJS.Timeout {
  console.log(`🔧 启动缓存维护任务 (间隔: ${intervalMs}ms)`);
  
  return setInterval(() => {
    const globalStats = globalCache.cleanup();
    const statsCleanup = statsCache.cleanup();
    const storyCleanup = storyCache.cleanup();
    
    const totalCleaned = globalStats.l1 + globalStats.l2 + statsCleanup + storyCleanup;
    
    if (totalCleaned > 0) {
      console.log(`🧹 缓存维护完成，清理 ${totalCleaned} 个过期项`);
    }
  }, intervalMs);
}

/**
 * 📊 获取所有缓存的状态报告
 */
export function getCacheReport() {
  return {
    timestamp: new Date().toISOString(),
    global: globalCache.getStats(),
    stats: statsCache.getStats(),
    story: storyCache.getStats(),
    summary: {
      totalCaches: 3,
      totalEntries: globalCache.getStats().l1.totalEntries + 
                   (globalCache.getStats().l2?.totalEntries || 0) +
                   statsCache.getStats().totalEntries +
                   storyCache.getStats().totalEntries,
      overallHitRate: calculateOverallHitRate()
    }
  };
}

/**
 * 计算总体命中率
 */
function calculateOverallHitRate(): number {
  const globalStats = globalCache.getStats();
  const statsStats = statsCache.getStats();
  const storyStats = storyCache.getStats();
  
  const totalHits = globalStats.l1.hits + 
                   (globalStats.l2?.hits || 0) + 
                   statsStats.hits + 
                   storyStats.hits;
  
  const totalRequests = globalStats.l1.hits + globalStats.l1.misses +
                       (globalStats.l2?.hits || 0) + (globalStats.l2?.misses || 0) +
                       statsStats.hits + statsStats.misses +
                       storyStats.hits + storyStats.misses;
  
  return totalRequests > 0 ? Math.round((totalHits / totalRequests) * 100 * 100) / 100 : 0;
}
