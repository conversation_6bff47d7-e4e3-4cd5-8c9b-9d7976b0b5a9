import React, { createContext, useContext, ReactNode } from 'react';
import { SnackbarProvider as NotistackProvider, useSnackbar as useNotistackSnackbar } from 'notistack';

// 创建一个空的上下文
const SnackbarContext = createContext<any>(undefined);

// 提供者属性
interface SnackbarProviderProps {
  children: ReactNode;
}

/**
 * Snackbar提供者组件
 * 
 * 提供全局Snackbar通知功能
 */
export function SnackbarProvider({ children }: SnackbarProviderProps) {
  return (
    <NotistackProvider
      maxSnack={3}
      anchorOrigin={{
        vertical: 'top',
        horizontal: 'right',
      }}
      autoHideDuration={3000}
    >
      {children}
    </NotistackProvider>
  );
}

/**
 * 使用Snackbar的钩子
 * 
 * 在组件中获取Snackbar通知功能
 */
export function useSnackbar() {
  return useNotistackSnackbar();
}
