/**
 * 测试环境设置
 */

import { vi } from 'vitest';

// 模拟环境变量
export const mockEnv: Env = {
  DB: {
    prepare: vi.fn(),
    exec: vi.fn(),
    batch: vi.fn(),
    dump: vi.fn()
  } as any,
  JWT_SECRET: 'test-jwt-secret',
  ADMIN_USERNAME: 'admin',
  ADMIN_PASSWORD: 'admin123',
  SUPERADMIN_USERNAME: 'superadmin',
  SUPERADMIN_PASSWORD: 'superadmin123'
};

// 模拟请求对象
export function createMockRequest(options: {
  method?: string;
  url?: string;
  headers?: Record<string, string>;
  body?: any;
}): Request {
  const {
    method = 'GET',
    url = 'http://localhost/api/v1/test',
    headers = {},
    body = null
  } = options;

  const headersObj = new Headers();
  Object.entries(headers).forEach(([key, value]) => {
    headersObj.append(key, value);
  });

  const requestInit: RequestInit = {
    method,
    headers: headersObj
  };

  if (body) {
    if (typeof body === 'string') {
      requestInit.body = body;
    } else {
      requestInit.body = JSON.stringify(body);
    }
  }

  return new Request(url, requestInit);
}

// 模拟数据库结果
export function createMockDbResult(options: {
  success?: boolean;
  results?: any[];
  meta?: any;
}): any {
  const {
    success = true,
    results = [],
    meta = { last_row_id: 1 }
  } = options;

  return {
    success,
    results,
    meta
  };
}

// 模拟数据库查询
export function mockDbQuery(result: any) {
  const bindFn = vi.fn().mockReturnThis();
  const firstFn = vi.fn().mockResolvedValue(result);
  const allFn = vi.fn().mockResolvedValue({ results: Array.isArray(result) ? result : [result] });
  const runFn = vi.fn().mockResolvedValue({ success: true, meta: { last_row_id: 1 } });

  const prepareResult = {
    bind: bindFn,
    first: firstFn,
    all: allFn,
    run: runFn
  };

  mockEnv.DB.prepare = vi.fn().mockReturnValue(prepareResult);

  return {
    bind: bindFn,
    first: firstFn,
    all: allFn,
    run: runFn
  };
}

// 解析响应JSON
export async function parseResponseJson(response: Response): Promise<any> {
  const text = await response.text();
  try {
    return JSON.parse(text);
  } catch (error) {
    console.error('Failed to parse response JSON:', text);
    throw error;
  }
}

// 创建JWT令牌
export function createMockJwt(payload: any): string {
  // 这只是一个模拟的JWT，不是真正的JWT
  return `mock.jwt.${Buffer.from(JSON.stringify(payload)).toString('base64')}`;
}

// 模拟认证头部
export function createAuthHeader(role: 'admin' | 'superadmin' | 'reviewer'): Record<string, string> {
  const payload = {
    sub: '1',
    role,
    permissions: role === 'admin' ? ['manage_reviewers', 'manage_content'] :
                 role === 'superadmin' ? ['manage_users', 'manage_reviewers', 'manage_content'] :
                 ['review_content']
  };

  return {
    'Authorization': `Bearer ${createMockJwt(payload)}`
  };
}
