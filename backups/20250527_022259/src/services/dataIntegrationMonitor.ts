/**
 * 数据对接监测中心
 * 
 * 负责监控前端与后端数据库的完整对接流程
 * 包括请求验证、API连接、数据库操作、环境兼容性等
 */

// 监测事件类型
export enum MonitorEventType {
  REQUEST_START = 'request_start',
  REQUEST_SUCCESS = 'request_success',
  REQUEST_ERROR = 'request_error',
  DATA_VALIDATION = 'data_validation',
  DATABASE_OPERATION = 'database_operation',
  ENVIRONMENT_CHECK = 'environment_check',
  PERFORMANCE_METRIC = 'performance_metric'
}

// 监测数据接口
export interface MonitorEvent {
  id: string;
  type: MonitorEventType;
  timestamp: string;
  page: string;
  component: string;
  details: {
    endpoint?: string;
    method?: string;
    requestData?: any;
    responseData?: any;
    error?: string;
    duration?: number;
    dataValidation?: {
      isValid: boolean;
      errors: string[];
      warnings: string[];
    };
    databaseOperation?: {
      operation: 'CREATE' | 'READ' | 'UPDATE' | 'DELETE';
      table: string;
      affectedRows?: number;
      queryTime?: number;
    };
    environmentInfo?: {
      nodeVersion?: string;
      browserInfo?: string;
      apiVersion?: string;
      databaseVersion?: string;
    };
    performanceMetrics?: {
      responseTime: number;
      memoryUsage?: number;
      cpuUsage?: number;
      networkLatency?: number;
    };
  };
}

// 监测配置
export interface MonitorConfig {
  enableLogging: boolean;
  enablePerformanceTracking: boolean;
  enableDataValidation: boolean;
  enableEnvironmentChecks: boolean;
  logLevel: 'debug' | 'info' | 'warn' | 'error';
  maxLogEntries: number;
  autoCleanupInterval: number; // 毫秒
}

// 默认配置
const DEFAULT_CONFIG: MonitorConfig = {
  enableLogging: true,
  enablePerformanceTracking: true,
  enableDataValidation: true,
  enableEnvironmentChecks: true,
  logLevel: 'info',
  maxLogEntries: 1000,
  autoCleanupInterval: 5 * 60 * 1000 // 5分钟
};

// 数据验证规则
export interface ValidationRule {
  field: string;
  type: 'string' | 'number' | 'boolean' | 'array' | 'object';
  required: boolean;
  validator?: (value: any) => boolean;
  errorMessage?: string;
}

// 页面数据结构定义
export const PAGE_DATA_SCHEMAS = {
  questionnaire: {
    request: [
      { field: 'educationLevel', type: 'string', required: true },
      { field: 'major', type: 'string', required: true },
      { field: 'graduationYear', type: 'number', required: true },
      { field: 'region', type: 'string', required: true }
    ] as ValidationRule[],
    response: [
      { field: 'success', type: 'boolean', required: true },
      { field: 'statistics', type: 'object', required: true },
      { field: 'statistics.totalResponses', type: 'number', required: true },
      { field: 'statistics.educationLevels', type: 'array', required: true }
    ] as ValidationRule[]
  },
  storyWall: {
    request: [
      { field: 'page', type: 'number', required: true },
      { field: 'pageSize', type: 'number', required: true },
      { field: 'sortBy', type: 'string', required: true }
    ] as ValidationRule[],
    response: [
      { field: 'success', type: 'boolean', required: true },
      { field: 'stories', type: 'array', required: true },
      { field: 'totalPages', type: 'number', required: true },
      { field: 'currentPage', type: 'number', required: true }
    ] as ValidationRule[]
  },
  visualization: {
    response: [
      { field: 'success', type: 'boolean', required: true },
      { field: 'stats', type: 'object', required: true },
      { field: 'stats.educationLevels', type: 'array', required: true },
      { field: 'stats.industries', type: 'array', required: true }
    ] as ValidationRule[]
  }
};

class DataIntegrationMonitor {
  private events: MonitorEvent[] = [];
  private config: MonitorConfig;
  private cleanupTimer?: NodeJS.Timeout;

  constructor(config: Partial<MonitorConfig> = {}) {
    this.config = { ...DEFAULT_CONFIG, ...config };
    this.startAutoCleanup();
  }

  /**
   * 记录监测事件
   */
  logEvent(event: Omit<MonitorEvent, 'id' | 'timestamp'>): void {
    if (!this.config.enableLogging) return;

    const monitorEvent: MonitorEvent = {
      ...event,
      id: this.generateEventId(),
      timestamp: new Date().toISOString()
    };

    this.events.push(monitorEvent);
    this.enforceMaxEntries();

    // 控制台输出（根据日志级别）
    this.logToConsole(monitorEvent);
  }

  /**
   * 验证数据结构
   */
  validateData(data: any, rules: ValidationRule[], context: string): {
    isValid: boolean;
    errors: string[];
    warnings: string[];
  } {
    if (!this.config.enableDataValidation) {
      return { isValid: true, errors: [], warnings: [] };
    }

    const errors: string[] = [];
    const warnings: string[] = [];

    for (const rule of rules) {
      const value = this.getNestedValue(data, rule.field);
      
      // 检查必需字段
      if (rule.required && (value === undefined || value === null)) {
        errors.push(`缺少必需字段: ${rule.field}`);
        continue;
      }

      // 检查类型
      if (value !== undefined && value !== null) {
        const actualType = Array.isArray(value) ? 'array' : typeof value;
        if (actualType !== rule.type) {
          errors.push(`字段 ${rule.field} 类型错误: 期望 ${rule.type}, 实际 ${actualType}`);
        }

        // 自定义验证器
        if (rule.validator && !rule.validator(value)) {
          errors.push(rule.errorMessage || `字段 ${rule.field} 验证失败`);
        }
      }
    }

    const result = {
      isValid: errors.length === 0,
      errors,
      warnings
    };

    // 记录验证结果
    this.logEvent({
      type: MonitorEventType.DATA_VALIDATION,
      page: context,
      component: 'DataValidator',
      details: { dataValidation: result }
    });

    return result;
  }

  /**
   * 监控API请求
   */
  async monitorApiRequest<T>(
    page: string,
    component: string,
    endpoint: string,
    method: string,
    requestData: any,
    apiCall: () => Promise<T>,
    validationRules?: { request?: ValidationRule[]; response?: ValidationRule[] }
  ): Promise<T> {
    const startTime = Date.now();
    const eventId = this.generateEventId();

    // 记录请求开始
    this.logEvent({
      type: MonitorEventType.REQUEST_START,
      page,
      component,
      details: {
        endpoint,
        method,
        requestData: this.sanitizeData(requestData)
      }
    });

    // 验证请求数据
    if (validationRules?.request) {
      this.validateData(requestData, validationRules.request, `${page}-request`);
    }

    try {
      // 执行API调用
      const response = await apiCall();
      const duration = Date.now() - startTime;

      // 验证响应数据
      if (validationRules?.response) {
        this.validateData(response, validationRules.response, `${page}-response`);
      }

      // 记录成功
      this.logEvent({
        type: MonitorEventType.REQUEST_SUCCESS,
        page,
        component,
        details: {
          endpoint,
          method,
          responseData: this.sanitizeData(response),
          duration,
          performanceMetrics: {
            responseTime: duration
          }
        }
      });

      return response;
    } catch (error) {
      const duration = Date.now() - startTime;

      // 记录错误
      this.logEvent({
        type: MonitorEventType.REQUEST_ERROR,
        page,
        component,
        details: {
          endpoint,
          method,
          error: error instanceof Error ? error.message : String(error),
          duration
        }
      });

      throw error;
    }
  }

  /**
   * 检查环境兼容性
   */
  checkEnvironmentCompatibility(page: string): void {
    if (!this.config.enableEnvironmentChecks) return;

    const environmentInfo = {
      nodeVersion: process.env.NODE_VERSION,
      browserInfo: navigator.userAgent,
      apiVersion: import.meta.env.VITE_API_VERSION || 'unknown',
      databaseVersion: 'unknown' // 需要从API获取
    };

    this.logEvent({
      type: MonitorEventType.ENVIRONMENT_CHECK,
      page,
      component: 'EnvironmentChecker',
      details: { environmentInfo }
    });
  }

  /**
   * 获取监测统计
   */
  getStatistics(): {
    totalEvents: number;
    eventsByType: Record<MonitorEventType, number>;
    errorRate: number;
    averageResponseTime: number;
    recentErrors: MonitorEvent[];
  } {
    const eventsByType = {} as Record<MonitorEventType, number>;
    let totalResponseTime = 0;
    let responseTimeCount = 0;
    const recentErrors: MonitorEvent[] = [];

    // 初始化计数器
    Object.values(MonitorEventType).forEach(type => {
      eventsByType[type] = 0;
    });

    // 统计事件
    this.events.forEach(event => {
      eventsByType[event.type]++;

      if (event.details.duration) {
        totalResponseTime += event.details.duration;
        responseTimeCount++;
      }

      if (event.type === MonitorEventType.REQUEST_ERROR) {
        recentErrors.push(event);
      }
    });

    const totalRequests = eventsByType[MonitorEventType.REQUEST_START];
    const errorCount = eventsByType[MonitorEventType.REQUEST_ERROR];

    return {
      totalEvents: this.events.length,
      eventsByType,
      errorRate: totalRequests > 0 ? (errorCount / totalRequests) * 100 : 0,
      averageResponseTime: responseTimeCount > 0 ? totalResponseTime / responseTimeCount : 0,
      recentErrors: recentErrors.slice(-10) // 最近10个错误
    };
  }

  /**
   * 获取所有事件
   */
  getAllEvents(): MonitorEvent[] {
    return [...this.events];
  }

  /**
   * 清除所有事件
   */
  clearEvents(): void {
    this.events = [];
  }

  /**
   * 导出监测数据
   */
  exportData(): string {
    return JSON.stringify({
      config: this.config,
      events: this.events,
      statistics: this.getStatistics(),
      exportTime: new Date().toISOString()
    }, null, 2);
  }

  // 私有方法
  private generateEventId(): string {
    return `event_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  private getNestedValue(obj: any, path: string): any {
    return path.split('.').reduce((current, key) => current?.[key], obj);
  }

  private sanitizeData(data: any): any {
    // 移除敏感信息
    if (typeof data !== 'object' || data === null) return data;
    
    const sanitized = { ...data };
    const sensitiveFields = ['password', 'token', 'secret', 'key'];
    
    for (const field of sensitiveFields) {
      if (field in sanitized) {
        sanitized[field] = '[REDACTED]';
      }
    }
    
    return sanitized;
  }

  private enforceMaxEntries(): void {
    if (this.events.length > this.config.maxLogEntries) {
      this.events = this.events.slice(-this.config.maxLogEntries);
    }
  }

  private logToConsole(event: MonitorEvent): void {
    const logLevel = this.config.logLevel;
    const message = `[${event.type}] ${event.page}/${event.component}`;

    switch (event.type) {
      case MonitorEventType.REQUEST_ERROR:
        if (['debug', 'info', 'warn', 'error'].includes(logLevel)) {
          console.error(message, event.details);
        }
        break;
      case MonitorEventType.DATA_VALIDATION:
        if (event.details.dataValidation && !event.details.dataValidation.isValid) {
          if (['debug', 'info', 'warn'].includes(logLevel)) {
            console.warn(message, event.details);
          }
        }
        break;
      default:
        if (logLevel === 'debug') {
          console.log(message, event.details);
        }
    }
  }

  private startAutoCleanup(): void {
    this.cleanupTimer = setInterval(() => {
      const cutoffTime = Date.now() - (24 * 60 * 60 * 1000); // 24小时前
      this.events = this.events.filter(event => 
        new Date(event.timestamp).getTime() > cutoffTime
      );
    }, this.config.autoCleanupInterval);
  }

  /**
   * 销毁监控器
   */
  destroy(): void {
    if (this.cleanupTimer) {
      clearInterval(this.cleanupTimer);
    }
    this.events = [];
  }
}

// 创建全局监控实例
export const dataMonitor = new DataIntegrationMonitor({
  enableLogging: import.meta.env.DEV,
  logLevel: import.meta.env.DEV ? 'debug' : 'error'
});

// 导出类型和实例
export { DataIntegrationMonitor };
export default dataMonitor;
