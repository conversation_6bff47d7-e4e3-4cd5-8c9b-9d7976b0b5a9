/**
 * 性能监控服务
 *
 * 提供全面的前端性能监控、分析和报告功能
 */

import * as webVitals from 'web-vitals';

// 性能指标类型
export interface PerformanceMetric {
  name: string;
  value: number;
  delta: number;
  id: string;
  navigationType?: string;
  attribution?: {
    [key: string]: any;
  };
}

// 性能数据类型
export interface PerformanceData {
  // Web Vitals
  webVitals: {
    CLS?: number; // Cumulative Layout Shift
    FID?: number; // First Input Delay
    LCP?: number; // Largest Contentful Paint
    FCP?: number; // First Contentful Paint
    TTFB?: number; // Time to First Byte
    INP?: number; // Interaction to Next Paint
  };

  // 导航和资源计时
  navigation: {
    navigationStart?: number;
    loadEventEnd?: number;
    domContentLoaded?: number;
    domInteractive?: number;
    connectEnd?: number;
    responseEnd?: number;
    fetchStart?: number;
    redirectCount?: number;
    type?: string;
  };

  // 资源加载
  resources: {
    totalCount: number;
    totalSize: number;
    byType: {
      [type: string]: {
        count: number;
        size: number;
        avgDuration: number;
      };
    };
    slowest: Array<{
      url: string;
      duration: number;
      size: number;
      type: string;
    }>;
  };

  // JavaScript性能
  javascript: {
    errors: number;
    longTasks: Array<{
      duration: number;
      startTime: number;
      attribution?: string;
    }>;
    memoryUsage?: {
      jsHeapSizeLimit?: number;
      totalJSHeapSize?: number;
      usedJSHeapSize?: number;
    };
    eventListeners: number;
  };

  // 组件性能
  components: {
    [name: string]: {
      renderCount: number;
      totalRenderTime: number;
      avgRenderTime: number;
      maxRenderTime: number;
      lastRenderTime: number;
    };
  };

  // API调用性能
  api: {
    totalCalls: number;
    totalDuration: number;
    avgDuration: number;
    slowest: Array<{
      url: string;
      method: string;
      duration: number;
      status: number;
    }>;
    errors: number;
  };

  // 用户交互
  interactions: {
    clicks: number;
    keyPresses: number;
    scrolls: number;
    inputDelay: number;
    pageTransitions: Array<{
      from: string;
      to: string;
      duration: number;
    }>;
  };

  // 会话信息
  session: {
    id: string;
    startTime: number;
    duration: number;
    pageViews: number;
    userAgent: string;
    deviceType: string;
    connectionType?: string;
    effectiveConnectionType?: string;
  };
}

class PerformanceMonitoringService {
  private static instance: PerformanceMonitoringService;
  private isInitialized: boolean = false;
  private isMonitoring: boolean = false;
  private data: PerformanceData;
  private observers: Array<PerformanceObserver> = [];
  private startTime: number = 0;
  private config = {
    sampleRate: 1.0, // 采样率 (0-1)
    reportingEndpoint: '', // 上报端点
    reportingInterval: 60000, // 上报间隔 (ms)
    maxEventsPerReport: 100, // 每次上报最大事件数
    logToConsole: false, // 是否记录到控制台
    monitorResources: true, // 是否监控资源加载
    monitorLongTasks: true, // 是否监控长任务
    monitorMemory: true, // 是否监控内存使用
    monitorInteractions: true, // 是否监控用户交互
    resourceSizeLimit: 1000000, // 资源大小限制 (bytes)
    slowResourceThreshold: 1000, // 慢资源阈值 (ms)
    slowApiThreshold: 500, // 慢API阈值 (ms)
    longTaskThreshold: 50, // 长任务阈值 (ms)
    maxResourcesTracked: 100, // 最大跟踪资源数
    maxApiCallsTracked: 100, // 最大跟踪API调用数
    maxLongTasksTracked: 50, // 最大跟踪长任务数
  };

  private reportingTimer: number | null = null;
  private eventQueue: Array<any> = [];

  /**
   * 构造函数
   */
  private constructor() {
    this.data = this.createInitialData();
    this.startTime = performance.now();
  }

  /**
   * 获取单例实例
   */
  public static getInstance(): PerformanceMonitoringService {
    if (!PerformanceMonitoringService.instance) {
      PerformanceMonitoringService.instance = new PerformanceMonitoringService();
    }

    return PerformanceMonitoringService.instance;
  }

  /**
   * 创建初始数据结构
   */
  private createInitialData(): PerformanceData {
    const sessionId = this.generateSessionId();

    return {
      webVitals: {},
      navigation: {},
      resources: {
        totalCount: 0,
        totalSize: 0,
        byType: {},
        slowest: []
      },
      javascript: {
        errors: 0,
        longTasks: [],
        eventListeners: 0
      },
      components: {},
      api: {
        totalCalls: 0,
        totalDuration: 0,
        avgDuration: 0,
        slowest: [],
        errors: 0
      },
      interactions: {
        clicks: 0,
        keyPresses: 0,
        scrolls: 0,
        inputDelay: 0,
        pageTransitions: []
      },
      session: {
        id: sessionId,
        startTime: Date.now(),
        duration: 0,
        pageViews: 1,
        userAgent: navigator.userAgent,
        deviceType: this.getDeviceType(),
        connectionType: this.getConnectionType()
      }
    };
  }

  /**
   * 生成会话ID
   */
  private generateSessionId(): string {
    return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function(c) {
      const r = Math.random() * 16 | 0;
      const v = c === 'x' ? r : (r & 0x3 | 0x8);
      return v.toString(16);
    });
  }

  /**
   * 获取设备类型
   */
  private getDeviceType(): string {
    const ua = navigator.userAgent;
    if (/(tablet|ipad|playbook|silk)|(android(?!.*mobi))/i.test(ua)) {
      return 'tablet';
    }
    if (/Mobile|Android|iP(hone|od)|IEMobile|BlackBerry|Kindle|Silk-Accelerated|(hpw|web)OS|Opera M(obi|ini)/.test(ua)) {
      return 'mobile';
    }
    return 'desktop';
  }

  /**
   * 获取连接类型
   */
  private getConnectionType(): string {
    if ('connection' in navigator && navigator.connection) {
      const conn = navigator.connection as any;
      return conn.effectiveType || conn.type || 'unknown';
    }
    return 'unknown';
  }

  /**
   * 初始化性能监控
   * @param config 配置
   */
  public initialize(config?: Partial<typeof PerformanceMonitoringService.prototype.config>): void {
    if (this.isInitialized) {
      console.warn('性能监控服务已经初始化');
      return;
    }

    // 合并配置
    if (config) {
      this.config = { ...this.config, ...config };
    }

    // 采样决定是否启用监控
    if (Math.random() > this.config.sampleRate) {
      console.log(`性能监控未启用 (采样率: ${this.config.sampleRate})`);
      return;
    }

    this.isInitialized = true;
    this.startMonitoring();

    // 记录日志
    if (this.config.logToConsole) {
      console.log('性能监控服务已初始化');
    }
  }

  /**
   * 开始监控
   */
  public startMonitoring(): void {
    if (!this.isInitialized) {
      console.warn('性能监控服务未初始化');
      return;
    }

    if (this.isMonitoring) {
      return;
    }

    this.isMonitoring = true;

    // 收集Web Vitals
    this.collectWebVitals();

    // 收集导航计时
    this.collectNavigationTiming();

    // 监控资源加载
    if (this.config.monitorResources) {
      this.monitorResources();
    }

    // 监控长任务
    if (this.config.monitorLongTasks) {
      this.monitorLongTasks();
    }

    // 监控内存使用
    if (this.config.monitorMemory) {
      this.monitorMemory();
    }

    // 监控用户交互
    if (this.config.monitorInteractions) {
      this.monitorInteractions();
    }

    // 监控错误
    this.monitorErrors();

    // 设置上报定时器
    if (this.config.reportingEndpoint) {
      this.reportingTimer = window.setInterval(() => {
        this.reportData();
      }, this.config.reportingInterval);
    }

    // 页面卸载前上报数据
    window.addEventListener('beforeunload', () => {
      this.reportData();
    });
  }

  /**
   * 停止监控
   */
  public stopMonitoring(): void {
    if (!this.isMonitoring) {
      return;
    }

    this.isMonitoring = false;

    // 断开所有观察器
    this.observers.forEach(observer => {
      observer.disconnect();
    });

    // 清除上报定时器
    if (this.reportingTimer !== null) {
      clearInterval(this.reportingTimer);
      this.reportingTimer = null;
    }

    // 记录日志
    if (this.config.logToConsole) {
      console.log('性能监控服务已停止');
    }
  }

  /**
   * 收集Web Vitals
   */
  private collectWebVitals(): void {
    webVitals.onCLS(metric => this.handleWebVitalMetric('CLS', metric));
    webVitals.onFID(metric => this.handleWebVitalMetric('FID', metric));
    webVitals.onLCP(metric => this.handleWebVitalMetric('LCP', metric));
    webVitals.onFCP(metric => this.handleWebVitalMetric('FCP', metric));
    webVitals.onTTFB(metric => this.handleWebVitalMetric('TTFB', metric));

    // 如果支持INP (Interaction to Next Paint)
    if ('onINP' in webVitals) {
      (webVitals as any).onINP(metric => this.handleWebVitalMetric('INP', metric));
    }
  }

  /**
   * 处理Web Vital指标
   * @param name 指标名称
   * @param metric 指标数据
   */
  private handleWebVitalMetric(name: string, metric: any): void {
    this.data.webVitals[name] = metric.value;

    // 添加到事件队列
    this.addToEventQueue('webVital', {
      name,
      value: metric.value,
      delta: metric.delta,
      id: metric.id,
      navigationType: metric.navigationType
    });

    // 记录日志
    if (this.config.logToConsole) {
      console.log(`Web Vital: ${name} = ${metric.value}`);
    }
  }

  /**
   * 收集导航计时
   */
  private collectNavigationTiming(): void {
    if (!performance || !performance.timing) {
      return;
    }

    // 等待加载完成
    window.addEventListener('load', () => {
      setTimeout(() => {
        const timing = performance.timing;

        this.data.navigation = {
          navigationStart: timing.navigationStart,
          loadEventEnd: timing.loadEventEnd,
          domContentLoaded: timing.domContentLoadedEventEnd - timing.navigationStart,
          domInteractive: timing.domInteractive - timing.navigationStart,
          connectEnd: timing.connectEnd - timing.navigationStart,
          responseEnd: timing.responseEnd - timing.navigationStart,
          fetchStart: timing.fetchStart - timing.navigationStart,
          redirectCount: performance.navigation ? performance.navigation.redirectCount : undefined,
          type: this.getNavigationType(performance.navigation ? performance.navigation.type : -1)
        };

        // 添加到事件队列
        this.addToEventQueue('navigationTiming', this.data.navigation);

        // 记录日志
        if (this.config.logToConsole) {
          console.log('导航计时:', this.data.navigation);
        }
      }, 0);
    });
  }

  /**
   * 获取导航类型
   * @param type 导航类型代码
   */
  private getNavigationType(type: number): string {
    switch (type) {
      case 0: return 'navigate';
      case 1: return 'reload';
      case 2: return 'back_forward';
      case 255: return 'prerender';
      default: return 'unknown';
    }
  }

  /**
   * 监控资源加载
   */
  private monitorResources(): void {
    if (!window.PerformanceObserver) {
      return;
    }

    try {
      const resourceObserver = new PerformanceObserver(list => {
        list.getEntries().forEach(entry => {
          this.processResourceEntry(entry as PerformanceResourceTiming);
        });
      });

      resourceObserver.observe({ entryTypes: ['resource'] });
      this.observers.push(resourceObserver);
    } catch (e) {
      console.error('资源监控初始化失败:', e);
    }
  }

  /**
   * 处理资源条目
   * @param entry 资源计时条目
   */
  private processResourceEntry(entry: PerformanceResourceTiming): void {
    // 提取资源类型
    const url = entry.name;
    const type = this.getResourceType(entry);
    const size = entry.transferSize || 0;
    const duration = entry.duration;

    // 更新总计数据
    this.data.resources.totalCount++;
    this.data.resources.totalSize += size;

    // 更新按类型分组的数据
    if (!this.data.resources.byType[type]) {
      this.data.resources.byType[type] = {
        count: 0,
        size: 0,
        avgDuration: 0
      };
    }

    const typeStats = this.data.resources.byType[type];
    typeStats.count++;
    typeStats.size += size;
    typeStats.avgDuration = (typeStats.avgDuration * (typeStats.count - 1) + duration) / typeStats.count;

    // 检查是否是慢资源
    if (duration > this.config.slowResourceThreshold) {
      // 添加到最慢资源列表
      this.data.resources.slowest.push({
        url,
        duration,
        size,
        type
      });

      // 保持列表按持续时间排序并限制大小
      this.data.resources.slowest.sort((a, b) => b.duration - a.duration);
      if (this.data.resources.slowest.length > this.config.maxResourcesTracked) {
        this.data.resources.slowest.pop();
      }

      // 添加到事件队列
      this.addToEventQueue('slowResource', {
        url,
        duration,
        size,
        type
      });
    }
  }

  /**
   * 获取资源类型
   * @param entry 资源计时条目
   */
  private getResourceType(entry: PerformanceResourceTiming): string {
    const url = entry.name;
    const initiatorType = entry.initiatorType;

    if (initiatorType === 'img' || initiatorType === 'image' || url.match(/\.(png|jpg|jpeg|gif|webp|svg|ico)(\?|$)/i)) {
      return 'image';
    } else if (initiatorType === 'script' || url.match(/\.(js)(\?|$)/i)) {
      return 'script';
    } else if (initiatorType === 'link' || initiatorType === 'css' || url.match(/\.(css)(\?|$)/i)) {
      return 'stylesheet';
    } else if (initiatorType === 'xmlhttprequest' || initiatorType === 'fetch') {
      return 'xhr';
    } else if (url.match(/\.(woff|woff2|ttf|otf|eot)(\?|$)/i)) {
      return 'font';
    } else if (url.match(/\.(mp4|webm|ogg|mp3|wav)(\?|$)/i)) {
      return 'media';
    } else {
      return initiatorType || 'other';
    }
  }

  /**
   * 监控长任务
   */
  private monitorLongTasks(): void {
    if (!window.PerformanceObserver) {
      return;
    }

    try {
      const longTaskObserver = new PerformanceObserver(list => {
        list.getEntries().forEach(entry => {
          this.processLongTaskEntry(entry);
        });
      });

      longTaskObserver.observe({ entryTypes: ['longtask'] });
      this.observers.push(longTaskObserver);
    } catch (e) {
      console.error('长任务监控初始化失败:', e);
    }
  }

  /**
   * 处理长任务条目
   * @param entry 长任务条目
   */
  private processLongTaskEntry(entry: any): void {
    const duration = entry.duration;
    const startTime = entry.startTime;
    let attribution = 'unknown';

    // 尝试获取归因信息
    if (entry.attribution && entry.attribution.length > 0) {
      const attr = entry.attribution[0];
      attribution = attr.name || attr.containerType || attr.containerName || 'unknown';
    }

    // 只记录超过阈值的任务
    if (duration > this.config.longTaskThreshold) {
      // 添加到长任务列表
      this.data.javascript.longTasks.push({
        duration,
        startTime,
        attribution
      });

      // 保持列表按持续时间排序并限制大小
      this.data.javascript.longTasks.sort((a, b) => b.duration - a.duration);
      if (this.data.javascript.longTasks.length > this.config.maxLongTasksTracked) {
        this.data.javascript.longTasks.pop();
      }

      // 添加到事件队列
      this.addToEventQueue('longTask', {
        duration,
        startTime,
        attribution
      });

      // 记录日志
      if (this.config.logToConsole) {
        console.warn(`检测到长任务: ${duration.toFixed(2)}ms, 归因: ${attribution}`);
      }
    }
  }

  /**
   * 监控内存使用
   */
  private monitorMemory(): void {
    // 检查是否支持内存API
    if (!performance || !('memory' in performance)) {
      return;
    }

    // 定期收集内存使用情况
    const memoryInterval = setInterval(() => {
      if (!this.isMonitoring) {
        clearInterval(memoryInterval);
        return;
      }

      const memory = (performance as any).memory;

      this.data.javascript.memoryUsage = {
        jsHeapSizeLimit: memory.jsHeapSizeLimit,
        totalJSHeapSize: memory.totalJSHeapSize,
        usedJSHeapSize: memory.usedJSHeapSize
      };

      // 添加到事件队列
      this.addToEventQueue('memory', this.data.javascript.memoryUsage);

      // 记录日志
      if (this.config.logToConsole) {
        const usedMB = (memory.usedJSHeapSize / 1048576).toFixed(2);
        const totalMB = (memory.totalJSHeapSize / 1048576).toFixed(2);
        const limitMB = (memory.jsHeapSizeLimit / 1048576).toFixed(2);

        console.log(`内存使用: ${usedMB}MB / ${totalMB}MB (限制: ${limitMB}MB)`);
      }
    }, 10000); // 每10秒收集一次
  }

  /**
   * 监控用户交互
   */
  private monitorInteractions(): void {
    // 点击事件
    document.addEventListener('click', () => {
      this.data.interactions.clicks++;
    });

    // 按键事件
    document.addEventListener('keydown', () => {
      this.data.interactions.keyPresses++;
    });

    // 滚动事件
    document.addEventListener('scroll', () => {
      this.data.interactions.scrolls++;
    }, { passive: true });

    // 页面转换
    if ('navigation' in window) {
      const nav = window.navigation as any;

      nav.addEventListener('navigate', (event: any) => {
        const from = event.from || window.location.pathname;
        const to = event.destination.url;
        const startTime = performance.now();

        // 等待导航完成
        setTimeout(() => {
          const duration = performance.now() - startTime;

          this.data.interactions.pageTransitions.push({
            from,
            to,
            duration
          });

          // 添加到事件队列
          this.addToEventQueue('pageTransition', {
            from,
            to,
            duration
          });
        }, 0);
      });
    }
  }

  /**
   * 监控错误
   */
  private monitorErrors(): void {
    // 全局错误
    window.addEventListener('error', (event) => {
      this.data.javascript.errors++;

      // 添加到事件队列
      this.addToEventQueue('error', {
        message: event.message,
        filename: event.filename,
        lineno: event.lineno,
        colno: event.colno,
        timestamp: Date.now()
      });

      // 记录日志
      if (this.config.logToConsole) {
        console.error(`JavaScript错误: ${event.message}`);
      }
    });

    // Promise错误
    window.addEventListener('unhandledrejection', (event) => {
      this.data.javascript.errors++;

      // 添加到事件队列
      this.addToEventQueue('unhandledRejection', {
        reason: event.reason?.toString() || 'Unknown reason',
        timestamp: Date.now()
      });

      // 记录日志
      if (this.config.logToConsole) {
        console.error(`未处理的Promise拒绝: ${event.reason}`);
      }
    });
  }

  /**
   * 添加到事件队列
   * @param type 事件类型
   * @param data 事件数据
   */
  private addToEventQueue(type: string, data: any): void {
    if (!this.config.reportingEndpoint) {
      return;
    }

    this.eventQueue.push({
      type,
      timestamp: Date.now(),
      data
    });

    // 限制队列大小
    if (this.eventQueue.length > this.config.maxEventsPerReport * 2) {
      this.eventQueue = this.eventQueue.slice(-this.config.maxEventsPerReport);
    }
  }

  /**
   * 上报数据
   */
  private reportData(): void {
    if (!this.config.reportingEndpoint || this.eventQueue.length === 0) {
      return;
    }

    // 获取要上报的事件
    const events = this.eventQueue.splice(0, this.config.maxEventsPerReport);

    // 构建上报数据
    const reportData = {
      session: this.data.session,
      timestamp: Date.now(),
      events
    };

    // 发送数据
    fetch(this.config.reportingEndpoint, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(reportData),
      // 使用keepalive确保页面卸载时也能发送
      keepalive: true
    }).catch(error => {
      console.error('性能数据上报失败:', error);
    });
  }

  /**
   * 记录组件渲染时间
   * @param componentName 组件名称
   * @param renderTime 渲染时间
   */
  public recordComponentRender(componentName: string, renderTime: number): void {
    if (!this.isMonitoring) {
      return;
    }

    if (!this.data.components[componentName]) {
      this.data.components[componentName] = {
        renderCount: 0,
        totalRenderTime: 0,
        avgRenderTime: 0,
        maxRenderTime: 0,
        lastRenderTime: 0
      };
    }

    const stats = this.data.components[componentName];

    stats.renderCount++;
    stats.totalRenderTime += renderTime;
    stats.avgRenderTime = stats.totalRenderTime / stats.renderCount;
    stats.lastRenderTime = renderTime;

    if (renderTime > stats.maxRenderTime) {
      stats.maxRenderTime = renderTime;
    }

    // 添加到事件队列
    this.addToEventQueue('componentRender', {
      componentName,
      renderTime,
      renderCount: stats.renderCount
    });

    // 记录日志
    if (this.config.logToConsole && renderTime > 16.67) { // 超过一帧的时间(60fps)
      console.warn(`组件渲染时间过长: ${componentName} = ${renderTime.toFixed(2)}ms`);
    }
  }

  /**
   * 记录API调用
   * @param url API URL
   * @param method HTTP方法
   * @param duration 持续时间
   * @param status HTTP状态码
   */
  public recordApiCall(url: string, method: string, duration: number, status: number): void {
    if (!this.isMonitoring) {
      return;
    }

    this.data.api.totalCalls++;
    this.data.api.totalDuration += duration;
    this.data.api.avgDuration = this.data.api.totalDuration / this.data.api.totalCalls;

    // 检查是否是错误
    if (status >= 400) {
      this.data.api.errors++;
    }

    // 检查是否是慢API调用
    if (duration > this.config.slowApiThreshold) {
      // 添加到最慢API调用列表
      this.data.api.slowest.push({
        url,
        method,
        duration,
        status
      });

      // 保持列表按持续时间排序并限制大小
      this.data.api.slowest.sort((a, b) => b.duration - a.duration);
      if (this.data.api.slowest.length > this.config.maxApiCallsTracked) {
        this.data.api.slowest.pop();
      }

      // 添加到事件队列
      this.addToEventQueue('slowApiCall', {
        url,
        method,
        duration,
        status
      });

      // 记录日志
      if (this.config.logToConsole) {
        console.warn(`慢API调用: ${method} ${url} = ${duration.toFixed(2)}ms, 状态: ${status}`);
      }
    }
  }

  /**
   * 获取性能数据
   */
  public getData(): PerformanceData {
    // 更新会话持续时间
    this.data.session.duration = Date.now() - this.data.session.startTime;

    return { ...this.data };
  }
}

// 导出单例实例
export default PerformanceMonitoringService.getInstance();
