/**
 * 故事相关处理程序
 */

import { verifyToken } from '../utils/auth';
import { generateHash } from '../utils/crypto';
import { generateSequenceNumber } from '../utils/sequence';

/**
 * 处理获取故事列表
 * @param request 请求对象
 * @param env 环境对象
 * @returns 响应对象
 */
export async function handleGetStories(request: Request, env: Env): Promise<Response> {
  try {
    // 获取查询参数
    const url = new URL(request.url);
    const tag = url.searchParams.get('tag');
    const status = url.searchParams.get('status') || 'approved';
    const search = url.searchParams.get('search');
    const sortBy = url.searchParams.get('sortBy') || 'latest';
    const page = parseInt(url.searchParams.get('page') || '1', 10);
    const pageSize = parseInt(url.searchParams.get('pageSize') || '10', 10);

    // 构建查询
    let query = `
      SELECT s.*, u.name as author_name
      FROM stories s
      LEFT JOIN users u ON s.author_id = u.id
      WHERE s.status = ?
    `;

    const params = [status];

    // 添加标签过滤
    if (tag) {
      query += `
        AND s.id IN (
          SELECT st.story_id
          FROM story_tags st
          JOIN tags t ON st.tag_id = t.id
          WHERE t.name = ?
        )
      `;
      params.push(tag);
    }

    // 添加搜索过滤
    if (search) {
      query += `
        AND (s.title LIKE ? OR s.content LIKE ?)
      `;
      params.push(`%${search}%`, `%${search}%`);
    }

    // 添加排序
    if (sortBy === 'latest') {
      query += ` ORDER BY s.created_at DESC`;
    } else if (sortBy === 'popular') {
      query += ` ORDER BY s.likes DESC`;
    }

    // 添加分页
    query += ` LIMIT ? OFFSET ?`;
    params.push(pageSize.toString(), ((page - 1) * pageSize).toString());

    // 执行查询
    const stories = await env.DB.prepare(query).bind(...params).all();

    // 获取总数
    let countQuery = `
      SELECT COUNT(*) as total
      FROM stories s
      WHERE s.status = ?
    `;

    const countParams = [status];

    // 添加标签过滤
    if (tag) {
      countQuery += `
        AND s.id IN (
          SELECT st.story_id
          FROM story_tags st
          JOIN tags t ON st.tag_id = t.id
          WHERE t.name = ?
        )
      `;
      countParams.push(tag);
    }

    // 添加搜索过滤
    if (search) {
      countQuery += `
        AND (s.title LIKE ? OR s.content LIKE ?)
      `;
      countParams.push(`%${search}%`, `%${search}%`);
    }

    const totalResult = await env.DB.prepare(countQuery).bind(...countParams).first();
    const total = totalResult ? (totalResult.total as number) : 0;

    // 获取故事标签
    const storyIds = stories.results.map(story => story.id);

    let tagsResult = { results: [] };

    if (storyIds.length > 0) {
      const tagsQuery = `
        SELECT st.story_id, t.name
        FROM story_tags st
        JOIN tags t ON st.tag_id = t.id
        WHERE st.story_id IN (${storyIds.map(() => '?').join(',')})
      `;

      tagsResult = await env.DB.prepare(tagsQuery).bind(...storyIds).all();
    }

    // 组织标签数据
    const storyTags: Record<number, string[]> = {};

    for (const tag of tagsResult.results) {
      if (!storyTags[tag.story_id]) {
        storyTags[tag.story_id] = [];
      }

      storyTags[tag.story_id].push(tag.name);
    }

    // 组织响应数据
    const formattedStories = stories.results.map(story => ({
      id: story.id,
      sequenceNumber: story.sequence_number,
      title: story.title,
      content: story.content,
      author: story.is_anonymous ? null : {
        id: story.author_id,
        name: story.author_name
      },
      isAnonymous: story.is_anonymous === 1,
      createdAt: story.created_at,
      updatedAt: story.updated_at,
      status: story.status,
      tags: storyTags[story.id] || [],
      likes: story.likes || 0,
      comments: 0 // 暂不实现评论功能
    }));

    // 返回响应
    return new Response(JSON.stringify({
      success: true,
      stories: formattedStories,
      total,
      page,
      pageSize,
      totalPages: Math.ceil(total / pageSize)
    }), {
      status: 200,
      headers: { 'Content-Type': 'application/json' }
    });
  } catch (error) {
    console.error('获取故事列表失败:', error);

    return new Response(JSON.stringify({
      success: false,
      error: '服务器错误'
    }), {
      status: 500,
      headers: { 'Content-Type': 'application/json' }
    });
  }
}

/**
 * 处理获取故事详情
 * @param request 请求对象
 * @param env 环境对象
 * @param storyId 故事ID
 * @returns 响应对象
 */
export async function handleGetStoryDetail(request: Request, env: Env, storyId: string): Promise<Response> {
  try {
    // 查询故事
    const story = await env.DB.prepare(`
      SELECT s.*, u.name as author_name
      FROM stories s
      LEFT JOIN users u ON s.author_id = u.id
      WHERE s.id = ?
    `).bind(storyId).first();

    if (!story) {
      return new Response(JSON.stringify({
        success: false,
        error: '故事不存在'
      }), {
        status: 404,
        headers: { 'Content-Type': 'application/json' }
      });
    }

    // 查询标签
    const tags = await env.DB.prepare(`
      SELECT t.name
      FROM story_tags st
      JOIN tags t ON st.tag_id = t.id
      WHERE st.story_id = ?
    `).bind(storyId).all();

    // 组织响应数据
    const formattedStory = {
      id: story.id,
      sequenceNumber: story.sequence_number,
      title: story.title,
      content: story.content,
      author: story.is_anonymous ? null : {
        id: story.author_id,
        name: story.author_name
      },
      isAnonymous: story.is_anonymous === 1,
      createdAt: story.created_at,
      updatedAt: story.updated_at,
      status: story.status,
      tags: tags.results.map(tag => tag.name),
      likes: story.likes || 0,
      comments: 0 // 暂不实现评论功能
    };

    // 返回响应
    return new Response(JSON.stringify({
      success: true,
      story: formattedStory
    }), {
      status: 200,
      headers: { 'Content-Type': 'application/json' }
    });
  } catch (error) {
    console.error('获取故事详情失败:', error);

    return new Response(JSON.stringify({
      success: false,
      error: '服务器错误'
    }), {
      status: 500,
      headers: { 'Content-Type': 'application/json' }
    });
  }
}

/**
 * 处理创建故事
 * @param request 请求对象
 * @param env 环境对象
 * @returns 响应对象
 */
export async function handleCreateStory(request: Request, env: Env): Promise<Response> {
  try {
    // 解析请求体
    const { title, content, isAnonymous, tags, identityA, identityB } = await request.json() as {
      title: string;
      content: string;
      isAnonymous: boolean;
      tags?: string[];
      identityA: string;
      identityB: string;
    };

    // 验证参数
    if (!title || !content || !identityA || !identityB) {
      return new Response(JSON.stringify({
        success: false,
        error: '缺少必要参数'
      }), {
        status: 400,
        headers: { 'Content-Type': 'application/json' }
      });
    }

    // 生成哈希值
    const identityAHash = generateHash(identityA);
    const identityBHash = generateHash(identityB);

    // 生成序列号
    const sequenceNumber = await generateSequenceNumber(env.DB, 'S');

    // 创建故事
    const result = await env.DB.prepare(`
      INSERT INTO stories (
        sequence_number, title, content, is_anonymous, status,
        identity_a_hash, identity_b_hash
      )
      VALUES (?, ?, ?, ?, ?, ?, ?)
    `).bind(
      sequenceNumber,
      title,
      content,
      isAnonymous ? 1 : 0,
      'pending',
      identityAHash,
      identityBHash
    ).run();

    if (!result.success) {
      throw new Error('Failed to create story');
    }

    // 获取新创建的故事ID
    const storyId = result.meta.last_row_id;

    // 添加标签
    if (tags && tags.length > 0) {
      for (const tagName of tags) {
        // 查找标签
        const tag = await env.DB.prepare(`
          SELECT id FROM tags WHERE name = ?
        `).bind(tagName).first();

        if (tag) {
          // 添加标签关联
          await env.DB.prepare(`
            INSERT INTO story_tags (story_id, tag_id)
            VALUES (?, ?)
          `).bind(storyId, tag.id).run();
        }
      }
    }

    // 返回响应
    return new Response(JSON.stringify({
      success: true,
      storyId,
      sequenceNumber,
      message: '故事创建成功，等待审核'
    }), {
      status: 201,
      headers: { 'Content-Type': 'application/json' }
    });
  } catch (error) {
    console.error('创建故事失败:', error);

    return new Response(JSON.stringify({
      success: false,
      error: '服务器错误'
    }), {
      status: 500,
      headers: { 'Content-Type': 'application/json' }
    });
  }
}

/**
 * 处理获取待审核故事列表
 * @param request 请求对象
 * @param env 环境对象
 * @returns 响应对象
 */
export async function handleGetPendingStories(request: Request, env: Env): Promise<Response> {
  try {
    // 验证管理员权限
    const authHeader = request.headers.get('Authorization');

    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return new Response(JSON.stringify({
        success: false,
        error: '未授权'
      }), {
        status: 401,
        headers: { 'Content-Type': 'application/json' }
      });
    }

    const token = authHeader.substring(7);
    const tokenResult = verifyToken(token, env.JWT_SECRET);

    if (!tokenResult.valid || !tokenResult.payload) {
      return new Response(JSON.stringify({
        success: false,
        error: '无效的令牌'
      }), {
        status: 401,
        headers: { 'Content-Type': 'application/json' }
      });
    }

    // 验证角色权限
    const role = tokenResult.payload.role;

    if (role !== 'admin' && role !== 'superadmin' && role !== 'reviewer') {
      return new Response(JSON.stringify({
        success: false,
        error: '没有权限'
      }), {
        status: 403,
        headers: { 'Content-Type': 'application/json' }
      });
    }

    // 查询待审核故事
    const stories = await env.DB.prepare(`
      SELECT * FROM stories
      WHERE status = 'pending'
      ORDER BY created_at DESC
    `).all();

    // 组织响应数据
    const formattedStories = stories.results.map(story => ({
      id: story.id,
      sequenceNumber: story.sequence_number,
      title: story.title,
      content: story.content,
      isAnonymous: story.is_anonymous === 1,
      createdAt: story.created_at,
      status: story.status
    }));

    // 返回响应
    return new Response(JSON.stringify({
      success: true,
      stories: formattedStories,
      total: formattedStories.length
    }), {
      status: 200,
      headers: { 'Content-Type': 'application/json' }
    });
  } catch (error) {
    console.error('获取待审核故事列表失败:', error);

    return new Response(JSON.stringify({
      success: false,
      error: '服务器错误'
    }), {
      status: 500,
      headers: { 'Content-Type': 'application/json' }
    });
  }
}

/**
 * 处理审核故事
 * @param request 请求对象
 * @param env 环境对象
 * @param storyId 故事ID
 * @returns 响应对象
 */
export async function handleModerateStory(request: Request, env: Env, storyId: string): Promise<Response> {
  try {
    // 验证管理员权限
    const authHeader = request.headers.get('Authorization');

    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return new Response(JSON.stringify({
        success: false,
        error: '未授权'
      }), {
        status: 401,
        headers: { 'Content-Type': 'application/json' }
      });
    }

    const token = authHeader.substring(7);
    const tokenResult = verifyToken(token, env.JWT_SECRET);

    if (!tokenResult.valid || !tokenResult.payload) {
      return new Response(JSON.stringify({
        success: false,
        error: '无效的令牌'
      }), {
        status: 401,
        headers: { 'Content-Type': 'application/json' }
      });
    }

    // 验证角色权限
    const role = tokenResult.payload.role;
    const reviewerId = tokenResult.payload.sub;

    if (role !== 'admin' && role !== 'superadmin' && role !== 'reviewer') {
      return new Response(JSON.stringify({
        success: false,
        error: '没有权限'
      }), {
        status: 403,
        headers: { 'Content-Type': 'application/json' }
      });
    }

    // 解析请求体
    const { status, reason } = await request.json() as {
      status: 'approved' | 'rejected';
      reason?: string;
    };

    // 验证参数
    if (status !== 'approved' && status !== 'rejected') {
      return new Response(JSON.stringify({
        success: false,
        error: '无效的状态'
      }), {
        status: 400,
        headers: { 'Content-Type': 'application/json' }
      });
    }

    // 查询故事
    const story = await env.DB.prepare(`
      SELECT * FROM stories
      WHERE id = ?
    `).bind(storyId).first();

    if (!story) {
      return new Response(JSON.stringify({
        success: false,
        error: '故事不存在'
      }), {
        status: 404,
        headers: { 'Content-Type': 'application/json' }
      });
    }

    // 检查故事状态
    if (story.status !== 'pending') {
      return new Response(JSON.stringify({
        success: false,
        error: '故事已审核'
      }), {
        status: 400,
        headers: { 'Content-Type': 'application/json' }
      });
    }

    // 更新故事状态
    await env.DB.prepare(`
      UPDATE stories
      SET status = ?, reviewer_id = ?, reviewed_at = CURRENT_TIMESTAMP, rejection_reason = ?
      WHERE id = ?
    `).bind(
      status,
      reviewerId,
      status === 'rejected' ? reason || null : null,
      storyId
    ).run();

    // 记录审核记录
    const startTime = Date.now();

    await env.DB.prepare(`
      INSERT INTO review_records (
        reviewer_id, content_id, content_type, decision, review_duration
      )
      VALUES (?, ?, ?, ?, ?)
    `).bind(
      reviewerId,
      storyId,
      'story',
      status,
      Math.floor((Date.now() - startTime) / 1000)
    ).run();

    // 返回响应
    return new Response(JSON.stringify({
      success: true,
      id: storyId,
      status,
      message: status === 'approved' ? '故事已通过审核' : '故事已被拒绝'
    }), {
      status: 200,
      headers: { 'Content-Type': 'application/json' }
    });
  } catch (error) {
    console.error('审核故事失败:', error);

    return new Response(JSON.stringify({
      success: false,
      error: '服务器错误'
    }), {
      status: 500,
      headers: { 'Content-Type': 'application/json' }
    });
  }
}