/**
 * 自动审核路由
 * 
 * 处理自动审核相关的路由
 */

import { Hono } from 'hono';
import { Env } from '../../types';
import * as autoModerationController from './auto-moderation.controller';
import { adminAuthMiddleware } from '../../middlewares/adminAuth.middleware';
import { superAdminAuthMiddleware } from '../../middlewares/superAdminAuth.middleware';

// 创建路由
const autoModerationRouter = new Hono<{ Bindings: Env }>();

// 获取自动审核配置（需要管理员权限）
autoModerationRouter.get('/config', adminAuthMiddleware, autoModerationController.getAutoModerationConfig);

// 更新自动审核配置（需要超级管理员权限）
autoModerationRouter.post('/config', superAdminAuthMiddleware, autoModerationController.updateAutoModerationConfig);

// 重置自动审核配置（需要超级管理员权限）
autoModerationRouter.post('/config/reset', superAdminAuthMiddleware, autoModerationController.resetAutoModerationConfig);

// 测试自动审核（需要管理员权限）
autoModerationRouter.post('/test', adminAuthMiddleware, autoModerationController.testAutoModeration);

// 获取自动审核统计（需要管理员权限）
autoModerationRouter.get('/stats', adminAuthMiddleware, autoModerationController.getAutoModerationStats);

export default autoModerationRouter;
