import { useEffect, useState } from 'react';
import { z } from 'zod';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage
} from '@/components/ui/form';
import { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue
} from '@/components/ui/select';
import { Input } from '@/components/ui/input';
import QuestionItem from '../QuestionItem';
import AnswerStats from '../AnswerStats';
import { QuestionnaireData, getQuestionnaireStats, QuestionnaireStats } from '@/lib/api';
import { useApiDebug, DataStructures } from '@/hooks/useApiDebug';
import ApiDebugPanel from '@/components/debug/ApiDebugPanel';
import DataIntegrationPanel from '@/components/debug/DataIntegrationPanel';
import DataArchitecturePanel from '@/components/debug/DataArchitecturePanel';

// Define the form schema
const formSchema = z.object({
  educationLevel: z.string().min(1, { message: '请选择学历' }),
  major: z.string().min(1, { message: '请选择专业大类' }),
  graduationYear: z.string().min(1, { message: '请选择毕业年份' }),
  region: z.string().min(1, { message: '请选择地区' }),
});

// 本地统计数据接口
interface LocalStats {
  educationLevel: Array<{ label: string; count: number; percentage: number }>;
  major: Array<{ label: string; count: number; percentage: number }>;
  graduationYear: Array<{ label: string; count: number; percentage: number }>;
  region: Array<{ label: string; count: number; percentage: number }>;
  totalResponses: number;
}

interface PersonalInfoModuleProps {
  data: Partial<QuestionnaireData>;
  onUpdate: (data: Partial<QuestionnaireData>) => void;
}

export default function PersonalInfoModule({ data, onUpdate }: PersonalInfoModuleProps) {
  // Set up form with default values from data
  const form = useForm<z.infer<typeof formSchema>>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      educationLevel: data.educationLevel || '',
      major: data.major || '',
      graduationYear: data.graduationYear?.toString() || '',
      region: data.region || '',
    },
  });

  // Track selected options for stats display
  const [selectedOptions, setSelectedOptions] = useState({
    educationLevel: data.educationLevel || '',
    major: data.major || '',
    graduationYear: data.graduationYear?.toString() || '',
    region: data.region || '',
  });

  // 统计数据状态
  const [stats, setStats] = useState<LocalStats>({
    educationLevel: [],
    major: [],
    graduationYear: [],
    region: [],
    totalResponses: 0
  });
  const [isLoadingStats, setIsLoadingStats] = useState(false);

  // API调试配置
  const apiDebug = useApiDebug({
    pageName: '问卷页面',
    requests: [
      {
        id: 'questionnaire-stats',
        name: '问卷统计数据',
        endpoint: '/api/questionnaire/stats',
        method: 'GET',
        expectedStructure: DataStructures.questionnaireStats,
        testFunction: async () => {
          const response = await getQuestionnaireStats();
          return response;
        }
      }
    ]
  });

  // 获取统计数据
  const fetchQuestionnaireStats = async () => {
    try {
      setIsLoadingStats(true);
      apiDebug.updateRequestStatus('questionnaire-stats', 'pending');

      const response = await getQuestionnaireStats({
        page: '问卷页面',
        component: 'PersonalInfoModule'
      });

      apiDebug.updateRequestStatus('questionnaire-stats', 'success', response);

      if (response.success && response.statistics) {
        const { statistics } = response;

        // 转换数据格式，使用后端已经计算好的百分比
        const convertToLocalFormat = (items: Array<{ name: string; count: number; percentage?: number }>) => {
          return items.map(item => ({
            label: item.name,
            count: item.count,
            percentage: item.percentage || 0
          }));
        };

        setStats({
          totalResponses: statistics.totalResponses,
          educationLevel: convertToLocalFormat(statistics.educationLevels || []),
          region: convertToLocalFormat(statistics.regions || []),
          major: convertToLocalFormat(statistics.majors || []),
          graduationYear: convertToLocalFormat(statistics.graduationYears || [])
        });
      }
    } catch (error) {
      console.error('获取问卷统计数据失败:', error);
      apiDebug.updateRequestStatus('questionnaire-stats', 'error', undefined, error instanceof Error ? error.message : String(error));
    } finally {
      setIsLoadingStats(false);
    }
  };

  // Update stats when form values change
  useEffect(() => {
    const subscription = form.watch((value) => {
      setSelectedOptions({
        educationLevel: value.educationLevel || '',
        major: value.major || '',
        graduationYear: value.graduationYear || '',
        region: value.region || '',
      });

      // Update parent component with form values
      if (value.educationLevel && value.major && value.graduationYear && value.region) {
        onUpdate({
          educationLevel: value.educationLevel,
          major: value.major,
          graduationYear: parseInt(value.graduationYear),
          region: value.region,
        });
      }
    });

    return () => subscription.unsubscribe();
  }, [form, onUpdate]);

  // 组件初始化时获取统计数据
  useEffect(() => {
    fetchQuestionnaireStats();
  }, []);

  // Prepare stats data with selected indicators
  const educationLevelStats = stats.educationLevel.map(stat => ({
    ...stat,
    isSelected: stat.label === selectedOptions.educationLevel,
  }));

  const majorStats = stats.major.map(stat => ({
    ...stat,
    isSelected: stat.label === selectedOptions.major,
  }));

  const graduationYearStats = stats.graduationYear.map(stat => ({
    ...stat,
    isSelected: stat.label === selectedOptions.graduationYear,
  }));

  const regionStats = stats.region.map(stat => ({
    ...stat,
    isSelected: stat.label === selectedOptions.region,
  }));



  return (
    <div>


      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <div className="md:col-span-2">
        <Form {...form}>
          <form className="space-y-6">
            <QuestionItem
              number={1}
              title="您的最高学历是?"
              required
            >
              <FormField
                control={form.control}
                name="educationLevel"
                render={({ field }) => (
                  <FormItem>
                    <FormControl>
                      <RadioGroup
                        onValueChange={field.onChange}
                        defaultValue={field.value}
                        className="flex flex-col space-y-2"
                      >
                        <FormItem className="flex items-center space-x-3 space-y-0">
                          <FormControl>
                            <RadioGroupItem value="高中/中专" />
                          </FormControl>
                          <FormLabel className="font-normal">高中/中专</FormLabel>
                        </FormItem>
                        <FormItem className="flex items-center space-x-3 space-y-0">
                          <FormControl>
                            <RadioGroupItem value="大专" />
                          </FormControl>
                          <FormLabel className="font-normal">大专</FormLabel>
                        </FormItem>
                        <FormItem className="flex items-center space-x-3 space-y-0">
                          <FormControl>
                            <RadioGroupItem value="本科" />
                          </FormControl>
                          <FormLabel className="font-normal">本科</FormLabel>
                        </FormItem>
                        <FormItem className="flex items-center space-x-3 space-y-0">
                          <FormControl>
                            <RadioGroupItem value="硕士" />
                          </FormControl>
                          <FormLabel className="font-normal">硕士</FormLabel>
                        </FormItem>
                        <FormItem className="flex items-center space-x-3 space-y-0">
                          <FormControl>
                            <RadioGroupItem value="博士" />
                          </FormControl>
                          <FormLabel className="font-normal">博士</FormLabel>
                        </FormItem>
                      </RadioGroup>
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </QuestionItem>

            <QuestionItem
              number={2}
              title="您的专业大类是?"
              required
            >
              <FormField
                control={form.control}
                name="major"
                render={({ field }) => (
                  <FormItem>
                    <Select
                      onValueChange={field.onChange}
                      defaultValue={field.value}
                    >
                      <FormControl>
                        <SelectTrigger>
                          <SelectValue placeholder="请选择专业大类" />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        <SelectItem value="理学">理学</SelectItem>
                        <SelectItem value="工学">工学</SelectItem>
                        <SelectItem value="文学">文学</SelectItem>
                        <SelectItem value="历史学">历史学</SelectItem>
                        <SelectItem value="哲学">哲学</SelectItem>
                        <SelectItem value="经济学">经济学</SelectItem>
                        <SelectItem value="管理学">管理学</SelectItem>
                        <SelectItem value="法学">法学</SelectItem>
                        <SelectItem value="教育学">教育学</SelectItem>
                        <SelectItem value="医学">医学</SelectItem>
                        <SelectItem value="农学">农学</SelectItem>
                        <SelectItem value="艺术学">艺术学</SelectItem>
                        <SelectItem value="其他">其他</SelectItem>
                      </SelectContent>
                    </Select>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </QuestionItem>

            <QuestionItem
              number={3}
              title="您的毕业年份是?"
              required
            >
              <FormField
                control={form.control}
                name="graduationYear"
                render={({ field }) => (
                  <FormItem>
                    <Select
                      onValueChange={field.onChange}
                      defaultValue={field.value}
                    >
                      <FormControl>
                        <SelectTrigger>
                          <SelectValue placeholder="请选择毕业年份" />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        <SelectItem value="2023">2023年</SelectItem>
                        <SelectItem value="2022">2022年</SelectItem>
                        <SelectItem value="2021">2021年</SelectItem>
                        <SelectItem value="2020">2020年</SelectItem>
                        <SelectItem value="2019">2019年</SelectItem>
                        <SelectItem value="2018">2018年及以前</SelectItem>
                        <SelectItem value="0">尚未毕业</SelectItem>
                      </SelectContent>
                    </Select>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </QuestionItem>

            <QuestionItem
              number={4}
              title="您目前所在/期望工作的地区是?"
              required
            >
              <FormField
                control={form.control}
                name="region"
                render={({ field }) => (
                  <FormItem>
                    <Select
                      onValueChange={field.onChange}
                      defaultValue={field.value}
                    >
                      <FormControl>
                        <SelectTrigger>
                          <SelectValue placeholder="请选择地区" />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        <SelectItem value="北上广深">北上广深</SelectItem>
                        <SelectItem value="省会城市">省会城市</SelectItem>
                        <SelectItem value="二线城市">二线城市</SelectItem>
                        <SelectItem value="三四线城市">三四线城市</SelectItem>
                        <SelectItem value="县城或乡镇">县城或乡镇</SelectItem>
                        <SelectItem value="海外">海外</SelectItem>
                        <SelectItem value="其他">其他</SelectItem>
                      </SelectContent>
                    </Select>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </QuestionItem>
          </form>
        </Form>
      </div>

      <div className="space-y-6">

        <AnswerStats
          title="学历分布"
          options={educationLevelStats}
          isLoading={isLoadingStats}
        />

        <AnswerStats
          title="专业分布"
          options={majorStats}
          isLoading={isLoadingStats}
        />

        <AnswerStats
          title="毕业年份分布"
          options={graduationYearStats}
          isLoading={isLoadingStats}
        />

        <AnswerStats
          title="地区分布"
          options={regionStats}
          isLoading={isLoadingStats}
        />
      </div>
      </div>
    </div>
  );
}
