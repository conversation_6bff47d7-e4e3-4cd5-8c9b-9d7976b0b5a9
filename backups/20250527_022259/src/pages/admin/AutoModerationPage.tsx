import React, { useState } from 'react';
import AdminLayout from '@/components/layouts/AdminLayout';
import { Tabs, TabsContent, Ta<PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import { useToast } from '@/components/ui/use-toast';
import { Shield, AlertTriangle, Settings, BarChart, History } from 'lucide-react';
import AutoModerationConfigPanel from '@/components/admin/AutoModerationConfigPanel';
import AutoModerationStatsDashboard from '@/components/admin/AutoModerationStatsDashboard';
import AutoModerationHistoryTable from '@/components/admin/AutoModerationHistoryTable';

/**
 * 自动审核管理页面
 *
 * 用于管理员配置和监控自动审核功能
 */
const AutoModerationPage: React.FC = () => {
  const { toast } = useToast();
  const [activeTab, setActiveTab] = useState<string>('config');

  return (
    <AdminLayout>
      <div className="container mx-auto py-6">
        <div className="flex items-center justify-between mb-6">
          <div>
            <h1 className="text-3xl font-bold">自动审核管理</h1>
            <p className="text-muted-foreground">
              配置和监控自动审核功能
            </p>
          </div>
        </div>

        <Alert className="mb-6">
          <AlertTriangle className="h-4 w-4" />
          <AlertTitle>注意</AlertTitle>
          <AlertDescription>
            自动审核功能会自动处理新提交的内容。请谨慎配置参数，以避免误判。建议定期检查审核历史和统计数据，以评估自动审核的效果。
          </AlertDescription>
        </Alert>

        <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
          <TabsList className="grid w-full grid-cols-3">
            <TabsTrigger value="config">
              <Settings className="w-4 h-4 mr-2" />
              配置
            </TabsTrigger>
            <TabsTrigger value="stats">
              <BarChart className="w-4 h-4 mr-2" />
              统计
            </TabsTrigger>
            <TabsTrigger value="history">
              <History className="w-4 h-4 mr-2" />
              历史
            </TabsTrigger>
          </TabsList>

          <TabsContent value="config" className="mt-6">
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              <div className="md:col-span-2">
                <AutoModerationConfigPanel />
              </div>

              <div>
                <Card>
                  <CardHeader>
                    <CardTitle className="flex items-center">
                      <Shield className="w-5 h-5 mr-2" />
                      自动审核说明
                    </CardTitle>
                    <CardDescription>
                      自动审核功能的工作原理和最佳实践
                    </CardDescription>
                  </CardHeader>
                  <CardContent>
                    <h3 className="text-sm font-medium mb-2">工作原理</h3>
                    <p className="text-sm mb-4">
                      自动审核功能使用AI技术自动分析新提交的内容，根据配置的参数决定是否自动通过、拒绝或标记为需要人工审核。
                    </p>

                    <h3 className="text-sm font-medium mb-2">配置参数说明</h3>
                    <ul className="list-disc list-inside text-sm space-y-1 mb-4">
                      <li>置信度阈值：低于此值的审核结果将被标记为需要人工审核</li>
                      <li>自动通过阈值：高于此值的安全内容将被自动通过</li>
                      <li>自动拒绝阈值：高于此值的不安全内容将被自动拒绝</li>
                      <li>严重程度阈值：根据问题的严重程度调整自动拒绝的标准</li>
                    </ul>

                    <h3 className="text-sm font-medium mb-2">最佳实践</h3>
                    <ul className="list-disc list-inside text-sm space-y-1">
                      <li>定期检查审核历史，评估自动审核的效果</li>
                      <li>适当调整参数，平衡审核效率和准确性</li>
                      <li>对于重要内容，建议设置较高的阈值，确保人工审核</li>
                      <li>定期测试不同类型的内容，确保审核效果符合预期</li>
                    </ul>
                  </CardContent>
                </Card>
              </div>
            </div>
          </TabsContent>

          <TabsContent value="stats" className="mt-6">
            <AutoModerationStatsDashboard />
          </TabsContent>

          <TabsContent value="history" className="mt-6">
            <AutoModerationHistoryTable />
          </TabsContent>
        </Tabs>
      </div>
    </AdminLayout>
  );
};

export default AutoModerationPage;
