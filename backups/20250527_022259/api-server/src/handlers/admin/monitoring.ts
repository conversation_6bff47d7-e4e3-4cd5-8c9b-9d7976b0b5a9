/**
 * 监控相关API处理函数
 */

import { Env } from '../../types';
import { verifyToken } from '../../utils/auth';
import { generateRandomData } from '../../utils/mockData';

/**
 * 获取监控统计数据
 */
export async function handleGetMonitoringStats(request: Request, env: Env): Promise<Response> {
  // 验证权限
  const authHeader = request.headers.get('Authorization');
  if (!authHeader || !authHeader.startsWith('Bearer ')) {
    return new Response(JSON.stringify({
      success: false,
      error: '未授权'
    }), {
      status: 401,
      headers: { 'Content-Type': 'application/json' }
    });
  }

  const token = authHeader.substring(7);
  const tokenResult = verifyToken(token, env.JWT_SECRET);

  if (!tokenResult.valid || !tokenResult.payload) {
    return new Response(JSON.stringify({
      success: false,
      error: '无效的令牌'
    }), {
      status: 401,
      headers: { 'Content-Type': 'application/json' }
    });
  }

  // 验证角色权限
  const role = tokenResult.payload.role;
  if (role !== 'superadmin') {
    return new Response(JSON.stringify({
      success: false,
      error: '没有权限'
    }), {
      status: 403,
      headers: { 'Content-Type': 'application/json' }
    });
  }

  // 获取查询参数
  const url = new URL(request.url);
  const startTime = url.searchParams.get('startTime');
  const endTime = url.searchParams.get('endTime');

  try {
    // 在实际应用中，这里应该从数据库或监控系统获取真实数据
    // 现在使用模拟数据
    const stats = {
      totalRequests: Math.floor(Math.random() * 10000) + 5000,
      avgResponseTime: Math.floor(Math.random() * 100) + 50,
      errorRate: Math.random() * 5,
      cpuUsage: Math.floor(Math.random() * 60) + 10,
      memoryUsage: Math.floor(Math.random() * 70) + 20,
      dbSize: Math.floor(Math.random() * 1024 * 1024 * 500) + 1024 * 1024 * 100,
    };

    // 生成请求数据
    const requestsData = generateRandomData(
      startTime ? new Date(startTime) : new Date(Date.now() - 7 * 24 * 60 * 60 * 1000),
      endTime ? new Date(endTime) : new Date(),
      [
        { type: '总请求数', min: 100, max: 500 },
        { type: 'GET请求', min: 50, max: 300 },
        { type: 'POST请求', min: 20, max: 150 },
        { type: 'PUT请求', min: 10, max: 50 },
        { type: 'DELETE请求', min: 5, max: 30 }
      ]
    );

    // 生成错误率数据
    const errorsData = generateRandomData(
      startTime ? new Date(startTime) : new Date(Date.now() - 7 * 24 * 60 * 60 * 1000),
      endTime ? new Date(endTime) : new Date(),
      [
        { type: '错误率', min: 0, max: 5 },
        { type: '4xx错误', min: 0, max: 3 },
        { type: '5xx错误', min: 0, max: 2 }
      ]
    );

    // 生成状态码分布数据
    const statusCodesData = [
      { type: '200 OK', value: 75 },
      { type: '201 Created', value: 10 },
      { type: '204 No Content', value: 5 },
      { type: '400 Bad Request', value: 4 },
      { type: '401 Unauthorized', value: 3 },
      { type: '403 Forbidden', value: 1 },
      { type: '404 Not Found', value: 1.5 },
      { type: '500 Server Error', value: 0.5 }
    ];

    // 生成性能指标数据
    const performanceData = generateRandomData(
      startTime ? new Date(startTime) : new Date(Date.now() - 7 * 24 * 60 * 60 * 1000),
      endTime ? new Date(endTime) : new Date(),
      [
        { type: '平均响应时间', min: 50, max: 150 },
        { type: 'P95响应时间', min: 100, max: 300 },
        { type: 'P99响应时间', min: 200, max: 500 }
      ]
    );

    // 确定系统状态
    let systemStatus: 'healthy' | 'warning' | 'critical' | 'unknown' = 'healthy';
    if (stats.errorRate > 3 || stats.avgResponseTime > 200 || stats.cpuUsage > 80 || stats.memoryUsage > 80) {
      systemStatus = 'critical';
    } else if (stats.errorRate > 1 || stats.avgResponseTime > 100 || stats.cpuUsage > 60 || stats.memoryUsage > 60) {
      systemStatus = 'warning';
    }

    return new Response(JSON.stringify({
      success: true,
      systemStatus,
      stats,
      requestsData,
      errorsData,
      statusCodesData,
      performanceData
    }), {
      status: 200,
      headers: { 'Content-Type': 'application/json' }
    });
  } catch (error) {
    console.error('获取监控统计数据错误:', error);
    return new Response(JSON.stringify({
      success: false,
      error: '获取监控统计数据失败'
    }), {
      status: 500,
      headers: { 'Content-Type': 'application/json' }
    });
  }
}

/**
 * 获取日志数据
 */
export async function handleGetLogs(request: Request, env: Env): Promise<Response> {
  // 验证权限
  const authHeader = request.headers.get('Authorization');
  if (!authHeader || !authHeader.startsWith('Bearer ')) {
    return new Response(JSON.stringify({
      success: false,
      error: '未授权'
    }), {
      status: 401,
      headers: { 'Content-Type': 'application/json' }
    });
  }

  const token = authHeader.substring(7);
  const tokenResult = verifyToken(token, env.JWT_SECRET);

  if (!tokenResult.valid || !tokenResult.payload) {
    return new Response(JSON.stringify({
      success: false,
      error: '无效的令牌'
    }), {
      status: 401,
      headers: { 'Content-Type': 'application/json' }
    });
  }

  // 验证角色权限
  const role = tokenResult.payload.role;
  if (role !== 'superadmin') {
    return new Response(JSON.stringify({
      success: false,
      error: '没有权限'
    }), {
      status: 403,
      headers: { 'Content-Type': 'application/json' }
    });
  }

  // 获取查询参数
  const url = new URL(request.url);
  const type = url.searchParams.get('type') || 'request';
  const level = url.searchParams.get('level');
  const search = url.searchParams.get('search');
  const startTime = url.searchParams.get('startTime');
  const endTime = url.searchParams.get('endTime');
  const page = parseInt(url.searchParams.get('page') || '1', 10);
  const pageSize = parseInt(url.searchParams.get('pageSize') || '10', 10);

  try {
    // 在实际应用中，这里应该从日志存储系统获取真实数据
    // 现在使用模拟数据
    const logs = [];
    const total = 100;
    const logTypes = {
      request: ['GET', 'POST', 'PUT', 'DELETE', 'PATCH'],
      error: ['info', 'warning', 'error', 'critical'],
      alert: ['info', 'warning', 'error', 'critical'],
      audit: ['login', 'logout', 'create', 'update', 'delete']
    };

    const statusCodes = [200, 201, 204, 400, 401, 403, 404, 500];
    const paths = ['/api/v1/users', '/api/v1/surveys', '/api/v1/stories', '/api/v1/tags', '/api/v1/stats'];
    const ips = ['***********', '***********', '********', '********', '127.0.0.1'];
    const users = ['admin', 'superadmin', 'reviewer1', 'reviewer2'];
    const actions = ['创建用户', '更新用户', '删除用户', '创建问卷', '更新问卷', '删除问卷'];
    const messages = [
      '操作成功',
      '操作失败',
      '权限不足',
      '资源不存在',
      '服务器错误',
      '数据库连接失败',
      'API请求超时'
    ];

    // 生成模拟日志
    for (let i = 0; i < pageSize; i++) {
      const timestamp = new Date(
        Date.now() - Math.floor(Math.random() * 7 * 24 * 60 * 60 * 1000)
      ).toISOString();

      let log: any = {
        id: `log-${(page - 1) * pageSize + i + 1}`,
        timestamp
      };

      if (type === 'request') {
        const method = logTypes.request[Math.floor(Math.random() * logTypes.request.length)];
        const path = paths[Math.floor(Math.random() * paths.length)];
        const status = statusCodes[Math.floor(Math.random() * statusCodes.length)];
        const duration = Math.floor(Math.random() * 500) + 10;
        const ip = ips[Math.floor(Math.random() * ips.length)];

        log = {
          ...log,
          method,
          path,
          status,
          duration,
          ip,
          query: { page: 1, limit: 10 },
          headers: {
            'user-agent': 'Mozilla/5.0',
            'content-type': 'application/json'
          }
        };
      } else if (type === 'error' || type === 'alert') {
        const logLevel = logTypes.error[Math.floor(Math.random() * logTypes.error.length)];
        if (level && logLevel !== level) continue;

        const message = messages[Math.floor(Math.random() * messages.length)];
        const ip = ips[Math.floor(Math.random() * ips.length)];

        log = {
          ...log,
          level: logLevel,
          message,
          ip,
          error: {
            name: 'Error',
            message,
            stack: 'Error: ' + message + '\n    at handleRequest (/src/handlers/index.ts:42:15)'
          }
        };
      } else if (type === 'audit') {
        const username = users[Math.floor(Math.random() * users.length)];
        const action = actions[Math.floor(Math.random() * actions.length)];
        const result = Math.random() > 0.2 ? 'success' : 'failure';
        const ip = ips[Math.floor(Math.random() * ips.length)];

        log = {
          ...log,
          username,
          role: username.includes('admin') ? 'admin' : 'reviewer',
          action,
          target: 'user123',
          result,
          ip,
          details: {
            before: { name: 'Old Name', status: 'active' },
            after: { name: 'New Name', status: 'inactive' }
          }
        };
      }

      // 搜索过滤
      if (search) {
        const searchLower = search.toLowerCase();
        const logString = JSON.stringify(log).toLowerCase();
        if (!logString.includes(searchLower)) continue;
      }

      // 时间范围过滤
      if (startTime && log.timestamp < startTime) continue;
      if (endTime && log.timestamp > endTime) continue;

      logs.push(log);
    }

    return new Response(JSON.stringify({
      success: true,
      logs,
      total,
      page,
      pageSize
    }), {
      status: 200,
      headers: { 'Content-Type': 'application/json' }
    });
  } catch (error) {
    console.error('获取日志数据错误:', error);
    return new Response(JSON.stringify({
      success: false,
      error: '获取日志数据失败'
    }), {
      status: 500,
      headers: { 'Content-Type': 'application/json' }
    });
  }
}
