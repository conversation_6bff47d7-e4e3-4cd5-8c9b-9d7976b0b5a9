#!/usr/bin/env node

/**
 * 快速修复脚本：只导入问卷响应数据到生产环境
 * 解决当前生产环境数据不完整的问题
 */

import sqlite3 from 'sqlite3';
import { open } from 'sqlite';
import fs from 'fs/promises';
import { execSync } from 'child_process';

const LOCAL_DB_PATH = './database.db';

async function openLocalDatabase() {
  return await open({
    filename: LOCAL_DB_PATH,
    driver: sqlite3.Database
  });
}

async function exportQuestionnaireData() {
  console.log('🚀 导出本地问卷数据...');
  
  const db = await openLocalDatabase();
  
  // 获取问卷响应数据
  const responses = await db.all(`
    SELECT 
      id, education_level_display, region_display, employment_status,
      major_display, salary_range, created_at, updated_at
    FROM questionnaire_responses_v2
  `);
  
  console.log(`📊 找到 ${responses.length} 条问卷响应记录`);
  
  await db.close();
  
  // 生成SQL插入语句
  const sqlStatements = [];
  
  // 清空现有数据
  sqlStatements.push('-- 清空问卷响应数据');
  sqlStatements.push('DELETE FROM questionnaire_responses_v2;');
  sqlStatements.push('');
  
  // 插入新数据
  sqlStatements.push(`-- 插入 ${responses.length} 条问卷响应记录`);
  
  for (const row of responses) {
    const values = [
      `'${row.id}'`,
      row.education_level_display ? `'${row.education_level_display.replace(/'/g, "''")}'` : 'NULL',
      row.region_display ? `'${row.region_display.replace(/'/g, "''")}'` : 'NULL', 
      row.employment_status ? `'${row.employment_status.replace(/'/g, "''")}'` : 'NULL',
      row.major_display ? `'${row.major_display.replace(/'/g, "''")}'` : 'NULL',
      row.salary_range ? `'${row.salary_range.replace(/'/g, "''")}'` : 'NULL',
      row.created_at ? `'${row.created_at}'` : 'CURRENT_TIMESTAMP',
      row.updated_at ? `'${row.updated_at}'` : 'CURRENT_TIMESTAMP'
    ];
    
    const insertSQL = `INSERT INTO questionnaire_responses_v2 (id, education_level_display, region_display, employment_status, major_display, salary_range, created_at, updated_at) VALUES (${values.join(', ')});`;
    sqlStatements.push(insertSQL);
  }
  
  // 保存SQL文件
  const sqlFile = './quick-fix-questionnaire.sql';
  await fs.writeFile(sqlFile, sqlStatements.join('\n'));
  
  console.log(`✅ SQL脚本已生成: ${sqlFile}`);
  
  return sqlFile;
}

async function importToProduction(sqlFile) {
  console.log('📤 导入数据到生产环境...');
  
  try {
    const command = `wrangler d1 execute college-employment-survey-realapi --file="${sqlFile}" --remote`;
    console.log(`执行命令: ${command}`);
    
    const output = execSync(command, { 
      encoding: 'utf8',
      stdio: 'pipe'
    });
    
    console.log('✅ 数据导入成功！');
    console.log(output);
    
  } catch (error) {
    console.error('❌ 数据导入失败:', error.message);
    console.error('输出:', error.stdout);
    console.error('错误:', error.stderr);
    throw error;
  }
}

async function verifyImport() {
  console.log('🔍 验证导入结果...');
  
  try {
    const command = `wrangler d1 execute college-employment-survey-realapi --command="SELECT COUNT(*) as count FROM questionnaire_responses_v2;" --remote --json`;
    const output = execSync(command, { encoding: 'utf8' });
    const result = JSON.parse(output);
    const count = result[0]?.results?.[0]?.count || 0;
    
    console.log(`✅ 生产环境问卷响应数据: ${count} 条记录`);
    
    // 测试API
    console.log('🧪 测试API响应...');
    const apiCommand = `curl -s "https://college-employment-survey-realapi.aibook2099.workers.dev/api/questionnaire/stats"`;
    const apiOutput = execSync(apiCommand, { encoding: 'utf8' });
    const apiResult = JSON.parse(apiOutput);
    
    console.log(`📊 API返回总数: ${apiResult.data?.total || 0}`);
    console.log(`📈 教育水平分布: ${apiResult.data?.educationLevels?.length || 0} 种`);
    console.log(`🌍 地区分布: ${apiResult.data?.regions?.length || 0} 个地区`);
    
    return count;
    
  } catch (error) {
    console.error('❌ 验证失败:', error.message);
    throw error;
  }
}

async function main() {
  try {
    console.log('🎯 开始快速修复生产环境问卷数据...\n');
    
    // 1. 导出本地数据
    const sqlFile = await exportQuestionnaireData();
    
    // 2. 导入到生产环境
    await importToProduction(sqlFile);
    
    // 3. 验证结果
    const count = await verifyImport();
    
    console.log('\n🎉 修复完成！');
    console.log(`✅ 成功导入 ${count} 条问卷响应记录`);
    console.log('🌐 生产环境API现在应该显示正确的数据');
    
    // 清理临时文件
    await fs.unlink(sqlFile);
    console.log('🧹 已清理临时文件');
    
  } catch (error) {
    console.error('\n❌ 修复失败:', error.message);
    process.exit(1);
  }
}

main().catch(console.error);
