/**
 * 测试数据生成服务
 * 用于生成随机的问卷和故事数据
 */

import { getCurrentConfig } from '@/config/api.config';

// 环境检测和权限控制
const isAllowedTestEnvironment = () => {
  const hostname = window.location.hostname;

  // 允许的环境：
  // 1. 本地开发环境
  if (hostname.includes('localhost') || hostname.includes('127.0.0.1') || import.meta.env.DEV) {
    return true;
  }

  // 2. 测试/预发布环境
  if (hostname.includes('staging') || hostname.includes('test')) {
    return true;
  }

  // 3. Cloudflare Pages 预览环境（包含随机字符串的域名）
  if (hostname.includes('.pages.dev')) {
    return true;
  }

  // 4. 特定的测试域名（可以根据需要添加）
  const allowedTestDomains = [
    'test.college-employment-survey.com',
    'demo.college-employment-survey.com',
    // 可以添加更多测试域名
  ];

  if (allowedTestDomains.some(domain => hostname.includes(domain))) {
    return true;
  }

  return false;
};

// 检查是否允许在当前环境使用测试数据生成器
const checkEnvironmentPermission = () => {
  if (!isAllowedTestEnvironment()) {
    throw new Error('测试数据生成器仅在测试环境中可用。当前环境不在允许列表中。');
  }
};

// 基础配置 - 使用与主应用相同的API配置
const getApiBaseUrl = () => {
  try {
    const config = getCurrentConfig();
    return config.api.baseURL;
  } catch (error) {
    console.warn('获取API配置失败，使用默认值:', error);
    return import.meta.env.VITE_API_BASE_URL || 'https://college-employment-survey.aibook2099.workers.dev';
  }
};

const API_BASE_URL = getApiBaseUrl();

// 随机数据模板
const EDUCATION_LEVELS = ['高中/中专', '大专', '本科', '硕士', '博士'];
const MAJORS = [
  '计算机科学与技术', '软件工程', '信息管理与信息系统', '电子信息工程',
  '机械工程', '电气工程', '土木工程', '化学工程', '生物工程',
  '经济学', '金融学', '会计学', '市场营销', '人力资源管理',
  '法学', '汉语言文学', '英语', '新闻学', '广告学',
  '医学', '护理学', '药学', '心理学', '教育学'
];
const REGIONS = [
  '北京市', '上海市', '广东省', '浙江省', '江苏省', '山东省',
  '河南省', '四川省', '湖北省', '湖南省', '河北省', '福建省',
  '安徽省', '江西省', '辽宁省', '黑龙江省', '吉林省', '山西省',
  '陕西省', '甘肃省', '青海省', '新疆维吾尔自治区', '西藏自治区'
];
const EMPLOYMENT_STATUS = ['已就业', '求职中', '继续深造', '创业', '其他'];
const INDUSTRIES = [
  '互联网', '金融', '教育', '制造业', '医疗健康', '房地产',
  '咨询', '政府机关', '国有企业', '外企', '民营企业'
];
const SALARY_RANGES = ['3-5万', '5-8万', '8-12万', '12-20万', '20-30万', '30万以上'];

const ADVICE_TEMPLATES = [
  '建议学弟学妹们多做项目，提升实际开发能力',
  '要注重理论与实践相结合，多参加实习',
  '学好英语很重要，很多好公司都需要英语能力',
  '保持学习的心态，技术更新很快',
  '多参加校园招聘，机会很多',
  '简历要突出重点，展示自己的优势',
  '面试前要做好充分准备，了解公司背景',
  '要有明确的职业规划，不要盲目投简历'
];

const OBSERVATION_TEMPLATES = [
  '当前互联网行业竞争激烈，但技术人才需求依然很大',
  '今年就业形势确实比较严峻，要做好心理准备',
  '大厂门槛高，中小企业机会多',
  '新兴行业机会多，传统行业相对稳定',
  '数据分析岗位需求大，但要求也高',
  '远程工作越来越普遍，要适应新的工作模式',
  '技能比学历更重要，要注重能力提升',
  '人工智能对就业市场影响很大，要跟上技术趋势'
];

const STORY_TITLES = [
  '我的求职经历分享',
  '职业转换之路',
  '实习成长记录',
  '创业心得体会',
  '跨专业求职经验',
  '面试经验总结',
  '小公司大机会',
  '职场成长感悟',
  '就业路上的思考',
  '工作中的收获'
];

const STORY_CONTENT_TEMPLATES = [
  '刚开始求职的时候，我也很迷茫，不知道自己适合什么工作。通过不断的尝试和学习，我逐渐找到了自己的方向。现在回想起来，那段经历虽然辛苦，但收获很大。每一次面试都是学习的机会，每一次拒绝都让我更加清楚自己的不足。最终找到心仪工作的那一刻，所有的努力都值得了。',
  '大学四年学的是理论知识，真正工作后才发现实践能力的重要性。建议学弟学妹们在校期间多参加实习，提前了解职场环境。实习不仅能让你了解行业现状，还能帮你建立人脉关系。我在实习期间学到的东西，比课堂上学到的更实用。',
  '面试是一个双向选择的过程，不仅是公司在选择你，你也在选择公司。要对自己有信心，同时也要客观评估自己的能力。准备充分的简历和作品集很重要，但更重要的是要展现真实的自己。诚实回答问题，展示你的学习能力和成长潜力。',
  '职业规划很重要，但也要保持灵活性。市场在变化，我们也要适应变化，不断学习新的技能。我原本计划做技术开发，但后来发现自己更适合产品管理。转换方向虽然有挑战，但找到真正适合自己的道路更重要。'
];

// 随机选择函数
const randomChoice = <T>(array: T[]): T => {
  return array[Math.floor(Math.random() * array.length)];
};

// 随机数字范围
const randomInt = (min: number, max: number): number => {
  return Math.floor(Math.random() * (max - min + 1)) + min;
};

// 生成随机问卷数据
export const generateRandomQuestionnaire = (status: 'pending' | 'approved' = 'approved') => {
  const educationLevel = randomChoice(EDUCATION_LEVELS);
  const major = randomChoice(MAJORS);
  const region = randomChoice(REGIONS);
  const employmentStatus = randomChoice(EMPLOYMENT_STATUS);
  const currentIndustry = randomChoice(INDUSTRIES);
  const salaryRange = randomChoice(SALARY_RANGES);

  return {
    // 1. Personal information (必需字段)
    educationLevel,
    major,
    graduationYear: randomInt(2020, 2024),
    region,

    // 2. Employment expectations
    expectedPosition: randomChoice(['软件工程师', '产品经理', '数据分析师', '运营专员']),
    expectedSalaryRange: salaryRange,
    expectedWorkHours: randomInt(8, 10),
    expectedVacationDays: randomInt(5, 15),

    // 3. Work experience
    employmentStatus,
    currentIndustry,
    currentPosition: '软件工程师',
    jobSatisfaction: randomInt(1, 5),

    // 4. Unemployment status
    unemploymentDuration: randomChoice(['无', '1-3个月', '3-6个月', '6个月以上']),
    unemploymentReason: randomChoice(['主动离职', '公司裁员', '合同到期', '其他']),
    jobHuntingDifficulty: randomInt(1, 5),

    // 5. Career change and reflection
    regretMajor: Math.random() > 0.5,
    preferredMajor: randomChoice(['计算机科学', '数据科学', '商业管理', '其他']),
    careerChangeIntention: Math.random() > 0.5,
    careerChangeTarget: randomChoice(['技术转管理', '转行互联网', '创业', '其他']),

    // 6. Advice and feedback
    adviceForStudents: randomChoice(ADVICE_TEMPLATES),
    observationOnEmployment: randomChoice(OBSERVATION_TEMPLATES),

    // 7. Submission options
    isAnonymous: Math.random() > 0.3, // 70% 匿名

    // 测试数据标识
    _testData: true,
    _source: 'test_generator',
    _status: status
  };
};

// 生成随机故事数据
export const generateRandomStory = (status: 'pending' | 'approved' = 'approved') => {
  const educationLevel = randomChoice(EDUCATION_LEVELS);
  const industry = randomChoice(INDUSTRIES);
  const title = randomChoice(STORY_TITLES);
  const content = randomChoice(STORY_CONTENT_TEMPLATES);
  const isAnonymous = Math.random() > 0.4; // 60% 匿名

  // 生成匿名账号（A+B格式）
  const generateAnonymousName = () => {
    const letters = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ';
    const letter1 = letters[Math.floor(Math.random() * letters.length)];
    const letter2 = letters[Math.floor(Math.random() * letters.length)];
    // 确保两个字母不同，增加多样性
    let finalLetter2 = letter2;
    while (finalLetter2 === letter1) {
      finalLetter2 = letters[Math.floor(Math.random() * letters.length)];
    }
    return `${letter1}+${finalLetter2}`;
  };

  // 确保标题长度在5-50字符之间
  const finalTitle = title.length > 50 ? title.substring(0, 47) + '...' : title;

  // 确保内容长度在20-2000字符之间
  let finalContent = content;
  if (finalContent.length < 20) {
    finalContent = content + ' ' + randomChoice(STORY_CONTENT_TEMPLATES);
  }
  if (finalContent.length > 2000) {
    finalContent = finalContent.substring(0, 1997) + '...';
  }

  return {
    title: finalTitle,
    content: finalContent,
    category: randomChoice(['求职经历', '职业转换', '创业经历', '实习体验']),
    educationLevel,
    industry,
    tags: ['求职经验', '职业规划', '个人成长'],
    isAnonymous,
    // 测试数据标识
    _testData: true,
    _source: 'test_generator',
    _status: status
  };
};

// 生成匿名身份标识
const generateAnonymousIdentity = () => {
  // 生成A值（11位数字）
  const identityA = Math.floor(10000000000 + Math.random() * 90000000000).toString();

  // 生成B值（4位或6位数字）
  const isShortB = Math.random() > 0.5;
  const identityB = isShortB
    ? Math.floor(1000 + Math.random() * 9000).toString()
    : Math.floor(100000 + Math.random() * 900000).toString();

  return { identityA, identityB };
};

// 生成UUID
const generateUUID = () => {
  return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function(c) {
    const r = Math.random() * 16 | 0;
    const v = c == 'x' ? r : (r & 0x3 | 0x8);
    return v.toString(16);
  });
};

// API 调用函数
export class TestDataGeneratorService {
  // 创建模拟结果（用于速率限制时的模拟模式）
  static createSimulationResult(type: 'questionnaire' | 'story', submissionData: any, anonymousIdentity: any) {
    const mockId = `MOCK_${type.toUpperCase()}_${Date.now()}`;
    return {
      success: true,
      data: {
        [type === 'questionnaire' ? 'responseId' : 'storyId']: mockId,
        anonymousIdentity,
        submissionFlow: `模拟模式：${type === 'questionnaire' ? '填写问卷 → 模拟提交 → 模拟分配ID → 模拟生成心声' : '填写故事 → 模拟提交 → 模拟分配ID'}`,
        testDataInfo: {
          generated: true,
          timestamp: new Date().toISOString(),
          type: type,
          simulation: true
        },
        message: '⚠️ 模拟模式：数据未实际提交到服务器'
      }
    };
  }
  // 模拟用户问卷提交流程
  static async simulateQuestionnaireSubmission(status: 'pending' | 'approved' = 'approved'): Promise<{ success: boolean; data?: any; error?: string }> {
    try {
      // 检查环境权限
      checkEnvironmentPermission();

      // 1. 生成匿名身份
      const { identityA, identityB } = generateAnonymousIdentity();

      // 2. 生成问卷数据
      const questionnaireData = generateRandomQuestionnaire(status);

      // 3. 添加匿名身份标识（模拟真实用户提交）
      const submissionData = {
        ...questionnaireData,
        identityA,
        identityB,
        isAnonymous: true,
        // 模拟测试数据标识
        _testData: true,
        _generatedAt: new Date().toISOString()
      };

      // 4. 提交问卷（模拟真实用户提交流程）
      // 真实流程：用户填写 → 提交到后端 → 后端分配内容ID → 自动生成心声 → 匿名身份绑定
      const response = await fetch(`${API_BASE_URL}/api/questionnaire/submit`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'X-Test-Data-Generator': 'true', // 标识这是测试数据生成器请求
        },
        body: JSON.stringify(submissionData)
      });

      const result = await response.json();

      if (!response.ok) {
        console.error('问卷提交失败:', {
          status: response.status,
          result: result,
          submissionData: submissionData
        });

        // 特殊处理速率限制错误
        if (response.status === 429) {
          const retryAfter = result.retryAfter || 60; // 默认1分钟
          const retryTime = retryAfter >= 60 ? Math.ceil(retryAfter / 60) + ' 分钟' : retryAfter + ' 秒';

          // 提供模拟模式选项
          const simulationResult = this.createSimulationResult('questionnaire', submissionData, { identityA, identityB });
          throw new Error(`速率限制：请等待 ${retryTime} 后重试。当前限制：每分钟1次请求。\n\n💡 提示：可以使用模拟模式继续测试（不会实际提交到服务器）`);
        }

        throw new Error(result.error || result.message || `HTTP ${response.status}: ${JSON.stringify(result)}`);
      }

      // 后端已完成：
      // 1. 创建问卷记录并分配 responseId（内容ID）
      // 2. 自动生成2条心声记录（各有独立的内容ID）
      // 3. 如果提供A+B，生成UUID并绑定所有相关内容
      return {
        success: true,
        data: {
          ...result,
          anonymousIdentity: { identityA, identityB },
          submissionFlow: '填写问卷 → 提交后端 → 分配内容ID → 自动生成心声 → 匿名身份绑定',
          testDataInfo: {
            generated: true,
            timestamp: new Date().toISOString(),
            type: 'questionnaire'
          }
        }
      };
    } catch (error) {
      console.error('模拟问卷提交失败:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : '未知错误'
      };
    }
  }

  // 模拟用户故事发布流程
  static async simulateStorySubmission(status: 'pending' | 'approved' = 'approved'): Promise<{ success: boolean; data?: any; error?: string }> {
    try {
      // 检查环境权限
      checkEnvironmentPermission();

      // 1. 生成匿名身份（模拟用户A+B组合）
      const { identityA, identityB } = generateAnonymousIdentity();

      // 2. 生成故事数据
      const storyData = generateRandomStory(status);

      // 3. 准备提交数据（不包含内容ID，由后端生成）
      const submissionData = {
        ...storyData,
        identityA,
        identityB,
        isAnonymous: true,
        // 模拟测试数据标识
        _testData: true,
        _generatedAt: new Date().toISOString()
      };

      // 4. 提交故事（模拟真实用户发布流程）
      // 真实流程：用户填写 → 提交到后端 → 后端分配内容ID → 匿名身份绑定
      const response = await fetch(`${API_BASE_URL}/api/story/submit`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'X-Test-Data-Generator': 'true', // 标识这是测试数据生成器请求
        },
        body: JSON.stringify(submissionData)
      });

      const result = await response.json();

      if (!response.ok) {
        console.error('故事提交失败:', {
          status: response.status,
          result: result,
          submissionData: submissionData
        });

        // 特殊处理速率限制错误
        if (response.status === 429) {
          const retryAfter = result.retryAfter || 60; // 默认1分钟
          const retryTime = retryAfter >= 60 ? Math.ceil(retryAfter / 60) + ' 分钟' : retryAfter + ' 秒';

          // 提供模拟模式选项
          const simulationResult = this.createSimulationResult('story', submissionData, { identityA, identityB });
          throw new Error(`速率限制：请等待 ${retryTime} 后重试。当前限制：每分钟1次请求。\n\n💡 提示：可以使用模拟模式继续测试（不会实际提交到服务器）`);
        }

        throw new Error(result.error || result.message || `HTTP ${response.status}: ${JSON.stringify(result)}`);
      }

      // 后端已完成：
      // 1. 创建故事记录并分配 storyId（内容ID）
      // 2. 如果提供A+B，生成UUID并绑定故事内容
      return {
        success: true,
        data: {
          ...result,
          anonymousIdentity: { identityA, identityB },
          submissionFlow: '填写故事 → 提交后端 → 分配内容ID → 匿名身份绑定',
          testDataInfo: {
            generated: true,
            timestamp: new Date().toISOString(),
            type: 'story'
          }
        }
      };
    } catch (error) {
      console.error('模拟故事发布失败:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : '未知错误'
      };
    }
  }

  // 生成并提交随机问卷（使用模拟用户流程）
  static async generateAndSubmitQuestionnaire(status: 'pending' | 'approved' = 'approved') {
    return await this.simulateQuestionnaireSubmission(status);
  }

  // 生成并提交随机故事（使用模拟用户流程）
  static async generateAndSubmitStory(status: 'pending' | 'approved' = 'approved') {
    return await this.simulateStorySubmission(status);
  }

  // 批量生成问卷
  static async batchGenerateQuestionnaires(count: number, status: 'pending' | 'approved' = 'approved') {
    const results = [];

    for (let i = 0; i < count; i++) {
      try {
        const result = await this.generateAndSubmitQuestionnaire(status);
        results.push(result);

        // 添加延迟避免请求过快
        if (i < count - 1) {
          await new Promise(resolve => setTimeout(resolve, 500));
        }
      } catch (error) {
        results.push({
          success: false,
          error: error instanceof Error ? error.message : '未知错误'
        });
      }
    }

    return results;
  }

  // 批量生成故事
  static async batchGenerateStories(count: number, status: 'pending' | 'approved' = 'approved') {
    const results = [];

    for (let i = 0; i < count; i++) {
      try {
        const result = await this.generateAndSubmitStory(status);
        results.push(result);

        // 添加延迟避免请求过快
        if (i < count - 1) {
          await new Promise(resolve => setTimeout(resolve, 500));
        }
      } catch (error) {
        results.push({
          success: false,
          error: error instanceof Error ? error.message : '未知错误'
        });
      }
    }

    return results;
  }

  // 获取数据统计
  static async getDataStats() {
    try {
      // 这里可以调用实际的统计API
      // 暂时返回模拟数据
      return {
        questionnaires: { total: 0, pending: 0, approved: 0 },
        stories: { total: 0, pending: 0, approved: 0 },
        voices: { total: 0, autoGenerated: 0 }
      };
    } catch (error) {
      console.error('获取数据统计失败:', error);
      return {
        questionnaires: { total: 0, pending: 0, approved: 0 },
        stories: { total: 0, pending: 0, approved: 0 },
        voices: { total: 0, autoGenerated: 0 }
      };
    }
  }
}
