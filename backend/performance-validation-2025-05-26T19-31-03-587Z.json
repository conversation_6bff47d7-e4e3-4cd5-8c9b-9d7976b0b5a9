{"timestamp": "2025-05-26T19:30:52.284Z", "baseUrl": "https://college-employment-survey.aibook2099.workers.dev", "tests": {"original_stats": {"url": "https://college-employment-survey.aibook2099.workers.dev/api/questionnaire/stats", "iterations": 15, "results": [{"success": true, "status": 200, "data": {"success": true, "message": "Statistics retrieved successfully", "data": null, "meta": {"timestamp": "2025-05-26T19:30:53.591Z", "statistics": {"totalResponses": 173, "verifiedCount": 173, "anonymousCount": 0, "employedCount": 7, "unemployedCount": 0, "educationLevels": [{"name": "硕士", "count": 51}, {"name": "博士", "count": 47}, {"name": "本科", "count": 42}, {"name": "大专", "count": 33}], "regions": [{"name": "二线城市", "count": 46}, {"name": "省会城市", "count": 41}, {"name": "三四线城市", "count": 38}, {"name": "北上广深", "count": 33}, {"name": "北京", "count": 5}, {"name": "深圳", "count": 4}, {"name": "上海", "count": 3}, {"name": "杭州", "count": 2}, {"name": "广州", "count": 1}], "majors": [{"name": "软件工程", "count": 25}, {"name": "电子信息工程", "count": 22}, {"name": "国际贸易", "count": 17}, {"name": "机械工程", "count": 15}, {"name": "计算机科学与技术", "count": 14}, {"name": "市场营销", "count": 14}, {"name": "英语", "count": 12}, {"name": "法学", "count": 11}, {"name": "建筑学", "count": 11}, {"name": "医学", "count": 9}, {"name": "金融学", "count": 8}, {"name": "会计学", "count": 7}, {"name": "计算机科学", "count": 4}, {"name": "人工智能", "count": 3}, {"name": "数据科学", "count": 1}], "graduationYears": [{"name": "2023", "count": 5}], "industries": [], "employmentStatus": [{"name": "employed", "count": 49}, {"name": "unemployed", "count": 49}, {"name": "entrepreneurship", "count": 35}, {"name": "studying", "count": 31}, {"name": "已就业", "count": 7}, {"name": "求职中", "count": 2}], "lastUpdated": "2025-05-26T19:30:53.591Z"}}}, "duration": 296, "size": 1312}, {"success": true, "status": 200, "data": {"success": true, "message": "Statistics retrieved successfully", "data": null, "meta": {"timestamp": "2025-05-26T19:30:53.889Z", "statistics": {"totalResponses": 173, "verifiedCount": 173, "anonymousCount": 0, "employedCount": 7, "unemployedCount": 0, "educationLevels": [{"name": "硕士", "count": 51}, {"name": "博士", "count": 47}, {"name": "本科", "count": 42}, {"name": "大专", "count": 33}], "regions": [{"name": "二线城市", "count": 46}, {"name": "省会城市", "count": 41}, {"name": "三四线城市", "count": 38}, {"name": "北上广深", "count": 33}, {"name": "北京", "count": 5}, {"name": "深圳", "count": 4}, {"name": "上海", "count": 3}, {"name": "杭州", "count": 2}, {"name": "广州", "count": 1}], "majors": [{"name": "软件工程", "count": 25}, {"name": "电子信息工程", "count": 22}, {"name": "国际贸易", "count": 17}, {"name": "机械工程", "count": 15}, {"name": "计算机科学与技术", "count": 14}, {"name": "市场营销", "count": 14}, {"name": "英语", "count": 12}, {"name": "法学", "count": 11}, {"name": "建筑学", "count": 11}, {"name": "医学", "count": 9}, {"name": "金融学", "count": 8}, {"name": "会计学", "count": 7}, {"name": "计算机科学", "count": 4}, {"name": "人工智能", "count": 3}, {"name": "数据科学", "count": 1}], "graduationYears": [{"name": "2023", "count": 5}], "industries": [], "employmentStatus": [{"name": "employed", "count": 49}, {"name": "unemployed", "count": 49}, {"name": "entrepreneurship", "count": 35}, {"name": "studying", "count": 31}, {"name": "已就业", "count": 7}, {"name": "求职中", "count": 2}], "lastUpdated": "2025-05-26T19:30:53.889Z"}}}, "duration": 304, "size": 1312}, {"success": true, "status": 200, "data": {"success": true, "message": "Statistics retrieved successfully", "data": null, "meta": {"timestamp": "2025-05-26T19:30:54.178Z", "statistics": {"totalResponses": 173, "verifiedCount": 173, "anonymousCount": 0, "employedCount": 7, "unemployedCount": 0, "educationLevels": [{"name": "硕士", "count": 51}, {"name": "博士", "count": 47}, {"name": "本科", "count": 42}, {"name": "大专", "count": 33}], "regions": [{"name": "二线城市", "count": 46}, {"name": "省会城市", "count": 41}, {"name": "三四线城市", "count": 38}, {"name": "北上广深", "count": 33}, {"name": "北京", "count": 5}, {"name": "深圳", "count": 4}, {"name": "上海", "count": 3}, {"name": "杭州", "count": 2}, {"name": "广州", "count": 1}], "majors": [{"name": "软件工程", "count": 25}, {"name": "电子信息工程", "count": 22}, {"name": "国际贸易", "count": 17}, {"name": "机械工程", "count": 15}, {"name": "计算机科学与技术", "count": 14}, {"name": "市场营销", "count": 14}, {"name": "英语", "count": 12}, {"name": "法学", "count": 11}, {"name": "建筑学", "count": 11}, {"name": "医学", "count": 9}, {"name": "金融学", "count": 8}, {"name": "会计学", "count": 7}, {"name": "计算机科学", "count": 4}, {"name": "人工智能", "count": 3}, {"name": "数据科学", "count": 1}], "graduationYears": [{"name": "2023", "count": 5}], "industries": [], "employmentStatus": [{"name": "employed", "count": 49}, {"name": "unemployed", "count": 49}, {"name": "entrepreneurship", "count": 35}, {"name": "studying", "count": 31}, {"name": "已就业", "count": 7}, {"name": "求职中", "count": 2}], "lastUpdated": "2025-05-26T19:30:54.178Z"}}}, "duration": 307, "size": 1312}, {"success": true, "status": 200, "data": {"success": true, "message": "Statistics retrieved successfully", "data": null, "meta": {"timestamp": "2025-05-26T19:30:54.485Z", "statistics": {"totalResponses": 173, "verifiedCount": 173, "anonymousCount": 0, "employedCount": 7, "unemployedCount": 0, "educationLevels": [{"name": "硕士", "count": 51}, {"name": "博士", "count": 47}, {"name": "本科", "count": 42}, {"name": "大专", "count": 33}], "regions": [{"name": "二线城市", "count": 46}, {"name": "省会城市", "count": 41}, {"name": "三四线城市", "count": 38}, {"name": "北上广深", "count": 33}, {"name": "北京", "count": 5}, {"name": "深圳", "count": 4}, {"name": "上海", "count": 3}, {"name": "杭州", "count": 2}, {"name": "广州", "count": 1}], "majors": [{"name": "软件工程", "count": 25}, {"name": "电子信息工程", "count": 22}, {"name": "国际贸易", "count": 17}, {"name": "机械工程", "count": 15}, {"name": "计算机科学与技术", "count": 14}, {"name": "市场营销", "count": 14}, {"name": "英语", "count": 12}, {"name": "法学", "count": 11}, {"name": "建筑学", "count": 11}, {"name": "医学", "count": 9}, {"name": "金融学", "count": 8}, {"name": "会计学", "count": 7}, {"name": "计算机科学", "count": 4}, {"name": "人工智能", "count": 3}, {"name": "数据科学", "count": 1}], "graduationYears": [{"name": "2023", "count": 5}], "industries": [], "employmentStatus": [{"name": "employed", "count": 49}, {"name": "unemployed", "count": 49}, {"name": "entrepreneurship", "count": 35}, {"name": "studying", "count": 31}, {"name": "已就业", "count": 7}, {"name": "求职中", "count": 2}], "lastUpdated": "2025-05-26T19:30:54.485Z"}}}, "duration": 237, "size": 1312}, {"success": true, "status": 200, "data": {"success": true, "message": "Statistics retrieved successfully", "data": null, "meta": {"timestamp": "2025-05-26T19:30:54.733Z", "statistics": {"totalResponses": 173, "verifiedCount": 173, "anonymousCount": 0, "employedCount": 7, "unemployedCount": 0, "educationLevels": [{"name": "硕士", "count": 51}, {"name": "博士", "count": 47}, {"name": "本科", "count": 42}, {"name": "大专", "count": 33}], "regions": [{"name": "二线城市", "count": 46}, {"name": "省会城市", "count": 41}, {"name": "三四线城市", "count": 38}, {"name": "北上广深", "count": 33}, {"name": "北京", "count": 5}, {"name": "深圳", "count": 4}, {"name": "上海", "count": 3}, {"name": "杭州", "count": 2}, {"name": "广州", "count": 1}], "majors": [{"name": "软件工程", "count": 25}, {"name": "电子信息工程", "count": 22}, {"name": "国际贸易", "count": 17}, {"name": "机械工程", "count": 15}, {"name": "计算机科学与技术", "count": 14}, {"name": "市场营销", "count": 14}, {"name": "英语", "count": 12}, {"name": "法学", "count": 11}, {"name": "建筑学", "count": 11}, {"name": "医学", "count": 9}, {"name": "金融学", "count": 8}, {"name": "会计学", "count": 7}, {"name": "计算机科学", "count": 4}, {"name": "人工智能", "count": 3}, {"name": "数据科学", "count": 1}], "graduationYears": [{"name": "2023", "count": 5}], "industries": [], "employmentStatus": [{"name": "employed", "count": 49}, {"name": "unemployed", "count": 49}, {"name": "entrepreneurship", "count": 35}, {"name": "studying", "count": 31}, {"name": "已就业", "count": 7}, {"name": "求职中", "count": 2}], "lastUpdated": "2025-05-26T19:30:54.733Z"}}}, "duration": 250, "size": 1312}, {"success": true, "status": 200, "data": {"success": true, "message": "Statistics retrieved successfully", "data": null, "meta": {"timestamp": "2025-05-26T19:30:54.973Z", "statistics": {"totalResponses": 173, "verifiedCount": 173, "anonymousCount": 0, "employedCount": 7, "unemployedCount": 0, "educationLevels": [{"name": "硕士", "count": 51}, {"name": "博士", "count": 47}, {"name": "本科", "count": 42}, {"name": "大专", "count": 33}], "regions": [{"name": "二线城市", "count": 46}, {"name": "省会城市", "count": 41}, {"name": "三四线城市", "count": 38}, {"name": "北上广深", "count": 33}, {"name": "北京", "count": 5}, {"name": "深圳", "count": 4}, {"name": "上海", "count": 3}, {"name": "杭州", "count": 2}, {"name": "广州", "count": 1}], "majors": [{"name": "软件工程", "count": 25}, {"name": "电子信息工程", "count": 22}, {"name": "国际贸易", "count": 17}, {"name": "机械工程", "count": 15}, {"name": "计算机科学与技术", "count": 14}, {"name": "市场营销", "count": 14}, {"name": "英语", "count": 12}, {"name": "法学", "count": 11}, {"name": "建筑学", "count": 11}, {"name": "医学", "count": 9}, {"name": "金融学", "count": 8}, {"name": "会计学", "count": 7}, {"name": "计算机科学", "count": 4}, {"name": "人工智能", "count": 3}, {"name": "数据科学", "count": 1}], "graduationYears": [{"name": "2023", "count": 5}], "industries": [], "employmentStatus": [{"name": "employed", "count": 49}, {"name": "unemployed", "count": 49}, {"name": "entrepreneurship", "count": 35}, {"name": "studying", "count": 31}, {"name": "已就业", "count": 7}, {"name": "求职中", "count": 2}], "lastUpdated": "2025-05-26T19:30:54.973Z"}}}, "duration": 331, "size": 1312}, {"success": true, "status": 200, "data": {"success": true, "message": "Statistics retrieved successfully", "data": null, "meta": {"timestamp": "2025-05-26T19:30:55.328Z", "statistics": {"totalResponses": 173, "verifiedCount": 173, "anonymousCount": 0, "employedCount": 7, "unemployedCount": 0, "educationLevels": [{"name": "硕士", "count": 51}, {"name": "博士", "count": 47}, {"name": "本科", "count": 42}, {"name": "大专", "count": 33}], "regions": [{"name": "二线城市", "count": 46}, {"name": "省会城市", "count": 41}, {"name": "三四线城市", "count": 38}, {"name": "北上广深", "count": 33}, {"name": "北京", "count": 5}, {"name": "深圳", "count": 4}, {"name": "上海", "count": 3}, {"name": "杭州", "count": 2}, {"name": "广州", "count": 1}], "majors": [{"name": "软件工程", "count": 25}, {"name": "电子信息工程", "count": 22}, {"name": "国际贸易", "count": 17}, {"name": "机械工程", "count": 15}, {"name": "计算机科学与技术", "count": 14}, {"name": "市场营销", "count": 14}, {"name": "英语", "count": 12}, {"name": "法学", "count": 11}, {"name": "建筑学", "count": 11}, {"name": "医学", "count": 9}, {"name": "金融学", "count": 8}, {"name": "会计学", "count": 7}, {"name": "计算机科学", "count": 4}, {"name": "人工智能", "count": 3}, {"name": "数据科学", "count": 1}], "graduationYears": [{"name": "2023", "count": 5}], "industries": [], "employmentStatus": [{"name": "employed", "count": 49}, {"name": "unemployed", "count": 49}, {"name": "entrepreneurship", "count": 35}, {"name": "studying", "count": 31}, {"name": "已就业", "count": 7}, {"name": "求职中", "count": 2}], "lastUpdated": "2025-05-26T19:30:55.328Z"}}}, "duration": 309, "size": 1312}, {"success": true, "status": 200, "data": {"success": true, "message": "Statistics retrieved successfully", "data": null, "meta": {"timestamp": "2025-05-26T19:30:55.616Z", "statistics": {"totalResponses": 173, "verifiedCount": 173, "anonymousCount": 0, "employedCount": 7, "unemployedCount": 0, "educationLevels": [{"name": "硕士", "count": 51}, {"name": "博士", "count": 47}, {"name": "本科", "count": 42}, {"name": "大专", "count": 33}], "regions": [{"name": "二线城市", "count": 46}, {"name": "省会城市", "count": 41}, {"name": "三四线城市", "count": 38}, {"name": "北上广深", "count": 33}, {"name": "北京", "count": 5}, {"name": "深圳", "count": 4}, {"name": "上海", "count": 3}, {"name": "杭州", "count": 2}, {"name": "广州", "count": 1}], "majors": [{"name": "软件工程", "count": 25}, {"name": "电子信息工程", "count": 22}, {"name": "国际贸易", "count": 17}, {"name": "机械工程", "count": 15}, {"name": "计算机科学与技术", "count": 14}, {"name": "市场营销", "count": 14}, {"name": "英语", "count": 12}, {"name": "法学", "count": 11}, {"name": "建筑学", "count": 11}, {"name": "医学", "count": 9}, {"name": "金融学", "count": 8}, {"name": "会计学", "count": 7}, {"name": "计算机科学", "count": 4}, {"name": "人工智能", "count": 3}, {"name": "数据科学", "count": 1}], "graduationYears": [{"name": "2023", "count": 5}], "industries": [], "employmentStatus": [{"name": "employed", "count": 49}, {"name": "unemployed", "count": 49}, {"name": "entrepreneurship", "count": 35}, {"name": "studying", "count": 31}, {"name": "已就业", "count": 7}, {"name": "求职中", "count": 2}], "lastUpdated": "2025-05-26T19:30:55.616Z"}}}, "duration": 308, "size": 1312}, {"success": true, "status": 200, "data": {"success": true, "message": "Statistics retrieved successfully", "data": null, "meta": {"timestamp": "2025-05-26T19:30:55.930Z", "statistics": {"totalResponses": 173, "verifiedCount": 173, "anonymousCount": 0, "employedCount": 7, "unemployedCount": 0, "educationLevels": [{"name": "硕士", "count": 51}, {"name": "博士", "count": 47}, {"name": "本科", "count": 42}, {"name": "大专", "count": 33}], "regions": [{"name": "二线城市", "count": 46}, {"name": "省会城市", "count": 41}, {"name": "三四线城市", "count": 38}, {"name": "北上广深", "count": 33}, {"name": "北京", "count": 5}, {"name": "深圳", "count": 4}, {"name": "上海", "count": 3}, {"name": "杭州", "count": 2}, {"name": "广州", "count": 1}], "majors": [{"name": "软件工程", "count": 25}, {"name": "电子信息工程", "count": 22}, {"name": "国际贸易", "count": 17}, {"name": "机械工程", "count": 15}, {"name": "计算机科学与技术", "count": 14}, {"name": "市场营销", "count": 14}, {"name": "英语", "count": 12}, {"name": "法学", "count": 11}, {"name": "建筑学", "count": 11}, {"name": "医学", "count": 9}, {"name": "金融学", "count": 8}, {"name": "会计学", "count": 7}, {"name": "计算机科学", "count": 4}, {"name": "人工智能", "count": 3}, {"name": "数据科学", "count": 1}], "graduationYears": [{"name": "2023", "count": 5}], "industries": [], "employmentStatus": [{"name": "employed", "count": 49}, {"name": "unemployed", "count": 49}, {"name": "entrepreneurship", "count": 35}, {"name": "studying", "count": 31}, {"name": "已就业", "count": 7}, {"name": "求职中", "count": 2}], "lastUpdated": "2025-05-26T19:30:55.930Z"}}}, "duration": 305, "size": 1312}, {"success": true, "status": 200, "data": {"success": true, "message": "Statistics retrieved successfully", "data": null, "meta": {"timestamp": "2025-05-26T19:30:56.223Z", "statistics": {"totalResponses": 173, "verifiedCount": 173, "anonymousCount": 0, "employedCount": 7, "unemployedCount": 0, "educationLevels": [{"name": "硕士", "count": 51}, {"name": "博士", "count": 47}, {"name": "本科", "count": 42}, {"name": "大专", "count": 33}], "regions": [{"name": "二线城市", "count": 46}, {"name": "省会城市", "count": 41}, {"name": "三四线城市", "count": 38}, {"name": "北上广深", "count": 33}, {"name": "北京", "count": 5}, {"name": "深圳", "count": 4}, {"name": "上海", "count": 3}, {"name": "杭州", "count": 2}, {"name": "广州", "count": 1}], "majors": [{"name": "软件工程", "count": 25}, {"name": "电子信息工程", "count": 22}, {"name": "国际贸易", "count": 17}, {"name": "机械工程", "count": 15}, {"name": "计算机科学与技术", "count": 14}, {"name": "市场营销", "count": 14}, {"name": "英语", "count": 12}, {"name": "法学", "count": 11}, {"name": "建筑学", "count": 11}, {"name": "医学", "count": 9}, {"name": "金融学", "count": 8}, {"name": "会计学", "count": 7}, {"name": "计算机科学", "count": 4}, {"name": "人工智能", "count": 3}, {"name": "数据科学", "count": 1}], "graduationYears": [{"name": "2023", "count": 5}], "industries": [], "employmentStatus": [{"name": "employed", "count": 49}, {"name": "unemployed", "count": 49}, {"name": "entrepreneurship", "count": 35}, {"name": "studying", "count": 31}, {"name": "已就业", "count": 7}, {"name": "求职中", "count": 2}], "lastUpdated": "2025-05-26T19:30:56.223Z"}}}, "duration": 307, "size": 1312}, {"success": true, "status": 200, "data": {"success": true, "message": "Statistics retrieved successfully", "data": null, "meta": {"timestamp": "2025-05-26T19:30:56.536Z", "statistics": {"totalResponses": 173, "verifiedCount": 173, "anonymousCount": 0, "employedCount": 7, "unemployedCount": 0, "educationLevels": [{"name": "硕士", "count": 51}, {"name": "博士", "count": 47}, {"name": "本科", "count": 42}, {"name": "大专", "count": 33}], "regions": [{"name": "二线城市", "count": 46}, {"name": "省会城市", "count": 41}, {"name": "三四线城市", "count": 38}, {"name": "北上广深", "count": 33}, {"name": "北京", "count": 5}, {"name": "深圳", "count": 4}, {"name": "上海", "count": 3}, {"name": "杭州", "count": 2}, {"name": "广州", "count": 1}], "majors": [{"name": "软件工程", "count": 25}, {"name": "电子信息工程", "count": 22}, {"name": "国际贸易", "count": 17}, {"name": "机械工程", "count": 15}, {"name": "计算机科学与技术", "count": 14}, {"name": "市场营销", "count": 14}, {"name": "英语", "count": 12}, {"name": "法学", "count": 11}, {"name": "建筑学", "count": 11}, {"name": "医学", "count": 9}, {"name": "金融学", "count": 8}, {"name": "会计学", "count": 7}, {"name": "计算机科学", "count": 4}, {"name": "人工智能", "count": 3}, {"name": "数据科学", "count": 1}], "graduationYears": [{"name": "2023", "count": 5}], "industries": [], "employmentStatus": [{"name": "employed", "count": 49}, {"name": "unemployed", "count": 49}, {"name": "entrepreneurship", "count": 35}, {"name": "studying", "count": 31}, {"name": "已就业", "count": 7}, {"name": "求职中", "count": 2}], "lastUpdated": "2025-05-26T19:30:56.536Z"}}}, "duration": 309, "size": 1312}, {"success": true, "status": 200, "data": {"success": true, "message": "Statistics retrieved successfully", "data": null, "meta": {"timestamp": "2025-05-26T19:30:56.835Z", "statistics": {"totalResponses": 173, "verifiedCount": 173, "anonymousCount": 0, "employedCount": 7, "unemployedCount": 0, "educationLevels": [{"name": "硕士", "count": 51}, {"name": "博士", "count": 47}, {"name": "本科", "count": 42}, {"name": "大专", "count": 33}], "regions": [{"name": "二线城市", "count": 46}, {"name": "省会城市", "count": 41}, {"name": "三四线城市", "count": 38}, {"name": "北上广深", "count": 33}, {"name": "北京", "count": 5}, {"name": "深圳", "count": 4}, {"name": "上海", "count": 3}, {"name": "杭州", "count": 2}, {"name": "广州", "count": 1}], "majors": [{"name": "软件工程", "count": 25}, {"name": "电子信息工程", "count": 22}, {"name": "国际贸易", "count": 17}, {"name": "机械工程", "count": 15}, {"name": "计算机科学与技术", "count": 14}, {"name": "市场营销", "count": 14}, {"name": "英语", "count": 12}, {"name": "法学", "count": 11}, {"name": "建筑学", "count": 11}, {"name": "医学", "count": 9}, {"name": "金融学", "count": 8}, {"name": "会计学", "count": 7}, {"name": "计算机科学", "count": 4}, {"name": "人工智能", "count": 3}, {"name": "数据科学", "count": 1}], "graduationYears": [{"name": "2023", "count": 5}], "industries": [], "employmentStatus": [{"name": "employed", "count": 49}, {"name": "unemployed", "count": 49}, {"name": "entrepreneurship", "count": 35}, {"name": "studying", "count": 31}, {"name": "已就业", "count": 7}, {"name": "求职中", "count": 2}], "lastUpdated": "2025-05-26T19:30:56.835Z"}}}, "duration": 230, "size": 1312}, {"success": true, "status": 200, "data": {"success": true, "message": "Statistics retrieved successfully", "data": null, "meta": {"timestamp": "2025-05-26T19:30:57.067Z", "statistics": {"totalResponses": 173, "verifiedCount": 173, "anonymousCount": 0, "employedCount": 7, "unemployedCount": 0, "educationLevels": [{"name": "硕士", "count": 51}, {"name": "博士", "count": 47}, {"name": "本科", "count": 42}, {"name": "大专", "count": 33}], "regions": [{"name": "二线城市", "count": 46}, {"name": "省会城市", "count": 41}, {"name": "三四线城市", "count": 38}, {"name": "北上广深", "count": 33}, {"name": "北京", "count": 5}, {"name": "深圳", "count": 4}, {"name": "上海", "count": 3}, {"name": "杭州", "count": 2}, {"name": "广州", "count": 1}], "majors": [{"name": "软件工程", "count": 25}, {"name": "电子信息工程", "count": 22}, {"name": "国际贸易", "count": 17}, {"name": "机械工程", "count": 15}, {"name": "计算机科学与技术", "count": 14}, {"name": "市场营销", "count": 14}, {"name": "英语", "count": 12}, {"name": "法学", "count": 11}, {"name": "建筑学", "count": 11}, {"name": "医学", "count": 9}, {"name": "金融学", "count": 8}, {"name": "会计学", "count": 7}, {"name": "计算机科学", "count": 4}, {"name": "人工智能", "count": 3}, {"name": "数据科学", "count": 1}], "graduationYears": [{"name": "2023", "count": 5}], "industries": [], "employmentStatus": [{"name": "employed", "count": 49}, {"name": "unemployed", "count": 49}, {"name": "entrepreneurship", "count": 35}, {"name": "studying", "count": 31}, {"name": "已就业", "count": 7}, {"name": "求职中", "count": 2}], "lastUpdated": "2025-05-26T19:30:57.067Z"}}}, "duration": 230, "size": 1312}, {"success": true, "status": 200, "data": {"success": true, "message": "Statistics retrieved successfully", "data": null, "meta": {"timestamp": "2025-05-26T19:30:57.325Z", "statistics": {"totalResponses": 173, "verifiedCount": 173, "anonymousCount": 0, "employedCount": 7, "unemployedCount": 0, "educationLevels": [{"name": "硕士", "count": 51}, {"name": "博士", "count": 47}, {"name": "本科", "count": 42}, {"name": "大专", "count": 33}], "regions": [{"name": "二线城市", "count": 46}, {"name": "省会城市", "count": 41}, {"name": "三四线城市", "count": 38}, {"name": "北上广深", "count": 33}, {"name": "北京", "count": 5}, {"name": "深圳", "count": 4}, {"name": "上海", "count": 3}, {"name": "杭州", "count": 2}, {"name": "广州", "count": 1}], "majors": [{"name": "软件工程", "count": 25}, {"name": "电子信息工程", "count": 22}, {"name": "国际贸易", "count": 17}, {"name": "机械工程", "count": 15}, {"name": "计算机科学与技术", "count": 14}, {"name": "市场营销", "count": 14}, {"name": "英语", "count": 12}, {"name": "法学", "count": 11}, {"name": "建筑学", "count": 11}, {"name": "医学", "count": 9}, {"name": "金融学", "count": 8}, {"name": "会计学", "count": 7}, {"name": "计算机科学", "count": 4}, {"name": "人工智能", "count": 3}, {"name": "数据科学", "count": 1}], "graduationYears": [{"name": "2023", "count": 5}], "industries": [], "employmentStatus": [{"name": "employed", "count": 49}, {"name": "unemployed", "count": 49}, {"name": "entrepreneurship", "count": 35}, {"name": "studying", "count": 31}, {"name": "已就业", "count": 7}, {"name": "求职中", "count": 2}], "lastUpdated": "2025-05-26T19:30:57.325Z"}}}, "duration": 259, "size": 1312}, {"success": true, "status": 200, "data": {"success": true, "message": "Statistics retrieved successfully", "data": null, "meta": {"timestamp": "2025-05-26T19:30:57.566Z", "statistics": {"totalResponses": 173, "verifiedCount": 173, "anonymousCount": 0, "employedCount": 7, "unemployedCount": 0, "educationLevels": [{"name": "硕士", "count": 51}, {"name": "博士", "count": 47}, {"name": "本科", "count": 42}, {"name": "大专", "count": 33}], "regions": [{"name": "二线城市", "count": 46}, {"name": "省会城市", "count": 41}, {"name": "三四线城市", "count": 38}, {"name": "北上广深", "count": 33}, {"name": "北京", "count": 5}, {"name": "深圳", "count": 4}, {"name": "上海", "count": 3}, {"name": "杭州", "count": 2}, {"name": "广州", "count": 1}], "majors": [{"name": "软件工程", "count": 25}, {"name": "电子信息工程", "count": 22}, {"name": "国际贸易", "count": 17}, {"name": "机械工程", "count": 15}, {"name": "计算机科学与技术", "count": 14}, {"name": "市场营销", "count": 14}, {"name": "英语", "count": 12}, {"name": "法学", "count": 11}, {"name": "建筑学", "count": 11}, {"name": "医学", "count": 9}, {"name": "金融学", "count": 8}, {"name": "会计学", "count": 7}, {"name": "计算机科学", "count": 4}, {"name": "人工智能", "count": 3}, {"name": "数据科学", "count": 1}], "graduationYears": [{"name": "2023", "count": 5}], "industries": [], "employmentStatus": [{"name": "employed", "count": 49}, {"name": "unemployed", "count": 49}, {"name": "entrepreneurship", "count": 35}, {"name": "studying", "count": 31}, {"name": "已就业", "count": 7}, {"name": "求职中", "count": 2}], "lastUpdated": "2025-05-26T19:30:57.566Z"}}}, "duration": 303, "size": 1312}], "statistics": {"count": 15, "min": 230, "max": 331, "avg": 285.67, "median": 304, "p95": 331, "p99": 331, "successRate": 100, "avgSize": 1312}}, "optimized_stats": {"url": "https://college-employment-survey.aibook2099.workers.dev/api/questionnaire/stats/optimized", "iterations": 15, "results": [{"success": false, "status": 404, "data": {"success": false, "error": "Endpoint not found", "code": 404, "details": {"path": "/api/questionnaire/stats/optimized", "method": "GET", "availableEndpoints": ["/api/questionnaire/stats", "/api/story/list", "/api/system/health"]}, "timestamp": "2025-05-26T19:30:57.813Z"}, "duration": 93, "size": 256}, {"success": false, "status": 404, "data": {"success": false, "error": "Endpoint not found", "code": 404, "details": {"path": "/api/questionnaire/stats/optimized", "method": "GET", "availableEndpoints": ["/api/questionnaire/stats", "/api/story/list", "/api/system/health"]}, "timestamp": "2025-05-26T19:30:57.906Z"}, "duration": 90, "size": 256}, {"success": false, "status": 404, "data": {"success": false, "error": "Endpoint not found", "code": 404, "details": {"path": "/api/questionnaire/stats/optimized", "method": "GET", "availableEndpoints": ["/api/questionnaire/stats", "/api/story/list", "/api/system/health"]}, "timestamp": "2025-05-26T19:30:57.997Z"}, "duration": 91, "size": 256}, {"success": false, "status": 404, "data": {"success": false, "error": "Endpoint not found", "code": 404, "details": {"path": "/api/questionnaire/stats/optimized", "method": "GET", "availableEndpoints": ["/api/questionnaire/stats", "/api/story/list", "/api/system/health"]}, "timestamp": "2025-05-26T19:30:58.086Z"}, "duration": 89, "size": 256}, {"success": false, "status": 404, "data": {"success": false, "error": "Endpoint not found", "code": 404, "details": {"path": "/api/questionnaire/stats/optimized", "method": "GET", "availableEndpoints": ["/api/questionnaire/stats", "/api/story/list", "/api/system/health"]}, "timestamp": "2025-05-26T19:30:58.176Z"}, "duration": 90, "size": 256}, {"success": false, "status": 404, "data": {"success": false, "error": "Endpoint not found", "code": 404, "details": {"path": "/api/questionnaire/stats/optimized", "method": "GET", "availableEndpoints": ["/api/questionnaire/stats", "/api/story/list", "/api/system/health"]}, "timestamp": "2025-05-26T19:30:58.266Z"}, "duration": 90, "size": 256}, {"success": false, "status": 404, "data": {"success": false, "error": "Endpoint not found", "code": 404, "details": {"path": "/api/questionnaire/stats/optimized", "method": "GET", "availableEndpoints": ["/api/questionnaire/stats", "/api/story/list", "/api/system/health"]}, "timestamp": "2025-05-26T19:30:58.356Z"}, "duration": 91, "size": 256}, {"success": false, "status": 404, "data": {"success": false, "error": "Endpoint not found", "code": 404, "details": {"path": "/api/questionnaire/stats/optimized", "method": "GET", "availableEndpoints": ["/api/questionnaire/stats", "/api/story/list", "/api/system/health"]}, "timestamp": "2025-05-26T19:30:58.446Z"}, "duration": 89, "size": 256}, {"success": false, "status": 404, "data": {"success": false, "error": "Endpoint not found", "code": 404, "details": {"path": "/api/questionnaire/stats/optimized", "method": "GET", "availableEndpoints": ["/api/questionnaire/stats", "/api/story/list", "/api/system/health"]}, "timestamp": "2025-05-26T19:30:58.537Z"}, "duration": 92, "size": 256}, {"success": false, "status": 404, "data": {"success": false, "error": "Endpoint not found", "code": 404, "details": {"path": "/api/questionnaire/stats/optimized", "method": "GET", "availableEndpoints": ["/api/questionnaire/stats", "/api/story/list", "/api/system/health"]}, "timestamp": "2025-05-26T19:30:58.627Z"}, "duration": 89, "size": 256}, {"success": false, "status": 404, "data": {"success": false, "error": "Endpoint not found", "code": 404, "details": {"path": "/api/questionnaire/stats/optimized", "method": "GET", "availableEndpoints": ["/api/questionnaire/stats", "/api/story/list", "/api/system/health"]}, "timestamp": "2025-05-26T19:30:58.716Z"}, "duration": 90, "size": 256}, {"success": false, "status": 404, "data": {"success": false, "error": "Endpoint not found", "code": 404, "details": {"path": "/api/questionnaire/stats/optimized", "method": "GET", "availableEndpoints": ["/api/questionnaire/stats", "/api/story/list", "/api/system/health"]}, "timestamp": "2025-05-26T19:30:58.807Z"}, "duration": 89, "size": 256}, {"success": false, "status": 404, "data": {"success": false, "error": "Endpoint not found", "code": 404, "details": {"path": "/api/questionnaire/stats/optimized", "method": "GET", "availableEndpoints": ["/api/questionnaire/stats", "/api/story/list", "/api/system/health"]}, "timestamp": "2025-05-26T19:30:58.896Z"}, "duration": 89, "size": 256}, {"success": false, "status": 404, "data": {"success": false, "error": "Endpoint not found", "code": 404, "details": {"path": "/api/questionnaire/stats/optimized", "method": "GET", "availableEndpoints": ["/api/questionnaire/stats", "/api/story/list", "/api/system/health"]}, "timestamp": "2025-05-26T19:30:58.987Z"}, "duration": 92, "size": 256}, {"success": false, "status": 404, "data": {"success": false, "error": "Endpoint not found", "code": 404, "details": {"path": "/api/questionnaire/stats/optimized", "method": "GET", "availableEndpoints": ["/api/questionnaire/stats", "/api/story/list", "/api/system/health"]}, "timestamp": "2025-05-26T19:30:59.080Z"}, "duration": 92, "size": 256}], "statistics": null}, "realtime_stats": {"url": "https://college-employment-survey.aibook2099.workers.dev/api/questionnaire/realtime-stats/optimized", "iterations": 15, "results": [{"success": false, "status": 404, "data": {"success": false, "error": "Endpoint not found", "code": 404, "details": {"path": "/api/questionnaire/realtime-stats/optimized", "method": "GET", "availableEndpoints": ["/api/questionnaire/stats", "/api/story/list", "/api/system/health"]}, "timestamp": "2025-05-26T19:30:59.261Z"}, "duration": 91, "size": 265}, {"success": false, "status": 404, "data": {"success": false, "error": "Endpoint not found", "code": 404, "details": {"path": "/api/questionnaire/realtime-stats/optimized", "method": "GET", "availableEndpoints": ["/api/questionnaire/stats", "/api/story/list", "/api/system/health"]}, "timestamp": "2025-05-26T19:30:59.351Z"}, "duration": 89, "size": 265}, {"success": false, "status": 404, "data": {"success": false, "error": "Endpoint not found", "code": 404, "details": {"path": "/api/questionnaire/realtime-stats/optimized", "method": "GET", "availableEndpoints": ["/api/questionnaire/stats", "/api/story/list", "/api/system/health"]}, "timestamp": "2025-05-26T19:30:59.443Z"}, "duration": 92, "size": 265}, {"success": false, "status": 404, "data": {"success": false, "error": "Endpoint not found", "code": 404, "details": {"path": "/api/questionnaire/realtime-stats/optimized", "method": "GET", "availableEndpoints": ["/api/questionnaire/stats", "/api/story/list", "/api/system/health"]}, "timestamp": "2025-05-26T19:30:59.534Z"}, "duration": 93, "size": 265}, {"success": false, "status": 404, "data": {"success": false, "error": "Endpoint not found", "code": 404, "details": {"path": "/api/questionnaire/realtime-stats/optimized", "method": "GET", "availableEndpoints": ["/api/questionnaire/stats", "/api/story/list", "/api/system/health"]}, "timestamp": "2025-05-26T19:30:59.625Z"}, "duration": 89, "size": 265}, {"success": false, "status": 404, "data": {"success": false, "error": "Endpoint not found", "code": 404, "details": {"path": "/api/questionnaire/realtime-stats/optimized", "method": "GET", "availableEndpoints": ["/api/questionnaire/stats", "/api/story/list", "/api/system/health"]}, "timestamp": "2025-05-26T19:30:59.715Z"}, "duration": 90, "size": 265}, {"success": false, "status": 404, "data": {"success": false, "error": "Endpoint not found", "code": 404, "details": {"path": "/api/questionnaire/realtime-stats/optimized", "method": "GET", "availableEndpoints": ["/api/questionnaire/stats", "/api/story/list", "/api/system/health"]}, "timestamp": "2025-05-26T19:30:59.805Z"}, "duration": 90, "size": 265}, {"success": false, "status": 404, "data": {"success": false, "error": "Endpoint not found", "code": 404, "details": {"path": "/api/questionnaire/realtime-stats/optimized", "method": "GET", "availableEndpoints": ["/api/questionnaire/stats", "/api/story/list", "/api/system/health"]}, "timestamp": "2025-05-26T19:30:59.896Z"}, "duration": 91, "size": 265}, {"success": false, "status": 404, "data": {"success": false, "error": "Endpoint not found", "code": 404, "details": {"path": "/api/questionnaire/realtime-stats/optimized", "method": "GET", "availableEndpoints": ["/api/questionnaire/stats", "/api/story/list", "/api/system/health"]}, "timestamp": "2025-05-26T19:30:59.986Z"}, "duration": 92, "size": 265}, {"success": false, "status": 404, "data": {"success": false, "error": "Endpoint not found", "code": 404, "details": {"path": "/api/questionnaire/realtime-stats/optimized", "method": "GET", "availableEndpoints": ["/api/questionnaire/stats", "/api/story/list", "/api/system/health"]}, "timestamp": "2025-05-26T19:31:00.077Z"}, "duration": 89, "size": 265}, {"success": false, "status": 404, "data": {"success": false, "error": "Endpoint not found", "code": 404, "details": {"path": "/api/questionnaire/realtime-stats/optimized", "method": "GET", "availableEndpoints": ["/api/questionnaire/stats", "/api/story/list", "/api/system/health"]}, "timestamp": "2025-05-26T19:31:00.168Z"}, "duration": 93, "size": 265}, {"success": false, "status": 404, "data": {"success": false, "error": "Endpoint not found", "code": 404, "details": {"path": "/api/questionnaire/realtime-stats/optimized", "method": "GET", "availableEndpoints": ["/api/questionnaire/stats", "/api/story/list", "/api/system/health"]}, "timestamp": "2025-05-26T19:31:00.261Z"}, "duration": 92, "size": 265}, {"success": false, "status": 404, "data": {"success": false, "error": "Endpoint not found", "code": 404, "details": {"path": "/api/questionnaire/realtime-stats/optimized", "method": "GET", "availableEndpoints": ["/api/questionnaire/stats", "/api/story/list", "/api/system/health"]}, "timestamp": "2025-05-26T19:31:00.351Z"}, "duration": 90, "size": 265}, {"success": false, "status": 404, "data": {"success": false, "error": "Endpoint not found", "code": 404, "details": {"path": "/api/questionnaire/realtime-stats/optimized", "method": "GET", "availableEndpoints": ["/api/questionnaire/stats", "/api/story/list", "/api/system/health"]}, "timestamp": "2025-05-26T19:31:00.442Z"}, "duration": 90, "size": 265}, {"success": false, "status": 404, "data": {"success": false, "error": "Endpoint not found", "code": 404, "details": {"path": "/api/questionnaire/realtime-stats/optimized", "method": "GET", "availableEndpoints": ["/api/questionnaire/stats", "/api/story/list", "/api/system/health"]}, "timestamp": "2025-05-26T19:31:00.533Z"}, "duration": 91, "size": 265}], "statistics": null}}, "summary": {}, "recommendations": [{"type": "info", "title": "持续监控", "description": "建议定期运行性能测试，监控生产环境性能指标", "priority": "low"}], "comparison": {"original": {"count": 15, "min": 230, "max": 331, "avg": 285.67, "median": 304, "p95": 331, "p99": 331, "successRate": 100, "avgSize": 1312}, "optimized": null, "realtime": null, "improvements": {}}, "loadTest": {"1": {"totalTime": 270, "successful": 1, "failed": 0, "avgDuration": 270, "throughput": 3.7, "successRate": 100}, "5": {"totalTime": 458, "successful": 5, "failed": 0, "avgDuration": 424.8, "throughput": 10.92, "successRate": 100}, "10": {"totalTime": 550, "successful": 10, "failed": 0, "avgDuration": 403.6, "throughput": 18.18, "successRate": 100}, "20": {"totalTime": 639, "successful": 20, "failed": 0, "avgDuration": 533.15, "throughput": 31.3, "successRate": 100}, "50": {"totalTime": 1227, "successful": 50, "failed": 0, "avgDuration": 1083.6, "throughput": 40.75, "successRate": 100}}}