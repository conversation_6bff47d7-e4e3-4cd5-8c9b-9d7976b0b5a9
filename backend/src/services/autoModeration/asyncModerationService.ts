/**
 * 异步审核服务
 *
 * 提供异步内容审核功能，减少请求处理时间
 */

import { Env } from '../../types';
import { ContentType, NotificationType } from '../../constants';
import { AutoModerationService } from './autoModerationService';
import { NotificationService } from '../notification/notificationService';
import { PrismaClient } from '@prisma/client';

/**
 * 异步审核服务
 */
export class AsyncModerationService {
  private static instance: AsyncModerationService;
  private prisma: PrismaClient;
  private autoModerationService: AutoModerationService;
  private notificationService: NotificationService;
  private initialized: boolean = false;
  private processingQueue: Map<string, boolean> = new Map();
  private cacheResults: Map<string, any> = new Map();
  private cacheTTL: number = 30 * 60 * 1000; // 30分钟缓存

  /**
   * 私有构造函数，防止直接实例化
   */
  private constructor() {
    this.prisma = new PrismaClient();
    this.autoModerationService = AutoModerationService.getInstance();
    this.notificationService = NotificationService.getInstance();
  }

  /**
   * 获取单例实例
   */
  public static getInstance(): AsyncModerationService {
    if (!AsyncModerationService.instance) {
      AsyncModerationService.instance = new AsyncModerationService();
    }
    return AsyncModerationService.instance;
  }

  /**
   * 初始化服务
   */
  public async initialize(): Promise<void> {
    if (!this.initialized) {
      if (!this.autoModerationService.isInitialized()) {
        await this.autoModerationService.initialize();
      }
      this.initialized = true;
    }
  }

  /**
   * 服务是否已初始化
   */
  public isInitialized(): boolean {
    return this.initialized;
  }

  /**
   * 异步审核内容
   * @param contentId 内容ID
   * @param content 内容
   * @param contentType 内容类型
   * @param env 环境变量
   */
  public async moderateContentAsync(contentId: string, content: any, contentType: ContentType, env: Env): Promise<void> {
    // 确保服务已初始化
    if (!this.isInitialized()) {
      await this.initialize();
    }

    // 检查是否已在处理队列中
    if (this.processingQueue.get(contentId)) {
      console.log(`内容 ${contentId} 已在处理队列中，跳过`);
      return;
    }

    // 添加到处理队列
    this.processingQueue.set(contentId, true);

    try {
      // 计算内容哈希，用于缓存
      const contentHash = this.calculateContentHash(content, contentType);

      // 检查缓存
      const cachedResult = this.getCachedResult(contentHash);
      if (cachedResult) {
        // 使用缓存结果
        await this.processResult(contentId, contentType, cachedResult, env);
        return;
      }

      // 执行审核
      const result = await this.autoModerationService.moderateContent(content, contentType, env);

      // 缓存结果
      this.cacheResult(contentHash, result);

      // 处理结果
      await this.processResult(contentId, contentType, result, env);
    } catch (error) {
      console.error(`异步审核内容 ${contentId} 失败:`, error);

      // 记录错误
      await this.recordError(contentId, contentType, error);
    } finally {
      // 从处理队列中移除
      this.processingQueue.delete(contentId);
    }
  }

  /**
   * 批量异步审核内容
   * @param items 待审核项目列表
   * @param env 环境变量
   */
  public async batchModerateAsync(items: { id: string, content: any, contentType: ContentType }[], env: Env): Promise<void> {
    // 确保服务已初始化
    if (!this.isInitialized()) {
      await this.initialize();
    }

    // 并行处理，但限制并发数
    const concurrencyLimit = 5;
    const chunks = this.chunkArray(items, concurrencyLimit);

    for (const chunk of chunks) {
      // 并行处理当前批次
      await Promise.all(
        chunk.map(item => this.moderateContentAsync(item.id, item.content, item.contentType, env))
      );
    }
  }

  /**
   * 处理审核结果
   * @param contentId 内容ID
   * @param contentType 内容类型
   * @param result 审核结果
   * @param env 环境变量
   */
  private async processResult(contentId: string, contentType: ContentType, result: any, env: Env): Promise<void> {
    try {
      // 获取待审核内容
      const pendingContent = await this.prisma.pendingContent.findUnique({
        where: { id: contentId }
      });

      if (!pendingContent) {
        console.log(`找不到待审核内容 ${contentId}`);
        return;
      }

      // 根据审核结果执行不同操作
      if (result.action === 'approve') {
        // 自动通过
        await this.approveContent(pendingContent, result, env);
      } else if (result.action === 'reject') {
        // 自动拒绝
        await this.rejectContent(pendingContent, result, env);
      } else {
        // 标记为需要人工审核
        await this.markForReview(pendingContent, result, env);
      }

      // 记录审核历史
      await this.autoModerationService.recordHistory(
        result.moderationResult,
        contentId,
        contentType,
        env
      );
    } catch (error) {
      console.error(`处理审核结果失败 ${contentId}:`, error);
    }
  }

  /**
   * 自动通过内容
   * @param pendingContent 待审核内容
   * @param result 审核结果
   * @param env 环境变量
   */
  private async approveContent(pendingContent: any, result: any, env: Env): Promise<void> {
    try {
      // 解析原始内容
      const originalContent = JSON.parse(pendingContent.originalContent);
      const contentType = pendingContent.type;

      // 根据内容类型创建相应的记录
      if (contentType === ContentType.STORY) {
        // 创建故事记录
        await this.prisma.story.create({
          data: {
            userId: originalContent.userId || 'anonymous',
            title: originalContent.title,
            content: originalContent.content,
            isAnonymous: originalContent.isAnonymous || false,
            status: 'approved'
          }
        });
      } else if (contentType === ContentType.QUESTIONNAIRE) {
        // 创建问卷记录
        await this.prisma.questionnaire.create({
          data: {
            userId: originalContent.userId || 'anonymous',
            graduationYear: originalContent.graduationYear,
            major: originalContent.major,
            educationLevel: originalContent.educationLevel,
            employmentStatus: originalContent.employmentStatus,
            jobTitle: originalContent.jobTitle || null,
            adviceForStudents: originalContent.adviceForStudents || null,
            status: 'approved'
          }
        });
      }

      // 更新待审核内容状态
      await this.prisma.pendingContent.update({
        where: { id: pendingContent.id },
        data: {
          status: 'approved',
          reviewerId: 'auto-moderation',
          reviewedAt: new Date(),
          reviewNotes: '自动通过'
        }
      });

      // 发送通知
      if (this.autoModerationService.getConfig().notifyUser && originalContent.userId) {
        await this.notificationService.sendContentModerationNotification(
          pendingContent.id,
          contentType,
          'approve',
          originalContent.userId,
          '您的内容已通过审核',
          env
        );
      }
    } catch (error) {
      console.error(`自动通过内容失败 ${pendingContent.id}:`, error);
    }
  }

  /**
   * 自动拒绝内容
   * @param pendingContent 待审核内容
   * @param result 审核结果
   * @param env 环境变量
   */
  private async rejectContent(pendingContent: any, result: any, env: Env): Promise<void> {
    try {
      // 解析原始内容
      const originalContent = JSON.parse(pendingContent.originalContent);

      // 记录被拒绝内容
      await this.prisma.rejectedContent.create({
        data: {
          userId: originalContent.userId || null,
          contentType: pendingContent.type,
          originalContent: pendingContent.originalContent,
          reason: result.message || '内容未通过自动审核',
          issues: result.moderationResult.issues.join(','),
          ipAddress: pendingContent.originIp,
          userAgent: pendingContent.userAgent
        }
      });

      // 更新待审核内容状态
      await this.prisma.pendingContent.update({
        where: { id: pendingContent.id },
        data: {
          status: 'rejected',
          reviewerId: 'auto-moderation',
          reviewedAt: new Date(),
          reviewNotes: result.message || '内容未通过自动审核'
        }
      });

      // 发送通知
      if (this.autoModerationService.getConfig().notifyUser && originalContent.userId) {
        await this.notificationService.sendContentModerationNotification(
          pendingContent.id,
          pendingContent.type,
          'reject',
          originalContent.userId,
          '您的内容未通过审核',
          env
        );
      }
    } catch (error) {
      console.error(`自动拒绝内容失败 ${pendingContent.id}:`, error);
    }
  }

  /**
   * 标记为需要人工审核
   * @param pendingContent 待审核内容
   * @param result 审核结果
   * @param env 环境变量
   */
  private async markForReview(pendingContent: any, result: any, env: Env): Promise<void> {
    try {
      // 更新待审核内容
      await this.prisma.pendingContent.update({
        where: { id: pendingContent.id },
        data: {
          status: 'pending',
          flags: result.moderationResult.issues.join(','),
          priority: this.calculatePriority(result.moderationResult),
          aiSuggestion: result.moderationResult.suggestedAction,
          aiConfidence: result.moderationResult.confidence,
          aiExplanation: result.moderationResult.explanation
        }
      });

      // 发送通知
      if (this.autoModerationService.getConfig().notifyAdmin) {
        await this.notificationService.sendAdminNotification(
          NotificationType.ADMIN_CONTENT_REVIEW,
          '新内容需要审核',
          `有新的${this.getContentTypeName(pendingContent.type)}需要人工审核`,
          {
            contentId: pendingContent.id,
            contentType: pendingContent.type,
            priority: this.calculatePriority(result.moderationResult),
            issues: result.moderationResult.issues
          },
          env
        );
      }

      // 解析原始内容，获取用户ID
      const originalContent = JSON.parse(pendingContent.originalContent);

      // 发送用户通知
      if (this.autoModerationService.getConfig().notifyUser && originalContent.userId) {
        await this.notificationService.sendContentModerationNotification(
          pendingContent.id,
          pendingContent.type,
          'review',
          originalContent.userId,
          '您的内容正在审核中',
          env
        );
      }
    } catch (error) {
      console.error(`标记内容为需要人工审核失败 ${pendingContent.id}:`, error);
    }
  }

  /**
   * 记录错误
   * @param contentId 内容ID
   * @param contentType 内容类型
   * @param error 错误
   */
  private async recordError(contentId: string, contentType: ContentType, error: any): Promise<void> {
    try {
      // 更新待审核内容
      await this.prisma.pendingContent.update({
        where: { id: contentId },
        data: {
          flags: '审核错误',
          priority: 5, // 最高优先级
          aiSuggestion: 'review',
          aiExplanation: `审核过程中发生错误: ${error.message || String(error)}`
        }
      });
    } catch (dbError) {
      console.error(`记录审核错误失败 ${contentId}:`, dbError);
    }
  }

  /**
   * 计算内容哈希
   * @param content 内容
   * @param contentType 内容类型
   */
  private calculateContentHash(content: any, contentType: ContentType): string {
    // 简单实现，实际应用中可以使用更复杂的哈希算法
    const contentStr = JSON.stringify(content);
    return `${contentType}:${contentStr.length}:${this.simpleHash(contentStr)}`;
  }

  /**
   * 简单哈希函数
   * @param str 字符串
   */
  private simpleHash(str: string): number {
    let hash = 0;
    for (let i = 0; i < str.length; i++) {
      const char = str.charCodeAt(i);
      hash = ((hash << 5) - hash) + char;
      hash = hash & hash; // Convert to 32bit integer
    }
    return Math.abs(hash);
  }

  /**
   * 获取缓存结果
   * @param contentHash 内容哈希
   */
  private getCachedResult(contentHash: string): any {
    const cached = this.cacheResults.get(contentHash);
    if (!cached) return null;

    if (Date.now() > cached.expiry) {
      this.cacheResults.delete(contentHash);
      return null;
    }

    return cached.result;
  }

  /**
   * 缓存结果
   * @param contentHash 内容哈希
   * @param result 结果
   */
  private cacheResult(contentHash: string, result: any): void {
    this.cacheResults.set(contentHash, {
      result,
      expiry: Date.now() + this.cacheTTL
    });
  }

  /**
   * 计算优先级
   * @param moderationResult 审核结果
   */
  private calculatePriority(moderationResult: any): number {
    // 基础优先级
    let priority = 1;

    // 根据置信度调整优先级
    if (moderationResult.confidence < 0.6) {
      priority += 1; // 低置信度需要更高优先级
    }

    // 根据严重程度调整优先级
    if (moderationResult.severity === 'high') {
      priority += 2;
    } else if (moderationResult.severity === 'medium') {
      priority += 1;
    }

    // 根据建议操作调整优先级
    if (moderationResult.suggestedAction === 'reject') {
      priority += 1;
    }

    // 确保优先级在合理范围内
    return Math.min(Math.max(priority, 1), 5);
  }

  /**
   * 获取内容类型名称
   * @param contentType 内容类型
   * @returns 内容类型名称
   */
  private getContentTypeName(contentType: string): string {
    switch (contentType) {
      case ContentType.STORY:
        return '故事';
      case ContentType.QUESTIONNAIRE:
        return '问卷';
      case ContentType.COMMENT:
        return '评论';
      case ContentType.PROFILE:
        return '个人资料';
      case ContentType.FEEDBACK:
        return '反馈';
      default:
        return '内容';
    }
  }

  /**
   * 将数组分成多个小数组
   * @param array 原数组
   * @param size 每个小数组的大小
   */
  private chunkArray<T>(array: T[], size: number): T[][] {
    const chunks: T[][] = [];
    for (let i = 0; i < array.length; i += size) {
      chunks.push(array.slice(i, i + size));
    }
    return chunks;
  }
}
