/**
 * 用户管理服务
 *
 * 提供用户管理相关的 API 调用
 */

import { apiClient } from './apiClient';

// 用户类型
export interface User {
  id: string;
  uuid?: string;
  username: string;
  email: string;
  role: 'user' | 'reviewer' | 'admin' | 'superadmin';
  status: 'active' | 'inactive' | 'banned';
  createdAt: string;
  lastLoginAt?: string;
  submissionCount?: number;
  storyCount?: number;
  questionnaireCount?: number;
}

// 用户内容类型
export interface UserContent {
  id: string;
  type: 'story' | 'questionnaire' | 'comment';
  title?: string;
  content: string;
  status: 'pending' | 'approved' | 'rejected';
  createdAt: string;
  reviewedAt?: string;
  reviewerId?: string;
  tags?: string[];
}

// 用户搜索参数
export interface UserSearchParams {
  uuid?: string;
  email?: string;
  username?: string;
  role?: string;
  status?: string;
  page?: number;
  limit?: number;
}

// 模拟用户数据
const mockUsers: User[] = [
  {
    id: '1',
    uuid: 'usr_123456789',
    username: 'z<PERSON><PERSON>',
    email: 'z<PERSON><PERSON>@example.com',
    role: 'user',
    status: 'active',
    createdAt: '2023-01-15T08:30:00Z',
    lastLoginAt: '2023-05-20T14:22:10Z',
    submissionCount: 5,
    storyCount: 3,
    questionnaireCount: 2
  },
  {
    id: '2',
    uuid: 'usr_987654321',
    username: 'lisi',
    email: '<EMAIL>',
    role: 'user',
    status: 'active',
    createdAt: '2023-02-10T10:15:00Z',
    lastLoginAt: '2023-05-18T09:45:30Z',
    submissionCount: 8,
    storyCount: 5,
    questionnaireCount: 3
  },
  {
    id: '3',
    uuid: 'usr_456789123',
    username: 'wangwu',
    email: '<EMAIL>',
    role: 'user',
    status: 'inactive',
    createdAt: '2023-03-05T14:20:00Z',
    lastLoginAt: '2023-04-10T16:30:00Z',
    submissionCount: 2,
    storyCount: 1,
    questionnaireCount: 1
  },
  {
    id: '4',
    uuid: 'usr_789123456',
    username: 'zhaoliu',
    email: '<EMAIL>',
    role: 'user',
    status: 'banned',
    createdAt: '2023-01-20T11:45:00Z',
    lastLoginAt: '2023-02-15T13:10:20Z',
    submissionCount: 3,
    storyCount: 2,
    questionnaireCount: 1
  },
  {
    id: '5',
    uuid: 'usr_321654987',
    username: 'qianqi',
    email: '<EMAIL>',
    role: 'user',
    status: 'active',
    createdAt: '2023-04-12T09:30:00Z',
    lastLoginAt: '2023-05-21T10:05:15Z',
    submissionCount: 10,
    storyCount: 7,
    questionnaireCount: 3
  }
];

// 模拟用户内容数据
const mockUserContents: Record<string, UserContent[]> = {
  'usr_123456789': [
    {
      id: '101',
      type: 'story',
      title: '我的大学经历',
      content: '大学四年，我参加了很多社团活动，收获了宝贵的经验...',
      status: 'approved',
      createdAt: '2023-03-10T14:30:00Z',
      reviewedAt: '2023-03-12T10:15:00Z',
      reviewerId: 'reviewer-1',
      tags: ['大学生活', '社团活动']
    },
    {
      id: '102',
      type: 'questionnaire',
      content: '{"question1":"回答1","question2":"回答2","question3":"回答3"}',
      status: 'approved',
      createdAt: '2023-04-05T09:20:00Z',
      reviewedAt: '2023-04-06T11:30:00Z',
      reviewerId: 'reviewer-2'
    }
  ],
  'usr_987654321': [
    {
      id: '201',
      type: 'story',
      title: '实习经历分享',
      content: '在某科技公司实习的三个月里，我学到了很多实用技能...',
      status: 'approved',
      createdAt: '2023-03-15T16:45:00Z',
      reviewedAt: '2023-03-17T14:20:00Z',
      reviewerId: 'reviewer-1',
      tags: ['实习', '职场经验']
    },
    {
      id: '202',
      type: 'comment',
      content: '这个故事很有启发性，谢谢分享！',
      status: 'approved',
      createdAt: '2023-04-10T13:25:00Z',
      reviewedAt: '2023-04-10T15:40:00Z',
      reviewerId: 'reviewer-3'
    }
  ]
};

/**
 * 搜索用户
 * @param params 搜索参数
 * @returns 搜索结果
 */
export async function searchUsers(params: UserSearchParams) {
  try {
    // 检查是否为开发环境
    if (import.meta.env.DEV && import.meta.env.VITE_USE_MOCK === 'true') {
      console.log('使用模拟数据搜索用户');

      // 模拟网络延迟
      await new Promise(resolve => setTimeout(resolve, 500));

      // 过滤用户
      let filteredUsers = [...mockUsers];

      if (params.uuid) {
        filteredUsers = filteredUsers.filter(user => 
          user.uuid?.toLowerCase().includes(params.uuid!.toLowerCase())
        );
      }

      if (params.email) {
        filteredUsers = filteredUsers.filter(user => 
          user.email.toLowerCase().includes(params.email!.toLowerCase())
        );
      }

      if (params.username) {
        filteredUsers = filteredUsers.filter(user => 
          user.username.toLowerCase().includes(params.username!.toLowerCase())
        );
      }

      if (params.role) {
        filteredUsers = filteredUsers.filter(user => user.role === params.role);
      }

      if (params.status) {
        filteredUsers = filteredUsers.filter(user => user.status === params.status);
      }

      // 分页
      const page = params.page || 1;
      const limit = params.limit || 10;
      const startIndex = (page - 1) * limit;
      const endIndex = startIndex + limit;
      const paginatedUsers = filteredUsers.slice(startIndex, endIndex);

      return {
        success: true,
        data: {
          users: paginatedUsers,
          total: filteredUsers.length,
          page,
          limit,
          totalPages: Math.ceil(filteredUsers.length / limit)
        }
      };
    }

    // 生产环境使用真实API
    const response = await apiClient.get('/admin/users', { params });
    return response.data;
  } catch (error) {
    console.error('搜索用户失败:', error);

    // 在开发环境中，如果API请求失败，返回模拟数据
    if (import.meta.env.DEV) {
      console.log('API请求失败，使用模拟数据');

      return {
        success: true,
        data: {
          users: mockUsers,
          total: mockUsers.length,
          page: 1,
          limit: 10,
          totalPages: 1
        }
      };
    }

    throw error;
  }
}

/**
 * 获取用户内容
 * @param uuid 用户UUID
 * @returns 用户内容
 */
export async function getUserContents(uuid: string) {
  try {
    // 检查是否为开发环境
    if (import.meta.env.DEV && import.meta.env.VITE_USE_MOCK === 'true') {
      console.log('使用模拟数据获取用户内容');

      // 模拟网络延迟
      await new Promise(resolve => setTimeout(resolve, 500));

      // 获取用户内容
      const contents = mockUserContents[uuid] || [];

      return {
        success: true,
        data: {
          contents,
          total: contents.length
        }
      };
    }

    // 生产环境使用真实API
    const response = await apiClient.get(`/admin/users/${uuid}/contents`);
    return response.data;
  } catch (error) {
    console.error('获取用户内容失败:', error);

    // 在开发环境中，如果API请求失败，返回模拟数据
    if (import.meta.env.DEV) {
      console.log('API请求失败，使用模拟数据');

      const contents = mockUserContents[uuid] || [];

      return {
        success: true,
        data: {
          contents,
          total: contents.length
        }
      };
    }

    throw error;
  }
}

/**
 * 更新用户状态
 * @param uuid 用户UUID
 * @param status 新状态
 * @returns 更新结果
 */
export async function updateUserStatus(uuid: string, status: 'active' | 'inactive' | 'banned') {
  try {
    // 检查是否为开发环境
    if (import.meta.env.DEV && import.meta.env.VITE_USE_MOCK === 'true') {
      console.log('使用模拟数据更新用户状态');

      // 模拟网络延迟
      await new Promise(resolve => setTimeout(resolve, 500));

      return {
        success: true,
        message: `用户状态已更新为 ${status}`
      };
    }

    // 生产环境使用真实API
    const response = await apiClient.patch(`/admin/users/${uuid}/status`, { status });
    return response.data;
  } catch (error) {
    console.error('更新用户状态失败:', error);
    throw error;
  }
}

/**
 * 批量更新用户状态
 * @param uuids 用户UUID列表
 * @param status 新状态
 * @returns 更新结果
 */
export async function batchUpdateUserStatus(uuids: string[], status: 'active' | 'inactive' | 'banned') {
  try {
    // 检查是否为开发环境
    if (import.meta.env.DEV && import.meta.env.VITE_USE_MOCK === 'true') {
      console.log('使用模拟数据批量更新用户状态');

      // 模拟网络延迟
      await new Promise(resolve => setTimeout(resolve, 500));

      return {
        success: true,
        message: `${uuids.length} 个用户状态已更新为 ${status}`
      };
    }

    // 生产环境使用真实API
    const response = await apiClient.patch('/admin/users/batch/status', { uuids, status });
    return response.data;
  } catch (error) {
    console.error('批量更新用户状态失败:', error);
    throw error;
  }
}
