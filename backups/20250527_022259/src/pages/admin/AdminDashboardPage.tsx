import { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { <PERSON><PERSON>, <PERSON><PERSON>Content, <PERSON><PERSON><PERSON>ist, TabsTrigger } from '@/components/ui/tabs';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { useToast } from '@/components/ui/use-toast';
import StoryModerationPanel from '@/components/admin/StoryModerationPanel';
import DataManagementPanel from '@/components/admin/DataManagementPanel';
import AnalyticsPanel from '@/components/admin/AnalyticsPanel';
import TagManagementPanel from '@/components/admin/TagManagementPanel';

interface AdminDashboardPageProps {
  initialTab?: 'stories' | 'tags' | 'responses' | 'analytics' | 'users';
}

export default function AdminDashboardPage({ initialTab = 'stories' }: AdminDashboardPageProps) {
  const { toast } = useToast();
  const navigate = useNavigate();
  const [activeTab, setActiveTab] = useState(initialTab);
  const [isLoading, setIsLoading] = useState(true);
  const [isAuthenticated, setIsAuthenticated] = useState(false);

  // 检查管理员是否已登录
  useEffect(() => {
    const token = localStorage.getItem('adminToken');
    if (!token) {
      navigate('/admin/login');
      return;
    }

    // 这里可以添加token验证逻辑
    // 简单起见，我们只检查token是否存在
    setIsAuthenticated(true);
    setIsLoading(false);
  }, [navigate]);

  // 处理登出
  const handleLogout = () => {
    localStorage.removeItem('adminToken');
    toast({
      title: '已登出',
      description: '您已成功退出管理员账号',
    });
    navigate('/admin/login');
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <p>加载中...</p>
      </div>
    );
  }

  if (!isAuthenticated) {
    return null; // 会被useEffect中的navigate重定向
  }

  return (
    <div className="container mx-auto py-6">
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-3xl font-bold">管理员控制台</h1>
        <Button variant="outline" onClick={handleLogout}>
          退出登录
        </Button>
      </div>

      <Tabs defaultValue="stories" value={activeTab} onValueChange={setActiveTab}>
        <TabsList className="grid w-full grid-cols-5 mb-6">
          <TabsTrigger value="stories">故事审核</TabsTrigger>
          <TabsTrigger value="tags">标签管理</TabsTrigger>
          <TabsTrigger value="responses">问卷回复</TabsTrigger>
          <TabsTrigger value="analytics">数据分析</TabsTrigger>
          <TabsTrigger value="users">用户管理</TabsTrigger>
        </TabsList>

        <TabsContent value="stories">
          <Card>
            <CardHeader>
              <CardTitle>故事审核</CardTitle>
              <CardDescription>
                审核用户提交的故事，只有审核通过的故事才会显示在故事墙上
              </CardDescription>
            </CardHeader>
            <CardContent>
              <StoryModerationPanel />
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="tags">
          <Card>
            <CardHeader>
              <CardTitle>标签管理</CardTitle>
              <CardDescription>
                管理故事墙的标签系统，包括创建、编辑和删除标签
              </CardDescription>
            </CardHeader>
            <CardContent>
              <TagManagementPanel />
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="responses">
          <Card>
            <CardHeader>
              <CardTitle>问卷回复</CardTitle>
              <CardDescription>
                查看用户提交的问卷回复数据
              </CardDescription>
            </CardHeader>
            <CardContent>
              <DataManagementPanel />
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="analytics">
          <Card>
            <CardHeader>
              <CardTitle>数据分析</CardTitle>
              <CardDescription>
                分析问卷数据，生成统计图表和导出数据
              </CardDescription>
            </CardHeader>
            <CardContent>
              <AnalyticsPanel />
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="users">
          <Card>
            <CardHeader>
              <CardTitle>用户管理</CardTitle>
              <CardDescription>
                管理系统用户，包括添加、编辑和删除用户
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="p-4 text-center">
                <p>用户管理功能正在开发中...</p>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}
