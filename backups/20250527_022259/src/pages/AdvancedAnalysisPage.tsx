import { useState, useEffect } from 'react';
import { useQuery } from '@tanstack/react-query';
import { Ta<PERSON>, Ta<PERSON>Content, <PERSON><PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import { Button } from '@/components/ui/button';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Skeleton } from '@/components/ui/skeleton';
import { getVisualizationData } from '@/lib/api';
import { hybridApiService } from '@/services/apiServiceV2';
import { Download, Share, RefreshCw, Filter, ChevronDown } from 'lucide-react';
import CorrelationAnalysis from '@/components/visualization/analysis/CorrelationAnalysis';
import TrendAnalysis from '@/components/visualization/analysis/TrendAnalysis';
import GroupComparison from '@/components/visualization/analysis/GroupComparison';
import PredictionAnalysis from '@/components/visualization/analysis/PredictionAnalysis';
import DataExport from '@/components/visualization/DataExport';
// 移除模拟数据导入，完全使用真实API数据
// import { mockVisualizationData } from '@/lib/mockData';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import {
  Sheet,
  SheetContent,
  SheetDescription,
  SheetHeader,
  SheetTitle,
  SheetTrigger,
} from '@/components/ui/sheet';

export default function AdvancedAnalysisPage() {
  // 状态管理
  const [activeTab, setActiveTab] = useState('correlation');
  const [educationLevel, setEducationLevel] = useState('all');
  const [region, setRegion] = useState('all');
  const [graduationYear, setGraduationYear] = useState<number | undefined>(undefined);
  const [isExportOpen, setIsExportOpen] = useState(false);

  // 获取可视化数据 - 使用混合API
  const { data, isLoading, error, refetch } = useQuery(
    ['visualization-data', educationLevel, region, graduationYear],
    async () => {
      try {
        // 优先使用混合API
        return await hybridApiService.getVisualizationData();
      } catch (error) {
        console.warn('混合API失败，使用原始API:', error);
        // 降级到原始API
        return await getVisualizationData({
          educationLevel: educationLevel === 'all' ? undefined : educationLevel,
          region: region === 'all' ? undefined : region,
          graduationYear,
          verified: true,
        });
      }
    },
    {
      staleTime: 5 * 60 * 1000, // 5分钟内不重新获取数据
      retry: 2, // 失败时重试2次
      retryDelay: 1000, // 重试延迟1秒
    }
  );

  // 处理分享
  const handleShare = () => {
    // 构建URL参数
    const params = new URLSearchParams();
    params.set('tab', activeTab);

    if (educationLevel !== 'all') {
      params.set('education', educationLevel);
    }

    if (region !== 'all') {
      params.set('region', region);
    }

    if (graduationYear) {
      params.set('year', graduationYear.toString());
    }

    // 构建完整URL
    const shareUrl = `${window.location.origin}${window.location.pathname}?${params.toString()}`;

    // 复制到剪贴板
    navigator.clipboard.writeText(shareUrl).then(() => {
      alert('分析链接已复制到剪贴板');
    }).catch(err => {
      console.error('无法复制链接:', err);
      alert('无法复制链接，请手动复制浏览器地址栏');
    });
  };

  // 从URL参数中恢复状态
  useEffect(() => {
    const params = new URLSearchParams(window.location.search);

    const tabParam = params.get('tab');
    if (tabParam && ['correlation', 'trend', 'group', 'prediction'].includes(tabParam)) {
      setActiveTab(tabParam);
    }

    const educationParam = params.get('education');
    if (educationParam) {
      setEducationLevel(educationParam);
    }

    const regionParam = params.get('region');
    if (regionParam) {
      setRegion(regionParam);
    }

    const yearParam = params.get('year');
    if (yearParam && !isNaN(Number(yearParam))) {
      setGraduationYear(Number(yearParam));
    }
  }, []);

  // 渲染加载状态
  if (isLoading) {
    return (
      <div className="container mx-auto py-8">
        <h1 className="text-3xl font-bold mb-6">高级数据分析</h1>
        <Card>
          <CardHeader>
            <Skeleton className="h-8 w-1/3" />
            <Skeleton className="h-4 w-1/2" />
          </CardHeader>
          <CardContent>
            <div className="h-80">
              <Skeleton className="h-full w-full" />
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }

  // 渲染错误状态
  if (error) {
    return (
      <div className="container mx-auto py-8">
        <h1 className="text-3xl font-bold mb-6">高级数据分析</h1>
        <Card className="bg-red-50">
          <CardHeader>
            <CardTitle className="text-red-600">加载数据失败</CardTitle>
            <CardDescription>
              无法加载分析数据，请稍后重试或联系管理员
            </CardDescription>
          </CardHeader>
          <CardContent>
            <Button onClick={() => refetch()}>重试</Button>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className="container mx-auto py-8">
      <div className="flex flex-col md:flex-row justify-between items-start md:items-center mb-6">
        <h1 className="text-3xl font-bold">高级数据分析</h1>

        <div className="flex items-center gap-2 mt-4 md:mt-0">
          <Sheet open={isExportOpen} onOpenChange={setIsExportOpen}>
            <SheetTrigger asChild>
              <Button variant="outline" className="flex items-center gap-2">
                <Download className="h-4 w-4" />
                导出数据
              </Button>
            </SheetTrigger>
            <SheetContent side="right" className="w-full sm:w-[540px]">
              <SheetHeader>
                <SheetTitle>导出数据</SheetTitle>
                <SheetDescription>
                  选择导出格式和内容，导出分析数据
                </SheetDescription>
              </SheetHeader>
              <div className="py-6">
                <DataExport data={data?.stats} />
              </div>
            </SheetContent>
          </Sheet>

          <Button variant="outline" onClick={handleShare} className="flex items-center gap-2">
            <Share className="h-4 w-4" />
            分享分析
          </Button>

          <Button variant="outline" onClick={() => refetch()} className="flex items-center gap-2">
            <RefreshCw className="h-4 w-4" />
            刷新数据
          </Button>

          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="outline" className="flex items-center gap-2">
                <Filter className="h-4 w-4" />
                筛选
                <ChevronDown className="h-4 w-4" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end" className="w-56">
              <div className="p-2">
                <div className="mb-4">
                  <label className="text-sm font-medium mb-1 block">学历层次</label>
                  <Select value={educationLevel} onValueChange={setEducationLevel}>
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="all">全部学历</SelectItem>
                      <SelectItem value="high-school">高中及以下</SelectItem>
                      <SelectItem value="college">专科</SelectItem>
                      <SelectItem value="bachelor">本科</SelectItem>
                      <SelectItem value="master">硕士</SelectItem>
                      <SelectItem value="phd">博士</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                <div className="mb-4">
                  <label className="text-sm font-medium mb-1 block">地区</label>
                  <Select value={region} onValueChange={setRegion}>
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="all">全部地区</SelectItem>
                      <SelectItem value="north">华北地区</SelectItem>
                      <SelectItem value="east">华东地区</SelectItem>
                      <SelectItem value="south">华南地区</SelectItem>
                      <SelectItem value="central">华中地区</SelectItem>
                      <SelectItem value="southwest">西南地区</SelectItem>
                      <SelectItem value="northwest">西北地区</SelectItem>
                      <SelectItem value="northeast">东北地区</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                <div className="mb-4">
                  <label className="text-sm font-medium mb-1 block">毕业年份</label>
                  <Select
                    value={graduationYear?.toString() || ''}
                    onValueChange={(value) => setGraduationYear(value ? Number(value) : undefined)}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="全部年份" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="">全部年份</SelectItem>
                      <SelectItem value="2023">2023年</SelectItem>
                      <SelectItem value="2022">2022年</SelectItem>
                      <SelectItem value="2021">2021年</SelectItem>
                      <SelectItem value="2020">2020年</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                <Button
                  variant="outline"
                  className="w-full"
                  onClick={() => {
                    setEducationLevel('all');
                    setRegion('all');
                    setGraduationYear(undefined);
                  }}
                >
                  重置筛选
                </Button>
              </div>
            </DropdownMenuContent>
          </DropdownMenu>
        </div>
      </div>

      <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-4">
        <TabsList className="grid grid-cols-2 md:grid-cols-4 w-full">
          <TabsTrigger value="correlation">相关性分析</TabsTrigger>
          <TabsTrigger value="trend">趋势分析</TabsTrigger>
          <TabsTrigger value="group">分组比较</TabsTrigger>
          <TabsTrigger value="prediction">预测分析</TabsTrigger>
        </TabsList>

        <TabsContent value="correlation" className="space-y-4">
          <CorrelationAnalysis data={data?.stats} />
        </TabsContent>

        <TabsContent value="trend" className="space-y-4">
          <TrendAnalysis data={data?.stats} />
        </TabsContent>

        <TabsContent value="group" className="space-y-4">
          <GroupComparison data={data?.stats} />
        </TabsContent>

        <TabsContent value="prediction" className="space-y-4">
          <PredictionAnalysis data={data?.stats} />
        </TabsContent>
      </Tabs>
    </div>
  );
}
