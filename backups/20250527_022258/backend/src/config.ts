/**
 * 应用配置
 *
 * 加载环境变量并提供配置接口
 *
 * 注意：由于Cloudflare Workers环境不支持文件系统，
 * 我们直接从环境变量中读取配置
 */

// 配置接口
interface Config {
  environment: string;
  database: {
    url: string;
  };
  mockData: {
    enabled: boolean;
  };
  api: {
    resendApiKey: string;
  };
  security: {
    aesSecretKey: string;
    aesIv: string;
    jwtSecret: string;
  };
  logging: {
    level: string;
  };
}

// 创建配置对象
const config: Config = {
  environment: 'development', // 在Cloudflare Workers中，我们硬编码为开发环境
  database: {
    url: 'postgresql://user:password@localhost:5432/survey_dev', // 模拟数据库URL
  },
  mockData: {
    enabled: false, // 强制禁用模拟数据，使用真实数据库
  },
  api: {
    resendApiKey: 'test_api_key',
  },
  security: {
    aesSecretKey: 'development_secret_key',
    aesIv: 'development_iv',
    jwtSecret: 'development_jwt_secret',
  },
  logging: {
    level: 'debug',
  },
};

// 输出配置信息
console.log(`Loaded configuration for environment: ${config.environment}`);
console.log(`Mock data enabled: ${config.mockData.enabled}`);
console.log(`Log level: ${config.logging.level}`);

export default config;
