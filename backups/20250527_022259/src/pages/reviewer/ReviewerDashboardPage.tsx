import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import ReviewerLayout from '@/components/layouts/ReviewerLayout';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import {
  BarChart, CheckCircle, Clock, FileText, AlertTriangle,
  BookOpen, ThumbsUp, ThumbsDown, Users, Quote, MessageSquare, FileCheck
} from 'lucide-react';

/**
 * 审核员仪表盘页面
 *
 * 显示审核员相关的统计数据和待处理任务
 */
const ReviewerDashboardPage: React.FC = () => {
  const navigate = useNavigate();
  const [approvedStories, setApprovedStories] = useState(0);
  const [rejectedStories, setRejectedStories] = useState(0);
  const [reviewRate, setReviewRate] = useState(0);
  const [totalReviewed, setTotalReviewed] = useState(0);
  const [approvedVoices, setApprovedVoices] = useState(0);
  const [rejectedVoices, setRejectedVoices] = useState(0);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    // 加载真实数据
    const loadData = async () => {
      setIsLoading(true);

      try {
        console.log('ReviewerDashboard - 开始加载真实数据');

        // 调用真实API获取审核员统计数据
        const response = await fetch('http://localhost:8788/api/reviewer/dashboard/stats', {
          headers: {
            'Authorization': `Bearer reviewer1`,
            'Content-Type': 'application/json'
          }
        });

        if (response.ok) {
          const data = await response.json();
          console.log('ReviewerDashboard - API数据:', data);

          setApprovedStories(data.approvedStories || 0);
          setRejectedStories(data.rejectedStories || 0);
          setReviewRate(data.reviewRate || 0);
          setTotalReviewed(data.totalReviewed || 0);
          setApprovedVoices(data.approvedVoices || 0);
          setRejectedVoices(data.rejectedVoices || 0);
        } else {
          throw new Error(`API请求失败: ${response.status}`);
        }
      } catch (error) {
        console.error('加载审核员仪表盘数据失败:', error);
        console.log('ReviewerDashboard - 使用默认值');

        // API失败时使用默认值
        setApprovedStories(0);
        setRejectedStories(0);
        setReviewRate(0);
        setTotalReviewed(0);
        setApprovedVoices(0);
        setRejectedVoices(0);
      } finally {
        setIsLoading(false);
      }
    };

    loadData();
  }, []);

  return (
    <ReviewerLayout>
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-2xl font-bold">审核员仪表盘</h1>
        <div className="flex items-center space-x-2">
          <Button variant="outline" size="sm">
            <Clock className="mr-2 h-4 w-4" />
            今日
          </Button>
          <Button variant="outline" size="sm">
            <Clock className="mr-2 h-4 w-4" />
            本周
          </Button>
          <Button variant="outline" size="sm">
            <Clock className="mr-2 h-4 w-4" />
            本月
          </Button>
        </div>
      </div>

      {/* 总体统计 */}
      <div className="grid gap-4 md:grid-cols-1 mb-6">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between pb-2">
            <CardTitle className="text-sm font-medium">总审核数量</CardTitle>
            <FileCheck className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{isLoading ? '...' : totalReviewed}</div>
            <p className="text-xs text-muted-foreground">
              您已审核的总内容数
            </p>
          </CardContent>
        </Card>
      </div>

      {/* 分类统计 */}
      <div className="grid gap-6 md:grid-cols-2 mb-6">
        {/* 故事审核统计 */}
        <div className="space-y-4">
          <h3 className="text-lg font-semibold flex items-center">
            <BookOpen className="mr-2 h-5 w-5" />
            故事审核
          </h3>
          <div className="grid gap-4 grid-cols-2">
            <Card>
              <CardHeader className="flex flex-row items-center justify-between pb-2">
                <CardTitle className="text-sm font-medium">已批准故事</CardTitle>
                <CheckCircle className="h-4 w-4 text-green-600" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{isLoading ? '...' : approvedStories}</div>
                <p className="text-xs text-muted-foreground">
                  您已批准的故事总数
                </p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="flex flex-row items-center justify-between pb-2">
                <CardTitle className="text-sm font-medium">已拒绝故事</CardTitle>
                <AlertTriangle className="h-4 w-4 text-red-600" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{isLoading ? '...' : rejectedStories}</div>
                <p className="text-xs text-muted-foreground">
                  您已拒绝的故事总数
                </p>
              </CardContent>
            </Card>

            <Card className="col-span-2">
              <CardHeader className="flex flex-row items-center justify-between pb-2">
                <CardTitle className="text-sm font-medium">故事审核通过率</CardTitle>
                <BarChart className="h-4 w-4 text-blue-600" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{isLoading ? '...' : `${reviewRate}%`}</div>
                <p className="text-xs text-muted-foreground">
                  故事审核的通过率
                </p>
              </CardContent>
            </Card>
          </div>
        </div>

        {/* 问卷心声审核统计 */}
        <div className="space-y-4">
          <h3 className="text-lg font-semibold flex items-center">
            <Quote className="mr-2 h-5 w-5" />
            问卷心声审核
          </h3>
          <div className="grid gap-4 grid-cols-2">
            <Card>
              <CardHeader className="flex flex-row items-center justify-between pb-2">
                <CardTitle className="text-sm font-medium">已批准心声</CardTitle>
                <CheckCircle className="h-4 w-4 text-green-600" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{isLoading ? '...' : approvedVoices}</div>
                <p className="text-xs text-muted-foreground">
                  您已批准的问卷心声数量
                </p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="flex flex-row items-center justify-between pb-2">
                <CardTitle className="text-sm font-medium">已拒绝心声</CardTitle>
                <AlertTriangle className="h-4 w-4 text-red-600" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{isLoading ? '...' : rejectedVoices}</div>
                <p className="text-xs text-muted-foreground">
                  您已拒绝的问卷心声数量
                </p>
              </CardContent>
            </Card>

            <Card className="col-span-2">
              <CardHeader className="flex flex-row items-center justify-between pb-2">
                <CardTitle className="text-sm font-medium">心声审核通过率</CardTitle>
                <BarChart className="h-4 w-4 text-purple-600" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">
                  {isLoading ? '...' : `${approvedVoices + rejectedVoices > 0 ? Math.round((approvedVoices / (approvedVoices + rejectedVoices)) * 100) : 0}%`}
                </div>
                <p className="text-xs text-muted-foreground">
                  问卷心声审核的通过率
                </p>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>

      {/* 快速操作 */}
      <div className="grid gap-4 md:grid-cols-2 mb-6">
        <Card>
          <CardHeader>
            <CardTitle>故事审核</CardTitle>
            <CardDescription>
              审核用户提交的故事内容
            </CardDescription>
          </CardHeader>
          <CardContent>
            <p className="text-sm text-muted-foreground mb-4">
              点击开始审核，系统将为您提供5条待审核的故事内容
            </p>
            <Button
              className="w-full"
              onClick={() => navigate('/reviewer/batch/stories')}
            >
              <BookOpen className="mr-2 h-4 w-4" />
              开始审核故事
            </Button>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>问卷心声审核</CardTitle>
            <CardDescription>
              审核来自问卷第6步的用户心声
            </CardDescription>
          </CardHeader>
          <CardContent>
            <p className="text-sm text-muted-foreground mb-4">
              点击开始审核，系统将为您提供5条待审核的问卷心声
            </p>
            <Button
              className="w-full"
              onClick={() => navigate('/reviewer/batch/voices')}
            >
              <Quote className="mr-2 h-4 w-4" />
              开始审核问卷心声
            </Button>
          </CardContent>
        </Card>
      </div>

      {/* 审核统计 */}
      <Card>
        <CardHeader>
          <CardTitle>审核统计</CardTitle>
          <CardDescription>
            您的审核工作统计数据
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="h-[200px] flex items-center justify-center">
            <p className="text-muted-foreground">图表加载中...</p>
          </div>
        </CardContent>
      </Card>
    </ReviewerLayout>
  );
};

export default ReviewerDashboardPage;
