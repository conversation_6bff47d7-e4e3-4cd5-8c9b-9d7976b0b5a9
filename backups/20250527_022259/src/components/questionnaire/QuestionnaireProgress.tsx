import { Progress } from '@/components/ui/progress';

interface QuestionnaireProgressProps {
  currentStep: number;
  totalSteps: number;
  progress: number;
  moduleTitles: string[];
}

export default function QuestionnaireProgress({
  currentStep,
  totalSteps,
  progress,
  moduleTitles,
}: QuestionnaireProgressProps) {
  return (
    <div className="mb-8">
      <div className="flex justify-between items-center mb-2">
        <span className="text-sm font-medium">
          第 {currentStep} 步，共 {totalSteps} 步
        </span>
        <span className="text-sm font-medium">{progress}%</span>
      </div>
      
      <Progress value={progress} className="h-2" />
      
      <div className="mt-4 grid grid-cols-7 gap-2">
        {moduleTitles.map((title, index) => (
          <div 
            key={index}
            className={`text-xs text-center py-1 px-2 rounded-md ${
              index < currentStep 
                ? 'bg-primary text-primary-foreground' 
                : index === currentStep - 1
                ? 'bg-primary/80 text-primary-foreground'
                : 'bg-secondary text-secondary-foreground'
            }`}
          >
            {title}
          </div>
        ))}
      </div>
    </div>
  );
}
