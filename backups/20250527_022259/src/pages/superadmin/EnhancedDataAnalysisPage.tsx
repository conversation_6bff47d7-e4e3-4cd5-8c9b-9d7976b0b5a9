import React, { useState, useEffect } from 'react';
import SuperAdminLayout from '@/components/layouts/SuperAdminLayout';
import { Card, CardContent, CardHeader, CardTitle, CardDescription, CardFooter } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { useToast } from '@/components/ui/use-toast';
import {
  <PERSON><PERSON>hart, LineChart, PieChart, Calendar, TrendingUp, TrendingDown,
  Users, FileText, MessageSquare, Eye, Download, RefreshCw,
  Filter, Share2, Printer, Save, FileDown, Map, Globe
} from 'lucide-react';

// 模拟图表组件
const ChartPlaceholder: React.FC<{ title: string, height: number, type?: 'bar' | 'line' | 'pie' | 'map' }> = ({ title, height, type = 'bar' }) => {
  const Icon = type === 'bar' ? BarChart : 
               type === 'line' ? LineChart : 
               type === 'pie' ? PieChart : Globe;
  
  return (
    <div 
      className="w-full bg-muted rounded-md flex items-center justify-center"
      style={{ height: `${height}px` }}
    >
      <div className="text-center">
        <Icon className="h-10 w-10 mx-auto mb-2 text-muted-foreground" />
        <p className="text-muted-foreground">{title}</p>
      </div>
    </div>
  );
};

/**
 * 增强数据分析页面
 * 
 * 提供更详细的数据洞察，包括用户分析、内容分析、地域分析和时间趋势分析
 */
const EnhancedDataAnalysisPage: React.FC = () => {
  const { toast } = useToast();
  const [isLoading, setIsLoading] = useState(true);
  const [timeRange, setTimeRange] = useState('month');
  const [dataType, setDataType] = useState('all');
  const [chartType, setChartType] = useState('bar');
  const [compareMode, setCompareMode] = useState(false);
  
  // 模拟获取数据
  useEffect(() => {
    const fetchData = async () => {
      try {
        setIsLoading(true);
        // 模拟网络延迟
        await new Promise(resolve => setTimeout(resolve, 1000));
        setIsLoading(false);
      } catch (error) {
        console.error('获取数据失败:', error);
        toast({
          variant: 'destructive',
          title: '获取数据失败',
          description: '无法加载分析数据，请稍后再试'
        });
        setIsLoading(false);
      }
    };

    fetchData();
  }, [toast, timeRange, dataType]);

  // 导出数据
  const handleExportData = (format: 'csv' | 'excel' | 'pdf') => {
    toast({
      title: '导出成功',
      description: `数据已导出为${format.toUpperCase()}格式`
    });
  };

  // 打印数据
  const handlePrintData = () => {
    toast({
      title: '正在打印',
      description: '数据报表已发送到打印队列'
    });
  };

  return (
    <SuperAdminLayout>
      <div className="container mx-auto py-6">
        <div className="flex justify-between items-center mb-6">
          <h1 className="text-2xl font-bold">增强数据分析</h1>
          <div className="flex items-center space-x-2">
            <Select
              value={timeRange}
              onValueChange={setTimeRange}
            >
              <SelectTrigger className="w-[180px]">
                <SelectValue placeholder="选择时间范围" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="today">今日</SelectItem>
                <SelectItem value="week">本周</SelectItem>
                <SelectItem value="month">本月</SelectItem>
                <SelectItem value="quarter">本季度</SelectItem>
                <SelectItem value="year">本年度</SelectItem>
                <SelectItem value="custom">自定义</SelectItem>
              </SelectContent>
            </Select>
            <Select
              value={dataType}
              onValueChange={setDataType}
            >
              <SelectTrigger className="w-[180px]">
                <SelectValue placeholder="选择数据类型" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">全部数据</SelectItem>
                <SelectItem value="questionnaire">问卷数据</SelectItem>
                <SelectItem value="story">故事数据</SelectItem>
                <SelectItem value="user">用户数据</SelectItem>
              </SelectContent>
            </Select>
            <Button variant="outline" onClick={() => setCompareMode(!compareMode)}>
              <Filter className="mr-2 h-4 w-4" />
              {compareMode ? '取消对比' : '同比对比'}
            </Button>
          </div>
        </div>

        <div className="flex justify-end mb-4 space-x-2">
          <Button variant="outline" size="sm" onClick={() => handleExportData('csv')}>
            <FileDown className="mr-2 h-4 w-4" />
            导出CSV
          </Button>
          <Button variant="outline" size="sm" onClick={() => handleExportData('excel')}>
            <FileDown className="mr-2 h-4 w-4" />
            导出Excel
          </Button>
          <Button variant="outline" size="sm" onClick={() => handleExportData('pdf')}>
            <FileDown className="mr-2 h-4 w-4" />
            导出PDF
          </Button>
          <Button variant="outline" size="sm" onClick={handlePrintData}>
            <Printer className="mr-2 h-4 w-4" />
            打印
          </Button>
          <Button variant="outline" size="sm">
            <Share2 className="mr-2 h-4 w-4" />
            分享
          </Button>
        </div>

        <Tabs defaultValue="overview" className="space-y-6">
          <TabsList className="grid grid-cols-4 w-full">
            <TabsTrigger value="overview">总体概览</TabsTrigger>
            <TabsTrigger value="user">用户分析</TabsTrigger>
            <TabsTrigger value="content">内容分析</TabsTrigger>
            <TabsTrigger value="geo">地域分析</TabsTrigger>
          </TabsList>

          {/* 总体概览 */}
          <TabsContent value="overview" className="space-y-6">
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              <Card>
                <CardHeader className="pb-2">
                  <CardTitle className="text-sm font-medium">图表类型</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="flex flex-wrap gap-2">
                    <Button 
                      variant={chartType === 'bar' ? 'default' : 'outline'} 
                      size="sm"
                      onClick={() => setChartType('bar')}
                    >
                      <BarChart className="mr-2 h-4 w-4" />
                      柱状图
                    </Button>
                    <Button 
                      variant={chartType === 'line' ? 'default' : 'outline'} 
                      size="sm"
                      onClick={() => setChartType('line')}
                    >
                      <LineChart className="mr-2 h-4 w-4" />
                      折线图
                    </Button>
                    <Button 
                      variant={chartType === 'pie' ? 'default' : 'outline'} 
                      size="sm"
                      onClick={() => setChartType('pie')}
                    >
                      <PieChart className="mr-2 h-4 w-4" />
                      饼图
                    </Button>
                  </div>
                </CardContent>
              </Card>
              
              <Card className="md:col-span-2">
                <CardHeader className="pb-2">
                  <CardTitle className="text-sm font-medium">数据摘要</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="grid grid-cols-3 gap-4">
                    <div className="flex flex-col">
                      <span className="text-2xl font-bold">3,427</span>
                      <span className="text-sm text-muted-foreground">问卷总数</span>
                    </div>
                    <div className="flex flex-col">
                      <span className="text-2xl font-bold">1,250</span>
                      <span className="text-sm text-muted-foreground">故事总数</span>
                    </div>
                    <div className="flex flex-col">
                      <span className="text-2xl font-bold">4,215</span>
                      <span className="text-sm text-muted-foreground">用户总数</span>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>
            
            <Card>
              <CardHeader>
                <CardTitle>数据趋势</CardTitle>
                <CardDescription>平台各项指标的变化趋势</CardDescription>
              </CardHeader>
              <CardContent>
                <ChartPlaceholder title="趋势图表" height={300} type={chartType as any} />
              </CardContent>
            </Card>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <Card>
                <CardHeader>
                  <CardTitle>数据分布</CardTitle>
                  <CardDescription>各类数据的分布情况</CardDescription>
                </CardHeader>
                <CardContent>
                  <ChartPlaceholder title="分布图表" height={250} type="pie" />
                </CardContent>
              </Card>
              
              <Card>
                <CardHeader>
                  <CardTitle>同比增长</CardTitle>
                  <CardDescription>与去年同期相比的增长情况</CardDescription>
                </CardHeader>
                <CardContent>
                  <ChartPlaceholder title="增长图表" height={250} type="bar" />
                </CardContent>
              </Card>
            </div>
          </TabsContent>

          {/* 用户分析 */}
          <TabsContent value="user" className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle>用户增长趋势</CardTitle>
                <CardDescription>平台用户数量的变化趋势</CardDescription>
              </CardHeader>
              <CardContent>
                <ChartPlaceholder title="用户增长趋势图" height={300} type="line" />
              </CardContent>
            </Card>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <Card>
                <CardHeader>
                  <CardTitle>用户活跃度</CardTitle>
                  <CardDescription>用户活跃程度分析</CardDescription>
                </CardHeader>
                <CardContent>
                  <ChartPlaceholder title="用户活跃度图表" height={250} type="bar" />
                </CardContent>
              </Card>
              
              <Card>
                <CardHeader>
                  <CardTitle>用户角色分布</CardTitle>
                  <CardDescription>不同角色用户的分布情况</CardDescription>
                </CardHeader>
                <CardContent>
                  <ChartPlaceholder title="用户角色分布图" height={250} type="pie" />
                </CardContent>
              </Card>
            </div>
          </TabsContent>

          {/* 内容分析 */}
          <TabsContent value="content" className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle>内容提交趋势</CardTitle>
                <CardDescription>问卷和故事提交的变化趋势</CardDescription>
              </CardHeader>
              <CardContent>
                <ChartPlaceholder title="内容提交趋势图" height={300} type="line" />
              </CardContent>
            </Card>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <Card>
                <CardHeader>
                  <CardTitle>内容类型分布</CardTitle>
                  <CardDescription>不同类型内容的分布情况</CardDescription>
                </CardHeader>
                <CardContent>
                  <ChartPlaceholder title="内容类型分布图" height={250} type="pie" />
                </CardContent>
              </Card>
              
              <Card>
                <CardHeader>
                  <CardTitle>内容审核情况</CardTitle>
                  <CardDescription>内容审核通过率和拒绝率</CardDescription>
                </CardHeader>
                <CardContent>
                  <ChartPlaceholder title="内容审核情况图" height={250} type="bar" />
                </CardContent>
              </Card>
            </div>
          </TabsContent>

          {/* 地域分析 */}
          <TabsContent value="geo" className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle>地域分布</CardTitle>
                <CardDescription>用户和内容的地域分布情况</CardDescription>
              </CardHeader>
              <CardContent>
                <ChartPlaceholder title="地域分布地图" height={400} type="map" />
              </CardContent>
            </Card>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <Card>
                <CardHeader>
                  <CardTitle>省份分布</CardTitle>
                  <CardDescription>各省份用户分布情况</CardDescription>
                </CardHeader>
                <CardContent>
                  <ChartPlaceholder title="省份分布图表" height={250} type="bar" />
                </CardContent>
              </Card>
              
              <Card>
                <CardHeader>
                  <CardTitle>城市分布</CardTitle>
                  <CardDescription>主要城市用户分布情况</CardDescription>
                </CardHeader>
                <CardContent>
                  <ChartPlaceholder title="城市分布图表" height={250} type="bar" />
                </CardContent>
              </Card>
            </div>
          </TabsContent>
        </Tabs>
      </div>
    </SuperAdminLayout>
  );
};

export default EnhancedDataAnalysisPage;
