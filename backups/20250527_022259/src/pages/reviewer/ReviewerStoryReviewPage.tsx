import React, { useState, useEffect } from 'react';
import ReviewerLayout from '@/components/layouts/ReviewerLayout';
import { Card, CardContent, CardDescription, CardHeader, CardTitle, CardFooter } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Badge } from '@/components/ui/badge';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from '@/components/ui/alert-dialog';
import {
  CheckCircle, XCircle, Search, Filter, Clock,
  ThumbsUp, ThumbsDown, AlertTriangle, Tag, Shield
} from 'lucide-react';
import { useToast } from '@/components/ui/use-toast';
import ContentModerationPanel from '@/components/admin/ContentModerationPanel';
import { ContentModerationResult } from '@/hooks/useContentModeration';
import { getPendingContents, approveContent, editContent } from '@/services/dataService';

// 模拟故事数据
const mockStories = [
  {
    id: 1001,
    sequenceNumber: 'S-2024-0101',
    title: '我的考研经历',
    content: '大四那年，我决定考研。备考期间，我每天学习12小时，最终成功考入了理想的学校。这个过程让我明白，坚持和自律是成功的关键。',
    createdAt: new Date(Date.now() - 5 * 24 * 60 * 60 * 1000).toISOString(),
    status: 'pending',
    identityA: '13800138000',
    identityB: '1234',
    tags: []
  },
  {
    id: 1002,
    sequenceNumber: 'S-2024-0102',
    title: '从校园到职场',
    content: '毕业后直接进入了一家大型企业，工作环境和学校完全不同。适应职场文化是我面临的第一个挑战，学会与不同背景的同事相处，理解企业的运作方式，这些都是学校里学不到的经验。',
    createdAt: new Date(Date.now() - 3 * 24 * 60 * 60 * 1000).toISOString(),
    status: 'pending',
    identityA: '13900139000',
    identityB: '123456',
    tags: []
  },
  {
    id: 1003,
    sequenceNumber: 'S-2024-0103',
    title: '我的实习故事',
    content: '大三暑假，我在一家创业公司实习，负责产品设计。虽然工作压力大，但学到了很多实用技能。这段经历让我确定了自己的职业方向。',
    createdAt: new Date(Date.now() - 2 * 24 * 60 * 60 * 1000).toISOString(),
    status: 'pending',
    identityA: '12345678901',
    identityB: '6789',
    tags: []
  }
];

// 模拟标签数据
const mockTags = [
  { id: 1, name: '求职' },
  { id: 2, name: '考研' },
  { id: 3, name: '实习' },
  { id: 4, name: '职场经验' },
  { id: 5, name: '跨专业' },
  { id: 6, name: '留学' },
  { id: 7, name: '创业' },
  { id: 8, name: '心态' }
];

/**
 * 审核员故事审核页面
 *
 * 用于审核员审核用户提交的故事
 */
const ReviewerStoryReviewPage: React.FC = () => {
  const { toast } = useToast();
  const [stories, setStories] = useState<any[]>([]);
  const [selectedStory, setSelectedStory] = useState<any>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedTags, setSelectedTags] = useState<number[]>([]);
  const [rejectDialogOpen, setRejectDialogOpen] = useState(false);
  const [rejectReason, setRejectReason] = useState('');
  const [moderationResult, setModerationResult] = useState<ContentModerationResult | null>(null);
  const [isAIModeratingContent, setIsAIModeratingContent] = useState(false);

  // 加载待审核故事
  const loadPendingStories = async () => {
    try {
      setIsLoading(true);

      // 获取故事类型的待审核内容
      const response = await getPendingContents('type=story&pageSize=50');

      if (response.success && response.data) {
        setStories(response.data);
        if (response.data.length > 0) {
          setSelectedStory(response.data[0]);
        }
      } else {
        console.error('获取待审核故事失败:', response.error);
        setStories([]);
        setSelectedStory(null);
      }
    } catch (error) {
      console.error('加载待审核故事时发生错误:', error);
      setStories([]);
      setSelectedStory(null);
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    loadPendingStories();
  }, []);

  // 处理AI内容审核结果
  const handleModerationResult = (result: ContentModerationResult) => {
    setModerationResult(result);
    setIsAIModeratingContent(false);

    // 根据AI审核结果给出建议
    if (result.suggestedAction === 'approve') {
      toast({
        title: 'AI审核建议',
        description: '内容安全，建议通过',
      });
    } else if (result.suggestedAction === 'reject') {
      toast({
        title: 'AI审核建议',
        description: '内容可能不适合发布，建议拒绝',
        variant: 'destructive',
      });

      // 自动填充拒绝原因
      setRejectReason(result.explanation);
    } else {
      toast({
        title: 'AI审核建议',
        description: '内容需要人工审核',
        variant: 'warning',
      });
    }
  };

  // 开始AI内容审核
  const startAIModeration = () => {
    if (!selectedStory) return;

    setIsAIModeratingContent(true);
    setModerationResult(null);
  };

  // 处理故事审核
  const handleReviewStory = async (storyId: string, approved: boolean) => {
    if (!approved) {
      setRejectDialogOpen(true);
      return;
    }

    try {
      // 调用API批准故事
      const response = await approveContent(storyId, { reviewNotes: '故事审核通过' });

      if (response.success) {
        // 从列表中移除已审核的故事
        const updatedStories = stories.filter(story => story.id !== storyId);
        setStories(updatedStories);

        if (updatedStories.length > 0) {
          setSelectedStory(updatedStories[0]);
        } else {
          setSelectedStory(null);
        }

        // 重置审核结果
        setModerationResult(null);

        toast({
          title: '审核成功',
          description: '故事已批准并发布',
        });
      } else {
        toast({
          title: '审核失败',
          description: response.error || '批准故事时发生错误',
          variant: 'destructive',
        });
      }
    } catch (error) {
      console.error('批准故事时发生错误:', error);
      toast({
        title: '审核失败',
        description: '网络错误，请稍后再试',
        variant: 'destructive',
      });
    }
  };

  // 处理拒绝确认
  const handleRejectConfirm = () => {
    if (!selectedStory) return;

    const updatedStories = stories.filter(story => story.id !== selectedStory.id);
    setStories(updatedStories);

    if (updatedStories.length > 0) {
      setSelectedStory(updatedStories[0]);
    } else {
      setSelectedStory(null);
    }

    toast({
      title: '审核成功',
      description: '故事已拒绝',
      variant: 'destructive',
    });

    setRejectDialogOpen(false);
    setRejectReason('');
  };

  // 处理标签选择
  const handleTagSelect = (tagId: number) => {
    if (selectedTags.includes(tagId)) {
      setSelectedTags(selectedTags.filter(id => id !== tagId));
    } else {
      setSelectedTags([...selectedTags, tagId]);
    }
  };

  return (
    <ReviewerLayout>
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-2xl font-bold">故事审核</h1>
        <div className="flex items-center space-x-2">
          <div className="relative">
            <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
            <Input
              type="search"
              placeholder="搜索故事..."
              className="pl-8 w-[200px]"
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
            />
          </div>
          <Button variant="outline" size="sm">
            <Filter className="mr-2 h-4 w-4" />
            筛选
          </Button>
        </div>
      </div>

      {isLoading ? (
        <div className="flex justify-center items-center h-[400px]">
          <p>加载中...</p>
        </div>
      ) : stories.length === 0 ? (
        <Card>
          <CardContent className="flex flex-col items-center justify-center h-[400px]">
            <AlertTriangle className="h-16 w-16 text-muted-foreground mb-4" />
            <h2 className="text-xl font-semibold mb-2">没有待审核的故事</h2>
            <p className="text-muted-foreground">当前没有需要审核的故事</p>
          </CardContent>
        </Card>
      ) : (
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          {/* 故事列表 */}
          <div className="md:col-span-1">
            <Card>
              <CardHeader>
                <CardTitle>待审核故事 ({stories.length})</CardTitle>
                <CardDescription>
                  点击故事查看详情
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-2">
                  {stories.map(story => (
                    <div
                      key={story.id}
                      className={`p-3 border rounded-md cursor-pointer transition-colors ${
                        selectedStory?.id === story.id
                          ? 'border-primary bg-primary/5'
                          : 'hover:bg-muted'
                      }`}
                      onClick={() => setSelectedStory(story)}
                    >
                      <div className="flex justify-between items-start">
                        <h3 className="font-medium truncate">{story.title}</h3>
                        <Badge variant="outline" className="ml-2 shrink-0">
                          {story.sequenceNumber}
                        </Badge>
                      </div>
                      <p className="text-sm text-muted-foreground mt-1 line-clamp-2">
                        {story.content}
                      </p>
                      <div className="flex items-center mt-2 text-xs text-muted-foreground">
                        <Clock className="h-3 w-3 mr-1" />
                        {new Date(story.createdAt).toLocaleString()}
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </div>

          {/* 故事详情 */}
          <div className="md:col-span-2">
            {selectedStory ? (
              <Card>
                <CardHeader>
                  <div className="flex justify-between items-start">
                    <div>
                      <CardTitle>{selectedStory.title}</CardTitle>
                      <CardDescription>
                        序列号: {selectedStory.sequenceNumber} | 提交时间: {new Date(selectedStory.createdAt).toLocaleString()}
                      </CardDescription>
                    </div>
                    <Badge variant="outline">待审核</Badge>
                  </div>
                </CardHeader>
                <CardContent>
                  <Tabs defaultValue="content">
                    <TabsList>
                      <TabsTrigger value="content">内容</TabsTrigger>
                      <TabsTrigger value="tags">标签</TabsTrigger>
                    </TabsList>

                    <TabsContent value="content" className="mt-4">
                      <div className="border rounded-md p-4 min-h-[200px] mb-4">
                        {selectedStory.content}
                      </div>

                      <div className="flex justify-end mb-4">
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={startAIModeration}
                          disabled={isAIModeratingContent}
                        >
                          <Shield className="mr-2 h-4 w-4" />
                          AI内容审核
                        </Button>
                      </div>

                      {isAIModeratingContent && (
                        <ContentModerationPanel
                          initialContent={selectedStory.content}
                          contentType="story"
                          contentId={selectedStory.id.toString()}
                          onResult={handleModerationResult}
                          autoModerate={true}
                        />
                      )}

                      {moderationResult && !isAIModeratingContent && (
                        <div className="mt-4 p-4 border rounded-md">
                          <h3 className="text-sm font-medium flex items-center mb-2">
                            <Shield className="mr-2 h-4 w-4" />
                            AI审核结果
                          </h3>

                          <div className="flex items-center mb-2">
                            <span className="text-sm mr-2">建议操作:</span>
                            {moderationResult.suggestedAction === 'approve' && (
                              <Badge variant="outline" className="bg-green-50 text-green-700 border-green-200">
                                <CheckCircle className="w-3 h-3 mr-1" />
                                通过
                              </Badge>
                            )}
                            {moderationResult.suggestedAction === 'reject' && (
                              <Badge variant="outline" className="bg-red-50 text-red-700 border-red-200">
                                <XCircle className="w-3 h-3 mr-1" />
                                拒绝
                              </Badge>
                            )}
                            {moderationResult.suggestedAction === 'review' && (
                              <Badge variant="outline" className="bg-yellow-50 text-yellow-700 border-yellow-200">
                                <AlertTriangle className="w-3 h-3 mr-1" />
                                人工审核
                              </Badge>
                            )}
                          </div>

                          {moderationResult.issues.length > 0 && (
                            <div className="mb-2">
                              <span className="text-sm font-medium">发现的问题:</span>
                              <ul className="list-disc list-inside text-sm text-red-600 ml-2">
                                {moderationResult.issues.map((issue, index) => (
                                  <li key={index}>{issue}</li>
                                ))}
                              </ul>
                            </div>
                          )}

                          <p className="text-sm">{moderationResult.explanation}</p>
                        </div>
                      )}
                    </TabsContent>

                    <TabsContent value="tags" className="mt-4">
                      <div className="border rounded-md p-4 min-h-[200px] mb-4">
                        <h3 className="text-sm font-medium mb-2">为故事添加标签</h3>
                        <div className="flex flex-wrap gap-2">
                          {mockTags.map(tag => (
                            <Badge
                              key={tag.id}
                              variant={selectedTags.includes(tag.id) ? "default" : "outline"}
                              className="cursor-pointer"
                              onClick={() => handleTagSelect(tag.id)}
                            >
                              <Tag className="h-3 w-3 mr-1" />
                              {tag.name}
                            </Badge>
                          ))}
                        </div>
                      </div>
                    </TabsContent>
                  </Tabs>
                </CardContent>
                <CardFooter className="flex justify-between">
                  <Button
                    variant="outline"
                    onClick={() => handleReviewStory(selectedStory.id, false)}
                  >
                    <ThumbsDown className="mr-2 h-4 w-4" />
                    拒绝
                  </Button>
                  <Button
                    onClick={() => handleReviewStory(selectedStory.id, true)}
                  >
                    <ThumbsUp className="mr-2 h-4 w-4" />
                    批准
                  </Button>
                </CardFooter>
              </Card>
            ) : (
              <Card>
                <CardContent className="flex flex-col items-center justify-center h-[400px]">
                  <p className="text-muted-foreground">请从左侧选择一个故事进行审核</p>
                </CardContent>
              </Card>
            )}
          </div>
        </div>
      )}

      {/* 拒绝原因对话框 */}
      <AlertDialog open={rejectDialogOpen} onOpenChange={setRejectDialogOpen}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>拒绝故事</AlertDialogTitle>
            <AlertDialogDescription>
              请提供拒绝原因，这将帮助用户理解为什么他们的故事被拒绝。
            </AlertDialogDescription>
          </AlertDialogHeader>
          <div className="py-4">
            <Textarea
              placeholder="请输入拒绝原因..."
              value={rejectReason}
              onChange={(e) => setRejectReason(e.target.value)}
              className="min-h-[100px]"
            />
          </div>
          <AlertDialogFooter>
            <AlertDialogCancel>取消</AlertDialogCancel>
            <AlertDialogAction onClick={handleRejectConfirm}>确认拒绝</AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </ReviewerLayout>
  );
};

export default ReviewerStoryReviewPage;
