/**
 * Cloudflare 优化的 API 实现
 * 基于 KV + D1 + R2 的多层数据架构
 */

import { Context } from 'hono';

interface Env {
  KV: KVNamespace;
  DB: D1Database;
  R2: R2Bucket;
}

/**
 * 数据路由器 - 智能选择数据源
 */
class CloudflareDataRouter {
  constructor(private env: Env) {}

  /**
   * 获取问卷统计数据
   * 优先级: KV缓存 → D1聚合 → 写入缓存
   */
  async getQuestionStats(questionId: string) {
    const cacheKey = `stats:question:${questionId}`;
    
    try {
      // 1. 尝试KV缓存 (1-10ms)
      const cached = await this.env.KV.get(cacheKey);
      if (cached) {
        return {
          data: JSON.parse(cached),
          source: 'cache',
          latency: 'low'
        };
      }
      
      // 2. 查询D1聚合数据 (10-50ms)
      const stats = await this.env.DB.prepare(`
        SELECT 
          answer_value,
          COUNT(*) as count,
          ROUND(COUNT(*) * 100.0 / SUM(COUNT(*)) OVER(), 2) as percentage
        FROM question_answers 
        WHERE question_id = ? 
        GROUP BY answer_value
        ORDER BY count DESC
      `).bind(questionId).all();
      
      if (!stats.results) {
        throw new Error('No data found');
      }
      
      const formattedStats = {
        questionId,
        totalResponses: stats.results.reduce((sum, row) => sum + row.count, 0),
        options: stats.results.map(row => ({
          value: row.answer_value,
          count: row.count,
          percentage: row.percentage
        })),
        lastUpdated: Date.now()
      };
      
      // 3. 写入KV缓存 (异步，不阻塞响应)
      this.env.KV.put(cacheKey, JSON.stringify(formattedStats), {
        expirationTtl: 3600 // 1小时
      }).catch(console.error);
      
      return {
        data: formattedStats,
        source: 'database',
        latency: 'medium'
      };
      
    } catch (error) {
      console.error('Failed to get question stats:', error);
      
      // 4. 降级到静态数据
      return {
        data: this.getStaticFallback(questionId),
        source: 'fallback',
        latency: 'high',
        error: error.message
      };
    }
  }

  /**
   * 获取故事内容
   * 流程: D1元数据 → R2完整内容 → 合并返回
   */
  async getStoryContent(storyId: string, includeFullContent = false) {
    try {
      // 1. 从D1获取元数据 (10-50ms)
      const metadata = await this.env.DB.prepare(`
        SELECT 
          id, user_id, title, category, tags, status,
          likes, views, word_count, r2_path,
          created_at, updated_at
        FROM content_metadata 
        WHERE id = ? AND content_type = 'story'
      `).bind(storyId).first();
      
      if (!metadata) {
        return { error: 'Story not found', code: 404 };
      }
      
      const result = {
        id: metadata.id,
        title: metadata.title,
        category: metadata.category,
        tags: metadata.tags ? JSON.parse(metadata.tags) : [],
        status: metadata.status,
        likes: metadata.likes,
        views: metadata.views,
        wordCount: metadata.word_count,
        createdAt: metadata.created_at,
        source: 'metadata'
      };
      
      // 2. 如果需要完整内容，从R2获取 (50-200ms)
      if (includeFullContent && metadata.r2_path) {
        try {
          const contentObject = await this.env.R2.get(metadata.r2_path);
          if (contentObject) {
            const fullContent = await contentObject.json();
            result.content = fullContent.content;
            result.fullMetadata = fullContent.metadata;
            result.source = 'full';
          }
        } catch (r2Error) {
          console.warn('Failed to get R2 content:', r2Error);
          // 不阻塞响应，只返回元数据
        }
      }
      
      // 3. 异步更新浏览量
      this.incrementViewCount(storyId).catch(console.error);
      
      return { data: result };
      
    } catch (error) {
      console.error('Failed to get story content:', error);
      return { error: 'Internal server error', code: 500 };
    }
  }

  /**
   * 用户内容管理
   * 流程: 验证会话 → 查询用户内容 → 返回列表
   */
  async getUserContent(userId: string, contentType: 'story' | 'voice') {
    try {
      // 1. 验证用户会话 (KV查询)
      const sessionValid = await this.validateUserSession(userId);
      if (!sessionValid) {
        return { error: 'Unauthorized', code: 401 };
      }
      
      // 2. 查询用户内容元数据
      const contents = await this.env.DB.prepare(`
        SELECT 
          id, title, status, likes, views, word_count,
          created_at, updated_at
        FROM content_metadata 
        WHERE user_id = ? AND content_type = ?
        ORDER BY created_at DESC
        LIMIT 50
      `).bind(userId, contentType).all();
      
      if (!contents.results) {
        return { data: [] };
      }
      
      // 3. 格式化返回数据
      const formattedContents = contents.results.map(item => ({
        id: item.id,
        title: item.title,
        status: item.status,
        stats: {
          likes: item.likes,
          views: item.views,
          wordCount: item.word_count
        },
        createdAt: item.created_at,
        updatedAt: item.updated_at,
        canEdit: item.status === 'pending' || item.status === 'rejected',
        canDelete: true
      }));
      
      return { data: formattedContents };
      
    } catch (error) {
      console.error('Failed to get user content:', error);
      return { error: 'Internal server error', code: 500 };
    }
  }

  /**
   * 提交新内容
   * 流程: 验证 → D1写入元数据 → R2写入内容 → KV更新缓存
   */
  async submitContent(userId: string, contentData: any) {
    const contentId = `text_${crypto.randomUUID()}`;
    const rollbackActions: (() => Promise<void>)[] = [];
    
    try {
      // 1. 验证用户权限
      const canSubmit = await this.validateSubmissionPermission(userId);
      if (!canSubmit) {
        return { error: 'Permission denied', code: 403 };
      }
      
      // 2. 写入D1元数据
      const r2Path = `content/${contentData.type}/${new Date().getFullYear()}/${String(new Date().getMonth() + 1).padStart(2, '0')}/${contentId}.json`;
      
      await this.env.DB.prepare(`
        INSERT INTO content_metadata (
          id, user_id, content_type, title, category, tags,
          word_count, r2_path, status, created_at
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, 'pending', datetime('now'))
      `).bind(
        contentId,
        userId,
        contentData.type,
        contentData.title,
        contentData.category || null,
        JSON.stringify(contentData.tags || []),
        contentData.content.length,
        r2Path
      ).run();
      
      rollbackActions.push(() => 
        this.env.DB.prepare('DELETE FROM content_metadata WHERE id = ?')
          .bind(contentId).run()
      );
      
      // 3. 写入R2完整内容
      const fullContent = {
        id: contentId,
        type: contentData.type,
        title: contentData.title,
        content: contentData.content,
        metadata: {
          userId,
          category: contentData.category,
          tags: contentData.tags || [],
          wordCount: contentData.content.length,
          language: 'zh-CN'
        },
        audit: {
          createdAt: new Date().toISOString(),
          status: 'pending',
          version: 1
        }
      };
      
      await this.env.R2.put(r2Path, JSON.stringify(fullContent), {
        httpMetadata: {
          contentType: 'application/json',
          contentLanguage: 'zh-CN'
        },
        customMetadata: {
          userId,
          contentType: contentData.type,
          status: 'pending'
        }
      });
      
      rollbackActions.push(() => this.env.R2.delete(r2Path));
      
      // 4. 清除相关缓存
      await this.clearUserContentCache(userId);
      
      return {
        data: {
          id: contentId,
          status: 'pending',
          message: 'Content submitted successfully'
        }
      };
      
    } catch (error) {
      console.error('Failed to submit content:', error);
      
      // 执行回滚
      for (const rollback of rollbackActions.reverse()) {
        try {
          await rollback();
        } catch (rollbackError) {
          console.error('Rollback failed:', rollbackError);
        }
      }
      
      return { error: 'Submission failed', code: 500 };
    }
  }

  /**
   * 删除用户内容 (软删除)
   */
  async deleteUserContent(userId: string, contentId: string) {
    try {
      // 1. 验证所有权
      const ownership = await this.env.DB.prepare(`
        SELECT id FROM content_metadata 
        WHERE id = ? AND user_id = ?
      `).bind(contentId, userId).first();
      
      if (!ownership) {
        return { error: 'Content not found or access denied', code: 404 };
      }
      
      // 2. 软删除 (更新状态而不是物理删除)
      await this.env.DB.prepare(`
        UPDATE content_metadata 
        SET status = 'deleted', updated_at = datetime('now')
        WHERE id = ?
      `).bind(contentId).run();
      
      // 3. 清除缓存
      await this.clearUserContentCache(userId);
      
      return {
        data: {
          id: contentId,
          status: 'deleted',
          message: 'Content deleted successfully'
        }
      };
      
    } catch (error) {
      console.error('Failed to delete content:', error);
      return { error: 'Deletion failed', code: 500 };
    }
  }

  /**
   * 辅助方法
   */
  private async validateUserSession(userId: string): Promise<boolean> {
    try {
      const sessions = await this.env.KV.list({ prefix: 'session:' });
      for (const key of sessions.keys) {
        const session = await this.env.KV.get(key.name);
        if (session) {
          const sessionData = JSON.parse(session);
          if (sessionData.userId === userId) {
            return true;
          }
        }
      }
      return false;
    } catch {
      return false;
    }
  }

  private async validateSubmissionPermission(userId: string): Promise<boolean> {
    // 检查用户是否有提交权限
    const user = await this.env.DB.prepare(`
      SELECT role, is_active FROM users_optimized WHERE id = ?
    `).bind(userId).first();
    
    return user && user.is_active && ['user', 'reviewer', 'admin', 'superadmin'].includes(user.role);
  }

  private async incrementViewCount(contentId: string): Promise<void> {
    await this.env.DB.prepare(`
      UPDATE content_metadata 
      SET views = views + 1 
      WHERE id = ?
    `).bind(contentId).run();
  }

  private async clearUserContentCache(userId: string): Promise<void> {
    // 清除用户相关的缓存
    const cacheKeys = [
      `user:content:${userId}`,
      `hot:content:daily`,
      `stats:user:${userId}`
    ];
    
    await Promise.all(
      cacheKeys.map(key => this.env.KV.delete(key))
    );
  }

  private getStaticFallback(questionId: string) {
    return {
      questionId,
      totalResponses: 0,
      options: [],
      lastUpdated: Date.now(),
      fallback: true
    };
  }
}

/**
 * API 路由处理器
 */
export class CloudflareAPIHandler {
  private router: CloudflareDataRouter;

  constructor(env: Env) {
    this.router = new CloudflareDataRouter(env);
  }

  /**
   * 问卷统计API
   */
  async getQuestionnaireStats(c: Context<{ Bindings: Env }>) {
    const questionId = c.req.query('questionId');
    
    if (!questionId) {
      return c.json({ error: 'Question ID required' }, 400);
    }
    
    const result = await this.router.getQuestionStats(questionId);
    
    if (result.data) {
      return c.json({
        success: true,
        data: result.data,
        meta: {
          source: result.source,
          latency: result.latency
        }
      });
    } else {
      return c.json({ error: result.error }, 500);
    }
  }

  /**
   * 故事内容API
   */
  async getStoryContent(c: Context<{ Bindings: Env }>) {
    const storyId = c.req.param('id');
    const includeContent = c.req.query('full') === 'true';
    
    const result = await this.router.getStoryContent(storyId, includeContent);
    
    if (result.data) {
      return c.json({
        success: true,
        story: result.data
      });
    } else {
      return c.json({ error: result.error }, result.code || 500);
    }
  }

  /**
   * 用户内容管理API
   */
  async getUserContent(c: Context<{ Bindings: Env }>) {
    const userId = c.req.header('X-User-ID'); // 从认证中间件获取
    const contentType = c.req.query('type') as 'story' | 'voice';
    
    if (!userId) {
      return c.json({ error: 'Authentication required' }, 401);
    }
    
    const result = await this.router.getUserContent(userId, contentType);
    
    if (result.data) {
      return c.json({
        success: true,
        contents: result.data
      });
    } else {
      return c.json({ error: result.error }, result.code || 500);
    }
  }

  /**
   * 提交内容API
   */
  async submitContent(c: Context<{ Bindings: Env }>) {
    const userId = c.req.header('X-User-ID');
    
    if (!userId) {
      return c.json({ error: 'Authentication required' }, 401);
    }
    
    const contentData = await c.req.json();
    const result = await this.router.submitContent(userId, contentData);
    
    if (result.data) {
      return c.json({
        success: true,
        ...result.data
      });
    } else {
      return c.json({ error: result.error }, result.code || 500);
    }
  }

  /**
   * 删除内容API
   */
  async deleteContent(c: Context<{ Bindings: Env }>) {
    const userId = c.req.header('X-User-ID');
    const contentId = c.req.param('id');
    
    if (!userId) {
      return c.json({ error: 'Authentication required' }, 401);
    }
    
    const result = await this.router.deleteUserContent(userId, contentId);
    
    if (result.data) {
      return c.json({
        success: true,
        ...result.data
      });
    } else {
      return c.json({ error: result.error }, result.code || 500);
    }
  }
}
