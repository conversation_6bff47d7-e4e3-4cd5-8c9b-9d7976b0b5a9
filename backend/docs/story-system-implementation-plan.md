# 用户认证故事发布系统实现计划

## 📋 实现步骤

### 阶段1: 数据库迁移 (1-2天)

#### 1.1 创建新表结构
```bash
# 执行数据库迁移脚本
cd backend/database
sqlite3 ../database.db < story-system-schema.sql
```

#### 1.2 数据迁移
- 将现有 `story_contents_v2` 数据迁移到新的 `stories` 表
- 将现有 `users_v2` 数据迁移到新的 `users` 表
- 迁移标签和关联关系

#### 1.3 验证数据完整性
- 检查外键约束
- 验证索引创建
- 测试视图查询

### 阶段2: 用户认证系统 (2-3天)

#### 2.1 JWT认证实现
```javascript
// backend/src/services/authService.js
class AuthService {
  async register(userData) {
    // 1. 验证邮箱唯一性
    // 2. 密码哈希处理
    // 3. 生成用户UUID
    // 4. 发送验证邮件
    // 5. 创建用户记录
  }
  
  async login(email, password) {
    // 1. 验证用户凭证
    // 2. 生成JWT令牌
    // 3. 创建会话记录
    // 4. 返回用户信息
  }
  
  async verifyToken(token) {
    // 1. 验证JWT令牌
    // 2. 检查会话有效性
    // 3. 返回用户信息
  }
}
```

#### 2.2 认证中间件
```javascript
// backend/src/middlewares/auth.middleware.js
export const requireAuth = async (c, next) => {
  const token = c.req.header('Authorization')?.replace('Bearer ', '');
  if (!token) {
    return c.json({ success: false, error: '需要登录' }, 401);
  }
  
  const user = await authService.verifyToken(token);
  if (!user) {
    return c.json({ success: false, error: '无效令牌' }, 401);
  }
  
  c.set('user', user);
  await next();
};
```

#### 2.3 前端认证集成
```typescript
// frontend/src/services/authService.ts
class AuthService {
  async register(userData: RegisterData) {
    const response = await apiClient.post('/auth/register', userData);
    return response.data;
  }
  
  async login(email: string, password: string) {
    const response = await apiClient.post('/auth/login', { email, password });
    if (response.data.success) {
      localStorage.setItem('auth_token', response.data.data.token);
      localStorage.setItem('user_uuid', response.data.data.user_uuid);
    }
    return response.data;
  }
  
  async logout() {
    localStorage.removeItem('auth_token');
    localStorage.removeItem('user_uuid');
  }
  
  isAuthenticated(): boolean {
    return !!localStorage.getItem('auth_token');
  }
}
```

### 阶段3: 故事发布系统 (3-4天)

#### 3.1 故事创建API
```javascript
// backend/src/api/story/story.controller.js
export const createStory = [
  requireAuth,
  zValidator('json', storySchema),
  async (c) => {
    const user = c.get('user');
    const storyData = c.req.valid('json');
    
    // 1. 验证用户权限
    // 2. 处理标签关联
    // 3. 创建故事记录
    // 4. 触发审核流程
    // 5. 返回创建结果
  }
];
```

#### 3.2 多维度搜索API
```javascript
export const searchStories = async (c) => {
  const { query, filters, sort, page, pageSize } = c.req.query();
  
  // 构建复杂查询
  let sql = `
    SELECT s.*, u.display_name, u.avatar_url,
           GROUP_CONCAT(t.display_name) as tag_names
    FROM stories s
    LEFT JOIN users u ON s.user_uuid = u.uuid
    LEFT JOIN story_tags st ON s.id = st.story_id
    LEFT JOIN tags t ON st.tag_id = t.id
    WHERE s.status = 'approved'
  `;
  
  // 动态添加筛选条件
  if (filters.categories) {
    sql += ` AND s.category IN (${filters.categories.map(c => `'${c}'`).join(',')})`;
  }
  
  if (filters.education_levels) {
    sql += ` AND s.education_level IN (${filters.education_levels.map(e => `'${e}'`).join(',')})`;
  }
  
  // 添加排序和分页
  sql += ` GROUP BY s.id ORDER BY ${getSortClause(sort)} LIMIT ${pageSize} OFFSET ${(page-1)*pageSize}`;
  
  const stories = await queryDatabase(sql);
  return c.json({ success: true, data: { stories, pagination: {...} } });
};
```

#### 3.3 前端故事发布组件
```typescript
// frontend/src/components/story/StoryCreateForm.tsx
export const StoryCreateForm = () => {
  const [formData, setFormData] = useState({
    title: '',
    content: '',
    category: '',
    tags: [],
    is_anonymous: false,
    education_level: '',
    industry_code: '',
    // ...
  });
  
  const handleSubmit = async (e) => {
    e.preventDefault();
    
    if (!authService.isAuthenticated()) {
      // 跳转到登录页面
      navigate('/login');
      return;
    }
    
    try {
      const response = await storyService.createStory(formData);
      if (response.success) {
        toast.success('故事提交成功，等待审核');
        navigate('/my-stories');
      }
    } catch (error) {
      toast.error('提交失败，请重试');
    }
  };
  
  return (
    <form onSubmit={handleSubmit}>
      {/* 表单字段 */}
    </form>
  );
};
```

### 阶段4: 标签系统优化 (1-2天)

#### 4.1 智能标签推荐
```javascript
// backend/src/services/tagService.js
export const getTagSuggestions = async (query, context) => {
  // 1. 基于输入的模糊匹配
  // 2. 基于用户历史的个性化推荐
  // 3. 基于内容相似度的推荐
  // 4. 热门标签推荐
};
```

#### 4.2 标签分类管理
```javascript
export const getTagsByCategory = async (category) => {
  const tags = await queryDatabase(`
    SELECT * FROM tags 
    WHERE category = ? AND status = 'active'
    ORDER BY usage_count DESC, is_featured DESC
  `, [category]);
  
  return tags;
};
```

### 阶段5: 前端界面改造 (2-3天)

#### 5.1 登录注册页面
- 用户注册表单
- 登录表单
- 邮箱验证流程
- 密码重置功能

#### 5.2 故事发布页面改造
- 移除匿名发布选项
- 添加用户认证检查
- 优化标签选择器
- 添加草稿保存功能

#### 5.3 用户个人中心
- 个人资料管理
- 我的故事列表
- 故事统计信息
- 账户设置

#### 5.4 故事浏览页面优化
- 高级搜索界面
- 多维度筛选器
- 作者信息展示
- 相关故事推荐

## 🔧 技术实现要点

### 1. 安全性考虑
- **密码安全**: 使用bcrypt进行密码哈希
- **JWT安全**: 设置合理的过期时间和刷新机制
- **SQL注入防护**: 使用参数化查询
- **XSS防护**: 前端输入验证和转义

### 2. 性能优化
- **数据库索引**: 针对搜索场景优化的复合索引
- **查询优化**: 使用视图简化复杂查询
- **缓存策略**: Redis缓存热门内容和用户会话
- **分页优化**: 游标分页替代偏移分页

### 3. 用户体验
- **渐进式注册**: 分步骤收集用户信息
- **智能表单**: 自动填充和验证
- **实时反馈**: 表单验证和提交状态
- **响应式设计**: 移动端适配

## 📅 时间安排

| 阶段 | 任务 | 预计时间 | 负责人 |
|------|------|----------|--------|
| 1 | 数据库迁移 | 1-2天 | 后端开发 |
| 2 | 用户认证系统 | 2-3天 | 后端开发 |
| 3 | 故事发布系统 | 3-4天 | 后端开发 |
| 4 | 标签系统优化 | 1-2天 | 后端开发 |
| 5 | 前端界面改造 | 2-3天 | 前端开发 |
| 6 | 测试和优化 | 1-2天 | 全栈测试 |

**总计**: 10-16天

## ✅ 验收标准

1. **用户认证**: 用户可以注册、登录、管理账户
2. **故事发布**: 只有登录用户可以发布故事
3. **UUID绑定**: 故事与用户UUID正确关联
4. **多维搜索**: 支持按分类、学历、行业、标签等搜索
5. **标签系统**: 智能标签推荐和分类管理
6. **性能要求**: 搜索响应时间 < 500ms
7. **安全要求**: 通过基本安全测试
