/**
 * 👨‍💼 管理员模块路由
 * 包含管理员仪表板、统计、数据管理等相关API
 */

import { Hono } from 'hono';
import { createSuccessResponse, createErrorResponse, wrapAsyncHandler } from '../utils/response.js';

const adminRouter = new Hono();

// 管理员仪表板统计API - 增强版，提供完整的真实数据
adminRouter.get('/dashboard/stats', wrapAsyncHandler(async (c) => {
  // 获取当前时间和今日开始时间
  const now = new Date();
  const todayStart = new Date(now.getFullYear(), now.getMonth(), now.getDate()).toISOString();
  const yesterdayStart = new Date(now.getFullYear(), now.getMonth(), now.getDate() - 1).toISOString();
  const weekStart = new Date(now.getFullYear(), now.getMonth(), now.getDate() - 7).toISOString();
  const monthStart = new Date(now.getFullYear(), now.getMonth() - 1, now.getDate()).toISOString();

  // 获取用户统计（总数、今日新增、活跃用户）
  const usersStatsResult = await c.env.DB.prepare(`
    SELECT
      COUNT(*) as total,
      SUM(CASE WHEN created_at >= ? THEN 1 ELSE 0 END) as today_new,
      SUM(CASE WHEN created_at >= ? THEN 1 ELSE 0 END) as yesterday_new,
      SUM(CASE WHEN created_at >= ? THEN 1 ELSE 0 END) as week_new,
      SUM(CASE WHEN updated_at >= ? THEN 1 ELSE 0 END) as active_today
    FROM users_v2
  `).bind(todayStart, yesterdayStart, weekStart, todayStart).first();

  // 获取故事统计
  const storiesStatsResult = await c.env.DB.prepare(`
    SELECT
      COUNT(*) as total,
      SUM(CASE WHEN created_at >= ? THEN 1 ELSE 0 END) as today_new,
      SUM(CASE WHEN created_at >= ? THEN 1 ELSE 0 END) as yesterday_new,
      SUM(CASE WHEN created_at >= ? THEN 1 ELSE 0 END) as week_new,
      SUM(CASE WHEN status = 'pending' THEN 1 ELSE 0 END) as pending,
      SUM(CASE WHEN status = 'approved' THEN 1 ELSE 0 END) as approved,
      SUM(likes) as total_likes,
      SUM(views) as total_views
    FROM story_contents_v2
  `).bind(todayStart, yesterdayStart, weekStart).first();

  // 获取问卷心声统计
  const voicesStatsResult = await c.env.DB.prepare(`
    SELECT
      COUNT(*) as total,
      SUM(CASE WHEN created_at >= ? THEN 1 ELSE 0 END) as today_new,
      SUM(CASE WHEN created_at >= ? THEN 1 ELSE 0 END) as yesterday_new,
      SUM(CASE WHEN created_at >= ? THEN 1 ELSE 0 END) as week_new,
      SUM(CASE WHEN status = 'pending' THEN 1 ELSE 0 END) as pending,
      SUM(CASE WHEN status = 'approved' THEN 1 ELSE 0 END) as approved,
      SUM(likes) as total_likes,
      SUM(views) as total_views
    FROM questionnaire_voices_v2
  `).bind(todayStart, yesterdayStart, weekStart).first();

  // 获取问卷回复统计
  const responsesStatsResult = await c.env.DB.prepare(`
    SELECT
      COUNT(*) as total,
      SUM(CASE WHEN created_at >= ? THEN 1 ELSE 0 END) as today_new,
      SUM(CASE WHEN created_at >= ? THEN 1 ELSE 0 END) as yesterday_new,
      SUM(CASE WHEN created_at >= ? THEN 1 ELSE 0 END) as week_new
    FROM questionnaire_responses_v2
  `).bind(todayStart, yesterdayStart, weekStart).first();

  // 计算趋势百分比（相比昨天）
  const calculateTrend = (today, yesterday) => {
    if (yesterday === 0) return today > 0 ? 100 : 0;
    return Math.round(((today - yesterday) / yesterday) * 100);
  };

  // 组织返回数据，提供前端需要的所有字段
  const stats = {
    // 基础统计数据
    totalUsers: usersStatsResult?.total || 0,
    totalStories: storiesStatsResult?.total || 0,
    totalQuestionnaireVoices: voicesStatsResult?.total || 0, // 修正字段名
    totalVoices: voicesStatsResult?.total || 0, // 保持兼容性
    totalResponses: responsesStatsResult?.total || 0,

    // 今日新增数据
    todayUsers: usersStatsResult?.today_new || 0,
    todayStories: storiesStatsResult?.today_new || 0,
    todayQuestionnaireVoices: voicesStatsResult?.today_new || 0,
    todayResponses: responsesStatsResult?.today_new || 0,

    // 活跃用户（今日有活动的用户）
    activeUsers: usersStatsResult?.active_today || 0,

    // 待审核统计
    pendingStories: storiesStatsResult?.pending || 0,
    pendingVoices: voicesStatsResult?.pending || 0,
    pendingReviews: {
      stories: storiesStatsResult?.pending || 0,
      voices: voicesStatsResult?.pending || 0,
      total: (storiesStatsResult?.pending || 0) + (voicesStatsResult?.pending || 0)
    },

    // 趋势数据（相比昨天的变化百分比）
    trends: {
      users: calculateTrend(usersStatsResult?.today_new || 0, usersStatsResult?.yesterday_new || 0),
      stories: calculateTrend(storiesStatsResult?.today_new || 0, storiesStatsResult?.yesterday_new || 0),
      voices: calculateTrend(voicesStatsResult?.today_new || 0, voicesStatsResult?.yesterday_new || 0),
      responses: calculateTrend(responsesStatsResult?.today_new || 0, responsesStatsResult?.yesterday_new || 0)
    },

    // 互动统计
    totalLikes: (storiesStatsResult?.total_likes || 0) + (voicesStatsResult?.total_likes || 0),
    totalViews: (storiesStatsResult?.total_views || 0) + (voicesStatsResult?.total_views || 0),

    // 系统状态
    systemHealth: 'good',
    lastUpdated: new Date().toISOString()
  };

  return c.json(createSuccessResponse(stats, 'Dashboard statistics retrieved successfully'));
}));

// 详细统计API - 提供更全面的数据分析
adminRouter.get('/dashboard/detailed-stats', wrapAsyncHandler(async (c) => {
  const timeRange = c.req.query('range') || 'today'; // today, week, month, all

  // 根据时间范围设置查询条件
  let timeCondition = '';
  const now = new Date();

  switch (timeRange) {
    case 'today':
      const todayStart = new Date(now.getFullYear(), now.getMonth(), now.getDate()).toISOString();
      timeCondition = `WHERE created_at >= '${todayStart}'`;
      break;
    case 'week':
      const weekStart = new Date(now.getFullYear(), now.getMonth(), now.getDate() - 7).toISOString();
      timeCondition = `WHERE created_at >= '${weekStart}'`;
      break;
    case 'month':
      const monthStart = new Date(now.getFullYear(), now.getMonth() - 1, now.getDate()).toISOString();
      timeCondition = `WHERE created_at >= '${monthStart}'`;
      break;
    default:
      timeCondition = ''; // 全部数据
  }

  // 获取用户分布统计
  const userDistributionResult = await c.env.DB.prepare(`
    SELECT
      user_type,
      COUNT(*) as count
    FROM users_v2
    ${timeCondition}
    GROUP BY user_type
  `).all();

  // 获取内容状态分布
  const contentStatusResult = await c.env.DB.prepare(`
    SELECT
      'story' as content_type,
      status,
      COUNT(*) as count
    FROM story_contents_v2
    ${timeCondition}
    GROUP BY status
    UNION ALL
    SELECT
      'voice' as content_type,
      status,
      COUNT(*) as count
    FROM questionnaire_voices_v2
    ${timeCondition}
    GROUP BY status
  `).all();

  // 获取每日活动统计（最近7天）
  const dailyActivityResult = await c.env.DB.prepare(`
    SELECT
      DATE(created_at) as date,
      'user' as type,
      COUNT(*) as count
    FROM users_v2
    WHERE created_at >= datetime('now', '-7 days')
    GROUP BY DATE(created_at)
    UNION ALL
    SELECT
      DATE(created_at) as date,
      'story' as type,
      COUNT(*) as count
    FROM story_contents_v2
    WHERE created_at >= datetime('now', '-7 days')
    GROUP BY DATE(created_at)
    UNION ALL
    SELECT
      DATE(created_at) as date,
      'voice' as type,
      COUNT(*) as count
    FROM questionnaire_voices_v2
    WHERE created_at >= datetime('now', '-7 days')
    GROUP BY DATE(created_at)
    ORDER BY date DESC
  `).all();

  // 获取热门内容统计
  const popularContentResult = await c.env.DB.prepare(`
    SELECT
      'story' as content_type,
      id,
      title,
      likes,
      views,
      created_at
    FROM story_contents_v2
    WHERE status = 'approved'
    ORDER BY (likes * 2 + views) DESC
    LIMIT 10
    UNION ALL
    SELECT
      'voice' as content_type,
      id,
      title,
      likes,
      views,
      created_at
    FROM questionnaire_voices_v2
    WHERE status = 'approved'
    ORDER BY (likes * 2 + views) DESC
    LIMIT 10
  `).all();

  const detailedStats = {
    timeRange,
    userDistribution: userDistributionResult.results || [],
    contentStatus: contentStatusResult.results || [],
    dailyActivity: dailyActivityResult.results || [],
    popularContent: popularContentResult.results || [],
    generatedAt: new Date().toISOString()
  };

  return c.json(createSuccessResponse(detailedStats, 'Detailed statistics retrieved successfully'));
}));

// 测试数据状态API
adminRouter.get('/test-data/status', wrapAsyncHandler(async (c) => {
  // 获取最新记录的创建时间
  const latestRecordResult = await c.env.DB.prepare(`
    SELECT created_at
    FROM questionnaire_responses_v2
    ORDER BY created_at DESC
    LIMIT 1
  `).first();

  // 获取记录总数
  const countResult = await c.env.DB.prepare(
    'SELECT COUNT(*) as total FROM questionnaire_responses_v2'
  ).first();

  const status = {
    testDataEnabled: true,
    lastGenerated: latestRecordResult?.created_at || new Date().toISOString(),
    recordCount: countResult?.total || 0,
    status: 'active'
  };

  return c.json(createSuccessResponse(status, 'Test data status retrieved successfully'));
}));

// 内容审核API
adminRouter.get('/review/pending', wrapAsyncHandler(async (c) => {
  const type = c.req.query('type') || 'all'; // all, stories, voices

  let pendingContent = [];

  if (type === 'all' || type === 'stories') {
    // 获取待审核故事
    const pendingStoriesResult = await c.env.DB.prepare(`
      SELECT
        id,
        'story' as content_type,
        title,
        content,
        category,
        created_at,
        education_level_display,
        industry_display
      FROM story_contents_v2
      WHERE status = 'pending'
      ORDER BY created_at ASC
      LIMIT 20
    `).all();

    if (pendingStoriesResult.results) {
      pendingContent = pendingContent.concat(pendingStoriesResult.results);
    }
  }

  if (type === 'all' || type === 'voices') {
    // 获取待审核心声
    const pendingVoicesResult = await c.env.DB.prepare(`
      SELECT
        id,
        'voice' as content_type,
        title,
        content,
        voice_type as category,
        created_at,
        education_level_display,
        industry_display
      FROM questionnaire_voices_v2
      WHERE status = 'pending'
      ORDER BY created_at ASC
      LIMIT 20
    `).all();

    if (pendingVoicesResult.results) {
      pendingContent = pendingContent.concat(pendingVoicesResult.results);
    }
  }

  // 按创建时间排序
  pendingContent.sort((a, b) => new Date(a.created_at) - new Date(b.created_at));

  return c.json(createSuccessResponse(pendingContent, 'Pending content retrieved successfully'));
}));

// 内容审核操作API
adminRouter.post('/review/action', wrapAsyncHandler(async (c) => {
  const { contentId, contentType, action, reason } = await c.req.json();

  if (!contentId || !contentType || !action) {
    return c.json(createErrorResponse('Content ID, type, and action are required', 400), 400);
  }

  if (!['approve', 'reject'].includes(action)) {
    return c.json(createErrorResponse('Invalid action', 400), 400);
  }

  const status = action === 'approve' ? 'approved' : 'rejected';
  let tableName;

  switch (contentType) {
  case 'story':
    tableName = 'story_contents_v2';
    break;
  case 'voice':
    tableName = 'questionnaire_voices_v2';
    break;
  default:
    return c.json(createErrorResponse('Invalid content type', 400), 400);
  }

  // 更新内容状态
  await c.env.DB.prepare(`
    UPDATE ${tableName}
    SET status = ?, updated_at = ?
    WHERE id = ?
  `).bind(status, new Date().toISOString(), contentId).run();

  /*
   * 记录审核日志（如果有审核日志表的话）
   * TODO: 添加审核日志记录
   */

  const result = {
    contentId,
    contentType,
    action,
    status,
    reason,
    reviewedAt: new Date().toISOString()
  };

  return c.json(createSuccessResponse(result, `Content ${action}d successfully`));
}));

// 系统统计API
adminRouter.get('/statistics/overview', wrapAsyncHandler(async (c) => {
  // 获取各种统计数据
  const overviewResult = await c.env.DB.prepare(`
    SELECT
      (SELECT COUNT(*) FROM questionnaire_responses_v2) as total_responses,
      (SELECT COUNT(*) FROM story_contents_v2 WHERE status = 'approved') as approved_stories,
      (SELECT COUNT(*) FROM questionnaire_voices_v2 WHERE status = 'approved') as approved_voices,
      (SELECT COUNT(*) FROM users_v2) as total_users,
      (SELECT SUM(likes) FROM story_contents_v2) as total_likes,
      (SELECT SUM(views) FROM story_contents_v2) as total_views
  `).first();

  // 获取最近7天的活动统计
  const recentActivityResult = await c.env.DB.prepare(`
    SELECT
      DATE(created_at) as date,
      COUNT(*) as count
    FROM questionnaire_responses_v2
    WHERE created_at >= datetime('now', '-7 days')
    GROUP BY DATE(created_at)
    ORDER BY date DESC
  `).all();

  const overview = {
    totals: {
      responses: overviewResult?.total_responses || 0,
      approvedStories: overviewResult?.approved_stories || 0,
      approvedVoices: overviewResult?.approved_voices || 0,
      users: overviewResult?.total_users || 0,
      likes: overviewResult?.total_likes || 0,
      views: overviewResult?.total_views || 0
    },
    recentActivity: recentActivityResult.results?.map(item => ({
      date: item.date,
      count: item.count
    })) || [],
    lastUpdated: new Date().toISOString()
  };

  return c.json(createSuccessResponse(overview, 'Statistics overview retrieved successfully'));
}));

// 数据导出API
adminRouter.get('/export/:type', wrapAsyncHandler(async (c) => {
  const type = c.req.param('type');
  const format = c.req.query('format') || 'json';

  if (!['responses', 'stories', 'voices'].includes(type)) {
    return c.json(createErrorResponse('Invalid export type', 400), 400);
  }

  if (!['json', 'csv'].includes(format)) {
    return c.json(createErrorResponse('Invalid format', 400), 400);
  }

  /*
   * 这里应该实现实际的数据导出逻辑
   * 为了演示，返回一个简单的响应
   */
  const exportInfo = {
    type,
    format,
    status: 'preparing',
    estimatedTime: '2-5 minutes',
    downloadUrl: `/api/admin/download/${type}-${Date.now()}.${format}`,
    createdAt: new Date().toISOString()
  };

  return c.json(createSuccessResponse(exportInfo, 'Export initiated successfully'));
}));

export default adminRouter;
