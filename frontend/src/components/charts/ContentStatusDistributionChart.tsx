import React, { useState, useEffect } from 'react';
import { <PERSON><PERSON><PERSON>, Pie, Cell, ResponsiveContainer, <PERSON>, Toolt<PERSON>, <PERSON><PERSON>hart, Bar, XAxis, <PERSON>A<PERSON>s, CartesianGrid } from 'recharts';
import { Button } from '@/components/ui/button';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Loader2, RefreshCw } from 'lucide-react';

interface ContentStatusData {
  status: string;
  stories: number;
  voices: number;
  total: number;
  percentage: number;
}

interface ContentStatusDistributionChartProps {
  height?: number;
  showControls?: boolean;
  chartType?: 'pie' | 'bar';
}

const ContentStatusDistributionChart: React.FC<ContentStatusDistributionChartProps> = ({ 
  height = 300,
  showControls = true,
  chartType = 'pie'
}) => {
  const [data, setData] = useState<ContentStatusData[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [selectedChartType, setSelectedChartType] = useState<'pie' | 'bar'>(chartType);
  const [selectedContentType, setSelectedContentType] = useState<'all' | 'stories' | 'voices'>('all');

  // 颜色配置
  const COLORS = [
    '#82ca9d', // 绿色 - 已通过
    '#ffc658', // 黄色 - 待审核
    '#ff7c7c', // 红色 - 已拒绝
    '#8884d8', // 蓝色 - 草稿
  ];

  // 状态名称映射
  const statusNameMap: { [key: string]: string } = {
    'approved': '已通过',
    'pending': '待审核',
    'rejected': '已拒绝',
    'draft': '草稿'
  };

  // 获取内容状态分布数据
  const fetchContentStatusData = async () => {
    setLoading(true);
    setError(null);
    
    try {
      const response = await fetch('/api/admin/dashboard/content-status');
      
      if (!response.ok) {
        throw new Error('获取内容状态分布数据失败');
      }
      
      const result = await response.json();
      
      if (result.success) {
        // 转换数据格式
        const formattedData = result.data.map((item: any) => ({
          status: statusNameMap[item.status] || item.status,
          stories: item.stories || 0,
          voices: item.voices || 0,
          total: (item.stories || 0) + (item.voices || 0),
          percentage: item.percentage || 0
        }));
        setData(formattedData);
      } else {
        throw new Error(result.message || '数据格式错误');
      }
    } catch (err) {
      console.error('内容状态分布数据获取错误:', err);
      setError(err instanceof Error ? err.message : '未知错误');
      
      // 使用模拟数据作为降级
      generateMockData();
    } finally {
      setLoading(false);
    }
  };

  // 生成模拟数据
  const generateMockData = () => {
    const mockData: ContentStatusData[] = [
      { status: '已通过', stories: 145, voices: 89, total: 234, percentage: 68.4 },
      { status: '待审核', stories: 23, voices: 15, total: 38, percentage: 11.1 },
      { status: '已拒绝', stories: 12, voices: 8, total: 20, percentage: 5.8 },
      { status: '草稿', stories: 35, voices: 15, total: 50, percentage: 14.6 }
    ];
    
    setData(mockData);
  };

  // 初始化数据
  useEffect(() => {
    fetchContentStatusData();
  }, []);

  // 获取显示数据
  const getDisplayData = () => {
    return data.map(item => ({
      ...item,
      value: selectedContentType === 'stories' ? item.stories :
             selectedContentType === 'voices' ? item.voices :
             item.total
    }));
  };

  // 自定义Tooltip
  const CustomTooltip = ({ active, payload }: any) => {
    if (active && payload && payload.length) {
      const data = payload[0].payload;
      return (
        <div className="bg-white p-3 border rounded-lg shadow-lg">
          <p className="font-medium mb-1">{data.status}</p>
          <p className="text-sm text-gray-600">故事: {data.stories}</p>
          <p className="text-sm text-gray-600">心声: {data.voices}</p>
          <p className="text-sm text-gray-600">总计: {data.total}</p>
          <p className="text-sm text-gray-600">占比: {data.percentage.toFixed(1)}%</p>
        </div>
      );
    }
    return null;
  };

  // 自定义标签
  const renderCustomLabel = ({ cx, cy, midAngle, innerRadius, outerRadius, percent }: any) => {
    if (percent < 0.05) return null; // 小于5%不显示标签
    
    const RADIAN = Math.PI / 180;
    const radius = innerRadius + (outerRadius - innerRadius) * 0.5;
    const x = cx + radius * Math.cos(-midAngle * RADIAN);
    const y = cy + radius * Math.sin(-midAngle * RADIAN);

    return (
      <text 
        x={x} 
        y={y} 
        fill="white" 
        textAnchor={x > cx ? 'start' : 'end'} 
        dominantBaseline="central"
        fontSize={12}
        fontWeight="bold"
      >
        {`${(percent * 100).toFixed(0)}%`}
      </text>
    );
  };

  // 处理刷新
  const handleRefresh = () => {
    fetchContentStatusData();
  };

  // 渲染加载状态
  if (loading) {
    return (
      <div className="flex items-center justify-center" style={{ height: `${height}px` }}>
        <Loader2 className="h-8 w-8 animate-spin text-muted-foreground" />
        <span className="ml-2 text-muted-foreground">加载内容状态分布数据...</span>
      </div>
    );
  }

  // 渲染错误状态
  if (error) {
    return (
      <div className="flex flex-col items-center justify-center" style={{ height: `${height}px` }}>
        <p className="text-red-500 mb-2">加载失败: {error}</p>
        <Button variant="outline" size="sm" onClick={handleRefresh}>
          <RefreshCw className="h-4 w-4 mr-2" />
          重试
        </Button>
      </div>
    );
  }

  const displayData = getDisplayData();

  return (
    <div className="w-full">
      {showControls && (
        <div className="flex justify-between items-center mb-4">
          <div className="flex space-x-2">
            <Select value={selectedChartType} onValueChange={(value: any) => setSelectedChartType(value)}>
              <SelectTrigger className="w-24">
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="pie">饼图</SelectItem>
                <SelectItem value="bar">柱图</SelectItem>
              </SelectContent>
            </Select>
            
            <Select value={selectedContentType} onValueChange={(value: any) => setSelectedContentType(value)}>
              <SelectTrigger className="w-28">
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">全部内容</SelectItem>
                <SelectItem value="stories">故事墙</SelectItem>
                <SelectItem value="voices">问卷心声</SelectItem>
              </SelectContent>
            </Select>
          </div>
          
          <Button variant="outline" size="sm" onClick={handleRefresh}>
            <RefreshCw className="h-4 w-4 mr-2" />
            刷新
          </Button>
        </div>
      )}
      
      <div style={{ height: `${height}px` }}>
        <ResponsiveContainer width="100%" height="100%">
          {selectedChartType === 'pie' ? (
            <PieChart>
              <Pie
                data={displayData}
                cx="50%"
                cy="50%"
                labelLine={false}
                label={renderCustomLabel}
                outerRadius={Math.min(height * 0.35, 120)}
                fill="#8884d8"
                dataKey="value"
              >
                {displayData.map((entry, index) => (
                  <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                ))}
              </Pie>
              <Tooltip content={<CustomTooltip />} />
              <Legend 
                verticalAlign="bottom" 
                height={36}
                formatter={(value, entry: any) => (
                  <span style={{ color: entry.color }}>
                    {value} ({entry.payload.value})
                  </span>
                )}
              />
            </PieChart>
          ) : (
            <BarChart data={displayData} margin={{ top: 20, right: 30, left: 20, bottom: 5 }}>
              <CartesianGrid strokeDasharray="3 3" />
              <XAxis dataKey="status" tick={{ fontSize: 12 }} />
              <YAxis tick={{ fontSize: 12 }} />
              <Tooltip content={<CustomTooltip />} />
              <Bar dataKey="value" fill="#8884d8">
                {displayData.map((entry, index) => (
                  <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                ))}
              </Bar>
            </BarChart>
          )}
        </ResponsiveContainer>
      </div>
      
      {data.length === 0 && (
        <div className="text-center text-muted-foreground mt-4">
          暂无内容状态分布数据
        </div>
      )}
    </div>
  );
};

export default ContentStatusDistributionChart;
