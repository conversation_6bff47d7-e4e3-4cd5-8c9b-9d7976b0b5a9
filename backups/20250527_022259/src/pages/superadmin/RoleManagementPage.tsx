import React, { useState, useEffect } from 'react';
import SuperAdminLayout from '@/components/layouts/SuperAdminLayout';
import { Card, CardContent, CardHeader, CardTitle, CardDescription, CardFooter } from '@/components/ui/card';
import { But<PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Checkbox } from '@/components/ui/checkbox';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { useToast } from '@/components/ui/use-toast';
import {
  Shield,
  Lock,
  Users,
  Eye,
  Settings,
  Database,
  FileText,
  Search,
  Plus,
  Edit,
  Trash2,
  RefreshCw,
  AlertTriangle,
  CheckCircle,
  XCircle,
  Info
} from 'lucide-react';
import { getRoles, updateRole, createRole, deleteRole } from '@/services/roleManagementService';
import { Role, Permission, PermissionGroup } from '@/types/role';
import PermissionEditor from '@/components/superadmin/PermissionEditor';

/**
 * 角色管理页面
 *
 * 管理系统角色和权限
 */
const RoleManagementPage: React.FC = () => {
  const { toast } = useToast();
  const [isLoading, setIsLoading] = useState(false);
  const [roles, setRoles] = useState<Role[]>([]);
  const [selectedRole, setSelectedRole] = useState<Role | null>(null);
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false);
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);
  const [isCreateDialogOpen, setIsCreateDialogOpen] = useState(false);
  const [isPermissionEditorOpen, setIsPermissionEditorOpen] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');

  // 新角色/编辑角色的状态
  const [editedRole, setEditedRole] = useState<{
    id?: string;
    name: string;
    description: string;
    permissions: string[];
  }>({
    name: '',
    description: '',
    permissions: []
  });

  // 权限分组
  const permissionGroups: PermissionGroup[] = [
    {
      id: 'dashboard',
      name: '仪表盘',
      permissions: [
        { id: 'DASHBOARD_PERSONAL', name: '个人仪表盘', description: '查看个人仪表盘', level: 'basic' },
        { id: 'DASHBOARD_SYSTEM', name: '系统概览', description: '查看系统概览', level: 'advanced' },
        { id: 'DASHBOARD_SECURITY', name: '安全监控', description: '查看安全监控', level: 'advanced' },
        { id: 'DASHBOARD_ANALYTICS', name: '数据分析', description: '查看数据分析仪表盘', level: 'advanced' }
      ]
    },
    {
      id: 'content',
      name: '内容管理',
      permissions: [
        { id: 'CONTENT_VIEW', name: '内容查看', description: '查看所有内容', level: 'basic' },
        { id: 'CONTENT_REVIEW', name: '内容审核', description: '审核用户提交的内容', level: 'intermediate' },
        { id: 'STORY_REVIEW', name: '故事审核', description: '审核用户提交的故事', level: 'intermediate' },
        { id: 'QUICK_REVIEW', name: '快速审核', description: '使用快速审核功能', level: 'intermediate' },
        { id: 'TAG_MANAGEMENT', name: '标签管理', description: '管理内容标签', level: 'advanced' },
        { id: 'CONTENT_DELETE', name: '内容删除', description: '删除内容', level: 'advanced' },
        { id: 'CONTENT_BULK_OPERATIONS', name: '批量操作', description: '批量处理内容', level: 'advanced' }
      ]
    },
    {
      id: 'data',
      name: '数据管理',
      permissions: [
        { id: 'QUESTIONNAIRE_VIEW', name: '问卷查看', description: '查看问卷回复', level: 'basic' },
        { id: 'QUESTIONNAIRE_EDIT', name: '问卷编辑', description: '编辑问卷回复', level: 'intermediate' },
        { id: 'DATA_ANALYSIS', name: '数据分析', description: '查看数据分析', level: 'intermediate' },
        { id: 'DATA_EXPORT', name: '数据导出', description: '导出数据', level: 'intermediate' },
        { id: 'DATA_IMPORT', name: '数据导入', description: '导入数据', level: 'advanced' },
        { id: 'DATA_BACKUP', name: '数据备份', description: '创建和管理数据备份', level: 'advanced' },
        { id: 'DATA_RESTORE', name: '数据恢复', description: '恢复数据', level: 'critical' }
      ]
    },
    {
      id: 'user',
      name: '用户管理',
      permissions: [
        { id: 'USER_VIEW', name: '用户查看', description: '查看用户列表', level: 'basic' },
        { id: 'USER_CREATE', name: '用户创建', description: '创建新用户', level: 'intermediate' },
        { id: 'USER_EDIT', name: '用户编辑', description: '编辑用户信息', level: 'intermediate' },
        { id: 'USER_DELETE', name: '用户删除', description: '删除用户', level: 'advanced' },
        { id: 'USER_MANAGEMENT', name: '用户管理', description: '完整的用户管理权限', level: 'advanced' },
        { id: 'REVIEWER_MANAGEMENT', name: '审核员管理', description: '管理审核员', level: 'advanced' },
        { id: 'ADMIN_MANAGEMENT', name: '管理员管理', description: '管理管理员', level: 'critical' },
        { id: 'ROLE_MANAGEMENT', name: '角色管理', description: '管理角色和权限', level: 'critical' }
      ]
    },
    {
      id: 'system',
      name: '系统设置',
      permissions: [
        { id: 'SETTINGS_PERSONAL', name: '个人设置', description: '修改个人设置', level: 'basic' },
        { id: 'DEIDENTIFICATION', name: '内容脱敏', description: '管理内容脱敏设置', level: 'intermediate' },
        { id: 'SECURITY_SETTINGS', name: '安全设置', description: '管理安全设置', level: 'advanced' },
        { id: 'SYSTEM_CONFIG', name: '系统配置', description: '管理系统配置', level: 'critical' },
        { id: 'SYSTEM_MAINTENANCE', name: '系统维护', description: '执行系统维护操作', level: 'critical' }
      ]
    },
    {
      id: 'security',
      name: '安全与审计',
      permissions: [
        { id: 'SECURITY_MONITOR', name: '安全监控', description: '查看安全监控', level: 'intermediate' },
        { id: 'SECURITY_LOGS', name: '安全日志', description: '查看安全日志', level: 'intermediate' },
        { id: 'ADMIN_AUDIT', name: '管理员审计', description: '查看管理员审计', level: 'advanced' },
        { id: 'LOGIN_RECORDS', name: '登录记录', description: '查看登录记录', level: 'intermediate' },
        { id: 'SECURITY_INCIDENT', name: '安全事件处理', description: '处理安全事件', level: 'advanced' },
        { id: 'SECURITY_CONFIG', name: '安全配置', description: '配置安全策略', level: 'critical' }
      ]
    }
  ];

  // 权限预设模板
  const roleTemplates = [
    {
      id: 'basic_user',
      name: '基础用户',
      description: '只能查看基本信息',
      permissions: ['DASHBOARD_PERSONAL', 'SETTINGS_PERSONAL']
    },
    {
      id: 'content_reviewer',
      name: '内容审核员',
      description: '专门负责内容审核',
      permissions: [
        'DASHBOARD_PERSONAL', 'CONTENT_VIEW', 'CONTENT_REVIEW',
        'STORY_REVIEW', 'QUICK_REVIEW', 'SETTINGS_PERSONAL'
      ]
    },
    {
      id: 'data_analyst',
      name: '数据分析师',
      description: '负责数据分析和报告',
      permissions: [
        'DASHBOARD_PERSONAL', 'DASHBOARD_ANALYTICS', 'QUESTIONNAIRE_VIEW',
        'DATA_ANALYSIS', 'DATA_EXPORT', 'SETTINGS_PERSONAL'
      ]
    },
    {
      id: 'user_manager',
      name: '用户管理员',
      description: '管理普通用户',
      permissions: [
        'DASHBOARD_PERSONAL', 'DASHBOARD_SYSTEM', 'USER_VIEW', 'USER_CREATE',
        'USER_EDIT', 'USER_MANAGEMENT', 'SETTINGS_PERSONAL'
      ]
    },
    {
      id: 'system_admin',
      name: '系统管理员',
      description: '完整的系统管理权限',
      permissions: [
        'DASHBOARD_PERSONAL', 'DASHBOARD_SYSTEM', 'DASHBOARD_SECURITY',
        'CONTENT_VIEW', 'CONTENT_REVIEW', 'USER_VIEW', 'USER_MANAGEMENT',
        'SECURITY_MONITOR', 'SECURITY_LOGS', 'SYSTEM_CONFIG', 'SETTINGS_PERSONAL'
      ]
    }
  ];

  // 权限依赖关系
  const permissionDependencies: Record<string, string[]> = {
    'USER_EDIT': ['USER_VIEW'],
    'USER_DELETE': ['USER_VIEW', 'USER_EDIT'],
    'USER_MANAGEMENT': ['USER_VIEW', 'USER_CREATE', 'USER_EDIT'],
    'CONTENT_REVIEW': ['CONTENT_VIEW'],
    'QUICK_REVIEW': ['CONTENT_REVIEW'],
    'DATA_EXPORT': ['DATA_ANALYSIS'],
    'DATA_IMPORT': ['DATA_ANALYSIS'],
    'DATA_RESTORE': ['DATA_BACKUP'],
    'ROLE_MANAGEMENT': ['USER_VIEW'],
    'ADMIN_MANAGEMENT': ['USER_MANAGEMENT'],
    'SYSTEM_MAINTENANCE': ['SYSTEM_CONFIG']
  };

  // 获取所有角色
  const fetchRoles = async () => {
    try {
      setIsLoading(true);

      // 调用真实API
      const response = await fetch('/api/admin/roles');
      if (!response.ok) {
        throw new Error('API调用失败');
      }

      const data = await response.json();
      if (data.success) {
        setRoles(data.data || []);
      } else {
        throw new Error(data.message || '获取角色失败');
      }
    } catch (error) {
      console.error('获取角色失败:', error);
      toast({
        variant: 'destructive',
        title: '获取角色失败',
        description: error instanceof Error ? error.message : '服务器错误，请稍后再试'
      });
    } finally {
      setIsLoading(false);
    }
  };

  // 组件加载时获取角色
  useEffect(() => {
    fetchRoles();
  }, []);

  // 处理角色编辑
  const handleEditRole = async () => {
    try {
      setIsLoading(true);

      if (!selectedRole) return;

      const response = await updateRole(selectedRole.id, {
        name: editedRole.name,
        description: editedRole.description,
        permissions: editedRole.permissions
      });

      if (response.success) {
        toast({
          title: '角色更新成功',
          description: `角色 "${editedRole.name}" 已成功更新`
        });

        // 刷新角色列表
        fetchRoles();

        // 关闭对话框
        setIsEditDialogOpen(false);
      } else {
        toast({
          variant: 'destructive',
          title: '角色更新失败',
          description: response.message || '无法更新角色'
        });
      }
    } catch (error) {
      console.error('角色更新失败:', error);
      toast({
        variant: 'destructive',
        title: '角色更新失败',
        description: '服务器错误，请稍后再试'
      });
    } finally {
      setIsLoading(false);
    }
  };

  // 处理角色创建
  const handleCreateRole = async () => {
    try {
      setIsLoading(true);

      const response = await createRole({
        name: editedRole.name,
        description: editedRole.description,
        permissions: editedRole.permissions
      });

      if (response.success) {
        toast({
          title: '角色创建成功',
          description: `角色 "${editedRole.name}" 已成功创建`
        });

        // 刷新角色列表
        fetchRoles();

        // 关闭对话框
        setIsCreateDialogOpen(false);
      } else {
        toast({
          variant: 'destructive',
          title: '角色创建失败',
          description: response.message || '无法创建角色'
        });
      }
    } catch (error) {
      console.error('角色创建失败:', error);
      toast({
        variant: 'destructive',
        title: '角色创建失败',
        description: '服务器错误，请稍后再试'
      });
    } finally {
      setIsLoading(false);
    }
  };

  // 处理角色删除
  const handleDeleteRole = async () => {
    try {
      setIsLoading(true);

      if (!selectedRole) return;

      const response = await deleteRole(selectedRole.id);

      if (response.success) {
        toast({
          title: '角色删除成功',
          description: `角色 "${selectedRole.name}" 已成功删除`
        });

        // 刷新角色列表
        fetchRoles();

        // 关闭对话框
        setIsDeleteDialogOpen(false);
      } else {
        toast({
          variant: 'destructive',
          title: '角色删除失败',
          description: response.message || '无法删除角色'
        });
      }
    } catch (error) {
      console.error('角色删除失败:', error);
      toast({
        variant: 'destructive',
        title: '角色删除失败',
        description: '服务器错误，请稍后再试'
      });
    } finally {
      setIsLoading(false);
    }
  };

  // 打开编辑对话框
  const openEditDialog = (role: Role) => {
    setSelectedRole(role);
    setEditedRole({
      id: role.id,
      name: role.name,
      description: role.description,
      permissions: role.permissions.map(p => p.id)
    });
    setIsEditDialogOpen(true);
  };

  // 打开创建对话框
  const openCreateDialog = () => {
    setEditedRole({
      name: '',
      description: '',
      permissions: []
    });
    setIsCreateDialogOpen(true);
  };

  // 打开删除对话框
  const openDeleteDialog = (role: Role) => {
    setSelectedRole(role);
    setIsDeleteDialogOpen(true);
  };

  // 打开权限编辑器
  const openPermissionEditor = (role: Role) => {
    setSelectedRole(role);
    setEditedRole({
      id: role.id,
      name: role.name,
      description: role.description,
      permissions: role.permissions.map(p => p.id)
    });
    setIsPermissionEditorOpen(true);
  };

  // 保存权限编辑
  const handleSavePermissions = async (permissions: string[]) => {
    try {
      setIsLoading(true);

      if (!selectedRole) return;

      const response = await updateRole(selectedRole.id, {
        name: selectedRole.name,
        description: selectedRole.description,
        permissions
      });

      if (response.success) {
        toast({
          title: '权限更新成功',
          description: `角色 "${selectedRole.name}" 的权限已成功更新`
        });

        // 刷新角色列表
        fetchRoles();

        // 关闭权限编辑器
        setIsPermissionEditorOpen(false);
      } else {
        toast({
          variant: 'destructive',
          title: '权限更新失败',
          description: response.message || '无法更新权限'
        });
      }
    } catch (error) {
      console.error('权限更新失败:', error);
      toast({
        variant: 'destructive',
        title: '权限更新失败',
        description: '服务器错误，请稍后再试'
      });
    } finally {
      setIsLoading(false);
    }
  };

  // 检查权限依赖
  const checkPermissionDependencies = (permissionId: string, currentPermissions: string[]): string[] => {
    const dependencies = permissionDependencies[permissionId] || [];
    const missingDependencies = dependencies.filter(dep => !currentPermissions.includes(dep));
    return missingDependencies;
  };

  // 获取权限级别颜色
  const getPermissionLevelColor = (level: string) => {
    switch (level) {
      case 'basic':
        return 'text-green-600';
      case 'intermediate':
        return 'text-blue-600';
      case 'advanced':
        return 'text-orange-600';
      case 'critical':
        return 'text-red-600';
      default:
        return 'text-gray-600';
    }
  };

  // 获取权限级别标签
  const getPermissionLevelBadge = (level: string) => {
    const colors = {
      basic: 'bg-green-100 text-green-800',
      intermediate: 'bg-blue-100 text-blue-800',
      advanced: 'bg-orange-100 text-orange-800',
      critical: 'bg-red-100 text-red-800'
    };

    const labels = {
      basic: '基础',
      intermediate: '中级',
      advanced: '高级',
      critical: '关键'
    };

    return (
      <span className={`px-1.5 py-0.5 text-xs rounded ${colors[level as keyof typeof colors] || 'bg-gray-100 text-gray-800'}`}>
        {labels[level as keyof typeof labels] || level}
      </span>
    );
  };

  // 应用角色模板
  const applyRoleTemplate = (templateId: string) => {
    const template = roleTemplates.find(t => t.id === templateId);
    if (template) {
      setEditedRole(prev => ({
        ...prev,
        name: prev.name || template.name,
        description: prev.description || template.description,
        permissions: template.permissions
      }));

      toast({
        title: '模板已应用',
        description: `已应用 "${template.name}" 模板`,
      });
    }
  };

  // 处理权限切换（增强版，包含依赖检查）
  const handlePermissionToggle = (permissionId: string) => {
    setEditedRole(prev => {
      const permissions = [...prev.permissions];

      if (permissions.includes(permissionId)) {
        // 移除权限时，检查是否有其他权限依赖于此权限
        const dependentPermissions = Object.entries(permissionDependencies)
          .filter(([_, deps]) => deps.includes(permissionId))
          .map(([perm, _]) => perm)
          .filter(perm => permissions.includes(perm));

        if (dependentPermissions.length > 0) {
          const dependentNames = dependentPermissions.map(perm => {
            const permission = permissionGroups
              .flatMap(g => g.permissions)
              .find(p => p.id === perm);
            return permission?.name || perm;
          });

          toast({
            title: '无法移除权限',
            description: `以下权限依赖于此权限：${dependentNames.join(', ')}`,
            variant: 'destructive',
          });
          return prev;
        }

        return {
          ...prev,
          permissions: permissions.filter(id => id !== permissionId)
        };
      } else {
        // 添加权限时，自动添加依赖权限
        const missingDependencies = checkPermissionDependencies(permissionId, permissions);
        const newPermissions = [...permissions, permissionId, ...missingDependencies];

        if (missingDependencies.length > 0) {
          const dependencyNames = missingDependencies.map(dep => {
            const permission = permissionGroups
              .flatMap(g => g.permissions)
              .find(p => p.id === dep);
            return permission?.name || dep;
          });

          toast({
            title: '自动添加依赖权限',
            description: `已自动添加依赖权限：${dependencyNames.join(', ')}`,
          });
        }

        return {
          ...prev,
          permissions: [...new Set(newPermissions)] // 去重
        };
      }
    });
  };

  // 处理权限组切换
  const handlePermissionGroupToggle = (groupId: string) => {
    const group = permissionGroups.find(g => g.id === groupId);

    if (!group) return;

    const groupPermissionIds = group.permissions.map(p => p.id);

    setEditedRole(prev => {
      // 检查组内所有权限是否都已选中
      const allSelected = groupPermissionIds.every(id => prev.permissions.includes(id));

      if (allSelected) {
        // 如果全部选中，则取消选中所有
        return {
          ...prev,
          permissions: prev.permissions.filter(id => !groupPermissionIds.includes(id))
        };
      } else {
        // 否则，选中所有未选中的
        const newPermissions = [...prev.permissions];

        groupPermissionIds.forEach(id => {
          if (!newPermissions.includes(id)) {
            newPermissions.push(id);
          }
        });

        return {
          ...prev,
          permissions: newPermissions
        };
      }
    });
  };

  // 过滤角色
  const filteredRoles = (roles || []).filter(role =>
    role.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
    role.description.toLowerCase().includes(searchQuery.toLowerCase())
  );

  return (
    <SuperAdminLayout>
      <div className="container mx-auto py-6">
        <div className="flex justify-between items-center mb-6">
          <h1 className="text-2xl font-bold flex items-center">
            <Lock className="h-6 w-6 mr-2" />
            角色权限管理
          </h1>
          <div className="flex items-center space-x-2">
            <Button
              variant="outline"
              onClick={fetchRoles}
              disabled={isLoading}
            >
              <RefreshCw className={`h-4 w-4 mr-2 ${isLoading ? 'animate-spin' : ''}`} />
              刷新
            </Button>
            <Button onClick={openCreateDialog}>
              <Plus className="h-4 w-4 mr-2" />
              创建角色
            </Button>
          </div>
        </div>

        <Card className="mb-6">
          <CardHeader>
            <div className="flex justify-between items-center">
              <div>
                <CardTitle>角色列表</CardTitle>
                <CardDescription>管理系统角色和权限</CardDescription>
              </div>
              <div className="flex items-center w-[300px]">
                <Search className="h-4 w-4 mr-2 text-muted-foreground" />
                <Input
                  placeholder="搜索角色..."
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  className="w-full"
                />
              </div>
            </div>
          </CardHeader>
          <CardContent>
            <div className="rounded-md border">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>角色名称</TableHead>
                    <TableHead>描述</TableHead>
                    <TableHead>权限数量</TableHead>
                    <TableHead>用户数量</TableHead>
                    <TableHead className="text-right">操作</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {isLoading ? (
                    <TableRow>
                      <TableCell colSpan={5} className="text-center py-4">
                        <div className="flex justify-center items-center">
                          <RefreshCw className="h-5 w-5 animate-spin mr-2" />
                          加载中...
                        </div>
                      </TableCell>
                    </TableRow>
                  ) : filteredRoles.length === 0 ? (
                    <TableRow>
                      <TableCell colSpan={5} className="text-center py-4">
                        未找到角色
                      </TableCell>
                    </TableRow>
                  ) : (
                    filteredRoles.map((role) => (
                      <TableRow key={role.id}>
                        <TableCell className="font-medium">
                          <div className="flex items-center">
                            <Shield className="h-4 w-4 mr-2 text-primary" />
                            {role.name}
                          </div>
                        </TableCell>
                        <TableCell>{role.description}</TableCell>
                        <TableCell>{role.permissions.length}</TableCell>
                        <TableCell>{role.userCount || 0}</TableCell>
                        <TableCell className="text-right">
                          <div className="flex justify-end space-x-2">
                            <Button
                              variant="ghost"
                              size="icon"
                              onClick={() => openEditDialog(role)}
                              disabled={role.isSystem}
                              title={role.isSystem ? "系统角色不可编辑" : "编辑角色"}
                            >
                              <Edit className="h-4 w-4" />
                            </Button>
                            <Button
                              variant="ghost"
                              size="icon"
                              onClick={() => openPermissionEditor(role)}
                              title="编辑权限"
                              className="text-blue-600"
                            >
                              <Lock className="h-4 w-4" />
                            </Button>
                            <Button
                              variant="ghost"
                              size="icon"
                              onClick={() => openDeleteDialog(role)}
                              disabled={role.isSystem}
                              title={role.isSystem ? "系统角色不可删除" : "删除角色"}
                              className="text-destructive"
                            >
                              <Trash2 className="h-4 w-4" />
                            </Button>
                          </div>
                        </TableCell>
                      </TableRow>
                    ))
                  )}
                </TableBody>
              </Table>
            </div>
          </CardContent>
        </Card>

        {/* 编辑角色对话框 */}
        <Dialog open={isEditDialogOpen} onOpenChange={setIsEditDialogOpen}>
          <DialogContent className="max-w-3xl">
            <DialogHeader>
              <DialogTitle className="flex items-center">
                <Edit className="h-5 w-5 mr-2" />
                编辑角色
              </DialogTitle>
              <DialogDescription>
                修改角色信息和权限设置
              </DialogDescription>
            </DialogHeader>

            <div className="grid grid-cols-1 gap-4">
              <div className="space-y-4">
                <div>
                  <label className="text-sm font-medium">角色名称</label>
                  <Input
                    value={editedRole.name}
                    onChange={(e) => setEditedRole({ ...editedRole, name: e.target.value })}
                    placeholder="输入角色名称"
                  />
                </div>

                <div>
                  <label className="text-sm font-medium">角色描述</label>
                  <Input
                    value={editedRole.description}
                    onChange={(e) => setEditedRole({ ...editedRole, description: e.target.value })}
                    placeholder="输入角色描述"
                  />
                </div>
              </div>

              <div>
                <div className="flex justify-between items-center mb-2">
                  <label className="text-sm font-medium">权限设置</label>
                  <div className="flex items-center space-x-2">
                    <span className="text-xs text-muted-foreground">快速模板:</span>
                    <select
                      className="text-xs border rounded px-2 py-1"
                      onChange={(e) => e.target.value && applyRoleTemplate(e.target.value)}
                      value=""
                    >
                      <option value="">选择模板</option>
                      {roleTemplates.map(template => (
                        <option key={template.id} value={template.id}>
                          {template.name}
                        </option>
                      ))}
                    </select>
                  </div>
                </div>

                <div className="border rounded-md p-4 mt-2 max-h-[400px] overflow-y-auto">
                  {permissionGroups.map((group) => (
                    <div key={group.id} className="mb-4">
                      <div className="flex items-center space-x-2 mb-2">
                        <Checkbox
                          id={`group-${group.id}`}
                          checked={group.permissions.every(p => editedRole.permissions.includes(p.id))}
                          onCheckedChange={() => handlePermissionGroupToggle(group.id)}
                        />
                        <label
                          htmlFor={`group-${group.id}`}
                          className="text-sm font-medium cursor-pointer"
                        >
                          {group.name}
                        </label>
                        <span className="text-xs text-muted-foreground">
                          ({group.permissions.filter(p => editedRole.permissions.includes(p.id)).length}/{group.permissions.length})
                        </span>
                      </div>

                      <div className="ml-6 space-y-2">
                        {group.permissions.map((permission) => (
                          <div key={permission.id} className="flex items-center justify-between p-2 border rounded-sm hover:bg-gray-50">
                            <div className="flex items-center space-x-2">
                              <Checkbox
                                id={`permission-${permission.id}`}
                                checked={editedRole.permissions.includes(permission.id)}
                                onCheckedChange={() => handlePermissionToggle(permission.id)}
                              />
                              <div className="flex flex-col">
                                <label
                                  htmlFor={`permission-${permission.id}`}
                                  className="text-sm cursor-pointer font-medium"
                                >
                                  {permission.name}
                                </label>
                                <span className="text-xs text-muted-foreground">
                                  {permission.description}
                                </span>
                              </div>
                            </div>
                            <div className="flex items-center space-x-2">
                              {getPermissionLevelBadge(permission.level)}
                              {permissionDependencies[permission.id] && (
                                <span className="text-xs text-blue-600" title="此权限有依赖关系">
                                  <Info className="h-3 w-3" />
                                </span>
                              )}
                            </div>
                          </div>
                        ))}
                      </div>
                    </div>
                  ))}
                </div>

                <div className="mt-3 p-3 bg-blue-50 rounded-md">
                  <div className="text-xs text-blue-800">
                    <strong>权限级别说明：</strong>
                    <div className="mt-1 space-x-3">
                      <span className="inline-flex items-center">
                        {getPermissionLevelBadge('basic')} 基础权限
                      </span>
                      <span className="inline-flex items-center">
                        {getPermissionLevelBadge('intermediate')} 中级权限
                      </span>
                      <span className="inline-flex items-center">
                        {getPermissionLevelBadge('advanced')} 高级权限
                      </span>
                      <span className="inline-flex items-center">
                        {getPermissionLevelBadge('critical')} 关键权限
                      </span>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            <DialogFooter>
              <Button variant="outline" onClick={() => setIsEditDialogOpen(false)}>
                取消
              </Button>
              <Button onClick={handleEditRole} disabled={isLoading}>
                {isLoading ? (
                  <>
                    <RefreshCw className="h-4 w-4 mr-2 animate-spin" />
                    保存中...
                  </>
                ) : (
                  <>
                    <CheckCircle className="h-4 w-4 mr-2" />
                    保存
                  </>
                )}
              </Button>
            </DialogFooter>
          </DialogContent>
        </Dialog>

        {/* 创建角色对话框 */}
        <Dialog open={isCreateDialogOpen} onOpenChange={setIsCreateDialogOpen}>
          <DialogContent className="max-w-3xl">
            <DialogHeader>
              <DialogTitle className="flex items-center">
                <Plus className="h-5 w-5 mr-2" />
                创建角色
              </DialogTitle>
              <DialogDescription>
                创建新角色并设置权限
              </DialogDescription>
            </DialogHeader>

            <div className="grid grid-cols-1 gap-4">
              <div className="space-y-4">
                <div>
                  <label className="text-sm font-medium">角色名称</label>
                  <Input
                    value={editedRole.name}
                    onChange={(e) => setEditedRole({ ...editedRole, name: e.target.value })}
                    placeholder="输入角色名称"
                  />
                </div>

                <div>
                  <label className="text-sm font-medium">角色描述</label>
                  <Input
                    value={editedRole.description}
                    onChange={(e) => setEditedRole({ ...editedRole, description: e.target.value })}
                    placeholder="输入角色描述"
                  />
                </div>
              </div>

              <div>
                <label className="text-sm font-medium">权限设置</label>
                <div className="border rounded-md p-4 mt-2 max-h-[400px] overflow-y-auto">
                  {permissionGroups.map((group) => (
                    <div key={group.id} className="mb-4">
                      <div className="flex items-center space-x-2 mb-2">
                        <Checkbox
                          id={`new-group-${group.id}`}
                          checked={group.permissions.every(p => editedRole.permissions.includes(p.id))}
                          onCheckedChange={() => handlePermissionGroupToggle(group.id)}
                        />
                        <label
                          htmlFor={`new-group-${group.id}`}
                          className="text-sm font-medium cursor-pointer"
                        >
                          {group.name}
                        </label>
                      </div>

                      <div className="ml-6 grid grid-cols-1 md:grid-cols-2 gap-2">
                        {group.permissions.map((permission) => (
                          <div key={permission.id} className="flex items-center space-x-2">
                            <Checkbox
                              id={`new-permission-${permission.id}`}
                              checked={editedRole.permissions.includes(permission.id)}
                              onCheckedChange={() => handlePermissionToggle(permission.id)}
                            />
                            <label
                              htmlFor={`new-permission-${permission.id}`}
                              className="text-sm cursor-pointer flex items-center"
                              title={permission.description}
                            >
                              {permission.name}
                              <Info className="h-3 w-3 ml-1 text-muted-foreground" />
                            </label>
                          </div>
                        ))}
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            </div>

            <DialogFooter>
              <Button variant="outline" onClick={() => setIsCreateDialogOpen(false)}>
                取消
              </Button>
              <Button onClick={handleCreateRole} disabled={isLoading}>
                {isLoading ? (
                  <>
                    <RefreshCw className="h-4 w-4 mr-2 animate-spin" />
                    创建中...
                  </>
                ) : (
                  <>
                    <Plus className="h-4 w-4 mr-2" />
                    创建
                  </>
                )}
              </Button>
            </DialogFooter>
          </DialogContent>
        </Dialog>

        {/* 删除角色对话框 */}
        <Dialog open={isDeleteDialogOpen} onOpenChange={setIsDeleteDialogOpen}>
          <DialogContent>
            <DialogHeader>
              <DialogTitle className="flex items-center text-destructive">
                <AlertTriangle className="h-5 w-5 mr-2" />
                删除角色
              </DialogTitle>
              <DialogDescription>
                确定要删除角色 "{selectedRole?.name}" 吗？此操作不可撤销。
              </DialogDescription>
            </DialogHeader>

            <div className="bg-destructive/10 border border-destructive/30 rounded-md p-4">
              <p className="text-sm">
                删除角色将会影响所有使用该角色的用户。这些用户将会失去与该角色相关的所有权限，直到为他们分配新的角色。
              </p>
            </div>

            <DialogFooter>
              <Button variant="outline" onClick={() => setIsDeleteDialogOpen(false)}>
                取消
              </Button>
              <Button variant="destructive" onClick={handleDeleteRole} disabled={isLoading}>
                {isLoading ? (
                  <>
                    <RefreshCw className="h-4 w-4 mr-2 animate-spin" />
                    删除中...
                  </>
                ) : (
                  <>
                    <Trash2 className="h-4 w-4 mr-2" />
                    确认删除
                  </>
                )}
              </Button>
            </DialogFooter>
          </DialogContent>
        </Dialog>

        {/* 权限编辑器对话框 */}
        <Dialog open={isPermissionEditorOpen} onOpenChange={setIsPermissionEditorOpen}>
          <DialogContent className="max-w-6xl max-h-[90vh] overflow-y-auto">
            <DialogHeader>
              <DialogTitle className="flex items-center">
                <Lock className="h-5 w-5 mr-2" />
                权限编辑器
              </DialogTitle>
              <DialogDescription>
                为角色 "{selectedRole?.name}" 配置详细权限
              </DialogDescription>
            </DialogHeader>

            {selectedRole && (
              <PermissionEditor
                role={selectedRole}
                onSave={handleSavePermissions}
                onCancel={() => setIsPermissionEditorOpen(false)}
                isLoading={isLoading}
              />
            )}
          </DialogContent>
        </Dialog>
      </div>
    </SuperAdminLayout>
  );
};

export default RoleManagementPage;
