/**
 * ⚙️ 系统模块路由
 * 包含健康检查、调试、系统监控等相关API
 */

import { Hono } from 'hono';
import { createHealthResponse, createSuccessResponse, createErrorResponse, wrapAsyncHandler } from '../utils/response.js';

const systemRouter = new Hono();

// 健康检查API
systemRouter.get('/health', (c) => {
  const checks = {
    database: 'healthy',
    memory: 'healthy',
    disk: 'healthy'
  };

  return c.json(createHealthResponse('ok', checks, 'v3.0-modular'));
});

// 系统信息API
systemRouter.get('/info', (c) => {
  const info = {
    name: 'College Employment Survey API',
    version: 'v3.0-modular',
    status: 'running',
    environment: c.env?.ENVIRONMENT || 'production',
    timestamp: new Date().toISOString(),
    modules: [
      'questionnaire',
      'story', 
      'admin',
      'deidentification',
      'system'
    ],
    endpoints: {
      health: '/api/system/health',
      questionnaireStats: '/api/questionnaire/stats',
      questionnaireVoices: '/api/questionnaire/voices',
      visualizationData: '/api/questionnaire/visualization/data',
      storyList: '/api/story/list',
      adminDashboard: '/api/admin/dashboard/stats'
    }
  };

  return c.json(createSuccessResponse(info, 'System information retrieved successfully'));
});

// API调用追踪端点
systemRouter.get('/trace/:endpoint', wrapAsyncHandler(async (c) => {
  const endpoint = c.req.param('endpoint');
  const trace = {
    timestamp: new Date().toISOString(),
    endpoint: endpoint,
    steps: [],
    errors: [],
    performance: {}
  };

  const startTime = Date.now();

  try {
    trace.steps.push({ step: 'start', timestamp: new Date().toISOString() });

    if (endpoint === 'questionnaire-stats') {
      // 追踪问卷统计API调用
      trace.steps.push({ step: 'checking_db_connection', timestamp: new Date().toISOString() });

      if (!c.env.DB) {
        trace.errors.push('DB binding not found');
        return c.json(createErrorResponse('Database not available', 500), 500);
      }

      trace.steps.push({ step: 'db_connected', timestamp: new Date().toISOString() });

      // 测试数据库查询
      const testResult = await c.env.DB.prepare('SELECT COUNT(*) as count FROM questionnaire_responses_v2').first();
      trace.steps.push({ 
        step: 'test_query_executed', 
        timestamp: new Date().toISOString(),
        result: `Found ${testResult?.count || 0} records`
      });

    } else if (endpoint === 'story-list') {
      // 追踪故事列表API调用
      trace.steps.push({ step: 'checking_story_table', timestamp: new Date().toISOString() });

      if (!c.env.DB) {
        trace.errors.push('DB binding not found');
        return c.json(createErrorResponse('Database not available', 500), 500);
      }

      const storyCount = await c.env.DB.prepare('SELECT COUNT(*) as count FROM story_contents_v2').first();
      trace.steps.push({ 
        step: 'story_count_retrieved', 
        timestamp: new Date().toISOString(),
        result: `Found ${storyCount?.count || 0} stories`
      });

    } else {
      trace.errors.push(`Unknown endpoint: ${endpoint}`);
    }

    trace.performance.totalTime = Date.now() - startTime;
    trace.steps.push({ step: 'completed', timestamp: new Date().toISOString() });

    return c.json(createSuccessResponse(trace, 'API trace completed'));

  } catch (error) {
    trace.errors.push(error.message);
    trace.performance.totalTime = Date.now() - startTime;
    trace.steps.push({ step: 'error', timestamp: new Date().toISOString() });

    return c.json(createErrorResponse('Trace failed', 500, trace), 500);
  }
}));

// 调试端点：检查专业和毕业年份数据
systemRouter.get('/debug/majors-graduation', wrapAsyncHandler(async (c) => {
  // 检查专业数据
  const majorSampleResult = await c.env.DB.prepare(`
    SELECT major_display, COUNT(*) as count
    FROM questionnaire_responses_v2
    WHERE major_display IS NOT NULL
    GROUP BY major_display
    ORDER BY count DESC
    LIMIT 10
  `).all();

  // 检查毕业年份数据
  const graduationSampleResult = await c.env.DB.prepare(`
    SELECT graduation_year, COUNT(*) as count
    FROM questionnaire_responses_v2
    WHERE graduation_year IS NOT NULL
    GROUP BY graduation_year
    ORDER BY graduation_year DESC
    LIMIT 10
  `).all();

  // 检查字段是否存在数据
  const majorCountResult = await c.env.DB.prepare(`
    SELECT COUNT(*) as total_with_major
    FROM questionnaire_responses_v2
    WHERE major_display IS NOT NULL AND major_display != ''
  `).first();

  const graduationCountResult = await c.env.DB.prepare(`
    SELECT COUNT(*) as total_with_graduation
    FROM questionnaire_responses_v2
    WHERE graduation_year IS NOT NULL
  `).first();

  // 检查所有记录的样本
  const sampleRecordsResult = await c.env.DB.prepare(`
    SELECT major_display, graduation_year, education_level_display
    FROM questionnaire_responses_v2
    LIMIT 5
  `).all();

  const debug = {
    majorSamples: majorSampleResult.results || [],
    graduationSamples: graduationSampleResult.results || [],
    counts: {
      totalWithMajor: majorCountResult?.total_with_major || 0,
      totalWithGraduation: graduationCountResult?.total_with_graduation || 0
    },
    sampleRecords: sampleRecordsResult.results || []
  };

  return c.json(createSuccessResponse(debug, 'Debug data retrieved successfully'));
}));

// 数据库状态检查API
systemRouter.get('/database/status', wrapAsyncHandler(async (c) => {
  const tables = [
    'questionnaire_responses_v2',
    'story_contents_v2', 
    'questionnaire_voices_v2',
    'users_v2'
  ];

  const status = {
    connected: true,
    tables: {},
    lastCheck: new Date().toISOString()
  };

  for (const table of tables) {
    try {
      const result = await c.env.DB.prepare(`SELECT COUNT(*) as count FROM ${table}`).first();
      status.tables[table] = {
        exists: true,
        count: result?.count || 0,
        status: 'healthy'
      };
    } catch (error) {
      status.tables[table] = {
        exists: false,
        error: error.message,
        status: 'error'
      };
      status.connected = false;
    }
  }

  return c.json(createSuccessResponse(status, 'Database status retrieved successfully'));
}));

// 性能监控API
systemRouter.get('/performance', wrapAsyncHandler(async (c) => {
  const startTime = Date.now();

  // 执行一些性能测试
  const tests = [];

  // 数据库查询性能测试
  const dbStartTime = Date.now();
  try {
    await c.env.DB.prepare('SELECT COUNT(*) FROM questionnaire_responses_v2').first();
    tests.push({
      name: 'database_query',
      duration: Date.now() - dbStartTime,
      status: 'success'
    });
  } catch (error) {
    tests.push({
      name: 'database_query',
      duration: Date.now() - dbStartTime,
      status: 'error',
      error: error.message
    });
  }

  // 内存使用测试（模拟）
  const memoryTest = {
    name: 'memory_usage',
    duration: 5,
    status: 'success',
    details: {
      used: '45MB',
      available: '128MB',
      percentage: 35
    }
  };
  tests.push(memoryTest);

  const performance = {
    totalTestTime: Date.now() - startTime,
    tests,
    summary: {
      passed: tests.filter(t => t.status === 'success').length,
      failed: tests.filter(t => t.status === 'error').length,
      total: tests.length
    },
    timestamp: new Date().toISOString()
  };

  return c.json(createSuccessResponse(performance, 'Performance test completed'));
}));

export default systemRouter;
