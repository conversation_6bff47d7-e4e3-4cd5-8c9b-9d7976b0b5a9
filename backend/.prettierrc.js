/**
 * 🎨 Prettier配置文件
 * 统一的代码格式化规则
 */

module.exports = {
  // 基础格式设置
  printWidth: 100,
  tabWidth: 2,
  useTabs: false,
  semi: true,
  singleQuote: true,
  quoteProps: 'as-needed',
  
  // 尾随逗号
  trailingComma: 'es5',
  
  // 括号空格
  bracketSpacing: true,
  bracketSameLine: false,
  
  // 箭头函数括号
  arrowParens: 'avoid',
  
  // 换行符
  endOfLine: 'lf',
  
  // HTML/JSX 设置
  htmlWhitespaceSensitivity: 'css',
  
  // 嵌入代码格式化
  embeddedLanguageFormatting: 'auto',
  
  // 文件覆盖设置
  overrides: [
    {
      files: '*.json',
      options: {
        printWidth: 80,
        tabWidth: 2
      }
    },
    {
      files: '*.md',
      options: {
        printWidth: 80,
        proseWrap: 'always'
      }
    },
    {
      files: '*.yml',
      options: {
        tabWidth: 2,
        singleQuote: false
      }
    }
  ]
};
