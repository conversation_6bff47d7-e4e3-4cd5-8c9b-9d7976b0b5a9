import React, { useState, useEffect } from 'react';
import SuperAdminLayout from '@/components/layouts/SuperAdminLayout';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { useToast } from '@/components/ui/use-toast';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import {
  FileText, Search, Download, Book, RefreshCw, Eye, GitBranch,
  Clock, CheckCircle, AlertTriangle, Database, Folder, Settings,
  Users, Shield, Code, Tag, Info, Copy
} from 'lucide-react';
import {
  Dialog, DialogContent, DialogDescription, DialogFooter,
  DialogHeader, DialogTitle
} from '@/components/ui/dialog';

// 文档项接口（基于项目文档管理规范）
interface ProjectDocument {
  id: string;
  title: string;
  description: string;
  filePath: string;
  category: string;
  type: 'markdown' | 'pdf' | 'word' | 'text';
  tags: string[];
  lastUpdated: string;
  size: string;
  author: string;
  version: string;
  status: 'published' | 'archived';
  accessLevel: 'public' | 'internal' | 'restricted' | 'confidential';
  isLocal: boolean;
  createdBy: 'augment' | 'user';
  syncStatus: 'synced' | 'pending' | 'error';
  versions: DocumentVersion[];
}

interface DocumentVersion {
  version: string;
  date: string;
  author: string;
  changes: string;
  filePath: string;
  size: string;
}

interface SyncStatus {
  lastSync: string;
  status: 'success' | 'error' | 'pending';
  totalDocs: number;
  syncedDocs: number;
  errorDocs: number;
}

/**
 * 项目文档中心页面 - 只读技术仓库
 *
 * 基于项目文档管理规范，提供本地文档同步和多版本管理
 */
const DocumentationPage: React.FC = () => {
  const { toast } = useToast();
  const [isLoading, setIsLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedCategory, setSelectedCategory] = useState('all');
  const [selectedAccessLevel, setSelectedAccessLevel] = useState('all');
  const [selectedCreatedBy, setSelectedCreatedBy] = useState('all');
  const [documents, setDocuments] = useState<ProjectDocument[]>([]);
  const [selectedDocument, setSelectedDocument] = useState<ProjectDocument | null>(null);
  const [isViewDialogOpen, setIsViewDialogOpen] = useState(false);
  const [isSyncing, setIsSyncing] = useState(false);
  const [syncStatus, setSyncStatus] = useState<SyncStatus>({
    lastSync: '2024-01-15 10:30:00',
    status: 'success',
    totalDocs: 0,
    syncedDocs: 0,
    errorDocs: 0
  });

  // 项目文档数据（基于实际项目结构和文档管理规范）
  const projectDocuments: ProjectDocument[] = [
    {
      id: '1',
      title: '项目文档管理规则',
      description: '定义项目文档管理的规则、流程和标准',
      filePath: '/docs/documentation-management-rules.md',
      category: '项目概述',
      type: 'markdown',
      tags: ['文档', '规则', '流程'],
      lastUpdated: '2024-05-23',
      size: '12.8 KB',
      author: '项目团队',
      version: '1.0.0',
      status: 'published',
      accessLevel: 'internal',
      isLocal: true,
      createdBy: 'augment',
      syncStatus: 'synced',
      versions: [
        {
          version: '1.0.0',
          date: '2024-05-23',
          author: '项目团队',
          changes: '初始版本',
          filePath: '/docs/documentation-management-rules.md',
          size: '12.8 KB'
        }
      ]
    },
    {
      id: '2',
      title: '系统架构概览',
      description: '系统整体架构设计和技术选型说明',
      filePath: '/docs/architecture/overview.md',
      category: '技术文档',
      type: 'markdown',
      tags: ['架构', '设计', '技术选型'],
      lastUpdated: '2024-01-10',
      size: '32.1 KB',
      author: '架构师',
      version: '1.5.0',
      status: 'published',
      accessLevel: 'internal',
      isLocal: true,
      createdBy: 'augment',
      syncStatus: 'synced',
      versions: [
        {
          version: '1.5.0',
          date: '2024-01-10',
          author: '架构师',
          changes: '更新微服务架构设计',
          filePath: '/docs/architecture/overview.md',
          size: '32.1 KB'
        }
      ]
    },
    {
      id: '3',
      title: 'API 概览文档',
      description: 'REST API 接口的概览和基础说明',
      filePath: '/docs/api/overview.md',
      category: 'API文档',
      type: 'markdown',
      tags: ['API', '概览', 'REST'],
      lastUpdated: '2024-01-13',
      size: '22.8 KB',
      author: '后端团队',
      version: '2.1.0',
      status: 'published',
      accessLevel: 'internal',
      isLocal: true,
      createdBy: 'augment',
      syncStatus: 'synced',
      versions: [
        {
          version: '2.1.0',
          date: '2024-01-13',
          author: '后端团队',
          changes: '添加新的认证接口',
          filePath: '/docs/api/overview.md',
          size: '22.8 KB'
        }
      ]
    },
    {
      id: '4',
      title: '超级管理员功能指南',
      description: '超级管理员功能的详细使用指南和最佳实践',
      filePath: '/docs/superadmin/superadmin-features-guide.md',
      category: '用户指南',
      type: 'markdown',
      tags: ['超级管理员', '功能指南', '最佳实践'],
      lastUpdated: '2024-01-14',
      size: '28.5 KB',
      author: '产品团队',
      version: '1.2.0',
      status: 'published',
      accessLevel: 'restricted',
      isLocal: true,
      createdBy: 'user',
      syncStatus: 'synced',
      versions: [
        {
          version: '1.2.0',
          date: '2024-01-14',
          author: '产品团队',
          changes: '添加新功能说明',
          filePath: '/docs/superadmin/superadmin-features-guide.md',
          size: '28.5 KB'
        }
      ]
    },
    {
      id: '5',
      title: '部署指南',
      description: '生产环境部署步骤和配置说明',
      filePath: '/docs/deployment.md',
      category: '部署文档',
      type: 'markdown',
      tags: ['部署', 'Docker', '生产环境'],
      lastUpdated: '2024-01-07',
      size: '18.7 KB',
      author: '运维团队',
      version: '1.3.0',
      status: 'published',
      accessLevel: 'restricted',
      isLocal: true,
      createdBy: 'augment',
      syncStatus: 'synced',
      versions: [
        {
          version: '1.3.0',
          date: '2024-01-07',
          author: '运维团队',
          changes: '更新Docker配置',
          filePath: '/docs/deployment.md',
          size: '18.7 KB'
        }
      ]
    },
    {
      id: '6',
      title: '测试执行报告',
      description: '系统功能测试的执行结果和问题记录',
      filePath: '/docs/testing/test-execution-report.md',
      category: '测试文档',
      type: 'markdown',
      tags: ['测试', '执行报告', '质量保证'],
      lastUpdated: '2024-01-01',
      size: '24.6 KB',
      author: '测试团队',
      version: '1.0.0',
      status: 'published',
      accessLevel: 'internal',
      isLocal: true,
      createdBy: 'augment',
      syncStatus: 'synced',
      versions: [
        {
          version: '1.0.0',
          date: '2024-01-01',
          author: '测试团队',
          changes: '初始测试报告',
          filePath: '/docs/testing/test-execution-report.md',
          size: '24.6 KB'
        }
      ]
    },
    {
      id: '7',
      title: '数据使用维护指南',
      description: '数据使用和维护的详细指南',
      filePath: '/docs/data-usage-maintenance-guide.md',
      category: '维护文档',
      type: 'markdown',
      tags: ['数据维护', '使用指南', '最佳实践'],
      lastUpdated: '2023-12-28',
      size: '19.3 KB',
      author: '数据团队',
      version: '1.1.0',
      status: 'published',
      accessLevel: 'restricted',
      isLocal: true,
      createdBy: 'user',
      syncStatus: 'synced',
      versions: [
        {
          version: '1.1.0',
          date: '2023-12-28',
          author: '数据团队',
          changes: '添加数据清理流程',
          filePath: '/docs/data-usage-maintenance-guide.md',
          size: '19.3 KB'
        }
      ]
    }
  ];

  // 文档分类（基于项目规范）
  const documentCategories = [
    { id: 'all', name: '全部文档', icon: <Folder className="h-4 w-4" />, count: projectDocuments.length },
    { id: '项目概述', name: '项目概述', icon: <Book className="h-4 w-4" />, count: projectDocuments.filter(d => d.category === '项目概述').length },
    { id: '技术文档', name: '技术文档', icon: <Code className="h-4 w-4" />, count: projectDocuments.filter(d => d.category === '技术文档').length },
    { id: 'API文档', name: 'API文档', icon: <Database className="h-4 w-4" />, count: projectDocuments.filter(d => d.category === 'API文档').length },
    { id: '用户指南', name: '用户指南', icon: <Users className="h-4 w-4" />, count: projectDocuments.filter(d => d.category === '用户指南').length },
    { id: '部署文档', name: '部署文档', icon: <Shield className="h-4 w-4" />, count: projectDocuments.filter(d => d.category === '部署文档').length },
    { id: '测试文档', name: '测试文档', icon: <CheckCircle className="h-4 w-4" />, count: projectDocuments.filter(d => d.category === '测试文档').length },
    { id: '维护文档', name: '维护文档', icon: <Settings className="h-4 w-4" />, count: projectDocuments.filter(d => d.category === '维护文档').length }
  ];

  // 加载文档数据
  useEffect(() => {
    const loadDocuments = async () => {
      try {
        setIsLoading(true);

        // 尝试从真实API获取文档
        const response = await fetch(`${import.meta.env.VITE_API_BASE_URL}/documentation/documents`);

        if (response.ok) {
          const result = await response.json();
          if (result.success) {
            setDocuments(result.data);
            setSyncStatus(result.syncStatus);

            toast({
              title: result.fallback ? '文档加载完成（模拟数据）' : '文档同步完成',
              description: `已加载 ${result.data.length} 个文档`,
            });
          } else {
            throw new Error(result.error || '获取文档失败');
          }
        } else {
          throw new Error(`HTTP ${response.status}: ${response.statusText}`);
        }
      } catch (error) {
        console.error('加载文档失败:', error);

        // 回退到模拟数据
        setDocuments(projectDocuments);
        setSyncStatus({
          lastSync: new Date().toISOString().slice(0, 19).replace('T', ' '),
          status: 'success',
          totalDocs: projectDocuments.length,
          syncedDocs: projectDocuments.filter(d => d.syncStatus === 'synced').length,
          errorDocs: projectDocuments.filter(d => d.syncStatus === 'error').length
        });

        toast({
          title: '文档加载完成（离线模式）',
          description: `已加载 ${projectDocuments.length} 个模拟文档`,
          variant: 'default',
        });
      } finally {
        setIsLoading(false);
      }
    };

    loadDocuments();
  }, []);

  // 过滤文档
  const filteredDocuments = documents.filter(doc => {
    const matchesSearch = doc.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         doc.description.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         doc.tags.some(tag => tag.toLowerCase().includes(searchTerm.toLowerCase()));

    const matchesCategory = selectedCategory === 'all' || doc.category === selectedCategory;
    const matchesAccessLevel = selectedAccessLevel === 'all' || doc.accessLevel === selectedAccessLevel;
    const matchesCreatedBy = selectedCreatedBy === 'all' || doc.createdBy === selectedCreatedBy;

    return matchesSearch && matchesCategory && matchesAccessLevel && matchesCreatedBy;
  });

  // 手动同步文档
  const handleManualSync = async () => {
    try {
      setIsSyncing(true);

      // 调用同步API
      const response = await fetch(`${import.meta.env.VITE_API_BASE_URL}/documentation/sync`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
      });

      if (response.ok) {
        const result = await response.json();
        if (result.success) {
          // 重新加载文档列表
          const docsResponse = await fetch(`${import.meta.env.VITE_API_BASE_URL}/documentation/documents`);
          if (docsResponse.ok) {
            const docsResult = await docsResponse.json();
            if (docsResult.success) {
              setDocuments(docsResult.data);
              setSyncStatus(docsResult.syncStatus);
            }
          }

          toast({
            title: '同步完成',
            description: result.message || '文档已成功同步',
          });
        } else {
          throw new Error(result.error || '同步失败');
        }
      } else {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }
    } catch (error) {
      console.error('同步失败:', error);

      // 回退到模拟同步
      setSyncStatus({
        lastSync: new Date().toISOString().slice(0, 19).replace('T', ' '),
        status: 'success',
        totalDocs: documents.length,
        syncedDocs: documents.length,
        errorDocs: 0
      });

      toast({
        title: '同步完成（离线模式）',
        description: '已更新本地文档状态',
      });
    } finally {
      setIsSyncing(false);
    }
  };

  // 查看文档
  const viewDocument = (document: ProjectDocument) => {
    setSelectedDocument(document);
    setIsViewDialogOpen(true);
  };

  // 在新窗口中打开文档
  const openDocumentInNewWindow = (document: ProjectDocument) => {
    const url = `http://localhost:8787/view/${document.id}`;
    window.open(url, '_blank', 'width=1000,height=800,scrollbars=yes,resizable=yes');
  };

  // 下载文档
  const downloadDocument = (document: ProjectDocument) => {
    if (!document.content) return;

    const blob = new Blob([document.content], { type: 'text/markdown' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `${document.title}.md`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
  };

  // 复制文档内容
  const copyDocument = async (document: ProjectDocument) => {
    if (!document.content) return;

    try {
      await navigator.clipboard.writeText(document.content);
      toast({
        title: '复制成功',
        description: '文档内容已复制到剪贴板',
        variant: 'default',
      });
    } catch (error) {
      console.error('复制失败:', error);
      toast({
        title: '复制失败',
        description: '无法复制到剪贴板，请手动选择文本复制',
        variant: 'destructive',
      });
    }
  };

  // 获取访问级别颜色
  const getAccessLevelColor = (level: string) => {
    switch (level) {
      case 'public': return 'bg-green-100 text-green-800';
      case 'internal': return 'bg-blue-100 text-blue-800';
      case 'restricted': return 'bg-orange-100 text-orange-800';
      case 'confidential': return 'bg-red-100 text-red-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  // 获取访问级别标签
  const getAccessLevelLabel = (level: string) => {
    switch (level) {
      case 'public': return '公开';
      case 'internal': return '内部';
      case 'restricted': return '受限';
      case 'confidential': return '机密';
      default: return level;
    }
  };

  // 获取创建者标签
  const getCreatedByLabel = (createdBy: string) => {
    return createdBy === 'augment' ? '开发环境' : '用户创建';
  };

  // 获取同步状态图标
  const getSyncStatusIcon = (status: string) => {
    switch (status) {
      case 'synced': return <CheckCircle className="h-4 w-4 text-green-600" />;
      case 'pending': return <Clock className="h-4 w-4 text-yellow-600" />;
      case 'error': return <AlertTriangle className="h-4 w-4 text-red-600" />;
      default: return <RefreshCw className="h-4 w-4 text-gray-600" />;
    }
  };

  return (
    <SuperAdminLayout>
      <div className="container mx-auto py-6">
        <div className="flex justify-between items-center mb-6">
          <div className="flex items-center">
            <Book className="h-6 w-6 mr-2" />
            <h1 className="text-2xl font-bold">项目文档中心</h1>
            <Badge variant="secondary" className="ml-3">
              只读技术仓库
            </Badge>
          </div>
          <div className="flex items-center space-x-2">
            <Button
              variant="outline"
              onClick={handleManualSync}
              disabled={isSyncing}
            >
              {isSyncing ? (
                <RefreshCw className="mr-2 h-4 w-4 animate-spin" />
              ) : (
                <RefreshCw className="mr-2 h-4 w-4" />
              )}
              {isSyncing ? '同步中...' : '同步文档'}
            </Button>
          </div>
        </div>

        {/* 同步状态概览 */}
        <Card className="mb-6">
          <CardContent className="p-4">
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
              <div className="flex items-center">
                <Database className="h-8 w-8 text-blue-600 mr-3" />
                <div>
                  <p className="text-sm font-medium text-muted-foreground">总文档数</p>
                  <p className="text-2xl font-bold">{syncStatus.totalDocs}</p>
                </div>
              </div>

              <div className="flex items-center">
                <CheckCircle className="h-8 w-8 text-green-600 mr-3" />
                <div>
                  <p className="text-sm font-medium text-muted-foreground">已同步</p>
                  <p className="text-2xl font-bold">{syncStatus.syncedDocs}</p>
                </div>
              </div>

              <div className="flex items-center">
                <GitBranch className="h-8 w-8 text-purple-600 mr-3" />
                <div>
                  <p className="text-sm font-medium text-muted-foreground">本地文档</p>
                  <p className="text-2xl font-bold">
                    {documents.filter(d => d.isLocal).length}
                  </p>
                </div>
              </div>

              <div className="flex items-center">
                <Clock className="h-8 w-8 text-gray-600 mr-3" />
                <div>
                  <p className="text-sm font-medium text-muted-foreground">最后同步</p>
                  <p className="text-sm font-medium">{syncStatus.lastSync}</p>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

        <Tabs defaultValue="documents" className="space-y-4">
          <TabsList>
            <TabsTrigger value="documents">
              <FileText className="mr-2 h-4 w-4" />
              文档库
            </TabsTrigger>
            <TabsTrigger value="categories">
              <Folder className="mr-2 h-4 w-4" />
              分类管理
            </TabsTrigger>
            <TabsTrigger value="sync">
              <RefreshCw className="mr-2 h-4 w-4" />
              同步管理
            </TabsTrigger>
          </TabsList>

          {/* 文档库 */}
          <TabsContent value="documents" className="space-y-4">
            {/* 搜索和筛选 */}
            <Card>
              <CardHeader>
                <div className="flex flex-col sm:flex-row gap-4">
                  <div className="flex-1">
                    <div className="relative">
                      <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground h-4 w-4" />
                      <Input
                        placeholder="搜索文档标题、描述或标签..."
                        value={searchTerm}
                        onChange={(e) => setSearchTerm(e.target.value)}
                        className="pl-10"
                      />
                    </div>
                  </div>
                  <div className="flex gap-2">
                    <Select value={selectedCategory} onValueChange={setSelectedCategory}>
                      <SelectTrigger className="w-[150px]">
                        <SelectValue placeholder="选择分类" />
                      </SelectTrigger>
                      <SelectContent>
                        {documentCategories.map(category => (
                          <SelectItem key={category.id} value={category.id}>
                            <div className="flex items-center">
                              {category.icon}
                              <span className="ml-2">{category.name}</span>
                            </div>
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>

                    <Select value={selectedAccessLevel} onValueChange={setSelectedAccessLevel}>
                      <SelectTrigger className="w-[120px]">
                        <SelectValue placeholder="访问级别" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="all">全部级别</SelectItem>
                        <SelectItem value="public">公开</SelectItem>
                        <SelectItem value="internal">内部</SelectItem>
                        <SelectItem value="restricted">受限</SelectItem>
                        <SelectItem value="confidential">机密</SelectItem>
                      </SelectContent>
                    </Select>

                    <Select value={selectedCreatedBy} onValueChange={setSelectedCreatedBy}>
                      <SelectTrigger className="w-[120px]">
                        <SelectValue placeholder="创建者" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="all">全部</SelectItem>
                        <SelectItem value="augment">开发环境</SelectItem>
                        <SelectItem value="user">用户创建</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                </div>
              </CardHeader>
            </Card>

            {/* 文档列表 */}
            {isLoading ? (
              <Card>
                <CardContent className="flex items-center justify-center py-8">
                  <RefreshCw className="h-6 w-6 animate-spin mr-2" />
                  加载文档中...
                </CardContent>
              </Card>
            ) : filteredDocuments.length === 0 ? (
              <Card>
                <CardContent className="text-center py-8">
                  <FileText className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
                  <p className="text-muted-foreground">
                    {searchTerm || selectedCategory !== 'all' || selectedAccessLevel !== 'all' || selectedCreatedBy !== 'all'
                      ? '没有找到匹配的文档'
                      : '暂无文档'}
                  </p>
                </CardContent>
              </Card>
            ) : (
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                {filteredDocuments.map((document) => (
                  <Card key={document.id} className="hover:shadow-md transition-shadow">
                    <CardHeader className="pb-3">
                      <div className="flex items-start justify-between">
                        <div className="flex items-center space-x-2">
                          <FileText className="h-4 w-4" />
                          <div className="flex-1 min-w-0">
                            <CardTitle className="text-sm font-medium truncate">
                              {document.title}
                            </CardTitle>
                            <div className="flex items-center space-x-2 mt-1">
                              <Badge variant="outline" className="text-xs">
                                {document.category}
                              </Badge>
                              <Badge className={`text-xs ${getAccessLevelColor(document.accessLevel)}`}>
                                {getAccessLevelLabel(document.accessLevel)}
                              </Badge>
                            </div>
                          </div>
                        </div>
                        <div className="flex items-center space-x-1">
                          {getSyncStatusIcon(document.syncStatus)}
                          <Badge variant={document.createdBy === 'augment' ? 'default' : 'secondary'} className="text-xs">
                            {getCreatedByLabel(document.createdBy)}
                          </Badge>
                        </div>
                      </div>
                    </CardHeader>

                    <CardContent className="pt-0">
                      <CardDescription className="text-xs mb-3 line-clamp-2">
                        {document.description}
                      </CardDescription>

                      <div className="flex flex-wrap gap-1 mb-3">
                        {Array.isArray(document.tags) && document.tags.slice(0, 3).map((tag, index) => (
                          <Badge key={index} variant="secondary" className="text-xs">
                            <Tag className="h-2 w-2 mr-1" />
                            {tag}
                          </Badge>
                        ))}
                        {Array.isArray(document.tags) && document.tags.length > 3 && (
                          <Badge variant="secondary" className="text-xs">
                            +{document.tags.length - 3}
                          </Badge>
                        )}
                      </div>

                      <div className="flex items-center justify-between text-xs text-muted-foreground mb-3">
                        <span>作者: {document.author}</span>
                        <span>v{document.version}</span>
                      </div>

                      <div className="flex items-center justify-between text-xs text-muted-foreground mb-4">
                        <span>{document.size}</span>
                        <span>{document.lastUpdated}</span>
                      </div>

                      <div className="flex space-x-2">
                        <Button
                          size="sm"
                          variant="outline"
                          className="flex-1"
                          onClick={() => viewDocument(document)}
                        >
                          <Eye className="h-3 w-3 mr-1" />
                          查看
                        </Button>
                        <Button
                          size="sm"
                          variant="outline"
                          onClick={() => downloadDocument(document)}
                        >
                          <Download className="h-3 w-3 mr-1" />
                          下载
                        </Button>
                        <Button size="sm" variant="outline" title="版本历史">
                          <GitBranch className="h-3 w-3" />
                        </Button>
                      </div>
                    </CardContent>
                  </Card>
                ))}
              </div>
            )}
          </TabsContent>

          {/* 分类管理 */}
          <TabsContent value="categories" className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              {documentCategories.filter(cat => cat.id !== 'all').map((category) => (
                <Card key={category.id}>
                  <CardHeader>
                    <CardTitle className="flex items-center text-sm">
                      {category.icon}
                      <span className="ml-2">{category.name}</span>
                      <Badge variant="secondary" className="ml-auto">
                        {category.count}
                      </Badge>
                    </CardTitle>
                    <CardDescription className="text-xs">
                      {category.name}相关的项目文档
                    </CardDescription>
                  </CardHeader>
                  <CardContent>
                    <div className="flex space-x-2">
                      <Button
                        size="sm"
                        variant="outline"
                        className="flex-1"
                        onClick={() => setSelectedCategory(category.id)}
                      >
                        <Eye className="h-3 w-3 mr-1" />
                        查看文档
                      </Button>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          </TabsContent>

          {/* 同步管理 */}
          <TabsContent value="sync" className="space-y-4">
            <Card>
              <CardHeader>
                <CardTitle>文档同步管理</CardTitle>
                <CardDescription>
                  管理本地文档与在线文档库的同步
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <Alert>
                  <Info className="h-4 w-4" />
                  <AlertTitle>同步说明</AlertTitle>
                  <AlertDescription>
                    文档中心会自动从项目的 docs 目录同步文档。所有文档都是只读的，
                    不支持在线编辑以避免与本地文档产生冲突。如需修改文档，请在本地编辑后重新同步。
                  </AlertDescription>
                </Alert>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <h4 className="text-sm font-medium mb-2">同步状态</h4>
                    <div className="space-y-2">
                      <div className="flex justify-between">
                        <span className="text-sm">最后同步时间</span>
                        <span className="text-sm font-medium">{syncStatus.lastSync}</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-sm">同步状态</span>
                        <Badge className={syncStatus.status === 'success' ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'}>
                          {syncStatus.status === 'success' ? '成功' : '失败'}
                        </Badge>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-sm">已同步文档</span>
                        <span className="text-sm font-medium">{syncStatus.syncedDocs}/{syncStatus.totalDocs}</span>
                      </div>
                    </div>
                  </div>

                  <div>
                    <h4 className="text-sm font-medium mb-2">文档统计</h4>
                    <div className="space-y-2">
                      <div className="flex justify-between">
                        <span className="text-sm">开发环境创建</span>
                        <span className="text-sm font-medium">
                          {documents.filter(d => d.createdBy === 'augment').length}
                        </span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-sm">用户创建</span>
                        <span className="text-sm font-medium">
                          {documents.filter(d => d.createdBy === 'user').length}
                        </span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-sm">本地文档</span>
                        <span className="text-sm font-medium">
                          {documents.filter(d => d.isLocal).length}
                        </span>
                      </div>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>

        {/* 文档查看对话框 */}
        <Dialog open={isViewDialogOpen} onOpenChange={setIsViewDialogOpen}>
          <DialogContent className="max-w-4xl max-h-[80vh] overflow-y-auto">
            {selectedDocument && (
              <>
                <DialogHeader>
                  <DialogTitle className="flex items-center">
                    <FileText className="h-5 w-5 mr-2" />
                    <span>{selectedDocument.title}</span>
                    <Badge className={`ml-2 ${getAccessLevelColor(selectedDocument.accessLevel)}`}>
                      {getAccessLevelLabel(selectedDocument.accessLevel)}
                    </Badge>
                  </DialogTitle>
                  <DialogDescription>
                    {selectedDocument.description}
                  </DialogDescription>
                </DialogHeader>

                <div className="space-y-4">
                  {/* 文档信息 */}
                  <div className="grid grid-cols-2 md:grid-cols-4 gap-4 p-4 bg-muted rounded-lg">
                    <div>
                      <p className="text-xs text-muted-foreground">作者</p>
                      <p className="text-sm font-medium">{selectedDocument.author}</p>
                    </div>
                    <div>
                      <p className="text-xs text-muted-foreground">版本</p>
                      <p className="text-sm font-medium">v{selectedDocument.version}</p>
                    </div>
                    <div>
                      <p className="text-xs text-muted-foreground">大小</p>
                      <p className="text-sm font-medium">{selectedDocument.size}</p>
                    </div>
                    <div>
                      <p className="text-xs text-muted-foreground">更新时间</p>
                      <p className="text-sm font-medium">{selectedDocument.lastUpdated}</p>
                    </div>
                  </div>

                  {/* 标签和分类 */}
                  <div className="flex flex-wrap gap-2">
                    <Badge variant="outline">
                      {selectedDocument.category}
                    </Badge>
                    <Badge variant={selectedDocument.createdBy === 'augment' ? 'default' : 'secondary'}>
                      {getCreatedByLabel(selectedDocument.createdBy)}
                    </Badge>
                    {Array.isArray(selectedDocument.tags) && selectedDocument.tags.map((tag, index) => (
                      <Badge key={index} variant="secondary">
                        <Tag className="h-3 w-3 mr-1" />
                        {tag}
                      </Badge>
                    ))}
                  </div>

                  {/* 文件路径 */}
                  <div className="p-3 bg-muted rounded-lg">
                    <p className="text-xs text-muted-foreground mb-1">文件路径</p>
                    <code className="text-sm">{selectedDocument.filePath}</code>
                  </div>

                  {/* 版本历史 */}
                  <div>
                    <h4 className="text-sm font-medium mb-3">版本历史</h4>
                    <div className="space-y-2">
                      {selectedDocument.versions.map((version, index) => (
                        <div key={index} className="flex items-center justify-between p-3 border rounded-lg">
                          <div className="flex items-center space-x-3">
                            <GitBranch className="h-4 w-4 text-muted-foreground" />
                            <div>
                              <p className="text-sm font-medium">v{version.version}</p>
                              <p className="text-xs text-muted-foreground">{version.changes}</p>
                            </div>
                          </div>
                          <div className="text-right">
                            <p className="text-xs text-muted-foreground">{version.author}</p>
                            <p className="text-xs text-muted-foreground">{version.date}</p>
                          </div>
                        </div>
                      ))}
                    </div>
                  </div>

                  {/* 只读提示 */}
                  <Alert>
                    <Info className="h-4 w-4" />
                    <AlertTitle>只读文档</AlertTitle>
                    <AlertDescription>
                      此文档是从本地项目同步的只读文档。如需修改，请在本地编辑源文件后重新同步。
                    </AlertDescription>
                  </Alert>
                </div>

                <DialogFooter>
                  <Button variant="outline" onClick={() => setIsViewDialogOpen(false)}>
                    关闭
                  </Button>
                  <Button
                    variant="outline"
                    onClick={() => copyDocument(selectedDocument)}
                  >
                    <Copy className="mr-2 h-4 w-4" />
                    复制内容
                  </Button>
                  <Button
                    variant="outline"
                    onClick={() => downloadDocument(selectedDocument)}
                  >
                    <Download className="mr-2 h-4 w-4" />
                    下载文档
                  </Button>
                  <Button onClick={() => openDocumentInNewWindow(selectedDocument)}>
                    <FileText className="mr-2 h-4 w-4" />
                    在新窗口打开
                  </Button>
                </DialogFooter>
              </>
            )}
          </DialogContent>
        </Dialog>
      </div>
    </SuperAdminLayout>
  );
};

export default DocumentationPage;