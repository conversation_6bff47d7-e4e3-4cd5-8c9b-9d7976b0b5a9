/**
 * 用户管理路由
 */

import { Hono } from 'hono';
import { Env } from '../../types';
import { superAdminAuthMiddleware } from '../../middlewares/superAdminAuth.middleware';
import * as userController from './user-management.controller';

// 创建路由
const userManagementRouter = new Hono<{ Bindings: Env }>();

// 应用超级管理员认证中间件
userManagementRouter.use('*', superAdminAuthMiddleware);

// 用户管理路由
userManagementRouter.get('/', userController.getUsers);           // 获取用户列表
userManagementRouter.get('/:id', userController.getUser);         // 获取单个用户详情
userManagementRouter.post('/', userController.createUser);        // 创建用户
userManagementRouter.put('/:id', userController.updateUser);      // 更新用户
userManagementRouter.delete('/:id', userController.deleteUser);   // 删除用户
userManagementRouter.post('/:id/reset-password', userController.resetUserPassword); // 重置密码

export default userManagementRouter;
