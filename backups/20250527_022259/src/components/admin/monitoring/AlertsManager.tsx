import React, { useState, useEffect } from 'react';
import { 
  Card, 
  Table, 
  Button, 
  Space, 
  Modal, 
  Form, 
  Input, 
  Select, 
  Switch, 
  message, 
  Tooltip, 
  Typography, 
  Alert, 
  Spin, 
  Popconfirm,
  Badge,
  Tag,
  Tabs,
  InputNumber,
  Divider
} from 'antd';
import { 
  ReloadOutlined, 
  PlusOutlined, 
  DeleteOutlined, 
  EditOutlined, 
  InfoCircleOutlined, 
  WarningOutlined, 
  CloseCircleOutlined,
  CheckCircleOutlined,
  BellOutlined,
  MailOutlined,
  SlackOutlined
} from '@ant-design/icons';
import { fetchData } from '../../../services/unifiedDataService';
import dayjs from 'dayjs';

const { TabPane } = Tabs;
const { Title, Text } = Typography;
const { Option } = Select;
const { TextArea } = Input;

// 告警类型
type AlertType = 'system' | 'business';

// 告警级别
type AlertLevel = 'info' | 'warning' | 'critical';

// 告警管理组件
const AlertsManager: React.FC = () => {
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);
  const [alerts, setAlerts] = useState<any[]>([]);
  const [alertRules, setAlertRules] = useState<any[]>([]);
  const [alertModalVisible, setAlertModalVisible] = useState<boolean>(false);
  const [editingAlert, setEditingAlert] = useState<any>(null);
  const [alertForm] = Form.useForm();
  const [notificationSettings, setNotificationSettings] = useState<any>({});
  const [settingsModalVisible, setSettingsModalVisible] = useState<boolean>(false);
  const [settingsForm] = Form.useForm();

  // 加载告警数据
  const loadAlerts = async () => {
    setLoading(true);
    setError(null);

    try {
      const response = await fetchData(
        'admin/alerts',
        'getAlerts',
        {},
        {
          method: 'GET',
          headers: {
            Authorization: `Bearer ${localStorage.getItem('authToken')}`,
          },
        }
      );

      if (response.success) {
        setAlerts(response.alerts || []);
        setAlertRules(response.rules || []);
        setNotificationSettings(response.notificationSettings || {});
      } else {
        setError(response.error || '获取告警数据失败');
      }
    } catch (err) {
      setError('获取告警数据时发生错误');
      console.error('获取告警数据错误:', err);
    } finally {
      setLoading(false);
    }
  };

  // 首次加载
  useEffect(() => {
    loadAlerts();
  }, []);

  // 创建或更新告警规则
  const handleSaveAlert = async (values: any) => {
    try {
      const endpoint = editingAlert 
        ? `admin/alerts/rules/${editingAlert.id}` 
        : 'admin/alerts/rules';
      
      const method = editingAlert ? 'PUT' : 'POST';
      
      const response = await fetchData(
        endpoint,
        editingAlert ? 'updateAlertRule' : 'createAlertRule',
        values,
        {
          method,
          headers: {
            Authorization: `Bearer ${localStorage.getItem('authToken')}`,
          },
        }
      );
      
      if (response.success) {
        message.success(editingAlert ? '告警规则更新成功' : '告警规则创建成功');
        setAlertModalVisible(false);
        alertForm.resetFields();
        setEditingAlert(null);
        
        // 重新加载告警数据
        loadAlerts();
      } else {
        message.error(response.error || (editingAlert ? '更新告警规则失败' : '创建告警规则失败'));
      }
    } catch (err) {
      console.error('保存告警规则错误:', err);
      message.error('保存告警规则时发生错误');
    }
  };

  // 删除告警规则
  const handleDeleteAlert = async (rule: any) => {
    try {
      const response = await fetchData(
        `admin/alerts/rules/${rule.id}`,
        'deleteAlertRule',
        {},
        {
          method: 'DELETE',
          headers: {
            Authorization: `Bearer ${localStorage.getItem('authToken')}`,
          },
        }
      );
      
      if (response.success) {
        message.success('告警规则已删除');
        
        // 重新加载告警数据
        loadAlerts();
      } else {
        message.error(response.error || '删除告警规则失败');
      }
    } catch (err) {
      console.error('删除告警规则错误:', err);
      message.error('删除告警规则时发生错误');
    }
  };

  // 保存通知设置
  const handleSaveSettings = async (values: any) => {
    try {
      const response = await fetchData(
        'admin/alerts/settings',
        'updateAlertSettings',
        values,
        {
          method: 'PUT',
          headers: {
            Authorization: `Bearer ${localStorage.getItem('authToken')}`,
          },
        }
      );
      
      if (response.success) {
        message.success('通知设置已保存');
        setSettingsModalVisible(false);
        
        // 重新加载告警数据
        loadAlerts();
      } else {
        message.error(response.error || '保存通知设置失败');
      }
    } catch (err) {
      console.error('保存通知设置错误:', err);
      message.error('保存通知设置时发生错误');
    }
  };

  // 编辑告警规则
  const handleEditAlert = (rule: any) => {
    setEditingAlert(rule);
    alertForm.setFieldsValue({
      name: rule.name,
      type: rule.type,
      level: rule.level,
      metric: rule.metric,
      operator: rule.operator,
      threshold: rule.threshold,
      duration: rule.duration,
      description: rule.description,
      enabled: rule.enabled,
    });
    setAlertModalVisible(true);
  };

  // 获取告警级别标签
  const getLevelTag = (level: AlertLevel) => {
    const levelConfig = {
      info: { color: 'blue', icon: <InfoCircleOutlined /> },
      warning: { color: 'orange', icon: <WarningOutlined /> },
      critical: { color: 'red', icon: <CloseCircleOutlined /> },
    };

    const config = levelConfig[level] || levelConfig.info;

    return (
      <Tag color={config.color} icon={config.icon}>
        {level.toUpperCase()}
      </Tag>
    );
  };

  // 告警表格列定义
  const alertColumns = [
    {
      title: '时间',
      dataIndex: 'timestamp',
      key: 'timestamp',
      render: (text: string) => dayjs(text).format('YYYY-MM-DD HH:mm:ss'),
      width: 180,
    },
    {
      title: '级别',
      dataIndex: 'level',
      key: 'level',
      width: 100,
      render: (level: AlertLevel) => getLevelTag(level),
    },
    {
      title: '类型',
      dataIndex: 'type',
      key: 'type',
      width: 120,
      render: (type: AlertType) => (
        <Tag color={type === 'system' ? 'purple' : 'green'}>
          {type === 'system' ? '系统' : '业务'}
        </Tag>
      ),
    },
    {
      title: '规则名称',
      dataIndex: 'ruleName',
      key: 'ruleName',
      ellipsis: true,
    },
    {
      title: '消息',
      dataIndex: 'message',
      key: 'message',
      ellipsis: true,
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      width: 100,
      render: (status: string) => (
        <Badge 
          status={status === 'resolved' ? 'success' : 'error'} 
          text={status === 'resolved' ? '已解决' : '未解决'} 
        />
      ),
    },
  ];

  // 告警规则表格列定义
  const ruleColumns = [
    {
      title: '名称',
      dataIndex: 'name',
      key: 'name',
      ellipsis: true,
    },
    {
      title: '类型',
      dataIndex: 'type',
      key: 'type',
      width: 100,
      render: (type: AlertType) => (
        <Tag color={type === 'system' ? 'purple' : 'green'}>
          {type === 'system' ? '系统' : '业务'}
        </Tag>
      ),
    },
    {
      title: '级别',
      dataIndex: 'level',
      key: 'level',
      width: 100,
      render: (level: AlertLevel) => getLevelTag(level),
    },
    {
      title: '指标',
      dataIndex: 'metric',
      key: 'metric',
      ellipsis: true,
    },
    {
      title: '条件',
      key: 'condition',
      ellipsis: true,
      render: (_, record) => `${record.operator} ${record.threshold} (${record.duration}秒)`,
    },
    {
      title: '状态',
      dataIndex: 'enabled',
      key: 'enabled',
      width: 100,
      render: (enabled: boolean) => (
        <Badge 
          status={enabled ? 'success' : 'default'} 
          text={enabled ? '启用' : '禁用'} 
        />
      ),
    },
    {
      title: '操作',
      key: 'action',
      width: 150,
      render: (_: any, record: any) => (
        <Space>
          <Button 
            type="text" 
            icon={<EditOutlined />} 
            onClick={() => handleEditAlert(record)}
          />
          <Popconfirm
            title="确定要删除此告警规则吗？"
            onConfirm={() => handleDeleteAlert(record)}
            okText="确定"
            cancelText="取消"
          >
            <Button 
              type="text" 
              danger 
              icon={<DeleteOutlined />}
            />
          </Popconfirm>
        </Space>
      ),
    },
  ];

  return (
    <div className="alerts-manager">
      <Tabs defaultActiveKey="active">
        <TabPane 
          tab={
            <span>
              <WarningOutlined />
              活动告警
            </span>
          } 
          key="active"
        >
          <Card>
            <div style={{ marginBottom: 16, display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
              <Title level={4}>活动告警</Title>
              <Button
                icon={<ReloadOutlined />}
                onClick={loadAlerts}
                loading={loading}
              >
                刷新
              </Button>
            </div>

            {error && (
              <Alert
                message="错误"
                description={error}
                type="error"
                showIcon
                style={{ marginBottom: 16 }}
              />
            )}

            <Table
              columns={alertColumns}
              dataSource={alerts.filter(alert => alert.status !== 'resolved')}
              rowKey="id"
              loading={loading}
              pagination={{ pageSize: 10 }}
              locale={{ emptyText: '当前没有活动告警' }}
            />
          </Card>
        </TabPane>
        
        <TabPane 
          tab={
            <span>
              <BellOutlined />
              告警规则
            </span>
          } 
          key="rules"
        >
          <Card>
            <div style={{ marginBottom: 16, display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
              <Title level={4}>告警规则</Title>
              <Space>
                <Button
                  type="primary"
                  icon={<PlusOutlined />}
                  onClick={() => {
                    setEditingAlert(null);
                    alertForm.resetFields();
                    setAlertModalVisible(true);
                  }}
                >
                  创建规则
                </Button>
                <Button
                  icon={<SettingOutlined />}
                  onClick={() => {
                    settingsForm.setFieldsValue(notificationSettings);
                    setSettingsModalVisible(true);
                  }}
                >
                  通知设置
                </Button>
                <Button
                  icon={<ReloadOutlined />}
                  onClick={loadAlerts}
                  loading={loading}
                >
                  刷新
                </Button>
              </Space>
            </div>

            {error && (
              <Alert
                message="错误"
                description={error}
                type="error"
                showIcon
                style={{ marginBottom: 16 }}
              />
            )}

            <Table
              columns={ruleColumns}
              dataSource={alertRules}
              rowKey="id"
              loading={loading}
              pagination={{ pageSize: 10 }}
            />
          </Card>
        </TabPane>
        
        <TabPane 
          tab={
            <span>
              <CheckCircleOutlined />
              历史告警
            </span>
          } 
          key="history"
        >
          <Card>
            <div style={{ marginBottom: 16, display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
              <Title level={4}>历史告警</Title>
              <Button
                icon={<ReloadOutlined />}
                onClick={loadAlerts}
                loading={loading}
              >
                刷新
              </Button>
            </div>

            {error && (
              <Alert
                message="错误"
                description={error}
                type="error"
                showIcon
                style={{ marginBottom: 16 }}
              />
            )}

            <Table
              columns={alertColumns}
              dataSource={alerts.filter(alert => alert.status === 'resolved')}
              rowKey="id"
              loading={loading}
              pagination={{ pageSize: 10 }}
              locale={{ emptyText: '没有历史告警记录' }}
            />
          </Card>
        </TabPane>
      </Tabs>

      {/* 告警规则模态框 */}
      <Modal
        title={editingAlert ? '编辑告警规则' : '创建告警规则'}
        visible={alertModalVisible}
        onCancel={() => {
          setAlertModalVisible(false);
          setEditingAlert(null);
        }}
        footer={null}
        width={600}
      >
        <Form
          form={alertForm}
          layout="vertical"
          onFinish={handleSaveAlert}
          initialValues={{
            type: 'system',
            level: 'warning',
            operator: '>',
            duration: 60,
            enabled: true,
          }}
        >
          <Form.Item
            name="name"
            label="规则名称"
            rules={[{ required: true, message: '请输入规则名称' }]}
          >
            <Input placeholder="请输入规则名称" />
          </Form.Item>
          
          <Form.Item
            name="type"
            label="告警类型"
            rules={[{ required: true, message: '请选择告警类型' }]}
          >
            <Select>
              <Option value="system">系统告警</Option>
              <Option value="business">业务告警</Option>
            </Select>
          </Form.Item>
          
          <Form.Item
            name="level"
            label="告警级别"
            rules={[{ required: true, message: '请选择告警级别' }]}
          >
            <Select>
              <Option value="info">信息</Option>
              <Option value="warning">警告</Option>
              <Option value="critical">严重</Option>
            </Select>
          </Form.Item>
          
          <Form.Item
            name="metric"
            label="监控指标"
            rules={[{ required: true, message: '请输入监控指标' }]}
          >
            <Select>
              <Option value="cpu_usage">CPU使用率</Option>
              <Option value="memory_usage">内存使用率</Option>
              <Option value="error_rate">错误率</Option>
              <Option value="response_time">响应时间</Option>
              <Option value="request_count">请求数</Option>
              <Option value="db_size">数据库大小</Option>
              <Option value="pending_reviews">待审核内容数</Option>
              <Option value="active_users">活跃用户数</Option>
            </Select>
          </Form.Item>
          
          <div style={{ display: 'flex', gap: 8 }}>
            <Form.Item
              name="operator"
              label="操作符"
              rules={[{ required: true, message: '请选择操作符' }]}
              style={{ width: '30%' }}
            >
              <Select>
                <Option value=">">大于</Option>
                <Option value=">=">大于等于</Option>
                <Option value="<">小于</Option>
                <Option value="<=">小于等于</Option>
                <Option value="==">等于</Option>
                <Option value="!=">不等于</Option>
              </Select>
            </Form.Item>
            
            <Form.Item
              name="threshold"
              label="阈值"
              rules={[{ required: true, message: '请输入阈值' }]}
              style={{ width: '40%' }}
            >
              <InputNumber style={{ width: '100%' }} />
            </Form.Item>
            
            <Form.Item
              name="duration"
              label="持续时间(秒)"
              rules={[{ required: true, message: '请输入持续时间' }]}
              style={{ width: '30%' }}
            >
              <InputNumber style={{ width: '100%' }} min={0} />
            </Form.Item>
          </div>
          
          <Form.Item
            name="description"
            label="规则描述"
          >
            <TextArea rows={3} placeholder="请输入规则描述" />
          </Form.Item>
          
          <Form.Item
            name="enabled"
            label="启用规则"
            valuePropName="checked"
          >
            <Switch />
          </Form.Item>
          
          <Form.Item>
            <div style={{ textAlign: 'right' }}>
              <Space>
                <Button onClick={() => {
                  setAlertModalVisible(false);
                  setEditingAlert(null);
                }}>
                  取消
                </Button>
                <Button type="primary" htmlType="submit">
                  保存
                </Button>
              </Space>
            </div>
          </Form.Item>
        </Form>
      </Modal>

      {/* 通知设置模态框 */}
      <Modal
        title="告警通知设置"
        visible={settingsModalVisible}
        onCancel={() => setSettingsModalVisible(false)}
        footer={null}
        width={600}
      >
        <Form
          form={settingsForm}
          layout="vertical"
          onFinish={handleSaveSettings}
          initialValues={{
            emailEnabled: true,
            slackEnabled: false,
          }}
        >
          <Divider>
            <MailOutlined /> 邮件通知
          </Divider>
          
          <Form.Item
            name="emailEnabled"
            label="启用邮件通知"
            valuePropName="checked"
          >
            <Switch />
          </Form.Item>
          
          <Form.Item
            name="emailRecipients"
            label="收件人"
            rules={[{ required: true, message: '请输入收件人' }]}
          >
            <Input placeholder="请输入收件人，多个收件人用逗号分隔" />
          </Form.Item>
          
          <Divider>
            <SlackOutlined /> Slack通知
          </Divider>
          
          <Form.Item
            name="slackEnabled"
            label="启用Slack通知"
            valuePropName="checked"
          >
            <Switch />
          </Form.Item>
          
          <Form.Item
            name="slackWebhook"
            label="Webhook URL"
            rules={[{ required: true, message: '请输入Webhook URL' }]}
          >
            <Input placeholder="请输入Slack Webhook URL" />
          </Form.Item>
          
          <Form.Item
            name="slackChannel"
            label="频道"
          >
            <Input placeholder="请输入Slack频道，例如：#alerts" />
          </Form.Item>
          
          <Form.Item>
            <div style={{ textAlign: 'right' }}>
              <Space>
                <Button onClick={() => setSettingsModalVisible(false)}>
                  取消
                </Button>
                <Button type="primary" htmlType="submit">
                  保存
                </Button>
              </Space>
            </div>
          </Form.Item>
        </Form>
      </Modal>
    </div>
  );
};

export default AlertsManager;
