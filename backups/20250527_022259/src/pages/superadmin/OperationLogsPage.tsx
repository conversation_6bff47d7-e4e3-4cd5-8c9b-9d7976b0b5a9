import React, { useState, useEffect } from 'react';
import SuperAdminLayout from '@/components/layouts/SuperAdminLayout';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Badge } from '@/components/ui/badge';
import { useToast } from '@/components/ui/use-toast';
import {
  Search,
  Filter,
  RefreshCw,
  Download,
  Calendar,
  User,
  Activity,
  CheckCircle,
  XCircle
} from 'lucide-react';
import {
  Pagination, PaginationContent, PaginationItem,
  PaginationLink, PaginationNext, PaginationPrevious
} from '@/components/ui/pagination-new';

// 操作日志类型
interface OperationLog {
  id: string;
  adminId: number;
  adminName: string;
  action: string;
  target: string;
  details: string;
  timestamp: string;
  ipAddress: string;
  result: 'success' | 'failure';
}

// 搜索参数
interface LogSearchParams {
  adminId?: string;
  action?: string;
  result?: string;
  page?: number;
  pageSize?: number;
}

/**
 * 操作日志页面
 * 
 * 显示系统中所有管理员的操作记录
 */
const OperationLogsPage: React.FC = () => {
  const { toast } = useToast();
  const [isLoading, setIsLoading] = useState(false);
  const [logs, setLogs] = useState<OperationLog[]>([]);
  const [totalLogs, setTotalLogs] = useState(0);
  const [totalPages, setTotalPages] = useState(1);
  const [searchParams, setSearchParams] = useState<LogSearchParams>({
    page: 1,
    pageSize: 20
  });

  // 获取操作日志
  const fetchLogs = async () => {
    try {
      setIsLoading(true);

      const params = new URLSearchParams();
      params.append('page', String(searchParams.page || 1));
      params.append('pageSize', String(searchParams.pageSize || 20));

      if (searchParams.adminId) {
        params.append('adminId', searchParams.adminId);
      }
      if (searchParams.action) {
        params.append('action', searchParams.action);
      }
      if (searchParams.result) {
        params.append('result', searchParams.result);
      }

      const apiUrl = `http://localhost:8789/api/admin/operation-logs?${params}`;
      console.log('🔍 正在获取操作日志:', apiUrl);

      const response = await fetch(apiUrl);
      console.log('📡 API响应状态:', response.status);

      if (!response.ok) {
        throw new Error(`API调用失败: ${response.status}`);
      }

      const data = await response.json();
      console.log('📊 API返回数据:', data);

      if (data.success && data.data) {
        const logs = data.data.logs || [];
        const total = data.data.pagination?.total || 0;
        const totalPages = data.data.pagination?.totalPages || 1;

        setLogs(logs);
        setTotalLogs(total);
        setTotalPages(totalPages);
        console.log('✅ 操作日志加载成功:', { total, count: logs.length });
      } else {
        console.warn('⚠️ API返回数据格式异常:', data);
        setLogs([]);
        setTotalLogs(0);
        setTotalPages(1);
        throw new Error(data.message || '获取操作日志失败');
      }
    } catch (error) {
      console.error('❌ 获取操作日志失败:', error);
      toast({
        title: '获取操作日志失败',
        description: error instanceof Error ? error.message : '发生错误，请稍后重试',
        variant: 'destructive',
      });
    } finally {
      setIsLoading(false);
    }
  };

  // 处理搜索参数变更
  const handleSearchParamChange = (key: keyof LogSearchParams, value: any) => {
    setSearchParams(prev => ({ ...prev, [key]: value, page: 1 }));
  };

  // 处理页码变更
  const handlePageChange = (page: number) => {
    setSearchParams(prev => ({ ...prev, page }));
  };

  // 重置搜索
  const resetSearch = () => {
    setSearchParams({
      page: 1,
      pageSize: 20
    });
  };

  // 导出日志
  const handleExportLogs = () => {
    toast({
      title: '导出功能',
      description: '日志导出功能正在开发中',
    });
  };

  // 获取结果徽章
  const getResultBadge = (result: string) => {
    switch (result) {
      case 'success':
        return (
          <Badge variant="outline" className="bg-green-50 text-green-700 border-green-200">
            <CheckCircle className="w-3 h-3 mr-1" />
            成功
          </Badge>
        );
      case 'failure':
        return (
          <Badge variant="outline" className="bg-red-50 text-red-700 border-red-200">
            <XCircle className="w-3 h-3 mr-1" />
            失败
          </Badge>
        );
      default:
        return <Badge variant="outline">未知</Badge>;
    }
  };

  // 格式化时间
  const formatTime = (timestamp: string) => {
    const date = new Date(timestamp);
    return {
      date: date.toLocaleDateString(),
      time: date.toLocaleTimeString()
    };
  };

  useEffect(() => {
    fetchLogs();
  }, [searchParams]);

  return (
    <SuperAdminLayout>
      <div className="space-y-6">
        <div className="flex justify-between items-center">
          <div>
            <h1 className="text-3xl font-bold tracking-tight">操作日志</h1>
            <p className="text-muted-foreground">
              查看系统中所有管理员的操作记录和审计日志
            </p>
          </div>
          <div className="flex space-x-2">
            <Button variant="outline" onClick={handleExportLogs}>
              <Download className="mr-2 h-4 w-4" />
              导出日志
            </Button>
            <Button onClick={() => fetchLogs()}>
              <RefreshCw className="mr-2 h-4 w-4" />
              刷新
            </Button>
          </div>
        </div>

        {/* 搜索和筛选 */}
        <Card>
          <CardHeader>
            <CardTitle>筛选条件</CardTitle>
            <CardDescription>
              根据管理员、操作类型或结果筛选日志
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
              <div className="space-y-2">
                <Label htmlFor="admin-filter">管理员</Label>
                <Select
                  value={searchParams.adminId || ''}
                  onValueChange={(value) => handleSearchParamChange('adminId', value)}
                >
                  <SelectTrigger id="admin-filter">
                    <SelectValue placeholder="选择管理员" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="">全部管理员</SelectItem>
                    <SelectItem value="1">超级管理员</SelectItem>
                    <SelectItem value="2">管理员</SelectItem>
                    <SelectItem value="3">审核员</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div className="space-y-2">
                <Label htmlFor="action-filter">操作类型</Label>
                <Select
                  value={searchParams.action || ''}
                  onValueChange={(value) => handleSearchParamChange('action', value)}
                >
                  <SelectTrigger id="action-filter">
                    <SelectValue placeholder="选择操作类型" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="">全部操作</SelectItem>
                    <SelectItem value="创建">创建操作</SelectItem>
                    <SelectItem value="更新">更新操作</SelectItem>
                    <SelectItem value="删除">删除操作</SelectItem>
                    <SelectItem value="登录">登录操作</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div className="space-y-2">
                <Label htmlFor="result-filter">操作结果</Label>
                <Select
                  value={searchParams.result || ''}
                  onValueChange={(value) => handleSearchParamChange('result', value)}
                >
                  <SelectTrigger id="result-filter">
                    <SelectValue placeholder="选择结果" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="">全部结果</SelectItem>
                    <SelectItem value="success">成功</SelectItem>
                    <SelectItem value="failure">失败</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div className="flex items-end space-x-2">
                <Button variant="outline" onClick={resetSearch}>
                  重置
                </Button>
                <Button onClick={() => fetchLogs()}>
                  <Search className="mr-2 h-4 w-4" />
                  搜索
                </Button>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* 日志列表 */}
        <Card>
          <CardHeader>
            <div className="flex justify-between items-center">
              <div>
                <CardTitle>操作记录</CardTitle>
                <CardDescription>
                  共找到 {totalLogs} 条操作记录
                </CardDescription>
              </div>
            </div>
          </CardHeader>
          <CardContent>
            <div className="rounded-md border">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>时间</TableHead>
                    <TableHead>管理员</TableHead>
                    <TableHead>操作</TableHead>
                    <TableHead>目标</TableHead>
                    <TableHead>详情</TableHead>
                    <TableHead>IP地址</TableHead>
                    <TableHead>结果</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {isLoading ? (
                    <TableRow>
                      <TableCell colSpan={7} className="text-center py-8">
                        <div className="flex justify-center items-center">
                          <RefreshCw className="h-6 w-6 animate-spin mr-2" />
                          <span>加载中...</span>
                        </div>
                      </TableCell>
                    </TableRow>
                  ) : logs.length === 0 ? (
                    <TableRow>
                      <TableCell colSpan={7} className="text-center py-8">
                        暂无操作记录
                      </TableCell>
                    </TableRow>
                  ) : (
                    logs.map((log) => {
                      const timeInfo = formatTime(log.timestamp);
                      return (
                        <TableRow key={log.id}>
                          <TableCell>
                            <div className="text-sm">
                              <div>{timeInfo.date}</div>
                              <div className="text-muted-foreground">{timeInfo.time}</div>
                            </div>
                          </TableCell>
                          <TableCell>
                            <div className="flex items-center">
                              <User className="h-4 w-4 mr-2 text-muted-foreground" />
                              {log.adminName}
                            </div>
                          </TableCell>
                          <TableCell>
                            <Badge variant="secondary">
                              <Activity className="h-3 w-3 mr-1" />
                              {log.action}
                            </Badge>
                          </TableCell>
                          <TableCell>
                            <code className="text-xs bg-gray-100 px-2 py-1 rounded">
                              {log.target}
                            </code>
                          </TableCell>
                          <TableCell className="max-w-xs">
                            <div className="truncate" title={log.details}>
                              {log.details}
                            </div>
                          </TableCell>
                          <TableCell>
                            <span className="text-sm text-muted-foreground">
                              {log.ipAddress}
                            </span>
                          </TableCell>
                          <TableCell>
                            {getResultBadge(log.result)}
                          </TableCell>
                        </TableRow>
                      );
                    })
                  )}
                </TableBody>
              </Table>
            </div>

            {/* 分页 */}
            {totalPages > 1 && (
              <div className="mt-4 flex justify-center">
                <Pagination>
                  <PaginationContent>
                    <PaginationItem>
                      <PaginationPrevious
                        onClick={() => handlePageChange(Math.max(1, searchParams.page! - 1))}
                        disabled={searchParams.page === 1}
                      />
                    </PaginationItem>

                    {Array.from({ length: totalPages }, (_, i) => i + 1).map((page) => (
                      <PaginationItem key={page}>
                        <PaginationLink
                          onClick={() => handlePageChange(page)}
                          isActive={page === searchParams.page}
                        >
                          {page}
                        </PaginationLink>
                      </PaginationItem>
                    ))}

                    <PaginationItem>
                      <PaginationNext
                        onClick={() => handlePageChange(Math.min(totalPages, searchParams.page! + 1))}
                        disabled={searchParams.page === totalPages}
                      />
                    </PaginationItem>
                  </PaginationContent>
                </Pagination>
              </div>
            )}
          </CardContent>
        </Card>
      </div>
    </SuperAdminLayout>
  );
};

export default OperationLogsPage;
