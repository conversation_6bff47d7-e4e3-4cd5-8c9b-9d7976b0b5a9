/**
 * 审核员管理服务
 *
 * 提供审核员管理相关的 API 调用
 */

import { fetchData } from './unifiedDataService';

// 审核员类型
export interface Reviewer {
  id: string;
  username: string;
  email: string;
  name: string;
  status: 'active' | 'inactive' | 'suspended';
  createdAt: string;
  lastLoginAt?: string;
  lastLoginIp?: string;
  reviewCount: number;
  approvedCount: number;
  rejectedCount: number;
  pendingCount: number;
  suspendedUntil?: string;
}

// 审核记录类型
export interface ReviewRecord {
  id: string;
  contentId: string;
  contentType: 'story' | 'questionnaire' | 'comment';
  contentTitle?: string;
  contentSummary: string;
  decision: 'approved' | 'rejected' | 'pending';
  reviewedAt: string;
  reviewDuration: number; // 单位：秒
  tags?: string[];
}

// 审核员搜索参数
export interface ReviewerSearchParams {
  email?: string;
  username?: string;
  status?: string;
  page?: number;
  limit?: number;
}

// 创建审核员参数
export interface CreateReviewerParams {
  email: string;
  username: string;
  name: string;
  password: string;
}

// 停用审核员参数
export interface SuspendReviewerParams {
  id: string;
  duration: '1day' | '1week' | '1month' | 'custom';
  customDays?: number;
  reason?: string;
}

// 模拟审核员数据
const mockReviewers: Reviewer[] = [
  {
    id: '1',
    username: 'reviewer1',
    email: '<EMAIL>',
    name: '审核员一',
    status: 'active',
    createdAt: '2023-01-10T08:30:00Z',
    lastLoginAt: '2023-05-22T14:30:00Z',
    lastLoginIp: '*************',
    reviewCount: 120,
    approvedCount: 95,
    rejectedCount: 20,
    pendingCount: 5
  },
  {
    id: '2',
    username: 'reviewer2',
    email: '<EMAIL>',
    name: '审核员二',
    status: 'active',
    createdAt: '2023-02-15T10:45:00Z',
    lastLoginAt: '2023-05-21T09:15:00Z',
    lastLoginIp: '*************',
    reviewCount: 85,
    approvedCount: 70,
    rejectedCount: 10,
    pendingCount: 5
  },
  {
    id: '3',
    username: 'reviewer3',
    email: '<EMAIL>',
    name: '审核员三',
    status: 'suspended',
    createdAt: '2023-03-05T09:20:00Z',
    lastLoginAt: '2023-05-15T11:30:00Z',
    lastLoginIp: '*************',
    reviewCount: 50,
    approvedCount: 40,
    rejectedCount: 8,
    pendingCount: 2,
    suspendedUntil: '2023-06-15T00:00:00Z'
  },
  {
    id: '4',
    username: 'reviewer4',
    email: '<EMAIL>',
    name: '审核员四',
    status: 'inactive',
    createdAt: '2023-01-20T14:10:00Z',
    lastLoginAt: '2023-04-10T16:45:00Z',
    lastLoginIp: '*************',
    reviewCount: 30,
    approvedCount: 25,
    rejectedCount: 5,
    pendingCount: 0
  }
];

// 模拟审核记录数据
const mockReviewRecords: Record<string, ReviewRecord[]> = {
  '1': [
    {
      id: '101',
      contentId: 'story-101',
      contentType: 'story',
      contentTitle: '我的大学经历',
      contentSummary: '大学四年，我参加了很多社团活动，收获了宝贵的经验...',
      decision: 'approved',
      reviewedAt: '2023-05-20T10:15:00Z',
      reviewDuration: 120,
      tags: ['大学生活', '社团活动']
    },
    {
      id: '102',
      contentId: 'questionnaire-102',
      contentType: 'questionnaire',
      contentSummary: '问卷回复内容，包含多个问题的答案...',
      decision: 'approved',
      reviewedAt: '2023-05-20T11:30:00Z',
      reviewDuration: 90
    },
    {
      id: '103',
      contentId: 'story-103',
      contentType: 'story',
      contentTitle: '实习经历分享',
      contentSummary: '在某科技公司实习的三个月里，我学到了很多实用技能...',
      decision: 'rejected',
      reviewedAt: '2023-05-21T09:45:00Z',
      reviewDuration: 150,
      tags: ['实习', '职场经验']
    }
  ],
  '2': [
    {
      id: '201',
      contentId: 'story-201',
      contentType: 'story',
      contentTitle: '毕业旅行',
      contentSummary: '毕业前和同学一起去了云南旅行，看到了美丽的风景...',
      decision: 'approved',
      reviewedAt: '2023-05-18T14:20:00Z',
      reviewDuration: 100,
      tags: ['旅行', '毕业']
    },
    {
      id: '202',
      contentId: 'comment-202',
      contentType: 'comment',
      contentSummary: '这个故事很有启发性，谢谢分享！',
      decision: 'approved',
      reviewedAt: '2023-05-19T10:10:00Z',
      reviewDuration: 30
    }
  ]
};

/**
 * 获取审核员列表
 * @param params 搜索参数
 * @returns 审核员列表
 */
export async function getReviewers(params: ReviewerSearchParams) {
  return fetchData(
    '/admin/reviewers',
    'getReviewersMock',
    params,
    { method: 'GET' }
  );
}

/**
 * 获取审核员详情
 * @param id 审核员ID
 * @returns 审核员详情
 */
export async function getReviewerDetails(id: string) {
  return fetchData(
    `/admin/reviewers/${id}`,
    'getReviewerDetailMock',
    { id },
    { method: 'GET' }
  );
}

/**
 * 获取审核员审核记录
 * @param id 审核员ID
 * @returns 审核记录
 */
export async function getReviewerRecords(id: string) {
  return fetchData(
    `/admin/reviewers/${id}/records`,
    'getReviewerRecordsMock',
    { id },
    { method: 'GET' }
  );
}

/**
 * 创建审核员
 * @param params 创建参数
 * @returns 创建结果
 */
export async function createReviewer(params: CreateReviewerParams) {
  return fetchData(
    '/admin/reviewers',
    'addReviewerMock',
    params,
    { method: 'POST' }
  );
}

/**
 * 停用审核员
 * @param params 停用参数
 * @returns 停用结果
 */
export async function suspendReviewer(params: SuspendReviewerParams) {
  return fetchData(
    `/admin/reviewers/${params.id}/suspend`,
    'updateReviewerStatusMock',
    {
      id: params.id,
      status: 'disabled',
      duration: params.duration
    },
    { method: 'POST' }
  );
}

/**
 * 删除审核员
 * @param id 审核员ID
 * @returns 删除结果
 */
export async function deleteReviewer(id: string) {
  return fetchData(
    `/admin/reviewers/${id}`,
    'deleteReviewerMock',
    { id },
    { method: 'DELETE' }
  );
}

/**
 * 重置审核员密码
 * @param id 审核员ID
 * @returns 重置结果
 */
export async function resetReviewerPassword(id: string) {
  return fetchData(
    `/admin/reviewers/${id}/reset-password`,
    'resetReviewerPasswordMock',
    { id },
    { method: 'POST' }
  );
}
