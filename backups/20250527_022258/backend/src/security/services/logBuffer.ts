/**
 * 日志缓冲服务
 *
 * 提供日志批处理和缓冲功能，减少频繁的存储操作
 */

import { LogEntry } from './securityLogger';

/**
 * 缓冲配置
 */
interface BufferConfig {
  enabled: boolean;
  maxSize: number;
  flushInterval: number;
  criticalFlushSize: number;
}

/**
 * 日志缓冲服务类
 */
export class LogBuffer {
  private config: BufferConfig;
  private buffer: LogEntry[] = [];
  private flushTimer: NodeJS.Timeout | null = null;
  private flushCallback: ((logs: LogEntry[]) => Promise<void>) | null = null;
  private isProcessing: boolean = false;
  private lastFlushTime: number = 0;
  private totalProcessed: number = 0;
  private totalBatches: number = 0;
  private totalErrors: number = 0;

  /**
   * 构造函数
   */
  constructor() {
    this.config = {
      enabled: true,
      maxSize: 100,
      flushInterval: 10000, // 10秒
      criticalFlushSize: 50 // 当缓冲区达到50条日志时立即刷新
    };
  }

  /**
   * 初始化服务
   * @param config 缓冲配置
   * @param flushCallback 刷新回调函数
   */
  public initialize(
    config: Partial<BufferConfig>,
    flushCallback: (logs: LogEntry[]) => Promise<void>
  ): void {
    this.config = { ...this.config, ...config };
    this.flushCallback = flushCallback;
    this.startFlushTimer();
  }

  /**
   * 更新配置
   * @param config 缓冲配置
   */
  public updateConfig(config: Partial<BufferConfig>): void {
    const wasEnabled = this.config.enabled;
    this.config = { ...this.config, ...config };

    // 如果启用状态改变，更新定时器
    if (wasEnabled !== this.config.enabled) {
      if (this.config.enabled) {
        this.startFlushTimer();
      } else {
        this.stopFlushTimer();
      }
    }
  }

  /**
   * 添加日志到缓冲区
   * @param log 日志条目
   */
  public async add(log: LogEntry): Promise<void> {
    // 如果缓冲区未启用，直接调用回调函数
    if (!this.config.enabled || !this.flushCallback) {
      try {
        await this.flushCallback?.([log]);
      } catch (error) {
        console.error('Error flushing single log:', error);
        this.totalErrors++;
      }
      return;
    }

    // 添加日志到缓冲区
    this.buffer.push(log);

    // 如果缓冲区达到临界大小，立即刷新
    if (this.buffer.length >= this.config.criticalFlushSize) {
      this.flush(false);
    }
  }

  /**
   * 刷新缓冲区
   * @param force 是否强制刷新（即使缓冲区为空）
   */
  public async flush(force: boolean = false): Promise<void> {
    // 如果正在处理或缓冲区为空，不执行刷新
    if (this.isProcessing || (this.buffer.length === 0 && !force)) {
      return;
    }

    // 如果回调函数未设置，不执行刷新
    if (!this.flushCallback) {
      return;
    }

    try {
      this.isProcessing = true;

      // 获取当前缓冲区的日志
      const logs = [...this.buffer];
      this.buffer = [];

      // 调用回调函数
      await this.flushCallback(logs);

      // 更新统计信息
      this.totalProcessed += logs.length;
      this.totalBatches++;
      this.lastFlushTime = Date.now();
    } catch (error) {
      console.error('Error flushing logs:', error);

      // 如果刷新失败，将日志放回缓冲区
      this.buffer = [...this.buffer, ...this.buffer];

      // 如果缓冲区超过最大大小，丢弃最旧的日志
      if (this.buffer.length > this.config.maxSize) {
        this.buffer = this.buffer.slice(-this.config.maxSize);
      }

      this.totalErrors++;
    } finally {
      this.isProcessing = false;
    }
  }

  /**
   * 启动刷新定时器
   */
  private startFlushTimer(): void {
    // 如果定时器已存在，先停止
    this.stopFlushTimer();

    // 如果缓冲区未启用，不启动定时器
    if (!this.config.enabled) {
      return;
    }

    // 在 Cloudflare Workers 环境中，我们需要避免在全局范围内设置定时器
    // 使用 setTimeout 来延迟设置 interval，确保它在请求处理程序中执行
    const startInterval = () => {
      this.flushTimer = setInterval(() => {
        this.flush(false);
      }, this.config.flushInterval);
    };

    // 检查是否在 Cloudflare Workers 环境中
    if (typeof addEventListener !== 'undefined') {
      // 延迟启动定时器
      setTimeout(startInterval, 0);
    } else {
      // 在 Node.js 环境中直接启动
      startInterval();
    }
  }

  /**
   * 停止刷新定时器
   */
  private stopFlushTimer(): void {
    if (this.flushTimer) {
      clearInterval(this.flushTimer);
      this.flushTimer = null;
    }
  }

  /**
   * 获取统计信息
   */
  public getStats(): {
    bufferSize: number;
    totalProcessed: number;
    totalBatches: number;
    totalErrors: number;
    lastFlushTime: number;
    isProcessing: boolean;
  } {
    return {
      bufferSize: this.buffer.length,
      totalProcessed: this.totalProcessed,
      totalBatches: this.totalBatches,
      totalErrors: this.totalErrors,
      lastFlushTime: this.lastFlushTime,
      isProcessing: this.isProcessing
    };
  }

  /**
   * 关闭缓冲区
   */
  public async close(): Promise<void> {
    // 停止定时器
    this.stopFlushTimer();

    // 刷新剩余的日志
    await this.flush(true);
  }
}
