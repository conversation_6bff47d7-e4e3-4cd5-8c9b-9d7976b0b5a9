/**
 * 常见的CORS配置错误示例
 * ⚠️ 这些都是错误的配置，仅用于学习和对比
 */

import { Hono } from 'hono';
import { cors } from 'hono/cors';

const app = new Hono();

// ❌ 错误1: 返回布尔值而不是域名
app.use('*', cors({
  origin: (origin) => {
    const allowedDomains = ['https://app.domain.com'];
    
    if (allowedDomains.includes(origin)) {
      return true;  // ❌ 这会导致 access-control-allow-origin: true
    }
    return false;
  }
}));

// ❌ 错误2: 使用通配符与credentials同时使用
app.use('*', cors({
  origin: '*',        // ❌ 通配符
  credentials: true   // ❌ 不能与通配符同时使用
}));

// ❌ 错误3: 忘记处理无origin的请求
app.use('*', cors({
  origin: (origin) => {
    // ❌ 没有处理 origin 为 undefined 的情况
    const allowedDomains = ['https://app.domain.com'];
    return allowedDomains.includes(origin) ? origin : false;
  }
}));

// ❌ 错误4: 在开发环境使用过于宽松的配置
app.use('*', cors({
  origin: true,  // ❌ 允许所有域名，生产环境很危险
  credentials: false  // ❌ 禁用凭证，可能导致认证问题
}));

// ❌ 错误5: 返回数组而不是字符串
app.use('*', cors({
  origin: (origin) => {
    const allowedDomains = ['https://app.domain.com'];
    
    if (allowedDomains.includes(origin)) {
      return allowedDomains;  // ❌ 返回数组而不是字符串
    }
    return false;
  }
}));

// ❌ 错误6: 不正确的域名格式
app.use('*', cors({
  origin: [
    'app.domain.com',           // ❌ 缺少协议
    'https://app.domain.com/',  // ❌ 多余的斜杠
    'https://app.domain.com:443' // ❌ HTTPS默认端口不需要指定
  ]
}));

// ❌ 错误7: 缺少必要的请求头
app.use('*', cors({
  origin: 'https://app.domain.com',
  allowHeaders: ['Content-Type']  // ❌ 缺少Authorization等常用头
}));

// ❌ 错误8: 不正确的方法配置
app.use('*', cors({
  origin: 'https://app.domain.com',
  allowMethods: ['GET', 'POST']  // ❌ 缺少OPTIONS，可能导致预检失败
}));

// ❌ 错误9: 环境配置泄露
const isDevelopment = true;  // ❌ 硬编码环境判断

app.use('*', cors({
  origin: isDevelopment ? true : 'https://app.domain.com'  // ❌ 可能泄露到生产
}));

// ❌ 错误10: 不正确的正则表达式
app.use('*', cors({
  origin: (origin) => {
    // ❌ 过于宽松的正则，可能匹配恶意域名
    if (origin.match(/\.domain\.com$/)) {
      return origin;
    }
    return false;
  }
}));

/**
 * 这些错误的后果:
 * 
 * 错误1: 浏览器无法识别 "true" 作为有效Origin
 * 错误2: 浏览器会拒绝请求
 * 错误3: Postman等工具请求可能失败
 * 错误4: 安全风险，任何网站都能访问API
 * 错误5: CORS中间件无法处理数组返回值
 * 错误6: 域名格式不匹配，请求被拒绝
 * 错误7: 带Authorization头的请求被拒绝
 * 错误8: 预检请求失败
 * 错误9: 生产环境可能允许所有域名
 * 错误10: 恶意网站可能绕过CORS限制
 */

/**
 * 正确的修复方法:
 */

// ✅ 正确的配置
app.use('*', cors({
  origin: (origin) => {
    // 处理无origin请求
    if (!origin) return origin;
    
    // 明确的域名列表
    const allowedDomains = [
      'http://localhost:3000',
      'https://app.domain.com'
    ];
    
    // 返回具体域名
    if (allowedDomains.includes(origin)) {
      return origin;  // ✅ 返回字符串
    }
    
    // 安全的正则匹配
    if (origin.match(/^https:\/\/[a-f0-9]{8}\.project\.pages\.dev$/)) {
      return origin;
    }
    
    return false;
  },
  
  // 完整的方法列表
  allowMethods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
  
  // 完整的请求头列表
  allowHeaders: [
    'Content-Type',
    'Authorization',
    'X-Requested-With',
    'Accept',
    'Origin'
  ],
  
  // 启用凭证
  credentials: true,
  
  // 合理的缓存时间
  maxAge: 86400
}));

export default app;
