const express = require('express');
const cors = require('cors');

const app = express();
const PORT = 8788;

// Middleware
app.use(cors({
  origin: [
    'http://localhost:5173',
    'http://localhost:5174',
    'http://localhost:5175',
    'http://localhost:5176',
    'http://localhost:5177'
  ],
  credentials: true
}));
app.use(express.json());

// Admin login endpoint
app.post('/api/admin/login', (req, res) => {
  const { username, password } = req.body;

  console.log(`🔐 管理员登录尝试: ${username}`);

  // Define admin users with different roles
  const adminUsers = [
    {
      username: 'admin1',
      password: 'admin123',
      role: 'admin',
      name: '管理员',
      id: 1,
      permissions: ['content_review', 'user_management', 'data_analysis']
    },
    {
      username: 'reviewer1',
      password: 'admin123',
      role: 'reviewer',
      name: '审核员',
      id: 2,
      permissions: ['content_review']
    },
    {
      username: 'superadmin',
      password: 'admin123',
      role: 'superadmin',
      name: '超级管理员',
      id: 3,
      permissions: ['content_review', 'user_management', 'data_analysis', 'system_config', 'security_management']
    }
  ];

  // Find matching user
  const user = adminUsers.find(u => u.username === username && u.password === password);

  if (user) {
    // Generate simple token
    const token = `token_${user.role}_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;

    console.log(`✅ 登录成功: ${user.name} (${user.role})`);

    return res.json({
      success: true,
      data: {
        token,
        user: {
          id: user.id,
          username: user.username,
          name: user.name,
          role: user.role,
          permissions: user.permissions
        }
      },
      message: '登录成功'
    });
  }

  console.log(`❌ 登录失败: 用户名或密码错误`);
  return res.status(401).json({ 
    success: false, 
    error: '用户名或密码错误' 
  });
});

// Health check
app.get('/health', (req, res) => {
  res.json({ 
    status: 'ok', 
    environment: 'development',
    timestamp: new Date().toISOString(),
    service: 'admin-login-server'
  });
});

// 404 handler
app.use('*', (req, res) => {
  res.status(404).json({
    success: false,
    error: 'API端点未找到',
    path: req.originalUrl,
    method: req.method,
    availableEndpoints: [
      'GET /health',
      'POST /api/admin/login'
    ]
  });
});

// Error handler
app.use((err, req, res, next) => {
  console.error(`${req.method} ${req.url}`, err);
  res.status(500).json({ 
    success: false,
    error: '服务器内部错误' 
  });
});

// Start server
app.listen(PORT, () => {
  console.log(`🚀 管理员登录服务器运行在 http://localhost:${PORT}`);
  console.log(`📋 可用端点:`);
  console.log(`   GET  /health - 健康检查`);
  console.log(`   POST /api/admin/login - 管理员登录`);
  console.log(`🔐 测试账号:`);
  console.log(`   管理员: admin1/admin123`);
  console.log(`   审核员: reviewer1/admin123`);
  console.log(`   超级管理员: superadmin/admin123`);
});
