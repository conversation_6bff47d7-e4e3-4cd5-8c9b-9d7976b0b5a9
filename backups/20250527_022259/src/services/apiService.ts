/**
 * API服务
 *
 * 统一的API调用服务，支持真实API和模拟数据切换
 */

import { ApiResponse, QuestionnaireResponse, Story, User } from '../types/api';
import { apiClient } from './apiClient';

// 强制使用真实API，不再支持模拟数据
const USE_MOCK = false;

/**
 * API服务类
 */
export class ApiService {

  /**
   * 提交问卷回复
   */
  static async submitQuestionnaire(data: Partial<QuestionnaireResponse>): Promise<ApiResponse<QuestionnaireResponse>> {
    try {
      const response = await apiClient.post('/api/questionnaires', data);
      return response.data;
    } catch (error) {
      console.error('提交问卷失败:', error);
      throw error;
    }
  }

  /**
   * 获取问卷统计数据
   */
  static async getQuestionnaireStats(): Promise<any> {
    try {
      const response = await apiClient.get('/api/questionnaire/stats');
      return response.data;
    } catch (error) {
      console.error('获取问卷统计失败:', error);
      throw error;
    }
  }

  /**
   * 获取问卷回复列表
   */
  static async getQuestionnaires(params?: any): Promise<ApiResponse<QuestionnaireResponse[]>> {
    try {
      const response = await apiClient.get('/api/questionnaire-voices', { params });
      return response.data;
    } catch (error) {
      console.error('获取问卷列表失败:', error);
      throw error;
    }
  }

  /**
   * 获取问卷回复详情
   */
  static async getQuestionnaireById(id: number): Promise<ApiResponse<QuestionnaireResponse>> {
    try {
      const response = await apiClient.get(`/api/questionnaires/${id}`);
      return response.data;
    } catch (error) {
      console.error('获取问卷详情失败:', error);
      throw error;
    }
  }

  /**
   * 提交故事
   */
  static async submitStory(data: Partial<Story>): Promise<ApiResponse<Story>> {
    try {
      const response = await apiClient.post('/api/stories', data);
      return response.data;
    } catch (error) {
      console.error('提交故事失败:', error);
      throw error;
    }
  }

  /**
   * 获取故事列表
   */
  static async getStories(params?: any): Promise<ApiResponse<Story[]>> {
    try {
      const response = await apiClient.get('/api/story/list', { params });
      return response.data;
    } catch (error) {
      console.error('获取故事列表失败:', error);
      throw error;
    }
  }

  /**
   * 获取故事详情
   */
  static async getStoryById(id: number): Promise<ApiResponse<Story>> {
    try {
      const response = await apiClient.get(`/api/stories/${id}`);
      return response.data;
    } catch (error) {
      console.error('获取故事详情失败:', error);
      throw error;
    }
  }

  /**
   * 故事投票
   */
  static async voteStory(id: number, type: 'like' | 'dislike'): Promise<ApiResponse<{ likes: number; dislikes: number }>> {
    try {
      const response = await apiClient.post(`/api/stories/${id}/vote`, { type });
      return response.data;
    } catch (error) {
      console.error('故事投票失败:', error);
      throw error;
    }
  }

  /**
   * 获取用户列表
   */
  static async getUsers(params?: any): Promise<ApiResponse<User[]>> {
    try {
      const response = await apiClient.get('/api/users', { params });
      return response.data;
    } catch (error) {
      console.error('获取用户列表失败:', error);
      throw error;
    }
  }

  /**
   * 创建用户
   */
  static async createUser(userData: Partial<User>): Promise<ApiResponse<User>> {
    try {
      const response = await apiClient.post('/api/users', userData);
      return response.data;
    } catch (error) {
      console.error('创建用户失败:', error);
      throw error;
    }
  }

  /**
   * 更新用户
   */
  static async updateUser(id: number, userData: Partial<User>): Promise<ApiResponse<User>> {
    try {
      const response = await apiClient.put(`/api/users/${id}`, userData);
      return response.data;
    } catch (error) {
      console.error('更新用户失败:', error);
      throw error;
    }
  }

  /**
   * 删除用户
   */
  static async deleteUser(id: number): Promise<ApiResponse<void>> {
    try {
      const response = await apiClient.delete(`/api/users/${id}`);
      return response.data;
    } catch (error) {
      console.error('删除用户失败:', error);
      throw error;
    }
  }

  /**
   * 获取可视化数据
   */
  static async getVisualizationData(): Promise<any> {
    try {
      const response = await apiClient.get('/api/visualization/data');
      return response.data;
    } catch (error) {
      console.error('获取可视化数据失败:', error);
      throw error;
    }
  }

  /**
   * 用户登录
   */
  static async login(credentials: { email: string; password: string }): Promise<ApiResponse<{ token: string; user: User }>> {
    try {
      const response = await apiClient.post('/api/auth/login', credentials);
      return response.data;
    } catch (error) {
      console.error('登录失败:', error);
      throw error;
    }
  }

  /**
   * 用户注册
   */
  static async register(userData: { email: string; password: string; username: string; name: string }): Promise<ApiResponse<User>> {
    try {
      const response = await apiClient.post('/api/auth/register', userData);
      return response.data;
    } catch (error) {
      console.error('注册失败:', error);
      throw error;
    }
  }

  /**
   * 获取当前用户信息
   */
  static async getCurrentUser(): Promise<ApiResponse<User>> {
    try {
      const response = await apiClient.get('/api/auth/me');
      return response.data;
    } catch (error) {
      console.error('获取当前用户失败:', error);
      throw error;
    }
  }
}

export const apiService = ApiService;
