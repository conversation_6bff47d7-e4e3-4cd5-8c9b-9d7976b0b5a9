import React, { useState, useEffect } from 'react';
import { Card, Button, Alert, Spin, Space, Typography, Divider, Statistic, Row, Col } from 'antd';
import { DatabaseOutlined, ImportOutlined, DeleteOutlined, ReloadOutlined } from '@ant-design/icons';
import { useApi } from '../../hooks/useApi';
import { useNotification } from '../../hooks/useNotification';

const { Title, Paragraph, Text } = Typography;

interface TestDataStatus {
  userCount: number;
  pendingContentCount: number;
  questionnaireCount: number;
  storyCount: number;
  hasTestData: boolean;
}

const TestDataManagement: React.FC = () => {
  const [loading, setLoading] = useState(false);
  const [importing, setImporting] = useState(false);
  const [clearing, setClearing] = useState(false);
  const [status, setStatus] = useState<TestDataStatus | null>(null);
  const [error, setError] = useState<string | null>(null);
  const { api } = useApi();
  const { showSuccess, showError } = useNotification();

  const fetchStatus = async () => {
    setLoading(true);
    setError(null);
    try {
      const response = await api.get('/admin/test-data/status');
      if (response.data.success) {
        setStatus(response.data.data);
      } else {
        setError(response.data.error || '获取测试数据状态失败');
      }
    } catch (err) {
      console.error('获取测试数据状态错误:', err);
      setError('获取测试数据状态时发生错误');
    } finally {
      setLoading(false);
    }
  };

  const importTestData = async () => {
    setImporting(true);
    setError(null);
    try {
      const response = await api.post('/admin/test-data/import');
      if (response.data.success) {
        showSuccess('测试数据导入成功');
        fetchStatus();
      } else {
        setError(response.data.error || '导入测试数据失败');
        showError('导入测试数据失败');
      }
    } catch (err) {
      console.error('导入测试数据错误:', err);
      setError('导入测试数据时发生错误');
      showError('导入测试数据时发生错误');
    } finally {
      setImporting(false);
    }
  };

  const clearTestData = async () => {
    setClearing(true);
    setError(null);
    try {
      const response = await api.post('/admin/test-data/clear');
      if (response.data.success) {
        showSuccess('测试数据清除成功');
        fetchStatus();
      } else {
        setError(response.data.error || '清除测试数据失败');
        showError('清除测试数据失败');
      }
    } catch (err) {
      console.error('清除测试数据错误:', err);
      setError('清除测试数据时发生错误');
      showError('清除测试数据时发生错误');
    } finally {
      setClearing(false);
    }
  };

  useEffect(() => {
    fetchStatus();
  }, []);

  return (
    <Card
      title={
        <Space>
          <DatabaseOutlined />
          <span>测试数据管理</span>
        </Space>
      }
      extra={
        <Button
          icon={<ReloadOutlined />}
          onClick={fetchStatus}
          loading={loading}
          size="small"
        >
          刷新
        </Button>
      }
    >
      {error && <Alert message={error} type="error" showIcon style={{ marginBottom: 16 }} />}

      <Spin spinning={loading}>
        <Typography>
          <Title level={4}>测试数据状态</Title>
          <Paragraph>
            测试数据用于系统功能测试和演示，包括用户、问卷和故事数据。这些数据会被标记为测试数据，可以随时清除。
          </Paragraph>

          {status && (
            <>
              <Row gutter={16}>
                <Col span={6}>
                  <Statistic title="用户数量" value={status.userCount} />
                </Col>
                <Col span={6}>
                  <Statistic title="待审核内容" value={status.pendingContentCount} />
                </Col>
                <Col span={6}>
                  <Statistic title="问卷数量" value={status.questionnaireCount} />
                </Col>
                <Col span={6}>
                  <Statistic title="故事数量" value={status.storyCount} />
                </Col>
              </Row>

              <Divider />

              <Space direction="vertical" style={{ width: '100%' }}>
                <Text>
                  系统中{status.hasTestData ? '已存在' : '不存在'}测试数据。
                </Text>

                <Space>
                  <Button
                    type="primary"
                    icon={<ImportOutlined />}
                    onClick={importTestData}
                    loading={importing}
                    disabled={loading}
                  >
                    导入测试数据
                  </Button>
                  <Button
                    danger
                    icon={<DeleteOutlined />}
                    onClick={clearTestData}
                    loading={clearing}
                    disabled={loading || !status.hasTestData}
                  >
                    清除测试数据
                  </Button>
                </Space>
              </Space>
            </>
          )}
        </Typography>
      </Spin>
    </Card>
  );
};

export default TestDataManagement;
