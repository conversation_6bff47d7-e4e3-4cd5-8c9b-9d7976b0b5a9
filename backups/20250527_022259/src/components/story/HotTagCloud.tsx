import { useState, useEffect } from 'react';
import { Badge } from '@/components/ui/badge';
import { Ta<PERSON>, TabsContent, Ta<PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { TrendingUp, Tag as TagIcon } from 'lucide-react';
import { cn } from '@/lib/utils';
import { STORY_TAGS, TAG_CATEGORY_LABELS, TAG_CATEGORY_COLORS, type TagWithCount } from '@/constants/storyConstants';

interface HotTagCloudProps {
  selectedTags: string[];
  onTagSelect: (tagId: string) => void;
  className?: string;
  maxTags?: number;
  showCounts?: boolean;
  showCategories?: boolean;
}

export default function HotTagCloud({
  selectedTags,
  onTagSelect,
  className,
  maxTags = 30,
  showCounts = true,
  showCategories = true,
}: HotTagCloudProps) {
  const [displayTags, setDisplayTags] = useState<Array<TagWithCount & { size: number }>>([]);
  const [activeCategory, setActiveCategory] = useState('all');
  const [loading, setLoading] = useState(true);

  // 获取热门标签数据（使用与数据库结构一致的模拟数据）
  const fetchHotTags = async () => {
    try {
      setLoading(true);

      // 模拟API调用延迟
      await new Promise(resolve => setTimeout(resolve, 300));

      // 使用与生成的数据库结构一致的标签数据
      // 这些数据来自 backend/scripts/init-tag-data.js 生成的标签
      const mockHotTags: TagWithCount[] = [
        { ...STORY_TAGS.find(t => t.id === 'job-hunting')!, count: 156 },
        { ...STORY_TAGS.find(t => t.id === 'interview')!, count: 134 },
        { ...STORY_TAGS.find(t => t.id === 'internship')!, count: 98 },
        { ...STORY_TAGS.find(t => t.id === 'career-change')!, count: 87 },
        { ...STORY_TAGS.find(t => t.id === 'salary')!, count: 76 },
        { ...STORY_TAGS.find(t => t.id === 'bachelor')!, count: 65 },
        { ...STORY_TAGS.find(t => t.id === 'it-industry')!, count: 54 },
        { ...STORY_TAGS.find(t => t.id === 'resume')!, count: 43 },
        { ...STORY_TAGS.find(t => t.id === 'master')!, count: 38 },
        { ...STORY_TAGS.find(t => t.id === 'work-life')!, count: 32 },
        { ...STORY_TAGS.find(t => t.id === 'advice')!, count: 29 },
        { ...STORY_TAGS.find(t => t.id === 'finance')!, count: 25 },
        { ...STORY_TAGS.find(t => t.id === 'overseas')!, count: 22 },
        { ...STORY_TAGS.find(t => t.id === 'startup')!, count: 18 },
        { ...STORY_TAGS.find(t => t.id === 'remote-work')!, count: 15 },
        { ...STORY_TAGS.find(t => t.id === 'success')!, count: 12 },
        { ...STORY_TAGS.find(t => t.id === 'challenge')!, count: 10 },
        { ...STORY_TAGS.find(t => t.id === 'freelance')!, count: 8 },
      ].filter(tag => tag.id); // 过滤掉undefined的标签

      console.log('HotTagCloud: 使用与数据库结构一致的标签数据');
      setDisplayTags(mockHotTags.map(tag => ({ ...tag, size: 1 })));

    } catch (error) {
      console.error('获取热门标签失败:', error);

      // 错误时使用基础标签
      const fallbackTags: TagWithCount[] = [
        { ...STORY_TAGS.find(t => t.id === 'job-hunting')!, count: 100 },
        { ...STORY_TAGS.find(t => t.id === 'interview')!, count: 80 },
        { ...STORY_TAGS.find(t => t.id === 'internship')!, count: 60 },
        { ...STORY_TAGS.find(t => t.id === 'career-change')!, count: 40 },
        { ...STORY_TAGS.find(t => t.id === 'salary')!, count: 20 },
      ].filter(tag => tag.id);

      setDisplayTags(fallbackTags.map(tag => ({ ...tag, size: 1 })));
    } finally {
      setLoading(false);
    }
  };

  // 计算标签大小
  const calculateTagSizes = (tags: TagWithCount[]) => {
    if (tags.length === 0) return [];

    const maxCount = Math.max(...tags.map(t => t.count));
    const minCount = Math.min(...tags.map(t => t.count));

    return tags.map(tag => {
      let size = 1;
      if (maxCount !== minCount) {
        // 将计数映射到 1-5 的范围
        size = 1 + Math.floor(((tag.count - minCount) / (maxCount - minCount)) * 4);
      }
      return {
        ...tag,
        size,
      };
    });
  };

  // 根据分类筛选标签
  const getFilteredTags = () => {
    let filteredTags = displayTags;

    if (activeCategory !== 'all') {
      filteredTags = displayTags.filter(tag => tag.category === activeCategory);
    }

    return filteredTags.slice(0, maxTags);
  };

  // 获取标签颜色
  const getTagColor = (tag: TagWithCount) => {
    if (tag.color) return tag.color;
    if (tag.category) return TAG_CATEGORY_COLORS[tag.category] || 'gray';
    return 'gray';
  };

  // 获取标签颜色类名
  const getTagColorClass = (tag: TagWithCount, isSelected: boolean) => {
    if (isSelected) return '';

    const color = getTagColor(tag);

    switch (color) {
      case 'blue':
        return 'bg-blue-50 text-blue-700 border-blue-200 hover:bg-blue-100';
      case 'green':
        return 'bg-green-50 text-green-700 border-green-200 hover:bg-green-100';
      case 'purple':
        return 'bg-purple-50 text-purple-700 border-purple-200 hover:bg-purple-100';
      case 'yellow':
        return 'bg-yellow-50 text-yellow-700 border-yellow-200 hover:bg-yellow-100';
      case 'gray':
      default:
        return 'bg-gray-50 text-gray-700 border-gray-200 hover:bg-gray-100';
    }
  };

  // 获取标签大小类名
  const getTagSizeClass = (size: number) => {
    switch (size) {
      case 1:
        return 'text-xs';
      case 2:
        return 'text-sm';
      case 3:
        return 'text-base';
      case 4:
        return 'text-lg';
      case 5:
        return 'text-xl';
      default:
        return 'text-sm';
    }
  };

  // 提取所有分类
  const categories = ['all', ...new Set(displayTags.filter(t => t.category).map(t => t.category))];

  useEffect(() => {
    fetchHotTags();
  }, []);

  useEffect(() => {
    if (displayTags.length > 0) {
      const tagsWithSizes = calculateTagSizes(displayTags);
      setDisplayTags(tagsWithSizes);
    }
  }, [displayTags.length]);

  if (loading) {
    return (
      <Card className={className}>
        <CardHeader>
          <CardTitle className="text-lg flex items-center gap-2">
            <TrendingUp className="h-5 w-5" />
            热门标签
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex items-center justify-center py-8">
            <div className="flex items-center gap-2 text-gray-500">
              <div className="h-4 w-4 border-2 border-blue-500 border-t-transparent rounded-full animate-spin"></div>
              <span>加载热门标签中...</span>
            </div>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className={className}>
      <CardHeader>
        <CardTitle className="text-lg flex items-center gap-2">
          <TrendingUp className="h-5 w-5" />
          热门标签
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        {/* 分类选项卡 */}
        {showCategories && categories.length > 1 && (
          <Tabs value={activeCategory} onValueChange={setActiveCategory}>
            <TabsList className="w-full flex overflow-x-auto">
              <TabsTrigger value="all">全部</TabsTrigger>
              {categories.filter(c => c !== 'all').map(category => (
                <TabsTrigger key={category} value={category}>
                  {TAG_CATEGORY_LABELS[category] || category}
                </TabsTrigger>
              ))}
            </TabsList>
          </Tabs>
        )}

        {/* 标签云 */}
        <div className="flex flex-wrap gap-2">
          {getFilteredTags().length === 0 ? (
            <div className="w-full flex flex-col items-center justify-center py-8 text-gray-500">
              <TagIcon className="h-8 w-8 mb-2" />
              <p>该分类下暂无热门标签</p>
            </div>
          ) : (
            getFilteredTags().map((tag) => {
              const isSelected = selectedTags.includes(tag.id);

              return (
                <Badge
                  key={tag.id}
                  variant={isSelected ? "default" : "outline"}
                  className={cn(
                    "cursor-pointer transition-all flex items-center gap-1",
                    getTagSizeClass(tag.size),
                    !isSelected && getTagColorClass(tag, isSelected)
                  )}
                  onClick={() => onTagSelect(tag.id)}
                >
                  <TagIcon className="h-3 w-3" />
                  {tag.label}
                  {showCounts && (
                    <span className="text-xs opacity-75">
                      ({tag.count})
                    </span>
                  )}
                </Badge>
              );
            })
          )}
        </div>

        {/* 统计信息 */}
        <div className="text-xs text-gray-500 text-center pt-2 border-t">
          显示 {getFilteredTags().length} 个热门标签
          {activeCategory !== 'all' && ` · ${TAG_CATEGORY_LABELS[activeCategory]}`}
        </div>
      </CardContent>
    </Card>
  );
}
