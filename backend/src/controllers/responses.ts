import { Context } from 'hono';
import { z } from 'zod';
import { zValidator } from '@hono/zod-validator';
import { PrismaClient } from '@prisma/client';
import { adminAuth } from '../middlewares/adminAuth';
import { getPrismaClient } from '../utils/prisma';
import { mockResponses, shouldUseMockData } from '../mock';

// 问卷回复过滤器验证模式
const responseFilterSchema = z.object({
  educationLevel: z.string().optional(),
  region: z.string().optional(),
  graduationYear: z.number().int().optional(),
  startDate: z.string().optional(),
  endDate: z.string().optional(),
  isAnonymous: z.boolean().optional(),
  employmentStatus: z.string().optional(),
  industry: z.string().optional(),
  page: z.number().int().min(1).optional(),
  limit: z.number().int().min(1).max(100).optional(),
});

// 获取所有问卷回复
export const getAllResponses = async (c: Context) => {
  try {
    // 解析查询参数
    const url = new URL(c.req.url);
    const page = parseInt(url.searchParams.get('page') || '1');
    const limit = parseInt(url.searchParams.get('limit') || '20');
    const educationLevel = url.searchParams.get('educationLevel') || undefined;
    const region = url.searchParams.get('region') || undefined;
    const graduationYear = url.searchParams.get('graduationYear') ?
      parseInt(url.searchParams.get('graduationYear')!) : undefined;
    const startDate = url.searchParams.get('startDate') || undefined;
    const endDate = url.searchParams.get('endDate') || undefined;
    const isAnonymous = url.searchParams.get('isAnonymous') ?
      url.searchParams.get('isAnonymous') === 'true' : undefined;
    const employmentStatus = url.searchParams.get('employmentStatus') || undefined;
    const industry = url.searchParams.get('industry') || undefined;

    // 检查是否应该使用模拟数据
    if (shouldUseMockData(c.env)) {
      console.log('Development mode: Returning mock responses');

      // 简单的分页和过滤
      let filteredResponses = [...mockResponses];

      if (educationLevel) {
        filteredResponses = filteredResponses.filter(r => r.educationLevel === educationLevel);
      }

      if (region) {
        filteredResponses = filteredResponses.filter(r => r.region === region);
      }

      if (graduationYear) {
        filteredResponses = filteredResponses.filter(r => r.graduationYear === graduationYear);
      }

      if (isAnonymous !== undefined) {
        filteredResponses = filteredResponses.filter(r => r.isAnonymous === isAnonymous);
      }

      if (employmentStatus) {
        filteredResponses = filteredResponses.filter(r => r.employmentStatus === employmentStatus);
      }

      if (industry) {
        filteredResponses = filteredResponses.filter(r => r.industry === industry);
      }

      if (startDate) {
        const startTimestamp = new Date(startDate).getTime();
        filteredResponses = filteredResponses.filter(r => new Date(r.createdAt).getTime() >= startTimestamp);
      }

      if (endDate) {
        const endTimestamp = new Date(endDate).getTime();
        filteredResponses = filteredResponses.filter(r => new Date(r.createdAt).getTime() <= endTimestamp);
      }

      // 计算总数和分页
      const total = filteredResponses.length;
      const offset = (page - 1) * limit;
      const paginatedResponses = filteredResponses.slice(offset, offset + limit);

      return c.json({
        success: true,
        responses: paginatedResponses,
        pagination: {
          total,
          page,
          limit,
          pages: Math.ceil(total / limit),
        },
      });
    }

    // 使用适配的Prisma客户端
    const prisma = getPrismaClient(c.env);

    // 构建查询条件
    const where: any = {};

    if (educationLevel) {
      where.educationLevel = educationLevel;
    }

    if (region) {
      where.region = region;
    }

    if (graduationYear) {
      where.graduationYear = graduationYear;
    }

    if (startDate || endDate) {
      where.createdAt = {};

      if (startDate) {
        where.createdAt.gte = new Date(startDate);
      }

      if (endDate) {
        where.createdAt.lte = new Date(endDate);
      }
    }

    if (isAnonymous !== undefined) {
      where.isAnonymous = isAnonymous;
    }

    if (employmentStatus) {
      where.employmentStatus = employmentStatus;
    }

    if (industry) {
      where.industry = industry;
    }

    // 获取总数
    const total = await prisma.questionnaireResponse.count({ where });

    // 获取分页数据
    const responses = await prisma.questionnaireResponse.findMany({
      where,
      orderBy: {
        createdAt: 'desc',
      },
      skip: (page - 1) * limit,
      take: limit,
    });

    // 注意：使用模拟客户端时不需要断开连接
    if (typeof prisma.$disconnect === 'function') {
      await prisma.$disconnect();
    }

    return c.json({
      success: true,
      responses,
      pagination: {
        total,
        page,
        limit,
        pages: Math.ceil(total / limit),
      },
    });
  } catch (error) {
    console.error('Error getting responses:', error);
    return c.json({ success: false, error: 'Failed to get responses' }, 500);
  }
};

// 获取单个问卷回复
export const getResponseById = async (c: Context) => {
  const { id } = c.req.param();
  const responseId = parseInt(id);

  try {
    // 检查是否应该使用模拟数据
    if (shouldUseMockData(c.env)) {
      console.log(`Development mode: Returning mock response ${id}`);

      // 查找模拟回复
      const response = mockResponses.find(r => r.id === responseId);

      if (!response) {
        return c.json({ success: false, error: 'Response not found' }, 404);
      }

      return c.json({
        success: true,
        response,
      });
    }

    // 使用适配的Prisma客户端
    const prisma = getPrismaClient(c.env);

    // 获取问卷回复
    const response = await prisma.questionnaireResponse.findUnique({
      where: { id: responseId },
    });

    // 注意：使用模拟客户端时不需要断开连接
    if (typeof prisma.$disconnect === 'function') {
      await prisma.$disconnect();
    }

    if (!response) {
      return c.json({ success: false, error: 'Response not found' }, 404);
    }

    return c.json({
      success: true,
      response,
    });
  } catch (error) {
    console.error('Error getting response:', error);
    return c.json({ success: false, error: 'Failed to get response' }, 500);
  }
};

// 创建并导出路由器
import { Hono } from 'hono';
export const responses = new Hono();

responses.get('/', getAllResponses);
responses.get('/:id', getResponseById);
