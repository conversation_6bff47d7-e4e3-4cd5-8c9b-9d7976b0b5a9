import { Context } from 'hono';
import { z } from 'zod';
import { zValidator } from '@hono/zod-validator';
import { rateLimit } from '../../middlewares/rateLimit';
import { filterSensitiveWords } from '../../utils/sensitiveWordFilter';
import { KVStorageService } from '../../services/kvStorageService';
import config from '../../config';

// Define environment interface
interface Env {
  SURVEY_KV: KVNamespace;
  DB: D1Database;
}

// Define validation schema for story submission
const storySubmitSchema = z.object({
  title: z.string().min(5, { message: '标题至少需要5个字符' }).max(50, { message: '标题最多50个字符' }),
  content: z.string().min(20, { message: '内容至少需要20个字符' }).max(2000, { message: '内容最多2000个字符' }),
  isAnonymous: z.boolean().default(true),
  emailVerificationId: z.string().optional(),
  tags: z.array(z.string()).optional(),
  category: z.string().optional(),
  educationLevel: z.string().optional(),
  industry: z.string().optional(),
});

// Define validation schema for story voting
const storyVoteSchema = z.object({
  storyId: z.number(),
  voteType: z.enum(['like', 'dislike']),
});

// Get story list
export const getStoryList = async (c: Context<{ Bindings: Env }>) => {
  try {
    console.log('📚 获取故事列表 (D1版本)');

    // 获取查询参数
    const page = parseInt(c.req.query('page') || '1');
    const pageSize = parseInt(c.req.query('pageSize') || '6');
    const sortBy = c.req.query('sortBy') || 'latest';
    const tag = c.req.query('tag');
    const category = c.req.query('category');
    const search = c.req.query('search');

    // 计算分页
    const offset = (page - 1) * pageSize;

    // 构建查询
    let query = `
      SELECT *
      FROM stories
      WHERE status = 'approved'
    `;

    const queryParams: any[] = [];

    // 添加筛选条件
    if (tag) {
      query += ` AND tags LIKE ?`;
      queryParams.push(`%"${tag}"%`);
    }

    if (category) {
      query += ` AND category = ?`;
      queryParams.push(category);
    }

    if (search) {
      query += ` AND (title LIKE ? OR content LIKE ?)`;
      queryParams.push(`%${search}%`, `%${search}%`);
    }

    // 添加排序
    if (sortBy === 'latest') {
      query += ` ORDER BY created_at DESC`;
    } else if (sortBy === 'popular') {
      query += ` ORDER BY like_count DESC`;
    }

    // 添加分页
    query += ` LIMIT ? OFFSET ?`;
    queryParams.push(pageSize, offset);

    // 执行查询
    const result = await c.env.DB.prepare(query).bind(...queryParams).all();
    const stories = result.results || [];

    // 获取总数
    let countQuery = `
      SELECT COUNT(*) as count
      FROM stories
      WHERE status = 'approved'
    `;

    const countParams: any[] = [];

    // 添加筛选条件
    if (tag) {
      countQuery += ` AND tags LIKE ?`;
      countParams.push(`%"${tag}"%`);
    }

    if (category) {
      countQuery += ` AND category = ?`;
      countParams.push(category);
    }

    if (search) {
      countQuery += ` AND (title LIKE ? OR content LIKE ?)`;
      countParams.push(`%${search}%`, `%${search}%`);
    }

    const countResult = await c.env.DB.prepare(countQuery).bind(...countParams).first();
    const totalCount = countResult ? (countResult.count as number) : 0;
    const totalPages = Math.ceil(totalCount / pageSize);

    // 获取热门标签 - 从stories表的tags字段中提取
    const tagsQuery = `
      SELECT tags
      FROM stories
      WHERE status = 'approved' AND tags IS NOT NULL AND tags != ''
    `;
    const tagsResult = await c.env.DB.prepare(tagsQuery).all();

    // 统计标签频次
    const tagCounts: { [key: string]: number } = {};
    tagsResult.results?.forEach((row: any) => {
      if (row.tags) {
        try {
          const tags = JSON.parse(row.tags);
          if (Array.isArray(tags)) {
            tags.forEach(tag => {
              tagCounts[tag] = (tagCounts[tag] || 0) + 1;
            });
          }
        } catch (e) {
          // 忽略JSON解析错误
        }
      }
    });

    // 转换为数组并排序
    const popularTags = Object.entries(tagCounts)
      .map(([tag, count]) => ({ tag, count }))
      .sort((a, b) => b.count - a.count)
      .slice(0, 10);

    // 格式化故事
    const formattedStories = stories.map((story: any) => {
      let tags = [];
      if (story.tags) {
        try {
          tags = JSON.parse(story.tags);
        } catch (e) {
          tags = [];
        }
      }

      return {
        id: story.id,
        title: story.title,
        content: story.content,
        author: story.is_anonymous ? '匿名用户' : '已验证用户',
        createdAt: story.created_at,
        likes: story.like_count || 0,
        dislikes: 0, // 暂时没有dislikes字段
        tags,
        category: story.category,
        status: story.status,
        viewCount: story.view_count || 0
      };
    });

    return c.json({
      success: true,
      stories: formattedStories,
      totalPages,
      currentPage: page,
      popularTags: popularTags,
    });
  } catch (error) {
    console.error('Error getting story list:', error);
    return c.json({ success: false, error: 'Failed to get story list' }, 500);
  }
};
