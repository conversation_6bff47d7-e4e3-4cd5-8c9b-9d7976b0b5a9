import { Hono } from 'hono';
import { cors } from 'hono/cors';
import { secureHeaders } from 'hono/secure-headers';
import { logger } from 'hono/logger';
import { rateLimit } from './middlewares/rateLimit';
import { email } from './controllers/email';
import { admin } from './controllers/admin';
import { analytics } from './controllers/analytics';
import apiRoutes from './api/routes';
import dataInitRoutes from './api/admin/data-init.routes';
import dataManagementRoutes from './api/admin/data-management.routes';
import deidentificationRoutes from './api/admin/deidentification.routes';
import websocketRoutes from './api/websocket';
import { anonymousAuthRoutes } from './api/anonymous-auth/anonymous-auth.routes';
import { createWebSocketService } from './services/websocket.service';
import reviewerRoutes from './routes/reviewer.routes';
import { CloudflareAIProviderManager } from './services/ai-provider.service';

import { Env } from './types';

// Create Hono app
const app = new Hono<{ Bindings: Env }>();

// Middleware
app.use('*', logger());
app.use('*', secureHeaders());
app.use('*', cors({
  origin: [
    'http://localhost:5173',
    'http://localhost:5174',
    'http://localhost:5175',
    'http://localhost:5176',
    'http://localhost:5177',
    'https://college-employment-survey.pages.dev',
    'https://beb4f845.college-employment-survey.pages.dev',
    'https://d2292b83.college-employment-survey.pages.dev',
    'https://c4534e21.college-employment-survey.pages.dev',
    'https://2ba531d5.college-employment-survey.pages.dev',
    'https://e4cd94b1.college-employment-survey.pages.dev',
    'https://d68f23d0.college-employment-survey.pages.dev',
    'https://6bb44961.college-employment-survey.pages.dev',
    'https://fb2915ee.college-employment-survey.pages.dev'
  ],
  allowMethods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
  allowHeaders: ['Content-Type', 'Authorization'],
  maxAge: 86400,
  credentials: true,
}));

// WebSocket服务中间件
app.use('*', async (c, next) => {
  // 创建WebSocket服务
  const webSocketService = createWebSocketService(c.env);

  // 将WebSocket服务添加到上下文
  c.set('webSocketService', webSocketService);

  // 继续处理请求
  await next();
});

// Apply rate limiting to specific routes
app.use('/api/submit/*', rateLimit({ limit: 3, window: 60 * 60 })); // 3 requests per hour
app.use('/api/verify/*', rateLimit({ limit: 3, window: 60 * 60 })); // 3 requests per hour
app.use('/api/vote/*', rateLimit({ limit: 10, window: 60 * 60 })); // 10 requests per hour

// Routes
app.route('/api', apiRoutes);
app.route('/api/email', email);
app.route('/api/admin', admin);
app.route('/api/admin/data', dataInitRoutes);
app.route('/api/ws', websocketRoutes);
app.route('/api/analytics', analytics);
app.route('/api/admin/data-management', dataManagementRoutes);
app.route('/api/admin/deidentification', deidentificationRoutes);
app.route('/api/anonymous-auth', anonymousAuthRoutes);
app.route('/api/reviewer', reviewerRoutes);

// Health check
app.get('/health', (c) => {
  return c.json({ status: 'ok', environment: c.env.ENVIRONMENT });
});

// 404 handler
app.notFound((c) => {
  return c.json({ error: 'Not found' }, 404);
});

// Error handler
app.onError((err, c) => {
  console.error(`${c.req.method} ${c.req.url}`, err);
  return c.json({ error: 'Internal Server Error' }, 500);
});

// Export for Cloudflare Workers
export default app;
