/**
 * 用户设置服务
 * 管理用户界面和功能偏好设置
 */

import { logInfo, logError } from '@/lib/logger';

// 主题类型
export enum ThemeType {
  SYSTEM = 'system',
  LIGHT = 'light',
  DARK = 'dark'
}

// 表格布局类型
export enum TableLayoutType {
  COMPACT = 'compact',
  DEFAULT = 'default',
  SPACIOUS = 'spacious'
}

// 通知设置
export interface NotificationSettings {
  enabled: boolean;
  sound: boolean;
  desktop: boolean;
  newContent: boolean;
  systemAlerts: boolean;
  reviewReminders: boolean;
}

// 表格设置
export interface TableSettings {
  layout: TableLayoutType;
  pageSize: number;
  showFilters: boolean;
  useVirtualScroll: boolean;
  defaultSorting: string;
  visibleColumns: string[];
}

// 审核设置
export interface ReviewSettings {
  autoSaveInterval: number;
  confirmBeforeSubmit: boolean;
  useTemplates: boolean;
  showKeyboardShortcuts: boolean;
  defaultView: 'list' | 'grid' | 'kanban';
  defaultTab: 'pending' | 'approved' | 'rejected';
}

// 用户界面设置
export interface UISettings {
  theme: ThemeType;
  fontSize: number;
  animationsEnabled: boolean;
  highContrastMode: boolean;
  compactMode: boolean;
  sidebarCollapsed: boolean;
}

// 用户设置
export interface UserSettings {
  notifications: NotificationSettings;
  tables: TableSettings;
  review: ReviewSettings;
  ui: UISettings;
  shortcuts: Record<string, string>;
  lastUpdated: number;
}

// 默认设置
const defaultSettings: UserSettings = {
  notifications: {
    enabled: true,
    sound: true,
    desktop: true,
    newContent: true,
    systemAlerts: true,
    reviewReminders: true
  },
  tables: {
    layout: TableLayoutType.DEFAULT,
    pageSize: 20,
    showFilters: true,
    useVirtualScroll: false,
    defaultSorting: 'createdAt:desc',
    visibleColumns: ['sequenceNumber', 'type', 'preview', 'flags', 'createdAt', 'actions']
  },
  review: {
    autoSaveInterval: 60, // 秒
    confirmBeforeSubmit: true,
    useTemplates: true,
    showKeyboardShortcuts: true,
    defaultView: 'list',
    defaultTab: 'pending'
  },
  ui: {
    theme: ThemeType.SYSTEM,
    fontSize: 16,
    animationsEnabled: true,
    highContrastMode: false,
    compactMode: false,
    sidebarCollapsed: false
  },
  shortcuts: {
    'approve': 'Alt+A',
    'edit': 'Alt+E',
    'reject': 'Alt+R',
    'next': 'Alt+N',
    'previous': 'Alt+P',
    'save': 'Ctrl+S',
    'cancel': 'Escape'
  },
  lastUpdated: Date.now()
};

// 存储键
const STORAGE_KEY = 'user_settings';

/**
 * 获取用户设置
 */
export const getUserSettings = (): UserSettings => {
  try {
    const storedSettings = localStorage.getItem(STORAGE_KEY);
    
    if (storedSettings) {
      const parsedSettings = JSON.parse(storedSettings);
      
      // 合并默认设置和存储的设置
      return {
        ...defaultSettings,
        ...parsedSettings,
        notifications: {
          ...defaultSettings.notifications,
          ...parsedSettings.notifications
        },
        tables: {
          ...defaultSettings.tables,
          ...parsedSettings.tables
        },
        review: {
          ...defaultSettings.review,
          ...parsedSettings.review
        },
        ui: {
          ...defaultSettings.ui,
          ...parsedSettings.ui
        },
        shortcuts: {
          ...defaultSettings.shortcuts,
          ...parsedSettings.shortcuts
        }
      };
    }
    
    return defaultSettings;
  } catch (error) {
    logError('获取用户设置失败', error);
    return defaultSettings;
  }
};

/**
 * 保存用户设置
 * @param settings 用户设置
 */
export const saveUserSettings = (settings: Partial<UserSettings>): void => {
  try {
    const currentSettings = getUserSettings();
    
    // 合并当前设置和新设置
    const newSettings: UserSettings = {
      ...currentSettings,
      ...settings,
      notifications: {
        ...currentSettings.notifications,
        ...(settings.notifications || {})
      },
      tables: {
        ...currentSettings.tables,
        ...(settings.tables || {})
      },
      review: {
        ...currentSettings.review,
        ...(settings.review || {})
      },
      ui: {
        ...currentSettings.ui,
        ...(settings.ui || {})
      },
      shortcuts: {
        ...currentSettings.shortcuts,
        ...(settings.shortcuts || {})
      },
      lastUpdated: Date.now()
    };
    
    // 保存到本地存储
    localStorage.setItem(STORAGE_KEY, JSON.stringify(newSettings));
    
    logInfo('用户设置已保存');
    
    // 应用设置
    applySettings(newSettings);
  } catch (error) {
    logError('保存用户设置失败', error);
  }
};

/**
 * 重置用户设置
 */
export const resetUserSettings = (): void => {
  try {
    // 保存默认设置
    localStorage.setItem(STORAGE_KEY, JSON.stringify(defaultSettings));
    
    logInfo('用户设置已重置');
    
    // 应用默认设置
    applySettings(defaultSettings);
  } catch (error) {
    logError('重置用户设置失败', error);
  }
};

/**
 * 应用设置到界面
 * @param settings 用户设置
 */
const applySettings = (settings: UserSettings): void => {
  try {
    // 应用主题
    const { theme } = settings.ui;
    
    if (theme === ThemeType.DARK) {
      document.documentElement.classList.add('dark');
    } else if (theme === ThemeType.LIGHT) {
      document.documentElement.classList.remove('dark');
    } else {
      // 系统主题
      if (window.matchMedia('(prefers-color-scheme: dark)').matches) {
        document.documentElement.classList.add('dark');
      } else {
        document.documentElement.classList.remove('dark');
      }
    }
    
    // 应用字体大小
    document.documentElement.style.fontSize = `${settings.ui.fontSize}px`;
    
    // 应用高对比度模式
    if (settings.ui.highContrastMode) {
      document.documentElement.classList.add('high-contrast');
    } else {
      document.documentElement.classList.remove('high-contrast');
    }
    
    // 应用紧凑模式
    if (settings.ui.compactMode) {
      document.documentElement.classList.add('compact');
    } else {
      document.documentElement.classList.remove('compact');
    }
    
    // 触发设置变更事件
    window.dispatchEvent(new CustomEvent('settings-changed', { detail: settings }));
  } catch (error) {
    logError('应用用户设置失败', error);
  }
};

// 初始化设置
export const initializeSettings = (): void => {
  const settings = getUserSettings();
  applySettings(settings);
  
  // 监听系统主题变化
  window.matchMedia('(prefers-color-scheme: dark)').addEventListener('change', () => {
    const currentSettings = getUserSettings();
    if (currentSettings.ui.theme === ThemeType.SYSTEM) {
      applySettings(currentSettings);
    }
  });
};
