import React, { useState } from 'react';
import { useParams, useNavigate, useLocation } from 'react-router-dom';
import MainLayout from '@/components/layouts/MainLayout';
import ModerationFeedbackForm from '@/components/feedback/ModerationFeedbackForm';
import ModerationFeedbackStatus from '@/components/feedback/ModerationFeedbackStatus';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import { Button } from '@/components/ui/button';
import { AlertCircle, ArrowLeft, MessageSquare } from 'lucide-react';

/**
 * 审核反馈页面
 */
const ModerationFeedbackPage: React.FC = () => {
  const { feedbackId } = useParams<{ feedbackId: string }>();
  const navigate = useNavigate();
  const location = useLocation();
  const queryParams = new URLSearchParams(location.search);
  
  const contentId = queryParams.get('contentId') || '';
  const contentType = queryParams.get('contentType') || '';
  const moderationId = queryParams.get('moderationId') || undefined;
  
  const [activeTab, setActiveTab] = useState<string>(feedbackId ? 'status' : 'submit');
  const [submittedFeedbackId, setSubmittedFeedbackId] = useState<string | null>(null);
  
  // 处理提交成功
  const handleSubmitSuccess = (newFeedbackId: string) => {
    setSubmittedFeedbackId(newFeedbackId);
    setActiveTab('status');
    
    // 更新URL，但不刷新页面
    navigate(`/feedback/moderation/${newFeedbackId}`, { replace: true });
  };
  
  // 返回上一页
  const handleGoBack = () => {
    navigate(-1);
  };
  
  return (
    <MainLayout>
      <div className="container mx-auto py-8 px-4">
        <div className="flex items-center mb-6">
          <Button
            variant="ghost"
            size="sm"
            className="mr-2"
            onClick={handleGoBack}
          >
            <ArrowLeft className="w-4 h-4 mr-1" />
            返回
          </Button>
          <h1 className="text-2xl font-bold">审核反馈</h1>
        </div>
        
        {!contentId && !feedbackId && (
          <Alert className="mb-6">
            <AlertCircle className="h-4 w-4" />
            <AlertTitle>缺少必要参数</AlertTitle>
            <AlertDescription>
              请提供内容ID和类型，或者反馈ID
            </AlertDescription>
          </Alert>
        )}
        
        {(contentId || feedbackId || submittedFeedbackId) && (
          <div className="max-w-md mx-auto">
            <Tabs value={activeTab} onValueChange={setActiveTab}>
              <TabsList className="grid w-full grid-cols-2">
                <TabsTrigger value="submit" disabled={!contentId}>
                  <MessageSquare className="w-4 h-4 mr-2" />
                  提交反馈
                </TabsTrigger>
                <TabsTrigger value="status" disabled={!feedbackId && !submittedFeedbackId}>
                  <AlertCircle className="w-4 h-4 mr-2" />
                  查看状态
                </TabsTrigger>
              </TabsList>
              
              <TabsContent value="submit" className="mt-6">
                {contentId && contentType ? (
                  <ModerationFeedbackForm
                    contentId={contentId}
                    contentType={contentType}
                    moderationId={moderationId}
                    onSuccess={handleSubmitSuccess}
                  />
                ) : (
                  <Alert>
                    <AlertCircle className="h-4 w-4" />
                    <AlertTitle>缺少必要参数</AlertTitle>
                    <AlertDescription>
                      请提供内容ID和类型
                    </AlertDescription>
                  </Alert>
                )}
              </TabsContent>
              
              <TabsContent value="status" className="mt-6">
                {(feedbackId || submittedFeedbackId) ? (
                  <ModerationFeedbackStatus
                    feedbackId={feedbackId || submittedFeedbackId || ''}
                  />
                ) : (
                  <Alert>
                    <AlertCircle className="h-4 w-4" />
                    <AlertTitle>缺少必要参数</AlertTitle>
                    <AlertDescription>
                      请提供反馈ID
                    </AlertDescription>
                  </Alert>
                )}
              </TabsContent>
            </Tabs>
          </div>
        )}
      </div>
    </MainLayout>
  );
};

export default ModerationFeedbackPage;
