/**
 * 统计相关处理程序
 */

import { verifyToken } from '../utils/auth';

/**
 * 处理获取统计数据
 * @param request 请求对象
 * @param env 环境对象
 * @returns 响应对象
 */
export async function handleGetStats(request: Request, env: Env): Promise<Response> {
  try {
    // 获取故事总数
    const storiesResult = await env.DB.prepare(`
      SELECT 
        COUNT(*) as total,
        SUM(CASE WHEN status = 'approved' THEN 1 ELSE 0 END) as approved,
        SUM(CASE WHEN status = 'pending' THEN 1 ELSE 0 END) as pending,
        SUM(CASE WHEN status = 'rejected' THEN 1 ELSE 0 END) as rejected
      FROM stories
    `).first();
    
    // 获取问卷总数
    const surveysResult = await env.DB.prepare(`
      SELECT COUNT(*) as total
      FROM surveys
    `).first();
    
    // 获取问卷回复总数
    const responsesResult = await env.DB.prepare(`
      SELECT 
        COUNT(*) as total,
        SUM(CASE WHEN status = 'verified' THEN 1 ELSE 0 END) as verified,
        SUM(CASE WHEN status = 'pending' THEN 1 ELSE 0 END) as pending,
        SUM(CASE WHEN status = 'rejected' THEN 1 ELSE 0 END) as rejected
      FROM survey_responses
    `).first();
    
    // 获取标签总数
    const tagsResult = await env.DB.prepare(`
      SELECT COUNT(*) as total
      FROM tags
    `).first();
    
    // 获取用户总数
    const usersResult = await env.DB.prepare(`
      SELECT 
        COUNT(*) as total,
        SUM(CASE WHEN role = 'admin' THEN 1 ELSE 0 END) as admin,
        SUM(CASE WHEN role = 'superadmin' THEN 1 ELSE 0 END) as superadmin,
        SUM(CASE WHEN role = 'reviewer' THEN 1 ELSE 0 END) as reviewer
      FROM users
    `).first();
    
    // 获取UUID总数
    const uuidResult = await env.DB.prepare(`
      SELECT COUNT(*) as total
      FROM uuid_mappings
    `).first();
    
    // 组织响应数据
    const stats = {
      stories: {
        total: storiesResult?.total || 0,
        approved: storiesResult?.approved || 0,
        pending: storiesResult?.pending || 0,
        rejected: storiesResult?.rejected || 0
      },
      surveys: {
        total: surveysResult?.total || 0
      },
      responses: {
        total: responsesResult?.total || 0,
        verified: responsesResult?.verified || 0,
        pending: responsesResult?.pending || 0,
        rejected: responsesResult?.rejected || 0
      },
      tags: {
        total: tagsResult?.total || 0
      },
      users: {
        total: usersResult?.total || 0,
        admin: usersResult?.admin || 0,
        superadmin: usersResult?.superadmin || 0,
        reviewer: usersResult?.reviewer || 0
      },
      uuid: {
        total: uuidResult?.total || 0
      }
    };
    
    // 返回响应
    return new Response(JSON.stringify({
      success: true,
      stats
    }), {
      status: 200,
      headers: { 'Content-Type': 'application/json' }
    });
  } catch (error) {
    console.error('获取统计数据失败:', error);
    
    return new Response(JSON.stringify({
      success: false,
      error: '服务器错误'
    }), {
      status: 500,
      headers: { 'Content-Type': 'application/json' }
    });
  }
}

/**
 * 处理获取审核统计数据
 * @param request 请求对象
 * @param env 环境对象
 * @returns 响应对象
 */
export async function handleGetReviewStats(request: Request, env: Env): Promise<Response> {
  try {
    // 验证管理员权限
    const authHeader = request.headers.get('Authorization');
    
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return new Response(JSON.stringify({
        success: false,
        error: '未授权'
      }), {
        status: 401,
        headers: { 'Content-Type': 'application/json' }
      });
    }
    
    const token = authHeader.substring(7);
    const tokenResult = verifyToken(token, env.JWT_SECRET);
    
    if (!tokenResult.valid || !tokenResult.payload) {
      return new Response(JSON.stringify({
        success: false,
        error: '无效的令牌'
      }), {
        status: 401,
        headers: { 'Content-Type': 'application/json' }
      });
    }
    
    // 验证角色权限
    const role = tokenResult.payload.role;
    
    if (role !== 'admin' && role !== 'superadmin' && role !== 'reviewer') {
      return new Response(JSON.stringify({
        success: false,
        error: '没有权限'
      }), {
        status: 403,
        headers: { 'Content-Type': 'application/json' }
      });
    }
    
    // 获取审核记录统计
    const reviewStats = await env.DB.prepare(`
      SELECT 
        COUNT(*) as total,
        SUM(CASE WHEN decision = 'approved' THEN 1 ELSE 0 END) as approved,
        SUM(CASE WHEN decision = 'rejected' THEN 1 ELSE 0 END) as rejected,
        AVG(review_duration) as avg_duration
      FROM review_records
    `).first();
    
    // 获取每日审核数据
    const dailyStats = await env.DB.prepare(`
      SELECT 
        DATE(reviewed_at) as date,
        COUNT(*) as total,
        SUM(CASE WHEN decision = 'approved' THEN 1 ELSE 0 END) as approved,
        SUM(CASE WHEN decision = 'rejected' THEN 1 ELSE 0 END) as rejected
      FROM review_records
      WHERE reviewed_at >= DATE('now', '-30 days')
      GROUP BY DATE(reviewed_at)
      ORDER BY date DESC
    `).all();
    
    // 组织响应数据
    const stats = {
      total: reviewStats?.total || 0,
      approved: reviewStats?.approved || 0,
      rejected: reviewStats?.rejected || 0,
      avgDuration: reviewStats?.avg_duration || 0,
      daily: dailyStats.results.map(day => ({
        date: day.date,
        total: day.total,
        approved: day.approved,
        rejected: day.rejected
      }))
    };
    
    // 返回响应
    return new Response(JSON.stringify({
      success: true,
      stats
    }), {
      status: 200,
      headers: { 'Content-Type': 'application/json' }
    });
  } catch (error) {
    console.error('获取审核统计数据失败:', error);
    
    return new Response(JSON.stringify({
      success: false,
      error: '服务器错误'
    }), {
      status: 500,
      headers: { 'Content-Type': 'application/json' }
    });
  }
}
