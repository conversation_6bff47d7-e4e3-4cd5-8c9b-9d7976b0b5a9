# 🏆 CORS配置最佳实践

> **适用于**: Cloudflare Workers + Hono框架项目

## 🎯 **核心原则**

### **1. 安全第一**
```typescript
// ❌ 危险配置
origin: '*'  // 允许所有域名
credentials: false  // 禁用凭证

// ✅ 安全配置
origin: (origin) => allowedDomains.includes(origin) ? origin : false
credentials: true  // 启用凭证保护
```

### **2. 明确性优于灵活性**
```typescript
// ❌ 过于宽松
origin: true

// ✅ 明确指定
origin: ['https://app.domain.com', 'https://admin.domain.com']
```

### **3. 环境隔离**
```typescript
// ✅ 根据环境配置不同的CORS策略
const getCORSConfig = (env: string) => {
  switch (env) {
    case 'development':
      return { origin: /^http:\/\/localhost:\d+$/ };
    case 'production':
      return { origin: ['https://app.domain.com'] };
    default:
      return { origin: false };
  }
};
```

---

## 🔧 **Hono框架特定配置**

### **标准配置模板**
```typescript
import { Hono } from 'hono';
import { cors } from 'hono/cors';

const app = new Hono();

// ✅ 推荐的CORS配置
app.use('*', cors({
  origin: (origin) => {
    // 处理无origin的请求 (如Postman, curl)
    if (!origin) return origin;
    
    // 开发环境
    if (process.env.NODE_ENV === 'development') {
      if (origin.startsWith('http://localhost:')) {
        return origin;
      }
    }
    
    // 生产环境允许的域名
    const allowedOrigins = [
      'https://app.domain.com',
      'https://admin.domain.com'
    ];
    
    if (allowedOrigins.includes(origin)) {
      return origin;  // ✅ 返回具体域名
    }
    
    // Cloudflare Pages临时域名 (可选)
    if (origin.match(/^https:\/\/[a-f0-9]{8}\.project\.pages\.dev$/)) {
      return origin;
    }
    
    return false;  // 拒绝其他域名
  },
  allowMethods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
  allowHeaders: [
    'Content-Type',
    'Authorization',
    'X-Requested-With',
    'Accept',
    'Origin'
  ],
  exposeHeaders: ['X-Total-Count', 'X-Page-Count'],
  credentials: true,
  maxAge: 86400  // 24小时缓存预检结果
}));
```

### **动态域名管理**
```typescript
// ✅ 使用环境变量管理域名
const getAllowedOrigins = (): string[] => {
  const origins = process.env.ALLOWED_ORIGINS;
  if (!origins) return [];
  
  return origins.split(',').map(origin => origin.trim());
};

app.use('*', cors({
  origin: (origin) => {
    if (!origin) return origin;
    
    const allowedOrigins = getAllowedOrigins();
    return allowedOrigins.includes(origin) ? origin : false;
  }
}));
```

---

## 🛡️ **安全配置**

### **1. 凭证处理**
```typescript
// ✅ 正确的凭证配置
app.use('*', cors({
  origin: (origin) => {
    // 必须明确指定域名，不能使用 '*'
    return allowedOrigins.includes(origin) ? origin : false;
  },
  credentials: true,  // 允许发送cookies和认证头
  allowHeaders: ['Authorization', 'Content-Type']
}));
```

### **2. 请求头控制**
```typescript
// ✅ 最小权限原则
app.use('*', cors({
  allowHeaders: [
    'Content-Type',      // 基本内容类型
    'Authorization',     // 认证令牌
    'X-Requested-With',  // AJAX标识
    'Accept',           // 接受类型
    'Origin'            // 来源域名
    // 避免添加不必要的头
  ]
}));
```

### **3. 方法限制**
```typescript
// ✅ 只允许必要的HTTP方法
app.use('*', cors({
  allowMethods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
  // 避免允许 PATCH, HEAD 等非必要方法
}));
```

---

## 🌐 **多环境配置**

### **配置文件结构**
```typescript
// config/cors.ts
interface CORSEnvironment {
  origins: string[];
  credentials: boolean;
  maxAge: number;
}

const corsConfigs: Record<string, CORSEnvironment> = {
  development: {
    origins: ['http://localhost:3000', 'http://localhost:5173'],
    credentials: true,
    maxAge: 0  // 开发时不缓存
  },
  
  staging: {
    origins: ['https://staging.domain.com'],
    credentials: true,
    maxAge: 3600  // 1小时缓存
  },
  
  production: {
    origins: ['https://app.domain.com'],
    credentials: true,
    maxAge: 86400  // 24小时缓存
  }
};

export const getCORSConfig = (env: string = 'production') => {
  const config = corsConfigs[env] || corsConfigs.production;
  
  return {
    origin: (origin: string) => {
      if (!origin) return origin;
      return config.origins.includes(origin) ? origin : false;
    },
    credentials: config.credentials,
    maxAge: config.maxAge,
    allowMethods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
    allowHeaders: ['Content-Type', 'Authorization', 'X-Requested-With']
  };
};
```

### **环境变量使用**
```bash
# .env.development
CORS_ORIGINS=http://localhost:3000,http://localhost:5173
CORS_MAX_AGE=0

# .env.production  
CORS_ORIGINS=https://app.domain.com
CORS_MAX_AGE=86400
```

---

## 🔍 **调试和监控**

### **1. 调试模式**
```typescript
// ✅ 开发环境启用详细日志
app.use('*', cors({
  origin: (origin) => {
    if (process.env.NODE_ENV === 'development') {
      console.log(`CORS request from origin: ${origin}`);
    }
    
    const allowed = allowedOrigins.includes(origin);
    
    if (process.env.NODE_ENV === 'development') {
      console.log(`Origin ${origin} ${allowed ? 'allowed' : 'rejected'}`);
    }
    
    return allowed ? origin : false;
  }
}));
```

### **2. 错误处理**
```typescript
// ✅ 优雅的错误处理
app.use('*', async (c, next) => {
  try {
    await next();
  } catch (error) {
    if (error.message.includes('CORS')) {
      console.error('CORS Error:', {
        origin: c.req.header('Origin'),
        method: c.req.method,
        path: c.req.path,
        error: error.message
      });
      
      return c.json({
        error: 'CORS configuration error',
        message: 'Please contact support'
      }, 403);
    }
    throw error;
  }
});
```

### **3. 监控指标**
```typescript
// ✅ CORS请求监控
let corsStats = {
  allowed: 0,
  rejected: 0,
  origins: new Set<string>()
};

app.use('*', cors({
  origin: (origin) => {
    corsStats.origins.add(origin || 'no-origin');
    
    const allowed = allowedOrigins.includes(origin);
    
    if (allowed) {
      corsStats.allowed++;
    } else {
      corsStats.rejected++;
      console.warn(`Rejected CORS request from: ${origin}`);
    }
    
    return allowed ? origin : false;
  }
}));

// 定期报告统计信息
setInterval(() => {
  console.log('CORS Stats:', corsStats);
}, 60000);  // 每分钟
```

---

## 📋 **常见错误避免**

### **1. 返回值类型错误**
```typescript
// ❌ 错误
origin: (origin) => allowedOrigins.includes(origin) ? true : false

// ✅ 正确
origin: (origin) => allowedOrigins.includes(origin) ? origin : false
```

### **2. 通配符与凭证冲突**
```typescript
// ❌ 错误 - 不能同时使用
origin: '*',
credentials: true

// ✅ 正确
origin: allowedOrigins,
credentials: true
```

### **3. 忘记处理无origin请求**
```typescript
// ❌ 可能导致问题
origin: (origin) => allowedOrigins.includes(origin) ? origin : false

// ✅ 正确处理
origin: (origin) => {
  if (!origin) return origin;  // 允许无origin请求
  return allowedOrigins.includes(origin) ? origin : false;
}
```

### **4. 开发环境配置泄露到生产**
```typescript
// ❌ 危险
origin: true  // 开发时方便但不安全

// ✅ 安全
origin: process.env.NODE_ENV === 'development' 
  ? true 
  : allowedOrigins
```

---

## 🚀 **性能优化**

### **1. 预检缓存**
```typescript
// ✅ 合理设置缓存时间
app.use('*', cors({
  maxAge: process.env.NODE_ENV === 'production' ? 86400 : 0
}));
```

### **2. 域名匹配优化**
```typescript
// ✅ 使用Set提高查找性能
const allowedOriginsSet = new Set(allowedOrigins);

app.use('*', cors({
  origin: (origin) => {
    if (!origin) return origin;
    return allowedOriginsSet.has(origin) ? origin : false;
  }
}));
```

### **3. 正则表达式缓存**
```typescript
// ✅ 缓存编译的正则表达式
const pagesDevRegex = /^https:\/\/[a-f0-9]{8}\.project\.pages\.dev$/;

app.use('*', cors({
  origin: (origin) => {
    if (!origin) return origin;
    
    if (allowedOriginsSet.has(origin)) return origin;
    if (pagesDevRegex.test(origin)) return origin;
    
    return false;
  }
}));
```
