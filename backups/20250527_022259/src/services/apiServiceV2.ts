/**
 * API服务 v2.0
 *
 * 基于优化后的数据库结构的API调用服务
 */

import { ApiService } from './apiService';

// 简化的API请求函数
async function fetchAPI<T>(endpoint: string, options: RequestInit = {}): Promise<{ data: T }> {
  const apiEndpoint = endpoint.startsWith('/api') ? endpoint : `/api${endpoint}`;

  const headers = {
    'Content-Type': 'application/json',
    ...options.headers,
  };

  const response = await fetch(apiEndpoint, {
    ...options,
    headers,
  });

  if (!response.ok) {
    const errorData = await response.json().catch(() => ({ error: `HTTP ${response.status}` }));
    throw new Error(errorData.error || `API error: ${response.status}`);
  }

  const data = await response.json();
  return { data };
}

// API版本配置
const API_V2_BASE = '/api/v2';

/**
 * API响应接口
 */
interface ApiResponseV2<T = any> {
  success: boolean;
  data?: T;
  error?: string;
  message?: string;
  source?: string;
  version?: string;
}

/**
 * 分页响应接口
 */
interface PaginatedResponse<T> {
  success: boolean;
  data: {
    [key: string]: T[];
    pagination: {
      page: number;
      pageSize: number;
      total: number;
      totalPages: number;
      hasMore: boolean;
    };
  };
  source: string;
  version: string;
}

/**
 * 问卷心声接口
 */
interface QuestionnaireVoice {
  id: string;
  voiceType: 'advice' | 'observation';
  title: string;
  content: string;
  contentId: string;
  isAnonymous: boolean;
  createdAt: string;
  status: string;
  metadata: {
    educationLevel?: string;
    region?: string;
    employmentStatus?: string;
  };
}

/**
 * 故事内容接口
 */
interface StoryContent {
  id: string;
  title: string;
  category?: string;
  tags: string[];
  status: string;
  likes: number;
  dislikes: number;
  views: number;
  wordCount: number;
  createdAt: string;
  updatedAt: string;
  author: string;
  isAnonymous: boolean;
  educationLevel?: string;
  industry?: string;
  contentPreview: string;
}

/**
 * 用户内容接口
 */
interface UserContent {
  id: string;
  contentType: 'story' | 'voice';
  title: string;
  status: string;
  stats: {
    likes: number;
    views: number;
    wordCount: number;
  };
  createdAt: string;
  updatedAt: string;
  canEdit: boolean;
  canDelete: boolean;
}

/**
 * 问卷统计接口
 */
interface QuestionnaireStats {
  questionId: string;
  field: string;
  totalResponses: number;
  options: Array<{
    value: string;
    count: number;
    percentage: number;
  }>;
  lastUpdated: string;
}

/**
 * API服务类 v2.0
 */
export class ApiServiceV2 {

  /**
   * 获取问卷心声列表
   */
  static async getQuestionnaireVoices(params: {
    page?: number;
    pageSize?: number;
    type?: 'advice' | 'observation';
    status?: string;
  } = {}): Promise<PaginatedResponse<QuestionnaireVoice>> {
    try {
      const queryParams = new URLSearchParams();
      Object.entries(params).forEach(([key, value]) => {
        if (value !== undefined) queryParams.append(key, String(value));
      });
      const response = await fetchAPI<PaginatedResponse<QuestionnaireVoice>>(
        `${API_V2_BASE}/questionnaire-voices?${queryParams.toString()}`
      );
      return response.data;
    } catch (error) {
      console.error('获取问卷心声失败:', error);
      throw error;
    }
  }

  /**
   * 获取故事列表
   */
  static async getStories(params: {
    page?: number;
    pageSize?: number;
    category?: string;
    status?: string;
  } = {}): Promise<PaginatedResponse<StoryContent>> {
    try {
      const queryParams = new URLSearchParams();
      Object.entries(params).forEach(([key, value]) => {
        if (value !== undefined) queryParams.append(key, String(value));
      });
      const response = await fetchAPI<PaginatedResponse<StoryContent>>(
        `${API_V2_BASE}/stories?${queryParams.toString()}`
      );
      return response.data;
    } catch (error) {
      console.error('获取故事列表失败:', error);
      throw error;
    }
  }

  /**
   * 获取问卷统计数据
   */
  static async getQuestionnaireStats(questionId: string): Promise<ApiResponseV2<QuestionnaireStats>> {
    try {
      const response = await fetchAPI<ApiResponseV2<QuestionnaireStats>>(
        `${API_V2_BASE}/questionnaire-stats?questionId=${questionId}`
      );
      return response.data;
    } catch (error) {
      console.error('获取问卷统计失败:', error);
      throw error;
    }
  }

  /**
   * 获取用户内容
   */
  static async getUserContent(userId: string, params: {
    type?: 'story' | 'voice';
  } = {}): Promise<ApiResponseV2<{ userId: string; contents: UserContent[] }>> {
    try {
      const queryParams = new URLSearchParams();
      Object.entries(params).forEach(([key, value]) => {
        if (value !== undefined) queryParams.append(key, String(value));
      });
      const response = await fetchAPI<ApiResponseV2<{ userId: string; contents: UserContent[] }>>(
        `${API_V2_BASE}/user/${userId}/content?${queryParams.toString()}`
      );
      return response.data;
    } catch (error) {
      console.error('获取用户内容失败:', error);
      throw error;
    }
  }

  /**
   * 获取数据库状态
   */
  static async getDatabaseStatus(): Promise<ApiResponseV2<{
    database: string;
    version: string;
    tables: Record<string, { count: number }>;
    timestamp: string;
  }>> {
    try {
      const response = await fetchAPI<ApiResponseV2<{
        database: string;
        version: string;
        tables: Record<string, { count: number }>;
        timestamp: string;
      }>>(`${API_V2_BASE}/database-status`);
      return response.data;
    } catch (error) {
      console.error('获取数据库状态失败:', error);
      throw error;
    }
  }

  /**
   * 获取可视化数据 (v2.0版本)
   */
  static async getVisualizationData(): Promise<ApiResponseV2<any>> {
    try {
      // 使用新的统计API获取数据
      const [
        educationStats,
        employmentStats,
        satisfactionStats,
        regionStats
      ] = await Promise.all([
        this.getQuestionnaireStats('qitem_education_level'),
        this.getQuestionnaireStats('qitem_employment_status'),
        this.getQuestionnaireStats('qitem_job_satisfaction'),
        this.getQuestionnaireStats('qitem_region')
      ]);

      // 转换为可视化数据格式
      const visualizationData = {
        totalCount: educationStats.data?.totalResponses || 0,
        educationLevel: educationStats.data?.options || [],
        employmentStatus: employmentStats.data?.options || [],
        jobSatisfaction: satisfactionStats.data?.options || [],
        region: regionStats.data?.options || [],
        lastUpdated: new Date().toISOString()
      };

      return {
        success: true,
        data: visualizationData,
        source: 'optimized_database',
        version: '2.0'
      };
    } catch (error) {
      console.error('获取可视化数据失败:', error);
      throw error;
    }
  }

  /**
   * 健康检查
   */
  static async healthCheck(): Promise<ApiResponseV2<{
    status: string;
    message: string;
    timestamp: string;
    database: string;
  }>> {
    try {
      const response = await fetchAPI<ApiResponseV2<{
        status: string;
        message: string;
        timestamp: string;
        database: string;
      }>>('/health');
      return response.data;
    } catch (error) {
      console.error('健康检查失败:', error);
      throw error;
    }
  }
}

/**
 * 兼容性适配器
 * 将v2.0 API响应转换为v1.0格式，确保前端兼容性
 */
export class ApiCompatibilityAdapter {

  /**
   * 将v2.0问卷心声转换为v1.0格式
   */
  static adaptQuestionnaireVoices(v2Response: PaginatedResponse<QuestionnaireVoice>) {
    return {
      success: v2Response.success,
      data: v2Response.data.voices?.map(voice => ({
        id: voice.id,
        type: voice.voiceType,
        title: voice.title,
        content: voice.content,
        author: voice.isAnonymous ? '匿名用户' : '注册用户',
        isAnonymous: voice.isAnonymous,
        createdAt: voice.createdAt,
        status: voice.status,
        educationLevel: voice.metadata.educationLevel,
        region: voice.metadata.region,
        employmentStatus: voice.metadata.employmentStatus
      })) || [],
      totalCount: v2Response.data.pagination?.total || 0,
      pagination: v2Response.data.pagination
    };
  }

  /**
   * 将v2.0故事转换为v1.0格式
   */
  static adaptStories(v2Response: PaginatedResponse<StoryContent>) {
    return {
      success: v2Response.success,
      data: v2Response.data.stories?.map(story => ({
        id: story.id,
        title: story.title,
        content: story.contentPreview,
        author: story.author,
        isAnonymous: story.isAnonymous,
        category: story.category,
        tags: story.tags,
        likes: story.likes,
        dislikes: story.dislikes,
        views: story.views,
        status: story.status,
        createdAt: story.createdAt,
        updatedAt: story.updatedAt,
        educationLevel: story.educationLevel,
        industry: story.industry
      })) || [],
      totalItems: v2Response.data.pagination?.total || 0,
      pagination: v2Response.data.pagination
    };
  }

  /**
   * 将v2.0可视化数据转换为v1.0格式
   */
  static adaptVisualizationData(v2Response: ApiResponseV2<any>) {
    if (!v2Response.success || !v2Response.data) {
      return { success: false, data: null };
    }

    const data = v2Response.data;
    return {
      success: true,
      data: {
        totalCount: data.totalCount,
        educationLevel: data.educationLevel,
        employmentStatus: data.employmentStatus,
        jobSatisfaction: data.jobSatisfaction,
        region: data.region
      },
      statistics: {
        totalResponses: data.totalCount,
        lastUpdated: data.lastUpdated
      }
    };
  }
}

/**
 * 混合API服务
 * 直接使用v1.0 API，确保稳定性
 */
export class HybridApiService {

  /**
   * 获取问卷心声（使用v1.0 API）
   */
  static async getQuestionnaireVoices(params: any = {}) {
    try {
      // 直接使用v1.0 API
      const response = await ApiService.getQuestionnaires(params);

      if (response.success && response.data) {
        // 转换数据格式以匹配前端期望
        const transformedData: any[] = [];

        response.data.forEach((item: any) => {
          // 如果有建议内容，添加一条建议记录
          if (item.advice && item.advice.trim()) {
            transformedData.push({
              id: `${item.id}_advice`,
              type: 'advice',
              content: item.advice,
              author: item.author || (item.isAnonymous ? '匿名用户' : '用户'),
              createdAt: item.createdAt,
              likes: Math.floor(Math.random() * 50), // 临时随机点赞数
              category: '学习建议',
              educationLevel: item.educationLevel,
              region: item.region,
              sequenceNumber: item.sequenceNumber
            });
          }

          // 如果有观察内容，添加一条观察记录
          if (item.observation && item.observation.trim()) {
            transformedData.push({
              id: `${item.id}_observation`,
              type: 'observation',
              content: item.observation,
              author: item.author || (item.isAnonymous ? '匿名用户' : '用户'),
              createdAt: item.createdAt,
              likes: Math.floor(Math.random() * 50), // 临时随机点赞数
              category: '就业观察',
              educationLevel: item.educationLevel,
              region: item.region,
              sequenceNumber: item.sequenceNumber
            });
          }
        });

        return {
          success: true,
          data: transformedData,
          pagination: response.pagination
        };
      }

      return response;
    } catch (error) {
      console.error('获取问卷心声失败:', error);
      throw error;
    }
  }

  /**
   * 获取故事列表（使用v1.0 API）
   */
  static async getStories(params: any = {}) {
    try {
      // 直接使用v1.0 API
      return await ApiService.getStories(params);
    } catch (error) {
      console.error('获取故事列表失败:', error);
      throw error;
    }
  }

  /**
   * 获取可视化数据（使用v1.0 API）
   */
  static async getVisualizationData() {
    try {
      // 直接使用v1.0 API
      return await ApiService.getVisualizationData();
    } catch (error) {
      console.error('获取可视化数据失败:', error);
      throw error;
    }
  }
}

// 导出
export const apiServiceV2 = ApiServiceV2;
export const hybridApiService = HybridApiService;
