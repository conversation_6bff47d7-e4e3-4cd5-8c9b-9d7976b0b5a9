export async function onRequest(context) {
  const { request } = context;
  const url = new URL(request.url);
  
  // 如果是 API 请求，转发到 Worker
  if (url.pathname.startsWith('/api/')) {
    console.log(`Proxying API request to Worker: ${url.pathname}`);
    
    // 使用 Cloudflare 的 service binding 或直接调用 Worker URL
    const workerUrl = 'https://college-employment-survey-api.aibook2099.workers.dev';
    const workerRequest = new Request(
      workerUrl + url.pathname + url.search,
      {
        method: request.method,
        headers: request.headers,
        body: request.body,
        redirect: 'follow'
      }
    );
    
    try {
      const response = await fetch(workerRequest);
      console.log(`Worker response status: ${response.status}`);
      return response;
    } catch (error) {
      console.error(`Error proxying to worker: ${error}`);
      return new Response(JSON.stringify({ error: 'Failed to proxy request to API' }), {
        status: 500,
        headers: { 'Content-Type': 'application/json' }
      });
    }
  }
  
  // 其他请求继续正常处理
  return context.next();
}
