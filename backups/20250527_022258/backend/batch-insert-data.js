#!/usr/bin/env node

/**
 * 批量插入数据脚本：逐条插入本地数据到生产环境
 * 避免外键约束问题
 */

import sqlite3 from 'sqlite3';
import { open } from 'sqlite';
import { execSync } from 'child_process';

const LOCAL_DB_PATH = './database.db';

async function openLocalDatabase() {
  return await open({
    filename: LOCAL_DB_PATH,
    driver: sqlite3.Database
  });
}

async function getLocalData() {
  console.log('📊 获取本地数据...');
  
  const db = await openLocalDatabase();
  
  // 获取问卷响应数据
  const responses = await db.all(`
    SELECT 
      id, education_level_display, region_display, employment_status,
      major_display, salary_range, created_at, updated_at
    FROM questionnaire_responses_v2
    ORDER BY created_at
  `);
  
  console.log(`✅ 找到 ${responses.length} 条问卷响应记录`);
  
  await db.close();
  return responses;
}

async function getCurrentProductionCount() {
  try {
    const command = `wrangler d1 execute college-employment-survey-realapi --command="SELECT COUNT(*) as count FROM questionnaire_responses_v2;" --remote --json`;
    const output = execSync(command, { encoding: 'utf8' });
    const result = JSON.parse(output);
    return result[0]?.results?.[0]?.count || 0;
  } catch (error) {
    console.error('❌ 获取生产环境数据数量失败:', error.message);
    return 0;
  }
}

async function insertSingleRecord(record) {
  const values = [
    `'${record.id}'`,
    record.education_level_display ? `'${record.education_level_display.replace(/'/g, "''")}'` : 'NULL',
    record.region_display ? `'${record.region_display.replace(/'/g, "''")}'` : 'NULL',
    record.employment_status ? `'${record.employment_status.replace(/'/g, "''")}'` : 'NULL',
    record.major_display ? `'${record.major_display.replace(/'/g, "''")}'` : 'NULL',
    record.salary_range ? `'${record.salary_range.replace(/'/g, "''")}'` : 'NULL',
    record.created_at ? `'${record.created_at}'` : 'CURRENT_TIMESTAMP',
    record.updated_at ? `'${record.updated_at}'` : 'CURRENT_TIMESTAMP'
  ];
  
  const sql = `INSERT OR IGNORE INTO questionnaire_responses_v2 (id, education_level_display, region_display, employment_status, major_display, salary_range, created_at, updated_at) VALUES (${values.join(', ')});`;
  
  try {
    const command = `wrangler d1 execute college-employment-survey-realapi --command="${sql}" --remote`;
    execSync(command, { encoding: 'utf8', stdio: 'pipe' });
    return true;
  } catch (error) {
    console.error(`❌ 插入记录失败 ${record.id}:`, error.message);
    return false;
  }
}

async function batchInsertData() {
  console.log('🚀 开始批量插入数据...\n');
  
  // 1. 获取本地数据
  const localData = await getLocalData();
  
  // 2. 获取当前生产环境数据量
  const currentCount = await getCurrentProductionCount();
  console.log(`📈 当前生产环境记录数: ${currentCount}`);
  
  // 3. 批量插入
  let successCount = 0;
  let failCount = 0;
  const batchSize = 10; // 每批处理10条
  
  for (let i = 0; i < localData.length; i += batchSize) {
    const batch = localData.slice(i, i + batchSize);
    console.log(`\n📦 处理批次 ${Math.floor(i / batchSize) + 1}/${Math.ceil(localData.length / batchSize)} (${batch.length} 条记录)`);
    
    for (const record of batch) {
      const success = await insertSingleRecord(record);
      if (success) {
        successCount++;
        process.stdout.write('✅');
      } else {
        failCount++;
        process.stdout.write('❌');
      }
    }
    
    // 每批次后检查进度
    const newCount = await getCurrentProductionCount();
    console.log(`\n   进度: ${newCount} 条记录 (+${newCount - currentCount})`);
    
    // 短暂延迟避免API限制
    await new Promise(resolve => setTimeout(resolve, 1000));
  }
  
  console.log(`\n📊 插入完成！`);
  console.log(`✅ 成功: ${successCount} 条`);
  console.log(`❌ 失败: ${failCount} 条`);
  
  // 4. 最终验证
  const finalCount = await getCurrentProductionCount();
  console.log(`🎯 最终生产环境记录数: ${finalCount}`);
  
  return finalCount;
}

async function testAPI() {
  console.log('\n🧪 测试API响应...');
  
  try {
    const command = `curl -s "https://college-employment-survey-realapi.aibook2099.workers.dev/api/questionnaire/stats"`;
    const output = execSync(command, { encoding: 'utf8' });
    const result = JSON.parse(output);
    
    console.log(`📊 API返回总数: ${result.data?.total || 0}`);
    console.log(`📈 教育水平分布: ${result.data?.educationLevels?.length || 0} 种`);
    console.log(`🌍 地区分布: ${result.data?.regions?.length || 0} 个地区`);
    console.log(`💼 就业状态分布: ${result.data?.employmentStatus?.length || 0} 种`);
    
    return result.data?.total || 0;
  } catch (error) {
    console.error('❌ API测试失败:', error.message);
    return 0;
  }
}

async function main() {
  try {
    console.log('🎯 开始批量数据插入任务...\n');
    
    const finalCount = await batchInsertData();
    const apiCount = await testAPI();
    
    console.log('\n🎉 任务完成！');
    console.log(`✅ 数据库记录数: ${finalCount}`);
    console.log(`🌐 API返回记录数: ${apiCount}`);
    
    if (finalCount === apiCount && finalCount > 100) {
      console.log('🎊 修复成功！生产环境数据已恢复正常');
    } else {
      console.log('⚠️  数据可能还需要进一步检查');
    }
    
  } catch (error) {
    console.error('\n❌ 任务失败:', error.message);
    process.exit(1);
  }
}

main().catch(console.error);
