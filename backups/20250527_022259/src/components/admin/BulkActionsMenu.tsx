import { useState } from 'react';
import { Button } from '@/components/ui/button';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import {
  Download,
  Tag,
  Edit,
  CheckSquare,
  Trash2,
  ChevronDown,
  FileSpreadsheet,
  FileText,
  FileJson,
} from 'lucide-react';

interface BulkActionsMenuProps {
  selectedCount: number;
  onExport: (format: string) => void;
  onBulkTag: () => void;
  onBulkEdit: () => void;
  onBulkVerify: () => void;
  onBulkDelete: () => void;
  disabled?: boolean;
}

export default function BulkActionsMenu({
  selectedCount,
  onExport,
  onBulkTag,
  onBulkEdit,
  onBulkVerify,
  onBulkDelete,
  disabled = false,
}: BulkActionsMenuProps) {
  const [exportMenuOpen, setExportMenuOpen] = useState(false);

  if (selectedCount === 0) {
    return null;
  }

  return (
    <div className="flex items-center gap-2">
      <span className="text-sm text-muted-foreground">
        已选择 {selectedCount} 项
      </span>

      <DropdownMenu>
        <DropdownMenuTrigger asChild>
          <Button
            variant="outline"
            size="sm"
            className="flex items-center gap-1"
            disabled={disabled}
          >
            批量操作
            <ChevronDown className="h-4 w-4" />
          </Button>
        </DropdownMenuTrigger>
        <DropdownMenuContent align="end" className="w-48">
          <DropdownMenuLabel>批量操作</DropdownMenuLabel>
          
          {/* 批量导出 */}
          <DropdownMenu open={exportMenuOpen} onOpenChange={setExportMenuOpen}>
            <DropdownMenuTrigger asChild>
              <DropdownMenuItem onSelect={(e) => e.preventDefault()}>
                <Download className="mr-2 h-4 w-4" />
                导出数据
              </DropdownMenuItem>
            </DropdownMenuTrigger>
            <DropdownMenuContent side="right" className="w-36">
              <DropdownMenuItem onClick={() => onExport('csv')}>
                <FileText className="mr-2 h-4 w-4" />
                CSV格式
              </DropdownMenuItem>
              <DropdownMenuItem onClick={() => onExport('excel')}>
                <FileSpreadsheet className="mr-2 h-4 w-4" />
                Excel格式
              </DropdownMenuItem>
              <DropdownMenuItem onClick={() => onExport('json')}>
                <FileJson className="mr-2 h-4 w-4" />
                JSON格式
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
          
          <DropdownMenuSeparator />
          
          {/* 批量标记 */}
          <DropdownMenuItem onClick={onBulkTag}>
            <Tag className="mr-2 h-4 w-4" />
            添加标签
          </DropdownMenuItem>
          
          {/* 批量编辑 */}
          <DropdownMenuItem onClick={onBulkEdit}>
            <Edit className="mr-2 h-4 w-4" />
            批量编辑
          </DropdownMenuItem>
          
          {/* 批量验证 */}
          <DropdownMenuItem onClick={onBulkVerify}>
            <CheckSquare className="mr-2 h-4 w-4" />
            批量验证
          </DropdownMenuItem>
          
          <DropdownMenuSeparator />
          
          {/* 批量删除 */}
          <DropdownMenuItem 
            onClick={onBulkDelete}
            className="text-red-600 focus:text-red-600"
          >
            <Trash2 className="mr-2 h-4 w-4" />
            批量删除
          </DropdownMenuItem>
        </DropdownMenuContent>
      </DropdownMenu>
    </div>
  );
}
