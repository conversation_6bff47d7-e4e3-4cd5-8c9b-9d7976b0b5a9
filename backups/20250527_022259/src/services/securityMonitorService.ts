/**
 * 安全监控服务
 *
 * 提供安全监控相关的 API 调用
 */

import { apiClient } from './apiClient';

// 安全事件类型
export interface SecurityEvent {
  id: string;
  timestamp: string;
  type: 'login_failure' | 'brute_force' | 'suspicious_activity' | 'data_access' | 'system_error' | 'permission_change' | 'config_change';
  severity: 'low' | 'medium' | 'high' | 'critical';
  source: string;
  ip?: string;
  userId?: string;
  username?: string;
  details: string;
  status: 'new' | 'investigating' | 'resolved' | 'false_positive';
  assignedTo?: string;
  resolution?: string;
}

// 系统资源类型
export interface SystemResources {
  cpu: number;
  memory: number;
  disk: number;
  network: number;
  activeUsers: number;
  requestsPerMinute: number;
  errorRate: number;
}

// 安全统计类型
export interface SecurityStats {
  securityScore: number;
  threatLevel: 'low' | 'medium' | 'high' | 'critical';
  activeThreats: number;
  resolvedThreats: number;
  loginFailures: number;
  bruteForceAttempts: number;
  suspiciousActivities: number;
  dataAccessViolations: number;
  systemErrors: number;
}

// 安全日志类型
export interface SecurityLog {
  id: string;
  timestamp: string;
  level: 'info' | 'warn' | 'error' | 'debug';
  category: 'login' | 'access' | 'data' | 'system' | 'config';
  message: string;
  details?: string;
  userId?: string;
  username?: string;
  ip?: string;
  userAgent?: string;
  relatedEventId?: string;
}

// 安全日志搜索参数
export interface SecurityLogSearchParams {
  level?: 'info' | 'warn' | 'error' | 'debug';
  category?: 'login' | 'access' | 'data' | 'system' | 'config';
  userId?: string;
  username?: string;
  ip?: string;
  startDate?: string;
  endDate?: string;
  keyword?: string;
  page?: number;
  limit?: number;
}

// 模拟安全事件数据
const mockSecurityEvents: SecurityEvent[] = [
  {
    id: '1',
    timestamp: new Date(Date.now() - 5 * 60000).toISOString(),
    type: 'login_failure',
    severity: 'medium',
    source: '登录系统',
    ip: '*************',
    userId: 'admin1',
    username: 'admin1',
    details: '多次登录失败，可能是暴力破解尝试',
    status: 'new'
  },
  {
    id: '2',
    timestamp: new Date(Date.now() - 10 * 60000).toISOString(),
    type: 'suspicious_activity',
    severity: 'high',
    source: '内容审核',
    ip: '*************',
    userId: 'reviewer2',
    username: 'reviewer2',
    details: '短时间内批量审核通过，可能存在滥用权限',
    status: 'investigating'
  },
  {
    id: '3',
    timestamp: new Date(Date.now() - 15 * 60000).toISOString(),
    type: 'permission_change',
    severity: 'high',
    source: '权限系统',
    ip: '*************',
    userId: 'admin2',
    username: 'admin2',
    details: '修改了超级管理员权限',
    status: 'resolved',
    resolution: '确认为正常操作'
  },
  {
    id: '4',
    timestamp: new Date(Date.now() - 30 * 60000).toISOString(),
    type: 'data_access',
    severity: 'critical',
    source: '数据库',
    ip: '*************',
    userId: 'system',
    username: 'system',
    details: '异常数据访问模式，可能是数据泄露尝试',
    status: 'investigating'
  },
  {
    id: '5',
    timestamp: new Date(Date.now() - 60 * 60000).toISOString(),
    type: 'system_error',
    severity: 'low',
    source: '应用服务器',
    details: '服务器内存使用率异常高',
    status: 'resolved',
    resolution: '已重启服务器并释放内存'
  }
];

// 模拟系统资源数据
const mockSystemResources: SystemResources = {
  cpu: 45,
  memory: 68,
  disk: 72,
  network: 38,
  activeUsers: 127,
  requestsPerMinute: 342,
  errorRate: 1.2
};

// 模拟安全统计数据
const mockSecurityStats: SecurityStats = {
  securityScore: 78,
  threatLevel: 'medium',
  activeThreats: 3,
  resolvedThreats: 12,
  loginFailures: 24,
  bruteForceAttempts: 3,
  suspiciousActivities: 7,
  dataAccessViolations: 2,
  systemErrors: 5
};

// 模拟安全日志数据
const mockSecurityLogs: SecurityLog[] = Array.from({ length: 50 }, (_, i) => ({
  id: `log-${i + 1}`,
  timestamp: new Date(Date.now() - i * 15 * 60000).toISOString(),
  level: ['info', 'warn', 'error', 'debug'][Math.floor(Math.random() * 4)] as any,
  category: ['login', 'access', 'data', 'system', 'config'][Math.floor(Math.random() * 5)] as any,
  message: [
    '用户登录成功',
    '用户登录失败',
    '访问敏感数据',
    '修改系统配置',
    '系统资源不足',
    '可疑活动检测',
    '权限变更',
    '数据导出',
    '批量操作',
    '系统备份'
  ][Math.floor(Math.random() * 10)],
  details: `详细信息 ${i + 1}`,
  userId: i % 5 === 0 ? undefined : `user-${Math.floor(Math.random() * 10) + 1}`,
  username: i % 5 === 0 ? undefined : `用户${Math.floor(Math.random() * 10) + 1}`,
  ip: `192.168.1.${Math.floor(Math.random() * 255) + 1}`,
  userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
  relatedEventId: i % 10 === 0 ? `event-${Math.floor(Math.random() * 5) + 1}` : undefined
}));

/**
 * 获取安全事件
 * @returns 安全事件列表
 */
export async function getSecurityEvents() {
  try {
    console.log('🔒 调用真实API获取安全事件');

    // 调用真实API
    const response = await fetch('http://localhost:8787/api/admin/security/events');

    if (!response.ok) {
      throw new Error(`API调用失败: ${response.status}`);
    }

    const data = await response.json();
    console.log('✅ 安全事件API响应:', data);

    if (data.success) {
      return {
        success: true,
        data: data.data.events || []
      };
    } else {
      throw new Error(data.message || '获取安全事件失败');
    }
  } catch (error) {
    console.error('❌ 获取安全事件失败:', error);
    throw error;
  }
}

/**
 * 获取系统资源
 * @returns 系统资源
 */
export async function getSystemResources() {
  try {
    console.log('📊 调用真实API获取系统资源');

    // 调用真实API
    const response = await fetch('http://localhost:8787/api/admin/security/resources');

    if (!response.ok) {
      throw new Error(`API调用失败: ${response.status}`);
    }

    const data = await response.json();
    console.log('✅ 系统资源API响应:', data);

    if (data.success) {
      // 转换API数据格式以匹配前端期望的格式
      const resources = {
        cpu: data.data.cpu.usage,
        memory: data.data.memory.usage,
        disk: data.data.disk.usage,
        network: Math.min(100, (data.data.network.inbound + data.data.network.outbound) / 2),
        activeUsers: Math.floor(data.data.network.connections / 10),
        requestsPerMinute: data.data.network.connections,
        errorRate: Math.random() * 2 // 模拟错误率
      };

      return {
        success: true,
        data: resources
      };
    } else {
      throw new Error(data.message || '获取系统资源失败');
    }
  } catch (error) {
    console.error('❌ 获取系统资源失败:', error);
    throw error;
  }
}

/**
 * 获取安全统计
 * @returns 安全统计
 */
export async function getSecurityStats() {
  try {
    console.log('📈 调用真实API获取安全统计');

    // 调用真实API
    const response = await fetch('http://localhost:8787/api/admin/security/stats');

    if (!response.ok) {
      throw new Error(`API调用失败: ${response.status}`);
    }

    const data = await response.json();
    console.log('✅ 安全统计API响应:', data);

    if (data.success) {
      return {
        success: true,
        data: data.data
      };
    } else {
      throw new Error(data.message || '获取安全统计失败');
    }
  } catch (error) {
    console.error('❌ 获取安全统计失败:', error);
    throw error;
  }
}

/**
 * 获取安全日志
 * @param params 搜索参数
 * @returns 安全日志列表
 */
export async function getSecurityLogs(params: SecurityLogSearchParams = {}) {
  try {
    // 检查是否为开发环境
    if (import.meta.env.DEV && import.meta.env.VITE_USE_MOCK === 'true') {
      console.log('使用模拟数据获取安全日志');

      // 模拟网络延迟
      await new Promise(resolve => setTimeout(resolve, 1000));

      // 过滤日志
      let filteredLogs = [...mockSecurityLogs];

      if (params.level) {
        filteredLogs = filteredLogs.filter(log => log.level === params.level);
      }

      if (params.category) {
        filteredLogs = filteredLogs.filter(log => log.category === params.category);
      }

      if (params.userId) {
        filteredLogs = filteredLogs.filter(log => log.userId === params.userId);
      }

      if (params.username) {
        filteredLogs = filteredLogs.filter(log =>
          log.username?.toLowerCase().includes(params.username!.toLowerCase())
        );
      }

      if (params.ip) {
        filteredLogs = filteredLogs.filter(log => log.ip === params.ip);
      }

      if (params.keyword) {
        const keyword = params.keyword.toLowerCase();
        filteredLogs = filteredLogs.filter(log =>
          log.message.toLowerCase().includes(keyword) ||
          log.details?.toLowerCase().includes(keyword)
        );
      }

      if (params.startDate) {
        const startDate = new Date(params.startDate);
        filteredLogs = filteredLogs.filter(log => new Date(log.timestamp) >= startDate);
      }

      if (params.endDate) {
        const endDate = new Date(params.endDate);
        filteredLogs = filteredLogs.filter(log => new Date(log.timestamp) <= endDate);
      }

      // 排序：按时间戳降序
      filteredLogs.sort((a, b) => new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime());

      // 分页
      const page = params.page || 1;
      const limit = params.limit || 10;
      const startIndex = (page - 1) * limit;
      const endIndex = startIndex + limit;
      const paginatedLogs = filteredLogs.slice(startIndex, endIndex);

      return {
        success: true,
        data: {
          logs: paginatedLogs,
          total: filteredLogs.length,
          page,
          limit,
          totalPages: Math.ceil(filteredLogs.length / limit)
        }
      };
    }

    // 生产环境使用真实API
    const response = await apiClient.get('/admin/security/logs', { params });
    return response.data;
  } catch (error) {
    console.error('获取安全日志失败:', error);
    throw error;
  }
}

/**
 * 更新安全事件状态
 * @param eventId 事件ID
 * @param status 新状态
 * @param resolution 解决方案（如果状态为resolved或false_positive）
 * @returns 更新结果
 */
export async function updateSecurityEventStatus(
  eventId: string,
  status: 'new' | 'investigating' | 'resolved' | 'false_positive',
  resolution?: string
) {
  try {
    // 检查是否为开发环境
    if (import.meta.env.DEV && import.meta.env.VITE_USE_MOCK === 'true') {
      console.log('使用模拟数据更新安全事件状态');

      // 模拟网络延迟
      await new Promise(resolve => setTimeout(resolve, 800));

      return {
        success: true,
        message: '安全事件状态已更新'
      };
    }

    // 生产环境使用真实API
    const response = await apiClient.put(`/admin/security/events/${eventId}/status`, {
      status,
      resolution
    });
    return response.data;
  } catch (error) {
    console.error('更新安全事件状态失败:', error);
    throw error;
  }
}
