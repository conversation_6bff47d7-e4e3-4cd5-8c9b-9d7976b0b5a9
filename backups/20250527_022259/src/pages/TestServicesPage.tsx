/**
 * 服务测试页面
 * 
 * 用于快速测试各种服务是否正常工作
 */

import React, { useState } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '../components/ui/card';
import { Button } from '../components/ui/button';
import { Badge } from '../components/ui/badge';
import { 
  TestTube, 
  CheckCircle, 
  XCircle, 
  RefreshCw,
  Play,
  Database,
  Activity
} from 'lucide-react';
import { runAllTests, TestResult } from '../utils/testServices';

const TestServicesPage: React.FC = () => {
  const [isRunning, setIsRunning] = useState(false);
  const [testResults, setTestResults] = useState<{
    mockDataTests: TestResult[];
    unifiedServiceTests: TestResult[];
    cacheTests: TestResult[];
    summary: {
      total: number;
      passed: number;
      failed: number;
      duration: number;
    };
  } | null>(null);

  /**
   * 运行所有测试
   */
  const handleRunTests = async () => {
    setIsRunning(true);
    setTestResults(null);
    
    try {
      const results = await runAllTests();
      setTestResults(results);
    } catch (error) {
      console.error('测试运行失败:', error);
    } finally {
      setIsRunning(false);
    }
  };

  /**
   * 获取状态图标
   */
  const getStatusIcon = (success: boolean) => {
    return success ? 
      <CheckCircle className="h-4 w-4 text-green-500" /> : 
      <XCircle className="h-4 w-4 text-red-500" />;
  };

  /**
   * 获取状态颜色
   */
  const getStatusColor = (success: boolean) => {
    return success ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800';
  };

  /**
   * 渲染测试结果
   */
  const renderTestResults = (tests: TestResult[], title: string, icon: React.ReactNode) => {
    if (!tests.length) return null;

    return (
      <Card className="mb-4">
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            {icon}
            {title}
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-2">
            {tests.map((test, index) => (
              <div
                key={index}
                className="flex items-center justify-between p-3 border rounded-lg"
              >
                <div className="flex items-center gap-3">
                  {getStatusIcon(test.success)}
                  <div>
                    <p className="font-medium">{test.name}</p>
                    <p className="text-sm text-gray-600">{test.message}</p>
                    {test.error && (
                      <p className="text-sm text-red-600">错误: {test.error}</p>
                    )}
                  </div>
                </div>
                
                <div className="flex items-center gap-2">
                  <span className="text-xs text-gray-500">
                    {test.duration}ms
                  </span>
                  <Badge className={getStatusColor(test.success)}>
                    {test.success ? '通过' : '失败'}
                  </Badge>
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    );
  };

  return (
    <div className="container mx-auto p-6 space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold flex items-center gap-2">
            <TestTube className="h-8 w-8" />
            服务测试页面
          </h1>
          <p className="text-gray-600 mt-2">快速测试各种服务是否正常工作</p>
        </div>
        
        <Button
          onClick={handleRunTests}
          disabled={isRunning}
          className="flex items-center gap-2"
        >
          {isRunning ? (
            <RefreshCw className="h-4 w-4 animate-spin" />
          ) : (
            <Play className="h-4 w-4" />
          )}
          {isRunning ? '测试中...' : '运行所有测试'}
        </Button>
      </div>

      {/* 测试摘要 */}
      {testResults && (
        <Card>
          <CardHeader>
            <CardTitle>测试摘要</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
              <div className="text-center">
                <p className="text-2xl font-bold text-blue-600">
                  {testResults.summary.total}
                </p>
                <p className="text-sm text-gray-600">总测试数</p>
              </div>
              <div className="text-center">
                <p className="text-2xl font-bold text-green-600">
                  {testResults.summary.passed}
                </p>
                <p className="text-sm text-gray-600">通过</p>
              </div>
              <div className="text-center">
                <p className="text-2xl font-bold text-red-600">
                  {testResults.summary.failed}
                </p>
                <p className="text-sm text-gray-600">失败</p>
              </div>
              <div className="text-center">
                <p className="text-2xl font-bold text-purple-600">
                  {testResults.summary.duration}ms
                </p>
                <p className="text-sm text-gray-600">总耗时</p>
              </div>
            </div>
          </CardContent>
        </Card>
      )}

      {/* 测试结果 */}
      {testResults && (
        <div className="space-y-4">
          {renderTestResults(
            testResults.mockDataTests,
            '模拟数据服务测试',
            <Database className="h-5 w-5 text-blue-500" />
          )}
          
          {renderTestResults(
            testResults.unifiedServiceTests,
            '统一数据服务测试',
            <Activity className="h-5 w-5 text-green-500" />
          )}
          
          {renderTestResults(
            testResults.cacheTests,
            '缓存服务测试',
            <RefreshCw className="h-5 w-5 text-purple-500" />
          )}
        </div>
      )}

      {/* 空状态 */}
      {!testResults && !isRunning && (
        <Card>
          <CardContent className="text-center py-12">
            <TestTube className="h-16 w-16 text-gray-400 mx-auto mb-4" />
            <h3 className="text-lg font-medium text-gray-900 mb-2">
              准备运行测试
            </h3>
            <p className="text-gray-600 mb-4">
              点击"运行所有测试"按钮开始测试各种服务
            </p>
            <Button onClick={handleRunTests}>
              <Play className="h-4 w-4 mr-2" />
              开始测试
            </Button>
          </CardContent>
        </Card>
      )}

      {/* 运行中状态 */}
      {isRunning && (
        <Card>
          <CardContent className="text-center py-12">
            <RefreshCw className="h-16 w-16 text-blue-500 mx-auto mb-4 animate-spin" />
            <h3 className="text-lg font-medium text-gray-900 mb-2">
              正在运行测试...
            </h3>
            <p className="text-gray-600">
              请稍候，正在测试各种服务的功能
            </p>
          </CardContent>
        </Card>
      )}
    </div>
  );
};

export default TestServicesPage;
