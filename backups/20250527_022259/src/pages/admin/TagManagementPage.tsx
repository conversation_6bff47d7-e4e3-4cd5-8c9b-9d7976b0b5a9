import React from 'react';
import AdminLayout from '@/components/layouts/AdminLayout';
import TagManagementPanel from '@/components/admin/TagManagementPanel';

const TagManagementPage: React.FC = () => {
  return (
    <AdminLayout>
      <div className="container mx-auto py-6">
        <h1 className="text-2xl font-bold mb-6">标签管理</h1>
        <p className="text-gray-500 mb-6">管理故事墙的标签系统，包括创建、编辑和删除标签</p>
        <TagManagementPanel />
      </div>
    </AdminLayout>
  );
};

export default TagManagementPage;
