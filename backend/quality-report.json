{"timestamp": "2025-05-26T19:21:10.432Z", "version": "v3.0-modular", "summary": {"overallStatus": "excellent", "scores": {"codeQuality": 98}, "issues": ["❌ ESLint发现 1 个问题"], "achievements": [], "overallScore": 98}, "codeQuality": {"eslint": {"status": "failed", "output": "\n> college-employment-survey-backend@0.1.0 lint\n> eslint . --ext .js,.ts\n\n\n/Users/<USER>/Desktop/vscode/college-employment-survey/backend/create-test-review-data.js\n    5:1  warning  Async function 'createTestReviewData' has too many lines (174). Maximum allowed is 50  max-lines-per-function\n    5:1  warning  Async function 'createTestReviewData' has a complexity of 23. Maximum allowed is 10    complexity\n    6:3  warning  Unexpected console statement                                                           no-console\n   40:5  warning  Unexpected console statement                                                           no-console\n   79:5  warning  Unexpected console statement                                                           no-console\n  114:5  warning  Unexpected console statement                                                           no-console\n  173:5  warning  Unexpected console statement                                                           no-console\n  175:5  warning  Unexpected console statement                                                           no-console\n  176:5  warning  Unexpected console statement                                                           no-console\n  177:5  warning  Unexpected console statement                                                           no-console\n  178:5  warning  Unexpected console statement                                                           no-console\n  179:5  warning  Unexpected console statement                                                           no-console\n  180:5  warning  Unexpected console statement                                                           no-console\n  181:5  warning  Unexpected console statement                                                           no-console\n  182:5  warning  Unexpected console statement                                                           no-console\n  183:5  warning  Unexpected console statement                                                           no-console\n  186:5  warning  Unexpected console statement                                                           no-console\n\n/Users/<USER>/Desktop/vscode/college-employment-survey/backend/database/generate-test-data-v2.js\n   12:23  error    'DataNormalizer' is defined but never used. Allowed unused vars must match /^_/u    no-unused-vars\n  252:3   warning  Async method 'generateTestDataSet' has too many lines (164). Maximum allowed is 50  max-lines-per-function\n  260:5   warning  Unexpected console statement                                                        no-console\n  261:5   warning  Unexpected console statement                                                        no-console\n  297:45  warning  Arrow function has too many lines (123). Maximum allowed is 50                      max-lines-per-function\n  422:11  warning  Unexpected console statement                                                        no-console\n  426:7   warning  Unexpected console statement                                                        no-console\n  427:7   warning  Unexpected console statement                                                        no-console\n  428:7   warning  Unexpected console statement                                                        no-console\n  429:7   warning  Unexpected console statement                                                        no-console\n  430:7   warning  Unexpected console statement                                                        no-console\n  446:3   warning  Unexpected console statement                                                        no-console\n  447:3   warning  Unexpected console statement                                                        no-console\n  459:5   warning  Unexpected console statement                                                        no-console\n  461:5   warning  Unexpected console statement                                                        no-console\n\n/Users/<USER>/Desktop/vscode/college-employment-survey/backend/database/init-database-v2.js\n   33:1  warning  Function 'parseArguments' has a complexity of 12. Maximum allowed is 10  complexity\n   70:3  warning  Unexpected console statement                                             no-console\n   94:5  warning  Unexpected console statement                                             no-console\n   96:5  warning  Unexpected console statement                                             no-console\n   98:5  warning  Unexpected console statement                                             no-console\n  105:5  warning  Unexpected console statement                                             no-console\n  108:3  warning  Unexpected console statement                                             no-console\n  114:5  warning  Unexpected console statement                                             no-console\n  129:5  warning  Unexpected console statement                                             no-console\n  140:5  warning  Unexpected console statement                                             no-console\n  144:3  warning  Unexpected console statement                                             no-console\n  149:5  warning  Unexpected console statement                                             no-console\n  158:5  warning  Unexpected console statement                                             no-console\n  162:3  warning  Unexpected console statement                                             no-console\n  172:5  warning  Unexpected console statement                                             no-console\n  180:3  warning  Unexpected console statement                                             no-console\n  201:9  warning  Unexpected console statement                                             no-console\n  203:9  warning  Unexpected console statement                                             no-console\n  215:7  warning  Unexpected console statement                                             no-console\n  224:5  warning  Unexpected console statement                                             no-console\n  226:5  warning  Unexpected console statement                                             no-console\n  252:3  warning  Unexpected console statement                                             no-console\n  253:3  warning  Unexpected console statement                                             no-console\n  254:3  warning  Unexpected console statement                                             no-console\n  255:3  warning  Unexpected console statement                                             no-console\n  256:3  warning  Unexpected console statement                                             no-console\n  257:3  warning  Unexpected console statement                                             no-console\n  258:3  warning  Unexpected console statement                                             no-console\n  263:3  warning  Unexpected console statement                                             no-console\n  268:3  warning  Unexpected console statement                                             no-console\n  269:3  warning  Unexpected console statement                                             no-console\n  270:3  warning  Unexpected console statement                                             no-console\n  271:3  warning  Unexpected console statement                                             no-console\n  272:3  warning  Unexpected console statement                                             no-console\n  273:3  warning  Unexpected console statement                                             no-console\n  274:3  warning  Unexpected console statement                                             no-console\n  275:3  warning  Unexpected console statement                                             no-console\n  302:5  warning  Unexpected console statement                                             no-console\n  305:5  warning  Unexpected console statement                                             no-console\n  306:5  warning  Unexpected console statement                                             no-console\n\n/Users/<USER>/Desktop/vscode/college-employment-survey/backend/database/migrate-to-v2.js\n   96:5  warning  Unexpected console statement                                                  no-console\n  299:3  warning  Async method 'generateVoices' has too many lines (56). Maximum allowed is 50  max-lines-per-function\n  419:3  warning  Unexpected console statement                                                  no-console\n  425:5  warning  Unexpected console statement                                                  no-console\n  427:5  warning  Unexpected console statement                                                  no-console\n\n/Users/<USER>/Desktop/vscode/college-employment-survey/backend/database/simple-init.js\n   22:3  warning  Unexpected console statement                                                    no-console\n   63:1  warning  Function 'generateTestDataSQL' has too many lines (104). Maximum allowed is 50  max-lines-per-function\n  213:1  warning  Async function 'main' has too many lines (61). Maximum allowed is 50            max-lines-per-function\n  214:3  warning  Unexpected console statement                                                    no-console\n  215:3  warning  Unexpected console statement                                                    no-console\n  216:3  warning  Unexpected console statement                                                    no-console\n  217:3  warning  Unexpected console statement                                                    no-console\n  261:5  warning  Unexpected console statement                                                    no-console\n  263:5  warning  Unexpected console statement                                                    no-console\n  264:5  warning  Unexpected console statement                                                    no-console\n  265:5  warning  Unexpected console statement                                                    no-console\n  266:5  warning  Unexpected console statement                                                    no-console\n  267:5  warning  Unexpected console statement                                                    no-console\n  268:5  warning  Unexpected console statement                                                    no-console\n  269:5  warning  Unexpected console statement                                                    no-console\n  270:5  warning  Unexpected console statement                                                    no-console\n  271:5  warning  Unexpected console statement                                                    no-console\n  272:5  warning  Unexpected console statement                                                    no-console\n\n/Users/<USER>/Desktop/vscode/college-employment-survey/backend/index-modular-simple.js\n  121:37  warning  Async arrow function has too many lines (109). Maximum allowed is 50  max-lines-per-function\n  121:37  warning  Async arrow function has a complexity of 11. Maximum allowed is 10    complexity\n  236:5   warning  Unexpected console statement                                          no-console\n  242:28  warning  Async arrow function has too many lines (60). Maximum allowed is 50   max-lines-per-function\n  303:5   warning  Unexpected console statement                                          no-console\n  339:3   warning  Unexpected console statement                                          no-console\n\n/Users/<USER>/Desktop/vscode/college-employment-survey/backend/index.js\n    54:38  warning  Async arrow function has too many lines (53). Maximum allowed is 50                     max-lines-per-function\n   107:5   warning  Unexpected console statement                                                            no-console\n   112:36  warning  Async arrow function has too many lines (110). Maximum allowed is 50                    max-lines-per-function\n   112:36  warning  Async arrow function has a complexity of 12. Maximum allowed is 10                      complexity\n   155:11  error    'majorResult' is assigned a value but never used. Allowed unused vars must match /^_/u  no-unused-vars\n   168:11  error    'offerRate' is assigned a value but never used. Allowed unused vars must match /^_/u    no-unused-vars\n   225:5   warning  Unexpected console statement                                                            no-console\n   230:28  warning  Async arrow function has too many lines (58). Maximum allowed is 50                     max-lines-per-function\n   288:5   warning  Unexpected console statement                                                            no-console\n   326:5   warning  Unexpected console statement                                                            no-console\n   644:58  warning  Async arrow function has a complexity of 12. Maximum allowed is 10                      complexity\n   694:37  warning  Async arrow function has too many lines (112). Maximum allowed is 50                    max-lines-per-function\n   694:37  warning  Async arrow function has a complexity of 14. Maximum allowed is 10                      complexity\n   766:5   warning  Unexpected console statement                                                            no-console\n   767:5   warning  Unexpected console statement                                                            no-console\n   768:5   warning  Unexpected console statement                                                            no-console\n   812:5   warning  Unexpected console statement                                                            no-console\n   818:50  warning  Async arrow function has too many lines (76). Maximum allowed is 50                     max-lines-per-function\n   924:36  warning  Async arrow function has too many lines (131). Maximum allowed is 50                    max-lines-per-function\n   924:36  warning  Async arrow function has a complexity of 20. Maximum allowed is 10                      complexity\n   965:11  warning  Blocks are nested too deeply (5). Maximum allowed is 4                                  max-depth\n   966:13  warning  Blocks are nested too deeply (6). Maximum allowed is 4                                  max-depth\n   988:11  warning  Blocks are nested too deeply (5). Maximum allowed is 4                                  max-depth\n  1070:40  warning  Async arrow function has too many lines (111). Maximum allowed is 50                    max-lines-per-function\n  1200:41  warning  Async arrow function has too many lines (53). Maximum allowed is 50                     max-lines-per-function\n  1260:32  warning  Async arrow function has too many lines (54). Maximum allowed is 50                     max-lines-per-function\n  1327:3   warning  Unexpected console statement                                                            no-console\n\n/Users/<USER>/Desktop/vscode/college-employment-survey/backend/prisma/seed.ts\n    5:1  warning  Async function 'main' has too many lines (153). Maximum allowed is 50  max-lines-per-function\n    5:1  warning  Async function 'main' has a complexity of 15. Maximum allowed is 10    complexity\n    6:3  warning  Unexpected console statement                                           no-console\n   28:3  warning  Unexpected console statement                                           no-console\n   50:3  warning  Unexpected console statement                                           no-console\n  115:3  warning  Unexpected console statement                                           no-console\n  177:3  warning  Unexpected console statement                                           no-console\n  179:3  warning  Unexpected console statement                                           no-console\n  184:5  warning  Unexpected console statement                                           no-console\n\n/Users/<USER>/Desktop/vscode/college-employment-survey/backend/scripts/api-functionality-test.js\n   45:5  warning  Unexpected console statement  no-console\n   59:5  warning  Unexpected console statement  no-console\n  262:3  warning  Unexpected console statement  no-console\n  265:3  warning  Unexpected console statement  no-console\n  270:3  warning  Unexpected console statement  no-console\n  283:3  warning  Unexpected console statement  no-console\n  284:3  warning  Unexpected console statement  no-console\n  287:3  warning  Unexpected console statement  no-console\n  288:3  warning  Unexpected console statement  no-console\n  289:3  warning  Unexpected console statement  no-console\n  293:5  warning  Unexpected console statement  no-console\n  297:3  warning  Unexpected console statement  no-console\n  298:3  warning  Unexpected console statement  no-console\n  299:3  warning  Unexpected console statement  no-console\n  303:5  warning  Unexpected console statement  no-console\n  308:5  warning  Unexpected console statement  no-console\n  316:7  warning  Unexpected console statement  no-console\n  318:7  warning  Unexpected console statement  no-console\n  325:5  warning  Unexpected console statement  no-console\n  327:7  warning  Unexpected console statement  no-console\n  336:3  warning  Unexpected console statement  no-console\n  339:5  warning  Unexpected console statement  no-console\n  341:5  warning  Unexpected console statement  no-console\n  349:7  warning  Unexpected console statement  no-console\n\n/Users/<USER>/Desktop/vscode/college-employment-survey/backend/scripts/cloudflare-migration-plan.js\n   18:5   warning  Unexpected console statement                                                  no-console\n   43:7   warning  Unexpected console statement                                                  no-console\n   46:7   warning  Unexpected console statement                                                  no-console\n   56:5   warning  Unexpected console statement                                                  no-console\n   88:5   warning  Unexpected console statement                                                  no-console\n   97:3   warning  Async method 'migrateToKV' has too many lines (57). Maximum allowed is 50     max-lines-per-function\n   98:5   warning  Unexpected console statement                                                  no-console\n  156:5   warning  Unexpected console statement                                                  no-console\n  165:3   warning  Async method 'migrateToD1' has too many lines (89). Maximum allowed is 50     max-lines-per-function\n  166:5   warning  Unexpected console statement                                                  no-console\n  261:5   warning  Unexpected console statement                                                  no-console\n  270:3   warning  Async method 'migrateToR2' has too many lines (98). Maximum allowed is 50     max-lines-per-function\n  271:5   warning  Unexpected console statement                                                  no-console\n  371:5   warning  Unexpected console statement                                                  no-console\n  381:5   warning  Unexpected console statement                                                  no-console\n  418:5   warning  Unexpected console statement                                                  no-console\n  427:29  error    'structures' is defined but never used. Allowed unused args must match /^_/u  no-unused-vars\n  478:5   warning  Unexpected console statement                                                  no-console\n  486:5   warning  Unexpected console statement                                                  no-console\n  496:5   warning  Unexpected console statement                                                  no-console\n  500:7   warning  Unexpected console statement                                                  no-console\n  510:12  warning  Unexpected console statement                                                  no-console\n\n/Users/<USER>/Desktop/vscode/college-employment-survey/backend/scripts/data-migration-plan.js\n   15:3   warning  Unexpected console statement                                                                   no-console\n   22:11  error    'createOptimizedSchema' is not defined                                                         no-undef\n   42:5   warning  Unexpected console statement                                                                   no-console\n   45:5   warning  Unexpected console statement                                                                   no-console\n   54:3   warning  Unexpected console statement                                                                   no-console\n   71:3   warning  Unexpected console statement                                                                   no-console\n   77:1   warning  Async function 'migrateUsers' has too many lines (53). Maximum allowed is 50                   max-lines-per-function\n   78:3   warning  Unexpected console statement                                                                   no-console\n  135:3   warning  Unexpected console statement                                                                   no-console\n  141:1   warning  Async function 'migrateQuestionnaireResponses' has too many lines (69). Maximum allowed is 50  max-lines-per-function\n  142:3   warning  Unexpected console statement                                                                   no-console\n  216:3   warning  Unexpected console statement                                                                   no-console\n  222:1   warning  Async function 'migrateQuestionnaireVoices' has too many lines (70). Maximum allowed is 50     max-lines-per-function\n  223:3   warning  Unexpected console statement                                                                   no-console\n  298:3   warning  Unexpected console statement                                                                   no-console\n  304:1   warning  Async function 'migrateStories' has too many lines (53). Maximum allowed is 50                 max-lines-per-function\n  305:3   warning  Unexpected console statement                                                                   no-console\n  361:3   warning  Unexpected console statement                                                                   no-console\n  367:1   warning  Async function 'createTestAdminAccounts' has too many lines (62). Maximum allowed is 50        max-lines-per-function\n  368:3   warning  Unexpected console statement                                                                   no-console\n  431:3   warning  Unexpected console statement                                                                   no-console\n  438:3   warning  Unexpected console statement                                                                   no-console\n  447:3   warning  Unexpected console statement                                                                   no-console\n  448:3   warning  Unexpected console statement                                                                   no-console\n  449:3   warning  Unexpected console statement                                                                   no-console\n  450:3   warning  Unexpected console statement                                                                   no-console\n  451:3   warning  Unexpected console statement                                                                   no-console\n  461:5   warning  Unexpected console statement                                                                   no-console\n  464:3   warning  Unexpected console statement                                                                   no-console\n  470:12  warning  Unexpected console statement                                                                   no-console\n\n/Users/<USER>/Desktop/vscode/college-employment-survey/backend/scripts/dataTransformService.js\n  13:42  warning  Static method 'transformQuestionnaireToFrontend' has a complexity of 16. Maximum allowed is 10  complexity\n\n/Users/<USER>/Desktop/vscode/college-employment-survey/backend/scripts/execute-migration.js\n   19:5  warning  Unexpected console statement                                                              no-console\n   40:7  warning  Unexpected console statement                                                              no-console\n   43:7  warning  Unexpected console statement                                                              no-console\n   52:5  warning  Unexpected console statement                                                              no-console\n   60:5  warning  Unexpected console statement                                                              no-console\n   70:3  warning  Async method 'createOptimizedTables' has too many lines (128). Maximum allowed is 50      max-lines-per-function\n   71:5  warning  Unexpected console statement                                                              no-console\n  196:7  warning  Unexpected console statement                                                              no-console\n  208:5  warning  Unexpected console statement                                                              no-console\n  223:3  warning  Async method 'migrateUsers' has too many lines (55). Maximum allowed is 50                max-lines-per-function\n  224:5  warning  Unexpected console statement                                                              no-console\n  276:5  warning  Unexpected console statement                                                              no-console\n  286:3  warning  Async method 'createDefaultQuestionnaire' has too many lines (74). Maximum allowed is 50  max-lines-per-function\n  287:5  warning  Unexpected console statement                                                              no-console\n  359:5  warning  Unexpected console statement                                                              no-console\n  370:3  warning  Async method 'migrateQuestionnaireData' has too many lines (56). Maximum allowed is 50    max-lines-per-function\n  371:5  warning  Unexpected console statement                                                              no-console\n  426:5  warning  Unexpected console statement                                                              no-console\n  436:3  warning  Async method 'migrateStoryData' has too many lines (56). Maximum allowed is 50            max-lines-per-function\n  437:5  warning  Unexpected console statement                                                              no-console\n  490:5  warning  Unexpected console statement                                                              no-console\n  501:5  warning  Unexpected console statement                                                              no-console\n  530:5  warning  Unexpected console statement                                                              no-console\n  541:5  warning  Unexpected console statement                                                              no-console\n  558:5  warning  Unexpected console statement                                                              no-console\n  559:5  warning  Unexpected console statement                                                              no-console\n  560:5  warning  Unexpected console statement                                                              no-console\n  561:5  warning  Unexpected console statement                                                              no-console\n  579:5  warning  Unexpected console statement                                                              no-console\n  590:5  warning  Unexpected console statement                                                              no-console\n  612:5  warning  Unexpected console statement                                                              no-console\n  623:7  warning  Unexpected console statement                                                              no-console\n  627:7  warning  Unexpected console statement                                                              no-console\n\n/Users/<USER>/Desktop/vscode/college-employment-survey/backend/scripts/generate-quality-report.js\n    9:7   error    'path' is assigned a value but never used. Allowed unused vars must match /^_/u  no-unused-vars\n   28:5   warning  Unexpected console statement                                                     no-console\n   50:5   warning  Unexpected console statement                                                     no-console\n   68:5   warning  Unexpected console statement                                                     no-console\n   74:7   warning  Unexpected console statement                                                     no-console\n   99:5   warning  Unexpected console statement                                                     no-console\n  123:5   warning  Unexpected console statement                                                     no-console\n  154:3   warning  Method 'generateSummary' has too many lines (63). Maximum allowed is 50          max-lines-per-function\n  154:18  warning  Method 'generateSummary' has a complexity of 12. Maximum allowed is 10           complexity\n  155:5   warning  Unexpected console statement                                                     no-console\n  229:3   warning  Method 'generateRecommendations' has too many lines (59). Maximum allowed is 50  max-lines-per-function\n  230:5   warning  Unexpected console statement                                                     no-console\n  297:3   warning  Method 'generateHTMLReport' has too many lines (75). Maximum allowed is 50       max-lines-per-function\n  375:5   warning  Unexpected console statement                                                     no-console\n  381:5   warning  Unexpected console statement                                                     no-console\n  386:5   warning  Unexpected console statement                                                     no-console\n  400:5   warning  Unexpected console statement                                                     no-console\n  401:5   warning  Unexpected console statement                                                     no-console\n  402:5   warning  Unexpected console statement                                                     no-console\n  413:16  warning  Unexpected console statement                                                     no-console\n\n/Users/<USER>/Desktop/vscode/college-employment-survey/backend/scripts/generate-test-data.js\n   38:3  warning  Unexpected console statement  no-console\n   74:3  warning  Unexpected console statement  no-console\n   79:3  warning  Unexpected console statement  no-console\n  108:3  warning  Unexpected console statement  no-console\n  113:3  warning  Unexpected console statement  no-console\n  160:3  warning  Unexpected console statement  no-console\n  165:3  warning  Unexpected console statement  no-console\n  206:3  warning  Unexpected console statement  no-console\n  211:3  warning  Unexpected console statement  no-console\n  250:3  warning  Unexpected console statement  no-console\n  256:5  warning  Unexpected console statement  no-console\n  273:5  warning  Unexpected console statement  no-console\n  275:5  warning  Unexpected console statement  no-console\n\n/Users/<USER>/Desktop/vscode/college-employment-survey/backend/scripts/import-mock-data.ts\n  28:35  error  Parsing error: Unexpected token :\n\n/Users/<USER>/Desktop/vscode/college-employment-survey/backend/scripts/init-tag-data.js\n   78:1  warning  Function 'generateSQL' has too many lines (79). Maximum allowed is 50  max-lines-per-function\n  203:5  warning  Unexpected console statement                                           no-console\n  209:5  warning  Unexpected console statement                                           no-console\n  212:5  warning  Unexpected console statement                                           no-console\n  213:5  warning  Unexpected console statement                                           no-console\n  214:5  warning  Unexpected console statement                                           no-console\n  215:5  warning  Unexpected console statement                                           no-console\n  216:5  warning  Unexpected console statement                                           no-console\n  218:5  warning  Unexpected console statement                                           no-console\n  219:5  warning  Unexpected console statement                                           no-console\n  220:5  warning  Unexpected console statement                                           no-console\n  221:5  warning  Unexpected console statement                                           no-console\n  224:5  warning  Unexpected console statement                                           no-console\n\n/Users/<USER>/Desktop/vscode/college-employment-survey/backend/scripts/populate-sequence-numbers.js\n   26:3  warning  Unexpected console statement                                                        no-console\n   37:3  warning  Unexpected console statement                                                        no-console\n   47:7  warning  Unexpected console statement                                                        no-console\n   49:7  warning  Unexpected console statement                                                        no-console\n   53:3  warning  Unexpected console statement                                                        no-console\n   60:3  warning  Unexpected console statement                                                        no-console\n   71:3  warning  Unexpected console statement                                                        no-console\n   85:7  warning  Unexpected console statement                                                        no-console\n   87:7  warning  Unexpected console statement                                                        no-console\n   91:3  warning  Unexpected console statement                                                        no-console\n   97:1  warning  Async function 'populateTagsFields' has too many lines (59). Maximum allowed is 50  max-lines-per-function\n   98:3  warning  Unexpected console statement                                                        no-console\n  107:3  warning  Unexpected console statement                                                        no-console\n  128:7  warning  Unexpected console statement                                                        no-console\n  130:7  warning  Unexpected console statement                                                        no-console\n  141:3  warning  Unexpected console statement                                                        no-console\n  159:7  warning  Unexpected console statement                                                        no-console\n  161:7  warning  Unexpected console statement                                                        no-console\n  165:3  warning  Unexpected console statement                                                        no-console\n  173:5  warning  Unexpected console statement                                                        no-console\n  179:5  warning  Unexpected console statement                                                        no-console\n  185:5  warning  Unexpected console statement                                                        no-console\n  186:5  warning  Unexpected console statement                                                        no-console\n  187:5  warning  Unexpected console statement                                                        no-console\n  190:5  warning  Unexpected console statement                                                        no-console\n\n/Users/<USER>/Desktop/vscode/college-employment-survey/backend/scripts/standards-validator.js\n    7:7   error    'path' is assigned a value but never used. Allowed unused vars must match /^_/u            no-unused-vars\n   59:5   warning  Unexpected console statement                                                               no-console\n   83:7   warning  Unexpected console statement                                                               no-console\n  190:5   warning  Unexpected console statement                                                               no-console\n  192:28  error    'table' is assigned a value but never used. Allowed unused vars must match /^_/u           no-unused-vars\n  210:5   warning  Unexpected console statement                                                               no-console\n  213:30  error    'field' is assigned a value but never used. Allowed unused vars must match /^_/u           no-unused-vars\n  230:5   warning  Unexpected console statement                                                               no-console\n  265:5   warning  Unexpected console statement                                                               no-console\n  301:5   warning  Unexpected console statement                                                               no-console\n  322:5   warning  Unexpected console statement                                                               no-console\n  378:5   warning  Unexpected console statement                                                               no-console\n  379:5   warning  Unexpected console statement                                                               no-console\n  382:5   warning  Unexpected console statement                                                               no-console\n  383:5   warning  Unexpected console statement                                                               no-console\n  384:5   warning  Unexpected console statement                                                               no-console\n  385:5   warning  Unexpected console statement                                                               no-console\n  389:7   warning  Unexpected console statement                                                               no-console\n  391:9   warning  Unexpected console statement                                                               no-console\n  392:9   warning  Unexpected console statement                                                               no-console\n  393:9   warning  Unexpected console statement                                                               no-console\n  399:7   warning  Unexpected console statement                                                               no-console\n  401:9   warning  Unexpected console statement                                                               no-console\n  402:9   warning  Unexpected console statement                                                               no-console\n  403:9   warning  Unexpected console statement                                                               no-console\n  409:7   warning  Unexpected console statement                                                               no-console\n  411:7   warning  Unexpected console statement                                                               no-console\n  451:7   warning  Unexpected console statement                                                               no-console\n  459:16  error    'validateProjectDatabase' is defined but never used. Allowed unused vars must match /^_/u  no-unused-vars\n  483:7   warning  Unexpected console statement                                                               no-console\n\n/Users/<USER>/Desktop/vscode/college-employment-survey/backend/scripts/story-wall-data-validator.js\n   16:7   error    'crypto' is assigned a value but never used. Allowed unused vars must match /^_/u  no-unused-vars\n   66:5   warning  Unexpected console statement                                                       no-console\n   88:5   warning  Unexpected console statement                                                       no-console\n   93:5   warning  Unexpected console statement                                                       no-console\n  114:5   warning  Unexpected console statement                                                       no-console\n  119:5   warning  Unexpected console statement                                                       no-console\n  144:5   warning  Unexpected console statement                                                       no-console\n  154:5   warning  Unexpected console statement                                                       no-console\n  195:5   warning  Unexpected console statement                                                       no-console\n  355:3   warning  Unexpected console statement                                                       no-console\n  368:3   warning  Unexpected console statement                                                       no-console\n  369:3   warning  Unexpected console statement                                                       no-console\n  370:3   warning  Unexpected console statement                                                       no-console\n  371:3   warning  Unexpected console statement                                                       no-console\n  372:3   warning  Unexpected console statement                                                       no-console\n  377:3   warning  Unexpected console statement                                                       no-console\n  381:5   warning  Unexpected console statement                                                       no-console\n  400:5   warning  Unexpected console statement                                                       no-console\n  403:3   warning  Unexpected console statement                                                       no-console\n  405:5   warning  Unexpected console statement                                                       no-console\n  406:5   warning  Unexpected console statement                                                       no-console\n  407:5   warning  Unexpected console statement                                                       no-console\n  410:3   warning  Unexpected console statement                                                       no-console\n  415:16  warning  Unexpected console statement                                                       no-console\n\n/Users/<USER>/Desktop/vscode/college-employment-survey/backend/scripts/test-data-cli.js\n   60:5   warning  Unexpected console statement                                          no-console\n  101:16  warning  Async method 'execute' has a complexity of 15. Maximum allowed is 10  complexity\n  145:9   warning  Unexpected console statement                                          no-console\n  150:7   warning  Unexpected console statement                                          no-console\n  159:5   warning  Unexpected console statement                                          no-console\n  165:5   warning  Unexpected console statement                                          no-console\n  166:5   warning  Unexpected console statement                                          no-console\n  167:5   warning  Unexpected console statement                                          no-console\n  168:5   warning  Unexpected console statement                                          no-console\n  169:5   warning  Unexpected console statement                                          no-console\n  170:5   warning  Unexpected console statement                                          no-console\n  178:7   warning  Unexpected console statement                                          no-console\n  181:7   warning  Unexpected console statement                                          no-console\n  182:7   warning  Unexpected console statement                                          no-console\n  183:7   warning  Unexpected console statement                                          no-console\n  184:7   warning  Unexpected console statement                                          no-console\n  186:7   warning  Unexpected console statement                                          no-console\n  190:5   warning  Unexpected console statement                                          no-console\n  198:7   warning  Unexpected console statement                                          no-console\n  202:5   warning  Unexpected console statement                                          no-console\n  205:5   warning  Unexpected console statement                                          no-console\n  206:5   warning  Unexpected console statement                                          no-console\n  207:5   warning  Unexpected console statement                                          no-console\n  208:5   warning  Unexpected console statement                                          no-console\n  209:5   warning  Unexpected console statement                                          no-console\n  210:5   warning  Unexpected console statement                                          no-console\n  211:5   warning  Unexpected console statement                                          no-console\n  219:7   warning  Unexpected console statement                                          no-console\n  223:5   warning  Unexpected console statement                                          no-console\n  226:5   warning  Unexpected console statement                                          no-console\n  227:5   warning  Unexpected console statement                                          no-console\n  228:5   warning  Unexpected console statement                                          no-console\n  229:5   warning  Unexpected console statement                                          no-console\n  230:5   warning  Unexpected console statement                                          no-console\n  237:5   warning  Unexpected console statement                                          no-console\n  241:5   warning  Unexpected console statement                                          no-console\n  242:5   warning  Unexpected console statement                                          no-console\n  243:5   warning  Unexpected console statement                                          no-console\n  244:5   warning  Unexpected console statement                                          no-console\n  245:5   warning  Unexpected console statement                                          no-console\n  250:7   warning  Unexpected console statement                                          no-console\n  251:7   warning  Unexpected console statement                                          no-console\n  252:7   warning  Unexpected console statement                                          no-console\n  253:7   warning  Unexpected console statement                                          no-console\n  254:7   warning  Unexpected console statement                                          no-console\n  255:7   warning  Unexpected console statement                                          no-console\n  297:5   warning  Unexpected console statement                                          no-console\n  302:7   warning  Unexpected console statement                                          no-console\n  307:7   warning  Unexpected console statement                                          no-console\n  308:7   warning  Unexpected console statement                                          no-console\n  309:7   warning  Unexpected console statement                                          no-console\n  310:7   warning  Unexpected console statement                                          no-console\n  311:7   warning  Unexpected console statement                                          no-console\n  314:5   warning  Unexpected console statement                                          no-console\n  321:5   warning  Unexpected console statement                                          no-console\n  325:5   warning  Unexpected console statement                                          no-console\n  332:5   warning  Unexpected console statement                                          no-console\n  336:5   warning  Unexpected console statement                                          no-console\n  344:5   warning  Unexpected console statement                                          no-console\n\n/Users/<USER>/Desktop/vscode/college-employment-survey/backend/scripts/test-data-manager.js\n   60:7   warning  Unexpected console statement                                                          no-console\n   68:5   warning  Unexpected console statement                                                          no-console\n   88:5   warning  Unexpected console statement                                                          no-console\n   89:5   warning  Unexpected console statement                                                          no-console\n   97:3   warning  Async method 'generateUsers' has too many lines (68). Maximum allowed is 50           max-lines-per-function\n   98:5   warning  Unexpected console statement                                                          no-console\n  162:5   warning  Unexpected console statement                                                          no-console\n  174:3   warning  Async method 'generateQuestionnaires' has too many lines (70). Maximum allowed is 50  max-lines-per-function\n  174:31  warning  Async method 'generateQuestionnaires' has a complexity of 13. Maximum allowed is 10   complexity\n  175:5   warning  Unexpected console statement                                                          no-console\n  250:5   warning  Unexpected console statement                                                          no-console\n  261:3   warning  Async method 'generateStories' has too many lines (61). Maximum allowed is 50         max-lines-per-function\n  262:5   warning  Unexpected console statement                                                          no-console\n  326:5   warning  Unexpected console statement                                                          no-console\n  339:5   warning  Unexpected console statement                                                          no-console\n  347:5   warning  Unexpected console statement                                                          no-console\n  354:5   warning  Unexpected console statement                                                          no-console\n  382:5   warning  Unexpected console statement                                                          no-console\n\n/Users/<USER>/Desktop/vscode/college-employment-survey/backend/scripts/test-data-reset.js\n   23:5   warning  Unexpected console statement                                                   no-console\n   38:7   warning  Unexpected console statement                                                   no-console\n   45:7   warning  Unexpected console statement                                                   no-console\n   54:5   warning  Unexpected console statement                                                   no-console\n   69:5   warning  Unexpected console statement                                                   no-console\n   77:5   warning  Unexpected console statement                                                   no-console\n   90:5   warning  Unexpected console statement                                                   no-console\n   98:5   warning  Unexpected console statement                                                   no-console\n  107:5   warning  Unexpected console statement                                                   no-console\n  115:5   warning  Unexpected console statement                                                   no-console\n  128:5   warning  Unexpected console statement                                                   no-console\n  136:5   warning  Unexpected console statement                                                   no-console\n  142:7   warning  Unexpected console statement                                                   no-console\n  157:7   warning  Unexpected console statement                                                   no-console\n  160:7   warning  Unexpected console statement                                                   no-console\n  211:5   warning  Unexpected console statement                                                   no-console\n  225:5   warning  Unexpected console statement                                                   no-console\n  233:5   warning  Unexpected console statement                                                   no-console\n  245:5   warning  Unexpected console statement                                                   no-console\n  253:5   warning  Unexpected console statement                                                   no-console\n  265:5   warning  Unexpected console statement                                                   no-console\n  273:5   warning  Unexpected console statement                                                   no-console\n  287:7   warning  Unexpected console statement                                                   no-console\n  295:7   warning  Unexpected console statement                                                   no-console\n  298:7   warning  Unexpected console statement                                                   no-console\n  306:20  warning  Async method 'restoreData' has a complexity of 13. Maximum allowed is 10       complexity\n  307:5   warning  Unexpected console statement                                                   no-console\n  312:17  error    'id' is assigned a value but never used. Allowed unused vars must match /^_/u  no-unused-vars\n  315:7   warning  Unexpected console statement                                                   no-console\n  321:17  error    'id' is assigned a value but never used. Allowed unused vars must match /^_/u  no-unused-vars\n  324:7   warning  Unexpected console statement                                                   no-console\n  330:17  error    'id' is assigned a value but never used. Allowed unused vars must match /^_/u  no-unused-vars\n  333:7   warning  Unexpected console statement                                                   no-console\n  339:17  error    'id' is assigned a value but never used. Allowed unused vars must match /^_/u  no-unused-vars\n  342:7   warning  Unexpected console statement                                                   no-console\n  371:7   warning  Unexpected console statement                                                   no-console\n  380:5   warning  Unexpected console statement                                                   no-console\n  391:11  warning  Unexpected console statement                                                   no-console\n  394:9   warning  Unexpected console statement                                                   no-console\n  396:9   warning  Unexpected console statement                                                   no-console\n  399:7   warning  Unexpected console statement                                                   no-console\n\n/Users/<USER>/Desktop/vscode/college-employment-survey/backend/scripts/test-data-transformation.js\n   15:1  warning  Async function 'testQuestionnaireTransformation' has too many lines (80). Maximum allowed is 50  max-lines-per-function\n   16:3  warning  Unexpected console statement                                                                     no-console\n   27:7  warning  Unexpected console statement                                                                     no-console\n   31:5  warning  Unexpected console statement                                                                     no-console\n   32:5  warning  Unexpected console statement                                                                     no-console\n   46:5  warning  Unexpected console statement                                                                     no-console\n   47:5  warning  Unexpected console statement                                                                     no-console\n   88:5  warning  Unexpected console statement                                                                     no-console\n   91:7  warning  Unexpected console statement                                                                     no-console\n   93:9  warning  Unexpected console statement                                                                     no-console\n  100:5  warning  Unexpected console statement                                                                     no-console\n  108:1  warning  Async function 'testStoryTransformation' has too many lines (67). Maximum allowed is 50          max-lines-per-function\n  109:3  warning  Unexpected console statement                                                                     no-console\n  120:7  warning  Unexpected console statement                                                                     no-console\n  124:5  warning  Unexpected console statement                                                                     no-console\n  125:5  warning  Unexpected console statement                                                                     no-console\n  138:5  warning  Unexpected console statement                                                                     no-console\n  139:5  warning  Unexpected console statement                                                                     no-console\n  168:5  warning  Unexpected console statement                                                                     no-console\n  171:7  warning  Unexpected console statement                                                                     no-console\n  173:9  warning  Unexpected console statement                                                                     no-console\n  180:5  warning  Unexpected console statement                                                                     no-console\n  189:3  warning  Unexpected console statement                                                                     no-console\n  197:3  warning  Unexpected console statement                                                                     no-console\n  206:3  warning  Unexpected console statement                                                                     no-console\n  216:3  warning  Unexpected console statement                                                                     no-console\n  224:3  warning  Unexpected console statement                                                                     no-console\n  234:3  warning  Unexpected console statement                                                                     no-console\n  239:3  warning  Unexpected console statement                                                                     no-console\n  240:3  warning  Unexpected console statement                                                                     no-console\n  246:3  warning  Unexpected console statement                                                                     no-console\n  247:3  warning  Unexpected console statement                                                                     no-console\n  256:3  warning  Unexpected console statement                                                                     no-console\n  271:5  warning  Unexpected console statement                                                                     no-console\n  272:5  warning  Unexpected console statement                                                                     no-console\n  273:5  warning  Unexpected console statement                                                                     no-console\n  276:7  warning  Unexpected console statement                                                                     no-console\n  278:7  warning  Unexpected console statement                                                                     no-console\n  282:5  warning  Unexpected console statement                                                                     no-console\n\n/Users/<USER>/Desktop/vscode/college-employment-survey/backend/src/api/admin/audit.controller.ts\n  12:37  error  Parsing error: Unexpected token :\n\n/Users/<USER>/Desktop/vscode/college-employment-survey/backend/src/api/admin/audit.routes.ts\n  12:49  error  Parsing error: Unexpected token )\n\n/Users/<USER>/Desktop/vscode/college-employment-survey/backend/src/api/admin/auto-moderation.controller.ts\n  45:48  error  Parsing error: Unexpected token :\n\n/Users/<USER>/Desktop/vscode/college-employment-survey/backend/src/api/admin/auto-moderation.routes.ts\n  14:58  error  Parsing error: Unexpected token )\n\n/Users/<USER>/Desktop/vscode/college-employment-survey/backend/src/api/admin/content-moderation.controller.ts\n  30:11  error  Parsing error: Unexpected token :\n\n/Users/<USER>/Desktop/vscode/college-employment-survey/backend/src/api/admin/content-moderation.routes.ts\n  14:61  error  Parsing error: Unexpected token )\n\n/Users/<USER>/Desktop/vscode/college-employment-survey/backend/src/api/admin/data-init.controller.ts\n  11:1  error  Parsing error: The keyword 'interface' is reserved\n\n/Users/<USER>/Desktop/vscode/college-employment-survey/backend/src/api/admin/data-init.routes.ts\n  15:1  error  Parsing error: The keyword 'interface' is reserved\n\n/Users/<USER>/Desktop/vscode/college-employment-survey/backend/src/api/admin/data-management.controller.ts\n  7:1  error  Parsing error: The keyword 'interface' is reserved\n\n/Users/<USER>/Desktop/vscode/college-employment-survey/backend/src/api/admin/data-management.routes.ts\n  6:1  error  Parsing error: The keyword 'interface' is reserved\n\n/Users/<USER>/Desktop/vscode/college-employment-survey/backend/src/api/admin/deidentification.routes.ts\n  13:60  error  Parsing error: Unexpected token )\n\n/Users/<USER>/Desktop/vscode/college-employment-survey/backend/src/api/admin/error-analysis.ts\n  43:52  error  Parsing error: Unexpected token :\n\n/Users/<USER>/Desktop/vscode/college-employment-survey/backend/src/api/admin/error-report.ts\n   14:19  warning  Async arrow function has too many lines (71). Maximum allowed is 50  max-lines-per-function\n   87:5   warning  Unexpected console statement                                         no-console\n  121:5   warning  Unexpected console statement                                         no-console\n  170:5   warning  Unexpected console statement                                         no-console\n\n/Users/<USER>/Desktop/vscode/college-employment-survey/backend/src/api/admin/error-visualization.ts\n  148:34  error  Parsing error: Unexpected token :\n\n/Users/<USER>/Desktop/vscode/college-employment-survey/backend/src/api/admin/r2-data-init.controller.ts\n  11:1  error  Parsing error: The keyword 'interface' is reserved\n\n/Users/<USER>/Desktop/vscode/college-employment-survey/backend/src/api/admin/r2-data-init.routes.ts\n  14:1  error  Parsing error: The keyword 'interface' is reserved\n\n/Users/<USER>/Desktop/vscode/college-employment-survey/backend/src/api/admin/review-settings.controller.ts\n  13:42  error  Parsing error: Unexpected token :\n\n/Users/<USER>/Desktop/vscode/college-employment-survey/backend/src/api/admin/review.controller.ts\n  15:43  error  Parsing error: Unexpected token :\n\n/Users/<USER>/Desktop/vscode/college-employment-survey/backend/src/api/admin/review.routes.ts\n  18:50  error  Parsing error: Unexpected token )\n\n/Users/<USER>/Desktop/vscode/college-employment-survey/backend/src/api/admin/role-management.controller.ts\n  97:33  error  Parsing error: Unexpected token :\n\n/Users/<USER>/Desktop/vscode/college-employment-survey/backend/src/api/admin/role-management.routes.ts\n  11:48  error  Parsing error: Unexpected token )\n\n/Users/<USER>/Desktop/vscode/college-employment-survey/backend/src/api/admin/security.ts\n  51:38  error  Parsing error: Unexpected token as\n\n/Users/<USER>/Desktop/vscode/college-employment-survey/backend/src/api/admin/statistics.controller.ts\n  10:38  error  Parsing error: Unexpected token :\n\n/Users/<USER>/Desktop/vscode/college-employment-survey/backend/src/api/admin/statistics.routes.ts\n  7:54  error  Parsing error: Unexpected token )\n\n/Users/<USER>/Desktop/vscode/college-employment-survey/backend/src/api/admin/tag-management.controller.ts\n  10:32  error  Parsing error: Unexpected token :\n\n/Users/<USER>/Desktop/vscode/college-employment-survey/backend/src/api/admin/tag-management.routes.ts\n  23:47  error  Parsing error: Unexpected token )\n\n/Users/<USER>/Desktop/vscode/college-employment-survey/backend/src/api/admin/test-data.controller.ts\n  17:11  error  Parsing error: Unexpected token :\n\n/Users/<USER>/Desktop/vscode/college-employment-survey/backend/src/api/admin/test-data.routes.ts\n  10:52  error  Parsing error: Unexpected token )\n\n/Users/<USER>/Desktop/vscode/college-employment-survey/backend/src/api/admin/user-management.controller.ts\n  11:33  error  Parsing error: Unexpected token :\n\n/Users/<USER>/Desktop/vscode/college-employment-survey/backend/src/api/admin/user-management.routes.ts\n  11:58  error  Parsing error: Unexpected token )\n\n/Users/<USER>/Desktop/vscode/college-employment-survey/backend/src/api/anonymous-auth/anonymous-auth.controller.ts\n  26:11  error  Parsing error: Unexpected token :\n\n/Users/<USER>/Desktop/vscode/college-employment-survey/backend/src/api/anonymous-auth/anonymous-auth.routes.ts\n  10:57  error  Parsing error: Unexpected token )\n\n/Users/<USER>/Desktop/vscode/college-employment-survey/backend/src/api/cloudflare-optimized-api.ts\n  8:1  error  Parsing error: The keyword 'interface' is reserved\n\n/Users/<USER>/Desktop/vscode/college-employment-survey/backend/src/api/comment/comment.routes.ts\n  15:51  error  Parsing error: Unexpected token )\n\n/Users/<USER>/Desktop/vscode/college-employment-survey/backend/src/api/documentation/documentation.controller.ts\n  12:1  error  Parsing error: The keyword 'interface' is reserved\n\n/Users/<USER>/Desktop/vscode/college-employment-survey/backend/src/api/feedback/moderation-feedback.controller.ts\n  34:11  error  Parsing error: Unexpected token :\n\n/Users/<USER>/Desktop/vscode/college-employment-survey/backend/src/api/feedback/moderation-feedback.routes.ts\n  14:62  error  Parsing error: Unexpected token )\n\n/Users/<USER>/Desktop/vscode/college-employment-survey/backend/src/api/notification/notification.controller.ts\n  18:45  error  Parsing error: Unexpected token :\n\n/Users/<USER>/Desktop/vscode/college-employment-survey/backend/src/api/notification/notification.routes.ts\n  13:56  error  Parsing error: Unexpected token )\n\n/Users/<USER>/Desktop/vscode/college-employment-survey/backend/src/api/questionnaire/questionnaire.controller.enhanced.ts\n  17:52  error  Parsing error: Unexpected token :\n\n/Users/<USER>/Desktop/vscode/college-employment-survey/backend/src/api/questionnaire/questionnaire.controller.ts\n  17:39  error  Parsing error: Unexpected token :\n\n/Users/<USER>/Desktop/vscode/college-employment-survey/backend/src/api/questionnaire/questionnaire.d1.controller.ts\n  12:46  error  Parsing error: Unexpected token :\n\n/Users/<USER>/Desktop/vscode/college-employment-survey/backend/src/api/questionnaire/questionnaire.routes.ts\n  14:57  error  Parsing error: Unexpected token )\n\n/Users/<USER>/Desktop/vscode/college-employment-survey/backend/src/api/r2/r2-api.controller.ts\n  11:1  error  Parsing error: The keyword 'interface' is reserved\n\n/Users/<USER>/Desktop/vscode/college-employment-survey/backend/src/api/r2/r2-api.routes.ts\n  13:1  error  Parsing error: The keyword 'interface' is reserved\n\n/Users/<USER>/Desktop/vscode/college-employment-survey/backend/src/api/routes.ts\n  44:1  error  Parsing error: The keyword 'interface' is reserved\n\n/Users/<USER>/Desktop/vscode/college-employment-survey/backend/src/api/story-wall-api.ts\n  17:1  error  Parsing error: The keyword 'interface' is reserved\n\n/Users/<USER>/Desktop/vscode/college-employment-survey/backend/src/api/story/story.controller.ts\n  15:1  error  Parsing error: The keyword 'interface' is reserved\n\n/Users/<USER>/Desktop/vscode/college-employment-survey/backend/src/api/story/story.d1.controller.ts\n  10:1  error  Parsing error: The keyword 'interface' is reserved\n\n/Users/<USER>/Desktop/vscode/college-employment-survey/backend/src/api/story/story.routes.ts\n  14:49  error  Parsing error: Unexpected token )\n\n/Users/<USER>/Desktop/vscode/college-employment-survey/backend/src/api/story/tag-recommendation.routes.ts\n  6:61  error  Parsing error: Unexpected token )\n\n/Users/<USER>/Desktop/vscode/college-employment-survey/backend/src/api/visualization/download.controller.ts\n  7:13  error  Parsing error: Unexpected token {\n\n/Users/<USER>/Desktop/vscode/college-employment-survey/backend/src/api/visualization/export.controller.ts\n  11:13  error  Parsing error: Unexpected token {\n\n/Users/<USER>/Desktop/vscode/college-employment-survey/backend/src/api/visualization/visualization.controller.ts\n  8:1  error  Parsing error: The keyword 'interface' is reserved\n\n/Users/<USER>/Desktop/vscode/college-employment-survey/backend/src/api/visualization/visualization.d1.controller.ts\n  12:45  error  Parsing error: Unexpected token :\n\n/Users/<USER>/Desktop/vscode/college-employment-survey/backend/src/api/websocket/index.ts\n  7:41  error  Parsing error: Unexpected token )\n\n/Users/<USER>/Desktop/vscode/college-employment-survey/backend/src/app.js\n  208:3  warning  Unexpected console statement  no-console\n\n/Users/<USER>/Desktop/vscode/college-employment-survey/backend/src/config.ts\n  11:1  error  Parsing error: The keyword 'interface' is reserved\n\n/Users/<USER>/Desktop/vscode/college-employment-survey/backend/src/constants.ts\n  8:8  error  Parsing error: Unexpected token enum\n\n/Users/<USER>/Desktop/vscode/college-employment-survey/backend/src/controllers/admin.ts\n  11:1  error  Parsing error: The keyword 'interface' is reserved\n\n/Users/<USER>/Desktop/vscode/college-employment-survey/backend/src/controllers/analytics.ts\n  6:13  error  Parsing error: Unexpected token {\n\n/Users/<USER>/Desktop/vscode/college-employment-survey/backend/src/controllers/email.ts\n  8:1  error  Parsing error: The keyword 'interface' is reserved\n\n/Users/<USER>/Desktop/vscode/college-employment-survey/backend/src/controllers/questionnaire.ts\n  7:1  error  Parsing error: The keyword 'interface' is reserved\n\n/Users/<USER>/Desktop/vscode/college-employment-survey/backend/src/controllers/responses.ts\n  24:40  error  Parsing error: Unexpected token :\n\n/Users/<USER>/Desktop/vscode/college-employment-survey/backend/src/controllers/statistics.ts\n  17:2  error  Parsing error: Unexpected token ;\n\n/Users/<USER>/Desktop/vscode/college-employment-survey/backend/src/middleware/auth.js\n   28:7   warning  Unexpected console statement                                              no-console\n   40:38  error    'secret' is defined but never used. Allowed unused args must match /^_/u  no-unused-vars\n  210:7   warning  Unexpected console statement                                              no-console\n  275:21  error    'createErrorResponse' is not defined                                      no-undef\n\n/Users/<USER>/Desktop/vscode/college-employment-survey/backend/src/middleware/validation.js\n   44:7  warning  Unexpected console statement                                              no-console\n   89:1  warning  Function 'validateField' has too many lines (74). Maximum allowed is 50   max-lines-per-function\n   89:1  warning  Function 'validateField' has a complexity of 25. Maximum allowed is 10    complexity\n  182:1  warning  Function 'validateType' has a complexity of 21. Maximum allowed is 10     complexity\n  205:5  error    Unexpected lexical declaration in case block                              no-case-declarations\n  237:1  warning  Function 'applyTransforms' has a complexity of 12. Maximum allowed is 10  complexity\n\n/Users/<USER>/Desktop/vscode/college-employment-survey/backend/src/middlewares/adminAuth.middleware.ts\n  4:1  error  Parsing error: The keyword 'interface' is reserved\n\n/Users/<USER>/Desktop/vscode/college-employment-survey/backend/src/middlewares/adminAuth.ts\n  4:1  error  Parsing error: The keyword 'interface' is reserved\n\n/Users/<USER>/Desktop/vscode/college-employment-survey/backend/src/middlewares/anomalyDetection.middleware.ts\n  18:9  error  Parsing error: Unexpected token :\n\n/Users/<USER>/Desktop/vscode/college-employment-survey/backend/src/middlewares/auth.middleware.ts\n  9:39  error  Parsing error: Unexpected token :\n\n/Users/<USER>/Desktop/vscode/college-employment-survey/backend/src/middlewares/contentModeration.middleware.ts\n  16:1  error  Parsing error: The keyword 'interface' is reserved\n\n/Users/<USER>/Desktop/vscode/college-employment-survey/backend/src/middlewares/permission.middleware.ts\n  16:56  error  Parsing error: Unexpected token :\n\n/Users/<USER>/Desktop/vscode/college-employment-survey/backend/src/middlewares/rateLimit.middleware.ts\n  3:1  error  Parsing error: The keyword 'interface' is reserved\n\n/Users/<USER>/Desktop/vscode/college-employment-survey/backend/src/middlewares/rateLimit.ts\n  3:1  error  Parsing error: The keyword 'interface' is reserved\n\n/Users/<USER>/Desktop/vscode/college-employment-survey/backend/src/middlewares/reviewerAuth.middleware.ts\n  13:47  error  Parsing error: Unexpected token :\n\n/Users/<USER>/Desktop/vscode/college-employment-survey/backend/src/middlewares/riskAssessment.middleware.ts\n  19:13  error  Parsing error: Unexpected token :\n\n/Users/<USER>/Desktop/vscode/college-employment-survey/backend/src/middlewares/role.middleware.ts\n  8:8  error  Parsing error: Unexpected token interface\n\n/Users/<USER>/Desktop/vscode/college-employment-survey/backend/src/middlewares/superAdminAuth.middleware.ts\n  13:49  error  Parsing error: Unexpected token :\n\n/Users/<USER>/Desktop/vscode/college-employment-survey/backend/src/mock/index.ts\n  27:33  error  Parsing error: Unexpected token :\n\n/Users/<USER>/Desktop/vscode/college-employment-survey/backend/src/mock/responses.ts\n  5:8  error  Parsing error: Unexpected token interface\n\n/Users/<USER>/Desktop/vscode/college-employment-survey/backend/src/mock/statistics.ts\n  87:47  error  Parsing error: Unexpected token :\n\n/Users/<USER>/Desktop/vscode/college-employment-survey/backend/src/mock/stories.ts\n  5:8  error  Parsing error: Unexpected token interface\n\n/Users/<USER>/Desktop/vscode/college-employment-survey/backend/src/mock/tags.ts\n  5:8  error  Parsing error: Unexpected token interface\n\n/Users/<USER>/Desktop/vscode/college-employment-survey/backend/src/routes/deidentification.js\n   87:67  warning  Async arrow function has a complexity of 13. Maximum allowed is 10   complexity\n  136:58  warning  Async arrow function has too many lines (51). Maximum allowed is 50  max-lines-per-function\n\n/Users/<USER>/Desktop/vscode/college-employment-survey/backend/src/routes/questionnaire.js\n    7:33  error    'createErrorResponse' is defined but never used. Allowed unused vars must match /^_/u  no-unused-vars\n   13:52  warning  Async arrow function has too many lines (104). Maximum allowed is 50                   max-lines-per-function\n  129:53  warning  Async arrow function has too many lines (53). Maximum allowed is 50                    max-lines-per-function\n\n/Users/<USER>/Desktop/vscode/college-employment-survey/backend/src/routes/reviewer.routes.ts\n   22:40  warning  Async arrow function has too many lines (75). Maximum allowed is 50  max-lines-per-function\n   22:40  warning  Async arrow function has a complexity of 12. Maximum allowed is 10   complexity\n   25:5   warning  Unexpected console statement                                         no-console\n   86:5   warning  Unexpected console statement                                         no-console\n   94:5   warning  Unexpected console statement                                         no-console\n  153:5   warning  Unexpected console statement                                         no-console\n  192:5   warning  Unexpected console statement                                         no-console\n  231:5   warning  Unexpected console statement                                         no-console\n\n/Users/<USER>/Desktop/vscode/college-employment-survey/backend/src/routes/story.js\n  13:43  warning  Async arrow function has too many lines (55). Maximum allowed is 50  max-lines-per-function\n\n/Users/<USER>/Desktop/vscode/college-employment-survey/backend/src/routes/system.js\n  51:55  warning  Async arrow function has too many lines (53). Maximum allowed is 50  max-lines-per-function\n\n/Users/<USER>/Desktop/vscode/college-employment-survey/backend/src/security/config.ts\n  10:21  error  Parsing error: Unexpected token :\n\n/Users/<USER>/Desktop/vscode/college-employment-survey/backend/src/security/errors.ts\n  9:10  error  Parsing error: Unexpected token code\n\n/Users/<USER>/Desktop/vscode/college-employment-survey/backend/src/security/index.ts\n  34:11  error  Parsing error: Unexpected token config\n\n/Users/<USER>/Desktop/vscode/college-employment-survey/backend/src/security/services/alertManager.ts\n  14:1  error  Parsing error: The keyword 'interface' is reserved\n\n/Users/<USER>/Desktop/vscode/college-employment-survey/backend/src/security/services/autoFixer.ts\n  13:1  error  Parsing error: The keyword 'interface' is reserved\n\n/Users/<USER>/Desktop/vscode/college-employment-survey/backend/src/security/services/behaviorAnalyzer.ts\n  12:1  error  Parsing error: The keyword 'interface' is reserved\n\n/Users/<USER>/Desktop/vscode/college-employment-survey/backend/src/security/services/captchaVerifier.ts\n  13:11  error  Parsing error: Unexpected token config\n\n/Users/<USER>/Desktop/vscode/college-employment-survey/backend/src/security/services/errorAnalyzer.ts\n  13:1  error  Parsing error: The keyword 'interface' is reserved\n\n/Users/<USER>/Desktop/vscode/college-employment-survey/backend/src/security/services/fingerprintTracker.ts\n  14:11  error  Parsing error: Unexpected token config\n\n/Users/<USER>/Desktop/vscode/college-employment-survey/backend/src/security/services/honeypotDetector.ts\n  13:11  error  Parsing error: Unexpected token config\n\n/Users/<USER>/Desktop/vscode/college-employment-survey/backend/src/security/services/logBuffer.ts\n  12:1  error  Parsing error: The keyword 'interface' is reserved\n\n/Users/<USER>/Desktop/vscode/college-employment-survey/backend/src/security/services/memoryCache.ts\n  10:1  error  Parsing error: The keyword 'interface' is reserved\n\n/Users/<USER>/Desktop/vscode/college-employment-survey/backend/src/security/services/rateLimiter.ts\n  14:11  error  Parsing error: Unexpected token config\n\n/Users/<USER>/Desktop/vscode/college-employment-survey/backend/src/security/services/securityLogger.ts\n  14:1  error  Parsing error: The keyword 'enum' is reserved\n\n/Users/<USER>/Desktop/vscode/college-employment-survey/backend/src/security/types.ts\n  8:8  error  Parsing error: Unexpected token interface\n\n/Users/<USER>/Desktop/vscode/college-employment-survey/backend/src/services/ai-provider.service.ts\n  8:1  error  Parsing error: The keyword 'interface' is reserved\n\n/Users/<USER>/Desktop/vscode/college-employment-survey/backend/src/services/ai/ai-service.ts\n  19:8  error  Parsing error: Unexpected token interface\n\n/Users/<USER>/Desktop/vscode/college-employment-survey/backend/src/services/ai/config.ts\n  7:8  error  Parsing error: Unexpected token interface\n\n/Users/<USER>/Desktop/vscode/college-employment-survey/backend/src/services/anomalyDetectionService.ts\n  11:8  error  Parsing error: Unexpected token enum\n\n/Users/<USER>/Desktop/vscode/college-employment-survey/backend/src/services/auditLogService.ts\n  11:8  error  Parsing error: Unexpected token enum\n\n/Users/<USER>/Desktop/vscode/college-employment-survey/backend/src/services/authService.js\n  48:3   warning  Async method 'registerAnonymous' has too many lines (54). Maximum allowed is 50  max-lines-per-function\n  49:13  error    Identifier 'identity_a' is not in camel case                                     camelcase\n  49:25  error    Identifier 'identity_b' is not in camel case                                     camelcase\n  49:37  error    Identifier 'display_name' is not in camel case                                   camelcase\n  49:51  error    Identifier 'education_level' is not in camel case                                camelcase\n  49:68  error    Identifier 'industry_code' is not in camel case                                  camelcase\n  49:83  error    Identifier 'graduation_year' is not in camel case                                camelcase\n  80:21  error    Identifier 'display_name' is not in camel case                                   camelcase\n  81:7   error    Identifier 'education_level' is not in camel case                                camelcase\n  83:7   error    Identifier 'industry_code' is not in camel case                                  camelcase\n  85:7   error    Identifier 'graduation_year' is not in camel case                                camelcase\n\n/Users/<USER>/Desktop/vscode/college-employment-survey/backend/src/services/autoModeration/asyncModerationService.ts\n  17:11  error  Parsing error: Unexpected token static\n\n/Users/<USER>/Desktop/vscode/college-employment-survey/backend/src/services/autoModeration/autoModerationMiddleware.ts\n  23:59  error  Parsing error: Unexpected token :\n\n/Users/<USER>/Desktop/vscode/college-employment-survey/backend/src/services/autoModeration/autoModerationService.ts\n  13:8  error  Parsing error: Unexpected token interface\n\n/Users/<USER>/Desktop/vscode/college-employment-survey/backend/src/services/dataInitService.ts\n  21:11  error  Parsing error: Unexpected token kvStorage\n\n/Users/<USER>/Desktop/vscode/college-employment-survey/backend/src/services/dataTransformService.ts\n  10:8  error  Parsing error: Unexpected token interface\n\n/Users/<USER>/Desktop/vscode/college-employment-survey/backend/src/services/deidentificationService.ts\n  11:8  error  Parsing error: Unexpected token interface\n\n/Users/<USER>/Desktop/vscode/college-employment-survey/backend/src/services/emailService.ts\n  5:21  error  Parsing error: Unexpected token :\n\n/Users/<USER>/Desktop/vscode/college-employment-survey/backend/src/services/kvStorageService.ts\n  29:11  error  Parsing error: Unexpected token kv\n\n/Users/<USER>/Desktop/vscode/college-employment-survey/backend/src/services/mockDataService.ts\n  11:8  warning  Function 'getMockQuestionnaireStats' has too many lines (62). Maximum allowed is 50  max-lines-per-function\n\n/Users/<USER>/Desktop/vscode/college-employment-survey/backend/src/services/notification/notificationService.ts\n  18:11  error  Parsing error: Unexpected token static\n\n/Users/<USER>/Desktop/vscode/college-employment-survey/backend/src/services/permissionService.ts\n  11:8  error  Parsing error: Unexpected token enum\n\n/Users/<USER>/Desktop/vscode/college-employment-survey/backend/src/services/r2StorageService.ts\n  24:11  error  Parsing error: Unexpected token r2\n\n/Users/<USER>/Desktop/vscode/college-employment-survey/backend/src/services/reviewAssignmentService.ts\n  10:8  error  Parsing error: Unexpected token enum\n\n/Users/<USER>/Desktop/vscode/college-employment-survey/backend/src/services/reviewService.ts\n  14:8  error  Parsing error: Unexpected token enum\n\n/Users/<USER>/Desktop/vscode/college-employment-survey/backend/src/services/riskAssessmentService.ts\n  11:8  error  Parsing error: Unexpected token enum\n\n/Users/<USER>/Desktop/vscode/college-employment-survey/backend/src/services/testDataService.ts\n  18:31  error  Parsing error: Unexpected token :\n\n/Users/<USER>/Desktop/vscode/college-employment-survey/backend/src/services/websocket.service.ts\n  4:1  error  Parsing error: The keyword 'interface' is reserved\n\n/Users/<USER>/Desktop/vscode/college-employment-survey/backend/src/tests/anonymous-auth.test.ts\n   7:44  warning  Arrow function has too many lines (81). Maximum allowed is 50  max-lines-per-function\n  52:14  warning  Too many nested callbacks (4). Maximum allowed is 3            max-nested-callbacks\n  53:14  warning  Too many nested callbacks (4). Maximum allowed is 3            max-nested-callbacks\n\n/Users/<USER>/Desktop/vscode/college-employment-survey/backend/src/types.ts\n  4:8  error  Parsing error: Unexpected token interface\n\n/Users/<USER>/Desktop/vscode/college-employment-survey/backend/src/utils/exportUtils.ts\n  10:31  error  Parsing error: Unexpected token :\n\n/Users/<USER>/Desktop/vscode/college-employment-survey/backend/src/utils/jwt.ts\n  11:38  error  Parsing error: Unexpected token :\n\n/Users/<USER>/Desktop/vscode/college-employment-survey/backend/src/utils/prisma.ts\n  5:17  error  Parsing error: Unexpected token :\n\n/Users/<USER>/Desktop/vscode/college-employment-survey/backend/src/utils/response.js\n   13:10  error    'createSuccessResponse' is defined but never used. Allowed unused vars must match /^_/u  no-unused-vars\n  151:7   warning  Unexpected console statement                                                             no-console\n\n/Users/<USER>/Desktop/vscode/college-employment-survey/backend/src/utils/sensitiveWordFilter.ts\n  11:48  error  Parsing error: Unexpected token :\n\n/Users/<USER>/Desktop/vscode/college-employment-survey/backend/src/utils/uuidGenerator.test.ts\n   7:28  warning  Arrow function has too many lines (76). Maximum allowed is 50  max-lines-per-function\n  64:14  warning  Too many nested callbacks (4). Maximum allowed is 3            max-nested-callbacks\n  65:14  warning  Too many nested callbacks (4). Maximum allowed is 3            max-nested-callbacks\n\n/Users/<USER>/Desktop/vscode/college-employment-survey/backend/src/utils/uuidGenerator.ts\n  17:31  error  Parsing error: Unexpected token :\n\n/Users/<USER>/Desktop/vscode/college-employment-survey/backend/src/utils/uuidGenerator.v2.ts\n  36:3  error  Parsing error: Unexpected token as\n\n/Users/<USER>/Desktop/vscode/college-employment-survey/backend/test-api-fixes.js\n   14:1   warning  Unexpected console statement                                                  no-console\n   19:5   warning  Unexpected console statement                                                  no-console\n   22:3   warning  Unexpected console statement                                                  no-console\n   26:1   warning  Async function 'testQueries' has too many lines (119). Maximum allowed is 50  max-lines-per-function\n   27:3   warning  Unexpected console statement                                                  no-console\n   33:9   warning  Unexpected console statement                                                  no-console\n   36:9   warning  Unexpected console statement                                                  no-console\n   51:9   warning  Unexpected console statement                                                  no-console\n   54:9   warning  Unexpected console statement                                                  no-console\n   56:11  warning  Unexpected console statement                                                  no-console\n   72:9   warning  Unexpected console statement                                                  no-console\n   75:9   warning  Unexpected console statement                                                  no-console\n   77:11  warning  Unexpected console statement                                                  no-console\n   93:9   warning  Unexpected console statement                                                  no-console\n   96:9   warning  Unexpected console statement                                                  no-console\n   98:11  warning  Unexpected console statement                                                  no-console\n  106:3   warning  Unexpected console statement                                                  no-console\n  110:9   warning  Unexpected console statement                                                  no-console\n  113:9   warning  Unexpected console statement                                                  no-console\n  136:9   warning  Unexpected console statement                                                  no-console\n  139:9   warning  Unexpected console statement                                                  no-console\n  141:11  warning  Unexpected console statement                                                  no-console\n  142:11  warning  Unexpected console statement                                                  no-console\n  143:11  warning  Unexpected console statement                                                  no-console\n  150:3   warning  Unexpected console statement                                                  no-console\n  156:5   warning  Unexpected console statement                                                  no-console\n  161:5   warning  Unexpected console statement                                                  no-console\n\n/Users/<USER>/Desktop/vscode/college-employment-survey/backend/test-api.js\n    23:3   warning  Unexpected console statement                                                            no-console\n    29:3   warning  Unexpected console statement                                                            no-console\n    34:30  warning  Async arrow function has too many lines (70). Maximum allowed is 50                     max-lines-per-function\n    35:3   warning  Unexpected console statement                                                            no-console\n    38:5   warning  Unexpected console statement                                                            no-console\n    58:7   warning  Unexpected console statement                                                            no-console\n    67:7   warning  Unexpected console statement                                                            no-console\n    76:7   warning  Unexpected console statement                                                            no-console\n    83:5   warning  Unexpected console statement                                                            no-console\n   103:5   warning  Unexpected console statement                                                            no-console\n   113:39  warning  Async arrow function has too many lines (83). Maximum allowed is 50                     max-lines-per-function\n   114:3   warning  Unexpected console statement                                                            no-console\n   159:5   warning  Unexpected console statement                                                            no-console\n   189:5   warning  Unexpected console statement                                                            no-console\n   192:5   warning  Unexpected console statement                                                            no-console\n   202:39  warning  Async arrow function has too many lines (62). Maximum allowed is 50                     max-lines-per-function\n   203:3   warning  Unexpected console statement                                                            no-console\n   230:5   warning  Unexpected console statement                                                            no-console\n   260:5   warning  Unexpected console statement                                                            no-console\n   271:3   warning  Unexpected console statement                                                            no-console\n   282:3   warning  Unexpected console statement                                                            no-console\n   293:3   warning  Unexpected console statement                                                            no-console\n   303:29  warning  Async arrow function has too many lines (71). Maximum allowed is 50                     max-lines-per-function\n   304:3   warning  Unexpected console statement                                                            no-console\n   306:52  error    'status' is assigned a value but never used. Allowed unused vars must match /^_/u       no-unused-vars\n   312:13  error    'searchLower' is assigned a value but never used. Allowed unused vars must match /^_/u  no-unused-vars\n   343:5   warning  Unexpected console statement                                                            no-console\n   370:5   warning  Unexpected console statement                                                            no-console\n   380:30  warning  Async arrow function has too many lines (62). Maximum allowed is 50                     max-lines-per-function\n   381:3   warning  Unexpected console statement                                                            no-console\n   383:42  error    'password' is assigned a value but never used. Allowed unused vars must match /^_/u     no-unused-vars\n   423:5   warning  Unexpected console statement                                                            no-console\n   440:5   warning  Unexpected console statement                                                            no-console\n   450:33  warning  Async arrow function has too many lines (73). Maximum allowed is 50                     max-lines-per-function\n   450:33  warning  Async arrow function has a complexity of 13. Maximum allowed is 10                      complexity\n   451:3   warning  Unexpected console statement                                                            no-console\n   504:5   warning  Unexpected console statement                                                            no-console\n   522:5   warning  Unexpected console statement                                                            no-console\n   533:3   warning  Unexpected console statement                                                            no-console\n   562:5   warning  Unexpected console statement                                                            no-console\n   569:5   warning  Unexpected console statement                                                            no-console\n   579:29  warning  Async arrow function has too many lines (74). Maximum allowed is 50                     max-lines-per-function\n   580:3   warning  Unexpected console statement                                                            no-console\n   648:5   warning  Unexpected console statement                                                            no-console\n   658:39  warning  Async arrow function has too many lines (95). Maximum allowed is 50                     max-lines-per-function\n   659:3   warning  Unexpected console statement                                                            no-console\n   749:5   warning  Unexpected console statement                                                            no-console\n   760:3   warning  Unexpected console statement                                                            no-console\n   792:5   warning  Unexpected console statement                                                            no-console\n   803:3   warning  Unexpected console statement                                                            no-console\n   824:5   warning  Unexpected console statement                                                            no-console\n   834:34  warning  Async arrow function has too many lines (137). Maximum allowed is 50                    max-lines-per-function\n   835:3   warning  Unexpected console statement                                                            no-console\n   963:5   warning  Unexpected console statement                                                            no-console\n   976:5   warning  Unexpected console statement                                                            no-console\n   987:3   warning  Unexpected console statement                                                            no-console\n  1013:5   warning  Unexpected console statement                                                            no-console\n  1020:5   warning  Unexpected console statement                                                            no-console\n  1031:3   warning  Unexpected console statement                                                            no-console\n  1033:13  error    'username' is assigned a value but never used. Allowed unused vars must match /^_/u     no-unused-vars\n  1033:23  error    'role' is assigned a value but never used. Allowed unused vars must match /^_/u         no-unused-vars\n  1033:29  error    'action' is assigned a value but never used. Allowed unused vars must match /^_/u       no-unused-vars\n  1033:37  error    'resource' is assigned a value but never used. Allowed unused vars must match /^_/u     no-unused-vars\n  1033:47  error    'status' is assigned a value but never used. Allowed unused vars must match /^_/u       no-unused-vars\n  1033:55  error    'riskLevel' is assigned a value but never used. Allowed unused vars must match /^_/u    no-unused-vars\n  1033:66  error    'startDate' is assigned a value but never used. Allowed unused vars must match /^_/u    no-unused-vars\n  1033:77  error    'endDate' is assigned a value but never used. Allowed unused vars must match /^_/u      no-unused-vars\n  1045:5   warning  Unexpected console statement                                                            no-console\n  1053:5   warning  Unexpected console statement                                                            no-console\n  1063:28  warning  Async arrow function has too many lines (94). Maximum allowed is 50                     max-lines-per-function\n  1064:3   warning  Unexpected console statement                                                            no-console\n  1129:5   warning  Unexpected console statement                                                            no-console\n  1160:5   warning  Unexpected console statement                                                            no-console\n  1170:34  warning  Async arrow function has too many lines (87). Maximum allowed is 50                     max-lines-per-function\n  1171:3   warning  Unexpected console statement                                                            no-console\n  1253:5   warning  Unexpected console statement                                                            no-console\n  1264:3   warning  Unexpected console statement                                                            no-console\n  1289:5   warning  Unexpected console statement                                                            no-console\n  1297:5   warning  Unexpected console statement                                                            no-console\n  1308:3   warning  Unexpected console statement                                                            no-console\n  1333:5   warning  Unexpected console statement                                                            no-console\n  1341:5   warning  Unexpected console statement                                                            no-console\n  1352:3   warning  Unexpected console statement                                                            no-console\n  1362:25  error    'next' is defined but never used. Allowed unused args must match /^_/u                  no-unused-vars\n  1363:3   warning  Unexpected console statement                                                            no-console\n  1372:1   warning  Async function 'initTestData' has too many lines (95). Maximum allowed is 50            max-lines-per-function\n  1377:7   warning  Unexpected console statement                                                            no-console\n  1462:7   warning  Unexpected console statement                                                            no-console\n  1464:7   warning  Unexpected console statement                                                            no-console\n  1467:5   warning  Unexpected console statement                                                            no-console\n  1473:3   warning  Unexpected console statement                                                            no-console\n  1474:3   warning  Unexpected console statement                                                            no-console\n  1475:3   warning  Unexpected console statement                                                            no-console\n  1483:3   warning  Unexpected console statement                                                            no-console\n  1485:5   warning  Unexpected console statement                                                            no-console\n  1492:3   warning  Unexpected console statement                                                            no-console\n  1496:43  error    'promise' is defined but never used. Allowed unused args must match /^_/u               no-unused-vars\n  1497:3   warning  Unexpected console statement                                                            no-console\n\n/Users/<USER>/Desktop/vscode/college-employment-survey/backend/test-enhanced-features.js\n   75:1   warning  Async function 'runTests' has too many lines (133). Maximum allowed is 50  max-lines-per-function\n   75:1   warning  Async function 'runTests' has a complexity of 31. Maximum allowed is 10    complexity\n   76:3   warning  Unexpected console statement                                               no-console\n   82:5   warning  Unexpected console statement                                               no-console\n   85:7   warning  Unexpected console statement                                               no-console\n   87:9   warning  Unexpected console statement                                               no-console\n   90:7   warning  Unexpected console statement                                               no-console\n   92:5   warning  Unexpected console statement                                               no-console\n   95:5   warning  Unexpected console statement                                               no-console\n   98:7   warning  Unexpected console statement                                               no-console\n  100:9   warning  Unexpected console statement                                               no-console\n  103:7   warning  Unexpected console statement                                               no-console\n  105:5   warning  Unexpected console statement                                               no-console\n  108:5   warning  Unexpected console statement                                               no-console\n  112:7   warning  Unexpected console statement                                               no-console\n  114:7   warning  Unexpected console statement                                               no-console\n  116:5   warning  Unexpected console statement                                               no-console\n  120:7   warning  Unexpected console statement                                               no-console\n  128:9   warning  Unexpected console statement                                               no-console\n  130:9   warning  Unexpected console statement                                               no-console\n  132:7   warning  Unexpected console statement                                               no-console\n  136:5   warning  Unexpected console statement                                               no-console\n  139:7   warning  Unexpected console statement                                               no-console\n  141:9   warning  Unexpected console statement                                               no-console\n  144:7   warning  Unexpected console statement                                               no-console\n  146:5   warning  Unexpected console statement                                               no-console\n  149:5   warning  Unexpected console statement                                               no-console\n  152:7   warning  Unexpected console statement                                               no-console\n  154:9   warning  Unexpected console statement                                               no-console\n  155:9   warning  Unexpected console statement                                               no-console\n  158:7   warning  Unexpected console statement                                               no-console\n  160:5   warning  Unexpected console statement                                               no-console\n  163:5   warning  Unexpected console statement                                               no-console\n  166:7   warning  Unexpected console statement                                               no-console\n  168:9   warning  Unexpected console statement                                               no-console\n  171:7   warning  Unexpected console statement                                               no-console\n  173:5   warning  Unexpected console statement                                               no-console\n  177:7   warning  Unexpected console statement                                               no-console\n  180:9   warning  Unexpected console statement                                               no-console\n  182:9   warning  Unexpected console statement                                               no-console\n  184:7   warning  Unexpected console statement                                               no-console\n  188:5   warning  Unexpected console statement                                               no-console\n  191:7   warning  Unexpected console statement                                               no-console\n  194:11  warning  Unexpected console statement                                               no-console\n  198:7   warning  Unexpected console statement                                               no-console\n  202:5   warning  Unexpected console statement                                               no-console\n  205:3   warning  Unexpected console statement                                               no-console\n  206:3   warning  Unexpected console statement                                               no-console\n  207:3   warning  Unexpected console statement                                               no-console\n  208:3   warning  Unexpected console statement                                               no-console\n  209:3   warning  Unexpected console statement                                               no-console\n  210:3   warning  Unexpected console statement                                               no-console\n  211:3   warning  Unexpected console statement                                               no-console\n  212:3   warning  Unexpected console statement                                               no-console\n  214:3   warning  Unexpected console statement                                               no-console\n  215:3   warning  Unexpected console statement                                               no-console\n  216:3   warning  Unexpected console statement                                               no-console\n  217:3   warning  Unexpected console statement                                               no-console\n  218:3   warning  Unexpected console statement                                               no-console\n  219:3   warning  Unexpected console statement                                               no-console\n  223:18  warning  Unexpected console statement                                               no-console\n\n/Users/<USER>/Desktop/vscode/college-employment-survey/backend/test-page-verification.js\n   38:1   warning  Async function 'testEndpoint' has too many lines (60). Maximum allowed is 50  max-lines-per-function\n   39:22  warning  Arrow function has too many lines (58). Maximum allowed is 50                 max-lines-per-function\n  107:1   warning  Async function 'runTests' has too many lines (55). Maximum allowed is 50      max-lines-per-function\n  107:1   warning  Async function 'runTests' has a complexity of 12. Maximum allowed is 10       complexity\n  108:3   warning  Unexpected console statement                                                  no-console\n  113:5   warning  Unexpected console statement                                                  no-console\n  118:7   warning  Unexpected console statement                                                  no-console\n  123:11  warning  Unexpected console statement                                                  no-console\n  124:11  warning  Blocks are nested too deeply (5). Maximum allowed is 4                        max-depth\n  125:13  warning  Unexpected console statement                                                  no-console\n  128:11  warning  Unexpected console statement                                                  no-console\n  130:11  warning  Unexpected console statement                                                  no-console\n  134:7   warning  Unexpected console statement                                                  no-console\n  136:9   warning  Unexpected console statement                                                  no-console\n  139:5   warning  Unexpected console statement                                                  no-console\n  143:3   warning  Unexpected console statement                                                  no-console\n  144:3   warning  Unexpected console statement                                                  no-console\n  149:3   warning  Unexpected console statement                                                  no-console\n  150:3   warning  Unexpected console statement                                                  no-console\n  151:3   warning  Unexpected console statement                                                  no-console\n  154:5   warning  Unexpected console statement                                                  no-console\n  156:5   warning  Unexpected console statement                                                  no-console\n  160:3   warning  Unexpected console statement                                                  no-console\n  161:3   warning  Unexpected console statement                                                  no-console\n  162:3   warning  Unexpected console statement                                                  no-console\n  163:3   warning  Unexpected console statement                                                  no-console\n  164:3   warning  Unexpected console statement                                                  no-console\n  166:3   warning  Unexpected console statement                                                  no-console\n  167:3   warning  Unexpected console statement                                                  no-console\n  168:3   warning  Unexpected console statement                                                  no-console\n  169:3   warning  Unexpected console statement                                                  no-console\n  170:3   warning  Unexpected console statement                                                  no-console\n  174:18  warning  Unexpected console statement                                                  no-console\n\n/Users/<USER>/Desktop/vscode/college-employment-survey/backend/test-questionnaire-submit.js\n  37:5  warning  Unexpected console statement  no-console\n  39:5  warning  Unexpected console statement  no-console\n\n/Users/<USER>/Desktop/vscode/college-employment-survey/backend/test-real-database-api.js\n   68:1   warning  Async function 'runRealDatabaseTests' has too many lines (199). Maximum allowed is 50  max-lines-per-function\n   69:3   warning  Unexpected console statement                                                           no-console\n   80:5   warning  Unexpected console statement                                                           no-console\n   85:7   warning  Unexpected console statement                                                           no-console\n   88:7   warning  Unexpected console statement                                                           no-console\n  101:5   warning  Unexpected console statement                                                           no-console\n  115:5   warning  Unexpected console statement                                                           no-console\n  123:7   warning  Unexpected console statement                                                           no-console\n  137:7   warning  Unexpected console statement                                                           no-console\n  148:5   warning  Unexpected console statement                                                           no-console\n  161:5   warning  Unexpected console statement                                                           no-console\n  163:7   warning  Unexpected console statement                                                           no-console\n  178:5   warning  Unexpected console statement                                                           no-console\n  182:7   warning  Unexpected console statement                                                           no-console\n  196:5   warning  Unexpected console statement                                                           no-console\n  198:7   warning  Unexpected console statement                                                           no-console\n  221:5   warning  Unexpected console statement                                                           no-console\n  247:5   warning  Unexpected console statement                                                           no-console\n  264:5   warning  Unexpected console statement                                                           no-console\n  268:3   warning  Unexpected console statement                                                           no-console\n  269:3   warning  Unexpected console statement                                                           no-console\n  270:3   warning  Unexpected console statement                                                           no-console\n  271:3   warning  Unexpected console statement                                                           no-console\n  272:3   warning  Unexpected console statement                                                           no-console\n  275:5   warning  Unexpected console statement                                                           no-console\n  276:5   warning  Unexpected console statement                                                           no-console\n  277:5   warning  Unexpected console statement                                                           no-console\n  278:5   warning  Unexpected console statement                                                           no-console\n  279:5   warning  Unexpected console statement                                                           no-console\n  280:5   warning  Unexpected console statement                                                           no-console\n  281:5   warning  Unexpected console statement                                                           no-console\n  282:5   warning  Unexpected console statement                                                           no-console\n  284:5   warning  Unexpected console statement                                                           no-console\n  287:3   warning  Unexpected console statement                                                           no-console\n  288:3   warning  Unexpected console statement                                                           no-console\n  289:3   warning  Unexpected console statement                                                           no-console\n  290:3   warning  Unexpected console statement                                                           no-console\n  291:3   warning  Unexpected console statement                                                           no-console\n  292:3   warning  Unexpected console statement                                                           no-console\n  296:30  warning  Unexpected console statement                                                           no-console\n\n/Users/<USER>/Desktop/vscode/college-employment-survey/backend/test-real-db-workflow.js\n    3:7  error    'API_BASE' is assigned a value but never used. Allowed unused vars must match /^_/u       no-unused-vars\n   46:1  warning  Async function 'testRealDatabaseWorkflow' has too many lines (80). Maximum allowed is 50  max-lines-per-function\n   46:1  warning  Async function 'testRealDatabaseWorkflow' has a complexity of 12. Maximum allowed is 10   complexity\n   47:3  warning  Unexpected console statement                                                              no-console\n   51:5  warning  Unexpected console statement                                                              no-console\n   53:5  warning  Unexpected console statement                                                              no-console\n   54:5  warning  Unexpected console statement                                                              no-console\n   57:5  warning  Unexpected console statement                                                              no-console\n   59:5  warning  Unexpected console statement                                                              no-console\n   60:5  warning  Unexpected console statement                                                              no-console\n   63:5  warning  Unexpected console statement                                                              no-console\n   65:5  warning  Unexpected console statement                                                              no-console\n   67:7  warning  Unexpected console statement                                                              no-console\n   73:5  warning  Unexpected console statement                                                              no-console\n   76:5  warning  Unexpected console statement                                                              no-console\n   78:5  warning  Unexpected console statement                                                              no-console\n   80:7  warning  Unexpected console statement                                                              no-console\n   86:5  warning  Unexpected console statement                                                              no-console\n   90:7  warning  Unexpected console statement                                                              no-console\n  105:7  warning  Unexpected console statement                                                              no-console\n  106:7  warning  Unexpected console statement                                                              no-console\n  109:7  warning  Unexpected console statement                                                              no-console\n  111:7  warning  Unexpected console statement                                                              no-console\n  116:9  warning  Unexpected console statement                                                              no-console\n  120:5  warning  Unexpected console statement                                                              no-console\n  121:5  warning  Unexpected console statement                                                              no-console\n  122:5  warning  Unexpected console statement                                                              no-console\n  123:5  warning  Unexpected console statement                                                              no-console\n  124:5  warning  Unexpected console statement                                                              no-console\n  125:5  warning  Unexpected console statement                                                              no-console\n  126:5  warning  Unexpected console statement                                                              no-console\n  127:5  warning  Unexpected console statement                                                              no-console\n  128:5  warning  Unexpected console statement                                                              no-console\n  129:5  warning  Unexpected console statement                                                              no-console\n  130:5  warning  Unexpected console statement                                                              no-console\n  131:5  warning  Unexpected console statement                                                              no-console\n  134:5  warning  Unexpected console statement                                                              no-console\n\n/Users/<USER>/Desktop/vscode/college-employment-survey/backend/test-server.js\n    8:7   error    'path' is assigned a value but never used. Allowed unused vars must match /^_/u  no-unused-vars\n    9:7   error    'fs' is assigned a value but never used. Allowed unused vars must match /^_/u    no-unused-vars\n   51:41  warning  Arrow function has too many lines (95). Maximum allowed is 50                    max-lines-per-function\n  213:25  error    'next' is defined but never used. Allowed unused args must match /^_/u           no-unused-vars\n  214:3   warning  Unexpected console statement                                                     no-console\n  220:3   warning  Unexpected console statement                                                     no-console\n  221:3   warning  Unexpected console statement                                                     no-console\n  222:3   warning  Unexpected console statement                                                     no-console\n\n/Users/<USER>/Desktop/vscode/college-employment-survey/backend/test-user-api.js\n  161:1   warning  Function 'logOperation' has too many parameters (6). Maximum allowed is 5        max-params\n  165:41  error    Expected '===' and instead saw '=='                                              eqeqeq\n  180:3   warning  Unexpected console statement                                                     no-console\n  185:10  error    'hasPermission' is defined but never used. Allowed unused vars must match /^_/u  no-unused-vars\n  207:34  warning  Arrow function has too many lines (343). Maximum allowed is 50                   max-lines-per-function\n  207:34  warning  Arrow function has a complexity of 32. Maximum allowed is 10                     complexity\n  227:3   warning  Unexpected console statement                                                     no-console\n  280:21  warning  Arrow function has too many lines (67). Maximum allowed is 50                    max-lines-per-function\n  364:21  warning  Arrow function has too many lines (75). Maximum allowed is 50                    max-lines-per-function\n  364:21  warning  Arrow function has a complexity of 15. Maximum allowed is 10                     complexity\n  514:63  error    Expected '===' and instead saw '=='                                              eqeqeq\n  581:5   warning  Unexpected console statement                                                     no-console\n  593:3   warning  Unexpected console statement                                                     no-console\n  594:3   warning  Unexpected console statement                                                     no-console\n  595:3   warning  Unexpected console statement                                                     no-console\n  596:3   warning  Unexpected console statement                                                     no-console\n  597:3   warning  Unexpected console statement                                                     no-console\n  602:3   warning  Unexpected console statement                                                     no-console\n  604:5   warning  Unexpected console statement                                                     no-console\n\n/Users/<USER>/Desktop/vscode/college-employment-survey/backend/tests/e2e/api-endpoints.test.js\n  201:7  error  Expected a block comment instead of consecutive line comments  multiline-comment-style\n\n/Users/<USER>/Desktop/vscode/college-employment-survey/backend/tests/integration/api.test.js\n   43:13  error  'response' is assigned a value but never used. Allowed unused vars must match /^_/u  no-unused-vars\n   79:13  error  'response' is assigned a value but never used. Allowed unused vars must match /^_/u  no-unused-vars\n  175:13  error  'response' is assigned a value but never used. Allowed unused vars must match /^_/u  no-unused-vars\n  219:13  error  'response' is assigned a value but never used. Allowed unused vars must match /^_/u  no-unused-vars\n  321:13  error  'response' is assigned a value but never used. Allowed unused vars must match /^_/u  no-unused-vars\n  363:13  error  'response' is assigned a value but never used. Allowed unused vars must match /^_/u  no-unused-vars\n  392:13  error  'response' is assigned a value but never used. Allowed unused vars must match /^_/u  no-unused-vars\n\n/Users/<USER>/Desktop/vscode/college-employment-survey/backend/tests/unit/utils.test.js\n  139:7   error    Expected a block comment instead of consecutive line comments  multiline-comment-style\n  167:25  warning  Too many nested callbacks (4). Maximum allowed is 3            max-nested-callbacks\n  185:31  warning  Too many nested callbacks (4). Maximum allowed is 3            max-nested-callbacks\n  194:9   error    Script URL is a form of eval                                   no-script-url\n  198:25  warning  Too many nested callbacks (4). Maximum allowed is 3            max-nested-callbacks\n\n/Users/<USER>/Desktop/vscode/college-employment-survey/backend/user-api-test.js\n   32:3   warning  Unexpected console statement                                          no-console\n   37:29  warning  Async arrow function has too many lines (108). Maximum allowed is 50  max-lines-per-function\n   39:5   warning  Unexpected console statement                                          no-console\n   40:5   warning  Unexpected console statement                                          no-console\n   87:5   warning  Unexpected console statement                                          no-console\n   88:5   warning  Unexpected console statement                                          no-console\n   91:27  warning  Arrow function has too many lines (55). Maximum allowed is 50         max-lines-per-function\n   93:9   warning  Unexpected console statement                                          no-console\n  128:11  warning  Unexpected console statement                                          no-console\n  138:9   warning  Unexpected console statement                                          no-console\n  139:9   warning  Unexpected console statement                                          no-console\n  157:5   warning  Unexpected console statement                                          no-console\n  169:3   warning  Unexpected console statement                                          no-console\n  187:7   warning  Unexpected console statement                                          no-console\n  210:30  warning  Arrow function has too many lines (59). Maximum allowed is 50         max-lines-per-function\n  213:3   warning  Unexpected console statement                                          no-console\n  228:7   warning  Unexpected console statement                                          no-console\n  255:9   warning  Unexpected console statement                                          no-console\n  263:7   warning  Unexpected console statement                                          no-console\n  287:3   warning  Unexpected console statement                                          no-console\n  293:3   warning  Unexpected console statement                                          no-console\n  294:3   warning  Unexpected console statement                                          no-console\n  295:3   warning  Unexpected console statement                                          no-console\n  296:3   warning  Unexpected console statement                                          no-console\n\n/Users/<USER>/Desktop/vscode/college-employment-survey/backend/user-management-server.js\n   37:5   warning  Unexpected console statement                                                    no-console\n  106:1   warning  Async function 'handleRequest' has too many lines (556). Maximum allowed is 50  max-lines-per-function\n  106:1   warning  Async function 'handleRequest' has a complexity of 62. Maximum allowed is 10    complexity\n  111:3   warning  Unexpected console statement                                                    no-console\n  132:7   warning  Unexpected console statement                                                    no-console\n  133:7   warning  Unexpected console statement                                                    no-console\n  184:7   warning  Unexpected console statement                                                    no-console\n  200:7   warning  Unexpected console statement                                                    no-console\n  214:7   warning  Unexpected console statement                                                    no-console\n  239:7   warning  Unexpected console statement                                                    no-console\n  255:7   warning  Unexpected console statement                                                    no-console\n  291:7   warning  Unexpected console statement                                                    no-console\n  302:7   warning  Unexpected console statement                                                    no-console\n  317:7   warning  Unexpected console statement                                                    no-console\n  351:7   warning  Unexpected console statement                                                    no-console\n  363:7   warning  Unexpected console statement                                                    no-console\n  374:43  error    Unnecessary escape character: \\/                                                no-useless-escape\n  377:7   warning  Unexpected console statement                                                    no-console\n  404:7   warning  Unexpected console statement                                                    no-console\n  416:7   warning  Unexpected console statement                                                    no-console\n  430:7   warning  Unexpected console statement                                                    no-console\n  450:7   warning  Unexpected console statement                                                    no-console\n  458:7   warning  Unexpected console statement                                                    no-console\n  473:7   warning  Unexpected console statement                                                    no-console\n  527:7   warning  Unexpected console statement                                                    no-console\n  540:7   warning  Unexpected console statement                                                    no-console\n  599:7   warning  Unexpected console statement                                                    no-console\n  603:7   warning  Unexpected console statement                                                    no-console\n  616:7   warning  Unexpected console statement                                                    no-console\n  693:7   warning  Unexpected console statement                                                    no-console\n  731:7   warning  Unexpected console statement                                                    no-console\n  754:3   warning  Unexpected console statement                                                    no-console\n  755:3   warning  Unexpected console statement                                                    no-console\n  756:3   warning  Unexpected console statement                                                    no-console\n  757:3   warning  Unexpected console statement                                                    no-console\n  761:5   warning  Unexpected console statement                                                    no-console\n  763:5   warning  Unexpected console statement                                                    no-console\n  766:3   warning  Unexpected console statement                                                    no-console\n  771:3   warning  Unexpected console statement                                                    no-console\n  774:5   warning  Unexpected console statement                                                    no-console\n\n✖ 1105 problems (185 errors, 920 warnings)\n  2 errors and 0 warnings potentially fixable with the `--fix` option.\n\n", "errorCount": 1}, "prettier": {"status": "failed", "output": "\n> college-employment-survey-backend@0.1.0 format:check\n> prettier --check .\n\nChecking formatting...\n"}}, "testing": {"results": {"unit": {"status": "passed", "output": "\n> college-employment-survey-backend@0.1.0 test:unit\n> jest --testPathPattern=tests/unit\n\n"}, "integration": {"status": "passed", "output": "\n> college-employment-survey-backend@0.1.0 test:integration\n> jest --testPathPattern=tests/integration\n\n"}, "e2e": {"status": "passed", "output": "\n> college-employment-survey-backend@0.1.0 test:e2e\n> jest --testPathPattern=tests/e2e\n\n"}}}, "coverage": {"status": "failed", "output": "\n> college-employment-survey-backend@0.1.0 test:coverage\n> jest --coverage\n\n--------------------------|---------|----------|---------|---------|-------------------\nFile                      | % Stmts | % Branch | % Funcs | % Lines | Uncovered Line #s \n--------------------------|---------|----------|---------|---------|-------------------\nAll files                 |       0 |        0 |       0 |       0 |                   \n backend                  |       0 |        0 |       0 |       0 |                   \n  index-modular-simple.js |       0 |        0 |       0 |       0 | 11-340            \n backend/src              |       0 |        0 |       0 |       0 |                   \n  app.js                  |       0 |        0 |       0 |       0 | 25-224            \n backend/src/middleware   |       0 |        0 |       0 |       0 |                   \n  auth.js                 |       0 |        0 |       0 |       0 | 14-281            \n  cors.js                 |       0 |        0 |       0 |       0 | 9-149             \n  validation.js           |       0 |        0 |       0 |       0 | 15-326            \n backend/src/routes       |       0 |        0 |       0 |       0 |                   \n  admin.js                |       0 |        0 |       0 |       0 | 9-259             \n  deidentification.js     |       0 |        0 |       0 |       0 | 9-224             \n  questionnaire.js        |       0 |        0 |       0 |       0 | 10-230            \n  story.js                |       0 |        0 |       0 |       0 | 10-237            \n  system.js               |       0 |        0 |       0 |       0 | 9-259             \n backend/src/services     |       0 |        0 |       0 |       0 |                   \n  authService.js          |       0 |        0 |       0 |       0 | 5-306             \n backend/src/utils        |       0 |        0 |       0 |       0 |                   \n  pagination.js           |       0 |        0 |       0 |       0 | 13-81             \n  response.js             |       0 |        0 |       0 |       0 | 14-167            \n--------------------------|---------|----------|---------|---------|-------------------\n\n=============================== Coverage summary ===============================\nStatements   : 0% ( 0/664 )\nBranches     : 0% ( 0/498 )\nFunctions    : 0% ( 0/135 )\nLines        : 0% ( 0/649 )\n================================================================================\n"}, "duplicates": {"status": "skipped", "reason": "detect-duplicates.js not found"}, "recommendations": [{"category": "Code Quality", "priority": "high", "title": "修复ESLint问题", "description": "运行 `npm run lint:fix` 自动修复可修复的问题", "action": "npm run lint:fix"}, {"category": "Maintenance", "priority": "low", "title": "定期运行质量检查", "description": "建议每周运行一次完整的质量检查", "action": "node scripts/generate-quality-report.js"}]}