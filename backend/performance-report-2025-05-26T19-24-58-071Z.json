{"timestamp": "2025-05-26T19:24:37.160Z", "baseUrl": "https://college-employment-survey.aibook2099.workers.dev", "endpoints": {"/": {"method": "GET", "iterations": 15, "successCount": 15, "errorCount": 0, "errors": [], "min": 94, "max": 105, "avg": 98.13, "median": 98, "p95": 105, "p99": 105, "stdDev": 3.07}, "/health": {"method": "GET", "iterations": 15, "successCount": 15, "errorCount": 0, "errors": [], "min": 94, "max": 101, "avg": 97.27, "median": 97, "p95": 101, "p99": 101, "stdDev": 1.91}, "/api/system/health": {"method": "GET", "iterations": 15, "successCount": 15, "errorCount": 0, "errors": [], "min": 94, "max": 106, "avg": 98.27, "median": 98, "p95": 106, "p99": 106, "stdDev": 3.15}, "/api/questionnaire/stats": {"method": "GET", "iterations": 15, "successCount": 15, "errorCount": 0, "errors": [], "min": 268, "max": 411, "avg": 316.2, "median": 306, "p95": 411, "p99": 411, "stdDev": 35.33}, "/api/story/list?page=1&pageSize=10": {"method": "GET", "iterations": 15, "successCount": 15, "errorCount": 0, "errors": [], "min": 129, "max": 148, "avg": 137.07, "median": 137, "p95": 148, "p99": 148, "stdDev": 4.75}, "/api/story/list?page=1&pageSize=50": {"method": "GET", "iterations": 15, "successCount": 15, "errorCount": 0, "errors": [], "min": 130, "max": 151, "avg": 135.27, "median": 135, "p95": 151, "p99": 151, "stdDev": 5}, "/api/story/list?page=1&pageSize=100": {"method": "GET", "iterations": 15, "successCount": 15, "errorCount": 0, "errors": [], "min": 130, "max": 155, "avg": 138.53, "median": 138, "p95": 155, "p99": 155, "stdDev": 5.92}}, "summary": {"totalEndpoints": 7, "avgResponseTime": 145.82, "avgP95ResponseTime": 168.14, "fastestEndpoint": {"path": "/health", "time": 97.27}, "slowestEndpoint": {"path": "/api/questionnaire/stats", "time": 316.2}, "performanceGrade": "good"}, "recommendations": [{"category": "General Optimization", "priority": "low", "title": "通用性能优化", "description": "持续性能改进建议", "actions": ["定期运行性能测试", "监控生产环境性能指标", "建立性能预算", "实施性能回归测试"]}], "concurrency": {"1": {"totalTime": 315, "avgTime": 315, "throughput": 3.17}, "5": {"totalTime": 450, "avgTime": 90, "throughput": 11.11}, "10": {"totalTime": 460, "avgTime": 46, "throughput": 21.74}, "20": {"totalTime": 616, "avgTime": 30.8, "throughput": 32.47}}}