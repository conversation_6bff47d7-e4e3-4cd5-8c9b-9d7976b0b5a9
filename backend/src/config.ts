/**
 * 应用配置
 *
 * 加载环境变量并提供配置接口
 *
 * 注意：由于Cloudflare Workers环境不支持文件系统，
 * 我们直接从环境变量中读取配置
 */

// 配置接口
interface Config {
  environment: string;
  database: {
    url: string;
  };
  mockData: {
    enabled: boolean;
    testDataGeneratorOnly: boolean; // 仅对测试数据生成器启用模拟数据
  };
  api: {
    resendApiKey: string;
  };
  security: {
    aesSecretKey: string;
    aesIv: string;
    jwtSecret: string;
  };
  logging: {
    level: string;
  };
}

// 创建配置对象
const config: Config = {
  environment: 'development', // 在Cloudflare Workers中，我们硬编码为开发环境
  database: {
    url: 'postgresql://user:password@localhost:5432/survey_dev', // 模拟数据库URL
  },
  mockData: {
    enabled: false, // 默认禁用模拟数据，使用真实数据库
    testDataGeneratorOnly: true, // 仅对测试数据生成器启用模拟数据
  },
  api: {
    resendApiKey: 'test_api_key',
  },
  security: {
    aesSecretKey: 'development_secret_key',
    aesIv: 'development_iv',
    jwtSecret: 'development_jwt_secret',
  },
  logging: {
    level: 'debug',
  },
};

// 辅助函数：检查是否应该使用模拟数据
export function shouldUseMockData(context?: { isTestDataGenerator?: boolean }): boolean {
  // 如果全局启用了模拟数据，则使用模拟数据
  if (config.mockData.enabled) {
    return true;
  }

  // 如果仅对测试数据生成器启用，且当前是测试数据生成器请求，则使用模拟数据
  if (config.mockData.testDataGeneratorOnly && context?.isTestDataGenerator) {
    return true;
  }

  return false;
}

// 输出配置信息
console.log(`Loaded configuration for environment: ${config.environment}`);
console.log(`Mock data enabled: ${config.mockData.enabled}`);
console.log(`Test data generator only: ${config.mockData.testDataGeneratorOnly}`);
console.log(`Log level: ${config.logging.level}`);

export default config;
