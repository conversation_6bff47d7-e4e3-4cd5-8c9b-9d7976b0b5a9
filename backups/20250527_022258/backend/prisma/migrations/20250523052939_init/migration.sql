-- CreateTable
CREATE TABLE "User" (
    "id" INTEGER NOT NULL PRIMARY KEY AUTOINCREMENT,
    "email" TEXT NOT NULL,
    "emailVerified" BOOLEAN NOT NULL DEFAULT false,
    "verificationCode" TEXT,
    "verificationExpiresAt" DATETIME,
    "ipAddress" TEXT,
    "userAgent" TEXT,
    "role" TEXT NOT NULL DEFAULT 'user',
    "username" TEXT,
    "passwordHash" TEXT,
    "lastLoginAt" DATETIME,
    "createdAt" DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" DATETIME NOT NULL
);

-- CreateTable
CREATE TABLE "QuestionnaireResponse" (
    "id" INTEGER NOT NULL PRIMARY KEY AUTOINCREMENT,
    "userId" INTEGER,
    "isAnonymous" BOOLEAN NOT NULL DEFAULT true,
    "ipAddress" TEXT,
    "submittedById" TEXT,
    "educationLevel" TEXT,
    "major" TEXT,
    "graduationYear" INTEGER,
    "region" TEXT,
    "expectedPosition" TEXT,
    "expectedSalaryRange" TEXT,
    "expectedWorkHours" INTEGER,
    "expectedVacationDays" INTEGER,
    "employmentStatus" TEXT,
    "currentIndustry" TEXT,
    "currentPosition" TEXT,
    "jobSatisfaction" INTEGER,
    "unemploymentDuration" TEXT,
    "unemploymentReason" TEXT,
    "jobHuntingDifficulty" INTEGER,
    "regretMajor" BOOLEAN,
    "preferredMajor" TEXT,
    "careerChangeIntention" BOOLEAN,
    "careerChangeTarget" TEXT,
    "adviceForStudents" TEXT,
    "observationOnEmployment" TEXT,
    "createdAt" DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" DATETIME NOT NULL,
    CONSTRAINT "QuestionnaireResponse_userId_fkey" FOREIGN KEY ("userId") REFERENCES "User" ("id") ON DELETE SET NULL ON UPDATE CASCADE
);

-- CreateTable
CREATE TABLE "Story" (
    "id" INTEGER NOT NULL PRIMARY KEY AUTOINCREMENT,
    "userId" INTEGER,
    "isAnonymous" BOOLEAN NOT NULL DEFAULT true,
    "title" TEXT NOT NULL,
    "content" TEXT NOT NULL,
    "ipAddress" TEXT,
    "likes" INTEGER NOT NULL DEFAULT 0,
    "dislikes" INTEGER NOT NULL DEFAULT 0,
    "status" TEXT NOT NULL DEFAULT 'pending',
    "submittedById" TEXT,
    "createdAt" DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" DATETIME NOT NULL,
    CONSTRAINT "Story_userId_fkey" FOREIGN KEY ("userId") REFERENCES "User" ("id") ON DELETE SET NULL ON UPDATE CASCADE
);

-- CreateTable
CREATE TABLE "Vote" (
    "id" INTEGER NOT NULL PRIMARY KEY AUTOINCREMENT,
    "storyId" INTEGER NOT NULL,
    "ipAddress" TEXT NOT NULL,
    "voteType" TEXT NOT NULL,
    "createdAt" DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    CONSTRAINT "Vote_storyId_fkey" FOREIGN KEY ("storyId") REFERENCES "Story" ("id") ON DELETE RESTRICT ON UPDATE CASCADE
);

-- CreateTable
CREATE TABLE "SensitiveWord" (
    "id" INTEGER NOT NULL PRIMARY KEY AUTOINCREMENT,
    "word" TEXT NOT NULL,
    "level" INTEGER NOT NULL DEFAULT 1,
    "createdAt" DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" DATETIME NOT NULL
);

-- CreateTable
CREATE TABLE "ReviewLog" (
    "id" TEXT NOT NULL PRIMARY KEY,
    "reviewerId" TEXT NOT NULL,
    "contentId" TEXT NOT NULL,
    "action" TEXT NOT NULL,
    "diff" TEXT,
    "reviewNotes" TEXT,
    "ipAddress" TEXT,
    "userAgent" TEXT,
    "timestamp" DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    CONSTRAINT "ReviewLog_contentId_fkey" FOREIGN KEY ("contentId") REFERENCES "PendingContent" ("id") ON DELETE RESTRICT ON UPDATE CASCADE
);

-- CreateTable
CREATE TABLE "ModerationHistory" (
    "id" TEXT NOT NULL PRIMARY KEY,
    "contentType" TEXT NOT NULL,
    "contentId" TEXT NOT NULL,
    "reviewerId" TEXT NOT NULL,
    "isSafe" BOOLEAN NOT NULL,
    "issues" TEXT NOT NULL,
    "confidence" REAL NOT NULL,
    "explanation" TEXT NOT NULL,
    "suggestedAction" TEXT NOT NULL,
    "severity" TEXT,
    "dataQuality" TEXT,
    "constructiveValue" TEXT,
    "storyValue" TEXT,
    "ipAddress" TEXT,
    "userAgent" TEXT,
    "createdAt" DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP
);

-- CreateTable
CREATE TABLE "PendingContent" (
    "id" TEXT NOT NULL PRIMARY KEY,
    "sequenceNumber" TEXT NOT NULL,
    "type" TEXT NOT NULL,
    "originalContent" TEXT NOT NULL,
    "sanitizedContent" TEXT NOT NULL,
    "status" TEXT NOT NULL DEFAULT 'pending',
    "originIp" TEXT,
    "userAgent" TEXT,
    "flags" TEXT,
    "priority" INTEGER NOT NULL DEFAULT 1,
    "aiSuggestion" TEXT,
    "aiConfidence" REAL,
    "aiExplanation" TEXT,
    "reviewerId" TEXT,
    "reviewedAt" DATETIME,
    "reviewNotes" TEXT,
    "createdAt" DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP
);

-- CreateTable
CREATE TABLE "RejectedContent" (
    "id" TEXT NOT NULL PRIMARY KEY,
    "userId" TEXT,
    "contentType" TEXT NOT NULL,
    "originalContent" TEXT NOT NULL,
    "reason" TEXT,
    "issues" TEXT,
    "ipAddress" TEXT,
    "userAgent" TEXT,
    "createdAt" DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP
);

-- CreateTable
CREATE TABLE "SystemConfig" (
    "id" TEXT NOT NULL PRIMARY KEY,
    "key" TEXT NOT NULL,
    "value" TEXT NOT NULL,
    "description" TEXT,
    "createdAt" DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP
);

-- CreateTable
CREATE TABLE "ModerationFeedback" (
    "id" TEXT NOT NULL PRIMARY KEY,
    "contentId" TEXT NOT NULL,
    "contentType" TEXT NOT NULL,
    "moderationId" TEXT,
    "feedbackType" TEXT NOT NULL,
    "reason" TEXT NOT NULL,
    "details" TEXT NOT NULL,
    "contactEmail" TEXT,
    "ipAddress" TEXT,
    "userAgent" TEXT,
    "status" TEXT NOT NULL DEFAULT 'pending',
    "response" TEXT,
    "reviewerId" TEXT,
    "reviewedAt" DATETIME,
    "createdAt" DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP
);

-- CreateTable
CREATE TABLE "AppealRequest" (
    "id" TEXT NOT NULL PRIMARY KEY,
    "feedbackId" TEXT NOT NULL,
    "contentId" TEXT NOT NULL,
    "contentType" TEXT NOT NULL,
    "reason" TEXT NOT NULL,
    "status" TEXT NOT NULL DEFAULT 'pending',
    "result" TEXT,
    "reviewerId" TEXT,
    "reviewedAt" DATETIME,
    "ipAddress" TEXT,
    "userAgent" TEXT,
    "createdAt" DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP
);

-- CreateTable
CREATE TABLE "Notification" (
    "id" TEXT NOT NULL PRIMARY KEY,
    "userId" TEXT NOT NULL,
    "type" TEXT NOT NULL,
    "title" TEXT NOT NULL,
    "message" TEXT NOT NULL,
    "data" TEXT NOT NULL,
    "isRead" BOOLEAN NOT NULL DEFAULT false,
    "readAt" DATETIME,
    "createdAt" DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP
);

-- CreateTable
CREATE TABLE "SystemSetting" (
    "id" TEXT NOT NULL PRIMARY KEY,
    "key" TEXT NOT NULL,
    "value" TEXT NOT NULL,
    "description" TEXT,
    "updatedBy" TEXT,
    "createdAt" DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" DATETIME NOT NULL
);

-- CreateIndex
CREATE UNIQUE INDEX "User_email_key" ON "User"("email");

-- CreateIndex
CREATE UNIQUE INDEX "Vote_storyId_ipAddress_key" ON "Vote"("storyId", "ipAddress");

-- CreateIndex
CREATE UNIQUE INDEX "SensitiveWord_word_key" ON "SensitiveWord"("word");

-- CreateIndex
CREATE INDEX "ModerationHistory_contentType_contentId_idx" ON "ModerationHistory"("contentType", "contentId");

-- CreateIndex
CREATE INDEX "ModerationHistory_reviewerId_idx" ON "ModerationHistory"("reviewerId");

-- CreateIndex
CREATE INDEX "ModerationHistory_suggestedAction_idx" ON "ModerationHistory"("suggestedAction");

-- CreateIndex
CREATE INDEX "ModerationHistory_severity_idx" ON "ModerationHistory"("severity");

-- CreateIndex
CREATE INDEX "ModerationHistory_createdAt_idx" ON "ModerationHistory"("createdAt");

-- CreateIndex
CREATE INDEX "PendingContent_sequenceNumber_idx" ON "PendingContent"("sequenceNumber");

-- CreateIndex
CREATE INDEX "PendingContent_type_idx" ON "PendingContent"("type");

-- CreateIndex
CREATE INDEX "PendingContent_status_idx" ON "PendingContent"("status");

-- CreateIndex
CREATE INDEX "PendingContent_priority_idx" ON "PendingContent"("priority");

-- CreateIndex
CREATE INDEX "PendingContent_reviewerId_idx" ON "PendingContent"("reviewerId");

-- CreateIndex
CREATE INDEX "PendingContent_createdAt_idx" ON "PendingContent"("createdAt");

-- CreateIndex
CREATE INDEX "RejectedContent_userId_idx" ON "RejectedContent"("userId");

-- CreateIndex
CREATE INDEX "RejectedContent_contentType_idx" ON "RejectedContent"("contentType");

-- CreateIndex
CREATE INDEX "RejectedContent_createdAt_idx" ON "RejectedContent"("createdAt");

-- CreateIndex
CREATE UNIQUE INDEX "SystemConfig_key_key" ON "SystemConfig"("key");

-- CreateIndex
CREATE INDEX "ModerationFeedback_contentId_contentType_idx" ON "ModerationFeedback"("contentId", "contentType");

-- CreateIndex
CREATE INDEX "ModerationFeedback_feedbackType_idx" ON "ModerationFeedback"("feedbackType");

-- CreateIndex
CREATE INDEX "ModerationFeedback_status_idx" ON "ModerationFeedback"("status");

-- CreateIndex
CREATE INDEX "ModerationFeedback_createdAt_idx" ON "ModerationFeedback"("createdAt");

-- CreateIndex
CREATE INDEX "AppealRequest_feedbackId_idx" ON "AppealRequest"("feedbackId");

-- CreateIndex
CREATE INDEX "AppealRequest_contentId_contentType_idx" ON "AppealRequest"("contentId", "contentType");

-- CreateIndex
CREATE INDEX "AppealRequest_status_idx" ON "AppealRequest"("status");

-- CreateIndex
CREATE INDEX "AppealRequest_createdAt_idx" ON "AppealRequest"("createdAt");

-- CreateIndex
CREATE INDEX "Notification_userId_idx" ON "Notification"("userId");

-- CreateIndex
CREATE INDEX "Notification_type_idx" ON "Notification"("type");

-- CreateIndex
CREATE INDEX "Notification_isRead_idx" ON "Notification"("isRead");

-- CreateIndex
CREATE INDEX "Notification_createdAt_idx" ON "Notification"("createdAt");

-- CreateIndex
CREATE UNIQUE INDEX "SystemSetting_key_key" ON "SystemSetting"("key");
