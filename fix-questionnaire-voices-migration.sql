-- 修复问卷心声数据迁移脚本
-- 从原始questionnaire_responses表提取心声数据到questionnaire_voices_v2表

-- 首先清除现有的测试数据
DELETE FROM questionnaire_voices_v2 WHERE id LIKE 'qv_test_%';

-- 从原始表提取建议类心声
INSERT INTO questionnaire_voices_v2 (
    id,
    source_response_id,
    user_id,
    voice_type,
    title,
    content,
    education_level,
    education_level_display,
    region_code,
    region_display,
    status,
    likes,
    views,
    created_at,
    updated_at
)
SELECT
    'qv_advice_' || REPLACE(id, '-', '_') as id,
    NULL as source_response_id,  -- 不使用外键约束
    user_id,
    'advice' as voice_type,
    '给学弟学妹的建议' as title,
    advice_for_students as content,
    education_level,
    education_level as education_level_display,
    region,
    region as region_display,
    'approved' as status,
    0 as likes,
    0 as views,
    created_at,
    updated_at
FROM questionnaire_responses
WHERE advice_for_students IS NOT NULL
AND advice_for_students != ''
AND LENGTH(TRIM(advice_for_students)) > 0;

-- 从原始表提取观察类心声
INSERT INTO questionnaire_voices_v2 (
    id,
    source_response_id,
    user_id,
    voice_type,
    title,
    content,
    education_level,
    education_level_display,
    region_code,
    region_display,
    status,
    likes,
    views,
    created_at,
    updated_at
)
SELECT
    'qv_observation_' || REPLACE(id, '-', '_') as id,
    NULL as source_response_id,  -- 不使用外键约束
    user_id,
    'observation' as voice_type,
    '对就业环境的观察' as title,
    observation_on_employment as content,
    education_level,
    education_level as education_level_display,
    region,
    region as region_display,
    'approved' as status,
    0 as likes,
    0 as views,
    created_at,
    updated_at
FROM questionnaire_responses
WHERE observation_on_employment IS NOT NULL
AND observation_on_employment != ''
AND LENGTH(TRIM(observation_on_employment)) > 0;
