# 综合测试与监控平台设计方案

## 🎯 平台概述
将 `questionnaire-voices-2` 升级为集成测试数据生成、性能监控、事件追踪的综合平台。

## 🔧 核心功能模块

### 1. 数据生成控制台
```
┌─────────────────────────────────────────────────────────┐
│ 🧪 测试数据生成控制台                                    │
├─────────────────────────────────────────────────────────┤
│ 问卷生成: [生成1条] [批量生成] 状态: ○未审核 ○已通过     │
│ 故事生成: [生成1条] [批量生成] 状态: ○未审核 ○已通过     │
│ 心声生成: [关联问卷生成] [独立生成]                      │
│                                                         │
│ 📈 实时监控: 成功率 98.5% | 平均响应 1.2s | 错误 2次    │
└─────────────────────────────────────────────────────────┘
```

### 2. 性能监控面板
```
┌─────────────────────────────────────────────────────────┐
│ ⚡ 性能监控面板                                          │
├─────────────────────────────────────────────────────────┤
│ API响应时间:  ████████░░ 1.2s (目标: <2s)               │
│ 成功率:      ██████████ 98.5% (目标: >95%)              │
│ 数据库延迟:   ███████░░░ 0.8s (目标: <1s)               │
│ 错误率:      ██░░░░░░░░ 1.5% (目标: <5%)                │
│                                                         │
│ 🔄 自动刷新: 5s | [手动刷新] [导出报告]                 │
└─────────────────────────────────────────────────────────┘
```

### 3. 事件追踪日志
```
┌─────────────────────────────────────────────────────────┐
│ 📝 实时事件日志                                          │
├─────────────────────────────────────────────────────────┤
│ 14:32:15 ✅ 问卷提交成功 (ID: test_001) - 1.2s          │
│ 14:32:16 ✅ 心声自动生成 (2条) - 0.8s                   │
│ 14:32:18 ❌ 故事提交失败 (网络超时) - 5.0s              │
│ 14:32:20 ✅ 数据库写入成功 - 0.5s                       │
│ 14:32:22 ⚠️  API响应慢 (3.2s > 2s阈值)                 │
│                                                         │
│ 筛选: [全部] [成功] [失败] [警告] [清空日志]             │
└─────────────────────────────────────────────────────────┘
```

### 4. 压力测试工具
```
┌─────────────────────────────────────────────────────────┐
│ 🚀 压力测试工具                                          │
├─────────────────────────────────────────────────────────┤
│ 并发数: [10] 持续时间: [60]秒 间隔: [1]秒               │
│ 测试类型: ☑问卷提交 ☑故事提交 ☑数据查询                │
│                                                         │
│ [开始压力测试] [停止测试] [查看历史]                     │
│                                                         │
│ 当前状态: 运行中... 已发送 45/100 请求                  │
│ 实时指标: 成功 43 | 失败 2 | 平均响应 1.8s              │
└─────────────────────────────────────────────────────────┘
```

## 📊 数据监控集成

### 与 data-monitor 页面的协同
```typescript
// 共享监控数据
interface MonitoringData {
  // 从 data-monitor 获取的基础数据
  apiEndpoints: ApiEndpoint[];
  systemHealth: SystemHealth;
  
  // 测试平台特有的数据
  testEvents: TestEvent[];
  performanceMetrics: PerformanceMetric[];
  generatedDataStats: GeneratedDataStats;
}

// 实时数据同步
class TestPlatformMonitor {
  // 继承 data-monitor 的监控能力
  // 添加测试特有的监控功能
}
```

### 事件记录系统
```typescript
interface TestEvent {
  id: string;
  timestamp: string;
  type: 'questionnaire' | 'story' | 'voice' | 'api_call';
  action: 'generate' | 'submit' | 'approve' | 'reject';
  status: 'success' | 'error' | 'warning';
  duration: number; // 毫秒
  details: {
    endpoint?: string;
    dataId?: string;
    errorMessage?: string;
    responseSize?: number;
  };
  metadata: {
    userAgent?: string;
    ip?: string;
    sessionId?: string;
  };
}
```

## 🔧 技术实现

### 1. 事件追踪系统
```typescript
class EventTracker {
  private events: TestEvent[] = [];
  
  // 记录事件
  async recordEvent(event: Partial<TestEvent>) {
    const fullEvent: TestEvent = {
      id: crypto.randomUUID(),
      timestamp: new Date().toISOString(),
      ...event
    };
    
    this.events.push(fullEvent);
    this.notifySubscribers(fullEvent);
    
    // 可选：发送到后端存储
    await this.sendToBackend(fullEvent);
  }
  
  // 性能监控
  async measurePerformance<T>(
    operation: () => Promise<T>,
    eventType: string
  ): Promise<T> {
    const startTime = performance.now();
    
    try {
      const result = await operation();
      const duration = performance.now() - startTime;
      
      await this.recordEvent({
        type: eventType as any,
        status: 'success',
        duration
      });
      
      return result;
    } catch (error) {
      const duration = performance.now() - startTime;
      
      await this.recordEvent({
        type: eventType as any,
        status: 'error',
        duration,
        details: { errorMessage: error.message }
      });
      
      throw error;
    }
  }
}
```

### 2. 性能指标收集
```typescript
class PerformanceCollector {
  // API响应时间监控
  async monitorApiCall(url: string, options: RequestInit) {
    const startTime = performance.now();
    
    try {
      const response = await fetch(url, options);
      const duration = performance.now() - startTime;
      
      this.recordMetric({
        type: 'api_response_time',
        value: duration,
        endpoint: url,
        status: response.status
      });
      
      return response;
    } catch (error) {
      const duration = performance.now() - startTime;
      
      this.recordMetric({
        type: 'api_error',
        value: duration,
        endpoint: url,
        error: error.message
      });
      
      throw error;
    }
  }
  
  // 数据生成效率监控
  async monitorDataGeneration(type: string, count: number) {
    const startTime = performance.now();
    
    // 执行数据生成...
    
    const duration = performance.now() - startTime;
    const efficiency = count / (duration / 1000); // 每秒生成数量
    
    this.recordMetric({
      type: 'data_generation_efficiency',
      value: efficiency,
      metadata: { type, count, duration }
    });
  }
}
```

### 3. 实时数据展示
```typescript
// 实时更新组件
const RealTimeMonitor: React.FC = () => {
  const [metrics, setMetrics] = useState<PerformanceMetric[]>([]);
  const [events, setEvents] = useState<TestEvent[]>([]);
  
  useEffect(() => {
    // WebSocket 连接实时数据
    const ws = new WebSocket('wss://api.example.com/monitor');
    
    ws.onmessage = (event) => {
      const data = JSON.parse(event.data);
      
      if (data.type === 'metric') {
        setMetrics(prev => [...prev.slice(-99), data.metric]);
      } else if (data.type === 'event') {
        setEvents(prev => [...prev.slice(-99), data.event]);
      }
    };
    
    return () => ws.close();
  }, []);
  
  return (
    <div className="real-time-monitor">
      <MetricsChart data={metrics} />
      <EventsLog events={events} />
    </div>
  );
};
```

## 📈 监控指标定义

### 核心性能指标
```typescript
interface PerformanceMetrics {
  // API性能
  apiResponseTime: {
    average: number;
    p95: number;
    p99: number;
  };
  
  // 成功率
  successRate: {
    overall: number;
    byEndpoint: Record<string, number>;
  };
  
  // 数据生成效率
  generationEfficiency: {
    questionnairesPerSecond: number;
    storiesPerSecond: number;
    voicesPerSecond: number;
  };
  
  // 错误统计
  errorStats: {
    totalErrors: number;
    errorsByType: Record<string, number>;
    errorRate: number;
  };
}
```

### 告警阈值
```typescript
const ALERT_THRESHOLDS = {
  apiResponseTime: 2000, // 2秒
  errorRate: 0.05, // 5%
  successRate: 0.95, // 95%
  dbConnectionTime: 1000 // 1秒
};
```

## 🎨 UI/UX 设计

### 页面布局
```
┌─────────────────────────────────────────────────────────┐
│ 🧪 综合测试与监控平台                                    │
│ ⚠️  仅限测试环境 | 🔄 自动刷新: 5s | 📊 数据监控        │
├─────────────────────────────────────────────────────────┤
│                                                         │
│ ┌─────────────┐ ┌─────────────┐ ┌─────────────┐         │
│ │ 数据生成器   │ │ 性能监控    │ │ 事件日志    │         │
│ │             │ │             │ │             │         │
│ └─────────────┘ └─────────────┘ └─────────────┘         │
│                                                         │
│ ┌─────────────┐ ┌─────────────┐ ┌─────────────┐         │
│ │ 压力测试    │ │ 数据统计    │ │ 系统健康    │         │
│ │             │ │             │ │             │         │
│ └─────────────┘ └─────────────┘ └─────────────┘         │
│                                                         │
│ [导出报告] [清理数据] [重置环境] [查看历史]              │
└─────────────────────────────────────────────────────────┘
```

## 🚀 实施计划

### Phase 1: 基础监控 (立即开始)
1. ✅ 事件记录系统
2. ✅ 基础性能监控
3. ✅ 实时日志显示

### Phase 2: 数据生成增强
1. 🔄 智能数据生成
2. 🔄 批量操作监控
3. 🔄 生成效率统计

### Phase 3: 高级功能
1. ⏳ 压力测试工具
2. ⏳ 历史数据分析
3. ⏳ 自动化报告

## 🎯 预期价值

### 内测阶段
- **全面监控**: 实时了解系统性能
- **问题发现**: 快速定位性能瓶颈
- **数据驱动**: 基于真实数据优化系统
- **效率提升**: 自动化测试数据生成

### 开发效率
- **一站式平台**: 测试+监控+分析
- **实时反馈**: 立即发现问题
- **性能基准**: 建立性能基线
- **问题追踪**: 完整的事件链路

这个升级方案将大大提升内测的效率和质量！您希望从哪个模块开始实施？
