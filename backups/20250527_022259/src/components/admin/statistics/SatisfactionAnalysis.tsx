import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { StatisticsData } from '../StatisticsPanel';
import { Separator } from '@/components/ui/separator';
import { Badge } from '@/components/ui/badge';
import { 
  Star, 
  TrendingUp, 
  TrendingDown, 
  Building, 
  DollarSign, 
  Clock 
} from 'lucide-react';

// 导入图表库
import { 
  BarChart, 
  Bar, 
  XAxis, 
  YAxis, 
  CartesianGrid, 
  Tooltip, 
  Legend, 
  ResponsiveContainer,
  LineChart,
  Line,
  PieChart,
  Pie,
  Cell,
  RadarChart,
  Radar,
  PolarGrid,
  PolarAngleAxis,
  PolarRadiusAxis,
  ComposedChart,
  Area
} from 'recharts';

interface SatisfactionAnalysisProps {
  data: StatisticsData;
}

// 图表颜色
const COLORS = ['#0088FE', '#00C49F', '#FFBB28', '#FF8042', '#8884d8', '#82ca9d'];

export default function SatisfactionAnalysis({ data }: SatisfactionAnalysisProps) {
  // 格式化满意度（保留一位小数）
  const formattedSatisfaction = data.overview.averageSatisfaction.toFixed(1);
  
  // 准备满意度分布数据
  const satisfactionDistributionData = data.satisfactionDistribution.labels.map((label, index) => ({
    name: label,
    value: data.satisfactionDistribution.data[index]
  }));
  
  // 准备行业与满意度关联数据
  const satisfactionByIndustryData = data.satisfactionByIndustry.labels.map((label, index) => ({
    name: label,
    value: data.satisfactionByIndustry.data[index]
  }));
  
  // 准备满意度影响因素雷达图数据（模拟数据，实际应从API获取）
  const satisfactionFactorsData = [
    { subject: '薪资水平', A: 4.2, fullMark: 5 },
    { subject: '工作环境', A: 3.8, fullMark: 5 },
    { subject: '工作压力', A: 3.2, fullMark: 5 },
    { subject: '晋升机会', A: 3.5, fullMark: 5 },
    { subject: '工作内容', A: 4.0, fullMark: 5 },
    { subject: '工作时间', A: 3.6, fullMark: 5 },
  ];
  
  // 准备满意度与薪资、工作时间关系数据（模拟数据，实际应从API获取）
  const satisfactionRelationData = [
    { name: '5000以下', 满意度: 3.2, 工作时间: 9.5, 薪资: 4500 },
    { name: '5000-8000', 满意度: 3.5, 工作时间: 9.2, 薪资: 6500 },
    { name: '8000-10000', 满意度: 3.8, 工作时间: 9.0, 薪资: 9000 },
    { name: '10000-15000', 满意度: 4.1, 工作时间: 8.8, 薪资: 12500 },
    { name: '15000-20000', 满意度: 4.3, 工作时间: 8.5, 薪资: 17500 },
    { name: '20000以上', 满意度: 4.5, 工作时间: 8.2, 薪资: 25000 },
  ];
  
  // 准备满意度与工作年限关系数据（模拟数据，实际应从API获取）
  const satisfactionByExperienceData = [
    { name: '应届生', 满意度: 3.5 },
    { name: '1-3年', 满意度: 3.8 },
    { name: '3-5年', 满意度: 4.0 },
    { name: '5-10年', 满意度: 4.2 },
    { name: '10年以上', 满意度: 4.5 },
  ];
  
  return (
    <div className="space-y-4">
      {/* 满意度概览 */}
      <Card>
        <CardHeader>
          <CardTitle>满意度概览</CardTitle>
          <CardDescription>毕业生工作满意度分析</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="flex flex-col md:flex-row items-center justify-between gap-4 mb-6">
            <div className="flex items-center gap-4">
              <div className="p-4 bg-amber-50 rounded-full">
                <Star className="h-8 w-8 text-amber-500" />
              </div>
              <div>
                <p className="text-sm text-muted-foreground">平均满意度</p>
                <h3 className="text-2xl font-bold">{formattedSatisfaction}/5</h3>
              </div>
            </div>
            
            <div className="flex items-center gap-4">
              <div className="p-4 bg-green-50 rounded-full">
                <TrendingUp className="h-8 w-8 text-green-500" />
              </div>
              <div>
                <p className="text-sm text-muted-foreground">同比增长</p>
                <h3 className="text-2xl font-bold">+0.3分</h3>
              </div>
            </div>
            
            <div className="flex items-center gap-4">
              <div className="p-4 bg-blue-50 rounded-full">
                <Building className="h-8 w-8 text-blue-500" />
              </div>
              <div>
                <p className="text-sm text-muted-foreground">最高满意度行业</p>
                <h3 className="text-2xl font-bold">IT/互联网</h3>
              </div>
            </div>
          </div>
          
          <Separator className="my-6" />
          
          <div className="h-80">
            <ResponsiveContainer width="100%" height="100%">
              <BarChart
                data={satisfactionDistributionData}
                margin={{ top: 5, right: 30, left: 20, bottom: 5 }}
              >
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis dataKey="name" />
                <YAxis />
                <Tooltip formatter={(value) => [`${value}人`, '人数']} />
                <Legend />
                <Bar dataKey="value" fill="#FFBB28" name="人数" />
              </BarChart>
            </ResponsiveContainer>
          </div>
        </CardContent>
      </Card>
      
      {/* 满意度影响因素分析 */}
      <Card>
        <CardHeader>
          <CardTitle>满意度影响因素分析</CardTitle>
          <CardDescription>影响工作满意度的主要因素</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="h-80">
            <ResponsiveContainer width="100%" height="100%">
              <RadarChart outerRadius={90} data={satisfactionFactorsData}>
                <PolarGrid />
                <PolarAngleAxis dataKey="subject" />
                <PolarRadiusAxis angle={30} domain={[0, 5]} />
                <Radar 
                  name="满意度评分" 
                  dataKey="A" 
                  stroke="#8884d8" 
                  fill="#8884d8" 
                  fillOpacity={0.6} 
                />
                <Legend />
                <Tooltip formatter={(value) => [`${value}分`, '评分']} />
              </RadarChart>
            </ResponsiveContainer>
          </div>
        </CardContent>
      </Card>
      
      {/* 行业与满意度关联分析 */}
      <Card>
        <CardHeader>
          <CardTitle>行业与满意度关联分析</CardTitle>
          <CardDescription>不同行业的工作满意度</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="h-80">
            <ResponsiveContainer width="100%" height="100%">
              <BarChart
                data={satisfactionByIndustryData}
                layout="vertical"
                margin={{ top: 5, right: 30, left: 20, bottom: 5 }}
              >
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis type="number" domain={[0, 5]} />
                <YAxis dataKey="name" type="category" width={100} />
                <Tooltip formatter={(value) => [`${value}分`, '平均满意度']} />
                <Legend />
                <Bar dataKey="value" fill="#0088FE" name="平均满意度" />
              </BarChart>
            </ResponsiveContainer>
          </div>
        </CardContent>
      </Card>
      
      {/* 满意度与薪资、工作时间关系 */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <Card>
          <CardHeader>
            <CardTitle>满意度与薪资、工作时间关系</CardTitle>
            <CardDescription>薪资水平和工作时间对满意度的影响</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="h-80">
              <ResponsiveContainer width="100%" height="100%">
                <ComposedChart
                  data={satisfactionRelationData}
                  margin={{ top: 20, right: 20, bottom: 20, left: 20 }}
                >
                  <CartesianGrid stroke="#f5f5f5" />
                  <XAxis dataKey="name" scale="band" />
                  <YAxis yAxisId="left" orientation="left" stroke="#8884d8" />
                  <YAxis yAxisId="right" orientation="right" stroke="#82ca9d" />
                  <Tooltip formatter={(value, name) => {
                    if (name === '满意度') return [`${value}分`, name];
                    if (name === '工作时间') return [`${value}小时/天`, name];
                    return [value, name];
                  }} />
                  <Legend />
                  <Bar yAxisId="left" dataKey="满意度" barSize={20} fill="#8884d8" />
                  <Line yAxisId="right" type="monotone" dataKey="工作时间" stroke="#82ca9d" />
                </ComposedChart>
              </ResponsiveContainer>
            </div>
          </CardContent>
        </Card>
        
        <Card>
          <CardHeader>
            <CardTitle>满意度与工作年限关系</CardTitle>
            <CardDescription>工作经验对满意度的影响</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="h-80">
              <ResponsiveContainer width="100%" height="100%">
                <LineChart
                  data={satisfactionByExperienceData}
                  margin={{ top: 5, right: 30, left: 20, bottom: 5 }}
                >
                  <CartesianGrid strokeDasharray="3 3" />
                  <XAxis dataKey="name" />
                  <YAxis domain={[0, 5]} />
                  <Tooltip formatter={(value) => [`${value}分`, '平均满意度']} />
                  <Legend />
                  <Line 
                    type="monotone" 
                    dataKey="满意度" 
                    stroke="#8884d8" 
                    activeDot={{ r: 8 }} 
                  />
                </LineChart>
              </ResponsiveContainer>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
