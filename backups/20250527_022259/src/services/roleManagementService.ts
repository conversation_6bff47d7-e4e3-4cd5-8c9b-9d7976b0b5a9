/**
 * 角色管理服务
 *
 * 提供角色管理相关的 API 调用
 */

import { apiClient } from './apiClient';
import { Role, Permission } from '@/types/role';

// 模拟角色数据
const mockRoles: Role[] = [
  {
    id: '1',
    name: '超级管理员',
    description: '拥有系统所有权限',
    isSystem: true,
    userCount: 1,
    permissions: [
      { id: 'DASHBOARD_PERSONAL', name: '个人仪表盘' },
      { id: 'DASHBOARD_SYSTEM', name: '系统概览' },
      { id: 'DASHBOARD_SECURITY', name: '安全监控' },
      { id: 'CONTENT_REVIEW', name: '内容审核' },
      { id: 'STORY_REVIEW', name: '故事审核' },
      { id: 'QUICK_REVIEW', name: '快速审核' },
      { id: 'TAG_MANAGEMENT', name: '标签管理' },
      { id: 'QUESTIONNAIRE_VIEW', name: '问卷查看' },
      { id: 'QUESTIONNAIRE_EDIT', name: '问卷编辑' },
      { id: 'DATA_ANALYSIS', name: '数据分析' },
      { id: 'DATA_EXPORT', name: '数据导出' },
      { id: 'USER_VIEW', name: '用户查看' },
      { id: 'USER_MANAGEMENT', name: '用户管理' },
      { id: 'REVIEWER_MANAGEMENT', name: '审核员管理' },
      { id: 'ADMIN_MANAGEMENT', name: '管理员管理' },
      { id: 'ROLE_MANAGEMENT', name: '角色管理' },
      { id: 'SETTINGS_PERSONAL', name: '个人设置' },
      { id: 'DEIDENTIFICATION', name: '内容脱敏' },
      { id: 'SECURITY_SETTINGS', name: '安全设置' },
      { id: 'SYSTEM_CONFIG', name: '系统配置' },
      { id: 'SECURITY_MONITOR', name: '安全监控' },
      { id: 'SECURITY_LOGS', name: '安全日志' },
      { id: 'ADMIN_AUDIT', name: '管理员审计' },
      { id: 'LOGIN_RECORDS', name: '登录记录' }
    ]
  },
  {
    id: '2',
    name: '管理员',
    description: '拥有大部分管理权限，但不包括系统级别和敏感操作',
    isSystem: true,
    userCount: 3,
    permissions: [
      { id: 'DASHBOARD_PERSONAL', name: '个人仪表盘' },
      { id: 'DASHBOARD_SYSTEM', name: '系统概览' },
      { id: 'CONTENT_REVIEW', name: '内容审核' },
      { id: 'STORY_REVIEW', name: '故事审核' },
      { id: 'QUICK_REVIEW', name: '快速审核' },
      { id: 'TAG_MANAGEMENT', name: '标签管理' },
      { id: 'QUESTIONNAIRE_VIEW', name: '问卷查看' },
      { id: 'QUESTIONNAIRE_EDIT', name: '问卷编辑' },
      { id: 'DATA_ANALYSIS', name: '数据分析' },
      { id: 'DATA_EXPORT', name: '数据导出' },
      { id: 'USER_VIEW', name: '用户查看' },
      { id: 'USER_MANAGEMENT', name: '用户管理' },
      { id: 'REVIEWER_MANAGEMENT', name: '审核员管理' },
      { id: 'SETTINGS_PERSONAL', name: '个人设置' },
      { id: 'DEIDENTIFICATION', name: '内容脱敏' }
    ]
  },
  {
    id: '3',
    name: '审核员',
    description: '负责内容审核，包括故事和问卷回复的审核',
    isSystem: true,
    userCount: 10,
    permissions: [
      { id: 'DASHBOARD_PERSONAL', name: '个人仪表盘' },
      { id: 'CONTENT_REVIEW', name: '内容审核' },
      { id: 'STORY_REVIEW', name: '故事审核' },
      { id: 'QUICK_REVIEW', name: '快速审核' },
      { id: 'QUESTIONNAIRE_VIEW', name: '问卷查看' },
      { id: 'SETTINGS_PERSONAL', name: '个人设置' }
    ]
  },
  {
    id: '4',
    name: '数据分析师',
    description: '负责数据分析和报表生成',
    isSystem: false,
    userCount: 2,
    permissions: [
      { id: 'DASHBOARD_PERSONAL', name: '个人仪表盘' },
      { id: 'DASHBOARD_SYSTEM', name: '系统概览' },
      { id: 'QUESTIONNAIRE_VIEW', name: '问卷查看' },
      { id: 'DATA_ANALYSIS', name: '数据分析' },
      { id: 'DATA_EXPORT', name: '数据导出' },
      { id: 'SETTINGS_PERSONAL', name: '个人设置' }
    ]
  },
  {
    id: '5',
    name: '内容管理员',
    description: '负责内容管理和标签维护',
    isSystem: false,
    userCount: 5,
    permissions: [
      { id: 'DASHBOARD_PERSONAL', name: '个人仪表盘' },
      { id: 'CONTENT_REVIEW', name: '内容审核' },
      { id: 'STORY_REVIEW', name: '故事审核' },
      { id: 'QUICK_REVIEW', name: '快速审核' },
      { id: 'TAG_MANAGEMENT', name: '标签管理' },
      { id: 'QUESTIONNAIRE_VIEW', name: '问卷查看' },
      { id: 'SETTINGS_PERSONAL', name: '个人设置' }
    ]
  }
];

/**
 * 获取所有角色
 * @returns 角色列表
 */
export async function getRoles() {
  try {
    // 检查是否为开发环境
    if (import.meta.env.DEV && import.meta.env.VITE_USE_MOCK === 'true') {
      console.log('使用模拟数据获取角色');

      // 模拟网络延迟
      await new Promise(resolve => setTimeout(resolve, 500));

      return {
        success: true,
        data: mockRoles
      };
    }

    // 生产环境使用真实API
    const response = await apiClient.get('/admin/roles');
    return response.data;
  } catch (error) {
    console.error('获取角色失败:', error);
    throw error;
  }
}

/**
 * 获取角色详情
 * @param id 角色ID
 * @returns 角色详情
 */
export async function getRole(id: string) {
  try {
    // 检查是否为开发环境
    if (import.meta.env.DEV && import.meta.env.VITE_USE_MOCK === 'true') {
      console.log('使用模拟数据获取角色详情');

      // 模拟网络延迟
      await new Promise(resolve => setTimeout(resolve, 500));

      const role = mockRoles.find(r => r.id === id);

      if (!role) {
        return {
          success: false,
          message: '角色不存在'
        };
      }

      return {
        success: true,
        data: role
      };
    }

    // 生产环境使用真实API
    const response = await apiClient.get(`/admin/roles/${id}`);
    return response.data;
  } catch (error) {
    console.error('获取角色详情失败:', error);
    throw error;
  }
}

/**
 * 创建角色
 * @param role 角色信息
 * @returns 创建结果
 */
export async function createRole(role: {
  name: string;
  description: string;
  permissions: string[];
}) {
  try {
    // 检查是否为开发环境
    if (import.meta.env.DEV && import.meta.env.VITE_USE_MOCK === 'true') {
      console.log('使用模拟数据创建角色');

      // 模拟网络延迟
      await new Promise(resolve => setTimeout(resolve, 500));

      // 检查角色名称是否已存在
      if (mockRoles.some(r => r.name === role.name)) {
        return {
          success: false,
          message: '角色名称已存在'
        };
      }

      return {
        success: true,
        message: '角色创建成功',
        data: {
          id: String(mockRoles.length + 1),
          ...role,
          isSystem: false,
          userCount: 0,
          permissions: role.permissions.map(id => ({ id, name: id.split('_').map(word => word.charAt(0) + word.slice(1).toLowerCase()).join(' ') }))
        }
      };
    }

    // 生产环境使用真实API
    const response = await apiClient.post('/admin/roles', role);
    return response.data;
  } catch (error) {
    console.error('创建角色失败:', error);
    throw error;
  }
}

/**
 * 更新角色
 * @param id 角色ID
 * @param role 角色信息
 * @returns 更新结果
 */
export async function updateRole(id: string, role: {
  name: string;
  description: string;
  permissions: string[];
}) {
  try {
    // 检查是否为开发环境
    if (import.meta.env.DEV && import.meta.env.VITE_USE_MOCK === 'true') {
      console.log('使用模拟数据更新角色');

      // 模拟网络延迟
      await new Promise(resolve => setTimeout(resolve, 500));

      // 检查角色是否存在
      const existingRole = mockRoles.find(r => r.id === id);
      if (!existingRole) {
        return {
          success: false,
          message: '角色不存在'
        };
      }

      // 检查是否为系统角色
      if (existingRole.isSystem) {
        return {
          success: false,
          message: '系统角色不可修改'
        };
      }

      // 检查角色名称是否已存在（排除自身）
      if (mockRoles.some(r => r.id !== id && r.name === role.name)) {
        return {
          success: false,
          message: '角色名称已存在'
        };
      }

      return {
        success: true,
        message: '角色更新成功',
        data: {
          ...existingRole,
          ...role,
          permissions: role.permissions.map(id => ({ id, name: id.split('_').map(word => word.charAt(0) + word.slice(1).toLowerCase()).join(' ') }))
        }
      };
    }

    // 生产环境使用真实API
    const response = await apiClient.put(`/admin/roles/${id}`, role);
    return response.data;
  } catch (error) {
    console.error('更新角色失败:', error);
    throw error;
  }
}

/**
 * 删除角色
 * @param id 角色ID
 * @returns 删除结果
 */
export async function deleteRole(id: string) {
  try {
    // 检查是否为开发环境
    if (import.meta.env.DEV && import.meta.env.VITE_USE_MOCK === 'true') {
      console.log('使用模拟数据删除角色');

      // 模拟网络延迟
      await new Promise(resolve => setTimeout(resolve, 500));

      // 检查角色是否存在
      const existingRole = mockRoles.find(r => r.id === id);
      if (!existingRole) {
        return {
          success: false,
          message: '角色不存在'
        };
      }

      // 检查是否为系统角色
      if (existingRole.isSystem) {
        return {
          success: false,
          message: '系统角色不可删除'
        };
      }

      // 检查角色是否有用户
      if (existingRole.userCount && existingRole.userCount > 0) {
        return {
          success: false,
          message: '该角色下还有用户，无法删除'
        };
      }

      return {
        success: true,
        message: '角色删除成功'
      };
    }

    // 生产环境使用真实API
    const response = await apiClient.delete(`/admin/roles/${id}`);
    return response.data;
  } catch (error) {
    console.error('删除角色失败:', error);
    throw error;
  }
}

/**
 * 获取所有权限
 * @returns 权限列表
 */
export async function getPermissions() {
  try {
    // 检查是否为开发环境
    if (import.meta.env.DEV && import.meta.env.VITE_USE_MOCK === 'true') {
      console.log('使用模拟数据获取权限');

      // 模拟网络延迟
      await new Promise(resolve => setTimeout(resolve, 500));

      // 从所有角色中提取权限
      const allPermissions = new Set<string>();
      mockRoles.forEach(role => {
        role.permissions.forEach(permission => {
          allPermissions.add(permission.id);
        });
      });

      const permissions = Array.from(allPermissions).map(id => ({
        id,
        name: id.split('_').map(word => word.charAt(0) + word.slice(1).toLowerCase()).join(' '),
        description: `允许${id.split('_').map(word => word.toLowerCase()).join(' ')}操作`
      }));

      return {
        success: true,
        data: permissions
      };
    }

    // 生产环境使用真实API
    const response = await apiClient.get('/admin/permissions');
    return response.data;
  } catch (error) {
    console.error('获取权限失败:', error);
    throw error;
  }
}
