-- 创建用户表
CREATE TABLE IF NOT EXISTS users (
  id INTEGER PRIMARY KEY AUTOINCREMENT,
  username TEXT NOT NULL UNIQUE,
  password TEXT NOT NULL,
  name TEXT NOT NULL,
  role TEXT NOT NULL,
  status TEXT NOT NULL DEFAULT 'active',
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  last_login TIMESTAMP
);

-- 创建权限表
CREATE TABLE IF NOT EXISTS permissions (
  id INTEGER PRIMARY KEY AUTOINCREMENT,
  user_id INTEGER NOT NULL,
  permission TEXT NOT NULL,
  FOREIGN KEY (user_id) REFERENCES users(id)
);

-- 创建UUID映射表
CREATE TABLE IF NOT EXISTS uuid_mappings (
  id INTEGER PRIMARY KEY AUTOINCREMENT,
  uuid TEXT NOT NULL UNIQUE,
  identity_a_hash TEXT NOT NULL,
  identity_b_hash TEXT NOT NULL,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  last_verified_at TIMESTAMP,
  verification_count INTEGER DEFAULT 0,
  UNIQUE (identity_a_hash, identity_b_hash)
);

-- 创建故事表
CREATE TABLE IF NOT EXISTS stories (
  id INTEGER PRIMARY KEY AUTOINCREMENT,
  sequence_number TEXT NOT NULL UNIQUE,
  title TEXT NOT NULL,
  content TEXT NOT NULL,
  author_id INTEGER,
  is_anonymous BOOLEAN NOT NULL DEFAULT 0,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  status TEXT NOT NULL DEFAULT 'pending',
  reviewer_id INTEGER,
  reviewed_at TIMESTAMP,
  rejection_reason TEXT,
  identity_a_hash TEXT NOT NULL,
  identity_b_hash TEXT NOT NULL,
  FOREIGN KEY (author_id) REFERENCES users(id),
  FOREIGN KEY (reviewer_id) REFERENCES users(id)
);

-- 创建故事标签表
CREATE TABLE IF NOT EXISTS story_tags (
  id INTEGER PRIMARY KEY AUTOINCREMENT,
  story_id INTEGER NOT NULL,
  tag_id INTEGER NOT NULL,
  FOREIGN KEY (story_id) REFERENCES stories(id),
  FOREIGN KEY (tag_id) REFERENCES tags(id),
  UNIQUE (story_id, tag_id)
);

-- 创建标签表
CREATE TABLE IF NOT EXISTS tags (
  id INTEGER PRIMARY KEY AUTOINCREMENT,
  name TEXT NOT NULL UNIQUE,
  color TEXT NOT NULL,
  priority INTEGER NOT NULL DEFAULT 0,
  category TEXT NOT NULL,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 创建问卷表
CREATE TABLE IF NOT EXISTS surveys (
  id INTEGER PRIMARY KEY AUTOINCREMENT,
  sequence_number TEXT NOT NULL UNIQUE,
  title TEXT NOT NULL,
  description TEXT NOT NULL,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  status TEXT NOT NULL DEFAULT 'active'
);

-- 创建问题表
CREATE TABLE IF NOT EXISTS questions (
  id INTEGER PRIMARY KEY AUTOINCREMENT,
  survey_id INTEGER NOT NULL,
  type TEXT NOT NULL,
  question TEXT NOT NULL,
  options TEXT,
  required BOOLEAN NOT NULL DEFAULT 0,
  order_index INTEGER NOT NULL,
  FOREIGN KEY (survey_id) REFERENCES surveys(id)
);

-- 创建问卷回复表
CREATE TABLE IF NOT EXISTS survey_responses (
  id INTEGER PRIMARY KEY AUTOINCREMENT,
  survey_id INTEGER NOT NULL,
  respondent_id INTEGER,
  is_anonymous BOOLEAN NOT NULL DEFAULT 0,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  status TEXT NOT NULL DEFAULT 'pending',
  reviewer_id INTEGER,
  reviewed_at TIMESTAMP,
  rejection_reason TEXT,
  identity_a_hash TEXT NOT NULL,
  identity_b_hash TEXT NOT NULL,
  FOREIGN KEY (survey_id) REFERENCES surveys(id),
  FOREIGN KEY (respondent_id) REFERENCES users(id),
  FOREIGN KEY (reviewer_id) REFERENCES users(id)
);

-- 创建回答表
CREATE TABLE IF NOT EXISTS answers (
  id INTEGER PRIMARY KEY AUTOINCREMENT,
  response_id INTEGER NOT NULL,
  question_id INTEGER NOT NULL,
  answer TEXT NOT NULL,
  FOREIGN KEY (response_id) REFERENCES survey_responses(id),
  FOREIGN KEY (question_id) REFERENCES questions(id)
);

-- 创建审核记录表
CREATE TABLE IF NOT EXISTS review_records (
  id INTEGER PRIMARY KEY AUTOINCREMENT,
  reviewer_id INTEGER NOT NULL,
  content_id INTEGER NOT NULL,
  content_type TEXT NOT NULL,
  decision TEXT NOT NULL,
  reviewed_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  review_duration INTEGER NOT NULL,
  FOREIGN KEY (reviewer_id) REFERENCES users(id)
);

-- 插入初始管理员用户
INSERT INTO users (username, password, name, role, status)
VALUES ('admin', 'admin123', '系统管理员', 'admin', 'active');

-- 插入初始超级管理员用户
INSERT INTO users (username, password, name, role, status)
VALUES ('superadmin', 'super123', '超级管理员', 'superadmin', 'active');

-- 插入初始审核员用户
INSERT INTO users (username, password, name, role, status)
VALUES ('reviewer', 'reviewer123', '内容审核员', 'reviewer', 'active');

-- 插入管理员权限
INSERT INTO permissions (user_id, permission)
VALUES (1, 'manage_reviewers'), (1, 'manage_content'), (1, 'view_statistics');

-- 插入超级管理员权限
INSERT INTO permissions (user_id, permission)
VALUES (2, 'manage_admins'), (2, 'manage_reviewers'), (2, 'manage_content'), (2, 'view_statistics'), (2, 'system_config');

-- 插入审核员权限
INSERT INTO permissions (user_id, permission)
VALUES (3, 'review_content'), (3, 'view_statistics');
