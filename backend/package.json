{"name": "college-employment-survey-backend", "version": "0.1.0", "description": "Backend for College Graduate Employment Survey Platform", "private": true, "type": "module", "prisma": {"seed": "ts-node prisma/seed.ts"}, "scripts": {"start": "node local-server.js", "dev": "NODE_ENV=development node local-server.js", "api:start": "node local-server.js", "api:dev": "NODE_ENV=development nodemon local-server.js", "dev:real": "NODE_ENV=development node local-server.js", "dev:cloudflare": "NODE_ENV=development wrangler dev", "dev:mock": "NODE_ENV=development USE_MOCK_DATA=true wrangler dev", "prod": "NODE_ENV=production node index.js", "deploy": "NODE_ENV=production wrangler deploy", "test": "jest", "build": "echo 'No build step required for Cloudflare Workers'", "prisma:generate": "prisma generate", "prisma:migrate": "prisma migrate dev", "prisma:studio": "prisma studio", "prisma:seed": "ts-node prisma/seed.ts", "setup:local": "bash scripts/setup-local.sh", "start:local": "wrangler dev --local --persist", "reset:db": "npx prisma migrate reset --force && npx prisma db seed", "import:mock": "ts-node scripts/import-mock-data.ts", "doc-server": "node simple-doc-server.js", "dev:docs": "node simple-doc-server.js", "test-data:generate": "node scripts/test-data-cli.js generate", "test-data:generate:role": "node scripts/test-data-cli.js generate role-testing", "test-data:generate:audit": "node scripts/test-data-cli.js generate audit-testing", "test-data:reset": "node scripts/test-data-cli.js reset", "test-data:reset:smart": "node scripts/test-data-cli.js reset --smart", "test-data:snapshot": "node scripts/test-data-cli.js snapshot", "test-data:restore": "node scripts/test-data-cli.js restore", "test-data:status": "node scripts/test-data-cli.js status", "test-data:list": "node scripts/test-data-cli.js list-snapshots", "test-data:clean": "node scripts/test-data-cli.js clean", "test-data:cleanup": "node scripts/test-data-cli.js cleanup-snapshots", "test-data:help": "node scripts/test-data-cli.js help", "env:check": "node scripts/environment-check.js", "env:monitor": "node scripts/monitor-api.sh", "cleanup:servers": "node scripts/cleanup-api-servers.js", "health:check": "curl -s http://localhost:8788/health | jq", "datasource:status": "curl -s http://localhost:8788/api/data-source/status | jq", "lint": "eslint . --ext .js,.ts", "lint:fix": "eslint . --ext .js,.ts --fix", "format": "prettier --write .", "format:check": "prettier --check .", "quality:check": "npm run lint && npm run format:check", "quality:fix": "npm run lint:fix && npm run format", "test:unit": "jest --testPathPattern=tests/unit", "test:integration": "jest --testPathPattern=tests/integration", "test:e2e": "jest --testPathPattern=tests/e2e", "test:coverage": "jest --coverage", "test:watch": "jest --watch", "pre-commit": "npm run quality:check && npm run test:unit", "pre-push": "npm run test:coverage"}, "dependencies": {"@hono/node-server": "^1.14.3", "@prisma/client": "^5.0.0", "bcryptjs": "^3.0.2", "cors": "^2.8.5", "crypto-js": "^4.1.1", "dotenv": "^16.5.0", "express": "^5.1.0", "hono": "^4.7.10", "resend": "^1.0.0", "sqlite": "^5.1.1", "sqlite3": "^5.1.7", "zod": "^3.21.4"}, "devDependencies": {"@cloudflare/workers-types": "^4.20230518.0", "@hono/zod-validator": "^0.1.8", "@types/crypto-js": "^4.1.1", "@types/jest": "^29.5.2", "@types/node": "^20.4.1", "@types/supertest": "^6.0.2", "archiver": "^7.0.1", "eslint": "^8.57.0", "husky": "^9.0.11", "jest": "^29.5.0", "jest-environment-miniflare": "^2.14.0", "lint-staged": "^15.2.2", "prettier": "^3.2.5", "prisma": "^5.0.0", "supertest": "^6.3.4", "ts-jest": "^29.1.0", "ts-node": "^10.9.1", "typescript": "^5.1.3", "wrangler": "^4.16.1"}}