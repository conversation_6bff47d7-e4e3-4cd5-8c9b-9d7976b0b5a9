import React, { useState, useEffect } from 'react';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import {
  Pagination,
  PaginationContent,
  PaginationItem,
  PaginationLink,
  PaginationNext,
  PaginationPrevious,
} from '@/components/ui/pagination';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Badge } from '@/components/ui/badge';
import { useToast } from '@/components/ui/use-toast';
import { useApi } from '@/hooks/useApi';
import { 
  AlertCircle, 
  CheckCircle, 
  ChevronDown, 
  Clock, 
  Download, 
  Eye, 
  Filter, 
  MoreVertical, 
  RefreshCw, 
  Search, 
  XCircle 
} from 'lucide-react';
import { DateRangePicker } from '@/components/ui/date-range-picker';
import { format } from 'date-fns';
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle } from '@/components/ui/dialog';

/**
 * 自动审核历史表格组件
 */
const AutoModerationHistoryTable: React.FC = () => {
  const { toast } = useToast();
  const api = useApi();
  const [isLoading, setIsLoading] = useState<boolean>(true);
  const [history, setHistory] = useState<any[]>([]);
  const [pagination, setPagination] = useState({
    page: 1,
    pageSize: 10,
    total: 0,
    totalPages: 0,
  });
  const [filters, setFilters] = useState({
    contentType: '',
    action: '',
    search: '',
    dateRange: {
      from: new Date(new Date().setDate(new Date().getDate() - 7)),
      to: new Date(),
    },
  });
  const [selectedRecord, setSelectedRecord] = useState<any>(null);
  const [showDetails, setShowDetails] = useState<boolean>(false);
  
  // 加载历史记录
  const loadHistory = async (page = 1) => {
    setIsLoading(true);
    
    try {
      // 构建查询参数
      const params = new URLSearchParams();
      params.append('page', page.toString());
      params.append('pageSize', pagination.pageSize.toString());
      
      if (filters.contentType) {
        params.append('contentType', filters.contentType);
      }
      
      if (filters.action) {
        params.append('action', filters.action);
      }
      
      if (filters.search) {
        params.append('search', filters.search);
      }
      
      if (filters.dateRange.from) {
        params.append('startDate', format(filters.dateRange.from, 'yyyy-MM-dd'));
      }
      
      if (filters.dateRange.to) {
        params.append('endDate', format(filters.dateRange.to, 'yyyy-MM-dd'));
      }
      
      // 发送请求
      const response = await api.get(`/admin/auto-moderation/history?${params.toString()}`);
      
      if (response.success) {
        setHistory(response.history);
        setPagination({
          page: response.pagination.page,
          pageSize: response.pagination.pageSize,
          total: response.pagination.total,
          totalPages: response.pagination.totalPages,
        });
      } else {
        toast({
          title: '加载失败',
          description: response.error || '无法加载审核历史记录',
          variant: 'destructive',
        });
      }
    } catch (error) {
      console.error('加载审核历史记录失败:', error);
      toast({
        title: '加载失败',
        description: '服务器错误，请稍后再试',
        variant: 'destructive',
      });
    } finally {
      setIsLoading(false);
    }
  };
  
  // 导出历史记录
  const exportHistory = async () => {
    try {
      // 构建查询参数
      const params = new URLSearchParams();
      
      if (filters.contentType) {
        params.append('contentType', filters.contentType);
      }
      
      if (filters.action) {
        params.append('action', filters.action);
      }
      
      if (filters.search) {
        params.append('search', filters.search);
      }
      
      if (filters.dateRange.from) {
        params.append('startDate', format(filters.dateRange.from, 'yyyy-MM-dd'));
      }
      
      if (filters.dateRange.to) {
        params.append('endDate', format(filters.dateRange.to, 'yyyy-MM-dd'));
      }
      
      params.append('export', 'true');
      
      // 发送请求
      const response = await api.get(`/admin/auto-moderation/history/export?${params.toString()}`);
      
      if (response.success && response.csvData) {
        // 创建Blob
        const blob = new Blob([response.csvData], { type: 'text/csv;charset=utf-8;' });
        
        // 创建下载链接
        const link = document.createElement('a');
        const url = URL.createObjectURL(blob);
        link.setAttribute('href', url);
        link.setAttribute('download', `auto-moderation-history-${format(new Date(), 'yyyy-MM-dd')}.csv`);
        link.style.visibility = 'hidden';
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
        
        toast({
          title: '导出成功',
          description: '审核历史记录已导出为CSV文件',
          variant: 'default',
        });
      } else {
        toast({
          title: '导出失败',
          description: response.error || '无法导出审核历史记录',
          variant: 'destructive',
        });
      }
    } catch (error) {
      console.error('导出审核历史记录失败:', error);
      toast({
        title: '导出失败',
        description: '服务器错误，请稍后再试',
        variant: 'destructive',
      });
    }
  };
  
  // 查看详情
  const viewDetails = (record: any) => {
    setSelectedRecord(record);
    setShowDetails(true);
  };
  
  // 渲染操作徽章
  const renderActionBadge = (action: string) => {
    switch (action) {
      case 'approve':
        return (
          <Badge variant="outline" className="bg-green-50 text-green-700 border-green-200">
            <CheckCircle className="w-3 h-3 mr-1" />
            通过
          </Badge>
        );
      case 'reject':
        return (
          <Badge variant="outline" className="bg-red-50 text-red-700 border-red-200">
            <XCircle className="w-3 h-3 mr-1" />
            拒绝
          </Badge>
        );
      case 'review':
        return (
          <Badge variant="outline" className="bg-yellow-50 text-yellow-700 border-yellow-200">
            <Clock className="w-3 h-3 mr-1" />
            人工审核
          </Badge>
        );
      default:
        return (
          <Badge variant="outline" className="bg-gray-50 text-gray-700 border-gray-200">
            <AlertCircle className="w-3 h-3 mr-1" />
            {action}
          </Badge>
        );
    }
  };
  
  // 渲染内容类型
  const renderContentType = (type: string) => {
    switch (type) {
      case 'story':
        return '故事';
      case 'questionnaire':
        return '问卷';
      case 'comment':
        return '评论';
      case 'profile':
        return '个人资料';
      case 'feedback':
        return '反馈';
      default:
        return type;
    }
  };
  
  // 组件加载时加载历史记录
  useEffect(() => {
    loadHistory();
  }, [filters.dateRange]);
  
  // 处理搜索
  const handleSearch = () => {
    loadHistory(1);
  };
  
  // 处理页码变化
  const handlePageChange = (page: number) => {
    loadHistory(page);
  };
  
  // 渲染分页
  const renderPagination = () => {
    const { page, totalPages } = pagination;
    
    return (
      <Pagination>
        <PaginationContent>
          <PaginationItem>
            <PaginationPrevious 
              onClick={() => handlePageChange(Math.max(1, page - 1))}
              disabled={page <= 1 || isLoading}
            />
          </PaginationItem>
          
          {Array.from({ length: Math.min(5, totalPages) }, (_, i) => {
            const pageNumber = page <= 3 
              ? i + 1 
              : page >= totalPages - 2 
                ? totalPages - 4 + i 
                : page - 2 + i;
                
            if (pageNumber <= 0 || pageNumber > totalPages) return null;
            
            return (
              <PaginationItem key={pageNumber}>
                <PaginationLink
                  isActive={pageNumber === page}
                  onClick={() => handlePageChange(pageNumber)}
                  disabled={isLoading}
                >
                  {pageNumber}
                </PaginationLink>
              </PaginationItem>
            );
          })}
          
          <PaginationItem>
            <PaginationNext 
              onClick={() => handlePageChange(Math.min(totalPages, page + 1))}
              disabled={page >= totalPages || isLoading}
            />
          </PaginationItem>
        </PaginationContent>
      </Pagination>
    );
  };
  
  return (
    <div className="space-y-4">
      <Card>
        <CardHeader>
          <div className="flex flex-col md:flex-row md:items-center justify-between gap-4">
            <div>
              <CardTitle>自动审核历史</CardTitle>
              <CardDescription>
                查看自动审核系统的历史记录和详情
              </CardDescription>
            </div>
            <div className="flex gap-2">
              <Button
                variant="outline"
                size="sm"
                onClick={() => loadHistory(pagination.page)}
                disabled={isLoading}
              >
                <RefreshCw className={`w-4 h-4 mr-2 ${isLoading ? 'animate-spin' : ''}`} />
                刷新
              </Button>
              <Button
                variant="outline"
                size="sm"
                onClick={exportHistory}
                disabled={isLoading || history.length === 0}
              >
                <Download className="w-4 h-4 mr-2" />
                导出
              </Button>
            </div>
          </div>
        </CardHeader>
        <CardContent>
          {/* 筛选器 */}
          <div className="flex flex-col md:flex-row gap-4 mb-6">
            <div className="flex-1 flex gap-2">
              <Input
                placeholder="搜索内容ID或关键词"
                value={filters.search}
                onChange={(e) => setFilters({ ...filters, search: e.target.value })}
                className="max-w-xs"
              />
              <Button variant="secondary" size="icon" onClick={handleSearch}>
                <Search className="h-4 w-4" />
              </Button>
            </div>
            
            <div className="flex gap-2">
              <Select
                value={filters.contentType}
                onValueChange={(value) => {
                  setFilters({ ...filters, contentType: value });
                  loadHistory(1);
                }}
              >
                <SelectTrigger className="w-[150px]">
                  <SelectValue placeholder="内容类型" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="">全部类型</SelectItem>
                  <SelectItem value="story">故事</SelectItem>
                  <SelectItem value="questionnaire">问卷</SelectItem>
                  <SelectItem value="comment">评论</SelectItem>
                  <SelectItem value="profile">个人资料</SelectItem>
                  <SelectItem value="feedback">反馈</SelectItem>
                </SelectContent>
              </Select>
              
              <Select
                value={filters.action}
                onValueChange={(value) => {
                  setFilters({ ...filters, action: value });
                  loadHistory(1);
                }}
              >
                <SelectTrigger className="w-[150px]">
                  <SelectValue placeholder="审核结果" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="">全部结果</SelectItem>
                  <SelectItem value="approve">通过</SelectItem>
                  <SelectItem value="reject">拒绝</SelectItem>
                  <SelectItem value="review">人工审核</SelectItem>
                </SelectContent>
              </Select>
              
              <DateRangePicker
                value={filters.dateRange}
                onChange={(value) => setFilters({ ...filters, dateRange: value || { from: undefined, to: undefined } })}
              />
            </div>
          </div>
          
          {/* 表格 */}
          <div className="rounded-md border">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>ID</TableHead>
                  <TableHead>内容类型</TableHead>
                  <TableHead>审核结果</TableHead>
                  <TableHead>置信度</TableHead>
                  <TableHead>时间</TableHead>
                  <TableHead className="text-right">操作</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {isLoading ? (
                  <TableRow>
                    <TableCell colSpan={6} className="text-center py-8">
                      <RefreshCw className="w-6 h-6 animate-spin mx-auto mb-2 text-primary" />
                      <p className="text-sm text-muted-foreground">加载中...</p>
                    </TableCell>
                  </TableRow>
                ) : history.length === 0 ? (
                  <TableRow>
                    <TableCell colSpan={6} className="text-center py-8">
                      <p className="text-sm text-muted-foreground">暂无审核历史记录</p>
                    </TableCell>
                  </TableRow>
                ) : (
                  history.map((record) => (
                    <TableRow key={record.id}>
                      <TableCell className="font-mono text-xs">
                        {record.id.substring(0, 8)}...
                      </TableCell>
                      <TableCell>{renderContentType(record.contentType)}</TableCell>
                      <TableCell>{renderActionBadge(record.action)}</TableCell>
                      <TableCell>
                        {record.confidence ? `${Math.round(record.confidence * 100)}%` : '-'}
                      </TableCell>
                      <TableCell>
                        {new Date(record.createdAt).toLocaleString()}
                      </TableCell>
                      <TableCell className="text-right">
                        <DropdownMenu>
                          <DropdownMenuTrigger asChild>
                            <Button variant="ghost" size="icon">
                              <MoreVertical className="h-4 w-4" />
                            </Button>
                          </DropdownMenuTrigger>
                          <DropdownMenuContent align="end">
                            <DropdownMenuLabel>操作</DropdownMenuLabel>
                            <DropdownMenuSeparator />
                            <DropdownMenuItem onClick={() => viewDetails(record)}>
                              <Eye className="h-4 w-4 mr-2" />
                              查看详情
                            </DropdownMenuItem>
                          </DropdownMenuContent>
                        </DropdownMenu>
                      </TableCell>
                    </TableRow>
                  ))
                )}
              </TableBody>
            </Table>
          </div>
          
          {/* 分页 */}
          <div className="flex items-center justify-between mt-4">
            <p className="text-sm text-muted-foreground">
              共 {pagination.total} 条记录，第 {pagination.page} / {pagination.totalPages} 页
            </p>
            {renderPagination()}
          </div>
        </CardContent>
      </Card>
      
      {/* 详情对话框 */}
      <Dialog open={showDetails} onOpenChange={setShowDetails}>
        <DialogContent className="max-w-3xl">
          <DialogHeader>
            <DialogTitle>审核详情</DialogTitle>
            <DialogDescription>
              查看自动审核的详细信息
            </DialogDescription>
          </DialogHeader>
          
          {selectedRecord && (
            <div className="space-y-4 mt-4">
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <h3 className="text-sm font-medium mb-1">ID</h3>
                  <p className="text-sm font-mono">{selectedRecord.id}</p>
                </div>
                <div>
                  <h3 className="text-sm font-medium mb-1">内容类型</h3>
                  <p className="text-sm">{renderContentType(selectedRecord.contentType)}</p>
                </div>
                <div>
                  <h3 className="text-sm font-medium mb-1">审核结果</h3>
                  <div>{renderActionBadge(selectedRecord.action)}</div>
                </div>
                <div>
                  <h3 className="text-sm font-medium mb-1">置信度</h3>
                  <p className="text-sm">
                    {selectedRecord.confidence ? `${Math.round(selectedRecord.confidence * 100)}%` : '-'}
                  </p>
                </div>
                <div>
                  <h3 className="text-sm font-medium mb-1">时间</h3>
                  <p className="text-sm">{new Date(selectedRecord.createdAt).toLocaleString()}</p>
                </div>
                <div>
                  <h3 className="text-sm font-medium mb-1">内容ID</h3>
                  <p className="text-sm font-mono">{selectedRecord.contentId || '-'}</p>
                </div>
              </div>
              
              {selectedRecord.issues && selectedRecord.issues.length > 0 && (
                <div>
                  <h3 className="text-sm font-medium mb-2">检测到的问题</h3>
                  <ul className="list-disc list-inside text-sm space-y-1">
                    {selectedRecord.issues.map((issue: string, index: number) => (
                      <li key={index}>{issue}</li>
                    ))}
                  </ul>
                </div>
              )}
              
              {selectedRecord.explanation && (
                <div>
                  <h3 className="text-sm font-medium mb-2">解释</h3>
                  <p className="text-sm whitespace-pre-wrap">{selectedRecord.explanation}</p>
                </div>
              )}
              
              {selectedRecord.originalContent && (
                <div>
                  <h3 className="text-sm font-medium mb-2">原始内容</h3>
                  <div className="bg-muted p-3 rounded-md overflow-auto max-h-[200px]">
                    <pre className="text-xs whitespace-pre-wrap">{selectedRecord.originalContent}</pre>
                  </div>
                </div>
              )}
            </div>
          )}
        </DialogContent>
      </Dialog>
    </div>
  );
};

export default AutoModerationHistoryTable;
