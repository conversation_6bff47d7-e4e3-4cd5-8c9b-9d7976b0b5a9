import { useState, useEffect, useRef } from 'react';
import { useToast } from '@/components/ui/use-toast';
import { Button } from '@/components/ui/button';
import { Tabs, TabsContent, Ta<PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import {
  <PERSON><PERSON><PERSON>,
  Pie<PERSON>hart,
  LineChart,
  Download,
  FileSpreadsheet,
  FileText,
  RefreshCw,
  Calendar,
  Filter,
  AlertCircle,
  Wifi,
  WifiOff
} from 'lucide-react';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue
} from '@/components/ui/select';
import { DatePicker } from '@/components/ui/date-picker';
import { Label } from '@/components/ui/label';
import { Separator } from '@/components/ui/separator';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Badge } from '@/components/ui/badge';
import { Switch } from '@/components/ui/switch';

// 导入图表组件
import OverviewDashboard from './statistics/OverviewDashboard';
import EmploymentAnalysis from './statistics/EmploymentAnalysis';
import SalaryAnalysis from './statistics/SalaryAnalysis';
import SatisfactionAnalysis from './statistics/SatisfactionAnalysis';
import TrendAnalysis from './statistics/TrendAnalysis';

// 导入API服务
import { getStatistics, exportStatisticsReport } from '@/lib/api';

// 导入WebSocket服务
import {
  WebSocketClient,
  WebSocketType,
  WebSocketMessageType,
  createWebSocketClient
} from '@/lib/websocket';

// 定义统计数据类型
export interface StatisticsData {
  overview: {
    totalResponses: number;
    employedCount: number;
    unemployedCount: number;
    furtherEducationCount: number;
    averageSalary: number;
    averageSatisfaction: number;
  };
  employmentStatus: {
    labels: string[];
    data: number[];
  };
  educationLevel: {
    labels: string[];
    data: number[];
  };
  industryDistribution: {
    labels: string[];
    data: number[];
  };
  regionDistribution: {
    labels: string[];
    data: number[];
  };
  salaryRanges: {
    labels: string[];
    data: number[];
  };
  salaryByEducation: {
    labels: string[];
    data: number[];
  };
  salaryByIndustry: {
    labels: string[];
    data: number[];
  };
  satisfactionDistribution: {
    labels: string[];
    data: number[];
  };
  satisfactionByIndustry: {
    labels: string[];
    data: number[];
  };
  trends: {
    timeLabels: string[];
    employmentRate: number[];
    averageSalary: number[];
    averageSatisfaction: number[];
  };
}

// 定义筛选条件类型
interface StatisticsFilters {
  dateRange: {
    startDate: Date | null;
    endDate: Date | null;
  };
  educationLevel: string;
  graduationYear: string;
  region: string;
}

export default function StatisticsPanel() {
  const { toast } = useToast();
  const [activeTab, setActiveTab] = useState('overview');
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [statisticsData, setStatisticsData] = useState<StatisticsData | null>(null);
  const [isExporting, setIsExporting] = useState(false);

  // 筛选条件
  const [filters, setFilters] = useState<StatisticsFilters>({
    dateRange: {
      startDate: null,
      endDate: null,
    },
    educationLevel: 'all',
    graduationYear: 'all',
    region: 'all',
  });

  // 实时更新状态
  const [realtimeEnabled, setRealtimeEnabled] = useState(true);
  const [isConnected, setIsConnected] = useState(false);
  const [lastUpdated, setLastUpdated] = useState<Date | null>(null);
  const [updateCount, setUpdateCount] = useState(0);

  // WebSocket客户端
  const wsClientRef = useRef<WebSocketClient | null>(null);

  // 获取统计数据
  const fetchStatistics = async () => {
    try {
      setIsLoading(true);
      setError(null);

      // 准备筛选条件
      const apiFilters: any = {};

      if (filters.dateRange.startDate) {
        apiFilters.startDate = filters.dateRange.startDate.toISOString();
      }

      if (filters.dateRange.endDate) {
        apiFilters.endDate = filters.dateRange.endDate.toISOString();
      }

      if (filters.educationLevel !== 'all') {
        apiFilters.educationLevel = filters.educationLevel;
      }

      if (filters.graduationYear !== 'all') {
        apiFilters.graduationYear = filters.graduationYear;
      }

      if (filters.region !== 'all') {
        apiFilters.region = filters.region;
      }

      // 调用API获取统计数据
      const data = await getStatistics(apiFilters);

      if (data.success) {
        setStatisticsData(data.statistics);
      } else {
        setError(data.error || '获取统计数据失败');
      }
    } catch (error) {
      console.error('获取统计数据错误:', error);
      setError('服务器错误，请稍后再试');
    } finally {
      setIsLoading(false);
    }
  };

  // 组件加载时获取统计数据
  useEffect(() => {
    fetchStatistics();

    // 初始化WebSocket连接
    if (realtimeEnabled) {
      connectWebSocket();
    }

    // 组件卸载时断开WebSocket连接
    return () => {
      disconnectWebSocket();
    };
  }, []);

  // 当筛选条件变化时重新获取数据
  useEffect(() => {
    fetchStatistics();
  }, [filters]);

  // 当实时更新状态变化时，连接或断开WebSocket
  useEffect(() => {
    if (realtimeEnabled) {
      connectWebSocket();
    } else {
      disconnectWebSocket();
    }
  }, [realtimeEnabled]);

  // 连接WebSocket
  const connectWebSocket = () => {
    // 如果已经连接，则不重复连接
    if (wsClientRef.current) {
      return;
    }

    // 获取管理员令牌
    const token = localStorage.getItem('adminToken');
    if (!token) {
      console.error('未找到管理员令牌，无法连接WebSocket');
      return;
    }

    // 创建WebSocket客户端
    const wsClient = createWebSocketClient({
      url: `${import.meta.env.VITE_API_BASE_URL.replace('http', 'ws')}/api/ws`,
      type: WebSocketType.STATISTICS,
      token,
      onOpen: () => {
        setIsConnected(true);
        toast({
          title: '实时连接已建立',
          description: '统计数据将实时更新',
        });
      },
      onMessage: handleWebSocketMessage,
      onClose: () => {
        setIsConnected(false);
      },
      onError: () => {
        setIsConnected(false);
        toast({
          variant: 'destructive',
          title: '实时连接失败',
          description: '无法建立实时连接，请检查网络',
        });
      },
      onReconnect: (attempt) => {
        toast({
          title: '正在重新连接',
          description: `尝试重新连接 (${attempt})`,
        });
      },
      onReconnectFailed: () => {
        setRealtimeEnabled(false);
        toast({
          variant: 'destructive',
          title: '重连失败',
          description: '无法重新建立连接，已禁用实时更新',
        });
      },
    });

    // 连接WebSocket
    wsClient.connect();

    // 保存WebSocket客户端
    wsClientRef.current = wsClient;
  };

  // 断开WebSocket连接
  const disconnectWebSocket = () => {
    if (wsClientRef.current) {
      wsClientRef.current.disconnect();
      wsClientRef.current = null;
      setIsConnected(false);
    }
  };

  // 处理WebSocket消息
  const handleWebSocketMessage = (message: any) => {
    if (message.type === WebSocketMessageType.DATA_UPDATE) {
      // 数据更新消息
      const { action, entityType, updateType } = message.data;

      if (entityType === 'response' && updateType === 'statistics') {
        // 问卷回复数据变更，需要更新统计数据
        console.log(`收到数据更新通知: ${action} ${entityType}`);

        // 更新最后更新时间
        setLastUpdated(new Date());

        // 增加更新计数
        setUpdateCount(prev => prev + 1);

        // 重新获取统计数据
        fetchStatistics();
      }
    }
  };

  // 处理筛选条件变化
  const handleFilterChange = (key: keyof StatisticsFilters, value: any) => {
    setFilters(prev => ({
      ...prev,
      [key]: value,
    }));
  };

  // 处理日期范围变化
  const handleDateRangeChange = (key: 'startDate' | 'endDate', value: Date | null) => {
    setFilters(prev => ({
      ...prev,
      dateRange: {
        ...prev.dateRange,
        [key]: value,
      },
    }));
  };

  // 处理导出报表
  const handleExportReport = async (format: string) => {
    try {
      setIsExporting(true);

      // 准备筛选条件
      const apiFilters: any = {};

      if (filters.dateRange.startDate) {
        apiFilters.startDate = filters.dateRange.startDate.toISOString();
      }

      if (filters.dateRange.endDate) {
        apiFilters.endDate = filters.dateRange.endDate.toISOString();
      }

      if (filters.educationLevel !== 'all') {
        apiFilters.educationLevel = filters.educationLevel;
      }

      if (filters.graduationYear !== 'all') {
        apiFilters.graduationYear = filters.graduationYear;
      }

      if (filters.region !== 'all') {
        apiFilters.region = filters.region;
      }

      apiFilters.format = format;
      apiFilters.reportType = activeTab;

      // 调用API导出报表
      const result = await exportStatisticsReport(apiFilters);

      if (result.success) {
        // 打开下载链接
        window.open(result.downloadUrl, '_blank');

        toast({
          title: '导出成功',
          description: `报表已导出为${format.toUpperCase()}格式，链接有效期24小时`,
        });
      } else {
        toast({
          variant: 'destructive',
          title: '导出失败',
          description: result.error || '导出报表失败',
        });
      }
    } catch (error) {
      console.error('导出报表错误:', error);
      toast({
        variant: 'destructive',
        title: '导出失败',
        description: '服务器错误，请稍后再试',
      });
    } finally {
      setIsExporting(false);
    }
  };

  // 学历选项
  const educationLevels = [
    { value: 'all', label: '全部学历' },
    { value: '高中/中专', label: '高中/中专' },
    { value: '大专', label: '大专' },
    { value: '本科', label: '本科' },
    { value: '硕士', label: '硕士' },
    { value: '博士', label: '博士' },
  ];

  // 毕业年份选项
  const graduationYears = [
    { value: 'all', label: '全部年份' },
    { value: '2023', label: '2023年' },
    { value: '2022', label: '2022年' },
    { value: '2021', label: '2021年' },
    { value: '2020', label: '2020年' },
    { value: '2019', label: '2019年' },
    { value: 'earlier', label: '2019年以前' },
  ];

  // 地区选项
  const regions = [
    { value: 'all', label: '全部地区' },
    { value: '北京', label: '北京' },
    { value: '上海', label: '上海' },
    { value: '广州', label: '广州' },
    { value: '深圳', label: '深圳' },
    { value: '杭州', label: '杭州' },
    { value: '南京', label: '南京' },
    { value: '武汉', label: '武汉' },
    { value: '成都', label: '成都' },
    { value: '重庆', label: '重庆' },
    { value: '西安', label: '西安' },
    { value: '其他', label: '其他' },
  ];

  // 渲染加载状态
  if (isLoading && !statisticsData) {
    return <p>加载中...</p>;
  }

  // 渲染错误状态
  if (error && !statisticsData) {
    return (
      <div className="flex flex-col items-center justify-center p-8 text-center">
        <AlertCircle className="h-12 w-12 text-red-500 mb-4" />
        <h3 className="text-lg font-medium mb-2">加载数据失败</h3>
        <p className="text-gray-500 mb-4">{error}</p>
        <Button onClick={fetchStatistics}>重试</Button>
      </div>
    );
  }

  return (
    <div className="space-y-4">
      <div className="flex flex-col md:flex-row justify-between items-start md:items-center gap-4">
        <div>
          <h3 className="text-lg font-medium">统计报表</h3>
          <p className="text-sm text-gray-500">
            分析问卷数据，生成统计报表
          </p>
        </div>

        <div className="flex flex-wrap gap-2 items-center">
          <Button
            variant="outline"
            size="sm"
            onClick={fetchStatistics}
            className="flex items-center gap-1"
          >
            <RefreshCw className="h-4 w-4" />
            刷新
          </Button>

          <Select
            value={format}
            onValueChange={(value) => handleExportReport(value)}
          >
            <SelectTrigger className="h-9 w-[130px]">
              <SelectValue placeholder="导出报表" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="excel">
                <div className="flex items-center gap-2">
                  <FileSpreadsheet className="h-4 w-4" />
                  <span>Excel格式</span>
                </div>
              </SelectItem>
              <SelectItem value="pdf">
                <div className="flex items-center gap-2">
                  <FileText className="h-4 w-4" />
                  <span>PDF格式</span>
                </div>
              </SelectItem>
              <SelectItem value="csv">
                <div className="flex items-center gap-2">
                  <Download className="h-4 w-4" />
                  <span>CSV格式</span>
                </div>
              </SelectItem>
            </SelectContent>
          </Select>

          <div className="flex items-center gap-2 ml-4">
            <div className="flex items-center gap-2">
              <Switch
                checked={realtimeEnabled}
                onCheckedChange={setRealtimeEnabled}
                id="realtime-mode"
              />
              <Label htmlFor="realtime-mode" className="text-sm">实时更新</Label>
            </div>

            {realtimeEnabled && (
              <Badge
                variant={isConnected ? "outline" : "secondary"}
                className={`flex items-center gap-1 ${isConnected ? 'bg-green-50' : 'bg-amber-50'}`}
              >
                {isConnected ? (
                  <>
                    <Wifi className="h-3 w-3 text-green-600" />
                    <span className="text-xs text-green-600">已连接</span>
                  </>
                ) : (
                  <>
                    <WifiOff className="h-3 w-3 text-amber-600" />
                    <span className="text-xs text-amber-600">未连接</span>
                  </>
                )}
              </Badge>
            )}

            {lastUpdated && (
              <span className="text-xs text-muted-foreground">
                最后更新: {lastUpdated.toLocaleTimeString()}
              </span>
            )}

            {updateCount > 0 && (
              <Badge variant="secondary" className="bg-blue-50">
                <span className="text-xs text-blue-600">已更新 {updateCount} 次</span>
              </Badge>
            )}
          </div>
        </div>
      </div>

      {/* 筛选条件 */}
      <Card>
        <CardHeader className="pb-3">
          <CardTitle className="text-base">筛选条件</CardTitle>
          <CardDescription>设置筛选条件以分析特定数据</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            <div className="space-y-1">
              <Label htmlFor="educationLevel">学历层次</Label>
              <Select
                value={filters.educationLevel}
                onValueChange={(value) => handleFilterChange('educationLevel', value)}
              >
                <SelectTrigger id="educationLevel">
                  <SelectValue placeholder="选择学历层次" />
                </SelectTrigger>
                <SelectContent>
                  {educationLevels.map((level) => (
                    <SelectItem key={level.value} value={level.value}>
                      {level.label}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-1">
              <Label htmlFor="graduationYear">毕业年份</Label>
              <Select
                value={filters.graduationYear}
                onValueChange={(value) => handleFilterChange('graduationYear', value)}
              >
                <SelectTrigger id="graduationYear">
                  <SelectValue placeholder="选择毕业年份" />
                </SelectTrigger>
                <SelectContent>
                  {graduationYears.map((year) => (
                    <SelectItem key={year.value} value={year.value}>
                      {year.label}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-1">
              <Label htmlFor="region">地区</Label>
              <Select
                value={filters.region}
                onValueChange={(value) => handleFilterChange('region', value)}
              >
                <SelectTrigger id="region">
                  <SelectValue placeholder="选择地区" />
                </SelectTrigger>
                <SelectContent>
                  {regions.map((region) => (
                    <SelectItem key={region.value} value={region.value}>
                      {region.label}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-1">
              <Label>提交日期范围</Label>
              <div className="grid grid-cols-2 gap-2">
                <DatePicker
                  selected={filters.dateRange.startDate}
                  onSelect={(date) => handleDateRangeChange('startDate', date)}
                  placeholder="开始日期"
                />
                <DatePicker
                  selected={filters.dateRange.endDate}
                  onSelect={(date) => handleDateRangeChange('endDate', date)}
                  placeholder="结束日期"
                />
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* 统计报表内容 */}
      <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
        <TabsList className="grid grid-cols-5">
          <TabsTrigger value="overview">数据概览</TabsTrigger>
          <TabsTrigger value="employment">就业状况</TabsTrigger>
          <TabsTrigger value="salary">薪资分析</TabsTrigger>
          <TabsTrigger value="satisfaction">满意度分析</TabsTrigger>
          <TabsTrigger value="trends">趋势分析</TabsTrigger>
        </TabsList>

        <div className="mt-4">
          <TabsContent value="overview">
            {statisticsData && <OverviewDashboard data={statisticsData} />}
          </TabsContent>

          <TabsContent value="employment">
            {statisticsData && <EmploymentAnalysis data={statisticsData} />}
          </TabsContent>

          <TabsContent value="salary">
            {statisticsData && <SalaryAnalysis data={statisticsData} />}
          </TabsContent>

          <TabsContent value="satisfaction">
            {statisticsData && <SatisfactionAnalysis data={statisticsData} />}
          </TabsContent>

          <TabsContent value="trends">
            {statisticsData && <TrendAnalysis data={statisticsData} />}
          </TabsContent>
        </div>
      </Tabs>
    </div>
  );
}
