import React, { useState, useRef } from 'react';
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Ta<PERSON>, <PERSON><PERSON>Content, Ta<PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import { useToast } from '@/components/ui/use-toast';
import { Download, Upload, RefreshCw, AlertTriangle, FileJson, CheckCircle2 } from 'lucide-react';
import { exportSystemConfig, importSystemConfig } from '@/services/systemConfigService';

interface SystemConfigImportExportDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onImportSuccess?: (data: any) => void;
}

const SystemConfigImportExportDialog: React.FC<SystemConfigImportExportDialogProps> = ({
  open,
  onOpenChange,
  onImportSuccess
}) => {
  const { toast } = useToast();
  const [activeTab, setActiveTab] = useState('export');
  const [isLoading, setIsLoading] = useState(false);
  const [selectedFile, setSelectedFile] = useState<File | null>(null);
  const [importError, setImportError] = useState<string | null>(null);
  const [importPreview, setImportPreview] = useState<any | null>(null);
  const fileInputRef = useRef<HTMLInputElement>(null);

  // 处理导出
  const handleExport = async () => {
    try {
      setIsLoading(true);

      const result = await exportSystemConfig();

      if (result.success && result.data) {
        // 创建下载链接
        const link = document.createElement('a');
        link.href = result.data.downloadUrl;
        link.download = result.data.fileName;
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);

        toast({
          title: '导出成功',
          description: '系统配置已成功导出'
        });

        // 关闭对话框
        onOpenChange(false);
      } else {
        toast({
          variant: 'destructive',
          title: '导出失败',
          description: result.message || '无法导出系统配置，请稍后再试'
        });
      }
    } catch (error) {
      console.error('导出系统配置失败:', error);
      toast({
        variant: 'destructive',
        title: '导出失败',
        description: '无法导出系统配置，请稍后再试'
      });
    } finally {
      setIsLoading(false);
    }
  };

  // 处理文件选择
  const handleFileSelect = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (file) {
      // 检查文件类型
      if (file.type !== 'application/json') {
        setImportError('请选择 JSON 格式的配置文件');
        setSelectedFile(null);
        setImportPreview(null);
        return;
      }

      // 检查文件大小
      if (file.size > 1024 * 1024) { // 1MB
        setImportError('配置文件过大，请选择小于 1MB 的文件');
        setSelectedFile(null);
        setImportPreview(null);
        return;
      }

      setSelectedFile(file);
      setImportError(null);

      // 预览文件内容
      const reader = new FileReader();
      reader.onload = (e) => {
        try {
          const content = e.target?.result as string;
          const data = JSON.parse(content);
          setImportPreview(data);
        } catch (error) {
          setImportError('无法解析配置文件，请确保文件格式正确');
          setImportPreview(null);
        }
      };
      reader.onerror = () => {
        setImportError('读取文件失败，请重试');
        setImportPreview(null);
      };
      reader.readAsText(file);
    }
  };

  // 处理导入
  const handleImport = async () => {
    if (!selectedFile) {
      setImportError('请先选择配置文件');
      return;
    }

    try {
      setIsLoading(true);
      setImportError(null);

      const result = await importSystemConfig(selectedFile);

      if (result.success) {
        toast({
          title: '导入成功',
          description: '系统配置已成功导入'
        });

        // 调用导入成功回调
        if (onImportSuccess && result.data) {
          onImportSuccess(result.data);
        }

        // 关闭对话框
        onOpenChange(false);
      } else {
        setImportError(result.message || '导入失败，请确保文件格式正确');
        toast({
          variant: 'destructive',
          title: '导入失败',
          description: result.message || '无法导入系统配置，请稍后再试'
        });
      }
    } catch (error) {
      console.error('导入系统配置失败:', error);
      setImportError('导入失败，请稍后再试');
      toast({
        variant: 'destructive',
        title: '导入失败',
        description: '无法导入系统配置，请稍后再试'
      });
    } finally {
      setIsLoading(false);
    }
  };

  // 重置导入状态
  const resetImport = () => {
    setSelectedFile(null);
    setImportError(null);
    setImportPreview(null);
    if (fileInputRef.current) {
      fileInputRef.current.value = '';
    }
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-md">
        <DialogHeader>
          <DialogTitle>系统配置导入/导出</DialogTitle>
          <DialogDescription>
            导出当前系统配置或导入配置文件
          </DialogDescription>
        </DialogHeader>

        <Tabs value={activeTab} onValueChange={setActiveTab}>
          <TabsList className="grid grid-cols-2 mb-4">
            <TabsTrigger value="export">导出配置</TabsTrigger>
            <TabsTrigger value="import">导入配置</TabsTrigger>
          </TabsList>

          <TabsContent value="export" className="space-y-4">
            <div className="text-center py-8">
              <FileJson className="h-16 w-16 mx-auto mb-4 text-primary" />
              <h3 className="text-lg font-medium mb-2">导出系统配置</h3>
              <p className="text-sm text-muted-foreground mb-4">
                将当前系统配置导出为 JSON 文件，可用于备份或迁移
              </p>
              <Button
                onClick={handleExport}
                disabled={isLoading}
                className="w-full"
              >
                {isLoading ? (
                  <>
                    <RefreshCw className="mr-2 h-4 w-4 animate-spin" />
                    导出中...
                  </>
                ) : (
                  <>
                    <Download className="mr-2 h-4 w-4" />
                    导出配置
                  </>
                )}
              </Button>
            </div>
          </TabsContent>

          <TabsContent value="import" className="space-y-4">
            <div className="text-center py-4">
              <h3 className="text-lg font-medium mb-2">导入系统配置</h3>
              <p className="text-sm text-muted-foreground mb-4">
                从 JSON 文件导入系统配置，将覆盖当前配置
              </p>

              {importError && (
                <Alert variant="destructive" className="mb-4">
                  <AlertTriangle className="h-4 w-4" />
                  <AlertTitle>导入错误</AlertTitle>
                  <AlertDescription>{importError}</AlertDescription>
                </Alert>
              )}

              {selectedFile && !importError && (
                <Alert variant="default" className="mb-4 bg-green-50">
                  <CheckCircle2 className="h-4 w-4 text-green-500" />
                  <AlertTitle>文件已选择</AlertTitle>
                  <AlertDescription>
                    {selectedFile.name} ({(selectedFile.size / 1024).toFixed(2)} KB)
                  </AlertDescription>
                </Alert>
              )}

              <div className="space-y-4">
                <input
                  type="file"
                  accept=".json"
                  onChange={handleFileSelect}
                  className="hidden"
                  ref={fileInputRef}
                />
                <Button
                  variant="outline"
                  onClick={() => fileInputRef.current?.click()}
                  className="w-full"
                >
                  <Upload className="mr-2 h-4 w-4" />
                  选择配置文件
                </Button>

                {selectedFile && (
                  <Button
                    onClick={handleImport}
                    disabled={isLoading || !!importError}
                    className="w-full"
                  >
                    {isLoading ? (
                      <>
                        <RefreshCw className="mr-2 h-4 w-4 animate-spin" />
                        导入中...
                      </>
                    ) : (
                      <>
                        <Upload className="mr-2 h-4 w-4" />
                        导入配置
                      </>
                    )}
                  </Button>
                )}

                {selectedFile && (
                  <Button
                    variant="ghost"
                    onClick={resetImport}
                    disabled={isLoading}
                    className="w-full"
                  >
                    重置
                  </Button>
                )}
              </div>
            </div>
          </TabsContent>
        </Tabs>

        <DialogFooter>
          <Button variant="outline" onClick={() => onOpenChange(false)}>
            取消
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};

export default SystemConfigImportExportDialog;
