#!/usr/bin/env node

/**
 * 导出本地数据到远程D1数据库
 * 将本地SQLite数据库中的测试数据导入到Cloudflare D1生产数据库
 */

const sqlite3 = require('sqlite3').verbose();
const fs = require('fs');
const path = require('path');

// 数据库路径
const localDbPath = path.join(__dirname, 'database.db');
const exportSqlPath = path.join(__dirname, 'export-data.sql');

console.log('📤 开始导出本地数据到远程D1数据库...\n');

// 创建数据库连接
const db = new sqlite3.Database(localDbPath, (err) => {
  if (err) {
    console.error('❌ 本地数据库连接失败:', err.message);
    process.exit(1);
  }
  console.log('✅ 本地数据库连接成功');
});

// 导出数据的SQL语句数组
let exportStatements = [];

async function exportData() {
  try {
    console.log('\n📊 导出问卷回复数据...');

    // 导出问卷回复数据
    const questionnaireData = await new Promise((resolve, reject) => {
      db.all('SELECT * FROM questionnaire_responses_v2', (err, rows) => {
        if (err) reject(err);
        else resolve(rows);
      });
    });

    console.log(`✅ 找到 ${questionnaireData.length} 条问卷回复数据`);

    // 生成INSERT语句
    questionnaireData.forEach(row => {
      const values = [
        `'${row.id}'`,
        row.user_id ? `'${row.user_id}'` : 'NULL',
        row.session_id ? `'${row.session_id}'` : 'NULL',
        row.education_level ? `'${row.education_level}'` : 'NULL',
        row.education_level_display ? `'${row.education_level_display}'` : 'NULL',
        row.major_category ? `'${row.major_category}'` : 'NULL',
        row.major_display ? `'${row.major_display}'` : 'NULL',
        row.graduation_year || 'NULL',
        row.region_code ? `'${row.region_code}'` : 'NULL',
        row.region_display ? `'${row.region_display}'` : 'NULL',
        row.employment_status ? `'${row.employment_status}'` : 'NULL',
        row.current_industry_code ? `'${row.current_industry_code}'` : 'NULL',
        row.current_industry_display ? `'${row.current_industry_display}'` : 'NULL',
        row.salary_range ? `'${row.salary_range}'` : 'NULL',
        row.advice_content ? `'${row.advice_content.replace(/'/g, "''")}'` : 'NULL',
        row.observation_content ? `'${row.observation_content.replace(/'/g, "''")}'` : 'NULL',
        row.status ? `'${row.status}'` : "'processed'",
        row.created_at ? `'${row.created_at}'` : 'CURRENT_TIMESTAMP'
      ];

      exportStatements.push(
        `INSERT OR IGNORE INTO questionnaire_responses_v2 (id, user_id, session_id, education_level, education_level_display, major_category, major_display, graduation_year, region_code, region_display, employment_status, current_industry_code, current_industry_display, salary_range, advice_content, observation_content, status, created_at) VALUES (${values.join(', ')});`
      );
    });

    console.log('\n💬 导出问卷心声数据...');

    // 导出问卷心声数据
    const voicesData = await new Promise((resolve, reject) => {
      db.all('SELECT * FROM questionnaire_voices_v2', (err, rows) => {
        if (err) reject(err);
        else resolve(rows);
      });
    });

    console.log(`✅ 找到 ${voicesData.length} 条问卷心声数据`);

    // 生成INSERT语句
    voicesData.forEach(row => {
      const values = [
        `'${row.id}'`,
        row.source_response_id ? `'${row.source_response_id}'` : 'NULL',
        row.user_id ? `'${row.user_id}'` : 'NULL',
        row.voice_type ? `'${row.voice_type}'` : 'NULL',
        row.title ? `'${row.title.replace(/'/g, "''")}'` : 'NULL',
        row.content ? `'${row.content.replace(/'/g, "''")}'` : 'NULL',
        row.education_level ? `'${row.education_level}'` : 'NULL',
        row.education_level_display ? `'${row.education_level_display}'` : 'NULL',
        row.region_code ? `'${row.region_code}'` : 'NULL',
        row.region_display ? `'${row.region_display}'` : 'NULL',
        row.status ? `'${row.status}'` : "'approved'",
        row.likes || 0,
        row.views || 0,
        row.created_at ? `'${row.created_at}'` : 'CURRENT_TIMESTAMP'
      ];

      exportStatements.push(
        `INSERT OR IGNORE INTO questionnaire_voices_v2 (id, source_response_id, user_id, voice_type, title, content, education_level, education_level_display, region_code, region_display, status, likes, views, created_at) VALUES (${values.join(', ')});`
      );
    });

    console.log('\n📚 导出故事内容数据...');

    // 导出故事内容数据
    const storiesData = await new Promise((resolve, reject) => {
      db.all('SELECT * FROM story_contents_v2', (err, rows) => {
        if (err) reject(err);
        else resolve(rows);
      });
    });

    console.log(`✅ 找到 ${storiesData.length} 条故事内容数据`);

    // 生成INSERT语句
    storiesData.forEach(row => {
      const values = [
        `'${row.id}'`,
        row.user_id ? `'${row.user_id}'` : 'NULL',
        row.title ? `'${row.title.replace(/'/g, "''")}'` : 'NULL',
        row.content ? `'${row.content.replace(/'/g, "''")}'` : 'NULL',
        row.summary ? `'${row.summary.replace(/'/g, "''")}'` : 'NULL',
        row.category ? `'${row.category}'` : 'NULL',
        row.education_level ? `'${row.education_level}'` : 'NULL',
        row.education_level_display ? `'${row.education_level_display}'` : 'NULL',
        row.industry_code ? `'${row.industry_code}'` : 'NULL',
        row.industry_display ? `'${row.industry_display}'` : 'NULL',
        row.story_type ? `'${row.story_type}'` : "'experience'",
        row.is_anonymous ? row.is_anonymous : 1,
        row.status ? `'${row.status}'` : "'approved'",
        row.likes || 0,
        row.dislikes || 0,
        row.views || 0,
        row.quality_score || 0.0,
        row.word_count || 0,
        row.reading_time || 0,
        row.created_at ? `'${row.created_at}'` : 'CURRENT_TIMESTAMP'
      ];

      exportStatements.push(
        `INSERT OR IGNORE INTO story_contents_v2 (id, user_id, title, content, summary, category, education_level, education_level_display, industry_code, industry_display, story_type, is_anonymous, status, likes, dislikes, views, quality_score, word_count, reading_time, created_at) VALUES (${values.join(', ')});`
      );
    });

    // 写入SQL文件，移除外键约束和事务控制（D1不支持）
    const sqlContent = [
      '-- 数据导入到D1数据库',
      '-- 注意：D1会自动处理事务和约束',
      '',
      ...exportStatements
    ].join('\n');

    fs.writeFileSync(exportSqlPath, sqlContent);

    console.log(`\n📝 导出完成！生成了 ${exportStatements.length} 条SQL语句`);
    console.log(`📁 导出文件: ${exportSqlPath}`);
    console.log('\n🚀 现在可以运行以下命令将数据导入到远程D1数据库:');
    console.log(`npx wrangler d1 execute college-employment-survey-realapi --remote --file=export-data.sql`);

  } catch (error) {
    console.error('\n❌ 导出失败:', error);
    process.exit(1);
  } finally {
    db.close();
  }
}

// 运行导出
exportData();
