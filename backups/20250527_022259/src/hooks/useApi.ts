/**
 * useApi Hook
 * 
 * 提供对API客户端的访问，并处理认证令牌
 */

import { useCallback } from 'react';
import { apiClient } from '../services/apiClient';
import { useAuth } from './useAuth';

/**
 * API钩子，提供对API客户端的访问
 * 自动处理认证令牌
 */
export const useApi = () => {
  const { getToken } = useAuth();

  /**
   * 获取带有认证头的选项
   */
  const getAuthOptions = useCallback(() => {
    const token = getToken();
    return token ? { headers: { Authorization: `Bearer ${token}` } } : {};
  }, [getToken]);

  /**
   * GET请求
   */
  const get = useCallback(async (endpoint: string, options: any = {}) => {
    const authOptions = getAuthOptions();
    return apiClient.get(endpoint, {
      ...options,
      headers: {
        ...authOptions.headers,
        ...options.headers
      }
    });
  }, [getAuthOptions]);

  /**
   * POST请求
   */
  const post = useCallback(async (endpoint: string, data: any, options: any = {}) => {
    const authOptions = getAuthOptions();
    return apiClient.post(endpoint, data, {
      ...options,
      headers: {
        ...authOptions.headers,
        ...options.headers
      }
    });
  }, [getAuthOptions]);

  /**
   * PUT请求
   */
  const put = useCallback(async (endpoint: string, data: any, options: any = {}) => {
    const authOptions = getAuthOptions();
    return apiClient.put(endpoint, data, {
      ...options,
      headers: {
        ...authOptions.headers,
        ...options.headers
      }
    });
  }, [getAuthOptions]);

  /**
   * DELETE请求
   */
  const del = useCallback(async (endpoint: string, options: any = {}) => {
    const authOptions = getAuthOptions();
    return apiClient.delete(endpoint, {
      ...options,
      headers: {
        ...authOptions.headers,
        ...options.headers
      }
    });
  }, [getAuthOptions]);

  /**
   * PATCH请求
   */
  const patch = useCallback(async (endpoint: string, data: any, options: any = {}) => {
    const authOptions = getAuthOptions();
    return apiClient.patch(endpoint, data, {
      ...options,
      headers: {
        ...authOptions.headers,
        ...options.headers
      }
    });
  }, [getAuthOptions]);

  return {
    get,
    post,
    put,
    delete: del,
    patch
  };
};
