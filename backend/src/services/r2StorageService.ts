/**
 * R2 存储服务
 *
 * 提供与 Cloudflare R2 存储的交互功能
 */

import { Env } from '../types';
import { getMockStories, getMockQuestionnaireResponses, getMockStatistics } from './mockDataService';

// 定义 R2 存储的路径前缀
const PATH_PREFIXES = {
  STORIES: 'data/stories.json',
  STORY_TAGS: 'data/story_tags.json',
  POPULAR_TAGS: 'data/popular_tags.json',
  QUESTIONNAIRES: 'data/questionnaires.json',
  STATISTICS: 'data/statistics.json',
  VISUALIZATION_DATA: 'data/visualization_data.json',
};

/**
 * R2 存储服务类
 */
export class R2StorageService {
  private r2: R2Bucket;

  /**
   * 构造函数
   * @param env 环境变量
   */
  constructor(env: Env) {
    this.r2 = env.R2_BUCKET;
  }

  /**
   * 导出所有数据到 R2
   */
  async exportAllData(): Promise<void> {
    await this.exportStories();
    await this.exportQuestionnaires();
    await this.exportStatistics();
    console.log('All data exported to R2 successfully');
  }

  /**
   * 导出故事数据到 R2
   */
  async exportStories(): Promise<void> {
    try {
      // 获取故事数据
      const stories = getMockStories();
      
      // 将数据存储到 R2
      await this.r2.put(PATH_PREFIXES.STORIES, JSON.stringify(stories));
      
      // 提取标签
      const allTags: string[] = [];
      stories.forEach(story => {
        allTags.push(...story.tags);
      });
      
      // 计算标签出现次数
      const tagCounts: Record<string, number> = {};
      allTags.forEach(tag => {
        tagCounts[tag] = (tagCounts[tag] || 0) + 1;
      });
      
      // 转换为数组并按出现次数排序
      const popularTags = Object.entries(tagCounts)
        .map(([tag, count]) => ({ tag, count }))
        .sort((a, b) => b.count - a.count)
        .slice(0, 10); // 获取前10个标签
      
      // 将热门标签存储到 R2
      await this.r2.put(PATH_PREFIXES.POPULAR_TAGS, JSON.stringify(popularTags));
      
      console.log(`Exported ${stories.length} stories to R2`);
    } catch (error) {
      console.error('Error exporting stories to R2:', error);
      throw error;
    }
  }

  /**
   * 导出问卷数据到 R2
   */
  async exportQuestionnaires(): Promise<void> {
    try {
      // 获取问卷数据
      const questionnaires = getMockQuestionnaireResponses();
      
      // 将数据存储到 R2
      await this.r2.put(PATH_PREFIXES.QUESTIONNAIRES, JSON.stringify(questionnaires));
      
      console.log(`Exported ${questionnaires.length} questionnaires to R2`);
    } catch (error) {
      console.error('Error exporting questionnaires to R2:', error);
      throw error;
    }
  }

  /**
   * 导出统计数据到 R2
   */
  async exportStatistics(): Promise<void> {
    try {
      // 获取统计数据
      const stats = getMockStatistics();
      
      // 将数据存储到 R2
      await this.r2.put(PATH_PREFIXES.STATISTICS, JSON.stringify(stats));
      
      // 将可视化数据存储到 R2
      await this.r2.put(PATH_PREFIXES.VISUALIZATION_DATA, JSON.stringify({
        success: true,
        stats: stats,
      }));
      
      console.log('Exported statistics data to R2');
    } catch (error) {
      console.error('Error exporting statistics to R2:', error);
      throw error;
    }
  }

  /**
   * 从 R2 获取故事列表
   */
  async getStories(): Promise<any[]> {
    try {
      const storiesObj = await this.r2.get(PATH_PREFIXES.STORIES);
      if (!storiesObj) {
        return [];
      }
      
      const storiesText = await storiesObj.text();
      return JSON.parse(storiesText);
    } catch (error) {
      console.error('Error getting stories from R2:', error);
      return [];
    }
  }

  /**
   * 从 R2 获取热门标签
   */
  async getPopularTags(): Promise<any[]> {
    try {
      const tagsObj = await this.r2.get(PATH_PREFIXES.POPULAR_TAGS);
      if (!tagsObj) {
        return [];
      }
      
      const tagsText = await tagsObj.text();
      return JSON.parse(tagsText);
    } catch (error) {
      console.error('Error getting popular tags from R2:', error);
      return [];
    }
  }

  /**
   * 从 R2 获取问卷数据
   */
  async getQuestionnaires(): Promise<any[]> {
    try {
      const questionnairesObj = await this.r2.get(PATH_PREFIXES.QUESTIONNAIRES);
      if (!questionnairesObj) {
        return [];
      }
      
      const questionnairesText = await questionnairesObj.text();
      return JSON.parse(questionnairesText);
    } catch (error) {
      console.error('Error getting questionnaires from R2:', error);
      return [];
    }
  }

  /**
   * 从 R2 获取统计数据
   */
  async getStatistics(): Promise<any | null> {
    try {
      const statsObj = await this.r2.get(PATH_PREFIXES.STATISTICS);
      if (!statsObj) {
        return null;
      }
      
      const statsText = await statsObj.text();
      return JSON.parse(statsText);
    } catch (error) {
      console.error('Error getting statistics from R2:', error);
      return null;
    }
  }

  /**
   * 从 R2 获取可视化数据
   */
  async getVisualizationData(): Promise<any | null> {
    try {
      const dataObj = await this.r2.get(PATH_PREFIXES.VISUALIZATION_DATA);
      if (!dataObj) {
        return null;
      }
      
      const dataText = await dataObj.text();
      return JSON.parse(dataText);
    } catch (error) {
      console.error('Error getting visualization data from R2:', error);
      return null;
    }
  }
}
