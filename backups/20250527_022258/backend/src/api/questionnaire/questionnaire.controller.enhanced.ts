/**
 * 增强版问卷控制器
 * 
 * 使用数据转换服务确保前后端数据一致性
 */

import { Context } from 'hono';
import { PrismaClient } from '@prisma/client';
import { DataTransformService } from '../../services/dataTransformService';
import { Env } from '../../types';

const prisma = new PrismaClient();

/**
 * 提交问卷回复
 */
export const submitQuestionnaireEnhanced = async (c: Context<{ Bindings: Env }>) => {
  try {
    const body = await c.req.json();
    console.log('收到问卷提交请求:', body);

    // 验证数据
    const validationErrors = DataTransformService.validateQuestionnaireData(body);
    if (validationErrors.length > 0) {
      return c.json({
        success: false,
        error: '数据验证失败',
        details: validationErrors
      }, 400);
    }

    // 转换前端数据为后端格式
    const backendData = DataTransformService.transformQuestionnaireToBackend(body);
    
    // 生成序列号
    const tempId = Date.now(); // 临时ID用于生成序列号
    const sequenceNumber = DataTransformService.generateSequenceNumber('questionnaire', tempId);
    
    // 创建问卷回复
    const questionnaireResponse = await prisma.questionnaireResponse.create({
      data: {
        ...backendData,
        sequenceNumber,
        ipAddress: c.req.header('x-forwarded-for') || c.req.header('x-real-ip') || 'unknown'
      }
    });

    console.log('问卷回复创建成功:', questionnaireResponse.id);

    // 转换为前端格式返回
    const frontendResponse = DataTransformService.transformQuestionnaireToFrontend(questionnaireResponse);

    return c.json({
      success: true,
      data: frontendResponse,
      message: '问卷提交成功'
    });

  } catch (error) {
    console.error('提交问卷时发生错误:', error);
    return c.json({
      success: false,
      error: '提交失败，请稍后重试'
    }, 500);
  }
};

/**
 * 获取问卷统计数据
 */
export const getQuestionnaireStatsEnhanced = async (c: Context<{ Bindings: Env }>) => {
  try {
    console.log('获取问卷统计数据');

    // 获取基础统计
    const totalResponses = await prisma.questionnaireResponse.count();
    const verifiedResponses = await prisma.questionnaireResponse.count({
      where: { status: 'verified' }
    });
    const anonymousResponses = await prisma.questionnaireResponse.count({
      where: { isAnonymous: true }
    });

    // 获取教育水平分布
    const educationLevels = await prisma.questionnaireResponse.groupBy({
      by: ['educationLevel'],
      _count: { educationLevel: true },
      where: { educationLevel: { not: null } }
    });

    // 获取地区分布
    const regions = await prisma.questionnaireResponse.groupBy({
      by: ['region'],
      _count: { region: true },
      where: { region: { not: null } }
    });

    // 获取就业状态分布
    const employmentStatus = await prisma.questionnaireResponse.groupBy({
      by: ['employmentStatus'],
      _count: { employmentStatus: true },
      where: { employmentStatus: { not: null } }
    });

    // 获取行业分布
    const industries = await prisma.questionnaireResponse.groupBy({
      by: ['currentIndustry'],
      _count: { currentIndustry: true },
      where: { currentIndustry: { not: null } }
    });

    // 按日期统计回复数量
    const responsesByDate = await prisma.$queryRaw`
      SELECT DATE(createdAt) as date, COUNT(*) as count
      FROM QuestionnaireResponse
      WHERE createdAt >= datetime('now', '-30 days')
      GROUP BY DATE(createdAt)
      ORDER BY date DESC
    `;

    // 转换数据格式
    const statistics = {
      totalResponses,
      verifiedResponses,
      anonymousResponses,
      responsesByDate: Array.isArray(responsesByDate) ? responsesByDate.map((item: any) => ({
        date: item.date,
        count: Number(item.count)
      })) : [],
      educationLevels: educationLevels.map(item => ({
        name: item.educationLevel || '未知',
        count: item._count.educationLevel
      })),
      regions: regions.map(item => ({
        name: item.region || '未知',
        count: item._count.region
      })),
      employmentStatus: employmentStatus.map(item => ({
        name: item.employmentStatus || '未知',
        count: item._count.employmentStatus
      })),
      industries: industries.map(item => ({
        name: item.currentIndustry || '未知',
        count: item._count.currentIndustry
      }))
    };

    return c.json({
      success: true,
      statistics
    });

  } catch (error) {
    console.error('获取问卷统计时发生错误:', error);
    return c.json({
      success: false,
      error: '获取统计数据失败'
    }, 500);
  }
};

/**
 * 获取问卷回复列表
 */
export const getQuestionnairesEnhanced = async (c: Context<{ Bindings: Env }>) => {
  try {
    const page = parseInt(c.req.query('page') || '1');
    const pageSize = parseInt(c.req.query('pageSize') || '10');
    const skip = (page - 1) * pageSize;

    // 获取筛选参数
    const filters: any = {};
    const educationLevel = c.req.query('educationLevel');
    const employmentStatus = c.req.query('employmentStatus');
    const region = c.req.query('region');
    const isAnonymous = c.req.query('isAnonymous');

    if (educationLevel) filters.educationLevel = educationLevel;
    if (employmentStatus) filters.employmentStatus = employmentStatus;
    if (region) filters.region = region;
    if (isAnonymous !== undefined) filters.isAnonymous = isAnonymous === 'true';

    // 获取问卷回复
    const responses = await prisma.questionnaireResponse.findMany({
      where: filters,
      skip,
      take: pageSize,
      orderBy: { createdAt: 'desc' },
      include: {
        user: {
          select: {
            id: true,
            email: true,
            name: true,
            username: true
          }
        }
      }
    });

    // 获取总数
    const total = await prisma.questionnaireResponse.count({ where: filters });

    // 转换为前端格式
    const frontendResponses = responses.map(response => 
      DataTransformService.transformQuestionnaireToFrontend(response)
    );

    return c.json({
      success: true,
      data: frontendResponses,
      pagination: {
        page,
        pageSize,
        total,
        totalPages: Math.ceil(total / pageSize)
      }
    });

  } catch (error) {
    console.error('获取问卷回复列表时发生错误:', error);
    return c.json({
      success: false,
      error: '获取问卷回复列表失败'
    }, 500);
  }
};

/**
 * 获取单个问卷回复详情
 */
export const getQuestionnaireByIdEnhanced = async (c: Context<{ Bindings: Env }>) => {
  try {
    const id = parseInt(c.req.param('id'));

    if (isNaN(id)) {
      return c.json({
        success: false,
        error: '无效的问卷ID'
      }, 400);
    }

    const response = await prisma.questionnaireResponse.findUnique({
      where: { id },
      include: {
        user: {
          select: {
            id: true,
            email: true,
            name: true,
            username: true
          }
        }
      }
    });

    if (!response) {
      return c.json({
        success: false,
        error: '问卷回复不存在'
      }, 404);
    }

    // 转换为前端格式
    const frontendResponse = DataTransformService.transformQuestionnaireToFrontend(response);

    return c.json({
      success: true,
      data: frontendResponse
    });

  } catch (error) {
    console.error('获取问卷回复详情时发生错误:', error);
    return c.json({
      success: false,
      error: '获取问卷回复详情失败'
    }, 500);
  }
};

/**
 * 清理资源
 */
process.on('beforeExit', async () => {
  await prisma.$disconnect();
});
