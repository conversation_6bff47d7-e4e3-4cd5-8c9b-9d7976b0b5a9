/**
 * 指纹跟踪服务
 * 
 * 负责跟踪浏览器指纹，识别重复提交
 */

import { Context } from 'hono';
import { FingerprintingConfig, ValidationResult } from '../types';

/**
 * 指纹跟踪服务类
 */
export class FingerprintTracker {
  private config: FingerprintingConfig;

  /**
   * 构造函数
   */
  constructor() {
    this.config = {
      enabled: false,
      storageTime: 0
    };
  }

  /**
   * 初始化服务
   * @param config 指纹配置
   */
  public initialize(config: FingerprintingConfig): void {
    this.config = { ...config };
  }

  /**
   * 更新配置
   * @param config 指纹配置
   */
  public updateConfig(config: FingerprintingConfig): void {
    this.config = { ...this.config, ...config };
  }

  /**
   * 跟踪指纹
   * @param fingerprint 浏览器指纹
   * @param clientIp 客户端 IP
   * @param env 环境变量
   */
  public async trackFingerprint(
    fingerprint: string,
    clientIp: string,
    env: any
  ): Promise<ValidationResult> {
    // 如果未启用指纹跟踪，直接返回有效
    if (!this.config.enabled) {
      return { valid: true };
    }

    try {
      // 记录指纹
      await this.recordFingerprint(fingerprint, clientIp, env);
      
      return { valid: true };
    } catch (error) {
      console.error('Error tracking fingerprint:', error);
      // 出错时允许请求通过
      return { valid: true };
    }
  }

  /**
   * 记录指纹
   * @param fingerprint 浏览器指纹
   * @param clientIp 客户端 IP
   * @param env 环境变量
   */
  private async recordFingerprint(
    fingerprint: string,
    clientIp: string,
    env: any
  ): Promise<void> {
    const key = `fingerprint:${fingerprint}`;
    
    // 创建指纹记录
    const record = {
      fingerprint,
      clientIp,
      timestamp: Date.now(),
      count: 1
    };
    
    try {
      // 尝试从 KV 存储获取现有记录
      if (env && env.SURVEY_SECURITY) {
        const existingRecord = await env.SURVEY_SECURITY.get(key, { type: 'json' });
        
        if (existingRecord) {
          // 更新记录
          record.count = existingRecord.count + 1;
        }
        
        // 存储记录
        await env.SURVEY_SECURITY.put(key, JSON.stringify(record), {
          expirationTtl: this.config.storageTime * 24 * 60 * 60 // 转换为秒
        });
      } else {
        // 如果没有 KV 存储，使用内存存储（仅用于开发环境）
        const memoryStorage = global.__fingerprints = global.__fingerprints || {};
        
        if (memoryStorage[key]) {
          // 更新记录
          record.count = memoryStorage[key].count + 1;
        }
        
        memoryStorage[key] = record;
        
        // 设置过期时间
        setTimeout(() => {
          if (memoryStorage[key] === record) {
            delete memoryStorage[key];
          }
        }, this.config.storageTime * 24 * 60 * 60 * 1000); // 转换为毫秒
      }
    } catch (error) {
      console.error('Error recording fingerprint:', error);
      throw error;
    }
  }

  /**
   * 获取指纹记录
   * @param fingerprint 浏览器指纹
   * @param env 环境变量
   */
  public async getFingerprintRecord(
    fingerprint: string,
    env: any
  ): Promise<any | null> {
    const key = `fingerprint:${fingerprint}`;
    
    try {
      // 尝试从 KV 存储获取记录
      if (env && env.SURVEY_SECURITY) {
        return await env.SURVEY_SECURITY.get(key, { type: 'json' });
      } else {
        // 如果没有 KV 存储，使用内存存储
        const memoryStorage = global.__fingerprints = global.__fingerprints || {};
        return memoryStorage[key] || null;
      }
    } catch (error) {
      console.error('Error getting fingerprint record:', error);
      return null;
    }
  }
}

// 为内存存储声明全局类型
declare global {
  var __fingerprints: Record<string, any>;
}
