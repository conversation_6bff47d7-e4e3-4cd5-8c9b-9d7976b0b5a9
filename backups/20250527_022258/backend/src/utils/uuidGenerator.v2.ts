/**
 * UUID生成工具 v2.0
 * 基于优化后的命名规范生成带前缀的UUID
 */

import { v4 as uuidv4 } from 'uuid';

// UUID前缀定义
export const UUID_PREFIXES = {
  // 用户相关
  USER: 'user_',
  ANONYMOUS: 'anon_',
  
  // 问卷系统
  QUESTIONNAIRE: 'quest_',
  QUESTION: 'qitem_',
  ANSWER: 'ans_',
  RESPONSE: 'resp_',
  VOICE: 'voice_',
  
  // 故事系统
  STORY_CONTENT: 'text_', // txid
  
  // 审核系统
  REVIEW_LOG: 'review_',
  OPERATION_LOG: 'op_',
  
  // 投票和互动
  VOTE: 'vote_',
  
  // 系统配置
  CONFIG: 'config_',
  WORD: 'word_',
  TAG: 'tag_',
  STATS: 'stats_'
} as const;

// UUID类型定义
export type UUIDPrefix = typeof UUID_PREFIXES[keyof typeof UUID_PREFIXES];

/**
 * 生成带前缀的UUID
 * @param prefix UUID前缀
 * @returns 带前缀的UUID字符串
 */
export function generatePrefixedUUID(prefix: UUIDPrefix): string {
  return `${prefix}${uuidv4()}`;
}

/**
 * 生成用户UUID
 * @param isAnonymous 是否为匿名用户
 * @returns 用户UUID
 */
export function generateUserUUID(isAnonymous: boolean = false): string {
  const prefix = isAnonymous ? UUID_PREFIXES.ANONYMOUS : UUID_PREFIXES.USER;
  return generatePrefixedUUID(prefix);
}

/**
 * 生成匿名登录ID
 * @returns 匿名登录ID (anon_xxxxxxxx)
 */
export function generateAnonymousId(): string {
  const uuid = uuidv4();
  return `anon_${uuid.slice(0, 8)}`;
}

/**
 * 生成问卷相关UUID
 */
export const QuestionnaireUUIDs = {
  /**
   * 生成问卷模板UUID
   */
  template: () => generatePrefixedUUID(UUID_PREFIXES.QUESTIONNAIRE),
  
  /**
   * 生成题目UUID
   */
  question: () => generatePrefixedUUID(UUID_PREFIXES.QUESTION),
  
  /**
   * 生成答案UUID
   */
  answer: () => generatePrefixedUUID(UUID_PREFIXES.ANSWER),
  
  /**
   * 生成问卷回复UUID
   */
  response: () => generatePrefixedUUID(UUID_PREFIXES.RESPONSE),
  
  /**
   * 生成心声UUID
   */
  voice: () => generatePrefixedUUID(UUID_PREFIXES.VOICE),
  
  /**
   * 生成统计UUID
   */
  stats: () => generatePrefixedUUID(UUID_PREFIXES.STATS)
};

/**
 * 生成故事相关UUID
 */
export const StoryUUIDs = {
  /**
   * 生成故事内容UUID (txid)
   */
  content: () => generatePrefixedUUID(UUID_PREFIXES.STORY_CONTENT)
};

/**
 * 生成审核相关UUID
 */
export const ReviewUUIDs = {
  /**
   * 生成审核日志UUID
   */
  log: () => generatePrefixedUUID(UUID_PREFIXES.REVIEW_LOG),
  
  /**
   * 生成操作日志UUID
   */
  operation: () => generatePrefixedUUID(UUID_PREFIXES.OPERATION_LOG)
};

/**
 * 生成系统相关UUID
 */
export const SystemUUIDs = {
  /**
   * 生成配置UUID
   */
  config: () => generatePrefixedUUID(UUID_PREFIXES.CONFIG),
  
  /**
   * 生成敏感词UUID
   */
  word: () => generatePrefixedUUID(UUID_PREFIXES.WORD),
  
  /**
   * 生成标签UUID
   */
  tag: () => generatePrefixedUUID(UUID_PREFIXES.TAG),
  
  /**
   * 生成投票UUID
   */
  vote: () => generatePrefixedUUID(UUID_PREFIXES.VOTE)
};

/**
 * 生成提交会话ID
 * @returns 会话ID
 */
export function generateSubmissionSessionId(): string {
  return `session_${uuidv4()}`;
}

/**
 * 验证UUID格式
 * @param uuid UUID字符串
 * @param expectedPrefix 期望的前缀
 * @returns 是否有效
 */
export function validateUUID(uuid: string, expectedPrefix?: UUIDPrefix): boolean {
  // 基本格式验证
  const uuidRegex = /^[a-z]+_[0-9a-f]{8}-[0-9a-f]{4}-4[0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i;
  
  if (!uuidRegex.test(uuid)) {
    return false;
  }
  
  // 前缀验证
  if (expectedPrefix) {
    return uuid.startsWith(expectedPrefix);
  }
  
  return true;
}

/**
 * 从UUID中提取前缀
 * @param uuid UUID字符串
 * @returns 前缀字符串
 */
export function extractPrefix(uuid: string): string {
  const underscoreIndex = uuid.indexOf('_');
  if (underscoreIndex === -1) {
    return '';
  }
  return uuid.substring(0, underscoreIndex + 1);
}

/**
 * 从UUID中提取纯UUID部分
 * @param uuid 带前缀的UUID
 * @returns 纯UUID字符串
 */
export function extractPureUUID(uuid: string): string {
  const underscoreIndex = uuid.indexOf('_');
  if (underscoreIndex === -1) {
    return uuid;
  }
  return uuid.substring(underscoreIndex + 1);
}

/**
 * 批量生成UUID
 * @param prefix UUID前缀
 * @param count 生成数量
 * @returns UUID数组
 */
export function generateBatchUUIDs(prefix: UUIDPrefix, count: number): string[] {
  return Array.from({ length: count }, () => generatePrefixedUUID(prefix));
}

/**
 * UUID工具类
 */
export class UUIDGenerator {
  /**
   * 生成用户UUID
   */
  static user(isAnonymous: boolean = false): string {
    return generateUserUUID(isAnonymous);
  }
  
  /**
   * 生成匿名ID
   */
  static anonymousId(): string {
    return generateAnonymousId();
  }
  
  /**
   * 问卷相关UUID
   */
  static questionnaire = QuestionnaireUUIDs;
  
  /**
   * 故事相关UUID
   */
  static story = StoryUUIDs;
  
  /**
   * 审核相关UUID
   */
  static review = ReviewUUIDs;
  
  /**
   * 系统相关UUID
   */
  static system = SystemUUIDs;
  
  /**
   * 生成会话ID
   */
  static sessionId(): string {
    return generateSubmissionSessionId();
  }
  
  /**
   * 验证UUID
   */
  static validate(uuid: string, expectedPrefix?: UUIDPrefix): boolean {
    return validateUUID(uuid, expectedPrefix);
  }
  
  /**
   * 提取前缀
   */
  static getPrefix(uuid: string): string {
    return extractPrefix(uuid);
  }
  
  /**
   * 提取纯UUID
   */
  static getPureUUID(uuid: string): string {
    return extractPureUUID(uuid);
  }
}

// 默认导出
export default UUIDGenerator;
