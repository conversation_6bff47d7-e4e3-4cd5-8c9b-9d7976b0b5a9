/**
 * 简化的数据库初始化脚本
 * 
 * 使用 sqlite3 命令行工具而不是 better-sqlite3 包
 * 避免编译问题，直接执行 SQL 脚本
 */

import { execSync } from 'child_process';
import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// 配置
const DB_PATH = '../database.db';
const BACKUP_PATH = `../database-backup-${Date.now()}.db`;

// 日志函数
function log(message) {
  console.log(`[${new Date().toISOString()}] ${message}`);
}

// 检查 sqlite3 是否可用
function checkSqlite3() {
  try {
    execSync('sqlite3 --version', { stdio: 'pipe' });
    log('✅ sqlite3 命令行工具可用');
    return true;
  } catch (error) {
    log('❌ sqlite3 命令行工具不可用');
    log('请安装 sqlite3: brew install sqlite (macOS) 或 apt-get install sqlite3 (Ubuntu)');
    return false;
  }
}

// 备份现有数据库
function backupDatabase() {
  if (fs.existsSync(DB_PATH)) {
    log('📦 备份现有数据库...');
    fs.copyFileSync(DB_PATH, BACKUP_PATH);
    log(`✅ 数据库已备份到: ${BACKUP_PATH}`);
  } else {
    log('ℹ️  未找到现有数据库，将创建新数据库');
  }
}

// 执行 SQL 脚本
function executeSqlScript(scriptPath, dbPath) {
  try {
    log(`📝 执行 SQL 脚本: ${scriptPath}`);
    const command = `sqlite3 "${dbPath}" < "${scriptPath}"`;
    execSync(command, { stdio: 'pipe' });
    log('✅ SQL 脚本执行成功');
  } catch (error) {
    log(`❌ SQL 脚本执行失败: ${error.message}`);
    throw error;
  }
}

// 生成测试数据的 SQL
function generateTestDataSQL() {
  const testDataSQL = `
-- 插入测试用户
INSERT OR IGNORE INTO users_v2 (id, user_type, display_name, created_at) VALUES
('anon_test_001', 'anonymous', NULL, datetime('now', '-30 days')),
('anon_test_002', 'anonymous', NULL, datetime('now', '-25 days')),
('anon_test_003', 'anonymous', NULL, datetime('now', '-20 days')),
('anon_test_004', 'anonymous', NULL, datetime('now', '-15 days')),
('anon_test_005', 'anonymous', NULL, datetime('now', '-10 days')),
('user_test_001', 'registered', '测试用户1', datetime('now', '-35 days')),
('user_test_002', 'registered', '测试用户2', datetime('now', '-28 days'));

-- 插入测试问卷回复
INSERT OR IGNORE INTO questionnaire_responses_v2 (
  id, user_id, session_id, education_level, education_level_display,
  major_category, major_display, graduation_year, region_code, region_display,
  employment_status, current_industry_code, current_industry_display,
  salary_range, advice_content, observation_content, created_at
) VALUES
('quest_test_001', 'anon_test_001', 'sess_test_001', 'undergraduate', '本科',
 '计算机科学与技术', '计算机科学与技术', 2023, 'tier1_cities', '北上广深',
 'employed', 'technology', '互联网/IT', '8000-12000',
 '建议学弟学妹们要注重实践能力的培养，多参加实习和项目经验。',
 '当前互联网行业的就业形势总体向好，特别是技术技能的需求在增长。',
 datetime('now', '-30 days')),

('quest_test_002', 'anon_test_002', 'sess_test_002', 'master', '硕士',
 '软件工程', '软件工程', 2022, 'provincial_capitals', '省会城市',
 'employed', 'technology', '互联网/IT', '12000-20000',
 '在技术行业工作了几年，我觉得最重要的是保持学习的心态，技术更新很快。',
 '从省会城市的就业市场来看，硕士学历的竞争比较激烈，需要有突出的专业能力。',
 datetime('now', '-25 days')),

('quest_test_003', 'anon_test_003', 'sess_test_003', 'undergraduate', '本科',
 '金融学', '金融学', 2023, 'tier2_cities', '二线城市',
 'unemployed', 'finance', '金融', '5000-8000',
 '找工作时一定要做好充分的准备，包括简历优化、面试技巧和专业知识的复习。',
 '疫情对就业市场产生了一定影响，但金融行业相对稳定，远程工作机会增加。',
 datetime('now', '-20 days')),

('quest_test_004', 'anon_test_004', 'sess_test_004', 'associate', '大专',
 '市场营销', '市场营销', 2023, 'tier3_cities', '三四线城市',
 'employed', 'service', '服务业', '3000-5000',
 '不要只看重薪资，公司的发展前景和学习机会同样重要。',
 '一线城市机会多但竞争激烈，二三线城市的发展机会也不错，生活成本相对较低。',
 datetime('now', '-15 days')),

('quest_test_005', 'anon_test_005', 'sess_test_005', 'undergraduate', '本科',
 '机械工程', '机械工程', 2022, 'tier1_cities', '北上广深',
 'studying', 'manufacturing', '制造业', '6000-8000',
 '建议多关注行业动态，了解市场需求，这样能更好地规划自己的职业发展。',
 '现在企业更看重候选人的综合素质，不仅仅是专业技能，沟通能力和团队协作也很重要。',
 datetime('now', '-10 days'));

-- 插入问卷心声（从问卷回复中提取）
INSERT OR IGNORE INTO questionnaire_voices_v2 (
  id, source_response_id, user_id, voice_type, title, content,
  education_level, education_level_display, region_code, region_display,
  status, likes, views, created_at
) VALUES
('voice_test_001', 'quest_test_001', 'anon_test_001', 'advice', '给高三学子的建议',
 '建议学弟学妹们要注重实践能力的培养，多参加实习和项目经验。',
 'undergraduate', '本科', 'tier1_cities', '北上广深',
 'approved', 15, 120, datetime('now', '-30 days')),

('voice_test_002', 'quest_test_001', 'anon_test_001', 'observation', '对当前就业环境的观察',
 '当前互联网行业的就业形势总体向好，特别是技术技能的需求在增长。',
 'undergraduate', '本科', 'tier1_cities', '北上广深',
 'approved', 8, 95, datetime('now', '-30 days')),

('voice_test_003', 'quest_test_002', 'anon_test_002', 'advice', '给高三学子的建议',
 '在技术行业工作了几年，我觉得最重要的是保持学习的心态，技术更新很快。',
 'master', '硕士', 'provincial_capitals', '省会城市',
 'approved', 22, 180, datetime('now', '-25 days')),

('voice_test_004', 'quest_test_003', 'anon_test_003', 'advice', '给高三学子的建议',
 '找工作时一定要做好充分的准备，包括简历优化、面试技巧和专业知识的复习。',
 'undergraduate', '本科', 'tier2_cities', '二线城市',
 'approved', 18, 150, datetime('now', '-20 days')),

('voice_test_005', 'quest_test_004', 'anon_test_004', 'advice', '给高三学子的建议',
 '不要只看重薪资，公司的发展前景和学习机会同样重要。',
 'associate', '大专', 'tier3_cities', '三四线城市',
 'approved', 12, 88, datetime('now', '-15 days'));

-- 插入测试故事
INSERT OR IGNORE INTO story_contents_v2 (
  id, user_id, title, content, summary, category,
  education_level, education_level_display, industry_code, industry_display,
  story_type, is_anonymous, status, likes, dislikes, views,
  quality_score, word_count, reading_time, created_at
) VALUES
('story_test_001', 'anon_test_001', '从迷茫到清晰：我的职业规划之路',
 '刚毕业的时候，我对未来充满了不确定性。经过了几个月的求职经历，我逐渐明确了自己的职业方向...',
 '刚毕业的时候，我对未来充满了不确定性。经过了几个月的求职经历...',
 '求职经验', 'undergraduate', '本科', 'technology', '互联网/IT',
 'experience', true, 'approved', 45, 2, 320, 4.2, 850, 4, datetime('now', '-28 days')),

('story_test_002', 'anon_test_002', '三次面试失败后，我终于找到了理想工作',
 '记得第一次面试时的紧张和兴奋，虽然没有成功，但每次面试都是一次宝贵的学习机会...',
 '记得第一次面试时的紧张和兴奋，虽然没有成功，但每次面试都是一次宝贵的学习机会...',
 '求职经验', 'master', '硕士', 'technology', '互联网/IT',
 'experience', true, 'approved', 38, 1, 280, 4.5, 720, 3, datetime('now', '-22 days')),

('story_test_003', 'user_test_001', '在北京打拼的这些年',
 '从本科毕业后，我选择了北京作为我职业生涯的起点，这里的机会和挑战并存...',
 '从本科毕业后，我选择了北京作为我职业生涯的起点，这里的机会和挑战并存...',
 '职场感悟', 'undergraduate', '本科', 'finance', '金融',
 'experience', false, 'approved', 52, 3, 410, 4.0, 950, 5, datetime('now', '-18 days'));

-- 更新统计信息
UPDATE system_config_v2 SET value = datetime('now') WHERE key = 'last_data_update';
INSERT OR REPLACE INTO system_config_v2 (key, value, description, category) VALUES
('test_data_version', '2.0', '测试数据版本', 'system'),
('test_data_generated', datetime('now'), '测试数据生成时间', 'system');
`;

  return testDataSQL;
}

// 验证数据库
function validateDatabase() {
  log('🔍 验证数据库结构和数据...');
  
  const tables = [
    'users_v2',
    'questionnaire_responses_v2',
    'questionnaire_voices_v2',
    'story_contents_v2',
    'tags_v2'
  ];
  
  const results = {};
  
  for (const table of tables) {
    try {
      const command = `sqlite3 "${DB_PATH}" "SELECT COUNT(*) FROM ${table};"`;
      const count = execSync(command, { encoding: 'utf8' }).trim();
      results[table] = parseInt(count);
      log(`  ✅ ${table}: ${count} 条记录`);
    } catch (error) {
      log(`  ❌ ${table}: 查询失败`);
      results[table] = 0;
    }
  }
  
  return results;
}

// 主执行函数
async function main() {
  console.log('======================================');
  console.log('       简化数据库初始化脚本 v2.0');
  console.log('======================================');
  console.log('');
  
  try {
    // 1. 检查依赖
    if (!checkSqlite3()) {
      process.exit(1);
    }
    
    // 2. 备份现有数据库
    backupDatabase();
    
    // 3. 执行数据库结构脚本
    log('🏗️  创建数据库结构...');
    executeSqlScript('schema-v2.sql', DB_PATH);
    
    // 4. 生成并执行测试数据
    log('📊 生成测试数据...');
    const testDataSQL = generateTestDataSQL();
    const testDataPath = 'test-data-v2.sql';
    fs.writeFileSync(testDataPath, testDataSQL);
    executeSqlScript(testDataPath, DB_PATH);
    
    // 5. 验证数据库
    const results = validateDatabase();
    
    // 6. 生成报告
    log('📋 生成初始化报告...');
    const report = {
      timestamp: new Date().toISOString(),
      database: DB_PATH,
      backup: BACKUP_PATH,
      results,
      summary: {
        totalUsers: results.users_v2 || 0,
        totalQuestionnaires: results.questionnaire_responses_v2 || 0,
        totalVoices: results.questionnaire_voices_v2 || 0,
        totalStories: results.story_contents_v2 || 0,
        totalTags: results.tags_v2 || 0
      }
    };
    
    const reportPath = `init-report-${Date.now()}.json`;
    fs.writeFileSync(reportPath, JSON.stringify(report, null, 2));
    
    console.log('');
    log('🎉 数据库初始化完成！');
    console.log('');
    console.log('📊 数据统计:');
    console.log(`   - 用户: ${report.summary.totalUsers}`);
    console.log(`   - 问卷回复: ${report.summary.totalQuestionnaires}`);
    console.log(`   - 问卷心声: ${report.summary.totalVoices}`);
    console.log(`   - 故事内容: ${report.summary.totalStories}`);
    console.log(`   - 系统标签: ${report.summary.totalTags}`);
    console.log('');
    console.log(`📋 详细报告: ${reportPath}`);
    console.log(`💾 数据库备份: ${BACKUP_PATH}`);
    
    // 清理临时文件
    if (fs.existsSync(testDataPath)) {
      fs.unlinkSync(testDataPath);
    }
    
  } catch (error) {
    log(`❌ 初始化失败: ${error.message}`);
    process.exit(1);
  }
}

// 如果直接运行此脚本
if (import.meta.url === `file://${process.argv[1]}`) {
  main();
}

export { main };
