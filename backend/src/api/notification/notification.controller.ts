/**
 * 通知控制器
 * 
 * 处理通知相关的API请求
 */

import { Context } from 'hono';
import { Env } from '../../types';
import { PrismaClient } from '@prisma/client';
import { NotificationService } from '../../services/notification/notificationService';

// 初始化Prisma客户端
const prisma = new PrismaClient();

/**
 * 获取用户通知列表
 */
export const getUserNotifications = async (c: Context<{ Bindings: Env }>) => {
  try {
    // 获取用户ID
    const userId = c.get('userId');
    
    if (!userId) {
      return c.json({
        success: false,
        error: '未授权'
      }, 401);
    }
    
    // 获取分页参数
    const page = parseInt(c.req.query('page') || '1');
    const pageSize = parseInt(c.req.query('pageSize') || '10');
    
    // 获取通知服务实例
    const notificationService = NotificationService.getInstance();
    
    // 获取用户通知列表
    const result = await notificationService.getUserNotifications(userId, page, pageSize);
    
    return c.json({
      success: true,
      ...result
    });
  } catch (error) {
    console.error('获取用户通知列表失败:', error);
    return c.json({
      success: false,
      error: '获取通知列表失败'
    }, 500);
  }
};

/**
 * 获取未读通知数量
 */
export const getUnreadCount = async (c: Context<{ Bindings: Env }>) => {
  try {
    // 获取用户ID
    const userId = c.get('userId');
    
    if (!userId) {
      return c.json({
        success: false,
        error: '未授权'
      }, 401);
    }
    
    // 获取通知服务实例
    const notificationService = NotificationService.getInstance();
    
    // 获取未读通知数量
    const count = await notificationService.getUnreadCount(userId);
    
    return c.json({
      success: true,
      count
    });
  } catch (error) {
    console.error('获取未读通知数量失败:', error);
    return c.json({
      success: false,
      error: '获取未读通知数量失败'
    }, 500);
  }
};

/**
 * 标记通知为已读
 */
export const markAsRead = async (c: Context<{ Bindings: Env }>) => {
  try {
    // 获取用户ID
    const userId = c.get('userId');
    
    if (!userId) {
      return c.json({
        success: false,
        error: '未授权'
      }, 401);
    }
    
    // 获取通知ID
    const { id } = c.req.param();
    
    // 验证通知所有权
    const notification = await prisma.notification.findUnique({
      where: { id }
    });
    
    if (!notification) {
      return c.json({
        success: false,
        error: '通知不存在'
      }, 404);
    }
    
    if (notification.userId !== userId) {
      return c.json({
        success: false,
        error: '无权操作此通知'
      }, 403);
    }
    
    // 获取通知服务实例
    const notificationService = NotificationService.getInstance();
    
    // 标记为已读
    await notificationService.markAsRead(id);
    
    return c.json({
      success: true,
      message: '已标记为已读'
    });
  } catch (error) {
    console.error('标记通知为已读失败:', error);
    return c.json({
      success: false,
      error: '标记通知为已读失败'
    }, 500);
  }
};

/**
 * 标记所有通知为已读
 */
export const markAllAsRead = async (c: Context<{ Bindings: Env }>) => {
  try {
    // 获取用户ID
    const userId = c.get('userId');
    
    if (!userId) {
      return c.json({
        success: false,
        error: '未授权'
      }, 401);
    }
    
    // 更新所有未读通知
    await prisma.notification.updateMany({
      where: {
        userId,
        isRead: false
      },
      data: {
        isRead: true,
        readAt: new Date()
      }
    });
    
    return c.json({
      success: true,
      message: '已标记所有通知为已读'
    });
  } catch (error) {
    console.error('标记所有通知为已读失败:', error);
    return c.json({
      success: false,
      error: '标记所有通知为已读失败'
    }, 500);
  }
};

/**
 * 删除通知
 */
export const deleteNotification = async (c: Context<{ Bindings: Env }>) => {
  try {
    // 获取用户ID
    const userId = c.get('userId');
    
    if (!userId) {
      return c.json({
        success: false,
        error: '未授权'
      }, 401);
    }
    
    // 获取通知ID
    const { id } = c.req.param();
    
    // 验证通知所有权
    const notification = await prisma.notification.findUnique({
      where: { id }
    });
    
    if (!notification) {
      return c.json({
        success: false,
        error: '通知不存在'
      }, 404);
    }
    
    if (notification.userId !== userId) {
      return c.json({
        success: false,
        error: '无权操作此通知'
      }, 403);
    }
    
    // 删除通知
    await prisma.notification.delete({
      where: { id }
    });
    
    return c.json({
      success: true,
      message: '通知已删除'
    });
  } catch (error) {
    console.error('删除通知失败:', error);
    return c.json({
      success: false,
      error: '删除通知失败'
    }, 500);
  }
};
