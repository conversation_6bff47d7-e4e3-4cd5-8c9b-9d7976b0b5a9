import { Hono } from 'hono';
import { z } from 'zod';
import { zValidator } from '@hono/zod-validator';
import { PrismaClient } from '@prisma/client';
import { adminAuth } from '../middlewares/adminAuth';
import { getPrismaClient } from '../utils/prisma';
import { mockStatistics, shouldUseMockData } from '../mock';

// 创建统计路由
const app = new Hono();

// 统计过滤器验证模式
const statisticsFilterSchema = z.object({
  educationLevel: z.string().optional(),
  region: z.string().optional(),
  graduationYear: z.number().int().optional(),
  startDate: z.string().optional(),
  endDate: z.string().optional(),
  isAnonymous: z.boolean().optional(),
});

// 获取统计数据
app.get('/', adminAuth, async (c) => {
  try {
    // 解析查询参数
    const url = new URL(c.req.url);
    const educationLevel = url.searchParams.get('educationLevel') || undefined;
    const region = url.searchParams.get('region') || undefined;
    const graduationYear = url.searchParams.get('graduationYear') ?
      parseInt(url.searchParams.get('graduationYear')!) : undefined;
    const startDate = url.searchParams.get('startDate') || undefined;
    const endDate = url.searchParams.get('endDate') || undefined;
    const isAnonymous = url.searchParams.get('isAnonymous') ?
      url.searchParams.get('isAnonymous') === 'true' : undefined;

    // 检查是否应该使用模拟数据
    if (shouldUseMockData(c.env)) {
      console.log('Development mode: Returning mock statistics');

      // 返回模拟统计数据
      return c.json({
        success: true,
        statistics: mockStatistics,
      });
    }

    // 使用适配的Prisma客户端
    const prisma = getPrismaClient(c.env);

    // 构建查询条件
    const where: any = {};

    if (educationLevel) {
      where.educationLevel = educationLevel;
    }

    if (region) {
      where.region = region;
    }

    if (graduationYear) {
      where.graduationYear = graduationYear;
    }

    if (startDate || endDate) {
      where.createdAt = {};

      if (startDate) {
        where.createdAt.gte = new Date(startDate);
      }

      if (endDate) {
        where.createdAt.lte = new Date(endDate);
      }
    }

    if (isAnonymous !== undefined) {
      where.isAnonymous = isAnonymous;
    }

    // 获取总数
    const totalCount = await prisma.questionnaireResponse.count({ where });

    // 获取已验证数量
    const verifiedCount = await prisma.questionnaireResponse.count({
      where: {
        ...where,
        isAnonymous: false,
      },
    });

    // 获取匿名数量
    const anonymousCount = await prisma.questionnaireResponse.count({
      where: {
        ...where,
        isAnonymous: true,
      },
    });

    // 获取已就业数量
    const employedCount = await prisma.questionnaireResponse.count({
      where: {
        ...where,
        employmentStatus: 'employed',
      },
    });

    // 获取未就业数量
    const unemployedCount = await prisma.questionnaireResponse.count({
      where: {
        ...where,
        employmentStatus: 'unemployed',
      },
    });

    // 注意：使用模拟客户端时不需要断开连接
    if (typeof prisma.$disconnect === 'function') {
      await prisma.$disconnect();
    }

    // 构建统计数据
    const statistics = {
      totalCount,
      verifiedCount,
      anonymousCount,
      employedCount,
      unemployedCount,
      // 其他统计数据...
    };

    return c.json({
      success: true,
      statistics,
    });
  } catch (error) {
    console.error('Error getting statistics:', error);
    return c.json({ success: false, error: 'Failed to get statistics' }, 500);
  }
});

// 导出统计报表
app.get('/export', adminAuth, async (c) => {
  try {
    // 解析查询参数
    const url = new URL(c.req.url);
    const format = url.searchParams.get('format') || 'csv';

    // 检查是否应该使用模拟数据
    if (shouldUseMockData(c.env)) {
      console.log('Development mode: Returning mock export URL');

      // 返回模拟导出URL
      return c.json({
        success: true,
        downloadUrl: `https://example.com/mock-export.${format}`,
        expiresAt: new Date(Date.now() + 24 * 60 * 60 * 1000).toISOString(),
      });
    }

    // 使用适配的Prisma客户端
    const prisma = getPrismaClient(c.env);

    // 生成导出URL
    const downloadUrl = `https://example.com/export.${format}`;
    const expiresAt = new Date(Date.now() + 24 * 60 * 60 * 1000).toISOString();

    // 注意：使用模拟客户端时不需要断开连接
    if (typeof prisma.$disconnect === 'function') {
      await prisma.$disconnect();
    }

    return c.json({
      success: true,
      downloadUrl,
      expiresAt,
    });
  } catch (error) {
    console.error('Error exporting statistics:', error);
    return c.json({ success: false, error: 'Failed to export statistics' }, 500);
  }
});

export { app as statistics };
