import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Collapsible, CollapsibleContent, CollapsibleTrigger } from '@/components/ui/collapsible';
import { ScrollArea } from '@/components/ui/scroll-area';
import { 
  Database, 
  GitBranch, 
  AlertTriangle, 
  CheckCircle, 
  XCircle, 
  TrendingUp,
  Download,
  RefreshCw,
  ChevronDown,
  ChevronRight,
  Zap,
  Target,
  BarChart3
} from 'lucide-react';
import DataFlowAnalyzer from '@/utils/dataFlowAnalyzer';
import { IDGenerator } from '@/utils/idGenerator';

interface DataArchitecturePanelProps {
  pageName: string;
}

export const DataArchitecturePanel: React.FC<DataArchitecturePanelProps> = ({ pageName }) => {
  const [isExpanded, setIsExpanded] = useState(false);
  const [optimizationReport, setOptimizationReport] = useState<any>(null);
  const [selectedFlow, setSelectedFlow] = useState<string>('questionnaire');

  // 检查是否应该显示面板
  const shouldShow = import.meta.env.DEV || import.meta.env.VITE_SHOW_DEBUG_PANEL === 'true';

  useEffect(() => {
    if (!shouldShow) return;

    // 生成优化报告
    const report = DataFlowAnalyzer.generateOptimizationReport();
    setOptimizationReport(report);
  }, [shouldShow]);

  if (!shouldShow) return null;

  const handleExportReport = () => {
    if (!optimizationReport) return;
    
    const data = JSON.stringify(optimizationReport, null, 2);
    const blob = new Blob([data], { type: 'application/json' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `data-architecture-report-${new Date().toISOString().split('T')[0]}.json`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
  };

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'high': return 'bg-red-100 text-red-800';
      case 'medium': return 'bg-yellow-100 text-yellow-800';
      case 'low': return 'bg-green-100 text-green-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const getFlowIcon = (nodeType: string) => {
    switch (nodeType) {
      case 'frontend': return <Target className="h-4 w-4 text-blue-500" />;
      case 'api': return <GitBranch className="h-4 w-4 text-green-500" />;
      case 'database': return <Database className="h-4 w-4 text-purple-500" />;
      case 'cache': return <Zap className="h-4 w-4 text-orange-500" />;
      default: return <BarChart3 className="h-4 w-4 text-gray-500" />;
    }
  };

  return (
    <Card className="mb-6 border-2 border-dashed border-green-200 bg-green-50/50">
      <Collapsible open={isExpanded} onOpenChange={setIsExpanded}>
        <CollapsibleTrigger asChild>
          <CardHeader className="cursor-pointer hover:bg-green-100/50 transition-colors">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-2">
                <GitBranch className="h-5 w-5 text-green-600" />
                <CardTitle className="text-lg">数据架构分析 - {pageName}</CardTitle>
                {isExpanded ? <ChevronDown className="h-4 w-4" /> : <ChevronRight className="h-4 w-4" />}
              </div>
              <div className="flex items-center gap-2">
                {optimizationReport && (
                  <>
                    <Badge variant="outline" className="bg-white">
                      {optimizationReport.summary.totalIssues} 问题
                    </Badge>
                    <Badge variant="destructive">
                      {optimizationReport.summary.criticalIssues} 关键
                    </Badge>
                  </>
                )}
              </div>
            </div>
            <CardDescription>
              分析前端数据需求与后端数据库设计的匹配度，提供架构优化建议
            </CardDescription>
          </CardHeader>
        </CollapsibleTrigger>

        <CollapsibleContent>
          <CardContent>
            {optimizationReport && (
              <>
                {/* 概览统计 */}
                <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mb-6">
                  <div className="text-center p-3 bg-white rounded-lg border">
                    <div className="text-2xl font-bold text-red-600">
                      {optimizationReport.summary.totalIssues}
                    </div>
                    <div className="text-sm text-gray-600">总问题数</div>
                  </div>
                  <div className="text-center p-3 bg-white rounded-lg border">
                    <div className="text-2xl font-bold text-orange-600">
                      {optimizationReport.summary.criticalIssues}
                    </div>
                    <div className="text-sm text-gray-600">关键问题</div>
                  </div>
                  <div className="text-center p-3 bg-white rounded-lg border">
                    <div className="text-2xl font-bold text-yellow-600">
                      {optimizationReport.summary.performanceIssues}
                    </div>
                    <div className="text-sm text-gray-600">性能问题</div>
                  </div>
                  <div className="text-center p-3 bg-white rounded-lg border">
                    <div className="text-2xl font-bold text-blue-600">
                      {optimizationReport.summary.dataConsistencyIssues}
                    </div>
                    <div className="text-sm text-gray-600">一致性问题</div>
                  </div>
                </div>

                {/* 操作按钮 */}
                <div className="flex gap-2 mb-6">
                  <Button onClick={handleExportReport} variant="outline" size="sm">
                    <Download className="h-4 w-4 mr-2" />
                    导出报告
                  </Button>
                  <Button 
                    onClick={() => setOptimizationReport(DataFlowAnalyzer.generateOptimizationReport())} 
                    variant="outline" 
                    size="sm"
                  >
                    <RefreshCw className="h-4 w-4 mr-2" />
                    刷新分析
                  </Button>
                </div>

                {/* 主要内容 */}
                <Tabs defaultValue="recommendations" className="w-full">
                  <TabsList>
                    <TabsTrigger value="recommendations">优化建议</TabsTrigger>
                    <TabsTrigger value="dataflow">数据流向</TabsTrigger>
                    <TabsTrigger value="consistency">一致性检查</TabsTrigger>
                    <TabsTrigger value="performance">性能分析</TabsTrigger>
                  </TabsList>

                  <TabsContent value="recommendations" className="mt-4">
                    <div className="space-y-4">
                      <h4 className="font-medium mb-4">架构优化建议</h4>
                      {optimizationReport.recommendations.map((rec: any, index: number) => (
                        <div key={index} className="p-4 bg-white rounded-lg border">
                          <div className="flex items-start justify-between mb-2">
                            <div className="flex items-center gap-2">
                              <Badge className={getPriorityColor(rec.priority)}>
                                {rec.priority.toUpperCase()}
                              </Badge>
                              <h5 className="font-medium">{rec.category}</h5>
                            </div>
                            <div className="text-sm text-gray-500">{rec.effort}</div>
                          </div>
                          <p className="text-sm text-gray-700 mb-2">{rec.description}</p>
                          <p className="text-sm text-green-700">
                            <strong>预期效果:</strong> {rec.impact}
                          </p>
                        </div>
                      ))}
                    </div>
                  </TabsContent>

                  <TabsContent value="dataflow" className="mt-4">
                    <div className="space-y-4">
                      <div className="flex gap-2 mb-4">
                        <Button
                          variant={selectedFlow === 'questionnaire' ? 'default' : 'outline'}
                          size="sm"
                          onClick={() => setSelectedFlow('questionnaire')}
                        >
                          问卷流程
                        </Button>
                        <Button
                          variant={selectedFlow === 'storyWall' ? 'default' : 'outline'}
                          size="sm"
                          onClick={() => setSelectedFlow('storyWall')}
                        >
                          故事墙流程
                        </Button>
                        <Button
                          variant={selectedFlow === 'voices' ? 'default' : 'outline'}
                          size="sm"
                          onClick={() => setSelectedFlow('voices')}
                        >
                          问卷心声流程
                        </Button>
                      </div>

                      <DataFlowVisualization 
                        flow={optimizationReport.dataFlows[selectedFlow]} 
                      />
                    </div>
                  </TabsContent>

                  <TabsContent value="consistency" className="mt-4">
                    <DataConsistencyCheck />
                  </TabsContent>

                  <TabsContent value="performance" className="mt-4">
                    <PerformanceAnalysis 
                      flows={optimizationReport.dataFlows} 
                    />
                  </TabsContent>
                </Tabs>
              </>
            )}
          </CardContent>
        </CollapsibleContent>
      </Collapsible>
    </Card>
  );
};

// 数据流向可视化组件
const DataFlowVisualization: React.FC<{ flow: any }> = ({ flow }) => {
  if (!flow) return <div>暂无数据流信息</div>;

  return (
    <div className="space-y-4">
      <h4 className="font-medium">数据流向节点</h4>
      <div className="grid gap-4">
        {flow.nodes.map((node: any) => (
          <div key={node.id} className="p-3 bg-white rounded-lg border">
            <div className="flex items-center gap-2 mb-2">
              {getFlowIcon(node.type)}
              <h5 className="font-medium">{node.name}</h5>
              <Badge variant="outline">{node.type}</Badge>
            </div>
            <p className="text-sm text-gray-600 mb-2">{node.description}</p>
            {node.performance.avgResponseTime > 0 && (
              <div className="text-xs text-gray-500">
                响应时间: {node.performance.avgResponseTime}ms | 
                错误率: {node.performance.errorRate}% | 
                吞吐量: {node.performance.throughput}/s
              </div>
            )}
          </div>
        ))}
      </div>

      <h4 className="font-medium mt-6">数据流向问题</h4>
      <div className="space-y-2">
        {flow.edges.map((edge: any) => (
          <div key={edge.id} className="p-3 bg-red-50 rounded-lg border border-red-200">
            <h5 className="font-medium text-red-800 mb-2">{edge.dataTransformation}</h5>
            {edge.issues.length > 0 && (
              <div className="mb-2">
                <strong className="text-sm text-red-700">问题:</strong>
                <ul className="list-disc list-inside text-sm text-red-600 ml-2">
                  {edge.issues.map((issue: string, index: number) => (
                    <li key={index}>{issue}</li>
                  ))}
                </ul>
              </div>
            )}
            {edge.suggestions.length > 0 && (
              <div>
                <strong className="text-sm text-green-700">建议:</strong>
                <ul className="list-disc list-inside text-sm text-green-600 ml-2">
                  {edge.suggestions.map((suggestion: string, index: number) => (
                    <li key={index}>{suggestion}</li>
                  ))}
                </ul>
              </div>
            )}
          </div>
        ))}
      </div>
    </div>
  );
};

// 数据一致性检查组件
const DataConsistencyCheck: React.FC = () => {
  const [consistencyResults, setConsistencyResults] = useState<any>(null);

  useEffect(() => {
    // 模拟数据一致性检查
    const frontendData = {
      educationLevel: '本科',
      major: '计算机科学',
      graduationYear: 2023
    };
    
    const databaseData = {
      education_level: 'undergraduate',
      major: 'computer_science',
      graduation_year: 2023
    };

    const results = DataFlowAnalyzer.checkDataConsistency(frontendData, databaseData);
    setConsistencyResults(results);
  }, []);

  if (!consistencyResults) return <div>检查中...</div>;

  return (
    <div className="space-y-4">
      <div className="flex items-center gap-2">
        {consistencyResults.isConsistent ? (
          <CheckCircle className="h-5 w-5 text-green-500" />
        ) : (
          <XCircle className="h-5 w-5 text-red-500" />
        )}
        <h4 className="font-medium">
          数据一致性: {consistencyResults.isConsistent ? '通过' : '存在问题'}
        </h4>
      </div>

      {consistencyResults.mismatches.length > 0 && (
        <div className="space-y-2">
          <h5 className="font-medium text-red-700">不一致项目:</h5>
          {consistencyResults.mismatches.map((mismatch: any, index: number) => (
            <div key={index} className="p-3 bg-yellow-50 rounded-lg border border-yellow-200">
              <div className="font-medium text-yellow-800">{mismatch.field}</div>
              <div className="text-sm text-gray-600">
                前端: {JSON.stringify(mismatch.frontendValue)} | 
                数据库: {JSON.stringify(mismatch.databaseValue)}
              </div>
              <div className="text-sm text-blue-600 mt-1">
                建议: {mismatch.suggestion}
              </div>
            </div>
          ))}
        </div>
      )}
    </div>
  );
};

// 性能分析组件
const PerformanceAnalysis: React.FC<{ flows: any }> = ({ flows }) => {
  const performanceData = Object.entries(flows).map(([key, flow]: [string, any]) => {
    const avgResponseTime = flow.nodes
      .filter((node: any) => node.performance.avgResponseTime > 0)
      .reduce((sum: number, node: any) => sum + node.performance.avgResponseTime, 0) / 
      flow.nodes.filter((node: any) => node.performance.avgResponseTime > 0).length || 0;

    const avgErrorRate = flow.nodes
      .filter((node: any) => node.performance.errorRate > 0)
      .reduce((sum: number, node: any) => sum + node.performance.errorRate, 0) / 
      flow.nodes.filter((node: any) => node.performance.errorRate > 0).length || 0;

    return {
      name: key,
      avgResponseTime: Math.round(avgResponseTime),
      avgErrorRate: Math.round(avgErrorRate * 100) / 100,
      issueCount: flow.edges.reduce((sum: number, edge: any) => sum + edge.issues.length, 0)
    };
  });

  return (
    <div className="space-y-4">
      <h4 className="font-medium">性能分析概览</h4>
      <div className="grid gap-4">
        {performanceData.map((data) => (
          <div key={data.name} className="p-4 bg-white rounded-lg border">
            <h5 className="font-medium mb-2 capitalize">{data.name}</h5>
            <div className="grid grid-cols-3 gap-4 text-sm">
              <div>
                <div className="text-gray-600">平均响应时间</div>
                <div className="font-medium">{data.avgResponseTime}ms</div>
              </div>
              <div>
                <div className="text-gray-600">平均错误率</div>
                <div className="font-medium">{data.avgErrorRate}%</div>
              </div>
              <div>
                <div className="text-gray-600">问题数量</div>
                <div className="font-medium">{data.issueCount}</div>
              </div>
            </div>
          </div>
        ))}
      </div>
    </div>
  );
};

// 辅助函数
const getFlowIcon = (nodeType: string) => {
  switch (nodeType) {
    case 'frontend': return <Target className="h-4 w-4 text-blue-500" />;
    case 'api': return <GitBranch className="h-4 w-4 text-green-500" />;
    case 'database': return <Database className="h-4 w-4 text-purple-500" />;
    case 'cache': return <Zap className="h-4 w-4 text-orange-500" />;
    default: return <BarChart3 className="h-4 w-4 text-gray-500" />;
  }
};

export default DataArchitecturePanel;
