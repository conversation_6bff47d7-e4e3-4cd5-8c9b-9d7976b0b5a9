import { useState, useEffect, useMemo } from 'react';
import { useNavigate, useSearchParams } from 'react-router-dom';
import { Tabs, Ta<PERSON>Content, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import {
  LayoutGrid,
  LayoutList,
  Plus,
  Settings2,
  Tag as TagIcon,
  SlidersHorizontal,
  RefreshCw,
  Filter
} from 'lucide-react';
import { cn } from '@/lib/utils';
import { motion, AnimatePresence } from 'framer-motion';
import { useMediaQuery } from '@/hooks/useMediaQuery';
import { hybridApiService } from '@/services/apiServiceV2';
import { getStories } from '@/lib/api';

// 导入改进的组件
import ImprovedStoryList from '@/components/story/ImprovedStoryList';
import ImprovedTagCloud from '@/components/story/ImprovedTagCloud';
import ImprovedMultiTagSelector from '@/components/story/ImprovedMultiTagSelector';
import StorySubmitDialog from '@/components/story/StorySubmitDialog';
import AdvancedSearch from '@/components/story/AdvancedSearch';
import MobileFilterDrawer from '@/components/story/MobileFilterDrawer';

// Story tags
const STORY_TAGS = [
  { id: 'all', label: '全部' },
  // 求职相关标签
  { id: 'job-hunting', label: '求职故事', category: 'job', color: 'blue' },
  { id: 'interview', label: '面试经验', category: 'job', color: 'blue' },
  { id: 'resume', label: '简历技巧', category: 'job', color: 'blue' },
  { id: 'job-search', label: '找工作', category: 'job', color: 'blue' },
  { id: 'salary', label: '薪资谈判', category: 'job', color: 'blue' },
  { id: 'offer', label: 'Offer选择', category: 'job', color: 'blue' },

  // 学历相关标签
  { id: 'bachelor', label: '本科经验', category: 'education', color: 'green' },
  { id: 'master', label: '硕士经验', category: 'education', color: 'green' },
  { id: 'phd', label: '博士经验', category: 'education', color: 'green' },
  { id: 'overseas-edu', label: '海外学历', category: 'education', color: 'green' },
  { id: 'continuing-edu', label: '继续教育', category: 'education', color: 'green' },
  { id: 'self-taught', label: '自学成才', category: 'education', color: 'green' },

  // 行业相关标签
  { id: 'it-industry', label: 'IT行业', category: 'industry', color: 'purple' },
  { id: 'finance', label: '金融行业', category: 'industry', color: 'purple' },
  { id: 'education-industry', label: '教育行业', category: 'industry', color: 'purple' },
  { id: 'healthcare', label: '医疗行业', category: 'industry', color: 'purple' },
  { id: 'manufacturing', label: '制造业', category: 'industry', color: 'purple' },
  { id: 'service', label: '服务业', category: 'industry', color: 'purple' },

  // 经验相关标签
  { id: 'career-change', label: '转行经历', category: 'experience', color: 'yellow' },
  { id: 'work-life', label: '工作生活', category: 'experience', color: 'yellow' },
  { id: 'advice', label: '建议分享', category: 'experience', color: 'yellow' },
  { id: 'internship', label: '实习经历', category: 'experience', color: 'yellow' },
  { id: 'overseas', label: '海外就业', category: 'experience', color: 'yellow' },
  { id: 'startup', label: '创业经历', category: 'experience', color: 'yellow' },
  { id: 'remote-work', label: '远程工作', category: 'experience', color: 'yellow' },
  { id: 'freelance', label: '自由职业', category: 'experience', color: 'yellow' },

  // 其他标签
  { id: 'success', label: '成功故事', category: 'other', color: 'gray' },
  { id: 'challenge', label: '挑战经历', category: 'other', color: 'gray' },
  { id: 'inspiration', label: '励志故事', category: 'other', color: 'gray' },
];

// Story categories
const STORY_CATEGORIES = [
  { id: 'all', label: '全部分类' },
  { id: 'success', label: '成功故事' },
  { id: 'challenge', label: '挑战经历' },
  { id: 'advice', label: '建议分享' },
  { id: 'experience', label: '经验分享' },
];

// Education levels
const EDUCATION_LEVELS = [
  { id: 'all', label: '全部学历' },
  { id: 'high-school', label: '高中及以下' },
  { id: 'college', label: '专科' },
  { id: 'bachelor', label: '本科' },
  { id: 'master', label: '硕士' },
  { id: 'phd', label: '博士' },
];

// Industries
const INDUSTRIES = [
  { id: 'all', label: '全部行业' },
  { id: 'it', label: '互联网/IT' },
  { id: 'finance', label: '金融' },
  { id: 'education', label: '教育' },
  { id: 'healthcare', label: '医疗' },
  { id: 'manufacturing', label: '制造业' },
  { id: 'service', label: '服务业' },
  { id: 'government', label: '政府/公共服务' },
  { id: 'media', label: '媒体/文化' },
];

export default function StoryWallPage() {
  const navigate = useNavigate();
  const [searchParams, setSearchParams] = useSearchParams();
  const [isSubmitDialogOpen, setIsSubmitDialogOpen] = useState(false);
  const [isFilterDrawerOpen, setIsFilterDrawerOpen] = useState(false);

  // 添加调试信息
  useEffect(() => {
    console.log('StoryWallPage mounted');
    console.log('Environment:', import.meta.env.MODE);
    console.log('API Base URL:', import.meta.env.VITE_API_BASE_URL);
    console.log('DEV mode:', import.meta.env.DEV);
  }, []);

  // 移除了测试功能，保持代码简洁

  // 检测是否为移动设备
  const isMobile = useMediaQuery('(max-width: 768px)');

  // 排序和布局状态
  const [sortBy, setSortBy] = useState<'latest' | 'popular'>(
    searchParams.get('sort') === 'popular' ? 'popular' : 'latest'
  );
  const [layout, setLayout] = useState<'grid' | 'list'>(
    searchParams.get('layout') === 'list' ? 'list' : 'grid'
  );

  // 筛选状态
  const [selectedTag, setSelectedTag] = useState(searchParams.get('tag') || 'all');
  const [selectedTags, setSelectedTags] = useState<string[]>(
    searchParams.get('tags') ? searchParams.get('tags')!.split(',') : []
  );
  const [selectedCategory, setSelectedCategory] = useState(searchParams.get('category') || 'all');
  const [selectedEducationLevel, setSelectedEducationLevel] = useState(searchParams.get('education') || 'all');
  const [selectedIndustry, setSelectedIndustry] = useState(searchParams.get('industry') || 'all');
  const [searchQuery, setSearchQuery] = useState(searchParams.get('search') || '');

  // UI状态
  const [isFilterOpen, setIsFilterOpen] = useState(false);
  const [popularTags, setPopularTags] = useState<Array<{ tag: string; count: number }>>([]);
  const [showAdvancedOptions, setShowAdvancedOptions] = useState(false);
  const [isPageLoaded, setIsPageLoaded] = useState(false);

  // 页面加载动画
  useEffect(() => {
    setIsPageLoaded(true);
  }, []);

  // Update URL when filters change
  useEffect(() => {
    const params = new URLSearchParams();

    // 排序和布局
    if (sortBy === 'popular') {
      params.set('sort', 'popular');
    }

    if (layout === 'list') {
      params.set('layout', 'list');
    }

    // 标签筛选
    if (selectedTag !== 'all') {
      params.set('tag', selectedTag);
    }

    if (selectedTags.length > 0) {
      params.set('tags', selectedTags.join(','));
    }

    // 其他筛选条件
    if (selectedCategory !== 'all') {
      params.set('category', selectedCategory);
    }

    if (selectedEducationLevel !== 'all') {
      params.set('education', selectedEducationLevel);
    }

    if (selectedIndustry !== 'all') {
      params.set('industry', selectedIndustry);
    }

    // 搜索
    if (searchQuery) {
      params.set('search', searchQuery);
    }

    setSearchParams(params);
  }, [
    sortBy,
    layout,
    selectedTag,
    selectedTags,
    selectedCategory,
    selectedEducationLevel,
    selectedIndustry,
    searchQuery,
    setSearchParams
  ]);

  // 处理排序变更
  const handleSortChange = (value: string) => {
    setSortBy(value as 'latest' | 'popular');
  };

  // 处理布局变更
  const handleLayoutChange = (newLayout: 'grid' | 'list') => {
    setLayout(newLayout);
  };

  // 处理单个标签选择
  const handleTagSelect = (tagId: string) => {
    if (tagId === 'all') {
      // 清除所有标签筛选
      setSelectedTag('all');
      setSelectedTags([]);
    } else {
      setSelectedTag(tagId);
    }
  };

  // 处理多标签选择
  const handleTagsChange = (tags: string[]) => {
    setSelectedTags(tags);
    // 如果有多标签选择，清除单标签选择
    if (tags.length > 0) {
      setSelectedTag('all');
    }
  };

  // 处理搜索
  const handleSearch = (e: React.FormEvent) => {
    e.preventDefault();
    // 搜索已通过输入框的onChange更新
  };

  // 处理搜索查询变更
  const handleSearchChange = (query: string) => {
    setSearchQuery(query);
  };

  // 处理清除筛选
  const handleClearFilters = () => {
    setSortBy('latest');
    setSelectedTag('all');
    setSelectedTags([]);
    setSelectedCategory('all');
    setSelectedEducationLevel('all');
    setSelectedIndustry('all');
    setSearchQuery('');
  };

  // 检查是否有活跃的筛选条件
  const isFilterActive =
    selectedTag !== 'all' ||
    selectedTags.length > 0 ||
    selectedCategory !== 'all' ||
    selectedEducationLevel !== 'all' ||
    selectedIndustry !== 'all' ||
    searchQuery !== '';

  // 使用 useMemo 稳定 options 对象，避免不必要的重新渲染
  const storyOptions = useMemo(() => ({
    category: selectedCategory === 'all' ? undefined : selectedCategory,
    educationLevel: selectedEducationLevel === 'all' ? undefined : selectedEducationLevel,
    industry: selectedIndustry === 'all' ? undefined : selectedIndustry,
    search: searchQuery || undefined,
  }), [selectedCategory, selectedEducationLevel, selectedIndustry, searchQuery]);

  return (
    <AnimatePresence mode="wait">
      <motion.div
        key="story-wall-page"
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        exit={{ opacity: 0 }}
        transition={{ duration: 0.5 }}
        className="min-h-screen"
      >
        <div className="container mx-auto py-6 md:py-8 px-4 md:px-6">
          <motion.div
            initial={{ opacity: 0, y: -20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.1, duration: 0.5 }}
            className="mb-6"
          >
            <h1 className="text-2xl md:text-3xl font-bold mb-2">故事墙</h1>
            <p className="text-sm md:text-base text-gray-600">
              在这里，你可以匿名分享你的就业故事、经验和建议，也可以阅读其他人的故事，获取灵感和共鸣。
            </p>
          </motion.div>

          {/* 桌面版高级搜索组件 */}
          {!isMobile && (
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.2, duration: 0.5 }}
              className="mb-6"
            >
              <AdvancedSearch
                searchQuery={searchQuery}
                onSearchChange={handleSearchChange}
                onSearch={handleSearch}
                selectedCategory={selectedCategory}
                onCategoryChange={setSelectedCategory}
                selectedEducationLevel={selectedEducationLevel}
                onEducationLevelChange={setSelectedEducationLevel}
                selectedIndustry={selectedIndustry}
                onIndustryChange={setSelectedIndustry}
                onClearFilters={handleClearFilters}
                isFilterActive={isFilterActive}
                categories={STORY_CATEGORIES}
                educationLevels={EDUCATION_LEVELS}
                industries={INDUSTRIES}
              />
            </motion.div>
          )}

          {/* 改进的多标签选择器 - 仅在桌面版显示 */}
          {!isMobile && (
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.3, duration: 0.5 }}
              className="mb-6"
            >
              <ImprovedMultiTagSelector
                tags={STORY_TAGS.filter(tag => tag.id !== 'all').map(tag => ({
                  id: tag.id,
                  label: tag.label,
                  category: tag.category,
                  color: tag.color
                }))}
                selectedTags={selectedTags}
                onTagsChange={handleTagsChange}
                maxTags={5}
                className="mb-4"
                content={searchQuery}
                showRecommendations={true}
              />
            </motion.div>
          )}

          {/* 移动版简化搜索栏 */}
          {isMobile && (
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.2, duration: 0.5 }}
              className="mb-4"
            >
              <div className="relative">
                <input
                  type="text"
                  placeholder="搜索故事..."
                  value={searchQuery}
                  onChange={(e) => handleSearchChange(e.target.value)}
                  className="w-full px-4 py-2 pr-10 border rounded-full focus:outline-none focus:ring-2 focus:ring-primary/30"
                />
                <Button
                  variant="ghost"
                  size="icon"
                  className="absolute right-1 top-1/2 transform -translate-y-1/2 h-8 w-8"
                  onClick={() => setIsFilterDrawerOpen(true)}
                >
                  <Filter className="h-4 w-4" />
                </Button>
              </div>

              {/* 显示活跃的筛选条件 */}
              {isFilterActive && (
                <div className="flex flex-wrap gap-1 mt-2">
                  {selectedTags.length > 0 && (
                    <Badge variant="outline" className="text-xs bg-blue-50 text-blue-700">
                      {selectedTags.length}个标签
                    </Badge>
                  )}

                  {selectedCategory !== 'all' && (
                    <Badge variant="outline" className="text-xs bg-green-50 text-green-700">
                      {STORY_CATEGORIES.find(c => c.id === selectedCategory)?.label}
                    </Badge>
                  )}

                  {selectedEducationLevel !== 'all' && (
                    <Badge variant="outline" className="text-xs bg-purple-50 text-purple-700">
                      {EDUCATION_LEVELS.find(e => e.id === selectedEducationLevel)?.label}
                    </Badge>
                  )}

                  {selectedIndustry !== 'all' && (
                    <Badge variant="outline" className="text-xs bg-amber-50 text-amber-700">
                      {INDUSTRIES.find(i => i.id === selectedIndustry)?.label}
                    </Badge>
                  )}
                </div>
              )}
            </motion.div>
          )}

          {/* 控制栏 */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.4, duration: 0.5 }}
            className={cn(
              "flex justify-between items-center gap-4 mb-6 bg-gray-50 p-3 md:p-4 rounded-lg",
              isMobile ? "flex-row" : "flex-col md:flex-row"
            )}
          >
            <div className="flex items-center gap-2 md:gap-4">
              <Tabs value={sortBy} onValueChange={handleSortChange}>
                <TabsList className={isMobile ? "h-8" : ""}>
                  <TabsTrigger value="latest" className={isMobile ? "text-xs px-2 h-7" : ""}>最新</TabsTrigger>
                  <TabsTrigger value="popular" className={isMobile ? "text-xs px-2 h-7" : ""}>最热</TabsTrigger>
                </TabsList>
              </Tabs>

              {!isMobile && (
                <div className="flex items-center border rounded-md overflow-hidden">
                  <Button
                    variant={layout === 'grid' ? "default" : "ghost"}
                    size="sm"
                    className="rounded-none h-9 px-3"
                    onClick={() => handleLayoutChange('grid')}
                  >
                    <LayoutGrid className="h-4 w-4" />
                  </Button>
                  <Button
                    variant={layout === 'list' ? "default" : "ghost"}
                    size="sm"
                    className="rounded-none h-9 px-3"
                    onClick={() => handleLayoutChange('list')}
                  >
                    <LayoutList className="h-4 w-4" />
                  </Button>
                </div>
              )}
            </div>

            <div className="flex items-center gap-2">
              {!isMobile && (
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => setShowAdvancedOptions(!showAdvancedOptions)}
                  className="flex items-center gap-1"
                >
                  <Settings2 className="h-4 w-4" />
                  高级选项
                </Button>
              )}

              <Button
                onClick={() => setIsSubmitDialogOpen(true)}
                className={cn(
                  "flex items-center gap-1",
                  isMobile && "text-xs px-3 h-8"
                )}
                size={isMobile ? "sm" : "default"}
              >
                <Plus className={isMobile ? "h-3 w-3" : "h-4 w-4"} />
                {isMobile ? "分享" : "分享故事"}
              </Button>


            </div>
          </motion.div>

          {/* 主要内容区 */}
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ delay: 0.5, duration: 0.5 }}
            className={cn(
              "grid grid-cols-1 md:grid-cols-3 gap-4 md:gap-8",
              isMobile && "mt-2"
            )}
          >
            <div className={cn(
              "md:col-span-2",
              layout === 'list' && "md:col-span-3"
            )}>
              {console.log('StoryWallPage: Rendering ImprovedStoryList with props:', {
                sortBy,
                selectedTag,
                selectedTags,
                selectedCategory,
                selectedEducationLevel,
                selectedIndustry,
                searchQuery,
                layout,
                isMobile
              })}
              <ImprovedStoryList
                sortBy={sortBy}
                tag={selectedTag === 'all' ? undefined : selectedTag}
                tags={selectedTags}
                options={storyOptions}
                onPopularTagsLoaded={setPopularTags}
                layout={layout}
                searchQuery={searchQuery}
                infiniteScroll={true}
                compact={isMobile}
              />
            </div>

            {layout === 'grid' && !isMobile && (
              <div className="md:col-span-1 hidden md:block">
                <div className="sticky top-4 space-y-6">
                  {/* 改进的热门标签云 */}
                  <motion.div
                    initial={{ opacity: 0, x: 20 }}
                    animate={{ opacity: 1, x: 0 }}
                    transition={{ delay: 0.6, duration: 0.5 }}
                    className="bg-gray-50 rounded-lg p-4"
                  >
                    <h3 className="text-lg font-medium mb-3">热门标签</h3>
                    <ImprovedTagCloud
                      tags={popularTags.map(tag => {
                        const tagInfo = STORY_TAGS.find(t => t.id === tag.tag);
                        return {
                          ...tag,
                          category: tagInfo?.category,
                          color: tagInfo?.color
                        };
                      })}
                      selectedTags={[selectedTag, ...selectedTags].filter(t => t !== 'all')}
                      onTagSelect={handleTagSelect}
                      maxTags={30}
                      showCounts={true}
                      showCategories={true}
                    />
                  </motion.div>

                  {/* 分享故事卡片 */}
                  <motion.div
                    initial={{ opacity: 0, x: 20 }}
                    animate={{ opacity: 1, x: 0 }}
                    transition={{ delay: 0.7, duration: 0.5 }}
                    className="bg-gray-50 rounded-lg p-4"
                  >
                    <h3 className="text-lg font-medium mb-3">分享你的故事</h3>
                    <p className="text-sm text-gray-600 mb-4">
                      你的经历和建议可能会帮助到其他人。分享你的故事，让更多人受益。
                    </p>
                    <Button
                      onClick={() => setIsSubmitDialogOpen(true)}
                      className="w-full"
                    >
                      立即分享
                    </Button>
                  </motion.div>
                </div>
              </div>
            )}
          </motion.div>

          {/* 故事提交对话框 */}
          <StorySubmitDialog
            open={isSubmitDialogOpen}
            onOpenChange={setIsSubmitDialogOpen}
          />

          {/* 移动端筛选抽屉 */}
          <MobileFilterDrawer
            open={isFilterDrawerOpen}
            onOpenChange={setIsFilterDrawerOpen}
            tags={STORY_TAGS.filter(tag => tag.id !== 'all').map(tag => ({
              id: tag.id,
              label: tag.label,
              category: tag.category,
              color: tag.color
            }))}
            selectedTags={selectedTags}
            onTagsChange={setSelectedTags}
            searchQuery={searchQuery}
            onSearchChange={setSearchQuery}
            categories={STORY_CATEGORIES}
            selectedCategory={selectedCategory}
            onCategoryChange={setSelectedCategory}
            educationLevels={EDUCATION_LEVELS}
            selectedEducationLevel={selectedEducationLevel}
            onEducationLevelChange={setSelectedEducationLevel}
            industries={INDUSTRIES}
            selectedIndustry={selectedIndustry}
            onIndustryChange={setSelectedIndustry}
            onClearFilters={handleClearFilters}
            onApplyFilters={() => {
              // 应用筛选条件
              handleSearch(new Event('submit') as React.FormEvent);
            }}
          />
        </div>
      </motion.div>
    </AnimatePresence>
  );
}
