/**
 * 安全模块类型定义
 */

/**
 * 安全配置
 */
export interface SecurityConfig {
  // 全局设置
  enabled: boolean;           // 是否启用安全模块
  protectionLevel: 0 | 1 | 2 | 3 | 4;  // 防护等级

  // 验证码设置
  captcha: CaptchaConfig;

  // 行为分析设置
  behaviorAnalysis: BehaviorAnalysisConfig;

  // 速率限制设置
  rateLimit: RateLimitConfig;

  // 蜜罐设置
  honeypot: HoneypotConfig;

  // 指纹识别设置
  fingerprinting: FingerprintingConfig;

  // 日志和监控设置
  logging: LoggingConfig;

  // 错误分析设置
  errorAnalysis: ErrorAnalysisConfig;

  // 告警设置
  alerting: AlertingConfig;

  // 自动修复设置
  autoFix: AutoFixConfig;
}

/**
 * 验证码配置
 */
export interface CaptchaConfig {
  enabled: boolean;         // 是否启用验证码
  type: 'none' | 'turnstile' | 'custom';  // 验证码类型
  siteKey?: string;         // 验证码站点密钥
  secretKey?: string;       // 验证码密钥（仅后端使用）
  triggerThreshold: number; // 触发验证码的阈值
}

/**
 * 行为分析配置
 */
export interface BehaviorAnalysisConfig {
  enabled: boolean;         // 是否启用行为分析
  minCompletionTime: number;  // 最短完成时间（毫秒）
  trackMouseMovements: boolean;  // 是否跟踪鼠标移动
  trackKeyboardEvents: boolean;  // 是否跟踪键盘事件
  suspiciousScoreThreshold: number;  // 可疑分数阈值
}

/**
 * 速率限制配置
 */
export interface RateLimitConfig {
  enabled: boolean;         // 是否启用速率限制
  ipLimit: number;          // 每IP限制（次/小时）
  fingerprintLimit: number; // 每指纹限制（次/小时）
  cooldownPeriod: number;   // 冷却期（秒）
}

/**
 * 蜜罐配置
 */
export interface HoneypotConfig {
  enabled: boolean;         // 是否启用蜜罐
  fieldCount: number;       // 蜜罐字段数量
  silentRejection: boolean; // 是否静默拒绝
}

/**
 * 指纹识别配置
 */
export interface FingerprintingConfig {
  enabled: boolean;         // 是否启用指纹识别
  storageTime: number;      // 存储时间（天）
}

/**
 * 日志和监控配置
 */
export interface LoggingConfig {
  enabled: boolean;         // 是否启用日志
  logLevel: 'error' | 'warn' | 'info' | 'debug';  // 日志级别
  storeSubmissionData: boolean;  // 是否存储提交数据
}

/**
 * 验证结果
 */
export interface ValidationResult {
  valid: boolean;           // 是否有效
  error?: string;           // 错误信息
  status?: number;          // HTTP 状态码
  suspiciousScore?: number; // 可疑分数
  reasons?: string[];       // 可疑原因
  silentRejection?: boolean; // 是否静默拒绝
}

/**
 * 安全元数据
 */
export interface SecurityMetadata {
  timestamp: number;        // 时间戳
  protectionLevel: number;  // 防护等级
  captcha?: any;            // 验证码数据
  behavior?: any;           // 行为数据
  fingerprint?: string;     // 指纹数据
}

/**
 * 安全事件
 */
export interface SecurityEvent {
  type: string;             // 事件类型
  timestamp?: number;       // 时间戳
  level?: 'error' | 'warn' | 'info' | 'debug';  // 事件级别
  message?: string;         // 事件消息
  data?: any;               // 事件数据
}

/**
 * 分析结果
 */
export interface AnalysisResult {
  suspicious: boolean;      // 是否可疑
  suspiciousScore: number;  // 可疑分数
  reasons?: string[];       // 可疑原因
  data?: any;               // 分析数据
}

/**
 * 错误分析配置
 */
export interface ErrorAnalysisConfig {
  enabled: boolean;         // 是否启用错误分析
  clusteringEnabled: boolean;  // 是否启用错误聚类
  anomalyDetectionEnabled: boolean;  // 是否启用异常检测
  patternRecognitionEnabled: boolean;  // 是否启用模式识别
  similarityThreshold: number;  // 相似度阈值
  anomalyThreshold: number;  // 异常阈值
  minClusterSize: number;  // 最小聚类大小
  maxClusters: number;  // 最大聚类数量
  maxSamplesPerCluster: number;  // 每个聚类的最大样本数
  analysisInterval: number;  // 分析间隔（毫秒）
  baselineWindowDays: number;  // 基线窗口（天）
}

/**
 * 告警配置
 */
export interface AlertingConfig {
  enabled: boolean;         // 是否启用告警
  channels: AlertChannelConfig[];  // 告警渠道
  throttleInterval: number;  // 告警节流间隔（毫秒）
  minSeverity: 'low' | 'medium' | 'high' | 'critical';  // 最小告警严重性
  groupSimilarAlerts: boolean;  // 是否分组相似告警
  includeDetails: boolean;  // 是否包含详细信息
  maxAlertsPerHour: number;  // 每小时最大告警数量
}

/**
 * 告警渠道配置
 */
export interface AlertChannelConfig {
  type: 'email' | 'webhook' | 'console' | 'custom';  // 告警渠道类型
  enabled: boolean;         // 是否启用
  name: string;             // 渠道名称
  recipients?: string[];    // 接收者（邮件）
  webhookUrl?: string;      // Webhook URL
  headers?: Record<string, string>;  // 自定义头部
  templateId?: string;      // 模板 ID
  throttleInterval?: number;  // 渠道特定节流间隔
  minSeverity?: 'low' | 'medium' | 'high' | 'critical';  // 渠道特定最小严重性
}

/**
 * 自动修复配置
 */
export interface AutoFixConfig {
  enabled: boolean;         // 是否启用自动修复
  maxAttemptsPerError: number;  // 每个错误的最大尝试次数
  cooldownPeriod: number;   // 冷却期（毫秒）
  requireApproval: boolean;  // 是否需要批准
  notifyOnFix: boolean;     // 修复时是否通知
  fixStrategies: FixStrategyConfig[];  // 修复策略
}

/**
 * 修复策略配置
 */
export interface FixStrategyConfig {
  id: string;               // 策略 ID
  name: string;             // 策略名称
  description: string;      // 策略描述
  enabled: boolean;         // 是否启用
  targetErrorCodes: string[];  // 目标错误代码
  action: string;           // 修复动作
  parameters?: Record<string, any>;  // 参数
  maxAttempts?: number;     // 最大尝试次数
  successRate?: number;     // 成功率
}

/**
 * 错误分析配置
 */
export interface ErrorAnalysisConfig {
  enabled: boolean;         // 是否启用错误分析
  clusteringEnabled: boolean;  // 是否启用错误聚类
  anomalyDetectionEnabled: boolean;  // 是否启用异常检测
  patternRecognitionEnabled: boolean;  // 是否启用模式识别
  similarityThreshold: number;  // 相似度阈值
  anomalyThreshold: number;  // 异常阈值
  minClusterSize: number;  // 最小聚类大小
  maxClusters: number;  // 最大聚类数量
  maxSamplesPerCluster: number;  // 每个聚类的最大样本数
  analysisInterval: number;  // 分析间隔（毫秒）
  baselineWindowDays: number;  // 基线窗口（天）
}

/**
 * 告警配置
 */
export interface AlertingConfig {
  enabled: boolean;         // 是否启用告警
  channels: AlertChannelConfig[];  // 告警渠道
  throttleInterval: number;  // 告警节流间隔（毫秒）
  minSeverity: 'low' | 'medium' | 'high' | 'critical';  // 最小告警严重性
  groupSimilarAlerts: boolean;  // 是否分组相似告警
  includeDetails: boolean;  // 是否包含详细信息
  maxAlertsPerHour: number;  // 每小时最大告警数量
}

/**
 * 告警渠道配置
 */
export interface AlertChannelConfig {
  type: 'email' | 'webhook' | 'console' | 'custom';  // 告警渠道类型
  enabled: boolean;         // 是否启用
  name: string;             // 渠道名称
  recipients?: string[];    // 接收者（邮件）
  webhookUrl?: string;      // Webhook URL
  headers?: Record<string, string>;  // 自定义头部
  templateId?: string;      // 模板 ID
  throttleInterval?: number;  // 渠道特定节流间隔
  minSeverity?: 'low' | 'medium' | 'high' | 'critical';  // 渠道特定最小严重性
}

/**
 * 自动修复配置
 */
export interface AutoFixConfig {
  enabled: boolean;         // 是否启用自动修复
  maxAttemptsPerError: number;  // 每个错误的最大尝试次数
  cooldownPeriod: number;   // 冷却期（毫秒）
  requireApproval: boolean;  // 是否需要批准
  notifyOnFix: boolean;     // 修复时是否通知
  fixStrategies: FixStrategyConfig[];  // 修复策略
}

/**
 * 修复策略配置
 */
export interface FixStrategyConfig {
  id: string;               // 策略 ID
  name: string;             // 策略名称
  description: string;      // 策略描述
  enabled: boolean;         // 是否启用
  targetErrorCodes: string[];  // 目标错误代码
  action: string;           // 修复动作
  parameters?: Record<string, any>;  // 参数
  maxAttempts?: number;     // 最大尝试次数
  successRate?: number;     // 成功率
}
