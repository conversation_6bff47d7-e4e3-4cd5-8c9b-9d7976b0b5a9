import { useState, useEffect } from 'react';
import { useToast } from '@/components/ui/use-toast';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle
} from '@/components/ui/dialog';
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage
} from '@/components/ui/form';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue
} from '@/components/ui/select';
import { Checkbox } from '@/components/ui/checkbox';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Loader2 } from 'lucide-react';
import { z } from 'zod';
import { zodResolver } from '@hookform/resolvers/zod';
import { useForm } from 'react-hook-form';

// 定义问卷回复数据类型
interface QuestionnaireResponse {
  id: number;
  sequenceNumber: string;
  isAnonymous: boolean;
  educationLevel?: string;
  major?: string;
  graduationYear?: number;
  region?: string;
  employmentStatus?: string;
  industry?: string;
  position?: string;
  salary?: string;
  jobSatisfaction?: string;
  unemploymentDuration?: string;
  careerChangeIntention?: boolean;
  challenges?: string;
  suggestions?: string;
  createdAt: string;
  updatedAt?: string;
  ipAddress?: string;
  [key: string]: any;
}

// 定义表单验证模式
const formSchema = z.object({
  id: z.number(),
  sequenceNumber: z.string(),
  isAnonymous: z.boolean(),
  educationLevel: z.string().optional(),
  major: z.string().optional(),
  graduationYear: z.number().optional(),
  region: z.string().optional(),
  employmentStatus: z.string().optional(),
  industry: z.string().optional(),
  position: z.string().optional(),
  salary: z.string().optional(),
  jobSatisfaction: z.string().optional(),
  unemploymentDuration: z.string().optional(),
  careerChangeIntention: z.boolean().optional(),
  challenges: z.string().optional(),
  suggestions: z.string().optional(),
});

// 组件属性类型
interface EditResponseDialogProps {
  response: QuestionnaireResponse | null;
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onSave: (updatedResponse: QuestionnaireResponse) => void;
}

export default function EditResponseDialog({
  response,
  open,
  onOpenChange,
  onSave,
}: EditResponseDialogProps) {
  const { toast } = useToast();
  const [isSaving, setIsSaving] = useState(false);

  // 初始化表单
  const form = useForm<z.infer<typeof formSchema>>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      id: response?.id || 0,
      sequenceNumber: response?.sequenceNumber || '',
      isAnonymous: response?.isAnonymous || false,
      educationLevel: response?.educationLevel || '',
      major: response?.major || '',
      graduationYear: response?.graduationYear || undefined,
      region: response?.region || '',
      employmentStatus: response?.employmentStatus || '',
      industry: response?.industry || '',
      position: response?.position || '',
      salary: response?.salary || '',
      jobSatisfaction: response?.jobSatisfaction || '',
      unemploymentDuration: response?.unemploymentDuration || '',
      careerChangeIntention: response?.careerChangeIntention || false,
      challenges: response?.challenges || '',
      suggestions: response?.suggestions || '',
    },
  });

  // 当response变化时更新表单值
  useEffect(() => {
    if (response) {
      form.reset({
        id: response.id,
        sequenceNumber: response.sequenceNumber,
        isAnonymous: response.isAnonymous,
        educationLevel: response.educationLevel || '',
        major: response.major || '',
        graduationYear: response.graduationYear,
        region: response.region || '',
        employmentStatus: response.employmentStatus || '',
        industry: response.industry || '',
        position: response.position || '',
        salary: response.salary || '',
        jobSatisfaction: response.jobSatisfaction || '',
        unemploymentDuration: response.unemploymentDuration || '',
        careerChangeIntention: response.careerChangeIntention || false,
        challenges: response.challenges || '',
        suggestions: response.suggestions || '',
      });
    }
  }, [response, form]);

  // 处理表单提交
  const onSubmit = async (values: z.infer<typeof formSchema>) => {
    if (!response) return;

    try {
      setIsSaving(true);

      // 通知父组件保存更新后的数据
      onSave(values as QuestionnaireResponse);

      // 关闭对话框
      onOpenChange(false);
    } catch (error) {
      console.error('保存数据错误:', error);
      toast({
        variant: 'destructive',
        title: '保存失败',
        description: '服务器错误，请稍后再试',
      });
    } finally {
      setIsSaving(false);
    }
  };

  // 学历选项
  const educationLevels = [
    { value: '高中/中专', label: '高中/中专' },
    { value: '大专', label: '大专' },
    { value: '本科', label: '本科' },
    { value: '硕士', label: '硕士' },
    { value: '博士', label: '博士' },
  ];

  // 就业状态选项
  const employmentStatuses = [
    { value: '已就业', label: '已就业' },
    { value: '待业中', label: '待业中' },
    { value: '继续深造', label: '继续深造' },
    { value: '其他', label: '其他' },
  ];

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-3xl max-h-[90vh]">
        <DialogHeader>
          <DialogTitle>编辑问卷回复</DialogTitle>
          <DialogDescription>
            编辑问卷回复数据，修改后点击保存按钮提交更改
          </DialogDescription>
        </DialogHeader>

        <ScrollArea className="max-h-[calc(90vh-180px)] pr-4">
          <Form {...form}>
            <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                {/* 基本信息 */}
                <FormField
                  control={form.control}
                  name="sequenceNumber"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>编号</FormLabel>
                      <FormControl>
                        <Input {...field} disabled />
                      </FormControl>
                      <FormDescription>系统自动生成的编号，不可修改</FormDescription>
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="isAnonymous"
                  render={({ field }) => (
                    <FormItem className="flex flex-row items-start space-x-3 space-y-0 rounded-md border p-4">
                      <FormControl>
                        <Checkbox
                          checked={field.value}
                          onCheckedChange={field.onChange}
                        />
                      </FormControl>
                      <div className="space-y-1 leading-none">
                        <FormLabel>匿名提交</FormLabel>
                        <FormDescription>
                          是否为匿名提交的问卷
                        </FormDescription>
                      </div>
                    </FormItem>
                  )}
                />
              </div>

              {/* 教育信息 */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <FormField
                  control={form.control}
                  name="educationLevel"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>学历层次</FormLabel>
                      <Select
                        value={field.value}
                        onValueChange={field.onChange}
                      >
                        <FormControl>
                          <SelectTrigger>
                            <SelectValue placeholder="选择学历层次" />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          <SelectItem value="">未选择</SelectItem>
                          {educationLevels.map((level) => (
                            <SelectItem key={level.value} value={level.value}>
                              {level.label}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="major"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>专业</FormLabel>
                      <FormControl>
                        <Input {...field} placeholder="专业名称" />
                      </FormControl>
                    </FormItem>
                  )}
                />
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <FormField
                  control={form.control}
                  name="graduationYear"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>毕业年份</FormLabel>
                      <FormControl>
                        <Input
                          type="number"
                          {...field}
                          value={field.value || ''}
                          onChange={(e) => field.onChange(e.target.value ? parseInt(e.target.value) : undefined)}
                          placeholder="毕业年份"
                        />
                      </FormControl>
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="region"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>地区</FormLabel>
                      <FormControl>
                        <Input {...field} placeholder="所在地区" />
                      </FormControl>
                    </FormItem>
                  )}
                />
              </div>

              {/* 就业信息 */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <FormField
                  control={form.control}
                  name="employmentStatus"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>就业状态</FormLabel>
                      <Select
                        value={field.value}
                        onValueChange={field.onChange}
                      >
                        <FormControl>
                          <SelectTrigger>
                            <SelectValue placeholder="选择就业状态" />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          <SelectItem value="">未选择</SelectItem>
                          {employmentStatuses.map((status) => (
                            <SelectItem key={status.value} value={status.value}>
                              {status.label}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="industry"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>行业</FormLabel>
                      <FormControl>
                        <Input {...field} placeholder="所在行业" />
                      </FormControl>
                    </FormItem>
                  )}
                />
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <FormField
                  control={form.control}
                  name="position"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>职位</FormLabel>
                      <FormControl>
                        <Input {...field} placeholder="当前职位" />
                      </FormControl>
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="salary"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>薪资</FormLabel>
                      <FormControl>
                        <Input {...field} placeholder="薪资范围" />
                      </FormControl>
                    </FormItem>
                  )}
                />
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <FormField
                  control={form.control}
                  name="jobSatisfaction"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>工作满意度</FormLabel>
                      <FormControl>
                        <Input {...field} placeholder="工作满意度" />
                      </FormControl>
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="unemploymentDuration"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>失业时长</FormLabel>
                      <FormControl>
                        <Input {...field} placeholder="失业时长" />
                      </FormControl>
                    </FormItem>
                  )}
                />
              </div>

              <FormField
                control={form.control}
                name="careerChangeIntention"
                render={({ field }) => (
                  <FormItem className="flex flex-row items-start space-x-3 space-y-0 rounded-md border p-4">
                    <FormControl>
                      <Checkbox
                        checked={field.value}
                        onCheckedChange={field.onChange}
                      />
                    </FormControl>
                    <div className="space-y-1 leading-none">
                      <FormLabel>转行意向</FormLabel>
                      <FormDescription>
                        是否有转行意向
                      </FormDescription>
                    </div>
                  </FormItem>
                )}
              />

              {/* 其他信息 */}
              <FormField
                control={form.control}
                name="challenges"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>就业挑战</FormLabel>
                    <FormControl>
                      <Textarea
                        {...field}
                        placeholder="就业过程中遇到的挑战"
                        className="min-h-[100px]"
                      />
                    </FormControl>
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="suggestions"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>建议</FormLabel>
                    <FormControl>
                      <Textarea
                        {...field}
                        placeholder="对学校或就业市场的建议"
                        className="min-h-[100px]"
                      />
                    </FormControl>
                  </FormItem>
                )}
              />

              <div className="flex justify-end space-x-4">
                <Button
                  type="button"
                  variant="outline"
                  onClick={() => onOpenChange(false)}
                >
                  取消
                </Button>
                <Button type="submit" disabled={isSaving}>
                  {isSaving && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
                  保存
                </Button>
              </div>
            </form>
          </Form>
        </ScrollArea>
      </DialogContent>
    </Dialog>
  );
}
