/**
 * 自动审核控制器
 * 
 * 处理自动审核相关的API请求
 */

import { Context } from 'hono';
import { Env } from '../../types';
import { zValidator } from '@hono/zod-validator';
import { z } from 'zod';
import { AutoModerationService, AutoModerationConfig } from '../../services/autoModeration/autoModerationService';
import { ContentType } from '../../constants';

// 自动审核配置验证模式
const autoModerationConfigSchema = z.object({
  enabled: z.boolean().optional(),
  confidenceThreshold: z.number().min(0).max(1).optional(),
  autoApproveThreshold: z.number().min(0).max(1).optional(),
  autoRejectThreshold: z.number().min(0).max(1).optional(),
  severityThresholds: z.object({
    low: z.number().min(0).max(1).optional(),
    medium: z.number().min(0).max(1).optional(),
    high: z.number().min(0).max(1).optional()
  }).optional(),
  notifyAdmin: z.boolean().optional(),
  notifyUser: z.boolean().optional(),
  logResults: z.boolean().optional()
});

// 内容审核请求验证模式
const contentModerationRequestSchema = z.object({
  content: z.any(),
  contentType: z.enum([
    ContentType.STORY, 
    ContentType.QUESTIONNAIRE, 
    ContentType.COMMENT, 
    ContentType.PROFILE, 
    ContentType.FEEDBACK
  ])
});

/**
 * 获取自动审核配置
 */
export const getAutoModerationConfig = async (c: Context<{ Bindings: Env }>) => {
  try {
    // 获取自动审核服务实例
    const autoModerationService = AutoModerationService.getInstance();
    
    // 确保服务已初始化
    if (!autoModerationService.isInitialized()) {
      await autoModerationService.initialize();
    }
    
    // 获取配置
    const config = autoModerationService.getConfig();
    
    // 返回配置
    return c.json({
      success: true,
      config
    });
  } catch (error) {
    console.error('获取自动审核配置失败:', error);
    
    return c.json({
      success: false,
      error: error instanceof Error ? error.message : '获取自动审核配置失败'
    }, 500);
  }
};

/**
 * 更新自动审核配置
 */
export const updateAutoModerationConfig = [
  zValidator('json', autoModerationConfigSchema),
  async (c: Context<{ Bindings: Env }>) => {
    try {
      const config = c.req.valid('json');
      
      // 获取自动审核服务实例
      const autoModerationService = AutoModerationService.getInstance();
      
      // 确保服务已初始化
      if (!autoModerationService.isInitialized()) {
        await autoModerationService.initialize();
      }
      
      // 更新配置
      autoModerationService.updateConfig(config);
      
      // 获取更新后的配置
      const updatedConfig = autoModerationService.getConfig();
      
      // 返回更新后的配置
      return c.json({
        success: true,
        config: updatedConfig
      });
    } catch (error) {
      console.error('更新自动审核配置失败:', error);
      
      return c.json({
        success: false,
        error: error instanceof Error ? error.message : '更新自动审核配置失败'
      }, 500);
    }
  }
];

/**
 * 测试自动审核
 */
export const testAutoModeration = [
  zValidator('json', contentModerationRequestSchema),
  async (c: Context<{ Bindings: Env }>) => {
    try {
      const { content, contentType } = c.req.valid('json');
      
      // 获取自动审核服务实例
      const autoModerationService = AutoModerationService.getInstance();
      
      // 确保服务已初始化
      if (!autoModerationService.isInitialized()) {
        await autoModerationService.initialize();
      }
      
      // 执行自动审核
      const result = await autoModerationService.moderateContent(content, contentType, c.env);
      
      // 返回审核结果
      return c.json({
        success: true,
        result
      });
    } catch (error) {
      console.error('测试自动审核失败:', error);
      
      return c.json({
        success: false,
        error: error instanceof Error ? error.message : '测试自动审核失败'
      }, 500);
    }
  }
];

/**
 * 获取自动审核统计
 */
export const getAutoModerationStats = async (c: Context<{ Bindings: Env }>) => {
  try {
    // 获取时间范围参数
    const { startDate, endDate } = c.req.query();
    
    // 构建查询条件
    const whereClause: any = {
      reviewerId: 'auto-moderation'
    };
    
    if (startDate) {
      whereClause.createdAt = {
        ...whereClause.createdAt,
        gte: new Date(startDate)
      };
    }
    
    if (endDate) {
      whereClause.createdAt = {
        ...whereClause.createdAt,
        lte: new Date(endDate)
      };
    }
    
    // 获取Prisma客户端
    const prisma = new PrismaClient();
    
    // 获取总审核数
    const totalCount = await prisma.moderationHistory.count({
      where: whereClause
    });
    
    // 获取自动通过数
    const approvedCount = await prisma.moderationHistory.count({
      where: {
        ...whereClause,
        suggestedAction: 'approve'
      }
    });
    
    // 获取自动拒绝数
    const rejectedCount = await prisma.moderationHistory.count({
      where: {
        ...whereClause,
        suggestedAction: 'reject'
      }
    });
    
    // 获取人工审核数
    const reviewCount = await prisma.moderationHistory.count({
      where: {
        ...whereClause,
        suggestedAction: 'review'
      }
    });
    
    // 获取按内容类型分组的数量
    const typeStats = await prisma.$queryRaw`
      SELECT contentType, COUNT(*) as count
      FROM ModerationHistory
      WHERE reviewerId = 'auto-moderation'
      ${startDate ? `AND createdAt >= ${new Date(startDate)}` : ''}
      ${endDate ? `AND createdAt <= ${new Date(endDate)}` : ''}
      GROUP BY contentType
    `;
    
    // 获取按严重程度分组的数量
    const severityStats = await prisma.$queryRaw`
      SELECT severity, COUNT(*) as count
      FROM ModerationHistory
      WHERE reviewerId = 'auto-moderation'
      AND severity IS NOT NULL
      ${startDate ? `AND createdAt >= ${new Date(startDate)}` : ''}
      ${endDate ? `AND createdAt <= ${new Date(endDate)}` : ''}
      GROUP BY severity
    `;
    
    // 获取每日审核数量
    const dailyStats = await prisma.$queryRaw`
      SELECT DATE(createdAt) as date, COUNT(*) as count
      FROM ModerationHistory
      WHERE reviewerId = 'auto-moderation'
      ${startDate ? `AND createdAt >= ${new Date(startDate)}` : ''}
      ${endDate ? `AND createdAt <= ${new Date(endDate)}` : ''}
      GROUP BY DATE(createdAt)
      ORDER BY date ASC
    `;
    
    // 关闭Prisma客户端
    await prisma.$disconnect();
    
    // 返回统计数据
    return c.json({
      success: true,
      stats: {
        total: totalCount,
        approved: approvedCount,
        rejected: rejectedCount,
        review: reviewCount,
        byType: typeStats,
        bySeverity: severityStats,
        daily: dailyStats
      }
    });
  } catch (error) {
    console.error('获取自动审核统计失败:', error);
    
    return c.json({
      success: false,
      error: error instanceof Error ? error.message : '获取自动审核统计失败'
    }, 500);
  }
};

/**
 * 重置自动审核配置
 */
export const resetAutoModerationConfig = async (c: Context<{ Bindings: Env }>) => {
  try {
    // 获取自动审核服务实例
    const autoModerationService = AutoModerationService.getInstance();
    
    // 重新初始化服务（使用默认配置）
    await autoModerationService.initialize();
    
    // 获取重置后的配置
    const config = autoModerationService.getConfig();
    
    // 返回重置后的配置
    return c.json({
      success: true,
      config
    });
  } catch (error) {
    console.error('重置自动审核配置失败:', error);
    
    return c.json({
      success: false,
      error: error instanceof Error ? error.message : '重置自动审核配置失败'
    }, 500);
  }
};
