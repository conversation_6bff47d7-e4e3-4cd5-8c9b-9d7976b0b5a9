import React, { useState, useEffect } from 'react';
import { <PERSON>, CardContent, Card<PERSON>eader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from '@/components/ui/tabs';
import { AlertCircle, CheckCircle, Clock, Database, Globe, Server, Monitor, Wifi, HardDrive, Copy, Download } from 'lucide-react';

interface ApiEndpoint {
  name: string;
  path: string;
  method: string;
  description: string;
  database: string;
  tables: string[];
  status: 'healthy' | 'error' | 'loading';
  responseTime?: number;
  lastChecked?: string;
  error?: string;
}

interface DatabaseInfo {
  name: string;
  type: string;
  status: 'connected' | 'error' | 'loading';
  tables: { name: string; count: number; status: string }[];
  responseTime?: number;
  error?: string;
}

interface FrontendPage {
  name: string;
  path: string;
  apis: string[];
  status: 'healthy' | 'error' | 'loading';
  description: string;
}

const DataMonitorPage: React.FC = () => {
  const [isLoading, setIsLoading] = useState(false);
  const [lastUpdate, setLastUpdate] = useState<string>('');
  const [copySuccess, setCopySuccess] = useState(false);
  const [networkDiagnostics, setNetworkDiagnostics] = useState<any>(null);
  const [databaseTests, setDatabaseTests] = useState<any[]>([]);

  // 数据库查询测试配置（使用现有API端点）
  const databaseQueryTests = [
    {
      id: 'questionnaire_stats',
      name: '问卷统计数据',
      description: '检查问卷统计API和数据库连接',
      table: 'questionnaire_responses_v2',
      query: '通过API获取问卷统计数据',
      endpoint: '/api/questionnaire/stats',
      expectedType: 'object',
      testFunction: (data: any) => {
        return data.success && data.data && typeof data.data.total === 'number';
      }
    },
    {
      id: 'questionnaire_voices',
      name: '问卷声音数据',
      description: '检查问卷声音API和数据库连接',
      table: 'questionnaire_voices_v2',
      query: '通过API获取问卷声音数据',
      endpoint: '/api/questionnaire-voices',
      expectedType: 'array',
      testFunction: (data: any) => {
        return data.success && Array.isArray(data.data);
      }
    },
    {
      id: 'visualization_data',
      name: '可视化数据',
      description: '检查可视化数据API和数据库连接',
      table: 'questionnaire_responses_v2',
      query: '通过API获取可视化数据',
      endpoint: '/api/visualization/data',
      expectedType: 'object',
      testFunction: (data: any) => {
        return data.success && data.data && typeof data.data === 'object';
      }
    },
    {
      id: 'stories_data',
      name: '故事列表数据',
      description: '检查故事列表API和数据库连接',
      table: 'story_contents_v2',
      query: '通过API获取故事列表数据',
      endpoint: '/api/story/list',
      expectedType: 'object',
      testFunction: (data: any) => {
        return data.success && data.data && Array.isArray(data.data.stories);
      }
    },
    {
      id: 'system_monitor',
      name: '系统监控状态',
      description: '检查系统监控API和基础连接',
      table: 'system',
      query: '通过API获取系统状态',
      endpoint: '/api/system/monitor',
      expectedType: 'object',
      testFunction: (data: any) => {
        return data.status === 'monitoring' && data.timestamp;
      }
    }
  ];

  // API端点配置
  const [apiEndpoints, setApiEndpoints] = useState<ApiEndpoint[]>([
    {
      name: '问卷统计',
      path: '/api/questionnaire/stats',
      method: 'GET',
      description: '获取问卷参与统计数据',
      database: 'college-employment-survey-realapi (D1)',
      tables: ['questionnaire_responses_v2'],
      status: 'loading'
    },
    {
      name: '问卷声音',
      path: '/api/questionnaire-voices',
      method: 'GET',
      description: '获取问卷声音数据',
      database: 'college-employment-survey-realapi (D1)',
      tables: ['questionnaire_voices_v2'],
      status: 'loading'
    },
    {
      name: '可视化数据',
      path: '/api/visualization/data',
      method: 'GET',
      description: '获取数据可视化图表数据',
      database: 'college-employment-survey-realapi (D1)',
      tables: ['questionnaire_responses_v2'],
      status: 'loading'
    },
    {
      name: '故事内容',
      path: '/api/story/list',
      method: 'GET',
      description: '获取故事墙内容',
      database: 'college-employment-survey-realapi (D1)',
      tables: ['story_contents_v2'],
      status: 'loading'
    },
    {
      name: '提交问卷',
      path: '/api/questionnaire/submit',
      method: 'POST',
      description: '提交问卷数据',
      database: 'college-employment-survey-realapi (D1)',
      tables: ['questionnaire_responses_v2'],
      status: 'loading'
    },
    {
      name: '管理员登录',
      path: '/api/admin/login',
      method: 'POST',
      description: '管理员身份验证',
      database: 'college-employment-survey-realapi (D1)',
      tables: ['users_v2'],
      status: 'loading'
    }
  ]);

  // 数据库信息
  const [databases, setDatabases] = useState<DatabaseInfo[]>([
    {
      name: 'college-employment-survey-realapi',
      type: 'Cloudflare D1',
      status: 'loading',
      tables: []
    }
  ]);

  // 前端页面配置
  const [frontendPages, setFrontendPages] = useState<FrontendPage[]>([
    {
      name: '首页',
      path: '/',
      apis: ['/api/questionnaire/stats'],
      status: 'loading',
      description: '展示平台概览和统计信息'
    },
    {
      name: '问卷调研',
      path: '/questionnaire',
      apis: ['/api/questionnaire/stats', '/api/questionnaire/submit'],
      status: 'loading',
      description: '问卷填写和实时统计'
    },
    {
      name: '数据可视化',
      path: '/visualization',
      apis: ['/api/visualization/data', '/api/questionnaire/stats'],
      status: 'loading',
      description: '数据图表和分析'
    },
    {
      name: '故事墙',
      path: '/story-wall',
      apis: ['/api/story/list', '/api/questionnaire-voices'],
      status: 'loading',
      description: '用户故事展示'
    },
    {
      name: '管理员登录',
      path: '/admin/login',
      apis: ['/api/admin/login'],
      status: 'loading',
      description: '管理员身份验证'
    }
  ]);

  // 获取API基础URL
  const getApiBaseUrl = () => {
    return import.meta.env.VITE_API_BASE_URL || 'https://college-employment-survey.aibook2099.workers.dev';
  };

  // 高级网络诊断功能
  const runAdvancedDiagnostics = async () => {
    const diagnostics = {
      dns: { status: 'unknown', details: '', rawData: null },
      connectivity: { status: 'unknown', details: '', rawData: null },
      cors: { status: 'unknown', details: '', rawData: null },
      ssl: { status: 'unknown', details: '', rawData: null },
      browser: { status: 'unknown', details: '', rawData: null },
      network: { status: 'unknown', details: '', rawData: null },
      headers: { status: 'unknown', details: '', rawData: null },
      timing: { status: 'unknown', details: '', rawData: null }
    };

    try {
      const apiBaseUrl = getApiBaseUrl();
      const hostname = new URL(apiBaseUrl).hostname;

      // 1. 浏览器环境检测
      diagnostics.browser = {
        status: 'healthy',
        details: `浏览器: ${navigator.userAgent.split(' ').pop()}, 在线: ${navigator.onLine}`,
        rawData: {
          userAgent: navigator.userAgent,
          online: navigator.onLine,
          language: navigator.language,
          platform: navigator.platform,
          cookieEnabled: navigator.cookieEnabled,
          doNotTrack: navigator.doNotTrack,
          connection: (navigator as any).connection ? {
            effectiveType: (navigator as any).connection.effectiveType,
            downlink: (navigator as any).connection.downlink,
            rtt: (navigator as any).connection.rtt
          } : null
        }
      };

      // 2. 网络状态检测
      if ((navigator as any).connection) {
        const conn = (navigator as any).connection;
        diagnostics.network = {
          status: conn.effectiveType === 'slow-2g' ? 'warning' : 'healthy',
          details: `网络类型: ${conn.effectiveType}, 下行: ${conn.downlink}Mbps, 延迟: ${conn.rtt}ms`,
          rawData: conn
        };
      }

      // 3. DNS解析测试（通过图片加载）
      try {
        const dnsTestStart = Date.now();
        const img = new Image();
        await new Promise((resolve, reject) => {
          img.onload = resolve;
          img.onerror = reject;
          img.src = `${apiBaseUrl}/favicon.ico?t=${Date.now()}`;
          setTimeout(reject, 5000); // 5秒超时
        });
        const dnsTime = Date.now() - dnsTestStart;
        diagnostics.dns = {
          status: 'healthy',
          details: `DNS解析成功: ${hostname} (${dnsTime}ms)`,
          rawData: { hostname, resolveTime: dnsTime }
        };
      } catch (dnsError) {
        diagnostics.dns = {
          status: 'error',
          details: `DNS解析失败: ${hostname}`,
          rawData: { hostname, error: dnsError }
        };
      }

      // 4. 基础连接测试
      const startTime = Date.now();
      const controller = new AbortController();
      const timeoutId = setTimeout(() => controller.abort(), 10000);

      const response = await fetch(`${apiBaseUrl}/api/system/monitor`, {
        method: 'GET',
        mode: 'cors',
        credentials: 'omit',
        headers: { 'Content-Type': 'application/json' },
        signal: controller.signal
      });

      clearTimeout(timeoutId);

      const responseTime = Date.now() - startTime;

      // 5. 响应头分析
      const responseHeaders: Record<string, string> = {};
      response.headers.forEach((value, key) => {
        responseHeaders[key] = value;
      });

      diagnostics.headers = {
        status: 'healthy',
        details: `响应头: ${Object.keys(responseHeaders).length}个`,
        rawData: {
          requestHeaders: {
            'Content-Type': 'application/json',
            'Origin': window.location.origin,
            'User-Agent': navigator.userAgent
          },
          responseHeaders
        }
      };

      // 6. 时间分析
      diagnostics.timing = {
        status: responseTime > 2000 ? 'warning' : 'healthy',
        details: `总响应时间: ${responseTime}ms`,
        rawData: {
          totalTime: responseTime,
          dnsTime: diagnostics.dns.rawData?.resolveTime || 0,
          networkTime: responseTime - (diagnostics.dns.rawData?.resolveTime || 0)
        }
      };

      if (response.ok) {
        const responseData = await response.json();
        diagnostics.connectivity = {
          status: 'healthy',
          details: `连接成功，响应时间: ${responseTime}ms，状态: ${response.status}`,
          rawData: {
            status: response.status,
            statusText: response.statusText,
            responseTime,
            responseData,
            url: response.url
          }
        };

        diagnostics.ssl = {
          status: 'healthy',
          details: `SSL证书有效，协议: ${response.url.startsWith('https') ? 'HTTPS' : 'HTTP'}`,
          rawData: { protocol: response.url.startsWith('https') ? 'HTTPS' : 'HTTP' }
        };
      } else {
        diagnostics.connectivity = {
          status: 'error',
          details: `HTTP错误: ${response.status} ${response.statusText}`,
          rawData: {
            status: response.status,
            statusText: response.statusText,
            responseTime,
            url: response.url
          }
        };
      }

      // 7. CORS预检测试
      try {
        const corsResponse = await fetch(`${apiBaseUrl}/api/system/monitor`, {
          method: 'OPTIONS',
          mode: 'cors',
          headers: {
            'Origin': window.location.origin,
            'Access-Control-Request-Method': 'GET',
            'Access-Control-Request-Headers': 'Content-Type'
          }
        });

        const corsHeaders: Record<string, string> = {};
        corsResponse.headers.forEach((value, key) => {
          if (key.startsWith('access-control-')) {
            corsHeaders[key] = value;
          }
        });

        if (corsResponse.ok) {
          const allowOrigin = corsResponse.headers.get('Access-Control-Allow-Origin');
          diagnostics.cors = {
            status: allowOrigin === '*' || allowOrigin === window.location.origin ? 'healthy' : 'warning',
            details: `CORS配置: 允许源=${allowOrigin}, 状态=${corsResponse.status}`,
            rawData: {
              status: corsResponse.status,
              allowOrigin,
              corsHeaders,
              requestOrigin: window.location.origin
            }
          };
        } else {
          diagnostics.cors = {
            status: 'error',
            details: `CORS预检失败: ${corsResponse.status} ${corsResponse.statusText}`,
            rawData: {
              status: corsResponse.status,
              statusText: corsResponse.statusText,
              corsHeaders
            }
          };
        }
      } catch (corsError) {
        diagnostics.cors = {
          status: 'error',
          details: `CORS错误: ${corsError instanceof Error ? corsError.message : 'Unknown'}`,
          rawData: { error: corsError }
        };
      }

    } catch (error) {
      const errorDetails = error instanceof Error ? error.message : 'Unknown error';
      const errorName = error instanceof Error ? error.name : 'UnknownError';

      // 详细错误分析
      if (error instanceof Error) {
        if (error.name === 'AbortError') {
          diagnostics.connectivity = {
            status: 'error',
            details: '连接超时（10秒）',
            rawData: { error: errorDetails, errorType: 'timeout' }
          };
        } else if (error.name === 'TimeoutError') {
          diagnostics.connectivity = {
            status: 'error',
            details: '连接超时（10秒）',
            rawData: { error: errorDetails, errorType: 'timeout' }
          };
        } else if (error.message.includes('Failed to fetch')) {
          diagnostics.connectivity = {
            status: 'error',
            details: `网络连接失败: ${errorDetails}`,
            rawData: {
              error: errorDetails,
              errorType: 'fetch_failed',
              possibleCauses: ['DNS解析失败', 'CORS阻止', '网络连接问题', '防火墙阻止', '服务器不可达']
            }
          };
        } else if (error.message.includes('NetworkError')) {
          diagnostics.connectivity = {
            status: 'error',
            details: `网络错误: ${errorDetails}`,
            rawData: { error: errorDetails, errorType: 'network_error' }
          };
        } else {
          diagnostics.connectivity = {
            status: 'error',
            details: `未知错误: ${errorDetails}`,
            rawData: { error: errorDetails, errorType: errorName }
          };
        }
      }
    }

    return diagnostics;
  };

  // 数据库查询测试（使用现有API端点）
  const runDatabaseTests = async () => {
    const results = [];

    for (const test of databaseQueryTests) {
      const startTime = Date.now();
      try {
        const apiBaseUrl = getApiBaseUrl();
        const response = await fetch(`${apiBaseUrl}${test.endpoint}`, {
          method: 'GET',
          mode: 'cors',
          credentials: 'omit',
          headers: { 'Content-Type': 'application/json' },
        });

        const responseTime = Date.now() - startTime;

        if (response.ok) {
          const data = await response.json();

          // 使用自定义测试函数验证数据
          let isValid = false;
          let resultValue = null;
          let resultCount = 0;

          if (test.testFunction) {
            isValid = test.testFunction(data);

            // 提取有用的结果信息
            if (test.expectedType === 'object') {
              if (data.data && typeof data.data.total === 'number') {
                resultValue = data.data.total;
                resultCount = data.data.total;
              } else if (data.data && Array.isArray(data.data.stories)) {
                resultValue = data.data.stories.length;
                resultCount = data.data.stories.length;
              } else if (data.status) {
                resultValue = data.status;
                resultCount = 1;
              } else {
                resultValue = 'API响应正常';
                resultCount = 1;
              }
            } else if (test.expectedType === 'array') {
              resultValue = Array.isArray(data.data) ? data.data.length : 0;
              resultCount = Array.isArray(data.data) ? data.data.length : 0;
            }
          } else {
            // 回退到基本类型检查
            if (test.expectedType === 'object') {
              isValid = typeof data === 'object' && data !== null;
              resultValue = 'API响应正常';
              resultCount = 1;
            } else if (test.expectedType === 'array') {
              isValid = Array.isArray(data.data);
              resultValue = Array.isArray(data.data) ? data.data.length : 0;
              resultCount = Array.isArray(data.data) ? data.data.length : 0;
            }
          }

          results.push({
            ...test,
            status: isValid ? 'healthy' : 'warning',
            responseTime,
            result: resultValue,
            details: isValid
              ? `数据库连接正常，${test.expectedType === 'object' ? `返回数据: ${resultValue}` : `记录数: ${resultCount}`}`
              : `API响应但数据格式异常，期望: ${test.expectedType}`,
            rawResponse: data,
            error: null
          });
        } else {
          results.push({
            ...test,
            status: 'error',
            responseTime,
            result: null,
            details: `HTTP错误: ${response.status} ${response.statusText}`,
            rawResponse: null,
            error: `HTTP ${response.status}`
          });
        }
      } catch (error) {
        const responseTime = Date.now() - startTime;
        results.push({
          ...test,
          status: 'error',
          responseTime,
          result: null,
          details: `连接失败: ${error instanceof Error ? error.message : 'Unknown error'}`,
          rawResponse: null,
          error: error instanceof Error ? error.message : 'Unknown error'
        });
      }
    }

    return results;
  };

  // 检查API端点状态
  const checkApiEndpoint = async (endpoint: ApiEndpoint): Promise<ApiEndpoint> => {
    const startTime = Date.now();
    try {
      const apiBaseUrl = getApiBaseUrl();
      const response = await fetch(`${apiBaseUrl}${endpoint.path}`, {
        method: endpoint.method === 'GET' ? 'GET' : 'OPTIONS', // 使用OPTIONS检查POST端点
        mode: 'cors',
        credentials: 'omit',
        headers: {
          'Content-Type': 'application/json',
        },
      });

      const responseTime = Date.now() - startTime;

      if (response.ok) {
        return {
          ...endpoint,
          status: 'healthy',
          responseTime,
          lastChecked: new Date().toISOString(),
          error: undefined
        };
      } else {
        return {
          ...endpoint,
          status: 'error',
          responseTime,
          lastChecked: new Date().toISOString(),
          error: `HTTP ${response.status}: ${response.statusText}`
        };
      }
    } catch (error) {
      return {
        ...endpoint,
        status: 'error',
        responseTime: Date.now() - startTime,
        lastChecked: new Date().toISOString(),
        error: error instanceof Error ? error.message : 'Unknown error'
      };
    }
  };

  // 检查数据库状态
  const checkDatabaseStatus = async (): Promise<DatabaseInfo[]> => {
    try {
      const apiBaseUrl = getApiBaseUrl();
      const response = await fetch(`${apiBaseUrl}/api/system/diagnostics`, {
        mode: 'cors',
        credentials: 'omit',
        headers: {
          'Content-Type': 'application/json',
        },
      });

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      const data = await response.json();

      if (data.databases?.D1) {
        const d1Data = data.databases.D1;
        const tables = [];

        // 处理表信息
        if (d1Data.questionnaire_responses_v2) {
          tables.push({
            name: 'questionnaire_responses_v2',
            count: d1Data.questionnaire_responses_v2.count || 0,
            status: d1Data.questionnaire_responses_v2.status || 'unknown'
          });
        }

        if (d1Data.questionnaire_voices_v2) {
          tables.push({
            name: 'questionnaire_voices_v2',
            count: d1Data.questionnaire_voices_v2.count || 0,
            status: d1Data.questionnaire_voices_v2.status || 'unknown'
          });
        }

        if (d1Data.story_contents_v2) {
          tables.push({
            name: 'story_contents_v2',
            count: d1Data.story_contents_v2.count || 0,
            status: d1Data.story_contents_v2.status || 'unknown'
          });
        }

        return [{
          name: 'college-employment-survey-realapi',
          type: 'Cloudflare D1',
          status: d1Data.status === 'connected' ? 'connected' : 'error',
          tables,
          error: d1Data.status !== 'connected' ? d1Data.error : undefined
        }];
      }

      return databases.map(db => ({ ...db, status: 'error' as const, error: 'No database info found' }));
    } catch (error) {
      return databases.map(db => ({
        ...db,
        status: 'error' as const,
        error: error instanceof Error ? error.message : 'Unknown error'
      }));
    }
  };

  // 运行所有检查
  const runAllChecks = async () => {
    setIsLoading(true);
    setLastUpdate(new Date().toISOString());

    try {
      // 首先运行高级网络诊断
      const networkDiag = await runAdvancedDiagnostics();
      setNetworkDiagnostics(networkDiag);

      // 运行数据库查询测试
      const dbTestResults = await runDatabaseTests();
      setDatabaseTests(dbTestResults);

      // 检查所有API端点
      const apiPromises = apiEndpoints.map(checkApiEndpoint);
      const updatedApis = await Promise.all(apiPromises);
      setApiEndpoints(updatedApis);

      // 检查数据库状态
      const updatedDatabases = await checkDatabaseStatus();
      setDatabases(updatedDatabases);

      // 更新前端页面状态（基于API状态）
      const updatedPages = frontendPages.map(page => {
        const pageApis = page.apis.map(apiPath =>
          updatedApis.find(api => api.path === apiPath)
        );
        const hasError = pageApis.some(api => api?.status === 'error');
        const isLoading = pageApis.some(api => api?.status === 'loading');

        return {
          ...page,
          status: hasError ? 'error' as const : isLoading ? 'loading' as const : 'healthy' as const
        };
      });
      setFrontendPages(updatedPages);

    } catch (error) {
      console.error('检查失败:', error);
    } finally {
      setIsLoading(false);
    }
  };

  // 组件挂载时运行检查
  useEffect(() => {
    runAllChecks();
  }, []);

  // 生成完整的监测报告文本
  const generateReportText = () => {
    const timestamp = new Date().toLocaleString('zh-CN');
    const apiBaseUrl = getApiBaseUrl();

    let report = `# 数据监测系统报告\n`;
    report += `生成时间: ${timestamp}\n`;
    report += `API基础URL: ${apiBaseUrl}\n`;
    report += `前端域名: ${window.location.origin}\n\n`;

    // 概览统计
    report += `## 系统概览\n`;
    report += `- API端点总数: ${apiEndpoints.length}\n`;
    report += `- 正常API: ${apiEndpoints.filter(api => api.status === 'healthy').length}\n`;
    report += `- 异常API: ${apiEndpoints.filter(api => api.status === 'error').length}\n`;
    report += `- 数据库连接: ${databases.filter(db => db.status === 'connected').length}/${databases.length}\n`;
    report += `- 前端页面: ${frontendPages.length}\n`;
    report += `- 正常页面: ${frontendPages.filter(page => page.status === 'healthy').length}\n\n`;

    // API端点详情
    report += `## API端点状态\n`;
    apiEndpoints.forEach((api, index) => {
      const statusIcon = api.status === 'healthy' ? '✅' : api.status === 'error' ? '❌' : '⏳';
      report += `${index + 1}. ${statusIcon} ${api.name}\n`;
      report += `   路径: ${api.method} ${api.path}\n`;
      report += `   状态: ${api.status}\n`;
      report += `   数据库: ${api.database}\n`;
      report += `   数据表: ${api.tables.join(', ')}\n`;
      if (api.responseTime) {
        report += `   响应时间: ${api.responseTime}ms\n`;
      }
      if (api.error) {
        report += `   错误信息: ${api.error}\n`;
      }
      if (api.lastChecked) {
        report += `   最后检查: ${new Date(api.lastChecked).toLocaleString('zh-CN')}\n`;
      }
      report += `\n`;
    });

    // 数据库状态
    report += `## 数据库状态\n`;
    databases.forEach((db, index) => {
      const statusIcon = db.status === 'connected' ? '✅' : '❌';
      report += `${index + 1}. ${statusIcon} ${db.name}\n`;
      report += `   类型: ${db.type}\n`;
      report += `   状态: ${db.status}\n`;
      if (db.error) {
        report += `   错误信息: ${db.error}\n`;
      }
      if (db.tables.length > 0) {
        report += `   数据表:\n`;
        db.tables.forEach(table => {
          report += `   - ${table.name}: ${table.count}条记录 (${table.status})\n`;
        });
      }
      report += `\n`;
    });

    // 数据库查询测试
    if (databaseTests.length > 0) {
      report += `## 数据库查询测试\n`;
      databaseTests.forEach((test, index) => {
        const statusIcon = test.status === 'healthy' ? '✅' : test.status === 'warning' ? '⚠️' : '❌';
        report += `${index + 1}. ${statusIcon} ${test.name}\n`;
        report += `   描述: ${test.description}\n`;
        report += `   数据表: ${test.table}\n`;
        report += `   查询: ${test.query}\n`;
        report += `   状态: ${test.status}\n`;
        report += `   响应时间: ${test.responseTime}ms\n`;
        report += `   详情: ${test.details}\n`;
        if (test.result !== null) {
          report += `   结果: ${typeof test.result === 'object' ? JSON.stringify(test.result) : test.result}\n`;
        }
        if (test.error) {
          report += `   错误: ${test.error}\n`;
        }
        report += `\n`;
      });
    }

    // 前端页面状态
    report += `## 前端页面状态\n`;
    frontendPages.forEach((page, index) => {
      const statusIcon = page.status === 'healthy' ? '✅' : page.status === 'error' ? '❌' : '⏳';
      report += `${index + 1}. ${statusIcon} ${page.name}\n`;
      report += `   路径: ${page.path}\n`;
      report += `   状态: ${page.status}\n`;
      report += `   描述: ${page.description}\n`;
      report += `   依赖API: ${page.apis.join(', ')}\n`;
      report += `\n`;
    });

    // 高级网络诊断信息
    if (networkDiagnostics) {
      report += `## 高级网络诊断\n`;
      Object.entries(networkDiagnostics).forEach(([key, value]: [string, any]) => {
        const statusIcon = value.status === 'healthy' ? '✅' : value.status === 'error' ? '❌' : value.status === 'warning' ? '⚠️' : '⏳';
        const keyName = {
          dns: 'DNS解析',
          connectivity: '网络连接',
          cors: 'CORS配置',
          ssl: 'SSL证书',
          browser: '浏览器环境',
          network: '网络状态',
          headers: '响应头',
          timing: '时间分析'
        }[key] || key;
        report += `- ${statusIcon} ${keyName}: ${value.details}\n`;

        // 添加原始数据（如果有错误）
        if (value.status === 'error' && value.rawData) {
          if (value.rawData.possibleCauses) {
            report += `  可能原因: ${value.rawData.possibleCauses.join(', ')}\n`;
          }
          if (value.rawData.errorType) {
            report += `  错误类型: ${value.rawData.errorType}\n`;
          }
        }
      });
      report += `\n`;
    }

    // 环境信息
    report += `## 环境配置\n`;
    report += `- NODE_ENV: ${import.meta.env.MODE}\n`;
    report += `- VITE_API_BASE_URL: ${import.meta.env.VITE_API_BASE_URL || '未设置'}\n`;
    report += `- VITE_ENVIRONMENT: ${import.meta.env.VITE_ENVIRONMENT || '未设置'}\n`;
    report += `- 前端域名: ${window.location.origin}\n`;
    report += `- 当前页面: ${window.location.pathname}\n`;
    report += `- 用户代理: ${navigator.userAgent}\n`;
    report += `- 在线状态: ${navigator.onLine ? '在线' : '离线'}\n`;

    if ((navigator as any).connection) {
      const conn = (navigator as any).connection;
      report += `- 网络类型: ${conn.effectiveType || '未知'}\n`;
      report += `- 下行速度: ${conn.downlink || '未知'}Mbps\n`;
      report += `- 网络延迟: ${conn.rtt || '未知'}ms\n`;
    }

    report += `\n---\n`;
    report += `报告生成完成 - ${timestamp}`;

    return report;
  };

  // 复制报告到剪贴板
  const copyReportToClipboard = async () => {
    try {
      const reportText = generateReportText();
      await navigator.clipboard.writeText(reportText);
      setCopySuccess(true);
      setTimeout(() => setCopySuccess(false), 2000);
    } catch (error) {
      console.error('复制失败:', error);
      // 降级方案：创建临时文本区域
      const textArea = document.createElement('textarea');
      textArea.value = generateReportText();
      document.body.appendChild(textArea);
      textArea.select();
      document.execCommand('copy');
      document.body.removeChild(textArea);
      setCopySuccess(true);
      setTimeout(() => setCopySuccess(false), 2000);
    }
  };

  // 下载报告为文本文件
  const downloadReport = () => {
    const reportText = generateReportText();
    const blob = new Blob([reportText], { type: 'text/plain;charset=utf-8' });
    const url = URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.href = url;
    link.download = `数据监测报告_${new Date().toISOString().slice(0, 19).replace(/:/g, '-')}.txt`;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    URL.revokeObjectURL(url);
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'healthy':
      case 'connected':
        return <CheckCircle className="h-4 w-4 text-green-500" />;
      case 'error':
        return <AlertCircle className="h-4 w-4 text-red-500" />;
      case 'loading':
        return <Clock className="h-4 w-4 text-yellow-500 animate-spin" />;
      default:
        return <AlertCircle className="h-4 w-4 text-gray-500" />;
    }
  };

  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'healthy':
      case 'connected':
        return <Badge variant="default" className="bg-green-500">正常</Badge>;
      case 'error':
        return <Badge variant="destructive">错误</Badge>;
      case 'loading':
        return <Badge variant="secondary">检查中...</Badge>;
      default:
        return <Badge variant="outline">未知</Badge>;
    }
  };

  return (
    <div className="container mx-auto px-4 py-8 space-y-6">
      {/* 页面标题 */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold">数据监测中心</h1>
          <p className="text-gray-600 mt-2">实时监控API端点、数据库连接和前端页面状态</p>
        </div>
        <div className="flex items-center gap-2">
          <Button
            onClick={copyReportToClipboard}
            variant="outline"
            className="flex items-center gap-2"
          >
            {copySuccess ? <CheckCircle className="h-4 w-4 text-green-500" /> : <Copy className="h-4 w-4" />}
            {copySuccess ? '已复制!' : '复制报告'}
          </Button>
          <Button
            onClick={downloadReport}
            variant="outline"
            className="flex items-center gap-2"
          >
            <Download className="h-4 w-4" />
            下载报告
          </Button>
          <Button
            onClick={() => window.open('/tools/local-debug-test.html', '_blank')}
            variant="outline"
            className="flex items-center gap-2"
          >
            <span className="text-sm">🛠️</span>
            本地调试工具
          </Button>
          <Button
            onClick={runAllChecks}
            disabled={isLoading}
            className="flex items-center gap-2"
          >
            {isLoading ? <Clock className="h-4 w-4 animate-spin" /> : <Monitor className="h-4 w-4" />}
            {isLoading ? '检查中...' : '刷新监测'}
          </Button>
        </div>
      </div>

      {/* 最后更新时间 */}
      {lastUpdate && (
        <div className="text-sm text-gray-500">
          最后更新: {new Date(lastUpdate).toLocaleString('zh-CN')}
        </div>
      )}

      {/* 概览统计 */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">API端点</CardTitle>
            <Server className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{apiEndpoints.length}</div>
            <p className="text-xs text-muted-foreground">
              正常: {apiEndpoints.filter(api => api.status === 'healthy').length} |
              错误: {apiEndpoints.filter(api => api.status === 'error').length}
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">数据库</CardTitle>
            <Database className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{databases.length}</div>
            <p className="text-xs text-muted-foreground">
              连接: {databases.filter(db => db.status === 'connected').length} |
              错误: {databases.filter(db => db.status === 'error').length}
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">前端页面</CardTitle>
            <Globe className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{frontendPages.length}</div>
            <p className="text-xs text-muted-foreground">
              正常: {frontendPages.filter(page => page.status === 'healthy').length} |
              错误: {frontendPages.filter(page => page.status === 'error').length}
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">数据表</CardTitle>
            <HardDrive className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {databases.reduce((total, db) => total + db.tables.length, 0)}
            </div>
            <p className="text-xs text-muted-foreground">
              总记录: {databases.reduce((total, db) =>
                total + db.tables.reduce((sum, table) => sum + table.count, 0), 0
              )}
            </p>
          </CardContent>
        </Card>
      </div>

      {/* 详细监测信息 */}
      <Tabs defaultValue="apis" className="w-full">
        <TabsList className="grid w-full grid-cols-5">
          <TabsTrigger value="apis">API端点</TabsTrigger>
          <TabsTrigger value="databases">数据库</TabsTrigger>
          <TabsTrigger value="db-tests">数据库测试</TabsTrigger>
          <TabsTrigger value="pages">前端页面</TabsTrigger>
          <TabsTrigger value="network">网络诊断</TabsTrigger>
        </TabsList>

        <TabsContent value="apis" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>API端点监测</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {apiEndpoints.map((api, index) => (
                  <div key={index} className="border rounded-lg p-4">
                    <div className="flex items-center justify-between mb-2">
                      <div className="flex items-center gap-2">
                        {getStatusIcon(api.status)}
                        <h3 className="font-semibold">{api.name}</h3>
                        {getStatusBadge(api.status)}
                      </div>
                      <div className="text-sm text-gray-500">
                        {api.responseTime && `${api.responseTime}ms`}
                      </div>
                    </div>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
                      <div>
                        <p><strong>路径:</strong> {api.method} {api.path}</p>
                        <p><strong>描述:</strong> {api.description}</p>
                      </div>
                      <div>
                        <p><strong>数据库:</strong> {api.database}</p>
                        <p><strong>数据表:</strong> {api.tables.join(', ')}</p>
                      </div>
                    </div>
                    {api.error && (
                      <div className="mt-2 p-2 bg-red-50 border border-red-200 rounded text-red-700 text-sm">
                        错误: {api.error}
                      </div>
                    )}
                    {api.lastChecked && (
                      <div className="mt-2 text-xs text-gray-500">
                        最后检查: {new Date(api.lastChecked).toLocaleString('zh-CN')}
                      </div>
                    )}
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="databases" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>数据库连接状态</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {databases.map((db, index) => (
                  <div key={index} className="border rounded-lg p-4">
                    <div className="flex items-center justify-between mb-4">
                      <div className="flex items-center gap-2">
                        {getStatusIcon(db.status)}
                        <h3 className="font-semibold">{db.name}</h3>
                        {getStatusBadge(db.status)}
                      </div>
                      <div className="text-sm text-gray-500">
                        类型: {db.type}
                      </div>
                    </div>

                    {db.tables.length > 0 && (
                      <div>
                        <h4 className="font-medium mb-2">数据表状态:</h4>
                        <div className="grid grid-cols-1 md:grid-cols-3 gap-2">
                          {db.tables.map((table, tableIndex) => (
                            <div key={tableIndex} className="bg-gray-50 p-2 rounded">
                              <div className="flex items-center justify-between">
                                <span className="font-medium text-sm">{table.name}</span>
                                <Badge variant="outline" className="text-xs">
                                  {table.count} 条记录
                                </Badge>
                              </div>
                              <div className="text-xs text-gray-500 mt-1">
                                状态: {table.status}
                              </div>
                            </div>
                          ))}
                        </div>
                      </div>
                    )}

                    {db.error && (
                      <div className="mt-2 p-2 bg-red-50 border border-red-200 rounded text-red-700 text-sm">
                        错误: {db.error}
                      </div>
                    )}
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="db-tests" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>数据库查询测试</CardTitle>
              <p className="text-sm text-gray-600">
                通过具体的SQL查询测试数据库连接和数据完整性
              </p>
            </CardHeader>
            <CardContent>
              {databaseTests.length > 0 ? (
                <div className="space-y-4">
                  {databaseTests.map((test, index) => (
                    <div key={test.id} className="border rounded-lg p-4">
                      <div className="flex items-center justify-between mb-2">
                        <div className="flex items-center gap-2">
                          {getStatusIcon(test.status)}
                          <h3 className="font-semibold">{test.name}</h3>
                          {getStatusBadge(test.status)}
                        </div>
                        <div className="text-sm text-gray-500">
                          {test.responseTime}ms
                        </div>
                      </div>

                      <div className="text-sm text-gray-600 mb-3">
                        {test.description}
                      </div>

                      <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm mb-3">
                        <div>
                          <p><strong>数据表:</strong> {test.table}</p>
                          <p><strong>期望类型:</strong> {test.expectedType}</p>
                        </div>
                        <div>
                          <p><strong>端点:</strong> {test.endpoint}</p>
                          <p><strong>状态:</strong> {test.details}</p>
                        </div>
                      </div>

                      {/* SQL查询 */}
                      <details className="mb-3">
                        <summary className="cursor-pointer text-sm font-medium text-blue-600 hover:text-blue-800">
                          查看SQL查询
                        </summary>
                        <pre className="mt-2 p-2 bg-gray-50 rounded text-xs overflow-auto">
                          {test.query}
                        </pre>
                      </details>

                      {/* 查询结果 */}
                      {test.result !== null && (
                        <div className="mb-3">
                          <strong className="text-sm">查询结果:</strong>
                          <div className="mt-1 p-2 bg-green-50 border border-green-200 rounded text-sm">
                            {typeof test.result === 'object' ? (
                              <pre className="text-xs overflow-auto max-h-32">
                                {JSON.stringify(test.result, null, 2)}
                              </pre>
                            ) : (
                              <span className="font-mono">{test.result}</span>
                            )}
                          </div>
                        </div>
                      )}

                      {/* 原始响应 */}
                      {test.rawResponse && (
                        <details className="mb-3">
                          <summary className="cursor-pointer text-xs text-blue-600 hover:text-blue-800">
                            查看原始响应
                          </summary>
                          <pre className="mt-2 p-2 bg-gray-50 rounded text-xs overflow-auto max-h-32">
                            {JSON.stringify(test.rawResponse, null, 2)}
                          </pre>
                        </details>
                      )}

                      {/* 错误信息 */}
                      {test.error && (
                        <div className="mt-2 p-2 bg-red-50 border border-red-200 rounded text-red-700 text-sm">
                          <strong>错误:</strong> {test.error}
                        </div>
                      )}

                      {/* 警告信息 */}
                      {test.status === 'warning' && (
                        <div className="mt-2 p-2 bg-yellow-50 border border-yellow-200 rounded text-yellow-700 text-sm">
                          <strong>警告:</strong> 查询成功但数据类型或格式可能不符合预期
                        </div>
                      )}
                    </div>
                  ))}
                </div>
              ) : (
                <div className="text-center py-8 text-gray-500">
                  <Database className="h-12 w-12 mx-auto mb-4 opacity-50" />
                  <p>点击"刷新监测"开始数据库查询测试</p>
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="pages" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>前端页面状态</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {frontendPages.map((page, index) => (
                  <div key={index} className="border rounded-lg p-4">
                    <div className="flex items-center justify-between mb-2">
                      <div className="flex items-center gap-2">
                        {getStatusIcon(page.status)}
                        <h3 className="font-semibold">{page.name}</h3>
                        {getStatusBadge(page.status)}
                      </div>
                      <div className="text-sm text-gray-500">
                        路径: {page.path}
                      </div>
                    </div>
                    <p className="text-sm text-gray-600 mb-2">{page.description}</p>
                    <div>
                      <strong className="text-sm">依赖的API:</strong>
                      <div className="flex flex-wrap gap-1 mt-1">
                        {page.apis.map((apiPath, apiIndex) => {
                          const apiStatus = apiEndpoints.find(api => api.path === apiPath);
                          return (
                            <Badge
                              key={apiIndex}
                              variant={apiStatus?.status === 'healthy' ? 'default' : 'destructive'}
                              className="text-xs"
                            >
                              {apiPath}
                            </Badge>
                          );
                        })}
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="network" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>高级网络诊断</CardTitle>
              <p className="text-sm text-gray-600">
                深度网络连接诊断，包含浏览器环境、DNS解析、CORS配置等详细分析
              </p>
            </CardHeader>
            <CardContent>
              {networkDiagnostics ? (
                <div className="space-y-4">
                  {Object.entries(networkDiagnostics).map(([key, value]: [string, any]) => {
                    const keyName = {
                      dns: 'DNS解析',
                      connectivity: '网络连接',
                      cors: 'CORS配置',
                      ssl: 'SSL证书',
                      browser: '浏览器环境',
                      network: '网络状态',
                      headers: '响应头',
                      timing: '时间分析'
                    }[key] || key;

                    return (
                      <div key={key} className="border rounded-lg p-4">
                        <div className="flex items-center justify-between mb-2">
                          <div className="flex items-center gap-2">
                            {getStatusIcon(value.status)}
                            <h3 className="font-semibold">{keyName}</h3>
                            {getStatusBadge(value.status)}
                          </div>
                        </div>
                        <div className="text-sm text-gray-600 mb-2">
                          {value.details}
                        </div>

                        {/* 显示原始数据 */}
                        {value.rawData && (
                          <details className="mt-2">
                            <summary className="cursor-pointer text-xs text-blue-600 hover:text-blue-800">
                              查看详细数据
                            </summary>
                            <pre className="mt-2 p-2 bg-gray-50 rounded text-xs overflow-auto max-h-32">
                              {JSON.stringify(value.rawData, null, 2)}
                            </pre>
                          </details>
                        )}

                        {/* 错误处理建议 */}
                        {value.status === 'error' && (
                          <div className="mt-2 p-2 bg-red-50 border border-red-200 rounded text-red-700 text-sm">
                            <strong>建议解决方案：</strong>
                            {key === 'dns' && ' 检查网络连接，尝试刷新页面或更换DNS服务器'}
                            {key === 'connectivity' && ' 检查网络连接，确认后端服务是否正常运行'}
                            {key === 'cors' && ' 联系管理员检查CORS配置，确认前端域名已添加到允许列表'}
                            {key === 'ssl' && ' 检查SSL证书是否有效，确认HTTPS配置正确'}
                            {key === 'browser' && ' 检查浏览器设置，确认JavaScript已启用'}
                            {key === 'network' && ' 检查网络连接质量，考虑切换到更稳定的网络'}
                            {key === 'headers' && ' 检查请求头配置，确认Content-Type正确'}
                            {key === 'timing' && ' 网络响应较慢，检查网络连接或服务器性能'}

                            {/* 显示可能原因 */}
                            {value.rawData?.possibleCauses && (
                              <div className="mt-1">
                                <strong>可能原因：</strong> {value.rawData.possibleCauses.join(', ')}
                              </div>
                            )}
                          </div>
                        )}

                        {/* 警告处理建议 */}
                        {value.status === 'warning' && (
                          <div className="mt-2 p-2 bg-yellow-50 border border-yellow-200 rounded text-yellow-700 text-sm">
                            <strong>注意：</strong>
                            {key === 'network' && ' 网络连接较慢，可能影响用户体验'}
                            {key === 'timing' && ' 响应时间较长，建议优化网络连接'}
                            {key === 'cors' && ' CORS配置可能不是最优的，建议检查'}
                          </div>
                        )}
                      </div>
                    );
                  })}
                </div>
              ) : (
                <div className="text-center py-8 text-gray-500">
                  <Wifi className="h-12 w-12 mx-auto mb-4 opacity-50" />
                  <p>点击"重新诊断"开始高级网络诊断</p>
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>

      {/* 环境信息 */}
      <Card>
        <CardHeader>
          <CardTitle>环境配置</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
            <div>
              <p><strong>API基础URL:</strong> {getApiBaseUrl()}</p>
              <p><strong>前端域名:</strong> {window.location.origin}</p>
            </div>
            <div>
              <p><strong>环境:</strong> {import.meta.env.MODE}</p>
              <p><strong>构建时间:</strong> {new Date().toLocaleString('zh-CN')}</p>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default DataMonitorPage;
