import React, { useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { Button } from '@/components/ui/button';

/**
 * 临时登录页面
 *
 * 提供一键登录功能，用于开发和测试
 */
const TempLoginPage: React.FC = () => {
  const navigate = useNavigate();

  // 组件加载时记录日志
  useEffect(() => {
    console.log('临时登录页面已加载');

    // 检查当前认证状态
    const token = localStorage.getItem('adminToken');
    const userJson = localStorage.getItem('adminUser');

    if (token && userJson) {
      try {
        const userData = JSON.parse(userJson);
        console.log('用户已登录:', userData);
      } catch (e) {
        console.error('解析用户数据失败:', e);
        // 清除无效的用户数据
        localStorage.removeItem('adminToken');
        localStorage.removeItem('adminUser');
      }
    } else {
      console.log('用户未登录');
    }
  }, []);

  /**
   * 处理登录
   * @param role 用户角色
   */
  const handleLogin = (role: string) => {
    console.log(`尝试以 ${role} 角色登录`);

    // 生成登录时间
    const loginTime = new Date().toISOString();

    // 创建用户数据
    const userData = {
      id: `${role}-${Date.now()}`,
      username: role,
      role: role,
      name: role === 'admin' ? '管理员' : role === 'reviewer' ? '审核员' : '超级管理员',
      permissions: ['manage_users', 'manage_content', 'view_statistics'],
      loginTime: loginTime
    };

    // 保存到本地存储
    const token = `mock_token_${role}_${Date.now()}`;
    localStorage.setItem('adminToken', token);
    localStorage.setItem('adminUser', JSON.stringify(userData));

    console.log('登录成功:', { token, userData });

    // 根据角色跳转
    let targetPath = '';
    if (role === 'admin') {
      targetPath = '/admin/dashboard';
    } else if (role === 'reviewer') {
      targetPath = '/reviewer/dashboard';
    } else if (role === 'superadmin') {
      targetPath = '/superadmin/dashboard';
    }

    console.log(`正在跳转到: ${targetPath}`);
    navigate(targetPath);
  };

  return (
    <div className="flex items-center justify-center min-h-screen bg-gray-100">
      <div className="w-full max-w-md p-8 bg-white rounded-lg shadow-md">
        <h1 className="text-2xl font-bold text-center mb-6">临时登录页面</h1>
        <p className="text-center mb-6">请选择一个角色进行登录</p>

        <div className="grid grid-cols-3 gap-4">
          <Button onClick={() => handleLogin('admin')}>管理员</Button>
          <Button onClick={() => handleLogin('reviewer')}>审核员</Button>
          <Button onClick={() => handleLogin('superadmin')}>超级管理员</Button>
        </div>

        <div className="mt-6 text-center text-sm text-gray-500">
          <p>开发环境一键登录，无需密码</p>
          <p className="mt-2">管理员账号: admin1 / admin123</p>
          <p>审核员账号: reviewer1 / admin123</p>
          <p>超级管理员账号: superadmin / admin123</p>
        </div>
      </div>
    </div>
  );
};

export default TempLoginPage;
