import React, { useState, useEffect } from 'react';
import { SecurityModule } from '../../lib/security';
import '../../styles/admin/security-logs.css';

// 日志条目接口
interface LogEntry {
  timestamp: number;
  level: 'error' | 'warn' | 'info' | 'debug';
  message: string;
  data?: any;
  clientIp?: string;
}

// 安全日志组件
const SecurityLogs: React.FC = () => {
  // 日志状态
  const [logs, setLogs] = useState<LogEntry[]>([]);
  const [loading, setLoading] = useState(true);
  const [filter, setFilter] = useState('all');
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const [summary, setSummary] = useState({
    error: 0,
    warn: 0,
    suspicious: 0,
    total: 0
  });

  // 每页显示的日志数量
  const logsPerPage = 10;

  // 加载日志
  useEffect(() => {
    fetchLogs();
  }, [filter, currentPage]);

  // 获取日志
  const fetchLogs = async () => {
    setLoading(true);
    
    try {
      // 模拟 API 请求
      await new Promise(resolve => setTimeout(resolve, 500));
      
      // 获取本地存储的日志
      const storedLogs = localStorage.getItem('security_logs');
      let allLogs: LogEntry[] = storedLogs ? JSON.parse(storedLogs) : [];
      
      // 如果没有日志，生成一些模拟日志
      if (allLogs.length === 0) {
        allLogs = generateMockLogs();
      }
      
      // 过滤日志
      let filteredLogs = allLogs;
      if (filter !== 'all') {
        filteredLogs = allLogs.filter(log => {
          if (filter === 'suspicious') {
            return log.message.toLowerCase().includes('suspicious') || 
                   log.message.toLowerCase().includes('可疑');
          }
          return log.level === filter;
        });
      }
      
      // 计算总页数
      setTotalPages(Math.max(1, Math.ceil(filteredLogs.length / logsPerPage)));
      
      // 分页
      const startIndex = (currentPage - 1) * logsPerPage;
      const endIndex = startIndex + logsPerPage;
      const paginatedLogs = filteredLogs.slice(startIndex, endIndex);
      
      // 更新日志
      setLogs(paginatedLogs);
      
      // 更新摘要
      setSummary({
        error: allLogs.filter(log => log.level === 'error').length,
        warn: allLogs.filter(log => log.level === 'warn').length,
        suspicious: allLogs.filter(log => 
          log.message.toLowerCase().includes('suspicious') || 
          log.message.toLowerCase().includes('可疑')
        ).length,
        total: allLogs.length
      });
    } catch (error) {
      console.error('Failed to fetch security logs:', error);
    } finally {
      setLoading(false);
    }
  };

  // 生成模拟日志
  const generateMockLogs = (): LogEntry[] => {
    const mockLogs: LogEntry[] = [];
    const now = Date.now();
    const messages = [
      { level: 'warn', message: '可疑提交检测到', clientIp: '************' },
      { level: 'warn', message: '验证码验证失败', clientIp: '************' },
      { level: 'warn', message: '蜜罐字段填写', clientIp: '************' },
      { level: 'error', message: 'API 请求验证失败', clientIp: '*************' },
      { level: 'warn', message: '速率限制超出', clientIp: '*************' },
      { level: 'warn', message: '行为分析失败', clientIp: '*************' },
      { level: 'info', message: '安全模块配置更新', clientIp: '**********' },
      { level: 'warn', message: '可疑提交检测到', clientIp: '*************' },
      { level: 'error', message: '安全中间件错误', clientIp: '*************' },
      { level: 'info', message: '安全模块初始化', clientIp: '**********' }
    ];
    
    // 生成 50 条模拟日志
    for (let i = 0; i < 50; i++) {
      const mockMessage = messages[i % messages.length];
      mockLogs.push({
        timestamp: now - i * 1000 * 60 * 15, // 每 15 分钟一条日志
        level: mockMessage.level as 'error' | 'warn' | 'info' | 'debug',
        message: mockMessage.message,
        clientIp: mockMessage.clientIp,
        data: { mockData: true }
      });
    }
    
    return mockLogs;
  };

  // 导出日志
  const exportLogs = () => {
    try {
      // 获取所有日志
      const storedLogs = localStorage.getItem('security_logs');
      const allLogs = storedLogs ? JSON.parse(storedLogs) : [];
      
      // 转换为 CSV
      const headers = ['时间', '级别', '消息', 'IP 地址', '详情'];
      const csvRows = [headers.join(',')];
      
      allLogs.forEach((log: LogEntry) => {
        const row = [
          new Date(log.timestamp).toISOString(),
          log.level,
          `"${log.message.replace(/"/g, '""')}"`,
          log.clientIp || '-',
          `"${JSON.stringify(log.data || {}).replace(/"/g, '""')}"`
        ];
        csvRows.push(row.join(','));
      });
      
      // 创建 CSV 文件
      const csvContent = csvRows.join('\n');
      const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
      const url = URL.createObjectURL(blob);
      
      // 下载文件
      const link = document.createElement('a');
      link.href = url;
      link.setAttribute('download', `security-logs-${new Date().toISOString().slice(0, 10)}.csv`);
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
    } catch (error) {
      console.error('Failed to export logs:', error);
      alert('导出日志失败，请重试');
    }
  };

  // 查看日志详情
  const viewLogDetails = (log: LogEntry) => {
    alert(JSON.stringify(log.data || {}, null, 2));
  };

  // 格式化时间
  const formatDate = (timestamp: number): string => {
    const date = new Date(timestamp);
    return `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}-${String(date.getDate()).padStart(2, '0')} ${String(date.getHours()).padStart(2, '0')}:${String(date.getMinutes()).padStart(2, '0')}:${String(date.getSeconds()).padStart(2, '0')}`;
  };

  return (
    <div className="security-logs">
      <div className="header">
        <h1>安全日志</h1>
        
        <button className="export-button" onClick={exportLogs}>
          导出日志
        </button>
      </div>
      
      <div className="summary-cards">
        <div className="summary-card error">
          <h3>错误</h3>
          <p>{summary.error}</p>
        </div>
        
        <div className="summary-card warn">
          <h3>警告</h3>
          <p>{summary.warn}</p>
        </div>
        
        <div className="summary-card success">
          <h3>阻止的可疑提交</h3>
          <p>{summary.suspicious}</p>
        </div>
        
        <div className="summary-card info">
          <h3>总日志数</h3>
          <p>{summary.total}</p>
        </div>
      </div>
      
      <div className="card">
        <div className="filters">
          <select
            value={filter}
            onChange={(e) => {
              setFilter(e.target.value);
              setCurrentPage(1);
            }}
          >
            <option value="all">所有日志</option>
            <option value="error">错误</option>
            <option value="warn">警告</option>
            <option value="suspicious">可疑活动</option>
            <option value="info">信息</option>
          </select>
          
          <button onClick={fetchLogs}>刷新</button>
        </div>
        
        {loading ? (
          <div className="loading">加载中...</div>
        ) : (
          <>
            <div className="table-container">
              <table className="logs-table">
                <thead>
                  <tr>
                    <th>时间</th>
                    <th>级别</th>
                    <th>消息</th>
                    <th>IP 地址</th>
                    <th>详情</th>
                  </tr>
                </thead>
                <tbody>
                  {logs.map((log, index) => (
                    <tr key={index} className={`log-level-${log.level}`}>
                      <td>{formatDate(log.timestamp)}</td>
                      <td>
                        <span className={`badge badge-${log.level}`}>
                          {log.level === 'error' ? '错误' : 
                           log.level === 'warn' ? '警告' : 
                           log.level === 'info' ? '信息' : '调试'}
                        </span>
                      </td>
                      <td>{log.message}</td>
                      <td>{log.clientIp || '-'}</td>
                      <td>
                        <button 
                          className="detail-button"
                          onClick={() => viewLogDetails(log)}
                        >
                          查看详情
                        </button>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
            
            <div className="pagination">
              <button 
                className="pagination-button"
                disabled={currentPage === 1}
                onClick={() => setCurrentPage(prev => Math.max(1, prev - 1))}
              >
                上一页
              </button>
              
              {Array.from({ length: Math.min(5, totalPages) }, (_, i) => {
                const pageNum = currentPage <= 3 
                  ? i + 1 
                  : currentPage >= totalPages - 2 
                    ? totalPages - 4 + i 
                    : currentPage - 2 + i;
                
                if (pageNum <= 0 || pageNum > totalPages) return null;
                
                return (
                  <button 
                    key={pageNum}
                    className={`pagination-button ${currentPage === pageNum ? 'active' : ''}`}
                    onClick={() => setCurrentPage(pageNum)}
                  >
                    {pageNum}
                  </button>
                );
              })}
              
              <button 
                className="pagination-button"
                disabled={currentPage === totalPages}
                onClick={() => setCurrentPage(prev => Math.min(totalPages, prev + 1))}
              >
                下一页
              </button>
            </div>
          </>
        )}
      </div>
    </div>
  );
};

export default SecurityLogs;
