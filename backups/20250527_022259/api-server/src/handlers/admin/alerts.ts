/**
 * 告警管理相关API处理函数
 */

import { Env } from '../../types';
import { verifyToken } from '../../utils/auth';

/**
 * 获取告警数据
 */
export async function handleGetAlerts(request: Request, env: Env): Promise<Response> {
  // 验证权限
  const authHeader = request.headers.get('Authorization');
  if (!authHeader || !authHeader.startsWith('Bearer ')) {
    return new Response(JSON.stringify({
      success: false,
      error: '未授权'
    }), {
      status: 401,
      headers: { 'Content-Type': 'application/json' }
    });
  }

  const token = authHeader.substring(7);
  const tokenResult = verifyToken(token, env.JWT_SECRET);

  if (!tokenResult.valid || !tokenResult.payload) {
    return new Response(JSON.stringify({
      success: false,
      error: '无效的令牌'
    }), {
      status: 401,
      headers: { 'Content-Type': 'application/json' }
    });
  }

  // 验证角色权限
  const role = tokenResult.payload.role;
  if (role !== 'superadmin') {
    return new Response(JSON.stringify({
      success: false,
      error: '没有权限'
    }), {
      status: 403,
      headers: { 'Content-Type': 'application/json' }
    });
  }

  try {
    // 在实际应用中，这里应该从数据库获取真实数据
    // 现在使用模拟数据
    const alerts = [];
    const alertCount = 20;
    const alertTypes = ['system', 'business'];
    const alertLevels = ['info', 'warning', 'critical'];
    const alertStatuses = ['active', 'resolved'];
    const ruleNames = [
      'CPU使用率过高',
      '内存使用率过高',
      '错误率过高',
      '响应时间过长',
      '数据库大小接近限制',
      '待审核内容过多',
      '登录失败次数过多',
      '敏感操作频率过高'
    ];
    const messages = [
      'CPU使用率超过80%',
      '内存使用率超过90%',
      '错误率超过5%',
      '平均响应时间超过500ms',
      '数据库大小接近1GB限制',
      '待审核内容超过100条',
      '登录失败次数超过10次',
      '敏感操作频率超过正常值'
    ];

    for (let i = 1; i <= alertCount; i++) {
      const timestamp = new Date(Date.now() - Math.floor(Math.random() * 7 * 24 * 60 * 60 * 1000)).toISOString();
      const type = alertTypes[Math.floor(Math.random() * alertTypes.length)];
      const level = alertLevels[Math.floor(Math.random() * alertLevels.length)];
      const status = alertStatuses[Math.floor(Math.random() * alertStatuses.length)];
      const ruleNameIndex = Math.floor(Math.random() * ruleNames.length);
      const ruleName = ruleNames[ruleNameIndex];
      const message = messages[ruleNameIndex];

      alerts.push({
        id: i,
        timestamp,
        type,
        level,
        status,
        ruleName,
        message
      });
    }

    // 排序告警，最新的在前面
    alerts.sort((a, b) => new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime());

    // 模拟告警规则
    const rules = [
      {
        id: 1,
        name: 'CPU使用率监控',
        type: 'system',
        level: 'warning',
        metric: 'cpu_usage',
        operator: '>',
        threshold: 80,
        duration: 300,
        description: '监控CPU使用率，超过80%持续5分钟触发告警',
        enabled: true
      },
      {
        id: 2,
        name: '内存使用率监控',
        type: 'system',
        level: 'warning',
        metric: 'memory_usage',
        operator: '>',
        threshold: 90,
        duration: 300,
        description: '监控内存使用率，超过90%持续5分钟触发告警',
        enabled: true
      },
      {
        id: 3,
        name: '错误率监控',
        type: 'system',
        level: 'critical',
        metric: 'error_rate',
        operator: '>',
        threshold: 5,
        duration: 180,
        description: '监控错误率，超过5%持续3分钟触发告警',
        enabled: true
      },
      {
        id: 4,
        name: '响应时间监控',
        type: 'system',
        level: 'warning',
        metric: 'response_time',
        operator: '>',
        threshold: 500,
        duration: 300,
        description: '监控平均响应时间，超过500ms持续5分钟触发告警',
        enabled: true
      },
      {
        id: 5,
        name: '数据库大小监控',
        type: 'system',
        level: 'warning',
        metric: 'db_size',
        operator: '>',
        threshold: 900000000,
        duration: 0,
        description: '监控数据库大小，接近1GB限制时触发告警',
        enabled: true
      },
      {
        id: 6,
        name: '待审核内容监控',
        type: 'business',
        level: 'info',
        metric: 'pending_reviews',
        operator: '>',
        threshold: 100,
        duration: 0,
        description: '监控待审核内容数量，超过100条触发告警',
        enabled: true
      },
      {
        id: 7,
        name: '登录失败监控',
        type: 'system',
        level: 'warning',
        metric: 'login_failures',
        operator: '>',
        threshold: 10,
        duration: 600,
        description: '监控登录失败次数，10分钟内超过10次触发告警',
        enabled: true
      },
      {
        id: 8,
        name: '敏感操作监控',
        type: 'business',
        level: 'info',
        metric: 'sensitive_operations',
        operator: '>',
        threshold: 20,
        duration: 3600,
        description: '监控敏感操作频率，1小时内超过20次触发告警',
        enabled: false
      }
    ];

    // 模拟通知设置
    const notificationSettings = {
      emailEnabled: true,
      emailRecipients: '<EMAIL>,<EMAIL>',
      slackEnabled: false,
      slackWebhook: '*****************************************************************************',
      slackChannel: '#alerts'
    };

    return new Response(JSON.stringify({
      success: true,
      alerts,
      rules,
      notificationSettings
    }), {
      status: 200,
      headers: { 'Content-Type': 'application/json' }
    });
  } catch (error) {
    console.error('获取告警数据错误:', error);
    return new Response(JSON.stringify({
      success: false,
      error: '获取告警数据失败'
    }), {
      status: 500,
      headers: { 'Content-Type': 'application/json' }
    });
  }
}

/**
 * 创建告警规则
 */
export async function handleCreateAlertRule(request: Request, env: Env): Promise<Response> {
  // 验证权限
  const authHeader = request.headers.get('Authorization');
  if (!authHeader || !authHeader.startsWith('Bearer ')) {
    return new Response(JSON.stringify({
      success: false,
      error: '未授权'
    }), {
      status: 401,
      headers: { 'Content-Type': 'application/json' }
    });
  }

  const token = authHeader.substring(7);
  const tokenResult = verifyToken(token, env.JWT_SECRET);

  if (!tokenResult.valid || !tokenResult.payload) {
    return new Response(JSON.stringify({
      success: false,
      error: '无效的令牌'
    }), {
      status: 401,
      headers: { 'Content-Type': 'application/json' }
    });
  }

  // 验证角色权限
  const role = tokenResult.payload.role;
  if (role !== 'superadmin') {
    return new Response(JSON.stringify({
      success: false,
      error: '没有权限'
    }), {
      status: 403,
      headers: { 'Content-Type': 'application/json' }
    });
  }

  try {
    // 获取请求体
    const body = await request.json();
    const { name, type, level, metric, operator, threshold, duration, description, enabled } = body;

    // 验证参数
    if (!name || !type || !level || !metric || !operator || threshold === undefined || duration === undefined) {
      return new Response(JSON.stringify({
        success: false,
        error: '参数不完整'
      }), {
        status: 400,
        headers: { 'Content-Type': 'application/json' }
      });
    }

    // 在实际应用中，这里应该创建告警规则
    // 现在模拟创建过程
    await new Promise(resolve => setTimeout(resolve, 1000));

    // 模拟规则ID
    const ruleId = Date.now();

    return new Response(JSON.stringify({
      success: true,
      ruleId,
      message: '告警规则创建成功'
    }), {
      status: 200,
      headers: { 'Content-Type': 'application/json' }
    });
  } catch (error) {
    console.error('创建告警规则错误:', error);
    return new Response(JSON.stringify({
      success: false,
      error: '创建告警规则失败'
    }), {
      status: 500,
      headers: { 'Content-Type': 'application/json' }
    });
  }
}

/**
 * 更新告警规则
 */
export async function handleUpdateAlertRule(request: Request, env: Env): Promise<Response> {
  // 验证权限
  const authHeader = request.headers.get('Authorization');
  if (!authHeader || !authHeader.startsWith('Bearer ')) {
    return new Response(JSON.stringify({
      success: false,
      error: '未授权'
    }), {
      status: 401,
      headers: { 'Content-Type': 'application/json' }
    });
  }

  const token = authHeader.substring(7);
  const tokenResult = verifyToken(token, env.JWT_SECRET);

  if (!tokenResult.valid || !tokenResult.payload) {
    return new Response(JSON.stringify({
      success: false,
      error: '无效的令牌'
    }), {
      status: 401,
      headers: { 'Content-Type': 'application/json' }
    });
  }

  // 验证角色权限
  const role = tokenResult.payload.role;
  if (role !== 'superadmin') {
    return new Response(JSON.stringify({
      success: false,
      error: '没有权限'
    }), {
      status: 403,
      headers: { 'Content-Type': 'application/json' }
    });
  }

  try {
    // 获取规则ID
    const url = new URL(request.url);
    const parts = url.pathname.split('/');
    const ruleId = parts[parts.length - 1];

    if (!ruleId) {
      return new Response(JSON.stringify({
        success: false,
        error: '规则ID不能为空'
      }), {
        status: 400,
        headers: { 'Content-Type': 'application/json' }
      });
    }

    // 获取请求体
    const body = await request.json();
    const { name, type, level, metric, operator, threshold, duration, description, enabled } = body;

    // 验证参数
    if (!name || !type || !level || !metric || !operator || threshold === undefined || duration === undefined) {
      return new Response(JSON.stringify({
        success: false,
        error: '参数不完整'
      }), {
        status: 400,
        headers: { 'Content-Type': 'application/json' }
      });
    }

    // 在实际应用中，这里应该更新告警规则
    // 现在模拟更新过程
    await new Promise(resolve => setTimeout(resolve, 1000));

    return new Response(JSON.stringify({
      success: true,
      message: '告警规则更新成功'
    }), {
      status: 200,
      headers: { 'Content-Type': 'application/json' }
    });
  } catch (error) {
    console.error('更新告警规则错误:', error);
    return new Response(JSON.stringify({
      success: false,
      error: '更新告警规则失败'
    }), {
      status: 500,
      headers: { 'Content-Type': 'application/json' }
    });
  }
}

/**
 * 删除告警规则
 */
export async function handleDeleteAlertRule(request: Request, env: Env): Promise<Response> {
  // 验证权限
  const authHeader = request.headers.get('Authorization');
  if (!authHeader || !authHeader.startsWith('Bearer ')) {
    return new Response(JSON.stringify({
      success: false,
      error: '未授权'
    }), {
      status: 401,
      headers: { 'Content-Type': 'application/json' }
    });
  }

  const token = authHeader.substring(7);
  const tokenResult = verifyToken(token, env.JWT_SECRET);

  if (!tokenResult.valid || !tokenResult.payload) {
    return new Response(JSON.stringify({
      success: false,
      error: '无效的令牌'
    }), {
      status: 401,
      headers: { 'Content-Type': 'application/json' }
    });
  }

  // 验证角色权限
  const role = tokenResult.payload.role;
  if (role !== 'superadmin') {
    return new Response(JSON.stringify({
      success: false,
      error: '没有权限'
    }), {
      status: 403,
      headers: { 'Content-Type': 'application/json' }
    });
  }

  try {
    // 获取规则ID
    const url = new URL(request.url);
    const parts = url.pathname.split('/');
    const ruleId = parts[parts.length - 1];

    if (!ruleId) {
      return new Response(JSON.stringify({
        success: false,
        error: '规则ID不能为空'
      }), {
        status: 400,
        headers: { 'Content-Type': 'application/json' }
      });
    }

    // 在实际应用中，这里应该删除告警规则
    // 现在模拟删除过程
    await new Promise(resolve => setTimeout(resolve, 1000));

    return new Response(JSON.stringify({
      success: true,
      message: '告警规则删除成功'
    }), {
      status: 200,
      headers: { 'Content-Type': 'application/json' }
    });
  } catch (error) {
    console.error('删除告警规则错误:', error);
    return new Response(JSON.stringify({
      success: false,
      error: '删除告警规则失败'
    }), {
      status: 500,
      headers: { 'Content-Type': 'application/json' }
    });
  }
}

/**
 * 更新告警通知设置
 */
export async function handleUpdateAlertSettings(request: Request, env: Env): Promise<Response> {
  // 验证权限
  const authHeader = request.headers.get('Authorization');
  if (!authHeader || !authHeader.startsWith('Bearer ')) {
    return new Response(JSON.stringify({
      success: false,
      error: '未授权'
    }), {
      status: 401,
      headers: { 'Content-Type': 'application/json' }
    });
  }

  const token = authHeader.substring(7);
  const tokenResult = verifyToken(token, env.JWT_SECRET);

  if (!tokenResult.valid || !tokenResult.payload) {
    return new Response(JSON.stringify({
      success: false,
      error: '无效的令牌'
    }), {
      status: 401,
      headers: { 'Content-Type': 'application/json' }
    });
  }

  // 验证角色权限
  const role = tokenResult.payload.role;
  if (role !== 'superadmin') {
    return new Response(JSON.stringify({
      success: false,
      error: '没有权限'
    }), {
      status: 403,
      headers: { 'Content-Type': 'application/json' }
    });
  }

  try {
    // 获取请求体
    const body = await request.json();
    const { emailEnabled, emailRecipients, slackEnabled, slackWebhook, slackChannel } = body;

    // 验证参数
    if (emailEnabled && !emailRecipients) {
      return new Response(JSON.stringify({
        success: false,
        error: '启用邮件通知时，收件人不能为空'
      }), {
        status: 400,
        headers: { 'Content-Type': 'application/json' }
      });
    }

    if (slackEnabled && !slackWebhook) {
      return new Response(JSON.stringify({
        success: false,
        error: '启用Slack通知时，Webhook URL不能为空'
      }), {
        status: 400,
        headers: { 'Content-Type': 'application/json' }
      });
    }

    // 在实际应用中，这里应该更新告警通知设置
    // 现在模拟更新过程
    await new Promise(resolve => setTimeout(resolve, 1000));

    return new Response(JSON.stringify({
      success: true,
      message: '告警通知设置更新成功'
    }), {
      status: 200,
      headers: { 'Content-Type': 'application/json' }
    });
  } catch (error) {
    console.error('更新告警通知设置错误:', error);
    return new Response(JSON.stringify({
      success: false,
      error: '更新告警通知设置失败'
    }), {
      status: 500,
      headers: { 'Content-Type': 'application/json' }
    });
  }
}
