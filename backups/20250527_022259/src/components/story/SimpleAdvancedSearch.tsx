import { useState, useEffect } from 'react';
import { Search, X, ChevronDown, ChevronUp } from 'lucide-react';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '@/components/ui/popover';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import {
  Collapsible,
  CollapsibleContent,
  CollapsibleTrigger,
} from '@/components/ui/collapsible';
import { POPULAR_SEARCHES, STORY_CATEGORIES, EDUCATION_LEVELS, INDUSTRIES } from '@/constants/storyConstants';

interface SimpleAdvancedSearchProps {
  searchQuery: string;
  onSearchChange: (query: string) => void;
  selectedCategory: string;
  onCategoryChange: (category: string) => void;
  selectedEducationLevel: string;
  onEducationLevelChange: (level: string) => void;
  selectedIndustry: string;
  onIndustryChange: (industry: string) => void;
  onClearFilters: () => void;
  isFilterActive: boolean;
}

export default function SimpleAdvancedSearch({
  searchQuery,
  onSearchChange,
  selectedCategory,
  onCategoryChange,
  selectedEducationLevel,
  onEducationLevelChange,
  selectedIndustry,
  onIndustryChange,
  onClearFilters,
  isFilterActive,
}: SimpleAdvancedSearchProps) {
  const [isAdvancedOpen, setIsAdvancedOpen] = useState(false);
  const [recentSearches, setRecentSearches] = useState<string[]>([]);
  const [showSuggestions, setShowSuggestions] = useState(false);

  // 加载最近搜索记录
  useEffect(() => {
    const saved = localStorage.getItem('recentSearches');
    if (saved) {
      try {
        setRecentSearches(JSON.parse(saved));
      } catch (e) {
        console.error('Failed to parse recent searches', e);
      }
    }
  }, []);

  // 保存搜索记录
  const saveToRecentSearches = (query: string) => {
    if (!query.trim()) return;
    
    const updated = [query, ...recentSearches.filter(s => s !== query)].slice(0, 5);
    setRecentSearches(updated);
    localStorage.setItem('recentSearches', JSON.stringify(updated));
  };

  // 选择搜索建议
  const selectSuggestion = (suggestion: string) => {
    onSearchChange(suggestion);
    saveToRecentSearches(suggestion);
    setShowSuggestions(false);
  };

  // 处理搜索提交
  const handleSearch = (e: React.FormEvent) => {
    e.preventDefault();
    if (searchQuery.trim()) {
      saveToRecentSearches(searchQuery);
    }
    setShowSuggestions(false);
  };

  // 处理输入变化
  const handleInputChange = (value: string) => {
    onSearchChange(value);
    setShowSuggestions(value.length > 0);
  };

  return (
    <div className="space-y-4">
      {/* 搜索框 */}
      <form onSubmit={handleSearch} className="flex gap-2">
        <div className="flex-1 relative">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
          
          <Popover open={showSuggestions} onOpenChange={setShowSuggestions}>
            <PopoverTrigger asChild>
              <Input
                placeholder="搜索故事..."
                value={searchQuery}
                onChange={(e) => handleInputChange(e.target.value)}
                onFocus={() => setShowSuggestions(searchQuery.length > 0)}
                className="pl-10 pr-10"
              />
            </PopoverTrigger>
            
            {(searchQuery.length > 0 || recentSearches.length > 0) && (
              <PopoverContent className="w-full p-0" align="start">
                <div className="max-h-64 overflow-y-auto">
                  {/* 热门搜索 */}
                  {searchQuery.length === 0 && (
                    <div className="p-2">
                      <div className="text-xs text-gray-500 mb-2">热门搜索</div>
                      {POPULAR_SEARCHES.map((suggestion) => (
                        <div
                          key={suggestion}
                          className="px-2 py-1.5 text-sm hover:bg-gray-100 cursor-pointer flex items-center"
                          onClick={() => selectSuggestion(suggestion)}
                        >
                          <Search className="h-3 w-3 mr-2 text-gray-400" />
                          {suggestion}
                        </div>
                      ))}
                    </div>
                  )}
                  
                  {/* 搜索建议 */}
                  {searchQuery.length > 0 && (
                    <div className="p-2">
                      {POPULAR_SEARCHES.filter(s =>
                        s.toLowerCase().includes(searchQuery.toLowerCase())
                      ).slice(0, 3).map((suggestion) => (
                        <div
                          key={suggestion}
                          className="px-2 py-1.5 text-sm hover:bg-gray-100 cursor-pointer flex items-center"
                          onClick={() => selectSuggestion(suggestion)}
                        >
                          <Search className="h-3 w-3 mr-2 text-gray-400" />
                          {suggestion}
                        </div>
                      ))}
                      
                      {/* 最近搜索 */}
                      {recentSearches.filter(s =>
                        s.toLowerCase().includes(searchQuery.toLowerCase()) &&
                        !POPULAR_SEARCHES.includes(s)
                      ).slice(0, 3).map((suggestion) => (
                        <div
                          key={suggestion}
                          className="px-2 py-1.5 text-sm hover:bg-gray-100 cursor-pointer flex items-center"
                          onClick={() => selectSuggestion(suggestion)}
                        >
                          <span className="text-gray-400 mr-1">最近:</span> {suggestion}
                        </div>
                      ))}
                    </div>
                  )}
                </div>
              </PopoverContent>
            )}
          </Popover>
          
          {searchQuery && (
            <Button
              type="button"
              variant="ghost"
              size="sm"
              onClick={() => onSearchChange('')}
              className="absolute right-1 top-1/2 transform -translate-y-1/2 h-8 w-8 p-0"
            >
              <X className="h-4 w-4" />
            </Button>
          )}
        </div>
        
        <Button type="submit">搜索</Button>
        <Button
          type="button"
          variant="outline"
          onClick={() => setIsAdvancedOpen(!isAdvancedOpen)}
          className="flex items-center gap-1"
        >
          高级
          {isAdvancedOpen ? (
            <ChevronUp className="h-4 w-4" />
          ) : (
            <ChevronDown className="h-4 w-4" />
          )}
        </Button>
      </form>

      {/* 高级筛选选项 */}
      <Collapsible open={isAdvancedOpen} onOpenChange={setIsAdvancedOpen}>
        <CollapsibleContent>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4 p-4 border rounded-lg bg-gray-50">
            {/* 分类筛选 */}
            <div className="space-y-2">
              <label className="text-sm font-medium">故事分类</label>
              <Select value={selectedCategory} onValueChange={onCategoryChange}>
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  {STORY_CATEGORIES.map((category) => (
                    <SelectItem key={category.id} value={category.id}>
                      {category.label}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            {/* 学历筛选 */}
            <div className="space-y-2">
              <label className="text-sm font-medium">学历背景</label>
              <Select value={selectedEducationLevel} onValueChange={onEducationLevelChange}>
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  {EDUCATION_LEVELS.map((level) => (
                    <SelectItem key={level.id} value={level.id}>
                      {level.label}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            {/* 行业筛选 */}
            <div className="space-y-2">
              <label className="text-sm font-medium">行业领域</label>
              <Select value={selectedIndustry} onValueChange={onIndustryChange}>
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  {INDUSTRIES.map((industry) => (
                    <SelectItem key={industry.id} value={industry.id}>
                      {industry.label}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            {/* 清除筛选按钮 */}
            {isFilterActive && (
              <div className="md:col-span-3 flex justify-center">
                <Button variant="outline" onClick={onClearFilters}>
                  清除所有筛选
                </Button>
              </div>
            )}
          </div>
        </CollapsibleContent>
      </Collapsible>

      {/* 活跃筛选条件显示 */}
      {isFilterActive && (
        <div className="flex flex-wrap gap-2">
          {searchQuery && (
            <Badge variant="secondary">
              搜索: {searchQuery}
              <X 
                className="h-3 w-3 ml-1 cursor-pointer" 
                onClick={() => onSearchChange('')}
              />
            </Badge>
          )}
          {selectedCategory !== 'all' && (
            <Badge variant="secondary">
              分类: {STORY_CATEGORIES.find(c => c.id === selectedCategory)?.label}
              <X 
                className="h-3 w-3 ml-1 cursor-pointer" 
                onClick={() => onCategoryChange('all')}
              />
            </Badge>
          )}
          {selectedEducationLevel !== 'all' && (
            <Badge variant="secondary">
              学历: {EDUCATION_LEVELS.find(e => e.id === selectedEducationLevel)?.label}
              <X 
                className="h-3 w-3 ml-1 cursor-pointer" 
                onClick={() => onEducationLevelChange('all')}
              />
            </Badge>
          )}
          {selectedIndustry !== 'all' && (
            <Badge variant="secondary">
              行业: {INDUSTRIES.find(i => i.id === selectedIndustry)?.label}
              <X 
                className="h-3 w-3 ml-1 cursor-pointer" 
                onClick={() => onIndustryChange('all')}
              />
            </Badge>
          )}
        </div>
      )}
    </div>
  );
}
