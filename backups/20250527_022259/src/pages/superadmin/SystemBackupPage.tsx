import React, { useState, useEffect } from 'react';
import SuperAdminLayout from '@/components/layouts/SuperAdminLayout';
import { Card, CardContent, CardHeader, CardTitle, CardDescription, CardFooter } from '@/components/ui/card';
import { But<PERSON> } from '@/components/ui/button';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Switch } from '@/components/ui/switch';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { useToast } from '@/components/ui/use-toast';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Checkbox } from '@/components/ui/checkbox';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import {
  Save, Database, HardDrive, RefreshCw, Calendar, Clock,
  Download, Upload, Trash2, CheckCircle, AlertTriangle,
  FileText, Settings, ArrowRight, Play, Pause, Info
} from 'lucide-react';
import {
  Table, TableBody, TableCaption, TableCell, TableHead,
  TableHeader, TableRow
} from '@/components/ui/table';
import {
  Dialog, DialogContent, DialogDescription, DialogFooter,
  DialogHeader, DialogTitle, DialogTrigger
} from '@/components/ui/dialog';
import {
  getBackups,
  createBackup,
  restoreBackup,
  deleteBackup,
  downloadBackup,
  Backup,
  BackupProgress
} from '@/services/systemBackupService';
import {
  getBackupSchedules,
  createBackupSchedule,
  updateBackupSchedule,
  deleteBackupSchedule,
  getBackupScheduleSettings,
  updateBackupScheduleSettings,
  BackupSchedule,
  BackupScheduleSettings
} from '@/services/backupScheduleService';

/**
 * 系统备份和恢复页面
 *
 * 提供系统备份和恢复功能，包括手动备份、自动备份计划和备份恢复
 */
const SystemBackupPage: React.FC = () => {
  const { toast } = useToast();
  const [isLoading, setIsLoading] = useState(true);
  const [backups, setBackups] = useState<Backup[]>([]);
  const [selectedBackup, setSelectedBackup] = useState<Backup | null>(null);
  const [isBackupInProgress, setIsBackupInProgress] = useState(false);
  const [backupProgress, setBackupProgress] = useState(0);
  const [isRestoreDialogOpen, setIsRestoreDialogOpen] = useState(false);
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);
  const [schedules, setSchedules] = useState<BackupSchedule[]>([]);
  const [isLoadingSchedules, setIsLoadingSchedules] = useState(true);
  const [selectedSchedule, setSelectedSchedule] = useState<BackupSchedule | null>(null);
  const [isScheduleDialogOpen, setIsScheduleDialogOpen] = useState(false);
  const [scheduleSettings, setScheduleSettings] = useState<BackupScheduleSettings>({
    enabled: true,
    frequency: 'daily',
    time: '02:00',
    retention: 30,
    backupType: 'mixed',
    location: 'local',
    compressionLevel: 'medium',
    includeUploads: true,
    includeDatabase: true,
    includeConfigs: true
  });

  // 备份设置状态
  const [backupSettings, setBackupSettings] = useState({
    location: 'local',
    compressionLevel: 'medium',
    includeDatabase: true,
    includeUploads: true,
    includeConfigs: true,
    retentionPolicy: 'count',
    retentionCount: 10,
    retentionDays: 30,
    namingFormat: '{type}_{date}_{time}',
    verifyBackup: true,
    notifyOnSuccess: true,
    notifyOnFailure: true,
    notifyEmail: '',
    maxParallelUploads: 3
  });

  // 模拟备份数据
  const mockBackups: Backup[] = [
    {
      id: '1',
      name: 'Full Backup - 2023-06-15',
      type: 'full',
      status: 'completed',
      size: '1.2 GB',
      createdAt: '2023-06-15 02:00:00',
      duration: '15 分钟',
      location: '本地存储',
      description: '每日自动完整备份'
    },
    {
      id: '2',
      name: 'Incremental Backup - 2023-06-16',
      type: 'incremental',
      status: 'completed',
      size: '250 MB',
      createdAt: '2023-06-16 02:00:00',
      duration: '5 分钟',
      location: '本地存储',
      description: '每日自动增量备份'
    },
    {
      id: '3',
      name: 'Incremental Backup - 2023-06-17',
      type: 'incremental',
      status: 'completed',
      size: '180 MB',
      createdAt: '2023-06-17 02:00:00',
      duration: '4 分钟',
      location: '本地存储',
      description: '每日自动增量备份'
    },
    {
      id: '4',
      name: 'Manual Backup - 2023-06-17',
      type: 'full',
      status: 'completed',
      size: '1.3 GB',
      createdAt: '2023-06-17 15:30:00',
      duration: '16 分钟',
      location: '云存储',
      description: '手动完整备份'
    },
    {
      id: '5',
      name: 'Differential Backup - 2023-06-18',
      type: 'differential',
      status: 'completed',
      size: '450 MB',
      createdAt: '2023-06-18 02:00:00',
      duration: '8 分钟',
      location: '本地存储',
      description: '每日自动差异备份'
    }
  ];

  // 初始化
  useEffect(() => {
    fetchBackups();
    fetchBackupSchedules();
    fetchBackupScheduleSettings();
  }, []);

  // 获取备份列表
  const fetchBackups = async () => {
    try {
      setIsLoading(true);

      const response = await getBackups();

      if (response.success) {
        // 转换备份数据格式
        const formattedBackups = response.data.map((backup: any) => ({
          id: backup.id,
          name: backup.name,
          type: backup.type === 'partial' ? 'incremental' : 'full',
          status: backup.status,
          size: typeof backup.size === 'number' ? formatFileSize(backup.size) : backup.size,
          createdAt: new Date(backup.createdAt).toLocaleString(),
          duration: '15 分钟', // 假设值，实际API可能不提供
          location: backup.metadata?.location || '本地存储',
          description: backup.description
        }));

        setBackups(formattedBackups);
      } else {
        toast({
          variant: 'destructive',
          title: '获取数据失败',
          description: response.message || '无法加载备份列表'
        });
      }
    } catch (error) {
      console.error('获取备份列表失败:', error);
      toast({
        variant: 'destructive',
        title: '获取数据失败',
        description: '无法加载备份列表，请稍后再试'
      });
    } finally {
      setIsLoading(false);
    }
  };

  // 格式化文件大小
  const formatFileSize = (bytes: number) => {
    if (bytes < 1024) return bytes + ' B';
    if (bytes < 1024 * 1024) return (bytes / 1024).toFixed(2) + ' KB';
    if (bytes < 1024 * 1024 * 1024) return (bytes / (1024 * 1024)).toFixed(2) + ' MB';
    return (bytes / (1024 * 1024 * 1024)).toFixed(2) + ' GB';
  };

  // 处理备份进度回调
  const handleBackupProgress = (progress: BackupProgress) => {
    setBackupProgress(progress.progress);
  };

  // 开始备份
  const startBackup = async (type: 'full' | 'incremental' | 'differential') => {
    try {
      setIsBackupInProgress(true);
      setBackupProgress(0);

      // 转换备份类型
      const backupType = type === 'full' ? 'full' : 'partial';
      const backupName = `${type === 'full' ? '完整' : type === 'incremental' ? '增量' : '差异'}备份 - ${new Date().toLocaleDateString()}`;

      const response = await createBackup({
        name: backupName,
        description: '手动创建的备份',
        type: backupType
      }, handleBackupProgress);

      if (response.success) {
        // 添加新备份到列表
        const newBackup: Backup = {
          id: response.data.id,
          name: response.data.name,
          type: type,
          status: 'completed',
          size: typeof response.data.size === 'number' ? formatFileSize(response.data.size) : '未知',
          createdAt: new Date(response.data.createdAt).toLocaleString(),
          duration: '完成', // 假设值，实际API可能不提供
          location: response.data.metadata?.location || '本地存储',
          description: response.data.description
        };

        setBackups(prev => [newBackup, ...prev]);

        toast({
          title: '备份完成',
          description: `${type === 'full' ? '完整' : type === 'incremental' ? '增量' : '差异'}备份已成功完成`
        });
      } else {
        toast({
          variant: 'destructive',
          title: '备份失败',
          description: response.message || '无法完成备份操作'
        });
      }
    } catch (error) {
      console.error('备份失败:', error);
      toast({
        variant: 'destructive',
        title: '备份失败',
        description: '无法完成备份操作，请稍后再试'
      });
    } finally {
      setIsBackupInProgress(false);
    }
  };

  // 恢复备份
  const handleRestoreBackup = async (backup: Backup) => {
    try {
      setIsBackupInProgress(true);
      setBackupProgress(0);

      toast({
        title: '开始恢复',
        description: `正在恢复备份: ${backup.name}`
      });

      const response = await restoreBackup({
        backupId: backup.id,
        options: {
          overwrite: true
        }
      }, handleBackupProgress);

      if (response.success) {
        toast({
          title: '恢复完成',
          description: response.message || '系统已成功恢复到所选备份点'
        });

        setIsRestoreDialogOpen(false);
      } else {
        toast({
          variant: 'destructive',
          title: '恢复失败',
          description: response.message || '无法完成恢复操作'
        });
      }
    } catch (error) {
      console.error('恢复失败:', error);
      toast({
        variant: 'destructive',
        title: '恢复失败',
        description: '无法完成恢复操作，请稍后再试'
      });
    } finally {
      setIsBackupInProgress(false);
    }
  };

  // 删除备份
  const handleDeleteBackup = async (backup: Backup) => {
    try {
      setIsLoading(true);

      const response = await deleteBackup(backup.id);

      if (response.success) {
        // 从列表中移除
        setBackups(prev => prev.filter(b => b.id !== backup.id));

        toast({
          title: '删除成功',
          description: response.message || `备份 ${backup.name} 已成功删除`
        });

        setIsDeleteDialogOpen(false);
      } else {
        toast({
          variant: 'destructive',
          title: '删除失败',
          description: response.message || '无法删除所选备份'
        });
      }
    } catch (error) {
      console.error('删除失败:', error);
      toast({
        variant: 'destructive',
        title: '删除失败',
        description: '无法删除所选备份，请稍后再试'
      });
    } finally {
      setIsLoading(false);
    }
  };

  // 下载备份
  const handleDownloadBackup = async (backup: Backup) => {
    try {
      const response = await downloadBackup(backup.id);

      if (response.success && response.data?.downloadUrl) {
        // 如果是真实URL，则打开下载链接
        if (response.data.downloadUrl !== '#') {
          window.open(response.data.downloadUrl, '_blank');
        } else {
          toast({
            title: '开发环境提示',
            description: '开发环境无法下载备份文件',
            variant: 'default'
          });
        }
      } else {
        toast({
          variant: 'destructive',
          title: '获取下载链接失败',
          description: response.message || '无法获取备份下载链接'
        });
      }
    } catch (error) {
      console.error('下载备份失败:', error);
      toast({
        variant: 'destructive',
        title: '下载备份失败',
        description: '无法下载备份文件，请稍后再试'
      });
    }
  };

  // 获取备份计划列表
  const fetchBackupSchedules = async () => {
    try {
      setIsLoadingSchedules(true);

      const response = await getBackupSchedules();

      if (response.success) {
        setSchedules(response.data);
      } else {
        toast({
          variant: 'destructive',
          title: '获取数据失败',
          description: response.message || '无法加载备份计划列表'
        });
      }
    } catch (error) {
      console.error('获取备份计划列表失败:', error);
      toast({
        variant: 'destructive',
        title: '获取数据失败',
        description: '无法加载备份计划列表，请稍后再试'
      });
    } finally {
      setIsLoadingSchedules(false);
    }
  };

  // 获取备份计划设置
  const fetchBackupScheduleSettings = async () => {
    try {
      const response = await getBackupScheduleSettings();

      if (response.success) {
        setScheduleSettings(response.data);
      } else {
        toast({
          variant: 'destructive',
          title: '获取数据失败',
          description: response.message || '无法加载备份计划设置'
        });
      }
    } catch (error) {
      console.error('获取备份计划设置失败:', error);
      toast({
        variant: 'destructive',
        title: '获取数据失败',
        description: '无法加载备份计划设置，请稍后再试'
      });
    }
  };

  // 保存备份计划设置
  const saveBackupScheduleSettings = async () => {
    try {
      const response = await updateBackupScheduleSettings(scheduleSettings);

      if (response.success) {
        toast({
          title: '设置已保存',
          description: '备份计划设置已成功保存'
        });
      } else {
        toast({
          variant: 'destructive',
          title: '保存失败',
          description: response.message || '无法保存备份计划设置'
        });
      }
    } catch (error) {
      console.error('保存备份计划设置失败:', error);
      toast({
        variant: 'destructive',
        title: '保存失败',
        description: '无法保存备份计划设置，请稍后再试'
      });
    }
  };

  // 保存备份设置
  const saveBackupSettings = async () => {
    try {
      toast({
        title: '设置已保存',
        description: '备份设置已成功保存'
      });
    } catch (error) {
      console.error('保存备份设置失败:', error);
      toast({
        variant: 'destructive',
        title: '保存失败',
        description: '无法保存备份设置，请稍后再试'
      });
    }
  };

  // 创建备份计划
  const handleCreateSchedule = async (schedule: Partial<BackupSchedule>) => {
    try {
      const response = await createBackupSchedule(schedule);

      if (response.success) {
        setSchedules(prev => [response.data, ...prev]);
        toast({
          title: '创建成功',
          description: '备份计划已成功创建'
        });
        setIsScheduleDialogOpen(false);
      } else {
        toast({
          variant: 'destructive',
          title: '创建失败',
          description: response.message || '无法创建备份计划'
        });
      }
    } catch (error) {
      console.error('创建备份计划失败:', error);
      toast({
        variant: 'destructive',
        title: '创建失败',
        description: '无法创建备份计划，请稍后再试'
      });
    }
  };

  // 更新备份计划
  const handleUpdateSchedule = async (id: string, schedule: Partial<BackupSchedule>) => {
    try {
      const response = await updateBackupSchedule(id, schedule);

      if (response.success) {
        // 更新列表中的计划
        setSchedules(prev => prev.map(s => s.id === id ? { ...s, ...schedule } : s));
        toast({
          title: '更新成功',
          description: '备份计划已成功更新'
        });
      } else {
        toast({
          variant: 'destructive',
          title: '更新失败',
          description: response.message || '无法更新备份计划'
        });
      }
    } catch (error) {
      console.error('更新备份计划失败:', error);
      toast({
        variant: 'destructive',
        title: '更新失败',
        description: '无法更新备份计划，请稍后再试'
      });
    }
  };

  // 删除备份计划
  const handleDeleteSchedule = async (id: string) => {
    try {
      const response = await deleteBackupSchedule(id);

      if (response.success) {
        // 从列表中移除
        setSchedules(prev => prev.filter(s => s.id !== id));
        toast({
          title: '删除成功',
          description: '备份计划已成功删除'
        });
      } else {
        toast({
          variant: 'destructive',
          title: '删除失败',
          description: response.message || '无法删除备份计划'
        });
      }
    } catch (error) {
      console.error('删除备份计划失败:', error);
      toast({
        variant: 'destructive',
        title: '删除失败',
        description: '无法删除备份计划，请稍后再试'
      });
    }
  };

  // 获取备份类型标签
  const getBackupTypeLabel = (type: string) => {
    switch (type) {
      case 'full':
        return '完整备份';
      case 'incremental':
        return '增量备份';
      case 'differential':
        return '差异备份';
      default:
        return type;
    }
  };

  // 获取备份状态标签和颜色
  const getBackupStatusBadge = (status: string) => {
    switch (status) {
      case 'completed':
        return <Badge className="bg-green-100 text-green-800">已完成</Badge>;
      case 'in_progress':
        return <Badge className="bg-blue-100 text-blue-800">进行中</Badge>;
      case 'failed':
        return <Badge className="bg-red-100 text-red-800">失败</Badge>;
      default:
        return <Badge className="bg-gray-100 text-gray-800">{status}</Badge>;
    }
  };

  return (
    <SuperAdminLayout>
      <div className="container mx-auto py-6">
        <div className="flex justify-between items-center mb-6">
          <div className="flex items-center">
            <Database className="h-6 w-6 mr-2" />
            <h1 className="text-2xl font-bold">系统备份与恢复</h1>
          </div>
          <div className="flex items-center space-x-2">
            <Button variant="outline" onClick={() => fetchBackups()}>
              <RefreshCw className={`mr-2 h-4 w-4 ${isLoading ? 'animate-spin' : ''}`} />
              刷新
            </Button>
          </div>
        </div>

        <Alert className="mb-6">
          <AlertTriangle className="h-4 w-4" />
          <AlertTitle>重要提示</AlertTitle>
          <AlertDescription>
            系统备份功能已升级，现在支持更高效的备份和恢复操作。请定期创建备份以防止数据丢失。
          </AlertDescription>
        </Alert>

        <Tabs defaultValue="backups" className="space-y-6">
          <TabsList>
            <TabsTrigger value="backups">备份列表</TabsTrigger>
            <TabsTrigger value="create">创建备份</TabsTrigger>
            <TabsTrigger value="schedule">备份计划</TabsTrigger>
            <TabsTrigger value="settings">备份设置</TabsTrigger>
          </TabsList>

          {/* 备份列表 */}
          <TabsContent value="backups" className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle>备份历史</CardTitle>
                <CardDescription>
                  系统备份历史记录
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="rounded-md border">
                  <Table>
                    <TableHeader>
                      <TableRow>
                        <TableHead>备份名称</TableHead>
                        <TableHead>类型</TableHead>
                        <TableHead>大小</TableHead>
                        <TableHead>创建时间</TableHead>
                        <TableHead>状态</TableHead>
                        <TableHead>存储位置</TableHead>
                        <TableHead className="text-right">操作</TableHead>
                      </TableRow>
                    </TableHeader>
                    <TableBody>
                      {isLoading ? (
                        <TableRow>
                          <TableCell colSpan={7} className="text-center py-8">
                            <div className="flex justify-center items-center">
                              <RefreshCw className="h-6 w-6 animate-spin mr-2" />
                              <span>加载中...</span>
                            </div>
                          </TableCell>
                        </TableRow>
                      ) : backups.length === 0 ? (
                        <TableRow>
                          <TableCell colSpan={7} className="text-center py-8">
                            未找到备份记录
                          </TableCell>
                        </TableRow>
                      ) : (
                        backups.map((backup) => (
                          <TableRow key={backup.id}>
                            <TableCell>
                              <div className="font-medium">{backup.name}</div>
                              {backup.description && (
                                <div className="text-sm text-muted-foreground">{backup.description}</div>
                              )}
                            </TableCell>
                            <TableCell>{getBackupTypeLabel(backup.type)}</TableCell>
                            <TableCell>{backup.size}</TableCell>
                            <TableCell>
                              <div>{backup.createdAt}</div>
                              <div className="text-sm text-muted-foreground">耗时: {backup.duration}</div>
                            </TableCell>
                            <TableCell>{getBackupStatusBadge(backup.status)}</TableCell>
                            <TableCell>{backup.location}</TableCell>
                            <TableCell className="text-right">
                              <div className="flex justify-end space-x-1">
                                <Button
                                  variant="ghost"
                                  size="sm"
                                  onClick={() => {
                                    setSelectedBackup(backup);
                                    setIsRestoreDialogOpen(true);
                                  }}
                                >
                                  <ArrowRight className="h-4 w-4 text-blue-500" />
                                </Button>
                                <Button
                                  variant="ghost"
                                  size="sm"
                                  onClick={() => handleDownloadBackup(backup)}
                                >
                                  <Download className="h-4 w-4 text-green-500" />
                                </Button>
                                <Button
                                  variant="ghost"
                                  size="sm"
                                  onClick={() => {
                                    setSelectedBackup(backup);
                                    setIsDeleteDialogOpen(true);
                                  }}
                                >
                                  <Trash2 className="h-4 w-4 text-red-500" />
                                </Button>
                              </div>
                            </TableCell>
                          </TableRow>
                        ))
                      )}
                    </TableBody>
                  </Table>
                </div>
              </CardContent>
            </Card>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <Card>
                <CardHeader>
                  <CardTitle>备份统计</CardTitle>
                  <CardDescription>备份使用情况统计</CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    <div>
                      <div className="flex justify-between mb-1">
                        <span className="text-sm font-medium">存储空间使用</span>
                        <span className="text-sm text-muted-foreground">3.4 GB / 10 GB</span>
                      </div>
                      <Progress value={34} className="h-2" />
                    </div>

                    <div className="grid grid-cols-2 gap-4">
                      <div className="space-y-1">
                        <span className="text-sm text-muted-foreground">总备份数</span>
                        <div className="text-2xl font-bold">{backups.length}</div>
                      </div>

                      <div className="space-y-1">
                        <span className="text-sm text-muted-foreground">完整备份</span>
                        <div className="text-2xl font-bold">{backups.filter(b => b.type === 'full').length}</div>
                      </div>

                      <div className="space-y-1">
                        <span className="text-sm text-muted-foreground">增量备份</span>
                        <div className="text-2xl font-bold">{backups.filter(b => b.type === 'incremental').length}</div>
                      </div>

                      <div className="space-y-1">
                        <span className="text-sm text-muted-foreground">差异备份</span>
                        <div className="text-2xl font-bold">{backups.filter(b => b.type === 'differential').length}</div>
                      </div>
                    </div>

                    <div className="pt-2">
                      <div className="text-sm font-medium mb-1">最近备份</div>
                      <div className="text-sm">
                        {backups.length > 0 ? (
                          <div className="flex items-center">
                            <Clock className="h-4 w-4 mr-1 text-muted-foreground" />
                            <span>{backups[0].createdAt}</span>
                          </div>
                        ) : (
                          <span className="text-muted-foreground">无备份记录</span>
                        )}
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle>备份状态</CardTitle>
                  <CardDescription>备份系统状态</CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    <div className="flex items-center justify-between p-3 border rounded-md">
                      <div className="flex items-center">
                        <div className="w-3 h-3 rounded-full bg-green-500 mr-2"></div>
                        <span className="font-medium">备份服务</span>
                      </div>
                      <Badge className="bg-green-100 text-green-800">正常运行</Badge>
                    </div>

                    <div className="flex items-center justify-between p-3 border rounded-md">
                      <div className="flex items-center">
                        <div className="w-3 h-3 rounded-full bg-green-500 mr-2"></div>
                        <span className="font-medium">存储服务</span>
                      </div>
                      <Badge className="bg-green-100 text-green-800">正常运行</Badge>
                    </div>

                    <div className="flex items-center justify-between p-3 border rounded-md">
                      <div className="flex items-center">
                        <div className="w-3 h-3 rounded-full bg-green-500 mr-2"></div>
                        <span className="font-medium">自动备份</span>
                      </div>
                      <Badge className="bg-green-100 text-green-800">已启用</Badge>
                    </div>

                    <div className="flex items-center justify-between p-3 border rounded-md">
                      <div className="flex items-center">
                        <div className="w-3 h-3 rounded-full bg-green-500 mr-2"></div>
                        <span className="font-medium">下次备份</span>
                      </div>
                      <span className="text-sm">今天 02:00</span>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>
          </TabsContent>

          {/* 创建备份 */}
          <TabsContent value="create" className="space-y-6">
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              <Card>
                <CardHeader>
                  <CardTitle>完整备份</CardTitle>
                  <CardDescription>
                    创建系统的完整备份
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="space-y-2">
                    <p className="text-sm text-muted-foreground">
                      完整备份包含系统的所有数据，包括数据库、上传文件和配置文件。
                    </p>
                    <p className="text-sm text-muted-foreground">
                      建议每周进行一次完整备份。
                    </p>
                    <div className="flex items-center mt-4 text-sm text-muted-foreground">
                      <HardDrive className="h-4 w-4 mr-1" />
                      <span>预计大小: ~1.2 GB</span>
                    </div>
                    <div className="flex items-center mt-1 text-sm text-muted-foreground">
                      <Clock className="h-4 w-4 mr-1" />
                      <span>预计时间: 15-20 分钟</span>
                    </div>
                  </div>
                </CardContent>
                <CardFooter>
                  <Button
                    className="w-full"
                    onClick={() => startBackup('full')}
                    disabled={isBackupInProgress}
                  >
                    <Save className="mr-2 h-4 w-4" />
                    开始完整备份
                  </Button>
                </CardFooter>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle>增量备份</CardTitle>
                  <CardDescription>
                    创建自上次备份以来的变更备份
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="space-y-2">
                    <p className="text-sm text-muted-foreground">
                      增量备份仅包含自上次备份以来发生变更的数据。
                    </p>
                    <p className="text-sm text-muted-foreground">
                      建议每天进行一次增量备份。
                    </p>
                    <div className="flex items-center mt-4 text-sm text-muted-foreground">
                      <HardDrive className="h-4 w-4 mr-1" />
                      <span>预计大小: ~200 MB</span>
                    </div>
                    <div className="flex items-center mt-1 text-sm text-muted-foreground">
                      <Clock className="h-4 w-4 mr-1" />
                      <span>预计时间: 3-5 分钟</span>
                    </div>
                  </div>
                </CardContent>
                <CardFooter>
                  <Button
                    className="w-full"
                    onClick={() => startBackup('incremental')}
                    disabled={isBackupInProgress}
                  >
                    <Save className="mr-2 h-4 w-4" />
                    开始增量备份
                  </Button>
                </CardFooter>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle>差异备份</CardTitle>
                  <CardDescription>
                    创建自上次完整备份以来的变更备份
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="space-y-2">
                    <p className="text-sm text-muted-foreground">
                      差异备份包含自上次完整备份以来发生变更的所有数据。
                    </p>
                    <p className="text-sm text-muted-foreground">
                      建议每三天进行一次差异备份。
                    </p>
                    <div className="flex items-center mt-4 text-sm text-muted-foreground">
                      <HardDrive className="h-4 w-4 mr-1" />
                      <span>预计大小: ~450 MB</span>
                    </div>
                    <div className="flex items-center mt-1 text-sm text-muted-foreground">
                      <Clock className="h-4 w-4 mr-1" />
                      <span>预计时间: 8-10 分钟</span>
                    </div>
                  </div>
                </CardContent>
                <CardFooter>
                  <Button
                    className="w-full"
                    onClick={() => startBackup('differential')}
                    disabled={isBackupInProgress}
                  >
                    <Save className="mr-2 h-4 w-4" />
                    开始差异备份
                  </Button>
                </CardFooter>
              </Card>
            </div>

            {isBackupInProgress && (
              <Card>
                <CardHeader>
                  <CardTitle>备份进行中</CardTitle>
                  <CardDescription>
                    正在创建系统备份，请勿关闭页面
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    <div>
                      <div className="flex justify-between mb-1">
                        <span className="text-sm font-medium">备份进度</span>
                        <span className="text-sm text-muted-foreground">{backupProgress}%</span>
                      </div>
                      <Progress value={backupProgress} className="h-2" />
                    </div>

                    <div className="flex items-center text-sm text-muted-foreground">
                      <RefreshCw className="h-4 w-4 mr-1 animate-spin" />
                      <span>正在备份系统数据，请稍候...</span>
                    </div>
                  </div>
                </CardContent>
              </Card>
            )}

            <Card>
              <CardHeader>
                <CardTitle>自定义备份</CardTitle>
                <CardDescription>
                  创建自定义备份
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div className="space-y-2">
                      <Label htmlFor="backup-name">备份名称</Label>
                      <Input id="backup-name" placeholder="输入备份名称" />
                    </div>

                    <div className="space-y-2">
                      <Label htmlFor="backup-type">备份类型</Label>
                      <Select defaultValue="full">
                        <SelectTrigger id="backup-type">
                          <SelectValue placeholder="选择备份类型" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="full">完整备份</SelectItem>
                          <SelectItem value="incremental">增量备份</SelectItem>
                          <SelectItem value="differential">差异备份</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="backup-description">备份描述</Label>
                    <Input id="backup-description" placeholder="输入备份描述（可选）" />
                  </div>

                  <div className="space-y-2">
                    <Label>备份内容</Label>
                    <div className="space-y-2">
                      <div className="flex items-center space-x-2">
                        <Checkbox id="backup-database" defaultChecked />
                        <label
                          htmlFor="backup-database"
                          className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
                        >
                          数据库
                        </label>
                      </div>
                      <div className="flex items-center space-x-2">
                        <Checkbox id="backup-uploads" defaultChecked />
                        <label
                          htmlFor="backup-uploads"
                          className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
                        >
                          上传文件
                        </label>
                      </div>
                      <div className="flex items-center space-x-2">
                        <Checkbox id="backup-configs" defaultChecked />
                        <label
                          htmlFor="backup-configs"
                          className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
                        >
                          配置文件
                        </label>
                      </div>
                    </div>
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="backup-location">存储位置</Label>
                    <Select defaultValue="local">
                      <SelectTrigger id="backup-location">
                        <SelectValue placeholder="选择存储位置" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="local">本地存储</SelectItem>
                        <SelectItem value="cloud">云存储</SelectItem>
                        <SelectItem value="remote">远程服务器</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                </div>
              </CardContent>
              <CardFooter>
                <Button
                  className="w-full"
                  onClick={() => startBackup('full')}
                  disabled={isBackupInProgress}
                >
                  <Save className="mr-2 h-4 w-4" />
                  开始自定义备份
                </Button>
              </CardFooter>
            </Card>
          </TabsContent>

          {/* 备份计划 */}
          <TabsContent value="schedule" className="space-y-6">
            <Card>
              <CardHeader>
                <div className="flex justify-between items-center">
                  <div>
                    <CardTitle>自动备份计划</CardTitle>
                    <CardDescription>
                      配置系统自动备份计划
                    </CardDescription>
                  </div>
                  <div className="flex items-center space-x-2">
                    <span className="text-sm text-muted-foreground">启用自动备份</span>
                    <Switch
                      checked={scheduleSettings.enabled}
                      onCheckedChange={(checked) => setScheduleSettings(prev => ({ ...prev, enabled: checked }))}
                    />
                  </div>
                </div>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div className="space-y-2">
                      <Label htmlFor="backup-frequency">备份频率</Label>
                      <Select
                        value={scheduleSettings.frequency}
                        onValueChange={(value: 'hourly' | 'daily' | 'weekly' | 'monthly') =>
                          setScheduleSettings(prev => ({ ...prev, frequency: value }))
                        }
                        disabled={!scheduleSettings.enabled}
                      >
                        <SelectTrigger id="backup-frequency">
                          <SelectValue placeholder="选择备份频率" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="hourly">每小时</SelectItem>
                          <SelectItem value="daily">每天</SelectItem>
                          <SelectItem value="weekly">每周</SelectItem>
                          <SelectItem value="monthly">每月</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>

                    <div className="space-y-2">
                      <Label htmlFor="backup-time">备份时间</Label>
                      <Input
                        id="backup-time"
                        type="time"
                        value={scheduleSettings.time}
                        onChange={(e) =>
                          setScheduleSettings(prev => ({ ...prev, time: e.target.value }))
                        }
                        disabled={!scheduleSettings.enabled || scheduleSettings.frequency === 'hourly'}
                      />
                    </div>
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="backup-retention">备份保留时间（天）</Label>
                    <Input
                      id="backup-retention"
                      type="number"
                      min="1"
                      max="365"
                      value={scheduleSettings.retention}
                      onChange={(e) =>
                        setScheduleSettings(prev => ({ ...prev, retention: parseInt(e.target.value) || 30 }))
                      }
                      disabled={!scheduleSettings.enabled}
                    />
                    <p className="text-xs text-muted-foreground">
                      超过保留时间的备份将被自动删除
                    </p>
                  </div>

                  <div className="space-y-2">
                    <Label>备份类型</Label>
                    <div className="space-y-4 pt-2">
                      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                        <div className="flex items-center space-x-2">
                          <input
                            type="radio"
                            id="backup-type-full"
                            name="backup-type"
                            value="full"
                            checked={scheduleSettings.backupType === 'full'}
                            onChange={() => setScheduleSettings(prev => ({ ...prev, backupType: 'full' }))}
                            disabled={!scheduleSettings.enabled}
                          />
                          <label
                            htmlFor="backup-type-full"
                            className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
                          >
                            仅完整备份
                          </label>
                        </div>

                        <div className="flex items-center space-x-2">
                          <input
                            type="radio"
                            id="backup-type-incremental"
                            name="backup-type"
                            value="incremental"
                            checked={scheduleSettings.backupType === 'incremental'}
                            onChange={() => setScheduleSettings(prev => ({ ...prev, backupType: 'incremental' }))}
                            disabled={!scheduleSettings.enabled}
                          />
                          <label
                            htmlFor="backup-type-incremental"
                            className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
                          >
                            增量备份
                          </label>
                        </div>

                        <div className="flex items-center space-x-2">
                          <input
                            type="radio"
                            id="backup-type-mixed"
                            name="backup-type"
                            value="mixed"
                            checked={scheduleSettings.backupType === 'mixed'}
                            onChange={() => setScheduleSettings(prev => ({ ...prev, backupType: 'mixed' }))}
                            disabled={!scheduleSettings.enabled}
                          />
                          <label
                            htmlFor="backup-type-mixed"
                            className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
                          >
                            混合策略
                          </label>
                        </div>
                      </div>

                      <div className="p-4 border rounded-md bg-muted/50">
                        <h4 className="text-sm font-medium mb-2">混合备份策略</h4>
                        <ul className="text-sm text-muted-foreground space-y-1 list-disc pl-4">
                          <li>每天：增量备份</li>
                          <li>每周（周日）：差异备份</li>
                          <li>每月（1日）：完整备份</li>
                        </ul>
                      </div>
                    </div>
                  </div>
                </div>
              </CardContent>
              <CardFooter>
                <Button
                  className="w-full"
                  disabled={!scheduleSettings.enabled}
                  onClick={saveBackupScheduleSettings}
                >
                  <Save className="mr-2 h-4 w-4" />
                  保存备份计划
                </Button>
              </CardFooter>
            </Card>

            <Card>
              <CardHeader>
                <div className="flex justify-between items-center">
                  <div>
                    <CardTitle>计划任务</CardTitle>
                    <CardDescription>
                      查看已计划的备份任务
                    </CardDescription>
                  </div>
                  <Button
                    variant="outline"
                    onClick={() => {
                      setSelectedSchedule(null);
                      setIsScheduleDialogOpen(true);
                    }}
                  >
                    <Calendar className="mr-2 h-4 w-4" />
                    添加计划
                  </Button>
                </div>
              </CardHeader>
              <CardContent>
                <div className="rounded-md border">
                  <Table>
                    <TableHeader>
                      <TableRow>
                        <TableHead>任务名称</TableHead>
                        <TableHead>备份类型</TableHead>
                        <TableHead>频率</TableHead>
                        <TableHead>下次执行时间</TableHead>
                        <TableHead>状态</TableHead>
                        <TableHead className="text-right">操作</TableHead>
                      </TableRow>
                    </TableHeader>
                    <TableBody>
                      {isLoadingSchedules ? (
                        <TableRow>
                          <TableCell colSpan={6} className="text-center py-8">
                            <div className="flex justify-center items-center">
                              <RefreshCw className="h-6 w-6 animate-spin mr-2" />
                              <span>加载中...</span>
                            </div>
                          </TableCell>
                        </TableRow>
                      ) : schedules.length === 0 ? (
                        <TableRow>
                          <TableCell colSpan={6} className="text-center py-8">
                            未找到备份计划
                          </TableCell>
                        </TableRow>
                      ) : (
                        schedules.map((schedule) => (
                          <TableRow key={schedule.id}>
                            <TableCell>
                              <div className="font-medium">{schedule.name}</div>
                              <div className="text-sm text-muted-foreground">自动计划任务</div>
                            </TableCell>
                            <TableCell>{getBackupTypeLabel(schedule.type)}</TableCell>
                            <TableCell>
                              {schedule.frequency === 'hourly' && '每小时'}
                              {schedule.frequency === 'daily' && '每天'}
                              {schedule.frequency === 'weekly' && `每周${schedule.dayOfWeek !== undefined ? `（周${['日', '一', '二', '三', '四', '五', '六'][schedule.dayOfWeek]}）` : ''}`}
                              {schedule.frequency === 'monthly' && `每月${schedule.dayOfMonth !== undefined ? `（${schedule.dayOfMonth}日）` : ''}`}
                            </TableCell>
                            <TableCell>
                              {new Date(schedule.nextRun).toLocaleString()}
                            </TableCell>
                            <TableCell>
                              <Badge className={schedule.status === 'enabled' ? 'bg-green-100 text-green-800' : 'bg-gray-100 text-gray-800'}>
                                {schedule.status === 'enabled' ? '已启用' : '已禁用'}
                              </Badge>
                            </TableCell>
                            <TableCell className="text-right">
                              <div className="flex justify-end space-x-1">
                                {schedule.status === 'disabled' ? (
                                  <Button
                                    variant="ghost"
                                    size="sm"
                                    onClick={() => handleUpdateSchedule(schedule.id, { status: 'enabled' })}
                                  >
                                    <Play className="h-4 w-4 text-green-500" />
                                  </Button>
                                ) : (
                                  <Button
                                    variant="ghost"
                                    size="sm"
                                    onClick={() => handleUpdateSchedule(schedule.id, { status: 'disabled' })}
                                  >
                                    <Pause className="h-4 w-4 text-yellow-500" />
                                  </Button>
                                )}
                                <Button
                                  variant="ghost"
                                  size="sm"
                                  onClick={() => {
                                    setSelectedSchedule(schedule);
                                    setIsScheduleDialogOpen(true);
                                  }}
                                >
                                  <Settings className="h-4 w-4 text-blue-500" />
                                </Button>
                                <Button
                                  variant="ghost"
                                  size="sm"
                                  onClick={() => handleDeleteSchedule(schedule.id)}
                                >
                                  <Trash2 className="h-4 w-4 text-red-500" />
                                </Button>
                              </div>
                            </TableCell>
                          </TableRow>
                        ))
                      )}
                    </TableBody>
                  </Table>
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          {/* 备份设置 */}
          <TabsContent value="settings" className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle>存储设置</CardTitle>
                <CardDescription>
                  配置备份存储设置
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div className="space-y-2">
                    <Label htmlFor="backup-location">默认存储位置</Label>
                    <Select
                      value={backupSettings.location}
                      onValueChange={(value) =>
                        setBackupSettings(prev => ({ ...prev, location: value }))
                      }
                    >
                      <SelectTrigger id="backup-location">
                        <SelectValue placeholder="选择存储位置" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="local">本地存储</SelectItem>
                        <SelectItem value="cloud">云存储</SelectItem>
                        <SelectItem value="remote">远程服务器</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>

                  {backupSettings.location === 'cloud' && (
                    <div className="space-y-4 p-4 border rounded-md">
                      <div className="space-y-2">
                        <Label htmlFor="cloud-provider">云存储提供商</Label>
                        <Select defaultValue="aws">
                          <SelectTrigger id="cloud-provider">
                            <SelectValue placeholder="选择云存储提供商" />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="aws">Amazon S3</SelectItem>
                            <SelectItem value="azure">Microsoft Azure</SelectItem>
                            <SelectItem value="gcp">Google Cloud Storage</SelectItem>
                            <SelectItem value="aliyun">阿里云 OSS</SelectItem>
                          </SelectContent>
                        </Select>
                      </div>

                      <div className="space-y-2">
                        <Label htmlFor="cloud-bucket">存储桶/容器名称</Label>
                        <Input id="cloud-bucket" placeholder="输入存储桶名称" defaultValue="system-backups" />
                      </div>

                      <div className="space-y-2">
                        <Label htmlFor="cloud-region">区域</Label>
                        <Input id="cloud-region" placeholder="输入区域" defaultValue="ap-northeast-1" />
                      </div>

                      <div className="space-y-2">
                        <Label htmlFor="cloud-access-key">访问密钥 ID</Label>
                        <Input id="cloud-access-key" type="password" placeholder="输入访问密钥 ID" defaultValue="********" />
                      </div>

                      <div className="space-y-2">
                        <Label htmlFor="cloud-secret-key">秘密访问密钥</Label>
                        <Input id="cloud-secret-key" type="password" placeholder="输入秘密访问密钥" defaultValue="********" />
                      </div>
                    </div>
                  )}

                  {backupSettings.location === 'remote' && (
                    <div className="space-y-4 p-4 border rounded-md">
                      <div className="space-y-2">
                        <Label htmlFor="remote-host">远程服务器地址</Label>
                        <Input id="remote-host" placeholder="输入服务器地址" defaultValue="backup-server.example.com" />
                      </div>

                      <div className="space-y-2">
                        <Label htmlFor="remote-port">端口</Label>
                        <Input id="remote-port" type="number" placeholder="输入端口" defaultValue="22" />
                      </div>

                      <div className="space-y-2">
                        <Label htmlFor="remote-username">用户名</Label>
                        <Input id="remote-username" placeholder="输入用户名" defaultValue="backup-user" />
                      </div>

                      <div className="space-y-2">
                        <Label htmlFor="remote-path">备份路径</Label>
                        <Input id="remote-path" placeholder="输入备份路径" defaultValue="/var/backups/system" />
                      </div>

                      <div className="space-y-2">
                        <Label>认证方式</Label>
                        <div className="grid grid-cols-2 gap-4">
                          <div className="flex items-center space-x-2">
                            <input
                              type="radio"
                              id="auth-key"
                              name="auth-method"
                              value="key"
                              defaultChecked
                            />
                            <label
                              htmlFor="auth-key"
                              className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
                            >
                              SSH 密钥
                            </label>
                          </div>

                          <div className="flex items-center space-x-2">
                            <input
                              type="radio"
                              id="auth-password"
                              name="auth-method"
                              value="password"
                            />
                            <label
                              htmlFor="auth-password"
                              className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
                            >
                              密码
                            </label>
                          </div>
                        </div>
                      </div>
                    </div>
                  )}

                  <div className="space-y-2">
                    <Label htmlFor="backup-path">本地备份路径</Label>
                    <Input
                      id="backup-path"
                      placeholder="输入本地备份路径"
                      defaultValue="/var/backups/system"
                      disabled={backupSettings.location !== 'local'}
                    />
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="disk-limit">磁盘空间限制 (GB)</Label>
                    <Input
                      id="disk-limit"
                      type="number"
                      min="1"
                      placeholder="输入磁盘空间限制"
                      defaultValue="10"
                    />
                    <p className="text-xs text-muted-foreground">
                      当备份占用空间超过此限制时，将自动删除最旧的备份
                    </p>
                  </div>
                </div>
              </CardContent>
              <CardFooter>
                <Button
                  className="w-full"
                  onClick={saveBackupSettings}
                >
                  <Save className="mr-2 h-4 w-4" />
                  保存存储设置
                </Button>
              </CardFooter>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>备份选项</CardTitle>
                <CardDescription>
                  配置备份选项和压缩设置
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div className="space-y-2">
                    <Label htmlFor="compression-level">压缩级别</Label>
                    <Select
                      value={backupSettings.compressionLevel}
                      onValueChange={(value) =>
                        setBackupSettings(prev => ({ ...prev, compressionLevel: value }))
                      }
                    >
                      <SelectTrigger id="compression-level">
                        <SelectValue placeholder="选择压缩级别" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="none">不压缩</SelectItem>
                        <SelectItem value="low">低 (快速)</SelectItem>
                        <SelectItem value="medium">中等 (平衡)</SelectItem>
                        <SelectItem value="high">高 (节省空间)</SelectItem>
                      </SelectContent>
                    </Select>
                    <p className="text-xs text-muted-foreground">
                      较高的压缩级别可以节省存储空间，但会增加备份和恢复时间
                    </p>
                  </div>

                  <div className="space-y-2">
                    <Label>备份内容</Label>
                    <div className="space-y-2">
                      <div className="flex items-center justify-between">
                        <div className="flex items-center space-x-2">
                          <Checkbox
                            id="include-database"
                            checked={backupSettings.includeDatabase}
                            onCheckedChange={(checked) =>
                              setBackupSettings(prev => ({ ...prev, includeDatabase: !!checked }))
                            }
                          />
                          <label
                            htmlFor="include-database"
                            className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
                          >
                            数据库
                          </label>
                        </div>
                        <span className="text-xs text-muted-foreground">~800 MB</span>
                      </div>

                      <div className="flex items-center justify-between">
                        <div className="flex items-center space-x-2">
                          <Checkbox
                            id="include-uploads"
                            checked={backupSettings.includeUploads}
                            onCheckedChange={(checked) =>
                              setBackupSettings(prev => ({ ...prev, includeUploads: !!checked }))
                            }
                          />
                          <label
                            htmlFor="include-uploads"
                            className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
                          >
                            上传文件
                          </label>
                        </div>
                        <span className="text-xs text-muted-foreground">~400 MB</span>
                      </div>

                      <div className="flex items-center justify-between">
                        <div className="flex items-center space-x-2">
                          <Checkbox
                            id="include-configs"
                            checked={backupSettings.includeConfigs}
                            onCheckedChange={(checked) =>
                              setBackupSettings(prev => ({ ...prev, includeConfigs: !!checked }))
                            }
                          />
                          <label
                            htmlFor="include-configs"
                            className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
                          >
                            配置文件
                          </label>
                        </div>
                        <span className="text-xs text-muted-foreground">~5 MB</span>
                      </div>
                    </div>
                  </div>

                  <div className="space-y-2">
                    <Label>加密选项</Label>
                    <div className="flex items-center space-x-2">
                      <Checkbox
                        id="encrypt-backup"
                        onCheckedChange={(checked) => {
                          const passwordInput = document.getElementById('encryption-password') as HTMLInputElement;
                          if (passwordInput) {
                            passwordInput.disabled = !checked;
                            if (!checked) passwordInput.value = '';
                          }
                        }}
                      />
                      <label
                        htmlFor="encrypt-backup"
                        className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
                      >
                        加密备份
                      </label>
                    </div>
                    <p className="text-xs text-muted-foreground">
                      使用 AES-256 加密备份文件，确保数据安全
                    </p>
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="encryption-password">加密密码</Label>
                    <Input
                      id="encryption-password"
                      type="password"
                      placeholder="输入加密密码"
                      disabled={true}
                    />
                    <p className="text-xs text-muted-foreground">
                      请妥善保管加密密码，丢失密码将无法恢复备份数据
                    </p>
                  </div>
                </div>
              </CardContent>
              <CardFooter>
                <Button
                  className="w-full"
                  onClick={saveBackupSettings}
                >
                  <Save className="mr-2 h-4 w-4" />
                  保存备份选项
                </Button>
              </CardFooter>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>高级设置</CardTitle>
                <CardDescription>
                  配置备份保留策略、命名格式和通知设置
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div className="space-y-2">
                    <Label>备份保留策略</Label>
                    <div className="grid grid-cols-2 gap-4">
                      <div className="flex items-center space-x-2">
                        <input
                          type="radio"
                          id="retention-count"
                          name="retention-policy"
                          value="count"
                          defaultChecked
                          onChange={() => {
                            const countInput = document.getElementById('retention-count-input') as HTMLInputElement;
                            const daysInput = document.getElementById('retention-days-input') as HTMLInputElement;
                            if (countInput && daysInput) {
                              countInput.disabled = false;
                              daysInput.disabled = true;
                            }
                          }}
                        />
                        <label
                          htmlFor="retention-count"
                          className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
                        >
                          按数量保留
                        </label>
                      </div>

                      <div className="flex items-center space-x-2">
                        <input
                          type="radio"
                          id="retention-days"
                          name="retention-policy"
                          value="days"
                          onChange={() => {
                            const countInput = document.getElementById('retention-count-input') as HTMLInputElement;
                            const daysInput = document.getElementById('retention-days-input') as HTMLInputElement;
                            if (countInput && daysInput) {
                              countInput.disabled = true;
                              daysInput.disabled = false;
                            }
                          }}
                        />
                        <label
                          htmlFor="retention-days"
                          className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
                        >
                          按天数保留
                        </label>
                      </div>
                    </div>
                  </div>

                  <div className="grid grid-cols-2 gap-4">
                    <div className="space-y-2">
                      <Label htmlFor="retention-count-input">保留备份数量</Label>
                      <Input
                        id="retention-count-input"
                        type="number"
                        min="1"
                        max="100"
                        defaultValue="10"
                      />
                    </div>

                    <div className="space-y-2">
                      <Label htmlFor="retention-days-input">保留天数</Label>
                      <Input
                        id="retention-days-input"
                        type="number"
                        min="1"
                        max="365"
                        defaultValue="30"
                        disabled
                      />
                    </div>
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="backup-naming-format">备份命名格式</Label>
                    <Input
                      id="backup-naming-format"
                      placeholder="输入命名格式"
                      defaultValue="{type}_{date}_{time}"
                    />
                    <p className="text-xs text-muted-foreground">
                      可用变量: {'{type}'} - 备份类型, {'{date}'} - 日期, {'{time}'} - 时间, {'{id}'} - 唯一ID
                    </p>
                  </div>

                  <div className="space-y-2">
                    <Label>备份验证</Label>
                    <div className="flex items-center space-x-2">
                      <Checkbox id="verify-backup" defaultChecked />
                      <label
                        htmlFor="verify-backup"
                        className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
                      >
                        创建后验证备份完整性
                      </label>
                    </div>
                    <p className="text-xs text-muted-foreground">
                      验证备份文件的完整性，确保备份可以正确恢复
                    </p>
                  </div>

                  <div className="space-y-2">
                    <Label>备份通知</Label>
                    <div className="space-y-2">
                      <div className="flex items-center space-x-2">
                        <Checkbox id="notify-success" defaultChecked />
                        <label
                          htmlFor="notify-success"
                          className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
                        >
                          备份成功时通知
                        </label>
                      </div>

                      <div className="flex items-center space-x-2">
                        <Checkbox id="notify-failure" defaultChecked />
                        <label
                          htmlFor="notify-failure"
                          className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
                        >
                          备份失败时通知
                        </label>
                      </div>
                    </div>
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="notify-email">通知邮箱</Label>
                    <Input
                      id="notify-email"
                      type="email"
                      placeholder="输入通知邮箱"
                      defaultValue=""
                    />
                    <p className="text-xs text-muted-foreground">
                      留空则使用系统管理员邮箱
                    </p>
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="max-parallel-uploads">最大并行上传数</Label>
                    <Input
                      id="max-parallel-uploads"
                      type="number"
                      min="1"
                      max="10"
                      defaultValue="3"
                    />
                    <p className="text-xs text-muted-foreground">
                      备份上传到远程存储时的最大并行上传数
                    </p>
                  </div>
                </div>
              </CardContent>
              <CardFooter>
                <Button
                  className="w-full"
                  onClick={() => {
                    // 获取高级设置
                    const retentionPolicy = document.querySelector('input[name="retention-policy"]:checked') as HTMLInputElement;
                    const retentionCount = document.getElementById('retention-count-input') as HTMLInputElement;
                    const retentionDays = document.getElementById('retention-days-input') as HTMLInputElement;
                    const namingFormat = document.getElementById('backup-naming-format') as HTMLInputElement;
                    const verifyBackup = document.getElementById('verify-backup') as HTMLInputElement;
                    const notifySuccess = document.getElementById('notify-success') as HTMLInputElement;
                    const notifyFailure = document.getElementById('notify-failure') as HTMLInputElement;
                    const notifyEmail = document.getElementById('notify-email') as HTMLInputElement;
                    const maxParallelUploads = document.getElementById('max-parallel-uploads') as HTMLInputElement;

                    // 更新备份设置
                    const updatedSettings = {
                      ...backupSettings,
                      retentionPolicy: retentionPolicy?.value || 'count',
                      retentionCount: parseInt(retentionCount?.value || '10'),
                      retentionDays: parseInt(retentionDays?.value || '30'),
                      namingFormat: namingFormat?.value || '{type}_{date}_{time}',
                      verifyBackup: verifyBackup?.checked || true,
                      notifyOnSuccess: notifySuccess?.checked || true,
                      notifyOnFailure: notifyFailure?.checked || true,
                      notifyEmail: notifyEmail?.value || '',
                      maxParallelUploads: parseInt(maxParallelUploads?.value || '3')
                    };

                    // 保存设置
                    setBackupSettings(updatedSettings);

                    toast({
                      title: '设置已保存',
                      description: '高级备份设置已成功保存'
                    });
                  }}
                >
                  <Save className="mr-2 h-4 w-4" />
                  保存高级设置
                </Button>
              </CardFooter>
            </Card>
          </TabsContent>
        </Tabs>

        {/* 恢复备份对话框 */}
        <Dialog open={isRestoreDialogOpen} onOpenChange={setIsRestoreDialogOpen}>
          <DialogContent>
            {selectedBackup && (
              <>
                <DialogHeader>
                  <DialogTitle>恢复备份</DialogTitle>
                  <DialogDescription>
                    您确定要恢复此备份吗？这将覆盖当前系统数据。
                  </DialogDescription>
                </DialogHeader>

                <div className="py-4">
                  <div className="space-y-3">
                    <div className="grid grid-cols-3 gap-2">
                      <span className="font-medium">备份名称:</span>
                      <span className="col-span-2">{selectedBackup.name}</span>
                    </div>

                    <div className="grid grid-cols-3 gap-2">
                      <span className="font-medium">备份类型:</span>
                      <span className="col-span-2">{getBackupTypeLabel(selectedBackup.type)}</span>
                    </div>

                    <div className="grid grid-cols-3 gap-2">
                      <span className="font-medium">创建时间:</span>
                      <span className="col-span-2">{selectedBackup.createdAt}</span>
                    </div>

                    <div className="grid grid-cols-3 gap-2">
                      <span className="font-medium">大小:</span>
                      <span className="col-span-2">{selectedBackup.size}</span>
                    </div>
                  </div>

                  <div className="mt-4 p-3 bg-yellow-50 border border-yellow-200 rounded-md">
                    <div className="flex items-start">
                      <AlertCircle className="h-5 w-5 text-yellow-500 mr-2 mt-0.5" />
                      <div>
                        <h4 className="font-medium text-yellow-800">警告</h4>
                        <p className="text-sm text-yellow-700">
                          恢复操作将覆盖当前系统的所有数据。此操作不可撤销。请确保您已了解恢复的影响。
                        </p>
                      </div>
                    </div>
                  </div>
                </div>

                <DialogFooter>
                  <Button variant="outline" onClick={() => setIsRestoreDialogOpen(false)}>
                    取消
                  </Button>
                  <Button
                    variant="default"
                    onClick={() => handleRestoreBackup(selectedBackup)}
                    disabled={isBackupInProgress}
                  >
                    {isBackupInProgress ? (
                      <>
                        <RefreshCw className="mr-2 h-4 w-4 animate-spin" />
                        恢复中...
                      </>
                    ) : (
                      <>
                        <Upload className="mr-2 h-4 w-4" />
                        确认恢复
                      </>
                    )}
                  </Button>
                </DialogFooter>
              </>
            )}
          </DialogContent>
        </Dialog>

        {/* 删除备份对话框 */}
        <Dialog open={isDeleteDialogOpen} onOpenChange={setIsDeleteDialogOpen}>
          <DialogContent>
            {selectedBackup && (
              <>
                <DialogHeader>
                  <DialogTitle>删除备份</DialogTitle>
                  <DialogDescription>
                    您确定要删除此备份吗？此操作不可撤销。
                  </DialogDescription>
                </DialogHeader>

                <div className="py-4">
                  <div className="space-y-3">
                    <div className="grid grid-cols-3 gap-2">
                      <span className="font-medium">备份名称:</span>
                      <span className="col-span-2">{selectedBackup.name}</span>
                    </div>

                    <div className="grid grid-cols-3 gap-2">
                      <span className="font-medium">备份类型:</span>
                      <span className="col-span-2">{getBackupTypeLabel(selectedBackup.type)}</span>
                    </div>

                    <div className="grid grid-cols-3 gap-2">
                      <span className="font-medium">创建时间:</span>
                      <span className="col-span-2">{selectedBackup.createdAt}</span>
                    </div>

                    <div className="grid grid-cols-3 gap-2">
                      <span className="font-medium">大小:</span>
                      <span className="col-span-2">{selectedBackup.size}</span>
                    </div>
                  </div>
                </div>

                <DialogFooter>
                  <Button variant="outline" onClick={() => setIsDeleteDialogOpen(false)}>
                    取消
                  </Button>
                  <Button
                    variant="destructive"
                    onClick={() => handleDeleteBackup(selectedBackup)}
                    disabled={isLoading}
                  >
                    {isLoading ? (
                      <>
                        <RefreshCw className="mr-2 h-4 w-4 animate-spin" />
                        删除中...
                      </>
                    ) : (
                      <>
                        <Trash2 className="mr-2 h-4 w-4" />
                        确认删除
                      </>
                    )}
                  </Button>
                </DialogFooter>
              </>
            )}
          </DialogContent>
        </Dialog>

        {/* 备份计划对话框 */}
        <Dialog open={isScheduleDialogOpen} onOpenChange={setIsScheduleDialogOpen}>
          <DialogContent>
            <DialogHeader>
              <DialogTitle>{selectedSchedule ? '编辑备份计划' : '创建备份计划'}</DialogTitle>
              <DialogDescription>
                {selectedSchedule ? '修改现有备份计划的设置' : '创建新的自动备份计划'}
              </DialogDescription>
            </DialogHeader>
            <div className="py-4 space-y-4">
              <div className="space-y-2">
                <Label htmlFor="schedule-name">计划名称</Label>
                <Input
                  id="schedule-name"
                  placeholder="输入计划名称"
                  defaultValue={selectedSchedule?.name || ''}
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="schedule-type">备份类型</Label>
                <Select defaultValue={selectedSchedule?.type || 'full'}>
                  <SelectTrigger id="schedule-type">
                    <SelectValue placeholder="选择备份类型" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="full">完整备份</SelectItem>
                    <SelectItem value="incremental">增量备份</SelectItem>
                    <SelectItem value="differential">差异备份</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              <div className="space-y-2">
                <Label htmlFor="schedule-frequency">执行频率</Label>
                <Select defaultValue={selectedSchedule?.frequency || 'daily'}>
                  <SelectTrigger id="schedule-frequency">
                    <SelectValue placeholder="选择执行频率" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="hourly">每小时</SelectItem>
                    <SelectItem value="daily">每天</SelectItem>
                    <SelectItem value="weekly">每周</SelectItem>
                    <SelectItem value="monthly">每月</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              <div className="space-y-2">
                <Label htmlFor="schedule-time">执行时间</Label>
                <Input
                  id="schedule-time"
                  type="time"
                  defaultValue={selectedSchedule?.time || '02:00'}
                />
              </div>
              <div className="flex items-center space-x-2">
                <Checkbox
                  id="schedule-enabled"
                  defaultChecked={selectedSchedule?.status === 'enabled'}
                />
                <label
                  htmlFor="schedule-enabled"
                  className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
                >
                  启用此计划
                </label>
              </div>
            </div>
            <DialogFooter>
              <Button
                variant="outline"
                onClick={() => setIsScheduleDialogOpen(false)}
              >
                取消
              </Button>
              <Button
                variant="default"
                onClick={() => {
                  // 模拟创建或更新计划
                  if (selectedSchedule) {
                    handleUpdateSchedule(selectedSchedule.id, {
                      name: (document.getElementById('schedule-name') as HTMLInputElement).value,
                      type: (document.querySelector('#schedule-type [data-value]')?.getAttribute('data-value') || 'full') as 'full' | 'incremental' | 'differential',
                      frequency: (document.querySelector('#schedule-frequency [data-value]')?.getAttribute('data-value') || 'daily') as 'hourly' | 'daily' | 'weekly' | 'monthly',
                      time: (document.getElementById('schedule-time') as HTMLInputElement).value,
                      status: (document.getElementById('schedule-enabled') as HTMLInputElement).checked ? 'enabled' : 'disabled'
                    });
                  } else {
                    handleCreateSchedule({
                      name: (document.getElementById('schedule-name') as HTMLInputElement).value,
                      type: (document.querySelector('#schedule-type [data-value]')?.getAttribute('data-value') || 'full') as 'full' | 'incremental' | 'differential',
                      frequency: (document.querySelector('#schedule-frequency [data-value]')?.getAttribute('data-value') || 'daily') as 'hourly' | 'daily' | 'weekly' | 'monthly',
                      time: (document.getElementById('schedule-time') as HTMLInputElement).value,
                      status: (document.getElementById('schedule-enabled') as HTMLInputElement).checked ? 'enabled' : 'disabled'
                    });
                  }
                  setIsScheduleDialogOpen(false);
                }}
              >
                {selectedSchedule ? '更新' : '创建'}
              </Button>
            </DialogFooter>
          </DialogContent>
        </Dialog>
      </div>
    </SuperAdminLayout>
  );
};

export default SystemBackupPage;
