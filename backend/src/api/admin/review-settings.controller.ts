/**
 * 审核设置控制器
 */

import { Context } from 'hono';
import { PrismaClient } from '@prisma/client';
import { ReviewService, ReviewLevel } from '../../services/reviewService';
import { Env } from '../../types';

/**
 * 获取审核设置
 */
export const getReviewSettings = async (c: Context<{ Bindings: Env }>) => {
  try {
    // 获取审核服务实例
    const reviewService = ReviewService.getInstance();

    // 确保审核服务已初始化
    if (!reviewService.isInitialized) {
      await reviewService.initialize();
    }

    // 获取当前审核等级
    const reviewLevel = reviewService.getReviewLevel();

    // 获取其他设置
    const prisma = new PrismaClient();
    const settings = await prisma.systemSetting.findMany({
      where: {
        key: {
          startsWith: 'review_'
        }
      }
    });

    // 构建设置对象
    const settingsObj: Record<string, any> = {
      reviewLevel
    };

    // 添加其他设置
    settings.forEach(setting => {
      const key = setting.key.replace('review_', '');
      settingsObj[key] = setting.value;
    });

    await prisma.$disconnect();

    // 返回结果
    return c.json({
      success: true,
      settings: settingsObj
    });
  } catch (error) {
    console.error('Error getting review settings:', error);
    return c.json({
      success: false,
      error: '获取审核设置失败'
    }, 500);
  }
};

/**
 * 更新审核设置
 */
export const updateReviewSettings = async (c: Context<{ Bindings: Env }>) => {
  try {
    // 获取请求数据
    const data = await c.req.json();

    // 获取审核员信息
    const user = c.get('user');
    const userId = user.username || user.id || 'unknown';

    // 获取审核服务实例
    const reviewService = ReviewService.getInstance();

    // 确保审核服务已初始化
    if (!reviewService.isInitialized) {
      await reviewService.initialize();
    }

    // 更新审核等级
    if (data.reviewLevel) {
      // 验证审核等级
      if (!Object.values(ReviewLevel).includes(data.reviewLevel)) {
        return c.json({
          success: false,
          error: '无效的审核等级'
        }, 400);
      }

      // 更新审核等级
      await reviewService.setReviewLevel(data.reviewLevel, userId);
    }

    // 更新其他设置
    const prisma = new PrismaClient();

    // 获取要更新的设置
    const settingsToUpdate = Object.entries(data)
      .filter(([key]) => key !== 'reviewLevel')
      .map(([key, value]) => ({
        key: `review_${key}`,
        value: String(value)
      }));

    // 更新设置
    for (const setting of settingsToUpdate) {
      await prisma.systemSetting.upsert({
        where: {
          key: setting.key
        },
        update: {
          value: setting.value,
          updatedBy: userId,
          updatedAt: new Date()
        },
        create: {
          key: setting.key,
          value: setting.value,
          description: `审核设置: ${setting.key}`,
          updatedBy: userId
        }
      });
    }

    await prisma.$disconnect();

    // 返回结果
    return c.json({
      success: true,
      message: '审核设置已更新'
    });
  } catch (error) {
    console.error('Error updating review settings:', error);
    return c.json({
      success: false,
      error: '更新审核设置失败'
    }, 500);
  }
};

/**
 * 获取审核统计信息
 */
export const getReviewStats = async (c: Context<{ Bindings: Env }>) => {
  try {
    const prisma = new PrismaClient();

    // 获取待审核内容数量
    const pendingCount = await prisma.pendingContent.count({
      where: {
        status: 'pending'
      }
    });

    // 获取已审核内容数量
    const approvedCount = await prisma.pendingContent.count({
      where: {
        status: 'approved'
      }
    });

    // 获取已拒绝内容数量
    const rejectedCount = await prisma.pendingContent.count({
      where: {
        status: 'rejected'
      }
    });

    // 获取已编辑内容数量
    const editedCount = await prisma.pendingContent.count({
      where: {
        status: 'edited'
      }
    });

    // 获取总内容数量
    const totalCount = await prisma.pendingContent.count();

    // 获取按类型分组的内容数量
    const typeStats = await prisma.$queryRaw`
      SELECT type, COUNT(*) as count
      FROM PendingContent
      GROUP BY type
    `;

    // 获取按标记分组的内容数量
    const flagStats = await prisma.$queryRaw`
      SELECT flags, COUNT(*) as count
      FROM PendingContent
      GROUP BY flags
    `;

    // 获取最近7天的审核数量
    const last7Days = new Date();
    last7Days.setDate(last7Days.getDate() - 7);

    const dailyStats = await prisma.$queryRaw`
      SELECT DATE(reviewedAt) as date, COUNT(*) as count
      FROM PendingContent
      WHERE reviewedAt >= ${last7Days}
      GROUP BY DATE(reviewedAt)
      ORDER BY date ASC
    `;

    await prisma.$disconnect();

    // 返回结果
    return c.json({
      success: true,
      stats: {
        pending: pendingCount,
        approved: approvedCount,
        rejected: rejectedCount,
        edited: editedCount,
        total: totalCount,
        byType: typeStats,
        byFlag: flagStats,
        daily: dailyStats
      }
    });
  } catch (error) {
    console.error('Error getting review stats:', error);
    return c.json({
      success: false,
      error: '获取审核统计信息失败'
    }, 500);
  }
};

/**
 * 获取审核员绩效统计
 */
export const getReviewerPerformance = async (c: Context<{ Bindings: Env }>) => {
  try {
    // 返回模拟数据，因为在Cloudflare Workers环境中
    return c.json({
      success: true,
      performance: {
        totalReviews: 0,
        averageTime: 0,
        accuracy: 0,
        productivity: 0
      }
    });
  } catch (error) {
    console.error('Error getting reviewer performance:', error);
    return c.json({
      success: false,
      error: '获取审核员绩效失败'
    }, 500);
  }
};