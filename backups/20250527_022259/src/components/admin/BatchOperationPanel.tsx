import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { But<PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Badge } from '@/components/ui/badge';
import { Checkbox } from '@/components/ui/checkbox';
import { Progress } from '@/components/ui/progress';
import { Separator } from '@/components/ui/separator';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import { 
  CheckSquare, 
  Square, 
  Play, 
  Pause, 
  X, 
  Alert<PERSON>riangle,
  CheckCircle,
  Clock,
  Download,
  Trash2,
  Tag,
  User,
  Edit,
  FileText,
  BarChart3
} from 'lucide-react';
import { useToast } from '@/components/ui/use-toast';
import {
  BatchOperation,
  BatchOperationResult,
  executeBatchOperation,
  getBatchOperationStatus,
  getBatchOperationHistory,
  cancelBatchOperation,
  type PendingContent
} from '@/services/contentManagementService';

interface BatchOperationPanelProps {
  selectedItems: PendingContent[];
  onSelectionChange: (items: PendingContent[]) => void;
  allItems: PendingContent[];
  onOperationComplete: () => void;
}

/**
 * 批量操作面板组件
 * 
 * 提供强大的批量操作功能，包括：
 * - 批量选择和管理
 * - 多种批量操作类型
 * - 操作进度监控
 * - 操作历史记录
 */
const BatchOperationPanel: React.FC<BatchOperationPanelProps> = ({
  selectedItems,
  onSelectionChange,
  allItems,
  onOperationComplete
}) => {
  const { toast } = useToast();
  const [isOperationDialogOpen, setIsOperationDialogOpen] = useState(false);
  const [selectedOperation, setSelectedOperation] = useState<BatchOperation['action'] | ''>('');
  const [operationData, setOperationData] = useState<any>({});
  const [currentOperation, setCurrentOperation] = useState<BatchOperationResult | null>(null);
  const [operationHistory, setOperationHistory] = useState<BatchOperationResult[]>([]);
  const [isMonitoring, setIsMonitoring] = useState(false);

  // 批量操作选项
  const operationOptions = [
    { value: 'approve', label: '批量通过', icon: CheckCircle, description: '批量审核通过选中的内容' },
    { value: 'reject', label: '批量拒绝', icon: X, description: '批量拒绝选中的内容' },
    { value: 'assign', label: '批量分配', icon: User, description: '将选中内容分配给指定审核员' },
    { value: 'tag', label: '批量标签', icon: Tag, description: '为选中内容添加或移除标签' },
    { value: 'priority', label: '批量优先级', icon: BarChart3, description: '修改选中内容的优先级' },
    { value: 'export', label: '批量导出', icon: Download, description: '导出选中的内容' },
    { value: 'delete', label: '批量删除', icon: Trash2, description: '删除选中的内容（谨慎操作）' }
  ];

  // 加载操作历史
  useEffect(() => {
    const loadOperationHistory = async () => {
      try {
        const response = await getBatchOperationHistory({ page: 1, pageSize: 10 });
        if (response.success) {
          setOperationHistory(response.data.operations);
        }
      } catch (error) {
        console.error('加载操作历史失败:', error);
      }
    };

    loadOperationHistory();
  }, []);

  // 监控当前操作进度
  useEffect(() => {
    let interval: NodeJS.Timeout;

    if (isMonitoring && currentOperation) {
      interval = setInterval(async () => {
        try {
          const response = await getBatchOperationStatus(currentOperation.operationId);
          if (response.success) {
            setCurrentOperation(response.data);
            
            if (response.data.status === 'completed' || response.data.status === 'failed') {
              setIsMonitoring(false);
              onOperationComplete();
              
              // 重新加载操作历史
              const historyResponse = await getBatchOperationHistory({ page: 1, pageSize: 10 });
              if (historyResponse.success) {
                setOperationHistory(historyResponse.data.operations);
              }
            }
          }
        } catch (error) {
          console.error('获取操作状态失败:', error);
          setIsMonitoring(false);
        }
      }, 2000);
    }

    return () => {
      if (interval) clearInterval(interval);
    };
  }, [isMonitoring, currentOperation, onOperationComplete]);

  // 全选/取消全选
  const handleSelectAll = (checked: boolean) => {
    if (checked) {
      onSelectionChange(allItems);
    } else {
      onSelectionChange([]);
    }
  };

  // 选择/取消选择单个项目
  const handleSelectItem = (item: PendingContent, checked: boolean) => {
    if (checked) {
      onSelectionChange([...selectedItems, item]);
    } else {
      onSelectionChange(selectedItems.filter(i => i.id !== item.id));
    }
  };

  // 执行批量操作
  const handleExecuteOperation = async () => {
    if (!selectedOperation || selectedItems.length === 0) {
      toast({
        title: '操作失败',
        description: '请选择操作类型和要操作的项目',
        variant: 'destructive',
      });
      return;
    }

    try {
      const operation: BatchOperation = {
        action: selectedOperation,
        itemIds: selectedItems.map(item => item.id),
        data: operationData
      };

      const response = await executeBatchOperation(operation);
      
      if (response.success) {
        setCurrentOperation(response.data);
        setIsMonitoring(true);
        setIsOperationDialogOpen(false);
        
        toast({
          title: '操作已开始',
          description: `正在执行批量${operationOptions.find(op => op.value === selectedOperation)?.label}操作`,
        });
      } else {
        throw new Error(response.error || '执行批量操作失败');
      }
    } catch (error) {
      console.error('执行批量操作失败:', error);
      toast({
        title: '操作失败',
        description: error instanceof Error ? error.message : '执行批量操作时发生错误',
        variant: 'destructive',
      });
    }
  };

  // 取消当前操作
  const handleCancelOperation = async () => {
    if (!currentOperation) return;

    try {
      await cancelBatchOperation(currentOperation.operationId);
      setIsMonitoring(false);
      setCurrentOperation(null);
      
      toast({
        title: '操作已取消',
        description: '批量操作已成功取消',
      });
    } catch (error) {
      toast({
        title: '取消失败',
        description: '取消批量操作时发生错误',
        variant: 'destructive',
      });
    }
  };

  // 渲染操作数据输入
  const renderOperationDataInput = () => {
    switch (selectedOperation) {
      case 'approve':
      case 'reject':
        return (
          <div className="space-y-4">
            <div>
              <Label htmlFor="reviewNotes">审核备注</Label>
              <Textarea
                id="reviewNotes"
                placeholder="输入审核备注（可选）"
                value={operationData.reviewNotes || ''}
                onChange={(e) => setOperationData({ ...operationData, reviewNotes: e.target.value })}
              />
            </div>
            {selectedOperation === 'reject' && (
              <div>
                <Label htmlFor="reason">拒绝原因</Label>
                <Textarea
                  id="reason"
                  placeholder="输入拒绝原因"
                  value={operationData.reason || ''}
                  onChange={(e) => setOperationData({ ...operationData, reason: e.target.value })}
                  required
                />
              </div>
            )}
          </div>
        );

      case 'assign':
        return (
          <div>
            <Label htmlFor="assigneeId">分配给审核员</Label>
            <Input
              id="assigneeId"
              placeholder="输入审核员ID"
              value={operationData.assigneeId || ''}
              onChange={(e) => setOperationData({ ...operationData, assigneeId: e.target.value })}
              required
            />
          </div>
        );

      case 'tag':
        return (
          <div className="space-y-4">
            <div>
              <Label htmlFor="addTags">添加标签</Label>
              <Input
                id="addTags"
                placeholder="输入要添加的标签，用逗号分隔"
                value={operationData.addTags?.join(',') || ''}
                onChange={(e) => {
                  const tags = e.target.value.split(',').map(tag => tag.trim()).filter(Boolean);
                  setOperationData({ ...operationData, addTags: tags });
                }}
              />
            </div>
            <div>
              <Label htmlFor="removeTags">移除标签</Label>
              <Input
                id="removeTags"
                placeholder="输入要移除的标签，用逗号分隔"
                value={operationData.removeTags?.join(',') || ''}
                onChange={(e) => {
                  const tags = e.target.value.split(',').map(tag => tag.trim()).filter(Boolean);
                  setOperationData({ ...operationData, removeTags: tags });
                }}
              />
            </div>
          </div>
        );

      case 'priority':
        return (
          <div>
            <Label htmlFor="priority">优先级</Label>
            <Select 
              value={operationData.priority?.toString() || ''} 
              onValueChange={(value) => setOperationData({ ...operationData, priority: parseInt(value) })}
            >
              <SelectTrigger>
                <SelectValue placeholder="选择优先级" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="1">低优先级</SelectItem>
                <SelectItem value="2">普通</SelectItem>
                <SelectItem value="3">中等</SelectItem>
                <SelectItem value="4">高优先级</SelectItem>
                <SelectItem value="5">紧急</SelectItem>
              </SelectContent>
            </Select>
          </div>
        );

      case 'export':
        return (
          <div className="space-y-4">
            <div>
              <Label htmlFor="format">导出格式</Label>
              <Select 
                value={operationData.format || ''} 
                onValueChange={(value) => setOperationData({ ...operationData, format: value })}
              >
                <SelectTrigger>
                  <SelectValue placeholder="选择导出格式" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="csv">CSV</SelectItem>
                  <SelectItem value="xlsx">Excel</SelectItem>
                  <SelectItem value="json">JSON</SelectItem>
                  <SelectItem value="pdf">PDF</SelectItem>
                </SelectContent>
              </Select>
            </div>
            <div>
              <Label htmlFor="fields">导出字段</Label>
              <Input
                id="fields"
                placeholder="输入要导出的字段，用逗号分隔（留空导出全部）"
                value={operationData.fields?.join(',') || ''}
                onChange={(e) => {
                  const fields = e.target.value.split(',').map(field => field.trim()).filter(Boolean);
                  setOperationData({ ...operationData, fields: fields.length > 0 ? fields : undefined });
                }}
              />
            </div>
          </div>
        );

      default:
        return null;
    }
  };

  const isAllSelected = allItems.length > 0 && selectedItems.length === allItems.length;
  const isPartiallySelected = selectedItems.length > 0 && selectedItems.length < allItems.length;

  return (
    <div className="space-y-4">
      {/* 选择控制 */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <CheckSquare className="h-5 w-5" />
            批量操作
          </CardTitle>
          <CardDescription>
            选择多个项目进行批量操作
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-4">
              <div className="flex items-center space-x-2">
                <Checkbox
                  id="select-all"
                  checked={isAllSelected}
                  ref={(el) => {
                    if (el) el.indeterminate = isPartiallySelected;
                  }}
                  onCheckedChange={handleSelectAll}
                />
                <Label htmlFor="select-all" className="text-sm">
                  全选 ({selectedItems.length}/{allItems.length})
                </Label>
              </div>
              
              {selectedItems.length > 0 && (
                <Badge variant="secondary">
                  已选择 {selectedItems.length} 个项目
                </Badge>
              )}
            </div>

            {selectedItems.length > 0 && (
              <Dialog open={isOperationDialogOpen} onOpenChange={setIsOperationDialogOpen}>
                <DialogTrigger asChild>
                  <Button>
                    <Play className="h-4 w-4 mr-2" />
                    执行批量操作
                  </Button>
                </DialogTrigger>
                <DialogContent className="max-w-2xl">
                  <DialogHeader>
                    <DialogTitle>批量操作</DialogTitle>
                    <DialogDescription>
                      对选中的 {selectedItems.length} 个项目执行批量操作
                    </DialogDescription>
                  </DialogHeader>
                  
                  <div className="space-y-6">
                    {/* 操作类型选择 */}
                    <div className="space-y-3">
                      <Label>选择操作类型</Label>
                      <div className="grid grid-cols-2 gap-3">
                        {operationOptions.map((option) => {
                          const Icon = option.icon;
                          return (
                            <div
                              key={option.value}
                              className={`p-3 border rounded-lg cursor-pointer transition-colors ${
                                selectedOperation === option.value
                                  ? 'border-blue-500 bg-blue-50'
                                  : 'border-gray-200 hover:border-gray-300'
                              }`}
                              onClick={() => {
                                setSelectedOperation(option.value);
                                setOperationData({});
                              }}
                            >
                              <div className="flex items-center gap-2">
                                <Icon className="h-4 w-4" />
                                <span className="font-medium">{option.label}</span>
                              </div>
                              <p className="text-xs text-gray-500 mt-1">{option.description}</p>
                            </div>
                          );
                        })}
                      </div>
                    </div>

                    {/* 操作参数输入 */}
                    {selectedOperation && (
                      <div className="space-y-3">
                        <Label>操作参数</Label>
                        {renderOperationDataInput()}
                      </div>
                    )}

                    {/* 警告信息 */}
                    {(selectedOperation === 'delete' || selectedOperation === 'reject') && (
                      <Alert>
                        <AlertTriangle className="h-4 w-4" />
                        <AlertTitle>警告</AlertTitle>
                        <AlertDescription>
                          此操作不可撤销，请确认您要{selectedOperation === 'delete' ? '删除' : '拒绝'}选中的 {selectedItems.length} 个项目。
                        </AlertDescription>
                      </Alert>
                    )}
                  </div>

                  <DialogFooter>
                    <Button variant="outline" onClick={() => setIsOperationDialogOpen(false)}>
                      取消
                    </Button>
                    <Button 
                      onClick={handleExecuteOperation}
                      disabled={!selectedOperation}
                      variant={selectedOperation === 'delete' ? 'destructive' : 'default'}
                    >
                      执行操作
                    </Button>
                  </DialogFooter>
                </DialogContent>
              </Dialog>
            )}
          </div>
        </CardContent>
      </Card>

      {/* 当前操作进度 */}
      {currentOperation && (
        <Card>
          <CardHeader>
            <div className="flex items-center justify-between">
              <CardTitle className="flex items-center gap-2">
                <Clock className="h-5 w-5" />
                操作进度
              </CardTitle>
              {isMonitoring && currentOperation.status === 'processing' && (
                <Button variant="outline" size="sm" onClick={handleCancelOperation}>
                  <Pause className="h-4 w-4 mr-2" />
                  取消操作
                </Button>
              )}
            </div>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <span className="text-sm font-medium">
                  {operationOptions.find(op => op.value === selectedOperation)?.label || '批量操作'}
                </span>
                <Badge 
                  variant={
                    currentOperation.status === 'completed' ? 'default' :
                    currentOperation.status === 'failed' ? 'destructive' :
                    'secondary'
                  }
                >
                  {currentOperation.status === 'pending' && '等待中'}
                  {currentOperation.status === 'processing' && '处理中'}
                  {currentOperation.status === 'completed' && '已完成'}
                  {currentOperation.status === 'failed' && '失败'}
                </Badge>
              </div>

              <Progress 
                value={(currentOperation.processedItems / currentOperation.totalItems) * 100} 
                className="w-full"
              />

              <div className="flex justify-between text-sm text-gray-500">
                <span>
                  已处理: {currentOperation.processedItems}/{currentOperation.totalItems}
                </span>
                <span>
                  成功: {currentOperation.successfulItems} | 失败: {currentOperation.failedItems}
                </span>
              </div>

              {currentOperation.downloadUrl && (
                <Button variant="outline" size="sm" asChild>
                  <a href={currentOperation.downloadUrl} download>
                    <Download className="h-4 w-4 mr-2" />
                    下载结果
                  </a>
                </Button>
              )}

              {currentOperation.errors.length > 0 && (
                <div className="space-y-2">
                  <Label className="text-sm font-medium text-red-600">错误详情:</Label>
                  <div className="max-h-32 overflow-y-auto space-y-1">
                    {currentOperation.errors.map((error, index) => (
                      <div key={index} className="text-xs text-red-600 bg-red-50 p-2 rounded">
                        项目 {error.itemId}: {error.error}
                      </div>
                    ))}
                  </div>
                </div>
              )}
            </div>
          </CardContent>
        </Card>
      )}

      {/* 操作历史 */}
      {operationHistory.length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <FileText className="h-5 w-5" />
              最近操作
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              {operationHistory.slice(0, 5).map((operation) => (
                <div key={operation.operationId} className="flex items-center justify-between p-3 border rounded-lg">
                  <div className="flex items-center gap-3">
                    <Badge 
                      variant={
                        operation.status === 'completed' ? 'default' :
                        operation.status === 'failed' ? 'destructive' :
                        'secondary'
                      }
                    >
                      {operation.status === 'completed' && '已完成'}
                      {operation.status === 'failed' && '失败'}
                      {operation.status === 'processing' && '处理中'}
                      {operation.status === 'pending' && '等待中'}
                    </Badge>
                    <div>
                      <p className="text-sm font-medium">
                        批量操作 ({operation.totalItems} 个项目)
                      </p>
                      <p className="text-xs text-gray-500">
                        {new Date(operation.startedAt).toLocaleString()}
                      </p>
                    </div>
                  </div>
                  <div className="text-right">
                    <p className="text-sm">
                      成功: {operation.successfulItems} | 失败: {operation.failedItems}
                    </p>
                    {operation.downloadUrl && (
                      <Button variant="ghost" size="sm" asChild>
                        <a href={operation.downloadUrl} download>
                          <Download className="h-3 w-3" />
                        </a>
                      </Button>
                    )}
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
};

export default BatchOperationPanel;
