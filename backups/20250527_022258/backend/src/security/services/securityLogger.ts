/**
 * 安全日志服务
 *
 * 负责记录安全事件
 */

import { LoggingConfig } from '../types';
import { LogBuffer } from './logBuffer';
import { MemoryCache } from './memoryCache';

/**
 * 日志级别枚举
 */
enum LogLevel {
  ERROR = 0,
  WARN = 1,
  INFO = 2,
  DEBUG = 3
}

/**
 * 日志条目接口
 */
export interface LogEntry {
  id: string;
  timestamp: number;
  level: 'error' | 'warn' | 'info' | 'debug';
  message: string;
  clientIp?: string;
  data?: any;
  requestId?: string;
  errorCode?: string;
  errorName?: string;
  severity?: 'low' | 'medium' | 'high' | 'critical';
  source?: string;
  tags?: string[];
  duration?: number;
  stack?: string;
}

/**
 * 安全日志服务类
 */
export class SecurityLogger {
  private config: LoggingConfig;
  private maxLogs: number = 1000;
  private logBuffer: LogBuffer;
  private cache: MemoryCache;
  private performanceMetrics: {
    totalLogs: number;
    totalProcessingTime: number;
    maxProcessingTime: number;
    minProcessingTime: number;
    lastProcessingTime: number;
  };

  /**
   * 构造函数
   */
  constructor() {
    this.config = {
      enabled: true,
      logLevel: 'error',
      storeSubmissionData: false
    };

    // 初始化日志缓冲区
    this.logBuffer = new LogBuffer();

    // 初始化缓存
    this.cache = new MemoryCache();

    // 初始化性能指标
    this.performanceMetrics = {
      totalLogs: 0,
      totalProcessingTime: 0,
      maxProcessingTime: 0,
      minProcessingTime: Number.MAX_SAFE_INTEGER,
      lastProcessingTime: 0
    };
  }

  /**
   * 初始化服务
   * @param config 日志配置
   */
  public initialize(config: LoggingConfig): void {
    this.config = { ...config };

    // 初始化日志缓冲区
    this.logBuffer.initialize({
      enabled: true,
      maxSize: 100,
      flushInterval: 5000, // 5秒
      criticalFlushSize: 20 // 当缓冲区达到20条日志时立即刷新
    }, this.flushLogs.bind(this));

    // 初始化缓存
    this.cache.initialize({
      enabled: true,
      defaultTTL: 300000, // 5分钟
      maxSize: 500,
      cleanupInterval: 60000 // 1分钟
    });
  }

  /**
   * 更新配置
   * @param config 日志配置
   */
  public updateConfig(config: Partial<LoggingConfig>): void {
    this.config = { ...this.config, ...config };

    // 更新日志缓冲区配置
    this.logBuffer.updateConfig({
      enabled: this.config.enabled
    });
  }

  /**
   * 刷新日志
   * @param logs 日志条目
   */
  private async flushLogs(logs: LogEntry[]): Promise<void> {
    if (logs.length === 0) {
      return;
    }

    try {
      // 批量存储日志
      for (const log of logs) {
        await this.storeLog(log);
      }
    } catch (error) {
      console.error('Error flushing logs:', error);
    }
  }

  /**
   * 记录日志
   * @param level 日志级别
   * @param message 日志消息
   * @param data 日志数据
   * @param clientIp 客户端 IP
   * @param env 环境变量
   * @param options 其他日志选项
   */
  public async log(
    level: 'error' | 'warn' | 'info' | 'debug',
    message: string,
    data?: any,
    clientIp?: string,
    env?: any,
    options: {
      requestId?: string;
      errorCode?: string;
      errorName?: string;
      severity?: 'low' | 'medium' | 'high' | 'critical';
      source?: string;
      tags?: string[];
      duration?: number;
      stack?: string;
    } = {}
  ): Promise<string> {
    // 性能测量开始
    const startTime = performance.now();

    // 如果未启用日志，直接返回
    if (!this.config.enabled) {
      return '';
    }

    // 检查日志级别
    if (!this.shouldLog(level)) {
      return '';
    }

    // 生成日志 ID
    const logId = crypto.randomUUID();

    // 创建缓存键
    const cacheKey = `log:${level}:${message}:${clientIp || ''}:${options.requestId || ''}`;

    // 检查缓存中是否有类似的日志
    const cachedLog = this.cache.get<{ count: number, lastLogId: string }>(cacheKey);

    // 如果有类似的日志且不是错误或警告，增加计数并返回上一个日志 ID
    if (cachedLog && (level === 'info' || level === 'debug')) {
      cachedLog.count++;

      // 如果是调试日志，可以直接返回，不需要存储
      if (level === 'debug' && cachedLog.count > 3) {
        // 性能测量结束
        const endTime = performance.now();
        this.updatePerformanceMetrics(endTime - startTime);

        return cachedLog.lastLogId;
      }

      // 更新缓存
      this.cache.set(cacheKey, cachedLog);
    }

    // 从错误对象中提取信息
    if (data && data.stack && !options.stack) {
      options.stack = data.stack;
    }

    if (data && data.name && !options.errorName) {
      options.errorName = data.name;
    }

    if (data && data.code && !options.errorCode) {
      options.errorCode = data.code;
    }

    // 创建日志条目
    const logEntry: LogEntry = {
      id: logId,
      timestamp: Date.now(),
      level,
      message,
      clientIp,
      data: this.sanitizeData(data),
      requestId: options.requestId,
      errorCode: options.errorCode,
      errorName: options.errorName,
      severity: options.severity,
      source: options.source || 'security',
      tags: options.tags,
      duration: options.duration,
      stack: options.stack
    };

    // 输出到控制台
    this.logToConsole(logEntry);

    try {
      // 将日志添加到缓冲区
      await this.logBuffer.add({
        ...logEntry,
        env // 临时存储环境变量，用于批处理
      });

      // 更新缓存
      this.cache.set(cacheKey, { count: 1, lastLogId: logId }, 60000); // 1分钟过期

      // 性能测量结束
      const endTime = performance.now();
      this.updatePerformanceMetrics(endTime - startTime);

      return logId;
    } catch (error) {
      console.error('Failed to store security log:', error);

      // 性能测量结束
      const endTime = performance.now();
      this.updatePerformanceMetrics(endTime - startTime);

      return logId;
    }
  }

  /**
   * 更新性能指标
   * @param processingTime 处理时间
   */
  private updatePerformanceMetrics(processingTime: number): void {
    this.performanceMetrics.totalLogs++;
    this.performanceMetrics.totalProcessingTime += processingTime;
    this.performanceMetrics.lastProcessingTime = processingTime;

    if (processingTime > this.performanceMetrics.maxProcessingTime) {
      this.performanceMetrics.maxProcessingTime = processingTime;
    }

    if (processingTime < this.performanceMetrics.minProcessingTime) {
      this.performanceMetrics.minProcessingTime = processingTime;
    }
  }

  /**
   * 获取性能指标
   */
  public getPerformanceMetrics(): any {
    const avgProcessingTime = this.performanceMetrics.totalLogs > 0
      ? this.performanceMetrics.totalProcessingTime / this.performanceMetrics.totalLogs
      : 0;

    return {
      ...this.performanceMetrics,
      avgProcessingTime,
      bufferStats: this.logBuffer.getStats(),
      cacheStats: this.cache.getStats()
    };
  }

  /**
   * 记录错误
   * @param error 错误对象
   * @param clientIp 客户端 IP
   * @param env 环境变量
   * @param options 其他日志选项
   */
  public async logError(
    error: Error,
    clientIp?: string,
    env?: any,
    options: {
      requestId?: string;
      source?: string;
      tags?: string[];
      duration?: number;
      level?: 'error' | 'warn';
    } = {}
  ): Promise<string> {
    // 确定日志级别
    const level = options.level || 'error';

    // 提取错误信息
    const errorData: any = {
      name: error.name,
      message: error.message,
      stack: error.stack
    };

    // 如果是自定义错误，提取更多信息
    if (error instanceof Error && (error as any).code) {
      errorData.code = (error as any).code;
    }

    if (error instanceof Error && (error as any).statusCode) {
      errorData.statusCode = (error as any).statusCode;
    }

    if (error instanceof Error && (error as any).context) {
      errorData.context = (error as any).context;
    }

    // 记录日志
    return this.log(
      level,
      `Security error: ${error.message}`,
      errorData,
      clientIp,
      env,
      {
        requestId: options.requestId,
        errorCode: errorData.code,
        errorName: error.name,
        severity: (error as any).severity || 'medium',
        source: options.source || 'security',
        tags: options.tags,
        duration: options.duration,
        stack: error.stack
      }
    );
  }

  /**
   * 获取日志
   * @param filter 过滤条件
   * @param page 页码
   * @param pageSize 每页大小
   * @param env 环境变量
   */
  public async getLogs(
    filter: string = 'all',
    page: number = 1,
    pageSize: number = 10,
    env?: any
  ): Promise<{ logs: LogEntry[]; total: number; page: number; pageSize: number; totalPages: number }> {
    try {
      // 获取所有日志
      const allLogs = await this.getAllLogs(env);

      // 过滤日志
      let filteredLogs = allLogs;
      if (filter !== 'all') {
        if (filter === 'suspicious') {
          filteredLogs = allLogs.filter(log =>
            log.message.toLowerCase().includes('suspicious') ||
            log.message.toLowerCase().includes('可疑')
          );
        } else {
          filteredLogs = allLogs.filter(log => log.level === filter);
        }
      }

      // 计算总页数
      const totalPages = Math.max(1, Math.ceil(filteredLogs.length / pageSize));

      // 分页
      const startIndex = (page - 1) * pageSize;
      const endIndex = startIndex + pageSize;
      const paginatedLogs = filteredLogs.slice(startIndex, endIndex);

      return {
        logs: paginatedLogs,
        total: filteredLogs.length,
        page,
        pageSize,
        totalPages
      };
    } catch (error) {
      console.error('Error getting logs:', error);
      return {
        logs: [],
        total: 0,
        page,
        pageSize,
        totalPages: 1
      };
    }
  }

  /**
   * 获取所有日志
   * @param env 环境变量
   */
  private async getAllLogs(env?: any): Promise<LogEntry[]> {
    try {
      // 检查缓存
      const cacheKey = 'allLogs';
      const cachedLogs = this.cache.get<LogEntry[]>(cacheKey);

      if (cachedLogs) {
        return cachedLogs;
      }

      // 性能测量开始
      const startTime = performance.now();

      // 尝试从 KV 存储获取日志
      if (env && env.SURVEY_SECURITY) {
        // 使用批处理优化 KV 存储访问
        const keys = await env.SURVEY_SECURITY.list({ prefix: 'log:' });

        if (keys.keys.length === 0) {
          return [];
        }

        // 获取所有日志
        const logs: LogEntry[] = [];
        const batchSize = 20; // 每批处理的键数量

        // 批量获取日志
        for (let i = 0; i < keys.keys.length; i += batchSize) {
          const batchKeys = keys.keys.slice(i, i + batchSize);
          const batchPromises = batchKeys.map(key =>
            env.SURVEY_SECURITY.get(key.name, { type: 'json' })
          );

          const batchResults = await Promise.all(batchPromises);

          for (const log of batchResults) {
            if (log) {
              logs.push(log);
            }
          }
        }

        // 按时间戳排序
        const sortedLogs = logs.sort((a, b) => b.timestamp - a.timestamp);

        // 缓存结果
        this.cache.set(cacheKey, sortedLogs, 10000); // 10秒缓存

        // 性能测量结束
        const endTime = performance.now();
        console.debug(`Retrieved ${logs.length} logs from KV in ${endTime - startTime}ms`);

        return sortedLogs;
      } else {
        // 如果没有 KV 存储，使用内存存储
        const memoryStorage = global.__securityLogs = global.__securityLogs || [];

        // 按时间戳排序
        const sortedLogs = [...memoryStorage].sort((a, b) => b.timestamp - a.timestamp);

        // 缓存结果
        this.cache.set(cacheKey, sortedLogs, 10000); // 10秒缓存

        // 性能测量结束
        const endTime = performance.now();
        console.debug(`Retrieved ${sortedLogs.length} logs from memory in ${endTime - startTime}ms`);

        return sortedLogs;
      }
    } catch (error) {
      console.error('Error getting all logs:', error);
      return [];
    }
  }

  /**
   * 存储日志
   * @param logEntry 日志条目
   * @param env 环境变量
   */
  private async storeLog(logEntry: LogEntry, env?: any): Promise<void> {
    try {
      // 尝试存储到 KV 存储
      if (env && env.SURVEY_SECURITY) {
        const key = `log:${logEntry.id}`;

        // 存储日志
        await env.SURVEY_SECURITY.put(key, JSON.stringify(logEntry), {
          expirationTtl: 30 * 24 * 60 * 60 // 30 天过期
        });

        // 限制日志数量
        const keys = await env.SURVEY_SECURITY.list({ prefix: 'log:' });

        if (keys.keys.length > this.maxLogs) {
          // 按名称排序（实际上是按时间戳排序，因为 ID 是随机的）
          const oldestKeys = keys.keys
            .sort((a, b) => a.name.localeCompare(b.name))
            .slice(0, keys.keys.length - this.maxLogs);

          // 删除最旧的日志
          for (const key of oldestKeys) {
            await env.SURVEY_SECURITY.delete(key.name);
          }
        }
      } else {
        // 如果没有 KV 存储，使用内存存储
        const memoryStorage = global.__securityLogs = global.__securityLogs || [];

        // 添加日志
        memoryStorage.push(logEntry);

        // 限制日志数量
        if (memoryStorage.length > this.maxLogs) {
          memoryStorage.splice(0, memoryStorage.length - this.maxLogs);
        }
      }
    } catch (error) {
      console.error('Error storing log:', error);
    }
  }

  /**
   * 检查是否应该记录该级别的日志
   * @param level 日志级别
   */
  private shouldLog(level: 'error' | 'warn' | 'info' | 'debug'): boolean {
    const configLevel = this.getLogLevelValue(this.config.logLevel);
    const messageLevel = this.getLogLevelValue(level);

    return messageLevel <= configLevel;
  }

  /**
   * 获取日志级别值
   * @param level 日志级别
   */
  private getLogLevelValue(level: 'error' | 'warn' | 'info' | 'debug'): number {
    switch (level) {
      case 'error': return LogLevel.ERROR;
      case 'warn': return LogLevel.WARN;
      case 'info': return LogLevel.INFO;
      case 'debug': return LogLevel.DEBUG;
      default: return LogLevel.ERROR;
    }
  }

  /**
   * 清理数据
   * @param data 日志数据
   */
  private sanitizeData(data: any): any {
    if (!data) {
      return undefined;
    }

    // 如果不存储提交数据，移除敏感信息
    if (!this.config.storeSubmissionData) {
      // 创建数据的副本
      const sanitized = { ...data };

      // 移除可能的敏感字段
      const sensitiveFields = ['password', 'token', 'secret', 'key', 'auth', 'credentials'];

      // 递归清理对象
      const cleanObject = (obj: any) => {
        if (!obj || typeof obj !== 'object') {
          return;
        }

        for (const key in obj) {
          // 检查是否是敏感字段
          if (sensitiveFields.some(field => key.toLowerCase().includes(field))) {
            obj[key] = '[REDACTED]';
          } else if (typeof obj[key] === 'object') {
            // 递归清理嵌套对象
            cleanObject(obj[key]);
          }
        }
      };

      cleanObject(sanitized);

      return sanitized;
    }

    return data;
  }

  /**
   * 输出日志到控制台
   * @param logEntry 日志条目
   */
  private logToConsole(logEntry: LogEntry): void {
    const timestamp = new Date(logEntry.timestamp).toISOString();
    let prefix = `[Security ${logEntry.level.toUpperCase()}] ${timestamp}:`;

    // 添加请求 ID
    if (logEntry.requestId) {
      prefix += ` [ReqID:${logEntry.requestId.substring(0, 8)}]`;
    }

    // 添加客户端 IP
    if (logEntry.clientIp) {
      prefix += ` [IP:${logEntry.clientIp}]`;
    }

    // 添加错误代码
    if (logEntry.errorCode) {
      prefix += ` [Code:${logEntry.errorCode}]`;
    }

    // 添加严重性
    if (logEntry.severity) {
      prefix += ` [Severity:${logEntry.severity}]`;
    }

    // 添加标签
    if (logEntry.tags && logEntry.tags.length > 0) {
      prefix += ` [Tags:${logEntry.tags.join(',')}]`;
    }

    // 格式化日志数据
    const logData = { ...logEntry.data };

    // 如果有堆栈信息，单独显示
    if (logEntry.stack) {
      logData.stack = logEntry.stack;
    }

    switch (logEntry.level) {
      case 'error':
        console.error(prefix, logEntry.message);
        if (Object.keys(logData).length > 0) {
          console.error('Error details:', logData);
        }
        break;
      case 'warn':
        console.warn(prefix, logEntry.message);
        if (Object.keys(logData).length > 0) {
          console.warn('Warning details:', logData);
        }
        break;
      case 'info':
        console.info(prefix, logEntry.message);
        if (Object.keys(logData).length > 0) {
          console.info('Info details:', logData);
        }
        break;
      case 'debug':
        console.debug(prefix, logEntry.message);
        if (Object.keys(logData).length > 0) {
          console.debug('Debug details:', logData);
        }
        break;
    }
  }
}

// 为内存存储声明全局类型
declare global {
  var __securityLogs: LogEntry[];
}
