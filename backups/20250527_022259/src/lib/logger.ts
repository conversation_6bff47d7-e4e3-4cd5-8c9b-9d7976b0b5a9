/**
 * 日志工具
 * 提供统一的日志记录功能
 */

// 日志级别
export enum LogLevel {
  DEBUG = 'debug',
  INFO = 'info',
  WARN = 'warn',
  ERROR = 'error'
}

// 是否启用日志
const isLogEnabled = process.env.NODE_ENV !== 'production' || localStorage.getItem('enableLogs') === 'true';

// 是否启用控制台颜色
const useColors = true;

// 日志颜色
const colors = {
  [LogLevel.DEBUG]: '#9ca3af', // 灰色
  [LogLevel.INFO]: '#3b82f6',  // 蓝色
  [LogLevel.WARN]: '#f59e0b',  // 黄色
  [LogLevel.ERROR]: '#ef4444'  // 红色
};

/**
 * 记录日志
 * @param level 日志级别
 * @param message 日志消息
 * @param data 附加数据
 */
const log = (level: LogLevel, message: string, data?: any) => {
  if (!isLogEnabled && level !== LogLevel.ERROR) {
    return;
  }

  const timestamp = new Date().toISOString();
  const prefix = `[${timestamp}] [${level.toUpperCase()}]`;

  if (useColors) {
    const color = colors[level];
    console.log(`%c${prefix}`, `color: ${color}; font-weight: bold`, message);
    if (data) {
      console.log(`%c${prefix} Data:`, `color: ${color}`, data);
    }
  } else {
    console.log(prefix, message);
    if (data) {
      console.log(`${prefix} Data:`, data);
    }
  }

  // 在生产环境中，可以将日志发送到服务器
  if (process.env.NODE_ENV === 'production' && level === LogLevel.ERROR) {
    // TODO: 发送错误日志到服务器
  }
};

/**
 * 记录调试日志
 * @param message 日志消息
 * @param data 附加数据
 */
export const logDebug = (message: string, data?: any) => {
  log(LogLevel.DEBUG, message, data);
};

/**
 * 记录信息日志
 * @param message 日志消息
 * @param data 附加数据
 */
export const logInfo = (message: string, data?: any) => {
  log(LogLevel.INFO, message, data);
};

/**
 * 记录警告日志
 * @param message 日志消息
 * @param data 附加数据
 */
export const logWarn = (message: string, data?: any) => {
  log(LogLevel.WARN, message, data);
};

/**
 * 记录错误日志
 * @param message 日志消息
 * @param error 错误对象
 */
export const logError = (message: string, error?: any) => {
  log(LogLevel.ERROR, message, error);
};

/**
 * 启用日志
 */
export const enableLogs = () => {
  localStorage.setItem('enableLogs', 'true');
};

/**
 * 禁用日志
 */
export const disableLogs = () => {
  localStorage.setItem('enableLogs', 'false');
};
