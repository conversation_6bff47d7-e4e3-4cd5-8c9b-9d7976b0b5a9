/**
 * API集成测试
 */

import { describe, it, expect, beforeEach, vi } from 'vitest';
import { handleRequest } from '../../src/index';
import { mockEnv, createMockRequest, parseResponseJson } from '../setup';
import { resetAllMocks, mockVerifyToken } from '../utils';

// 模拟处理程序
vi.mock('../../src/handlers/admin', () => ({
  handleAdminLogin: vi.fn().mockImplementation(async () => {
    return new Response(JSON.stringify({
      success: true,
      token: 'mock-token',
      user: { id: 1, username: 'admin', role: 'admin' }
    }), {
      status: 200,
      headers: { 'Content-Type': 'application/json' }
    });
  })
}));

vi.mock('../../src/handlers/survey', () => ({
  handleGetSurveys: vi.fn().mockImplementation(async () => {
    return new Response(JSON.stringify({
      success: true,
      surveys: [{ id: 1, title: '测试问卷' }],
      total: 1,
      page: 1,
      pageSize: 10,
      totalPages: 1
    }), {
      status: 200,
      headers: { 'Content-Type': 'application/json' }
    });
  }),
  handleGetSurveyDetail: vi.fn().mockImplementation(async () => {
    return new Response(JSON.stringify({
      success: true,
      survey: { 
        id: 1, 
        title: '测试问卷',
        questions: [{ id: 1, question: '测试问题' }]
      }
    }), {
      status: 200,
      headers: { 'Content-Type': 'application/json' }
    });
  }),
  handleCreateSurvey: vi.fn().mockImplementation(async () => {
    return new Response(JSON.stringify({
      success: true,
      surveyId: 1,
      message: '问卷创建成功'
    }), {
      status: 201,
      headers: { 'Content-Type': 'application/json' }
    });
  }),
  handleSubmitSurveyResponse: vi.fn().mockImplementation(async () => {
    return new Response(JSON.stringify({
      success: true,
      responseId: 1,
      message: '问卷提交成功，等待审核'
    }), {
      status: 201,
      headers: { 'Content-Type': 'application/json' }
    });
  }),
  handleGetSurveyResponses: vi.fn().mockImplementation(async () => {
    return new Response(JSON.stringify({
      success: true,
      responses: [{ id: 1, status: 'pending' }],
      total: 1,
      page: 1,
      pageSize: 10,
      totalPages: 1
    }), {
      status: 200,
      headers: { 'Content-Type': 'application/json' }
    });
  }),
  handleGetSurveyResponseDetail: vi.fn().mockImplementation(async () => {
    return new Response(JSON.stringify({
      success: true,
      response: { 
        id: 1, 
        status: 'pending',
        answers: [{ id: 1, answer: '测试回答' }]
      }
    }), {
      status: 200,
      headers: { 'Content-Type': 'application/json' }
    });
  }),
  handleModerateSurveyResponse: vi.fn().mockImplementation(async () => {
    return new Response(JSON.stringify({
      success: true,
      id: 1,
      status: 'verified',
      message: '问卷回复已验证'
    }), {
      status: 200,
      headers: { 'Content-Type': 'application/json' }
    });
  })
}));

vi.mock('../../src/handlers/user', () => ({
  handleGetUsers: vi.fn().mockImplementation(async () => {
    return new Response(JSON.stringify({
      success: true,
      users: [{ id: 1, username: 'admin', role: 'admin' }],
      total: 1,
      page: 1,
      pageSize: 10,
      totalPages: 1
    }), {
      status: 200,
      headers: { 'Content-Type': 'application/json' }
    });
  }),
  handleCreateUser: vi.fn().mockImplementation(async () => {
    return new Response(JSON.stringify({
      success: true,
      userId: 1,
      message: '用户创建成功'
    }), {
      status: 201,
      headers: { 'Content-Type': 'application/json' }
    });
  }),
  handleUpdateUser: vi.fn().mockImplementation(async () => {
    return new Response(JSON.stringify({
      success: true,
      message: '用户更新成功'
    }), {
      status: 200,
      headers: { 'Content-Type': 'application/json' }
    });
  }),
  handleDeleteUser: vi.fn().mockImplementation(async () => {
    return new Response(JSON.stringify({
      success: true,
      message: '用户删除成功'
    }), {
      status: 200,
      headers: { 'Content-Type': 'application/json' }
    });
  }),
  handleResetUserPassword: vi.fn().mockImplementation(async () => {
    return new Response(JSON.stringify({
      success: true,
      newPassword: 'admin123',
      message: '密码已重置'
    }), {
      status: 200,
      headers: { 'Content-Type': 'application/json' }
    });
  })
}));

describe('API集成测试', () => {
  beforeEach(() => {
    resetAllMocks();
    vi.clearAllMocks();
  });

  describe('路由测试', () => {
    it('应该正确路由管理员登录请求', async () => {
      // 准备请求
      const request = createMockRequest({
        method: 'POST',
        url: 'http://localhost/api/v1/admin/login',
        body: {
          username: 'admin',
          password: 'admin123'
        }
      });

      // 执行请求处理
      const response = await handleRequest(request, mockEnv);
      
      // 验证响应
      expect(response.status).toBe(200);
      
      const data = await parseResponseJson(response);
      expect(data.success).toBe(true);
      expect(data.token).toBe('mock-token');
      expect(data.user).toBeDefined();
    });

    it('应该正确路由获取问卷列表请求', async () => {
      // 准备请求
      const request = createMockRequest({
        method: 'GET',
        url: 'http://localhost/api/v1/surveys'
      });

      // 执行请求处理
      const response = await handleRequest(request, mockEnv);
      
      // 验证响应
      expect(response.status).toBe(200);
      
      const data = await parseResponseJson(response);
      expect(data.success).toBe(true);
      expect(data.surveys).toBeDefined();
    });

    it('应该正确路由获取问卷详情请求', async () => {
      // 准备请求
      const request = createMockRequest({
        method: 'GET',
        url: 'http://localhost/api/v1/surveys/1'
      });

      // 执行请求处理
      const response = await handleRequest(request, mockEnv);
      
      // 验证响应
      expect(response.status).toBe(200);
      
      const data = await parseResponseJson(response);
      expect(data.success).toBe(true);
      expect(data.survey).toBeDefined();
    });

    it('应该正确路由创建问卷请求', async () => {
      // 准备请求
      const request = createMockRequest({
        method: 'POST',
        url: 'http://localhost/api/v1/surveys',
        headers: { 'Authorization': 'Bearer mock-token' },
        body: {
          title: '测试问卷',
          description: '这是一个测试问卷',
          questions: []
        }
      });

      // 执行请求处理
      const response = await handleRequest(request, mockEnv);
      
      // 验证响应
      expect(response.status).toBe(201);
      
      const data = await parseResponseJson(response);
      expect(data.success).toBe(true);
      expect(data.surveyId).toBeDefined();
    });

    it('应该正确路由提交问卷回复请求', async () => {
      // 准备请求
      const request = createMockRequest({
        method: 'POST',
        url: 'http://localhost/api/v1/surveys/1/responses',
        body: {
          answers: [],
          isAnonymous: true,
          identityA: 'a',
          identityB: 'b'
        }
      });

      // 执行请求处理
      const response = await handleRequest(request, mockEnv);
      
      // 验证响应
      expect(response.status).toBe(201);
      
      const data = await parseResponseJson(response);
      expect(data.success).toBe(true);
      expect(data.responseId).toBeDefined();
    });

    it('应该正确路由获取用户列表请求', async () => {
      // 准备请求
      const request = createMockRequest({
        method: 'GET',
        url: 'http://localhost/api/v1/admin/users',
        headers: { 'Authorization': 'Bearer mock-token' }
      });

      // 执行请求处理
      const response = await handleRequest(request, mockEnv);
      
      // 验证响应
      expect(response.status).toBe(200);
      
      const data = await parseResponseJson(response);
      expect(data.success).toBe(true);
      expect(data.users).toBeDefined();
    });

    it('应该返回404错误当路由不存在', async () => {
      // 准备请求
      const request = createMockRequest({
        method: 'GET',
        url: 'http://localhost/api/v1/nonexistent'
      });

      // 执行请求处理
      const response = await handleRequest(request, mockEnv);
      
      // 验证响应
      expect(response.status).toBe(404);
      
      const data = await parseResponseJson(response);
      expect(data.success).toBe(false);
      expect(data.error).toBe('Not Found');
    });

    it('应该处理CORS预检请求', async () => {
      // 准备请求
      const request = createMockRequest({
        method: 'OPTIONS',
        url: 'http://localhost/api/v1/surveys',
        headers: {
          'Origin': 'http://localhost:3000',
          'Access-Control-Request-Method': 'GET',
          'Access-Control-Request-Headers': 'Content-Type'
        }
      });

      // 执行请求处理
      const response = await handleRequest(request, mockEnv);
      
      // 验证响应
      expect(response.status).toBe(204);
      expect(response.headers.get('Access-Control-Allow-Origin')).toBe('*');
      expect(response.headers.get('Access-Control-Allow-Methods')).toBeDefined();
      expect(response.headers.get('Access-Control-Allow-Headers')).toBeDefined();
    });
  });
});
