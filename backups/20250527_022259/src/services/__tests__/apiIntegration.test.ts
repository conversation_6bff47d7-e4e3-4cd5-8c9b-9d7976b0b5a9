/**
 * API集成测试
 * 
 * 这个测试文件用于测试前端与真实API的集成
 * 注意：这些测试需要API服务器运行
 */

import { describe, it, expect, beforeAll, afterAll } from 'vitest';
import { fetchData } from '../unifiedDataService';
import { apiClient } from '../apiClient';

// 测试配置
const TEST_CONFIG = {
  // 是否运行真实API测试
  // 设置为false可以跳过需要真实API的测试
  RUN_REAL_API_TESTS: true,
  // API基础URL
  API_BASE_URL: 'http://localhost:8787/api',
  // API版本
  API_VERSION: 'v1',
  // 测试凭据
  TEST_CREDENTIALS: {
    admin: {
      username: 'admin',
      password: 'admin123'
    },
    reviewer: {
      username: 'reviewer1',
      password: 'reviewer123'
    }
  }
};

// 测试令牌
let adminToken: string = '';
let reviewerToken: string = '';

// 测试数据
const testSurvey = {
  title: '测试问卷',
  description: '这是一个测试问卷',
  questions: [
    {
      type: 'radio',
      question: '测试问题1',
      options: ['选项1', '选项2', '选项3'],
      required: true
    },
    {
      type: 'checkbox',
      question: '测试问题2',
      options: ['选项A', '选项B', '选项C'],
      required: true
    },
    {
      type: 'text',
      question: '测试问题3',
      required: false
    }
  ]
};

// 测试故事
const testStory = {
  title: '测试故事',
  content: '这是一个测试故事的内容，用于测试API集成。',
  isAnonymous: true,
  identityA: 'test-identity-a',
  identityB: 'test-identity-b'
};

// 测试标签
const testTag = {
  name: '测试标签',
  description: '这是一个测试标签'
};

// 测试问卷回复
const testSurveyResponse = {
  answers: [
    {
      questionId: 1,
      answer: '选项1'
    },
    {
      questionId: 2,
      answer: ['选项A', '选项C']
    },
    {
      questionId: 3,
      answer: '这是文本回答'
    }
  ],
  isAnonymous: true,
  identityA: 'test-identity-a',
  identityB: 'test-identity-b'
};

// 跳过测试的条件
const skipIfNoRealApi = TEST_CONFIG.RUN_REAL_API_TESTS ? it : it.skip;

// 在所有测试之前登录
beforeAll(async () => {
  if (!TEST_CONFIG.RUN_REAL_API_TESTS) {
    return;
  }

  try {
    // 设置API基础URL
    apiClient.baseUrl = TEST_CONFIG.API_BASE_URL;

    // 管理员登录
    const adminLoginResponse = await fetchData(
      'admin/login',
      'adminLogin',
      TEST_CONFIG.TEST_CREDENTIALS.admin,
      {
        method: 'POST',
        forceReal: true
      }
    );

    adminToken = adminLoginResponse.token;
    console.log('管理员登录成功，令牌:', adminToken.substring(0, 10) + '...');

    // 审核员登录
    const reviewerLoginResponse = await fetchData(
      'reviewer/login',
      'reviewerLogin',
      TEST_CONFIG.TEST_CREDENTIALS.reviewer,
      {
        method: 'POST',
        forceReal: true
      }
    );

    reviewerToken = reviewerLoginResponse.token;
    console.log('审核员登录成功，令牌:', reviewerToken.substring(0, 10) + '...');
  } catch (error) {
    console.error('登录失败:', error);
    // 如果登录失败，跳过真实API测试
    TEST_CONFIG.RUN_REAL_API_TESTS = false;
  }
});

describe('API集成测试', () => {
  describe('认证API', () => {
    skipIfNoRealApi('管理员登录', async () => {
      const response = await fetchData(
        'admin/login',
        'adminLogin',
        TEST_CONFIG.TEST_CREDENTIALS.admin,
        {
          method: 'POST',
          forceReal: true
        }
      );

      expect(response.success).toBe(true);
      expect(response.token).toBeDefined();
      expect(response.user).toBeDefined();
      expect(response.user.role).toBe('admin');
    });

    skipIfNoRealApi('审核员登录', async () => {
      const response = await fetchData(
        'reviewer/login',
        'reviewerLogin',
        TEST_CONFIG.TEST_CREDENTIALS.reviewer,
        {
          method: 'POST',
          forceReal: true
        }
      );

      expect(response.success).toBe(true);
      expect(response.token).toBeDefined();
      expect(response.user).toBeDefined();
      expect(response.user.role).toBe('reviewer');
    });
  });

  describe('问卷API', () => {
    let createdSurveyId: number;

    skipIfNoRealApi('创建问卷', async () => {
      const response = await fetchData(
        'surveys',
        'createSurvey',
        testSurvey,
        {
          method: 'POST',
          forceReal: true,
          headers: {
            Authorization: `Bearer ${adminToken}`
          }
        }
      );

      expect(response.success).toBe(true);
      expect(response.surveyId).toBeDefined();
      createdSurveyId = response.surveyId;
    });

    skipIfNoRealApi('获取问卷列表', async () => {
      const response = await fetchData(
        'surveys',
        'getSurveys',
        {},
        {
          forceReal: true
        }
      );

      expect(response.success).toBe(true);
      expect(response.surveys).toBeDefined();
      expect(Array.isArray(response.surveys)).toBe(true);
    });

    skipIfNoRealApi('获取问卷详情', async () => {
      // 跳过如果没有创建问卷
      if (!createdSurveyId) {
        return;
      }

      const response = await fetchData(
        `surveys/${createdSurveyId}`,
        'getSurveyDetail',
        {},
        {
          forceReal: true
        }
      );

      expect(response.success).toBe(true);
      expect(response.survey).toBeDefined();
      expect(response.survey.id).toBe(createdSurveyId);
      expect(response.survey.questions).toBeDefined();
      expect(Array.isArray(response.survey.questions)).toBe(true);
    });
  });

  describe('故事API', () => {
    let createdStoryId: number;

    skipIfNoRealApi('提交故事', async () => {
      const response = await fetchData(
        'stories',
        'submitStory',
        testStory,
        {
          method: 'POST',
          forceReal: true
        }
      );

      expect(response.success).toBe(true);
      expect(response.storyId).toBeDefined();
      createdStoryId = response.storyId;
    });

    skipIfNoRealApi('获取故事列表', async () => {
      const response = await fetchData(
        'stories',
        'getStories',
        { status: 'verified' },
        {
          forceReal: true
        }
      );

      expect(response.success).toBe(true);
      expect(response.stories).toBeDefined();
      expect(Array.isArray(response.stories)).toBe(true);
    });
  });

  describe('标签API', () => {
    let createdTagId: number;

    skipIfNoRealApi('创建标签', async () => {
      const response = await fetchData(
        'tags',
        'createTag',
        testTag,
        {
          method: 'POST',
          forceReal: true,
          headers: {
            Authorization: `Bearer ${adminToken}`
          }
        }
      );

      expect(response.success).toBe(true);
      expect(response.tagId).toBeDefined();
      createdTagId = response.tagId;
    });

    skipIfNoRealApi('获取标签列表', async () => {
      const response = await fetchData(
        'tags',
        'getTags',
        {},
        {
          forceReal: true
        }
      );

      expect(response.success).toBe(true);
      expect(response.tags).toBeDefined();
      expect(Array.isArray(response.tags)).toBe(true);
    });
  });

  describe('统计API', () => {
    skipIfNoRealApi('获取统计数据', async () => {
      const response = await fetchData(
        'stats',
        'getStats',
        {},
        {
          forceReal: true,
          headers: {
            Authorization: `Bearer ${adminToken}`
          }
        }
      );

      expect(response.success).toBe(true);
      expect(response.stats).toBeDefined();
    });

    skipIfNoRealApi('获取审核统计数据', async () => {
      const response = await fetchData(
        'stats/review',
        'getReviewStats',
        {},
        {
          forceReal: true,
          headers: {
            Authorization: `Bearer ${adminToken}`
          }
        }
      );

      expect(response.success).toBe(true);
      expect(response.stats).toBeDefined();
    });
  });
});
