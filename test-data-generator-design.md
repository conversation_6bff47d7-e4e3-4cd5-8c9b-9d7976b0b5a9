# 测试数据生成页面设计方案

## 🎯 页面概述
将 `questionnaire-voices-2` 页面改造为内测阶段的数据生成工具，支持快速生成各类测试数据。

## 🔧 功能模块设计

### 1. 问卷数据生成器
```
┌─────────────────────────────────────┐
│ 📝 问卷数据生成器                    │
├─────────────────────────────────────┤
│ 状态选择: ○ 未审核  ○ 已通过        │
│ 生成数量: [1] 条                    │
│ 包含心声: ☑ 建议内容 ☑ 观察内容     │
│                                     │
│ [生成随机问卷] [批量生成(10条)]      │
│                                     │
│ 最近生成: 3条问卷 (2条已生成心声)    │
└─────────────────────────────────────┘
```

### 2. 故事墙数据生成器
```
┌─────────────────────────────────────┐
│ 📖 故事墙数据生成器                  │
├─────────────────────────────────────┤
│ 状态选择: ○ 未审核  ○ 已通过        │
│ 故事类型: [求职经历] ▼               │
│ 生成数量: [1] 条                    │
│                                     │
│ [生成随机故事] [批量生成(10条)]      │
│                                     │
│ 最近生成: 5条故事 (3条待审核)        │
└─────────────────────────────────────┘
```

### 3. 数据统计面板
```
┌─────────────────────────────────────┐
│ 📊 当前数据统计                      │
├─────────────────────────────────────┤
│ 问卷数据: 156条 (待审核: 12)         │
│ 心声数据: 89条 (自动生成: 67)        │
│ 故事数据: 234条 (待审核: 18)         │
│                                     │
│ [清空测试数据] [重置为初始状态]      │
└─────────────────────────────────────┘
```

## 📋 详细功能规格

### 问卷生成功能
- **随机字段生成**:
  - 教育水平: 随机选择(高中/大专/本科/硕士/博士)
  - 专业: 从预设专业库随机选择
  - 毕业年份: 2020-2024随机
  - 地区: 从省市库随机选择
  - 就业状态: 随机分布(已就业70%, 求职中20%, 其他10%)
  
- **心声内容生成**:
  - 建议内容: 从模板库随机组合生成
  - 观察内容: 基于当前就业环境的真实观察
  - 可选择是否包含心声内容

- **状态控制**:
  - 未审核: 直接插入数据库，状态为'pending'
  - 已通过: 插入后自动设置为'approved'

### 故事生成功能
- **故事类型**:
  - 求职经历 (40%)
  - 职业转换 (25%)
  - 创业经历 (15%)
  - 实习体验 (20%)

- **内容生成**:
  - 标题: 根据类型生成吸引人的标题
  - 内容: 1000-2000字的完整故事
  - 摘要: 自动提取关键信息
  - 标签: 自动生成相关标签

### 数据质量保证
- **真实性**: 基于真实场景的模板
- **多样性**: 确保数据分布合理
- **一致性**: 字段间逻辑关系正确
- **可追踪**: 测试数据带有特殊标识

## 🔒 安全控制

### 访问控制
```typescript
// 仅在开发/测试环境可用
const isTestEnvironment = () => {
  return window.location.hostname.includes('pages.dev') || 
         window.location.hostname === 'localhost' ||
         window.location.search.includes('test=true');
};
```

### 数据标识
```sql
-- 测试数据标识
INSERT INTO questionnaire_responses_v2 (
  id, -- 以 'test_' 开头
  source, -- 标记为 'test_generator'
  ...
);
```

## 🎨 UI/UX 设计

### 页面布局
```
┌─────────────────────────────────────┐
│ 🧪 内测数据生成工具                  │
│ ⚠️  仅限内测环境使用                 │
├─────────────────────────────────────┤
│                                     │
│ [问卷生成器]  [故事生成器]           │
│                                     │
│ [数据统计]    [操作日志]             │
│                                     │
│ [导出数据]    [清理工具]             │
└─────────────────────────────────────┘
```

### 交互反馈
- 生成进度条
- 实时数据更新
- 操作成功/失败提示
- 生成日志显示

## 🚀 实现优先级

### Phase 1 (核心功能)
1. ✅ 问卷随机生成器
2. ✅ 基础状态控制
3. ✅ 数据统计显示

### Phase 2 (增强功能)  
1. 🔄 故事墙生成器
2. 🔄 批量生成功能
3. 🔄 数据清理工具

### Phase 3 (高级功能)
1. ⏳ 数据导出功能
2. ⏳ 生成模板管理
3. ⏳ 操作日志记录

## 📊 预期效果

### 内测价值
- **快速数据填充**: 几分钟内生成数百条测试数据
- **场景覆盖**: 覆盖各种用户场景和边界情况
- **功能验证**: 验证问卷→心声自动生成流程
- **性能测试**: 测试大数据量下的系统表现

### 开发效率
- **减少手工操作**: 自动化数据生成
- **提高测试覆盖**: 多样化的测试数据
- **便于调试**: 可控的数据生成环境
- **快速重置**: 一键清理和重新开始

## 🎯 总结

这个方案非常适合内测阶段，建议立即开始实施。优先实现问卷生成功能，然后逐步添加故事生成和其他高级功能。
