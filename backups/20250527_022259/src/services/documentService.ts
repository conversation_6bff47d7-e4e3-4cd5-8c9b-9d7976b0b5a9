/**
 * 文档管理服务
 * 
 * 提供文档的读取、创建、更新和删除功能
 */

export interface DocumentItem {
  id: string;
  title: string;
  description: string;
  filePath: string;
  category: string;
  type: 'markdown' | 'pdf' | 'word' | 'text' | 'code';
  tags: string[];
  lastUpdated: string;
  size: string;
  author: string;
  version: string;
  status: 'draft' | 'review' | 'published' | 'archived';
  isLocal: boolean;
  content?: string;
}

export interface DocumentCategory {
  id: string;
  name: string;
  description: string;
  count: number;
}

class DocumentService {
  private baseUrl = '/api/documents';

  /**
   * 获取所有文档列表
   */
  async getDocuments(): Promise<DocumentItem[]> {
    try {
      // 在实际应用中，这里会调用后端 API
      // const response = await fetch(`${this.baseUrl}`);
      // return await response.json();
      
      // 目前返回模拟数据
      return this.getMockDocuments();
    } catch (error) {
      console.error('获取文档列表失败:', error);
      throw new Error('无法获取文档列表');
    }
  }

  /**
   * 根据 ID 获取文档详情
   */
  async getDocumentById(id: string): Promise<DocumentItem | null> {
    try {
      // 在实际应用中，这里会调用后端 API
      // const response = await fetch(`${this.baseUrl}/${id}`);
      // return await response.json();
      
      const documents = await this.getDocuments();
      return documents.find(doc => doc.id === id) || null;
    } catch (error) {
      console.error('获取文档详情失败:', error);
      throw new Error('无法获取文档详情');
    }
  }

  /**
   * 读取文档内容
   */
  async getDocumentContent(filePath: string): Promise<string> {
    try {
      // 在实际应用中，这里会调用后端 API 读取文件内容
      // const response = await fetch(`${this.baseUrl}/content?path=${encodeURIComponent(filePath)}`);
      // return await response.text();
      
      // 目前返回模拟内容
      return this.getMockDocumentContent(filePath);
    } catch (error) {
      console.error('读取文档内容失败:', error);
      throw new Error('无法读取文档内容');
    }
  }

  /**
   * 创建新文档
   */
  async createDocument(document: Omit<DocumentItem, 'id' | 'lastUpdated' | 'version'>): Promise<DocumentItem> {
    try {
      // 在实际应用中，这里会调用后端 API
      // const response = await fetch(`${this.baseUrl}`, {
      //   method: 'POST',
      //   headers: { 'Content-Type': 'application/json' },
      //   body: JSON.stringify(document)
      // });
      // return await response.json();
      
      // 目前返回模拟创建的文档
      const newDocument: DocumentItem = {
        ...document,
        id: Date.now().toString(),
        lastUpdated: new Date().toISOString().split('T')[0],
        version: '1.0.0'
      };
      
      return newDocument;
    } catch (error) {
      console.error('创建文档失败:', error);
      throw new Error('无法创建文档');
    }
  }

  /**
   * 更新文档
   */
  async updateDocument(id: string, updates: Partial<DocumentItem>): Promise<DocumentItem> {
    try {
      // 在实际应用中，这里会调用后端 API
      // const response = await fetch(`${this.baseUrl}/${id}`, {
      //   method: 'PUT',
      //   headers: { 'Content-Type': 'application/json' },
      //   body: JSON.stringify(updates)
      // });
      // return await response.json();
      
      // 目前返回模拟更新的文档
      const document = await this.getDocumentById(id);
      if (!document) {
        throw new Error('文档不存在');
      }
      
      return {
        ...document,
        ...updates,
        lastUpdated: new Date().toISOString().split('T')[0]
      };
    } catch (error) {
      console.error('更新文档失败:', error);
      throw new Error('无法更新文档');
    }
  }

  /**
   * 删除文档
   */
  async deleteDocument(id: string): Promise<void> {
    try {
      // 在实际应用中，这里会调用后端 API
      // await fetch(`${this.baseUrl}/${id}`, { method: 'DELETE' });
      
      console.log(`删除文档: ${id}`);
    } catch (error) {
      console.error('删除文档失败:', error);
      throw new Error('无法删除文档');
    }
  }

  /**
   * 获取文档分类统计
   */
  async getDocumentCategories(): Promise<DocumentCategory[]> {
    try {
      const documents = await this.getDocuments();
      const categoryMap = new Map<string, number>();
      
      documents.forEach(doc => {
        categoryMap.set(doc.category, (categoryMap.get(doc.category) || 0) + 1);
      });
      
      const categories: DocumentCategory[] = [
        { id: 'all', name: '全部文档', description: '所有文档', count: documents.length }
      ];
      
      categoryMap.forEach((count, category) => {
        categories.push({
          id: category,
          name: category,
          description: `${category}相关文档`,
          count
        });
      });
      
      return categories;
    } catch (error) {
      console.error('获取文档分类失败:', error);
      throw new Error('无法获取文档分类');
    }
  }

  /**
   * 搜索文档
   */
  async searchDocuments(query: string, filters?: {
    category?: string;
    status?: string;
    type?: string;
  }): Promise<DocumentItem[]> {
    try {
      const documents = await this.getDocuments();
      
      return documents.filter(doc => {
        // 文本搜索
        const matchesQuery = !query || 
          doc.title.toLowerCase().includes(query.toLowerCase()) ||
          doc.description.toLowerCase().includes(query.toLowerCase()) ||
          doc.tags.some(tag => tag.toLowerCase().includes(query.toLowerCase()));
        
        // 分类筛选
        const matchesCategory = !filters?.category || 
          filters.category === 'all' || 
          doc.category === filters.category;
        
        // 状态筛选
        const matchesStatus = !filters?.status || 
          filters.status === 'all' || 
          doc.status === filters.status;
        
        // 类型筛选
        const matchesType = !filters?.type || 
          filters.type === 'all' || 
          doc.type === filters.type;
        
        return matchesQuery && matchesCategory && matchesStatus && matchesType;
      });
    } catch (error) {
      console.error('搜索文档失败:', error);
      throw new Error('无法搜索文档');
    }
  }

  /**
   * 获取模拟文档数据
   */
  private getMockDocuments(): DocumentItem[] {
    // 这里返回与页面中相同的文档数据
    // 在实际应用中，这些数据会从后端 API 获取
    return [
      {
        id: '1',
        title: 'README - 项目概述',
        description: '项目的主要说明文档，包含安装、配置和使用指南',
        filePath: '/docs/README.md',
        category: '项目文档',
        type: 'markdown',
        tags: ['入门', '安装', '配置'],
        lastUpdated: '2024-01-15',
        size: '15.2 KB',
        author: '项目团队',
        version: '1.0.0',
        status: 'published',
        isLocal: true
      },
      // ... 其他文档数据
    ];
  }

  /**
   * 获取模拟文档内容
   */
  private getMockDocumentContent(filePath: string): string {
    // 根据文件路径返回对应的模拟内容
    // 在实际应用中，这里会读取真实的文件内容
    switch (filePath) {
      case '/docs/README.md':
        return `# 问卷调查系统

## 项目概述
这是一个基于 React + Node.js 的问卷调查系统，支持用户提交问卷、管理员审核、数据分析等功能。

## 主要功能
- 用户管理和权限控制
- 问卷创建和提交
- 内容审核工作流
- 数据分析和报告
- 系统管理和监控

## 技术栈
- 前端：React 18, TypeScript, Tailwind CSS
- 后端：Node.js, Express, PostgreSQL
- 部署：Docker, Nginx`;
      
      default:
        return `# ${filePath}

这是 ${filePath} 文档的内容。

在实际应用中，这里会显示文件的真实内容。`;
    }
  }
}

export const documentService = new DocumentService();
export default documentService;
