-- AlterTable
ALTER TABLE "Story" ADD COLUMN "author" TEXT;
ALTER TABLE "Story" ADD COLUMN "category" TEXT;
ALTER TABLE "Story" ADD COLUMN "educationLevel" TEXT;
ALTER TABLE "Story" ADD COLUMN "industry" TEXT;
ALTER TABLE "Story" ADD COLUMN "tags" TEXT;

-- AlterTable
ALTER TABLE "User" ADD COLUMN "name" TEXT;

-- RedefineTables
PRAGMA defer_foreign_keys=ON;
PRAGMA foreign_keys=OFF;
CREATE TABLE "new_QuestionnaireResponse" (
    "id" INTEGER NOT NULL PRIMARY KEY AUTOINCREMENT,
    "userId" INTEGER,
    "sequenceNumber" TEXT,
    "isAnonymous" BOOLEAN NOT NULL DEFAULT true,
    "ipAddress" TEXT,
    "submittedById" TEXT,
    "educationLevel" TEXT,
    "major" TEXT,
    "graduationYear" INTEGER,
    "region" TEXT,
    "expectedPosition" TEXT,
    "expectedSalaryRange" TEXT,
    "expectedWorkHours" INTEGER,
    "expectedVacationDays" INTEGER,
    "employmentStatus" TEXT,
    "currentIndustry" TEXT,
    "currentPosition" TEXT,
    "monthlySalary" INTEGER,
    "jobSatisfaction" TEXT,
    "unemploymentDuration" TEXT,
    "unemploymentReason" TEXT,
    "jobHuntingDifficulty" INTEGER,
    "regretMajor" BOOLEAN,
    "preferredMajor" TEXT,
    "careerChangeIntention" BOOLEAN,
    "careerChangeTarget" TEXT,
    "adviceForStudents" TEXT,
    "observationOnEmployment" TEXT,
    "tags" TEXT,
    "status" TEXT NOT NULL DEFAULT 'normal',
    "createdAt" DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" DATETIME NOT NULL,
    CONSTRAINT "QuestionnaireResponse_userId_fkey" FOREIGN KEY ("userId") REFERENCES "User" ("id") ON DELETE SET NULL ON UPDATE CASCADE
);
INSERT INTO "new_QuestionnaireResponse" ("adviceForStudents", "careerChangeIntention", "careerChangeTarget", "createdAt", "currentIndustry", "currentPosition", "educationLevel", "employmentStatus", "expectedPosition", "expectedSalaryRange", "expectedVacationDays", "expectedWorkHours", "graduationYear", "id", "ipAddress", "isAnonymous", "jobHuntingDifficulty", "jobSatisfaction", "major", "observationOnEmployment", "preferredMajor", "region", "regretMajor", "submittedById", "unemploymentDuration", "unemploymentReason", "updatedAt", "userId") SELECT "adviceForStudents", "careerChangeIntention", "careerChangeTarget", "createdAt", "currentIndustry", "currentPosition", "educationLevel", "employmentStatus", "expectedPosition", "expectedSalaryRange", "expectedVacationDays", "expectedWorkHours", "graduationYear", "id", "ipAddress", "isAnonymous", "jobHuntingDifficulty", "jobSatisfaction", "major", "observationOnEmployment", "preferredMajor", "region", "regretMajor", "submittedById", "unemploymentDuration", "unemploymentReason", "updatedAt", "userId" FROM "QuestionnaireResponse";
DROP TABLE "QuestionnaireResponse";
ALTER TABLE "new_QuestionnaireResponse" RENAME TO "QuestionnaireResponse";
CREATE UNIQUE INDEX "QuestionnaireResponse_sequenceNumber_key" ON "QuestionnaireResponse"("sequenceNumber");
PRAGMA foreign_keys=ON;
PRAGMA defer_foreign_keys=OFF;
