/**
 * 管理员认证服务
 * 
 * 提供管理员登录、获取管理员信息等功能
 */

import { fetchData } from './unifiedDataService';

/**
 * 管理员登录
 * @param username 用户名
 * @param password 密码
 * @returns 登录结果
 */
export async function adminLogin(username: string, password: string) {
  return fetchData(
    '/admin/login',
    'adminLoginMock',
    { username, password },
    { method: 'POST' }
  );
}

/**
 * 获取管理员信息
 * @param id 管理员ID
 * @returns 管理员信息
 */
export async function getAdminInfo(id: number) {
  return fetchData(
    `/admin/${id}`,
    'getAdminInfoMock',
    { id },
    { method: 'GET' }
  );
}

/**
 * 获取管理员列表
 * @returns 管理员列表
 */
export async function getAdmins() {
  return fetchData(
    '/admin',
    'getAdminsMock',
    {},
    { method: 'GET' }
  );
}

/**
 * 添加管理员
 * @param adminData 管理员数据
 * @returns 添加结果
 */
export async function addAdmin(adminData: {
  username: string;
  password: string;
  name: string;
  role: string;
  permissions?: string[];
}) {
  return fetchData(
    '/admin',
    'addAdminMock',
    adminData,
    { method: 'POST' }
  );
}

/**
 * 更新管理员
 * @param id 管理员ID
 * @param adminData 管理员数据
 * @returns 更新结果
 */
export async function updateAdmin(
  id: number,
  adminData: {
    name?: string;
    password?: string;
    role?: string;
    permissions?: string[];
  }
) {
  return fetchData(
    `/admin/${id}`,
    'updateAdminMock',
    { id, ...adminData },
    { method: 'PUT' }
  );
}

/**
 * 删除管理员
 * @param id 管理员ID
 * @returns 删除结果
 */
export async function deleteAdmin(id: number) {
  return fetchData(
    `/admin/${id}`,
    'deleteAdminMock',
    { id },
    { method: 'DELETE' }
  );
}
