import { Hono } from 'hono';
import { z } from 'zod';
import { zValidator } from '@hono/zod-validator';
import { PrismaClient } from '@prisma/client';

// Define environment interface
interface Env {
  SURVEY_KV: KVNamespace;
  DATABASE_URL: string;
}

// Create Hono app
const app = new Hono<{ Bindings: Env }>();

// Define validation schema for questionnaire submission
const questionnaireSchema = z.object({
  // 1. Personal information
  educationLevel: z.string().optional(),
  major: z.string().optional(),
  graduationYear: z.number().optional(),
  region: z.string().optional(),
  
  // 2. Employment expectations
  expectedPosition: z.string().optional(),
  expectedSalaryRange: z.string().optional(),
  expectedWorkHours: z.number().optional(),
  expectedVacationDays: z.number().optional(),
  
  // 3. Work experience
  employmentStatus: z.string().optional(),
  currentIndustry: z.string().optional(),
  currentPosition: z.string().optional(),
  jobSatisfaction: z.number().min(1).max(5).optional(),
  
  // 4. Unemployment status
  unemploymentDuration: z.string().optional(),
  unemploymentReason: z.string().optional(),
  jobHuntingDifficulty: z.number().min(1).max(5).optional(),
  
  // 5. Career change and reflection
  regretMajor: z.boolean().optional(),
  preferredMajor: z.string().optional(),
  careerChangeIntention: z.boolean().optional(),
  careerChangeTarget: z.string().optional(),
  
  // 6. Advice and feedback
  adviceForStudents: z.string().optional(),
  observationOnEmployment: z.string().optional(),
  
  // Submission options
  isAnonymous: z.boolean().default(true),
  emailVerificationId: z.string().optional(),
});

// Submit questionnaire
app.post('/submit', zValidator('json', questionnaireSchema), async (c) => {
  const data = c.req.valid('json');
  const ip = c.req.header('CF-Connecting-IP') || c.req.header('X-Forwarded-For') || 'unknown';
  const userAgent = c.req.header('User-Agent') || 'unknown';
  
  try {
    // Initialize Prisma client
    const prisma = new PrismaClient({
      datasources: {
        db: {
          url: c.env.DATABASE_URL,
        },
      },
    });

    // Check if this is a verified submission
    let userId = null;
    if (data.emailVerificationId && !data.isAnonymous) {
      // Get verification data from KV
      const verification = await c.env.SURVEY_KV.get(
        `verification:${data.emailVerificationId}`, 
        'json'
      ) as { userId: number, verified: boolean } | null;
      
      if (verification && verification.verified) {
        userId = verification.userId;
      }
    }
    
    // Create questionnaire response
    const response = await prisma.questionnaireResponse.create({
      data: {
        userId: userId,
        isAnonymous: data.isAnonymous,
        ipAddress: ip,
        
        // 1. Personal information
        educationLevel: data.educationLevel,
        major: data.major,
        graduationYear: data.graduationYear,
        region: data.region,
        
        // 2. Employment expectations
        expectedPosition: data.expectedPosition,
        expectedSalaryRange: data.expectedSalaryRange,
        expectedWorkHours: data.expectedWorkHours,
        expectedVacationDays: data.expectedVacationDays,
        
        // 3. Work experience
        employmentStatus: data.employmentStatus,
        currentIndustry: data.currentIndustry,
        currentPosition: data.currentPosition,
        jobSatisfaction: data.jobSatisfaction,
        
        // 4. Unemployment status
        unemploymentDuration: data.unemploymentDuration,
        unemploymentReason: data.unemploymentReason,
        jobHuntingDifficulty: data.jobHuntingDifficulty,
        
        // 5. Career change and reflection
        regretMajor: data.regretMajor,
        preferredMajor: data.preferredMajor,
        careerChangeIntention: data.careerChangeIntention,
        careerChangeTarget: data.careerChangeTarget,
        
        // 6. Advice and feedback
        adviceForStudents: data.adviceForStudents,
        observationOnEmployment: data.observationOnEmployment,
      },
    });
    
    await prisma.$disconnect();
    
    return c.json({
      success: true,
      message: 'Questionnaire submitted successfully',
      responseId: response.id,
      verified: userId !== null,
    });
  } catch (error) {
    console.error('Error submitting questionnaire:', error);
    return c.json({ success: false, error: 'Failed to submit questionnaire' }, 500);
  }
});

// Get questionnaire statistics
app.get('/stats', async (c) => {
  try {
    // Initialize Prisma client
    const prisma = new PrismaClient({
      datasources: {
        db: {
          url: c.env.DATABASE_URL,
        },
      },
    });
    
    // Get total count
    const totalCount = await prisma.questionnaireResponse.count();
    
    // Get verified count
    const verifiedCount = await prisma.questionnaireResponse.count({
      where: {
        isAnonymous: false,
      },
    });
    
    // Get education level distribution
    const educationLevels = await prisma.questionnaireResponse.groupBy({
      by: ['educationLevel'],
      _count: true,
      where: {
        educationLevel: {
          not: null,
        },
      },
    });
    
    // Get region distribution
    const regions = await prisma.questionnaireResponse.groupBy({
      by: ['region'],
      _count: true,
      where: {
        region: {
          not: null,
        },
      },
    });
    
    await prisma.$disconnect();
    
    return c.json({
      success: true,
      stats: {
        totalCount,
        verifiedCount,
        anonymousCount: totalCount - verifiedCount,
        educationLevels: educationLevels.map(level => ({
          name: level.educationLevel,
          count: level._count,
        })),
        regions: regions.map(region => ({
          name: region.region,
          count: region._count,
        })),
      },
    });
  } catch (error) {
    console.error('Error getting questionnaire statistics:', error);
    return c.json({ success: false, error: 'Failed to get statistics' }, 500);
  }
});

export { app as questionnaire };
