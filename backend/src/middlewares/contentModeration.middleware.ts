/**
 * 内容审核中间件
 * 
 * 提供内容审核功能，可以同步或异步方式审核内容
 */

import { Context, Next } from 'hono';
import { Env } from '../types';
import { AutoModerationService } from '../services/autoModeration/autoModerationService';
import { AsyncModerationService } from '../services/autoModeration/asyncModerationService';
import { ContentType } from '../constants';

/**
 * 内容审核中间件选项
 */
interface ContentModerationOptions {
  contentType: ContentType;  // 内容类型
  contentField: string;      // 内容字段名
  async?: boolean;           // 是否异步审核
  strictMode?: boolean;      // 是否严格模式
  bypassKey?: string;        // 绕过审核的密钥字段
  bypassValue?: string;      // 绕过审核的密钥值
  extractContent?: (data: any) => string; // 自定义内容提取函数
}

/**
 * 创建内容审核中间件
 * @param options 中间件选项
 * @returns 中间件函数
 */
export const contentModeration = (options: ContentModerationOptions) => {
  return async (c: Context<{ Bindings: Env }>, next: Next) => {
    // 获取请求数据
    const data = c.req.valid('json');
    
    // 检查是否需要绕过审核
    if (options.bypassKey && options.bypassValue) {
      const bypassKeyValue = data[options.bypassKey];
      if (bypassKeyValue === options.bypassValue) {
        // 绕过审核
        await next();
        return;
      }
    }
    
    // 提取需要审核的内容
    let content: string;
    if (options.extractContent) {
      // 使用自定义函数提取内容
      content = options.extractContent(data);
    } else {
      // 使用默认字段提取内容
      content = data[options.contentField];
    }
    
    // 如果内容为空，跳过审核
    if (!content || content.trim() === '') {
      await next();
      return;
    }
    
    try {
      if (options.async) {
        // 异步审核
        await handleAsyncModeration(c, content, options.contentType, data);
      } else {
        // 同步审核
        await handleSyncModeration(c, content, options.contentType, data, options.strictMode);
      }
      
      // 继续处理请求
      await next();
    } catch (error) {
      console.error('内容审核失败:', error);
      return c.json({
        success: false,
        error: '内容审核失败，请稍后再试',
        code: 'MODERATION_ERROR'
      }, 500);
    }
  };
};

/**
 * 处理同步审核
 * @param c 上下文
 * @param content 内容
 * @param contentType 内容类型
 * @param data 请求数据
 * @param strictMode 是否严格模式
 */
async function handleSyncModeration(
  c: Context<{ Bindings: Env }>,
  content: string,
  contentType: ContentType,
  data: any,
  strictMode?: boolean
): Promise<void> {
  // 获取自动审核服务
  const autoModerationService = AutoModerationService.getInstance();
  
  // 检查是否启用自动审核
  if (!autoModerationService.isEnabled()) {
    return;
  }
  
  // 获取审核配置
  const config = autoModerationService.getConfig();
  
  // 检查内容类型是否启用审核
  if (!config.contentTypes.includes(contentType)) {
    return;
  }
  
  // 执行内容审核
  const result = await autoModerationService.moderateContent(content, {
    contentType,
    strictMode: strictMode || config.strictMode,
    env: c.env
  });
  
  // 根据审核结果处理
  if (result.action === 'reject') {
    // 拒绝内容
    throw new Error(result.message || '内容未通过审核');
  } else if (result.action === 'review') {
    // 标记为需要人工审核
    data._autoModeration = {
      action: 'review',
      confidence: result.confidence,
      issues: result.issues,
      explanation: result.explanation,
      severity: result.severity
    };
  } else {
    // 通过内容
    data._autoModeration = {
      action: 'approve',
      confidence: result.confidence
    };
  }
}

/**
 * 处理异步审核
 * @param c 上下文
 * @param content 内容
 * @param contentType 内容类型
 * @param data 请求数据
 */
async function handleAsyncModeration(
  c: Context<{ Bindings: Env }>,
  content: string,
  contentType: ContentType,
  data: any
): Promise<void> {
  // 获取异步审核服务
  const asyncModerationService = AsyncModerationService.getInstance();
  
  // 检查是否启用异步审核
  if (!asyncModerationService.isEnabled()) {
    return;
  }
  
  // 提交异步审核
  const pendingContent = await asyncModerationService.submitContent(content, {
    contentType,
    userId: data.userId || null,
    ip: c.req.header('CF-Connecting-IP') || c.req.header('X-Forwarded-For') || 'unknown',
    userAgent: c.req.header('User-Agent') || 'unknown',
    env: c.env
  });
  
  // 标记为待审核
  data._pendingModeration = {
    id: pendingContent.id,
    sequenceNumber: pendingContent.sequenceNumber
  };
}

/**
 * 创建问卷审核中间件
 * @param options 中间件选项
 * @returns 中间件函数
 */
export const questionnaireModeration = (options?: Partial<ContentModerationOptions>) => {
  return contentModeration({
    contentType: ContentType.QUESTIONNAIRE,
    contentField: 'adviceForStudents',
    extractContent: (data) => {
      // 提取问卷中的所有文本内容
      const textFields = [
        data.adviceForStudents,
        data.observationOnEmployment,
        data.preferredMajor,
        data.careerChangeTarget,
        data.unemploymentReason
      ];
      
      // 过滤掉空值并连接
      return textFields.filter(Boolean).join(' ');
    },
    ...options
  });
};

/**
 * 创建故事审核中间件
 * @param options 中间件选项
 * @returns 中间件函数
 */
export const storyModeration = (options?: Partial<ContentModerationOptions>) => {
  return contentModeration({
    contentType: ContentType.STORY,
    contentField: 'content',
    ...options
  });
};

/**
 * 创建评论审核中间件
 * @param options 中间件选项
 * @returns 中间件函数
 */
export const commentModeration = (options?: Partial<ContentModerationOptions>) => {
  return contentModeration({
    contentType: ContentType.COMMENT,
    contentField: 'content',
    ...options
  });
};

/**
 * 创建个人资料审核中间件
 * @param options 中间件选项
 * @returns 中间件函数
 */
export const profileModeration = (options?: Partial<ContentModerationOptions>) => {
  return contentModeration({
    contentType: ContentType.PROFILE,
    contentField: 'bio',
    extractContent: (data) => {
      // 提取个人资料中的所有文本内容
      const textFields = [
        data.bio,
        data.displayName,
        data.interests,
        data.skills
      ];
      
      // 过滤掉空值并连接
      return textFields.filter(Boolean).join(' ');
    },
    ...options
  });
};

/**
 * 创建反馈审核中间件
 * @param options 中间件选项
 * @returns 中间件函数
 */
export const feedbackModeration = (options?: Partial<ContentModerationOptions>) => {
  return contentModeration({
    contentType: ContentType.FEEDBACK,
    contentField: 'content',
    extractContent: (data) => {
      // 提取反馈中的所有文本内容
      const textFields = [
        data.reason,
        data.details
      ];
      
      // 过滤掉空值并连接
      return textFields.filter(Boolean).join(' ');
    },
    ...options
  });
};
