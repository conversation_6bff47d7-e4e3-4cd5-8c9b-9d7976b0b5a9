/**
 * 安全模块配置
 */

import { SecurityConfig } from './types';

/**
 * 默认配置 - 关闭
 */
const disabledConfig: SecurityConfig = {
  enabled: false,
  protectionLevel: 0,
  captcha: {
    enabled: false,
    type: 'none',
    triggerThreshold: 0
  },
  behaviorAnalysis: {
    enabled: false,
    minCompletionTime: 0,
    trackMouseMovements: false,
    trackKeyboardEvents: false,
    suspiciousScoreThreshold: 100
  },
  rateLimit: {
    enabled: false,
    ipLimit: 0,
    fingerprintLimit: 0,
    cooldownPeriod: 0
  },
  honeypot: {
    enabled: false,
    fieldCount: 0,
    silentRejection: true
  },
  fingerprinting: {
    enabled: false,
    storageTime: 0
  },
  logging: {
    enabled: true,
    logLevel: 'error',
    storeSubmissionData: false
  },
  errorAnalysis: {
    enabled: false,
    clusteringEnabled: false,
    anomalyDetectionEnabled: false,
    patternRecognitionEnabled: false,
    similarityThreshold: 0.7,
    anomalyThreshold: 2.0,
    minClusterSize: 3,
    maxClusters: 50,
    maxSamplesPerCluster: 5,
    analysisInterval: 3600000, // 1小时
    baselineWindowDays: 7
  },
  alerting: {
    enabled: false,
    channels: [],
    throttleInterval: 300000, // 5分钟
    minSeverity: 'high',
    groupSimilarAlerts: true,
    includeDetails: true,
    maxAlertsPerHour: 10
  },
  autoFix: {
    enabled: false,
    maxAttemptsPerError: 3,
    cooldownPeriod: 3600000, // 1小时
    requireApproval: true,
    notifyOnFix: true,
    fixStrategies: []
  }
};

/**
 * 默认配置 - 基础
 */
const basicConfig: SecurityConfig = {
  enabled: true,
  protectionLevel: 1,
  captcha: {
    enabled: true,
    type: 'turnstile',
    triggerThreshold: 5  // 5次提交后触发
  },
  behaviorAnalysis: {
    enabled: false,
    minCompletionTime: 0,
    trackMouseMovements: false,
    trackKeyboardEvents: false,
    suspiciousScoreThreshold: 100
  },
  rateLimit: {
    enabled: true,
    ipLimit: 10,  // 每IP每小时10次
    fingerprintLimit: 0,
    cooldownPeriod: 3600
  },
  honeypot: {
    enabled: false,
    fieldCount: 0,
    silentRejection: true
  },
  fingerprinting: {
    enabled: false,
    storageTime: 0
  },
  logging: {
    enabled: true,
    logLevel: 'warn',
    storeSubmissionData: false
  },
  errorAnalysis: {
    enabled: true,
    clusteringEnabled: false,
    anomalyDetectionEnabled: false,
    patternRecognitionEnabled: true,
    similarityThreshold: 0.7,
    anomalyThreshold: 2.0,
    minClusterSize: 3,
    maxClusters: 50,
    maxSamplesPerCluster: 5,
    analysisInterval: 3600000,
    baselineWindowDays: 7
  },
  alerting: {
    enabled: true,
    channels: [
      {
        type: 'console',
        enabled: true,
        name: 'Console'
      }
    ],
    throttleInterval: 300000,
    minSeverity: 'high',
    groupSimilarAlerts: true,
    includeDetails: true,
    maxAlertsPerHour: 10
  },
  autoFix: {
    enabled: false,
    maxAttemptsPerError: 3,
    cooldownPeriod: 3600000,
    requireApproval: true,
    notifyOnFix: true,
    fixStrategies: []
  }
};

/**
 * 默认配置 - 标准
 */
const standardConfig: SecurityConfig = {
  enabled: true,
  protectionLevel: 2,
  captcha: {
    enabled: true,
    type: 'turnstile',
    triggerThreshold: 3  // 3次提交后触发
  },
  behaviorAnalysis: {
    enabled: true,
    minCompletionTime: 30000,  // 30秒
    trackMouseMovements: true,
    trackKeyboardEvents: false,
    suspiciousScoreThreshold: 50
  },
  rateLimit: {
    enabled: true,
    ipLimit: 5,  // 每IP每小时5次
    fingerprintLimit: 0,
    cooldownPeriod: 3600
  },
  honeypot: {
    enabled: true,
    fieldCount: 1,
    silentRejection: true
  },
  fingerprinting: {
    enabled: false,
    storageTime: 0
  },
  logging: {
    enabled: true,
    logLevel: 'warn',
    storeSubmissionData: false
  },
  errorAnalysis: {
    enabled: true,
    clusteringEnabled: true,
    anomalyDetectionEnabled: true,
    patternRecognitionEnabled: true,
    similarityThreshold: 0.7,
    anomalyThreshold: 2.0,
    minClusterSize: 3,
    maxClusters: 50,
    maxSamplesPerCluster: 5,
    analysisInterval: 3600000,
    baselineWindowDays: 7
  },
  alerting: {
    enabled: true,
    channels: [
      {
        type: 'console',
        enabled: true,
        name: 'Console'
      }
    ],
    throttleInterval: 300000,
    minSeverity: 'medium',
    groupSimilarAlerts: true,
    includeDetails: true,
    maxAlertsPerHour: 20
  },
  autoFix: {
    enabled: true,
    maxAttemptsPerError: 3,
    cooldownPeriod: 3600000,
    requireApproval: true,
    notifyOnFix: true,
    fixStrategies: [
      {
        id: 'fix_rate_limit',
        name: 'Clear Rate Limit',
        description: 'Clears rate limit for a specific IP address',
        enabled: true,
        targetErrorCodes: ['RATE_LIMIT_EXCEEDED', 'RATE_LIMIT_ERROR'],
        action: 'clearRateLimit'
      },
      {
        id: 'fix_honeypot',
        name: 'Block Bot IP',
        description: 'Blocks IP address that triggered honeypot',
        enabled: true,
        targetErrorCodes: ['HONEYPOT_TRIGGERED'],
        action: 'blockIP',
        parameters: {
          duration: '24h'
        }
      }
    ]
  }
};

/**
 * 默认配置 - 增强
 */
const enhancedConfig: SecurityConfig = {
  enabled: true,
  protectionLevel: 3,
  captcha: {
    enabled: true,
    type: 'turnstile',
    triggerThreshold: 1  // 每次提交都触发
  },
  behaviorAnalysis: {
    enabled: true,
    minCompletionTime: 60000,  // 60秒
    trackMouseMovements: true,
    trackKeyboardEvents: true,
    suspiciousScoreThreshold: 30
  },
  rateLimit: {
    enabled: true,
    ipLimit: 3,  // 每IP每小时3次
    fingerprintLimit: 5,  // 每指纹每小时5次
    cooldownPeriod: 7200  // 2小时
  },
  honeypot: {
    enabled: true,
    fieldCount: 2,
    silentRejection: true
  },
  fingerprinting: {
    enabled: true,
    storageTime: 7  // 存储7天
  },
  logging: {
    enabled: true,
    logLevel: 'info',
    storeSubmissionData: false
  },
  errorAnalysis: {
    enabled: true,
    clusteringEnabled: true,
    anomalyDetectionEnabled: true,
    patternRecognitionEnabled: true,
    similarityThreshold: 0.6,
    anomalyThreshold: 1.5,
    minClusterSize: 2,
    maxClusters: 100,
    maxSamplesPerCluster: 10,
    analysisInterval: 1800000, // 30分钟
    baselineWindowDays: 14
  },
  alerting: {
    enabled: true,
    channels: [
      {
        type: 'console',
        enabled: true,
        name: 'Console'
      },
      {
        type: 'webhook',
        enabled: true,
        name: 'Webhook',
        webhookUrl: 'https://example.com/webhook',
        minSeverity: 'medium'
      }
    ],
    throttleInterval: 180000, // 3分钟
    minSeverity: 'medium',
    groupSimilarAlerts: true,
    includeDetails: true,
    maxAlertsPerHour: 30
  },
  autoFix: {
    enabled: true,
    maxAttemptsPerError: 5,
    cooldownPeriod: 1800000, // 30分钟
    requireApproval: false,
    notifyOnFix: true,
    fixStrategies: [
      {
        id: 'fix_rate_limit',
        name: 'Clear Rate Limit',
        description: 'Clears rate limit for a specific IP address',
        enabled: true,
        targetErrorCodes: ['RATE_LIMIT_EXCEEDED', 'RATE_LIMIT_ERROR'],
        action: 'clearRateLimit'
      },
      {
        id: 'fix_fingerprint',
        name: 'Reset Fingerprint',
        description: 'Resets fingerprint tracking data',
        enabled: true,
        targetErrorCodes: ['FINGERPRINT_LIMIT_EXCEEDED', 'FINGERPRINT_TRACKING_FAILED'],
        action: 'resetFingerprint'
      },
      {
        id: 'fix_honeypot',
        name: 'Block Bot IP',
        description: 'Blocks IP address that triggered honeypot',
        enabled: true,
        targetErrorCodes: ['HONEYPOT_TRIGGERED'],
        action: 'blockIP',
        parameters: {
          duration: '24h'
        }
      },
      {
        id: 'fix_suspicious',
        name: 'Block Suspicious IP',
        description: 'Blocks IP address with suspicious activity',
        enabled: true,
        targetErrorCodes: ['SUSPICIOUS_ACTIVITY'],
        action: 'blockIP',
        parameters: {
          duration: '12h'
        }
      }
    ]
  }
};

/**
 * 默认配置 - 最高
 */
const maximumConfig: SecurityConfig = {
  enabled: true,
  protectionLevel: 4,
  captcha: {
    enabled: true,
    type: 'turnstile',
    triggerThreshold: 1  // 每次提交都触发
  },
  behaviorAnalysis: {
    enabled: true,
    minCompletionTime: 90000,  // 90秒
    trackMouseMovements: true,
    trackKeyboardEvents: true,
    suspiciousScoreThreshold: 20
  },
  rateLimit: {
    enabled: true,
    ipLimit: 2,  // 每IP每小时2次
    fingerprintLimit: 3,  // 每指纹每小时3次
    cooldownPeriod: 14400  // 4小时
  },
  honeypot: {
    enabled: true,
    fieldCount: 3,
    silentRejection: false  // 明确拒绝
  },
  fingerprinting: {
    enabled: true,
    storageTime: 30  // 存储30天
  },
  logging: {
    enabled: true,
    logLevel: 'debug',
    storeSubmissionData: true
  },
  errorAnalysis: {
    enabled: true,
    clusteringEnabled: true,
    anomalyDetectionEnabled: true,
    patternRecognitionEnabled: true,
    similarityThreshold: 0.5,
    anomalyThreshold: 1.2,
    minClusterSize: 2,
    maxClusters: 200,
    maxSamplesPerCluster: 20,
    analysisInterval: 900000, // 15分钟
    baselineWindowDays: 30
  },
  alerting: {
    enabled: true,
    channels: [
      {
        type: 'console',
        enabled: true,
        name: 'Console'
      },
      {
        type: 'webhook',
        enabled: true,
        name: 'Webhook',
        webhookUrl: 'https://example.com/webhook',
        minSeverity: 'low'
      },
      {
        type: 'email',
        enabled: true,
        name: 'Email',
        recipients: ['<EMAIL>'],
        minSeverity: 'medium'
      }
    ],
    throttleInterval: 60000, // 1分钟
    minSeverity: 'low',
    groupSimilarAlerts: false,
    includeDetails: true,
    maxAlertsPerHour: 60
  },
  autoFix: {
    enabled: true,
    maxAttemptsPerError: 10,
    cooldownPeriod: 900000, // 15分钟
    requireApproval: false,
    notifyOnFix: true,
    fixStrategies: [
      {
        id: 'fix_rate_limit',
        name: 'Clear Rate Limit',
        description: 'Clears rate limit for a specific IP address',
        enabled: true,
        targetErrorCodes: ['RATE_LIMIT_EXCEEDED', 'RATE_LIMIT_ERROR'],
        action: 'clearRateLimit'
      },
      {
        id: 'fix_fingerprint',
        name: 'Reset Fingerprint',
        description: 'Resets fingerprint tracking data',
        enabled: true,
        targetErrorCodes: ['FINGERPRINT_LIMIT_EXCEEDED', 'FINGERPRINT_TRACKING_FAILED'],
        action: 'resetFingerprint'
      },
      {
        id: 'fix_config',
        name: 'Fix Configuration',
        description: 'Updates invalid configuration settings',
        enabled: true,
        targetErrorCodes: ['CONFIGURATION_ERROR'],
        action: 'updateConfiguration',
        parameters: {
          configPath: '',
          configValue: ''
        }
      },
      {
        id: 'fix_honeypot',
        name: 'Block Bot IP',
        description: 'Blocks IP address that triggered honeypot',
        enabled: true,
        targetErrorCodes: ['HONEYPOT_TRIGGERED'],
        action: 'blockIP',
        parameters: {
          duration: '72h'
        }
      },
      {
        id: 'fix_suspicious',
        name: 'Block Suspicious IP',
        description: 'Blocks IP address with suspicious activity',
        enabled: true,
        targetErrorCodes: ['SUSPICIOUS_ACTIVITY'],
        action: 'blockIP',
        parameters: {
          duration: '24h'
        }
      },
      {
        id: 'fix_storage',
        name: 'Fix Storage Error',
        description: 'Attempts to fix storage errors',
        enabled: true,
        targetErrorCodes: ['STORAGE_ERROR'],
        action: 'restartService',
        parameters: {
          serviceName: 'storage'
        }
      }
    ]
  }
};

/**
 * 默认配置数组
 */
export const defaultConfigs: SecurityConfig[] = [
  disabledConfig,    // 0: 关闭
  basicConfig,       // 1: 基础
  standardConfig,    // 2: 标准
  enhancedConfig,    // 3: 增强
  maximumConfig      // 4: 最高
];
