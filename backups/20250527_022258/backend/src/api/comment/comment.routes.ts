/**
 * 评论路由
 * 
 * 处理评论相关的路由
 */

import { Hono } from 'hono';
import { Env } from '../../types';
import * as commentController from './comment.controller';
import { rateLimit } from '../../middlewares/rateLimit';
import { commentModeration } from '../../middlewares/contentModeration.middleware';
import { authMiddleware } from '../../middlewares/auth.middleware';

// 创建路由
const app = new Hono<{ Bindings: Env }>();

// 提交评论
app.post('/submit', 
  authMiddleware,
  // 使用同步审核中间件
  commentModeration({ async: false }),
  ...commentController.submitComment
);

// 获取评论列表
app.get('/', commentController.getComments);

// 获取评论详情
app.get('/:id', commentController.getCommentById);

// 点赞评论
app.post('/:id/like', authMiddleware, commentController.likeComment);

// 回复评论
app.post('/:id/reply', 
  authMiddleware,
  // 使用同步审核中间件
  commentModeration({ async: false }),
  commentController.replyComment
);

// 删除评论
app.delete('/:id', authMiddleware, commentController.deleteComment);

export default app;
