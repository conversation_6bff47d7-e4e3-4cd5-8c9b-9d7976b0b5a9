#!/usr/bin/env node

/**
 * 🧪 性能验证器
 * 验证优化效果，对比优化前后的性能差异
 */

const fs = require('fs');

class PerformanceValidator {
  constructor() {
    this.baseUrl = process.env.API_BASE_URL || 'https://college-employment-survey.aibook2099.workers.dev';
    this.results = {
      timestamp: new Date().toISOString(),
      baseUrl: this.baseUrl,
      tests: {},
      summary: {},
      recommendations: []
    };
  }

  // 发送HTTP请求
  async makeRequest(url, options = {}) {
    const startTime = Date.now();
    
    try {
      const response = await fetch(url, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
          'User-Agent': 'Performance-Validator/1.0'
        },
        ...options
      });

      const data = await response.json();
      const duration = Date.now() - startTime;

      return {
        success: response.ok,
        status: response.status,
        data,
        duration,
        size: JSON.stringify(data).length
      };
    } catch (error) {
      const duration = Date.now() - startTime;
      return {
        success: false,
        error: error.message,
        duration,
        size: 0
      };
    }
  }

  // 运行多次测试并计算统计数据
  async runMultipleTests(name, url, iterations = 10) {
    console.log(`🔍 测试 ${name} (${iterations}次迭代)`);

    const results = [];
    
    // 预热
    await this.makeRequest(url);
    console.log('  🔥 预热完成');

    // 正式测试
    for (let i = 0; i < iterations; i++) {
      const result = await this.makeRequest(url);
      results.push(result);
      
      if (result.success) {
        console.log(`    测试 ${i + 1}/${iterations}: ${result.duration}ms`);
      } else {
        console.log(`    测试 ${i + 1}/${iterations}: 失败 - ${result.error}`);
      }
    }

    // 计算统计数据
    const successfulResults = results.filter(r => r.success);
    const durations = successfulResults.map(r => r.duration);
    
    const stats = durations.length > 0 ? {
      count: durations.length,
      min: Math.min(...durations),
      max: Math.max(...durations),
      avg: Math.round((durations.reduce((a, b) => a + b, 0) / durations.length) * 100) / 100,
      median: durations.sort((a, b) => a - b)[Math.floor(durations.length / 2)],
      p95: durations.sort((a, b) => a - b)[Math.floor(durations.length * 0.95)],
      p99: durations.sort((a, b) => a - b)[Math.floor(durations.length * 0.99)],
      successRate: Math.round((successfulResults.length / results.length) * 100),
      avgSize: Math.round(successfulResults.reduce((sum, r) => sum + r.size, 0) / successfulResults.length)
    } : null;

    this.results.tests[name] = {
      url,
      iterations,
      results,
      statistics: stats
    };

    return stats;
  }

  // 对比测试
  async runComparisonTest() {
    console.log('🚀 开始性能对比测试...\n');

    // 测试原版API
    console.log('📊 测试原版问卷统计API...');
    const originalStats = await this.runMultipleTests(
      'original_stats',
      `${this.baseUrl}/api/questionnaire/stats`,
      15
    );

    console.log('');

    // 测试优化版API（如果存在）
    console.log('⚡ 测试优化版问卷统计API...');
    const optimizedStats = await this.runMultipleTests(
      'optimized_stats',
      `${this.baseUrl}/api/questionnaire/stats/optimized`,
      15
    );

    console.log('');

    // 测试实时统计API（如果存在）
    console.log('🔄 测试实时统计API...');
    const realtimeStats = await this.runMultipleTests(
      'realtime_stats',
      `${this.baseUrl}/api/questionnaire/realtime-stats/optimized`,
      15
    );

    console.log('');

    // 生成对比分析
    this.generateComparison(originalStats, optimizedStats, realtimeStats);
  }

  // 生成对比分析
  generateComparison(original, optimized, realtime) {
    console.log('📊 生成性能对比分析...');

    const comparison = {
      original: original,
      optimized: optimized,
      realtime: realtime,
      improvements: {}
    };

    // 计算改进幅度
    if (original && optimized) {
      comparison.improvements.optimized = {
        avgImprovement: original.avg - optimized.avg,
        avgImprovementPercent: Math.round(((original.avg - optimized.avg) / original.avg) * 100),
        p95Improvement: original.p95 - optimized.p95,
        p95ImprovementPercent: Math.round(((original.p95 - optimized.p95) / original.p95) * 100),
        reliabilityImprovement: optimized.successRate - original.successRate
      };
    }

    if (original && realtime) {
      comparison.improvements.realtime = {
        avgImprovement: original.avg - realtime.avg,
        avgImprovementPercent: Math.round(((original.avg - realtime.avg) / original.avg) * 100),
        sizeReduction: original.avgSize - realtime.avgSize,
        sizeReductionPercent: Math.round(((original.avgSize - realtime.avgSize) / original.avgSize) * 100)
      };
    }

    this.results.comparison = comparison;

    // 输出对比结果
    console.log('\n📈 性能对比结果:');
    
    if (original) {
      console.log(`原版API: 平均 ${original.avg}ms, P95 ${original.p95}ms, 成功率 ${original.successRate}%`);
    }
    
    if (optimized) {
      console.log(`优化版API: 平均 ${optimized.avg}ms, P95 ${optimized.p95}ms, 成功率 ${optimized.successRate}%`);
      
      if (comparison.improvements.optimized) {
        const imp = comparison.improvements.optimized;
        console.log(`  ⚡ 性能提升: ${imp.avgImprovementPercent}% (${imp.avgImprovement}ms)`);
        console.log(`  📊 P95提升: ${imp.p95ImprovementPercent}% (${imp.p95Improvement}ms)`);
      }
    }
    
    if (realtime) {
      console.log(`实时API: 平均 ${realtime.avg}ms, P95 ${realtime.p95}ms, 大小 ${realtime.avgSize} bytes`);
      
      if (comparison.improvements.realtime) {
        const imp = comparison.improvements.realtime;
        console.log(`  ⚡ 性能提升: ${imp.avgImprovementPercent}% (${imp.avgImprovement}ms)`);
        console.log(`  📦 大小减少: ${imp.sizeReductionPercent}% (${imp.sizeReduction} bytes)`);
      }
    }
  }

  // 运行负载测试
  async runLoadTest() {
    console.log('\n🔄 开始负载测试...');

    const endpoint = `${this.baseUrl}/api/questionnaire/stats`;
    const concurrencyLevels = [1, 5, 10, 20, 50];
    const loadTestResults = {};

    for (const concurrency of concurrencyLevels) {
      console.log(`\n  测试并发级别: ${concurrency}`);
      
      const promises = [];
      const startTime = Date.now();

      // 创建并发请求
      for (let i = 0; i < concurrency; i++) {
        promises.push(this.makeRequest(endpoint));
      }

      try {
        const results = await Promise.allSettled(promises);
        const totalTime = Date.now() - startTime;
        
        const successful = results.filter(r => r.status === 'fulfilled' && r.value.success);
        const failed = results.filter(r => r.status === 'rejected' || !r.value.success);
        
        const durations = successful.map(r => r.value.duration);
        const avgDuration = durations.length > 0 ? 
          durations.reduce((a, b) => a + b, 0) / durations.length : 0;

        const throughput = (successful.length / totalTime) * 1000; // requests per second

        loadTestResults[concurrency] = {
          totalTime,
          successful: successful.length,
          failed: failed.length,
          avgDuration: Math.round(avgDuration * 100) / 100,
          throughput: Math.round(throughput * 100) / 100,
          successRate: Math.round((successful.length / concurrency) * 100)
        };

        console.log(`    成功: ${successful.length}/${concurrency}, 平均耗时: ${loadTestResults[concurrency].avgDuration}ms`);
        console.log(`    吞吐量: ${loadTestResults[concurrency].throughput} req/s`);

      } catch (error) {
        console.log(`    错误: ${error.message}`);
        loadTestResults[concurrency] = {
          error: error.message
        };
      }
    }

    this.results.loadTest = loadTestResults;
  }

  // 生成性能等级
  calculatePerformanceGrade(avgTime) {
    if (avgTime < 100) return 'A+ (优秀)';
    if (avgTime < 200) return 'A (良好)';
    if (avgTime < 500) return 'B (一般)';
    if (avgTime < 1000) return 'C (较差)';
    return 'D (差)';
  }

  // 生成建议
  generateRecommendations() {
    console.log('\n💡 生成性能建议...');

    const recommendations = [];
    const comparison = this.results.comparison;

    // 基于对比结果的建议
    if (comparison?.improvements?.optimized) {
      const imp = comparison.improvements.optimized;
      
      if (imp.avgImprovementPercent > 30) {
        recommendations.push({
          type: 'success',
          title: '优化效果显著',
          description: `优化版API性能提升 ${imp.avgImprovementPercent}%，建议全面部署`,
          priority: 'high'
        });
      } else if (imp.avgImprovementPercent > 10) {
        recommendations.push({
          type: 'info',
          title: '优化效果良好',
          description: `优化版API性能提升 ${imp.avgImprovementPercent}%，可以考虑部署`,
          priority: 'medium'
        });
      } else {
        recommendations.push({
          type: 'warning',
          title: '优化效果有限',
          description: `优化版API性能提升仅 ${imp.avgImprovementPercent}%，需要进一步优化`,
          priority: 'medium'
        });
      }
    }

    // 基于负载测试的建议
    if (this.results.loadTest) {
      const loadResults = Object.values(this.results.loadTest);
      const hasErrors = loadResults.some(r => r.error || r.successRate < 95);
      
      if (hasErrors) {
        recommendations.push({
          type: 'warning',
          title: '负载测试发现问题',
          description: '高并发下出现错误或成功率下降，需要优化并发处理',
          priority: 'high'
        });
      }
    }

    // 通用建议
    recommendations.push({
      type: 'info',
      title: '持续监控',
      description: '建议定期运行性能测试，监控生产环境性能指标',
      priority: 'low'
    });

    this.results.recommendations = recommendations;

    // 输出建议
    console.log('\n📋 性能建议:');
    recommendations.forEach((rec, index) => {
      console.log(`${index + 1}. [${rec.priority.toUpperCase()}] ${rec.title}`);
      console.log(`   ${rec.description}`);
    });
  }

  // 保存结果
  saveResults() {
    const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
    const filename = `performance-validation-${timestamp}.json`;
    
    fs.writeFileSync(filename, JSON.stringify(this.results, null, 2));
    console.log(`\n📄 性能验证报告已保存: ${filename}`);

    // 生成简化的总结报告
    const summary = {
      timestamp: this.results.timestamp,
      baseUrl: this.results.baseUrl,
      summary: {
        testsRun: Object.keys(this.results.tests).length,
        overallGrade: this.calculateOverallGrade(),
        keyFindings: this.extractKeyFindings()
      }
    };

    const summaryFilename = `performance-summary-${timestamp}.json`;
    fs.writeFileSync(summaryFilename, JSON.stringify(summary, null, 2));
    console.log(`📄 性能总结报告已保存: ${summaryFilename}`);
  }

  // 计算总体等级
  calculateOverallGrade() {
    const tests = Object.values(this.results.tests);
    if (tests.length === 0) return 'N/A';

    const avgTimes = tests
      .map(t => t.statistics?.avg)
      .filter(t => t !== null && t !== undefined);

    if (avgTimes.length === 0) return 'N/A';

    const overallAvg = avgTimes.reduce((a, b) => a + b, 0) / avgTimes.length;
    return this.calculatePerformanceGrade(overallAvg);
  }

  // 提取关键发现
  extractKeyFindings() {
    const findings = [];
    
    if (this.results.comparison?.improvements?.optimized) {
      const imp = this.results.comparison.improvements.optimized;
      findings.push(`优化版API性能提升 ${imp.avgImprovementPercent}%`);
    }

    if (this.results.loadTest) {
      const maxConcurrency = Math.max(...Object.keys(this.results.loadTest).map(Number));
      findings.push(`支持最大并发: ${maxConcurrency}`);
    }

    return findings;
  }

  // 运行完整验证
  async runValidation() {
    console.log('🚀 开始性能验证...\n');

    try {
      await this.runComparisonTest();
      await this.runLoadTest();
      this.generateRecommendations();
      this.saveResults();

      console.log('\n🎉 性能验证完成!');
      console.log(`📊 总体等级: ${this.calculateOverallGrade()}`);

    } catch (error) {
      console.error('❌ 性能验证失败:', error);
      process.exit(1);
    }
  }
}

// 主函数
async function main() {
  const validator = new PerformanceValidator();
  await validator.runValidation();
}

if (require.main === module) {
  main().catch(console.error);
}

module.exports = PerformanceValidator;
