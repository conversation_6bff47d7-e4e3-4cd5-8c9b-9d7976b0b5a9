import { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '../ui/table.tsx';
import { Badge } from '@/components/ui/badge';
import { <PERSON><PERSON>, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { ScrollArea } from '../ui/scroll-area.tsx';
import { useToast } from '@/components/ui/use-toast';

interface QuestionnaireResponse {
  id: number;
  sequenceNumber: string; // 添加顺序编号字段
  isAnonymous: boolean;
  educationLevel?: string;
  major?: string;
  graduationYear?: number;
  region?: string;
  employmentStatus?: string;
  createdAt: string;
  [key: string]: any;
}

export default function ResponsesPanel() {
  const { toast } = useToast();
  const [responses, setResponses] = useState<QuestionnaireResponse[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [selectedResponse, setSelectedResponse] = useState<QuestionnaireResponse | null>(null);

  // 获取问卷回复
  const fetchResponses = async () => {
    try {
      setIsLoading(true);
      setError(null);

      const token = localStorage.getItem('adminToken');
      const response = await fetch(`${import.meta.env.VITE_API_BASE_URL}/admin/responses`, {
        headers: {
          'Authorization': `Bearer ${token}`,
        },
      });

      const data = await response.json();

      if (data.success) {
        setResponses(data.responses);
      } else {
        setError(data.error || '获取问卷回复失败');
      }
    } catch (error) {
      console.error('获取问卷回复错误:', error);
      setError('服务器错误，请稍后再试');
    } finally {
      setIsLoading(false);
    }
  };

  // 组件加载时获取问卷回复
  useEffect(() => {
    fetchResponses();
  }, []);

  // 查看详细信息
  const handleViewDetails = (response: QuestionnaireResponse) => {
    setSelectedResponse(response);
  };

  if (isLoading) {
    return <p>加载中...</p>;
  }

  if (error) {
    return (
      <div className="p-4 bg-red-50 text-red-500 rounded-md">
        <p>错误: {error}</p>
        <Button variant="outline" className="mt-2" onClick={fetchResponses}>
          重试
        </Button>
      </div>
    );
  }

  if (responses.length === 0) {
    return (
      <div className="text-center p-8">
        <p className="text-gray-500">没有问卷回复数据</p>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <h3 className="text-lg font-medium">问卷回复 ({responses.length})</h3>
        <Button variant="outline" onClick={fetchResponses}>
          刷新
        </Button>
      </div>

      <div className="rounded-md border">
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead>编号</TableHead>
              <TableHead>ID</TableHead>
              <TableHead>提交类型</TableHead>
              <TableHead>学历</TableHead>
              <TableHead>专业</TableHead>
              <TableHead>毕业年份</TableHead>
              <TableHead>地区</TableHead>
              <TableHead>就业状态</TableHead>
              <TableHead>提交时间</TableHead>
              <TableHead>操作</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {responses.map((response) => (
              <TableRow key={response.id}>
                <TableCell>
                  <Badge variant="outline">
                    {response.sequenceNumber}
                  </Badge>
                </TableCell>
                <TableCell>{response.id}</TableCell>
                <TableCell>
                  <Badge variant={response.isAnonymous ? "secondary" : "outline"}>
                    {response.isAnonymous ? '匿名' : '已验证'}
                  </Badge>
                </TableCell>
                <TableCell>{response.educationLevel || '-'}</TableCell>
                <TableCell>{response.major || '-'}</TableCell>
                <TableCell>{response.graduationYear || '-'}</TableCell>
                <TableCell>{response.region || '-'}</TableCell>
                <TableCell>{response.employmentStatus || '-'}</TableCell>
                <TableCell>{new Date(response.createdAt).toLocaleString('zh-CN')}</TableCell>
                <TableCell>
                  <Dialog>
                    <DialogTrigger asChild>
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => handleViewDetails(response)}
                      >
                        详情
                      </Button>
                    </DialogTrigger>
                    <DialogContent className="max-w-3xl">
                      <DialogHeader>
                        <DialogTitle>问卷回复详情 (编号: {selectedResponse?.sequenceNumber}, ID: {selectedResponse?.id})</DialogTitle>
                      </DialogHeader>
                      <ScrollArea className="max-h-[70vh] mt-4">
                        {selectedResponse && (
                          <div className="space-y-4">
                            <div className="grid grid-cols-2 gap-4">
                              <div>
                                <h4 className="font-medium">基本信息</h4>
                                <ul className="mt-2 space-y-1">
                                  <li><span className="text-gray-500">提交类型:</span> {selectedResponse.isAnonymous ? '匿名' : '已验证'}</li>
                                  <li><span className="text-gray-500">学历:</span> {selectedResponse.educationLevel || '-'}</li>
                                  <li><span className="text-gray-500">专业:</span> {selectedResponse.major || '-'}</li>
                                  <li><span className="text-gray-500">毕业年份:</span> {selectedResponse.graduationYear || '-'}</li>
                                  <li><span className="text-gray-500">地区:</span> {selectedResponse.region || '-'}</li>
                                </ul>
                              </div>
                              <div>
                                <h4 className="font-medium">就业期望</h4>
                                <ul className="mt-2 space-y-1">
                                  <li><span className="text-gray-500">期望职位:</span> {selectedResponse.expectedPosition || '-'}</li>
                                  <li><span className="text-gray-500">期望薪资范围:</span> {selectedResponse.expectedSalaryRange || '-'}</li>
                                  <li><span className="text-gray-500">期望工作时长:</span> {selectedResponse.expectedWorkHours || '-'}</li>
                                  <li><span className="text-gray-500">期望年假天数:</span> {selectedResponse.expectedVacationDays || '-'}</li>
                                </ul>
                              </div>
                            </div>

                            <div className="grid grid-cols-2 gap-4">
                              <div>
                                <h4 className="font-medium">工作经验</h4>
                                <ul className="mt-2 space-y-1">
                                  <li><span className="text-gray-500">就业状态:</span> {selectedResponse.employmentStatus || '-'}</li>
                                  <li><span className="text-gray-500">当前行业:</span> {selectedResponse.currentIndustry || '-'}</li>
                                  <li><span className="text-gray-500">当前职位:</span> {selectedResponse.currentPosition || '-'}</li>
                                  <li><span className="text-gray-500">工作满意度:</span> {selectedResponse.jobSatisfaction || '-'}</li>
                                </ul>
                              </div>
                              <div>
                                <h4 className="font-medium">失业状况</h4>
                                <ul className="mt-2 space-y-1">
                                  <li><span className="text-gray-500">失业时长:</span> {selectedResponse.unemploymentDuration || '-'}</li>
                                  <li><span className="text-gray-500">失业原因:</span> {selectedResponse.unemploymentReason || '-'}</li>
                                  <li><span className="text-gray-500">求职难度:</span> {selectedResponse.jobHuntingDifficulty || '-'}</li>
                                </ul>
                              </div>
                            </div>

                            <div className="grid grid-cols-2 gap-4">
                              <div>
                                <h4 className="font-medium">职业变化与反思</h4>
                                <ul className="mt-2 space-y-1">
                                  <li><span className="text-gray-500">是否后悔所学专业:</span> {selectedResponse.regretMajor ? '是' : '否'}</li>
                                  <li><span className="text-gray-500">理想专业:</span> {selectedResponse.preferredMajor || '-'}</li>
                                  <li><span className="text-gray-500">是否有转行意向:</span> {selectedResponse.careerChangeIntention ? '是' : '否'}</li>
                                  <li><span className="text-gray-500">转行目标:</span> {selectedResponse.careerChangeTarget || '-'}</li>
                                </ul>
                              </div>
                              <div>
                                <h4 className="font-medium">建议与反馈</h4>
                                <ul className="mt-2 space-y-1">
                                  <li><span className="text-gray-500">给在校生的建议:</span> {selectedResponse.adviceForStudents || '-'}</li>
                                  <li><span className="text-gray-500">对就业市场的观察:</span> {selectedResponse.observationOnEmployment || '-'}</li>
                                </ul>
                              </div>
                            </div>

                            <div>
                              <h4 className="font-medium">提交信息</h4>
                              <ul className="mt-2 space-y-1">
                                <li><span className="text-gray-500">提交时间:</span> {new Date(selectedResponse.createdAt).toLocaleString('zh-CN')}</li>
                                <li><span className="text-gray-500">更新时间:</span> {new Date(selectedResponse.updatedAt).toLocaleString('zh-CN')}</li>
                                <li><span className="text-gray-500">IP地址:</span> {selectedResponse.ipAddress || '-'}</li>
                              </ul>
                            </div>
                          </div>
                        )}
                      </ScrollArea>
                    </DialogContent>
                  </Dialog>
                </TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </div>
    </div>
  );
}
