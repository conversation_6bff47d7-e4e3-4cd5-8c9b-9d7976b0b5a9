import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Ta<PERSON>, Ta<PERSON>Content, Ta<PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import { AlertCircle, CheckCircle, Clock, Database, Globe, Server } from 'lucide-react';

interface DiagnosticResult {
  timestamp: string;
  status: 'success' | 'error' | 'loading';
  data?: any;
  error?: string;
  duration?: number;
}

interface SystemDiagnosticsProps {
  className?: string;
}

export const SystemDiagnostics: React.FC<SystemDiagnosticsProps> = ({ className = '' }) => {
  const [diagnostics, setDiagnostics] = useState<{
    frontend: DiagnosticResult;
    backend: DiagnosticResult;
    api: DiagnosticResult;
    trace: DiagnosticResult;
  }>({
    frontend: { timestamp: '', status: 'loading' },
    backend: { timestamp: '', status: 'loading' },
    api: { timestamp: '', status: 'loading' },
    trace: { timestamp: '', status: 'loading' }
  });

  const [isRunning, setIsRunning] = useState(false);

  // 获取API基础URL
  const getApiBaseUrl = () => {
    return import.meta.env.VITE_API_BASE_URL || 'https://college-employment-survey.aibook2099.workers.dev';
  };

  // 前端环境检查
  const checkFrontendEnvironment = async (): Promise<DiagnosticResult> => {
    const startTime = Date.now();
    try {
      const result = {
        timestamp: new Date().toISOString(),
        status: 'success' as const,
        duration: Date.now() - startTime,
        data: {
          environment: {
            NODE_ENV: import.meta.env.NODE_ENV,
            VITE_API_BASE_URL: import.meta.env.VITE_API_BASE_URL,
            VITE_ENVIRONMENT: import.meta.env.VITE_ENVIRONMENT,
            userAgent: navigator.userAgent,
            location: window.location.href,
            timestamp: new Date().toISOString()
          },
          network: {
            online: navigator.onLine,
            connection: (navigator as any).connection ? {
              effectiveType: (navigator as any).connection.effectiveType,
              downlink: (navigator as any).connection.downlink,
              rtt: (navigator as any).connection.rtt
            } : 'not_available'
          },
          storage: {
            localStorage: typeof localStorage !== 'undefined',
            sessionStorage: typeof sessionStorage !== 'undefined',
            indexedDB: typeof indexedDB !== 'undefined'
          }
        }
      };
      return result;
    } catch (error) {
      return {
        timestamp: new Date().toISOString(),
        status: 'error',
        error: error instanceof Error ? error.message : 'Unknown error',
        duration: Date.now() - startTime
      };
    }
  };

  // 后端系统诊断
  const checkBackendDiagnostics = async (): Promise<DiagnosticResult> => {
    const startTime = Date.now();
    try {
      const apiBaseUrl = getApiBaseUrl();
      const response = await fetch(`${apiBaseUrl}/api/system/diagnostics`);
      
      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      const data = await response.json();
      return {
        timestamp: new Date().toISOString(),
        status: 'success',
        duration: Date.now() - startTime,
        data
      };
    } catch (error) {
      return {
        timestamp: new Date().toISOString(),
        status: 'error',
        error: error instanceof Error ? error.message : 'Unknown error',
        duration: Date.now() - startTime
      };
    }
  };

  // API调用测试
  const checkApiCall = async (): Promise<DiagnosticResult> => {
    const startTime = Date.now();
    try {
      const apiBaseUrl = getApiBaseUrl();
      const response = await fetch(`${apiBaseUrl}/api/questionnaire/stats`);
      
      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      const data = await response.json();
      return {
        timestamp: new Date().toISOString(),
        status: 'success',
        duration: Date.now() - startTime,
        data
      };
    } catch (error) {
      return {
        timestamp: new Date().toISOString(),
        status: 'error',
        error: error instanceof Error ? error.message : 'Unknown error',
        duration: Date.now() - startTime
      };
    }
  };

  // API调用追踪
  const checkApiTrace = async (): Promise<DiagnosticResult> => {
    const startTime = Date.now();
    try {
      const apiBaseUrl = getApiBaseUrl();
      const response = await fetch(`${apiBaseUrl}/api/system/trace/questionnaire-stats`);
      
      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      const data = await response.json();
      return {
        timestamp: new Date().toISOString(),
        status: 'success',
        duration: Date.now() - startTime,
        data
      };
    } catch (error) {
      return {
        timestamp: new Date().toISOString(),
        status: 'error',
        error: error instanceof Error ? error.message : 'Unknown error',
        duration: Date.now() - startTime
      };
    }
  };

  // 运行所有诊断
  const runDiagnostics = async () => {
    setIsRunning(true);
    
    // 重置状态
    setDiagnostics({
      frontend: { timestamp: '', status: 'loading' },
      backend: { timestamp: '', status: 'loading' },
      api: { timestamp: '', status: 'loading' },
      trace: { timestamp: '', status: 'loading' }
    });

    try {
      // 并行运行所有检查
      const [frontendResult, backendResult, apiResult, traceResult] = await Promise.all([
        checkFrontendEnvironment(),
        checkBackendDiagnostics(),
        checkApiCall(),
        checkApiTrace()
      ]);

      setDiagnostics({
        frontend: frontendResult,
        backend: backendResult,
        api: apiResult,
        trace: traceResult
      });
    } catch (error) {
      console.error('诊断运行失败:', error);
    } finally {
      setIsRunning(false);
    }
  };

  // 组件挂载时自动运行诊断
  useEffect(() => {
    runDiagnostics();
  }, []);

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'success':
        return <CheckCircle className="h-4 w-4 text-green-500" />;
      case 'error':
        return <AlertCircle className="h-4 w-4 text-red-500" />;
      case 'loading':
        return <Clock className="h-4 w-4 text-yellow-500 animate-spin" />;
      default:
        return <AlertCircle className="h-4 w-4 text-gray-500" />;
    }
  };

  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'success':
        return <Badge variant="default" className="bg-green-500">正常</Badge>;
      case 'error':
        return <Badge variant="destructive">错误</Badge>;
      case 'loading':
        return <Badge variant="secondary">检查中...</Badge>;
      default:
        return <Badge variant="outline">未知</Badge>;
    }
  };

  return (
    <div className={`space-y-6 ${className}`}>
      <div className="flex items-center justify-between">
        <h2 className="text-2xl font-bold">系统诊断</h2>
        <Button 
          onClick={runDiagnostics} 
          disabled={isRunning}
          className="flex items-center gap-2"
        >
          {isRunning ? <Clock className="h-4 w-4 animate-spin" /> : <Server className="h-4 w-4" />}
          {isRunning ? '诊断中...' : '重新诊断'}
        </Button>
      </div>

      {/* 概览卡片 */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">前端环境</CardTitle>
            <Globe className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="flex items-center space-x-2">
              {getStatusIcon(diagnostics.frontend.status)}
              {getStatusBadge(diagnostics.frontend.status)}
            </div>
            {diagnostics.frontend.duration && (
              <p className="text-xs text-muted-foreground mt-1">
                {diagnostics.frontend.duration}ms
              </p>
            )}
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">后端系统</CardTitle>
            <Database className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="flex items-center space-x-2">
              {getStatusIcon(diagnostics.backend.status)}
              {getStatusBadge(diagnostics.backend.status)}
            </div>
            {diagnostics.backend.duration && (
              <p className="text-xs text-muted-foreground mt-1">
                {diagnostics.backend.duration}ms
              </p>
            )}
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">API调用</CardTitle>
            <Server className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="flex items-center space-x-2">
              {getStatusIcon(diagnostics.api.status)}
              {getStatusBadge(diagnostics.api.status)}
            </div>
            {diagnostics.api.duration && (
              <p className="text-xs text-muted-foreground mt-1">
                {diagnostics.api.duration}ms
              </p>
            )}
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">调用追踪</CardTitle>
            <AlertCircle className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="flex items-center space-x-2">
              {getStatusIcon(diagnostics.trace.status)}
              {getStatusBadge(diagnostics.trace.status)}
            </div>
            {diagnostics.trace.duration && (
              <p className="text-xs text-muted-foreground mt-1">
                {diagnostics.trace.duration}ms
              </p>
            )}
          </CardContent>
        </Card>
      </div>

      {/* 详细信息 */}
      <Tabs defaultValue="frontend" className="w-full">
        <TabsList className="grid w-full grid-cols-4">
          <TabsTrigger value="frontend">前端环境</TabsTrigger>
          <TabsTrigger value="backend">后端系统</TabsTrigger>
          <TabsTrigger value="api">API调用</TabsTrigger>
          <TabsTrigger value="trace">调用追踪</TabsTrigger>
        </TabsList>

        <TabsContent value="frontend" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>前端环境检查</CardTitle>
            </CardHeader>
            <CardContent>
              {diagnostics.frontend.status === 'error' ? (
                <div className="text-red-500">
                  <p>错误: {diagnostics.frontend.error}</p>
                </div>
              ) : diagnostics.frontend.data ? (
                <pre className="text-sm bg-gray-100 p-4 rounded overflow-auto max-h-96">
                  {JSON.stringify(diagnostics.frontend.data, null, 2)}
                </pre>
              ) : (
                <p>检查中...</p>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="backend" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>后端系统诊断</CardTitle>
            </CardHeader>
            <CardContent>
              {diagnostics.backend.status === 'error' ? (
                <div className="text-red-500">
                  <p>错误: {diagnostics.backend.error}</p>
                </div>
              ) : diagnostics.backend.data ? (
                <pre className="text-sm bg-gray-100 p-4 rounded overflow-auto max-h-96">
                  {JSON.stringify(diagnostics.backend.data, null, 2)}
                </pre>
              ) : (
                <p>检查中...</p>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="api" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>API调用测试</CardTitle>
            </CardHeader>
            <CardContent>
              {diagnostics.api.status === 'error' ? (
                <div className="text-red-500">
                  <p>错误: {diagnostics.api.error}</p>
                </div>
              ) : diagnostics.api.data ? (
                <pre className="text-sm bg-gray-100 p-4 rounded overflow-auto max-h-96">
                  {JSON.stringify(diagnostics.api.data, null, 2)}
                </pre>
              ) : (
                <p>检查中...</p>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="trace" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>API调用追踪</CardTitle>
            </CardHeader>
            <CardContent>
              {diagnostics.trace.status === 'error' ? (
                <div className="text-red-500">
                  <p>错误: {diagnostics.trace.error}</p>
                </div>
              ) : diagnostics.trace.data ? (
                <pre className="text-sm bg-gray-100 p-4 rounded overflow-auto max-h-96">
                  {JSON.stringify(diagnostics.trace.data, null, 2)}
                </pre>
              ) : (
                <p>检查中...</p>
              )}
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
};

export default SystemDiagnostics;
