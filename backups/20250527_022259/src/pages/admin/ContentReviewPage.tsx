import React, { Suspense } from 'react';
import AdminLayout from '@/components/layouts/AdminLayout';
import ContentReviewPanel from '@/components/admin/ContentReviewPanel';
import CommentReviewPanel from '@/components/admin/CommentReviewPanel';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Shield, Settings, MessageSquare } from 'lucide-react';
import ReviewSettingsPanel from '@/components/admin/ReviewSettingsPanel';
import { LoadingSpinner } from '@/components/ui/loading-spinner';
import ErrorBoundary from '@/components/ErrorBoundary';

export default function ContentReviewPage() {
  return (
    <AdminLayout>
      <div className="container mx-auto py-6 space-y-6">
        <div>
          <h1 className="text-3xl font-bold">内容审核</h1>
          <p className="text-muted-foreground mt-1">
            审核用户提交的内容，确保符合平台规范
          </p>
        </div>

        <Tabs defaultValue="review">
          <TabsList>
            <TabsTrigger value="review">
              <Shield className="h-4 w-4 mr-2" />
              内容审核
            </TabsTrigger>
            <TabsTrigger value="comments">
              <MessageSquare className="h-4 w-4 mr-2" />
              评论审核
            </TabsTrigger>
            <TabsTrigger value="settings">
              <Settings className="h-4 w-4 mr-2" />
              审核设置
            </TabsTrigger>
          </TabsList>

          <TabsContent value="review" className="mt-6">
            <Suspense fallback={<div className="flex justify-center py-12"><LoadingSpinner size="lg" text="加载内容审核面板..." /></div>}>
              <ErrorBoundary fallback={<div className="p-4 border border-red-300 bg-red-50 rounded-md text-red-800">内容审核面板加载失败</div>}>
                <ContentReviewPanel />
              </ErrorBoundary>
            </Suspense>
          </TabsContent>

          <TabsContent value="comments" className="mt-6">
            <Suspense fallback={<div className="flex justify-center py-12"><LoadingSpinner size="lg" text="加载评论审核面板..." /></div>}>
              <ErrorBoundary fallback={<div className="p-4 border border-red-300 bg-red-50 rounded-md text-red-800">评论审核面板加载失败</div>}>
                <CommentReviewPanel />
              </ErrorBoundary>
            </Suspense>
          </TabsContent>

          <TabsContent value="settings" className="mt-6">
            <Suspense fallback={<div className="flex justify-center py-12"><LoadingSpinner size="lg" text="加载审核设置面板..." /></div>}>
              <ErrorBoundary fallback={<div className="p-4 border border-red-300 bg-red-50 rounded-md text-red-800">审核设置面板加载失败</div>}>
                <ReviewSettingsPanel />
              </ErrorBoundary>
            </Suspense>
          </TabsContent>
        </Tabs>
      </div>
    </AdminLayout>
  );
}
