import React, { useState, useEffect } from 'react';
import { useNavigate, useSearchParams, useLocation } from 'react-router-dom';
import SmartLayout from '@/components/layouts/SmartLayout';
import QuickReviewMode from '@/components/admin/QuickReviewMode';
import QuickReviewGuide from '@/components/admin/QuickReviewGuide';
import { Button } from '@/components/ui/button';
import { ArrowLeft } from 'lucide-react';
import { useToast } from '@/components/ui/use-toast';

/**
 * 快速审核页面
 * 提供专注的内容审核体验，支持键盘快捷操作
 */
export default function QuickReviewPage() {
  const navigate = useNavigate();
  const { toast } = useToast();
  const [searchParams] = useSearchParams();
  const location = useLocation();

  // 是否显示操作指南
  const [showGuide, setShowGuide] = useState(false);

  // 从本地存储获取是否已经查看过指南
  useEffect(() => {
    const hasSeenGuide = localStorage.getItem('quickReviewGuideShown');
    setShowGuide(!hasSeenGuide);
  }, []);

  // 处理指南关闭
  const handleGuideClose = (dontShowAgain: boolean) => {
    if (dontShowAgain) {
      localStorage.setItem('quickReviewGuideShown', 'true');
    }
    setShowGuide(false);
  };

  // 处理退出快速审核模式
  const handleExit = () => {
    navigate('/admin/content-review');

    toast({
      title: '已退出快速审核模式',
      description: '您可以随时再次进入快速审核模式',
      variant: 'default'
    });
  };

  // 获取筛选参数
  // 优先使用路由状态中的筛选条件，其次使用URL参数
  const routeFilters = location.state?.filters || {};

  const filters = {
    type: routeFilters.type || searchParams.get('type') || '',
    flag: routeFilters.flag || searchParams.get('flag') || '',
    priority: routeFilters.priority || searchParams.get('priority') || ''
  };

  return (
    <SmartLayout allowedRoles={['admin', 'superadmin']}>
      <div className="container mx-auto py-4">
        <div className="flex justify-between items-center mb-4">
          <Button
            variant="ghost"
            onClick={handleExit}
            className="flex items-center"
          >
            <ArrowLeft className="h-4 w-4 mr-2" />
            返回常规审核
          </Button>

          <Button
            variant="outline"
            onClick={() => setShowGuide(true)}
          >
            操作指南
          </Button>
        </div>

        {/* 操作指南对话框 */}
        {showGuide && (
          <QuickReviewGuide onClose={handleGuideClose} />
        )}

        {/* 快速审核模式组件 */}
        <QuickReviewMode
          filters={filters}
          onExit={handleExit}
        />
      </div>
    </SmartLayout>
  );
}
