import React, { useState, useEffect } from 'react';
import { Link } from 'react-router-dom';
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '@/components/ui/popover';
import { Button } from '@/components/ui/button';
import { Separator } from '@/components/ui/separator';
import { ScrollArea } from '@/components/ui/scroll-area';
import { useToast } from '@/components/ui/use-toast';
import { useApi } from '@/hooks/useApi';
import { 
  Bell, 
  CheckCircle, 
  Clock, 
  RefreshCw, 
  Trash2, 
  XCircle 
} from 'lucide-react';

/**
 * 通知中心组件
 */
const NotificationCenter: React.FC = () => {
  const { toast } = useToast();
  const api = useApi();
  const [isOpen, setIsOpen] = useState<boolean>(false);
  const [notifications, setNotifications] = useState<any[]>([]);
  const [unreadCount, setUnreadCount] = useState<number>(0);
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [isMarkingAll, setIsMarkingAll] = useState<boolean>(false);
  
  // 加载未读通知数量
  const loadUnreadCount = async () => {
    try {
      const response = await api.get('/notifications/unread-count');
      
      if (response.success) {
        setUnreadCount(response.count);
      }
    } catch (error) {
      console.error('获取未读通知数量失败:', error);
    }
  };
  
  // 加载通知列表
  const loadNotifications = async () => {
    if (isLoading) return;
    
    setIsLoading(true);
    
    try {
      const response = await api.get('/notifications');
      
      if (response.success) {
        setNotifications(response.notifications);
      } else {
        toast({
          title: '获取通知失败',
          description: response.error || '无法获取通知列表',
          variant: 'destructive',
        });
      }
    } catch (error) {
      console.error('获取通知列表失败:', error);
      toast({
        title: '获取通知失败',
        description: '服务器错误，请稍后再试',
        variant: 'destructive',
      });
    } finally {
      setIsLoading(false);
    }
  };
  
  // 标记通知为已读
  const markAsRead = async (id: string) => {
    try {
      const response = await api.post(`/notifications/${id}/read`);
      
      if (response.success) {
        // 更新通知列表
        setNotifications(notifications.map(notification => 
          notification.id === id ? { ...notification, isRead: true } : notification
        ));
        
        // 更新未读数量
        setUnreadCount(prev => Math.max(0, prev - 1));
      }
    } catch (error) {
      console.error('标记通知为已读失败:', error);
    }
  };
  
  // 标记所有通知为已读
  const markAllAsRead = async () => {
    if (isMarkingAll) return;
    
    setIsMarkingAll(true);
    
    try {
      const response = await api.post('/notifications/read-all');
      
      if (response.success) {
        // 更新通知列表
        setNotifications(notifications.map(notification => ({ ...notification, isRead: true })));
        
        // 更新未读数量
        setUnreadCount(0);
        
        toast({
          title: '已标记所有通知为已读',
          variant: 'default',
        });
      }
    } catch (error) {
      console.error('标记所有通知为已读失败:', error);
      toast({
        title: '操作失败',
        description: '标记所有通知为已读失败，请稍后再试',
        variant: 'destructive',
      });
    } finally {
      setIsMarkingAll(false);
    }
  };
  
  // 删除通知
  const deleteNotification = async (id: string) => {
    try {
      const response = await api.delete(`/notifications/${id}`);
      
      if (response.success) {
        // 更新通知列表
        const updatedNotifications = notifications.filter(notification => notification.id !== id);
        setNotifications(updatedNotifications);
        
        // 如果删除的是未读通知，更新未读数量
        const deletedNotification = notifications.find(notification => notification.id === id);
        if (deletedNotification && !deletedNotification.isRead) {
          setUnreadCount(prev => Math.max(0, prev - 1));
        }
      }
    } catch (error) {
      console.error('删除通知失败:', error);
      toast({
        title: '删除失败',
        description: '删除通知失败，请稍后再试',
        variant: 'destructive',
      });
    }
  };
  
  // 渲染通知图标
  const renderNotificationIcon = (type: string) => {
    if (type.includes('approve')) {
      return <CheckCircle className="w-4 h-4 text-green-500" />;
    } else if (type.includes('reject')) {
      return <XCircle className="w-4 h-4 text-red-500" />;
    } else if (type.includes('review')) {
      return <Clock className="w-4 h-4 text-yellow-500" />;
    } else {
      return <Bell className="w-4 h-4 text-blue-500" />;
    }
  };
  
  // 格式化时间
  const formatTime = (dateString: string) => {
    const date = new Date(dateString);
    const now = new Date();
    const diffMs = now.getTime() - date.getTime();
    const diffSec = Math.floor(diffMs / 1000);
    const diffMin = Math.floor(diffSec / 60);
    const diffHour = Math.floor(diffMin / 60);
    const diffDay = Math.floor(diffHour / 24);
    
    if (diffSec < 60) {
      return '刚刚';
    } else if (diffMin < 60) {
      return `${diffMin}分钟前`;
    } else if (diffHour < 24) {
      return `${diffHour}小时前`;
    } else if (diffDay < 30) {
      return `${diffDay}天前`;
    } else {
      return date.toLocaleDateString();
    }
  };
  
  // 组件加载时获取未读通知数量
  useEffect(() => {
    loadUnreadCount();
    
    // 定时刷新未读通知数量
    const interval = setInterval(loadUnreadCount, 60000); // 每分钟刷新一次
    
    return () => clearInterval(interval);
  }, []);
  
  // 打开通知中心时加载通知列表
  useEffect(() => {
    if (isOpen) {
      loadNotifications();
    }
  }, [isOpen]);
  
  return (
    <Popover open={isOpen} onOpenChange={setIsOpen}>
      <PopoverTrigger asChild>
        <Button variant="ghost" size="icon" className="relative">
          <Bell className="h-5 w-5" />
          {unreadCount > 0 && (
            <span className="absolute top-0 right-0 h-4 w-4 rounded-full bg-red-500 text-[10px] font-medium text-white flex items-center justify-center">
              {unreadCount > 99 ? '99+' : unreadCount}
            </span>
          )}
        </Button>
      </PopoverTrigger>
      <PopoverContent className="w-80 p-0" align="end">
        <div className="flex items-center justify-between p-4">
          <h3 className="font-medium">通知</h3>
          <div className="flex gap-1">
            <Button
              variant="ghost"
              size="icon"
              className="h-8 w-8"
              onClick={loadNotifications}
              disabled={isLoading}
            >
              <RefreshCw className={`h-4 w-4 ${isLoading ? 'animate-spin' : ''}`} />
            </Button>
            <Button
              variant="ghost"
              size="icon"
              className="h-8 w-8"
              onClick={markAllAsRead}
              disabled={isMarkingAll || unreadCount === 0}
            >
              <CheckCircle className="h-4 w-4" />
            </Button>
          </div>
        </div>
        <Separator />
        <ScrollArea className="h-[300px]">
          {notifications.length === 0 ? (
            <div className="flex flex-col items-center justify-center p-4 h-full">
              <Bell className="h-8 w-8 text-muted-foreground mb-2" />
              <p className="text-sm text-muted-foreground">暂无通知</p>
            </div>
          ) : (
            <div className="space-y-1 p-1">
              {notifications.map((notification) => (
                <div
                  key={notification.id}
                  className={`flex items-start gap-3 p-3 rounded-md ${
                    !notification.isRead ? 'bg-muted' : ''
                  }`}
                >
                  <div className="mt-1">
                    {renderNotificationIcon(notification.type)}
                  </div>
                  <div className="flex-1 space-y-1">
                    <div className="flex items-center justify-between">
                      <h4 className="text-sm font-medium">{notification.title}</h4>
                      <span className="text-xs text-muted-foreground">
                        {formatTime(notification.createdAt)}
                      </span>
                    </div>
                    <p className="text-sm text-muted-foreground">
                      {notification.message}
                    </p>
                    {!notification.isRead && (
                      <Button
                        variant="ghost"
                        size="sm"
                        className="h-7 px-2 text-xs"
                        onClick={() => markAsRead(notification.id)}
                      >
                        标记为已读
                      </Button>
                    )}
                  </div>
                  <Button
                    variant="ghost"
                    size="icon"
                    className="h-6 w-6 opacity-0 group-hover:opacity-100"
                    onClick={() => deleteNotification(notification.id)}
                  >
                    <Trash2 className="h-3 w-3" />
                  </Button>
                </div>
              ))}
            </div>
          )}
        </ScrollArea>
        <Separator />
        <div className="p-2">
          <Button
            variant="ghost"
            size="sm"
            className="w-full justify-center"
            asChild
          >
            <Link to="/notifications">查看全部通知</Link>
          </Button>
        </div>
      </PopoverContent>
    </Popover>
  );
};

export default NotificationCenter;
