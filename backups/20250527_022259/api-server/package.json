{"name": "college-employment-survey-api", "version": "1.0.0", "description": "College Employment Survey API", "main": "src/index.ts", "scripts": {"dev": "wrangler dev", "build": "wrangler build", "deploy": "wrangler deploy", "deploy:staging": "wrangler deploy --env staging", "deploy:production": "wrangler deploy --env production", "migrate": "wrangler d1 migrations apply college-survey-db", "migrate:staging": "wrangler d1 migrations apply college-survey-db --env staging", "migrate:production": "wrangler d1 migrations apply college-survey-db --env production", "test": "vitest run", "test:watch": "vitest", "test:coverage": "vitest run --coverage", "test:ui": "vitest --ui"}, "keywords": ["cloudflare", "workers", "api"], "author": "", "license": "ISC", "dependencies": {"@cloudflare/workers-types": "^4.20240208.0"}, "devDependencies": {"wrangler": "^4.15.2", "vitest": "^1.2.1", "@vitest/coverage-v8": "^1.2.1", "@vitest/ui": "^1.2.1", "typescript": "^5.3.3"}}