import { useState, useEffect, Suspense, lazy } from 'react';
import { useNavigate } from 'react-router-dom';
import { Button } from '@/components/ui/button';
import { useToast } from '@/components/ui/use-toast';
import QuestionnaireProgress from '@/components/questionnaire/QuestionnaireProgress';
import QuestionnaireRealtimeStats from '@/components/questionnaire/QuestionnaireRealtimeStats';
import { QuestionnaireData, submitQuestionnaire } from '@/lib/api';
import { SecurityModule } from '@/lib/security';
import { Skeleton } from '@/components/ui/skeleton';
import { processContentWithDeidentification } from '@/services/deidentificationService';


// 导入组件级代码分割工具
import { createLazyComponent } from '@/utils/componentSplitting';

// 懒加载问卷模块组件
const PersonalInfoModule = createLazyComponent(
  () => import('@/components/questionnaire/modules/PersonalInfoModule'),
  { name: 'PersonalInfoModule' }
);

const EmploymentExpectationsModule = createLazyComponent(
  () => import('@/components/questionnaire/modules/EmploymentExpectationsModule'),
  { name: 'EmploymentExpectationsModule' }
);

const WorkExperienceModule = createLazyComponent(
  () => import('@/components/questionnaire/modules/WorkExperienceModule'),
  { name: 'WorkExperienceModule' }
);

const UnemploymentStatusModule = createLazyComponent(
  () => import('@/components/questionnaire/modules/UnemploymentStatusModule'),
  { name: 'UnemploymentStatusModule' }
);

const CareerReflectionModule = createLazyComponent(
  () => import('@/components/questionnaire/modules/CareerReflectionModule'),
  { name: 'CareerReflectionModule' }
);

const AdviceFeedbackModule = createLazyComponent(
  () => import('@/components/questionnaire/modules/AdviceFeedbackModule'),
  { name: 'AdviceFeedbackModule' }
);

const EmailVerificationModule = createLazyComponent(
  () => import('@/components/questionnaire/modules/EmailVerificationModule'),
  { name: 'EmailVerificationModule' }
);

// 模块加载占位符
const ModuleLoadingFallback = () => (
  <div className="space-y-4">
    <Skeleton className="h-8 w-3/4" />
    <Skeleton className="h-24 w-full" />
    <Skeleton className="h-12 w-1/2" />
    <Skeleton className="h-24 w-full" />
  </div>
);

// Define the questionnaire modules
const MODULES = [
  { id: 'personal-info', title: '个人基本信息', component: PersonalInfoModule },
  { id: 'employment-expectations', title: '就业期望', component: EmploymentExpectationsModule },
  { id: 'work-experience', title: '工作经历', component: WorkExperienceModule },
  { id: 'unemployment-status', title: '失业状况', component: UnemploymentStatusModule },
  { id: 'career-reflection', title: '转行与反思', component: CareerReflectionModule },
  { id: 'advice-feedback', title: '建议与反馈', component: AdviceFeedbackModule },
  { id: 'email-verification', title: '提交与验证', component: EmailVerificationModule },
];

export default function QuestionnairePage() {
  const [currentModuleIndex, setCurrentModuleIndex] = useState(0);
  const [formData, setFormData] = useState<Partial<QuestionnaireData>>({
    isAnonymous: true,
  });
  const [isSubmitting, setIsSubmitting] = useState(false);
  const { toast } = useToast();
  const navigate = useNavigate();

  // Get current module
  const currentModule = MODULES[currentModuleIndex];
  const CurrentModuleComponent = currentModule.component;

  // Calculate progress percentage
  const progress = Math.round(((currentModuleIndex) / (MODULES.length - 1)) * 100);

  // Handle module data update
  const handleModuleUpdate = (moduleData: Partial<QuestionnaireData>) => {
    setFormData(prevData => ({
      ...prevData,
      ...moduleData
    }));
  };

  // 预加载下一个模块
  const preloadNextModule = (index: number) => {
    if (index < MODULES.length - 1) {
      const nextModule = MODULES[index + 1];
      if (nextModule && nextModule.component && typeof nextModule.component.preload === 'function') {
        console.log(`预加载模块: ${nextModule.title}`);
        nextModule.component.preload();
      }
    }
  };

  // 预加载所有模块
  const preloadAllModules = () => {
    MODULES.forEach(module => {
      if (module.component && typeof module.component.preload === 'function') {
        module.component.preload();
      }
    });
  };

  // Handle navigation between modules
  const handleNext = () => {
    // Save current state to localStorage for recovery
    localStorage.setItem('questionnaire_data', JSON.stringify(formData));

    if (currentModuleIndex < MODULES.length - 1) {
      // 预加载下一个模块的下一个模块
      preloadNextModule(currentModuleIndex + 1);

      setCurrentModuleIndex(currentModuleIndex + 1);
      window.scrollTo(0, 0);
    }
  };

  const handlePrevious = () => {
    if (currentModuleIndex > 0) {
      setCurrentModuleIndex(currentModuleIndex - 1);
      window.scrollTo(0, 0);
    }
  };

  // Handle final submission
  const handleSubmit = async (verificationId?: string) => {
    try {
      setIsSubmitting(true);

      // 验证表单（安全模块）
      const validationResult = await SecurityModule.validateForm(formData);
      if (!validationResult.valid) {
        toast({
          title: '提交失败',
          description: validationResult.error || '验证失败，请重试',
          variant: 'destructive',
        });
        setIsSubmitting(false);
        return;
      }

      // 获取安全元数据
      const securityMetadata = SecurityModule.getSecurityMetadata();

      // 准备基础数据
      let finalData: QuestionnaireData = {
        ...formData as QuestionnaireData,
        isAnonymous: !verificationId,
        emailVerificationId: verificationId,
        _security: securityMetadata // 添加安全元数据
      };

      // 对建议内容进行脱敏处理
      if (finalData.adviceForStudents || finalData.observationOnEmployment) {
        console.log('🔒 开始对问卷建议内容进行脱敏处理...');

        try {
          // 处理给高三学子的建议
          if (finalData.adviceForStudents) {
            const adviceResult = await processContentWithDeidentification(
              finalData.adviceForStudents,
              'questionnaire',
              verificationId || 'anonymous'
            );

            finalData.adviceForStudents = adviceResult.content;

            if (adviceResult.needsReview) {
              console.log('⚠️ 建议内容需要人工审核，审核ID:', adviceResult.reviewId);
              toast({
                title: '内容审核提醒',
                description: '您的建议内容将在审核通过后显示',
                variant: 'default',
              });
            }
          }

          // 处理对就业环境的观察
          if (finalData.observationOnEmployment) {
            const observationResult = await processContentWithDeidentification(
              finalData.observationOnEmployment,
              'questionnaire',
              verificationId || 'anonymous'
            );

            finalData.observationOnEmployment = observationResult.content;

            if (observationResult.needsReview) {
              console.log('⚠️ 观察内容需要人工审核，审核ID:', observationResult.reviewId);
              toast({
                title: '内容审核提醒',
                description: '您的观察内容将在审核通过后显示',
                variant: 'default',
              });
            }
          }

          console.log('✅ 问卷内容脱敏处理完成');
        } catch (deidentifyError) {
          console.error('❌ 内容脱敏处理失败:', deidentifyError);
          // 脱敏失败不阻止提交，但记录错误
          toast({
            title: '内容处理提醒',
            description: '内容安全检查遇到问题，但不影响提交',
            variant: 'default',
          });
        }
      }

      console.log('Submitting questionnaire data:', finalData);

      // 使用 API 客户端提交数据
      const data = await submitQuestionnaire(finalData);

      console.log('Response data:', data);

      if (data.success) {
        // Clear local storage after successful submission
        localStorage.removeItem('questionnaire_data');

        // Navigate to success page
        navigate('/submit-success', {
          state: {
            responseId: data.responseId,
            verified: data.verified
          }
        });
      } else {
        toast({
          title: '提交失败',
          description: data.error || '请稍后重试',
          variant: 'destructive',
        });
      }
    } catch (error) {
      console.error('Error submitting questionnaire:', error);
      toast({
        title: '提交失败',
        description: '发生错误，请稍后重试',
        variant: 'destructive',
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  // 初始化和恢复数据
  useEffect(() => {
    // 预加载第一个模块
    if (MODULES[0] && MODULES[0].component && typeof MODULES[0].component.preload === 'function') {
      MODULES[0].component.preload();
    }

    // 在浏览器空闲时预加载第二个模块
    if ('requestIdleCallback' in window) {
      (window as any).requestIdleCallback(() => {
        if (MODULES[1] && MODULES[1].component && typeof MODULES[1].component.preload === 'function') {
          MODULES[1].component.preload();
        }
      });
    }

    // 尝试恢复保存的数据
    const savedData = localStorage.getItem('questionnaire_data');
    if (savedData) {
      try {
        const parsedData = JSON.parse(savedData);
        setFormData(parsedData);

        // 如果有保存的数据，预加载当前模块和下一个模块
        // 估计用户可能在哪个模块
        let estimatedModuleIndex = 0;

        // 根据已填写的字段数量估计当前模块
        const fieldCount = Object.keys(parsedData).length;
        if (fieldCount > 20) {
          estimatedModuleIndex = Math.min(4, MODULES.length - 1);
        } else if (fieldCount > 15) {
          estimatedModuleIndex = Math.min(3, MODULES.length - 1);
        } else if (fieldCount > 10) {
          estimatedModuleIndex = Math.min(2, MODULES.length - 1);
        } else if (fieldCount > 5) {
          estimatedModuleIndex = Math.min(1, MODULES.length - 1);
        }

        // 预加载估计的当前模块和下一个模块
        if (MODULES[estimatedModuleIndex] && MODULES[estimatedModuleIndex].component) {
          MODULES[estimatedModuleIndex].component.preload();
        }

        preloadNextModule(estimatedModuleIndex);

        toast({
          title: '已恢复之前的填写进度',
          description: '您可以继续填写问卷',
        });
      } catch (error) {
        console.error('Error parsing saved data:', error);
      }
    }
  }, []);

  return (
    <div className="max-w-7xl mx-auto">
      <QuestionnaireProgress
        currentStep={currentModuleIndex + 1}
        totalSteps={MODULES.length}
        progress={progress}
        moduleTitles={MODULES.map(m => m.title)}
      />

      {/* 顶部实时统计条 */}
      <div className="mt-6 mb-6">
        <QuestionnaireRealtimeStats
          autoRefresh={true}
          refreshInterval={30000}
          className="compact-mode"
        />
      </div>

      {/* 主要问卷内容 */}
      <div className="bg-white rounded-lg shadow-md p-6">
        <h2 className="text-2xl font-bold mb-6">{currentModule.title}</h2>

        <Suspense fallback={<ModuleLoadingFallback />}>
          <CurrentModuleComponent
            data={formData}
            onUpdate={handleModuleUpdate}
            onSubmit={currentModuleIndex === MODULES.length - 1 ? handleSubmit : undefined}
            isSubmitting={isSubmitting}
          />
        </Suspense>
      </div>

      {/* 安全组件（验证码等） */}
      {currentModuleIndex === MODULES.length - 1 && (
        <div className="mt-6 lg:col-span-3">
          <div className="p-4 bg-gray-50 rounded-md">
            {SecurityModule.renderSecurityComponents({ theme: 'light' })}
          </div>
        </div>
      )}

      {/* 导航按钮 */}
      {currentModuleIndex !== MODULES.length - 1 && (
        <div className="mt-8 flex justify-between">
          <Button
            variant="outline"
            onClick={handlePrevious}
            disabled={currentModuleIndex === 0}
          >
            上一步
          </Button>

          <Button onClick={handleNext}>
            下一步
          </Button>
        </div>
      )}
    </div>
  );
}
