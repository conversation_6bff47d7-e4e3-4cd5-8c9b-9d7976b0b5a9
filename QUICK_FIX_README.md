# 快速修复工具包 - 使用指南

## 📋 概述

这个工具包包含了用于快速诊断和修复大学生就业调研系统常见问题的脚本和文档。旨在减少重复的调试时间，提高问题解决效率。

## 📁 文件结构

```
├── TROUBLESHOOTING_GUIDE.md          # 详细的故障排除指南
├── QUICK_FIX_README.md               # 本文件 - 快速使用指南
├── scripts/
│   ├── quick-health-check.sh         # 系统健康检查脚本
│   └── frontend-auth-check.js        # 前端认证诊断脚本
```

## 🚀 快速开始

### 1. 系统出现问题时的第一步

```bash
# 运行系统健康检查
chmod +x scripts/quick-health-check.sh
./scripts/quick-health-check.sh
```

这个脚本会自动检查：
- ✅ API端点连通性
- ✅ 管理员登录功能
- ✅ 受保护API访问
- ✅ 前端页面状态

### 2. 前端页面问题诊断

如果前端页面出现问题（空白、登录失败、API错误等）：

1. 打开浏览器开发者工具 (F12)
2. 切换到 Console 标签
3. 复制 `scripts/frontend-auth-check.js` 的内容
4. 粘贴到控制台并回车执行

脚本会检查：
- 🔐 认证token状态
- 🌐 API连接测试
- 💾 本地存储状态
- 🔧 自动生成修复建议

## 🎯 常见问题快速修复

### 问题1: API返回404错误

**症状**: 控制台显示 `Failed to load resource: 404`

**快速修复**:
```bash
# 1. 检查路由注册
grep -r "deidentification" backend/src/api/routes.ts

# 2. 如果没有找到，添加路由注册
# 编辑 backend/src/api/routes.ts，添加：
# import deidentificationRoutes from './admin/deidentification.routes';
# api.route('/admin/deidentification', deidentificationRoutes);

# 3. 重新部署
cd backend && npm run deploy
```

### 问题2: API返回401错误

**症状**: API调用返回 `{"success":false,"error":"Unauthorized"}`

**快速修复**:
```bash
# 1. 检查认证中间件
grep -A 5 -B 5 "payload.role" backend/src/middlewares/adminAuth.ts

# 2. 确保支持所有管理员角色
# 修改为: (payload.role !== 'admin' && payload.role !== 'superadmin')

# 3. 重新部署
cd backend && npm run deploy
```

### 问题3: 前端认证失败

**症状**: 登录成功但无法访问功能

**快速修复**:
```javascript
// 在浏览器控制台运行
localStorage.clear();
location.reload();
// 然后重新登录
```

### 问题4: 管理员登录按钮无效

**症状**: 一键登录按钮点击无反应

**快速修复**:
```bash
# 1. 检查用户名配置
grep -A 10 "const users" backend/src/controllers/admin.ts
grep -A 5 "adminLogin.*admin" frontend/src/pages/admin/AdminLoginPage.tsx

# 2. 确保前后端用户名一致
# 后端: admin1, reviewer1, superadmin
# 前端: 对应的一键登录按钮应使用相同用户名
```

## 🔧 高级诊断工具

### 手动API测试

```bash
# 获取管理员token
TOKEN=$(curl -s -X POST https://college-employment-survey.aibook2099.workers.dev/api/admin/login \
  -H "Content-Type: application/json" \
  -d '{"username":"superadmin","password":"admin123"}' | jq -r '.data.token')

# 测试受保护的API
curl -X GET https://college-employment-survey.aibook2099.workers.dev/api/admin/deidentification/config \
  -H "Authorization: Bearer $TOKEN" | jq .
```

### 检查部署状态

```bash
# 查看后端部署历史
cd backend && npx wrangler deployments list

# 查看前端部署状态
cd frontend && npx wrangler pages project list

# 查看实时日志
npx wrangler tail
```

### 本地开发调试

```bash
# 启动本地开发服务器
cd frontend && npm run dev

# 在另一个终端启动后端
cd backend && npm run dev
```

## 📚 详细文档参考

### 完整故障排除指南
查看 `TROUBLESHOOTING_GUIDE.md` 获取：
- 详细的错误分析
- 完整的修复步骤
- 预防措施
- 自动化工具

### 系统架构要点
- 认证流程
- API调用模式
- 路由注册机制
- 权限管理

## ⚡ 紧急修复检查清单

当系统出现问题时，按以下顺序检查：

1. **[ ] 运行健康检查脚本**
   ```bash
   ./scripts/quick-health-check.sh
   ```

2. **[ ] 检查API端点状态**
   - 404错误 → 路由未注册
   - 401错误 → 认证权限问题
   - 500错误 → 服务器内部错误

3. **[ ] 验证管理员登录**
   - admin1/admin123
   - reviewer1/admin123  
   - superadmin/admin123

4. **[ ] 检查前端认证状态**
   ```javascript
   // 在浏览器控制台运行
   localStorage.getItem('adminToken')
   ```

5. **[ ] 重新部署（如果需要）**
   ```bash
   # 后端
   cd backend && npm run deploy
   
   # 前端
   cd frontend && npm run build && npx wrangler pages deploy dist --project-name college-employment-survey
   ```

## 🆘 紧急联系

如果以上方法都无法解决问题：

1. **检查系统状态页面**
   - https://college-employment-survey.pages.dev/health
   - https://college-employment-survey.aibook2099.workers.dev/health

2. **查看部署日志**
   ```bash
   npx wrangler tail
   ```

3. **回滚到上一个工作版本**
   ```bash
   npx wrangler deployments list
   npx wrangler rollback [deployment-id]
   ```

## 📝 问题记录

每次修复问题后，请更新 `TROUBLESHOOTING_GUIDE.md`：
- 记录新的问题类型
- 添加修复步骤
- 更新预防措施

---

**记住**: 大多数问题都是由以下三个原因引起的：
1. 🔗 **路由未注册** - 检查 `backend/src/api/routes.ts`
2. 🔐 **认证权限** - 检查 `backend/src/middlewares/adminAuth.ts`
3. 🌐 **前端API调用** - 检查是否包含 `Authorization` 头

*最后更新：2025-05-27*
