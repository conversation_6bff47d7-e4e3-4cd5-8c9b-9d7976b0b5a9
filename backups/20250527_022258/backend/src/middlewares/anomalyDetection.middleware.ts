/**
 * 异常检测中间件
 * 检测请求中的异常行为
 */

import { Context, Next } from 'hono';
import { Env } from '../types';
import { AnomalyDetectionService } from '../services/anomalyDetectionService';
import { AuditAction } from '../services/auditLogService';

/**
 * 创建异常检测中间件
 * @param action 操作类型
 * @param resourceType 资源类型
 * @param options 选项
 */
export const anomalyDetectionMiddleware = (
  action: AuditAction,
  resourceType: string,
  options: {
    timeWindowMinutes?: number;
    thresholdCount?: number;
    blockOnAnomaly?: boolean;
  } = {}
) => {
  return async (c: Context<{ Bindings: Env }>, next: Next) => {
    // 开发环境下的简化处理
    if (c.env.ENVIRONMENT === 'development') {
      console.warn(`Development mode: Bypassing anomaly detection for ${action}`);
      await next();
      return;
    }

    // 获取用户信息
    const user = c.get('user');
    
    if (!user) {
      return c.json({ success: false, error: 'Unauthorized' }, 401);
    }

    // 获取用户ID
    const userId = user.id || user.username || 'unknown';
    
    // 获取资源ID（如果有）
    let resourceId: string | undefined;
    if (c.req.param('id')) {
      resourceId = c.req.param('id');
    }
    
    // 获取客户端信息
    const ipAddress = c.req.headers.get('CF-Connecting-IP') || c.req.headers.get('X-Forwarded-For') || 'unknown';
    const userAgent = c.req.headers.get('User-Agent') || 'unknown';
    
    // 获取异常检测服务实例
    const anomalyDetectionService = AnomalyDetectionService.getInstance();
    
    // 执行异常检测
    const timeWindowMinutes = options.timeWindowMinutes || 5;
    const thresholdCount = options.thresholdCount || 10;
    
    // 1. 检测操作频率异常
    const frequencyAnomaly = await anomalyDetectionService.detectFrequencyAnomaly(
      userId,
      action,
      timeWindowMinutes,
      thresholdCount,
      c.env
    );
    
    // 2. 检测操作时间异常
    const timeAnomaly = await anomalyDetectionService.detectTimeAnomaly(
      new Date(),
      userId,
      c.env
    );
    
    // 3. 检测操作位置异常
    const locationAnomaly = await anomalyDetectionService.detectLocationAnomaly(
      ipAddress,
      userId,
      c.env
    );
    
    // 合并异常检测结果
    const anomalies = [];
    
    if (frequencyAnomaly.isAnomaly) {
      anomalies.push(frequencyAnomaly);
      
      // 记录频率异常
      await anomalyDetectionService.logAnomaly(
        userId,
        action,
        resourceType,
        resourceId,
        frequencyAnomaly,
        { ipAddress, userAgent },
        c.env
      );
    }
    
    if (timeAnomaly.isAnomaly) {
      anomalies.push(timeAnomaly);
      
      // 记录时间异常
      await anomalyDetectionService.logAnomaly(
        userId,
        action,
        resourceType,
        resourceId,
        timeAnomaly,
        { ipAddress, userAgent },
        c.env
      );
    }
    
    if (locationAnomaly.isAnomaly) {
      anomalies.push(locationAnomaly);
      
      // 记录位置异常
      await anomalyDetectionService.logAnomaly(
        userId,
        action,
        resourceType,
        resourceId,
        locationAnomaly,
        { ipAddress, userAgent },
        c.env
      );
    }
    
    // 检查是否需要阻止请求
    if (options.blockOnAnomaly && anomalies.length > 0) {
      return c.json({ 
        success: false, 
        error: '检测到异常操作行为',
        anomalies
      }, 403);
    }
    
    // 将异常检测结果添加到请求上下文
    c.set('anomalies', anomalies);
    
    await next();
  };
};
