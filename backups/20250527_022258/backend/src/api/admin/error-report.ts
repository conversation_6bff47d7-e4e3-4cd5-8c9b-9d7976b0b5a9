/**
 * 错误报告 API
 */

import { Hono } from 'hono';
import { SecurityModule } from '../../security';

// 创建路由
const app = new Hono();

/**
 * 获取错误统计信息
 */
app.get('/stats', async (c) => {
  try {
    // 获取所有日志
    const allLogs = await SecurityModule.getLogs('all', 1, 1000, c.env);
    
    // 计算错误统计信息
    const errorStats = {
      total: allLogs.total,
      byLevel: {
        error: allLogs.logs.filter(log => log.level === 'error').length,
        warn: allLogs.logs.filter(log => log.level === 'warn').length,
        info: allLogs.logs.filter(log => log.level === 'info').length,
        debug: allLogs.logs.filter(log => log.level === 'debug').length
      },
      bySeverity: {
        critical: allLogs.logs.filter(log => log.severity === 'critical').length,
        high: allLogs.logs.filter(log => log.severity === 'high').length,
        medium: allLogs.logs.filter(log => log.severity === 'medium').length,
        low: allLogs.logs.filter(log => log.severity === 'low').length
      },
      bySource: {},
      byErrorCode: {},
      byTag: {},
      timeDistribution: {
        last24h: 0,
        last7d: 0,
        last30d: 0
      }
    };
    
    // 计算时间分布
    const now = Date.now();
    const day = 24 * 60 * 60 * 1000;
    
    // 计算各种分布
    allLogs.logs.forEach(log => {
      // 计算时间分布
      if (now - log.timestamp < day) {
        errorStats.timeDistribution.last24h++;
      }
      
      if (now - log.timestamp < 7 * day) {
        errorStats.timeDistribution.last7d++;
      }
      
      if (now - log.timestamp < 30 * day) {
        errorStats.timeDistribution.last30d++;
      }
      
      // 计算来源分布
      if (log.source) {
        errorStats.bySource[log.source] = (errorStats.bySource[log.source] || 0) + 1;
      }
      
      // 计算错误代码分布
      if (log.errorCode) {
        errorStats.byErrorCode[log.errorCode] = (errorStats.byErrorCode[log.errorCode] || 0) + 1;
      }
      
      // 计算标签分布
      if (log.tags && Array.isArray(log.tags)) {
        log.tags.forEach(tag => {
          errorStats.byTag[tag] = (errorStats.byTag[tag] || 0) + 1;
        });
      }
    });
    
    // 返回统计信息
    return c.json({
      success: true,
      stats: errorStats
    });
  } catch (error) {
    console.error('Error getting error stats:', error);
    return c.json({
      success: false,
      error: '获取错误统计信息失败'
    }, 500);
  }
});

/**
 * 获取错误详情
 */
app.get('/details/:id', async (c) => {
  try {
    const id = c.req.param('id');
    
    // 获取所有日志
    const allLogs = await SecurityModule.getLogs('all', 1, 1000, c.env);
    
    // 查找指定 ID 的日志
    const log = allLogs.logs.find(log => log.id === id);
    
    if (!log) {
      return c.json({
        success: false,
        error: '未找到指定的错误记录'
      }, 404);
    }
    
    // 返回错误详情
    return c.json({
      success: true,
      error: log
    });
  } catch (error) {
    console.error('Error getting error details:', error);
    return c.json({
      success: false,
      error: '获取错误详情失败'
    }, 500);
  }
});

/**
 * 获取错误趋势
 */
app.get('/trends', async (c) => {
  try {
    // 获取所有日志
    const allLogs = await SecurityModule.getLogs('all', 1, 1000, c.env);
    
    // 计算每天的错误数量
    const dailyErrors = {};
    const hourlyErrors = {};
    
    allLogs.logs.forEach(log => {
      const date = new Date(log.timestamp);
      const dateKey = `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}-${String(date.getDate()).padStart(2, '0')}`;
      const hourKey = `${dateKey} ${String(date.getHours()).padStart(2, '0')}:00`;
      
      // 按日统计
      dailyErrors[dateKey] = (dailyErrors[dateKey] || 0) + 1;
      
      // 按小时统计
      hourlyErrors[hourKey] = (hourlyErrors[hourKey] || 0) + 1;
    });
    
    // 转换为数组格式
    const dailyTrend = Object.entries(dailyErrors).map(([date, count]) => ({ date, count }));
    const hourlyTrend = Object.entries(hourlyErrors).map(([hour, count]) => ({ hour, count }));
    
    // 按日期排序
    dailyTrend.sort((a, b) => a.date.localeCompare(b.date));
    hourlyTrend.sort((a, b) => a.hour.localeCompare(b.hour));
    
    // 返回趋势数据
    return c.json({
      success: true,
      trends: {
        daily: dailyTrend,
        hourly: hourlyTrend
      }
    });
  } catch (error) {
    console.error('Error getting error trends:', error);
    return c.json({
      success: false,
      error: '获取错误趋势失败'
    }, 500);
  }
});

// 导出路由
export default app;
