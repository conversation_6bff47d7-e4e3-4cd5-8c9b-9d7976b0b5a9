#!/usr/bin/env node

/**
 * 📊 质量报告生成器
 * 生成完整的代码质量和测试报告
 */

const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

class QualityReportGenerator {
  constructor() {
    this.report = {
      timestamp: new Date().toISOString(),
      version: 'v3.0-modular',
      summary: {},
      codeQuality: {},
      testing: {},
      coverage: {},
      duplicates: {},
      recommendations: []
    };
  }

  // 运行ESLint检查
  async runESLintCheck() {
    console.log('🔍 运行ESLint检查...');
    
    try {
      const result = execSync('npm run lint', { encoding: 'utf8' });
      this.report.codeQuality.eslint = {
        status: 'passed',
        output: result
      };
    } catch (error) {
      const output = error.stdout || error.message;
      const errorCount = (output.match(/✖ \d+ problems?/g) || []).length;
      
      this.report.codeQuality.eslint = {
        status: 'failed',
        output: output,
        errorCount: errorCount
      };
    }
  }

  // 运行Prettier检查
  async runPrettierCheck() {
    console.log('🎨 运行Prettier检查...');
    
    try {
      const result = execSync('npm run format:check', { encoding: 'utf8' });
      this.report.codeQuality.prettier = {
        status: 'passed',
        output: result
      };
    } catch (error) {
      this.report.codeQuality.prettier = {
        status: 'failed',
        output: error.stdout || error.message
      };
    }
  }

  // 运行测试套件
  async runTests() {
    console.log('🧪 运行测试套件...');
    
    const testTypes = ['unit', 'integration', 'e2e'];
    this.report.testing.results = {};

    for (const testType of testTypes) {
      console.log(`  📋 运行${testType}测试...`);
      
      try {
        const result = execSync(`npm run test:${testType}`, { encoding: 'utf8' });
        this.report.testing.results[testType] = {
          status: 'passed',
          output: result
        };
      } catch (error) {
        const output = error.stdout || error.message;
        const passedMatch = output.match(/(\d+) passed/);
        const failedMatch = output.match(/(\d+) failed/);
        
        this.report.testing.results[testType] = {
          status: 'failed',
          output: output,
          passed: passedMatch ? parseInt(passedMatch[1]) : 0,
          failed: failedMatch ? parseInt(failedMatch[1]) : 0
        };
      }
    }
  }

  // 运行覆盖率测试
  async runCoverageTest() {
    console.log('📊 运行覆盖率测试...');
    
    try {
      const result = execSync('npm run test:coverage', { encoding: 'utf8' });
      this.report.coverage = {
        status: 'completed',
        output: result
      };

      // 尝试读取覆盖率报告
      if (fs.existsSync('coverage/coverage-summary.json')) {
        const coverageData = JSON.parse(fs.readFileSync('coverage/coverage-summary.json', 'utf8'));
        this.report.coverage.summary = coverageData.total;
      }
    } catch (error) {
      this.report.coverage = {
        status: 'failed',
        output: error.stdout || error.message
      };
    }
  }

  // 运行重复检测
  async runDuplicateDetection() {
    console.log('🔍 运行重复检测...');
    
    try {
      if (fs.existsSync('scripts/detect-duplicates.js')) {
        const result = execSync('node scripts/detect-duplicates.js', { encoding: 'utf8' });
        this.report.duplicates = {
          status: 'completed',
          output: result
        };

        // 尝试读取重复检测报告
        if (fs.existsSync('duplicate-report.json')) {
          const duplicateData = JSON.parse(fs.readFileSync('duplicate-report.json', 'utf8'));
          this.report.duplicates.summary = duplicateData.summary;
          this.report.duplicates.count = duplicateData.duplicates?.length || 0;
        }
      } else {
        this.report.duplicates = {
          status: 'skipped',
          reason: 'detect-duplicates.js not found'
        };
      }
    } catch (error) {
      this.report.duplicates = {
        status: 'failed',
        output: error.stdout || error.message
      };
    }
  }

  // 生成总结
  generateSummary() {
    console.log('📋 生成总结...');
    
    const summary = {
      overallStatus: 'unknown',
      scores: {},
      issues: [],
      achievements: []
    };

    // ESLint评分
    if (this.report.codeQuality.eslint?.status === 'passed') {
      summary.scores.codeQuality = 100;
      summary.achievements.push('✅ ESLint检查通过');
    } else {
      const errorCount = this.report.codeQuality.eslint?.errorCount || 0;
      summary.scores.codeQuality = Math.max(0, 100 - errorCount * 2);
      summary.issues.push(`❌ ESLint发现 ${errorCount} 个问题`);
    }

    // 测试评分
    let totalTests = 0;
    let passedTests = 0;
    
    Object.values(this.report.testing.results || {}).forEach(result => {
      if (result.passed !== undefined) {
        totalTests += result.passed + (result.failed || 0);
        passedTests += result.passed;
      }
    });

    if (totalTests > 0) {
      summary.scores.testing = Math.round((passedTests / totalTests) * 100);
      summary.achievements.push(`🧪 测试通过率: ${summary.scores.testing}% (${passedTests}/${totalTests})`);
    }

    // 覆盖率评分
    if (this.report.coverage.summary) {
      const coverage = this.report.coverage.summary;
      summary.scores.coverage = Math.round(
        (coverage.lines.pct + coverage.functions.pct + coverage.branches.pct + coverage.statements.pct) / 4
      );
      summary.achievements.push(`📊 代码覆盖率: ${summary.scores.coverage}%`);
    }

    // 重复代码评分
    if (this.report.duplicates.count !== undefined) {
      summary.scores.duplicates = Math.max(0, 100 - this.report.duplicates.count * 5);
      if (this.report.duplicates.count === 0) {
        summary.achievements.push('✅ 无重复代码');
      } else {
        summary.issues.push(`⚠️ 发现 ${this.report.duplicates.count} 个重复项`);
      }
    }

    // 计算总体评分
    const scores = Object.values(summary.scores);
    if (scores.length > 0) {
      summary.overallScore = Math.round(scores.reduce((a, b) => a + b, 0) / scores.length);
      
      if (summary.overallScore >= 90) {
        summary.overallStatus = 'excellent';
      } else if (summary.overallScore >= 80) {
        summary.overallStatus = 'good';
      } else if (summary.overallScore >= 70) {
        summary.overallStatus = 'fair';
      } else {
        summary.overallStatus = 'poor';
      }
    }

    this.report.summary = summary;
  }

  // 生成建议
  generateRecommendations() {
    console.log('💡 生成改进建议...');
    
    const recommendations = [];

    // 基于ESLint结果的建议
    if (this.report.codeQuality.eslint?.status === 'failed') {
      recommendations.push({
        category: 'Code Quality',
        priority: 'high',
        title: '修复ESLint问题',
        description: '运行 `npm run lint:fix` 自动修复可修复的问题',
        action: 'npm run lint:fix'
      });
    }

    // 基于测试结果的建议
    const testResults = this.report.testing.results || {};
    Object.entries(testResults).forEach(([testType, result]) => {
      if (result.status === 'failed' && result.failed > 0) {
        recommendations.push({
          category: 'Testing',
          priority: 'medium',
          title: `修复${testType}测试`,
          description: `有 ${result.failed} 个${testType}测试失败，需要修复`,
          action: `npm run test:${testType}`
        });
      }
    });

    // 基于覆盖率的建议
    if (this.report.coverage.summary) {
      const coverage = this.report.coverage.summary;
      if (coverage.lines.pct < 80) {
        recommendations.push({
          category: 'Coverage',
          priority: 'medium',
          title: '提高测试覆盖率',
          description: `当前行覆盖率为 ${coverage.lines.pct}%，建议提高到80%以上`,
          action: '添加更多单元测试'
        });
      }
    }

    // 基于重复代码的建议
    if (this.report.duplicates.count > 0) {
      recommendations.push({
        category: 'Code Quality',
        priority: 'low',
        title: '清理重复代码',
        description: `发现 ${this.report.duplicates.count} 个重复项，建议进行重构`,
        action: 'node scripts/refactor-api.js'
      });
    }

    // 通用建议
    recommendations.push({
      category: 'Maintenance',
      priority: 'low',
      title: '定期运行质量检查',
      description: '建议每周运行一次完整的质量检查',
      action: 'node scripts/generate-quality-report.js'
    });

    this.report.recommendations = recommendations;
  }

  // 生成HTML报告
  generateHTMLReport() {
    const html = `
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>代码质量报告 - ${this.report.version}</title>
    <style>
        body { font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif; margin: 0; padding: 20px; background: #f5f5f5; }
        .container { max-width: 1200px; margin: 0 auto; background: white; border-radius: 8px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        .header { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 30px; border-radius: 8px 8px 0 0; }
        .content { padding: 30px; }
        .score-card { display: inline-block; background: #f8f9fa; border-radius: 8px; padding: 20px; margin: 10px; text-align: center; min-width: 120px; }
        .score { font-size: 2em; font-weight: bold; margin-bottom: 5px; }
        .excellent { color: #28a745; }
        .good { color: #17a2b8; }
        .fair { color: #ffc107; }
        .poor { color: #dc3545; }
        .section { margin: 30px 0; }
        .section h2 { color: #333; border-bottom: 2px solid #eee; padding-bottom: 10px; }
        .recommendation { background: #f8f9fa; border-left: 4px solid #007bff; padding: 15px; margin: 10px 0; border-radius: 4px; }
        .high { border-left-color: #dc3545; }
        .medium { border-left-color: #ffc107; }
        .low { border-left-color: #28a745; }
        .achievement { color: #28a745; margin: 5px 0; }
        .issue { color: #dc3545; margin: 5px 0; }
        pre { background: #f8f9fa; padding: 15px; border-radius: 4px; overflow-x: auto; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>📊 代码质量报告</h1>
            <p>版本: ${this.report.version} | 生成时间: ${new Date(this.report.timestamp).toLocaleString('zh-CN')}</p>
        </div>
        
        <div class="content">
            <div class="section">
                <h2>📈 总体评分</h2>
                <div class="score-card">
                    <div class="score ${this.report.summary.overallStatus}">${this.report.summary.overallScore || 'N/A'}</div>
                    <div>总体评分</div>
                </div>
                ${Object.entries(this.report.summary.scores || {}).map(([key, score]) => `
                    <div class="score-card">
                        <div class="score">${score}</div>
                        <div>${key}</div>
                    </div>
                `).join('')}
            </div>

            <div class="section">
                <h2>✅ 成就</h2>
                ${this.report.summary.achievements?.map(achievement => `<div class="achievement">${achievement}</div>`).join('') || '<p>暂无成就</p>'}
            </div>

            <div class="section">
                <h2>⚠️ 问题</h2>
                ${this.report.summary.issues?.map(issue => `<div class="issue">${issue}</div>`).join('') || '<p>暂无问题</p>'}
            </div>

            <div class="section">
                <h2>💡 改进建议</h2>
                ${this.report.recommendations?.map(rec => `
                    <div class="recommendation ${rec.priority}">
                        <h4>${rec.title}</h4>
                        <p>${rec.description}</p>
                        <code>${rec.action}</code>
                    </div>
                `).join('') || '<p>暂无建议</p>'}
            </div>
        </div>
    </div>
</body>
</html>`;

    fs.writeFileSync('quality-report.html', html);
    console.log('📄 HTML报告已生成: quality-report.html');
  }

  // 保存JSON报告
  saveJSONReport() {
    fs.writeFileSync('quality-report.json', JSON.stringify(this.report, null, 2));
    console.log('📄 JSON报告已生成: quality-report.json');
  }

  // 运行完整报告
  async generateReport() {
    console.log('🚀 开始生成质量报告...\n');

    await this.runESLintCheck();
    await this.runPrettierCheck();
    await this.runTests();
    await this.runCoverageTest();
    await this.runDuplicateDetection();
    
    this.generateSummary();
    this.generateRecommendations();
    
    this.saveJSONReport();
    this.generateHTMLReport();

    console.log('\n🎉 质量报告生成完成!');
    console.log(`📊 总体评分: ${this.report.summary.overallScore || 'N/A'} (${this.report.summary.overallStatus || 'unknown'})`);
    console.log('📄 查看报告: quality-report.html');
  }
}

// 主函数
async function main() {
  const generator = new QualityReportGenerator();
  await generator.generateReport();
}

if (require.main === module) {
  main().catch(console.error);
}

module.exports = QualityReportGenerator;
