/**
 * 内存缓存服务
 *
 * 提供高性能的内存缓存功能，减少重复计算和存储访问
 */

/**
 * 缓存配置
 */
interface CacheConfig {
  enabled: boolean;
  defaultTTL: number; // 默认过期时间（毫秒）
  maxSize: number; // 最大缓存项数量
  cleanupInterval: number; // 清理间隔（毫秒）
}

/**
 * 缓存项
 */
interface CacheItem<T> {
  value: T;
  expiry: number;
  hits: number;
  lastAccessed: number;
}

/**
 * 缓存统计信息
 */
interface CacheStats {
  size: number;
  hits: number;
  misses: number;
  hitRate: number;
  evictions: number;
  expirations: number;
}

/**
 * 内存缓存服务类
 */
export class MemoryCache {
  private config: CacheConfig;
  private cache: Map<string, CacheItem<any>> = new Map();
  private cleanupTimer: NodeJS.Timeout | null = null;
  private hits: number = 0;
  private misses: number = 0;
  private evictions: number = 0;
  private expirations: number = 0;

  /**
   * 构造函数
   */
  constructor() {
    this.config = {
      enabled: true,
      defaultTTL: 300000, // 5分钟
      maxSize: 1000,
      cleanupInterval: 60000 // 1分钟
    };

    // Don't start the cleanup timer in the constructor to avoid Cloudflare Workers restrictions
    // It will be started when initialize() is called
  }

  /**
   * 初始化服务
   * @param config 缓存配置
   */
  public initialize(config: Partial<CacheConfig>): void {
    this.config = { ...this.config, ...config };

    // Only start the cleanup timer if we're not in a Cloudflare Worker global scope
    // This will be called from a request handler, which is safe
    if (typeof addEventListener !== 'undefined') {
      // We're in a browser or worker environment where addEventListener exists
      // Defer the timer start to avoid global scope restrictions
      setTimeout(() => {
        this.startCleanupTimer();
      }, 0);
    } else {
      // We're in a Node.js environment, safe to start immediately
      this.startCleanupTimer();
    }
  }

  /**
   * 更新配置
   * @param config 缓存配置
   */
  public updateConfig(config: Partial<CacheConfig>): void {
    const wasEnabled = this.config.enabled;
    this.config = { ...this.config, ...config };

    // 如果启用状态改变，更新定时器
    if (wasEnabled !== this.config.enabled) {
      if (this.config.enabled) {
        this.startCleanupTimer();
      } else {
        this.stopCleanupTimer();
      }
    }
  }

  /**
   * 获取缓存项
   * @param key 缓存键
   */
  public get<T>(key: string): T | undefined {
    // 如果缓存未启用，返回 undefined
    if (!this.config.enabled) {
      this.misses++;
      return undefined;
    }

    // 获取缓存项
    const item = this.cache.get(key);

    // 如果缓存项不存在，返回 undefined
    if (!item) {
      this.misses++;
      return undefined;
    }

    // 如果缓存项已过期，删除并返回 undefined
    if (item.expiry < Date.now()) {
      this.cache.delete(key);
      this.expirations++;
      this.misses++;
      return undefined;
    }

    // 更新访问信息
    item.hits++;
    item.lastAccessed = Date.now();
    this.hits++;

    return item.value;
  }

  /**
   * 设置缓存项
   * @param key 缓存键
   * @param value 缓存值
   * @param ttl 过期时间（毫秒）
   */
  public set<T>(key: string, value: T, ttl?: number): void {
    // 如果缓存未启用，不执行操作
    if (!this.config.enabled) {
      return;
    }

    // 如果缓存已满，清理一些空间
    if (this.cache.size >= this.config.maxSize) {
      this.evictItems();
    }

    // 计算过期时间
    const expiry = Date.now() + (ttl || this.config.defaultTTL);

    // 设置缓存项
    this.cache.set(key, {
      value,
      expiry,
      hits: 0,
      lastAccessed: Date.now()
    });
  }

  /**
   * 删除缓存项
   * @param key 缓存键
   */
  public delete(key: string): boolean {
    return this.cache.delete(key);
  }

  /**
   * 清空缓存
   */
  public clear(): void {
    this.cache.clear();
  }

  /**
   * 获取缓存统计信息
   */
  public getStats(): CacheStats {
    const totalRequests = this.hits + this.misses;
    const hitRate = totalRequests > 0 ? this.hits / totalRequests : 0;

    return {
      size: this.cache.size,
      hits: this.hits,
      misses: this.misses,
      hitRate,
      evictions: this.evictions,
      expirations: this.expirations
    };
  }

  /**
   * 启动清理定时器
   */
  private startCleanupTimer(): void {
    // 如果定时器已存在，先停止
    this.stopCleanupTimer();

    // 如果缓存未启用，不启动定时器
    if (!this.config.enabled) {
      return;
    }

    // 启动定时器
    this.cleanupTimer = setInterval(() => {
      this.cleanup();
    }, this.config.cleanupInterval);
  }

  /**
   * 停止清理定时器
   */
  private stopCleanupTimer(): void {
    if (this.cleanupTimer) {
      clearInterval(this.cleanupTimer);
      this.cleanupTimer = null;
    }
  }

  /**
   * 清理过期的缓存项
   */
  private cleanup(): void {
    const now = Date.now();
    let expiredCount = 0;

    // 遍历缓存项，删除过期的
    for (const [key, item] of this.cache.entries()) {
      if (item.expiry < now) {
        this.cache.delete(key);
        expiredCount++;
      }
    }

    // 更新统计信息
    this.expirations += expiredCount;
  }

  /**
   * 驱逐缓存项
   */
  private evictItems(): void {
    // 如果缓存为空，不执行操作
    if (this.cache.size === 0) {
      return;
    }

    // 计算要驱逐的项数
    const evictCount = Math.ceil(this.cache.size * 0.1); // 驱逐 10% 的缓存项

    // 按最后访问时间排序
    const items = Array.from(this.cache.entries())
      .sort(([, a], [, b]) => a.lastAccessed - b.lastAccessed);

    // 驱逐最早访问的项
    for (let i = 0; i < evictCount && i < items.length; i++) {
      this.cache.delete(items[i][0]);
    }

    // 更新统计信息
    this.evictions += evictCount;
  }

  /**
   * 关闭缓存
   */
  public close(): void {
    this.stopCleanupTimer();
    this.clear();
  }
}
