/**
 * 验证码服务
 */

import React, { useEffect, useState } from 'react';
import { CaptchaConfig, ValidationResult } from '../types';

/**
 * 验证码服务类
 */
export class CaptchaService {
  private config: CaptchaConfig;
  private token: string | null = null;
  private submissionCount: number = 0;

  /**
   * 构造函数
   */
  constructor() {
    this.config = {
      enabled: false,
      type: 'none',
      triggerThreshold: 0
    };
  }

  /**
   * 初始化服务
   * @param config 验证码配置
   */
  public initialize(config: CaptchaConfig): void {
    this.config = { ...config };
    
    // 从本地存储加载提交计数
    const storedCount = localStorage.getItem('security_submission_count');
    if (storedCount) {
      this.submissionCount = parseInt(storedCount, 10);
    }
    
    // 如果使用 Turnstile，加载脚本
    if (this.config.enabled && this.config.type === 'turnstile') {
      this.loadTurnstileScript();
    }
  }

  /**
   * 更新配置
   * @param config 验证码配置
   */
  public updateConfig(config: CaptchaConfig): void {
    const oldType = this.config.type;
    this.config = { ...this.config, ...config };
    
    // 如果类型变更为 Turnstile，加载脚本
    if (this.config.enabled && this.config.type === 'turnstile' && oldType !== 'turnstile') {
      this.loadTurnstileScript();
    }
  }

  /**
   * 获取验证码元数据
   */
  public getMetadata(): any {
    return {
      token: this.token,
      type: this.config.type
    };
  }

  /**
   * 验证验证码
   */
  public async validateCaptcha(): Promise<ValidationResult> {
    // 如果未启用验证码，直接返回有效
    if (!this.config.enabled) {
      return { valid: true };
    }
    
    // 检查是否需要触发验证码
    if (this.submissionCount < this.config.triggerThreshold) {
      // 增加提交计数
      this.submissionCount++;
      localStorage.setItem('security_submission_count', this.submissionCount.toString());
      return { valid: true };
    }
    
    // 验证 Turnstile 令牌
    if (this.config.type === 'turnstile') {
      if (!this.token) {
        return { valid: false, error: '请完成人机验证' };
      }
      
      // 前端只验证令牌存在，实际验证在后端进行
      return { valid: true };
    }
    
    // 自定义验证码类型
    if (this.config.type === 'custom') {
      // 实现自定义验证码验证逻辑
      return { valid: true };
    }
    
    return { valid: true };
  }

  /**
   * 渲染验证码组件
   * @param props 组件属性
   */
  public renderCaptcha(props: any = {}): React.ReactNode {
    // 如果未启用验证码或未达到触发阈值，不渲染
    if (!this.config.enabled || this.submissionCount < this.config.triggerThreshold) {
      return null;
    }
    
    // 渲染 Turnstile 验证码
    if (this.config.type === 'turnstile') {
      return <TurnstileCaptcha 
        siteKey={this.config.siteKey || ''}
        onVerify={(token) => this.token = token}
        theme={props.theme || 'light'}
      />;
    }
    
    // 渲染自定义验证码
    if (this.config.type === 'custom') {
      return <div>自定义验证码</div>;
    }
    
    return null;
  }

  /**
   * 加载 Turnstile 脚本
   */
  private loadTurnstileScript(): void {
    // 检查脚本是否已加载
    if (document.querySelector('script[src*="turnstile"]')) {
      return;
    }
    
    // 创建脚本元素
    const script = document.createElement('script');
    script.src = 'https://challenges.cloudflare.com/turnstile/v0/api.js?render=explicit';
    script.async = true;
    script.defer = true;
    
    // 添加到文档
    document.head.appendChild(script);
  }
}

/**
 * Turnstile 验证码组件
 */
interface TurnstileCaptchaProps {
  siteKey: string;
  onVerify: (token: string) => void;
  theme?: 'light' | 'dark' | 'auto';
}

function TurnstileCaptcha({ siteKey, onVerify, theme = 'light' }: TurnstileCaptchaProps) {
  const [widgetId, setWidgetId] = useState<number | null>(null);
  const containerRef = React.useRef<HTMLDivElement>(null);

  useEffect(() => {
    // 确保 turnstile 已加载
    if (!window.turnstile) {
      const checkTurnstile = setInterval(() => {
        if (window.turnstile) {
          clearInterval(checkTurnstile);
          renderWidget();
        }
      }, 100);
      
      return () => clearInterval(checkTurnstile);
    } else {
      renderWidget();
    }

    function renderWidget() {
      if (!containerRef.current) return;
      
      // 如果已有 widget，先重置
      if (widgetId !== null) {
        window.turnstile.reset(widgetId);
      }
      
      // 渲染 widget
      const id = window.turnstile.render(containerRef.current, {
        sitekey: siteKey,
        callback: onVerify,
        theme: theme
      });
      
      setWidgetId(id);
    }

    return () => {
      // 清理 widget
      if (widgetId !== null && window.turnstile) {
        window.turnstile.remove(widgetId);
      }
    };
  }, [siteKey, onVerify, theme]);

  return <div ref={containerRef} className="security-captcha"></div>;
}

// 为 Turnstile 声明全局类型
declare global {
  interface Window {
    turnstile: {
      render: (container: HTMLElement, options: any) => number;
      reset: (widgetId: number) => void;
      remove: (widgetId: number) => void;
    };
  }
}
