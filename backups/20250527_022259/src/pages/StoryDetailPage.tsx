import { useState, useEffect } from 'react';
import { usePara<PERSON>, useNavigate } from 'react-router-dom';
import { useQuery } from '@tanstack/react-query';
import { Card, CardContent, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Skeleton } from '@/components/ui/skeleton';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import {
  ThumbsUp,
  ThumbsDown,
  ArrowLeft,
  Share2,
  Calendar,
  User,
  Tag as TagIcon,
  MessageSquare,
  Eye,
  Clock,
  BookOpen,
  ShieldAlert
} from 'lucide-react';
import { formatDate, cn } from '@/lib/utils';
import { useToast } from '@/components/ui/use-toast';
import { getStoryDetail, voteStory, getStories } from '@/lib/api';
import { motion, AnimatePresence } from 'framer-motion';
import { useMediaQuery } from '@/hooks/useMediaQuery';
import ImprovedStoryCard from '@/components/story/ImprovedStoryCard';

// Map tag IDs to labels and colors
const TAG_LABELS: Record<string, { label: string; color?: string; category?: string }> = {
  'career-change': { label: '转行经历', color: 'yellow', category: 'experience' },
  'job-hunting': { label: '求职故事', color: 'blue', category: 'job' },
  'interview': { label: '面试经验', color: 'blue', category: 'job' },
  'salary': { label: '薪资谈判', color: 'blue', category: 'job' },
  'work-life': { label: '工作生活', color: 'yellow', category: 'experience' },
  'advice': { label: '建议分享', color: 'yellow', category: 'experience' },
  'internship': { label: '实习经历', color: 'yellow', category: 'experience' },
  'overseas': { label: '海外就业', color: 'yellow', category: 'experience' },
  'startup': { label: '创业经历', color: 'yellow', category: 'experience' },
  'bachelor': { label: '本科经验', color: 'green', category: 'education' },
  'master': { label: '硕士经验', color: 'green', category: 'education' },
  'phd': { label: '博士经验', color: 'green', category: 'education' },
  'it-industry': { label: 'IT行业', color: 'purple', category: 'industry' },
  'finance': { label: '金融行业', color: 'purple', category: 'industry' },
  'education-industry': { label: '教育行业', color: 'purple', category: 'industry' },
};

// Map category IDs to labels
const CATEGORY_LABELS: Record<string, string> = {
  'success': '成功故事',
  'challenge': '挑战经历',
  'advice': '建议分享',
  'experience': '经验分享',
};

// Map education level IDs to labels
const EDUCATION_LABELS: Record<string, string> = {
  'high-school': '高中及以下',
  'college': '专科',
  'bachelor': '本科',
  'master': '硕士',
  'phd': '博士',
};

// Map industry IDs to labels
const INDUSTRY_LABELS: Record<string, string> = {
  'it': '互联网/IT',
  'finance': '金融',
  'education': '教育',
  'healthcare': '医疗',
  'manufacturing': '制造业',
  'service': '服务业',
};

export default function StoryDetailPage() {
  const { toast } = useToast();
  const { storyId } = useParams<{ storyId: string }>();
  const navigate = useNavigate();
  const [userVote, setUserVote] = useState<'like' | 'dislike' | null>(null);
  const [isVoting, setIsVoting] = useState(false);
  const [isPageLoaded, setIsPageLoaded] = useState(false);

  // 检测是否为移动设备
  const isMobile = useMediaQuery('(max-width: 768px)');

  // 页面加载动画
  useEffect(() => {
    setIsPageLoaded(true);
  }, []);

  // Fetch story detail
  const { data: storyData, isLoading, error } = useQuery(
    ['story', storyId],
    () => getStoryDetail(parseInt(storyId!)),
    {
      enabled: !!storyId,
    }
  );

  // Fetch related stories
  const { data: relatedStoriesData } = useQuery(
    ['relatedStories', storyId, storyData?.story?.tags],
    () => getStories(1, 'latest', undefined, {
      tags: storyData?.story?.tags,
      excludeId: parseInt(storyId!),
      limit: 3,
    }),
    {
      enabled: !!storyData?.story?.tags && storyData.story.tags.length > 0,
    }
  );

  // Handle voting
  const handleVote = async (voteType: 'like' | 'dislike') => {
    if (!storyId || userVote === voteType || isVoting) {
      return;
    }

    try {
      setIsVoting(true);

      const response = await voteStory({
        storyId: parseInt(storyId),
        voteType,
      });

      if (response.success) {
        setUserVote(voteType);

        // Update story data with new vote counts
        if (storyData) {
          storyData.story.likes = response.likes;
          storyData.story.dislikes = response.dislikes;
        }
      } else {
        toast({
          title: '投票失败',
          description: '请稍后重试',
          variant: 'destructive',
        });
      }
    } catch (error) {
      console.error('Error voting:', error);

      toast({
        title: '投票失败',
        description: '发生错误，请稍后重试',
        variant: 'destructive',
      });
    } finally {
      setIsVoting(false);
    }
  };

  // Handle share
  const handleShare = () => {
    if (navigator.share) {
      navigator.share({
        title: storyData?.story?.title || '故事分享',
        text: `查看这个故事：${storyData?.story?.title}`,
        url: window.location.href,
      }).catch(error => {
        console.error('Error sharing:', error);
      });
    } else {
      // Fallback for browsers that don't support the Web Share API
      navigator.clipboard.writeText(window.location.href).then(() => {
        toast({
          title: '链接已复制',
          description: '故事链接已复制到剪贴板',
        });
      });
    }
  };

  // Render loading state
  if (isLoading) {
    return (
      <div className="container mx-auto py-8">
        <Button
          variant="ghost"
          className="mb-6 flex items-center gap-2"
          onClick={() => navigate('/story-wall')}
        >
          <ArrowLeft className="h-4 w-4" />
          返回故事墙
        </Button>

        <Card className="mb-8">
          <CardHeader>
            <Skeleton className="h-8 w-3/4 mb-2" />
            <div className="flex gap-2">
              <Skeleton className="h-6 w-20" />
              <Skeleton className="h-6 w-20" />
            </div>
          </CardHeader>
          <CardContent>
            <Skeleton className="h-4 w-full mb-2" />
            <Skeleton className="h-4 w-full mb-2" />
            <Skeleton className="h-4 w-full mb-2" />
            <Skeleton className="h-4 w-3/4" />
          </CardContent>
          <CardFooter className="flex justify-between border-t pt-4">
            <Skeleton className="h-4 w-40" />
            <div className="flex gap-2">
              <Skeleton className="h-10 w-20" />
              <Skeleton className="h-10 w-20" />
            </div>
          </CardFooter>
        </Card>

        <div className="mb-8">
          <Skeleton className="h-6 w-40 mb-4" />
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            {[1, 2, 3].map(i => (
              <Card key={i}>
                <CardHeader>
                  <Skeleton className="h-6 w-3/4" />
                </CardHeader>
                <CardContent>
                  <Skeleton className="h-4 w-full mb-2" />
                  <Skeleton className="h-4 w-full mb-2" />
                  <Skeleton className="h-4 w-2/3" />
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </div>
    );
  }

  // Render error state
  if (error || !storyData || !storyData.success) {
    return (
      <div className="container mx-auto py-8">
        <Button
          variant="ghost"
          className="mb-6 flex items-center gap-2"
          onClick={() => navigate('/story-wall')}
        >
          <ArrowLeft className="h-4 w-4" />
          返回故事墙
        </Button>

        <div className="text-center py-12 border rounded-lg bg-gray-50">
          <h3 className="text-lg font-medium mb-2">故事不存在或已被删除</h3>
          <p className="text-gray-500 mb-4">
            无法找到该故事，请返回故事墙查看其他故事
          </p>
          <Button onClick={() => navigate('/story-wall')}>返回故事墙</Button>
        </div>
      </div>
    );
  }

  const { story } = storyData;

  return (
    <AnimatePresence mode="wait">
      <motion.div
        key="story-detail-page"
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        exit={{ opacity: 0 }}
        transition={{ duration: 0.5 }}
        className="min-h-screen"
      >
        <div className="container mx-auto py-6 md:py-8 px-4 md:px-6">
          <motion.div
            initial={{ opacity: 0, x: -20 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ delay: 0.1, duration: 0.3 }}
          >
            <Button
              variant="ghost"
              className="mb-6 flex items-center gap-2"
              onClick={() => navigate('/story-wall')}
              size={isMobile ? "sm" : "default"}
            >
              <ArrowLeft className={isMobile ? "h-3 w-3" : "h-4 w-4"} />
              返回故事墙
            </Button>
          </motion.div>

          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.2, duration: 0.5 }}
          >
            <Card className="mb-8 overflow-hidden">
              <CardHeader className={cn(
                "pb-3 space-y-2",
                isMobile ? "p-4" : "p-6"
              )}>
                <CardTitle className={cn(
                  "leading-tight",
                  isMobile ? "text-xl" : "text-2xl"
                )}>
                  {story.title}
                </CardTitle>

                <div className="flex flex-wrap gap-1.5 mt-2">
                  {story.tags.map((tag) => {
                    const tagInfo = TAG_LABELS[tag] || { label: tag };
                    const colorClass = tagInfo.color ? `bg-${tagInfo.color}-50 text-${tagInfo.color}-700 border-${tagInfo.color}-200` : '';

                    return (
                      <Badge
                        key={tag}
                        variant="outline"
                        className={cn(
                          "text-xs py-0.5 px-2 rounded-full transition-all",
                          colorClass,
                          "hover:bg-opacity-80"
                        )}
                        onClick={() => navigate(`/story-wall?tag=${tag}`)}
                      >
                        <TagIcon className="h-3 w-3 mr-1" />
                        {tagInfo.label}
                      </Badge>
                    );
                  })}

                  {story.category && (
                    <Badge
                      variant="outline"
                      className="bg-blue-50 text-blue-700 border-blue-200 text-xs py-0.5 px-2 rounded-full"
                      onClick={() => navigate(`/story-wall?category=${story.category}`)}
                    >
                      {CATEGORY_LABELS[story.category] || story.category}
                    </Badge>
                  )}

                  {story.educationLevel && (
                    <Badge
                      variant="outline"
                      className="bg-green-50 text-green-700 border-green-200 text-xs py-0.5 px-2 rounded-full"
                      onClick={() => navigate(`/story-wall?education=${story.educationLevel}`)}
                    >
                      {EDUCATION_LABELS[story.educationLevel] || story.educationLevel}
                    </Badge>
                  )}

                  {story.industry && (
                    <Badge
                      variant="outline"
                      className="bg-purple-50 text-purple-700 border-purple-200 text-xs py-0.5 px-2 rounded-full"
                      onClick={() => navigate(`/story-wall?industry=${story.industry}`)}
                    >
                      {INDUSTRY_LABELS[story.industry] || story.industry}
                    </Badge>
                  )}
                </div>

                <div className="flex items-center text-xs text-gray-500 gap-3 pt-2">
                  <div className="flex items-center">
                    <User className="h-3 w-3 mr-1 text-gray-400" />
                    <span>{story.author}</span>
                  </div>

                  <div className="flex items-center">
                    <Clock className="h-3 w-3 mr-1 text-gray-400" />
                    <span>{formatDate(new Date(story.createdAt))}</span>
                  </div>

                  {story.views !== undefined && (
                    <div className="flex items-center">
                      <Eye className="h-3 w-3 mr-1 text-gray-400" />
                      <span>{story.views}</span>
                    </div>
                  )}
                </div>
              </CardHeader>

              <CardContent className={cn(
                "prose prose-gray max-w-none",
                isMobile ? "p-4 pt-2 text-sm" : "p-6 pt-2"
              )}>
                {story.metadata?.deidentified && (
                  <Alert className="mb-4">
                    <ShieldAlert className="h-4 w-4" />
                    <AlertTitle>内容已脱敏</AlertTitle>
                    <AlertDescription>
                      为保护用户隐私，该内容已经过脱敏处理，可能已替换或泛化了部分个人信息。
                      {story.metadata?.deidentificationLevel === 'low' && '（低级别脱敏）'}
                      {story.metadata?.deidentificationLevel === 'medium' && '（中级别脱敏）'}
                      {story.metadata?.deidentificationLevel === 'high' && '（高级别脱敏）'}
                    </AlertDescription>
                  </Alert>
                )}

                <motion.div
                  initial={{ opacity: 0 }}
                  animate={{ opacity: 1 }}
                  transition={{ delay: 0.3, duration: 0.5 }}
                  className="text-gray-700 whitespace-pre-line"
                  dangerouslySetInnerHTML={{ __html: story.content }}
                />
              </CardContent>

              <CardFooter className={cn(
                "flex justify-between items-center border-t",
                isMobile ? "p-4 pt-3" : "p-6 pt-4"
              )}>
                <div className="flex items-center gap-2">
                  <Button
                    variant="ghost"
                    size={isMobile ? "sm" : "default"}
                    className={cn(
                      "flex items-center gap-1 px-3 rounded-full",
                      userVote === 'like' ? "bg-green-100 text-green-700" : "hover:bg-gray-100"
                    )}
                    onClick={() => handleVote('like')}
                    disabled={isVoting}
                  >
                    <ThumbsUp className={isMobile ? "h-3.5 w-3.5" : "h-4 w-4"} />
                    <span className={isMobile ? "text-xs" : "text-sm"}>{story.likes}</span>
                  </Button>

                  <Button
                    variant="ghost"
                    size={isMobile ? "sm" : "default"}
                    className={cn(
                      "flex items-center gap-1 px-3 rounded-full",
                      userVote === 'dislike' ? "bg-red-100 text-red-700" : "hover:bg-gray-100"
                    )}
                    onClick={() => handleVote('dislike')}
                    disabled={isVoting}
                  >
                    <ThumbsDown className={isMobile ? "h-3.5 w-3.5" : "h-4 w-4"} />
                    <span className={isMobile ? "text-xs" : "text-sm"}>{story.dislikes}</span>
                  </Button>
                </div>

                <Button
                  variant="ghost"
                  size={isMobile ? "sm" : "default"}
                  className="flex items-center gap-1 px-3 rounded-full hover:bg-gray-100"
                  onClick={handleShare}
                >
                  <Share2 className={isMobile ? "h-3.5 w-3.5" : "h-4 w-4"} />
                  <span className={isMobile ? "text-xs" : "text-sm"}>分享</span>
                </Button>
              </CardFooter>
            </Card>
          </motion.div>

          {relatedStoriesData && relatedStoriesData.stories.length > 0 && (
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.4, duration: 0.5 }}
              className="mb-8"
            >
              <div className="flex items-center gap-2 mb-4">
                <BookOpen className={isMobile ? "h-4 w-4" : "h-5 w-5"} />
                <h2 className={isMobile ? "text-lg font-bold" : "text-xl font-bold"}>相关故事</h2>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-3 gap-4 md:gap-6">
                <AnimatePresence>
                  {relatedStoriesData.stories.map((relatedStory, index) => (
                    <motion.div
                      key={relatedStory.id}
                      initial={{ opacity: 0, y: 20 }}
                      animate={{ opacity: 1, y: 0 }}
                      exit={{ opacity: 0, scale: 0.9 }}
                      transition={{
                        duration: 0.4,
                        delay: 0.5 + index * 0.1,
                        ease: [0.25, 0.1, 0.25, 1.0]
                      }}
                    >
                      <ImprovedStoryCard
                        story={relatedStory}
                        onClick={() => navigate(`/story/${relatedStory.id}`)}
                        compact={true}
                        showAnimation={false}
                      />
                    </motion.div>
                  ))}
                </AnimatePresence>
              </div>
            </motion.div>
          )}
        </div>
      </motion.div>
    </AnimatePresence>
  );
}
