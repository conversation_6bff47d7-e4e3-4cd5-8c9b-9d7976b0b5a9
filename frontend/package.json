{"name": "college-employment-survey-frontend", "private": true, "version": "0.1.0", "type": "module", "scripts": {"start": "vite", "dev": "vite", "build": "vite build && node copy-functions.js", "build:dev": "vite build --mode development", "build:staging": "vite build --mode staging", "build:prod": "vite build --mode production", "build:check": "tsc && vite build", "lint": "eslint . --ext ts,tsx --report-unused-disable-directives --max-warnings 0", "preview": "vite preview", "test": "vitest run", "test:watch": "vitest", "test:ui": "vitest --ui", "deploy": "vite build && wrangler pages deploy dist", "deploy:staging": "npm run build:staging && wrangler pages deploy dist --project-name=\"college-employment-survey-staging\"", "deploy:prod": "npm run build:prod && wrangler pages deploy dist --project-name=\"college-employment-survey-production\"", "setup:dev": "node scripts/setup-dev-env.cjs", "check:env": "node scripts/setup-dev-env.cjs", "dev:full": "npm run setup:dev && npm run dev"}, "dependencies": {"@ant-design/charts": "^2.3.0", "@cloudflare/workers-types": "^4.20250520.0", "@emotion/react": "^11.14.0", "@emotion/styled": "^11.14.0", "@fingerprintjs/fingerprintjs": "^4.6.2", "@hookform/resolvers": "^3.1.1", "@mui/icons-material": "^5.15.11", "@mui/material": "^5.15.11", "@radix-ui/react-alert-dialog": "^1.1.13", "@radix-ui/react-avatar": "^1.0.3", "@radix-ui/react-checkbox": "^1.0.4", "@radix-ui/react-collapsible": "^1.1.10", "@radix-ui/react-dialog": "^1.1.13", "@radix-ui/react-dropdown-menu": "^2.1.14", "@radix-ui/react-label": "^2.0.2", "@radix-ui/react-popover": "^1.1.13", "@radix-ui/react-progress": "^1.0.3", "@radix-ui/react-radio-group": "^1.1.3", "@radix-ui/react-scroll-area": "^1.2.8", "@radix-ui/react-select": "^1.2.2", "@radix-ui/react-separator": "^1.1.6", "@radix-ui/react-slider": "^1.3.4", "@radix-ui/react-slot": "^1.0.2", "@radix-ui/react-switch": "^1.2.4", "@radix-ui/react-tabs": "^1.0.4", "@radix-ui/react-toast": "^1.1.4", "@radix-ui/react-tooltip": "^1.2.6", "@tanstack/react-query": "^4.29.19", "@types/react-beautiful-dnd": "^13.1.8", "antd": "^5.25.2", "axios": "^1.9.0", "chart.js": "^4.4.9", "class-variance-authority": "^0.6.1", "clsx": "^1.2.1", "date-fns": "^4.1.0", "framer-motion": "^12.11.3", "lucide-react": "^0.259.0", "notistack": "^3.0.2", "react": "^18.2.0", "react-beautiful-dnd": "^13.1.1", "react-chartjs-2": "^5.3.0", "react-day-picker": "^9.7.0", "react-dom": "^18.2.0", "react-hook-form": "^7.45.1", "react-intersection-observer": "^9.16.0", "react-json-view-lite": "^2.4.1", "react-router-dom": "^6.14.1", "react-window": "^1.8.11", "react-window-infinite-loader": "^1.0.10", "recharts": "^2.15.3", "tailwind-merge": "^1.14.0", "tailwindcss-animate": "^1.0.7", "uuid": "^11.1.0", "web-vitals": "^3.5.2", "zod": "^3.21.4"}, "devDependencies": {"@testing-library/jest-dom": "^5.16.5", "@testing-library/react": "^14.0.0", "@testing-library/user-event": "^14.4.3", "@types/node": "^20.4.1", "@types/react": "^18.2.14", "@types/react-dom": "^18.2.6", "@types/react-window": "^1.8.8", "@typescript-eslint/eslint-plugin": "^5.61.0", "@typescript-eslint/parser": "^5.61.0", "@vitejs/plugin-react": "^4.0.1", "autoprefixer": "^10.4.14", "eslint": "^8.44.0", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-refresh": "^0.4.1", "jsdom": "^22.1.0", "msw": "^2.8.4", "postcss": "^8.4.25", "tailwindcss": "^3.3.2", "typescript": "^5.0.2", "vite": "^4.4.0", "vitest": "^0.33.0", "wrangler": "^4.17.0"}, "msw": {"workerDirectory": ["public"]}, "description": "本项目是一个基于 React + TypeScript 的前端应用，旨在通过结构化问卷收集大学生及各类学历背景毕业生的就业情况、求职经历和期望信息，同时搭建一个轻量级匿名社交平台，用于分享故事、观点和经验，实现数据统计、共鸣传播与舆情反馈的结合。", "main": "postcss.config.js", "keywords": [], "author": "", "license": "ISC"}