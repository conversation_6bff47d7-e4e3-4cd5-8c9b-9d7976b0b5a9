import { useState } from 'react';
import { useApi } from './useApi';
import { useToast } from '@/components/ui/use-toast';

// 内容审核结果接口
export interface ContentModerationResult {
  isSafe: boolean;
  issues: string[];
  confidence: number;
  explanation: string;
  suggestedAction: 'approve' | 'reject' | 'review';
  severity?: 'low' | 'medium' | 'high';
  dataQuality?: 'low' | 'medium' | 'high';
  constructiveValue?: 'none' | 'low' | 'medium' | 'high';
  storyValue?: 'none' | 'low' | 'medium' | 'high';
}

// 内容审核钩子返回值接口
interface UseContentModerationReturn {
  moderateContent: (content: string, contentType?: string, contentId?: string) => Promise<ContentModerationResult | null>;
  moderateBatch: (contents: { id: string; content: string; type?: string }[]) => Promise<{ id: string; result: ContentModerationResult }[] | null>;
  getAIServiceStatus: () => Promise<{ available: boolean; provider: string; model: string } | null>;
  isLoading: boolean;
  error: string | null;
  clearError: () => void;
}

/**
 * 内容审核钩子
 *
 * 提供内容审核相关的功能
 */
export function useContentModeration(): UseContentModerationReturn {
  const api = useApi();
  const { toast } = useToast();
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [error, setError] = useState<string | null>(null);

  /**
   * 清除错误
   */
  const clearError = () => {
    setError(null);
  };

  /**
   * 审核单条内容
   *
   * @param content 要审核的内容
   * @param contentType 内容类型
   * @param contentId 内容ID
   * @returns 审核结果
   */
  const moderateContent = async (
    content: string,
    contentType?: string,
    contentId?: string
  ): Promise<ContentModerationResult | null> => {
    if (!content || content.trim().length === 0) {
      setError('内容不能为空');
      return null;
    }

    setIsLoading(true);
    setError(null);

    try {
      const response = await api.post('/admin/content-moderation/moderate', {
        content,
        contentType,
        contentId
      });

      if (response.success && response.result) {
        return response.result;
      } else {
        setError(response.error || '内容审核失败');
        return null;
      }
    } catch (error) {
      console.error('内容审核失败:', error);
      setError('内容审核服务暂时不可用');
      return null;
    } finally {
      setIsLoading(false);
    }
  };

  /**
   * 批量审核内容
   *
   * @param contents 要审核的内容列表
   * @returns 审核结果列表
   */
  const moderateBatch = async (
    contents: { id: string; content: string; type?: string }[]
  ): Promise<{ id: string; result: ContentModerationResult }[] | null> => {
    if (!contents || contents.length === 0) {
      setError('内容列表不能为空');
      return null;
    }

    setIsLoading(true);
    setError(null);

    try {
      const response = await api.post('/admin/content-moderation/moderate-batch', {
        contents
      });

      if (response.success && response.results) {
        return response.results;
      } else {
        setError(response.error || '批量内容审核失败');
        return null;
      }
    } catch (error) {
      console.error('批量内容审核失败:', error);
      setError('内容审核服务暂时不可用');
      return null;
    } finally {
      setIsLoading(false);
    }
  };

  /**
   * 获取AI服务状态
   *
   * @returns AI服务状态
   */
  const getAIServiceStatus = async (): Promise<{ available: boolean; provider: string; model: string } | null> => {
    setIsLoading(true);
    setError(null);

    try {
      const response = await api.get('/admin/content-moderation/status');

      if (response.success) {
        return {
          available: response.status === 'available',
          provider: response.provider,
          model: response.model
        };
      } else {
        setError(response.error || 'AI服务状态检查失败');
        return null;
      }
    } catch (error) {
      console.error('AI服务状态检查失败:', error);
      setError('无法获取AI服务状态');
      return null;
    } finally {
      setIsLoading(false);
    }
  };

  return {
    moderateContent,
    moderateBatch,
    getAIServiceStatus,
    isLoading,
    error,
    clearError
  };
}

export default useContentModeration;
