import { useState } from 'react';
import { useToast } from '@/components/ui/use-toast';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { 
  Dialog, 
  DialogContent, 
  DialogDescription, 
  DialogFooter, 
  DialogHeader, 
  DialogTitle 
} from '@/components/ui/dialog';
import { 
  Select, 
  SelectContent, 
  SelectItem, 
  SelectTrigger, 
  SelectValue 
} from '@/components/ui/select';
import { Checkbox } from '@/components/ui/checkbox';
import { Label } from '@/components/ui/label';
import { Loader2 } from 'lucide-react';
import { ScrollArea } from '@/components/ui/scroll-area';

interface BulkEditDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onConfirm: (fields: Record<string, any>) => Promise<void>;
  selectedCount: number;
}

export default function BulkEditDialog({
  open,
  onOpenChange,
  onConfirm,
  selectedCount,
}: BulkEditDialogProps) {
  const { toast } = useToast();
  const [isProcessing, setIsProcessing] = useState(false);
  const [selectedFields, setSelectedFields] = useState<string[]>([]);
  const [fieldValues, setFieldValues] = useState<Record<string, any>>({});
  
  // 可编辑字段列表
  const editableFields = [
    { id: 'educationLevel', label: '学历层次', type: 'select', options: [
      { value: '高中/中专', label: '高中/中专' },
      { value: '大专', label: '大专' },
      { value: '本科', label: '本科' },
      { value: '硕士', label: '硕士' },
      { value: '博士', label: '博士' },
    ]},
    { id: 'employmentStatus', label: '就业状态', type: 'select', options: [
      { value: '已就业', label: '已就业' },
      { value: '待业中', label: '待业中' },
      { value: '继续深造', label: '继续深造' },
      { value: '其他', label: '其他' },
    ]},
    { id: 'region', label: '地区', type: 'text' },
    { id: 'industry', label: '行业', type: 'text' },
    { id: 'position', label: '职位', type: 'text' },
    { id: 'salary', label: '薪资', type: 'text' },
    { id: 'jobSatisfaction', label: '工作满意度', type: 'text' },
    { id: 'unemploymentDuration', label: '失业时长', type: 'text' },
    { id: 'careerChangeIntention', label: '转行意向', type: 'checkbox' },
    { id: 'isAnonymous', label: '匿名提交', type: 'checkbox' },
  ];
  
  // 处理字段选择变化
  const handleFieldSelectionChange = (fieldId: string, selected: boolean) => {
    if (selected) {
      setSelectedFields([...selectedFields, fieldId]);
      
      // 初始化字段值
      const field = editableFields.find(f => f.id === fieldId);
      if (field) {
        if (field.type === 'checkbox') {
          setFieldValues(prev => ({ ...prev, [fieldId]: false }));
        } else {
          setFieldValues(prev => ({ ...prev, [fieldId]: '' }));
        }
      }
    } else {
      setSelectedFields(selectedFields.filter(id => id !== fieldId));
      
      // 移除字段值
      const newFieldValues = { ...fieldValues };
      delete newFieldValues[fieldId];
      setFieldValues(newFieldValues);
    }
  };
  
  // 处理字段值变化
  const handleFieldValueChange = (fieldId: string, value: any) => {
    setFieldValues(prev => ({ ...prev, [fieldId]: value }));
  };
  
  // 处理确认
  const handleConfirm = async () => {
    if (selectedFields.length === 0) {
      toast({
        variant: 'destructive',
        title: '请选择至少一个字段',
        description: '您需要选择至少一个要编辑的字段',
      });
      return;
    }
    
    try {
      setIsProcessing(true);
      
      // 只包含选中的字段
      const fieldsToUpdate: Record<string, any> = {};
      selectedFields.forEach(fieldId => {
        fieldsToUpdate[fieldId] = fieldValues[fieldId];
      });
      
      await onConfirm(fieldsToUpdate);
      
      // 重置状态
      setSelectedFields([]);
      setFieldValues({});
      
      // 关闭对话框
      onOpenChange(false);
      
      toast({
        title: '批量编辑成功',
        description: `已成功编辑 ${selectedCount} 条数据`,
      });
    } catch (error) {
      console.error('批量编辑错误:', error);
      toast({
        variant: 'destructive',
        title: '批量编辑失败',
        description: '服务器错误，请稍后再试',
      });
    } finally {
      setIsProcessing(false);
    }
  };
  
  // 渲染字段编辑器
  const renderFieldEditor = (field: any) => {
    switch (field.type) {
      case 'select':
        return (
          <Select
            value={fieldValues[field.id] || ''}
            onValueChange={(value) => handleFieldValueChange(field.id, value)}
          >
            <SelectTrigger>
              <SelectValue placeholder={`选择${field.label}`} />
            </SelectTrigger>
            <SelectContent>
              {field.options.map((option: any) => (
                <SelectItem key={option.value} value={option.value}>
                  {option.label}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        );
      
      case 'checkbox':
        return (
          <div className="flex items-center space-x-2">
            <Checkbox
              id={`checkbox-${field.id}`}
              checked={fieldValues[field.id] || false}
              onCheckedChange={(checked) => 
                handleFieldValueChange(field.id, checked === true)
              }
            />
            <label
              htmlFor={`checkbox-${field.id}`}
              className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
            >
              {field.label}
            </label>
          </div>
        );
      
      case 'textarea':
        return (
          <Textarea
            value={fieldValues[field.id] || ''}
            onChange={(e) => handleFieldValueChange(field.id, e.target.value)}
            placeholder={`输入${field.label}`}
          />
        );
      
      default: // text
        return (
          <Input
            value={fieldValues[field.id] || ''}
            onChange={(e) => handleFieldValueChange(field.id, e.target.value)}
            placeholder={`输入${field.label}`}
          />
        );
    }
  };
  
  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-md max-h-[90vh]">
        <DialogHeader>
          <DialogTitle>批量编辑</DialogTitle>
          <DialogDescription>
            同时编辑选中的 {selectedCount} 条数据
          </DialogDescription>
        </DialogHeader>
        
        <ScrollArea className="max-h-[calc(90vh-180px)] pr-4">
          <div className="grid gap-4 py-4">
            <div className="space-y-2">
              <Label>选择要编辑的字段</Label>
              <div className="grid grid-cols-2 gap-2">
                {editableFields.map(field => (
                  <div key={field.id} className="flex items-center space-x-2">
                    <Checkbox
                      id={`field-${field.id}`}
                      checked={selectedFields.includes(field.id)}
                      onCheckedChange={(checked) => 
                        handleFieldSelectionChange(field.id, checked === true)
                      }
                    />
                    <label
                      htmlFor={`field-${field.id}`}
                      className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
                    >
                      {field.label}
                    </label>
                  </div>
                ))}
              </div>
            </div>
            
            {selectedFields.length > 0 && (
              <div className="space-y-4">
                <Label>编辑选中的字段</Label>
                {selectedFields.map(fieldId => {
                  const field = editableFields.find(f => f.id === fieldId);
                  if (!field) return null;
                  
                  return (
                    <div key={fieldId} className="space-y-2">
                      <Label htmlFor={fieldId}>{field.label}</Label>
                      {renderFieldEditor(field)}
                    </div>
                  );
                })}
              </div>
            )}
          </div>
        </ScrollArea>
        
        <DialogFooter>
          <Button
            type="button"
            variant="outline"
            onClick={() => onOpenChange(false)}
            disabled={isProcessing}
          >
            取消
          </Button>
          <Button 
            type="button" 
            onClick={handleConfirm}
            disabled={isProcessing || selectedFields.length === 0}
          >
            {isProcessing && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
            确认编辑
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
