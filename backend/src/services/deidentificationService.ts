/**
 * 内容脱敏服务
 *
 * 该服务负责处理用户生成内容的脱敏和防溯源保护
 */

import { Env } from '../types';
import config from '../config';

// 脱敏配置类型
export interface DeidentificationConfig {
  enabled: boolean;                // 是否启用脱敏
  level: 'low' | 'medium' | 'high'; // 脱敏级别
  aiProvider: 'openai' | 'mock';   // AI 提供商
  apiKey?: string;                 // API 密钥
  model?: string;                  // 模型名称
  preserveSemantics: boolean;      // 是否保留语义
  applyToStories: boolean;         // 是否应用于故事
  applyToQuestionnaires: boolean;  // 是否应用于问卷
  adminCanViewOriginal: boolean;   // 管理员是否可以查看原始内容
}

// 默认脱敏配置
const defaultConfig: DeidentificationConfig = {
  enabled: false,
  level: 'medium',
  aiProvider: 'mock',
  model: 'gpt-4o',
  preserveSemantics: true,
  applyToStories: true,
  applyToQuestionnaires: false,
  adminCanViewOriginal: true
};

// 当前配置
let currentConfig: DeidentificationConfig = { ...defaultConfig };

/**
 * 脱敏服务类
 */
export class DeidentificationService {
  /**
   * 获取当前配置
   */
  static getConfig(): DeidentificationConfig {
    return { ...currentConfig };
  }

  /**
   * 更新配置
   */
  static updateConfig(newConfig: Partial<DeidentificationConfig>): DeidentificationConfig {
    currentConfig = { ...currentConfig, ...newConfig };
    return { ...currentConfig };
  }

  /**
   * 脱敏内容
   * @param content 原始内容
   * @param contentType 内容类型
   * @param env 环境变量
   * @returns 脱敏后的内容
   */
  static async sanitizeContent(
    content: string,
    contentType: 'story' | 'questionnaire' = 'story',
    env?: Env
  ): Promise<{ sanitized: string; modified: boolean; error?: string }> {
    try {
      // 输入验证
      if (!content || typeof content !== 'string') {
        return {
          sanitized: content || '',
          modified: false,
          error: '无效的内容格式'
        };
      }

      // 检查是否启用脱敏
      if (!currentConfig.enabled) {
        return { sanitized: content, modified: false };
      }

      // 检查内容类型是否需要脱敏
      if (contentType === 'story' && !currentConfig.applyToStories) {
        return { sanitized: content, modified: false };
      }

      if (contentType === 'questionnaire' && !currentConfig.applyToQuestionnaires) {
        return { sanitized: content, modified: false };
      }

      // 根据提供商选择脱敏方法
      let result;
      if (currentConfig.aiProvider === 'openai') {
        result = await DeidentificationService.sanitizeWithOpenAI(content, env);
      } else {
        result = await DeidentificationService.sanitizeWithMock(content);
      }

      // 结果验证
      if (!result.sanitized || typeof result.sanitized !== 'string') {
        console.error('脱敏结果无效:', result);
        return {
          sanitized: content,
          modified: false,
          error: '脱敏处理返回了无效的结果'
        };
      }

      return result;
    } catch (error) {
      console.error('脱敏处理过程中发生错误:', error);
      return {
        sanitized: content,
        modified: false,
        error: error instanceof Error ? error.message : '脱敏处理过程中发生未知错误'
      };
    }
  }

  /**
   * 使用 OpenAI 进行脱敏
   */
  private static async sanitizeWithOpenAI(content: string, env?: Env): Promise<{ sanitized: string; modified: boolean; error?: string }> {
    try {
      // 获取 API 密钥
      const apiKey = currentConfig.apiKey || env?.OPENAI_API_KEY || '';

      if (!apiKey) {
        console.error('OpenAI API key not found');
        return {
          sanitized: content,
          modified: false,
          error: 'OpenAI API 密钥未配置'
        };
      }

      // 构建提示词
      const prompt = DeidentificationService.buildPrompt(content);

      // 设置超时
      const controller = new AbortController();
      const timeoutId = setTimeout(() => controller.abort(), 30000); // 30秒超时

      try {
        // 调用 OpenAI API
        const response = await fetch('https://api.openai.com/v1/chat/completions', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${apiKey}`
          },
          body: JSON.stringify({
            model: currentConfig.model || 'gpt-4o',
            messages: [
              {
                role: 'system',
                content: prompt.system
              },
              {
                role: 'user',
                content: prompt.user
              }
            ],
            temperature: 0.3,
            max_tokens: 2000
          }),
          signal: controller.signal
        });

        clearTimeout(timeoutId);

        // 解析响应
        const data = await response.json();

        if (!response.ok) {
          const errorMessage = data.error?.message || JSON.stringify(data);
          console.error('OpenAI API error:', errorMessage);
          return {
            sanitized: content,
            modified: false,
            error: `OpenAI API 错误: ${errorMessage}`
          };
        }

        if (!data.choices || !data.choices[0] || !data.choices[0].message || !data.choices[0].message.content) {
          console.error('Invalid OpenAI API response:', data);
          return {
            sanitized: content,
            modified: false,
            error: 'OpenAI API 返回了无效的响应格式'
          };
        }

        const sanitized = data.choices[0].message.content.trim();

        // 检查是否有修改
        const modified = sanitized !== content;

        return { sanitized, modified };
      } catch (fetchError) {
        clearTimeout(timeoutId);
        if (fetchError.name === 'AbortError') {
          console.error('OpenAI API request timed out');
          return {
            sanitized: content,
            modified: false,
            error: 'OpenAI API 请求超时'
          };
        }
        throw fetchError; // 重新抛出其他错误，由外层 catch 处理
      }
    } catch (error) {
      console.error('Error sanitizing content with OpenAI:', error);
      return {
        sanitized: content,
        modified: false,
        error: error instanceof Error ? `OpenAI 处理错误: ${error.message}` : '脱敏处理过程中发生未知错误'
      };
    }
  }

  /**
   * 使用模拟数据进行脱敏
   */
  private static async sanitizeWithMock(content: string): Promise<{ sanitized: string; modified: boolean; error?: string }> {
    try {
      // 简单的模拟脱敏逻辑
      let sanitized = content;

      // 根据脱敏级别选择不同的替换策略
      if (currentConfig.level === 'low') {
        // 低级别脱敏：仅替换明显的个人信息

        // 替换姓名
        sanitized = sanitized.replace(/我叫([^，。,.!?！？\s]{1,4})/g, '我叫某位学生');
        sanitized = sanitized.replace(/我是([^，。,.!?！？\s]{1,4})/g, '我是某位学生');

        // 替换学校
        const schools = ['北京大学', '清华大学', '复旦大学', '上海交通大学', '浙江大学', '南京大学', '武汉大学', '中山大学'];
        schools.forEach(school => {
          sanitized = sanitized.replace(new RegExp(school, 'g'), '某高校');
        });
      } else if (currentConfig.level === 'medium' || currentConfig.level === 'high') {
        // 中高级别脱敏：替换更多信息

        // 替换姓名
        sanitized = sanitized.replace(/我叫([^，。,.!?！？\s]{1,4})/g, '我叫某位学生');
        sanitized = sanitized.replace(/我是([^，。,.!?！？\s]{1,4})/g, '我是某位学生');

        // 替换学校
        const schools = ['北京大学', '清华大学', '复旦大学', '上海交通大学', '浙江大学', '南京大学', '武汉大学', '中山大学'];
        schools.forEach(school => {
          sanitized = sanitized.replace(new RegExp(school, 'g'), '某高校');
        });

        // 替换公司
        const companies = ['阿里巴巴', '腾讯', '百度', '字节跳动', '美团', '京东', '华为', '小米'];
        companies.forEach(company => {
          sanitized = sanitized.replace(new RegExp(company, 'g'), '某公司');
        });

        // 替换城市
        const cities = ['北京', '上海', '广州', '深圳', '杭州', '南京', '武汉', '成都', '西安'];
        cities.forEach(city => {
          sanitized = sanitized.replace(new RegExp(city, 'g'), '某城市');
        });

        // 高级别脱敏：额外替换更多信息
        if (currentConfig.level === 'high') {
          // 替换年份
          sanitized = sanitized.replace(/20\d\d年/g, '某年');
          sanitized = sanitized.replace(/20\d\d届/g, '某届');

          // 替换专业
          const majors = ['计算机科学', '软件工程', '人工智能', '数据科学', '电子工程', '通信工程', '金融学', '经济学'];
          majors.forEach(major => {
            sanitized = sanitized.replace(new RegExp(major, 'g'), '某专业');
          });

          // 替换职位
          const positions = ['工程师', '开发', '产品经理', '设计师', '运营', '市场', '销售', '人力资源'];
          positions.forEach(position => {
            sanitized = sanitized.replace(new RegExp(position, 'g'), '某职位');
          });
        }
      }

      // 检查是否有修改
      const modified = sanitized !== content;

      return { sanitized, modified };
    } catch (error) {
      console.error('Error sanitizing content with mock:', error);
      return {
        sanitized: content,
        modified: false,
        error: error instanceof Error ? `模拟脱敏处理错误: ${error.message}` : '模拟脱敏处理过程中发生未知错误'
      };
    }
  }

  /**
   * 构建提示词
   */
  private static buildPrompt(content: string): { system: string; user: string } {
    // 根据脱敏级别选择提示词
    let systemPrompt = '';

    switch (currentConfig.level) {
      case 'low':
        systemPrompt = `你是一个文本隐私保护助手，请将用户生成内容进行基础脱敏处理，仅替换明显的个人身份信息，如具体姓名、精确地点、学校名称等。保持内容的整体语义和表达方式不变。`;
        break;
      case 'medium':
        systemPrompt = `你是一个文本隐私保护助手，请将用户生成内容进行中度脱敏处理，替换所有可能暴露身份的信息，包括人名、地点、学校、公司、职位、联系方式等，同时保持内容可读性和情感表达。`;
        break;
      case 'high':
        systemPrompt = `你是一个文本隐私保护助手，请将用户生成内容进行高度脱敏处理，替换所有可能暴露身份的信息，包括人名、地点、学校、公司、职位、联系方式、特定经历等。对于可能具有唯一性的经历或情况，请进行泛化处理，确保内容无法被用于识别个人。`;
        break;
      default:
        systemPrompt = `你是一个文本隐私保护助手，请将用户生成内容进行中度脱敏处理，替换所有可能暴露身份的信息，包括人名、地点、学校、公司、职位、联系方式等，同时保持内容可读性和情感表达。`;
    }

    // 添加示例
    systemPrompt += `\n\n示例输入：
"我是复旦大学2022届的李明，在阿里巴巴实习，被裁员了很绝望。"

输出应为：
"我是某高校2022届的一名学生，在某科技公司实习后被裁员了，感到非常绝望。"`;

    // 用户提示词
    const userPrompt = `请对以下内容进行脱敏处理：\n\n${content}`;

    return {
      system: systemPrompt,
      user: userPrompt
    };
  }
}
