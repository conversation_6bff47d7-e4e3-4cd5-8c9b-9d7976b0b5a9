import { useState, useCallback } from 'react';

interface ApiRequest {
  id: string;
  name: string;
  endpoint: string;
  method: string;
  status: 'pending' | 'success' | 'error' | 'idle';
  responseTime?: number;
  data?: any;
  error?: string;
  timestamp?: string;
  expectedStructure?: any;
  dataValidation?: {
    isValid: boolean;
    missingFields: string[];
    extraFields: string[];
    typeErrors: string[];
  };
}

interface ApiDebugConfig {
  pageName: string;
  requests: {
    id: string;
    name: string;
    endpoint: string;
    method: string;
    expectedStructure?: any;
    testFunction: () => Promise<any>;
  }[];
}

export const useApiDebug = (config: ApiDebugConfig) => {
  const [requests, setRequests] = useState<ApiRequest[]>(
    config.requests.map(req => ({
      ...req,
      status: 'idle' as const,
    }))
  );

  // 验证数据结构
  const validateDataStructure = useCallback((data: any, expectedStructure: any) => {
    if (!expectedStructure) {
      return {
        isValid: true,
        missingFields: [],
        extraFields: [],
        typeErrors: []
      };
    }

    const missingFields: string[] = [];
    const extraFields: string[] = [];
    const typeErrors: string[] = [];

    // 检查必需字段
    const checkFields = (obj: any, expected: any, path = '') => {
      for (const key in expected) {
        const currentPath = path ? `${path}.${key}` : key;
        
        if (!(key in obj)) {
          missingFields.push(currentPath);
        } else {
          const expectedType = typeof expected[key];
          const actualType = typeof obj[key];
          
          if (expectedType !== 'object' && expectedType !== actualType) {
            typeErrors.push(`${currentPath}: 期望 ${expectedType}, 实际 ${actualType}`);
          } else if (expectedType === 'object' && expected[key] !== null && obj[key] !== null) {
            checkFields(obj[key], expected[key], currentPath);
          }
        }
      }

      // 检查额外字段
      for (const key in obj) {
        const currentPath = path ? `${path}.${key}` : key;
        if (!(key in expected)) {
          extraFields.push(currentPath);
        }
      }
    };

    if (data && typeof data === 'object') {
      checkFields(data, expectedStructure);
    }

    return {
      isValid: missingFields.length === 0 && typeErrors.length === 0,
      missingFields,
      extraFields,
      typeErrors
    };
  }, []);

  // 测试单个API请求
  const testRequest = useCallback(async (requestId: string) => {
    const configRequest = config.requests.find(r => r.id === requestId);
    if (!configRequest) return;

    setRequests(prev => prev.map(req => 
      req.id === requestId 
        ? { ...req, status: 'pending', timestamp: new Date().toISOString() }
        : req
    ));

    const startTime = Date.now();

    try {
      const data = await configRequest.testFunction();
      const responseTime = Date.now() - startTime;
      
      const validation = validateDataStructure(data, configRequest.expectedStructure);

      setRequests(prev => prev.map(req => 
        req.id === requestId 
          ? { 
              ...req, 
              status: 'success',
              data,
              responseTime,
              dataValidation: validation,
              error: undefined
            }
          : req
      ));
    } catch (error) {
      const responseTime = Date.now() - startTime;
      
      setRequests(prev => prev.map(req => 
        req.id === requestId 
          ? { 
              ...req, 
              status: 'error',
              error: error instanceof Error ? error.message : String(error),
              responseTime,
              data: undefined
            }
          : req
      ));
    }
  }, [config.requests, validateDataStructure]);

  // 测试所有API请求
  const testAllRequests = useCallback(async () => {
    const promises = config.requests.map(req => testRequest(req.id));
    await Promise.allSettled(promises);
  }, [config.requests, testRequest]);

  // 更新请求状态（用于页面组件调用）
  const updateRequestStatus = useCallback((requestId: string, status: ApiRequest['status'], data?: any, error?: string) => {
    setRequests(prev => prev.map(req => 
      req.id === requestId 
        ? { 
            ...req, 
            status,
            data,
            error,
            timestamp: new Date().toISOString(),
            dataValidation: data ? validateDataStructure(data, config.requests.find(r => r.id === requestId)?.expectedStructure) : undefined
          }
        : req
    ));
  }, [config.requests, validateDataStructure]);

  // 获取请求统计
  const getStats = useCallback(() => {
    const total = requests.length;
    const success = requests.filter(r => r.status === 'success').length;
    const error = requests.filter(r => r.status === 'error').length;
    const pending = requests.filter(r => r.status === 'pending').length;
    const avgResponseTime = requests
      .filter(r => r.responseTime)
      .reduce((sum, r) => sum + (r.responseTime || 0), 0) / 
      requests.filter(r => r.responseTime).length || 0;

    return {
      total,
      success,
      error,
      pending,
      avgResponseTime,
      successRate: total > 0 ? (success / total) * 100 : 0
    };
  }, [requests]);

  return {
    requests,
    testRequest,
    testAllRequests,
    updateRequestStatus,
    getStats,
    pageName: config.pageName
  };
};

// 预定义的数据结构模板
export const DataStructures = {
  // 问卷统计数据结构
  questionnaireStats: {
    success: true,
    statistics: {
      totalResponses: 0,
      verifiedCount: 0,
      anonymousCount: 0,
      educationLevels: [{ name: '', count: 0 }],
      regions: [{ name: '', count: 0 }],
      majors: [{ name: '', count: 0 }],
      industries: [{ name: '', count: 0 }]
    }
  },

  // 故事列表数据结构
  storyList: {
    success: true,
    stories: [{
      id: 0,
      title: '',
      content: '',
      author: '',
      isAnonymous: true,
      category: '',
      tags: [''],
      likes: 0,
      dislikes: 0,
      status: '',
      createdAt: '',
      educationLevel: '',
      industry: ''
    }],
    totalPages: 0,
    currentPage: 0,
    popularTags: ['']
  },

  // 数据可视化数据结构
  visualizationData: {
    success: true,
    stats: {
      educationLevels: [{ name: '', count: 0 }],
      industries: [{ name: '', count: 0 }],
      regions: [{ name: '', count: 0 }],
      expectedSalaries: [{ range: '', count: 0 }],
      actualSalaries: [{ range: '', count: 0 }],
      unemploymentDurations: [{ duration: '', count: 0 }],
      careerChanges: [{ group: '', count: 0, hasIntention: 0 }],
      verifiedCount: 0,
      anonymousCount: 0
    }
  },

  // 问卷心声数据结构
  questionnaireVoices: {
    success: true,
    data: [{
      id: '',
      type: '',
      content: '',
      author: '',
      createdAt: '',
      likes: 0,
      category: '',
      educationLevel: '',
      region: ''
    }],
    pagination: {
      page: 0,
      pageSize: 0,
      total: 0,
      hasMore: false
    }
  }
};

export default useApiDebug;
