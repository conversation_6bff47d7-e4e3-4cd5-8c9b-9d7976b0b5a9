import { Hono } from 'hono';
import { Env } from '@/types';
import * as statisticsController from './statistics.controller';
import { adminAuthMiddleware } from '@/middlewares/auth.middleware';

// 创建路由
const app = new Hono<{ Bindings: Env }>();

// 添加管理员认证中间件
app.use('*', adminAuthMiddleware);

// 获取统计数据
app.get('/', statisticsController.getStatistics);

// 导出统计报表
app.get('/export', statisticsController.exportStatisticsReport);

export default app;
