# API设计文档 v2.0

基于优化后的数据库结构重新设计API接口

## 🎯 设计原则

### 1. **高频查询优化**
- 问卷统计：直接查询 `QuestionnaireStats` 表
- 用户内容：通过用户UUID直接查询
- 故事展示：查询 `StoryContent` 表

### 2. **低频查询交叉验证**
- 用户权限验证：跨表查询用户角色
- 审核历史：关联多个审核相关表
- 数据分析：聚合多个数据源

### 3. **UUID命名规范**
- 所有ID都使用前缀标识：`user_`, `text_`, `voice_` 等
- 便于系统识别和调试
- 提高查询效率

## 📊 数据可视化 API

### 1. 问卷统计数据
```typescript
// 高频查询：直接从统计表获取
GET /api/analytics/questionnaire-stats
Query: {
  questionId?: string,
  dateRange?: string,
  refresh?: boolean // 是否重新计算
}

Response: {
  success: boolean,
  data: {
    totalResponses: number,
    questions: Array<{
      questionId: string, // qitem_xxx
      questionText: string,
      questionType: 'single' | 'multiple' | 'text',
      options: Array<{
        value: string,
        text: string,
        count: number,
        percentage: number
      }>
    }>
  }
}
```

### 2. 就业分析数据
```typescript
// 基于用户答案表聚合查询
GET /api/analytics/employment-analysis
Query: {
  educationLevel?: string,
  region?: string,
  timeRange?: string
}

Response: {
  success: boolean,
  data: {
    employmentRate: number,
    salaryDistribution: Array<{
      range: string,
      count: number,
      percentage: number
    }>,
    industryDistribution: Array<{
      industry: string,
      count: number,
      avgSalary: number
    }>,
    regionalData: Array<{
      region: string,
      employmentRate: number,
      avgSalary: number
    }>
  }
}
```

## 💭 问卷心声 API

### 1. 心声列表
```typescript
// 高频查询：直接查询心声表
GET /api/voices/questionnaire
Query: {
  page: number,
  pageSize: number,
  status: 'pending' | 'approved' | 'rejected',
  voiceType: 'advice' | 'observation',
  search?: string,
  educationLevel?: string,
  region?: string
}

Response: {
  success: boolean,
  voices: Array<{
    id: string, // voice_xxx
    contentId: string, // text_xxx
    title: string,
    content: string,
    voiceType: 'advice' | 'observation',
    author: string,
    isAnonymous: boolean,
    educationLevel?: string,
    region?: string,
    createdAt: string,
    status: string
  }>,
  pagination: {
    total: number,
    pages: number,
    current: number,
    hasMore: boolean
  }
}
```

### 2. 用户自己的心声
```typescript
// 用户内容管理：通过用户UUID查询
GET /api/voices/my-voices
Headers: {
  Authorization: "Bearer {userToken}"
}

Response: {
  success: boolean,
  voices: Array<{
    id: string, // voice_xxx
    title: string,
    content: string,
    voiceType: string,
    status: 'pending' | 'approved' | 'rejected',
    createdAt: string,
    canEdit: boolean,
    canDelete: boolean
  }>
}
```

### 3. 删除心声
```typescript
// 用户内容管理：软删除
DELETE /api/voices/:voiceId
Headers: {
  Authorization: "Bearer {userToken}"
}

Response: {
  success: boolean,
  message: string
}
```

## 📖 故事墙 API

### 1. 故事列表
```typescript
// 高频查询：直接查询故事内容表
GET /api/stories
Query: {
  page: number,
  pageSize: number,
  category?: string,
  status: 'approved',
  sortBy: 'latest' | 'popular' | 'trending',
  search?: string
}

Response: {
  success: boolean,
  stories: Array<{
    id: string, // text_xxx (txid)
    title: string,
    content: string,
    author: string,
    isAnonymous: boolean,
    category?: string,
    tags: string[],
    likes: number,
    dislikes: number,
    viewCount: number,
    createdAt: string,
    version: number,
    isLatestVersion: boolean
  }>,
  pagination: {
    total: number,
    pages: number,
    current: number
  }
}
```

### 2. 故事详情
```typescript
// 包含投票数据的详细查询
GET /api/stories/:storyId
Response: {
  success: boolean,
  story: {
    id: string, // text_xxx
    title: string,
    content: string,
    author: string,
    isAnonymous: boolean,
    category?: string,
    tags: string[],
    likes: number,
    dislikes: number,
    viewCount: number,
    shareCount: number,
    createdAt: string,
    version: number,
    // 版本历史
    versions?: Array<{
      version: number,
      createdAt: string,
      changes: string
    }>
  }
}
```

### 3. 用户故事管理
```typescript
// 用户自己的故事
GET /api/stories/my-stories
Headers: {
  Authorization: "Bearer {userToken}"
}

// 发布新故事
POST /api/stories
Headers: {
  Authorization: "Bearer {userToken}"
}
Body: {
  title: string,
  content: string,
  category?: string,
  tags?: string[],
  isAnonymous: boolean
}

// 编辑故事（创建新版本）
PUT /api/stories/:storyId
Headers: {
  Authorization: "Bearer {userToken}"
}
Body: {
  title?: string,
  content?: string,
  category?: string,
  tags?: string[]
}

// 删除故事（软删除）
DELETE /api/stories/:storyId
Headers: {
  Authorization: "Bearer {userToken}"
}
```

## 🔐 用户管理 API

### 1. 匿名用户注册
```typescript
POST /api/auth/anonymous-register
Body: {
  email?: string, // 可选邮箱
  displayName?: string
}

Response: {
  success: boolean,
  user: {
    id: string, // user_xxx
    anonymousId: string, // anon_xxxxxxxx
    displayName: string,
    token: string
  }
}
```

### 2. 匿名用户登录
```typescript
POST /api/auth/anonymous-login
Body: {
  anonymousId: string // anon_xxxxxxxx
}

Response: {
  success: boolean,
  user: {
    id: string,
    anonymousId: string,
    displayName: string,
    token: string
  }
}
```

### 3. 用户内容概览
```typescript
GET /api/user/content-overview
Headers: {
  Authorization: "Bearer {userToken}"
}

Response: {
  success: boolean,
  overview: {
    questionnaires: {
      total: number,
      completed: number
    },
    voices: {
      total: number,
      approved: number,
      pending: number,
      rejected: number
    },
    stories: {
      total: number,
      approved: number,
      pending: number,
      rejected: number,
      totalViews: number,
      totalLikes: number
    }
  }
}
```

## 🛡️ 审核管理 API

### 1. 待审核内容
```typescript
// 审核员获取待审核内容
GET /api/review/pending
Headers: {
  Authorization: "Bearer {reviewerToken}"
}
Query: {
  contentType: 'questionnaire_voice' | 'story_content',
  limit: number
}

Response: {
  success: boolean,
  items: Array<{
    id: string,
    contentType: string,
    title: string,
    content: string,
    author: string,
    createdAt: string,
    priority: number
  }>
}
```

### 2. 提交审核结果
```typescript
POST /api/review/submit
Headers: {
  Authorization: "Bearer {reviewerToken}"
}
Body: {
  contentId: string,
  contentType: string,
  action: 'approve' | 'reject' | 'edit',
  notes?: string,
  modifiedContent?: string
}

Response: {
  success: boolean,
  reviewLog: {
    id: string, // review_xxx
    action: string,
    createdAt: string
  }
}
```

## 📈 高级分析 API

### 1. 趋势分析
```typescript
GET /api/analytics/trends
Query: {
  metric: 'employmentRate' | 'averageSalary' | 'jobSatisfaction',
  timeUnit: 'month' | 'quarter' | 'year',
  groupBy?: 'educationLevel' | 'region' | 'industry'
}

Response: {
  success: boolean,
  trends: Array<{
    time: string,
    value: number,
    groups?: Record<string, number>
  }>
}
```

### 2. 相关性分析
```typescript
GET /api/analytics/correlation
Query: {
  xVariable: string,
  yVariable: string,
  filters?: Record<string, string>
}

Response: {
  success: boolean,
  correlation: {
    coefficient: number,
    significance: number,
    dataPoints: Array<{
      x: string | number,
      y: string | number,
      count: number
    }>
  }
}
```

## 🔧 实施优先级

### 高优先级（立即实施）
1. 问卷统计API - 基于新的统计表
2. 心声列表API - 基于独立心声表
3. 故事列表API - 基于故事内容表
4. 用户内容管理API

### 中优先级（第二阶段）
1. 高级分析API
2. 审核管理API
3. 用户权限验证

### 低优先级（第三阶段）
1. 实时统计更新
2. 缓存优化
3. 性能监控
