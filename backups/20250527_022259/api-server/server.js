/**
 * 简单的HTTP服务器，用于模拟API
 */

const http = require('http');

// 模拟数据
const mockStories = [
  { id: 1, title: '我的就业故事', author: '张三', content: '这是我的就业故事内容...', tags: ['互联网', '程序员'], createdAt: '2024-07-01', status: 'approved' },
  { id: 2, title: '从校园到职场', author: '李四', content: '这是从校园到职场的故事...', tags: ['金融', '实习'], createdAt: '2024-07-05', status: 'approved' },
  { id: 3, title: '求职经历分享', author: '王五', content: '这是我的求职经历分享...', tags: ['求职', '面试'], createdAt: '2024-07-10', status: 'approved' }
];

const mockSurveys = [
  { id: 1, title: '2024年毕业生就业调查', description: '了解2024年毕业生就业情况', questions: [], createdAt: '2024-06-01' },
  { id: 2, title: '实习经历调查', description: '了解学生实习经历和收获', questions: [], createdAt: '2024-06-15' }
];

const mockTags = [
  { id: 1, name: '互联网', count: 10 },
  { id: 2, name: '金融', count: 5 },
  { id: 3, name: '教育', count: 8 },
  { id: 4, name: '医疗', count: 3 },
  { id: 5, name: '制造业', count: 6 },
  { id: 6, name: '程序员', count: 12 },
  { id: 7, name: '实习', count: 15 },
  { id: 8, name: '求职', count: 20 },
  { id: 9, name: '面试', count: 18 }
];

const mockStats = {
  totalStories: 100,
  totalSurveys: 10,
  totalResponses: 500,
  industryDistribution: [
    { name: '互联网', value: 30 },
    { name: '金融', value: 20 },
    { name: '教育', value: 15 },
    { name: '医疗', value: 10 },
    { name: '制造业', value: 25 }
  ],
  salaryDistribution: [
    { range: '5k以下', count: 50 },
    { range: '5k-10k', count: 150 },
    { range: '10k-15k', count: 200 },
    { range: '15k-20k', count: 80 },
    { range: '20k以上', count: 20 }
  ],
  monthlyTrends: [
    { month: '1月', stories: 5, surveys: 1 },
    { month: '2月', stories: 8, surveys: 0 },
    { month: '3月', stories: 12, surveys: 1 },
    { month: '4月', stories: 15, surveys: 2 },
    { month: '5月', stories: 20, surveys: 1 },
    { month: '6月', stories: 25, surveys: 3 },
    { month: '7月', stories: 15, surveys: 2 }
  ]
};

// CORS头
const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
  'Access-Control-Allow-Headers': 'Content-Type, Authorization',
  'Content-Type': 'application/json'
};

// 创建HTTP服务器
const server = http.createServer((req, res) => {
  // 处理CORS预检请求
  if (req.method === 'OPTIONS') {
    res.writeHead(204, corsHeaders);
    res.end();
    return;
  }

  // 设置CORS头
  Object.keys(corsHeaders).forEach(key => {
    res.setHeader(key, corsHeaders[key]);
  });

  // 获取请求URL和路径
  const url = new URL(req.url, `http://${req.headers.host}`);
  const path = url.pathname;

  // API版本前缀
  const apiVersionPrefix = '/api/v1';

  try {
    // 健康检查
    if (path === `${apiVersionPrefix}/health` && req.method === 'GET') {
      res.writeHead(200);
      res.end(JSON.stringify({
        success: true,
        data: {
          status: 'ok',
          version: '1.0.0',
          timestamp: new Date().toISOString()
        }
      }));
      return;
    }

    // 获取故事列表
    if (path === `${apiVersionPrefix}/stories` && req.method === 'GET') {
      res.writeHead(200, {
        ...corsHeaders,
        'X-Cache': 'HIT',
        'Cache-Control': 'max-age=300'
      });
      res.end(JSON.stringify({
        success: true,
        data: {
          stories: mockStories,
          total: mockStories.length
        }
      }));
      return;
    }

    // 获取故事详情
    const storyDetailMatch = path.match(new RegExp(`^${apiVersionPrefix}/stories/(\\d+)$`));
    if (storyDetailMatch && req.method === 'GET') {
      const storyId = parseInt(storyDetailMatch[1]);
      const story = mockStories.find(s => s.id === storyId);

      if (story) {
        res.writeHead(200, {
          ...corsHeaders,
          'X-Cache': 'HIT',
          'Cache-Control': 'max-age=300'
        });
        res.end(JSON.stringify({
          success: true,
          data: story
        }));
      } else {
        res.writeHead(404);
        res.end(JSON.stringify({
          success: false,
          error: 'Story not found'
        }));
      }
      return;
    }

    // 获取调查问卷列表
    if (path === `${apiVersionPrefix}/surveys` && req.method === 'GET') {
      res.writeHead(200, {
        ...corsHeaders,
        'X-Cache': 'HIT',
        'Cache-Control': 'max-age=300'
      });
      res.end(JSON.stringify({
        success: true,
        data: {
          surveys: mockSurveys,
          total: mockSurveys.length
        }
      }));
      return;
    }

    // 获取标签列表
    if (path === `${apiVersionPrefix}/tags` && req.method === 'GET') {
      res.writeHead(200, {
        ...corsHeaders,
        'X-Cache': 'HIT',
        'Cache-Control': 'max-age=300'
      });
      res.end(JSON.stringify({
        success: true,
        data: {
          tags: mockTags,
          total: mockTags.length
        }
      }));
      return;
    }

    // 获取统计数据
    if (path === `${apiVersionPrefix}/stats` && req.method === 'GET') {
      res.writeHead(200, {
        ...corsHeaders,
        'X-Cache': 'HIT',
        'Cache-Control': 'max-age=300'
      });
      res.end(JSON.stringify({
        success: true,
        data: mockStats
      }));
      return;
    }

    // 管理员登录
    if (path === `${apiVersionPrefix}/admin/login` && req.method === 'POST') {
      let body = '';
      req.on('data', chunk => {
        body += chunk.toString();
      });

      req.on('end', () => {
        try {
          const { username, password } = JSON.parse(body);

          // 简单的模拟登录逻辑
          if (username === 'admin' && password === 'admin123') {
            res.writeHead(200);
            res.end(JSON.stringify({
              success: true,
              data: {
                token: 'mock-jwt-token',
                user: {
                  id: 1,
                  username: 'admin',
                  role: 'admin',
                  name: '管理员'
                }
              }
            }));
          } else if (username === 'superadmin' && password === 'super123') {
            res.writeHead(200);
            res.end(JSON.stringify({
              success: true,
              data: {
                token: 'mock-jwt-token-super',
                user: {
                  id: 2,
                  username: 'superadmin',
                  role: 'superadmin',
                  name: '超级管理员'
                }
              }
            }));
          } else if (username === 'reviewer' && password === 'reviewer123') {
            res.writeHead(200);
            res.end(JSON.stringify({
              success: true,
              data: {
                token: 'mock-jwt-token-reviewer',
                user: {
                  id: 3,
                  username: 'reviewer',
                  role: 'reviewer',
                  name: '审核员'
                }
              }
            }));
          } else {
            res.writeHead(401);
            res.end(JSON.stringify({
              success: false,
              error: '用户名或密码错误'
            }));
          }
        } catch (error) {
          res.writeHead(400);
          res.end(JSON.stringify({
            success: false,
            error: '无效的请求数据'
          }));
        }
      });
      return;
    }

    // 性能监控API
    if (path === `${apiVersionPrefix}/admin/performance` && req.method === 'GET') {
      res.writeHead(200);
      res.end(JSON.stringify({
        success: true,
        data: {
          metrics: {
            totalRequests: 1000,
            successfulRequests: 950,
            failedRequests: 50,
            responseTimeAvg: 120,
            responseTimeMin: 50,
            responseTimeMax: 500,
            cacheHitRate: 0.85,
            dbQueryCount: 5000,
            dbQueryTimeAvg: 30
          },
          report: "=== API性能报告 ===\n\n运行时间: 7天 5小时\n总请求数: 1000\n成功请求: 950 (95.00%)\n失败请求: 50 (5.00%)\n\n响应时间:\n  平均: 120.00ms\n  最小: 50ms\n  最大: 500ms\n\n缓存性能:\n  命中率: 85.00%\n  命中数: 850\n  未命中数: 150\n"
        }
      }));
      return;
    }

    // 404 Not Found
    res.writeHead(404);
    res.end(JSON.stringify({
      success: false,
      error: 'Not Found'
    }));
  } catch (error) {
    // 处理错误
    console.error('API Error:', error);

    res.writeHead(500);
    res.end(JSON.stringify({
      success: false,
      error: 'Internal Server Error'
    }));
  }
});

// 启动服务器
const PORT = process.env.PORT || 8787;
server.listen(PORT, () => {
  console.log(`API服务器运行在 http://localhost:${PORT}`);
  console.log(`健康检查: http://localhost:${PORT}/api/v1/health`);
  console.log(`故事列表: http://localhost:${PORT}/api/v1/stories`);
  console.log(`标签列表: http://localhost:${PORT}/api/v1/tags`);
  console.log(`统计数据: http://localhost:${PORT}/api/v1/stats`);
});
