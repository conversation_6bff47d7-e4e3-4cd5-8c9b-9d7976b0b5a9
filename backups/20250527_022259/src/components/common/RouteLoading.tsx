import React from 'react';

interface RouteLoadingProps {
  message?: string;
  fullScreen?: boolean;
  showProgress?: boolean;
  progressValue?: number;
}

/**
 * 路由加载组件
 *
 * 在路由切换过程中显示加载状态
 */
const RouteLoading: React.FC<RouteLoadingProps> = ({
  message = '加载中...',
  fullScreen = true,
  showProgress = false,
  progressValue
}) => {
  return (
    <div className={`route-loading-container ${fullScreen ? 'fullscreen' : ''}`}>
      <div className="route-loading-spinner"></div>
      <p className="route-loading-message">{message}</p>

      {showProgress && progressValue !== undefined && (
        <div className="route-loading-progress">
          <div className="route-loading-progress-bar" style={{ width: `${progressValue}%` }}></div>
          <span className="route-loading-progress-text">{Math.round(progressValue)}%</span>
        </div>
      )}

      <style jsx>{`
        .route-loading-container {
          display: flex;
          flex-direction: column;
          align-items: center;
          justify-content: center;
          padding: 2rem;
          background-color: #fff;
          border-radius: 8px;
          box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }

        .fullscreen {
          position: fixed;
          top: 0;
          left: 0;
          right: 0;
          bottom: 0;
          height: 100vh;
          width: 100%;
          background-color: rgba(255, 255, 255, 0.9);
          z-index: 9999;
          box-shadow: none;
          border-radius: 0;
        }

        .route-loading-spinner {
          width: 40px;
          height: 40px;
          border: 4px solid rgba(0, 0, 0, 0.1);
          border-radius: 50%;
          border-top-color: #1976d2;
          animation: spin 1s ease-in-out infinite;
          margin-bottom: 16px;
        }

        @keyframes spin {
          to {
            transform: rotate(360deg);
          }
        }

        .route-loading-message {
          font-size: 16px;
          color: #666;
          margin-bottom: 16px;
        }

        .route-loading-progress {
          width: 200px;
          height: 8px;
          background-color: #e0e0e0;
          border-radius: 4px;
          overflow: hidden;
          position: relative;
          margin-top: 8px;
        }

        .route-loading-progress-bar {
          height: 100%;
          background-color: #1976d2;
          border-radius: 4px;
          transition: width 0.3s ease;
        }

        .route-loading-progress-text {
          font-size: 12px;
          color: #666;
          margin-top: 4px;
          display: block;
          text-align: center;
        }
      `}</style>
    </div>
  );
};

export default RouteLoading;
