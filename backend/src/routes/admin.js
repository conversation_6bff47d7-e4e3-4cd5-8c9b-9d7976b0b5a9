/**
 * 👨‍💼 管理员模块路由
 * 包含管理员仪表板、统计、数据管理等相关API
 */

import { Hono } from 'hono';
import { createSuccessResponse, createErrorResponse, wrapAsyncHandler } from '../utils/response.js';

const adminRouter = new Hono();

// 管理员仪表板统计API
adminRouter.get('/dashboard/stats', wrapAsyncHandler(async (c) => {
  // 获取用户统计
  const usersResult = await c.env.DB.prepare(
    'SELECT COUNT(*) as total FROM users_v2'
  ).first();

  // 获取故事统计
  const storiesResult = await c.env.DB.prepare(
    'SELECT COUNT(*) as total FROM story_contents_v2'
  ).first();

  // 获取心声统计
  const voicesResult = await c.env.DB.prepare(
    'SELECT COUNT(*) as total FROM questionnaire_voices_v2'
  ).first();

  // 获取问卷回复统计
  const responsesResult = await c.env.DB.prepare(
    'SELECT COUNT(*) as total FROM questionnaire_responses_v2'
  ).first();

  // 获取待审核内容统计
  const pendingReviewsResult = await c.env.DB.prepare(`
    SELECT 
      (SELECT COUNT(*) FROM story_contents_v2 WHERE status = 'pending') as pending_stories,
      (SELECT COUNT(*) FROM questionnaire_voices_v2 WHERE status = 'pending') as pending_voices
  `).first();

  const stats = {
    totalUsers: usersResult?.total || 0,
    totalStories: storiesResult?.total || 0,
    totalVoices: voicesResult?.total || 0,
    totalResponses: responsesResult?.total || 0,
    pendingReviews: {
      stories: pendingReviewsResult?.pending_stories || 0,
      voices: pendingReviewsResult?.pending_voices || 0,
      total: (pendingReviewsResult?.pending_stories || 0) + (pendingReviewsResult?.pending_voices || 0)
    },
    systemHealth: 'good',
    lastUpdated: new Date().toISOString()
  };

  return c.json(createSuccessResponse(stats, 'Dashboard statistics retrieved successfully'));
}));

// 测试数据状态API
adminRouter.get('/test-data/status', wrapAsyncHandler(async (c) => {
  // 获取最新记录的创建时间
  const latestRecordResult = await c.env.DB.prepare(`
    SELECT created_at
    FROM questionnaire_responses_v2
    ORDER BY created_at DESC
    LIMIT 1
  `).first();

  // 获取记录总数
  const countResult = await c.env.DB.prepare(
    'SELECT COUNT(*) as total FROM questionnaire_responses_v2'
  ).first();

  const status = {
    testDataEnabled: true,
    lastGenerated: latestRecordResult?.created_at || new Date().toISOString(),
    recordCount: countResult?.total || 0,
    status: 'active'
  };

  return c.json(createSuccessResponse(status, 'Test data status retrieved successfully'));
}));

// 内容审核API
adminRouter.get('/review/pending', wrapAsyncHandler(async (c) => {
  const type = c.req.query('type') || 'all'; // all, stories, voices

  let pendingContent = [];

  if (type === 'all' || type === 'stories') {
    // 获取待审核故事
    const pendingStoriesResult = await c.env.DB.prepare(`
      SELECT
        id,
        'story' as content_type,
        title,
        content,
        category,
        created_at,
        education_level_display,
        industry_display
      FROM story_contents_v2
      WHERE status = 'pending'
      ORDER BY created_at ASC
      LIMIT 20
    `).all();

    if (pendingStoriesResult.results) {
      pendingContent = pendingContent.concat(pendingStoriesResult.results);
    }
  }

  if (type === 'all' || type === 'voices') {
    // 获取待审核心声
    const pendingVoicesResult = await c.env.DB.prepare(`
      SELECT
        id,
        'voice' as content_type,
        title,
        content,
        voice_type as category,
        created_at,
        education_level_display,
        industry_display
      FROM questionnaire_voices_v2
      WHERE status = 'pending'
      ORDER BY created_at ASC
      LIMIT 20
    `).all();

    if (pendingVoicesResult.results) {
      pendingContent = pendingContent.concat(pendingVoicesResult.results);
    }
  }

  // 按创建时间排序
  pendingContent.sort((a, b) => new Date(a.created_at) - new Date(b.created_at));

  return c.json(createSuccessResponse(pendingContent, 'Pending content retrieved successfully'));
}));

// 内容审核操作API
adminRouter.post('/review/action', wrapAsyncHandler(async (c) => {
  const { contentId, contentType, action, reason } = await c.req.json();

  if (!contentId || !contentType || !action) {
    return c.json(createErrorResponse('Content ID, type, and action are required', 400), 400);
  }

  if (!['approve', 'reject'].includes(action)) {
    return c.json(createErrorResponse('Invalid action', 400), 400);
  }

  const status = action === 'approve' ? 'approved' : 'rejected';
  let tableName;

  switch (contentType) {
  case 'story':
    tableName = 'story_contents_v2';
    break;
  case 'voice':
    tableName = 'questionnaire_voices_v2';
    break;
  default:
    return c.json(createErrorResponse('Invalid content type', 400), 400);
  }

  // 更新内容状态
  await c.env.DB.prepare(`
    UPDATE ${tableName}
    SET status = ?, updated_at = ?
    WHERE id = ?
  `).bind(status, new Date().toISOString(), contentId).run();

  /*
   * 记录审核日志（如果有审核日志表的话）
   * TODO: 添加审核日志记录
   */

  const result = {
    contentId,
    contentType,
    action,
    status,
    reason,
    reviewedAt: new Date().toISOString()
  };

  return c.json(createSuccessResponse(result, `Content ${action}d successfully`));
}));

// 系统统计API
adminRouter.get('/statistics/overview', wrapAsyncHandler(async (c) => {
  // 获取各种统计数据
  const overviewResult = await c.env.DB.prepare(`
    SELECT
      (SELECT COUNT(*) FROM questionnaire_responses_v2) as total_responses,
      (SELECT COUNT(*) FROM story_contents_v2 WHERE status = 'approved') as approved_stories,
      (SELECT COUNT(*) FROM questionnaire_voices_v2 WHERE status = 'approved') as approved_voices,
      (SELECT COUNT(*) FROM users_v2) as total_users,
      (SELECT SUM(likes) FROM story_contents_v2) as total_likes,
      (SELECT SUM(views) FROM story_contents_v2) as total_views
  `).first();

  // 获取最近7天的活动统计
  const recentActivityResult = await c.env.DB.prepare(`
    SELECT
      DATE(created_at) as date,
      COUNT(*) as count
    FROM questionnaire_responses_v2
    WHERE created_at >= datetime('now', '-7 days')
    GROUP BY DATE(created_at)
    ORDER BY date DESC
  `).all();

  const overview = {
    totals: {
      responses: overviewResult?.total_responses || 0,
      approvedStories: overviewResult?.approved_stories || 0,
      approvedVoices: overviewResult?.approved_voices || 0,
      users: overviewResult?.total_users || 0,
      likes: overviewResult?.total_likes || 0,
      views: overviewResult?.total_views || 0
    },
    recentActivity: recentActivityResult.results?.map(item => ({
      date: item.date,
      count: item.count
    })) || [],
    lastUpdated: new Date().toISOString()
  };

  return c.json(createSuccessResponse(overview, 'Statistics overview retrieved successfully'));
}));

// 数据导出API
adminRouter.get('/export/:type', wrapAsyncHandler(async (c) => {
  const type = c.req.param('type');
  const format = c.req.query('format') || 'json';

  if (!['responses', 'stories', 'voices'].includes(type)) {
    return c.json(createErrorResponse('Invalid export type', 400), 400);
  }

  if (!['json', 'csv'].includes(format)) {
    return c.json(createErrorResponse('Invalid format', 400), 400);
  }

  /*
   * 这里应该实现实际的数据导出逻辑
   * 为了演示，返回一个简单的响应
   */
  const exportInfo = {
    type,
    format,
    status: 'preparing',
    estimatedTime: '2-5 minutes',
    downloadUrl: `/api/admin/download/${type}-${Date.now()}.${format}`,
    createdAt: new Date().toISOString()
  };

  return c.json(createSuccessResponse(exportInfo, 'Export initiated successfully'));
}));

export default adminRouter;
