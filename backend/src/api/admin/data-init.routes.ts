/**
 * 数据初始化路由
 */

import { Hono } from 'hono';
import { 
  initializeAllData, 
  initializeUsers, 
  initializeQuestionnaires, 
  initializeStories, 
  initializeStatistics 
} from './data-init.controller';

// Define environment interface
interface Env {
  SURVEY_KV: KVNamespace;
}

// Create Hono app
const dataInitRouter = new Hono<{ Bindings: Env }>();

// Data initialization routes
dataInitRouter.get('/init', initializeAllData);
dataInitRouter.get('/init/users', initializeUsers);
dataInitRouter.get('/init/questionnaires', initializeQuestionnaires);
dataInitRouter.get('/init/stories', initializeStories);
dataInitRouter.get('/init/statistics', initializeStatistics);

export default dataInitRouter;
