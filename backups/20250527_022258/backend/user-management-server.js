/**
 * 用户管理API服务器
 * 端口: 8787
 */

const http = require('http');
const url = require('url');
const { PrismaClient } = require('@prisma/client');

const PORT = 8789;
const prisma = new PrismaClient();

// 生成真实的UUID (暂时不使用，因为数据库schema中没有uuid字段)
// function generateUUID() {
//   return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function(c) {
//     var r = Math.random() * 16 | 0, v = c == 'x' ? r : (r & 0x3 | 0x8);
//     return v.toString(16);
//   });
// }

// 获取用户的提交统计
async function getUserSubmissionStats(userId) {
  try {
    const [storyCount, questionnaireCount] = await Promise.all([
      prisma.story.count({ where: { userId: userId } }),
      prisma.questionnaireResponse.count({ where: { userId: userId } })
    ]);

    return {
      storyCount,
      questionnaireCount,
      submissionCount: storyCount + questionnaireCount
    };
  } catch (error) {
    console.error('获取用户统计失败:', error);
    return {
      storyCount: 0,
      questionnaireCount: 0,
      submissionCount: 0
    };
  }
}

// 格式化用户数据
function formatUserData(user, stats = {}) {
  return {
    id: user.id.toString(),
    username: user.username || user.name || `用户${user.id}`,
    email: user.email,
    name: user.name,
    role: user.role || 'user',
    status: user.emailVerified ? 'active' : 'inactive',
    created_at: user.createdAt?.toISOString(),
    updated_at: user.updatedAt?.toISOString(),
    last_login_at: user.lastLoginAt?.toISOString(),
    lastLoginAt: user.lastLoginAt?.toISOString(),
    lastLoginIp: user.ipAddress,
    submissionCount: stats.submissionCount || 0,
    storyCount: stats.storyCount || 0,
    questionnaireCount: stats.questionnaireCount || 0
  };
}

// CORS headers
const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
  'Access-Control-Allow-Headers': 'Content-Type, Authorization'
};

// 发送JSON响应
function sendJSON(res, data, statusCode = 200) {
  res.writeHead(statusCode, {
    'Content-Type': 'application/json',
    ...corsHeaders
  });
  res.end(JSON.stringify(data));
}

// 处理OPTIONS请求
function handleOptions(res) {
  res.writeHead(200, corsHeaders);
  res.end();
}

// 解析请求体
function parseRequestBody(req) {
  return new Promise((resolve, reject) => {
    let body = '';
    req.on('data', chunk => {
      body += chunk.toString();
    });
    req.on('end', () => {
      try {
        resolve(JSON.parse(body));
      } catch (error) {
        reject(error);
      }
    });
  });
}

// 主请求处理函数
async function handleRequest(req, res) {
  const parsedUrl = url.parse(req.url, true);
  const path = parsedUrl.pathname;
  const method = req.method;

  console.log(`${new Date().toISOString()} ${method} ${path}`);

  // 处理OPTIONS请求
  if (method === 'OPTIONS') {
    handleOptions(res);
    return;
  }

  // 健康检查
  if (path === '/health' && method === 'GET') {
    sendJSON(res, {
      status: 'ok',
      service: 'user-management-api',
      timestamp: new Date().toISOString()
    });
    return;
  }

  // 用户管理API
  if (path === '/api/admin/users' && method === 'GET') {
    try {
      console.log('🔍 获取用户列表请求');
      console.log('查询参数:', parsedUrl.query);

      const { role, status, search, page = 1, pageSize = 10 } = parsedUrl.query;

      // 构建查询条件
      const where = {};

      // 角色过滤
      if (role && role !== 'all') {
        where.role = role;
      }

      // 状态过滤 (基于emailVerified字段)
      if (status === 'active') {
        where.emailVerified = true;
      } else if (status === 'inactive') {
        where.emailVerified = false;
      }

      // 搜索过滤 (SQLite不支持mode: 'insensitive'，使用contains即可)
      if (search) {
        where.OR = [
          { username: { contains: search } },
          { email: { contains: search } },
          { name: { contains: search } }
        ];
      }

      // 获取总数
      const total = await prisma.user.count({ where });

      // 分页查询
      const pageNum = parseInt(page);
      const pageSizeNum = parseInt(pageSize);
      const skip = (pageNum - 1) * pageSizeNum;

      const users = await prisma.user.findMany({
        where,
        skip,
        take: pageSizeNum,
        orderBy: { createdAt: 'desc' }
      });

      // 获取每个用户的统计信息
      const usersWithStats = await Promise.all(
        users.map(async (user) => {
          const stats = await getUserSubmissionStats(user.id);
          return formatUserData(user, stats);
        })
      );

      console.log('📊 返回用户数据:', usersWithStats.length, '条记录，总计:', total);

      sendJSON(res, {
        success: true,
        data: {
          users: usersWithStats,
          pagination: {
            page: pageNum,
            pageSize: pageSizeNum,
            total: total,
            totalPages: Math.ceil(total / pageSizeNum)
          }
        }
      });

    } catch (error) {
      console.error('❌ 获取用户列表失败:', error);
      sendJSON(res, {
        success: false,
        error: '获取用户列表失败',
        details: error.message
      }, 500);
    }
    return;
  }

  // 获取单个用户详情
  if (path.startsWith('/api/admin/users/') && method === 'GET') {
    try {
      const userId = path.split('/').pop();
      console.log('🔍 获取用户详情:', userId);

      // 按ID查找用户
      const user = await prisma.user.findUnique({
        where: { id: parseInt(userId) || 0 }
      });

      if (!user) {
        sendJSON(res, {
          success: false,
          error: '用户不存在'
        }, 404);
        return;
      }

      // 获取用户统计信息
      const stats = await getUserSubmissionStats(user.id);
      const formattedUser = formatUserData(user, stats);

      sendJSON(res, {
        success: true,
        data: formattedUser
      });

    } catch (error) {
      console.error('❌ 获取用户详情失败:', error);
      sendJSON(res, {
        success: false,
        error: '获取用户详情失败',
        details: error.message
      }, 500);
    }
    return;
  }

  // 创建用户
  if (path === '/api/admin/users' && method === 'POST') {
    try {
      const body = await parseRequestBody(req);
      const { username, email, password, role = 'user', name } = body;

      console.log('🆕 创建用户:', { username, email, role });

      // 验证必填字段
      if (!email) {
        sendJSON(res, {
          success: false,
          error: '邮箱为必填项'
        }, 400);
        return;
      }

      // 检查邮箱是否已存在
      const existingUser = await prisma.user.findUnique({
        where: { email }
      });

      if (existingUser) {
        sendJSON(res, {
          success: false,
          error: '邮箱已存在'
        }, 400);
        return;
      }

      // 创建新用户
      const newUser = await prisma.user.create({
        data: {
          email,
          username: username || name,
          name: name || username,
          role,
          emailVerified: true, // 管理员创建的用户默认已验证
          passwordHash: password ? require('bcryptjs').hashSync(password, 10) : null
        }
      });

      console.log('✅ 用户创建成功:', newUser.id);

      // 格式化返回数据
      const formattedUser = formatUserData(newUser);

      sendJSON(res, {
        success: true,
        data: formattedUser
      }, 201);

    } catch (error) {
      console.error('❌ 创建用户失败:', error);
      sendJSON(res, {
        success: false,
        error: '创建用户失败',
        details: error.message
      }, 500);
    }
    return;
  }

  // 更新用户信息
  if (path.startsWith('/api/admin/users/') && method === 'PUT') {
    try {
      const userId = path.split('/').pop();
      const body = await parseRequestBody(req);
      console.log('🔄 更新用户信息:', userId, body);

      // 查找用户
      const user = await prisma.user.findUnique({
        where: { id: parseInt(userId) || 0 }
      });

      if (!user) {
        sendJSON(res, {
          success: false,
          error: '用户不存在'
        }, 404);
        return;
      }

      // 构建更新数据
      const updateData = {};
      if (body.username) updateData.username = body.username;
      if (body.name) updateData.name = body.name;
      if (body.email) updateData.email = body.email;
      if (body.role) updateData.role = body.role;
      if (body.status !== undefined) {
        updateData.emailVerified = body.status === 'active';
      }
      if (body.password) {
        updateData.passwordHash = require('bcryptjs').hashSync(body.password, 10);
      }

      // 更新用户
      const updatedUser = await prisma.user.update({
        where: { id: user.id },
        data: updateData
      });

      console.log('✅ 用户更新成功:', updatedUser.id);

      // 获取更新后的统计信息
      const stats = await getUserSubmissionStats(updatedUser.id);
      const formattedUser = formatUserData(updatedUser, stats);

      sendJSON(res, {
        success: true,
        data: formattedUser
      });

    } catch (error) {
      console.error('❌ 更新用户失败:', error);
      sendJSON(res, {
        success: false,
        error: '更新用户失败',
        details: error.message
      }, 500);
    }
    return;
  }

  // 重置用户密码
  if (path.match(/^\/api\/admin\/users\/[^\/]+\/reset-password$/) && method === 'POST') {
    try {
      const userId = path.split('/')[4];
      console.log('🔑 重置用户密码:', userId);

      // 查找用户
      const user = await prisma.user.findUnique({
        where: { id: parseInt(userId) || 0 }
      });

      if (!user) {
        sendJSON(res, {
          success: false,
          error: '用户不存在'
        }, 404);
        return;
      }

      // 生成新密码
      const newPassword = Math.random().toString(36).slice(-8);
      const hashedPassword = require('bcryptjs').hashSync(newPassword, 10);

      // 更新密码
      await prisma.user.update({
        where: { id: user.id },
        data: {
          passwordHash: hashedPassword
        }
      });

      console.log('✅ 密码重置成功:', { userId, newPassword: '***' });

      sendJSON(res, {
        success: true,
        message: '密码重置成功',
        data: {
          newPassword, // 实际应用中应该通过邮件发送
          email: user.email
        }
      });

    } catch (error) {
      console.error('❌ 重置密码失败:', error);
      sendJSON(res, {
        success: false,
        error: '重置密码失败',
        details: error.message
      }, 500);
    }
    return;
  }

  // 删除用户
  if (path.startsWith('/api/admin/users/') && method === 'DELETE') {
    try {
      const userId = path.split('/').pop();
      console.log('🗑️ 删除用户:', userId);

      // 查找用户
      const user = await prisma.user.findUnique({
        where: { id: parseInt(userId) || 0 }
      });

      if (!user) {
        sendJSON(res, {
          success: false,
          error: '用户不存在'
        }, 404);
        return;
      }

      // 删除用户（注意：这会级联删除相关数据）
      await prisma.user.delete({
        where: { id: user.id }
      });

      console.log('✅ 用户删除成功:', user.id);

      sendJSON(res, {
        success: true,
        message: '用户删除成功'
      });

    } catch (error) {
      console.error('❌ 删除用户失败:', error);
      sendJSON(res, {
        success: false,
        error: '删除用户失败',
        details: error.message
      }, 500);
    }
    return;
  }



  // 获取操作日志（使用ReviewLog表模拟）
  if (path === '/api/admin/operation-logs' && method === 'GET') {
    try {
      console.log('🔍 获取操作日志');
      const { page = 1, pageSize = 20, adminId, action } = parsedUrl.query;

      // 构建查询条件
      const where = {};
      if (adminId) {
        where.reviewerId = adminId.toString();
      }
      if (action) {
        where.action = { contains: action };
      }

      // 获取总数
      const total = await prisma.reviewLog.count({ where });

      // 分页查询
      const pageNum = parseInt(page);
      const pageSizeNum = parseInt(pageSize);
      const skip = (pageNum - 1) * pageSizeNum;

      const logs = await prisma.reviewLog.findMany({
        where,
        skip,
        take: pageSizeNum,
        orderBy: { timestamp: 'desc' }
      });

      // 格式化日志数据
      const formattedLogs = logs.map(log => ({
        id: log.id,
        adminId: log.reviewerId,
        adminName: `管理员${log.reviewerId}`,
        action: log.action,
        target: log.contentId,
        details: log.reviewNotes || '无详细信息',
        timestamp: log.timestamp.toISOString(),
        ipAddress: log.ipAddress || '127.0.0.1',
        result: 'success'
      }));

      sendJSON(res, {
        success: true,
        data: {
          logs: formattedLogs,
          pagination: {
            page: pageNum,
            pageSize: pageSizeNum,
            total: total,
            totalPages: Math.ceil(total / pageSizeNum)
          }
        }
      });

    } catch (error) {
      console.error('❌ 获取操作日志失败:', error);
      sendJSON(res, {
        success: false,
        error: '获取操作日志失败',
        details: error.message
      }, 500);
    }
    return;
  }

  // 平台统计API - 为平台概况页面提供数据
  if (path === '/api/admin/dashboard/stats' && method === 'GET') {
    try {
      console.log('📊 获取平台统计数据...');

      // 获取用户统计
      const totalUsers = await prisma.user.count();
      const activeUsers = await prisma.user.count({
        where: {
          emailVerified: true
        }
      });

      // 获取今日新增用户
      const today = new Date();
      today.setHours(0, 0, 0, 0);
      const todayUsers = await prisma.user.count({
        where: {
          createdAt: {
            gte: today
          }
        }
      });

      // 获取故事统计（直接从Story表查询）
      const totalStories = await prisma.story.count();

      // 获取问卷统计（直接从QuestionnaireResponse表查询）
      const totalResponses = await prisma.questionnaireResponse.count();

      // 获取问卷心声统计（从QuestionnaireResponse表中有adviceForStudents的记录）
      const totalQuestionnaireVoices = await prisma.questionnaireResponse.count({
        where: {
          adviceForStudents: {
            not: null
          }
        }
      });

      // 模拟今日数据（实际应该从数据库查询）
      const todayStories = Math.floor(totalStories * 0.02); // 假设今日2%
      const todayResponses = Math.floor(totalResponses * 0.03); // 假设今日3%
      const todayQuestionnaireVoices = Math.floor(totalQuestionnaireVoices * 0.05); // 假设今日5%

      const result = {
        success: true,
        data: {
          totalUsers,
          activeUsers,
          totalStories,
          pendingStories: 0, // 暂时设为0，实际需要从审核系统获取
          totalResponses,
          todayUsers,
          todayStories,
          todayResponses,
          totalQuestionnaireVoices,
          todayQuestionnaireVoices,
          securityAlerts: 0,
          testDataStatus: 'loaded'
        }
      };

      console.log('✅ 平台统计数据:', result.data);
      sendJSON(res, result);

    } catch (error) {
      console.error('❌ 获取平台统计失败:', error);
      sendJSON(res, {
        success: false,
        message: '获取平台统计数据失败',
        error: error.message
      }, 500);
    }
    return;
  }

  // 角色管理API
  if (path === '/api/admin/roles' && method === 'GET') {
    try {
      console.log('🔍 获取角色列表');

      // 统计各角色用户数量
      const roleStats = await prisma.user.groupBy({
        by: ['role'],
        _count: { role: true }
      });

      // 系统预定义角色
      const systemRoles = [
        {
          id: 'superadmin',
          name: '超级管理员',
          description: '拥有系统所有权限，可以管理所有功能',
          permissions: [
            { id: 'ALL', name: '所有权限' }
          ],
          userCount: 0,
          isSystem: true
        },
        {
          id: 'admin',
          name: '管理员',
          description: '拥有用户管理、内容审核、数据分析等权限',
          permissions: [
            { id: 'USER_MANAGEMENT', name: '用户管理' },
            { id: 'CONTENT_REVIEW', name: '内容审核' },
            { id: 'DATA_ANALYSIS', name: '数据分析' },
            { id: 'SYSTEM_CONFIG', name: '系统配置' }
          ],
          userCount: 0,
          isSystem: true
        },
        {
          id: 'reviewer',
          name: '审核员',
          description: '负责内容审核和个人仪表盘管理',
          permissions: [
            { id: 'CONTENT_REVIEW', name: '内容审核' },
            { id: 'DASHBOARD_PERSONAL', name: '个人仪表盘' }
          ],
          userCount: 0,
          isSystem: true
        },
        {
          id: 'user',
          name: '普通用户',
          description: '基础用户权限，可以提交内容和查看个人仪表盘',
          permissions: [
            { id: 'DASHBOARD_PERSONAL', name: '个人仪表盘' },
            { id: 'SUBMIT_CONTENT', name: '提交内容' }
          ],
          userCount: 0,
          isSystem: true
        }
      ];

      // 更新实际用户数量
      roleStats.forEach(stat => {
        const role = systemRoles.find(r => r.id === stat.role);
        if (role) {
          role.userCount = stat._count.role;
        }
      });

      sendJSON(res, {
        success: true,
        data: systemRoles,
        pagination: {
          page: 1,
          pageSize: 10,
          total: systemRoles.length,
          totalPages: 1
        }
      });

    } catch (error) {
      console.error('❌ 获取角色列表失败:', error);
      sendJSON(res, {
        success: false,
        error: '获取角色列表失败',
        details: error.message
      }, 500);
    }
    return;
  }

  // 获取权限配置
  if (path === '/api/admin/permissions' && method === 'GET') {
    try {
      const { role } = parsedUrl.query;

      const permissions = {
        superadmin: ['ALL'],
        admin: ['USER_MANAGEMENT', 'CONTENT_REVIEW', 'DATA_ANALYSIS', 'SYSTEM_CONFIG'],
        reviewer: ['CONTENT_REVIEW', 'DASHBOARD_PERSONAL'],
        user: ['DASHBOARD_PERSONAL', 'SUBMIT_CONTENT']
      };

      if (role) {
        sendJSON(res, {
          success: true,
          data: {
            role,
            permissions: permissions[role] || []
          }
        });
      } else {
        sendJSON(res, {
          success: true,
          data: permissions
        });
      }

    } catch (error) {
      console.error('❌ 获取权限配置失败:', error);
      sendJSON(res, {
        success: false,
        error: '获取权限配置失败',
        details: error.message
      }, 500);
    }
    return;
  }

  // 404处理
  sendJSON(res, {
    success: false,
    error: 'API endpoint not found',
    path: path,
    method: method
  }, 404);
}

// 创建服务器
const server = http.createServer(handleRequest);

server.listen(PORT, async () => {
  console.log(`🚀 用户管理API服务器启动成功！`);
  console.log(`📍 地址: http://localhost:${PORT}`);
  console.log(`🔍 健康检查: http://localhost:${PORT}/health`);
  console.log(`👥 用户列表API: http://localhost:${PORT}/api/admin/users`);

  try {
    const userCount = await prisma.user.count();
    console.log(`📊 数据库用户数据: ${userCount} 条记录`);
  } catch (error) {
    console.log(`⚠️ 无法连接数据库: ${error.message}`);
  }

  console.log(`\n按 Ctrl+C 停止服务器`);
});

// 优雅关闭
process.on('SIGINT', async () => {
  console.log('\n🛑 正在关闭服务器...');
  server.close(async () => {
    await prisma.$disconnect();
    console.log('✅ 服务器已关闭');
    process.exit(0);
  });
});
