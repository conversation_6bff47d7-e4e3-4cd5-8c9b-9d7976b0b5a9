import React, { useState, useEffect } from 'react';
import { <PERSON><PERSON><PERSON>, <PERSON>, XAxis, YAxis, CartesianGrid, <PERSON>lt<PERSON>, Responsive<PERSON><PERSON><PERSON>, <PERSON>, LineChart, Line } from 'recharts';
import { Button } from '@/components/ui/button';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Loader2, RefreshCw, TrendingUp, TrendingDown, Calendar } from 'lucide-react';

interface TimeComparisonData {
  period: string;
  currentUsers: number;
  previousUsers: number;
  currentStories: number;
  previousStories: number;
  currentVoices: number;
  previousVoices: number;
  currentResponses: number;
  previousResponses: number;
  userGrowth: number;
  contentGrowth: number;
}

interface TimeComparisonChartProps {
  height?: number;
  showControls?: boolean;
  chartType?: 'bar' | 'line';
}

const TimeComparisonChart: React.FC<TimeComparisonChartProps> = ({ 
  height = 300,
  showControls = true,
  chartType = 'bar'
}) => {
  const [data, setData] = useState<TimeComparisonData[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [selectedChartType, setSelectedChartType] = useState<'bar' | 'line'>(chartType);
  const [comparisonType, setComparisonType] = useState<'week' | 'month' | 'quarter'>('month');
  const [selectedMetric, setSelectedMetric] = useState<'users' | 'stories' | 'voices' | 'responses' | 'all'>('all');

  // 获取时间段对比数据
  const fetchTimeComparisonData = async () => {
    setLoading(true);
    setError(null);
    
    try {
      const response = await fetch(`/api/admin/dashboard/time-comparison?type=${comparisonType}`);
      
      if (!response.ok) {
        throw new Error('获取时间段对比数据失败');
      }
      
      const result = await response.json();
      
      if (result.success) {
        setData(result.data);
      } else {
        throw new Error(result.message || '数据格式错误');
      }
    } catch (err) {
      console.error('时间段对比数据获取错误:', err);
      setError(err instanceof Error ? err.message : '未知错误');
      
      // 使用模拟数据作为降级
      generateMockData();
    } finally {
      setLoading(false);
    }
  };

  // 生成模拟数据
  const generateMockData = () => {
    const periods = comparisonType === 'week' ? 
      ['第1周', '第2周', '第3周', '第4周'] :
      comparisonType === 'month' ?
      ['1月', '2月', '3月', '4月', '5月', '6月'] :
      ['Q1', 'Q2', 'Q3', 'Q4'];
    
    const mockData: TimeComparisonData[] = periods.map((period, index) => {
      const currentUsers = 200 + Math.random() * 100 + index * 20;
      const previousUsers = currentUsers * (0.8 + Math.random() * 0.3);
      const currentStories = 30 + Math.random() * 20 + index * 5;
      const previousStories = currentStories * (0.7 + Math.random() * 0.4);
      const currentVoices = 25 + Math.random() * 15 + index * 3;
      const previousVoices = currentVoices * (0.6 + Math.random() * 0.5);
      const currentResponses = 80 + Math.random() * 40 + index * 10;
      const previousResponses = currentResponses * (0.75 + Math.random() * 0.35);
      
      return {
        period,
        currentUsers: Math.round(currentUsers),
        previousUsers: Math.round(previousUsers),
        currentStories: Math.round(currentStories),
        previousStories: Math.round(previousStories),
        currentVoices: Math.round(currentVoices),
        previousVoices: Math.round(previousVoices),
        currentResponses: Math.round(currentResponses),
        previousResponses: Math.round(previousResponses),
        userGrowth: Math.round(((currentUsers - previousUsers) / previousUsers) * 100 * 100) / 100,
        contentGrowth: Math.round((((currentStories + currentVoices) - (previousStories + previousVoices)) / (previousStories + previousVoices)) * 100 * 100) / 100
      };
    });
    
    setData(mockData);
  };

  // 初始化数据
  useEffect(() => {
    fetchTimeComparisonData();
  }, [comparisonType]);

  // 获取显示数据
  const getDisplayData = () => {
    if (selectedMetric === 'all') {
      return data.map(item => ({
        ...item,
        current: item.currentUsers + item.currentStories + item.currentVoices + item.currentResponses,
        previous: item.previousUsers + item.previousStories + item.previousVoices + item.previousResponses
      }));
    }
    
    return data.map(item => ({
      ...item,
      current: item[`current${selectedMetric.charAt(0).toUpperCase() + selectedMetric.slice(1)}` as keyof TimeComparisonData] as number,
      previous: item[`previous${selectedMetric.charAt(0).toUpperCase() + selectedMetric.slice(1)}` as keyof TimeComparisonData] as number
    }));
  };

  // 自定义Tooltip
  const CustomTooltip = ({ active, payload, label }: any) => {
    if (active && payload && payload.length) {
      const data = payload[0].payload;
      return (
        <div className="bg-white p-3 border rounded-lg shadow-lg">
          <p className="font-medium mb-2">{`时间段: ${label}`}</p>
          {payload.map((entry: any, index: number) => (
            <p key={index} style={{ color: entry.color }} className="text-sm">
              {`${entry.name}: ${entry.value.toLocaleString()}`}
            </p>
          ))}
          <div className="mt-2 pt-2 border-t">
            <div className="flex items-center text-sm">
              {data.userGrowth >= 0 ? (
                <TrendingUp className="h-3 w-3 text-green-500 mr-1" />
              ) : (
                <TrendingDown className="h-3 w-3 text-red-500 mr-1" />
              )}
              <span>用户增长: {data.userGrowth}%</span>
            </div>
            <div className="flex items-center text-sm">
              {data.contentGrowth >= 0 ? (
                <TrendingUp className="h-3 w-3 text-green-500 mr-1" />
              ) : (
                <TrendingDown className="h-3 w-3 text-red-500 mr-1" />
              )}
              <span>内容增长: {data.contentGrowth}%</span>
            </div>
          </div>
        </div>
      );
    }
    return null;
  };

  // 处理刷新
  const handleRefresh = () => {
    fetchTimeComparisonData();
  };

  // 渲染加载状态
  if (loading) {
    return (
      <div className="flex items-center justify-center" style={{ height: `${height}px` }}>
        <Loader2 className="h-8 w-8 animate-spin text-muted-foreground" />
        <span className="ml-2 text-muted-foreground">加载时间段对比数据...</span>
      </div>
    );
  }

  // 渲染错误状态
  if (error) {
    return (
      <div className="flex flex-col items-center justify-center" style={{ height: `${height}px` }}>
        <p className="text-red-500 mb-2">加载失败: {error}</p>
        <Button variant="outline" size="sm" onClick={handleRefresh}>
          <RefreshCw className="h-4 w-4 mr-2" />
          重试
        </Button>
      </div>
    );
  }

  const displayData = getDisplayData();

  return (
    <div className="w-full">
      {showControls && (
        <div className="space-y-4 mb-4">
          <div className="flex justify-between items-center">
            <div className="flex space-x-2">
              <Select value={comparisonType} onValueChange={(value: any) => setComparisonType(value)}>
                <SelectTrigger className="w-32">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="week">按周对比</SelectItem>
                  <SelectItem value="month">按月对比</SelectItem>
                  <SelectItem value="quarter">按季对比</SelectItem>
                </SelectContent>
              </Select>
              
              <Select value={selectedChartType} onValueChange={(value: any) => setSelectedChartType(value)}>
                <SelectTrigger className="w-24">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="bar">柱图</SelectItem>
                  <SelectItem value="line">线图</SelectItem>
                </SelectContent>
              </Select>
              
              <Select value={selectedMetric} onValueChange={(value: any) => setSelectedMetric(value)}>
                <SelectTrigger className="w-32">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">全部数据</SelectItem>
                  <SelectItem value="users">用户数据</SelectItem>
                  <SelectItem value="stories">故事数据</SelectItem>
                  <SelectItem value="voices">心声数据</SelectItem>
                  <SelectItem value="responses">问卷数据</SelectItem>
                </SelectContent>
              </Select>
            </div>
            
            <Button variant="outline" size="sm" onClick={handleRefresh}>
              <RefreshCw className="h-4 w-4 mr-2" />
              刷新
            </Button>
          </div>
          
          {/* 对比说明 */}
          <div className="flex items-center text-sm text-muted-foreground">
            <Calendar className="h-4 w-4 mr-2" />
            <span>
              对比说明: 当前{comparisonType === 'week' ? '周' : comparisonType === 'month' ? '月' : '季度'}
              与上一{comparisonType === 'week' ? '周' : comparisonType === 'month' ? '月' : '季度'}同期数据
            </span>
          </div>
        </div>
      )}
      
      <div style={{ height: `${height}px` }}>
        <ResponsiveContainer width="100%" height="100%">
          {selectedChartType === 'bar' ? (
            <BarChart data={displayData} margin={{ top: 20, right: 30, left: 20, bottom: 5 }}>
              <CartesianGrid strokeDasharray="3 3" />
              <XAxis dataKey="period" tick={{ fontSize: 12 }} />
              <YAxis tick={{ fontSize: 12 }} />
              <Tooltip content={<CustomTooltip />} />
              <Legend />
              <Bar dataKey="current" fill="#8884d8" name="当前期间" />
              <Bar dataKey="previous" fill="#82ca9d" name="上一期间" />
            </BarChart>
          ) : (
            <LineChart data={displayData} margin={{ top: 20, right: 30, left: 20, bottom: 5 }}>
              <CartesianGrid strokeDasharray="3 3" />
              <XAxis dataKey="period" tick={{ fontSize: 12 }} />
              <YAxis tick={{ fontSize: 12 }} />
              <Tooltip content={<CustomTooltip />} />
              <Legend />
              <Line 
                type="monotone" 
                dataKey="current" 
                stroke="#8884d8" 
                strokeWidth={2}
                name="当前期间"
                dot={{ r: 4 }}
              />
              <Line 
                type="monotone" 
                dataKey="previous" 
                stroke="#82ca9d" 
                strokeWidth={2}
                name="上一期间"
                dot={{ r: 4 }}
              />
            </LineChart>
          )}
        </ResponsiveContainer>
      </div>
      
      {data.length === 0 && (
        <div className="text-center text-muted-foreground mt-4">
          暂无时间段对比数据
        </div>
      )}
      
      {/* 增长率摘要 */}
      {data.length > 0 && (
        <div className="mt-4 grid grid-cols-2 gap-4 text-sm">
          <div className="text-center">
            <p className="text-muted-foreground">平均用户增长率</p>
            <div className="flex items-center justify-center">
              {(data.reduce((sum, item) => sum + item.userGrowth, 0) / data.length) >= 0 ? (
                <TrendingUp className="h-4 w-4 text-green-500 mr-1" />
              ) : (
                <TrendingDown className="h-4 w-4 text-red-500 mr-1" />
              )}
              <p className="font-semibold text-lg">
                {(data.reduce((sum, item) => sum + item.userGrowth, 0) / data.length).toFixed(1)}%
              </p>
            </div>
          </div>
          <div className="text-center">
            <p className="text-muted-foreground">平均内容增长率</p>
            <div className="flex items-center justify-center">
              {(data.reduce((sum, item) => sum + item.contentGrowth, 0) / data.length) >= 0 ? (
                <TrendingUp className="h-4 w-4 text-green-500 mr-1" />
              ) : (
                <TrendingDown className="h-4 w-4 text-red-500 mr-1" />
              )}
              <p className="font-semibold text-lg">
                {(data.reduce((sum, item) => sum + item.contentGrowth, 0) / data.length).toFixed(1)}%
              </p>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default TimeComparisonChart;
