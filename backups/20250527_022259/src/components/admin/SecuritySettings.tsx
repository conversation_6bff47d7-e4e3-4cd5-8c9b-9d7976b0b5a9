import React, { useState, useEffect } from 'react';
import { SecurityModule, SecurityConfig } from '../../lib/security';
import '../../styles/admin/security-settings.css';

// 安全设置组件
const SecuritySettings: React.FC = () => {
  // 配置状态
  const [config, setConfig] = useState<SecurityConfig>(SecurityModule.getConfig());
  const [isSaving, setIsSaving] = useState(false);
  const [saveMessage, setSaveMessage] = useState<{ type: 'success' | 'error'; text: string } | null>(null);

  // 处理防护等级变更
  const handleProtectionLevelChange = (level: 0 | 1 | 2 | 3 | 4) => {
    // 更新安全模块配置
    SecurityModule.updateConfig({ protectionLevel: level });
    
    // 更新状态
    setConfig(SecurityModule.getConfig());
    
    // 显示保存消息
    setSaveMessage({
      type: 'success',
      text: `防护等级已更新为: ${getProtectionLevelName(level)}`
    });
    
    // 3秒后清除消息
    setTimeout(() => {
      setSaveMessage(null);
    }, 3000);
  };
  
  // 处理配置变更
  const handleConfigChange = (section: keyof SecurityConfig, key: string, value: any) => {
    // 创建新配置
    const newConfig = { ...config };
    (newConfig[section] as any)[key] = value;
    
    // 更新安全模块配置
    SecurityModule.updateConfig(newConfig);
    
    // 更新状态
    setConfig(SecurityModule.getConfig());
  };
  
  // 保存配置
  const saveConfig = async () => {
    setIsSaving(true);
    
    try {
      // 模拟 API 请求
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      // 显示成功消息
      setSaveMessage({
        type: 'success',
        text: '配置已保存'
      });
      
      // 3秒后清除消息
      setTimeout(() => {
        setSaveMessage(null);
      }, 3000);
    } catch (error) {
      // 显示错误消息
      setSaveMessage({
        type: 'error',
        text: '保存失败，请重试'
      });
    } finally {
      setIsSaving(false);
    }
  };
  
  // 获取防护等级名称
  const getProtectionLevelName = (level: number): string => {
    switch (level) {
      case 0: return '关闭';
      case 1: return '基础';
      case 2: return '标准';
      case 3: return '增强';
      case 4: return '最高';
      default: return '未知';
    }
  };
  
  return (
    <div className="security-settings">
      <div className="header">
        <h1>安全设置</h1>
        
        <button 
          className="save-button"
          onClick={saveConfig}
          disabled={isSaving}
        >
          {isSaving ? '保存中...' : '保存设置'}
        </button>
      </div>
      
      {saveMessage && (
        <div className={`message ${saveMessage.type}`}>
          {saveMessage.text}
        </div>
      )}
      
      <div className="security-status">
        <div className="security-status-icon">
          <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="#10b981" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
            <path d="M12 22s8-4 8-10V5l-8-3-8 3v7c0 6 8 10 8 10z"></path>
            <path d="M9 12l2 2 4-4"></path>
          </svg>
        </div>
        <div className="security-status-text">
          <h3>安全状态良好</h3>
          <p>当前使用{getProtectionLevelName(config.protectionLevel)}防护等级</p>
        </div>
      </div>
      
      <div className="card">
        <h2>防护等级</h2>
        <p>选择适合您需求的防护等级。更高的防护等级提供更强的安全性，但可能影响用户体验。</p>
        
        <div className="protection-levels">
          <button
            className={`level-button ${config.protectionLevel === 0 ? 'active' : ''}`}
            onClick={() => handleProtectionLevelChange(0)}
          >
            关闭
          </button>
          
          <button
            className={`level-button ${config.protectionLevel === 1 ? 'active' : ''}`}
            onClick={() => handleProtectionLevelChange(1)}
          >
            基础
          </button>
          
          <button
            className={`level-button ${config.protectionLevel === 2 ? 'active' : ''}`}
            onClick={() => handleProtectionLevelChange(2)}
          >
            标准
          </button>
          
          <button
            className={`level-button ${config.protectionLevel === 3 ? 'active' : ''}`}
            onClick={() => handleProtectionLevelChange(3)}
          >
            增强
          </button>
          
          <button
            className={`level-button ${config.protectionLevel === 4 ? 'active' : ''}`}
            onClick={() => handleProtectionLevelChange(4)}
          >
            最高
          </button>
        </div>
      </div>
      
      <div className="card">
        <h2>验证码设置</h2>
        
        <div className="form-group">
          <label>
            <input
              type="checkbox"
              checked={config.captcha.enabled}
              onChange={(e) => handleConfigChange('captcha', 'enabled', e.target.checked)}
            />
            启用验证码
          </label>
        </div>
        
        {config.captcha.enabled && (
          <>
            <div className="form-group">
              <label>验证码类型</label>
              <select
                value={config.captcha.type}
                onChange={(e) => handleConfigChange('captcha', 'type', e.target.value)}
              >
                <option value="turnstile">Cloudflare Turnstile</option>
                <option value="custom">自定义验证码</option>
              </select>
            </div>
            
            <div className="form-group">
              <label>触发阈值</label>
              <input
                type="number"
                value={config.captcha.triggerThreshold}
                onChange={(e) => handleConfigChange('captcha', 'triggerThreshold', parseInt(e.target.value))}
                min="0"
                max="10"
              />
              <small>设置为 0 表示每次提交都显示验证码</small>
            </div>
            
            <div className="form-group">
              <label>站点密钥</label>
              <input
                type="text"
                value={config.captcha.siteKey || ''}
                onChange={(e) => handleConfigChange('captcha', 'siteKey', e.target.value)}
              />
              <small>从 Cloudflare Turnstile 获取的站点密钥</small>
            </div>
          </>
        )}
      </div>
      
      <div className="card">
        <h2>行为分析设置</h2>
        
        <div className="form-group">
          <label>
            <input
              type="checkbox"
              checked={config.behaviorAnalysis.enabled}
              onChange={(e) => handleConfigChange('behaviorAnalysis', 'enabled', e.target.checked)}
            />
            启用行为分析
          </label>
        </div>
        
        {config.behaviorAnalysis.enabled && (
          <>
            <div className="form-group">
              <label>最短完成时间（秒）</label>
              <input
                type="number"
                value={config.behaviorAnalysis.minCompletionTime / 1000}
                onChange={(e) => handleConfigChange('behaviorAnalysis', 'minCompletionTime', parseInt(e.target.value) * 1000)}
                min="0"
              />
              <small>完成问卷所需的最短时间，低于此时间的提交将被标记为可疑</small>
            </div>
            
            <div className="form-group">
              <label>
                <input
                  type="checkbox"
                  checked={config.behaviorAnalysis.trackMouseMovements}
                  onChange={(e) => handleConfigChange('behaviorAnalysis', 'trackMouseMovements', e.target.checked)}
                />
                跟踪鼠标移动
              </label>
            </div>
            
            <div className="form-group">
              <label>
                <input
                  type="checkbox"
                  checked={config.behaviorAnalysis.trackKeyboardEvents}
                  onChange={(e) => handleConfigChange('behaviorAnalysis', 'trackKeyboardEvents', e.target.checked)}
                />
                跟踪键盘事件
              </label>
            </div>
            
            <div className="form-group">
              <label>可疑分数阈值</label>
              <input
                type="number"
                value={config.behaviorAnalysis.suspiciousScoreThreshold}
                onChange={(e) => handleConfigChange('behaviorAnalysis', 'suspiciousScoreThreshold', parseInt(e.target.value))}
                min="0"
                max="100"
              />
              <small>超过此阈值的提交将被拒绝</small>
            </div>
          </>
        )}
      </div>
      
      <div className="card">
        <h2>蜜罐设置</h2>
        
        <div className="form-group">
          <label>
            <input
              type="checkbox"
              checked={config.honeypot.enabled}
              onChange={(e) => handleConfigChange('honeypot', 'enabled', e.target.checked)}
            />
            启用蜜罐字段
          </label>
        </div>
        
        {config.honeypot.enabled && (
          <>
            <div className="form-group">
              <label>蜜罐字段数量</label>
              <input
                type="number"
                value={config.honeypot.fieldCount}
                onChange={(e) => handleConfigChange('honeypot', 'fieldCount', parseInt(e.target.value))}
                min="0"
                max="5"
              />
              <small>添加到表单中的隐藏字段数量</small>
            </div>
            
            <div className="form-group">
              <label>
                <input
                  type="checkbox"
                  checked={config.honeypot.silentRejection}
                  onChange={(e) => handleConfigChange('honeypot', 'silentRejection', e.target.checked)}
                />
                静默拒绝
              </label>
              <small>如果启用，填写了蜜罐字段的提交将被静默拒绝（返回成功但不保存数据）</small>
            </div>
          </>
        )}
      </div>
      
      <div className="card">
        <h2>指纹识别设置</h2>
        
        <div className="form-group">
          <label>
            <input
              type="checkbox"
              checked={config.fingerprinting.enabled}
              onChange={(e) => handleConfigChange('fingerprinting', 'enabled', e.target.checked)}
            />
            启用浏览器指纹识别
          </label>
        </div>
        
        {config.fingerprinting.enabled && (
          <div className="form-group">
            <label>存储时间（天）</label>
            <input
              type="number"
              value={config.fingerprinting.storageTime}
              onChange={(e) => handleConfigChange('fingerprinting', 'storageTime', parseInt(e.target.value))}
              min="1"
            />
            <small>指纹存储的时间</small>
          </div>
        )}
      </div>
    </div>
  );
};

export default SecuritySettings;
