/**
 * 规范化测试数据生成器 v2.0
 * 
 * 功能：
 * 1. 生成符合新数据结构的测试数据
 * 2. 模拟真实用户行为模式
 * 3. 确保数据的一致性和完整性
 * 4. 支持不同规模的数据生成
 */

import Database from 'better-sqlite3';
import { IDGenerator, DataNormalizer } from './migrate-to-v2.js';

// 测试数据模板
const TEST_DATA_TEMPLATES = {
  educationLevels: [
    { code: 'high_school', display: '高中', weight: 10 },
    { code: 'associate', display: '大专', weight: 15 },
    { code: 'undergraduate', display: '本科', weight: 50 },
    { code: 'master', display: '硕士', weight: 20 },
    { code: 'doctorate', display: '博士', weight: 5 }
  ],
  
  regions: [
    { code: 'tier1_cities', display: '北上广深', weight: 30 },
    { code: 'provincial_capitals', display: '省会城市', weight: 25 },
    { code: 'tier2_cities', display: '二线城市', weight: 25 },
    { code: 'tier3_cities', display: '三四线城市', weight: 15 },
    { code: 'county_town', display: '县城或乡镇', weight: 5 }
  ],
  
  industries: [
    { code: 'technology', display: '互联网/IT', weight: 35 },
    { code: 'finance', display: '金融', weight: 15 },
    { code: 'education', display: '教育', weight: 10 },
    { code: 'healthcare', display: '医疗', weight: 8 },
    { code: 'manufacturing', display: '制造业', weight: 12 },
    { code: 'service', display: '服务业', weight: 10 },
    { code: 'government', display: '政府/公共部门', weight: 5 },
    { code: 'other', display: '其他', weight: 5 }
  ],
  
  majors: [
    '计算机科学与技术', '软件工程', '电子信息工程', '通信工程',
    '机械工程', '电气工程', '土木工程', '化学工程',
    '经济学', '金融学', '会计学', '市场营销',
    '汉语言文学', '英语', '新闻学', '法学',
    '临床医学', '护理学', '药学', '生物医学工程',
    '教育学', '心理学', '数学与应用数学', '物理学'
  ],
  
  employmentStatus: [
    { code: 'employed', display: '已就业', weight: 60 },
    { code: 'unemployed', display: '待就业', weight: 25 },
    { code: 'studying', display: '继续深造', weight: 10 },
    { code: 'entrepreneurship', display: '创业', weight: 5 }
  ],
  
  salaryRanges: [
    '3000以下', '3000-5000', '5000-8000', '8000-12000',
    '12000-20000', '20000-30000', '30000以上'
  ],
  
  adviceTemplates: [
    '作为一名{education}毕业生，我建议学弟学妹们要注重实践能力的培养，多参加实习和项目经验。',
    '在{industry}行业工作了几年，我觉得最重要的是保持学习的心态，技术更新很快。',
    '找工作时一定要做好充分的准备，包括简历优化、面试技巧和专业知识的复习。',
    '不要只看重薪资，公司的发展前景和学习机会同样重要。',
    '建议多关注行业动态，了解市场需求，这样能更好地规划自己的职业发展。',
    '实习经验非常重要，能让你提前了解职场环境和工作要求。',
    '保持良好的心态，求职过程可能会有挫折，但坚持下去一定会有收获。'
  ],
  
  observationTemplates: [
    '当前{industry}行业的就业形势总体{trend}，特别是{skill}技能的需求在增长。',
    '从{region}的就业市场来看，{education}学历的竞争比较激烈，需要有突出的专业能力。',
    '疫情对就业市场产生了一定影响，但{industry}行业相对稳定，远程工作机会增加。',
    '现在企业更看重候选人的综合素质，不仅仅是专业技能，沟通能力和团队协作也很重要。',
    '新兴技术领域如人工智能、大数据等提供了很多就业机会，建议关注这些方向。',
    '一线城市机会多但竞争激烈，二三线城市的发展机会也不错，生活成本相对较低。'
  ],
  
  storyTitles: [
    '从迷茫到清晰：我的职业规划之路',
    '三次面试失败后，我终于找到了理想工作',
    '转行的那些事：从{oldIndustry}到{newIndustry}',
    '实习经历如何改变了我的职业观',
    '在{city}打拼的这些年',
    '毕业即失业？我是如何走出困境的',
    '选择大厂还是创业公司？我的思考',
    '工作两年后的反思与成长'
  ],
  
  storyContents: [
    '刚毕业的时候，我对未来充满了不确定性。经过了几个月的求职经历，我逐渐明确了自己的职业方向...',
    '记得第一次面试时的紧张和兴奋，虽然没有成功，但每次面试都是一次宝贵的学习机会...',
    '在{industry}行业工作了{years}年，我积累了丰富的经验，也遇到了很多挑战...',
    '实习期间，我不仅学到了专业技能，更重要的是了解了职场文化和工作方式...',
    '从{education}毕业后，我选择了{city}作为我职业生涯的起点，这里的机会和挑战并存...'
  ]
};

// 权重随机选择器
class WeightedRandomSelector {
  static select(items) {
    const totalWeight = items.reduce((sum, item) => sum + (item.weight || 1), 0);
    let random = Math.random() * totalWeight;
    
    for (const item of items) {
      random -= (item.weight || 1);
      if (random <= 0) {
        return item;
      }
    }
    
    return items[items.length - 1];
  }
}

// 测试数据生成器
class TestDataGenerator {
  constructor(dbPath) {
    this.db = new Database(dbPath);
    this.db.pragma('journal_mode = WAL');
    this.generatedUsers = new Map();
    this.generatedSessions = new Set();
  }
  
  // 生成随机日期
  generateRandomDate(daysAgo = 365) {
    const now = new Date();
    const randomDays = Math.floor(Math.random() * daysAgo);
    const date = new Date(now.getTime() - randomDays * 24 * 60 * 60 * 1000);
    return date.toISOString();
  }
  
  // 生成用户
  generateUser(type = 'anonymous') {
    const userId = IDGenerator.generateUserID(type === 'registered' ? 'user' : 'anon');
    const sessionId = IDGenerator.generateSystemID('sess');
    
    const user = {
      id: userId,
      user_type: type,
      email: type === 'registered' ? `user${Date.now()}@example.com` : null,
      display_name: type === 'registered' ? `用户${Math.floor(Math.random() * 10000)}` : null,
      session_id: sessionId,
      created_at: this.generateRandomDate(180)
    };
    
    this.generatedUsers.set(userId, user);
    this.generatedSessions.add(sessionId);
    
    return user;
  }
  
  // 生成问卷回复
  generateQuestionnaireResponse(user) {
    const education = WeightedRandomSelector.select(TEST_DATA_TEMPLATES.educationLevels);
    const region = WeightedRandomSelector.select(TEST_DATA_TEMPLATES.regions);
    const industry = WeightedRandomSelector.select(TEST_DATA_TEMPLATES.industries);
    const employment = WeightedRandomSelector.select(TEST_DATA_TEMPLATES.employmentStatus);
    
    const major = TEST_DATA_TEMPLATES.majors[Math.floor(Math.random() * TEST_DATA_TEMPLATES.majors.length)];
    const graduationYear = 2020 + Math.floor(Math.random() * 5); // 2020-2024
    const salaryRange = TEST_DATA_TEMPLATES.salaryRanges[Math.floor(Math.random() * TEST_DATA_TEMPLATES.salaryRanges.length)];
    
    // 生成建议内容
    const adviceTemplate = TEST_DATA_TEMPLATES.adviceTemplates[Math.floor(Math.random() * TEST_DATA_TEMPLATES.adviceTemplates.length)];
    const adviceContent = adviceTemplate
      .replace('{education}', education.display)
      .replace('{industry}', industry.display);
    
    // 生成观察内容
    const observationTemplate = TEST_DATA_TEMPLATES.observationTemplates[Math.floor(Math.random() * TEST_DATA_TEMPLATES.observationTemplates.length)];
    const observationContent = observationTemplate
      .replace('{industry}', industry.display)
      .replace('{region}', region.display)
      .replace('{education}', education.display)
      .replace('{trend}', Math.random() > 0.5 ? '向好' : '稳定')
      .replace('{skill}', ['技术', '沟通', '管理', '创新'][Math.floor(Math.random() * 4)]);
    
    return {
      id: IDGenerator.generateContentID('quest'),
      user_id: user.id,
      session_id: user.session_id,
      education_level: education.code,
      education_level_display: education.display,
      major_category: major,
      major_display: major,
      graduation_year: graduationYear,
      region_code: region.code,
      region_display: region.display,
      employment_status: employment.code,
      current_industry_code: industry.code,
      current_industry_display: industry.display,
      salary_range: salaryRange,
      job_search_duration: ['1个月内', '1-3个月', '3-6个月', '6个月以上'][Math.floor(Math.random() * 4)],
      interview_count: Math.floor(Math.random() * 20),
      offer_count: Math.floor(Math.random() * 5),
      advice_content: Math.random() > 0.3 ? adviceContent : null,
      observation_content: Math.random() > 0.4 ? observationContent : null,
      ip_address: `192.168.1.${Math.floor(Math.random() * 255)}`,
      user_agent: 'Mozilla/5.0 (Test Data Generator)',
      created_at: this.generateRandomDate(90)
    };
  }
  
  // 生成故事内容
  generateStoryContent(user) {
    const education = WeightedRandomSelector.select(TEST_DATA_TEMPLATES.educationLevels);
    const industry = WeightedRandomSelector.select(TEST_DATA_TEMPLATES.industries);
    
    const titleTemplate = TEST_DATA_TEMPLATES.storyTitles[Math.floor(Math.random() * TEST_DATA_TEMPLATES.storyTitles.length)];
    const title = titleTemplate
      .replace('{oldIndustry}', '传统制造业')
      .replace('{newIndustry}', industry.display)
      .replace('{city}', ['北京', '上海', '深圳', '杭州'][Math.floor(Math.random() * 4)]);
    
    const contentTemplate = TEST_DATA_TEMPLATES.storyContents[Math.floor(Math.random() * TEST_DATA_TEMPLATES.storyContents.length)];
    const content = contentTemplate
      .replace('{industry}', industry.display)
      .replace('{years}', Math.floor(Math.random() * 5) + 1)
      .replace('{education}', education.display)
      .replace('{city}', ['北京', '上海', '深圳', '杭州'][Math.floor(Math.random() * 4)]);
    
    return {
      id: IDGenerator.generateContentID('story'),
      user_id: user.id,
      title: title,
      content: content + '...(内容继续)',
      summary: content.substring(0, 100) + '...',
      category: ['求职经验', '职场感悟', '行业观察', '技能分享'][Math.floor(Math.random() * 4)],
      education_level: education.code,
      education_level_display: education.display,
      industry_code: industry.code,
      industry_display: industry.display,
      story_type: 'experience',
      is_anonymous: Math.random() > 0.3,
      status: 'approved',
      likes: Math.floor(Math.random() * 50),
      dislikes: Math.floor(Math.random() * 5),
      views: Math.floor(Math.random() * 200) + 50,
      quality_score: Math.random() * 5,
      word_count: Math.floor(Math.random() * 1000) + 200,
      reading_time: Math.floor(Math.random() * 10) + 2,
      created_at: this.generateRandomDate(120)
    };
  }
  
  // 生成完整的测试数据集
  async generateTestDataSet(config = {}) {
    const {
      userCount = 100,
      questionnaireRatio = 0.8,
      storyRatio = 0.3,
      registeredUserRatio = 0.2
    } = config;
    
    console.log('开始生成测试数据...');
    console.log(`计划生成: ${userCount} 个用户`);
    
    // 准备插入语句
    const insertUser = this.db.prepare(`
      INSERT INTO users_v2 (id, user_type, email, display_name, session_id, created_at)
      VALUES (?, ?, ?, ?, ?, ?)
    `);
    
    const insertQuestionnaire = this.db.prepare(`
      INSERT INTO questionnaire_responses_v2 (
        id, user_id, session_id, education_level, education_level_display,
        major_category, major_display, graduation_year, region_code, region_display,
        employment_status, current_industry_code, current_industry_display,
        salary_range, job_search_duration, interview_count, offer_count,
        advice_content, observation_content, ip_address, user_agent, created_at
      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
    `);
    
    const insertStory = this.db.prepare(`
      INSERT INTO story_contents_v2 (
        id, user_id, title, content, summary, category,
        education_level, education_level_display, industry_code, industry_display,
        story_type, is_anonymous, status, likes, dislikes, views,
        quality_score, word_count, reading_time, created_at
      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
    `);
    
    const insertVoice = this.db.prepare(`
      INSERT INTO questionnaire_voices_v2 (
        id, source_response_id, user_id, voice_type, title, content,
        education_level, education_level_display, region_code, region_display,
        status, likes, views, created_at
      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
    `);
    
    // 开始事务
    const transaction = this.db.transaction(() => {
      let questionnaireCount = 0;
      let storyCount = 0;
      let voiceCount = 0;
      
      for (let i = 0; i < userCount; i++) {
        // 生成用户
        const userType = Math.random() < registeredUserRatio ? 'registered' : 'anonymous';
        const user = this.generateUser(userType);
        
        insertUser.run(
          user.id,
          user.user_type,
          user.email,
          user.display_name,
          user.session_id,
          user.created_at
        );
        
        // 生成问卷回复
        if (Math.random() < questionnaireRatio) {
          const questionnaire = this.generateQuestionnaireResponse(user);
          
          insertQuestionnaire.run(
            questionnaire.id,
            questionnaire.user_id,
            questionnaire.session_id,
            questionnaire.education_level,
            questionnaire.education_level_display,
            questionnaire.major_category,
            questionnaire.major_display,
            questionnaire.graduation_year,
            questionnaire.region_code,
            questionnaire.region_display,
            questionnaire.employment_status,
            questionnaire.current_industry_code,
            questionnaire.current_industry_display,
            questionnaire.salary_range,
            questionnaire.job_search_duration,
            questionnaire.interview_count,
            questionnaire.offer_count,
            questionnaire.advice_content,
            questionnaire.observation_content,
            questionnaire.ip_address,
            questionnaire.user_agent,
            questionnaire.created_at
          );
          
          questionnaireCount++;
          
          // 生成问卷心声
          if (questionnaire.advice_content) {
            const voiceId = IDGenerator.generateContentID('voice');
            insertVoice.run(
              voiceId,
              questionnaire.id,
              questionnaire.user_id,
              'advice',
              '给高三学子的建议',
              questionnaire.advice_content,
              questionnaire.education_level,
              questionnaire.education_level_display,
              questionnaire.region_code,
              questionnaire.region_display,
              'approved',
              Math.floor(Math.random() * 20),
              Math.floor(Math.random() * 100),
              questionnaire.created_at
            );
            voiceCount++;
          }
          
          if (questionnaire.observation_content) {
            const voiceId = IDGenerator.generateContentID('voice');
            insertVoice.run(
              voiceId,
              questionnaire.id,
              questionnaire.user_id,
              'observation',
              '对当前就业环境的观察',
              questionnaire.observation_content,
              questionnaire.education_level,
              questionnaire.education_level_display,
              questionnaire.region_code,
              questionnaire.region_display,
              'approved',
              Math.floor(Math.random() * 15),
              Math.floor(Math.random() * 80),
              questionnaire.created_at
            );
            voiceCount++;
          }
        }
        
        // 生成故事内容
        if (Math.random() < storyRatio) {
          const story = this.generateStoryContent(user);
          
          insertStory.run(
            story.id,
            story.user_id,
            story.title,
            story.content,
            story.summary,
            story.category,
            story.education_level,
            story.education_level_display,
            story.industry_code,
            story.industry_display,
            story.story_type,
            story.is_anonymous,
            story.status,
            story.likes,
            story.dislikes,
            story.views,
            story.quality_score,
            story.word_count,
            story.reading_time,
            story.created_at
          );
          
          storyCount++;
        }
        
        if ((i + 1) % 10 === 0) {
          console.log(`已生成 ${i + 1}/${userCount} 个用户...`);
        }
      }
      
      console.log(`\n生成完成:`);
      console.log(`- 用户: ${userCount}`);
      console.log(`- 问卷回复: ${questionnaireCount}`);
      console.log(`- 问卷心声: ${voiceCount}`);
      console.log(`- 故事内容: ${storyCount}`);
    });
    
    transaction();
  }
  
  close() {
    this.db.close();
  }
}

// 主执行函数
async function main() {
  const dbPath = process.argv[2] || './database.db';
  const userCount = parseInt(process.argv[3]) || 50;
  
  console.log(`开始为数据库生成测试数据: ${dbPath}`);
  console.log(`用户数量: ${userCount}`);
  
  const generator = new TestDataGenerator(dbPath);
  
  try {
    await generator.generateTestDataSet({
      userCount,
      questionnaireRatio: 0.8,
      storyRatio: 0.3,
      registeredUserRatio: 0.2
    });
    
    console.log('测试数据生成完成！');
  } catch (error) {
    console.error('生成测试数据失败:', error);
    process.exit(1);
  } finally {
    generator.close();
  }
}

// 如果直接运行此脚本
if (import.meta.url === `file://${process.argv[1]}`) {
  main();
}

export { TestDataGenerator };
