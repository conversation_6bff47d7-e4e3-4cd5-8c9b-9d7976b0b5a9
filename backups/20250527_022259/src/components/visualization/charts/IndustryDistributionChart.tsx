import { BarChart, Bar, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer } from 'recharts';

interface IndustryDistributionChartProps {
  data: Array<{ name: string; count: number }>;
}

export default function IndustryDistributionChart({ data }: IndustryDistributionChartProps) {
  // If no data, show placeholder
  if (!data || data.length === 0) {
    return (
      <div className="h-full flex items-center justify-center">
        <p className="text-gray-500">暂无数据</p>
      </div>
    );
  }
  
  // Sort data by count in descending order
  const sortedData = [...data].sort((a, b) => b.count - a.count);
  
  // Take top 10 industries
  const chartData = sortedData.slice(0, 10).map(item => ({
    name: item.name,
    count: item.count,
  }));
  
  // Custom tooltip
  const CustomTooltip = ({ active, payload, label }: any) => {
    if (active && payload && payload.length) {
      return (
        <div className="bg-white p-2 border rounded shadow-sm">
          <p className="font-medium">{`${label}`}</p>
          <p className="text-sm">{`人数: ${payload[0].value}`}</p>
          <p className="text-sm">{`占比: ${((payload[0].value / data.reduce((sum, item) => sum + item.count, 0)) * 100).toFixed(1)}%`}</p>
        </div>
      );
    }
    return null;
  };
  
  return (
    <ResponsiveContainer width="100%" height="100%">
      <BarChart
        data={chartData}
        layout="vertical"
        margin={{ top: 5, right: 30, left: 80, bottom: 5 }}
      >
        <CartesianGrid strokeDasharray="3 3" horizontal={false} />
        <XAxis type="number" />
        <YAxis 
          type="category" 
          dataKey="name" 
          tick={{ fontSize: 12 }}
          width={80}
        />
        <Tooltip content={<CustomTooltip />} />
        <Bar dataKey="count" fill="#8884d8" barSize={20} />
      </BarChart>
    </ResponsiveContainer>
  );
}
