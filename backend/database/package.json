{"name": "database-optimization-v2", "version": "2.0.0", "description": "数据库结构优化和测试数据生成工具", "type": "module", "main": "init-database-v2.js", "scripts": {"init": "node init-database-v2.js", "migrate": "node migrate-to-v2.js", "generate-data": "node generate-test-data-v2.js", "test": "node --test"}, "dependencies": {"better-sqlite3": "^8.7.0"}, "devDependencies": {}, "keywords": ["database", "sqlite", "migration", "test-data"], "author": "Data Optimization Team", "license": "MIT"}