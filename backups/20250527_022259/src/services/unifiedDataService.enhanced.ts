/**
 * 增强版统一数据服务
 *
 * 支持模拟数据和真实API之间的智能切换
 * 提供数据缓存、错误处理、重试机制等功能
 */

import { ApiResponse, QuestionnaireResponse, Story, User } from '../types/api';
// import { mockDataService } from './mockDataService'; // 已删除模拟数据服务
import { apiService } from './apiService';

// 环境配置
const USE_MOCK = import.meta.env.VITE_USE_MOCK === 'true';
const API_BASE_URL = import.meta.env.VITE_API_BASE_URL || '/api';
const ENABLE_LOCAL_DB = import.meta.env.VITE_ENABLE_LOCAL_DB === 'true';

// 缓存配置
const CACHE_DURATION = 5 * 60 * 1000; // 5分钟
const cache = new Map<string, { data: any; timestamp: number }>();

// 请求去重
const pendingRequests = new Map<string, Promise<any>>();

/**
 * 缓存管理
 */
class CacheManager {
  static get<T>(key: string): T | null {
    const cached = cache.get(key);
    if (!cached) return null;

    if (Date.now() - cached.timestamp > CACHE_DURATION) {
      cache.delete(key);
      return null;
    }

    return cached.data;
  }

  static set<T>(key: string, data: T): void {
    cache.set(key, {
      data,
      timestamp: Date.now()
    });
  }

  static clear(): void {
    cache.clear();
  }

  static clearPattern(pattern: string): void {
    for (const key of cache.keys()) {
      if (key.includes(pattern)) {
        cache.delete(key);
      }
    }
  }
}

/**
 * 请求去重管理
 */
class RequestDeduplicator {
  static async execute<T>(key: string, requestFn: () => Promise<T>): Promise<T> {
    // 如果已有相同请求在进行中，返回该请求的Promise
    if (pendingRequests.has(key)) {
      return pendingRequests.get(key)!;
    }

    // 创建新请求
    const promise = requestFn().finally(() => {
      pendingRequests.delete(key);
    });

    pendingRequests.set(key, promise);
    return promise;
  }
}

/**
 * 错误处理和重试
 */
class ErrorHandler {
  static async withRetry<T>(
    operation: () => Promise<T>,
    maxRetries: number = 3,
    delay: number = 1000
  ): Promise<T> {
    let lastError: Error;

    for (let attempt = 1; attempt <= maxRetries; attempt++) {
      try {
        return await operation();
      } catch (error) {
        lastError = error as Error;
        console.warn(`请求失败 (尝试 ${attempt}/${maxRetries}):`, error);

        if (attempt < maxRetries) {
          await new Promise(resolve => setTimeout(resolve, delay * attempt));
        }
      }
    }

    throw lastError!;
  }

  static handleApiError(error: any): never {
    if (error.response?.status === 401) {
      // 未授权，可能需要重新登录
      window.location.href = '/login';
      throw new Error('登录已过期，请重新登录');
    } else if (error.response?.status === 403) {
      throw new Error('权限不足');
    } else if (error.response?.status === 404) {
      throw new Error('请求的资源不存在');
    } else if (error.response?.status >= 500) {
      throw new Error('服务器内部错误，请稍后重试');
    } else {
      throw new Error(error.message || '请求失败');
    }
  }
}

/**
 * 增强版统一数据服务
 */
export class UnifiedDataServiceEnhanced {

  /**
   * 获取数据源标识
   */
  static getDataSource(): 'mock' | 'api' {
    return USE_MOCK ? 'mock' : 'api';
  }

  /**
   * 检查API连接状态
   */
  static async checkApiConnection(): Promise<boolean> {
    if (USE_MOCK) return true;

    try {
      const response = await fetch(`${API_BASE_URL}/health`, {
        method: 'GET',
        timeout: 5000
      } as any);
      return response.ok;
    } catch {
      return false;
    }
  }

  /**
   * 智能数据获取 - 使用真实API
   */
  private static async smartFetch<T>(
    cacheKey: string,
    apiCall: () => Promise<T>
  ): Promise<T> {
    // 检查缓存
    const cached = CacheManager.get<T>(cacheKey);
    if (cached) {
      console.log(`📦 从缓存获取数据: ${cacheKey}`);
      return cached;
    }

    // 请求去重
    return RequestDeduplicator.execute(cacheKey, async () => {
      console.log(`🌐 调用真实API: ${cacheKey}`);
      const result = await ErrorHandler.withRetry(apiCall);
      CacheManager.set(cacheKey, result);
      return result;
    });
  }

  // ==================== 问卷相关接口 ====================

  /**
   * 提交问卷回复
   */
  static async submitQuestionnaire(data: Partial<QuestionnaireResponse>): Promise<ApiResponse<QuestionnaireResponse>> {
    const cacheKey = `submit_questionnaire_${Date.now()}`;

    return this.smartFetch(
      cacheKey,
      () => apiService.submitQuestionnaire(data)
    );
  }

  /**
   * 获取问卷统计数据
   */
  static async getQuestionnaireStats(): Promise<any> {
    const cacheKey = 'questionnaire_stats';

    return this.smartFetch(
      cacheKey,
      () => apiService.getQuestionnaireStats()
    );
  }

  /**
   * 获取问卷回复列表
   */
  static async getQuestionnaires(params?: {
    page?: number;
    pageSize?: number;
    educationLevel?: string;
    employmentStatus?: string;
    region?: string;
    isAnonymous?: boolean;
  }): Promise<ApiResponse<QuestionnaireResponse[]>> {
    const cacheKey = `questionnaires_${JSON.stringify(params || {})}`;

    return this.smartFetch(
      cacheKey,
      () => apiService.getQuestionnaires(params)
    );
  }

  /**
   * 获取问卷回复详情
   */
  static async getQuestionnaireById(id: number): Promise<ApiResponse<QuestionnaireResponse>> {
    const cacheKey = `questionnaire_${id}`;

    return this.smartFetch(
      cacheKey,
      () => apiService.getQuestionnaireById(id)
    );
  }

  // ==================== 故事相关接口 ====================

  /**
   * 提交故事
   */
  static async submitStory(data: Partial<Story>): Promise<ApiResponse<Story>> {
    const cacheKey = `submit_story_${Date.now()}`;

    // 清除相关缓存
    CacheManager.clearPattern('stories');

    return this.smartFetch(
      cacheKey,
      () => apiService.submitStory(data)
    );
  }

  /**
   * 获取故事列表
   */
  static async getStories(params?: {
    page?: number;
    pageSize?: number;
    category?: string;
    educationLevel?: string;
    industry?: string;
    sortBy?: 'latest' | 'popular';
  }): Promise<ApiResponse<Story[]>> {
    const cacheKey = `stories_${JSON.stringify(params || {})}`;

    return this.smartFetch(
      cacheKey,
      () => apiService.getStories(params)
    );
  }

  /**
   * 获取故事详情
   */
  static async getStoryById(id: number): Promise<ApiResponse<Story>> {
    const cacheKey = `story_${id}`;

    return this.smartFetch(
      cacheKey,
      () => apiService.getStoryById(id)
    );
  }

  /**
   * 故事投票
   */
  static async voteStory(id: number, type: 'like' | 'dislike'): Promise<ApiResponse<{ likes: number; dislikes: number }>> {
    const cacheKey = `vote_story_${id}_${type}_${Date.now()}`;

    // 清除相关缓存
    CacheManager.clearPattern(`story_${id}`);
    CacheManager.clearPattern('stories');

    return this.smartFetch(
      cacheKey,
      () => apiService.voteStory(id, type)
    );
  }

  // ==================== 用户相关接口 ====================

  /**
   * 获取用户列表
   */
  static async getUsers(params?: {
    page?: number;
    pageSize?: number;
    role?: string;
    search?: string;
  }): Promise<ApiResponse<User[]>> {
    const cacheKey = `users_${JSON.stringify(params || {})}`;

    return this.smartFetch(
      cacheKey,
      () => apiService.getUsers(params)
    );
  }

  // ==================== 可视化数据接口 ====================

  /**
   * 获取可视化数据
   */
  static async getVisualizationData(): Promise<any> {
    const cacheKey = 'visualization_data';

    return this.smartFetch(
      cacheKey,
      () => apiService.getVisualizationData()
    );
  }

  // ==================== 工具方法 ====================

  /**
   * 清除所有缓存
   */
  static clearCache(): void {
    CacheManager.clear();
    console.log('🗑️ 已清除所有缓存');
  }

  /**
   * 获取缓存状态
   */
  static getCacheStatus(): { size: number; keys: string[] } {
    return {
      size: cache.size,
      keys: Array.from(cache.keys())
    };
  }

  /**
   * 预加载数据
   */
  static async preloadData(): Promise<void> {
    console.log('🚀 开始预加载数据...');

    try {
      await Promise.all([
        this.getQuestionnaireStats(),
        this.getStories({ page: 1, pageSize: 10 }),
        this.getVisualizationData()
      ]);
      console.log('✅ 数据预加载完成');
    } catch (error) {
      console.warn('⚠️ 数据预加载部分失败:', error);
    }
  }
}

export default UnifiedDataServiceEnhanced;
