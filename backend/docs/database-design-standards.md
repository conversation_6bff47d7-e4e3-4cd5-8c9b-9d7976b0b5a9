# 数据库设计规范 v1.0

## 🎯 核心设计原则

### 1. **兼容性优先**
- **向前兼容**: 新版本必须兼容旧数据
- **向后兼容**: 支持功能降级和回滚
- **版本管理**: 所有结构变更都有版本标识

### 2. **性能导向**
- **分层存储**: KV(热) → D1(核心) → R2(冷)
- **查询优化**: 基于使用频率设计索引
- **缓存策略**: 标准化的TTL和更新机制

### 3. **扩展性设计**
- **预留字段**: 每个表预留扩展空间
- **JSON扩展**: 灵活的元数据存储
- **模块化**: 功能模块独立数据设计

## 🏗️ 数据分层标准

### 📊 **KV 存储标准**

#### 命名规范
```typescript
// 格式: {category}:{type}:{identifier}:{suffix?}
// 示例:
session:user:user_123abc:main          // 用户会话
cache:stats:question:qitem_456def      // 统计缓存
config:system:moderation:rules         // 系统配置
temp:upload:file_789ghi:metadata       // 临时数据
```

#### TTL 策略
```typescript
interface TTLStandards {
  // 用户会话类
  userSession: 86400,        // 24小时
  tempToken: 3600,           // 1小时
  
  // 缓存类
  hotStats: 1800,            // 30分钟
  warmStats: 3600,           // 1小时
  coldStats: 7200,           // 2小时
  
  // 临时数据类
  uploadTemp: 900,           // 15分钟
  verificationCode: 600,     // 10分钟
  
  // 配置类
  systemConfig: null,        // 永久
  featureFlags: 86400        // 24小时
}
```

#### 数据结构标准
```typescript
// 基础结构模板
interface KVDataTemplate {
  // 必需字段
  id: string;                // 唯一标识
  type: string;              // 数据类型
  version: string;           // 数据版本
  createdAt: number;         // 创建时间戳
  
  // 核心数据
  data: any;                 // 主要数据内容
  
  // 扩展字段
  metadata?: {               // 元数据
    source?: string;         // 数据来源
    tags?: string[];         // 标签
    priority?: number;       // 优先级
    [key: string]: any;      // 其他扩展
  };
  
  // 审计字段
  audit?: {
    lastModified?: number;   // 最后修改时间
    modifiedBy?: string;     // 修改者
    changeLog?: string[];    // 变更日志
  };
}
```

### 🗄️ **D1 数据库标准**

#### 表命名规范
```sql
-- 格式: {module}_{entity}_{suffix?}
-- 示例:
user_profiles                    -- 用户模块-档案表
user_sessions                    -- 用户模块-会话表
content_metadata                 -- 内容模块-元数据表
content_reviews                  -- 内容模块-审核表
system_configs                   -- 系统模块-配置表
audit_operation_logs             -- 审计模块-操作日志表
```

#### 字段命名规范
```sql
-- 主键: id (TEXT PRIMARY KEY)
-- 外键: {table}_id
-- 时间: {action}_at (DATETIME)
-- 状态: {entity}_status
-- 标识: is_{condition} (BOOLEAN)
-- 计数: {entity}_count (INTEGER)
-- JSON: {entity}_data (TEXT)
```

#### 标准字段模板
```sql
-- 每个表的标准字段
CREATE TABLE template_table (
  -- 主键 (必需)
  id TEXT PRIMARY KEY,                    -- 使用前缀UUID
  
  -- 关联字段 (按需)
  user_id TEXT,                          -- 用户关联
  parent_id TEXT,                        -- 父级关联
  
  -- 核心业务字段
  -- ... 具体业务字段 ...
  
  -- 状态管理 (推荐)
  status TEXT DEFAULT 'active',          -- 状态字段
  is_deleted BOOLEAN DEFAULT false,      -- 软删除标识
  is_public BOOLEAN DEFAULT true,        -- 公开标识
  
  -- 扩展字段 (必需)
  metadata TEXT,                         -- JSON格式元数据
  tags TEXT,                             -- JSON格式标签
  extra_data TEXT,                       -- JSON格式扩展数据
  
  -- 版本管理 (推荐)
  version INTEGER DEFAULT 1,             -- 数据版本
  schema_version TEXT DEFAULT '1.0',     -- 结构版本
  
  -- 审计字段 (必需)
  created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
  updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
  created_by TEXT,                       -- 创建者ID
  updated_by TEXT,                       -- 更新者ID
  
  -- 测试数据标识 (必需)
  is_test_data BOOLEAN DEFAULT false,    -- 测试数据标识
  test_data_set TEXT,                    -- 测试数据集
  test_data_version TEXT                 -- 测试数据版本
);
```

#### 索引标准
```sql
-- 每个表的标准索引
CREATE INDEX idx_{table}_status ON {table}(status);
CREATE INDEX idx_{table}_created_at ON {table}(created_at);
CREATE INDEX idx_{table}_user_id ON {table}(user_id);
CREATE INDEX idx_{table}_is_deleted ON {table}(is_deleted);

-- 复合索引 (按查询模式)
CREATE INDEX idx_{table}_user_status ON {table}(user_id, status);
CREATE INDEX idx_{table}_status_created ON {table}(status, created_at);
```

### 📁 **R2 存储标准**

#### 路径命名规范
```typescript
// 格式: {category}/{type}/{year}/{month}/{id}.{ext}
// 示例:
content/story/2024/01/text_abc123.json           // 故事内容
content/voice/2024/01/voice_def456.json         // 心声内容
attachments/user_123/content_456/image.jpg      // 用户附件
backups/daily/2024-01-15/users.json            // 数据备份
temp/uploads/2024/01/15/temp_789.tmp           // 临时文件
```

#### 元数据标准
```typescript
interface R2MetadataTemplate {
  // HTTP元数据
  httpMetadata: {
    contentType: string;       // MIME类型
    contentLanguage?: string;  // 语言标识
    contentEncoding?: string;  // 编码格式
    cacheControl?: string;     // 缓存控制
  };
  
  // 自定义元数据
  customMetadata: {
    // 必需字段
    id: string;                // 内容ID
    type: string;              // 内容类型
    version: string;           // 版本号
    userId: string;            // 用户ID
    
    // 可选字段
    category?: string;         // 分类
    tags?: string;             // 标签 (JSON字符串)
    status?: string;           // 状态
    size?: string;             // 文件大小
    checksum?: string;         // 校验和
    
    // 审计字段
    createdAt: string;         // 创建时间
    createdBy: string;         // 创建者
    lastModified?: string;     // 最后修改时间
    modifiedBy?: string;       // 修改者
  };
}
```

## 🔧 功能扩展标准

### 1. **新功能添加流程**

#### 步骤1: 需求分析
```typescript
interface FeatureRequirement {
  name: string;                    // 功能名称
  module: string;                  // 所属模块
  dataRequirements: {
    newTables?: string[];          // 新增表
    modifiedTables?: string[];     // 修改表
    kvKeys?: string[];             // KV键
    r2Paths?: string[];            // R2路径
  };
  compatibility: {
    breakingChanges: boolean;      // 是否有破坏性变更
    migrationRequired: boolean;    // 是否需要数据迁移
    rollbackSupport: boolean;      // 是否支持回滚
  };
}
```

#### 步骤2: 数据设计
```sql
-- 新表设计模板
CREATE TABLE new_feature_table (
  -- 继承标准字段模板
  id TEXT PRIMARY KEY,
  
  -- 功能特定字段
  feature_specific_field TEXT,
  
  -- 必需的标准字段
  metadata TEXT,
  tags TEXT,
  extra_data TEXT,
  version INTEGER DEFAULT 1,
  schema_version TEXT DEFAULT '1.0',
  status TEXT DEFAULT 'active',
  is_deleted BOOLEAN DEFAULT false,
  created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
  updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
  is_test_data BOOLEAN DEFAULT false
);
```

#### 步骤3: 兼容性验证
```typescript
// 兼容性检查清单
interface CompatibilityChecklist {
  dataStructure: {
    existingTablesUnchanged: boolean;    // 现有表结构不变
    newFieldsOptional: boolean;          // 新字段为可选
    defaultValuesProvided: boolean;      // 提供默认值
  };
  
  apiCompatibility: {
    existingEndpointsWork: boolean;      // 现有API正常工作
    newFieldsOptional: boolean;          // 新字段为可选参数
    responseFormatConsistent: boolean;   // 响应格式一致
  };
  
  performanceImpact: {
    queryPerformanceAcceptable: boolean; // 查询性能可接受
    indexesOptimized: boolean;           // 索引已优化
    cacheStrategyDefined: boolean;       // 缓存策略已定义
  };
}
```

### 2. **版本管理标准**

#### 数据版本控制
```typescript
interface DataVersioning {
  // 结构版本
  schemaVersion: string;           // 如 "1.0", "1.1", "2.0"
  
  // 数据版本
  dataVersion: string;             // 如 "2024.01.15"
  
  // 兼容性矩阵
  compatibility: {
    minSupportedVersion: string;   // 最低支持版本
    maxSupportedVersion: string;   // 最高支持版本
    deprecatedVersions: string[];  // 已弃用版本
  };
  
  // 迁移路径
  migrationPath: {
    from: string;                  // 源版本
    to: string;                    // 目标版本
    script: string;                // 迁移脚本路径
    rollback: string;              // 回滚脚本路径
  }[];
}
```

#### API版本控制
```typescript
// API版本标准
interface APIVersioning {
  // 版本标识
  version: string;                 // 如 "v1", "v2"
  
  // 兼容性
  supportedVersions: string[];     // 支持的版本列表
  defaultVersion: string;          // 默认版本
  deprecationNotice?: {
    version: string;               // 弃用版本
    deprecatedAt: string;          // 弃用时间
    removeAt: string;              // 移除时间
    migrationGuide: string;        // 迁移指南
  };
}
```

### 3. **模块化设计标准**

#### 模块定义
```typescript
interface ModuleDefinition {
  name: string;                    // 模块名称
  version: string;                 // 模块版本
  dependencies: string[];          // 依赖模块
  
  dataStructure: {
    tables: string[];              // 相关表
    kvKeys: string[];              // KV键模式
    r2Paths: string[];             // R2路径模式
  };
  
  apis: {
    endpoints: string[];           // API端点
    permissions: string[];         // 所需权限
  };
  
  configuration: {
    required: Record<string, any>; // 必需配置
    optional: Record<string, any>; // 可选配置
  };
}
```

#### 模块间通信标准
```typescript
// 模块间数据交换格式
interface ModuleDataExchange {
  source: string;                  // 源模块
  target: string;                  // 目标模块
  dataType: string;                // 数据类型
  format: 'json' | 'event' | 'stream'; // 交换格式
  
  schema: {
    version: string;               // 数据格式版本
    structure: any;                // 数据结构定义
  };
  
  validation: {
    required: boolean;             // 是否必需验证
    rules: any[];                  // 验证规则
  };
}
```

## 📈 性能优化标准

### 1. **查询优化标准**
```sql
-- 查询性能标准
-- 单表查询: < 10ms
-- 关联查询: < 50ms
-- 聚合查询: < 100ms
-- 复杂分析: < 500ms

-- 索引设计原则
-- 1. 主键自动索引
-- 2. 外键必须索引
-- 3. 查询条件字段索引
-- 4. 排序字段索引
-- 5. 复合索引覆盖常用查询
```

### 2. **缓存策略标准**
```typescript
interface CacheStrategy {
  // 缓存层级
  l1: 'KV',                        // 热数据缓存
  l2: 'D1_prepared',               // 预编译查询
  l3: 'R2_metadata',               // 元数据缓存
  
  // TTL策略
  ttl: {
    hot: 1800,                     // 30分钟
    warm: 3600,                    // 1小时
    cold: 7200,                    // 2小时
  };
  
  // 更新策略
  updateStrategy: 'write-through' | 'write-behind' | 'cache-aside';
  
  // 失效策略
  invalidation: {
    manual: string[];              // 手动失效键
    automatic: string[];           // 自动失效键
    cascade: string[];             // 级联失效键
  };
}
```

## 🔒 安全和审计标准

### 1. **数据安全标准**
```typescript
interface DataSecurity {
  // 敏感数据处理
  sensitiveFields: {
    encryption: string[];          // 需要加密的字段
    hashing: string[];             // 需要哈希的字段
    masking: string[];             // 需要脱敏的字段
  };
  
  // 访问控制
  accessControl: {
    authentication: boolean;       // 是否需要认证
    authorization: string[];       // 所需权限
    rateLimit: number;             // 访问频率限制
  };
  
  // 审计要求
  auditRequirements: {
    logAccess: boolean;            // 记录访问日志
    logChanges: boolean;           // 记录变更日志
    retentionPeriod: number;       // 日志保留期
  };
}
```

### 2. **操作审计标准**
```sql
-- 审计日志表标准
CREATE TABLE audit_logs (
  id TEXT PRIMARY KEY,
  
  -- 操作信息
  operation_type TEXT NOT NULL,     -- 操作类型
  operation_target TEXT NOT NULL,   -- 操作目标
  operation_result TEXT NOT NULL,   -- 操作结果
  
  -- 用户信息
  user_id TEXT,                     -- 操作用户
  user_role TEXT,                   -- 用户角色
  session_id TEXT,                  -- 会话ID
  
  -- 环境信息
  ip_address TEXT,                  -- IP地址
  user_agent TEXT,                  -- 用户代理
  request_id TEXT,                  -- 请求ID
  
  -- 数据快照
  before_data TEXT,                 -- 操作前数据
  after_data TEXT,                  -- 操作后数据
  
  -- 标准字段
  created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
  metadata TEXT,
  is_test_data BOOLEAN DEFAULT false
);
```

## 📋 实施检查清单

### 新功能开发检查清单
- [ ] 数据设计符合命名规范
- [ ] 包含所有标准字段
- [ ] 索引设计已优化
- [ ] 兼容性测试通过
- [ ] 性能测试达标
- [ ] 安全审计完成
- [ ] 文档更新完整
- [ ] 迁移脚本准备
- [ ] 回滚方案确认
- [ ] 监控告警配置

### 数据库变更检查清单
- [ ] 变更影响评估完成
- [ ] 备份策略确认
- [ ] 迁移脚本测试
- [ ] 回滚脚本验证
- [ ] 性能影响评估
- [ ] 兼容性验证
- [ ] 文档更新
- [ ] 团队评审通过
- [ ] 生产部署计划
- [ ] 监控指标配置
