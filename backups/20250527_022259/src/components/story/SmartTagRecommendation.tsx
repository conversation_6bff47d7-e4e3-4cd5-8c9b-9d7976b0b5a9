import { useState, useEffect } from 'react';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { <PERSON><PERSON><PERSON>, Brain, TrendingUp, Clock } from 'lucide-react';
import { cn } from '@/lib/utils';
import { STORY_TAGS, type Tag } from '@/constants/storyConstants';

interface SmartTagRecommendationProps {
  content: string;
  selectedTags: string[];
  onTagSelect: (tagId: string) => void;
  onTagsSelect: (tagIds: string[]) => void;
  className?: string;
  maxRecommendations?: number;
}

interface RecommendationGroup {
  type: 'content' | 'trending' | 'related' | 'recent';
  title: string;
  icon: React.ReactNode;
  tags: Array<Tag & { score: number; reason?: string }>;
}

export default function SmartTagRecommendation({
  content,
  selectedTags,
  onTagSelect,
  onTagsSelect,
  className,
  maxRecommendations = 6,
}: SmartTagRecommendationProps) {
  const [recommendations, setRecommendations] = useState<RecommendationGroup[]>([]);
  const [loading, setLoading] = useState(false);

  // 基于内容的标签推荐
  const getContentBasedRecommendations = (text: string): Array<Tag & { score: number; reason: string }> => {
    const keywords = text.toLowerCase();
    const contentRecommendations: Array<Tag & { score: number; reason: string }> = [];

    // 关键词匹配规则
    const keywordRules = [
      { keywords: ['求职', '找工作', '应聘'], tags: ['job-hunting', 'job-search'], reason: '检测到求职相关内容' },
      { keywords: ['面试', '面谈'], tags: ['interview'], reason: '检测到面试相关内容' },
      { keywords: ['实习', '实习生'], tags: ['internship'], reason: '检测到实习相关内容' },
      { keywords: ['转行', '换行业'], tags: ['career-change'], reason: '检测到转行相关内容' },
      { keywords: ['薪资', '工资', '薪水', '待遇'], tags: ['salary'], reason: '检测到薪资相关内容' },
      { keywords: ['简历', 'CV', 'resume'], tags: ['resume'], reason: '检测到简历相关内容' },
      { keywords: ['本科', '学士'], tags: ['bachelor'], reason: '检测到本科学历' },
      { keywords: ['硕士', '研究生', 'master'], tags: ['master'], reason: '检测到硕士学历' },
      { keywords: ['博士', 'PhD', '博导'], tags: ['phd'], reason: '检测到博士学历' },
      { keywords: ['IT', '程序', '开发', '编程', '软件'], tags: ['it-industry'], reason: '检测到IT行业内容' },
      { keywords: ['金融', '银行', '投资', '证券'], tags: ['finance'], reason: '检测到金融行业内容' },
      { keywords: ['创业', '初创', 'startup'], tags: ['startup'], reason: '检测到创业相关内容' },
      { keywords: ['远程', '在家办公', 'remote'], tags: ['remote-work'], reason: '检测到远程工作内容' },
      { keywords: ['自由职业', '自由工作者', 'freelance'], tags: ['freelance'], reason: '检测到自由职业内容' },
      { keywords: ['海外', '国外', '出国'], tags: ['overseas'], reason: '检测到海外相关内容' },
      { keywords: ['成功', '成就', '胜利'], tags: ['success'], reason: '检测到成功故事内容' },
      { keywords: ['挑战', '困难', '难题'], tags: ['challenge'], reason: '检测到挑战相关内容' },
      { keywords: ['建议', '经验', '分享'], tags: ['advice'], reason: '检测到经验分享内容' },
    ];

    keywordRules.forEach(rule => {
      const matchCount = rule.keywords.filter(keyword => keywords.includes(keyword)).length;
      if (matchCount > 0) {
        rule.tags.forEach(tagId => {
          const tag = STORY_TAGS.find(t => t.id === tagId);
          if (tag && !selectedTags.includes(tagId)) {
            contentRecommendations.push({
              ...tag,
              score: matchCount * 10,
              reason: rule.reason,
            });
          }
        });
      }
    });

    return contentRecommendations.sort((a, b) => b.score - a.score).slice(0, 3);
  };

  // 获取热门标签推荐
  const getTrendingRecommendations = (): Array<Tag & { score: number; reason: string }> => {
    // 模拟热门标签数据
    const trendingTagIds = ['job-hunting', 'interview', 'internship', 'career-change', 'salary', 'it-industry'];
    
    return trendingTagIds
      .filter(tagId => !selectedTags.includes(tagId))
      .map(tagId => {
        const tag = STORY_TAGS.find(t => t.id === tagId);
        return tag ? {
          ...tag,
          score: Math.floor(Math.random() * 50) + 50,
          reason: '当前热门标签',
        } : null;
      })
      .filter(Boolean)
      .slice(0, 3) as Array<Tag & { score: number; reason: string }>;
  };

  // 获取相关标签推荐
  const getRelatedRecommendations = (): Array<Tag & { score: number; reason: string }> => {
    if (selectedTags.length === 0) return [];

    const relatedTags: Array<Tag & { score: number; reason: string }> = [];
    
    // 基于已选标签的类别推荐相关标签
    selectedTags.forEach(selectedTagId => {
      const selectedTag = STORY_TAGS.find(t => t.id === selectedTagId);
      if (selectedTag?.category) {
        const categoryTags = STORY_TAGS.filter(t => 
          t.category === selectedTag.category && 
          t.id !== selectedTagId && 
          !selectedTags.includes(t.id)
        );
        
        categoryTags.forEach(tag => {
          relatedTags.push({
            ...tag,
            score: 30,
            reason: `与"${selectedTag.label}"相关`,
          });
        });
      }
    });

    return relatedTags.slice(0, 3);
  };

  // 获取最近使用的标签推荐
  const getRecentRecommendations = (): Array<Tag & { score: number; reason: string }> => {
    try {
      const recentTags = JSON.parse(localStorage.getItem('recentTags') || '[]') as string[];
      
      return recentTags
        .filter(tagId => !selectedTags.includes(tagId))
        .map(tagId => {
          const tag = STORY_TAGS.find(t => t.id === tagId);
          return tag ? {
            ...tag,
            score: 20,
            reason: '最近使用过',
          } : null;
        })
        .filter(Boolean)
        .slice(0, 3) as Array<Tag & { score: number; reason: string }>;
    } catch {
      return [];
    }
  };

  // 保存标签使用记录
  const saveTagUsage = (tagId: string) => {
    try {
      const recentTags = JSON.parse(localStorage.getItem('recentTags') || '[]') as string[];
      const updatedTags = [tagId, ...recentTags.filter(id => id !== tagId)].slice(0, 10);
      localStorage.setItem('recentTags', JSON.stringify(updatedTags));
    } catch (error) {
      console.error('保存标签使用记录失败:', error);
    }
  };

  // 处理标签选择
  const handleTagSelect = (tagId: string) => {
    saveTagUsage(tagId);
    onTagSelect(tagId);
  };

  // 一键应用推荐
  const applyRecommendations = (tags: Array<Tag & { score: number }>) => {
    const tagIds = tags.map(t => t.id);
    tagIds.forEach(saveTagUsage);
    onTagsSelect(tagIds);
  };

  // 获取所有推荐
  const fetchRecommendations = async () => {
    setLoading(true);
    
    try {
      // 模拟API延迟
      await new Promise(resolve => setTimeout(resolve, 300));
      
      const groups: RecommendationGroup[] = [];

      // 基于内容的推荐
      if (content.trim()) {
        const contentRecs = getContentBasedRecommendations(content);
        if (contentRecs.length > 0) {
          groups.push({
            type: 'content',
            title: '内容匹配',
            icon: <Brain className="h-4 w-4" />,
            tags: contentRecs,
          });
        }
      }

      // 相关标签推荐
      const relatedRecs = getRelatedRecommendations();
      if (relatedRecs.length > 0) {
        groups.push({
          type: 'related',
          title: '相关标签',
          icon: <Sparkles className="h-4 w-4" />,
          tags: relatedRecs,
        });
      }

      // 热门标签推荐
      const trendingRecs = getTrendingRecommendations();
      if (trendingRecs.length > 0) {
        groups.push({
          type: 'trending',
          title: '热门标签',
          icon: <TrendingUp className="h-4 w-4" />,
          tags: trendingRecs,
        });
      }

      // 最近使用推荐
      const recentRecs = getRecentRecommendations();
      if (recentRecs.length > 0) {
        groups.push({
          type: 'recent',
          title: '最近使用',
          icon: <Clock className="h-4 w-4" />,
          tags: recentRecs,
        });
      }

      setRecommendations(groups);
    } catch (error) {
      console.error('获取标签推荐失败:', error);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchRecommendations();
  }, [content, selectedTags.join(',')]);

  if (loading) {
    return (
      <Card className={className}>
        <CardHeader>
          <CardTitle className="text-lg flex items-center gap-2">
            <Sparkles className="h-5 w-5" />
            智能推荐
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex items-center justify-center py-8">
            <div className="flex items-center gap-2 text-gray-500">
              <div className="h-4 w-4 border-2 border-blue-500 border-t-transparent rounded-full animate-spin"></div>
              <span>分析中...</span>
            </div>
          </div>
        </CardContent>
      </Card>
    );
  }

  if (recommendations.length === 0) {
    return (
      <Card className={className}>
        <CardHeader>
          <CardTitle className="text-lg flex items-center gap-2">
            <Sparkles className="h-5 w-5" />
            智能推荐
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="text-center py-8 text-gray-500">
            <Sparkles className="h-8 w-8 mx-auto mb-2" />
            <p>暂无推荐标签</p>
            <p className="text-sm">输入内容或选择标签后会有智能推荐</p>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className={className}>
      <CardHeader>
        <CardTitle className="text-lg flex items-center gap-2">
          <Sparkles className="h-5 w-5" />
          智能推荐
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        {recommendations.map((group) => (
          <div key={group.type} className="space-y-2">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-2 text-sm font-medium">
                {group.icon}
                {group.title}
              </div>
              {group.tags.length > 1 && (
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => applyRecommendations(group.tags)}
                  className="text-xs"
                >
                  全部应用
                </Button>
              )}
            </div>
            
            <div className="flex flex-wrap gap-2">
              {group.tags.map((tag) => (
                <Badge
                  key={tag.id}
                  variant="outline"
                  className="cursor-pointer hover:bg-blue-50 hover:border-blue-300 flex items-center gap-1"
                  onClick={() => handleTagSelect(tag.id)}
                  title={tag.reason}
                >
                  {tag.label}
                  <span className="text-xs opacity-60">
                    {tag.score}
                  </span>
                </Badge>
              ))}
            </div>
          </div>
        ))}
      </CardContent>
    </Card>
  );
}
