/**
 * 统计服务
 * 
 * 提供统计相关的功能
 */

import { fetchData } from './unifiedDataService';

/**
 * 获取统计数据
 * @param params 请求参数
 * @returns 统计数据
 */
export async function getStats(params: any = {}) {
  return fetchData(
    '/stats',
    'getStatsMock',
    params,
    { method: 'GET' }
  );
}

/**
 * 获取审核统计数据
 * @returns 审核统计数据
 */
export async function getReviewStats() {
  return fetchData(
    '/stats/review',
    'getReviewStatsMock',
    {},
    { method: 'GET' }
  );
}

/**
 * 获取审核员绩效数据
 * @param timeRange 时间范围
 * @returns 审核员绩效数据
 */
export async function getReviewerPerformance(timeRange: string = 'week') {
  return fetchData(
    '/stats/reviewer-performance',
    'getReviewerPerformanceMock',
    { timeRange },
    { method: 'GET' }
  );
}
