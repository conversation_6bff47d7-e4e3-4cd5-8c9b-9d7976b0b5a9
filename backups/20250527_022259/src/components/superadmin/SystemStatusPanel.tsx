import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from '@/components/ui/card';
import { Progress } from '@/components/ui/progress';
import { Badge } from '@/components/ui/badge';
import { But<PERSON> } from '@/components/ui/button';
import { Tabs, Ta<PERSON>Content, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { 
  Activity, 
  Server, 
  Database, 
  HardDrive, 
  Cpu, 
  RefreshCw,
  Memory,
  Network,
  Users,
  Clock,
  AlertTriangle
} from 'lucide-react';
import { useToast } from '@/components/ui/use-toast';

// 系统状态接口
export interface SystemStatus {
  cpu: number;
  memory: number;
  disk: number;
  network: number;
  activeUsers: number;
  requestsPerMinute: number;
  errorRate: number;
  uptime: string;
  lastUpdated: string;
}

interface SystemStatusPanelProps {
  compact?: boolean;
  refreshInterval?: number; // 刷新间隔，单位为毫秒，默认为30秒
  onStatusChange?: (status: SystemStatus) => void;
}

/**
 * 系统状态面板组件
 * 
 * 显示系统的关键指标，包括CPU、内存、磁盘、网络等使用情况
 */
const SystemStatusPanel: React.FC<SystemStatusPanelProps> = ({ 
  compact = false,
  refreshInterval = 30000,
  onStatusChange
}) => {
  const { toast } = useToast();
  const [isLoading, setIsLoading] = useState(false);
  const [systemStatus, setSystemStatus] = useState<SystemStatus>({
    cpu: 0,
    memory: 0,
    disk: 0,
    network: 0,
    activeUsers: 0,
    requestsPerMinute: 0,
    errorRate: 0,
    uptime: '0天0小时0分钟',
    lastUpdated: new Date().toLocaleString()
  });

  // 获取系统状态
  const fetchSystemStatus = async () => {
    try {
      setIsLoading(true);
      
      // 这里应该是实际的API调用
      // 现在使用模拟数据
      setTimeout(() => {
        const newStatus: SystemStatus = {
          cpu: Math.floor(Math.random() * 100),
          memory: Math.floor(Math.random() * 100),
          disk: Math.floor(Math.random() * 100),
          network: Math.floor(Math.random() * 100),
          activeUsers: Math.floor(Math.random() * 100),
          requestsPerMinute: Math.floor(Math.random() * 1000),
          errorRate: Math.floor(Math.random() * 10),
          uptime: `${Math.floor(Math.random() * 30)}天${Math.floor(Math.random() * 24)}小时${Math.floor(Math.random() * 60)}分钟`,
          lastUpdated: new Date().toLocaleString()
        };
        
        setSystemStatus(newStatus);
        
        // 如果有状态变化回调，则调用
        if (onStatusChange) {
          onStatusChange(newStatus);
        }
        
        setIsLoading(false);
      }, 1000);
    } catch (error) {
      console.error('获取系统状态失败:', error);
      toast({
        variant: 'destructive',
        title: '获取系统状态失败',
        description: '无法加载系统状态数据，请稍后再试'
      });
      setIsLoading(false);
    }
  };

  // 组件加载时获取系统状态
  useEffect(() => {
    fetchSystemStatus();
    
    // 设置定时刷新
    if (refreshInterval > 0) {
      const intervalId = setInterval(fetchSystemStatus, refreshInterval);
      
      // 组件卸载时清除定时器
      return () => clearInterval(intervalId);
    }
  }, [refreshInterval]);

  // 获取状态颜色
  const getStatusColor = (value: number, thresholds = { warning: 70, critical: 90 }) => {
    if (value >= thresholds.critical) return 'bg-red-500';
    if (value >= thresholds.warning) return 'bg-yellow-500';
    return 'bg-green-500';
  };

  // 获取状态文本
  const getStatusText = (value: number, thresholds = { warning: 70, critical: 90 }) => {
    if (value >= thresholds.critical) return '危险';
    if (value >= thresholds.warning) return '警告';
    return '正常';
  };

  // 获取状态Badge变体
  const getStatusBadgeVariant = (value: number, thresholds = { warning: 70, critical: 90 }) => {
    if (value >= thresholds.critical) return 'destructive';
    if (value >= thresholds.warning) return 'warning';
    return 'success';
  };

  // 紧凑模式下的渲染
  if (compact) {
    return (
      <Card>
        <CardHeader className="pb-2">
          <CardTitle className="text-sm font-medium flex items-center">
            <Server className="h-4 w-4 mr-2" />
            系统状态
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-2">
            <div className="flex justify-between items-center">
              <span className="text-sm">CPU</span>
              <div className="flex items-center">
                <Progress value={systemStatus.cpu} className="w-24 h-2 mr-2" indicatorClassName={getStatusColor(systemStatus.cpu)} />
                <span className="text-xs">{systemStatus.cpu}%</span>
              </div>
            </div>
            <div className="flex justify-between items-center">
              <span className="text-sm">内存</span>
              <div className="flex items-center">
                <Progress value={systemStatus.memory} className="w-24 h-2 mr-2" indicatorClassName={getStatusColor(systemStatus.memory)} />
                <span className="text-xs">{systemStatus.memory}%</span>
              </div>
            </div>
            <div className="flex justify-between items-center">
              <span className="text-sm">磁盘</span>
              <div className="flex items-center">
                <Progress value={systemStatus.disk} className="w-24 h-2 mr-2" indicatorClassName={getStatusColor(systemStatus.disk)} />
                <span className="text-xs">{systemStatus.disk}%</span>
              </div>
            </div>
          </div>
          <div className="mt-2 text-xs text-muted-foreground">
            最后更新: {systemStatus.lastUpdated}
          </div>
        </CardContent>
      </Card>
    );
  }

  // 完整模式下的渲染
  return (
    <Card className="w-full">
      <CardHeader>
        <div className="flex justify-between items-center">
          <CardTitle className="flex items-center">
            <Server className="h-5 w-5 mr-2" />
            系统状态监控
          </CardTitle>
          <Button 
            variant="outline" 
            size="sm" 
            onClick={fetchSystemStatus}
            disabled={isLoading}
          >
            <RefreshCw className={`h-4 w-4 mr-2 ${isLoading ? 'animate-spin' : ''}`} />
            刷新
          </Button>
        </div>
        <CardDescription>
          监控系统关键指标和性能数据
        </CardDescription>
      </CardHeader>
      <CardContent>
        <Tabs defaultValue="resources" className="space-y-4">
          <TabsList>
            <TabsTrigger value="resources">
              <HardDrive className="h-4 w-4 mr-2" />
              资源使用
            </TabsTrigger>
            <TabsTrigger value="performance">
              <Activity className="h-4 w-4 mr-2" />
              性能指标
            </TabsTrigger>
          </TabsList>
          
          <TabsContent value="resources" className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <Card>
                <CardHeader className="pb-2">
                  <CardTitle className="text-sm font-medium flex items-center">
                    <Cpu className="h-4 w-4 mr-2" />
                    CPU 使用率
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="flex justify-between items-center mb-2">
                    <span className="text-2xl font-bold">{systemStatus.cpu}%</span>
                    <Badge variant={getStatusBadgeVariant(systemStatus.cpu) as any}>
                      {getStatusText(systemStatus.cpu)}
                    </Badge>
                  </div>
                  <Progress 
                    value={systemStatus.cpu} 
                    className="h-2" 
                    indicatorClassName={getStatusColor(systemStatus.cpu)} 
                  />
                </CardContent>
              </Card>
              
              <Card>
                <CardHeader className="pb-2">
                  <CardTitle className="text-sm font-medium flex items-center">
                    <Memory className="h-4 w-4 mr-2" />
                    内存使用率
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="flex justify-between items-center mb-2">
                    <span className="text-2xl font-bold">{systemStatus.memory}%</span>
                    <Badge variant={getStatusBadgeVariant(systemStatus.memory) as any}>
                      {getStatusText(systemStatus.memory)}
                    </Badge>
                  </div>
                  <Progress 
                    value={systemStatus.memory} 
                    className="h-2" 
                    indicatorClassName={getStatusColor(systemStatus.memory)} 
                  />
                </CardContent>
              </Card>
              
              <Card>
                <CardHeader className="pb-2">
                  <CardTitle className="text-sm font-medium flex items-center">
                    <Database className="h-4 w-4 mr-2" />
                    磁盘使用率
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="flex justify-between items-center mb-2">
                    <span className="text-2xl font-bold">{systemStatus.disk}%</span>
                    <Badge variant={getStatusBadgeVariant(systemStatus.disk) as any}>
                      {getStatusText(systemStatus.disk)}
                    </Badge>
                  </div>
                  <Progress 
                    value={systemStatus.disk} 
                    className="h-2" 
                    indicatorClassName={getStatusColor(systemStatus.disk)} 
                  />
                </CardContent>
              </Card>
              
              <Card>
                <CardHeader className="pb-2">
                  <CardTitle className="text-sm font-medium flex items-center">
                    <Network className="h-4 w-4 mr-2" />
                    网络使用率
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="flex justify-between items-center mb-2">
                    <span className="text-2xl font-bold">{systemStatus.network}%</span>
                    <Badge variant={getStatusBadgeVariant(systemStatus.network) as any}>
                      {getStatusText(systemStatus.network)}
                    </Badge>
                  </div>
                  <Progress 
                    value={systemStatus.network} 
                    className="h-2" 
                    indicatorClassName={getStatusColor(systemStatus.network)} 
                  />
                </CardContent>
              </Card>
            </div>
          </TabsContent>
          
          <TabsContent value="performance" className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <Card>
                <CardHeader className="pb-2">
                  <CardTitle className="text-sm font-medium flex items-center">
                    <Users className="h-4 w-4 mr-2" />
                    活跃用户数
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold">{systemStatus.activeUsers}</div>
                </CardContent>
              </Card>
              
              <Card>
                <CardHeader className="pb-2">
                  <CardTitle className="text-sm font-medium flex items-center">
                    <Activity className="h-4 w-4 mr-2" />
                    每分钟请求数
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold">{systemStatus.requestsPerMinute}</div>
                </CardContent>
              </Card>
              
              <Card>
                <CardHeader className="pb-2">
                  <CardTitle className="text-sm font-medium flex items-center">
                    <AlertTriangle className="h-4 w-4 mr-2" />
                    错误率
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="flex justify-between items-center mb-2">
                    <span className="text-2xl font-bold">{systemStatus.errorRate}%</span>
                    <Badge variant={getStatusBadgeVariant(systemStatus.errorRate, { warning: 5, critical: 10 }) as any}>
                      {getStatusText(systemStatus.errorRate, { warning: 5, critical: 10 })}
                    </Badge>
                  </div>
                  <Progress 
                    value={systemStatus.errorRate} 
                    className="h-2" 
                    indicatorClassName={getStatusColor(systemStatus.errorRate, { warning: 5, critical: 10 })} 
                  />
                </CardContent>
              </Card>
              
              <Card>
                <CardHeader className="pb-2">
                  <CardTitle className="text-sm font-medium flex items-center">
                    <Clock className="h-4 w-4 mr-2" />
                    系统运行时间
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold">{systemStatus.uptime}</div>
                </CardContent>
              </Card>
            </div>
          </TabsContent>
        </Tabs>
        
        <div className="mt-4 text-xs text-muted-foreground text-right">
          最后更新: {systemStatus.lastUpdated}
        </div>
      </CardContent>
    </Card>
  );
};

export default SystemStatusPanel;
