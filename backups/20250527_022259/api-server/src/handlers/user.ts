/**
 * 用户管理相关处理程序
 */

import { verifyToken } from '../utils/auth';

/**
 * 处理获取用户列表
 * @param request 请求对象
 * @param env 环境对象
 * @returns 响应对象
 */
export async function handleGetUsers(request: Request, env: Env): Promise<Response> {
  try {
    // 验证超级管理员权限
    const authHeader = request.headers.get('Authorization');

    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return new Response(JSON.stringify({
        success: false,
        error: '未授权'
      }), {
        status: 401,
        headers: { 'Content-Type': 'application/json' }
      });
    }

    const token = authHeader.substring(7);
    const tokenResult = verifyToken(token, env.JWT_SECRET);

    if (!tokenResult.valid || !tokenResult.payload) {
      return new Response(JSON.stringify({
        success: false,
        error: '无效的令牌'
      }), {
        status: 401,
        headers: { 'Content-Type': 'application/json' }
      });
    }

    // 验证角色权限
    const role = tokenResult.payload.role;

    if (role !== 'superadmin') {
      return new Response(JSON.stringify({
        success: false,
        error: '没有权限'
      }), {
        status: 403,
        headers: { 'Content-Type': 'application/json' }
      });
    }

    // 获取查询参数
    const url = new URL(request.url);
    const roleFilter = url.searchParams.get('role');
    const status = url.searchParams.get('status');
    const search = url.searchParams.get('search');
    const page = parseInt(url.searchParams.get('page') || '1', 10);
    const pageSize = parseInt(url.searchParams.get('pageSize') || '10', 10);

    // 构建查询
    let query = `
      SELECT * FROM users
      WHERE 1=1
    `;

    const params = [];

    // 添加角色过滤
    if (roleFilter) {
      query += ` AND role = ?`;
      params.push(roleFilter);
    }

    // 添加状态过滤
    if (status) {
      query += ` AND status = ?`;
      params.push(status);
    }

    // 添加搜索过滤
    if (search) {
      query += ` AND (username LIKE ? OR name LIKE ?)`;
      params.push(`%${search}%`, `%${search}%`);
    }

    // 添加排序
    query += ` ORDER BY created_at DESC`;

    // 添加分页
    query += ` LIMIT ? OFFSET ?`;
    params.push(pageSize.toString(), ((page - 1) * pageSize).toString());

    // 执行查询
    const users = await env.DB.prepare(query).bind(...params).all();

    // 获取总数
    let countQuery = `
      SELECT COUNT(*) as total
      FROM users
      WHERE 1=1
    `;

    const countParams = [];

    // 添加角色过滤
    if (roleFilter) {
      countQuery += ` AND role = ?`;
      countParams.push(roleFilter);
    }

    // 添加状态过滤
    if (status) {
      countQuery += ` AND status = ?`;
      countParams.push(status);
    }

    // 添加搜索过滤
    if (search) {
      countQuery += ` AND (username LIKE ? OR name LIKE ?)`;
      countParams.push(`%${search}%`, `%${search}%`);
    }

    const totalResult = await env.DB.prepare(countQuery).bind(...countParams).first();
    const total = totalResult ? (totalResult.total as number) : 0;

    // 获取用户权限
    const userIds = users.results.map(user => user.id);
    let permissionsResult = { results: [] };

    if (userIds.length > 0) {
      const permissionsQuery = `
        SELECT user_id, permission
        FROM permissions
        WHERE user_id IN (${userIds.map(() => '?').join(',')})
      `;

      permissionsResult = await env.DB.prepare(permissionsQuery).bind(...userIds).all();
    }

    // 组织权限数据
    const userPermissions: Record<number, string[]> = {};

    for (const perm of permissionsResult.results) {
      if (!userPermissions[perm.user_id]) {
        userPermissions[perm.user_id] = [];
      }

      userPermissions[perm.user_id].push(perm.permission);
    }

    // 组织响应数据
    const formattedUsers = users.results.map(user => ({
      id: user.id,
      username: user.username,
      name: user.name,
      role: user.role,
      status: user.status,
      lastLogin: user.last_login,
      createdAt: user.created_at,
      permissions: userPermissions[user.id] || []
    }));

    // 返回响应
    return new Response(JSON.stringify({
      success: true,
      users: formattedUsers,
      total,
      page,
      pageSize,
      totalPages: Math.ceil(total / pageSize)
    }), {
      status: 200,
      headers: { 'Content-Type': 'application/json' }
    });
  } catch (error) {
    console.error('获取用户列表失败:', error);

    return new Response(JSON.stringify({
      success: false,
      error: '服务器错误'
    }), {
      status: 500,
      headers: { 'Content-Type': 'application/json' }
    });
  }
}

/**
 * 处理创建用户
 * @param request 请求对象
 * @param env 环境对象
 * @returns 响应对象
 */
export async function handleCreateUser(request: Request, env: Env): Promise<Response> {
  try {
    // 验证超级管理员权限
    const authHeader = request.headers.get('Authorization');

    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return new Response(JSON.stringify({
        success: false,
        error: '未授权'
      }), {
        status: 401,
        headers: { 'Content-Type': 'application/json' }
      });
    }

    const token = authHeader.substring(7);
    const tokenResult = verifyToken(token, env.JWT_SECRET);

    if (!tokenResult.valid || !tokenResult.payload) {
      return new Response(JSON.stringify({
        success: false,
        error: '无效的令牌'
      }), {
        status: 401,
        headers: { 'Content-Type': 'application/json' }
      });
    }

    // 验证角色权限
    const role = tokenResult.payload.role;

    if (role !== 'superadmin') {
      return new Response(JSON.stringify({
        success: false,
        error: '没有权限'
      }), {
        status: 403,
        headers: { 'Content-Type': 'application/json' }
      });
    }

    // 解析请求体
    const { username, password, name, role: newRole, permissions } = await request.json() as {
      username: string;
      password: string;
      name: string;
      role: string;
      permissions: string[];
    };

    // 验证参数
    if (!username || !password || !name || !newRole) {
      return new Response(JSON.stringify({
        success: false,
        error: '缺少必要参数'
      }), {
        status: 400,
        headers: { 'Content-Type': 'application/json' }
      });
    }

    // 验证角色
    if (newRole !== 'admin' && newRole !== 'reviewer') {
      return new Response(JSON.stringify({
        success: false,
        error: '无效的角色'
      }), {
        status: 400,
        headers: { 'Content-Type': 'application/json' }
      });
    }

    // 检查用户名是否已存在
    const existingUser = await env.DB.prepare(`
      SELECT id FROM users WHERE username = ?
    `).bind(username).first();

    if (existingUser) {
      return new Response(JSON.stringify({
        success: false,
        error: '用户名已存在'
      }), {
        status: 400,
        headers: { 'Content-Type': 'application/json' }
      });
    }

    // 创建用户
    const result = await env.DB.prepare(`
      INSERT INTO users (username, password, name, role, status)
      VALUES (?, ?, ?, ?, ?)
    `).bind(
      username,
      password,
      name,
      newRole,
      'active'
    ).run();

    if (!result.success) {
      throw new Error('Failed to create user');
    }

    // 获取新创建的用户ID
    const userId = result.meta.last_row_id;

    // 添加权限
    if (permissions && permissions.length > 0) {
      for (const permission of permissions) {
        await env.DB.prepare(`
          INSERT INTO permissions (user_id, permission)
          VALUES (?, ?)
        `).bind(userId, permission).run();
      }
    }

    // 返回响应
    return new Response(JSON.stringify({
      success: true,
      userId,
      message: '用户创建成功'
    }), {
      status: 201,
      headers: { 'Content-Type': 'application/json' }
    });
  } catch (error) {
    console.error('创建用户失败:', error);

    return new Response(JSON.stringify({
      success: false,
      error: '服务器错误'
    }), {
      status: 500,
      headers: { 'Content-Type': 'application/json' }
    });
  }
}

/**
 * 处理更新用户
 * @param request 请求对象
 * @param env 环境对象
 * @param userId 用户ID
 * @returns 响应对象
 */
export async function handleUpdateUser(request: Request, env: Env, userId: string): Promise<Response> {
  try {
    // 验证超级管理员权限
    const authHeader = request.headers.get('Authorization');

    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return new Response(JSON.stringify({
        success: false,
        error: '未授权'
      }), {
        status: 401,
        headers: { 'Content-Type': 'application/json' }
      });
    }

    const token = authHeader.substring(7);
    const tokenResult = verifyToken(token, env.JWT_SECRET);

    if (!tokenResult.valid || !tokenResult.payload) {
      return new Response(JSON.stringify({
        success: false,
        error: '无效的令牌'
      }), {
        status: 401,
        headers: { 'Content-Type': 'application/json' }
      });
    }

    // 验证角色权限
    const role = tokenResult.payload.role;

    if (role !== 'superadmin') {
      return new Response(JSON.stringify({
        success: false,
        error: '没有权限'
      }), {
        status: 403,
        headers: { 'Content-Type': 'application/json' }
      });
    }

    // 解析请求体
    const { name, role: newRole, status, permissions } = await request.json() as {
      name?: string;
      role?: string;
      status?: string;
      permissions?: string[];
    };

    // 检查用户是否存在
    const user = await env.DB.prepare(`
      SELECT * FROM users WHERE id = ?
    `).bind(userId).first();

    if (!user) {
      return new Response(JSON.stringify({
        success: false,
        error: '用户不存在'
      }), {
        status: 404,
        headers: { 'Content-Type': 'application/json' }
      });
    }

    // 构建更新查询
    let updateFields = [];
    let updateParams = [];

    if (name) {
      updateFields.push('name = ?');
      updateParams.push(name);
    }

    if (newRole) {
      // 验证角色
      if (newRole !== 'admin' && newRole !== 'reviewer') {
        return new Response(JSON.stringify({
          success: false,
          error: '无效的角色'
        }), {
          status: 400,
          headers: { 'Content-Type': 'application/json' }
        });
      }

      updateFields.push('role = ?');
      updateParams.push(newRole);
    }

    if (status) {
      // 验证状态
      if (status !== 'active' && status !== 'inactive') {
        return new Response(JSON.stringify({
          success: false,
          error: '无效的状态'
        }), {
          status: 400,
          headers: { 'Content-Type': 'application/json' }
        });
      }

      updateFields.push('status = ?');
      updateParams.push(status);
    }

    // 如果有字段需要更新
    if (updateFields.length > 0) {
      const updateQuery = `
        UPDATE users
        SET ${updateFields.join(', ')}
        WHERE id = ?
      `;

      updateParams.push(userId);

      await env.DB.prepare(updateQuery).bind(...updateParams).run();
    }

    // 更新权限
    if (permissions) {
      // 删除现有权限
      await env.DB.prepare(`
        DELETE FROM permissions
        WHERE user_id = ?
      `).bind(userId).run();

      // 添加新权限
      for (const permission of permissions) {
        await env.DB.prepare(`
          INSERT INTO permissions (user_id, permission)
          VALUES (?, ?)
        `).bind(userId, permission).run();
      }
    }

    // 返回响应
    return new Response(JSON.stringify({
      success: true,
      message: '用户更新成功'
    }), {
      status: 200,
      headers: { 'Content-Type': 'application/json' }
    });
  } catch (error) {
    console.error('更新用户失败:', error);

    return new Response(JSON.stringify({
      success: false,
      error: '服务器错误'
    }), {
      status: 500,
      headers: { 'Content-Type': 'application/json' }
    });
  }
}

/**
 * 处理删除用户
 * @param request 请求对象
 * @param env 环境对象
 * @param userId 用户ID
 * @returns 响应对象
 */
export async function handleDeleteUser(request: Request, env: Env, userId: string): Promise<Response> {
  try {
    // 验证超级管理员权限
    const authHeader = request.headers.get('Authorization');

    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return new Response(JSON.stringify({
        success: false,
        error: '未授权'
      }), {
        status: 401,
        headers: { 'Content-Type': 'application/json' }
      });
    }

    const token = authHeader.substring(7);
    const tokenResult = verifyToken(token, env.JWT_SECRET);

    if (!tokenResult.valid || !tokenResult.payload) {
      return new Response(JSON.stringify({
        success: false,
        error: '无效的令牌'
      }), {
        status: 401,
        headers: { 'Content-Type': 'application/json' }
      });
    }

    // 验证角色权限
    const role = tokenResult.payload.role;

    if (role !== 'superadmin') {
      return new Response(JSON.stringify({
        success: false,
        error: '没有权限'
      }), {
        status: 403,
        headers: { 'Content-Type': 'application/json' }
      });
    }

    // 检查用户是否存在
    const user = await env.DB.prepare(`
      SELECT * FROM users WHERE id = ?
    `).bind(userId).first();

    if (!user) {
      return new Response(JSON.stringify({
        success: false,
        error: '用户不存在'
      }), {
        status: 404,
        headers: { 'Content-Type': 'application/json' }
      });
    }

    // 删除用户权限
    await env.DB.prepare(`
      DELETE FROM permissions
      WHERE user_id = ?
    `).bind(userId).run();

    // 删除用户
    await env.DB.prepare(`
      DELETE FROM users
      WHERE id = ?
    `).bind(userId).run();

    // 返回响应
    return new Response(JSON.stringify({
      success: true,
      message: '用户删除成功'
    }), {
      status: 200,
      headers: { 'Content-Type': 'application/json' }
    });
  } catch (error) {
    console.error('删除用户失败:', error);

    return new Response(JSON.stringify({
      success: false,
      error: '服务器错误'
    }), {
      status: 500,
      headers: { 'Content-Type': 'application/json' }
    });
  }
}

/**
 * 处理重置用户密码
 * @param request 请求对象
 * @param env 环境对象
 * @param userId 用户ID
 * @returns 响应对象
 */
export async function handleResetUserPassword(request: Request, env: Env, userId: string): Promise<Response> {
  try {
    // 验证超级管理员权限
    const authHeader = request.headers.get('Authorization');

    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return new Response(JSON.stringify({
        success: false,
        error: '未授权'
      }), {
        status: 401,
        headers: { 'Content-Type': 'application/json' }
      });
    }

    const token = authHeader.substring(7);
    const tokenResult = verifyToken(token, env.JWT_SECRET);

    if (!tokenResult.valid || !tokenResult.payload) {
      return new Response(JSON.stringify({
        success: false,
        error: '无效的令牌'
      }), {
        status: 401,
        headers: { 'Content-Type': 'application/json' }
      });
    }

    // 验证角色权限
    const role = tokenResult.payload.role;

    if (role !== 'superadmin') {
      return new Response(JSON.stringify({
        success: false,
        error: '没有权限'
      }), {
        status: 403,
        headers: { 'Content-Type': 'application/json' }
      });
    }

    // 检查用户是否存在
    const user = await env.DB.prepare(`
      SELECT * FROM users WHERE id = ?
    `).bind(userId).first();

    if (!user) {
      return new Response(JSON.stringify({
        success: false,
        error: '用户不存在'
      }), {
        status: 404,
        headers: { 'Content-Type': 'application/json' }
      });
    }

    // 生成新密码
    const newPassword = `${user.role}123`;

    // 更新密码
    await env.DB.prepare(`
      UPDATE users
      SET password = ?
      WHERE id = ?
    `).bind(newPassword, userId).run();

    // 返回响应
    return new Response(JSON.stringify({
      success: true,
      newPassword,
      message: '密码已重置'
    }), {
      status: 200,
      headers: { 'Content-Type': 'application/json' }
    });
  } catch (error) {
    console.error('重置用户密码失败:', error);

    return new Response(JSON.stringify({
      success: false,
      error: '服务器错误'
    }), {
      status: 500,
      headers: { 'Content-Type': 'application/json' }
    });
  }
}