/**
 * 问卷心声生成服务
 * 自动从问卷回复中提取心声数据
 */

import { Context } from 'hono';
import { Env } from '../types';

export class VoiceGenerationService {
  /**
   * 从问卷回复自动生成心声数据
   */
  static async generateVoicesFromQuestionnaire(
    c: Context<{ Bindings: Env }>,
    questionnaireData: {
      id: string;
      userId?: string;
      adviceForStudents?: string;
      observationOnEmployment?: string;
      educationLevel?: string;
      region?: string;
      isAnonymous?: boolean;
    }
  ): Promise<{ success: boolean; voicesCreated: number; errors?: string[] }> {
    const errors: string[] = [];
    let voicesCreated = 0;

    try {
      // 生成建议类心声
      if (questionnaireData.adviceForStudents && questionnaireData.adviceForStudents.trim()) {
        try {
          const adviceVoiceId = `qv_advice_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
          
          await c.env.<PERSON>.prepare(`
            INSERT INTO questionnaire_voices_v2 (
              id, source_response_id, user_id, voice_type, title, content,
              education_level, education_level_display, region_code, region_display,
              status, likes, views, created_at, updated_at
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
          `).bind(
            adviceVoiceId,
            questionnaireData.id,
            questionnaireData.userId || null,
            'advice',
            '给学弟学妹的建议',
            questionnaireData.adviceForStudents,
            questionnaireData.educationLevel || null,
            questionnaireData.educationLevel || null,
            questionnaireData.region || null,
            questionnaireData.region || null,
            'approved', // 自动审核通过
            0, // 初始点赞数
            0, // 初始浏览数
            new Date().toISOString(),
            new Date().toISOString()
          ).run();

          voicesCreated++;
          console.log(`✅ 创建建议类心声: ${adviceVoiceId}`);
        } catch (error) {
          const errorMsg = `创建建议类心声失败: ${error instanceof Error ? error.message : String(error)}`;
          errors.push(errorMsg);
          console.error(errorMsg);
        }
      }

      // 生成观察类心声
      if (questionnaireData.observationOnEmployment && questionnaireData.observationOnEmployment.trim()) {
        try {
          const observationVoiceId = `qv_observation_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
          
          await c.env.DB.prepare(`
            INSERT INTO questionnaire_voices_v2 (
              id, source_response_id, user_id, voice_type, title, content,
              education_level, education_level_display, region_code, region_display,
              status, likes, views, created_at, updated_at
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
          `).bind(
            observationVoiceId,
            questionnaireData.id,
            questionnaireData.userId || null,
            'observation',
            '对就业环境的观察',
            questionnaireData.observationOnEmployment,
            questionnaireData.educationLevel || null,
            questionnaireData.educationLevel || null,
            questionnaireData.region || null,
            questionnaireData.region || null,
            'approved', // 自动审核通过
            0, // 初始点赞数
            0, // 初始浏览数
            new Date().toISOString(),
            new Date().toISOString()
          ).run();

          voicesCreated++;
          console.log(`✅ 创建观察类心声: ${observationVoiceId}`);
        } catch (error) {
          const errorMsg = `创建观察类心声失败: ${error instanceof Error ? error.message : String(error)}`;
          errors.push(errorMsg);
          console.error(errorMsg);
        }
      }

      return {
        success: errors.length === 0,
        voicesCreated,
        errors: errors.length > 0 ? errors : undefined
      };

    } catch (error) {
      const errorMsg = `心声生成服务异常: ${error instanceof Error ? error.message : String(error)}`;
      console.error(errorMsg);
      return {
        success: false,
        voicesCreated: 0,
        errors: [errorMsg]
      };
    }
  }
}
