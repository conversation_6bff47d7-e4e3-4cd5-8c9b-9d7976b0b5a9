/**
 * 内容审核路由
 */

import { Hono } from 'hono';
import { Env } from '../../types';
import { adminAuthMiddleware } from '../../middlewares/auth.middleware';
import { reviewerAuthMiddleware } from '../../middlewares/reviewerAuth.middleware';
import { superAdminAuthMiddleware } from '../../middlewares/superAdminAuth.middleware';
import { permissionMiddleware, anyPermissionMiddleware } from '../../middlewares/permission.middleware';
import { riskAssessmentMiddleware } from '../../middlewares/riskAssessment.middleware';
import { anomalyDetectionMiddleware } from '../../middlewares/anomalyDetection.middleware';
import { Permission } from '../../services/permissionService';
import { AuditAction } from '../../services/auditLogService';
import * as reviewController from './review.controller';
import * as reviewSettingsController from './review-settings.controller';

// 创建路由
const reviewRouter = new Hono<{ Bindings: Env }>();

// 通用认证中间件 - 允许审核员、管理员和超级管理员访问
const anyRoleAuthMiddleware = async (c: any, next: any) => {
  // 尝试超级管理员认证
  try {
    await superAdminAuthMiddleware(c, () => {});
    await next();
    return;
  } catch (error) {
    // 超级管理员认证失败，继续尝试管理员认证
  }

  // 尝试管理员认证
  try {
    await adminAuthMiddleware(c, () => {});
    await next();
    return;
  } catch (error) {
    // 管理员认证失败，继续尝试审核员认证
  }

  // 尝试审核员认证
  try {
    await reviewerAuthMiddleware(c, next);
    return;
  } catch (error) {
    // 所有认证都失败
    return c.json({ success: false, error: 'Unauthorized' }, 401);
  }
};

// 审核员路由（允许审核员、管理员和超级管理员访问）
// 获取待审核内容列表
reviewRouter.get('/pending',
  anyRoleAuthMiddleware,
  permissionMiddleware(Permission.REVIEW_VIEW, 'review'),
  reviewController.getPendingContents
);

// 获取单条待审核内容详情
reviewRouter.get('/:id',
  anyRoleAuthMiddleware,
  permissionMiddleware(Permission.REVIEW_VIEW, 'review'),
  reviewController.getPendingContent
);

// 审核通过内容
reviewRouter.post('/:id/approve',
  anyRoleAuthMiddleware,
  permissionMiddleware(Permission.REVIEW_APPROVE, 'review'),
  reviewController.approveContent
);

// 编辑并通过内容
reviewRouter.put('/:id/edit',
  anyRoleAuthMiddleware,
  permissionMiddleware(Permission.REVIEW_EDIT, 'review'),
  reviewController.editContent
);

// 拒绝内容
reviewRouter.post('/:id/reject',
  anyRoleAuthMiddleware,
  permissionMiddleware(Permission.REVIEW_REJECT, 'review'),
  reviewController.rejectContent
);

// 批量操作
reviewRouter.post('/batch',
  anyRoleAuthMiddleware,
  permissionMiddleware(Permission.REVIEW_BATCH, 'review'),
  riskAssessmentMiddleware(Permission.REVIEW_BATCH, 'review_batch'),
  anomalyDetectionMiddleware(AuditAction.BATCH_APPROVE, 'review_batch', {
    timeWindowMinutes: 10,
    thresholdCount: 5
  }),
  reviewController.batchOperation
);

// 管理员路由（允许管理员和超级管理员访问）
// 通用管理员认证中间件
const adminOrSuperAdminAuthMiddleware = async (c: any, next: any) => {
  // 尝试超级管理员认证
  try {
    await superAdminAuthMiddleware(c, () => {});
    await next();
    return;
  } catch (error) {
    // 超级管理员认证失败，尝试管理员认证
  }

  // 尝试管理员认证
  try {
    await adminAuthMiddleware(c, next);
    return;
  } catch (error) {
    // 所有认证都失败
    return c.json({ success: false, error: 'Unauthorized' }, 401);
  }
};

// 获取审核设置
reviewRouter.get('/settings',
  adminOrSuperAdminAuthMiddleware,
  permissionMiddleware(Permission.SETTINGS_VIEW, 'review_settings'),
  reviewSettingsController.getReviewSettings
);

// 更新审核设置
reviewRouter.post('/settings',
  adminOrSuperAdminAuthMiddleware,
  permissionMiddleware(Permission.SETTINGS_EDIT, 'review_settings'),
  reviewSettingsController.updateReviewSettings
);

// 获取审核统计信息
reviewRouter.get('/stats',
  adminOrSuperAdminAuthMiddleware,
  permissionMiddleware(Permission.ANALYTICS_VIEW, 'review_stats'),
  reviewSettingsController.getReviewStats
);

// 获取审核员绩效数据
reviewRouter.get('/performance',
  adminOrSuperAdminAuthMiddleware,
  anyPermissionMiddleware([Permission.ANALYTICS_VIEW, Permission.USER_VIEW], 'reviewer_performance'),
  reviewSettingsController.getReviewerPerformance
);

// 临时测试端点 - 无需认证
reviewRouter.get('/test', async (c) => {
  return c.json({
    success: true,
    message: 'Review routes are working',
    timestamp: new Date().toISOString(),
    path: '/api/admin/review/test'
  });
});

// 临时测试待审核内容端点 - 无需认证，返回模拟数据
reviewRouter.get('/pending-test', async (c) => {
  return c.json({
    success: true,
    pendingContents: [
      {
        id: 'test-1',
        sequenceNumber: 'TEST-001',
        type: 'story',
        originalContent: '这是一个测试内容',
        status: 'pending',
        priority: 1,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString()
      }
    ],
    pagination: {
      page: 1,
      pageSize: 50,
      total: 1,
      totalPages: 1
    },
    message: 'Test data for pending contents'
  });
});

export default reviewRouter;
