/**
 * 安全日志导出服务
 *
 * 提供安全日志导出相关的 API 调用
 */

import { apiClient } from './apiClient';

// 导出格式类型
export type ExportFormat = 'csv' | 'json' | 'xml' | 'excel' | 'pdf';

// 日志级别类型
export type LogLevel = 'debug' | 'info' | 'warn' | 'error' | 'all';

// 日志类别类型
export type LogCategory = 'login' | 'access' | 'data' | 'system' | 'config' | 'all';

// 导出参数
export interface ExportParams {
  format: ExportFormat;
  level?: LogLevel;
  category?: LogCategory;
  startDate?: string;
  endDate?: string;
  search?: string;
  includeDetails?: boolean;
  includeContext?: boolean;
  maxRows?: number;
}

// 导出结果
export interface ExportResult {
  success: boolean;
  message?: string;
  data?: {
    downloadUrl: string;
    fileName: string;
    fileSize: string;
    expiresAt: string;
  };
}

/**
 * 导出安全日志
 * @param params 导出参数
 * @returns 导出结果
 */
export async function exportSecurityLogs(params: ExportParams): Promise<ExportResult> {
  try {
    // 检查是否为开发环境
    if (import.meta.env.DEV && import.meta.env.VITE_USE_MOCK === 'true') {
      console.log('使用模拟数据导出安全日志');

      // 模拟网络延迟
      await new Promise(resolve => setTimeout(resolve, 1000));

      // 根据格式生成不同的文件名
      const fileExtension = params.format === 'excel' ? 'xlsx' : params.format;
      const fileName = `security_logs_${new Date().toISOString().split('T')[0]}.${fileExtension}`;

      // 模拟文件大小
      const fileSize = Math.floor(Math.random() * 10) + 1 + ' MB';

      // 模拟过期时间
      const expiresAt = new Date();
      expiresAt.setHours(expiresAt.getHours() + 24);

      // 模拟下载链接
      const downloadUrl = params.format === 'pdf' || params.format === 'excel' 
        ? '#' // 这些格式在开发环境中不提供实际下载
        : generateMockDownloadUrl(params);

      return {
        success: true,
        message: '日志导出成功',
        data: {
          downloadUrl,
          fileName,
          fileSize,
          expiresAt: expiresAt.toISOString()
        }
      };
    }

    // 生产环境使用真实API
    const response = await apiClient.post('/admin/security/logs/export', params);
    return response.data;
  } catch (error) {
    console.error('导出安全日志失败:', error);
    return {
      success: false,
      message: '导出安全日志失败，请稍后再试'
    };
  }
}

/**
 * 生成模拟下载链接
 * @param params 导出参数
 * @returns 模拟下载链接
 */
function generateMockDownloadUrl(params: ExportParams): string {
  // 生成模拟数据
  let content = '';
  
  if (params.format === 'csv') {
    content = generateMockCsvContent();
  } else if (params.format === 'json') {
    content = generateMockJsonContent();
  } else if (params.format === 'xml') {
    content = generateMockXmlContent();
  }
  
  // 创建 Blob 对象
  const blob = new Blob([content], { type: getMimeType(params.format) });
  
  // 创建下载链接
  return URL.createObjectURL(blob);
}

/**
 * 获取 MIME 类型
 * @param format 导出格式
 * @returns MIME 类型
 */
function getMimeType(format: ExportFormat): string {
  switch (format) {
    case 'csv':
      return 'text/csv';
    case 'json':
      return 'application/json';
    case 'xml':
      return 'application/xml';
    case 'excel':
      return 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet';
    case 'pdf':
      return 'application/pdf';
    default:
      return 'text/plain';
  }
}

/**
 * 生成模拟 CSV 内容
 * @returns 模拟 CSV 内容
 */
function generateMockCsvContent(): string {
  return `id,timestamp,level,category,message,user,ip
1,2023-06-15T10:30:00Z,info,login,User login successful,user123,*************
2,2023-06-15T10:35:00Z,warn,access,Unauthorized access attempt,user456,*************
3,2023-06-15T10:40:00Z,error,system,Database connection failed,system,*************
4,2023-06-15T10:45:00Z,info,data,Data export completed,admin,*************
5,2023-06-15T10:50:00Z,debug,config,Configuration updated,admin,*************
`;
}

/**
 * 生成模拟 JSON 内容
 * @returns 模拟 JSON 内容
 */
function generateMockJsonContent(): string {
  const logs = [
    {
      id: '1',
      timestamp: '2023-06-15T10:30:00Z',
      level: 'info',
      category: 'login',
      message: 'User login successful',
      user: 'user123',
      ip: '*************',
      details: {
        browser: 'Chrome 114.0.5735.198',
        os: 'Windows 10',
        location: 'Beijing, China'
      }
    },
    {
      id: '2',
      timestamp: '2023-06-15T10:35:00Z',
      level: 'warn',
      category: 'access',
      message: 'Unauthorized access attempt',
      user: 'user456',
      ip: '*************',
      details: {
        resource: '/api/admin/users',
        method: 'GET'
      }
    },
    {
      id: '3',
      timestamp: '2023-06-15T10:40:00Z',
      level: 'error',
      category: 'system',
      message: 'Database connection failed',
      user: 'system',
      ip: '*************',
      details: {
        error: 'Connection timeout',
        database: 'users'
      }
    },
    {
      id: '4',
      timestamp: '2023-06-15T10:45:00Z',
      level: 'info',
      category: 'data',
      message: 'Data export completed',
      user: 'admin',
      ip: '*************',
      details: {
        format: 'csv',
        rows: 1250
      }
    },
    {
      id: '5',
      timestamp: '2023-06-15T10:50:00Z',
      level: 'debug',
      category: 'config',
      message: 'Configuration updated',
      user: 'admin',
      ip: '*************',
      details: {
        changes: {
          'site.name': 'Old Value -> New Value',
          'security.maxLoginAttempts': '5 -> 3'
        }
      }
    }
  ];
  
  return JSON.stringify({ logs }, null, 2);
}

/**
 * 生成模拟 XML 内容
 * @returns 模拟 XML 内容
 */
function generateMockXmlContent(): string {
  return `<?xml version="1.0" encoding="UTF-8"?>
<logs>
  <log>
    <id>1</id>
    <timestamp>2023-06-15T10:30:00Z</timestamp>
    <level>info</level>
    <category>login</category>
    <message>User login successful</message>
    <user>user123</user>
    <ip>*************</ip>
    <details>
      <browser>Chrome 114.0.5735.198</browser>
      <os>Windows 10</os>
      <location>Beijing, China</location>
    </details>
  </log>
  <log>
    <id>2</id>
    <timestamp>2023-06-15T10:35:00Z</timestamp>
    <level>warn</level>
    <category>access</category>
    <message>Unauthorized access attempt</message>
    <user>user456</user>
    <ip>*************</ip>
    <details>
      <resource>/api/admin/users</resource>
      <method>GET</method>
    </details>
  </log>
  <log>
    <id>3</id>
    <timestamp>2023-06-15T10:40:00Z</timestamp>
    <level>error</level>
    <category>system</category>
    <message>Database connection failed</message>
    <user>system</user>
    <ip>*************</ip>
    <details>
      <error>Connection timeout</error>
      <database>users</database>
    </details>
  </log>
</logs>`;
}

/**
 * 获取导出格式列表
 * @returns 导出格式列表
 */
export function getExportFormats(): { value: ExportFormat; label: string; icon: string }[] {
  return [
    { value: 'csv', label: 'CSV', icon: 'FileText' },
    { value: 'json', label: 'JSON', icon: 'Braces' },
    { value: 'xml', label: 'XML', icon: 'Code' },
    { value: 'excel', label: 'Excel', icon: 'Table' },
    { value: 'pdf', label: 'PDF', icon: 'FileType' }
  ];
}

/**
 * 获取日志级别列表
 * @returns 日志级别列表
 */
export function getLogLevels(): { value: LogLevel; label: string; color: string }[] {
  return [
    { value: 'all', label: '全部级别', color: 'gray' },
    { value: 'debug', label: '调试', color: 'blue' },
    { value: 'info', label: '信息', color: 'green' },
    { value: 'warn', label: '警告', color: 'yellow' },
    { value: 'error', label: '错误', color: 'red' }
  ];
}

/**
 * 获取日志类别列表
 * @returns 日志类别列表
 */
export function getLogCategories(): { value: LogCategory; label: string }[] {
  return [
    { value: 'all', label: '全部类别' },
    { value: 'login', label: '登录' },
    { value: 'access', label: '访问' },
    { value: 'data', label: '数据' },
    { value: 'system', label: '系统' },
    { value: 'config', label: '配置' }
  ];
}
