/**
 * 序列号生成工具函数
 */

/**
 * 生成序列号
 * @param db D1数据库
 * @param prefix 前缀
 * @returns 序列号
 */
export async function generateSequenceNumber(db: D1Database, prefix: string): Promise<string> {
  // 获取当前年份
  const year = new Date().getFullYear();
  
  // 查询当前年份的最大序列号
  const result = await db.prepare(`
    SELECT MAX(sequence_number) as max_sequence
    FROM (
      SELECT sequence_number FROM stories WHERE sequence_number LIKE ?
      UNION ALL
      SELECT sequence_number FROM surveys WHERE sequence_number LIKE ?
    )
  `).bind(`${prefix}-${year}-%`, `${prefix}-${year}-%`).first();
  
  let sequenceNumber = 1;
  
  if (result && result.max_sequence) {
    // 提取序列号
    const match = (result.max_sequence as string).match(/(\d+)$/);
    
    if (match) {
      sequenceNumber = parseInt(match[1], 10) + 1;
    }
  }
  
  // 格式化序列号
  return `${prefix}-${year}-${sequenceNumber.toString().padStart(5, '0')}`;
}
