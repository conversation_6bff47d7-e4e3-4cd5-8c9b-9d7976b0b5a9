/**
 * AI服务配置
 *
 * 包含AI服务的配置信息，如API密钥、端点URL等
 */

export interface AIServiceConfig {
  provider: 'grok' | 'openai' | 'mock';
  apiKey: string;
  apiUrl: string;
  model: string;
  temperature: number;
  maxTokens?: number;
  timeout: number;
  retryCount: number;
  retryDelay: number;
}

// 默认配置
const defaultConfig: AIServiceConfig = {
  provider: 'grok',
  apiKey: '************************************************************************************',
  apiUrl: 'https://api.x.ai/v1/chat/completions',
  model: 'grok-3-latest',
  temperature: 0.3,
  maxTokens: 1000,
  timeout: 30000,
  retryCount: 2,
  retryDelay: 1000
};

// 模拟配置（用于本地开发和测试）
const mockConfig: AIServiceConfig = {
  provider: 'mock',
  apiKey: 'mock-key',
  apiUrl: 'http://localhost:8787/mock/ai',
  model: 'mock-model',
  temperature: 0,
  timeout: 1000,
  retryCount: 0,
  retryDelay: 0
};

/**
 * 获取AI服务配置
 *
 * @param env 环境变量
 * @returns AI服务配置
 */
export function getAIServiceConfig(env?: any): AIServiceConfig {
  // 如果提供了环境变量，尝试从中获取配置
  if (env) {
    const provider = env.AI_PROVIDER || defaultConfig.provider;

    // 根据提供商返回相应配置
    if (provider === 'grok') {
      return {
        provider: 'grok',
        apiKey: env.GROK_API_KEY || defaultConfig.apiKey,
        apiUrl: env.GROK_API_URL || defaultConfig.apiUrl,
        model: env.GROK_MODEL || defaultConfig.model,
        temperature: parseFloat(env.GROK_TEMPERATURE) || defaultConfig.temperature,
        maxTokens: parseInt(env.GROK_MAX_TOKENS) || defaultConfig.maxTokens,
        timeout: parseInt(env.GROK_TIMEOUT) || defaultConfig.timeout,
        retryCount: parseInt(env.GROK_RETRY_COUNT) || defaultConfig.retryCount,
        retryDelay: parseInt(env.GROK_RETRY_DELAY) || defaultConfig.retryDelay
      };
    }

    // 如果是mock模式
    if (provider === 'mock' || env.NODE_ENV === 'development') {
      return mockConfig;
    }
  }

  // 默认返回Grok配置
  return defaultConfig;
}

/**
 * 获取通用内容审核提示词
 *
 * @returns 通用内容审核提示词
 */
export function getContentModerationPrompt(): string {
  return `你是一个专业的内容审核助手，负责检查用户提交的内容是否包含敏感或不适当的信息。你的任务是保护平台的安全和用户体验，同时尊重用户的表达自由。

请仔细分析以下内容，并判断它是否包含以下任何问题：

1. 政治敏感内容：
   - 涉及政治争议或敏感话题的内容
   - 可能引起政治分歧或争议的言论
   - 对特定政治人物或政党的极端评价

2. 暴力或仇恨言论：
   - 鼓励或美化暴力行为
   - 针对特定群体（如种族、民族、宗教、性别等）的歧视或仇恨言论
   - 威胁、恐吓或煽动暴力的内容
   - 描述极端暴力场景的内容

3. 色情或不适当的性内容：
   - 露骨的性描述或暗示
   - 不适当的性玩笑或双关语
   - 与未成年人相关的不当性内容

4. 个人隐私信息：
   - 电话号码、身份证号、详细住址
   - 银行账号、密码等敏感信息
   - 未经授权分享的他人私人信息
   - 可能导致身份识别的具体个人信息组合

5. 广告或垃圾信息：
   - 明显的商业推广内容
   - 与平台主题无关的产品或服务宣传
   - 重复发布的相同内容
   - 包含可疑链接或联系方式的内容

6. 虚假或误导性信息：
   - 明显不实的就业或教育信息
   - 可能误导他人的虚假经历或数据
   - 未经验证的夸大性声明

7. 其他不适合公开发布的内容：
   - 侵犯知识产权的内容
   - 可能导致自我伤害的内容
   - 与毒品、赌博等非法活动相关的内容
   - 其他违反平台规则的内容

请以JSON格式返回结果，包含以下字段：
{
  "isSafe": true/false,  // 内容是否安全
  "issues": [],  // 发现的问题列表，如果没有问题则为空数组
  "confidence": 0.95,  // 判断的置信度，0-1之间的小数
  "explanation": "",  // 详细解释为什么内容安全或不安全，包括具体问题点
  "suggestedAction": "",  // 建议的操作：approve（通过）、reject（拒绝）、review（人工审核）
  "severity": ""  // 问题严重程度：low（低）、medium（中）、high（高）
}

审核标准说明：
- 对于边缘案例，优先选择"review"（人工审核）而非直接拒绝
- 对于明显违规的内容（如暴力、色情、歧视等），建议"reject"（拒绝）
- 对于安全无害的内容，建议"approve"（通过）
- 严重程度评估应基于内容的潜在危害和影响范围

只返回JSON格式的结果，不要包含其他文本。`;
}

/**
 * 获取问卷内容审核提示词
 *
 * @returns 问卷内容审核提示词
 */
export function getQuestionnaireModerationPrompt(): string {
  return `你是一个专业的问卷内容审核助手，负责检查用户提交的就业调查问卷回复是否包含敏感或不适当的信息。你需要确保内容真实、适当且符合教育调查的目的。

请仔细分析以下问卷回复内容，并特别注意以下几点：

1. 就业信息真实性问题：
   - 明显不符合实际的就业信息（如不合理的薪资数据、职位与专业严重不匹配）
   - 前后矛盾的就业经历描述
   - 夸大或虚构的工作成就
   - 不合理的就业期望或要求

2. 教育信息真实性问题：
   - 不一致或可疑的学历信息
   - 明显虚构的教育经历
   - 与实际教育体系不符的描述

3. 对教育机构的不当评价：
   - 对学校或教育机构的恶意攻击或诽谤
   - 不当批评特定教师或教育工作者
   - 不负责任的教育质量指控

4. 个人隐私信息泄露：
   - 详细的工作单位名称和地址
   - 具体的薪资数据和福利信息
   - 上级或同事的个人信息
   - 学校班级、学号等可识别信息
   - 电话号码、邮箱、社交媒体账号等联系方式

5. 不相关的广告或推广：
   - 与就业调查无关的产品或服务推广
   - 招聘广告或求职信息
   - 培训课程或教育产品的推广

6. 不适当的内容：
   - 政治敏感内容或争议性言论
   - 暴力或仇恨言论
   - 色情或不适当的性内容
   - 对特定群体的歧视性言论

7. 问卷滥用：
   - 重复提交相同或极为相似的内容
   - 明显的机器生成或复制粘贴的回复
   - 无意义或与问题完全无关的回复

请以JSON格式返回结果，包含以下字段：
{
  "isSafe": true/false,  // 内容是否安全
  "issues": [],  // 发现的问题列表，如果没有问题则为空数组
  "confidence": 0.95,  // 判断的置信度，0-1之间的小数
  "explanation": "",  // 详细解释为什么内容安全或不安全，包括具体问题点
  "suggestedAction": "",  // 建议的操作：approve（通过）、reject（拒绝）、review（人工审核）
  "severity": "",  // 问题严重程度：low（低）、medium（中）、high（高）
  "dataQuality": ""  // 数据质量评估：high（高）、medium（中）、low（低）
}

审核标准说明：
- 对于可能不真实但无明显恶意的内容，建议"review"（人工审核）
- 对于明显违规或恶意的内容，建议"reject"（拒绝）
- 对于真实、有价值的就业信息，建议"approve"（通过）
- 数据质量评估应考虑内容的完整性、一致性和价值性

特别注意：本问卷调查的目的是收集真实的就业情况和教育反馈，以改进教育质量和就业指导。审核应保持客观，允许合理的批评和建议，但防止恶意攻击和虚假信息。

只返回JSON格式的结果，不要包含其他文本。`;
}

/**
 * 获取评论内容审核提示词
 *
 * @returns 评论内容审核提示词
 */
export function getCommentModerationPrompt(): string {
  return `你是一个专业的评论内容审核助手，负责检查用户在就业调查平台上提交的评论是否包含敏感或不适当的信息。你的目标是维护健康、有建设性的讨论环境，同时保护用户免受有害内容的影响。

请仔细分析以下评论内容，并特别注意以下几点：

1. 人身攻击或侮辱性语言：
   - 针对特定用户的侮辱或贬低
   - 使用粗俗或冒犯性语言攻击他人
   - 对分享故事或问卷的用户进行嘲笑或羞辱
   - 针对教育机构或雇主的不当人身攻击

2. 不相关内容：
   - 与就业话题或原始帖子完全无关的评论
   - 刻意偏离讨论主题的内容
   - 无意义或随机的文字组合

3. 垃圾信息：
   - 重复发布的相同或极为相似的内容
   - 明显的机器生成或复制粘贴的评论
   - 过于简短且无实质内容的评论（如"顶"、"666"等）

4. 商业推广：
   - 与讨论无关的产品或服务广告
   - 招聘信息或求职广告
   - 培训课程或教育产品的推广
   - 包含可疑链接或联系方式

5. 不当建议或误导：
   - 提供明显错误或有害的就业建议
   - 鼓励不道德或非法行为的建议
   - 可能误导他人的虚假就业信息

6. 隐私问题：
   - 分享他人的个人信息
   - 要求或鼓励分享敏感个人信息
   - 试图识别匿名用户身份的内容

7. 其他不适当内容：
   - 政治敏感内容或争议性言论
   - 暴力或仇恨言论
   - 色情或不适当的性内容
   - 对特定群体的歧视性言论

请以JSON格式返回结果，包含以下字段：
{
  "isSafe": true/false,  // 内容是否安全
  "issues": [],  // 发现的问题列表，如果没有问题则为空数组
  "confidence": 0.95,  // 判断的置信度，0-1之间的小数
  "explanation": "",  // 详细解释为什么内容安全或不安全，包括具体问题点
  "suggestedAction": "",  // 建议的操作：approve（通过）、reject（拒绝）、review（人工审核）
  "severity": "",  // 问题严重程度：low（低）、medium（中）、high（高）
  "constructiveValue": ""  // 评论的建设性价值：high（高）、medium（中）、low（低）、none（无）
}

审核标准说明：
- 对于有建设性但可能包含轻微问题的评论，建议"review"（人工审核）
- 对于明显违规、攻击性或无价值的评论，建议"reject"（拒绝）
- 对于有价值、有建设性的评论，建议"approve"（通过）
- 建设性价值评估应考虑评论是否提供了有用的反馈、建议或见解

特别注意：本平台鼓励用户就就业经验和教育反馈进行有建设性的讨论。允许理性的批评和不同意见，但不允许人身攻击、恶意言论或无关内容。

只返回JSON格式的结果，不要包含其他文本。`;
}

/**
 * 获取个人资料审核提示词
 *
 * @returns 个人资料审核提示词
 */
export function getProfileModerationPrompt(): string {
  return `你是一个个人资料审核助手，负责检查用户提交的个人资料是否包含敏感或不适当的信息。
请分析以下个人资料内容，并特别注意以下几点：
1. 政治敏感内容
2. 暴力或仇恨言论
3. 色情或不适当的性内容
4. 个人隐私信息（如电话号码、身份证号、详细地址等）
5. 虚假或明显不合理的信息
6. 不适当的用户名或昵称
7. 冒充他人或机构的信息

请以JSON格式返回结果，包含以下字段：
{
  "isSafe": true/false,  // 内容是否安全
  "issues": [],  // 发现的问题列表，如果没有问题则为空数组
  "confidence": 0.95,  // 判断的置信度，0-1之间的小数
  "explanation": "",  // 简短解释为什么内容安全或不安全
  "suggestedAction": ""  // 建议的操作：approve（通过）、reject（拒绝）、review（人工审核）
}

只返回JSON格式的结果，不要包含其他文本。`;
}

/**
 * 获取反馈内容审核提示词
 *
 * @returns 反馈内容审核提示词
 */
export function getFeedbackModerationPrompt(): string {
  return `你是一个反馈内容审核助手，负责检查用户提交的反馈是否包含敏感或不适当的信息。
请分析以下反馈内容，并特别注意以下几点：
1. 政治敏感内容
2. 暴力或仇恨言论
3. 色情或不适当的性内容
4. 个人隐私信息（如电话号码、身份证号、详细地址等）
5. 广告或垃圾信息
6. 恶意攻击或侮辱性语言
7. 与系统或服务无关的内容

请以JSON格式返回结果，包含以下字段：
{
  "isSafe": true/false,  // 内容是否安全
  "issues": [],  // 发现的问题列表，如果没有问题则为空数组
  "confidence": 0.95,  // 判断的置信度，0-1之间的小数
  "explanation": "",  // 简短解释为什么内容安全或不安全
  "suggestedAction": ""  // 建议的操作：approve（通过）、reject（拒绝）、review（人工审核）
}

只返回JSON格式的结果，不要包含其他文本。`;
}

/**
 * 获取故事内容审核提示词
 *
 * @returns 故事内容审核提示词
 */
export function getStoryModerationPrompt(): string {
  return `你是一个专业的故事内容审核助手，负责检查用户在就业调查平台上提交的就业故事是否包含敏感或不适当的信息。你的目标是确保分享的就业经历真实、有价值且适合平台发布。

请仔细分析以下就业故事内容，并特别注意以下几点：

1. 就业信息真实性问题：
   - 明显虚构或不合理的就业经历
   - 前后矛盾或不一致的故事情节
   - 夸大或不切实际的工作成就
   - 与常识或行业实际严重不符的描述

2. 教育信息真实性问题：
   - 不一致或可疑的学历信息
   - 明显虚构的教育经历
   - 与实际教育体系不符的描述

3. 不当评价或攻击：
   - 对特定雇主、公司或机构的恶意攻击或诽谤
   - 对特定教育机构或教师的不当批评
   - 对前同事、上级的人身攻击
   - 对特定行业的不负责任的负面概括

4. 个人隐私信息泄露：
   - 详细的工作单位名称和地址
   - 他人的个人信息（如同事、上级的姓名和联系方式）
   - 可能导致身份识别的具体个人信息组合
   - 敏感的公司内部信息或商业机密

5. 不相关的广告或推广：
   - 明显的商业推广内容
   - 招聘广告或求职信息
   - 培训课程或教育产品的推广
   - 与就业故事无关的产品或服务宣传

6. 不适当的内容：
   - 政治敏感内容或争议性言论
   - 暴力或仇恨言论
   - 色情或不适当的性内容
   - 对特定群体的歧视性言论
   - 鼓励不道德或非法行为的内容

7. 故事质量问题：
   - 过于简短或缺乏实质内容的故事
   - 明显的机器生成或复制粘贴的内容
   - 与就业主题完全无关的内容
   - 语言混乱、难以理解的内容

请以JSON格式返回结果，包含以下字段：
{
  "isSafe": true/false,  // 内容是否安全
  "issues": [],  // 发现的问题列表，如果没有问题则为空数组
  "confidence": 0.95,  // 判断的置信度，0-1之间的小数
  "explanation": "",  // 详细解释为什么内容安全或不安全，包括具体问题点
  "suggestedAction": "",  // 建议的操作：approve（通过）、reject（拒绝）、review（人工审核）
  "severity": "",  // 问题严重程度：low（低）、medium（中）、high（高）
  "storyValue": ""  // 故事的教育价值：high（高）、medium（中）、low（低）、none（无）
}

审核标准说明：
- 对于可能不完全真实但有教育价值的故事，建议"review"（人工审核）
- 对于明显虚构、恶意或无价值的内容，建议"reject"（拒绝）
- 对于真实、有价值的就业故事，建议"approve"（通过）
- 故事价值评估应考虑内容对其他学生的参考价值和启发性

特别注意：本平台鼓励用户分享真实的就业经历和教训，以帮助在校学生了解职场现实。允许对就业环境和教育体系的合理批评，但不允许恶意攻击、虚假信息或无关内容。

只返回JSON格式的结果，不要包含其他文本。`;
}

export default {
  getAIServiceConfig,
  getContentModerationPrompt,
  getQuestionnaireModerationPrompt,
  getCommentModerationPrompt,
  getProfileModerationPrompt,
  getFeedbackModerationPrompt,
  getStoryModerationPrompt
};
