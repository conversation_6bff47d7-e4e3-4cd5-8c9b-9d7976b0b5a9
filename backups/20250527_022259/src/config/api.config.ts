/**
 * API配置管理
 * 统一管理不同环境下的API配置
 */

export interface ApiConfig {
  baseURL: string;
  timeout: number;
  enableLogging: boolean;
  enableMock: boolean;
  retryAttempts: number;
  retryDelay: number;
}

export interface EnvironmentConfig {
  name: string;
  api: ApiConfig;
  features: {
    analytics: boolean;
    debugging: boolean;
    hotReload: boolean;
    mockData: boolean;
  };
}

// 环境检测
const getEnvironment = (): 'local' | 'staging' | 'production' => {
  const nodeEnv = import.meta.env.VITE_NODE_ENV;
  const apiBaseUrl = import.meta.env.VITE_API_BASE_URL;
  
  if (nodeEnv === 'development' || apiBaseUrl?.includes('localhost')) {
    return 'local';
  }
  
  if (nodeEnv === 'staging' || apiBaseUrl?.includes('staging')) {
    return 'staging';
  }
  
  return 'production';
};

// 环境配置
const environments: Record<string, EnvironmentConfig> = {
  local: {
    name: '本地开发',
    api: {
      baseURL: import.meta.env.VITE_API_BASE_URL || 'http://localhost:8788',
      timeout: 10000,
      enableLogging: true,
      enableMock: import.meta.env.VITE_USE_MOCK === 'true',
      retryAttempts: 2,
      retryDelay: 1000,
    },
    features: {
      analytics: false,
      debugging: true,
      hotReload: true,
      mockData: import.meta.env.VITE_ENABLE_MOCK_DATA === 'true',
    },
  },
  
  staging: {
    name: '预发布环境',
    api: {
      baseURL: import.meta.env.VITE_API_BASE_URL || 'https://college-employment-survey.aibook2099.workers.dev',
      timeout: 15000,
      enableLogging: true,
      enableMock: false,
      retryAttempts: 3,
      retryDelay: 2000,
    },
    features: {
      analytics: true,
      debugging: true,
      hotReload: false,
      mockData: false,
    },
  },
  
  production: {
    name: '生产环境',
    api: {
      baseURL: import.meta.env.VITE_API_BASE_URL || 'https://college-employment-survey.aibook2099.workers.dev',
      timeout: 20000,
      enableLogging: false,
      enableMock: false,
      retryAttempts: 3,
      retryDelay: 3000,
    },
    features: {
      analytics: true,
      debugging: false,
      hotReload: false,
      mockData: false,
    },
  },
};

// 获取当前环境配置
export const getCurrentEnvironment = (): string => {
  return getEnvironment();
};

export const getCurrentConfig = (): EnvironmentConfig => {
  const env = getEnvironment();
  return environments[env];
};

// API URL构建器
export const buildApiUrl = (endpoint: string): string => {
  const config = getCurrentConfig();
  const baseURL = config.api.baseURL;
  
  // 确保endpoint以/开头
  const normalizedEndpoint = endpoint.startsWith('/') ? endpoint : `/${endpoint}`;
  
  // 如果是完整URL，直接返回
  if (endpoint.startsWith('http')) {
    return endpoint;
  }
  
  return `${baseURL}${normalizedEndpoint}`;
};

// 日志工具
export const apiLogger = {
  log: (message: string, data?: any) => {
    const config = getCurrentConfig();
    if (config.api.enableLogging) {
      console.log(`[API] ${message}`, data || '');
    }
  },
  
  error: (message: string, error?: any) => {
    const config = getCurrentConfig();
    if (config.api.enableLogging) {
      console.error(`[API Error] ${message}`, error || '');
    }
  },
  
  warn: (message: string, data?: any) => {
    const config = getCurrentConfig();
    if (config.api.enableLogging) {
      console.warn(`[API Warning] ${message}`, data || '');
    }
  },
};

// 环境信息显示
export const logEnvironmentInfo = () => {
  const env = getEnvironment();
  const config = getCurrentConfig();
  
  console.log(`🌍 当前环境: ${config.name} (${env})`);
  console.log(`🔗 API地址: ${config.api.baseURL}`);
  console.log(`🔧 调试模式: ${config.features.debugging ? '开启' : '关闭'}`);
  console.log(`📊 分析统计: ${config.features.analytics ? '开启' : '关闭'}`);
  
  if (config.features.debugging) {
    console.log('🔍 环境详细信息:', {
      environment: env,
      config: config,
      envVars: {
        VITE_NODE_ENV: import.meta.env.VITE_NODE_ENV,
        VITE_API_BASE_URL: import.meta.env.VITE_API_BASE_URL,
        VITE_USE_MOCK: import.meta.env.VITE_USE_MOCK,
      }
    });
  }
};

export default {
  getCurrentEnvironment,
  getCurrentConfig,
  buildApiUrl,
  apiLogger,
  logEnvironmentInfo,
};
