import React, { Component, ErrorInfo, ReactNode } from 'react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';

interface Props {
  children: ReactNode;
  fallback?: ReactNode;
}

interface State {
  hasError: boolean;
  error: Error | null;
  errorInfo: ErrorInfo | null;
}

/**
 * 错误边界组件
 * 
 * 捕获子组件树中的 JavaScript 错误，记录错误并显示备用 UI
 */
class ErrorBoundary extends Component<Props, State> {
  public state: State = {
    hasError: false,
    error: null,
    errorInfo: null
  };

  public static getDerivedStateFromError(error: Error): State {
    // 更新 state，下次渲染时显示备用 UI
    return { hasError: true, error, errorInfo: null };
  }

  public componentDidCatch(error: Error, errorInfo: ErrorInfo): void {
    // 记录错误信息
    console.error('错误边界捕获到错误:', error, errorInfo);
    this.setState({ errorInfo });
  }

  private handleReload = (): void => {
    window.location.reload();
  };

  private handleGoHome = (): void => {
    window.location.href = '/';
  };

  public render(): ReactNode {
    if (this.state.hasError) {
      // 如果提供了自定义的 fallback，则使用它
      if (this.props.fallback) {
        return this.props.fallback;
      }

      // 否则使用默认的错误 UI
      return (
        <div className="flex items-center justify-center min-h-screen bg-gray-50">
          <Card className="w-full max-w-md">
            <CardHeader>
              <CardTitle className="text-red-600">出错了</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <p>抱歉，应用程序遇到了一个错误。</p>
                {this.state.error && (
                  <div className="bg-gray-100 p-4 rounded-md overflow-auto text-sm">
                    <p className="font-semibold">错误信息:</p>
                    <p className="text-red-600">{this.state.error.toString()}</p>
                  </div>
                )}
                {process.env.NODE_ENV === 'development' && this.state.errorInfo && (
                  <div className="bg-gray-100 p-4 rounded-md overflow-auto text-xs">
                    <p className="font-semibold">组件堆栈:</p>
                    <pre>{this.state.errorInfo.componentStack}</pre>
                  </div>
                )}
              </div>
            </CardContent>
            <CardFooter className="flex justify-between">
              <Button variant="outline" onClick={this.handleGoHome}>
                返回首页
              </Button>
              <Button onClick={this.handleReload}>
                重新加载
              </Button>
            </CardFooter>
          </Card>
        </div>
      );
    }

    // 正常情况下渲染子组件
    return this.props.children;
  }
}

export default ErrorBoundary;
