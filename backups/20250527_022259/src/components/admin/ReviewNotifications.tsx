import React, { useState, useEffect } from 'react';
import { useToast } from '@/components/ui/use-toast';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '@/components/ui/popover';
import {
  Bell,
  BellRing,
  CheckCircle,
  XCircle,
  Clock,
  AlertCircle,
  AlertTriangle,
  Eye
} from 'lucide-react';
import { formatDistanceToNow } from 'date-fns';
import { zhCN } from 'date-fns/locale';
import { useNavigate } from 'react-router-dom';

// 导入数据服务
import { getPendingContents } from '@/services/dataService';

// 通知类型
interface Notification {
  id: string;
  title: string;
  message: string;
  type: 'info' | 'warning' | 'error';
  timestamp: string;
  read: boolean;
  link?: string;
}

// 审核通知组件
export default function ReviewNotifications() {
  const { toast } = useToast();
  const navigate = useNavigate();
  const [notifications, setNotifications] = useState<Notification[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [isOpen, setIsOpen] = useState(false);
  const [lastCheckTime, setLastCheckTime] = useState<Date>(new Date());
  
  // 获取通知
  const fetchNotifications = async () => {
    try {
      setIsLoading(true);
      
      // 获取待审核内容
      const response = await getPendingContents('flag=high-exposure&flag=sensitive-words');
      
      if (response.success) {
        // 将待审核内容转换为通知
        const newNotifications: Notification[] = response.pendingContents.map((content: any) => {
          // 确定通知类型
          let type: 'info' | 'warning' | 'error' = 'info';
          
          if (content.flags.includes('high-exposure')) {
            type = 'error';
          } else if (content.flags.includes('sensitive-words')) {
            type = 'warning';
          }
          
          // 创建通知
          return {
            id: content.id,
            title: content.type === 'story' ? '新故事需要审核' : '新问卷需要审核',
            message: content.type === 'story' 
              ? `标题: ${content.originalContent.title}` 
              : `问卷回复 #${content.sequenceNumber}`,
            type,
            timestamp: content.createdAt,
            read: false,
            link: `/admin/content-review?id=${content.id}`
          };
        });
        
        // 更新通知列表
        setNotifications(prev => {
          // 合并新通知和旧通知，去重
          const merged = [...newNotifications, ...prev];
          const unique = merged.filter((notification, index, self) => 
            index === self.findIndex(n => n.id === notification.id)
          );
          
          // 按时间排序
          return unique.sort((a, b) => 
            new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime()
          );
        });
        
        // 检查是否有新通知
        const newNotificationsCount = newNotifications.filter(
          n => new Date(n.timestamp) > lastCheckTime
        ).length;
        
        // 如果有新通知，显示提示
        if (newNotificationsCount > 0) {
          toast({
            title: '新的待审核内容',
            description: `有 ${newNotificationsCount} 个新的内容需要审核`,
            variant: 'default'
          });
          
          // 播放提示音
          const audio = new Audio('/notification.mp3');
          audio.play().catch(e => console.log('无法播放提示音:', e));
        }
        
        // 更新最后检查时间
        setLastCheckTime(new Date());
      }
    } catch (error) {
      console.error('获取通知错误:', error);
    } finally {
      setIsLoading(false);
    }
  };
  
  // 标记通知为已读
  const markAsRead = (id: string) => {
    setNotifications(prev => 
      prev.map(notification => 
        notification.id === id 
          ? { ...notification, read: true } 
          : notification
      )
    );
  };
  
  // 标记所有通知为已读
  const markAllAsRead = () => {
    setNotifications(prev => 
      prev.map(notification => ({ ...notification, read: true }))
    );
  };
  
  // 清除所有通知
  const clearAllNotifications = () => {
    setNotifications([]);
  };
  
  // 处理通知点击
  const handleNotificationClick = (notification: Notification) => {
    // 标记为已读
    markAsRead(notification.id);
    
    // 关闭弹出框
    setIsOpen(false);
    
    // 如果有链接，导航到链接
    if (notification.link) {
      navigate(notification.link);
    }
  };
  
  // 组件加载时获取通知
  useEffect(() => {
    fetchNotifications();
    
    // 设置定时器，每5分钟检查一次
    const interval = setInterval(fetchNotifications, 5 * 60 * 1000);
    
    return () => {
      clearInterval(interval);
    };
  }, []);
  
  // 获取未读通知数量
  const unreadCount = notifications.filter(n => !n.read).length;
  
  return (
    <Popover open={isOpen} onOpenChange={setIsOpen}>
      <PopoverTrigger asChild>
        <Button variant="outline" size="icon" className="relative">
          {unreadCount > 0 ? (
            <>
              <BellRing className="h-5 w-5" />
              <span className="absolute -top-1 -right-1 bg-red-500 text-white text-xs rounded-full h-5 w-5 flex items-center justify-center">
                {unreadCount}
              </span>
            </>
          ) : (
            <Bell className="h-5 w-5" />
          )}
        </Button>
      </PopoverTrigger>
      <PopoverContent className="w-80 p-0" align="end">
        <div className="flex items-center justify-between p-3 border-b">
          <h3 className="font-medium">审核通知</h3>
          <div className="flex space-x-1">
            <Button variant="ghost" size="sm" onClick={fetchNotifications} disabled={isLoading}>
              <Clock className="h-4 w-4" />
            </Button>
            <Button variant="ghost" size="sm" onClick={markAllAsRead} disabled={unreadCount === 0}>
              <CheckCircle className="h-4 w-4" />
            </Button>
            <Button variant="ghost" size="sm" onClick={clearAllNotifications} disabled={notifications.length === 0}>
              <XCircle className="h-4 w-4" />
            </Button>
          </div>
        </div>
        
        <div className="max-h-80 overflow-y-auto">
          {notifications.length === 0 ? (
            <div className="p-4 text-center text-muted-foreground">
              <Bell className="h-8 w-8 mx-auto mb-2 opacity-50" />
              <p>暂无通知</p>
            </div>
          ) : (
            <div>
              {notifications.map(notification => (
                <div 
                  key={notification.id}
                  className={`p-3 border-b hover:bg-muted/50 cursor-pointer ${notification.read ? 'opacity-70' : 'bg-muted/20'}`}
                  onClick={() => handleNotificationClick(notification)}
                >
                  <div className="flex items-start">
                    <div className="mr-2 mt-0.5">
                      {notification.type === 'info' && <AlertCircle className="h-4 w-4 text-blue-500" />}
                      {notification.type === 'warning' && <AlertTriangle className="h-4 w-4 text-amber-500" />}
                      {notification.type === 'error' && <AlertCircle className="h-4 w-4 text-red-500" />}
                    </div>
                    <div className="flex-1">
                      <div className="flex items-center justify-between">
                        <p className="font-medium text-sm">{notification.title}</p>
                        <Badge variant="outline" className="text-xs">
                          {formatDistanceToNow(new Date(notification.timestamp), { addSuffix: true, locale: zhCN })}
                        </Badge>
                      </div>
                      <p className="text-sm text-muted-foreground mt-1">{notification.message}</p>
                      <div className="flex items-center mt-2 text-xs text-muted-foreground">
                        <Eye className="h-3 w-3 mr-1" />
                        <span>点击查看详情</span>
                      </div>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          )}
        </div>
        
        <div className="p-2 border-t">
          <Button 
            variant="outline" 
            size="sm" 
            className="w-full"
            onClick={() => {
              setIsOpen(false);
              navigate('/admin/content-review');
            }}
          >
            查看所有待审核内容
          </Button>
        </div>
      </PopoverContent>
    </Popover>
  );
}
