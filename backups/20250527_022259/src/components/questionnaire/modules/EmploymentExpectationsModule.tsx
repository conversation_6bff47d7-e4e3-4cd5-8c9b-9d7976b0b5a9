import { useEffect, useState } from 'react';
import { z } from 'zod';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { 
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage 
} from '@/components/ui/form';
import { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group';
import { 
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue
} from '@/components/ui/select';
import { Input } from '@/components/ui/input';
import { Checkbox } from '@/components/ui/checkbox';
import QuestionItem from '../QuestionItem';
import AnswerStats from '../AnswerStats';
import { QuestionnaireData } from '@/lib/api';

// Define the form schema
const formSchema = z.object({
  expectedPosition: z.string().min(1, { message: '请选择期望岗位' }),
  expectedSalaryRange: z.string().min(1, { message: '请选择期望薪资范围' }),
  expectedWorkHours: z.string().min(1, { message: '请选择期望工作时长' }),
  expectedVacationDays: z.string().min(1, { message: '请选择期望休假天数' }),
  priorities: z.array(z.string()).min(1, { message: '请至少选择一项您最看重的方面' }).max(3, { message: '最多选择3项' }),
});

// Mock statistics data
const mockStats = {
  expectedPosition: [
    { label: '技术研发', percentage: 30 },
    { label: '产品运营', percentage: 20 },
    { label: '市场营销', percentage: 15 },
    { label: '人力资源', percentage: 10 },
    { label: '财务会计', percentage: 10 },
    { label: '其他', percentage: 15 },
  ],
  expectedSalaryRange: [
    { label: '3000-5000元', percentage: 5 },
    { label: '5000-8000元', percentage: 15 },
    { label: '8000-12000元', percentage: 30 },
    { label: '12000-20000元', percentage: 35 },
    { label: '20000元以上', percentage: 15 },
  ],
  expectedWorkHours: [
    { label: '标准工作制', percentage: 45 },
    { label: '弹性工作制', percentage: 30 },
    { label: '996工作制', percentage: 15 },
    { label: '大小周', percentage: 10 },
  ],
  priorities: [
    { label: '薪资待遇', percentage: 35 },
    { label: '工作环境', percentage: 15 },
    { label: '发展空间', percentage: 20 },
    { label: '工作稳定性', percentage: 15 },
    { label: '工作与生活平衡', percentage: 15 },
  ],
};

interface EmploymentExpectationsModuleProps {
  data: Partial<QuestionnaireData>;
  onUpdate: (data: Partial<QuestionnaireData>) => void;
}

export default function EmploymentExpectationsModule({ 
  data, 
  onUpdate 
}: EmploymentExpectationsModuleProps) {
  // Set up form with default values from data
  const form = useForm<z.infer<typeof formSchema>>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      expectedPosition: data.expectedPosition || '',
      expectedSalaryRange: data.expectedSalaryRange || '',
      expectedWorkHours: data.expectedWorkHours?.toString() || '',
      expectedVacationDays: data.expectedVacationDays?.toString() || '',
      priorities: [],
    },
  });
  
  // Track selected options for stats display
  const [selectedOptions, setSelectedOptions] = useState({
    expectedPosition: data.expectedPosition || '',
    expectedSalaryRange: data.expectedSalaryRange || '',
    expectedWorkHours: data.expectedWorkHours?.toString() || '',
    priorities: [] as string[],
  });
  
  // Update stats when form values change
  useEffect(() => {
    const subscription = form.watch((value) => {
      setSelectedOptions({
        expectedPosition: value.expectedPosition || '',
        expectedSalaryRange: value.expectedSalaryRange || '',
        expectedWorkHours: value.expectedWorkHours || '',
        priorities: value.priorities || [],
      });
      
      // Update parent component with form values
      if (value.expectedPosition && value.expectedSalaryRange && 
          value.expectedWorkHours && value.expectedVacationDays) {
        onUpdate({
          expectedPosition: value.expectedPosition,
          expectedSalaryRange: value.expectedSalaryRange,
          expectedWorkHours: parseInt(value.expectedWorkHours),
          expectedVacationDays: parseInt(value.expectedVacationDays),
        });
      }
    });
    
    return () => subscription.unsubscribe();
  }, [form, onUpdate]);
  
  // Prepare stats data with selected indicators
  const positionStats = mockStats.expectedPosition.map(stat => ({
    ...stat,
    isSelected: stat.label === selectedOptions.expectedPosition,
  }));
  
  const salaryStats = mockStats.expectedSalaryRange.map(stat => ({
    ...stat,
    isSelected: stat.label === selectedOptions.expectedSalaryRange,
  }));
  
  const workHoursStats = mockStats.expectedWorkHours.map(stat => ({
    ...stat,
    isSelected: stat.label === selectedOptions.expectedWorkHours,
  }));
  
  const prioritiesStats = mockStats.priorities.map(stat => ({
    ...stat,
    isSelected: selectedOptions.priorities.includes(stat.label),
  }));
  
  return (
    <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
      <div className="md:col-span-2">
        <Form {...form}>
          <form className="space-y-6">
            <QuestionItem
              number={1}
              title="您期望的工作岗位类型是?"
              required
            >
              <FormField
                control={form.control}
                name="expectedPosition"
                render={({ field }) => (
                  <FormItem>
                    <Select
                      onValueChange={field.onChange}
                      defaultValue={field.value}
                    >
                      <FormControl>
                        <SelectTrigger>
                          <SelectValue placeholder="请选择期望岗位" />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        <SelectItem value="技术研发">技术研发</SelectItem>
                        <SelectItem value="产品运营">产品运营</SelectItem>
                        <SelectItem value="市场营销">市场营销</SelectItem>
                        <SelectItem value="人力资源">人力资源</SelectItem>
                        <SelectItem value="财务会计">财务会计</SelectItem>
                        <SelectItem value="行政管理">行政管理</SelectItem>
                        <SelectItem value="教育培训">教育培训</SelectItem>
                        <SelectItem value="医疗卫生">医疗卫生</SelectItem>
                        <SelectItem value="其他">其他</SelectItem>
                      </SelectContent>
                    </Select>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </QuestionItem>
            
            <QuestionItem
              number={2}
              title="您期望的月薪范围是?"
              required
            >
              <FormField
                control={form.control}
                name="expectedSalaryRange"
                render={({ field }) => (
                  <FormItem>
                    <FormControl>
                      <RadioGroup
                        onValueChange={field.onChange}
                        defaultValue={field.value}
                        className="flex flex-col space-y-2"
                      >
                        <FormItem className="flex items-center space-x-3 space-y-0">
                          <FormControl>
                            <RadioGroupItem value="3000元以下" />
                          </FormControl>
                          <FormLabel className="font-normal">3000元以下</FormLabel>
                        </FormItem>
                        <FormItem className="flex items-center space-x-3 space-y-0">
                          <FormControl>
                            <RadioGroupItem value="3000-5000元" />
                          </FormControl>
                          <FormLabel className="font-normal">3000-5000元</FormLabel>
                        </FormItem>
                        <FormItem className="flex items-center space-x-3 space-y-0">
                          <FormControl>
                            <RadioGroupItem value="5000-8000元" />
                          </FormControl>
                          <FormLabel className="font-normal">5000-8000元</FormLabel>
                        </FormItem>
                        <FormItem className="flex items-center space-x-3 space-y-0">
                          <FormControl>
                            <RadioGroupItem value="8000-12000元" />
                          </FormControl>
                          <FormLabel className="font-normal">8000-12000元</FormLabel>
                        </FormItem>
                        <FormItem className="flex items-center space-x-3 space-y-0">
                          <FormControl>
                            <RadioGroupItem value="12000-20000元" />
                          </FormControl>
                          <FormLabel className="font-normal">12000-20000元</FormLabel>
                        </FormItem>
                        <FormItem className="flex items-center space-x-3 space-y-0">
                          <FormControl>
                            <RadioGroupItem value="20000元以上" />
                          </FormControl>
                          <FormLabel className="font-normal">20000元以上</FormLabel>
                        </FormItem>
                      </RadioGroup>
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </QuestionItem>
            
            <QuestionItem
              number={3}
              title="您期望的工作时长是?"
              required
            >
              <FormField
                control={form.control}
                name="expectedWorkHours"
                render={({ field }) => (
                  <FormItem>
                    <FormControl>
                      <RadioGroup
                        onValueChange={field.onChange}
                        defaultValue={field.value}
                        className="flex flex-col space-y-2"
                      >
                        <FormItem className="flex items-center space-x-3 space-y-0">
                          <FormControl>
                            <RadioGroupItem value="40" />
                          </FormControl>
                          <FormLabel className="font-normal">标准工作制(每周40小时)</FormLabel>
                        </FormItem>
                        <FormItem className="flex items-center space-x-3 space-y-0">
                          <FormControl>
                            <RadioGroupItem value="30" />
                          </FormControl>
                          <FormLabel className="font-normal">弹性工作制</FormLabel>
                        </FormItem>
                        <FormItem className="flex items-center space-x-3 space-y-0">
                          <FormControl>
                            <RadioGroupItem value="60" />
                          </FormControl>
                          <FormLabel className="font-normal">996工作制</FormLabel>
                        </FormItem>
                        <FormItem className="flex items-center space-x-3 space-y-0">
                          <FormControl>
                            <RadioGroupItem value="50" />
                          </FormControl>
                          <FormLabel className="font-normal">大小周</FormLabel>
                        </FormItem>
                        <FormItem className="flex items-center space-x-3 space-y-0">
                          <FormControl>
                            <RadioGroupItem value="20" />
                          </FormControl>
                          <FormLabel className="font-normal">远程工作</FormLabel>
                        </FormItem>
                      </RadioGroup>
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </QuestionItem>
            
            <QuestionItem
              number={4}
              title="您期望的年休假天数是?"
              required
            >
              <FormField
                control={form.control}
                name="expectedVacationDays"
                render={({ field }) => (
                  <FormItem>
                    <Select
                      onValueChange={field.onChange}
                      defaultValue={field.value}
                    >
                      <FormControl>
                        <SelectTrigger>
                          <SelectValue placeholder="请选择期望休假天数" />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        <SelectItem value="5">5天以内</SelectItem>
                        <SelectItem value="10">5-10天</SelectItem>
                        <SelectItem value="15">11-15天</SelectItem>
                        <SelectItem value="20">16-20天</SelectItem>
                        <SelectItem value="30">20天以上</SelectItem>
                      </SelectContent>
                    </Select>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </QuestionItem>
            
            <QuestionItem
              number={5}
              title="您最看重工作的哪些方面?"
              description="最多选择3项"
              required
            >
              <FormField
                control={form.control}
                name="priorities"
                render={() => (
                  <FormItem>
                    <div className="grid grid-cols-2 gap-2">
                      {[
                        "薪资待遇",
                        "工作环境",
                        "发展空间",
                        "工作稳定性",
                        "工作与生活平衡",
                        "企业文化",
                        "行业前景",
                        "其他"
                      ].map((item) => (
                        <FormField
                          key={item}
                          control={form.control}
                          name="priorities"
                          render={({ field }) => {
                            return (
                              <FormItem
                                key={item}
                                className="flex flex-row items-start space-x-3 space-y-0"
                              >
                                <FormControl>
                                  <Checkbox
                                    checked={field.value?.includes(item)}
                                    onCheckedChange={(checked) => {
                                      return checked
                                        ? field.onChange([...field.value, item])
                                        : field.onChange(
                                            field.value?.filter(
                                              (value) => value !== item
                                            )
                                          )
                                    }}
                                  />
                                </FormControl>
                                <FormLabel className="font-normal">
                                  {item}
                                </FormLabel>
                              </FormItem>
                            )
                          }}
                        />
                      ))}
                    </div>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </QuestionItem>
          </form>
        </Form>
      </div>
      
      <div className="space-y-6">
        <AnswerStats 
          title="期望岗位分布" 
          options={positionStats} 
        />
        
        <AnswerStats 
          title="期望薪资分布" 
          options={salaryStats} 
        />
        
        <AnswerStats 
          title="期望工作时长分布" 
          options={workHoursStats} 
        />
        
        <AnswerStats 
          title="最看重的工作方面" 
          options={prioritiesStats} 
        />
      </div>
    </div>
  );
}
