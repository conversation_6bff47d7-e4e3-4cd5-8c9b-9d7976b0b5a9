-- RedefineTables
PRAGMA defer_foreign_keys=ON;
PRAGMA foreign_keys=OFF;
CREATE TABLE "new_QuestionnaireResponse" (
    "id" INTEGER NOT NULL PRIMARY KEY AUTOINCREMENT,
    "userId" INTEGER,
    "sequenceNumber" TEXT NOT NULL,
    "isAnonymous" BOOLEAN NOT NULL DEFAULT true,
    "ipAddress" TEXT,
    "submittedById" TEXT,
    "educationLevel" TEXT,
    "major" TEXT,
    "graduationYear" INTEGER,
    "region" TEXT,
    "expectedPosition" TEXT,
    "expectedSalaryRange" TEXT,
    "expectedWorkHours" INTEGER,
    "expectedVacationDays" INTEGER,
    "employmentStatus" TEXT,
    "currentIndustry" TEXT,
    "currentPosition" TEXT,
    "monthlySalary" INTEGER,
    "jobSatisfaction" TEXT,
    "unemploymentDuration" TEXT,
    "unemploymentReason" TEXT,
    "jobHuntingDifficulty" INTEGER,
    "regretMajor" BOOLEAN,
    "preferredMajor" TEXT,
    "careerChangeIntention" BOOLEAN,
    "careerChangeTarget" TEXT,
    "adviceForStudents" TEXT,
    "observationOnEmployment" TEXT,
    "tags" TEXT,
    "status" TEXT NOT NULL DEFAULT 'normal',
    "createdAt" DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" DATETIME NOT NULL,
    "isTestData" BOOLEAN NOT NULL DEFAULT false,
    "testDataVersion" TEXT,
    "testDataSet" TEXT,
    CONSTRAINT "QuestionnaireResponse_userId_fkey" FOREIGN KEY ("userId") REFERENCES "User" ("id") ON DELETE SET NULL ON UPDATE CASCADE
);
INSERT INTO "new_QuestionnaireResponse" ("adviceForStudents", "careerChangeIntention", "careerChangeTarget", "createdAt", "currentIndustry", "currentPosition", "educationLevel", "employmentStatus", "expectedPosition", "expectedSalaryRange", "expectedVacationDays", "expectedWorkHours", "graduationYear", "id", "ipAddress", "isAnonymous", "jobHuntingDifficulty", "jobSatisfaction", "major", "monthlySalary", "observationOnEmployment", "preferredMajor", "region", "regretMajor", "sequenceNumber", "status", "submittedById", "tags", "unemploymentDuration", "unemploymentReason", "updatedAt", "userId") SELECT "adviceForStudents", "careerChangeIntention", "careerChangeTarget", "createdAt", "currentIndustry", "currentPosition", "educationLevel", "employmentStatus", "expectedPosition", "expectedSalaryRange", "expectedVacationDays", "expectedWorkHours", "graduationYear", "id", "ipAddress", "isAnonymous", "jobHuntingDifficulty", "jobSatisfaction", "major", "monthlySalary", "observationOnEmployment", "preferredMajor", "region", "regretMajor", "sequenceNumber", "status", "submittedById", "tags", "unemploymentDuration", "unemploymentReason", "updatedAt", "userId" FROM "QuestionnaireResponse";
DROP TABLE "QuestionnaireResponse";
ALTER TABLE "new_QuestionnaireResponse" RENAME TO "QuestionnaireResponse";
CREATE UNIQUE INDEX "QuestionnaireResponse_sequenceNumber_key" ON "QuestionnaireResponse"("sequenceNumber");
CREATE TABLE "new_Story" (
    "id" INTEGER NOT NULL PRIMARY KEY AUTOINCREMENT,
    "userId" INTEGER,
    "isAnonymous" BOOLEAN NOT NULL DEFAULT true,
    "title" TEXT NOT NULL,
    "content" TEXT NOT NULL,
    "author" TEXT,
    "ipAddress" TEXT,
    "likes" INTEGER NOT NULL DEFAULT 0,
    "dislikes" INTEGER NOT NULL DEFAULT 0,
    "tags" TEXT,
    "category" TEXT,
    "educationLevel" TEXT,
    "industry" TEXT,
    "status" TEXT NOT NULL DEFAULT 'pending',
    "submittedById" TEXT,
    "createdAt" DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" DATETIME NOT NULL,
    "isTestData" BOOLEAN NOT NULL DEFAULT false,
    "testDataVersion" TEXT,
    "testDataSet" TEXT,
    CONSTRAINT "Story_userId_fkey" FOREIGN KEY ("userId") REFERENCES "User" ("id") ON DELETE SET NULL ON UPDATE CASCADE
);
INSERT INTO "new_Story" ("author", "category", "content", "createdAt", "dislikes", "educationLevel", "id", "industry", "ipAddress", "isAnonymous", "likes", "status", "submittedById", "tags", "title", "updatedAt", "userId") SELECT "author", "category", "content", "createdAt", "dislikes", "educationLevel", "id", "industry", "ipAddress", "isAnonymous", "likes", "status", "submittedById", "tags", "title", "updatedAt", "userId" FROM "Story";
DROP TABLE "Story";
ALTER TABLE "new_Story" RENAME TO "Story";
CREATE TABLE "new_User" (
    "id" INTEGER NOT NULL PRIMARY KEY AUTOINCREMENT,
    "email" TEXT NOT NULL,
    "emailVerified" BOOLEAN NOT NULL DEFAULT false,
    "verificationCode" TEXT,
    "verificationExpiresAt" DATETIME,
    "ipAddress" TEXT,
    "userAgent" TEXT,
    "role" TEXT NOT NULL DEFAULT 'user',
    "username" TEXT,
    "name" TEXT,
    "passwordHash" TEXT,
    "lastLoginAt" DATETIME,
    "createdAt" DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" DATETIME NOT NULL,
    "isTestData" BOOLEAN NOT NULL DEFAULT false,
    "testDataVersion" TEXT,
    "testDataSet" TEXT
);
INSERT INTO "new_User" ("createdAt", "email", "emailVerified", "id", "ipAddress", "lastLoginAt", "name", "passwordHash", "role", "updatedAt", "userAgent", "username", "verificationCode", "verificationExpiresAt") SELECT "createdAt", "email", "emailVerified", "id", "ipAddress", "lastLoginAt", "name", "passwordHash", "role", "updatedAt", "userAgent", "username", "verificationCode", "verificationExpiresAt" FROM "User";
DROP TABLE "User";
ALTER TABLE "new_User" RENAME TO "User";
CREATE UNIQUE INDEX "User_email_key" ON "User"("email");
CREATE TABLE "new_Vote" (
    "id" INTEGER NOT NULL PRIMARY KEY AUTOINCREMENT,
    "storyId" INTEGER NOT NULL,
    "ipAddress" TEXT NOT NULL,
    "voteType" TEXT NOT NULL,
    "createdAt" DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "isTestData" BOOLEAN NOT NULL DEFAULT false,
    "testDataVersion" TEXT,
    "testDataSet" TEXT,
    CONSTRAINT "Vote_storyId_fkey" FOREIGN KEY ("storyId") REFERENCES "Story" ("id") ON DELETE RESTRICT ON UPDATE CASCADE
);
INSERT INTO "new_Vote" ("createdAt", "id", "ipAddress", "storyId", "voteType") SELECT "createdAt", "id", "ipAddress", "storyId", "voteType" FROM "Vote";
DROP TABLE "Vote";
ALTER TABLE "new_Vote" RENAME TO "Vote";
CREATE UNIQUE INDEX "Vote_storyId_ipAddress_key" ON "Vote"("storyId", "ipAddress");
PRAGMA foreign_keys=ON;
PRAGMA defer_foreign_keys=OFF;
