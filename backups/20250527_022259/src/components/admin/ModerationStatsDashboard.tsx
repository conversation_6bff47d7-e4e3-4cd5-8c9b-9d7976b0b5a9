import React, { useState, useEffect } from 'react';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { Ta<PERSON>, Ta<PERSON>Content, Ta<PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import { Button } from '@/components/ui/button';
import { Skeleton } from '@/components/ui/skeleton';
import { useToast } from '@/components/ui/use-toast';
import { 
  <PERSON><PERSON><PERSON>, 
  <PERSON><PERSON><PERSON>, 
  <PERSON><PERSON>hart, 
  RefreshCw,
  CheckCircle,
  XCircle,
  AlertTriangle,
  FileText,
  User,
  Tag
} from 'lucide-react';
import { useApi } from '@/hooks/useApi';
import {
  Chart as ChartJS,
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  BarElement,
  ArcElement,
  Title,
  Tooltip,
  Legend,
} from 'chart.js';
import { Bar, Pie, Line } from 'react-chartjs-2';

// 注册Chart.js组件
ChartJS.register(
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  BarElement,
  ArcElement,
  Title,
  Tooltip,
  Legend
);

interface ModerationStats {
  total: number;
  safe: number;
  unsafe: number;
  byAction: { suggestedAction: string; count: number }[];
  byType: { contentType: string; count: number }[];
  daily: { date: string; count: number }[];
}

/**
 * 内容审核统计仪表板组件
 * 
 * 用于显示内容审核的统计数据和图表
 */
const ModerationStatsDashboard: React.FC = () => {
  const { toast } = useToast();
  const api = useApi();
  const [isLoading, setIsLoading] = useState<boolean>(true);
  const [stats, setStats] = useState<ModerationStats | null>(null);
  
  // 加载统计数据
  const loadStats = async () => {
    setIsLoading(true);
    
    try {
      // 发送请求
      const response = await api.get('/admin/content-moderation/stats');
      
      if (response.success) {
        setStats(response.stats);
      } else {
        toast({
          title: '加载失败',
          description: response.error || '无法加载审核统计数据',
          variant: 'destructive'
        });
      }
    } catch (error) {
      console.error('加载审核统计数据失败:', error);
      toast({
        title: '加载失败',
        description: '服务器错误，请稍后再试',
        variant: 'destructive'
      });
    } finally {
      setIsLoading(false);
    }
  };
  
  // 组件加载时加载数据
  useEffect(() => {
    loadStats();
  }, []);
  
  // 准备图表数据
  const prepareChartData = () => {
    if (!stats) return null;
    
    // 安全性饼图数据
    const safetyData = {
      labels: ['安全内容', '不安全内容'],
      datasets: [
        {
          data: [stats.safe, stats.unsafe],
          backgroundColor: ['rgba(34, 197, 94, 0.6)', 'rgba(239, 68, 68, 0.6)'],
          borderColor: ['rgba(34, 197, 94, 1)', 'rgba(239, 68, 68, 1)'],
          borderWidth: 1,
        },
      ],
    };
    
    // 建议操作饼图数据
    const actionData = {
      labels: stats.byAction.map(item => 
        item.suggestedAction === 'approve' ? '通过' : 
        item.suggestedAction === 'reject' ? '拒绝' : 
        item.suggestedAction === 'review' ? '人工审核' : 
        item.suggestedAction
      ),
      datasets: [
        {
          data: stats.byAction.map(item => item.count),
          backgroundColor: [
            'rgba(34, 197, 94, 0.6)',
            'rgba(239, 68, 68, 0.6)',
            'rgba(234, 179, 8, 0.6)',
            'rgba(59, 130, 246, 0.6)',
          ],
          borderColor: [
            'rgba(34, 197, 94, 1)',
            'rgba(239, 68, 68, 1)',
            'rgba(234, 179, 8, 1)',
            'rgba(59, 130, 246, 1)',
          ],
          borderWidth: 1,
        },
      ],
    };
    
    // 内容类型柱状图数据
    const typeData = {
      labels: stats.byType.map(item => 
        item.contentType === 'story' ? '故事' : 
        item.contentType === 'questionnaire' ? '问卷' : 
        item.contentType === 'comment' ? '评论' : 
        item.contentType === 'profile' ? '个人资料' : 
        item.contentType === 'feedback' ? '反馈' : 
        item.contentType
      ),
      datasets: [
        {
          label: '审核数量',
          data: stats.byType.map(item => item.count),
          backgroundColor: 'rgba(59, 130, 246, 0.6)',
          borderColor: 'rgba(59, 130, 246, 1)',
          borderWidth: 1,
        },
      ],
    };
    
    // 每日审核数量折线图数据
    const dailyData = {
      labels: stats.daily.map(item => item.date),
      datasets: [
        {
          label: '审核数量',
          data: stats.daily.map(item => item.count),
          fill: false,
          backgroundColor: 'rgba(99, 102, 241, 0.6)',
          borderColor: 'rgba(99, 102, 241, 1)',
          tension: 0.1,
        },
      ],
    };
    
    return {
      safetyData,
      actionData,
      typeData,
      dailyData
    };
  };
  
  // 图表选项
  const chartOptions = {
    responsive: true,
    maintainAspectRatio: false,
    plugins: {
      legend: {
        position: 'bottom' as const,
      },
    },
  };
  
  // 准备图表数据
  const chartData = prepareChartData();
  
  return (
    <Card>
      <CardHeader className="flex flex-row items-center justify-between">
        <div>
          <CardTitle>内容审核统计</CardTitle>
          <CardDescription>
            AI内容审核的统计数据和趋势分析
          </CardDescription>
        </div>
        <Button variant="outline" size="sm" onClick={loadStats} disabled={isLoading}>
          <RefreshCw className={`w-4 h-4 mr-2 ${isLoading ? 'animate-spin' : ''}`} />
          刷新
        </Button>
      </CardHeader>
      <CardContent>
        {isLoading ? (
          <div className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <Skeleton className="h-24 w-full" />
              <Skeleton className="h-24 w-full" />
              <Skeleton className="h-24 w-full" />
            </div>
            <Skeleton className="h-[300px] w-full" />
          </div>
        ) : stats ? (
          <>
            {/* 统计卡片 */}
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
              <Card>
                <CardContent className="pt-6">
                  <div className="flex justify-between items-center">
                    <div>
                      <p className="text-sm font-medium text-muted-foreground">总审核数</p>
                      <h3 className="text-2xl font-bold mt-1">{stats.total}</h3>
                    </div>
                    <div className="bg-blue-100 p-3 rounded-full">
                      <FileText className="h-5 w-5 text-blue-600" />
                    </div>
                  </div>
                </CardContent>
              </Card>
              
              <Card>
                <CardContent className="pt-6">
                  <div className="flex justify-between items-center">
                    <div>
                      <p className="text-sm font-medium text-muted-foreground">安全内容</p>
                      <h3 className="text-2xl font-bold mt-1">{stats.safe}</h3>
                      <p className="text-xs text-muted-foreground mt-1">
                        {stats.total > 0 ? Math.round((stats.safe / stats.total) * 100) : 0}% 的内容
                      </p>
                    </div>
                    <div className="bg-green-100 p-3 rounded-full">
                      <CheckCircle className="h-5 w-5 text-green-600" />
                    </div>
                  </div>
                </CardContent>
              </Card>
              
              <Card>
                <CardContent className="pt-6">
                  <div className="flex justify-between items-center">
                    <div>
                      <p className="text-sm font-medium text-muted-foreground">不安全内容</p>
                      <h3 className="text-2xl font-bold mt-1">{stats.unsafe}</h3>
                      <p className="text-xs text-muted-foreground mt-1">
                        {stats.total > 0 ? Math.round((stats.unsafe / stats.total) * 100) : 0}% 的内容
                      </p>
                    </div>
                    <div className="bg-red-100 p-3 rounded-full">
                      <XCircle className="h-5 w-5 text-red-600" />
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>
            
            {/* 图表 */}
            <Tabs defaultValue="overview" className="w-full">
              <TabsList className="grid w-full grid-cols-4">
                <TabsTrigger value="overview">概览</TabsTrigger>
                <TabsTrigger value="by-type">内容类型</TabsTrigger>
                <TabsTrigger value="by-action">建议操作</TabsTrigger>
                <TabsTrigger value="daily">每日趋势</TabsTrigger>
              </TabsList>
              
              <TabsContent value="overview" className="mt-4">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <Card>
                    <CardHeader>
                      <CardTitle className="text-base">内容安全性分布</CardTitle>
                    </CardHeader>
                    <CardContent>
                      <div className="h-[300px]">
                        {chartData && <Pie data={chartData.safetyData} options={chartOptions} />}
                      </div>
                    </CardContent>
                  </Card>
                  
                  <Card>
                    <CardHeader>
                      <CardTitle className="text-base">建议操作分布</CardTitle>
                    </CardHeader>
                    <CardContent>
                      <div className="h-[300px]">
                        {chartData && <Pie data={chartData.actionData} options={chartOptions} />}
                      </div>
                    </CardContent>
                  </Card>
                </div>
              </TabsContent>
              
              <TabsContent value="by-type" className="mt-4">
                <Card>
                  <CardHeader>
                    <CardTitle>按内容类型统计</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="h-[400px]">
                      {chartData && <Bar data={chartData.typeData} options={chartOptions} />}
                    </div>
                  </CardContent>
                </Card>
              </TabsContent>
              
              <TabsContent value="by-action" className="mt-4">
                <Card>
                  <CardHeader>
                    <CardTitle>按建议操作统计</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="h-[400px]">
                      {chartData && <Pie data={chartData.actionData} options={chartOptions} />}
                    </div>
                  </CardContent>
                </Card>
              </TabsContent>
              
              <TabsContent value="daily" className="mt-4">
                <Card>
                  <CardHeader>
                    <CardTitle>每日审核趋势</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="h-[400px]">
                      {chartData && <Line data={chartData.dailyData} options={chartOptions} />}
                    </div>
                  </CardContent>
                </Card>
              </TabsContent>
            </Tabs>
          </>
        ) : (
          <div className="text-center py-8">
            <p className="text-muted-foreground">暂无审核统计数据</p>
          </div>
        )}
      </CardContent>
    </Card>
  );
};

export default ModerationStatsDashboard;
