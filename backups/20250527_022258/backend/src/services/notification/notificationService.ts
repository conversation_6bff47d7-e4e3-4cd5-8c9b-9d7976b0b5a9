/**
 * 通知服务
 * 
 * 处理系统通知，包括用户通知和管理员通知
 */

import { PrismaClient } from '@prisma/client';
import { Env } from '../../types';
import { NotificationType } from '../../constants';

// 初始化Prisma客户端
const prisma = new PrismaClient();

/**
 * 通知服务
 */
export class NotificationService {
  private static instance: NotificationService;

  /**
   * 私有构造函数，防止直接实例化
   */
  private constructor() {}

  /**
   * 获取单例实例
   */
  public static getInstance(): NotificationService {
    if (!NotificationService.instance) {
      NotificationService.instance = new NotificationService();
    }
    return NotificationService.instance;
  }

  /**
   * 发送用户通知
   * @param userId 用户ID
   * @param type 通知类型
   * @param title 通知标题
   * @param message 通知内容
   * @param data 通知数据
   * @param env 环境变量
   */
  public async sendUserNotification(
    userId: string,
    type: NotificationType,
    title: string,
    message: string,
    data: any = {},
    env: Env
  ): Promise<void> {
    try {
      // 创建通知记录
      await prisma.notification.create({
        data: {
          userId,
          type,
          title,
          message,
          data: JSON.stringify(data),
          isRead: false
        }
      });

      // TODO: 实现实时通知（如WebSocket）
    } catch (error) {
      console.error('发送用户通知失败:', error);
    }
  }

  /**
   * 发送管理员通知
   * @param type 通知类型
   * @param title 通知标题
   * @param message 通知内容
   * @param data 通知数据
   * @param env 环境变量
   */
  public async sendAdminNotification(
    type: NotificationType,
    title: string,
    message: string,
    data: any = {},
    env: Env
  ): Promise<void> {
    try {
      // 获取所有管理员
      const admins = await prisma.user.findMany({
        where: {
          role: {
            in: ['admin', 'superadmin']
          }
        }
      });

      // 为每个管理员创建通知
      for (const admin of admins) {
        await prisma.notification.create({
          data: {
            userId: admin.id,
            type,
            title,
            message,
            data: JSON.stringify(data),
            isRead: false
          }
        });
      }

      // TODO: 实现实时通知（如WebSocket）
    } catch (error) {
      console.error('发送管理员通知失败:', error);
    }
  }

  /**
   * 发送审核员通知
   * @param type 通知类型
   * @param title 通知标题
   * @param message 通知内容
   * @param data 通知数据
   * @param env 环境变量
   */
  public async sendReviewerNotification(
    type: NotificationType,
    title: string,
    message: string,
    data: any = {},
    env: Env
  ): Promise<void> {
    try {
      // 获取所有审核员
      const reviewers = await prisma.user.findMany({
        where: {
          role: 'reviewer'
        }
      });

      // 为每个审核员创建通知
      for (const reviewer of reviewers) {
        await prisma.notification.create({
          data: {
            userId: reviewer.id,
            type,
            title,
            message,
            data: JSON.stringify(data),
            isRead: false
          }
        });
      }

      // TODO: 实现实时通知（如WebSocket）
    } catch (error) {
      console.error('发送审核员通知失败:', error);
    }
  }

  /**
   * 发送内容审核通知
   * @param contentId 内容ID
   * @param contentType 内容类型
   * @param action 审核操作
   * @param userId 用户ID
   * @param message 通知内容
   * @param env 环境变量
   */
  public async sendContentModerationNotification(
    contentId: string,
    contentType: string,
    action: 'approve' | 'reject' | 'review',
    userId: string | null,
    message: string,
    env: Env
  ): Promise<void> {
    try {
      // 根据操作类型确定通知类型
      let userNotificationType: NotificationType;
      let adminNotificationType: NotificationType;

      switch (action) {
        case 'approve':
          userNotificationType = NotificationType.CONTENT_APPROVED;
          adminNotificationType = NotificationType.ADMIN_CONTENT_APPROVED;
          break;
        case 'reject':
          userNotificationType = NotificationType.CONTENT_REJECTED;
          adminNotificationType = NotificationType.ADMIN_CONTENT_REJECTED;
          break;
        case 'review':
          userNotificationType = NotificationType.CONTENT_REVIEW;
          adminNotificationType = NotificationType.ADMIN_CONTENT_REVIEW;
          break;
      }

      // 发送用户通知
      if (userId) {
        let title = '';
        switch (action) {
          case 'approve':
            title = '内容已通过审核';
            break;
          case 'reject':
            title = '内容未通过审核';
            break;
          case 'review':
            title = '内容正在审核中';
            break;
        }

        await this.sendUserNotification(
          userId,
          userNotificationType,
          title,
          message,
          { contentId, contentType, action },
          env
        );
      }

      // 发送管理员通知
      if (action === 'review') {
        await this.sendAdminNotification(
          adminNotificationType,
          '新内容需要审核',
          `有新的${this.getContentTypeName(contentType)}需要审核`,
          { contentId, contentType, action },
          env
        );
      }
    } catch (error) {
      console.error('发送内容审核通知失败:', error);
    }
  }

  /**
   * 获取内容类型名称
   * @param contentType 内容类型
   * @returns 内容类型名称
   */
  private getContentTypeName(contentType: string): string {
    switch (contentType) {
      case 'story':
        return '故事';
      case 'questionnaire':
        return '问卷';
      case 'comment':
        return '评论';
      case 'profile':
        return '个人资料';
      case 'feedback':
        return '反馈';
      default:
        return '内容';
    }
  }

  /**
   * 标记通知为已读
   * @param notificationId 通知ID
   */
  public async markAsRead(notificationId: string): Promise<void> {
    try {
      await prisma.notification.update({
        where: { id: notificationId },
        data: { isRead: true, readAt: new Date() }
      });
    } catch (error) {
      console.error('标记通知为已读失败:', error);
    }
  }

  /**
   * 获取用户未读通知数量
   * @param userId 用户ID
   * @returns 未读通知数量
   */
  public async getUnreadCount(userId: string): Promise<number> {
    try {
      return await prisma.notification.count({
        where: {
          userId,
          isRead: false
        }
      });
    } catch (error) {
      console.error('获取未读通知数量失败:', error);
      return 0;
    }
  }

  /**
   * 获取用户通知列表
   * @param userId 用户ID
   * @param page 页码
   * @param pageSize 每页数量
   * @returns 通知列表
   */
  public async getUserNotifications(
    userId: string,
    page: number = 1,
    pageSize: number = 10
  ): Promise<any> {
    try {
      const skip = (page - 1) * pageSize;
      
      // 获取通知列表
      const notifications = await prisma.notification.findMany({
        where: { userId },
        orderBy: { createdAt: 'desc' },
        skip,
        take: pageSize
      });
      
      // 获取总数
      const total = await prisma.notification.count({
        where: { userId }
      });
      
      return {
        notifications,
        pagination: {
          page,
          pageSize,
          total,
          totalPages: Math.ceil(total / pageSize)
        }
      };
    } catch (error) {
      console.error('获取用户通知列表失败:', error);
      return {
        notifications: [],
        pagination: {
          page,
          pageSize,
          total: 0,
          totalPages: 0
        }
      };
    }
  }
}
