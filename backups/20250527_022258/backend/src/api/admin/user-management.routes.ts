/**
 * 用户管理路由
 */

import { Hono } from 'hono';
import { Env } from '../../types';
import { superAdminAuthMiddleware } from '../../middlewares/superAdminAuth.middleware';
import * as userController from './user-management.controller';

// 创建路由
const app = new Hono<{ Bindings: Env }>();

// 应用超级管理员认证中间件
app.use('*', superAdminAuthMiddleware);

// 用户管理路由
app.get('/', userController.getUsers);           // 获取用户列表
app.get('/:id', userController.getUser);         // 获取单个用户详情
app.post('/', userController.createUser);        // 创建用户
app.put('/:id', userController.updateUser);      // 更新用户
app.delete('/:id', userController.deleteUser);   // 删除用户
app.post('/:id/reset-password', userController.resetUserPassword); // 重置密码

export default app;
