import React, { useState, useEffect } from 'react';
import { <PERSON><PERSON><PERSON>, Pie, Cell, ResponsiveContainer, <PERSON>, Toolt<PERSON>, <PERSON><PERSON><PERSON>, Bar, XAxis, <PERSON><PERSON><PERSON><PERSON>, CartesianGrid } from 'recharts';
import { Button } from '@/components/ui/button';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Loader2, RefreshCw, Heart, Eye } from 'lucide-react';

interface StoryTypeData {
  type: string;
  count: number;
  percentage: number;
  avgLikes: number;
  avgViews: number;
  engagementRate: number;
}

interface StoryTypeDistributionChartProps {
  height?: number;
  showControls?: boolean;
  chartType?: 'pie' | 'bar';
}

const StoryTypeDistributionChart: React.FC<StoryTypeDistributionChartProps> = ({ 
  height = 300,
  showControls = true,
  chartType = 'pie'
}) => {
  const [data, setData] = useState<StoryTypeData[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [selectedChartType, setSelectedChartType] = useState<'pie' | 'bar'>(chartType);
  const [selectedMetric, setSelectedMetric] = useState<'count' | 'avgLikes' | 'avgViews' | 'engagementRate'>('count');

  // 颜色配置
  const COLORS = [
    '#8884d8', // 蓝色 - 求职经历
    '#82ca9d', // 绿色 - 工作感悟
    '#ffc658', // 黄色 - 职场困惑
    '#ff7c7c', // 红色 - 成功案例
    '#8dd1e1', // 浅蓝 - 建议分享
    '#d084d0', // 紫色 - 其他
  ];

  // 故事类型映射
  const storyTypeMap: { [key: string]: string } = {
    'job_search': '求职经历',
    'work_experience': '工作感悟',
    'workplace_confusion': '职场困惑',
    'success_story': '成功案例',
    'advice_sharing': '建议分享',
    'career_change': '转行经历',
    'other': '其他类型'
  };

  // 获取故事类型分布数据
  const fetchStoryTypeData = async () => {
    setLoading(true);
    setError(null);
    
    try {
      const response = await fetch('/api/admin/dashboard/story-types');
      
      if (!response.ok) {
        throw new Error('获取故事类型分布数据失败');
      }
      
      const result = await response.json();
      
      if (result.success) {
        // 转换数据格式
        const formattedData = result.data.map((item: any) => ({
          type: storyTypeMap[item.type] || item.type,
          count: item.count,
          percentage: item.percentage,
          avgLikes: item.avg_likes || 0,
          avgViews: item.avg_views || 0,
          engagementRate: item.engagement_rate || 0
        }));
        setData(formattedData);
      } else {
        throw new Error(result.message || '数据格式错误');
      }
    } catch (err) {
      console.error('故事类型分布数据获取错误:', err);
      setError(err instanceof Error ? err.message : '未知错误');
      
      // 使用模拟数据作为降级
      generateMockData();
    } finally {
      setLoading(false);
    }
  };

  // 生成模拟数据
  const generateMockData = () => {
    const mockData: StoryTypeData[] = [
      { 
        type: '求职经历', 
        count: 58, 
        percentage: 35.2, 
        avgLikes: 12.3,
        avgViews: 156.7,
        engagementRate: 7.8
      },
      { 
        type: '工作感悟', 
        count: 42, 
        percentage: 25.5, 
        avgLikes: 18.6,
        avgViews: 203.4,
        engagementRate: 9.1
      },
      { 
        type: '职场困惑', 
        count: 31, 
        percentage: 18.8, 
        avgLikes: 8.9,
        avgViews: 134.2,
        engagementRate: 6.6
      },
      { 
        type: '成功案例', 
        count: 19, 
        percentage: 11.5, 
        avgLikes: 25.4,
        avgViews: 287.9,
        engagementRate: 8.8
      },
      { 
        type: '建议分享', 
        count: 12, 
        percentage: 7.3, 
        avgLikes: 15.7,
        avgViews: 189.3,
        engagementRate: 8.3
      },
      { 
        type: '转行经历', 
        count: 3, 
        percentage: 1.8, 
        avgLikes: 22.1,
        avgViews: 245.6,
        engagementRate: 9.0
      }
    ];
    
    setData(mockData);
  };

  // 初始化数据
  useEffect(() => {
    fetchStoryTypeData();
  }, []);

  // 获取显示数据
  const getDisplayData = () => {
    return data.map(item => ({
      ...item,
      value: selectedMetric === 'count' ? item.count :
             selectedMetric === 'avgLikes' ? item.avgLikes :
             selectedMetric === 'avgViews' ? item.avgViews :
             item.engagementRate
    }));
  };

  // 自定义Tooltip
  const CustomTooltip = ({ active, payload }: any) => {
    if (active && payload && payload.length) {
      const data = payload[0].payload;
      return (
        <div className="bg-white p-3 border rounded-lg shadow-lg">
          <p className="font-medium mb-1">{data.type}</p>
          <p className="text-sm text-gray-600">故事数量: {data.count}</p>
          <p className="text-sm text-gray-600">占比: {data.percentage.toFixed(1)}%</p>
          <div className="flex items-center text-sm text-gray-600">
            <Heart className="h-3 w-3 mr-1" />
            平均点赞: {data.avgLikes.toFixed(1)}
          </div>
          <div className="flex items-center text-sm text-gray-600">
            <Eye className="h-3 w-3 mr-1" />
            平均浏览: {data.avgViews.toFixed(1)}
          </div>
          <p className="text-sm text-gray-600">参与度: {data.engagementRate.toFixed(1)}%</p>
        </div>
      );
    }
    return null;
  };

  // 自定义标签
  const renderCustomLabel = ({ cx, cy, midAngle, innerRadius, outerRadius, percent }: any) => {
    if (percent < 0.05) return null; // 小于5%不显示标签
    
    const RADIAN = Math.PI / 180;
    const radius = innerRadius + (outerRadius - innerRadius) * 0.5;
    const x = cx + radius * Math.cos(-midAngle * RADIAN);
    const y = cy + radius * Math.sin(-midAngle * RADIAN);

    return (
      <text 
        x={x} 
        y={y} 
        fill="white" 
        textAnchor={x > cx ? 'start' : 'end'} 
        dominantBaseline="central"
        fontSize={12}
        fontWeight="bold"
      >
        {`${(percent * 100).toFixed(0)}%`}
      </text>
    );
  };

  // 处理刷新
  const handleRefresh = () => {
    fetchStoryTypeData();
  };

  // 渲染加载状态
  if (loading) {
    return (
      <div className="flex items-center justify-center" style={{ height: `${height}px` }}>
        <Loader2 className="h-8 w-8 animate-spin text-muted-foreground" />
        <span className="ml-2 text-muted-foreground">加载故事类型分布数据...</span>
      </div>
    );
  }

  // 渲染错误状态
  if (error) {
    return (
      <div className="flex flex-col items-center justify-center" style={{ height: `${height}px` }}>
        <p className="text-red-500 mb-2">加载失败: {error}</p>
        <Button variant="outline" size="sm" onClick={handleRefresh}>
          <RefreshCw className="h-4 w-4 mr-2" />
          重试
        </Button>
      </div>
    );
  }

  const displayData = getDisplayData();

  return (
    <div className="w-full">
      {showControls && (
        <div className="flex justify-between items-center mb-4">
          <div className="flex space-x-2">
            <Select value={selectedChartType} onValueChange={(value: any) => setSelectedChartType(value)}>
              <SelectTrigger className="w-24">
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="pie">饼图</SelectItem>
                <SelectItem value="bar">柱图</SelectItem>
              </SelectContent>
            </Select>
            
            <Select value={selectedMetric} onValueChange={(value: any) => setSelectedMetric(value)}>
              <SelectTrigger className="w-32">
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="count">故事数量</SelectItem>
                <SelectItem value="avgLikes">平均点赞</SelectItem>
                <SelectItem value="avgViews">平均浏览</SelectItem>
                <SelectItem value="engagementRate">参与度</SelectItem>
              </SelectContent>
            </Select>
          </div>
          
          <Button variant="outline" size="sm" onClick={handleRefresh}>
            <RefreshCw className="h-4 w-4 mr-2" />
            刷新
          </Button>
        </div>
      )}
      
      <div style={{ height: `${height}px` }}>
        <ResponsiveContainer width="100%" height="100%">
          {selectedChartType === 'pie' ? (
            <PieChart>
              <Pie
                data={displayData}
                cx="50%"
                cy="50%"
                labelLine={false}
                label={renderCustomLabel}
                outerRadius={Math.min(height * 0.35, 120)}
                fill="#8884d8"
                dataKey="value"
              >
                {displayData.map((entry, index) => (
                  <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                ))}
              </Pie>
              <Tooltip content={<CustomTooltip />} />
              <Legend 
                verticalAlign="bottom" 
                height={36}
                formatter={(value, entry: any) => (
                  <span style={{ color: entry.color }}>
                    {value}
                  </span>
                )}
              />
            </PieChart>
          ) : (
            <BarChart data={displayData} margin={{ top: 20, right: 30, left: 20, bottom: 5 }}>
              <CartesianGrid strokeDasharray="3 3" />
              <XAxis 
                dataKey="type" 
                tick={{ fontSize: 12 }}
                angle={-45}
                textAnchor="end"
                height={80}
              />
              <YAxis tick={{ fontSize: 12 }} />
              <Tooltip content={<CustomTooltip />} />
              <Bar dataKey="value" fill="#8884d8">
                {displayData.map((entry, index) => (
                  <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                ))}
              </Bar>
            </BarChart>
          )}
        </ResponsiveContainer>
      </div>
      
      {data.length === 0 && (
        <div className="text-center text-muted-foreground mt-4">
          暂无故事类型分布数据
        </div>
      )}
      
      {/* 数据摘要 */}
      {data.length > 0 && (
        <div className="mt-4 grid grid-cols-4 gap-4 text-sm">
          <div className="text-center">
            <p className="text-muted-foreground">总故事数</p>
            <p className="font-semibold text-lg">
              {data.reduce((sum, item) => sum + item.count, 0).toLocaleString()}
            </p>
          </div>
          <div className="text-center">
            <p className="text-muted-foreground">平均点赞</p>
            <p className="font-semibold text-lg">
              {(data.reduce((sum, item) => sum + item.avgLikes, 0) / data.length).toFixed(1)}
            </p>
          </div>
          <div className="text-center">
            <p className="text-muted-foreground">平均浏览</p>
            <p className="font-semibold text-lg">
              {(data.reduce((sum, item) => sum + item.avgViews, 0) / data.length).toFixed(0)}
            </p>
          </div>
          <div className="text-center">
            <p className="text-muted-foreground">故事类型</p>
            <p className="font-semibold text-lg">{data.length}</p>
          </div>
        </div>
      )}
    </div>
  );
};

export default StoryTypeDistributionChart;
