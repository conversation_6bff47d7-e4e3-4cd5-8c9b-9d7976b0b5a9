/**
 * 管理员相关处理程序
 */

import { generateToken } from '../utils/auth';

/**
 * 处理管理员登录
 * @param request 请求对象
 * @param env 环境对象
 * @returns 响应对象
 */
export async function handleAdminLogin(request: Request, env: Env): Promise<Response> {
  try {
    // 解析请求体
    const { username, password } = await request.json() as { username: string; password: string };
    
    // 验证用户名和密码
    const user = await env.DB.prepare(
      `SELECT * FROM users WHERE username = ? AND password = ? AND (role = 'admin' OR role = 'superadmin' OR role = 'reviewer')`
    )
    .bind(username, password)
    .first();
    
    if (!user) {
      return new Response(JSON.stringify({
        success: false,
        error: '用户名或密码错误'
      }), {
        status: 401,
        headers: { 'Content-Type': 'application/json' }
      });
    }
    
    // 获取用户权限
    const permissions = await env.DB.prepare(
      `SELECT permission FROM permissions WHERE user_id = ?`
    )
    .bind(user.id)
    .all();
    
    // 生成令牌
    const token = generateToken(user, env.JWT_SECRET);
    
    // 更新最后登录时间
    await env.DB.prepare(
      `UPDATE users SET last_login = CURRENT_TIMESTAMP WHERE id = ?`
    )
    .bind(user.id)
    .run();
    
    // 返回响应
    return new Response(JSON.stringify({
      success: true,
      token,
      user: {
        id: user.id,
        username: user.username,
        name: user.name,
        role: user.role,
        permissions: permissions.results.map(p => p.permission)
      }
    }), {
      status: 200,
      headers: { 'Content-Type': 'application/json' }
    });
  } catch (error) {
    console.error('管理员登录失败:', error);
    
    return new Response(JSON.stringify({
      success: false,
      error: '服务器错误'
    }), {
      status: 500,
      headers: { 'Content-Type': 'application/json' }
    });
  }
}
