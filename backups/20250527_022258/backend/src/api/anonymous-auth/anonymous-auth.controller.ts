/**
 * 轻量级匿名身份验证控制器
 * 
 * 提供基于A+B组合的匿名身份验证功能
 */

import { Context } from 'hono';
import { Env } from '../../types';
import { PrismaClient } from '@prisma/client';
import { generateUUID, isValidA, isValidB, verifyUUID } from '../../utils/uuidGenerator';
import { z } from 'zod';
import { zValidator } from '@hono/zod-validator';

// 验证A+B输入的Schema
const abInputSchema = z.object({
  a: z.string().regex(/^\d{11}$/, '请输入11位数字'),
  b: z.string().regex(/^\d{4}$|^\d{6}$/, '请输入4位或6位数字')
});

/**
 * 获取用户内容
 * 根据A+B组合查询用户提交的内容
 */
export const getMyContent = [
  zValidator('query', abInputSchema),
  async (c: Context<{ Bindings: Env }>) => {
    try {
      const { a, b } = c.req.valid('query');
      
      // 生成UUID
      const uuid = generateUUID(a, b, c.env.UUID_SALT);
      
      // 初始化Prisma客户端
      const prisma = new PrismaClient({
        datasources: {
          db: {
            url: c.env.DATABASE_URL,
          },
        },
      });
      
      // 查询故事
      const stories = await prisma.story.findMany({
        where: {
          submittedById: uuid
        },
        select: {
          id: true,
          title: true,
          content: true,
          status: true,
          createdAt: true,
          updatedAt: true,
          likes: true,
          dislikes: true
        },
        orderBy: {
          createdAt: 'desc'
        }
      });
      
      // 查询问卷回复
      const questionnaireResponses = await prisma.questionnaireResponse.findMany({
        where: {
          submittedById: uuid
        },
        select: {
          id: true,
          createdAt: true,
          updatedAt: true,
          // 选择性地返回问卷字段，避免返回过多敏感信息
          educationLevel: true,
          major: true,
          graduationYear: true,
          region: true,
          employmentStatus: true
        },
        orderBy: {
          createdAt: 'desc'
        }
      });
      
      // 查询待审核内容
      const pendingContents = await prisma.pendingContent.findMany({
        where: {
          submittedById: uuid
        },
        select: {
          id: true,
          sequenceNumber: true,
          type: true,
          status: true,
          createdAt: true,
          updatedAt: true
        },
        orderBy: {
          createdAt: 'desc'
        }
      });
      
      await prisma.$disconnect();
      
      return c.json({
        success: true,
        data: {
          stories,
          questionnaireResponses,
          pendingContents
        }
      });
    } catch (error) {
      console.error('获取用户内容失败:', error);
      return c.json({ success: false, error: '获取内容失败，请稍后再试' }, 500);
    }
  }
];

/**
 * 删除故事
 * 根据A+B组合和故事ID删除用户提交的故事
 */
export const deleteStory = [
  zValidator('query', abInputSchema),
  async (c: Context<{ Bindings: Env }>) => {
    try {
      const { a, b } = c.req.valid('query');
      const storyId = c.req.param('id');
      
      if (!storyId || isNaN(parseInt(storyId))) {
        return c.json({ success: false, error: '无效的故事ID' }, 400);
      }
      
      // 生成UUID
      const uuid = generateUUID(a, b, c.env.UUID_SALT);
      
      // 初始化Prisma客户端
      const prisma = new PrismaClient({
        datasources: {
          db: {
            url: c.env.DATABASE_URL,
          },
        },
      });
      
      // 查询故事是否存在且属于该用户
      const story = await prisma.story.findFirst({
        where: {
          id: parseInt(storyId),
          submittedById: uuid
        }
      });
      
      if (!story) {
        await prisma.$disconnect();
        return c.json({ success: false, error: '故事不存在或您无权删除' }, 403);
      }
      
      // 删除故事
      await prisma.story.delete({
        where: {
          id: parseInt(storyId)
        }
      });
      
      await prisma.$disconnect();
      
      return c.json({
        success: true,
        message: '故事已成功删除'
      });
    } catch (error) {
      console.error('删除故事失败:', error);
      return c.json({ success: false, error: '删除故事失败，请稍后再试' }, 500);
    }
  }
];

/**
 * 删除问卷回复
 * 根据A+B组合和问卷回复ID删除用户提交的问卷回复
 */
export const deleteQuestionnaireResponse = [
  zValidator('query', abInputSchema),
  async (c: Context<{ Bindings: Env }>) => {
    try {
      const { a, b } = c.req.valid('query');
      const responseId = c.req.param('id');
      
      if (!responseId || isNaN(parseInt(responseId))) {
        return c.json({ success: false, error: '无效的问卷回复ID' }, 400);
      }
      
      // 生成UUID
      const uuid = generateUUID(a, b, c.env.UUID_SALT);
      
      // 初始化Prisma客户端
      const prisma = new PrismaClient({
        datasources: {
          db: {
            url: c.env.DATABASE_URL,
          },
        },
      });
      
      // 查询问卷回复是否存在且属于该用户
      const response = await prisma.questionnaireResponse.findFirst({
        where: {
          id: parseInt(responseId),
          submittedById: uuid
        }
      });
      
      if (!response) {
        await prisma.$disconnect();
        return c.json({ success: false, error: '问卷回复不存在或您无权删除' }, 403);
      }
      
      // 删除问卷回复
      await prisma.questionnaireResponse.delete({
        where: {
          id: parseInt(responseId)
        }
      });
      
      await prisma.$disconnect();
      
      return c.json({
        success: true,
        message: '问卷回复已成功删除'
      });
    } catch (error) {
      console.error('删除问卷回复失败:', error);
      return c.json({ success: false, error: '删除问卷回复失败，请稍后再试' }, 500);
    }
  }
];
