import React, { useState, useEffect } from 'react';
import { 
  Card, 
  Table, 
  Button, 
  Space, 
  Modal, 
  Form, 
  Input, 
  Select, 
  Upload, 
  message, 
  Tooltip, 
  Typography, 
  Tabs, 
  Alert, 
  Spin, 
  Popconfirm,
  Badge,
  Progress,
  Statistic,
  Row,
  Col,
  Divider
} from 'antd';
import { 
  ReloadOutlined, 
  DownloadOutlined, 
  UploadOutlined, 
  ExclamationCircleOutlined, 
  DeleteOutlined,
  SaveOutlined,
  SyncOutlined,
  InfoCircleOutlined,
  DatabaseOutlined,
  HistoryOutlined,
  FileTextOutlined
} from '@ant-design/icons';
import { fetchData } from '../../../services/unifiedDataService';
import dayjs from 'dayjs';

const { TabPane } = Tabs;
const { Title, Text, Paragraph } = Typography;
const { Option } = Select;
const { Dragger } = Upload;
const { confirm } = Modal;

// 数据库管理器组件
const DatabaseManager: React.FC = () => {
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);
  const [backups, setBackups] = useState<any[]>([]);
  const [dbStats, setDbStats] = useState<any>({});
  const [backupModalVisible, setBackupModalVisible] = useState<boolean>(false);
  const [restoreModalVisible, setRestoreModalVisible] = useState<boolean>(false);
  const [backupForm] = Form.useForm();
  const [restoreForm] = Form.useForm();
  const [backupInProgress, setBackupInProgress] = useState<boolean>(false);
  const [restoreInProgress, setRestoreInProgress] = useState<boolean>(false);
  const [backupProgress, setBackupProgress] = useState<number>(0);
  const [restoreProgress, setRestoreProgress] = useState<number>(0);
  const [selectedBackup, setSelectedBackup] = useState<any>(null);
  const [migrationStatus, setMigrationStatus] = useState<any[]>([]);

  // 加载数据库信息和备份列表
  const loadDatabaseInfo = async () => {
    setLoading(true);
    setError(null);

    try {
      const response = await fetchData(
        'admin/database/info',
        'getDatabaseInfo',
        {},
        {
          method: 'GET',
          headers: {
            Authorization: `Bearer ${localStorage.getItem('authToken')}`,
          },
        }
      );

      if (response.success) {
        setBackups(response.backups || []);
        setDbStats(response.stats || {});
        setMigrationStatus(response.migrations || []);
      } else {
        setError(response.error || '获取数据库信息失败');
      }
    } catch (err) {
      setError('获取数据库信息时发生错误');
      console.error('获取数据库信息错误:', err);
    } finally {
      setLoading(false);
    }
  };

  // 首次加载
  useEffect(() => {
    loadDatabaseInfo();
  }, []);

  // 创建备份
  const handleCreateBackup = async (values: any) => {
    setBackupInProgress(true);
    setBackupProgress(0);
    
    try {
      // 模拟进度更新
      const progressInterval = setInterval(() => {
        setBackupProgress(prev => {
          const newProgress = prev + Math.floor(Math.random() * 10);
          return newProgress >= 90 ? 90 : newProgress;
        });
      }, 500);
      
      const response = await fetchData(
        'admin/database/backup',
        'createDatabaseBackup',
        {
          description: values.description,
          includeData: values.includeData === 'yes',
        },
        {
          method: 'POST',
          headers: {
            Authorization: `Bearer ${localStorage.getItem('authToken')}`,
          },
        }
      );
      
      clearInterval(progressInterval);
      
      if (response.success) {
        setBackupProgress(100);
        message.success('数据库备份创建成功');
        setBackupModalVisible(false);
        backupForm.resetFields();
        
        // 重新加载备份列表
        setTimeout(() => {
          loadDatabaseInfo();
        }, 1000);
      } else {
        setError(response.error || '创建数据库备份失败');
        message.error('创建数据库备份失败');
      }
    } catch (err) {
      setError('创建数据库备份时发生错误');
      console.error('创建数据库备份错误:', err);
      message.error('创建数据库备份时发生错误');
    } finally {
      setTimeout(() => {
        setBackupInProgress(false);
        setBackupProgress(0);
      }, 1000);
    }
  };

  // 恢复备份
  const handleRestoreBackup = async (values: any) => {
    setRestoreInProgress(true);
    setRestoreProgress(0);
    
    try {
      // 模拟进度更新
      const progressInterval = setInterval(() => {
        setRestoreProgress(prev => {
          const newProgress = prev + Math.floor(Math.random() * 10);
          return newProgress >= 90 ? 90 : newProgress;
        });
      }, 500);
      
      const response = await fetchData(
        'admin/database/restore',
        'restoreDatabaseBackup',
        {
          backupId: selectedBackup ? selectedBackup.id : null,
          file: values.file ? values.file[0] : null,
          confirmOverwrite: values.confirmOverwrite === 'yes',
        },
        {
          method: 'POST',
          headers: {
            Authorization: `Bearer ${localStorage.getItem('authToken')}`,
          },
        }
      );
      
      clearInterval(progressInterval);
      
      if (response.success) {
        setRestoreProgress(100);
        message.success('数据库恢复成功');
        setRestoreModalVisible(false);
        restoreForm.resetFields();
        
        // 重新加载备份列表
        setTimeout(() => {
          loadDatabaseInfo();
        }, 1000);
      } else {
        setError(response.error || '恢复数据库失败');
        message.error('恢复数据库失败');
      }
    } catch (err) {
      setError('恢复数据库时发生错误');
      console.error('恢复数据库错误:', err);
      message.error('恢复数据库时发生错误');
    } finally {
      setTimeout(() => {
        setRestoreInProgress(false);
        setRestoreProgress(0);
      }, 1000);
    }
  };

  // 下载备份
  const handleDownloadBackup = async (backup: any) => {
    try {
      const response = await fetchData(
        `admin/database/backup/${backup.id}/download`,
        'downloadDatabaseBackup',
        {},
        {
          method: 'GET',
          headers: {
            Authorization: `Bearer ${localStorage.getItem('authToken')}`,
          },
        }
      );
      
      if (response.success && response.downloadUrl) {
        // 创建下载链接
        const a = document.createElement('a');
        a.href = response.downloadUrl;
        a.download = `backup_${backup.id}_${dayjs(backup.createdAt).format('YYYYMMDD_HHmmss')}.sql`;
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);
        
        message.success('备份下载已开始');
      } else {
        message.error(response.error || '下载备份失败');
      }
    } catch (err) {
      console.error('下载备份错误:', err);
      message.error('下载备份时发生错误');
    }
  };

  // 删除备份
  const handleDeleteBackup = async (backup: any) => {
    try {
      const response = await fetchData(
        `admin/database/backup/${backup.id}`,
        'deleteDatabaseBackup',
        {},
        {
          method: 'DELETE',
          headers: {
            Authorization: `Bearer ${localStorage.getItem('authToken')}`,
          },
        }
      );
      
      if (response.success) {
        message.success('备份已删除');
        
        // 重新加载备份列表
        loadDatabaseInfo();
      } else {
        message.error(response.error || '删除备份失败');
      }
    } catch (err) {
      console.error('删除备份错误:', err);
      message.error('删除备份时发生错误');
    }
  };

  // 确认恢复备份
  const confirmRestore = (backup: any) => {
    confirm({
      title: '确认恢复备份',
      icon: <ExclamationCircleOutlined />,
      content: (
        <div>
          <p>您确定要恢复此备份吗？</p>
          <p>备份时间: {dayjs(backup.createdAt).format('YYYY-MM-DD HH:mm:ss')}</p>
          <p>描述: {backup.description}</p>
          <p><strong>警告：此操作将覆盖当前数据库中的所有数据！</strong></p>
        </div>
      ),
      onOk() {
        setSelectedBackup(backup);
        setRestoreModalVisible(true);
        restoreForm.setFieldsValue({
          confirmOverwrite: 'no',
        });
      },
    });
  };

  // 备份表格列定义
  const backupColumns = [
    {
      title: 'ID',
      dataIndex: 'id',
      key: 'id',
      width: 80,
    },
    {
      title: '创建时间',
      dataIndex: 'createdAt',
      key: 'createdAt',
      render: (text: string) => dayjs(text).format('YYYY-MM-DD HH:mm:ss'),
      width: 180,
    },
    {
      title: '描述',
      dataIndex: 'description',
      key: 'description',
      ellipsis: true,
    },
    {
      title: '大小',
      dataIndex: 'size',
      key: 'size',
      width: 120,
      render: (size: number) => {
        if (size < 1024) {
          return `${size} B`;
        } else if (size < 1024 * 1024) {
          return `${(size / 1024).toFixed(2)} KB`;
        } else {
          return `${(size / (1024 * 1024)).toFixed(2)} MB`;
        }
      },
    },
    {
      title: '包含数据',
      dataIndex: 'includeData',
      key: 'includeData',
      width: 100,
      render: (includeData: boolean) => includeData ? <Badge status="success" text="是" /> : <Badge status="default" text="否" />,
    },
    {
      title: '操作',
      key: 'action',
      width: 200,
      render: (_: any, record: any) => (
        <Space>
          <Button 
            type="primary" 
            size="small" 
            icon={<DownloadOutlined />} 
            onClick={() => handleDownloadBackup(record)}
          >
            下载
          </Button>
          <Button 
            size="small" 
            icon={<SyncOutlined />} 
            onClick={() => confirmRestore(record)}
          >
            恢复
          </Button>
          <Popconfirm
            title="确定要删除此备份吗？"
            onConfirm={() => handleDeleteBackup(record)}
            okText="确定"
            cancelText="取消"
          >
            <Button 
              danger 
              size="small" 
              icon={<DeleteOutlined />}
            >
              删除
            </Button>
          </Popconfirm>
        </Space>
      ),
    },
  ];

  // 迁移状态表格列定义
  const migrationColumns = [
    {
      title: '迁移ID',
      dataIndex: 'id',
      key: 'id',
      width: 80,
    },
    {
      title: '文件名',
      dataIndex: 'filename',
      key: 'filename',
      ellipsis: true,
    },
    {
      title: '应用时间',
      dataIndex: 'appliedAt',
      key: 'appliedAt',
      width: 180,
      render: (text: string) => text ? dayjs(text).format('YYYY-MM-DD HH:mm:ss') : '-',
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      width: 100,
      render: (status: string) => {
        if (status === 'applied') {
          return <Badge status="success" text="已应用" />;
        } else if (status === 'pending') {
          return <Badge status="warning" text="待应用" />;
        } else {
          return <Badge status="error" text="失败" />;
        }
      },
    },
  ];

  return (
    <div className="database-manager">
      <Tabs defaultActiveKey="backups">
        <TabPane 
          tab={
            <span>
              <DatabaseOutlined />
              数据库概览
            </span>
          } 
          key="overview"
        >
          <Card>
            <Spin spinning={loading}>
              {error && (
                <Alert
                  message="错误"
                  description={error}
                  type="error"
                  showIcon
                  style={{ marginBottom: 16 }}
                />
              )}
              
              <Title level={4}>数据库状态</Title>
              
              <Row gutter={16}>
                <Col span={8}>
                  <Card bordered={false}>
                    <Statistic
                      title="数据库大小"
                      value={dbStats.size ? (dbStats.size / (1024 * 1024)).toFixed(2) : 0}
                      suffix="MB"
                    />
                    <div style={{ marginTop: 8 }}>
                      <Progress 
                        percent={dbStats.size ? (dbStats.size / (1024 * 1024 * 1000) * 100) : 0} 
                        status={(dbStats.size || 0) > 1024 * 1024 * 900 ? 'exception' : 'normal'} 
                        size="small"
                      />
                    </div>
                  </Card>
                </Col>
                <Col span={8}>
                  <Card bordered={false}>
                    <Statistic
                      title="表数量"
                      value={dbStats.tableCount || 0}
                    />
                  </Card>
                </Col>
                <Col span={8}>
                  <Card bordered={false}>
                    <Statistic
                      title="记录总数"
                      value={dbStats.recordCount || 0}
                    />
                  </Card>
                </Col>
              </Row>
              
              <Divider />
              
              <Title level={4}>表统计</Title>
              
              {dbStats.tables && (
                <Table
                  dataSource={dbStats.tables}
                  rowKey="name"
                  pagination={false}
                  size="small"
                  columns={[
                    {
                      title: '表名',
                      dataIndex: 'name',
                      key: 'name',
                    },
                    {
                      title: '记录数',
                      dataIndex: 'recordCount',
                      key: 'recordCount',
                      width: 120,
                    },
                    {
                      title: '大小',
                      dataIndex: 'size',
                      key: 'size',
                      width: 120,
                      render: (size: number) => {
                        if (size < 1024) {
                          return `${size} B`;
                        } else if (size < 1024 * 1024) {
                          return `${(size / 1024).toFixed(2)} KB`;
                        } else {
                          return `${(size / (1024 * 1024)).toFixed(2)} MB`;
                        }
                      },
                    },
                  ]}
                />
              )}
            </Spin>
          </Card>
        </TabPane>
        
        <TabPane 
          tab={
            <span>
              <SaveOutlined />
              备份管理
            </span>
          } 
          key="backups"
        >
          <Card>
            <div style={{ marginBottom: 16, display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
              <Title level={4}>数据库备份</Title>
              <Space>
                <Button
                  type="primary"
                  icon={<SaveOutlined />}
                  onClick={() => setBackupModalVisible(true)}
                >
                  创建备份
                </Button>
                <Button
                  icon={<UploadOutlined />}
                  onClick={() => {
                    setSelectedBackup(null);
                    setRestoreModalVisible(true);
                  }}
                >
                  恢复备份
                </Button>
                <Button
                  icon={<ReloadOutlined />}
                  onClick={loadDatabaseInfo}
                  loading={loading}
                >
                  刷新
                </Button>
              </Space>
            </div>

            {error && (
              <Alert
                message="错误"
                description={error}
                type="error"
                showIcon
                style={{ marginBottom: 16 }}
              />
            )}

            <Table
              columns={backupColumns}
              dataSource={backups}
              rowKey="id"
              loading={loading}
              pagination={{ pageSize: 10 }}
            />
          </Card>
        </TabPane>
        
        <TabPane 
          tab={
            <span>
              <HistoryOutlined />
              迁移历史
            </span>
          } 
          key="migrations"
        >
          <Card>
            <div style={{ marginBottom: 16, display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
              <Title level={4}>数据库迁移历史</Title>
              <Button
                icon={<ReloadOutlined />}
                onClick={loadDatabaseInfo}
                loading={loading}
              >
                刷新
              </Button>
            </div>

            {error && (
              <Alert
                message="错误"
                description={error}
                type="error"
                showIcon
                style={{ marginBottom: 16 }}
              />
            )}

            <Table
              columns={migrationColumns}
              dataSource={migrationStatus}
              rowKey="id"
              loading={loading}
              pagination={{ pageSize: 10 }}
            />
          </Card>
        </TabPane>
      </Tabs>

      {/* 创建备份模态框 */}
      <Modal
        title="创建数据库备份"
        visible={backupModalVisible}
        onCancel={() => setBackupModalVisible(false)}
        footer={null}
        maskClosable={false}
      >
        <Form
          form={backupForm}
          layout="vertical"
          onFinish={handleCreateBackup}
          initialValues={{
            includeData: 'yes',
          }}
        >
          <Form.Item
            name="description"
            label="备份描述"
            rules={[{ required: true, message: '请输入备份描述' }]}
          >
            <Input placeholder="请输入备份描述" />
          </Form.Item>
          
          <Form.Item
            name="includeData"
            label="包含数据"
            rules={[{ required: true, message: '请选择是否包含数据' }]}
          >
            <Select>
              <Option value="yes">是</Option>
              <Option value="no">否（仅结构）</Option>
            </Select>
          </Form.Item>
          
          {backupInProgress && (
            <Form.Item>
              <div style={{ marginBottom: 16 }}>
                <Progress percent={backupProgress} status="active" />
              </div>
              <div style={{ textAlign: 'center' }}>
                {backupProgress < 100 ? '正在创建备份...' : '备份创建成功！'}
              </div>
            </Form.Item>
          )}
          
          <Form.Item>
            <div style={{ textAlign: 'right' }}>
              <Space>
                <Button onClick={() => setBackupModalVisible(false)}>取消</Button>
                <Button type="primary" htmlType="submit" loading={backupInProgress}>
                  创建备份
                </Button>
              </Space>
            </div>
          </Form.Item>
        </Form>
      </Modal>

      {/* 恢复备份模态框 */}
      <Modal
        title="恢复数据库备份"
        visible={restoreModalVisible}
        onCancel={() => setRestoreModalVisible(false)}
        footer={null}
        maskClosable={false}
      >
        <Alert
          message="警告"
          description="恢复备份将覆盖当前数据库中的所有数据，请确保已备份重要数据。"
          type="warning"
          showIcon
          style={{ marginBottom: 16 }}
        />
        
        <Form
          form={restoreForm}
          layout="vertical"
          onFinish={handleRestoreBackup}
        >
          {selectedBackup ? (
            <Form.Item label="选中的备份">
              <Input 
                value={`${selectedBackup.id} - ${dayjs(selectedBackup.createdAt).format('YYYY-MM-DD HH:mm:ss')} - ${selectedBackup.description}`} 
                disabled 
              />
            </Form.Item>
          ) : (
            <Form.Item
              name="file"
              label="上传备份文件"
              rules={[{ required: true, message: '请上传备份文件' }]}
            >
              <Dragger
                name="file"
                multiple={false}
                beforeUpload={() => false}
                accept=".sql,.gz"
              >
                <p className="ant-upload-drag-icon">
                  <UploadOutlined />
                </p>
                <p className="ant-upload-text">点击或拖拽文件到此区域上传</p>
                <p className="ant-upload-hint">支持 .sql 或 .gz 格式的备份文件</p>
              </Dragger>
            </Form.Item>
          )}
          
          <Form.Item
            name="confirmOverwrite"
            label="确认覆盖"
            rules={[
              { required: true, message: '请确认是否覆盖当前数据' },
              { validator: (_, value) => value === 'yes' ? Promise.resolve() : Promise.reject('请确认覆盖当前数据') }
            ]}
          >
            <Select>
              <Option value="no">我不确认</Option>
              <Option value="yes">我确认覆盖当前所有数据</Option>
            </Select>
          </Form.Item>
          
          {restoreInProgress && (
            <Form.Item>
              <div style={{ marginBottom: 16 }}>
                <Progress percent={restoreProgress} status="active" />
              </div>
              <div style={{ textAlign: 'center' }}>
                {restoreProgress < 100 ? '正在恢复备份...' : '备份恢复成功！'}
              </div>
            </Form.Item>
          )}
          
          <Form.Item>
            <div style={{ textAlign: 'right' }}>
              <Space>
                <Button onClick={() => setRestoreModalVisible(false)}>取消</Button>
                <Button type="primary" danger htmlType="submit" loading={restoreInProgress}>
                  恢复备份
                </Button>
              </Space>
            </div>
          </Form.Item>
        </Form>
      </Modal>
    </div>
  );
};

export default DatabaseManager;
