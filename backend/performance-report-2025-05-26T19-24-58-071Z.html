
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>性能分析报告</title>
    <style>
        body { font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif; margin: 0; padding: 20px; background: #f5f5f5; }
        .container { max-width: 1200px; margin: 0 auto; background: white; border-radius: 8px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        .header { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 30px; border-radius: 8px 8px 0 0; }
        .content { padding: 30px; }
        .metric-card { display: inline-block; background: #f8f9fa; border-radius: 8px; padding: 20px; margin: 10px; text-align: center; min-width: 150px; }
        .metric-value { font-size: 2em; font-weight: bold; margin-bottom: 5px; }
        .excellent { color: #28a745; }
        .good { color: #17a2b8; }
        .fair { color: #ffc107; }
        .poor { color: #fd7e14; }
        .critical { color: #dc3545; }
        .section { margin: 30px 0; }
        .section h2 { color: #333; border-bottom: 2px solid #eee; padding-bottom: 10px; }
        .endpoint-table { width: 100%; border-collapse: collapse; margin: 20px 0; }
        .endpoint-table th, .endpoint-table td { padding: 12px; text-align: left; border-bottom: 1px solid #ddd; }
        .endpoint-table th { background-color: #f8f9fa; font-weight: 600; }
        .recommendation { background: #f8f9fa; border-left: 4px solid #007bff; padding: 15px; margin: 10px 0; border-radius: 4px; }
        .high { border-left-color: #dc3545; }
        .medium { border-left-color: #ffc107; }
        .low { border-left-color: #28a745; }
        .chart { margin: 20px 0; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🚀 性能分析报告</h1>
            <p>基准URL: https://college-employment-survey.aibook2099.workers.dev</p>
            <p>生成时间: 2025/5/27 03:24:37</p>
        </div>
        
        <div class="content">
            <div class="section">
                <h2>📊 总体性能指标</h2>
                <div class="metric-card">
                    <div class="metric-value good">145.82ms</div>
                    <div>平均响应时间</div>
                </div>
                <div class="metric-card">
                    <div class="metric-value">168.14ms</div>
                    <div>P95响应时间</div>
                </div>
                <div class="metric-card">
                    <div class="metric-value good">good</div>
                    <div>性能等级</div>
                </div>
                <div class="metric-card">
                    <div class="metric-value">7</div>
                    <div>测试端点数</div>
                </div>
            </div>

            <div class="section">
                <h2>🎯 端点性能详情</h2>
                <table class="endpoint-table">
                    <thead>
                        <tr>
                            <th>端点</th>
                            <th>平均时间</th>
                            <th>最小时间</th>
                            <th>最大时间</th>
                            <th>P95时间</th>
                            <th>成功率</th>
                        </tr>
                    </thead>
                    <tbody>
                        
                            <tr>
                                <td><code>/</code></td>
                                <td>98.13ms</td>
                                <td>94ms</td>
                                <td>105ms</td>
                                <td>105ms</td>
                                <td>100.0%</td>
                            </tr>
                        
                            <tr>
                                <td><code>/health</code></td>
                                <td>97.27ms</td>
                                <td>94ms</td>
                                <td>101ms</td>
                                <td>101ms</td>
                                <td>100.0%</td>
                            </tr>
                        
                            <tr>
                                <td><code>/api/system/health</code></td>
                                <td>98.27ms</td>
                                <td>94ms</td>
                                <td>106ms</td>
                                <td>106ms</td>
                                <td>100.0%</td>
                            </tr>
                        
                            <tr>
                                <td><code>/api/questionnaire/stats</code></td>
                                <td>316.2ms</td>
                                <td>268ms</td>
                                <td>411ms</td>
                                <td>411ms</td>
                                <td>100.0%</td>
                            </tr>
                        
                            <tr>
                                <td><code>/api/story/list?page=1&pageSize=10</code></td>
                                <td>137.07ms</td>
                                <td>129ms</td>
                                <td>148ms</td>
                                <td>148ms</td>
                                <td>100.0%</td>
                            </tr>
                        
                            <tr>
                                <td><code>/api/story/list?page=1&pageSize=50</code></td>
                                <td>135.27ms</td>
                                <td>130ms</td>
                                <td>151ms</td>
                                <td>151ms</td>
                                <td>100.0%</td>
                            </tr>
                        
                            <tr>
                                <td><code>/api/story/list?page=1&pageSize=100</code></td>
                                <td>138.53ms</td>
                                <td>130ms</td>
                                <td>155ms</td>
                                <td>155ms</td>
                                <td>100.0%</td>
                            </tr>
                        
                    </tbody>
                </table>
            </div>

            
            <div class="section">
                <h2>🔄 并发性能</h2>
                <table class="endpoint-table">
                    <thead>
                        <tr>
                            <th>并发数</th>
                            <th>总时间</th>
                            <th>平均时间</th>
                            <th>吞吐量 (req/s)</th>
                        </tr>
                    </thead>
                    <tbody>
                        
                            <tr>
                                <td>1</td>
                                <td>315ms</td>
                                <td>315ms</td>
                                <td>3.17</td>
                            </tr>
                        
                            <tr>
                                <td>5</td>
                                <td>450ms</td>
                                <td>90ms</td>
                                <td>11.11</td>
                            </tr>
                        
                            <tr>
                                <td>10</td>
                                <td>460ms</td>
                                <td>46ms</td>
                                <td>21.74</td>
                            </tr>
                        
                            <tr>
                                <td>20</td>
                                <td>616ms</td>
                                <td>30.8ms</td>
                                <td>32.47</td>
                            </tr>
                        
                    </tbody>
                </table>
            </div>
            

            <div class="section">
                <h2>💡 优化建议</h2>
                
                    <div class="recommendation low">
                        <h4>通用性能优化</h4>
                        <p>持续性能改进建议</p>
                        <ul>
                            <li>定期运行性能测试</li><li>监控生产环境性能指标</li><li>建立性能预算</li><li>实施性能回归测试</li>
                        </ul>
                    </div>
                
            </div>
        </div>
    </div>
</body>
</html>