-- 🗄️ 数据库性能优化脚本
-- 生成时间: 2025-05-26T19:27:46.201Z
-- 目标: 优化问卷统计和故事列表查询性能

-- ============================================
-- 索引优化
-- ============================================

-- questionnaire_responses_v2 表索引优化
-- 优化时间范围查询（今日、本周、本月统计） (影响: high)
CREATE INDEX IF NOT EXISTS idx_questionnaire_created_at ON questionnaire_responses_v2(created_at);

-- 优化专业分布统计查询 (影响: high)
CREATE INDEX IF NOT EXISTS idx_questionnaire_major_display ON questionnaire_responses_v2(major_display) WHERE major_display IS NOT NULL;

-- 优化就业状态统计查询 (影响: medium)
CREATE INDEX IF NOT EXISTS idx_questionnaire_employment_status ON questionnaire_responses_v2(employment_status_display);

-- 优化教育水平统计查询 (影响: medium)
CREATE INDEX IF NOT EXISTS idx_questionnaire_education_level ON questionnaire_responses_v2(education_level_display);

-- 优化毕业年份统计查询 (影响: medium)
CREATE INDEX IF NOT EXISTS idx_questionnaire_graduation_year ON questionnaire_responses_v2(graduation_year);

-- 优化匿名/实名统计查询 (影响: low)
CREATE INDEX IF NOT EXISTS idx_questionnaire_anonymous ON questionnaire_responses_v2(is_anonymous);

-- 优化复合统计查询 (影响: high)
CREATE INDEX IF NOT EXISTS idx_questionnaire_composite_stats ON questionnaire_responses_v2(created_at, major_display, employment_status_display);


-- story_contents_v2 表索引优化
-- 优化故事列表时间排序 (影响: high)
CREATE INDEX IF NOT EXISTS idx_story_created_at ON story_contents_v2(created_at DESC);

-- 优化故事状态过滤 (影响: medium)
CREATE INDEX IF NOT EXISTS idx_story_status ON story_contents_v2(status);

-- 优化故事列表查询（状态+时间） (影响: high)
CREATE INDEX IF NOT EXISTS idx_story_composite_list ON story_contents_v2(status, created_at DESC);


-- ============================================
-- 查询优化示例
-- ============================================

-- 优化版统计查询（替代多个独立查询）
-- 预期性能提升: 70%
CREATE VIEW IF NOT EXISTS questionnaire_stats_optimized AS
WITH base_stats AS (
  SELECT 
    COUNT(*) as total_responses,
    COUNT(CASE WHEN is_anonymous = 0 THEN 1 END) as verified_count,
    COUNT(CASE WHEN is_anonymous = 1 THEN 1 END) as anonymous_count,
    COUNT(CASE WHEN employment_status_display = '已就业' THEN 1 END) as employed_count,
    COUNT(CASE WHEN employment_status_display = '未就业' THEN 1 END) as unemployed_count
  FROM questionnaire_responses_v2
)
SELECT * FROM base_stats;

-- ============================================
-- 性能监控查询
-- ============================================

-- 检查索引使用情况
-- EXPLAIN QUERY PLAN SELECT * FROM questionnaire_responses_v2 WHERE created_at >= date('now', '-7 days');

-- 检查表统计信息
-- SELECT name, sql FROM sqlite_master WHERE type='index' AND tbl_name='questionnaire_responses_v2';

-- ============================================
-- 维护建议
-- ============================================

-- 1. 定期运行 ANALYZE 命令更新统计信息
-- ANALYZE;

-- 2. 定期检查查询计划
-- 使用 EXPLAIN QUERY PLAN 分析慢查询

-- 3. 监控数据库大小和性能
-- 定期运行性能测试脚本
