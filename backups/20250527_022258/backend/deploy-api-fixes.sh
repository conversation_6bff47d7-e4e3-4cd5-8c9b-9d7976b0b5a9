#!/bin/bash

# 部署修复后的API到生产环境
# 这个脚本将部署我们修复的API代码到Cloudflare Workers

set -e  # 遇到错误立即退出

# 颜色定义
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

echo -e "${BLUE}🚀 开始部署修复后的API到生产环境${NC}"
echo "========================================"

# 检查当前目录
if [ ! -f "wrangler.toml" ]; then
    echo -e "${RED}❌ 错误: 未找到wrangler.toml文件${NC}"
    echo "请确保在backend目录中运行此脚本"
    exit 1
fi

# 检查wrangler是否已安装
if ! command -v wrangler &> /dev/null; then
    echo -e "${RED}❌ 错误: 未找到wrangler命令${NC}"
    echo "请先安装wrangler: npm install -g wrangler"
    exit 1
fi

# 检查登录状态
echo -e "${YELLOW}🔐 检查Cloudflare登录状态...${NC}"
if ! wrangler whoami &> /dev/null; then
    echo -e "${YELLOW}⚠️  您尚未登录Cloudflare，正在启动登录流程...${NC}"
    wrangler login
    
    # 再次检查登录状态
    if ! wrangler whoami &> /dev/null; then
        echo -e "${RED}❌ 登录失败，请手动运行 'wrangler login' 并重试${NC}"
        exit 1
    fi
fi
echo -e "${GREEN}✅ 已登录Cloudflare${NC}"

# 显示当前配置
echo -e "${YELLOW}📋 当前部署配置:${NC}"
echo "   项目名称: college-employment-survey-realapi"
echo "   入口文件: index.js"
echo "   数据库: college-employment-survey-realapi"

# 确认部署
echo ""
read -p "确认要部署到生产环境吗? (y/N): " -n 1 -r
echo
if [[ ! $REPLY =~ ^[Yy]$ ]]; then
    echo -e "${YELLOW}⏹️  部署已取消${NC}"
    exit 0
fi

# 开始部署
echo -e "${BLUE}📦 开始部署...${NC}"

# 部署到Cloudflare Workers
echo -e "${YELLOW}🔄 部署API到Cloudflare Workers...${NC}"
if wrangler deploy; then
    echo -e "${GREEN}✅ API部署成功${NC}"
else
    echo -e "${RED}❌ API部署失败${NC}"
    exit 1
fi

# 等待部署生效
echo -e "${YELLOW}⏳ 等待部署生效 (10秒)...${NC}"
sleep 10

# 测试API健康状态
echo -e "${YELLOW}🧪 测试API健康状态...${NC}"
API_URL="https://college-employment-survey-realapi.pages.dev"

if curl -f -s "$API_URL/health" > /dev/null 2>&1; then
    echo -e "${GREEN}✅ API健康检查通过${NC}"
else
    echo -e "${YELLOW}⚠️  API健康检查失败，可能需要等待更长时间生效${NC}"
fi

# 测试问卷统计API
echo -e "${YELLOW}🧪 测试问卷统计API...${NC}"
STATS_RESPONSE=$(curl -s "$API_URL/api/questionnaire/stats" | jq -r '.data.total // "error"' 2>/dev/null || echo "error")

if [ "$STATS_RESPONSE" != "error" ] && [ "$STATS_RESPONSE" != "null" ]; then
    echo -e "${GREEN}✅ 问卷统计API正常，返回数据: $STATS_RESPONSE 条记录${NC}"
else
    echo -e "${YELLOW}⚠️  问卷统计API可能还未生效，返回: $STATS_RESPONSE${NC}"
fi

# 测试问卷心声API
echo -e "${YELLOW}🧪 测试问卷心声API...${NC}"
VOICES_RESPONSE=$(curl -s "$API_URL/api/questionnaire-voices?limit=1" | jq -r '.data.pagination.total // "error"' 2>/dev/null || echo "error")

if [ "$VOICES_RESPONSE" != "error" ] && [ "$VOICES_RESPONSE" != "null" ]; then
    echo -e "${GREEN}✅ 问卷心声API正常，返回数据: $VOICES_RESPONSE 条记录${NC}"
else
    echo -e "${YELLOW}⚠️  问卷心声API可能还未生效，返回: $VOICES_RESPONSE${NC}"
fi

# 部署完成
echo ""
echo -e "${GREEN}🎉 部署完成！${NC}"
echo "========================================"
echo -e "${BLUE}📊 部署信息:${NC}"
echo "   API地址: $API_URL"
echo "   健康检查: $API_URL/health"
echo "   问卷统计: $API_URL/api/questionnaire/stats"
echo "   问卷心声: $API_URL/api/questionnaire-voices"
echo "   可视化数据: $API_URL/api/visualization/data"
echo ""
echo -e "${YELLOW}📝 注意事项:${NC}"
echo "   1. 部署可能需要几分钟才能完全生效"
echo "   2. 如果API仍返回旧数据，请等待5-10分钟后重试"
echo "   3. 可以在Cloudflare Dashboard中查看部署状态和日志"
echo ""
echo -e "${GREEN}✅ 修复后的API已成功部署到生产环境！${NC}"
