import React, { useState, useEffect } from 'react';
import ReviewerLayout from '@/components/layouts/ReviewerLayout';
import { Card, CardContent, CardDescription, CardHeader, CardTitle, CardFooter } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Badge } from '@/components/ui/badge';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from '@/components/ui/alert-dialog';
import {
  CheckCircle, XCircle, Search, Filter, Clock,
  ThumbsUp, ThumbsDown, AlertTriangle, Tag, FileText
} from 'lucide-react';
import { useToast } from '@/components/ui/use-toast';
import { getPendingContents, approveContent, editContent } from '@/services/dataService';



/**
 * 审核员内容审核页面
 *
 * 用于审核员审核用户提交的各类内容（评论、经验分享等）
 */
const ReviewerContentReviewPage: React.FC = () => {
  const { toast } = useToast();
  const [contents, setContents] = useState<any[]>([]);
  const [selectedContent, setSelectedContent] = useState<any>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [rejectDialogOpen, setRejectDialogOpen] = useState(false);
  const [rejectReason, setRejectReason] = useState('');

  // 加载待审核内容
  const loadPendingContents = async () => {
    try {
      setIsLoading(true);

      const response = await getPendingContents('pageSize=50');

      if (response.success && response.data) {
        setContents(response.data);
        if (response.data.length > 0) {
          setSelectedContent(response.data[0]);
        }
      } else {
        console.error('获取待审核内容失败:', response.error);
        setContents([]);
        setSelectedContent(null);
      }
    } catch (error) {
      console.error('加载待审核内容时发生错误:', error);
      setContents([]);
      setSelectedContent(null);
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    loadPendingContents();
  }, []);

  // 处理内容审核
  const handleReviewContent = async (contentId: string, approved: boolean) => {
    if (!approved) {
      setRejectDialogOpen(true);
      return;
    }

    try {
      // 调用API批准内容
      const response = await approveContent(contentId, { reviewNotes: '审核通过' });

      if (response.success) {
        // 从列表中移除已审核的内容
        const updatedContents = contents.filter(content => content.id !== contentId);
        setContents(updatedContents);

        if (updatedContents.length > 0) {
          setSelectedContent(updatedContents[0]);
        } else {
          setSelectedContent(null);
        }

        toast({
          title: '审核成功',
          description: '内容已批准并发布',
        });
      } else {
        toast({
          title: '审核失败',
          description: response.error || '批准内容时发生错误',
          variant: 'destructive',
        });
      }
    } catch (error) {
      console.error('批准内容时发生错误:', error);
      toast({
        title: '审核失败',
        description: '网络错误，请稍后再试',
        variant: 'destructive',
      });
    }
  };

  // 处理拒绝确认
  const handleRejectConfirm = async () => {
    if (!selectedContent) return;

    try {
      // 这里应该调用拒绝API，暂时使用本地处理
      const updatedContents = contents.filter(content => content.id !== selectedContent.id);
      setContents(updatedContents);

      if (updatedContents.length > 0) {
        setSelectedContent(updatedContents[0]);
      } else {
        setSelectedContent(null);
      }

      toast({
        title: '审核成功',
        description: '内容已拒绝',
        variant: 'destructive',
      });

      setRejectDialogOpen(false);
      setRejectReason('');
    } catch (error) {
      console.error('拒绝内容时发生错误:', error);
      toast({
        title: '审核失败',
        description: '网络错误，请稍后再试',
        variant: 'destructive',
      });
    }
  };

  // 获取内容类型标签
  const getContentTypeLabel = (type: string) => {
    switch (type) {
      case 'story':
        return '故事';
      case 'comment':
        return '评论';
      case 'experience':
        return '经验分享';
      case 'advice':
        return '求职建议';
      case 'questionnaire':
        return '问卷';
      default:
        return '其他';
    }
  };

  // 获取内容标题
  const getContentTitle = (content: any) => {
    if (content.originalContent?.title) {
      return content.originalContent.title;
    }
    if (content.sanitizedContent?.title) {
      return content.sanitizedContent.title;
    }
    return `${getContentTypeLabel(content.type)} - ${content.sequenceNumber}`;
  };

  // 获取内容文本
  const getContentText = (content: any) => {
    if (content.originalContent?.content) {
      return content.originalContent.content;
    }
    if (content.sanitizedContent?.content) {
      return content.sanitizedContent.content;
    }
    return '无内容';
  };

  return (
    <ReviewerLayout>
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-2xl font-bold">内容审核</h1>
        <div className="flex items-center space-x-2">
          <div className="relative">
            <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
            <Input
              type="search"
              placeholder="搜索内容..."
              className="pl-8 w-[200px]"
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
            />
          </div>
          <Button variant="outline" size="sm">
            <Filter className="mr-2 h-4 w-4" />
            筛选
          </Button>
        </div>
      </div>

      {isLoading ? (
        <div className="flex justify-center items-center h-[400px]">
          <p>加载中...</p>
        </div>
      ) : contents.length === 0 ? (
        <Card>
          <CardContent className="flex flex-col items-center justify-center h-[400px]">
            <AlertTriangle className="h-16 w-16 text-muted-foreground mb-4" />
            <h2 className="text-xl font-semibold mb-2">没有待审核的内容</h2>
            <p className="text-muted-foreground">当前没有需要审核的内容</p>
          </CardContent>
        </Card>
      ) : (
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          {/* 内容列表 */}
          <div className="md:col-span-1">
            <Card>
              <CardHeader>
                <CardTitle>待审核内容 ({contents.length})</CardTitle>
                <CardDescription>
                  点击内容查看详情
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-2">
                  {contents.map(content => (
                    <div
                      key={content.id}
                      className={`p-3 border rounded-md cursor-pointer transition-colors ${
                        selectedContent?.id === content.id
                          ? 'border-primary bg-primary/5'
                          : 'hover:bg-muted'
                      }`}
                      onClick={() => setSelectedContent(content)}
                    >
                      <div className="flex justify-between items-start">
                        <h3 className="font-medium truncate">{getContentTitle(content)}</h3>
                        <Badge variant="outline" className="ml-2 shrink-0">
                          {getContentTypeLabel(content.type)}
                        </Badge>
                      </div>
                      <p className="text-sm text-muted-foreground mt-1 line-clamp-2">
                        {getContentText(content)}
                      </p>
                      <div className="flex items-center mt-2 text-xs text-muted-foreground">
                        <Clock className="h-3 w-3 mr-1" />
                        {new Date(content.createdAt).toLocaleString()}
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </div>

          {/* 内容详情 */}
          <div className="md:col-span-2">
            {selectedContent ? (
              <Card>
                <CardHeader>
                  <div className="flex justify-between items-start">
                    <div>
                      <CardTitle>{getContentTitle(selectedContent)}</CardTitle>
                      <CardDescription>
                        序列号: {selectedContent.sequenceNumber} | 提交时间: {new Date(selectedContent.createdAt).toLocaleString()}
                      </CardDescription>
                    </div>
                    <Badge variant="outline">
                      {getContentTypeLabel(selectedContent.type)}
                    </Badge>
                  </div>
                </CardHeader>
                <CardContent>
                  <div className="border rounded-md p-4 min-h-[200px] mb-4">
                    {getContentText(selectedContent)}
                  </div>

                  <div className="mt-4">
                    <h3 className="text-sm font-medium mb-2">提交信息</h3>
                    <div className="grid grid-cols-2 gap-4 text-sm">
                      <div>
                        <span className="text-muted-foreground">状态：</span>
                        <span>{selectedContent.status}</span>
                      </div>
                      <div>
                        <span className="text-muted-foreground">类型：</span>
                        <span>{getContentTypeLabel(selectedContent.type)}</span>
                      </div>
                      {selectedContent.flags && selectedContent.flags !== 'normal' && (
                        <div className="col-span-2">
                          <span className="text-muted-foreground">标记：</span>
                          <div className="flex flex-wrap gap-1 mt-1">
                            {selectedContent.flags.split(',').map((flag: string, index: number) => (
                              <Badge key={index} variant="secondary" className="text-xs">
                                {flag.trim()}
                              </Badge>
                            ))}
                          </div>
                        </div>
                      )}
                    </div>
                  </div>
                </CardContent>
                <CardFooter className="flex justify-between">
                  <Button
                    variant="outline"
                    onClick={() => handleReviewContent(selectedContent.id, false)}
                  >
                    <ThumbsDown className="mr-2 h-4 w-4" />
                    拒绝
                  </Button>
                  <Button
                    onClick={() => handleReviewContent(selectedContent.id, true)}
                  >
                    <ThumbsUp className="mr-2 h-4 w-4" />
                    批准
                  </Button>
                </CardFooter>
              </Card>
            ) : (
              <Card>
                <CardContent className="flex flex-col items-center justify-center h-[400px]">
                  <p className="text-muted-foreground">请从左侧选择一个内容进行审核</p>
                </CardContent>
              </Card>
            )}
          </div>
        </div>
      )}

      {/* 拒绝原因对话框 */}
      <AlertDialog open={rejectDialogOpen} onOpenChange={setRejectDialogOpen}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>拒绝内容</AlertDialogTitle>
            <AlertDialogDescription>
              请提供拒绝原因，这将帮助用户理解为什么他们的内容被拒绝。
            </AlertDialogDescription>
          </AlertDialogHeader>
          <div className="py-4">
            <Textarea
              placeholder="请输入拒绝原因..."
              value={rejectReason}
              onChange={(e) => setRejectReason(e.target.value)}
              className="min-h-[100px]"
            />
          </div>
          <AlertDialogFooter>
            <AlertDialogCancel>取消</AlertDialogCancel>
            <AlertDialogAction onClick={handleRejectConfirm}>确认拒绝</AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </ReviewerLayout>
  );
};

export default ReviewerContentReviewPage;
