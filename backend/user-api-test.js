/**
 * 用户管理API测试服务器
 */

const express = require('express');
const cors = require('cors');
const sqlite3 = require('sqlite3').verbose();
const path = require('path');

const app = express();
const PORT = 8787;

// 中间件
app.use(cors({
  origin: [
    'http://localhost:5173',
    'http://localhost:5174',
    'http://localhost:5175',
    'http://localhost:5176',
    'http://localhost:5177'
  ],
  credentials: true
}));
app.use(express.json());

// 数据库连接
const dbPath = path.join(__dirname, 'prisma', 'dev.db');
const db = new sqlite3.Database(dbPath);

// 日志中间件
app.use((req, res, next) => {
  console.log(`${new Date().toISOString()} ${req.method} ${req.url}`);
  next();
});

// 用户管理API
app.get('/api/admin/users', async (req, res) => {
  try {
    console.log('🔍 获取用户列表请求');
    console.log('查询参数:', req.query);

    const { role, status, search, page = 1, pageSize = 10 } = req.query;

    // 构建查询
    let query = `
      SELECT 
        uuid,
        username,
        email,
        role,
        status,
        created_at,
        updated_at,
        last_login_at
      FROM users
      WHERE 1=1
    `;

    const params = [];

    // 添加角色过滤
    if (role && role !== 'all') {
      query += ' AND role = ?';
      params.push(role);
    }

    // 添加状态过滤
    if (status) {
      query += ' AND status = ?';
      params.push(status);
    }

    // 添加搜索过滤
    if (search) {
      query += ' AND (username LIKE ? OR email LIKE ?)';
      params.push(`%${search}%`, `%${search}%`);
    }

    // 添加排序
    query += ' ORDER BY created_at DESC';

    // 添加分页
    const offset = (page - 1) * pageSize;
    query += ' LIMIT ? OFFSET ?';
    params.push(pageSize, offset);

    console.log('🔍 执行查询:', query);
    console.log('📝 查询参数:', params);

    // 执行查询
    db.all(query, params, (err, rows) => {
      if (err) {
        console.error('❌ 查询失败:', err);
        return res.status(500).json({
          success: false,
          error: '查询用户列表失败',
          details: err.message
        });
      }

      // 获取总数
      let countQuery = `
        SELECT COUNT(*) as total
        FROM users
        WHERE 1=1
      `;

      const countParams = [];

      // 添加相同的过滤条件
      if (role && role !== 'all') {
        countQuery += ' AND role = ?';
        countParams.push(role);
      }

      if (status) {
        countQuery += ' AND status = ?';
        countParams.push(status);
      }

      if (search) {
        countQuery += ' AND (username LIKE ? OR email LIKE ?)';
        countParams.push(`%${search}%`, `%${search}%`);
      }

      db.get(countQuery, countParams, (countErr, countResult) => {
        if (countErr) {
          console.error('❌ 计数查询失败:', countErr);
          return res.status(500).json({
            success: false,
            error: '获取用户总数失败',
            details: countErr.message
          });
        }

        const total = countResult?.total || 0;

        console.log('📊 查询结果:', rows.length, '条记录');
        console.log('📊 总数:', total);

        res.json({
          success: true,
          data: {
            users: rows || [],
            pagination: {
              page: parseInt(page),
              pageSize: parseInt(pageSize),
              total,
              totalPages: Math.ceil(total / pageSize)
            }
          }
        });
      });
    });

  } catch (error) {
    console.error('❌ 获取用户列表失败:', error);
    res.status(500).json({
      success: false,
      error: '获取用户列表失败',
      details: error.message
    });
  }
});

// 获取单个用户详情
app.get('/api/admin/users/:id', (req, res) => {
  const userId = req.params.id;
  console.log('🔍 获取用户详情:', userId);

  const query = `
    SELECT 
      uuid,
      username,
      email,
      role,
      status,
      created_at,
      updated_at,
      last_login_at
    FROM users 
    WHERE uuid = ?
  `;

  db.get(query, [userId], (err, row) => {
    if (err) {
      console.error('❌ 查询失败:', err);
      return res.status(500).json({
        success: false,
        error: '获取用户详情失败',
        details: err.message
      });
    }

    if (!row) {
      return res.status(404).json({
        success: false,
        error: '用户不存在'
      });
    }

    res.json({
      success: true,
      data: row
    });
  });
});

// 创建用户
app.post('/api/admin/users', (req, res) => {
  const { username, email, password, role = 'user' } = req.body;

  console.log('🆕 创建用户:', { username, email, role });

  // 验证必填字段
  if (!username || !email || !password) {
    return res.status(400).json({
      success: false,
      error: '用户名、邮箱和密码为必填项'
    });
  }

  // 检查用户名是否已存在
  const checkQuery = 'SELECT uuid FROM users WHERE username = ? OR email = ?';
  
  db.get(checkQuery, [username, email], (err, existingUser) => {
    if (err) {
      console.error('❌ 检查用户失败:', err);
      return res.status(500).json({
        success: false,
        error: '检查用户失败',
        details: err.message
      });
    }

    if (existingUser) {
      return res.status(400).json({
        success: false,
        error: '用户名或邮箱已存在'
      });
    }

    // 生成UUID
    const uuid = require('crypto').randomUUID();
    const now = new Date().toISOString();

    // 插入用户
    const insertQuery = `
      INSERT INTO users (uuid, username, email, password_hash, role, status, created_at, updated_at)
      VALUES (?, ?, ?, ?, ?, 'active', ?, ?)
    `;

    db.run(insertQuery, [uuid, username, email, password, role, now, now], (err) => {
      if (err) {
        console.error('❌ 创建用户失败:', err);
        return res.status(500).json({
          success: false,
          error: '创建用户失败',
          details: err.message
        });
      }

      console.log('✅ 用户创建成功:', uuid);

      res.json({
        success: true,
        data: {
          uuid,
          username,
          email,
          role,
          status: 'active',
          created_at: now
        }
      });
    });
  });
});

// 健康检查
app.get('/health', (req, res) => {
  res.json({ status: 'ok', timestamp: new Date().toISOString() });
});

// 404 处理
app.use((req, res) => {
  console.log('❌ 404 Not Found:', req.method, req.url);
  res.status(404).json({ error: 'Not found' });
});

// 启动服务器
app.listen(PORT, () => {
  console.log('🚀 用户管理API测试服务器启动成功');
  console.log(`📡 服务地址: http://localhost:${PORT}`);
  console.log(`🔍 用户列表API: http://localhost:${PORT}/api/admin/users`);
  console.log(`💾 数据库路径: ${dbPath}`);
});
