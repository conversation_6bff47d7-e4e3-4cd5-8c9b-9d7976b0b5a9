/**
 * 风险评估服务
 * 提供操作风险评估功能
 */

import { Env } from '../types';
import { AuditLogService, AuditAction, AuditSeverity } from './auditLogService';
import { PermissionService, Permission, RiskLevel } from './permissionService';

// 风险因素类型
export enum RiskFactor {
  TIME_OF_DAY = 'time_of_day',           // 操作时间
  UNUSUAL_LOCATION = 'unusual_location', // 异常位置
  BATCH_OPERATION = 'batch_operation',   // 批量操作
  SENSITIVE_DATA = 'sensitive_data',     // 敏感数据
  UNUSUAL_PATTERN = 'unusual_pattern',   // 异常模式
  HIGH_FREQUENCY = 'high_frequency',     // 高频操作
  PERMISSION_LEVEL = 'permission_level', // 权限级别
  PREVIOUS_ALERTS = 'previous_alerts'    // 之前的警报
}

// 风险评估结果
export interface RiskAssessment {
  riskLevel: RiskLevel;
  riskScore: number;
  riskFactors: {
    factor: RiskFactor;
    score: number;
    description: string;
  }[];
  requiresConfirmation: boolean;
  requiresApproval: boolean;
  mitigationSteps?: string[];
}

/**
 * 风险评估服务
 */
export class RiskAssessmentService {
  private static instance: RiskAssessmentService;
  private permissionService: PermissionService;
  private auditLogService: AuditLogService;
  
  private constructor() {
    this.permissionService = PermissionService.getInstance();
    this.auditLogService = AuditLogService.getInstance();
  }
  
  /**
   * 获取风险评估服务实例
   */
  public static getInstance(): RiskAssessmentService {
    if (!RiskAssessmentService.instance) {
      RiskAssessmentService.instance = new RiskAssessmentService();
    }
    return RiskAssessmentService.instance;
  }
  
  /**
   * 评估操作风险
   * @param userId 用户ID
   * @param permission 权限
   * @param resourceType 资源类型
   * @param resourceId 资源ID
   * @param details 操作详情
   * @param clientInfo 客户端信息
   * @param env 环境变量
   */
  public async assessRisk(
    userId: string,
    permission: Permission,
    resourceType: string,
    resourceId: string | undefined,
    details: any,
    clientInfo: {
      ipAddress: string;
      userAgent: string;
      timestamp: Date;
    },
    env: Env
  ): Promise<RiskAssessment> {
    // 初始化风险评估结果
    const riskFactors: {
      factor: RiskFactor;
      score: number;
      description: string;
    }[] = [];
    
    // 1. 基于权限的风险评估
    const permissionRisk = this.permissionService.getPermissionRisk(permission);
    let baseRiskScore = 0;
    
    switch (permissionRisk) {
      case RiskLevel.CRITICAL:
        baseRiskScore = 80;
        break;
      case RiskLevel.HIGH:
        baseRiskScore = 60;
        break;
      case RiskLevel.MEDIUM:
        baseRiskScore = 40;
        break;
      case RiskLevel.LOW:
        baseRiskScore = 20;
        break;
    }
    
    riskFactors.push({
      factor: RiskFactor.PERMISSION_LEVEL,
      score: baseRiskScore,
      description: `操作需要 ${permissionRisk} 级别权限`
    });
    
    // 2. 时间因素评估
    const hour = clientInfo.timestamp.getHours();
    if (hour < 6 || hour > 22) {
      riskFactors.push({
        factor: RiskFactor.TIME_OF_DAY,
        score: 15,
        description: '非工作时间操作'
      });
    }
    
    // 3. 批量操作评估
    if (details && details.batchOperation) {
      const itemCount = details.totalCount || details.ids?.length || 0;
      let batchScore = 0;
      
      if (itemCount > 100) {
        batchScore = 30;
      } else if (itemCount > 50) {
        batchScore = 20;
      } else if (itemCount > 10) {
        batchScore = 10;
      } else if (itemCount > 0) {
        batchScore = 5;
      }
      
      if (batchScore > 0) {
        riskFactors.push({
          factor: RiskFactor.BATCH_OPERATION,
          score: batchScore,
          description: `批量操作 ${itemCount} 项`
        });
      }
    }
    
    // 4. 敏感数据评估
    if (resourceType === 'user' || resourceType === 'personalData') {
      riskFactors.push({
        factor: RiskFactor.SENSITIVE_DATA,
        score: 20,
        description: '操作涉及敏感个人数据'
      });
    }
    
    // 5. 检查之前的警报
    try {
      // 获取过去24小时内的安全警报
      const pastAlerts = await this.auditLogService.getLogs(
        {
          userId,
          action: AuditAction.SECURITY_ALERT,
          startDate: new Date(Date.now() - 24 * 60 * 60 * 1000)
        },
        { page: 1, pageSize: 10 },
        env
      );
      
      if (pastAlerts.logs.length > 0) {
        riskFactors.push({
          factor: RiskFactor.PREVIOUS_ALERTS,
          score: 25,
          description: `过去24小时内有 ${pastAlerts.logs.length} 个安全警报`
        });
      }
    } catch (error) {
      console.error('Error checking previous alerts:', error);
      // 忽略错误，继续评估
    }
    
    // 6. 计算总风险分数
    const totalScore = riskFactors.reduce((sum, factor) => sum + factor.score, 0);
    const normalizedScore = Math.min(100, totalScore);
    
    // 7. 确定风险级别
    let riskLevel: RiskLevel;
    if (normalizedScore >= 80) {
      riskLevel = RiskLevel.CRITICAL;
    } else if (normalizedScore >= 60) {
      riskLevel = RiskLevel.HIGH;
    } else if (normalizedScore >= 40) {
      riskLevel = RiskLevel.MEDIUM;
    } else {
      riskLevel = RiskLevel.LOW;
    }
    
    // 8. 确定是否需要确认和审批
    const requiresConfirmation = normalizedScore >= 40;
    const requiresApproval = normalizedScore >= 70;
    
    // 9. 提供风险缓解步骤
    const mitigationSteps: string[] = [];
    
    if (requiresConfirmation) {
      mitigationSteps.push('操作前需要二次确认');
    }
    
    if (requiresApproval) {
      mitigationSteps.push('操作需要管理员审批');
    }
    
    if (riskFactors.some(f => f.factor === RiskFactor.TIME_OF_DAY)) {
      mitigationSteps.push('建议在正常工作时间执行此操作');
    }
    
    if (riskFactors.some(f => f.factor === RiskFactor.BATCH_OPERATION)) {
      mitigationSteps.push('考虑分批执行操作，减少一次性影响范围');
    }
    
    // 10. 记录高风险操作
    if (riskLevel === RiskLevel.HIGH || riskLevel === RiskLevel.CRITICAL) {
      try {
        await this.auditLogService.log({
          userId,
          action: AuditAction.RISK_OPERATION,
          resourceType,
          resourceId,
          details: {
            permission,
            riskLevel,
            riskScore: normalizedScore,
            riskFactors,
            clientInfo
          },
          ipAddress: clientInfo.ipAddress,
          userAgent: clientInfo.userAgent,
          severity: AuditSeverity.WARNING
        }, env);
      } catch (error) {
        console.error('Error logging risk operation:', error);
        // 忽略错误，继续评估
      }
    }
    
    // 返回风险评估结果
    return {
      riskLevel,
      riskScore: normalizedScore,
      riskFactors,
      requiresConfirmation,
      requiresApproval,
      mitigationSteps
    };
  }
}
