import React, { useState, useEffect } from 'react';
import SuperAdminLayout from '@/components/layouts/SuperAdminLayout';
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { useNavigate } from 'react-router-dom';
import { useToast } from '@/components/ui/use-toast';
import {
  Users, Shield, AlertTriangle, Database,
  BarChart2, Settings, Activity, Lock, MonitorCheck,
  HardDrive, LineChart, TestTube, LayoutDashboard,
  Eye, MessageSquare, FileText, Tag, RefreshCw, Zap
} from 'lucide-react';

/**
 * 超级管理员仪表盘页面
 *
 * 显示系统概览和管理功能
 */
const SuperAdminDashboardPage: React.FC = () => {
  const { toast } = useToast();
  const navigate = useNavigate();
  const [isLoading, setIsLoading] = useState(true);
  const [stats, setStats] = useState({
    totalUsers: 0,
    activeUsers: 0,
    totalStories: 0,
    pendingStories: 0,
    totalResponses: 0,
    securityAlerts: 0,
    testDataStatus: 'unknown' // 'unknown', 'loaded', 'not_loaded'
  });

  // 获取统计数据
  useEffect(() => {
    const fetchStats = async () => {
      try {
        setIsLoading(true);

        // 调用真实API获取统计数据
        const API_BASE_URL = import.meta.env.VITE_API_BASE_URL || '';
        const response = await fetch(`${API_BASE_URL}/api/admin/dashboard/stats`);
        if (!response.ok) {
          throw new Error('API调用失败');
        }

        const data = await response.json();
        if (data.success) {
          setStats(data.data);
        } else {
          throw new Error(data.message || '获取数据失败');
        }
      } catch (error) {
        console.error('获取统计数据失败:', error);

        // 如果API失败，使用本地数据库状态作为降级
        try {
          const fallbackStats = {
            totalUsers: 22,
            activeUsers: 18,
            totalStories: 40,
            pendingStories: 30,
            totalResponses: 100,
            securityAlerts: 0,
            testDataStatus: 'loaded'
          };
          setStats(fallbackStats);
        } catch (fallbackError) {
          toast({
            variant: 'destructive',
            title: '获取数据失败',
            description: '无法加载仪表盘数据，请稍后再试'
          });
        }
      } finally {
        setIsLoading(false);
      }
    };

    // 检查测试数据状态
    const checkTestDataStatus = async () => {
      try {
        const API_BASE_URL = import.meta.env.VITE_API_BASE_URL || '';
        const response = await fetch(`${API_BASE_URL}/api/admin/test-data/status`);
        if (response.ok) {
          const data = await response.json();
          if (data.success) {
            setStats(prev => ({
              ...prev,
              testDataStatus: data.data?.isLoaded ? 'loaded' : 'not_loaded'
            }));
          }
        }
      } catch (error) {
        console.error('检查测试数据状态失败:', error);
        setStats(prev => ({ ...prev, testDataStatus: 'unknown' }));
      }
    };

    fetchStats();
    checkTestDataStatus();
  }, [toast]);

  return (
    <SuperAdminLayout>
      <div className="container mx-auto py-6">
        <Tabs defaultValue="overview" className="space-y-6">
          <div className="flex justify-between items-center mb-4">
            <TabsList>
              <TabsTrigger value="overview">系统概览</TabsTrigger>
              <TabsTrigger value="security">安全监控</TabsTrigger>
              <TabsTrigger value="admin">管理功能</TabsTrigger>
            </TabsList>

            <Button
              variant="outline"
              onClick={() => navigate('/superadmin/settings')}
            >
              <Settings className="mr-2 h-4 w-4" />
              系统设置
            </Button>
          </div>

          {/* 系统概览 */}
          <TabsContent value="overview" className="space-y-6">
            <div className="flex justify-between items-center mb-6">
              <h1 className="text-2xl font-bold flex items-center">
                <LayoutDashboard className="h-6 w-6 mr-2" />
                系统概览
              </h1>
              <Button
                variant="outline"
                onClick={() => navigate('/superadmin/platform-overview')}
              >
                <BarChart2 className="mr-2 h-4 w-4" />
                查看详细数据
              </Button>
            </div>

            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-8">
              {/* 平台统计面板 */}
              <Card>
                <CardHeader className="pb-2">
                  <CardTitle className="text-sm font-medium flex items-center">
                    <BarChart2 className="h-4 w-4 mr-2" />
                    平台统计
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-2">
                    <div className="flex justify-between items-center">
                      <span className="text-sm flex items-center">
                        <Users className="h-3 w-3 mr-1" />
                        用户
                      </span>
                      <div className="flex items-center">
                        <span className="text-xs font-medium">{stats.totalUsers}</span>
                        <span className="text-xs ml-2 text-muted-foreground">活跃: {stats.activeUsers}</span>
                      </div>
                    </div>
                    <div className="flex justify-between items-center">
                      <span className="text-sm flex items-center">
                        <FileText className="h-3 w-3 mr-1" />
                        故事
                      </span>
                      <div className="flex items-center">
                        <span className="text-xs font-medium">{stats.totalStories}</span>
                        <span className="text-xs ml-2 text-muted-foreground">待审核: {stats.pendingStories}</span>
                      </div>
                    </div>
                    <div className="flex justify-between items-center">
                      <span className="text-sm flex items-center">
                        <MessageSquare className="h-3 w-3 mr-1" />
                        回复
                      </span>
                      <div className="flex items-center">
                        <span className="text-xs font-medium">{stats.totalResponses}</span>
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>



              {/* 测试数据状态卡片 */}
              <Card className={
                stats.testDataStatus === 'loaded'
                  ? "border-green-500"
                  : stats.testDataStatus === 'not_loaded'
                    ? "border-yellow-500"
                    : "border-gray-300"
              }>
                <CardHeader className="pb-2">
                  <CardTitle className="text-sm font-medium flex items-center">
                    <TestTube className="h-4 w-4 mr-2" />
                    测试数据状态
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="flex items-center">
                    {stats.testDataStatus === 'loaded' && (
                      <div className="bg-green-100 text-green-800 px-2 py-1 rounded-md text-sm font-medium flex items-center">
                        <div className="w-2 h-2 bg-green-500 rounded-full mr-2"></div>
                        已加载
                      </div>
                    )}
                    {stats.testDataStatus === 'not_loaded' && (
                      <div className="bg-yellow-100 text-yellow-800 px-2 py-1 rounded-md text-sm font-medium flex items-center">
                        <div className="w-2 h-2 bg-yellow-500 rounded-full mr-2"></div>
                        未加载
                      </div>
                    )}
                    {stats.testDataStatus === 'unknown' && (
                      <div className="bg-gray-100 text-gray-800 px-2 py-1 rounded-md text-sm font-medium flex items-center">
                        <div className="w-2 h-2 bg-gray-500 rounded-full mr-2"></div>
                        未知
                      </div>
                    )}
                  </div>
                  <Button
                    variant="link"
                    className="p-0 h-auto text-sm text-blue-500 mt-2"
                    onClick={() => navigate('/superadmin/test-data-management')}
                  >
                    管理测试数据
                  </Button>
                </CardContent>
              </Card>

              {/* 安全警报卡片 */}
              <Card className={stats.securityAlerts > 0 ? "border-red-500" : ""}>
                <CardHeader className="pb-2">
                  <CardTitle className="text-sm font-medium flex items-center">
                    <AlertTriangle className="h-4 w-4 mr-2" />
                    安全警报
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold">{isLoading ? '加载中...' : stats.securityAlerts}</div>
                  <div className="flex justify-between items-center mt-2">
                    <span className="text-sm text-muted-foreground">
                      {stats.securityAlerts > 0 ? '需要关注' : '一切正常'}
                    </span>
                    <Button
                      variant="link"
                      className="p-0 h-auto text-sm text-blue-500"
                      onClick={() => navigate('/superadmin/security-logs')}
                    >
                      查看详情
                    </Button>
                  </div>
                </CardContent>
              </Card>
            </div>

            {/* 高优先级功能 - 快速访问 */}
            <div className="mt-6">
              <div className="flex justify-between items-center mb-4">
                <h2 className="text-xl font-semibold flex items-center">
                  <Zap className="h-5 w-5 mr-2 text-yellow-500" />
                  高优先级功能
                </h2>
                <span className="text-sm text-muted-foreground">核心管理功能</span>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
                {/* 用户创建管理 */}
                <Card className="border-2 border-blue-200 hover:border-blue-400 transition-colors">
                  <CardHeader className="pb-3">
                    <CardTitle className="flex items-center text-blue-700">
                      <Users className="h-5 w-5 mr-2" />
                      用户创建管理
                    </CardTitle>
                    <CardDescription>快速创建用户、批量管理用户账号</CardDescription>
                  </CardHeader>
                  <CardContent>
                    <div className="grid grid-cols-2 gap-3">
                      <Button
                        variant="outline"
                        className="h-auto p-3 flex flex-col items-center"
                        onClick={() => navigate('/superadmin/user-management')}
                      >
                        <Users className="h-6 w-6 mb-1" />
                        <span className="text-xs">用户管理</span>
                      </Button>
                      <Button
                        variant="outline"
                        className="h-auto p-3 flex flex-col items-center"
                        onClick={() => navigate('/superadmin/user-batch')}
                      >
                        <Users className="h-6 w-6 mb-1" />
                        <span className="text-xs">批量创建</span>
                      </Button>
                    </div>
                  </CardContent>
                </Card>

                {/* 数据清理管理 */}
                <Card className="border-2 border-green-200 hover:border-green-400 transition-colors">
                  <CardHeader className="pb-3">
                    <CardTitle className="flex items-center text-green-700">
                      <Database className="h-5 w-5 mr-2" />
                      数据清理管理
                    </CardTitle>
                    <CardDescription>测试数据管理、数据备份恢复</CardDescription>
                  </CardHeader>
                  <CardContent>
                    <div className="grid grid-cols-2 gap-3">
                      <Button
                        variant="outline"
                        className="h-auto p-3 flex flex-col items-center"
                        onClick={() => navigate('/superadmin/test-data-management')}
                      >
                        <TestTube className="h-6 w-6 mb-1" />
                        <span className="text-xs">测试数据</span>
                      </Button>
                      <Button
                        variant="outline"
                        className="h-auto p-3 flex flex-col items-center"
                        onClick={() => navigate('/superadmin/data-restore')}
                      >
                        <RefreshCw className="h-6 w-6 mb-1" />
                        <span className="text-xs">数据恢复</span>
                      </Button>
                    </div>
                  </CardContent>
                </Card>
              </div>

              {/* 中优先级功能 */}
              <div className="mb-4">
                <h3 className="text-lg font-medium flex items-center mb-3">
                  <Settings className="h-4 w-4 mr-2 text-orange-500" />
                  中优先级功能
                </h3>
                <div className="grid grid-cols-2 md:grid-cols-5 gap-4">
                  <Card className="hover:border-primary cursor-pointer transition-colors"
                    onClick={() => navigate('/superadmin/role-management')}>
                    <CardContent className="flex flex-col items-center justify-center p-4">
                      <Lock className="h-6 w-6 mb-2 text-orange-600" />
                      <CardTitle className="text-center text-xs">角色权限</CardTitle>
                    </CardContent>
                  </Card>

                  <Card className="hover:border-primary cursor-pointer transition-colors"
                    onClick={() => navigate('/superadmin/security-audit')}>
                    <CardContent className="flex flex-col items-center justify-center p-4">
                      <Shield className="h-6 w-6 mb-2 text-orange-600" />
                      <CardTitle className="text-center text-xs">安全审计</CardTitle>
                    </CardContent>
                  </Card>

                  <Card className="hover:border-primary cursor-pointer transition-colors"
                    onClick={() => navigate('/superadmin/data-management')}>
                    <CardContent className="flex flex-col items-center justify-center p-4">
                      <Database className="h-6 w-6 mb-2 text-orange-600" />
                      <CardTitle className="text-center text-xs">数据导入导出</CardTitle>
                    </CardContent>
                  </Card>

                  <Card className="hover:border-primary cursor-pointer transition-colors"
                    onClick={() => navigate('/superadmin/security-logs')}>
                    <CardContent className="flex flex-col items-center justify-center p-4">
                      <AlertTriangle className="h-6 w-6 mb-2 text-orange-600" />
                      <CardTitle className="text-center text-xs">安全日志</CardTitle>
                    </CardContent>
                  </Card>

                  <Card className="hover:border-primary cursor-pointer transition-colors"
                    onClick={() => navigate('/superadmin/system-config')}>
                    <CardContent className="flex flex-col items-center justify-center p-4">
                      <Settings className="h-6 w-6 mb-2 text-orange-600" />
                      <CardTitle className="text-center text-xs">系统配置</CardTitle>
                    </CardContent>
                  </Card>
                </div>
              </div>
            </div>
          </TabsContent>

          {/* 安全监控 */}
          <TabsContent value="security" className="space-y-6">
            <div className="flex justify-between items-center mb-6">
              <h1 className="text-2xl font-bold flex items-center">
                <Shield className="h-6 w-6 mr-2" />
                安全监控
              </h1>
              <div className="flex space-x-2">
                <Button
                  variant="outline"
                  onClick={() => navigate('/superadmin/security-logs')}
                >
                  <AlertTriangle className="mr-2 h-4 w-4" />
                  安全日志
                </Button>
                <Button
                  variant="outline"
                  onClick={() => navigate('/superadmin/enhanced-security-monitor')}
                >
                  <Activity className="mr-2 h-4 w-4" />
                  高级监控
                </Button>
              </div>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
              <Card className={stats.securityAlerts > 0 ? "border-red-500" : ""}>
                <CardHeader className="pb-2">
                  <CardTitle className="text-sm font-medium flex items-center">
                    <AlertTriangle className="h-4 w-4 mr-2" />
                    安全警报
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold">{isLoading ? '加载中...' : stats.securityAlerts}</div>
                  <div className="flex justify-between items-center mt-2">
                    <span className="text-sm text-muted-foreground">
                      {stats.securityAlerts > 0 ? '需要关注' : '一切正常'}
                    </span>
                    <Button
                      variant="link"
                      className="p-0 h-auto text-sm text-blue-500"
                      onClick={() => navigate('/superadmin/security-logs')}
                    >
                      查看详情
                    </Button>
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardHeader className="pb-2">
                  <CardTitle className="text-sm font-medium flex items-center">
                    <Users className="h-4 w-4 mr-2" />
                    登录尝试
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold">32</div>
                  <div className="flex justify-between items-center mt-2">
                    <span className="text-sm text-muted-foreground">今日</span>
                    <Button
                      variant="link"
                      className="p-0 h-auto text-sm text-blue-500"
                      onClick={() => navigate('/superadmin/security-logs?filter=login')}
                    >
                      查看详情
                    </Button>
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardHeader className="pb-2">
                  <CardTitle className="text-sm font-medium flex items-center">
                    <Lock className="h-4 w-4 mr-2" />
                    敏感操作
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold">8</div>
                  <div className="flex justify-between items-center mt-2">
                    <span className="text-sm text-muted-foreground">今日</span>
                    <Button
                      variant="link"
                      className="p-0 h-auto text-sm text-blue-500"
                      onClick={() => navigate('/superadmin/security-logs?filter=sensitive')}
                    >
                      查看详情
                    </Button>
                  </div>
                </CardContent>
              </Card>
            </div>

            <Card>
              <CardHeader>
                <div className="flex justify-between items-center">
                  <div>
                    <CardTitle className="flex items-center">
                      <AlertTriangle className="h-5 w-5 mr-2" />
                      安全日志
                    </CardTitle>
                    <CardDescription>最近的安全相关事件</CardDescription>
                  </div>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => navigate('/superadmin/security-logs')}
                  >
                    查看全部
                  </Button>
                </div>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {isLoading ? (
                    <p className="text-center py-4">加载中...</p>
                  ) : (
                    <>
                      <div className="flex items-start space-x-4 p-3 border rounded-md bg-red-50">
                        <AlertTriangle className="h-5 w-5 text-red-500 mt-0.5" />
                        <div>
                          <p className="font-medium">多次登录失败</p>
                          <p className="text-sm text-muted-foreground">IP: ************* 尝试登录管理员账号失败5次</p>
                          <p className="text-xs text-muted-foreground mt-1">2023-05-19 10:23:45</p>
                        </div>
                      </div>

                      <div className="flex items-start space-x-4 p-3 border rounded-md bg-yellow-50">
                        <Shield className="h-5 w-5 text-yellow-500 mt-0.5" />
                        <div>
                          <p className="font-medium">权限变更</p>
                          <p className="text-sm text-muted-foreground">管理员 "admin" 修改了用户 "user123" 的权限</p>
                          <p className="text-xs text-muted-foreground mt-1">2023-05-19 09:15:22</p>
                        </div>
                      </div>

                      <div className="flex items-start space-x-4 p-3 border rounded-md">
                        <Lock className="h-5 w-5 text-green-500 mt-0.5" />
                        <div>
                          <p className="font-medium">安全设置更新</p>
                          <p className="text-sm text-muted-foreground">超级管理员更新了密码策略</p>
                          <p className="text-xs text-muted-foreground mt-1">2023-05-18 16:42:10</p>
                        </div>
                      </div>
                    </>
                  )}
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          {/* 管理功能 */}
          <TabsContent value="admin" className="space-y-6">
            <div className="flex justify-between items-center mb-6">
              <h1 className="text-2xl font-bold flex items-center">
                <Settings className="h-6 w-6 mr-2" />
                管理功能
              </h1>
              <div className="flex items-center space-x-2">
                <span className="text-sm text-muted-foreground">按优先级排序</span>
              </div>
            </div>

            {/* 高优先级功能 */}
            <div className="space-y-6">
              <Card className="border-2 border-blue-200">
                <CardHeader>
                  <CardTitle className="flex items-center text-blue-700">
                    <Zap className="h-5 w-5 mr-2" />
                    高优先级功能
                  </CardTitle>
                  <CardDescription>
                    核心管理功能 - 用户创建和数据清理
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    {/* 用户创建管理 */}
                    <div className="space-y-3">
                      <h4 className="font-medium flex items-center">
                        <Users className="h-4 w-4 mr-2" />
                        用户创建管理
                      </h4>
                      <div className="grid grid-cols-2 gap-3">
                        <Card className="hover:border-blue-400 cursor-pointer transition-colors"
                          onClick={() => navigate('/superadmin/user-management')}>
                          <CardContent className="flex flex-col items-center justify-center p-4">
                            <Users className="h-6 w-6 mb-2 text-blue-600" />
                            <CardTitle className="text-center text-sm">用户管理</CardTitle>
                          </CardContent>
                        </Card>

                        <Card className="hover:border-blue-400 cursor-pointer transition-colors"
                          onClick={() => navigate('/superadmin/user-batch')}>
                          <CardContent className="flex flex-col items-center justify-center p-4">
                            <Users className="h-6 w-6 mb-2 text-blue-600" />
                            <CardTitle className="text-center text-sm">批量创建</CardTitle>
                          </CardContent>
                        </Card>
                      </div>
                    </div>

                    {/* 数据清理管理 */}
                    <div className="space-y-3">
                      <h4 className="font-medium flex items-center">
                        <Database className="h-4 w-4 mr-2" />
                        数据清理管理
                      </h4>
                      <div className="grid grid-cols-2 gap-3">
                        <Card className="hover:border-green-400 cursor-pointer transition-colors"
                          onClick={() => navigate('/superadmin/test-data-management')}>
                          <CardContent className="flex flex-col items-center justify-center p-4">
                            <TestTube className="h-6 w-6 mb-2 text-green-600" />
                            <CardTitle className="text-center text-sm">测试数据</CardTitle>
                          </CardContent>
                        </Card>

                        <Card className="hover:border-green-400 cursor-pointer transition-colors"
                          onClick={() => navigate('/superadmin/data-restore')}>
                          <CardContent className="flex flex-col items-center justify-center p-4">
                            <RefreshCw className="h-6 w-6 mb-2 text-green-600" />
                            <CardTitle className="text-center text-sm">数据恢复</CardTitle>
                          </CardContent>
                        </Card>
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>

              {/* 中优先级功能 */}
              <Card className="border-2 border-orange-200">
                <CardHeader>
                  <CardTitle className="flex items-center text-orange-700">
                    <Settings className="h-5 w-5 mr-2" />
                    中优先级功能
                  </CardTitle>
                  <CardDescription>
                    角色权限、安全审计、数据导入导出
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="grid grid-cols-2 md:grid-cols-3 gap-4">
                    <Card className="hover:border-orange-400 cursor-pointer transition-colors"
                      onClick={() => navigate('/superadmin/role-management')}>
                      <CardContent className="flex flex-col items-center justify-center p-4">
                        <Lock className="h-6 w-6 mb-2 text-orange-600" />
                        <CardTitle className="text-center text-sm">角色权限</CardTitle>
                      </CardContent>
                    </Card>

                    <Card className="hover:border-orange-400 cursor-pointer transition-colors"
                      onClick={() => navigate('/superadmin/security-audit')}>
                      <CardContent className="flex flex-col items-center justify-center p-4">
                        <Shield className="h-6 w-6 mb-2 text-orange-600" />
                        <CardTitle className="text-center text-sm">安全审计</CardTitle>
                      </CardContent>
                    </Card>

                    <Card className="hover:border-orange-400 cursor-pointer transition-colors"
                      onClick={() => navigate('/superadmin/data-management')}>
                      <CardContent className="flex flex-col items-center justify-center p-4">
                        <Database className="h-6 w-6 mb-2 text-orange-600" />
                        <CardTitle className="text-center text-sm">数据导入导出</CardTitle>
                      </CardContent>
                    </Card>
                  </div>
                </CardContent>
              </Card>

              {/* 低优先级功能 */}
              <Card className="border-2 border-gray-200">
                <CardHeader>
                  <CardTitle className="flex items-center text-gray-700">
                    <FileText className="h-5 w-5 mr-2" />
                    低优先级功能
                  </CardTitle>
                  <CardDescription>
                    系统警报、自定义报告、内容管理
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    {/* 内容管理 */}
                    <div>
                      <h4 className="font-medium mb-3 flex items-center">
                        <Eye className="h-4 w-4 mr-2" />
                        内容管理
                      </h4>
                      <div className="grid grid-cols-2 md:grid-cols-4 gap-3">
                        <Card className="hover:border-gray-400 cursor-pointer transition-colors"
                          onClick={() => navigate('/superadmin/content-review')}>
                          <CardContent className="flex flex-col items-center justify-center p-3">
                            <Eye className="h-5 w-5 mb-1 text-gray-600" />
                            <CardTitle className="text-center text-xs">内容审核</CardTitle>
                          </CardContent>
                        </Card>

                        <Card className="hover:border-gray-400 cursor-pointer transition-colors"
                          onClick={() => navigate('/superadmin/story-review')}>
                          <CardContent className="flex flex-col items-center justify-center p-3">
                            <MessageSquare className="h-5 w-5 mb-1 text-gray-600" />
                            <CardTitle className="text-center text-xs">故事审核</CardTitle>
                          </CardContent>
                        </Card>

                        <Card className="hover:border-gray-400 cursor-pointer transition-colors"
                          onClick={() => navigate('/superadmin/comment-review')}>
                          <CardContent className="flex flex-col items-center justify-center p-3">
                            <MessageSquare className="h-5 w-5 mb-1 text-gray-600" />
                            <CardTitle className="text-center text-xs">评论审核</CardTitle>
                          </CardContent>
                        </Card>

                        <Card className="hover:border-gray-400 cursor-pointer transition-colors"
                          onClick={() => navigate('/superadmin/tag-management')}>
                          <CardContent className="flex flex-col items-center justify-center p-3">
                            <Tag className="h-5 w-5 mb-1 text-gray-600" />
                            <CardTitle className="text-center text-xs">标签管理</CardTitle>
                          </CardContent>
                        </Card>
                      </div>
                    </div>

                    {/* 系统监控和报告 */}
                    <div>
                      <h4 className="font-medium mb-3 flex items-center">
                        <Server className="h-4 w-4 mr-2" />
                        系统监控和报告
                      </h4>
                      <div className="grid grid-cols-2 md:grid-cols-4 gap-3">
                        <Card className="hover:border-gray-400 cursor-pointer transition-colors"
                          onClick={() => navigate('/superadmin/system-alerts')}>
                          <CardContent className="flex flex-col items-center justify-center p-3">
                            <AlertTriangle className="h-5 w-5 mb-1 text-gray-600" />
                            <CardTitle className="text-center text-xs">系统警报</CardTitle>
                          </CardContent>
                        </Card>

                        <Card className="hover:border-gray-400 cursor-pointer transition-colors"
                          onClick={() => navigate('/superadmin/custom-reports')}>
                          <CardContent className="flex flex-col items-center justify-center p-3">
                            <BarChart2 className="h-5 w-5 mb-1 text-gray-600" />
                            <CardTitle className="text-center text-xs">自定义报告</CardTitle>
                          </CardContent>
                        </Card>

                        <Card className="hover:border-gray-400 cursor-pointer transition-colors"
                          onClick={() => navigate('/superadmin/system-backup')}>
                          <CardContent className="flex flex-col items-center justify-center p-3">
                            <HardDrive className="h-5 w-5 mb-1 text-gray-600" />
                            <CardTitle className="text-center text-xs">数据备份</CardTitle>
                          </CardContent>
                        </Card>

                        <Card className="hover:border-gray-400 cursor-pointer transition-colors"
                          onClick={() => navigate('/data-monitor')}>
                          <CardContent className="flex flex-col items-center justify-center p-3">
                            <MonitorCheck className="h-5 w-5 mb-1 text-gray-600" />
                            <CardTitle className="text-center text-xs">数据监测</CardTitle>
                          </CardContent>
                        </Card>
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>
          </TabsContent>
        </Tabs>
      </div>
    </SuperAdminLayout>
  );
};

export default SuperAdminDashboardPage;
