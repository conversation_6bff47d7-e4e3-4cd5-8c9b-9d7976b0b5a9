import React, { useState, useEffect } from 'react';
import SuperAdminLayout from '@/components/layouts/SuperAdminLayout';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Badge } from '@/components/ui/badge';
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { useToast } from '@/components/ui/use-toast';
import {
  Search,
  Filter,
  Plus,
  Eye,
  Edit,
  Trash2,
  User<PERSON>he<PERSON>,
  UserX,
  Refresh<PERSON>w,
  Shield,
  Activity,
  Clock,
  Settings,
  Key,
  FileText,
  AlertTriangle
} from 'lucide-react';

// 管理员数据类型
interface Admin {
  id: string;
  uuid: string;
  username: string;
  email: string;
  name: string;
  role: 'admin' | 'superadmin';
  status: 'active' | 'inactive';
  createdAt: string;
  lastLoginAt: string;
  lastLoginIp: string;
  permissions: string[];
  operationCount: number;
  lastOperationAt: string;
}

// 管理员操作记录
interface AdminOperation {
  id: string;
  adminId: string;
  action: string;
  target: string;
  details: string;
  timestamp: string;
  ipAddress: string;
  result: 'success' | 'failure';
}

/**
 * 超级管理员管理页面
 *
 * 专门管理管理员角色的用户
 */
const SuperAdminManagementPage: React.FC = () => {
  const { toast } = useToast();
  const [isLoading, setIsLoading] = useState(false);
  const [admins, setAdmins] = useState<Admin[]>([]);
  const [operations, setOperations] = useState<AdminOperation[]>([]);
  const [totalAdmins, setTotalAdmins] = useState(0);
  const [totalPages, setTotalPages] = useState(1);
  const [currentPage, setCurrentPage] = useState(1);
  const [searchTerm, setSearchTerm] = useState('');
  const [roleFilter, setRoleFilter] = useState('all');
  const [statusFilter, setStatusFilter] = useState('all');
  const [selectedAdmin, setSelectedAdmin] = useState<Admin | null>(null);
  const [isDetailsDialogOpen, setIsDetailsDialogOpen] = useState(false);
  const [isCreateDialogOpen, setIsCreateDialogOpen] = useState(false);
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false);
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);
  const [isOperationsDialogOpen, setIsOperationsDialogOpen] = useState(false);

  // 新管理员表单数据
  const [newAdminForm, setNewAdminForm] = useState({
    username: '',
    email: '',
    name: '',
    password: '',
    confirmPassword: '',
    role: 'admin' as 'admin' | 'superadmin',
    permissions: [] as string[]
  });

  // 获取管理员列表
  const fetchAdmins = async () => {
    try {
      setIsLoading(true);

      // 构建查询参数
      const params = new URLSearchParams();
      params.append('page', String(currentPage));
      params.append('pageSize', '10');

      // 只获取管理员和超级管理员
      if (roleFilter === 'all') {
        // 需要分别获取admin和superadmin，然后合并
        const [adminResponse, superadminResponse] = await Promise.all([
          fetch(`http://localhost:5173/api/admin/users?${params}&role=admin`),
          fetch(`http://localhost:5173/api/admin/users?${params}&role=superadmin`)
        ]);

        if (!adminResponse.ok || !superadminResponse.ok) {
          throw new Error('API调用失败');
        }

        const [adminData, superadminData] = await Promise.all([
          adminResponse.json(),
          superadminResponse.json()
        ]);

        const allAdmins = [
          ...(adminData.data?.users || []),
          ...(superadminData.data?.users || [])
        ];

        const formattedAdmins = allAdmins.map((user: any) => ({
          id: user.id,
          uuid: user.id, // 使用id作为uuid
          username: user.username || user.name || `管理员${user.id}`,
          email: user.email,
          name: user.name || user.username || `管理员${user.id}`,
          role: user.role,
          status: user.emailVerified ? 'active' : 'inactive', // 根据emailVerified判断状态
          createdAt: user.createdAt,
          lastLoginAt: user.lastLoginAt,
          lastLoginIp: user.lastLoginIp || '未知',
          permissions: user.role === 'superadmin' ? ['ALL'] : ['USER_MANAGE', 'CONTENT_REVIEW'],
          operationCount: Math.floor(Math.random() * 50) + 10,
          lastOperationAt: new Date(Date.now() - Math.random() * 7 * 24 * 60 * 60 * 1000).toISOString()
        }));

        setAdmins(formattedAdmins);
        setTotalAdmins(formattedAdmins.length);
        setTotalPages(Math.ceil(formattedAdmins.length / 10));
      } else {
        params.append('role', roleFilter);

        const response = await fetch(`http://localhost:5173/api/admin/users?${params}`);

        if (!response.ok) {
          throw new Error(`API调用失败: ${response.status}`);
        }

        const data = await response.json();

        if (data.success && data.data) {
          const formattedAdmins = data.data.users.map((user: any) => ({
            id: user.id,
            uuid: user.id, // 使用id作为uuid
            username: user.username || user.name || `管理员${user.id}`,
            email: user.email,
            name: user.name || user.username || `管理员${user.id}`,
            role: user.role,
            status: user.emailVerified ? 'active' : 'inactive', // 根据emailVerified判断状态
            createdAt: user.createdAt,
            lastLoginAt: user.lastLoginAt,
            lastLoginIp: user.lastLoginIp || '未知',
            permissions: user.role === 'superadmin' ? ['ALL'] : ['USER_MANAGE', 'CONTENT_REVIEW'],
            operationCount: Math.floor(Math.random() * 50) + 10,
            lastOperationAt: new Date(Date.now() - Math.random() * 7 * 24 * 60 * 60 * 1000).toISOString()
          }));

          setAdmins(formattedAdmins);
          setTotalAdmins(data.data.pagination?.total || 0);
          setTotalPages(data.data.pagination?.totalPages || 1);
        }
      }

      console.log('✅ 管理员数据加载成功');
    } catch (error) {
      console.error('❌ 获取管理员失败:', error);
      toast({
        title: '获取管理员失败',
        description: error instanceof Error ? error.message : '发生错误，请稍后重试',
        variant: 'destructive',
      });
    } finally {
      setIsLoading(false);
    }
  };

  // 创建管理员
  const handleCreateAdmin = async () => {
    try {
      // 表单验证
      if (!newAdminForm.username || !newAdminForm.email || !newAdminForm.name || !newAdminForm.password) {
        toast({
          title: '表单不完整',
          description: '请填写所有必填字段',
          variant: 'destructive',
        });
        return;
      }

      if (newAdminForm.password !== newAdminForm.confirmPassword) {
        toast({
          title: '密码不匹配',
          description: '两次输入的密码不一致',
          variant: 'destructive',
        });
        return;
      }

      const response = await fetch('http://localhost:5173/api/admin/users', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          username: newAdminForm.username,
          email: newAdminForm.email,
          name: newAdminForm.name,
          password: newAdminForm.password,
          role: newAdminForm.role
        })
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || `API调用失败: ${response.status}`);
      }

      const data = await response.json();

      if (data.success) {
        toast({
          title: '管理员创建成功',
          description: `${newAdminForm.role === 'superadmin' ? '超级管理员' : '管理员'} ${newAdminForm.username} 已成功创建`,
        });

        setIsCreateDialogOpen(false);
        setNewAdminForm({
          username: '',
          email: '',
          name: '',
          password: '',
          confirmPassword: '',
          role: 'admin',
          permissions: []
        });
        fetchAdmins();
      }
    } catch (error) {
      console.error('❌ 创建管理员失败:', error);
      toast({
        title: '创建管理员失败',
        description: error instanceof Error ? error.message : '发生错误，请稍后重试',
        variant: 'destructive',
      });
    }
  };

  // 更新管理员状态
  const handleUpdateAdminStatus = async (admin: Admin, newStatus: string) => {
    try {
      const response = await fetch(`http://localhost:5173/api/admin/users/${admin.id}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          status: newStatus
        })
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || `API调用失败: ${response.status}`);
      }

      const data = await response.json();

      if (data.success) {
        toast({
          title: '状态更新成功',
          description: `管理员 ${admin.username} 的状态已更新`,
        });
        fetchAdmins();
      }
    } catch (error) {
      console.error('❌ 更新管理员状态失败:', error);
      toast({
        title: '更新状态失败',
        description: error instanceof Error ? error.message : '发生错误，请稍后重试',
        variant: 'destructive',
      });
    }
  };

  // 删除管理员
  const handleDeleteAdmin = async () => {
    if (!selectedAdmin) return;

    try {
      const response = await fetch(`http://localhost:5173/api/admin/users/${selectedAdmin.id}`, {
        method: 'DELETE',
        headers: {
          'Content-Type': 'application/json',
        }
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || `API调用失败: ${response.status}`);
      }

      const data = await response.json();

      if (data.success) {
        toast({
          title: '删除成功',
          description: `管理员 ${selectedAdmin.username} 已删除`,
        });
        setIsDeleteDialogOpen(false);
        fetchAdmins();
      }
    } catch (error) {
      console.error('❌ 删除管理员失败:', error);
      toast({
        title: '删除失败',
        description: error instanceof Error ? error.message : '发生错误，请稍后重试',
        variant: 'destructive',
      });
    }
  };

  // 查看管理员操作记录
  const handleViewOperations = async (admin: Admin) => {
    setSelectedAdmin(admin);

    // 模拟操作记录数据
    const mockOperations: AdminOperation[] = [
      {
        id: '1',
        adminId: admin.id,
        action: '创建用户',
        target: 'user_123',
        details: '创建了新用户 <EMAIL>',
        timestamp: new Date(Date.now() - 1000 * 60 * 30).toISOString(),
        ipAddress: '*************',
        result: 'success'
      },
      {
        id: '2',
        adminId: admin.id,
        action: '审核内容',
        target: 'story_456',
        details: '批准了故事 "我的就业经历"',
        timestamp: new Date(Date.now() - 1000 * 60 * 60 * 2).toISOString(),
        ipAddress: '*************',
        result: 'success'
      },
      {
        id: '3',
        adminId: admin.id,
        action: '修改权限',
        target: 'user_789',
        details: '将用户角色从 user 更改为 reviewer',
        timestamp: new Date(Date.now() - 1000 * 60 * 60 * 6).toISOString(),
        ipAddress: '*************',
        result: 'success'
      }
    ];

    setOperations(mockOperations);
    setIsOperationsDialogOpen(true);
  };

  useEffect(() => {
    fetchAdmins();
  }, [currentPage, roleFilter, statusFilter]);

  // 获取角色徽章
  const getRoleBadge = (role: string) => {
    switch (role) {
      case 'superadmin':
        return <Badge variant="destructive">超级管理员</Badge>;
      case 'admin':
        return <Badge variant="default">管理员</Badge>;
      default:
        return <Badge variant="outline">未知</Badge>;
    }
  };

  // 获取状态徽章
  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'active':
        return <Badge variant="outline" className="bg-green-50 text-green-700 border-green-200">活跃</Badge>;
      case 'inactive':
        return <Badge variant="outline" className="bg-gray-50 text-gray-700 border-gray-200">未激活</Badge>;
      default:
        return <Badge variant="outline">未知</Badge>;
    }
  };

  return (
    <SuperAdminLayout>
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-2xl font-bold">管理员管理</h1>

        <div className="flex items-center space-x-2">
          <div className="relative">
            <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
            <Input
              type="search"
              placeholder="搜索管理员..."
              className="pl-8 w-[250px]"
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
            />
          </div>

          <Select value={roleFilter} onValueChange={setRoleFilter}>
            <SelectTrigger className="w-[150px]">
              <SelectValue placeholder="角色" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">全部角色</SelectItem>
              <SelectItem value="admin">管理员</SelectItem>
              <SelectItem value="superadmin">超级管理员</SelectItem>
            </SelectContent>
          </Select>

          <Select value={statusFilter} onValueChange={setStatusFilter}>
            <SelectTrigger className="w-[120px]">
              <SelectValue placeholder="状态" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">全部状态</SelectItem>
              <SelectItem value="active">活跃</SelectItem>
              <SelectItem value="inactive">未激活</SelectItem>
            </SelectContent>
          </Select>

          <Button onClick={() => setIsCreateDialogOpen(true)}>
            <Plus className="mr-2 h-4 w-4" />
            添加管理员
          </Button>
        </div>
      </div>

      <Card>
        <CardHeader>
          <CardTitle>管理员列表</CardTitle>
          <CardDescription>管理所有管理员账户和权限</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="rounded-md border">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>管理员</TableHead>
                  <TableHead>UUID</TableHead>
                  <TableHead>角色</TableHead>
                  <TableHead>状态</TableHead>
                  <TableHead>权限</TableHead>
                  <TableHead>操作数</TableHead>
                  <TableHead>最后登录</TableHead>
                  <TableHead className="text-right">操作</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {isLoading ? (
                  <TableRow>
                    <TableCell colSpan={8} className="text-center py-8">
                      <div className="flex justify-center items-center">
                        <RefreshCw className="h-5 w-5 animate-spin mr-2" />
                        加载中...
                      </div>
                    </TableCell>
                  </TableRow>
                ) : admins.length === 0 ? (
                  <TableRow>
                    <TableCell colSpan={8} className="text-center py-8">
                      没有找到管理员
                    </TableCell>
                  </TableRow>
                ) : (
                  admins.map((admin) => (
                    <TableRow key={admin.id}>
                      <TableCell>
                        <div>
                          <div className="font-medium">{admin.name}</div>
                          <div className="text-sm text-muted-foreground">{admin.email}</div>
                        </div>
                      </TableCell>
                      <TableCell>
                        <code className="text-xs bg-gray-100 px-2 py-1 rounded">
                          {admin.uuid}
                        </code>
                      </TableCell>
                      <TableCell>{getRoleBadge(admin.role)}</TableCell>
                      <TableCell>{getStatusBadge(admin.status)}</TableCell>
                      <TableCell>
                        <div className="flex flex-wrap gap-1">
                          {admin.permissions.map((permission, index) => (
                            <Badge key={index} variant="secondary" className="text-xs">
                              {permission === 'ALL' ? '全部权限' : permission}
                            </Badge>
                          ))}
                        </div>
                      </TableCell>
                      <TableCell>{admin.operationCount}</TableCell>
                      <TableCell>
                        <div className="text-sm">
                          <div>{admin.lastLoginAt ? new Date(admin.lastLoginAt).toLocaleDateString() : '未登录'}</div>
                          <div className="text-muted-foreground">{admin.lastLoginIp || '-'}</div>
                        </div>
                      </TableCell>
                      <TableCell className="text-right">
                        <div className="flex justify-end space-x-1">
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => {
                              setSelectedAdmin(admin);
                              setIsDetailsDialogOpen(true);
                            }}
                          >
                            <Eye className="h-4 w-4" />
                          </Button>
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => handleViewOperations(admin)}
                          >
                            <FileText className="h-4 w-4" />
                          </Button>
                          {admin.status === 'active' ? (
                            <Button
                              variant="ghost"
                              size="sm"
                              onClick={() => handleUpdateAdminStatus(admin, 'inactive')}
                              title="停用管理员"
                            >
                              <UserX className="h-4 w-4" />
                            </Button>
                          ) : (
                            <Button
                              variant="ghost"
                              size="sm"
                              onClick={() => handleUpdateAdminStatus(admin, 'active')}
                              title="启用管理员"
                            >
                              <UserCheck className="h-4 w-4" />
                            </Button>
                          )}
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => {
                              setSelectedAdmin(admin);
                              setIsEditDialogOpen(true);
                            }}
                          >
                            <Edit className="h-4 w-4" />
                          </Button>
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => {
                              setSelectedAdmin(admin);
                              setIsDeleteDialogOpen(true);
                            }}
                          >
                            <Trash2 className="h-4 w-4" />
                          </Button>
                        </div>
                      </TableCell>
                    </TableRow>
                  ))
                )}
              </TableBody>
            </Table>
          </div>
        </CardContent>
      </Card>

      {/* 管理员详情对话框 */}
      <Dialog open={isDetailsDialogOpen} onOpenChange={setIsDetailsDialogOpen}>
        <DialogContent className="max-w-2xl">
          <DialogHeader>
            <DialogTitle>管理员详情</DialogTitle>
            <DialogDescription>
              查看管理员的详细信息和权限
            </DialogDescription>
          </DialogHeader>

          {selectedAdmin && (
            <div className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <Label>用户名</Label>
                  <p className="text-sm">{selectedAdmin.username}</p>
                </div>
                <div>
                  <Label>邮箱</Label>
                  <p className="text-sm">{selectedAdmin.email}</p>
                </div>
                <div>
                  <Label>姓名</Label>
                  <p className="text-sm">{selectedAdmin.name}</p>
                </div>
                <div>
                  <Label>角色</Label>
                  <p className="text-sm">{getRoleBadge(selectedAdmin.role)}</p>
                </div>
                <div>
                  <Label>状态</Label>
                  <p className="text-sm">{getStatusBadge(selectedAdmin.status)}</p>
                </div>
                <div>
                  <Label>UUID</Label>
                  <code className="text-xs bg-gray-100 px-2 py-1 rounded">
                    {selectedAdmin.uuid}
                  </code>
                </div>
              </div>

              <div>
                <Label>权限列表</Label>
                <div className="flex flex-wrap gap-2 mt-2">
                  {selectedAdmin.permissions.map((permission, index) => (
                    <Badge key={index} variant="secondary">
                      {permission === 'ALL' ? '全部权限' : permission}
                    </Badge>
                  ))}
                </div>
              </div>

              <div className="grid grid-cols-2 gap-4">
                <div>
                  <Label>操作数量</Label>
                  <p className="text-sm">{selectedAdmin.operationCount}</p>
                </div>
                <div>
                  <Label>最后操作时间</Label>
                  <p className="text-sm">
                    {selectedAdmin.lastOperationAt ?
                      new Date(selectedAdmin.lastOperationAt).toLocaleString() :
                      '无记录'
                    }
                  </p>
                </div>
                <div>
                  <Label>最后登录时间</Label>
                  <p className="text-sm">
                    {selectedAdmin.lastLoginAt ?
                      new Date(selectedAdmin.lastLoginAt).toLocaleString() :
                      '未登录'
                    }
                  </p>
                </div>
                <div>
                  <Label>最后登录IP</Label>
                  <p className="text-sm">{selectedAdmin.lastLoginIp || '-'}</p>
                </div>
              </div>
            </div>
          )}

          <DialogFooter>
            <Button variant="outline" onClick={() => setIsDetailsDialogOpen(false)}>
              关闭
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* 操作记录对话框 */}
      <Dialog open={isOperationsDialogOpen} onOpenChange={setIsOperationsDialogOpen}>
        <DialogContent className="max-w-4xl">
          <DialogHeader>
            <DialogTitle>操作记录</DialogTitle>
            <DialogDescription>
              {selectedAdmin?.username} 的操作记录和行为记录
            </DialogDescription>
          </DialogHeader>

          <div className="space-y-4">
            <div className="rounded-md border">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>时间</TableHead>
                    <TableHead>操作</TableHead>
                    <TableHead>目标</TableHead>
                    <TableHead>详情</TableHead>
                    <TableHead>IP地址</TableHead>
                    <TableHead>结果</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {operations.length === 0 ? (
                    <TableRow>
                      <TableCell colSpan={6} className="text-center py-4">
                        暂无操作记录
                      </TableCell>
                    </TableRow>
                  ) : (
                    operations.map((operation) => (
                      <TableRow key={operation.id}>
                        <TableCell>
                          {new Date(operation.timestamp).toLocaleString()}
                        </TableCell>
                        <TableCell>{operation.action}</TableCell>
                        <TableCell>
                          <code className="text-xs bg-gray-100 px-2 py-1 rounded">
                            {operation.target}
                          </code>
                        </TableCell>
                        <TableCell>{operation.details}</TableCell>
                        <TableCell>{operation.ipAddress}</TableCell>
                        <TableCell>
                          <Badge variant={operation.result === 'success' ? 'default' : 'destructive'}>
                            {operation.result === 'success' ? '成功' : '失败'}
                          </Badge>
                        </TableCell>
                      </TableRow>
                    ))
                  )}
                </TableBody>
              </Table>
            </div>
          </div>

          <DialogFooter>
            <Button variant="outline" onClick={() => setIsOperationsDialogOpen(false)}>
              关闭
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* 创建管理员对话框 */}
      <Dialog open={isCreateDialogOpen} onOpenChange={setIsCreateDialogOpen}>
        <DialogContent className="max-w-md">
          <DialogHeader>
            <DialogTitle>添加新管理员</DialogTitle>
            <DialogDescription>
              创建新的管理员账号
            </DialogDescription>
          </DialogHeader>

          <div className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="username">用户名 <span className="text-red-500">*</span></Label>
              <Input
                id="username"
                placeholder="输入用户名"
                value={newAdminForm.username}
                onChange={(e) => setNewAdminForm(prev => ({ ...prev, username: e.target.value }))}
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="email">邮箱 <span className="text-red-500">*</span></Label>
              <Input
                id="email"
                type="email"
                placeholder="输入邮箱地址"
                value={newAdminForm.email}
                onChange={(e) => setNewAdminForm(prev => ({ ...prev, email: e.target.value }))}
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="name">姓名 <span className="text-red-500">*</span></Label>
              <Input
                id="name"
                placeholder="输入真实姓名"
                value={newAdminForm.name}
                onChange={(e) => setNewAdminForm(prev => ({ ...prev, name: e.target.value }))}
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="role">角色 <span className="text-red-500">*</span></Label>
              <Select value={newAdminForm.role} onValueChange={(value: 'admin' | 'superadmin') => setNewAdminForm(prev => ({ ...prev, role: value }))}>
                <SelectTrigger>
                  <SelectValue placeholder="选择角色" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="admin">管理员</SelectItem>
                  <SelectItem value="superadmin">超级管理员</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <Label htmlFor="password">密码 <span className="text-red-500">*</span></Label>
              <Input
                id="password"
                type="password"
                placeholder="输入密码"
                value={newAdminForm.password}
                onChange={(e) => setNewAdminForm(prev => ({ ...prev, password: e.target.value }))}
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="confirmPassword">确认密码 <span className="text-red-500">*</span></Label>
              <Input
                id="confirmPassword"
                type="password"
                placeholder="再次输入密码"
                value={newAdminForm.confirmPassword}
                onChange={(e) => setNewAdminForm(prev => ({ ...prev, confirmPassword: e.target.value }))}
              />
            </div>
          </div>

          <DialogFooter>
            <Button variant="outline" onClick={() => setIsCreateDialogOpen(false)}>
              取消
            </Button>
            <Button onClick={handleCreateAdmin}>
              创建管理员
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* 删除管理员确认对话框 */}
      <Dialog open={isDeleteDialogOpen} onOpenChange={setIsDeleteDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>删除管理员</DialogTitle>
            <DialogDescription>
              此操作将永久删除管理员账号，无法恢复
            </DialogDescription>
          </DialogHeader>

          <div className="py-4">
            {selectedAdmin && (
              <>
                <p>您确定要删除以下管理员吗？</p>
                <div className="mt-2 p-3 bg-muted rounded-md">
                  <p><span className="font-medium">用户名:</span> {selectedAdmin.username}</p>
                  <p><span className="font-medium">邮箱:</span> {selectedAdmin.email}</p>
                  <p><span className="font-medium">角色:</span> {selectedAdmin.role}</p>
                  <p><span className="font-medium">UUID:</span>
                    <code className="ml-2 text-xs bg-gray-100 px-2 py-1 rounded">
                      {selectedAdmin.uuid}
                    </code>
                  </p>
                  <p><span className="font-medium">操作数量:</span> {selectedAdmin.operationCount}</p>
                </div>

                <div className="mt-4 bg-red-50 border border-red-200 rounded-md p-3 text-sm text-red-800">
                  <p>⚠️ 警告：删除管理员是高风险操作！</p>
                  <p className="mt-2 font-medium">删除管理员可能会影响：</p>
                  <ul className="mt-1 list-disc list-inside">
                    <li>相关的操作记录和审计日志</li>
                    <li>管理员创建的配置和设置</li>
                    <li>系统的管理权限分配</li>
                  </ul>
                </div>
              </>
            )}
          </div>

          <DialogFooter>
            <Button variant="outline" onClick={() => setIsDeleteDialogOpen(false)}>
              取消
            </Button>
            <Button
              variant="destructive"
              onClick={handleDeleteAdmin}
            >
              确认删除
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </SuperAdminLayout>
  );
};

export default SuperAdminManagementPage;
