-- 优化后的数据库结构 v2.0
-- 基于前端需求设计，统一ID标识系统，优化查询性能

-- 1. 统一用户表
CREATE TABLE IF NOT EXISTS users_v2 (
  id TEXT PRIMARY KEY,                    -- user_xxx 或 anon_xxx
  user_type TEXT NOT NULL CHECK (user_type IN ('registered', 'anonymous')),
  email TEXT UNIQUE,                      -- 仅注册用户
  display_name TEXT,                      -- 显示名称
  session_id TEXT,                        -- 会话标识
  metadata TEXT,                          -- JSON: 扩展信息
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 2. 优化后的问卷回复表
CREATE TABLE IF NOT EXISTS questionnaire_responses_v2 (
  id TEXT PRIMARY KEY,                    -- quest_xxx
  user_id TEXT NOT NULL,                 -- 关联用户
  session_id TEXT NOT NULL,              -- 提交会话
  
  -- 标准化基本信息字段
  education_level TEXT NOT NULL,         -- 标准化: undergraduate, master, etc.
  education_level_display TEXT NOT NULL, -- 显示用: 本科, 硕士, etc.
  major_category TEXT,                   -- 标准化专业分类
  major_display TEXT,                    -- 显示用专业名称
  graduation_year INTEGER,
  region_code TEXT,                      -- 标准化地区代码
  region_display TEXT,                   -- 显示用地区名称
  
  -- 就业相关信息
  employment_status TEXT,                -- employed, unemployed, studying
  current_industry_code TEXT,            -- 标准化行业代码
  current_industry_display TEXT,         -- 显示用行业名称
  position_level TEXT,                   -- entry, mid, senior, management
  salary_range TEXT,                     -- 标准化薪资范围
  work_location TEXT,                    -- 工作地点
  
  -- 求职相关信息
  job_search_duration TEXT,              -- 求职时长
  job_search_channels TEXT,              -- JSON: 求职渠道数组
  interview_count INTEGER DEFAULT 0,     -- 面试次数
  offer_count INTEGER DEFAULT 0,         -- 收到offer数量
  
  -- 原始内容（用于心声提取）
  advice_content TEXT,                   -- 给学生的建议
  observation_content TEXT,              -- 就业观察
  additional_comments TEXT,              -- 其他补充
  
  -- 元数据和状态
  original_data TEXT,                    -- JSON: 完整原始提交数据
  ip_address TEXT,                       -- 提交IP（用于防刷）
  user_agent TEXT,                       -- 用户代理
  submission_source TEXT DEFAULT 'web',  -- 提交来源
  status TEXT DEFAULT 'submitted' CHECK (status IN ('submitted', 'processed', 'archived')),
  
  -- 时间戳
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  
  FOREIGN KEY (user_id) REFERENCES users_v2(id)
);

-- 3. 问卷心声表（重新设计）
CREATE TABLE IF NOT EXISTS questionnaire_voices_v2 (
  id TEXT PRIMARY KEY,                   -- voice_xxx
  source_response_id TEXT NOT NULL,     -- 来源问卷回复ID
  user_id TEXT NOT NULL,                -- 关联用户ID
  
  -- 心声内容
  voice_type TEXT NOT NULL CHECK (voice_type IN ('advice', 'observation', 'comment')),
  title TEXT NOT NULL,                   -- 心声标题（自动生成或手动设置）
  content TEXT NOT NULL,                 -- 心声内容
  summary TEXT,                          -- 内容摘要（用于列表显示）
  
  -- 继承的分类属性
  education_level TEXT,
  education_level_display TEXT,
  region_code TEXT,
  region_display TEXT,
  industry_code TEXT,
  industry_display TEXT,
  
  -- 标签系统
  tags TEXT,                             -- JSON: 标签数组
  category TEXT,                         -- 主分类
  
  -- 审核状态
  status TEXT DEFAULT 'pending' CHECK (status IN ('pending', 'approved', 'rejected', 'archived')),
  reviewed_by TEXT,                      -- 审核员ID
  reviewed_at TIMESTAMP,                 -- 审核时间
  review_notes TEXT,                     -- 审核备注
  
  -- 互动数据
  likes INTEGER DEFAULT 0,
  dislikes INTEGER DEFAULT 0,
  views INTEGER DEFAULT 0,
  shares INTEGER DEFAULT 0,
  
  -- 质量评分
  quality_score REAL DEFAULT 0.0,       -- 内容质量评分
  helpfulness_score REAL DEFAULT 0.0,   -- 有用性评分
  
  -- 时间戳
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  
  FOREIGN KEY (source_response_id) REFERENCES questionnaire_responses_v2(id),
  FOREIGN KEY (user_id) REFERENCES users_v2(id)
);

-- 4. 故事内容表（优化）
CREATE TABLE IF NOT EXISTS story_contents_v2 (
  id TEXT PRIMARY KEY,                   -- story_xxx
  user_id TEXT NOT NULL,                -- 关联用户ID
  
  -- 基本内容
  title TEXT NOT NULL,
  content TEXT NOT NULL,
  summary TEXT,                          -- 故事摘要
  
  -- 分类信息
  category TEXT,                         -- 主分类
  subcategory TEXT,                      -- 子分类
  education_level TEXT,
  education_level_display TEXT,
  industry_code TEXT,
  industry_display TEXT,
  
  -- 故事属性
  story_type TEXT DEFAULT 'experience', -- experience, advice, observation
  is_anonymous BOOLEAN DEFAULT true,
  author_name TEXT,                      -- 作者署名（如果不匿名）
  
  -- 审核状态
  status TEXT DEFAULT 'pending' CHECK (status IN ('pending', 'approved', 'rejected', 'archived')),
  reviewed_by TEXT,
  reviewed_at TIMESTAMP,
  review_notes TEXT,
  
  -- 互动数据
  likes INTEGER DEFAULT 0,
  dislikes INTEGER DEFAULT 0,
  views INTEGER DEFAULT 0,
  shares INTEGER DEFAULT 0,
  comments_count INTEGER DEFAULT 0,
  
  -- 质量和推荐
  quality_score REAL DEFAULT 0.0,
  featured BOOLEAN DEFAULT false,        -- 是否精选
  trending_score REAL DEFAULT 0.0,      -- 热度评分
  
  -- 元数据
  word_count INTEGER,                    -- 字数统计
  reading_time INTEGER,                  -- 预估阅读时间（分钟）
  
  -- 时间戳
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  
  FOREIGN KEY (user_id) REFERENCES users_v2(id)
);

-- 5. 标签系统表
CREATE TABLE IF NOT EXISTS tags_v2 (
  id TEXT PRIMARY KEY,                   -- tag_xxx
  name TEXT NOT NULL UNIQUE,             -- 标签名称
  display_name TEXT NOT NULL,            -- 显示名称
  category TEXT,                         -- 标签分类
  description TEXT,                      -- 标签描述
  color TEXT DEFAULT '#3B82F6',          -- 标签颜色
  usage_count INTEGER DEFAULT 0,         -- 使用次数
  is_system BOOLEAN DEFAULT false,       -- 是否系统标签
  status TEXT DEFAULT 'active' CHECK (status IN ('active', 'inactive', 'deprecated')),
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 6. 内容标签关联表
CREATE TABLE IF NOT EXISTS content_tags_v2 (
  id TEXT PRIMARY KEY,
  content_id TEXT NOT NULL,              -- 内容ID（story_xxx 或 voice_xxx）
  tag_id TEXT NOT NULL,                  -- 标签ID
  content_type TEXT NOT NULL CHECK (content_type IN ('story', 'voice')),
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  
  UNIQUE(content_id, tag_id),
  FOREIGN KEY (tag_id) REFERENCES tags_v2(id)
);

-- 7. 系统配置表
CREATE TABLE IF NOT EXISTS system_config_v2 (
  key TEXT PRIMARY KEY,
  value TEXT NOT NULL,
  description TEXT,
  category TEXT,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 8. 审核日志表
CREATE TABLE IF NOT EXISTS review_logs_v2 (
  id TEXT PRIMARY KEY,                   -- review_xxx
  content_id TEXT NOT NULL,              -- 被审核内容ID
  content_type TEXT NOT NULL,            -- 内容类型
  reviewer_id TEXT NOT NULL,             -- 审核员ID
  action TEXT NOT NULL,                  -- approve, reject, archive
  previous_status TEXT,                  -- 之前状态
  new_status TEXT,                       -- 新状态
  notes TEXT,                            -- 审核备注
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 创建索引以优化查询性能

-- 问卷回复表索引
CREATE INDEX IF NOT EXISTS idx_questionnaire_responses_v2_user ON questionnaire_responses_v2(user_id);
CREATE INDEX IF NOT EXISTS idx_questionnaire_responses_v2_session ON questionnaire_responses_v2(session_id);
CREATE INDEX IF NOT EXISTS idx_questionnaire_responses_v2_stats ON questionnaire_responses_v2(education_level, region_code, graduation_year, created_at);
CREATE INDEX IF NOT EXISTS idx_questionnaire_responses_v2_status ON questionnaire_responses_v2(status, created_at);

-- 问卷心声表索引
CREATE INDEX IF NOT EXISTS idx_questionnaire_voices_v2_source ON questionnaire_voices_v2(source_response_id);
CREATE INDEX IF NOT EXISTS idx_questionnaire_voices_v2_user ON questionnaire_voices_v2(user_id);
CREATE INDEX IF NOT EXISTS idx_questionnaire_voices_v2_display ON questionnaire_voices_v2(voice_type, status, created_at DESC);
CREATE INDEX IF NOT EXISTS idx_questionnaire_voices_v2_category ON questionnaire_voices_v2(education_level, region_code, status);

-- 故事内容表索引
CREATE INDEX IF NOT EXISTS idx_story_contents_v2_user ON story_contents_v2(user_id);
CREATE INDEX IF NOT EXISTS idx_story_contents_v2_display ON story_contents_v2(status, category, created_at DESC);
CREATE INDEX IF NOT EXISTS idx_story_contents_v2_featured ON story_contents_v2(featured, status, trending_score DESC);
CREATE INDEX IF NOT EXISTS idx_story_contents_v2_search ON story_contents_v2(status, title, content);

-- 标签关联表索引
CREATE INDEX IF NOT EXISTS idx_content_tags_v2_content ON content_tags_v2(content_id, content_type);
CREATE INDEX IF NOT EXISTS idx_content_tags_v2_tag ON content_tags_v2(tag_id);

-- 审核日志表索引
CREATE INDEX IF NOT EXISTS idx_review_logs_v2_content ON review_logs_v2(content_id, content_type);
CREATE INDEX IF NOT EXISTS idx_review_logs_v2_reviewer ON review_logs_v2(reviewer_id, created_at DESC);

-- 插入系统配置默认值
INSERT OR IGNORE INTO system_config_v2 (key, value, description, category) VALUES
('schema_version', '2.0', '数据库结构版本', 'system'),
('max_questionnaire_per_session', '1', '每个会话最大问卷提交数', 'questionnaire'),
('auto_extract_voices', 'true', '是否自动提取问卷心声', 'voices'),
('default_page_size', '20', '默认分页大小', 'pagination'),
('max_page_size', '100', '最大分页大小', 'pagination');

-- 插入默认标签
INSERT OR IGNORE INTO tags_v2 (id, name, display_name, category, description, is_system) VALUES
('tag_education_advice', 'education_advice', '教育建议', 'advice', '关于教育和学习的建议', true),
('tag_career_planning', 'career_planning', '职业规划', 'career', '职业发展和规划相关', true),
('tag_job_search', 'job_search', '求职经验', 'career', '求职过程和经验分享', true),
('tag_industry_insight', 'industry_insight', '行业洞察', 'industry', '行业发展趋势和见解', true),
('tag_skill_development', 'skill_development', '技能发展', 'skill', '技能学习和发展', true),
('tag_workplace_culture', 'workplace_culture', '职场文化', 'workplace', '职场环境和文化', true),
('tag_salary_negotiation', 'salary_negotiation', '薪资谈判', 'career', '薪资和福利相关', true),
('tag_work_life_balance', 'work_life_balance', '工作生活平衡', 'lifestyle', '工作与生活的平衡', true);
