import React, { useState, useEffect } from 'react';
import { VirtualTable } from '@/components/ui/virtual-list';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Checkbox } from '@/components/ui/checkbox';
import { formatDistanceToNow } from 'date-fns';
import { zhCN } from 'date-fns/locale';
import { Clock, Eye, Edit, XCircle, CheckCircle } from 'lucide-react';

// 内容类型
enum ContentType {
  QUESTIONNAIRE = 'questionnaire',
  STORY = 'story'
}

// 内容状态
enum ContentStatus {
  PENDING = 'pending',
  APPROVED = 'approved',
  REJECTED = 'rejected',
  EDITED = 'edited'
}

// 待审核内容接口
interface PendingContent {
  id: string;
  sequenceNumber: string;
  type: ContentType;
  originalContent: any;
  sanitizedContent?: any;
  status: ContentStatus;
  reviewerId?: string;
  reviewedAt?: string;
  originIp?: string;
  userAgent?: string;
  flags: string[];
  reviewNotes?: string;
  createdAt: string;
  updatedAt: string;
}

interface VirtualContentTableProps {
  contents: PendingContent[];
  selectedIds: string[];
  onSelectContent: (id: string, isSelected: boolean) => void;
  onSelectAll: (isSelected: boolean) => void;
  onViewContent: (content: PendingContent) => void;
  onEditContent: (content: PendingContent) => void;
  onRejectContent: (content: PendingContent) => void;
  onLoadMore?: () => void;
  isLoading?: boolean;
  height?: number;
}

/**
 * 虚拟内容表格组件
 * 使用虚拟滚动优化大量数据的显示
 */
export default function VirtualContentTable({
  contents,
  selectedIds,
  onSelectContent,
  onSelectAll,
  onViewContent,
  onEditContent,
  onRejectContent,
  onLoadMore,
  isLoading = false,
  height = 600
}: VirtualContentTableProps) {
  const [visibleContents, setVisibleContents] = useState<PendingContent[]>([]);
  const [isAllSelected, setIsAllSelected] = useState(false);

  // 更新全选状态
  useEffect(() => {
    setIsAllSelected(contents.length > 0 && selectedIds.length === contents.length);
  }, [contents, selectedIds]);

  // 渲染标记徽章
  const renderFlagBadge = (flag: string) => {
    let color = 'default';
    let icon = null;
    
    switch (flag) {
      case 'sensitive-words':
        color = 'destructive';
        icon = <span className="h-3 w-3 mr-1">⚠️</span>;
        break;
      case 'high-exposure':
        color = 'destructive';
        icon = <span className="h-3 w-3 mr-1">🔥</span>;
        break;
      case 'suspicious-behavior':
        color = 'warning';
        icon = <span className="h-3 w-3 mr-1">⚠️</span>;
        break;
      default:
        color = 'secondary';
    }
    
    return (
      <Badge variant={color as any} className="mr-1 mb-1">
        {icon}
        {flag}
      </Badge>
    );
  };

  // 渲染内容类型徽章
  const renderTypeBadge = (type: ContentType) => {
    return (
      <Badge variant={type === ContentType.STORY ? 'default' : 'outline'}>
        {type === ContentType.STORY ? '故事' : '问卷'}
      </Badge>
    );
  };

  // 渲染表头
  const renderHeader = () => (
    <TableHeader>
      <TableRow>
        <TableHead className="w-[40px]">
          <Checkbox 
            checked={isAllSelected}
            onCheckedChange={(checked) => onSelectAll(!!checked)}
            aria-label="全选"
          />
        </TableHead>
        <TableHead>序列号</TableHead>
        <TableHead>类型</TableHead>
        <TableHead>内容预览</TableHead>
        <TableHead>标记</TableHead>
        <TableHead>提交时间</TableHead>
        <TableHead>操作</TableHead>
      </TableRow>
    </TableHeader>
  );

  // 渲染行
  const renderRow = (content: PendingContent, index: number, isVisible: boolean) => (
    <TableRow key={content.id} data-state={selectedIds.includes(content.id) ? 'selected' : undefined}>
      <TableCell>
        <Checkbox 
          checked={selectedIds.includes(content.id)}
          onCheckedChange={(checked) => onSelectContent(content.id, !!checked)}
          aria-label={`选择 ${content.sequenceNumber}`}
        />
      </TableCell>
      <TableCell className="font-mono">{content.sequenceNumber}</TableCell>
      <TableCell>{renderTypeBadge(content.type)}</TableCell>
      <TableCell className="max-w-xs truncate">
        {content.type === ContentType.STORY 
          ? content.originalContent.title
          : `问卷回复 #${content.sequenceNumber}`}
      </TableCell>
      <TableCell>
        <div className="flex flex-wrap">
          {content.flags.map((flag) => (
            <span key={flag}>{renderFlagBadge(flag)}</span>
          ))}
        </div>
      </TableCell>
      <TableCell>
        <div className="flex items-center">
          <Clock className="h-4 w-4 mr-1 text-muted-foreground" />
          <span title={new Date(content.createdAt).toLocaleString()}>
            {formatDistanceToNow(new Date(content.createdAt), { addSuffix: true, locale: zhCN })}
          </span>
        </div>
      </TableCell>
      <TableCell>
        <div className="flex items-center space-x-2">
          <Button 
            variant="outline" 
            size="sm"
            onClick={() => onViewContent(content)}
          >
            <Eye className="h-4 w-4 mr-1" />
            查看
          </Button>
          <Button 
            variant="default" 
            size="sm"
            onClick={() => onEditContent(content)}
          >
            <Edit className="h-4 w-4 mr-1" />
            编辑
          </Button>
          <Button 
            variant="destructive" 
            size="sm"
            onClick={() => onRejectContent(content)}
          >
            <XCircle className="h-4 w-4 mr-1" />
            拒绝
          </Button>
        </div>
      </TableCell>
    </TableRow>
  );

  // 处理加载更多
  const handleEndReached = () => {
    if (!isLoading && onLoadMore) {
      onLoadMore();
    }
  };

  return (
    <div className="border rounded-lg">
      <VirtualTable
        items={contents}
        height={height}
        itemHeight={60} // 每行高度
        renderHeader={renderHeader}
        renderRow={renderRow}
        overscan={5}
        onEndReached={handleEndReached}
        endReachedThreshold={0.8}
        className="w-full"
      />
      
      {isLoading && (
        <div className="p-4 text-center">
          <div className="inline-block h-6 w-6 animate-spin rounded-full border-2 border-solid border-current border-r-transparent align-[-0.125em] motion-reduce:animate-[spin_1.5s_linear_infinite]" />
          <span className="ml-2">加载更多...</span>
        </div>
      )}
    </div>
  );
}
