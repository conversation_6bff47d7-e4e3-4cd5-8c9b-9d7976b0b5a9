/**
 * 内容脱敏服务
 * 为问卷系统和故事墙提供内容脱敏功能
 */

export interface DeidentificationResult {
  success: boolean;
  processed: boolean;
  originalContent: string;
  processedContent: string;
  needsReview: boolean;
  method?: 'ai' | 'local';
  contentType?: string;
  flagged?: boolean;
  error?: string;
}

/**
 * 对内容进行脱敏处理
 * @param content 原始内容
 * @param contentType 内容类型 ('questionnaire' | 'story')
 * @param userId 用户ID（可选）
 * @returns 脱敏结果
 */
export async function deidentifyContent(
  content: string,
  contentType: 'questionnaire' | 'story',
  userId?: string
): Promise<DeidentificationResult> {
  try {
    const response = await fetch('http://localhost:5173/api/content/deidentify', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        content,
        contentType,
        userId
      })
    });

    if (!response.ok) {
      throw new Error(`脱敏API调用失败: ${response.status}`);
    }

    const result = await response.json();
    
    if (!result.success) {
      throw new Error(result.error || '脱敏处理失败');
    }

    return result;
  } catch (error) {
    console.error('内容脱敏失败:', error);
    
    // 返回原始内容作为回退
    return {
      success: false,
      processed: false,
      originalContent: content,
      processedContent: content,
      needsReview: false,
      error: error instanceof Error ? error.message : '脱敏服务不可用'
    };
  }
}

/**
 * 批量脱敏处理
 * @param contents 内容数组
 * @param contentType 内容类型
 * @param userId 用户ID（可选）
 * @returns 脱敏结果数组
 */
export async function deidentifyBatch(
  contents: string[],
  contentType: 'questionnaire' | 'story',
  userId?: string
): Promise<DeidentificationResult[]> {
  const results: DeidentificationResult[] = [];
  
  // 并发处理，但限制并发数量
  const batchSize = 3;
  for (let i = 0; i < contents.length; i += batchSize) {
    const batch = contents.slice(i, i + batchSize);
    const batchPromises = batch.map(content => 
      deidentifyContent(content, contentType, userId)
    );
    
    const batchResults = await Promise.all(batchPromises);
    results.push(...batchResults);
  }
  
  return results;
}

/**
 * 检查内容是否需要脱敏
 * @param content 内容
 * @param contentType 内容类型
 * @returns 是否需要脱敏
 */
export function shouldDeidentify(
  content: string,
  contentType: 'questionnaire' | 'story'
): boolean {
  // 简单的预检查，避免不必要的API调用
  if (!content || content.trim().length < 10) {
    return false;
  }
  
  // 检查是否包含可能的敏感信息
  const sensitivePatterns = [
    /1[3-9]\d{9}/, // 手机号
    /\b[\w.-]+@[\w.-]+\.\w+\b/, // 邮箱
    /\d{17}[\dXx]/, // 身份证
    /不良|违规|敏感|抵制/, // 不良信息关键词
  ];
  
  return sensitivePatterns.some(pattern => pattern.test(content));
}

/**
 * 创建审核记录（当内容需要人工审核时）
 * @param content 原始内容
 * @param processedContent 处理后内容
 * @param contentType 内容类型
 * @param userId 用户ID
 * @returns 审核记录ID
 */
export async function createReviewRecord(
  content: string,
  processedContent: string,
  contentType: 'questionnaire' | 'story',
  userId?: string
): Promise<string | null> {
  try {
    const response = await fetch('http://localhost:5173/api/admin/review/create', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        originalContent: content,
        processedContent: processedContent,
        contentType: contentType,
        userId: userId,
        status: 'pending',
        flagReason: '内容脱敏检测到需要人工审核',
        createdAt: new Date().toISOString()
      })
    });

    if (response.ok) {
      const result = await response.json();
      return result.reviewId || null;
    }
    
    return null;
  } catch (error) {
    console.error('创建审核记录失败:', error);
    return null;
  }
}

/**
 * 脱敏处理的统一入口
 * 包含完整的脱敏流程：检查 -> 脱敏 -> 审核
 * @param content 原始内容
 * @param contentType 内容类型
 * @param userId 用户ID
 * @returns 处理结果
 */
export async function processContentWithDeidentification(
  content: string,
  contentType: 'questionnaire' | 'story',
  userId?: string
): Promise<{
  content: string;
  needsReview: boolean;
  reviewId?: string;
  processed: boolean;
  method?: string;
}> {
  // 1. 检查是否需要脱敏
  if (!shouldDeidentify(content, contentType)) {
    return {
      content: content,
      needsReview: false,
      processed: false
    };
  }
  
  // 2. 执行脱敏处理
  const deidentifyResult = await deidentifyContent(content, contentType, userId);
  
  if (!deidentifyResult.success) {
    // 脱敏失败，返回原始内容
    return {
      content: content,
      needsReview: false,
      processed: false
    };
  }
  
  // 3. 如果需要审核，创建审核记录
  let reviewId: string | undefined;
  if (deidentifyResult.needsReview) {
    reviewId = await createReviewRecord(
      deidentifyResult.originalContent,
      deidentifyResult.processedContent,
      contentType,
      userId
    ) || undefined;
  }
  
  return {
    content: deidentifyResult.processedContent,
    needsReview: deidentifyResult.needsReview,
    reviewId: reviewId,
    processed: deidentifyResult.processed,
    method: deidentifyResult.method
  };
}

/**
 * 获取脱敏统计信息
 * @returns 统计信息
 */
export async function getDeidentificationStats(): Promise<{
  totalProcessed: number;
  flaggedContent: number;
  autoApproved: number;
  manualReview: number;
} | null> {
  try {
    const response = await fetch('http://localhost:5173/api/admin/deidentification/stats');
    
    if (response.ok) {
      const result = await response.json();
      return result.stats;
    }
    
    return null;
  } catch (error) {
    console.error('获取脱敏统计失败:', error);
    return null;
  }
}
