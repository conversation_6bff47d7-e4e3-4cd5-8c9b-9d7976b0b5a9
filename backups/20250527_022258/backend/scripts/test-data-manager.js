/**
 * 测试数据管理器
 *
 * 支持测试数据的生成、重置、备份、恢复等完整生命周期管理
 */

const { PrismaClient } = require('@prisma/client');
const fs = require('fs').promises;
const path = require('path');
const crypto = require('crypto');

const prisma = new PrismaClient();

/**
 * 测试数据配置
 */
const TEST_DATA_CONFIG = {
  version: '1.0',
  datasets: {
    'role-testing': {
      questionnaires: 50,
      stories: 30,
      reviewers: 5,
      admins: 3,
      regularUsers: 10,
      description: '角色权限测试数据集'
    },
    'audit-testing': {
      questionnaires: 20,
      stories: 50,
      reviewers: 3,
      admins: 2,
      description: '审核流程测试数据集'
    }
  },
  snapshots: {
    autoCapture: true,
    retentionDays: 30,
    maxSnapshots: 10
  }
};

/**
 * 测试数据管理器类
 */
class TestDataManager {
  constructor() {
    this.snapshotDir = path.join(__dirname, '../test-snapshots');
    this.backupDir = path.join(__dirname, '../test-backups');
  }

  /**
   * 初始化目录
   */
  async initDirectories() {
    try {
      await fs.mkdir(this.snapshotDir, { recursive: true });
      await fs.mkdir(this.backupDir, { recursive: true });
    } catch (error) {
      console.error('创建目录失败:', error);
    }
  }

  /**
   * 生成测试数据
   */
  async generateTestData(datasetName = 'role-testing') {
    console.log(`🚀 开始生成测试数据集: ${datasetName}`);

    const config = TEST_DATA_CONFIG.datasets[datasetName];
    if (!config) {
      throw new Error(`未找到数据集配置: ${datasetName}`);
    }

    // 清除现有测试数据
    await this.clearTestData();

    // 生成数据
    const results = {
      users: await this.generateUsers(config),
      questionnaires: await this.generateQuestionnaires(config),
      stories: await this.generateStories(config)
    };

    // 创建初始快照
    await this.createSnapshot('initial-state', '初始测试数据状态');

    console.log('✅ 测试数据生成完成');
    console.log('📊 生成统计:', results);

    return results;
  }

  /**
   * 生成用户数据
   */
  async generateUsers(config) {
    console.log('👥 生成用户数据...');

    const users = [];

    // 生成审核员账号
    for (let i = 1; i <= config.reviewers; i++) {
      const user = await prisma.user.create({
        data: {
          email: `reviewer${i}@test.com`,
          username: `reviewer${i}`,
          name: `审核员${i}`,
          role: 'reviewer',
          emailVerified: true,
          passwordHash: '$2b$10$example.hash.for.testing.purposes.only',
          isTestData: true,
          testDataVersion: TEST_DATA_CONFIG.version,
          testDataSet: 'role-testing',
          createdAt: new Date(),
          lastLoginAt: new Date(Date.now() - Math.random() * 7 * 24 * 60 * 60 * 1000) // 随机最近7天内
        }
      });
      users.push(user);
    }

    // 生成管理员账号
    for (let i = 1; i <= config.admins; i++) {
      const user = await prisma.user.create({
        data: {
          email: `admin${i}@test.com`,
          username: `admin${i}`,
          name: `管理员${i}`,
          role: 'admin',
          emailVerified: true,
          passwordHash: '$2b$10$example.hash.for.testing.purposes.only',
          isTestData: true,
          testDataVersion: TEST_DATA_CONFIG.version,
          testDataSet: 'role-testing',
          createdAt: new Date(),
          lastLoginAt: new Date(Date.now() - Math.random() * 3 * 24 * 60 * 60 * 1000) // 随机最近3天内
        }
      });
      users.push(user);
    }

    // 生成普通用户账号
    for (let i = 1; i <= config.regularUsers; i++) {
      const user = await prisma.user.create({
        data: {
          email: `user${i}@test.com`,
          username: `user${i}`,
          name: `用户${i}`,
          role: 'user',
          emailVerified: Math.random() > 0.3, // 70%已验证
          passwordHash: '$2b$10$example.hash.for.testing.purposes.only',
          isTestData: true,
          testDataVersion: TEST_DATA_CONFIG.version,
          testDataSet: 'role-testing',
          createdAt: new Date(Date.now() - Math.random() * 30 * 24 * 60 * 60 * 1000), // 随机最近30天内
          lastLoginAt: Math.random() > 0.2 ? new Date(Date.now() - Math.random() * 7 * 24 * 60 * 60 * 1000) : null
        }
      });
      users.push(user);
    }

    console.log(`✅ 生成了 ${users.length} 个用户账号`);
    return {
      total: users.length,
      reviewers: config.reviewers,
      admins: config.admins,
      regularUsers: config.regularUsers
    };
  }

  /**
   * 生成问卷数据
   */
  async generateQuestionnaires(config) {
    console.log('📋 生成问卷数据...');

    const questionnaires = [];
    const users = await prisma.user.findMany({
      where: { isTestData: true, role: 'user' }
    });

    const educationLevels = ['高中/中专', '大专', '本科', '硕士', '博士'];
    const regions = ['北上广深', '省会城市', '二线城市', '三四线城市', '县城或乡镇', '海外'];
    const employmentStatuses = ['已就业', '待业中', '创业', '自由职业', '其他'];
    const industries = ['IT/互联网', '金融', '教育', '制造业', '医疗', '文化/传媒', '政府/事业单位'];
    const majors = ['计算机科学', '经济学', '管理学', '法学', '电子工程', '医学', '教育学'];

    for (let i = 1; i <= config.questionnaires; i++) {
      const isAnonymous = i > 20; // 前20个有用户ID，后10个匿名
      const userId = isAnonymous ? null : users[Math.floor(Math.random() * users.length)]?.id;

      const questionnaire = await prisma.questionnaireResponse.create({
        data: {
          sequenceNumber: `TEST${Date.now()}${i.toString().padStart(3, '0')}`,
          userId: userId,
          isAnonymous: isAnonymous,
          submittedById: isAnonymous ? `anon_${crypto.randomUUID()}` : null,

          // 基本信息
          educationLevel: educationLevels[Math.floor(Math.random() * educationLevels.length)],
          major: majors[Math.floor(Math.random() * majors.length)],
          graduationYear: 2020 + Math.floor(Math.random() * 5),
          region: regions[Math.floor(Math.random() * regions.length)],

          // 就业期望
          expectedPosition: `测试职位${i}`,
          expectedSalaryRange: ['5k-10k', '10k-15k', '15k-20k', '20k+'][Math.floor(Math.random() * 4)],
          expectedWorkHours: 40 + Math.floor(Math.random() * 20),
          expectedVacationDays: 5 + Math.floor(Math.random() * 15),

          // 工作经验
          employmentStatus: employmentStatuses[Math.floor(Math.random() * employmentStatuses.length)],
          currentIndustry: Math.random() > 0.3 ? industries[Math.floor(Math.random() * industries.length)] : null,
          currentPosition: Math.random() > 0.4 ? `当前职位${i}` : null,
          monthlySalary: Math.random() > 0.5 ? 5000 + Math.floor(Math.random() * 20000) : null,
          jobSatisfaction: Math.random() > 0.3 ? ['很满意', '满意', '一般', '不满意', '很不满意'][Math.floor(Math.random() * 5)] : null,

          // 失业状态
          unemploymentDuration: Math.random() > 0.7 ? ['1个月以内', '1-3个月', '3-6个月', '6个月以上'][Math.floor(Math.random() * 4)] : null,
          unemploymentReason: Math.random() > 0.8 ? '测试失业原因' : null,
          jobHuntingDifficulty: Math.random() > 0.6 ? Math.floor(Math.random() * 5) + 1 : null,

          // 职业规划
          regretMajor: Math.random() > 0.5,
          preferredMajor: Math.random() > 0.6 ? majors[Math.floor(Math.random() * majors.length)] : null,
          careerChangeIntention: Math.random() > 0.4,
          careerChangeTarget: Math.random() > 0.7 ? '目标行业' : null,

          // 建议和反馈
          adviceForStudents: `这是第${i}份问卷的学生建议内容，包含了实用的就业指导。`,
          observationOnEmployment: `这是第${i}份问卷的就业观察，反映了当前就业市场的真实情况。`,

          // 元数据
          tags: JSON.stringify([
            educationLevels[Math.floor(Math.random() * educationLevels.length)],
            employmentStatuses[Math.floor(Math.random() * employmentStatuses.length)]
          ]),
          status: ['normal', 'verified', 'pending'][Math.floor(Math.random() * 3)],
          ipAddress: `192.168.1.${Math.floor(Math.random() * 255)}`,
          isTestData: true,
          testDataVersion: TEST_DATA_CONFIG.version,
          testDataSet: 'role-testing',
          createdAt: new Date(Date.now() - Math.random() * 30 * 24 * 60 * 60 * 1000)
        }
      });

      questionnaires.push(questionnaire);
    }

    console.log(`✅ 生成了 ${questionnaires.length} 份问卷`);
    return {
      total: questionnaires.length,
      withUserId: questionnaires.filter(q => q.userId).length,
      anonymous: questionnaires.filter(q => q.isAnonymous).length
    };
  }

  /**
   * 生成故事数据
   */
  async generateStories(config) {
    console.log('📖 生成故事数据...');

    const stories = [];
    const users = await prisma.user.findMany({
      where: { isTestData: true }
    });

    const categories = ['求职经历', '职场感悟', '创业故事', '转行经历', '实习体验'];
    const educationLevels = ['高中/中专', '大专', '本科', '硕士', '博士'];
    const industries = ['IT/互联网', '金融', '教育', '制造业', '医疗', '文化/传媒'];

    const storyTemplates = [
      '我的求职之路充满了挑战和机遇。从最初的迷茫到最终找到理想工作，这个过程让我成长了很多。',
      '在职场中，我学会了如何与同事协作，如何处理工作压力，这些经验对我来说非常宝贵。',
      '创业的道路虽然艰辛，但每一次挫折都是成长的机会。我想分享一些创业路上的心得体会。',
      '转行对我来说是一个重大决定。从原来的行业到现在的工作，我经历了很多困难但也收获了很多。',
      '实习期间的经历让我对职场有了更深的理解，也帮助我明确了未来的职业方向。'
    ];

    for (let i = 1; i <= config.stories; i++) {
      const isAnonymous = Math.random() > 0.6; // 40%实名，60%匿名
      const user = isAnonymous ? null : users[Math.floor(Math.random() * users.length)];

      const story = await prisma.story.create({
        data: {
          title: `测试故事${i}: ${categories[Math.floor(Math.random() * categories.length)]}`,
          content: storyTemplates[Math.floor(Math.random() * storyTemplates.length)] +
                  ` 这是第${i}个测试故事的详细内容，包含了丰富的职场经验和人生感悟。` +
                  `故事内容需要经过审核才能发布，当前状态为待审核。`,

          userId: user?.id || null,
          isAnonymous: isAnonymous,
          author: isAnonymous ? '匿名用户' : (user?.name || user?.username || '测试用户'),
          submittedById: isAnonymous ? `anon_${crypto.randomUUID()}` : null,

          // 分类和标签
          category: categories[Math.floor(Math.random() * categories.length)],
          educationLevel: educationLevels[Math.floor(Math.random() * educationLevels.length)],
          industry: industries[Math.floor(Math.random() * industries.length)],
          tags: JSON.stringify([
            categories[Math.floor(Math.random() * categories.length)],
            '测试数据',
            educationLevels[Math.floor(Math.random() * educationLevels.length)]
          ]),

          // 互动数据
          likes: Math.floor(Math.random() * 100),
          dislikes: Math.floor(Math.random() * 20),

          // 审核状态 - 全部设为待审核
          status: 'pending',

          // 元数据
          ipAddress: `192.168.1.${Math.floor(Math.random() * 255)}`,
          isTestData: true,
          testDataVersion: TEST_DATA_CONFIG.version,
          testDataSet: 'role-testing',
          createdAt: new Date(Date.now() - Math.random() * 15 * 24 * 60 * 60 * 1000) // 最近15天内
        }
      });

      stories.push(story);
    }

    console.log(`✅ 生成了 ${stories.length} 个故事（全部待审核状态）`);
    return {
      total: stories.length,
      withUserId: stories.filter(s => s.userId).length,
      anonymous: stories.filter(s => s.isAnonymous).length,
      pendingReview: stories.length // 全部待审核
    };
  }

  /**
   * 清除测试数据
   */
  async clearTestData() {
    console.log('🗑️ 清除现有测试数据...');

    // 按依赖关系顺序删除
    await prisma.vote.deleteMany({ where: { isTestData: true } });
    await prisma.story.deleteMany({ where: { isTestData: true } });
    await prisma.questionnaireResponse.deleteMany({ where: { isTestData: true } });
    await prisma.user.deleteMany({ where: { isTestData: true } });

    console.log('✅ 测试数据清除完成');
  }

  /**
   * 创建快照
   */
  async createSnapshot(name, description = '') {
    console.log(`📸 创建快照: ${name}`);

    await this.initDirectories();

    const snapshot = {
      id: crypto.randomUUID(),
      name,
      description,
      createdAt: new Date().toISOString(),
      version: TEST_DATA_CONFIG.version,
      checksum: '',
      data: {
        users: await prisma.user.findMany({ where: { isTestData: true } }),
        questionnaires: await prisma.questionnaireResponse.findMany({ where: { isTestData: true } }),
        stories: await prisma.story.findMany({ where: { isTestData: true } }),
        votes: await prisma.vote.findMany({ where: { isTestData: true } })
      }
    };

    // 计算校验和
    snapshot.checksum = crypto
      .createHash('md5')
      .update(JSON.stringify(snapshot.data))
      .digest('hex');

    const snapshotPath = path.join(this.snapshotDir, `${name}-${Date.now()}.json`);
    await fs.writeFile(snapshotPath, JSON.stringify(snapshot, null, 2));

    console.log(`✅ 快照已保存: ${snapshotPath}`);
    return snapshot;
  }

  /**
   * 获取测试数据状态
   */
  async getStatus() {
    const users = await prisma.user.count({ where: { isTestData: true } });
    const questionnaires = await prisma.questionnaireResponse.count({ where: { isTestData: true } });
    const stories = await prisma.story.count({ where: { isTestData: true } });
    const pendingStories = await prisma.story.count({
      where: { isTestData: true, status: 'pending' }
    });

    return {
      users,
      questionnaires,
      stories,
      pendingStories,
      hasTestData: users > 0 || questionnaires > 0 || stories > 0,
      lastGenerated: new Date().toISOString()
    };
  }
}

module.exports = { TestDataManager, TEST_DATA_CONFIG };
