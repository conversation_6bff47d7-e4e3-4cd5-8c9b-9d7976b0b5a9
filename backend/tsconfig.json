{"compilerOptions": {"target": "ES2021", "lib": ["ES2021"], "module": "ES2022", "moduleResolution": "node", "esModuleInterop": true, "strict": true, "noImplicitAny": true, "strictNullChecks": true, "strictFunctionTypes": true, "strictBindCallApply": true, "strictPropertyInitialization": true, "noImplicitThis": true, "alwaysStrict": true, "noUnusedLocals": true, "noUnusedParameters": true, "noImplicitReturns": true, "noFallthroughCasesInSwitch": true, "forceConsistentCasingInFileNames": true, "skipLibCheck": true, "outDir": "dist", "types": ["@cloudflare/workers-types", "jest"], "baseUrl": ".", "paths": {"@/*": ["./src/*"]}}, "include": ["src/**/*"], "exclude": ["node_modules", "dist"]}