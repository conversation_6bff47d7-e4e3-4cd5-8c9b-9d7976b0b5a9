import React, { useState } from 'react';
import { Di<PERSON>, DialogContent, DialogHeader, DialogTitle, DialogFooter } from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Checkbox } from '@/components/ui/checkbox';
import { Label } from '@/components/ui/label';
import { 
  ArrowLeft, 
  ArrowRight, 
  ArrowUp, 
  ArrowDown, 
  Edit, 
  CheckCircle, 
  XCircle,
  Play,
  Pause,
  Keyboard,
  HelpCircle
} from 'lucide-react';

interface QuickReviewGuideProps {
  onClose: (dontShowAgain: boolean) => void;
}

/**
 * 快速审核操作指南组件
 * 显示键盘快捷键和操作说明
 */
export default function QuickReviewGuide({ onClose }: QuickReviewGuideProps) {
  const [dontShowAgain, setDontShowAgain] = useState(false);
  
  return (
    <Dialog open={true} onOpenChange={() => onClose(dontShowAgain)}>
      <DialogContent className="max-w-3xl">
        <DialogHeader>
          <DialogTitle className="flex items-center text-xl">
            <Keyboard className="h-5 w-5 mr-2" />
            快速审核模式操作指南
          </DialogTitle>
        </DialogHeader>
        
        <div className="py-4 space-y-6">
          <p className="text-muted-foreground">
            快速审核模式旨在提高审核效率，通过键盘快捷键可以快速完成内容审核操作。
            以下是可用的键盘快捷键和操作说明：
          </p>
          
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            {/* 键盘导航 */}
            <div className="border rounded-lg p-4 space-y-3">
              <h3 className="font-medium flex items-center">
                <ArrowRight className="h-4 w-4 mr-2" />
                内容导航
              </h3>
              
              <div className="space-y-2">
                <div className="flex items-center">
                  <div className="bg-muted rounded px-2 py-1 mr-3 w-24 text-center">
                    <ArrowLeft className="h-4 w-4 inline-block" /> 左箭头
                  </div>
                  <span>查看上一条内容</span>
                </div>
                
                <div className="flex items-center">
                  <div className="bg-muted rounded px-2 py-1 mr-3 w-24 text-center">
                    <ArrowRight className="h-4 w-4 inline-block" /> 右箭头
                  </div>
                  <span>查看下一条内容</span>
                </div>
              </div>
            </div>
            
            {/* 审核操作 */}
            <div className="border rounded-lg p-4 space-y-3">
              <h3 className="font-medium flex items-center">
                <CheckCircle className="h-4 w-4 mr-2" />
                审核操作
              </h3>
              
              <div className="space-y-2">
                <div className="flex items-center">
                  <div className="bg-muted rounded px-2 py-1 mr-3 w-24 text-center">
                    <ArrowDown className="h-4 w-4 inline-block" /> 下箭头
                  </div>
                  <span>通过当前内容</span>
                </div>
                
                <div className="flex items-center">
                  <div className="bg-muted rounded px-2 py-1 mr-3 w-24 text-center">
                    <ArrowUp className="h-4 w-4 inline-block" /> 上箭头
                  </div>
                  <span>拒绝当前内容</span>
                </div>
                
                <div className="flex items-center">
                  <div className="bg-muted rounded px-2 py-1 mr-3 w-24 text-center">
                    Enter
                  </div>
                  <span>编辑当前内容</span>
                </div>
              </div>
            </div>
            
            {/* 自动播放 */}
            <div className="border rounded-lg p-4 space-y-3">
              <h3 className="font-medium flex items-center">
                <Play className="h-4 w-4 mr-2" />
                自动播放
              </h3>
              
              <div className="space-y-2">
                <div className="flex items-center">
                  <div className="bg-muted rounded px-2 py-1 mr-3 w-24 text-center">
                    空格键
                  </div>
                  <span>切换自动播放状态</span>
                </div>
                
                <p className="text-sm text-muted-foreground">
                  自动播放会按设定的时间间隔自动切换到下一条内容。
                  遇到标记为敏感的内容时会自动暂停。
                </p>
              </div>
            </div>
            
            {/* 其他操作 */}
            <div className="border rounded-lg p-4 space-y-3">
              <h3 className="font-medium flex items-center">
                <HelpCircle className="h-4 w-4 mr-2" />
                其他操作
              </h3>
              
              <div className="space-y-2">
                <div className="flex items-center">
                  <div className="bg-muted rounded px-2 py-1 mr-3 w-24 text-center">
                    Esc
                  </div>
                  <span>退出快速审核模式</span>
                </div>
                
                <p className="text-sm text-muted-foreground">
                  您可以随时点击左上角的"返回常规审核"按钮，
                  或按下Esc键退出快速审核模式。
                </p>
              </div>
            </div>
          </div>
          
          <div className="bg-muted p-4 rounded-lg">
            <h3 className="font-medium mb-2">使用技巧</h3>
            <ul className="list-disc list-inside space-y-1 text-sm">
              <li>使用键盘快捷键可以大大提高审核效率</li>
              <li>对于简单内容，可以启用自动播放功能</li>
              <li>敏感内容会自动暂停自动播放，需要人工审核</li>
              <li>每审核50条内容后，建议短暂休息，保护视力</li>
              <li>可以随时点击"操作指南"按钮查看此帮助</li>
            </ul>
          </div>
          
          <div className="flex items-center space-x-2">
            <Checkbox
              id="dont-show-again"
              checked={dontShowAgain}
              onCheckedChange={(checked) => setDontShowAgain(!!checked)}
            />
            <Label htmlFor="dont-show-again">不再显示此指南</Label>
          </div>
        </div>
        
        <DialogFooter>
          <Button onClick={() => onClose(dontShowAgain)}>
            开始审核
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
