import React, { useState, useEffect } from 'react';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { Ta<PERSON>, Ta<PERSON>Content, Ta<PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import { But<PERSON> } from '@/components/ui/button';
import { Skeleton } from '@/components/ui/skeleton';
import { useToast } from '@/components/ui/use-toast';
import { 
  <PERSON><PERSON><PERSON>, 
  <PERSON><PERSON>, 
  <PERSON><PERSON><PERSON>, 
  RefreshCw,
  CheckCircle,
  XCircle,
  AlertTriangle,
  FileText,
  User,
  Calendar,
  Filter
} from 'lucide-react';
import { useApi } from '@/hooks/useApi';
import {
  Chart as ChartJS,
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  BarElement,
  ArcElement,
  Title,
  Tooltip,
  Legend,
} from 'chart.js';
import { Bar, Pie, Line } from 'react-chartjs-2';
import { format } from 'date-fns';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { DatePicker } from '@/components/ui/date-picker';
import { Badge } from '@/components/ui/badge';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';

// 注册Chart.js组件
ChartJS.register(
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  BarElement,
  ArcElement,
  Title,
  Tooltip,
  Legend
);

interface ReviewerPerformance {
  reviewerId: string;
  totalCount: number;
  byType: { contentType: string; count: number }[];
  byAction: { suggestedAction: string; count: number }[];
  bySeverity: { severity: string; count: number }[];
  daily: { date: string; count: number }[];
  qualityStats: {
    avgDataQuality: number | null;
    avgConstructiveValue: number | null;
    avgStoryValue: number | null;
  };
}

interface ReviewerPerformanceDashboardProps {
  className?: string;
}

/**
 * 审核员绩效统计仪表板组件
 * 
 * 用于显示审核员的绩效统计数据
 */
const ReviewerPerformanceDashboard: React.FC<ReviewerPerformanceDashboardProps> = ({ className }) => {
  const { toast } = useToast();
  const api = useApi();
  const [isLoading, setIsLoading] = useState<boolean>(true);
  const [performance, setPerformance] = useState<ReviewerPerformance[]>([]);
  const [startDate, setStartDate] = useState<Date | undefined>(undefined);
  const [endDate, setEndDate] = useState<Date | undefined>(undefined);
  const [selectedReviewer, setSelectedReviewer] = useState<string>('all');
  
  // 加载绩效数据
  const loadPerformance = async () => {
    setIsLoading(true);
    
    try {
      // 构建查询参数
      const params = new URLSearchParams();
      
      if (startDate) {
        params.append('startDate', startDate.toISOString());
      }
      
      if (endDate) {
        params.append('endDate', endDate.toISOString());
      }
      
      // 发送请求
      const response = await api.get(`/admin/content-moderation/reviewer-performance?${params.toString()}`);
      
      if (response.success) {
        setPerformance(response.performance);
      } else {
        toast({
          title: '加载失败',
          description: response.error || '无法加载审核员绩效数据',
          variant: 'destructive'
        });
      }
    } catch (error) {
      console.error('加载审核员绩效数据失败:', error);
      toast({
        title: '加载失败',
        description: '服务器错误，请稍后再试',
        variant: 'destructive'
      });
    } finally {
      setIsLoading(false);
    }
  };
  
  // 组件加载时加载数据
  useEffect(() => {
    loadPerformance();
  }, []);
  
  // 处理筛选
  const handleFilter = () => {
    loadPerformance();
  };
  
  // 处理重置筛选
  const handleResetFilter = () => {
    setStartDate(undefined);
    setEndDate(undefined);
    setSelectedReviewer('all');
    
    // 重新加载数据
    setTimeout(() => {
      loadPerformance();
    }, 0);
  };
  
  // 获取当前选中的审核员数据
  const getSelectedReviewerData = () => {
    if (selectedReviewer === 'all') {
      return performance;
    }
    
    return performance.filter(p => p.reviewerId === selectedReviewer);
  };
  
  // 准备图表数据
  const prepareChartData = () => {
    const selectedData = getSelectedReviewerData();
    
    if (!selectedData || selectedData.length === 0) return null;
    
    // 合并所有审核员的数据（如果选择了"全部"）
    const mergeData = (key: 'byType' | 'byAction' | 'bySeverity') => {
      const merged: Record<string, number> = {};
      
      selectedData.forEach(reviewer => {
        reviewer[key].forEach(item => {
          const itemKey = Object.keys(item).find(k => k !== 'count') || '';
          const itemValue = item[itemKey as keyof typeof item] as string;
          
          if (!merged[itemValue]) {
            merged[itemValue] = 0;
          }
          
          merged[itemValue] += Number(item.count);
        });
      });
      
      return Object.entries(merged).map(([key, value]) => ({ [key.includes('Type') ? 'contentType' : key.includes('Action') ? 'suggestedAction' : 'severity']: key, count: value }));
    };
    
    // 内容类型分布
    const typeData = {
      labels: mergeData('byType').map(item => 
        item.contentType === 'story' ? '故事' : 
        item.contentType === 'questionnaire' ? '问卷' : 
        item.contentType === 'comment' ? '评论' : 
        item.contentType === 'profile' ? '个人资料' : 
        item.contentType === 'feedback' ? '反馈' : 
        item.contentType
      ),
      datasets: [
        {
          label: '审核数量',
          data: mergeData('byType').map(item => item.count),
          backgroundColor: [
            'rgba(59, 130, 246, 0.6)',
            'rgba(16, 185, 129, 0.6)',
            'rgba(249, 115, 22, 0.6)',
            'rgba(139, 92, 246, 0.6)',
            'rgba(236, 72, 153, 0.6)',
          ],
          borderColor: [
            'rgba(59, 130, 246, 1)',
            'rgba(16, 185, 129, 1)',
            'rgba(249, 115, 22, 1)',
            'rgba(139, 92, 246, 1)',
            'rgba(236, 72, 153, 1)',
          ],
          borderWidth: 1,
        },
      ],
    };
    
    // 建议操作分布
    const actionData = {
      labels: mergeData('byAction').map(item => 
        item.suggestedAction === 'approve' ? '通过' : 
        item.suggestedAction === 'reject' ? '拒绝' : 
        item.suggestedAction === 'review' ? '人工审核' : 
        item.suggestedAction
      ),
      datasets: [
        {
          data: mergeData('byAction').map(item => item.count),
          backgroundColor: [
            'rgba(34, 197, 94, 0.6)',
            'rgba(239, 68, 68, 0.6)',
            'rgba(234, 179, 8, 0.6)',
          ],
          borderColor: [
            'rgba(34, 197, 94, 1)',
            'rgba(239, 68, 68, 1)',
            'rgba(234, 179, 8, 1)',
          ],
          borderWidth: 1,
        },
      ],
    };
    
    // 严重程度分布
    const severityData = {
      labels: mergeData('bySeverity').map(item => 
        item.severity === 'low' ? '低' : 
        item.severity === 'medium' ? '中' : 
        item.severity === 'high' ? '高' : 
        item.severity
      ),
      datasets: [
        {
          data: mergeData('bySeverity').map(item => item.count),
          backgroundColor: [
            'rgba(234, 179, 8, 0.6)',
            'rgba(249, 115, 22, 0.6)',
            'rgba(239, 68, 68, 0.6)',
          ],
          borderColor: [
            'rgba(234, 179, 8, 1)',
            'rgba(249, 115, 22, 1)',
            'rgba(239, 68, 68, 1)',
          ],
          borderWidth: 1,
        },
      ],
    };
    
    // 质量评分比较
    const qualityData = {
      labels: selectedData.map(reviewer => reviewer.reviewerId),
      datasets: [
        {
          label: '数据质量评分',
          data: selectedData.map(reviewer => reviewer.qualityStats.avgDataQuality || 0),
          backgroundColor: 'rgba(59, 130, 246, 0.6)',
          borderColor: 'rgba(59, 130, 246, 1)',
          borderWidth: 1,
        },
        {
          label: '建设性价值评分',
          data: selectedData.map(reviewer => reviewer.qualityStats.avgConstructiveValue || 0),
          backgroundColor: 'rgba(16, 185, 129, 0.6)',
          borderColor: 'rgba(16, 185, 129, 1)',
          borderWidth: 1,
        },
        {
          label: '故事价值评分',
          data: selectedData.map(reviewer => reviewer.qualityStats.avgStoryValue || 0),
          backgroundColor: 'rgba(249, 115, 22, 0.6)',
          borderColor: 'rgba(249, 115, 22, 1)',
          borderWidth: 1,
        },
      ],
    };
    
    return {
      typeData,
      actionData,
      severityData,
      qualityData
    };
  };
  
  // 图表选项
  const chartOptions = {
    responsive: true,
    maintainAspectRatio: false,
    plugins: {
      legend: {
        position: 'bottom' as const,
      },
    },
  };
  
  // 准备图表数据
  const chartData = prepareChartData();
  
  return (
    <Card className={className}>
      <CardHeader className="flex flex-row items-center justify-between">
        <div>
          <CardTitle>审核员绩效统计</CardTitle>
          <CardDescription>
            审核员工作量和质量分析
          </CardDescription>
        </div>
        <Button variant="outline" size="sm" onClick={loadPerformance} disabled={isLoading}>
          <RefreshCw className={`w-4 h-4 mr-2 ${isLoading ? 'animate-spin' : ''}`} />
          刷新
        </Button>
      </CardHeader>
      <CardContent>
        {/* 筛选条件 */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
          <div>
            <label className="text-sm font-medium mb-1 block">审核员</label>
            <Select value={selectedReviewer} onValueChange={setSelectedReviewer}>
              <SelectTrigger>
                <SelectValue placeholder="选择审核员" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">全部审核员</SelectItem>
                {performance.map(reviewer => (
                  <SelectItem key={reviewer.reviewerId} value={reviewer.reviewerId}>
                    {reviewer.reviewerId}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>
          
          <div>
            <label className="text-sm font-medium mb-1 block">开始日期</label>
            <DatePicker
              date={startDate}
              setDate={setStartDate}
              placeholder="选择开始日期"
            />
          </div>
          
          <div>
            <label className="text-sm font-medium mb-1 block">结束日期</label>
            <DatePicker
              date={endDate}
              setDate={setEndDate}
              placeholder="选择结束日期"
            />
          </div>
          
          <div className="flex items-end gap-2">
            <Button onClick={handleFilter} className="flex-1">
              <Filter className="w-4 h-4 mr-2" />
              筛选
            </Button>
            <Button variant="outline" onClick={handleResetFilter}>
              重置
            </Button>
          </div>
        </div>
        
        {isLoading ? (
          <div className="space-y-4">
            <Skeleton className="h-[400px] w-full" />
          </div>
        ) : performance.length === 0 ? (
          <div className="text-center py-8">
            <p className="text-muted-foreground">暂无审核员绩效数据</p>
          </div>
        ) : (
          <>
            {/* 审核员绩效概览表格 */}
            <div className="mb-6">
              <h3 className="text-lg font-medium mb-3">审核员绩效概览</h3>
              <div className="border rounded-md">
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>审核员ID</TableHead>
                      <TableHead>审核总数</TableHead>
                      <TableHead>数据质量评分</TableHead>
                      <TableHead>建设性价值评分</TableHead>
                      <TableHead>故事价值评分</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {getSelectedReviewerData().map(reviewer => (
                      <TableRow key={reviewer.reviewerId}>
                        <TableCell className="font-medium">{reviewer.reviewerId}</TableCell>
                        <TableCell>{reviewer.totalCount}</TableCell>
                        <TableCell>
                          {reviewer.qualityStats.avgDataQuality 
                            ? reviewer.qualityStats.avgDataQuality.toFixed(2) 
                            : '无数据'}
                        </TableCell>
                        <TableCell>
                          {reviewer.qualityStats.avgConstructiveValue 
                            ? reviewer.qualityStats.avgConstructiveValue.toFixed(2) 
                            : '无数据'}
                        </TableCell>
                        <TableCell>
                          {reviewer.qualityStats.avgStoryValue 
                            ? reviewer.qualityStats.avgStoryValue.toFixed(2) 
                            : '无数据'}
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </div>
            </div>
            
            {/* 图表 */}
            <Tabs defaultValue="overview" className="w-full">
              <TabsList className="grid w-full grid-cols-4">
                <TabsTrigger value="overview">审核分布</TabsTrigger>
                <TabsTrigger value="content-type">内容类型</TabsTrigger>
                <TabsTrigger value="action">建议操作</TabsTrigger>
                <TabsTrigger value="quality">质量评分</TabsTrigger>
              </TabsList>
              
              <TabsContent value="overview" className="mt-4">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <Card>
                    <CardHeader>
                      <CardTitle className="text-base">内容类型分布</CardTitle>
                    </CardHeader>
                    <CardContent>
                      <div className="h-[300px]">
                        {chartData && <Pie data={chartData.typeData} options={chartOptions} />}
                      </div>
                    </CardContent>
                  </Card>
                  
                  <Card>
                    <CardHeader>
                      <CardTitle className="text-base">建议操作分布</CardTitle>
                    </CardHeader>
                    <CardContent>
                      <div className="h-[300px]">
                        {chartData && <Pie data={chartData.actionData} options={chartOptions} />}
                      </div>
                    </CardContent>
                  </Card>
                </div>
              </TabsContent>
              
              <TabsContent value="content-type" className="mt-4">
                <Card>
                  <CardHeader>
                    <CardTitle>按内容类型统计</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="h-[400px]">
                      {chartData && <Bar data={chartData.typeData} options={chartOptions} />}
                    </div>
                  </CardContent>
                </Card>
              </TabsContent>
              
              <TabsContent value="action" className="mt-4">
                <Card>
                  <CardHeader>
                    <CardTitle>按建议操作统计</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="h-[400px]">
                      {chartData && <Pie data={chartData.actionData} options={chartOptions} />}
                    </div>
                  </CardContent>
                </Card>
              </TabsContent>
              
              <TabsContent value="quality" className="mt-4">
                <Card>
                  <CardHeader>
                    <CardTitle>质量评分比较</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="h-[400px]">
                      {chartData && <Bar data={chartData.qualityData} options={chartOptions} />}
                    </div>
                  </CardContent>
                </Card>
              </TabsContent>
            </Tabs>
          </>
        )}
      </CardContent>
    </Card>
  );
};

export default ReviewerPerformanceDashboard;
