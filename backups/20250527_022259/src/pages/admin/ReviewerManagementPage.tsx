import React, { useState, useEffect } from 'react';
import AdminLayout from '@/components/layouts/AdminLayout';
import { Card, CardContent, CardHeader, CardTitle, CardDescription, CardFooter } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { Label } from '@/components/ui/label';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Checkbox } from '@/components/ui/checkbox';
import { Badge } from '@/components/ui/badge';
import { useToast } from '@/components/ui/use-toast';
import {
  Search, RefreshCw, UserCheck, UserX, Trash2,
  Eye, FileText, UserPlus as UserPlusIcon, Key, Clock, Activity
} from 'lucide-react';
import {
  Table, TableBody, TableCaption, TableCell, TableHead,
  TableHeader, TableRow
} from '@/components/ui/table';
import {
  Pagination, PaginationContent, PaginationItem,
  PaginationLink, PaginationNext, PaginationPrevious
} from '@/components/ui/pagination-new';
import {
  Dialog, DialogContent, DialogDescription, DialogFooter,
  DialogHeader, DialogTitle, DialogTrigger
} from '@/components/ui/dialog';
import {
  DropdownMenu, DropdownMenuContent, DropdownMenuItem,
  DropdownMenuLabel, DropdownMenuSeparator, DropdownMenuTrigger
} from '@/components/ui/dropdown-menu';
import {
  Reviewer, ReviewRecord, ReviewerSearchParams,
  getReviewers, getReviewerDetails, getReviewerRecords,
  createReviewer, suspendReviewer, deleteReviewer, resetReviewerPassword,
  CreateReviewerParams, SuspendReviewerParams
} from '@/services/reviewerManagementService';

const ReviewerManagementPage: React.FC = () => {
  const { toast } = useToast();
  const [activeTab, setActiveTab] = useState('list');
  const [isLoading, setIsLoading] = useState(false);
  const [searchParams, setSearchParams] = useState<ReviewerSearchParams>({
    page: 1,
    limit: 10
  });
  const [reviewers, setReviewers] = useState<Reviewer[]>([]);
  const [totalReviewers, setTotalReviewers] = useState(0);
  const [totalPages, setTotalPages] = useState(1);
  const [selectedReviewer, setSelectedReviewer] = useState<Reviewer | null>(null);
  const [reviewerRecords, setReviewerRecords] = useState<ReviewRecord[]>([]);
  const [isReviewerRecordsLoading, setIsReviewerRecordsLoading] = useState(false);
  const [isDetailsDialogOpen, setIsDetailsDialogOpen] = useState(false);
  const [isCreateDialogOpen, setIsCreateDialogOpen] = useState(false);
  const [isSuspendDialogOpen, setIsSuspendDialogOpen] = useState(false);
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);
  const [isResetPasswordDialogOpen, setIsResetPasswordDialogOpen] = useState(false);
  const [newPassword, setNewPassword] = useState('');

  // 创建审核员表单状态
  const [createForm, setCreateForm] = useState<CreateReviewerParams>({
    email: '',
    username: '',
    name: '',
    password: ''
  });

  // 停用审核员表单状态
  const [suspendForm, setSuspendForm] = useState<SuspendReviewerParams>({
    id: '',
    duration: '1day',
    customDays: 1,
    reason: ''
  });

  // 加载审核员数据
  useEffect(() => {
    fetchReviewers();
  }, [searchParams]);

  // 获取审核员列表
  const fetchReviewers = async () => {
    try {
      setIsLoading(true);
      const result = await getReviewers(searchParams);

      if (result.success) {
        setReviewers(result.data.reviewers);
        setTotalReviewers(result.data.total);
        setTotalPages(result.data.totalPages);
      } else {
        toast({
          title: '获取审核员失败',
          description: '无法加载审核员列表，请稍后重试',
          variant: 'destructive',
        });
      }
    } catch (error) {
      console.error('获取审核员失败:', error);
      toast({
        title: '获取审核员失败',
        description: '发生错误，请稍后重试',
        variant: 'destructive',
      });
    } finally {
      setIsLoading(false);
    }
  };

  // 获取审核员详情和审核记录
  const fetchReviewerDetails = async (id: string) => {
    try {
      const result = await getReviewerDetails(id);

      if (result.success) {
        setSelectedReviewer(result.data.reviewer);
        fetchReviewerRecords(id);
      } else {
        toast({
          title: '获取审核员详情失败',
          description: '无法加载审核员详情，请稍后重试',
          variant: 'destructive',
        });
      }
    } catch (error) {
      console.error('获取审核员详情失败:', error);
      toast({
        title: '获取审核员详情失败',
        description: '发生错误，请稍后重试',
        variant: 'destructive',
      });
    }
  };

  // 获取审核员审核记录
  const fetchReviewerRecords = async (id: string) => {
    try {
      setIsReviewerRecordsLoading(true);
      const result = await getReviewerRecords(id);

      if (result.success) {
        setReviewerRecords(result.data.records);
      } else {
        toast({
          title: '获取审核记录失败',
          description: '无法加载审核记录，请稍后重试',
          variant: 'destructive',
        });
      }
    } catch (error) {
      console.error('获取审核记录失败:', error);
      toast({
        title: '获取审核记录失败',
        description: '发生错误，请稍后重试',
        variant: 'destructive',
      });
    } finally {
      setIsReviewerRecordsLoading(false);
    }
  };

  // 处理搜索参数变更
  const handleSearchParamChange = (key: keyof ReviewerSearchParams, value: any) => {
    setSearchParams(prev => ({ ...prev, [key]: value, page: 1 }));
  };

  // 处理页码变更
  const handlePageChange = (page: number) => {
    setSearchParams(prev => ({ ...prev, page }));
  };

  // 查看审核员详情
  const handleViewReviewer = (reviewer: Reviewer) => {
    setSelectedReviewer(reviewer);
    fetchReviewerRecords(reviewer.id);
    setIsDetailsDialogOpen(true);
  };

  // 处理创建审核员表单变更
  const handleCreateFormChange = (key: keyof CreateReviewerParams, value: string) => {
    setCreateForm(prev => ({ ...prev, [key]: value }));
  };

  // 处理停用审核员表单变更
  const handleSuspendFormChange = (key: keyof SuspendReviewerParams, value: any) => {
    setSuspendForm(prev => ({ ...prev, [key]: value }));
  };

  // 创建审核员
  const handleCreateReviewer = async () => {
    try {
      // 表单验证
      if (!createForm.email || !createForm.username || !createForm.name || !createForm.password) {
        toast({
          title: '表单不完整',
          description: '请填写所有必填字段',
          variant: 'destructive',
        });
        return;
      }

      const result = await createReviewer(createForm);

      if (result.success) {
        toast({
          title: '创建成功',
          description: result.message || '审核员创建成功',
        });

        // 重置表单并刷新列表
        setCreateForm({
          email: '',
          username: '',
          name: '',
          password: ''
        });
        setIsCreateDialogOpen(false);
        fetchReviewers();
      } else {
        toast({
          title: '创建失败',
          description: result.error || '无法创建审核员，请稍后重试',
          variant: 'destructive',
        });
      }
    } catch (error) {
      console.error('创建审核员失败:', error);
      toast({
        title: '创建失败',
        description: '发生错误，请稍后重试',
        variant: 'destructive',
      });
    }
  };

  // 停用审核员
  const handleSuspendReviewer = async () => {
    try {
      const result = await suspendReviewer(suspendForm);

      if (result.success) {
        toast({
          title: '停用成功',
          description: result.message || '审核员已停用',
        });

        // 重置表单并刷新列表
        setSuspendForm({
          id: '',
          duration: '1day',
          customDays: 1,
          reason: ''
        });
        setIsSuspendDialogOpen(false);
        fetchReviewers();
      } else {
        toast({
          title: '停用失败',
          description: result.error || '无法停用审核员，请稍后重试',
          variant: 'destructive',
        });
      }
    } catch (error) {
      console.error('停用审核员失败:', error);
      toast({
        title: '停用失败',
        description: '发生错误，请稍后重试',
        variant: 'destructive',
      });
    }
  };

  // 删除审核员
  const handleDeleteReviewer = async (id: string) => {
    try {
      const result = await deleteReviewer(id);

      if (result.success) {
        toast({
          title: '删除成功',
          description: result.message || '审核员已删除',
        });

        setIsDeleteDialogOpen(false);
        fetchReviewers();
      } else {
        toast({
          title: '删除失败',
          description: result.error || '无法删除审核员，请稍后重试',
          variant: 'destructive',
        });
      }
    } catch (error) {
      console.error('删除审核员失败:', error);
      toast({
        title: '删除失败',
        description: '发生错误，请稍后重试',
        variant: 'destructive',
      });
    }
  };

  // 重置审核员密码
  const handleResetPassword = async (id: string) => {
    try {
      const result = await resetReviewerPassword(id);

      if (result.success) {
        setNewPassword(result.data.newPassword);
        toast({
          title: '重置成功',
          description: result.message || '密码已重置',
        });
      } else {
        toast({
          title: '重置失败',
          description: result.error || '无法重置密码，请稍后重试',
          variant: 'destructive',
        });
      }
    } catch (error) {
      console.error('重置密码失败:', error);
      toast({
        title: '重置失败',
        description: '发生错误，请稍后重试',
        variant: 'destructive',
      });
    }
  };

  // 重置搜索
  const resetSearch = () => {
    setSearchParams({
      page: 1,
      limit: 10
    });
  };

  return (
    <AdminLayout>
      <div className="container mx-auto py-6">
        <h1 className="text-2xl font-bold mb-6">审核员管理</h1>

        <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-4">
          <TabsList>
            <TabsTrigger value="list">
              <Search className="mr-2 h-4 w-4" />
              审核员列表
            </TabsTrigger>
            <TabsTrigger value="create">
              <UserPlusIcon className="mr-2 h-4 w-4" />
              添加审核员
            </TabsTrigger>
          </TabsList>

          <TabsContent value="list" className="space-y-4">
            {/* 搜索表单 */}
            <Card>
              <CardHeader>
                <CardTitle>搜索审核员</CardTitle>
                <CardDescription>
                  通过邮箱或用户名搜索审核员
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="email">邮箱</Label>
                    <Input
                      id="email"
                      placeholder="输入审核员邮箱"
                      value={searchParams.email || ''}
                      onChange={(e) => handleSearchParamChange('email', e.target.value)}
                    />
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="username">用户名</Label>
                    <Input
                      id="username"
                      placeholder="输入审核员用户名"
                      value={searchParams.username || ''}
                      onChange={(e) => handleSearchParamChange('username', e.target.value)}
                    />
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="status">状态</Label>
                    <Select
                      value={searchParams.status || ''}
                      onValueChange={(value) => handleSearchParamChange('status', value)}
                    >
                      <SelectTrigger id="status">
                        <SelectValue placeholder="选择状态" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="">全部状态</SelectItem>
                        <SelectItem value="active">活跃</SelectItem>
                        <SelectItem value="inactive">未激活</SelectItem>
                        <SelectItem value="suspended">已停用</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                </div>
              </CardContent>
              <CardFooter className="flex justify-between">
                <Button variant="outline" onClick={resetSearch}>
                  <RefreshCw className="mr-2 h-4 w-4" />
                  重置
                </Button>
                <Button onClick={() => fetchReviewers()}>
                  <Search className="mr-2 h-4 w-4" />
                  搜索
                </Button>
              </CardFooter>
            </Card>

            {/* 审核员列表 */}
            <Card>
              <CardHeader>
                <CardTitle>审核员列表</CardTitle>
                <CardDescription>
                  共找到 {totalReviewers} 个审核员
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="rounded-md border">
                  <Table>
                    <TableHeader>
                      <TableRow>
                        <TableHead>用户名</TableHead>
                        <TableHead>姓名</TableHead>
                        <TableHead>邮箱</TableHead>
                        <TableHead>状态</TableHead>
                        <TableHead>最后登录</TableHead>
                        <TableHead>IP地址</TableHead>
                        <TableHead>审核数量</TableHead>
                        <TableHead className="text-right">操作</TableHead>
                      </TableRow>
                    </TableHeader>
                    <TableBody>
                      {isLoading ? (
                        <TableRow>
                          <TableCell colSpan={8} className="text-center py-8">
                            <div className="flex justify-center items-center">
                              <RefreshCw className="h-6 w-6 animate-spin mr-2" />
                              <span>加载中...</span>
                            </div>
                          </TableCell>
                        </TableRow>
                      ) : reviewers.length === 0 ? (
                        <TableRow>
                          <TableCell colSpan={8} className="text-center py-8">
                            未找到匹配的审核员
                          </TableCell>
                        </TableRow>
                      ) : (
                        reviewers.map((reviewer) => (
                          <TableRow key={reviewer.id}>
                            <TableCell>{reviewer.username}</TableCell>
                            <TableCell>{reviewer.name}</TableCell>
                            <TableCell>{reviewer.email}</TableCell>
                            <TableCell>
                              <Badge variant={
                                reviewer.status === 'active' ? 'success' :
                                reviewer.status === 'inactive' ? 'warning' : 'destructive'
                              }>
                                {reviewer.status === 'active' ? '活跃' :
                                 reviewer.status === 'inactive' ? '未激活' : '已停用'}
                                {reviewer.suspendedUntil && ` (至 ${new Date(reviewer.suspendedUntil).toLocaleDateString()})`}
                              </Badge>
                            </TableCell>
                            <TableCell>
                              {reviewer.lastLoginAt ? new Date(reviewer.lastLoginAt).toLocaleString() : '未登录'}
                            </TableCell>
                            <TableCell>
                              {reviewer.lastLoginIp || '-'}
                            </TableCell>
                            <TableCell>
                              <div className="flex items-center space-x-1">
                                <span>{reviewer.reviewCount}</span>
                                <span className="text-xs text-muted-foreground">
                                  ({reviewer.approvedCount}/{reviewer.rejectedCount}/{reviewer.pendingCount})
                                </span>
                              </div>
                            </TableCell>
                            <TableCell className="text-right">
                              <DropdownMenu>
                                <DropdownMenuTrigger asChild>
                                  <Button variant="ghost" size="icon">
                                    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="h-4 w-4">
                                      <circle cx="12" cy="12" r="1" />
                                      <circle cx="12" cy="5" r="1" />
                                      <circle cx="12" cy="19" r="1" />
                                    </svg>
                                  </Button>
                                </DropdownMenuTrigger>
                                <DropdownMenuContent align="end">
                                  <DropdownMenuLabel>审核员操作</DropdownMenuLabel>
                                  <DropdownMenuItem onClick={() => handleViewReviewer(reviewer)}>
                                    <Eye className="mr-2 h-4 w-4" />
                                    查看详情
                                  </DropdownMenuItem>
                                  <DropdownMenuSeparator />
                                  {reviewer.status !== 'suspended' && (
                                    <DropdownMenuItem onClick={() => {
                                      setSuspendForm(prev => ({ ...prev, id: reviewer.id }));
                                      setIsSuspendDialogOpen(true);
                                    }}>
                                      <Clock className="mr-2 h-4 w-4" />
                                      停用账号
                                    </DropdownMenuItem>
                                  )}
                                  <DropdownMenuItem onClick={() => {
                                    setSelectedReviewer(reviewer);
                                    setIsResetPasswordDialogOpen(true);
                                  }}>
                                    <Key className="mr-2 h-4 w-4" />
                                    重置密码
                                  </DropdownMenuItem>
                                  <DropdownMenuSeparator />
                                  <DropdownMenuItem
                                    onClick={() => {
                                      setSelectedReviewer(reviewer);
                                      setIsDeleteDialogOpen(true);
                                    }}
                                    className="text-red-600"
                                  >
                                    <Trash2 className="mr-2 h-4 w-4" />
                                    删除审核员
                                  </DropdownMenuItem>
                                </DropdownMenuContent>
                              </DropdownMenu>
                            </TableCell>
                          </TableRow>
                        ))
                      )}
                    </TableBody>
                  </Table>
                </div>

                {/* 分页 */}
                {totalPages > 1 && (
                  <div className="mt-4 flex justify-center">
                    <Pagination>
                      <PaginationContent>
                        <PaginationItem>
                          <PaginationPrevious
                            onClick={() => handlePageChange(Math.max(1, searchParams.page! - 1))}
                            disabled={searchParams.page === 1}
                          />
                        </PaginationItem>

                        {Array.from({ length: totalPages }, (_, i) => i + 1).map((page) => (
                          <PaginationItem key={page}>
                            <PaginationLink
                              onClick={() => handlePageChange(page)}
                              isActive={page === searchParams.page}
                            >
                              {page}
                            </PaginationLink>
                          </PaginationItem>
                        ))}

                        <PaginationItem>
                          <PaginationNext
                            onClick={() => handlePageChange(Math.min(totalPages, searchParams.page! + 1))}
                            disabled={searchParams.page === totalPages}
                          />
                        </PaginationItem>
                      </PaginationContent>
                    </Pagination>
                  </div>
                )}
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="create" className="space-y-4">
            <Card>
              <CardHeader>
                <CardTitle>添加新审核员</CardTitle>
                <CardDescription>
                  创建新的审核员账号
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="create-email">邮箱 <span className="text-red-500">*</span></Label>
                    <Input
                      id="create-email"
                      placeholder="输入审核员邮箱"
                      value={createForm.email}
                      onChange={(e) => handleCreateFormChange('email', e.target.value)}
                    />
                    <p className="text-xs text-muted-foreground">
                      审核员将使用此邮箱登录系统
                    </p>
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="create-username">用户名 <span className="text-red-500">*</span></Label>
                    <Input
                      id="create-username"
                      placeholder="输入审核员用户名"
                      value={createForm.username}
                      onChange={(e) => handleCreateFormChange('username', e.target.value)}
                    />
                    <p className="text-xs text-muted-foreground">
                      用户名应由字母、数字和下划线组成
                    </p>
                  </div>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="create-name">姓名 <span className="text-red-500">*</span></Label>
                    <Input
                      id="create-name"
                      placeholder="输入审核员姓名"
                      value={createForm.name}
                      onChange={(e) => handleCreateFormChange('name', e.target.value)}
                    />
                    <p className="text-xs text-muted-foreground">
                      审核员的真实姓名或昵称
                    </p>
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="create-password">密码 <span className="text-red-500">*</span></Label>
                    <Input
                      id="create-password"
                      type="password"
                      placeholder="输入初始密码"
                      value={createForm.password}
                      onChange={(e) => handleCreateFormChange('password', e.target.value)}
                    />
                    <p className="text-xs text-muted-foreground">
                      密码长度至少为8位，包含字母和数字
                    </p>
                  </div>
                </div>

                <div className="bg-muted p-4 rounded-md">
                  <h4 className="text-sm font-medium mb-2">审核员权限说明</h4>
                  <ul className="text-sm text-muted-foreground space-y-1">
                    <li>• 审核员可以审核用户提交的内容，包括故事、问卷和评论</li>
                    <li>• 审核员可以批准或拒绝内容，并添加标签和评论</li>
                    <li>• 审核员无法修改系统设置或管理其他审核员</li>
                    <li>• 审核员的所有操作都会被记录，管理员可以查看</li>
                  </ul>
                </div>
              </CardContent>
              <CardFooter className="flex justify-between">
                <Button variant="outline" onClick={() => {
                  setCreateForm({
                    email: '',
                    username: '',
                    name: '',
                    password: ''
                  });
                }}>
                  重置
                </Button>
                <Button onClick={handleCreateReviewer}>
                  <UserPlusIcon className="mr-2 h-4 w-4" />
                  创建审核员
                </Button>
              </CardFooter>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>批量导入审核员</CardTitle>
                <CardDescription>
                  通过CSV文件批量导入审核员
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="bg-muted p-4 rounded-md text-center">
                  <p className="text-muted-foreground">批量导入功能正在开发中...</p>
                </div>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>

        {/* 审核员详情对话框 */}
        <Dialog open={isDetailsDialogOpen} onOpenChange={setIsDetailsDialogOpen}>
          <DialogContent className="max-w-4xl max-h-[80vh] overflow-y-auto">
            {selectedReviewer && (
              <>
                <DialogHeader>
                  <DialogTitle>审核员详情</DialogTitle>
                  <DialogDescription>
                    查看审核员信息和审核记录
                  </DialogDescription>
                </DialogHeader>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4 py-4">
                  <div>
                    <h3 className="text-lg font-medium mb-2">基本信息</h3>
                    <div className="space-y-2">
                      <div className="grid grid-cols-3 gap-2">
                        <span className="font-medium">用户名:</span>
                        <span className="col-span-2">{selectedReviewer.username}</span>
                      </div>
                      <div className="grid grid-cols-3 gap-2">
                        <span className="font-medium">姓名:</span>
                        <span className="col-span-2">{selectedReviewer.name}</span>
                      </div>
                      <div className="grid grid-cols-3 gap-2">
                        <span className="font-medium">邮箱:</span>
                        <span className="col-span-2">{selectedReviewer.email}</span>
                      </div>
                      <div className="grid grid-cols-3 gap-2">
                        <span className="font-medium">状态:</span>
                        <span className="col-span-2">
                          <Badge variant={
                            selectedReviewer.status === 'active' ? 'success' :
                            selectedReviewer.status === 'inactive' ? 'warning' : 'destructive'
                          }>
                            {selectedReviewer.status === 'active' ? '活跃' :
                             selectedReviewer.status === 'inactive' ? '未激活' : '已停用'}
                            {selectedReviewer.suspendedUntil && ` (至 ${new Date(selectedReviewer.suspendedUntil).toLocaleDateString()})`}
                          </Badge>
                        </span>
                      </div>
                      <div className="grid grid-cols-3 gap-2">
                        <span className="font-medium">创建时间:</span>
                        <span className="col-span-2">{new Date(selectedReviewer.createdAt).toLocaleString()}</span>
                      </div>
                      <div className="grid grid-cols-3 gap-2">
                        <span className="font-medium">最后登录:</span>
                        <span className="col-span-2">{selectedReviewer.lastLoginAt ? new Date(selectedReviewer.lastLoginAt).toLocaleString() : '未登录'}</span>
                      </div>
                      <div className="grid grid-cols-3 gap-2">
                        <span className="font-medium">IP地址:</span>
                        <span className="col-span-2">{selectedReviewer.lastLoginIp || '-'}</span>
                      </div>
                    </div>
                  </div>

                  <div>
                    <h3 className="text-lg font-medium mb-2">审核统计</h3>
                    <div className="space-y-2">
                      <div className="grid grid-cols-3 gap-2">
                        <span className="font-medium">总审核数:</span>
                        <span className="col-span-2">{selectedReviewer.reviewCount}</span>
                      </div>
                      <div className="grid grid-cols-3 gap-2">
                        <span className="font-medium">已批准:</span>
                        <span className="col-span-2">
                          {selectedReviewer.approvedCount}
                          <span className="text-xs text-muted-foreground ml-1">
                            ({Math.round(selectedReviewer.approvedCount / (selectedReviewer.reviewCount || 1) * 100)}%)
                          </span>
                        </span>
                      </div>
                      <div className="grid grid-cols-3 gap-2">
                        <span className="font-medium">已拒绝:</span>
                        <span className="col-span-2">
                          {selectedReviewer.rejectedCount}
                          <span className="text-xs text-muted-foreground ml-1">
                            ({Math.round(selectedReviewer.rejectedCount / (selectedReviewer.reviewCount || 1) * 100)}%)
                          </span>
                        </span>
                      </div>
                      <div className="grid grid-cols-3 gap-2">
                        <span className="font-medium">待处理:</span>
                        <span className="col-span-2">{selectedReviewer.pendingCount}</span>
                      </div>
                    </div>

                    <div className="mt-4 space-y-2">
                      <h3 className="text-lg font-medium">审核员操作</h3>
                      <div className="flex flex-wrap gap-2">
                        {selectedReviewer.status !== 'suspended' && (
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => {
                              setSuspendForm(prev => ({ ...prev, id: selectedReviewer.id }));
                              setIsDetailsDialogOpen(false);
                              setIsSuspendDialogOpen(true);
                            }}
                          >
                            <Clock className="mr-2 h-4 w-4" />
                            停用账号
                          </Button>
                        )}
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => {
                            setIsDetailsDialogOpen(false);
                            setIsResetPasswordDialogOpen(true);
                          }}
                        >
                          <Key className="mr-2 h-4 w-4" />
                          重置密码
                        </Button>
                        <Button
                          variant="outline"
                          size="sm"
                          className="text-red-600"
                          onClick={() => {
                            setIsDetailsDialogOpen(false);
                            setIsDeleteDialogOpen(true);
                          }}
                        >
                          <Trash2 className="mr-2 h-4 w-4" />
                          删除审核员
                        </Button>
                      </div>
                    </div>
                  </div>
                </div>

                <div className="space-y-4 mt-4">
                  <h3 className="text-lg font-medium">审核记录</h3>

                  {isReviewerRecordsLoading ? (
                    <div className="flex justify-center items-center py-8">
                      <RefreshCw className="h-6 w-6 animate-spin mr-2" />
                      <span>加载中...</span>
                    </div>
                  ) : reviewerRecords.length === 0 ? (
                    <div className="text-center py-8 border rounded-md">
                      <p className="text-muted-foreground">该审核员暂无审核记录</p>
                    </div>
                  ) : (
                    <div className="rounded-md border">
                      <Table>
                        <TableHeader>
                          <TableRow>
                            <TableHead>内容类型</TableHead>
                            <TableHead>内容摘要</TableHead>
                            <TableHead>审核结果</TableHead>
                            <TableHead>审核时间</TableHead>
                            <TableHead>耗时</TableHead>
                          </TableRow>
                        </TableHeader>
                        <TableBody>
                          {reviewerRecords.map((record) => (
                            <TableRow key={record.id}>
                              <TableCell>
                                <Badge variant={
                                  record.contentType === 'story' ? 'default' :
                                  record.contentType === 'questionnaire' ? 'secondary' : 'outline'
                                }>
                                  {record.contentType === 'story' ? '故事' :
                                   record.contentType === 'questionnaire' ? '问卷' : '评论'}
                                </Badge>
                              </TableCell>
                              <TableCell>
                                <div className="max-w-xs truncate">
                                  {record.contentTitle ? record.contentTitle : record.contentSummary}
                                </div>
                                {record.tags && record.tags.length > 0 && (
                                  <div className="flex flex-wrap gap-1 mt-1">
                                    {record.tags.map((tag, index) => (
                                      <Badge key={index} variant="outline" className="text-xs">{tag}</Badge>
                                    ))}
                                  </div>
                                )}
                              </TableCell>
                              <TableCell>
                                <Badge variant={
                                  record.decision === 'approved' ? 'success' :
                                  record.decision === 'rejected' ? 'destructive' : 'warning'
                                }>
                                  {record.decision === 'approved' ? '已批准' :
                                   record.decision === 'rejected' ? '已拒绝' : '待处理'}
                                </Badge>
                              </TableCell>
                              <TableCell>
                                {new Date(record.reviewedAt).toLocaleString()}
                              </TableCell>
                              <TableCell>
                                {record.reviewDuration < 60 ?
                                  `${record.reviewDuration}秒` :
                                  `${Math.floor(record.reviewDuration / 60)}分${record.reviewDuration % 60}秒`}
                              </TableCell>
                            </TableRow>
                          ))}
                        </TableBody>
                      </Table>
                    </div>
                  )}
                </div>

                <DialogFooter>
                  <Button variant="outline" onClick={() => setIsDetailsDialogOpen(false)}>
                    关闭
                  </Button>
                </DialogFooter>
              </>
            )}
          </DialogContent>
        </Dialog>

        {/* 停用审核员对话框 */}
        <Dialog open={isSuspendDialogOpen} onOpenChange={setIsSuspendDialogOpen}>
          <DialogContent>
            <DialogHeader>
              <DialogTitle>停用审核员账号</DialogTitle>
              <DialogDescription>
                临时停用审核员账号，在指定时间后自动恢复
              </DialogDescription>
            </DialogHeader>

            <div className="py-4 space-y-4">
              <div className="space-y-2">
                <Label htmlFor="suspend-duration">停用时长</Label>
                <Select
                  value={suspendForm.duration}
                  onValueChange={(value) => handleSuspendFormChange('duration', value)}
                >
                  <SelectTrigger id="suspend-duration">
                    <SelectValue placeholder="选择停用时长" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="1day">1天</SelectItem>
                    <SelectItem value="1week">1周</SelectItem>
                    <SelectItem value="1month">1个月</SelectItem>
                    <SelectItem value="custom">自定义</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              {suspendForm.duration === 'custom' && (
                <div className="space-y-2">
                  <Label htmlFor="custom-days">自定义天数</Label>
                  <Input
                    id="custom-days"
                    type="number"
                    min="1"
                    max="365"
                    value={suspendForm.customDays}
                    onChange={(e) => handleSuspendFormChange('customDays', parseInt(e.target.value))}
                  />
                </div>
              )}

              <div className="space-y-2">
                <Label htmlFor="suspend-reason">停用原因（可选）</Label>
                <Input
                  id="suspend-reason"
                  placeholder="输入停用原因"
                  value={suspendForm.reason || ''}
                  onChange={(e) => handleSuspendFormChange('reason', e.target.value)}
                />
              </div>

              <div className="bg-yellow-50 border border-yellow-200 rounded-md p-3 text-sm text-yellow-800">
                <p>停用期间，审核员将无法登录系统或执行任何操作。停用期结束后，账号将自动恢复。</p>
              </div>
            </div>

            <DialogFooter>
              <Button variant="outline" onClick={() => setIsSuspendDialogOpen(false)}>
                取消
              </Button>
              <Button
                variant="default"
                onClick={() => handleSuspendReviewer()}
              >
                确认停用
              </Button>
            </DialogFooter>
          </DialogContent>
        </Dialog>

        {/* 删除审核员确认对话框 */}
        <Dialog open={isDeleteDialogOpen} onOpenChange={setIsDeleteDialogOpen}>
          <DialogContent>
            <DialogHeader>
              <DialogTitle>删除审核员</DialogTitle>
              <DialogDescription>
                此操作将永久删除审核员账号，无法恢复
              </DialogDescription>
            </DialogHeader>

            <div className="py-4">
              {selectedReviewer && (
                <>
                  <p>您确定要删除以下审核员吗？</p>
                  <div className="mt-2 p-3 bg-muted rounded-md">
                    <p><span className="font-medium">用户名:</span> {selectedReviewer.username}</p>
                    <p><span className="font-medium">姓名:</span> {selectedReviewer.name}</p>
                    <p><span className="font-medium">邮箱:</span> {selectedReviewer.email}</p>
                  </div>

                  <div className="mt-4 bg-red-50 border border-red-200 rounded-md p-3 text-sm text-red-800">
                    <p>警告：删除操作不可逆，审核员的所有审核记录将保留，但将无法再登录系统。</p>
                  </div>
                </>
              )}
            </div>

            <DialogFooter>
              <Button variant="outline" onClick={() => setIsDeleteDialogOpen(false)}>
                取消
              </Button>
              <Button
                variant="destructive"
                onClick={() => selectedReviewer && handleDeleteReviewer(selectedReviewer.id)}
              >
                确认删除
              </Button>
            </DialogFooter>
          </DialogContent>
        </Dialog>

        {/* 重置密码对话框 */}
        <Dialog open={isResetPasswordDialogOpen} onOpenChange={setIsResetPasswordDialogOpen}>
          <DialogContent>
            <DialogHeader>
              <DialogTitle>重置审核员密码</DialogTitle>
              <DialogDescription>
                为审核员生成新的随机密码
              </DialogDescription>
            </DialogHeader>

            <div className="py-4">
              {selectedReviewer && (
                <>
                  <p>您确定要重置以下审核员的密码吗？</p>
                  <div className="mt-2 p-3 bg-muted rounded-md">
                    <p><span className="font-medium">用户名:</span> {selectedReviewer.username}</p>
                    <p><span className="font-medium">姓名:</span> {selectedReviewer.name}</p>
                    <p><span className="font-medium">邮箱:</span> {selectedReviewer.email}</p>
                  </div>

                  {newPassword && (
                    <div className="mt-4 bg-green-50 border border-green-200 rounded-md p-3">
                      <p className="text-sm text-green-800 mb-1">新密码已生成：</p>
                      <div className="bg-white p-2 rounded border border-green-300 font-mono text-center">
                        {newPassword}
                      </div>
                      <p className="text-xs text-green-700 mt-2">
                        请将此密码安全地传达给审核员。审核员首次登录后应立即修改密码。
                      </p>
                    </div>
                  )}

                  {!newPassword && (
                    <div className="mt-4 bg-blue-50 border border-blue-200 rounded-md p-3 text-sm text-blue-800">
                      <p>重置密码后，系统将生成一个新的随机密码。请确保将新密码安全地传达给审核员。</p>
                    </div>
                  )}
                </>
              )}
            </div>

            <DialogFooter>
              <Button variant="outline" onClick={() => {
                setIsResetPasswordDialogOpen(false);
                setNewPassword('');
              }}>
                关闭
              </Button>
              {!newPassword && selectedReviewer && (
                <Button
                  variant="default"
                  onClick={() => handleResetPassword(selectedReviewer.id)}
                >
                  重置密码
                </Button>
              )}
            </DialogFooter>
          </DialogContent>
        </Dialog>
      </div>
    </AdminLayout>
  );
};

export default ReviewerManagementPage;
