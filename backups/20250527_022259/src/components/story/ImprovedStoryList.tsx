import { useState, useEffect, useRef, useMemo } from 'react';
import { useQuery } from '@tanstack/react-query';
import { useNavigate } from 'react-router-dom';
import ImprovedStoryCard from './ImprovedStoryCard';
import { Button } from '@/components/ui/button';
import { Skeleton } from '@/components/ui/skeleton';
import { getStories, GetStoriesOptions } from '@/lib/api';
import {
  ChevronLeft,
  ChevronRight,
  ChevronDown,
  AlertCircle,
  Loader2,
  RefreshCw,
  Filter,
  SlidersHorizontal
} from 'lucide-react';
import { cn } from '@/lib/utils';
import { motion, AnimatePresence } from 'framer-motion';
import { useInView } from 'react-intersection-observer';
import { useMediaQuery } from '@/hooks/useMediaQuery';

interface ImprovedStoryListProps {
  sortBy: 'latest' | 'popular';
  tag?: string;
  tags?: string[]; // 支持多标签
  options?: GetStoriesOptions;
  onPopularTagsLoaded?: (tags: Array<{ tag: string; count: number }>) => void;
  layout?: 'grid' | 'list'; // 布局模式
  compact?: boolean; // 紧凑模式
  className?: string;
  searchQuery?: string; // 搜索关键词
  infiniteScroll?: boolean; // 是否启用无限滚动
}

export default function ImprovedStoryList({
  sortBy,
  tag,
  tags = [],
  options,
  onPopularTagsLoaded,
  layout = 'grid',
  compact = false,
  className,
  searchQuery = '',
  infiniteScroll = true
}: ImprovedStoryListProps) {
  const navigate = useNavigate();
  const [page, setPage] = useState(1);
  const [allStories, setAllStories] = useState<any[]>([]);
  const [hasNextPage, setHasNextPage] = useState(true);
  const [isLoadingMore, setIsLoadingMore] = useState(false);
  const [totalPages, setTotalPages] = useState(1);
  const loadMoreRef = useRef<HTMLDivElement>(null);
  const { ref: inViewRef, inView } = useInView({
    threshold: 0.5,
    triggerOnce: false
  });

  // 检测是否为移动设备
  const isMobile = useMediaQuery('(max-width: 768px)');

  // 根据设备调整布局
  const effectiveLayout = isMobile ? 'grid' : layout;

  // 合并标签
  const allTags = tag ? [tag, ...tags] : tags;

  // 使用 useMemo 稳定 mergedOptions 对象，避免不必要的重新渲染
  const mergedOptions = useMemo(() => {
    const result = {
      ...options,
      tags: allTags.length > 0 ? allTags : undefined
    };
    console.log('🔧 mergedOptions 重新计算:', result);
    return result;
  }, [options, allTags.join(',')]);

  // 创建稳定的 options 字符串用于依赖项比较
  const optionsString = useMemo(() => {
    const result = JSON.stringify(options);
    console.log('🔧 optionsString 重新计算:', result);
    return result;
  }, [options]);

  // Reset page and stories when sort or tag or options change
  useEffect(() => {
    console.log('🔄 ImprovedStoryList: Reset effect triggered', {
      sortBy, tag, tags: tags.join(','), options, searchQuery,
      currentPage: page,
      currentStoriesCount: allStories.length
    });

    // 只有在真正需要重置时才重置
    if (page !== 1) {
      console.log('📄 重置页面到第1页');
      setPage(1);
    }

    if (allStories.length > 0) {
      console.log('🗑️ 清空故事列表');
      setAllStories([]);
    }

    setHasNextPage(true);
  }, [sortBy, tag, tags.join(','), optionsString, searchQuery]);

  // Fetch stories
  const queryKey = ['stories', page, sortBy, tag, tags.join(','), JSON.stringify(mergedOptions), searchQuery];
  console.log('ImprovedStoryList: Query key:', queryKey);

  const { data, isLoading, error, isFetching, refetch } = useQuery(
    queryKey,
    async () => {
      // 添加详细的调试信息
      console.log('ImprovedStoryList: Fetching stories with params:', {
        page,
        sortBy,
        tag,
        tags,
        mergedOptions,
        searchQuery
      });

      // 强制使用真实API，不再回退到模拟数据
      const result = await getStories(page, sortBy, tag, mergedOptions);
      console.log('ImprovedStoryList: API result:', result);
      return result;
    },
    {
      keepPreviousData: false, // 禁用保持之前的数据
      staleTime: 0, // 立即过期，强制重新获取
      cacheTime: 0, // 不缓存
    }
  );

  // 添加一个专门的effect来监控无限滚动时的状态变化
  useEffect(() => {
    if (page > 1) {
      console.log('🔍 页面变化监控 - 页面 > 1:', {
        page,
        allStoriesLength: allStories.length,
        hasNextPage,
        isFetching,
        isLoading
      });
    }
  }, [page, allStories.length, hasNextPage, isFetching, isLoading]);

  // 处理数据更新 - 替代 onSuccess 回调
  useEffect(() => {
    console.log('ImprovedStoryList: Data update effect triggered', { data, page });

    if (data) {
      console.log('ImprovedStoryList: Processing data:', data);

      // Pass popular tags to parent component if available
      if (data?.popularTags && onPopularTagsLoaded) {
        onPopularTagsLoaded(data.popularTags);
      }

      // Update all stories - 确保 stories 是数组
      const stories = data?.stories || [];
      console.log('ImprovedStoryList: Stories from data:', stories);
      console.log('ImprovedStoryList: Stories length:', stories.length);

      if (page === 1) {
        console.log('ImprovedStoryList: Setting all stories (page 1):', stories);
        setAllStories(stories);
      } else {
        console.log('ImprovedStoryList: Appending stories (page > 1):', {
          previousCount: allStories?.length || 0,
          newStoriesCount: stories.length,
          totalAfterAppend: (allStories?.length || 0) + stories.length
        });
        setAllStories(prev => {
          const prevStories = prev || [];
          const newStories = [...prevStories, ...stories];
          console.log('ImprovedStoryList: Stories after append:', {
            before: prevStories.length,
            added: stories.length,
            after: newStories.length
          });
          return newStories;
        });
      }

      // Update pagination state
      setTotalPages(data?.totalPages || 1);
      setHasNextPage(page < (data?.totalPages || 1));
    } else {
      console.log('ImprovedStoryList: No data received');
    }
  }, [data, page, onPopularTagsLoaded]);

  // 处理无限滚动
  useEffect(() => {
    if (infiniteScroll && inView && hasNextPage && !isFetching && !isLoading && !isLoadingMore) {
      console.log('🔍 无限滚动触发条件检查:', {
        infiniteScroll,
        inView,
        hasNextPage,
        isFetching,
        isLoading,
        isLoadingMore
      });
      loadMoreStories();
    }
  }, [inView, hasNextPage, isFetching, isLoading, infiniteScroll, isLoadingMore]);

  // 加载更多故事
  const loadMoreStories = async () => {
    if (isLoadingMore || !hasNextPage || isFetching) return;

    console.log('🔄 开始加载更多故事:', {
      currentPage: page,
      nextPage: page + 1,
      currentStoriesCount: allStories.length,
      hasNextPage,
      isLoadingMore,
      isFetching
    });

    setIsLoadingMore(true);

    // 直接设置下一页，React Query 会自动重新获取数据
    setPage(prev => {
      const nextPage = prev + 1;
      console.log('📄 设置页面:', prev, '->', nextPage);
      return nextPage;
    });

    setIsLoadingMore(false);
  };

  // Handle pagination
  const handlePreviousPage = () => {
    setPage(prev => Math.max(prev - 1, 1));
    // 滚动到顶部
    window.scrollTo({ top: 0, behavior: 'smooth' });
  };

  const handleNextPage = () => {
    if (page < (data?.totalPages || 1)) {
      setPage(prev => prev + 1);
      // 滚动到顶部
      window.scrollTo({ top: 0, behavior: 'smooth' });
    }
  };

  // 刷新数据
  const handleRefresh = () => {
    refetch();
  };

  // 渲染骨架屏
  const renderSkeletons = () => {
    return Array.from({ length: isMobile ? 2 : (compact ? 3 : 6) }).map((_, index) => (
      <div key={index} className="border rounded-lg p-5 space-y-4 h-[280px]">
        <Skeleton className="h-7 w-3/4" />
        <div className="flex gap-1.5">
          <Skeleton className="h-5 w-16 rounded-full" />
          <Skeleton className="h-5 w-16 rounded-full" />
        </div>
        <Skeleton className="h-4 w-full" />
        <Skeleton className="h-4 w-full" />
        <Skeleton className="h-4 w-2/3" />
        <div className="flex justify-between items-center pt-4">
          <div className="flex gap-2">
            <Skeleton className="h-4 w-20" />
            <Skeleton className="h-4 w-20" />
          </div>
          <div className="flex space-x-2">
            <Skeleton className="h-8 w-16 rounded-full" />
            <Skeleton className="h-8 w-16 rounded-full" />
          </div>
        </div>
      </div>
    ));
  };

  // Render loading state
  if (isLoading && page === 1) {
    return (
      <div className={cn(
        effectiveLayout === 'grid'
          ? "grid grid-cols-1 md:grid-cols-2 gap-6"
          : "space-y-6",
        className
      )}>
        {renderSkeletons()}
      </div>
    );
  }

  // Render error state
  if (error) {
    return (
      <motion.div
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        className="text-center py-12 border rounded-lg bg-red-50"
      >
        <AlertCircle className="h-12 w-12 text-red-500 mx-auto mb-4" />
        <h3 className="text-lg font-medium mb-2 text-red-700">加载故事失败</h3>
        <p className="text-gray-600 mb-4">
          发生错误，请稍后重试或联系管理员
        </p>
        <Button
          onClick={handleRefresh}
          variant="outline"
          className="flex items-center gap-2"
        >
          <RefreshCw className="h-4 w-4" />
          重试
        </Button>
      </motion.div>
    );
  }

  // Render empty state - 只有在第一页且没有累积数据时才显示空状态
  console.log('ImprovedStoryList: Render check - data:', data);
  console.log('ImprovedStoryList: Render check - data.stories length:', data?.stories?.length);
  console.log('ImprovedStoryList: Render check - allStories length:', allStories?.length);
  console.log('ImprovedStoryList: Render check - isLoading:', isLoading);
  console.log('ImprovedStoryList: Render check - error:', error);

  // 修复：只有在第一页且没有累积数据时才显示空状态
  if (data && (data.stories || []).length === 0 && page === 1 && allStories.length === 0) {
    console.log('ImprovedStoryList: Rendering empty state');
    return (
      <motion.div
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        className="text-center py-12 border rounded-lg bg-gray-50"
      >
        <h3 className="text-lg font-medium mb-2">暂无故事</h3>
        <p className="text-gray-500 mb-4">
          {allTags.length > 0
            ? '该标签下暂无故事，请尝试其他标签'
            : searchQuery
              ? `没有找到包含"${searchQuery}"的故事`
              : '暂无故事，成为第一个分享故事的人吧！'
          }
        </p>
        <div className="flex gap-3 justify-center">
          <Button onClick={() => navigate('/story-wall')}>
            返回故事墙
          </Button>
          <Button variant="outline" onClick={() => {}}>
            分享故事
          </Button>
        </div>
      </motion.div>
    );
  }

  // 计算网格列数
  const getGridCols = () => {
    if (isMobile) return "grid-cols-1";
    if (effectiveLayout === 'grid') {
      return compact ? "md:grid-cols-3" : "md:grid-cols-2";
    }
    return "";
  };

  console.log('ImprovedStoryList: Final render - allStories:', allStories);
  console.log('ImprovedStoryList: Final render - allStories length:', allStories?.length);

  return (
    <div className={className}>

      <div className={cn(
        effectiveLayout === 'grid'
          ? `grid grid-cols-1 ${getGridCols()} gap-6`
          : "space-y-6"
      )}>
        <AnimatePresence mode="popLayout">
          {(allStories || []).map((story, index) => (
            <motion.div
              key={story.id}
              layout
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              exit={{ opacity: 0, scale: 0.9 }}
              transition={{
                duration: 0.4,
                delay: index * 0.05,
                ease: [0.25, 0.1, 0.25, 1.0]
              }}
            >
              <ImprovedStoryCard
                story={story}
                onClick={() => navigate(`/story/${story.id}`)}
                searchQuery={searchQuery}
                compact={compact}
                featured={index === 0 && page === 1 && sortBy === 'popular'}
                className={effectiveLayout === 'list' ? "w-full" : ""}
                showAnimation={false} // 因为外层已经有动画了
              />
            </motion.div>
          ))}
        </AnimatePresence>
      </div>

      {/* 无限滚动加载指示器 */}
      {infiniteScroll && hasNextPage && (
        <div
          ref={inViewRef}
          className="flex justify-center my-8"
        >
          {isFetching && (
            <div className="flex flex-col items-center">
              <Loader2 className="h-8 w-8 animate-spin text-primary mb-2" />
              <p className="text-sm text-gray-500">加载更多故事...</p>
            </div>
          )}
          {!isFetching && (
            <Button
              variant="outline"
              onClick={loadMoreStories}
              className="flex items-center gap-2"
            >
              加载更多
              <ChevronDown className="h-4 w-4" />
            </Button>
          )}
        </div>
      )}

      {/* 传统分页 */}
      {!infiniteScroll && data && data.totalPages > 1 && (
        <div className="flex justify-center items-center gap-4 mt-8">
          <Button
            variant="outline"
            onClick={handlePreviousPage}
            disabled={page === 1 || isFetching}
            className="flex items-center gap-1"
            size={isMobile ? "sm" : "default"}
          >
            <ChevronLeft className="h-4 w-4" />
            {!isMobile && "上一页"}
          </Button>

          <span className="text-sm">
            {isMobile ? `${page}/${data.totalPages}` : `第 ${page} 页，共 ${data.totalPages} 页`}
          </span>

          <Button
            variant="outline"
            onClick={handleNextPage}
            disabled={page === data.totalPages || isFetching}
            className="flex items-center gap-1"
            size={isMobile ? "sm" : "default"}
          >
            {!isMobile && "下一页"}
            <ChevronRight className="h-4 w-4" />
          </Button>
        </div>
      )}

      {/* 移动端筛选按钮 */}
      {isMobile && (
        <div className="fixed bottom-6 right-6 z-10">
          <Button
            size="icon"
            className="h-12 w-12 rounded-full shadow-lg bg-primary hover:bg-primary/90"
            onClick={() => {
              // 打开筛选抽屉
              document.dispatchEvent(new CustomEvent('open-filter-drawer'));
            }}
          >
            <SlidersHorizontal className="h-5 w-5" />
          </Button>
        </div>
      )}
    </div>
  );
}
