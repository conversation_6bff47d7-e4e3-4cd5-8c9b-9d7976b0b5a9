import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { StatisticsData } from '../StatisticsPanel';
import { Separator } from '@/components/ui/separator';
import { Badge } from '@/components/ui/badge';
import { 
  TrendingUp, 
  TrendingDown, 
  Calendar, 
  Briefcase, 
  DollarSign, 
  Star 
} from 'lucide-react';

// 导入图表库
import { 
  LineChart, 
  Line, 
  XAxis, 
  YAxis, 
  CartesianGrid, 
  Tooltip, 
  Legend, 
  ResponsiveContainer,
  AreaChart,
  Area,
  ComposedChart,
  Bar,
  Brush
} from 'recharts';

interface TrendAnalysisProps {
  data: StatisticsData;
}

export default function TrendAnalysis({ data }: TrendAnalysisProps) {
  // 准备趋势数据
  const trendData = data.trends.timeLabels.map((label, index) => ({
    name: label,
    就业率: data.trends.employmentRate[index],
    平均薪资: data.trends.averageSalary[index],
    平均满意度: data.trends.averageSatisfaction[index],
  }));
  
  // 准备就业率趋势数据
  const employmentRateTrendData = data.trends.timeLabels.map((label, index) => ({
    name: label,
    就业率: data.trends.employmentRate[index],
  }));
  
  // 准备薪资趋势数据
  const salaryTrendData = data.trends.timeLabels.map((label, index) => ({
    name: label,
    平均薪资: data.trends.averageSalary[index],
  }));
  
  // 准备满意度趋势数据
  const satisfactionTrendData = data.trends.timeLabels.map((label, index) => ({
    name: label,
    平均满意度: data.trends.averageSatisfaction[index],
  }));
  
  // 准备综合趋势数据（模拟数据，实际应从API获取）
  const comprehensiveTrendData = [
    { name: '2018', 就业率: 85, 平均薪资: 6500, 平均满意度: 3.5, 继续深造率: 10 },
    { name: '2019', 就业率: 86, 平均薪资: 7000, 平均满意度: 3.6, 继续深造率: 11 },
    { name: '2020', 就业率: 82, 平均薪资: 7200, 平均满意度: 3.5, 继续深造率: 13 },
    { name: '2021', 就业率: 84, 平均薪资: 7800, 平均满意度: 3.7, 继续深造率: 12 },
    { name: '2022', 就业率: 87, 平均薪资: 8500, 平均满意度: 3.8, 继续深造率: 10 },
    { name: '2023', 就业率: 88, 平均薪资: 9200, 平均满意度: 4.0, 继续深造率: 9 },
  ];
  
  // 准备季度趋势数据（模拟数据，实际应从API获取）
  const quarterlyTrendData = [
    { name: '2022Q1', 就业率: 85, 平均薪资: 8000, 平均满意度: 3.7 },
    { name: '2022Q2', 就业率: 86, 平均薪资: 8200, 平均满意度: 3.7 },
    { name: '2022Q3', 就业率: 87, 平均薪资: 8400, 平均满意度: 3.8 },
    { name: '2022Q4', 就业率: 87, 平均薪资: 8500, 平均满意度: 3.8 },
    { name: '2023Q1', 就业率: 86, 平均薪资: 8800, 平均满意度: 3.9 },
    { name: '2023Q2', 就业率: 87, 平均薪资: 9000, 平均满意度: 3.9 },
    { name: '2023Q3', 就业率: 88, 平均薪资: 9100, 平均满意度: 4.0 },
    { name: '2023Q4', 就业率: 88, 平均薪资: 9200, 平均满意度: 4.0 },
  ];
  
  return (
    <div className="space-y-4">
      {/* 趋势概览 */}
      <Card>
        <CardHeader>
          <CardTitle>趋势概览</CardTitle>
          <CardDescription>关键指标随时间的变化趋势</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="flex flex-col md:flex-row items-center justify-between gap-4 mb-6">
            <div className="flex items-center gap-4">
              <div className="p-4 bg-blue-50 rounded-full">
                <Briefcase className="h-8 w-8 text-blue-500" />
              </div>
              <div>
                <p className="text-sm text-muted-foreground">就业率趋势</p>
                <div className="flex items-center gap-2">
                  <h3 className="text-2xl font-bold">88%</h3>
                  <Badge variant="outline" className="bg-green-100">
                    <TrendingUp className="h-3 w-3 mr-1 text-green-600" />
                    +1%
                  </Badge>
                </div>
              </div>
            </div>
            
            <div className="flex items-center gap-4">
              <div className="p-4 bg-amber-50 rounded-full">
                <DollarSign className="h-8 w-8 text-amber-500" />
              </div>
              <div>
                <p className="text-sm text-muted-foreground">薪资趋势</p>
                <div className="flex items-center gap-2">
                  <h3 className="text-2xl font-bold">9,200元</h3>
                  <Badge variant="outline" className="bg-green-100">
                    <TrendingUp className="h-3 w-3 mr-1 text-green-600" />
                    +8.2%
                  </Badge>
                </div>
              </div>
            </div>
            
            <div className="flex items-center gap-4">
              <div className="p-4 bg-green-50 rounded-full">
                <Star className="h-8 w-8 text-green-500" />
              </div>
              <div>
                <p className="text-sm text-muted-foreground">满意度趋势</p>
                <div className="flex items-center gap-2">
                  <h3 className="text-2xl font-bold">4.0分</h3>
                  <Badge variant="outline" className="bg-green-100">
                    <TrendingUp className="h-3 w-3 mr-1 text-green-600" />
                    +0.2分
                  </Badge>
                </div>
              </div>
            </div>
          </div>
          
          <Separator className="my-6" />
          
          <div className="h-80">
            <ResponsiveContainer width="100%" height="100%">
              <ComposedChart
                data={comprehensiveTrendData}
                margin={{ top: 5, right: 30, left: 20, bottom: 5 }}
              >
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis dataKey="name" />
                <YAxis yAxisId="left" orientation="left" />
                <YAxis yAxisId="right" orientation="right" domain={[0, 5]} />
                <Tooltip formatter={(value, name) => {
                  if (name === '就业率' || name === '继续深造率') return [`${value}%`, name];
                  if (name === '平均薪资') return [`${value}元/月`, name];
                  if (name === '平均满意度') return [`${value}分`, name];
                  return [value, name];
                }} />
                <Legend />
                <Line 
                  yAxisId="left" 
                  type="monotone" 
                  dataKey="就业率" 
                  stroke="#0088FE" 
                  activeDot={{ r: 8 }} 
                />
                <Line 
                  yAxisId="left" 
                  type="monotone" 
                  dataKey="继续深造率" 
                  stroke="#00C49F" 
                  activeDot={{ r: 8 }} 
                />
                <Bar 
                  yAxisId="left" 
                  dataKey="平均薪资" 
                  barSize={20} 
                  fill="#FFBB28" 
                />
                <Line 
                  yAxisId="right" 
                  type="monotone" 
                  dataKey="平均满意度" 
                  stroke="#FF8042" 
                  activeDot={{ r: 8 }} 
                />
              </ComposedChart>
            </ResponsiveContainer>
          </div>
        </CardContent>
      </Card>
      
      {/* 就业率趋势分析 */}
      <Card>
        <CardHeader>
          <CardTitle>就业率趋势分析</CardTitle>
          <CardDescription>毕业生就业率随时间的变化</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="h-80">
            <ResponsiveContainer width="100%" height="100%">
              <AreaChart
                data={quarterlyTrendData}
                margin={{ top: 10, right: 30, left: 0, bottom: 0 }}
              >
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis dataKey="name" />
                <YAxis domain={[80, 90]} />
                <Tooltip formatter={(value) => [`${value}%`, '就业率']} />
                <Legend />
                <Area 
                  type="monotone" 
                  dataKey="就业率" 
                  stroke="#0088FE" 
                  fill="#0088FE" 
                  fillOpacity={0.3} 
                />
                <Brush dataKey="name" height={30} stroke="#0088FE" />
              </AreaChart>
            </ResponsiveContainer>
          </div>
        </CardContent>
      </Card>
      
      {/* 薪资和满意度趋势分析 */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <Card>
          <CardHeader>
            <CardTitle>薪资趋势分析</CardTitle>
            <CardDescription>毕业生平均薪资随时间的变化</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="h-80">
              <ResponsiveContainer width="100%" height="100%">
                <LineChart
                  data={quarterlyTrendData}
                  margin={{ top: 5, right: 30, left: 20, bottom: 5 }}
                >
                  <CartesianGrid strokeDasharray="3 3" />
                  <XAxis dataKey="name" />
                  <YAxis />
                  <Tooltip formatter={(value) => [`${value}元/月`, '平均薪资']} />
                  <Legend />
                  <Line 
                    type="monotone" 
                    dataKey="平均薪资" 
                    stroke="#FFBB28" 
                    activeDot={{ r: 8 }} 
                  />
                </LineChart>
              </ResponsiveContainer>
            </div>
          </CardContent>
        </Card>
        
        <Card>
          <CardHeader>
            <CardTitle>满意度趋势分析</CardTitle>
            <CardDescription>毕业生工作满意度随时间的变化</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="h-80">
              <ResponsiveContainer width="100%" height="100%">
                <LineChart
                  data={quarterlyTrendData}
                  margin={{ top: 5, right: 30, left: 20, bottom: 5 }}
                >
                  <CartesianGrid strokeDasharray="3 3" />
                  <XAxis dataKey="name" />
                  <YAxis domain={[3, 5]} />
                  <Tooltip formatter={(value) => [`${value}分`, '平均满意度']} />
                  <Legend />
                  <Line 
                    type="monotone" 
                    dataKey="平均满意度" 
                    stroke="#FF8042" 
                    activeDot={{ r: 8 }} 
                  />
                </LineChart>
              </ResponsiveContainer>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
