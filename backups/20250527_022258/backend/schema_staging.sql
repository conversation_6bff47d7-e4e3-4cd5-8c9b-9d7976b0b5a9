-- 创建用户表
CREATE TABLE IF NOT EXISTS users (
  id INTEGER PRIMARY KEY AUTOINCREMENT,
  email TEXT UNIQUE,
  name TEXT,
  password TEXT,
  role TEXT,
  uuid TEXT,
  is_verified BOOLEAN DEFAULT FALSE,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  is_test_data BOOLEAN DEFAULT FALSE
);

-- 创建问卷回复表
CREATE TABLE IF NOT EXISTS questionnaire_responses (
  id INTEGER PRIMARY KEY AUTOINCREMENT,
  user_id INTEGER,
  is_anonymous BOOLEAN DEFAULT FALSE,
  education_level TEXT,
  major TEXT,
  graduation_year INTEGER,
  region TEXT,
  employment_status TEXT,
  current_industry TEXT,
  current_position TEXT,
  monthly_salary INTEGER,
  expected_salary_range TEXT,
  job_satisfaction TEXT,
  unemployment_duration INTEGER,
  career_change_intention BOOLEAN,
  advice_for_students TEXT,
  observation_on_employment TEXT,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  ip_address TEXT,
  is_test_data BOOLEAN DEFAULT FALSE,
  FOREIGN KEY (user_id) REFERENCES users(id)
);

-- 创建故事表
CREATE TABLE IF NOT EXISTS stories (
  id INTEGER PRIMARY KEY AUTOINCREMENT,
  user_id INTEGER,
  is_anonymous BOOLEAN DEFAULT FALSE,
  title TEXT,
  content TEXT,
  status TEXT DEFAULT 'pending',
  likes INTEGER DEFAULT 0,
  dislikes INTEGER DEFAULT 0,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  ip_address TEXT,
  category TEXT,
  education_level TEXT,
  industry TEXT,
  is_test_data BOOLEAN DEFAULT FALSE,
  FOREIGN KEY (user_id) REFERENCES users(id)
);

-- 创建待审核内容表
CREATE TABLE IF NOT EXISTS pending_content (
  id INTEGER PRIMARY KEY AUTOINCREMENT,
  sequence_number TEXT UNIQUE,
  type TEXT,
  original_content TEXT,
  sanitized_content TEXT,
  status TEXT DEFAULT 'pending',
  priority INTEGER DEFAULT 1,
  origin_ip TEXT,
  user_agent TEXT,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  is_test_data BOOLEAN DEFAULT FALSE
);

-- 创建标签表
CREATE TABLE IF NOT EXISTS tags (
  id TEXT PRIMARY KEY,
  name TEXT,
  color TEXT DEFAULT 'blue',
  priority INTEGER DEFAULT 0,
  category TEXT DEFAULT 'other',
  parent_id TEXT,
  count INTEGER DEFAULT 0,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 创建故事标签关联表
CREATE TABLE IF NOT EXISTS story_tags (
  story_id INTEGER,
  tag_id TEXT,
  PRIMARY KEY (story_id, tag_id),
  FOREIGN KEY (story_id) REFERENCES stories(id),
  FOREIGN KEY (tag_id) REFERENCES tags(id)
);

-- 创建管理员表
CREATE TABLE IF NOT EXISTS admins (
  id INTEGER PRIMARY KEY AUTOINCREMENT,
  username TEXT UNIQUE,
  password_hash TEXT,
  role TEXT DEFAULT 'admin',
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 创建审计日志表
CREATE TABLE IF NOT EXISTS audit_logs (
  id INTEGER PRIMARY KEY AUTOINCREMENT,
  admin_id INTEGER,
  action TEXT,
  entity_type TEXT,
  entity_id TEXT,
  details TEXT,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  FOREIGN KEY (admin_id) REFERENCES admins(id)
);

-- 创建筛选器预设表
CREATE TABLE IF NOT EXISTS filter_presets (
  id TEXT PRIMARY KEY,
  name TEXT,
  user_id INTEGER,
  filter_data TEXT,
  is_default BOOLEAN DEFAULT FALSE,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 创建索引
CREATE INDEX IF NOT EXISTS users_is_test_data_idx ON users(is_test_data);
CREATE INDEX IF NOT EXISTS questionnaire_responses_is_test_data_idx ON questionnaire_responses(is_test_data);
CREATE INDEX IF NOT EXISTS stories_is_test_data_idx ON stories(is_test_data);
CREATE INDEX IF NOT EXISTS pending_content_is_test_data_idx ON pending_content(is_test_data);
