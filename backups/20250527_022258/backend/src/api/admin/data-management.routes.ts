import { Hono } from 'hono';
import * as dataManagementController from './data-management.controller';
import { adminAuth } from '../../middlewares/adminAuth';

// Define environment interface
interface Env {
  SURVEY_KV: KVNamespace;
  DATABASE_URL: string;
  JWT_SECRET: string;
}

// Create Hono app
const app = new Hono<{ Bindings: Env }>();

// 所有路由都需要管理员身份验证
app.use('*', adminAuth);

// 获取问卷回复列表
app.get('/responses', dataManagementController.getResponses);

// 获取单个问卷回复
app.get('/responses/:id', dataManagementController.getResponse);

// 更新问卷回复
app.put('/responses/:id', dataManagementController.updateResponse);

// 删除问卷回复
app.delete('/responses/:id', dataManagementController.deleteResponse);

// 批量删除问卷回复
app.post('/responses/bulk-delete', dataManagementController.bulkDeleteResponses);

// 批量添加标签
app.post('/responses/bulk-tag', dataManagementController.bulkAddTags);

// 批量编辑问卷回复
app.post('/responses/bulk-edit', dataManagementController.bulkEditResponses);

// 批量验证问卷回复
app.post('/responses/bulk-verify', dataManagementController.bulkVerifyResponses);

export default app;
