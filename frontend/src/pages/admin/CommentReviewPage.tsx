import React, { Suspense } from 'react';
import SmartLayout from '@/components/layouts/SmartLayout';
import CommentReviewPanel from '@/components/admin/CommentReviewPanel';
import { LoadingSpinner } from '@/components/ui/loading-spinner';
import ErrorBoundary from '@/components/ErrorBoundary';

export default function CommentReviewPage() {
  return (
    <SmartLayout allowedRoles={['admin', 'superadmin']}>
      <div className="container mx-auto py-6 space-y-6">
        <div>
          <h1 className="text-3xl font-bold">评论审核</h1>
          <p className="text-muted-foreground mt-1">
            审核用户提交的评论，确保符合平台规范
          </p>
        </div>

        <Suspense fallback={<div className="flex justify-center py-12"><LoadingSpinner size="lg" text="加载评论审核面板..." /></div>}>
          <ErrorBoundary fallback={<div className="p-4 border border-red-300 bg-red-50 rounded-md text-red-800">评论审核面板加载失败</div>}>
            <CommentReviewPanel />
          </ErrorBoundary>
        </Suspense>
      </div>
    </SmartLayout>
  );
}
