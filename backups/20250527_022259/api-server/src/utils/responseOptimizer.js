/**
 * API响应优化工具
 * 提供响应格式化、字段过滤和数据转换功能
 */

/**
 * 创建成功响应
 * @param {*} data 响应数据
 * @param {Object} options 选项
 * @returns {Object} 格式化的响应对象
 */
function createSuccessResponse(data, options = {}) {
  const {
    message = null,
    meta = null,
    status = 200,
    fields = null,
    exclude = null,
    transform = null,
    pagination = null
  } = options;
  
  // 基本响应结构
  const response = {
    success: true,
    status,
    data: processData(data, fields, exclude, transform)
  };
  
  // 添加可选字段
  if (message) {
    response.message = message;
  }
  
  if (meta) {
    response.meta = meta;
  }
  
  // 添加分页信息
  if (pagination) {
    response.pagination = pagination;
  }
  
  return response;
}

/**
 * 创建错误响应
 * @param {string} message 错误消息
 * @param {Object} options 选项
 * @returns {Object} 格式化的错误响应对象
 */
function createErrorResponse(message, options = {}) {
  const {
    status = 400,
    code = null,
    details = null,
    data = null
  } = options;
  
  // 基本错误响应结构
  const response = {
    success: false,
    status,
    error: {
      message
    }
  };
  
  // 添加可选字段
  if (code) {
    response.error.code = code;
  }
  
  if (details) {
    response.error.details = details;
  }
  
  if (data) {
    response.data = data;
  }
  
  return response;
}

/**
 * 处理响应数据
 * @param {*} data 原始数据
 * @param {Array} fields 要包含的字段
 * @param {Array} exclude 要排除的字段
 * @param {Function} transform 转换函数
 * @returns {*} 处理后的数据
 */
function processData(data, fields, exclude, transform) {
  if (data === null || data === undefined) {
    return null;
  }
  
  // 应用字段过滤
  let processedData = data;
  
  if (Array.isArray(data)) {
    // 处理数组
    processedData = data.map(item => filterFields(item, fields, exclude));
  } else if (typeof data === 'object') {
    // 处理对象
    processedData = filterFields(data, fields, exclude);
  }
  
  // 应用转换函数
  if (transform && typeof transform === 'function') {
    processedData = transform(processedData);
  }
  
  return processedData;
}

/**
 * 过滤对象字段
 * @param {Object} obj 原始对象
 * @param {Array} fields 要包含的字段
 * @param {Array} exclude 要排除的字段
 * @returns {Object} 过滤后的对象
 */
function filterFields(obj, fields, exclude) {
  if (!obj || typeof obj !== 'object') {
    return obj;
  }
  
  // 如果没有过滤条件，返回原始对象
  if (!fields && !exclude) {
    return obj;
  }
  
  // 创建结果对象
  const result = {};
  
  if (fields) {
    // 只包含指定字段
    fields.forEach(field => {
      if (field in obj) {
        result[field] = obj[field];
      }
    });
  } else {
    // 复制所有字段
    Object.assign(result, obj);
    
    // 排除指定字段
    if (exclude) {
      exclude.forEach(field => {
        delete result[field];
      });
    }
  }
  
  return result;
}

/**
 * 创建分页信息
 * @param {Object} options 分页选项
 * @returns {Object} 分页信息对象
 */
function createPagination(options) {
  const {
    page = 1,
    pageSize = 10,
    totalItems = 0,
    totalPages = Math.ceil(totalItems / pageSize),
    hasNext = page < totalPages,
    hasPrev = page > 1
  } = options;
  
  return {
    page: Number(page),
    pageSize: Number(pageSize),
    totalItems: Number(totalItems),
    totalPages: Number(totalPages),
    hasNext,
    hasPrev
  };
}

/**
 * 创建列表响应
 * @param {Array} items 列表项
 * @param {Object} options 选项
 * @returns {Object} 格式化的列表响应对象
 */
function createListResponse(items, options = {}) {
  const {
    page = 1,
    pageSize = items.length,
    totalItems = items.length,
    fields = null,
    exclude = null,
    transform = null,
    meta = null
  } = options;
  
  // 创建分页信息
  const pagination = createPagination({
    page,
    pageSize,
    totalItems
  });
  
  // 创建成功响应
  return createSuccessResponse(items, {
    fields,
    exclude,
    transform,
    pagination,
    meta
  });
}

/**
 * 创建详情响应
 * @param {Object} item 详情项
 * @param {Object} options 选项
 * @returns {Object} 格式化的详情响应对象
 */
function createDetailResponse(item, options = {}) {
  const {
    fields = null,
    exclude = null,
    transform = null,
    meta = null
  } = options;
  
  // 创建成功响应
  return createSuccessResponse(item, {
    fields,
    exclude,
    transform,
    meta
  });
}

/**
 * 创建验证错误响应
 * @param {Array} errors 验证错误列表
 * @returns {Object} 格式化的验证错误响应对象
 */
function createValidationErrorResponse(errors) {
  return createErrorResponse('验证失败', {
    status: 422,
    code: 'VALIDATION_ERROR',
    details: errors
  });
}

/**
 * 创建未找到响应
 * @param {string} resource 资源名称
 * @param {string} id 资源ID
 * @returns {Object} 格式化的未找到响应对象
 */
function createNotFoundResponse(resource, id) {
  return createErrorResponse(`未找到${resource}: ${id}`, {
    status: 404,
    code: 'NOT_FOUND'
  });
}

/**
 * 创建未授权响应
 * @param {string} message 错误消息
 * @returns {Object} 格式化的未授权响应对象
 */
function createUnauthorizedResponse(message = '未授权访问') {
  return createErrorResponse(message, {
    status: 401,
    code: 'UNAUTHORIZED'
  });
}

/**
 * 创建禁止访问响应
 * @param {string} message 错误消息
 * @returns {Object} 格式化的禁止访问响应对象
 */
function createForbiddenResponse(message = '禁止访问') {
  return createErrorResponse(message, {
    status: 403,
    code: 'FORBIDDEN'
  });
}

/**
 * 创建服务器错误响应
 * @param {string} message 错误消息
 * @param {Error} error 错误对象
 * @returns {Object} 格式化的服务器错误响应对象
 */
function createServerErrorResponse(message = '服务器内部错误', error = null) {
  const response = createErrorResponse(message, {
    status: 500,
    code: 'SERVER_ERROR'
  });
  
  // 在开发环境中添加错误详情
  if (error && process.env.NODE_ENV === 'development') {
    response.error.stack = error.stack;
    response.error.details = error.message;
  }
  
  return response;
}

/**
 * 创建响应对象
 * @param {Object} responseData 响应数据
 * @returns {Response} Cloudflare Workers响应对象
 */
function createResponse(responseData) {
  return new Response(JSON.stringify(responseData), {
    status: responseData.status || (responseData.success ? 200 : 400),
    headers: {
      'Content-Type': 'application/json',
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type, Authorization'
    }
  });
}

module.exports = {
  createSuccessResponse,
  createErrorResponse,
  createListResponse,
  createDetailResponse,
  createValidationErrorResponse,
  createNotFoundResponse,
  createUnauthorizedResponse,
  createForbiddenResponse,
  createServerErrorResponse,
  createResponse,
  createPagination
};
