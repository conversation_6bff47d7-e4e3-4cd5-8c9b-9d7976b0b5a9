import React, { useState, useEffect } from 'react';
import AdminLayout from '@/components/layouts/AdminLayout';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Ta<PERSON>, Ta<PERSON>Content, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { useNavigate } from 'react-router-dom';
import { useToast } from '@/components/ui/use-toast';
import PersonalizedDashboard from '@/components/admin/PersonalizedDashboard';

const AdminDashboardHomePage: React.FC = () => {
  const { toast } = useToast();
  const navigate = useNavigate();
  const [isLoading, setIsLoading] = useState(true);
  const [stats, setStats] = useState({
    totalStories: 0,
    pendingStories: 0,
    totalResponses: 0,
    totalTags: 0
  });

  // 模拟获取统计数据
  useEffect(() => {
    const fetchStats = async () => {
      try {
        // 这里应该是实际的API调用
        // 现在使用模拟数据
        setTimeout(() => {
          setStats({
            totalStories: 125,
            pendingStories: 18,
            totalResponses: 3427,
            totalTags: 42
          });
          setIsLoading(false);
        }, 1000);
      } catch (error) {
        console.error('获取统计数据失败:', error);
        toast({
          variant: 'destructive',
          title: '获取数据失败',
          description: '无法加载仪表盘数据，请稍后再试'
        });
        setIsLoading(false);
      }
    };

    fetchStats();
  }, [toast]);

  return (
    <AdminLayout>
      <div className="container mx-auto py-6">
        <Tabs defaultValue="personalized" className="space-y-6">
          <div className="flex justify-between items-center mb-4">
            <TabsList>
              <TabsTrigger value="personalized">个性化工作台</TabsTrigger>
              <TabsTrigger value="standard">标准仪表盘</TabsTrigger>
            </TabsList>

            <Button
              variant="outline"
              onClick={() => navigate('/admin/user-settings')}
            >
              用户设置
            </Button>
          </div>

          {/* 个性化工作台 */}
          <TabsContent value="personalized" className="space-y-6">
            <PersonalizedDashboard />
          </TabsContent>

          {/* 标准仪表盘 */}
          <TabsContent value="standard" className="space-y-6">
            <h1 className="text-2xl font-bold mb-6">管理员控制台</h1>

            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
              <Card>
                <CardHeader className="pb-2">
                  <CardTitle className="text-sm font-medium">总故事数</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold">{isLoading ? '加载中...' : stats.totalStories}</div>
                  <Button
                    variant="link"
                    className="p-0 h-auto text-sm text-blue-500"
                    onClick={() => navigate('/admin/story-review')}
                  >
                    查看详情
                  </Button>
                </CardContent>
              </Card>

              <Card>
                <CardHeader className="pb-2">
                  <CardTitle className="text-sm font-medium">待审核故事</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold">{isLoading ? '加载中...' : stats.pendingStories}</div>
                  <Button
                    variant="link"
                    className="p-0 h-auto text-sm text-blue-500"
                    onClick={() => navigate('/admin/story-review')}
                  >
                    去审核
                  </Button>
                </CardContent>
              </Card>

              <Card>
                <CardHeader className="pb-2">
                  <CardTitle className="text-sm font-medium">问卷回复数</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold">{isLoading ? '加载中...' : stats.totalResponses}</div>
                  <Button
                    variant="link"
                    className="p-0 h-auto text-sm text-blue-500"
                    onClick={() => navigate('/admin/questionnaire-responses')}
                  >
                    查看详情
                  </Button>
                </CardContent>
              </Card>

              <Card>
                <CardHeader className="pb-2">
                  <CardTitle className="text-sm font-medium">标签总数</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold">{isLoading ? '加载中...' : stats.totalTags}</div>
                  <Button
                    variant="link"
                    className="p-0 h-auto text-sm text-blue-500"
                    onClick={() => navigate('/admin/tag-management')}
                  >
                    管理标签
                  </Button>
                </CardContent>
              </Card>
            </div>

            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              <Card>
                <CardHeader>
                  <CardTitle>快速导航</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="grid grid-cols-2 gap-4">
                    <Button onClick={() => navigate('/admin/story-review')}>故事审核</Button>
                    <Button onClick={() => navigate('/admin/tag-management')}>标签管理</Button>
                    <Button onClick={() => navigate('/admin/questionnaire-responses')}>问卷回复</Button>
                    <Button onClick={() => navigate('/admin/data-analysis')}>数据分析</Button>
                    <Button onClick={() => navigate('/admin/deidentification-settings')}>内容脱敏</Button>
                    <Button onClick={() => navigate('/admin/security-settings')}>安全设置</Button>
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle>最近活动</CardTitle>
                </CardHeader>
                <CardContent>
                  <p className="text-center text-gray-500 py-8">暂无活动记录</p>
                </CardContent>
              </Card>
            </div>
          </TabsContent>
        </Tabs>
      </div>
    </AdminLayout>
  );
};

export default AdminDashboardHomePage;
