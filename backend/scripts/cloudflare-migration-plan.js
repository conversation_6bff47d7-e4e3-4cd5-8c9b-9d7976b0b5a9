/**
 * Cloudflare 多数据库迁移方案
 * KV + D1 + R2 数据分层迁移
 */

const { PrismaClient } = require('@prisma/client');

class CloudflareMigration {
  constructor() {
    this.prisma = new PrismaClient();
    this.migrationLog = [];
  }

  /**
   * 主迁移流程
   */
  async migrate() {
    console.log('🚀 开始 Cloudflare 多数据库迁移...');
    
    try {
      // 1. 数据分析和备份
      await this.analyzeCurrentData();
      await this.backupCurrentData();
      
      // 2. D1 结构优化
      await this.optimizeD1Schema();
      
      // 3. 数据分层迁移
      await this.migrateToKV();      // 热数据 → KV
      await this.migrateToD1();      // 核心数据 → D1
      await this.migrateToR2();      // 大文件 → R2
      
      // 4. 建立数据关联
      await this.establishDataLinks();
      
      // 5. 性能优化
      await this.optimizeIndexes();
      await this.setupCaching();
      
      // 6. 验证数据完整性
      await this.validateMigration();
      
      console.log('✅ Cloudflare 迁移完成！');
      
    } catch (error) {
      console.error('❌ 迁移失败:', error);
      await this.rollbackMigration();
      throw error;
    }
  }

  /**
   * 分析当前数据分布
   */
  async analyzeCurrentData() {
    console.log('📊 分析当前数据分布...');
    
    const analysis = {
      users: await this.prisma.user.count(),
      responses: await this.prisma.questionnaireResponse.count(),
      stories: await this.prisma.story.count(),
      
      // 数据大小分析
      largeContent: await this.prisma.questionnaireResponse.count({
        where: {
          OR: [
            { adviceForStudents: { not: null } },
            { observationOnEmployment: { not: null } }
          ]
        }
      }),
      
      // 查询频率分析
      hotQueries: [
        'user_sessions',
        'questionnaire_stats', 
        'story_list',
        'hot_content'
      ],
      
      coldData: [
        'full_text_content',
        'audit_logs',
        'historical_data'
      ]
    };
    
    console.log('数据分析结果:', analysis);
    this.migrationLog.push({ step: 'analysis', data: analysis });
    
    return analysis;
  }

  /**
   * 迁移热数据到 KV
   */
  async migrateToKV() {
    console.log('🔥 迁移热数据到 KV...');
    
    // 1. 用户会话数据结构
    const sessionStructure = {
      keyPattern: 'session:{sessionId}',
      ttl: 86400, // 24小时
      structure: {
        userId: 'string',
        anonymousId: 'string',
        role: 'string',
        permissions: 'array',
        lastActivity: 'timestamp',
        ipAddress: 'string'
      }
    };
    
    // 2. 实时统计数据结构
    const statsStructure = {
      keyPattern: 'stats:{type}:{id}:{date}',
      ttl: 3600, // 1小时
      structure: {
        questionId: 'string',
        totalResponses: 'number',
        options: 'array',
        lastUpdated: 'timestamp'
      }
    };
    
    // 3. 热门内容缓存结构
    const hotContentStructure = {
      keyPattern: 'hot:{type}:{timeframe}',
      ttl: 1800, // 30分钟
      structure: {
        stories: 'array',
        voices: 'array',
        lastUpdated: 'timestamp'
      }
    };
    
    // 4. 系统配置结构
    const configStructure = {
      keyPattern: 'config:{category}',
      ttl: null, // 永久
      structure: {
        moderation: 'object',
        features: 'object',
        limits: 'object'
      }
    };
    
    // 生成 KV 迁移脚本
    const kvMigrationScript = this.generateKVMigrationScript({
      session: sessionStructure,
      stats: statsStructure,
      hotContent: hotContentStructure,
      config: configStructure
    });
    
    console.log('KV 迁移脚本已生成');
    this.migrationLog.push({ step: 'kv_migration', script: kvMigrationScript });
    
    return kvMigrationScript;
  }

  /**
   * 优化 D1 数据结构
   */
  async migrateToD1() {
    console.log('🗄️ 优化 D1 数据结构...');
    
    // 1. 精简用户表
    const optimizedUserSchema = `
      CREATE TABLE users_optimized (
        id TEXT PRIMARY KEY,              -- user_xxx
        anonymous_id TEXT UNIQUE,         -- anon_xxx
        email TEXT,
        role TEXT DEFAULT 'user',
        is_active BOOLEAN DEFAULT true,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
      );
      
      -- 高频查询索引
      CREATE INDEX idx_users_role ON users_optimized(role);
      CREATE INDEX idx_users_anonymous_id ON users_optimized(anonymous_id);
      CREATE INDEX idx_users_active ON users_optimized(is_active);
    `;
    
    // 2. 问卷结构表
    const questionnaireSchema = `
      CREATE TABLE questionnaire_templates (
        id TEXT PRIMARY KEY,              -- quest_xxx
        title TEXT NOT NULL,
        version TEXT DEFAULT '1.0',
        is_active BOOLEAN DEFAULT true,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP
      );
      
      CREATE TABLE questions (
        id TEXT PRIMARY KEY,              -- qitem_xxx
        questionnaire_id TEXT NOT NULL,
        question_number INTEGER NOT NULL,
        question_text TEXT NOT NULL,
        question_type TEXT NOT NULL,
        options TEXT,                     -- JSON格式
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (questionnaire_id) REFERENCES questionnaire_templates(id)
      );
      
      CREATE INDEX idx_questions_questionnaire ON questions(questionnaire_id);
      CREATE INDEX idx_questions_type ON questions(question_type);
    `;
    
    // 3. 高频查询表
    const answersSchema = `
      CREATE TABLE question_answers (
        id TEXT PRIMARY KEY,              -- ans_xxx
        user_id TEXT NOT NULL,
        question_id TEXT NOT NULL,
        response_id TEXT NOT NULL,
        answer_value TEXT NOT NULL,       -- JSON格式
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (user_id) REFERENCES users_optimized(id),
        FOREIGN KEY (question_id) REFERENCES questions(id)
      );
      
      -- 关键高频索引
      CREATE INDEX idx_answers_question ON question_answers(question_id);
      CREATE INDEX idx_answers_user ON question_answers(user_id);
      CREATE INDEX idx_answers_response ON question_answers(response_id);
    `;
    
    // 4. 元数据表（不包含长文本）
    const metadataSchema = `
      CREATE TABLE content_metadata (
        id TEXT PRIMARY KEY,              -- text_xxx
        user_id TEXT NOT NULL,
        content_type TEXT NOT NULL,       -- story, voice
        title TEXT,
        category TEXT,
        tags TEXT,                        -- JSON格式
        status TEXT DEFAULT 'pending',
        likes INTEGER DEFAULT 0,
        views INTEGER DEFAULT 0,
        word_count INTEGER DEFAULT 0,
        r2_path TEXT,                     -- R2存储路径
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (user_id) REFERENCES users_optimized(id)
      );
      
      CREATE INDEX idx_content_user ON content_metadata(user_id);
      CREATE INDEX idx_content_type ON content_metadata(content_type);
      CREATE INDEX idx_content_status ON content_metadata(status);
    `;
    
    const d1Schema = {
      users: optimizedUserSchema,
      questionnaire: questionnaireSchema,
      answers: answersSchema,
      metadata: metadataSchema
    };
    
    console.log('D1 优化方案已生成');
    this.migrationLog.push({ step: 'd1_optimization', schema: d1Schema });
    
    return d1Schema;
  }

  /**
   * 迁移大文件到 R2
   */
  async migrateToR2() {
    console.log('📁 迁移大文件到 R2...');
    
    // 1. 长文本内容迁移策略
    const contentMigrationPlan = {
      // 故事内容
      stories: {
        sourcePath: 'questionnaire_responses.adviceForStudents',
        targetPath: 'content/voice/advice/{year}/{month}/{voiceId}.json',
        structure: {
          id: 'voice_xxx',
          type: 'advice',
          title: 'string',
          content: 'string',
          metadata: {
            userId: 'string',
            wordCount: 'number',
            language: 'string',
            tags: 'array'
          },
          audit: {
            createdAt: 'string',
            reviewStatus: 'string'
          }
        }
      },
      
      // 心声内容
      voices: {
        sourcePath: 'questionnaire_responses.observationOnEmployment',
        targetPath: 'content/voice/observation/{year}/{month}/{voiceId}.json',
        structure: {
          id: 'voice_xxx',
          type: 'observation',
          title: 'string',
          content: 'string',
          metadata: {
            userId: 'string',
            wordCount: 'number',
            language: 'string'
          }
        }
      },
      
      // 故事墙内容
      storyWall: {
        sourcePath: 'stories.content',
        targetPath: 'content/story/{year}/{month}/{storyId}.json',
        structure: {
          id: 'text_xxx',
          type: 'story',
          title: 'string',
          content: 'string',
          metadata: {
            userId: 'string',
            category: 'string',
            tags: 'array',
            version: 'number'
          }
        }
      }
    };
    
    // 2. 备份数据迁移策略
    const backupPlan = {
      daily: {
        path: 'backups/{date}/daily/',
        retention: '30 days',
        content: ['users', 'responses', 'metadata']
      },
      weekly: {
        path: 'backups/{date}/weekly/',
        retention: '12 weeks',
        content: ['full_export']
      },
      monthly: {
        path: 'backups/{date}/monthly/',
        retention: '12 months',
        content: ['archive']
      }
    };
    
    // 3. 文件附件迁移策略
    const attachmentPlan = {
      path: 'attachments/{userId}/{contentId}/{filename}',
      maxSize: '25MB',
      allowedTypes: ['image/*', 'application/pdf', 'text/*'],
      metadata: {
        originalName: 'string',
        mimeType: 'string',
        size: 'number',
        uploadedAt: 'string'
      }
    };
    
    const r2Plan = {
      content: contentMigrationPlan,
      backup: backupPlan,
      attachment: attachmentPlan
    };
    
    console.log('R2 迁移方案已生成');
    this.migrationLog.push({ step: 'r2_migration', plan: r2Plan });
    
    return r2Plan;
  }

  /**
   * 建立数据关联
   */
  async establishDataLinks() {
    console.log('🔗 建立数据关联...');
    
    const dataLinks = {
      // D1 → R2 关联
      contentLinks: {
        table: 'content_metadata',
        linkField: 'r2_path',
        pattern: 'content/{type}/{year}/{month}/{id}.json'
      },
      
      // KV → D1 关联
      cacheLinks: {
        userSessions: {
          kvKey: 'session:{sessionId}',
          d1Query: 'SELECT * FROM users_optimized WHERE id = ?'
        },
        questionStats: {
          kvKey: 'stats:question:{questionId}',
          d1Query: 'SELECT * FROM question_answers WHERE question_id = ?'
        }
      },
      
      // 跨数据库查询策略
      queryStrategy: {
        getUserContent: [
          '1. KV: 检查用户会话',
          '2. D1: 查询内容元数据',
          '3. R2: 获取完整内容'
        ],
        getQuestionStats: [
          '1. KV: 检查统计缓存',
          '2. D1: 聚合答案数据',
          '3. KV: 更新缓存'
        ]
      }
    };
    
    console.log('数据关联策略已建立');
    this.migrationLog.push({ step: 'data_links', links: dataLinks });
    
    return dataLinks;
  }

  /**
   * 生成 KV 迁移脚本
   */
  generateKVMigrationScript(structures) {
    return `
// Cloudflare KV 迁移脚本
export default {
  async fetch(request, env) {
    const { KV } = env;
    
    // 用户会话管理
    async function createUserSession(userId, sessionData) {
      const sessionId = generateSessionId();
      const sessionKey = \`session:\${sessionId}\`;
      
      await KV.put(sessionKey, JSON.stringify({
        userId,
        ...sessionData,
        createdAt: Date.now()
      }), { expirationTtl: 86400 });
      
      return sessionId;
    }
    
    // 实时统计更新
    async function updateQuestionStats(questionId, answers) {
      const statsKey = \`stats:question:\${questionId}\`;
      const stats = calculateStats(answers);
      
      await KV.put(statsKey, JSON.stringify(stats), { 
        expirationTtl: 3600 
      });
    }
    
    // 热门内容缓存
    async function updateHotContent(type, content) {
      const hotKey = \`hot:\${type}:daily\`;
      
      await KV.put(hotKey, JSON.stringify({
        content,
        lastUpdated: Date.now()
      }), { expirationTtl: 1800 });
    }
    
    return new Response('KV Migration Script Ready');
  }
};
    `;
  }

  /**
   * 验证迁移结果
   */
  async validateMigration() {
    console.log('🔍 验证迁移结果...');
    
    const validation = {
      dataIntegrity: await this.checkDataIntegrity(),
      performance: await this.checkPerformance(),
      functionality: await this.checkFunctionality()
    };
    
    console.log('迁移验证结果:', validation);
    this.migrationLog.push({ step: 'validation', results: validation });
    
    return validation;
  }

  /**
   * 回滚迁移
   */
  async rollbackMigration() {
    console.log('🔄 执行迁移回滚...');
    
    // 实现回滚逻辑
    for (const logEntry of this.migrationLog.reverse()) {
      console.log(`回滚步骤: ${logEntry.step}`);
      // 具体回滚操作
    }
  }
}

// 执行迁移
if (require.main === module) {
  const migration = new CloudflareMigration();
  migration.migrate()
    .catch(console.error)
    .finally(() => process.exit());
}

module.exports = CloudflareMigration;
