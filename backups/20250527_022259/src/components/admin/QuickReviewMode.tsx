import React, { useState, useEffect, useRef, useCallback } from 'react';
import { Card, CardContent, CardFooter, CardHeader } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { Switch } from '@/components/ui/switch';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogFooter } from '@/components/ui/dialog';
import { useToast } from '@/components/ui/use-toast';
import { LoadingSpinner } from '@/components/ui/loading-spinner';
import { 
  ArrowLeft, 
  ArrowRight, 
  ArrowUp, 
  ArrowDown, 
  Edit, 
  CheckCircle, 
  XCircle,
  Play,
  Pause,
  Clock,
  AlertTriangle,
  HelpCircle,
  Flag
} from 'lucide-react';
import { formatDistanceToNow } from 'date-fns';
import { zhCN } from 'date-fns/locale';

// 导入数据服务
import { 
  getPendingContents, 
  approveContent, 
  rejectContent, 
  editContent 
} from '@/services/dataService';

// 导入类型
import { ContentType, PendingContent, ReviewStatus } from '@/types';

// 组件属性
interface QuickReviewModeProps {
  filters: {
    type: string;
    flag: string;
    priority: string;
  };
  onExit: () => void;
}

/**
 * 快速审核模式组件
 * 提供专注的内容审核体验，支持键盘快捷操作
 */
export default function QuickReviewMode({ filters, onExit }: QuickReviewModeProps) {
  const { toast } = useToast();
  
  // 内容状态
  const [contents, setContents] = useState<PendingContent[]>([]);
  const [currentIndex, setCurrentIndex] = useState(0);
  const [isLoading, setIsLoading] = useState(true);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [isPreloading, setIsPreloading] = useState(false);
  const [hasMore, setHasMore] = useState(true);
  
  // 自动播放状态
  const [autoPlay, setAutoPlay] = useState(false);
  const [autoPlayInterval, setAutoPlayInterval] = useState(5); // 秒
  const autoPlayTimerRef = useRef<number | null>(null);
  
  // 编辑对话框状态
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false);
  const [editContent, setEditContent] = useState('');
  
  // 拒绝对话框状态
  const [isRejectDialogOpen, setIsRejectDialogOpen] = useState(false);
  const [rejectReason, setRejectReason] = useState('');
  
  // 统计数据
  const [stats, setStats] = useState({
    approved: 0,
    rejected: 0,
    edited: 0,
    total: 0,
    startTime: new Date(),
    reviewsPerMinute: 0
  });
  
  // 当前内容
  const currentContent = contents[currentIndex];
  
  // 加载内容
  const loadContents = useCallback(async (page = 1) => {
    try {
      setIsLoading(true);
      setError(null);
      
      // 构建查询参数
      const queryParams = new URLSearchParams();
      queryParams.append('page', page.toString());
      queryParams.append('pageSize', '10'); // 每次加载10条
      
      if (filters.type) {
        queryParams.append('type', filters.type);
      }
      
      if (filters.flag) {
        queryParams.append('flag', filters.flag);
      }
      
      if (filters.priority) {
        queryParams.append('priority', filters.priority);
      }
      
      // 获取待审核内容
      const response = await getPendingContents(queryParams.toString());
      
      if (response.success) {
        if (page === 1) {
          // 首次加载，替换内容
          setContents(response.pendingContents);
          setCurrentIndex(0);
        } else {
          // 预加载，追加内容
          setContents(prev => [...prev, ...response.pendingContents]);
        }
        
        // 检查是否还有更多内容
        setHasMore(
          response.pendingContents.length > 0 && 
          response.pagination.page < response.pagination.totalPages
        );
      } else {
        setError(response.error || '获取待审核内容失败');
      }
    } catch (error) {
      console.error('获取待审核内容错误:', error);
      setError('服务器错误，请稍后再试');
    } finally {
      setIsLoading(false);
      setIsPreloading(false);
    }
  }, [filters]);
  
  // 预加载下一批内容
  const preloadNextBatch = useCallback(() => {
    if (isPreloading || !hasMore) return;
    
    setIsPreloading(true);
    const nextPage = Math.floor(contents.length / 10) + 1;
    loadContents(nextPage);
  }, [contents.length, hasMore, isPreloading, loadContents]);
  
  // 初始加载
  useEffect(() => {
    loadContents();
    
    // 初始化统计数据
    setStats({
      approved: 0,
      rejected: 0,
      edited: 0,
      total: 0,
      startTime: new Date(),
      reviewsPerMinute: 0
    });
    
    // 清除自动播放定时器
    if (autoPlayTimerRef.current) {
      clearInterval(autoPlayTimerRef.current);
      autoPlayTimerRef.current = null;
    }
    
    // 组件卸载时清理
    return () => {
      if (autoPlayTimerRef.current) {
        clearInterval(autoPlayTimerRef.current);
      }
    };
  }, [filters, loadContents]);
  
  // 监听当前索引变化，预加载下一批内容
  useEffect(() => {
    // 当浏览到当前批次的70%时，预加载下一批
    if (currentIndex > 0 && currentIndex % 10 === 7) {
      preloadNextBatch();
    }
    
    // 更新统计数据
    const elapsedMinutes = (Date.now() - stats.startTime.getTime()) / 60000;
    if (elapsedMinutes > 0) {
      setStats(prev => ({
        ...prev,
        reviewsPerMinute: Math.round((prev.approved + prev.rejected + prev.edited) / elapsedMinutes * 10) / 10
      }));
    }
  }, [currentIndex, preloadNextBatch, stats.approved, stats.edited, stats.rejected, stats.startTime]);
  
  // 自动播放
  useEffect(() => {
    if (autoPlay) {
      // 启动自动播放
      autoPlayTimerRef.current = window.setInterval(() => {
        // 检查是否有敏感内容，如果有则暂停
        const current = contents[currentIndex];
        if (current && (current.flags.includes('sensitive-words') || current.flags.includes('high-exposure'))) {
          setAutoPlay(false);
          toast({
            title: '自动播放已暂停',
            description: '检测到敏感内容，需要人工审核',
            variant: 'default'
          });
          return;
        }
        
        // 自动前进到下一条
        handleNext();
      }, autoPlayInterval * 1000);
    } else {
      // 停止自动播放
      if (autoPlayTimerRef.current) {
        clearInterval(autoPlayTimerRef.current);
        autoPlayTimerRef.current = null;
      }
    }
    
    // 组件卸载时清理
    return () => {
      if (autoPlayTimerRef.current) {
        clearInterval(autoPlayTimerRef.current);
      }
    };
  }, [autoPlay, autoPlayInterval, contents, currentIndex]);
  
  // 键盘导航
  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      // 如果正在提交或对话框打开，不处理键盘事件
      if (isSubmitting || isEditDialogOpen || isRejectDialogOpen) {
        return;
      }
      
      switch (e.key) {
        case 'ArrowLeft':
          // 上一条
          e.preventDefault();
          handlePrevious();
          break;
        case 'ArrowRight':
          // 下一条
          e.preventDefault();
          handleNext();
          break;
        case 'ArrowUp':
          // 拒绝
          e.preventDefault();
          handleReject();
          break;
        case 'ArrowDown':
          // 通过
          e.preventDefault();
          handleApprove();
          break;
        case 'Enter':
          // 编辑
          e.preventDefault();
          handleEdit();
          break;
        case ' ':
          // 暂停/继续自动播放
          e.preventDefault();
          setAutoPlay(!autoPlay);
          break;
        case 'Escape':
          // 退出
          e.preventDefault();
          onExit();
          break;
      }
    };
    
    // 添加键盘事件监听
    window.addEventListener('keydown', handleKeyDown);
    
    // 组件卸载时移除监听
    return () => {
      window.removeEventListener('keydown', handleKeyDown);
    };
  }, [autoPlay, currentIndex, isEditDialogOpen, isRejectDialogOpen, isSubmitting, onExit]);
  
  // 处理上一条
  const handlePrevious = () => {
    if (currentIndex > 0) {
      setCurrentIndex(currentIndex - 1);
    } else {
      toast({
        title: '已经是第一条',
        description: '没有更多内容了',
        variant: 'default'
      });
    }
  };
  
  // 处理下一条
  const handleNext = () => {
    if (currentIndex < contents.length - 1) {
      setCurrentIndex(currentIndex + 1);
    } else if (hasMore) {
      toast({
        title: '正在加载更多内容',
        description: '请稍候...',
        variant: 'default'
      });
      preloadNextBatch();
    } else {
      toast({
        title: '已经是最后一条',
        description: '没有更多内容了',
        variant: 'default'
      });
    }
  };
  
  // 处理通过
  const handleApprove = async () => {
    if (!currentContent) return;
    
    try {
      setIsSubmitting(true);
      
      // 调用通过API
      const response = await approveContent(currentContent.id, {
        reviewNotes: '快速审核通过'
      });
      
      if (response.success) {
        // 更新统计
        setStats(prev => ({
          ...prev,
          approved: prev.approved + 1,
          total: prev.total + 1
        }));
        
        // 更新内容状态
        setContents(prev => 
          prev.map((item, index) => 
            index === currentIndex 
              ? { ...item, status: ReviewStatus.APPROVED } 
              : item
          )
        );
        
        // 显示成功提示
        toast({
          title: '内容已通过',
          variant: 'default'
        });
        
        // 自动前进到下一条
        handleNext();
      } else {
        toast({
          title: '操作失败',
          description: response.error || '通过内容失败',
          variant: 'destructive'
        });
      }
    } catch (error) {
      console.error('通过内容错误:', error);
      toast({
        title: '操作失败',
        description: '服务器错误，请稍后再试',
        variant: 'destructive'
      });
    } finally {
      setIsSubmitting(false);
    }
  };
  
  // 处理拒绝
  const handleReject = () => {
    if (!currentContent) return;
    
    // 打开拒绝对话框
    setRejectReason('');
    setIsRejectDialogOpen(true);
  };
  
  // 提交拒绝
  const submitReject = async () => {
    if (!currentContent) return;
    
    try {
      setIsSubmitting(true);
      
      // 调用拒绝API
      const response = await rejectContent(currentContent.id, {
        reason: rejectReason || '内容不符合规范'
      });
      
      if (response.success) {
        // 更新统计
        setStats(prev => ({
          ...prev,
          rejected: prev.rejected + 1,
          total: prev.total + 1
        }));
        
        // 更新内容状态
        setContents(prev => 
          prev.map((item, index) => 
            index === currentIndex 
              ? { ...item, status: ReviewStatus.REJECTED } 
              : item
          )
        );
        
        // 关闭对话框
        setIsRejectDialogOpen(false);
        
        // 显示成功提示
        toast({
          title: '内容已拒绝',
          variant: 'default'
        });
        
        // 自动前进到下一条
        handleNext();
      } else {
        toast({
          title: '操作失败',
          description: response.error || '拒绝内容失败',
          variant: 'destructive'
        });
      }
    } catch (error) {
      console.error('拒绝内容错误:', error);
      toast({
        title: '操作失败',
        description: '服务器错误，请稍后再试',
        variant: 'destructive'
      });
    } finally {
      setIsSubmitting(false);
    }
  };
  
  // 处理编辑
  const handleEdit = () => {
    if (!currentContent) return;
    
    // 打开编辑对话框
    setEditContent(
      currentContent.type === ContentType.STORY 
        ? currentContent.originalContent.content 
        : JSON.stringify(currentContent.originalContent, null, 2)
    );
    setIsEditDialogOpen(true);
  };
  
  // 提交编辑
  const submitEdit = async () => {
    if (!currentContent) return;
    
    try {
      setIsSubmitting(true);
      
      // 准备编辑数据
      let editedContent;
      if (currentContent.type === ContentType.STORY) {
        editedContent = {
          ...currentContent.originalContent,
          content: editContent
        };
      } else {
        try {
          editedContent = JSON.parse(editContent);
        } catch (e) {
          toast({
            title: '格式错误',
            description: '请输入有效的JSON格式',
            variant: 'destructive'
          });
          return;
        }
      }
      
      // 调用编辑API
      const response = await editContent(currentContent.id, {
        content: editedContent,
        reviewNotes: '快速审核编辑'
      });
      
      if (response.success) {
        // 更新统计
        setStats(prev => ({
          ...prev,
          edited: prev.edited + 1,
          total: prev.total + 1
        }));
        
        // 更新内容状态
        setContents(prev => 
          prev.map((item, index) => 
            index === currentIndex 
              ? { 
                  ...item, 
                  status: ReviewStatus.EDITED,
                  sanitizedContent: editedContent
                } 
              : item
          )
        );
        
        // 关闭对话框
        setIsEditDialogOpen(false);
        
        // 显示成功提示
        toast({
          title: '内容已编辑并通过',
          variant: 'default'
        });
        
        // 自动前进到下一条
        handleNext();
      } else {
        toast({
          title: '操作失败',
          description: response.error || '编辑内容失败',
          variant: 'destructive'
        });
      }
    } catch (error) {
      console.error('编辑内容错误:', error);
      toast({
        title: '操作失败',
        description: '服务器错误，请稍后再试',
        variant: 'destructive'
      });
    } finally {
      setIsSubmitting(false);
    }
  };
  
  // 渲染内容类型徽章
  const renderTypeBadge = (type: ContentType) => {
    return (
      <Badge variant={type === ContentType.STORY ? 'default' : 'outline'}>
        {type === ContentType.STORY ? '故事' : '问卷'}
      </Badge>
    );
  };
  
  // 渲染标记徽章
  const renderFlagBadge = (flag: string) => {
    let color = 'default';
    let icon = null;
    
    switch (flag) {
      case 'sensitive-words':
        color = 'destructive';
        icon = <AlertTriangle className="h-3 w-3 mr-1" />;
        break;
      case 'high-exposure':
        color = 'destructive';
        icon = <Flag className="h-3 w-3 mr-1" />;
        break;
      case 'suspicious-behavior':
        color = 'warning';
        icon = <AlertTriangle className="h-3 w-3 mr-1" />;
        break;
      default:
        color = 'secondary';
    }
    
    return (
      <Badge variant={color as any} className="mr-1 mb-1">
        {icon}
        {flag}
      </Badge>
    );
  };
  
  // 渲染内容预览
  const renderContentPreview = (content: PendingContent) => {
    if (content.type === ContentType.STORY) {
      return (
        <div className="space-y-4">
          <h3 className="text-xl font-bold">{content.originalContent.title}</h3>
          <div className="whitespace-pre-wrap">{content.originalContent.content}</div>
        </div>
      );
    } else {
      return (
        <div className="space-y-4">
          <h3 className="text-xl font-bold">问卷回复 #{content.sequenceNumber}</h3>
          <pre className="bg-muted p-4 rounded-md overflow-auto text-sm">
            {JSON.stringify(content.originalContent, null, 2)}
          </pre>
        </div>
      );
    }
  };
  
  if (isLoading && contents.length === 0) {
    return (
      <div className="flex flex-col items-center justify-center p-12">
        <LoadingSpinner size="lg" />
        <p className="mt-4 text-muted-foreground">加载内容中...</p>
      </div>
    );
  }
  
  if (error && contents.length === 0) {
    return (
      <div className="flex flex-col items-center justify-center p-12 text-destructive">
        <p>{error}</p>
        <Button 
          variant="outline" 
          onClick={() => loadContents()} 
          className="mt-4"
        >
          重试
        </Button>
      </div>
    );
  }
  
  if (contents.length === 0) {
    return (
      <div className="flex flex-col items-center justify-center p-12">
        <CheckCircle className="h-12 w-12 text-muted-foreground mb-4" />
        <h3 className="text-xl font-medium">没有待审核内容</h3>
        <p className="text-muted-foreground mt-2">所有内容已审核完毕</p>
        <Button 
          variant="outline" 
          onClick={onExit} 
          className="mt-4"
        >
          返回
        </Button>
      </div>
    );
  }
  
  return (
    <div className="space-y-4">
      {/* 进度和统计 */}
      <div className="bg-muted p-4 rounded-lg">
        <div className="flex flex-wrap justify-between items-center mb-2">
          <div className="flex items-center space-x-4">
            <div>
              <span className="text-sm text-muted-foreground">进度:</span>
              <span className="ml-1 font-medium">{currentIndex + 1}/{contents.length}</span>
            </div>
            
            <div>
              <span className="text-sm text-muted-foreground">审核速度:</span>
              <span className="ml-1 font-medium">{stats.reviewsPerMinute} 条/分钟</span>
            </div>
            
            <div>
              <span className="text-sm text-muted-foreground">已审核:</span>
              <span className="ml-1 font-medium">{stats.total} 条</span>
            </div>
          </div>
          
          <div className="flex items-center space-x-2">
            <Label htmlFor="auto-play" className="text-sm">自动播放</Label>
            <Switch
              id="auto-play"
              checked={autoPlay}
              onCheckedChange={setAutoPlay}
            />
            
            <div className="flex items-center ml-2">
              <Button
                variant="outline"
                size="sm"
                disabled={autoPlayInterval <= 1}
                onClick={() => setAutoPlayInterval(prev => Math.max(1, prev - 1))}
              >
                -
              </Button>
              <span className="mx-2 text-sm">{autoPlayInterval}秒</span>
              <Button
                variant="outline"
                size="sm"
                disabled={autoPlayInterval >= 10}
                onClick={() => setAutoPlayInterval(prev => Math.min(10, prev + 1))}
              >
                +
              </Button>
            </div>
          </div>
        </div>
        
        <Progress value={(currentIndex + 1) / contents.length * 100} className="h-2" />
      </div>
      
      {/* 内容卡片 */}
      <Card className="border-2">
        <CardHeader className="pb-2">
          <div className="flex justify-between items-start">
            <div className="flex items-center space-x-2">
              {renderTypeBadge(currentContent.type)}
              <span className="text-sm text-muted-foreground">
                {formatDistanceToNow(new Date(currentContent.createdAt), { addSuffix: true, locale: zhCN })}
              </span>
            </div>
            
            <div className="flex items-center">
              {isSubmitting && <LoadingSpinner size="sm" className="mr-2" />}
              <Badge variant="outline" className="font-mono">
                {currentContent.sequenceNumber}
              </Badge>
            </div>
          </div>
        </CardHeader>
        
        <CardContent className="py-4">
          {renderContentPreview(currentContent)}
        </CardContent>
        
        <CardFooter className="flex-col items-stretch space-y-4 pt-2">
          {/* 标记 */}
          {currentContent.flags.length > 0 && (
            <div className="flex flex-wrap">
              {currentContent.flags.map((flag) => (
                <span key={flag}>{renderFlagBadge(flag)}</span>
              ))}
            </div>
          )}
          
          {/* 操作按钮 */}
          <div className="grid grid-cols-3 gap-2">
            <Button
              variant="destructive"
              onClick={handleReject}
              disabled={isSubmitting}
              className="flex items-center justify-center"
            >
              <XCircle className="h-4 w-4 mr-2" />
              拒绝 (↑)
            </Button>
            
            <Button
              variant="outline"
              onClick={handleEdit}
              disabled={isSubmitting}
              className="flex items-center justify-center"
            >
              <Edit className="h-4 w-4 mr-2" />
              编辑 (Enter)
            </Button>
            
            <Button
              variant="default"
              onClick={handleApprove}
              disabled={isSubmitting}
              className="flex items-center justify-center"
            >
              <CheckCircle className="h-4 w-4 mr-2" />
              通过 (↓)
            </Button>
          </div>
          
          {/* 导航按钮 */}
          <div className="flex justify-between">
            <Button
              variant="ghost"
              onClick={handlePrevious}
              disabled={currentIndex === 0 || isSubmitting}
              className="flex items-center"
            >
              <ArrowLeft className="h-4 w-4 mr-2" />
              上一条 (←)
            </Button>
            
            <Button
              variant="ghost"
              onClick={handleNext}
              disabled={(currentIndex === contents.length - 1 && !hasMore) || isSubmitting}
              className="flex items-center"
            >
              下一条 (→)
              <ArrowRight className="h-4 w-4 ml-2" />
            </Button>
          </div>
        </CardFooter>
      </Card>
      
      {/* 键盘快捷键提示 */}
      <div className="bg-muted p-2 rounded-lg text-center text-sm text-muted-foreground">
        <span className="inline-block mx-1">↑拒绝</span>
        <span className="inline-block mx-1">↓通过</span>
        <span className="inline-block mx-1">←上一条</span>
        <span className="inline-block mx-1">→下一条</span>
        <span className="inline-block mx-1">Enter编辑</span>
        <span className="inline-block mx-1">Space暂停/播放</span>
        <span className="inline-block mx-1">Esc退出</span>
      </div>
      
      {/* 编辑对话框 */}
      <Dialog open={isEditDialogOpen} onOpenChange={setIsEditDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>编辑内容</DialogTitle>
          </DialogHeader>
          
          <div className="py-4">
            <Textarea
              value={editContent}
              onChange={(e) => setEditContent(e.target.value)}
              rows={10}
              className="font-mono"
            />
          </div>
          
          <DialogFooter>
            <Button 
              variant="outline" 
              onClick={() => setIsEditDialogOpen(false)}
              disabled={isSubmitting}
            >
              取消
            </Button>
            <Button 
              variant="default" 
              onClick={submitEdit}
              disabled={isSubmitting}
            >
              {isSubmitting ? <LoadingSpinner size="sm" /> : <Edit className="h-4 w-4 mr-2" />}
              保存并通过
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
      
      {/* 拒绝对话框 */}
      <Dialog open={isRejectDialogOpen} onOpenChange={setIsRejectDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>拒绝内容</DialogTitle>
          </DialogHeader>
          
          <div className="py-4">
            <Label htmlFor="reject-reason">拒绝原因</Label>
            <Textarea
              id="reject-reason"
              value={rejectReason}
              onChange={(e) => setRejectReason(e.target.value)}
              placeholder="请输入拒绝原因，将通知用户"
              rows={4}
            />
          </div>
          
          <DialogFooter>
            <Button 
              variant="outline" 
              onClick={() => setIsRejectDialogOpen(false)}
              disabled={isSubmitting}
            >
              取消
            </Button>
            <Button 
              variant="destructive" 
              onClick={submitReject}
              disabled={isSubmitting}
            >
              {isSubmitting ? <LoadingSpinner size="sm" /> : <XCircle className="h-4 w-4 mr-2" />}
              确认拒绝
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
}
