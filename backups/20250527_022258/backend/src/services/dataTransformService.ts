/**
 * 数据转换服务
 * 
 * 负责前后端数据格式转换，确保数据类型一致性
 */

import { User, QuestionnaireResponse, Story } from '@prisma/client';

// 前端数据类型定义
export interface FrontendUser {
  id: number;
  email: string;
  role: 'user' | 'reviewer' | 'admin' | 'superadmin';
  username?: string;
  name?: string;
  lastLoginAt?: string;
}

export interface FrontendQuestionnaireResponse {
  id: number;
  sequenceNumber: string;
  isAnonymous: boolean;
  educationLevel?: string;
  major?: string;
  graduationYear?: number;
  region?: string;
  employmentStatus?: string;
  industry?: string;
  position?: string;
  salary?: string;
  jobSatisfaction?: string;
  unemploymentDuration?: string;
  careerChangeIntention?: boolean;
  challenges?: string;
  suggestions?: string;
  createdAt: string;
  updatedAt?: string;
  ipAddress?: string;
  tags?: string[];
  status?: 'verified' | 'pending' | 'normal';
}

export interface FrontendStory {
  id: number;
  title: string;
  content: string;
  author: string;
  createdAt: string;
  likes: number;
  dislikes: number;
  tags: string[];
  category?: string;
  educationLevel?: string;
  industry?: string;
}

/**
 * 数据转换服务类
 */
export class DataTransformService {
  
  /**
   * 转换用户数据：后端 -> 前端
   */
  static transformUserToFrontend(user: User): FrontendUser {
    return {
      id: user.id,
      email: user.email,
      role: user.role as 'user' | 'reviewer' | 'admin' | 'superadmin',
      username: user.username || undefined,
      name: user.name || undefined,
      lastLoginAt: user.lastLoginAt?.toISOString()
    };
  }

  /**
   * 转换问卷回复数据：后端 -> 前端
   */
  static transformQuestionnaireToFrontend(response: QuestionnaireResponse): FrontendQuestionnaireResponse {
    return {
      id: response.id,
      sequenceNumber: response.sequenceNumber,
      isAnonymous: response.isAnonymous,
      educationLevel: response.educationLevel || undefined,
      major: response.major || undefined,
      graduationYear: response.graduationYear || undefined,
      region: response.region || undefined,
      employmentStatus: response.employmentStatus || undefined,
      industry: response.currentIndustry || undefined, // 字段映射
      position: response.currentPosition || undefined, // 字段映射
      salary: response.monthlySalary ? response.monthlySalary.toString() : undefined, // 类型转换
      jobSatisfaction: response.jobSatisfaction || undefined,
      unemploymentDuration: response.unemploymentDuration || undefined,
      careerChangeIntention: response.careerChangeIntention || undefined,
      challenges: response.observationOnEmployment || undefined, // 字段映射
      suggestions: response.adviceForStudents || undefined, // 字段映射
      createdAt: response.createdAt.toISOString(),
      updatedAt: response.updatedAt?.toISOString(),
      ipAddress: response.ipAddress || undefined,
      tags: response.tags ? JSON.parse(response.tags) : undefined, // JSON解析
      status: response.status as 'verified' | 'pending' | 'normal'
    };
  }

  /**
   * 转换故事数据：后端 -> 前端
   */
  static transformStoryToFrontend(story: Story & { user?: User }): FrontendStory {
    // 确定作者名称
    let author = story.author || '匿名用户';
    if (!story.isAnonymous && story.user) {
      author = story.user.name || story.user.username || story.user.email;
    }

    return {
      id: story.id,
      title: story.title,
      content: story.content,
      author: author,
      createdAt: story.createdAt.toISOString(),
      likes: story.likes,
      dislikes: story.dislikes,
      tags: story.tags ? JSON.parse(story.tags) : [], // JSON解析
      category: story.category || undefined,
      educationLevel: story.educationLevel || undefined,
      industry: story.industry || undefined
    };
  }

  /**
   * 转换问卷回复数据：前端 -> 后端
   */
  static transformQuestionnaireToBackend(data: Partial<FrontendQuestionnaireResponse>): Partial<QuestionnaireResponse> {
    return {
      sequenceNumber: data.sequenceNumber,
      isAnonymous: data.isAnonymous,
      educationLevel: data.educationLevel,
      major: data.major,
      graduationYear: data.graduationYear,
      region: data.region,
      employmentStatus: data.employmentStatus,
      currentIndustry: data.industry, // 字段映射
      currentPosition: data.position, // 字段映射
      monthlySalary: data.salary ? parseInt(data.salary) : undefined, // 类型转换
      jobSatisfaction: data.jobSatisfaction,
      unemploymentDuration: data.unemploymentDuration,
      careerChangeIntention: data.careerChangeIntention,
      observationOnEmployment: data.challenges, // 字段映射
      adviceForStudents: data.suggestions, // 字段映射
      ipAddress: data.ipAddress,
      tags: data.tags ? JSON.stringify(data.tags) : undefined, // JSON序列化
      status: data.status || 'normal'
    };
  }

  /**
   * 转换故事数据：前端 -> 后端
   */
  static transformStoryToBackend(data: Partial<FrontendStory>): Partial<Story> {
    return {
      title: data.title,
      content: data.content,
      author: data.author,
      tags: data.tags ? JSON.stringify(data.tags) : undefined, // JSON序列化
      category: data.category,
      educationLevel: data.educationLevel,
      industry: data.industry
    };
  }

  /**
   * 生成序列号
   */
  static generateSequenceNumber(type: 'questionnaire' | 'story', id: number): string {
    const prefix = type === 'questionnaire' ? 'Q' : 'S';
    const timestamp = new Date().getFullYear().toString().slice(-2) + 
                     (new Date().getMonth() + 1).toString().padStart(2, '0');
    return `${prefix}${timestamp}${id.toString().padStart(6, '0')}`;
  }

  /**
   * 验证数据完整性
   */
  static validateQuestionnaireData(data: Partial<FrontendQuestionnaireResponse>): string[] {
    const errors: string[] = [];
    
    if (!data.educationLevel) {
      errors.push('教育水平不能为空');
    }
    
    if (!data.region) {
      errors.push('地区不能为空');
    }
    
    if (data.graduationYear && (data.graduationYear < 1950 || data.graduationYear > new Date().getFullYear() + 10)) {
      errors.push('毕业年份不合理');
    }
    
    return errors;
  }

  /**
   * 验证故事数据
   */
  static validateStoryData(data: Partial<FrontendStory>): string[] {
    const errors: string[] = [];
    
    if (!data.title || data.title.trim().length < 5) {
      errors.push('标题至少需要5个字符');
    }
    
    if (!data.content || data.content.trim().length < 20) {
      errors.push('内容至少需要20个字符');
    }
    
    if (data.title && data.title.length > 100) {
      errors.push('标题不能超过100个字符');
    }
    
    if (data.content && data.content.length > 5000) {
      errors.push('内容不能超过5000个字符');
    }
    
    return errors;
  }

  /**
   * 清理敏感信息
   */
  static sanitizeData<T extends Record<string, any>>(data: T): T {
    const sanitized = { ...data };
    
    // 移除敏感字段
    delete sanitized.ipAddress;
    delete sanitized.userAgent;
    delete sanitized.passwordHash;
    
    return sanitized;
  }

  /**
   * 批量转换问卷回复
   */
  static transformQuestionnairesToFrontend(responses: QuestionnaireResponse[]): FrontendQuestionnaireResponse[] {
    return responses.map(response => this.transformQuestionnaireToFrontend(response));
  }

  /**
   * 批量转换故事
   */
  static transformStoriesToFrontend(stories: (Story & { user?: User })[]): FrontendStory[] {
    return stories.map(story => this.transformStoryToFrontend(story));
  }
}

export default DataTransformService;
