/**
 * 🧪 API集成测试
 * 测试模块化API的各个端点
 */

describe('API Integration Tests', () => {
  let mockContext;

  beforeEach(() => {
    mockContext = global.testUtils.createMockContext();
  });

  describe('Health Check Endpoints', () => {
    test('GET / should return API information', async () => {
      // 模拟根路径处理器
      const mockHandler = (c) => {
        const info = {
          name: 'College Employment Survey API',
          version: 'v3.0-modular-simple',
          status: 'running',
          environment: c.env?.ENVIRONMENT || 'production',
          timestamp: new Date().toISOString(),
          architecture: 'modular',
          modules: [
            'questionnaire',
            'story',
            'admin',
            'deidentification',
            'system'
          ]
        };

        return c.json({
          success: true,
          message: 'API information retrieved successfully',
          data: info,
          meta: {
            timestamp: new Date().toISOString()
          }
        });
      };

      const response = await mockHandler(mockContext);

      expect(mockContext.json).toHaveBeenCalledWith(
        expect.objectContaining({
          success: true,
          message: 'API information retrieved successfully',
          data: expect.objectContaining({
            name: 'College Employment Survey API',
            version: 'v3.0-modular-simple',
            architecture: 'modular',
            modules: expect.arrayContaining([
              'questionnaire',
              'story',
              'admin',
              'deidentification',
              'system'
            ])
          })
        })
      );
    });

    test('GET /health should return system health', async () => {
      const mockHealthHandler = (c) => {
        return c.json({
          status: 'ok',
          version: 'v3.0-modular-simple',
          checks: {
            database: 'healthy',
            memory: 'healthy',
            disk: 'healthy'
          },
          timestamp: new Date().toISOString()
        });
      };

      const response = await mockHealthHandler(mockContext);

      expect(mockContext.json).toHaveBeenCalledWith(
        expect.objectContaining({
          status: 'ok',
          version: 'v3.0-modular-simple',
          checks: expect.objectContaining({
            database: 'healthy',
            memory: 'healthy',
            disk: 'healthy'
          })
        })
      );
    });
  });

  describe('Questionnaire API', () => {
    beforeEach(() => {
      // 设置数据库模拟响应
      const mockStats = global.testUtils.createTestData.questionnaire.stats;

      mockContext.env.DB.prepare.mockImplementation((query) => {
        const mockQuery = {
          bind: jest.fn().mockReturnThis(),
          first: jest.fn(),
          all: jest.fn()
        };

        if (query.includes('COUNT(*)')) {
          mockQuery.first.mockResolvedValue({ total: mockStats.totalResponses });
        } else if (query.includes('major_display')) {
          mockQuery.all.mockResolvedValue({
            results: mockStats.majors,
            success: true
          });
        } else if (query.includes('graduation_year')) {
          mockQuery.all.mockResolvedValue({
            results: mockStats.graduationYears,
            success: true
          });
        } else if (query.includes('education_level_display')) {
          mockQuery.all.mockResolvedValue({
            results: mockStats.educationLevels,
            success: true
          });
        } else {
          mockQuery.all.mockResolvedValue({
            results: [],
            success: true
          });
          mockQuery.first.mockResolvedValue({ count: 0 });
        }

        return mockQuery;
      });
    });

    test('GET /api/questionnaire/stats should return statistics', async () => {
      // 模拟问卷统计处理器
      const mockStatsHandler = async (c) => {
        const totalResult = await c.env.DB.prepare(
          'SELECT COUNT(*) as total FROM questionnaire_responses_v2'
        ).first();
        const total = totalResult?.total || 0;

        const majorResult = await c.env.DB.prepare(`
          SELECT major_display as name, COUNT(*) as count
          FROM questionnaire_responses_v2
          WHERE major_display IS NOT NULL AND major_display != ''
          GROUP BY major_display
          ORDER BY count DESC
          LIMIT 15
        `).all();



        const statistics = {
          totalResponses: total,
          majors: majorResult?.results?.map(item => ({
            name: item.name,
            count: item.count
          })) || [],
          lastUpdated: new Date().toISOString()
        };

        return c.json({
          success: true,
          message: 'Statistics retrieved successfully',
          data: null,
          meta: {
            statistics,
            timestamp: new Date().toISOString()
          }
        });
      };

      const response = await mockStatsHandler(mockContext);

      expect(mockContext.json).toHaveBeenCalledWith(
        expect.objectContaining({
          success: true,
          message: 'Statistics retrieved successfully',
          meta: expect.objectContaining({
            statistics: expect.objectContaining({
              totalResponses: expect.any(Number),
              majors: expect.any(Array),
              lastUpdated: expect.any(String)
            })
          })
        })
      );

      // 验证数据库查询被调用
      expect(mockContext.env.DB.prepare).toHaveBeenCalledWith(
        expect.stringContaining('COUNT(*)')
      );
      expect(mockContext.env.DB.prepare).toHaveBeenCalledWith(
        expect.stringContaining('major_display')
      );
    });

    test('should handle database errors gracefully', async () => {
      // 模拟数据库错误
      mockContext.env.DB.prepare.mockImplementation(() => {
        throw new Error('Database connection failed');
      });

      const mockStatsHandler = async (c) => {
        try {
          await c.env.DB.prepare('SELECT COUNT(*) FROM questionnaire_responses_v2').first();
        } catch (error) {
          return c.json({
            success: false,
            error: error.message,
            code: 500,
            timestamp: new Date().toISOString()
          }, 500);
        }
      };

      const response = await mockStatsHandler(mockContext);

      expect(mockContext.json).toHaveBeenCalledWith(
        expect.objectContaining({
          success: false,
          error: 'Database connection failed',
          code: 500
        }),
        500
      );
    });
  });

  describe('Story API', () => {
    beforeEach(() => {
      const mockStory = global.testUtils.createTestData.story.item;

      mockContext.env.DB.prepare.mockImplementation((query) => {
        const mockQuery = {
          bind: jest.fn().mockReturnThis(),
          first: jest.fn(),
          all: jest.fn()
        };

        if (query.includes('COUNT(*)')) {
          mockQuery.first.mockResolvedValue({ total: 10 });
        } else if (query.includes('story_contents_v2')) {
          mockQuery.all.mockResolvedValue({
            results: [mockStory],
            success: true
          });
        }

        return mockQuery;
      });
    });

    test('GET /api/story/list should return paginated stories', async () => {
      // 设置查询参数
      const storyMockContext = global.testUtils.createMockContext({
        url: 'http://localhost:8787/api/story/list?page=1&pageSize=3'
      });

      // 重新设置数据库模拟，因为我们创建了新的context
      const mockStory = global.testUtils.createTestData.story.item;
      storyMockContext.env.DB.prepare.mockImplementation((query) => {
        const mockQuery = {
          bind: jest.fn().mockReturnThis(),
          first: jest.fn(),
          all: jest.fn()
        };

        if (query.includes('COUNT(*)')) {
          mockQuery.first.mockResolvedValue({ total: 10 });
        } else if (query.includes('story_contents_v2')) {
          mockQuery.all.mockResolvedValue({
            results: [mockStory],
            success: true
          });
        }

        return mockQuery;
      });

      const mockStoryHandler = async (c) => {
        const page = parseInt(c.req.query('page')) || 1;
        const limit = parseInt(c.req.query('pageSize')) || 20;
        const offset = (page - 1) * limit;

        const totalResult = await c.env.DB.prepare(
          'SELECT COUNT(*) as total FROM story_contents_v2 WHERE status = ?'
        ).bind('approved').first();
        const total = totalResult?.total || 0;

        const storiesResult = await c.env.DB.prepare(`
          SELECT * FROM story_contents_v2
          WHERE status = ?
          ORDER BY created_at DESC
          LIMIT ? OFFSET ?
        `).bind('approved', limit, offset).all();

        const stories = storiesResult.results || [];
        const pagination = {
          total,
          page,
          limit,
          pages: Math.ceil(total / limit),
          hasNext: page * limit < total,
          hasPrev: page > 1
        };

        return c.json({
          success: true,
          message: 'Stories retrieved successfully',
          data: stories,
          meta: {
            pagination,
            timestamp: new Date().toISOString()
          }
        });
      };

      const response = await mockStoryHandler(storyMockContext);

      expect(storyMockContext.json).toHaveBeenCalledWith(
        expect.objectContaining({
          success: true,
          message: 'Stories retrieved successfully',
          data: expect.any(Array),
          meta: expect.objectContaining({
            pagination: expect.objectContaining({
              total: expect.any(Number),
              page: 1,
              limit: 3,
              pages: expect.any(Number),
              hasNext: expect.any(Boolean),
              hasPrev: false
            })
          })
        })
      );
    });
  });

  describe('Error Handling', () => {
    test('should handle 404 errors', async () => {
      const mock404Handler = (c) => {
        return c.json({
          success: false,
          error: 'Endpoint not found',
          code: 404,
          details: {
            path: c.req.path || '/unknown',
            method: c.req.method || 'GET',
            availableEndpoints: [
              '/api/questionnaire/stats',
              '/api/story/list',
              '/api/system/health'
            ]
          },
          timestamp: new Date().toISOString()
        }, 404);
      };

      const response = await mock404Handler(mockContext);

      expect(mockContext.json).toHaveBeenCalledWith(
        expect.objectContaining({
          success: false,
          error: 'Endpoint not found',
          code: 404,
          details: expect.objectContaining({
            availableEndpoints: expect.any(Array)
          })
        }),
        404
      );
    });

    test('should handle validation errors', async () => {
      const mockValidationHandler = (c) => {
        return c.json({
          success: false,
          error: 'Validation failed',
          code: 400,
          validationErrors: [
            { field: 'page', message: 'Page must be a positive integer' },
            { field: 'limit', message: 'Limit must be between 1 and 100' }
          ],
          timestamp: new Date().toISOString()
        }, 400);
      };

      const response = await mockValidationHandler(mockContext);

      expect(mockContext.json).toHaveBeenCalledWith(
        expect.objectContaining({
          success: false,
          error: 'Validation failed',
          code: 400,
          validationErrors: expect.arrayContaining([
            expect.objectContaining({
              field: expect.any(String),
              message: expect.any(String)
            })
          ])
        }),
        400
      );
    });
  });
});
