import { useState, useEffect } from 'react';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { cn } from '@/lib/utils';

interface TagItem {
  tag: string;
  count: number;
  category?: string;
  color?: string;
}

interface ImprovedTagCloudProps {
  tags: TagItem[];
  selectedTags: string[];
  onTagSelect: (tag: string) => void;
  className?: string;
  maxTags?: number;
  showCounts?: boolean;
  showCategories?: boolean;
}

export default function ImprovedTagCloud({
  tags,
  selectedTags,
  onTagSelect,
  className,
  maxTags = 50,
  showCounts = true,
  showCategories = true,
}: ImprovedTagCloudProps) {
  const [displayTags, setDisplayTags] = useState<Array<TagItem & { size: number }>>(
    []
  );
  const [activeCategory, setActiveCategory] = useState('all');
  
  // 标签分类
  const categoryLabels: Record<string, string> = {
    'job': '求职相关',
    'education': '学历相关',
    'industry': '行业相关',
    'experience': '经验相关',
    'other': '其他'
  };
  
  // 标签颜色
  const categoryColors: Record<string, string> = {
    'job': 'blue',
    'education': 'green',
    'industry': 'purple',
    'experience': 'yellow',
    'other': 'gray'
  };
  
  // 提取所有分类
  const categories = ['all', ...new Set(tags.filter(t => t.category).map(t => t.category))];
  
  useEffect(() => {
    if (tags.length === 0) return;

    // 根据活动分类筛选标签
    let filteredTags = tags;
    if (activeCategory !== 'all') {
      filteredTags = tags.filter(t => t.category === activeCategory);
    }
    
    // 限制标签数量
    const limitedTags = filteredTags.slice(0, maxTags);

    // 找出最大和最小计数
    const maxCount = Math.max(...limitedTags.map((t) => t.count));
    const minCount = Math.min(...limitedTags.map((t) => t.count));
    
    // 计算每个标签的大小 (范围从 1 到 5)
    const tagSizes = limitedTags.map((tag) => {
      let size = 1;
      if (maxCount !== minCount) {
        // 将计数映射到 1-5 的范围
        size = 1 + Math.floor(((tag.count - minCount) / (maxCount - minCount)) * 4);
      }
      return {
        ...tag,
        size,
      };
    });

    // 按计数排序
    const sortedTags = [...tagSizes].sort((a, b) => b.count - a.count);
    
    setDisplayTags(sortedTags);
  }, [tags, maxTags, activeCategory]);

  // 获取标签颜色
  const getTagColor = (tag: TagItem) => {
    if (tag.color) return tag.color;
    if (tag.category) return categoryColors[tag.category] || 'gray';
    return 'gray';
  };
  
  if (tags.length === 0) {
    return (
      <div className={cn("text-center py-4", className)}>
        <p className="text-sm text-gray-500">暂无标签数据</p>
      </div>
    );
  }

  return (
    <div className={cn("space-y-4", className)}>
      {/* 分类选项卡 */}
      {showCategories && categories.length > 1 && (
        <Tabs value={activeCategory} onValueChange={setActiveCategory}>
          <TabsList className="w-full flex overflow-x-auto">
            <TabsTrigger value="all">全部</TabsTrigger>
            {categories.filter(c => c !== 'all').map(category => (
              <TabsTrigger key={category} value={category}>
                {categoryLabels[category] || category}
              </TabsTrigger>
            ))}
          </TabsList>
        </Tabs>
      )}
      
      {/* 标签云 */}
      <div className="flex flex-wrap gap-2">
        {displayTags.length === 0 ? (
          <p className="text-sm text-gray-500 w-full text-center py-4">
            该分类下暂无标签
          </p>
        ) : (
          displayTags.map((tag) => {
            const isSelected = selectedTags.includes(tag.tag);
            const color = getTagColor(tag);
            
            return (
              <Badge
                key={tag.tag}
                variant={isSelected ? "default" : "outline"}
                className={cn(
                  "cursor-pointer transition-all",
                  {
                    "text-xs": tag.size === 1,
                    "text-sm": tag.size === 2,
                    "text-base": tag.size === 3,
                    "text-lg": tag.size === 4,
                    "text-xl": tag.size === 5,
                    "bg-blue-100 text-blue-800 hover:bg-blue-200": color === 'blue' && !isSelected,
                    "bg-green-100 text-green-800 hover:bg-green-200": color === 'green' && !isSelected,
                    "bg-purple-100 text-purple-800 hover:bg-purple-200": color === 'purple' && !isSelected,
                    "bg-yellow-100 text-yellow-800 hover:bg-yellow-200": color === 'yellow' && !isSelected,
                    "bg-red-100 text-red-800 hover:bg-red-200": color === 'red' && !isSelected,
                    "bg-pink-100 text-pink-800 hover:bg-pink-200": color === 'pink' && !isSelected,
                    "bg-indigo-100 text-indigo-800 hover:bg-indigo-200": color === 'indigo' && !isSelected,
                    "bg-gray-100 text-gray-800 hover:bg-gray-200": color === 'gray' && !isSelected,
                  }
                )}
                onClick={() => onTagSelect(tag.tag)}
              >
                {tag.tag}
                {showCounts && ` (${tag.count})`}
              </Badge>
            );
          })
        )}
      </div>
    </div>
  );
}
