import { PrismaClient } from '@prisma/client';
import { D1Database } from '@cloudflare/workers-types';

// 全局变量，用于存储Prisma客户端实例
let prismaClient: PrismaClient | null = null;

/**
 * 创建适用于Cloudflare Workers环境的Prisma客户端
 *
 * 在开发环境中，使用模拟数据而不是实际连接数据库
 * 在生产环境中，使用D1数据库
 *
 * @param env 环境变量，包含数据库连接信息
 * @returns Prisma客户端实例
 */
export function getPrismaClient(env: {
  DATABASE_URL?: string;
  DB?: D1Database;
  ENVIRONMENT?: string;
  USE_MOCK_DATA?: string;
}): PrismaClient {
  // 如果已经有实例，直接返回
  if (prismaClient) {
    return prismaClient;
  }

  // 检查环境
  const isDevelopment = env.ENVIRONMENT === 'development';
  const useMockData = env.USE_MOCK_DATA === 'true';

  try {
    // 在开发环境中或明确指定使用模拟数据时，直接返回模拟客户端
    if (isDevelopment || useMockData) {
      console.log('Development mode or mock data enabled: Using mock Prisma client');
      return createMockPrismaClient();
    } else {
      // 在生产环境中，使用D1数据库
      // 注意：这里需要使用Prisma的D1适配器，目前这是一个简化的实现
      console.log('Production mode: Using D1 database');
      prismaClient = new PrismaClient({
        datasources: {
          db: {
            url: env.DATABASE_URL || 'file:./dev.db',
          },
        },
      });
    }

    return prismaClient;
  } catch (error) {
    console.error('Error initializing Prisma client:', error);

    // 如果初始化失败，返回一个模拟的Prisma客户端
    // 这将允许应用程序继续运行，但所有数据库操作都将返回模拟数据
    return createMockPrismaClient();
  }
}

/**
 * 创建一个模拟的Prisma客户端，用于开发和测试
 *
 * 这个客户端不会实际连接数据库，而是返回预定义的模拟数据
 *
 * @returns 模拟的Prisma客户端
 */
function createMockPrismaClient(): PrismaClient {
  // 创建一个基本的模拟对象，实现Prisma客户端的接口
  const mockClient = {
    // 添加所需的模型和方法
    user: createMockModel('user'),
    questionnaireResponse: createMockModel('questionnaireResponse'),
    story: createMockModel('story'),
    vote: createMockModel('vote'),
    sensitiveWord: createMockModel('sensitiveWord'),
    tag: createMockModel('tag'),

    // 连接和断开连接方法
    $connect: async () => Promise.resolve(),
    $disconnect: async () => Promise.resolve(),
  };

  // 返回模拟客户端，类型转换为PrismaClient
  return mockClient as unknown as PrismaClient;
}

/**
 * 创建模拟模型，实现基本的CRUD操作
 *
 * @param modelName 模型名称
 * @returns 模拟模型对象
 */
function createMockModel(modelName: string) {
  // 模拟数据存储
  const mockData: Record<string | number, any> = {};
  let nextId = 1;

  return {
    // 查找多个记录
    findMany: async (args?: any) => {
      console.log(`Mock ${modelName}.findMany called with:`, args);
      return Object.values(mockData);
    },

    // 查找单个记录
    findUnique: async (args?: any) => {
      console.log(`Mock ${modelName}.findUnique called with:`, args);
      const id = args?.where?.id;
      return id ? mockData[id] : null;
    },

    // 查找第一个匹配的记录
    findFirst: async (args?: any) => {
      console.log(`Mock ${modelName}.findFirst called with:`, args);
      return Object.values(mockData)[0] || null;
    },

    // 创建记录
    create: async (args?: any) => {
      console.log(`Mock ${modelName}.create called with:`, args);
      const id = nextId++;
      const data = { id, ...args?.data, createdAt: new Date(), updatedAt: new Date() };
      mockData[id] = data;
      return data;
    },

    // 更新记录
    update: async (args?: any) => {
      console.log(`Mock ${modelName}.update called with:`, args);
      const id = args?.where?.id;
      if (id && mockData[id]) {
        mockData[id] = { ...mockData[id], ...args?.data, updatedAt: new Date() };
        return mockData[id];
      }
      throw new Error(`Record with id ${id} not found`);
    },

    // 删除记录
    delete: async (args?: any) => {
      console.log(`Mock ${modelName}.delete called with:`, args);
      const id = args?.where?.id;
      if (id && mockData[id]) {
        const data = mockData[id];
        delete mockData[id];
        return data;
      }
      throw new Error(`Record with id ${id} not found`);
    },

    // 计数
    count: async (args?: any) => {
      console.log(`Mock ${modelName}.count called with:`, args);
      return Object.keys(mockData).length;
    },
  };
}
