/**
 * 🧪 Jest测试设置文件
 * 全局测试配置和工具函数
 */

// 设置测试环境变量
process.env.NODE_ENV = 'test';
process.env.ENVIRONMENT = 'test';

// 全局测试超时时间
jest.setTimeout(10000);

// 模拟Cloudflare Workers环境
global.mockCloudflareEnv = {
  DB: {
    prepare: jest.fn().mockReturnValue({
      bind: jest.fn().mockReturnThis(),
      first: jest.fn(),
      all: jest.fn(),
      run: jest.fn()
    })
  },
  KV: {
    get: jest.fn(),
    put: jest.fn(),
    delete: jest.fn(),
    list: jest.fn()
  },
  R2: {
    get: jest.fn(),
    put: jest.fn(),
    delete: jest.fn(),
    list: jest.fn()
  },
  ENVIRONMENT: 'test'
};

// 全局测试工具函数
global.testUtils = {
  // 创建模拟的Hono上下文
  createMockContext: (options = {}) => {
    const defaultOptions = {
      method: 'GET',
      url: 'http://localhost:8787/',
      headers: {},
      body: null,
      env: global.mockCloudflareEnv
    };
    
    const config = { ...defaultOptions, ...options };
    
    return {
      req: {
        method: config.method,
        url: config.url,
        header: jest.fn((name) => config.headers[name]),
        query: jest.fn((name) => {
          const url = new URL(config.url);
          return url.searchParams.get(name);
        }),
        param: jest.fn((name) => config.params?.[name]),
        json: jest.fn().mockResolvedValue(config.body)
      },
      env: config.env,
      json: jest.fn(),
      text: jest.fn(),
      html: jest.fn(),
      redirect: jest.fn(),
      set: jest.fn(),
      get: jest.fn()
    };
  },

  // 创建模拟的数据库响应
  createMockDbResponse: (data, isArray = false) => {
    if (isArray) {
      return {
        results: Array.isArray(data) ? data : [data],
        success: true,
        meta: {
          duration: Math.random() * 100,
          rows_read: Array.isArray(data) ? data.length : 1,
          rows_written: 0
        }
      };
    } else {
      return data;
    }
  },

  // 创建测试数据
  createTestData: {
    questionnaire: {
      response: {
        id: 1,
        education_level_display: '本科',
        major_display: '软件工程',
        graduation_year: 2023,
        employment_status: '已就业',
        current_industry_display: '互联网',
        region_display: '北京市',
        created_at: '2024-01-01T00:00:00.000Z'
      },
      
      stats: {
        totalResponses: 173,
        majors: [
          { name: '软件工程', count: 25 },
          { name: '电子信息工程', count: 22 },
          { name: '国际贸易', count: 17 }
        ],
        graduationYears: [
          { name: '2023', count: 5 }
        ],
        educationLevels: [
          { name: '本科', count: 120 },
          { name: '硕士', count: 40 },
          { name: '博士', count: 13 }
        ]
      }
    },

    story: {
      item: {
        id: 1,
        title: '我的求职经历',
        content: '这是一个关于求职的故事...',
        summary: '求职经历分享',
        category: '求职经历',
        education_level_display: '本科',
        industry_display: '互联网',
        likes: 10,
        views: 100,
        created_at: '2024-01-01T00:00:00.000Z',
        status: 'approved'
      }
    }
  },

  // 断言工具
  assertions: {
    // 检查API响应格式
    expectValidApiResponse: (response) => {
      expect(response).toHaveProperty('success');
      expect(response).toHaveProperty('message');
      expect(response).toHaveProperty('meta');
      expect(response.meta).toHaveProperty('timestamp');
    },

    // 检查分页响应格式
    expectValidPaginationResponse: (response) => {
      expect(response).toHaveProperty('data');
      expect(response).toHaveProperty('meta');
      expect(response.meta).toHaveProperty('pagination');
      
      const pagination = response.meta.pagination;
      expect(pagination).toHaveProperty('total');
      expect(pagination).toHaveProperty('page');
      expect(pagination).toHaveProperty('limit');
      expect(pagination).toHaveProperty('pages');
      expect(pagination).toHaveProperty('hasNext');
      expect(pagination).toHaveProperty('hasPrev');
    },

    // 检查错误响应格式
    expectValidErrorResponse: (response) => {
      expect(response).toHaveProperty('success', false);
      expect(response).toHaveProperty('error');
      expect(response).toHaveProperty('code');
      expect(response).toHaveProperty('timestamp');
    }
  }
};

// 控制台输出控制
const originalConsoleError = console.error;
const originalConsoleWarn = console.warn;

beforeEach(() => {
  // 在测试期间静默某些控制台输出
  console.error = jest.fn();
  console.warn = jest.fn();
});

afterEach(() => {
  // 恢复控制台输出
  console.error = originalConsoleError;
  console.warn = originalConsoleWarn;
  
  // 清理模拟
  jest.clearAllMocks();
});

// 全局错误处理
process.on('unhandledRejection', (reason, promise) => {
  console.error('Unhandled Rejection at:', promise, 'reason:', reason);
});

process.on('uncaughtException', (error) => {
  console.error('Uncaught Exception:', error);
});
