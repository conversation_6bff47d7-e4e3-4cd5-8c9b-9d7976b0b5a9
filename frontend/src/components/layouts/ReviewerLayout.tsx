import React, { useState, useEffect } from 'react';
import { useLocation, useNavigate } from 'react-router-dom';
import { Button } from '@/components/ui/button';
import { LogOut, User, Bell } from 'lucide-react';
import { useToast } from '@/components/ui/use-toast';
import { useAuth } from '@/contexts/AuthContext';
import SidebarMenu from '../common/SidebarMenu';
import { reviewerMenuGroups } from '@/config/menuConfig';
import '../../styles/admin/admin-layout.css';

interface ReviewerLayoutProps {
  children: React.ReactNode;
}

/**
 * 审核员专用布局组件
 *
 * 只显示审核员可访问的菜单项
 */
const ReviewerLayout: React.FC<ReviewerLayoutProps> = ({ children }) => {
  const location = useLocation();
  const navigate = useNavigate();
  const { toast } = useToast();
  const { user } = useAuth();
  const [userName, setUserName] = useState<string>('审核员');
  const [pendingCount, setPendingCount] = useState<number>(0);

  // 检查管理员或超级管理员访问审核员页面的情况，重定向到对应的管理页面
  useEffect(() => {
    if (user?.role === 'admin') {
      console.log('ReviewerLayout - 检测到管理员访问审核员页面，重定向到管理员页面');
      navigate('/admin/dashboard', { replace: true });
      return;
    }

    if (user?.role === 'superadmin') {
      console.log('ReviewerLayout - 检测到超级管理员访问审核员页面，重定向到超级管理员页面');
      navigate('/superadmin/dashboard', { replace: true });
      return;
    }
  }, [location.pathname, user?.role, navigate]);

  // 获取用户信息
  useEffect(() => {
    const userJson = localStorage.getItem('adminUser');
    if (userJson) {
      try {
        const user = JSON.parse(userJson);
        setUserName(user.name || '审核员');
      } catch (error) {
        console.error('解析用户信息失败:', error);
      }
    }

    // 模拟获取待审核内容数量
    setPendingCount(Math.floor(Math.random() * 10) + 1);
  }, []);

  // 处理登出
  const handleLogout = () => {
    localStorage.removeItem('adminToken');
    localStorage.removeItem('adminUser');

    toast({
      title: '已登出',
      description: '您已成功退出审核员账号',
    });

    // 使用window.location.href进行页面跳转，确保页面刷新
    window.location.href = '/admin/login';
  };

  return (
    <div className="admin-layout">
      <header className="admin-header" style={{ backgroundColor: '#1e3a8a' }}>
        <div className="logo">
          <h1>就业调查审核系统 - 审核员专区</h1>
        </div>
        <div className="user-info">
          <div className="flex items-center">
            <Bell className="h-5 w-5 mr-2" />
            <span className="bg-red-500 text-white rounded-full px-2 py-0.5 text-xs mr-4">
              {pendingCount}
            </span>
          </div>
          <div className="flex items-center ml-4">
            <User className="h-4 w-4 mr-2" />
            <span className="mr-4">{userName} (审核员)</span>
            <Button
              variant="ghost"
              size="sm"
              onClick={handleLogout}
              className="flex items-center"
            >
              <LogOut className="h-4 w-4 mr-1" />
              登出
            </Button>
          </div>
        </div>
      </header>

      <div className="admin-content">
        <div className="sidebar">
          <div className="sidebar-header" style={{ backgroundColor: '#1e3a8a' }}>
            <h2>审核员专用后台</h2>
          </div>

          <SidebarMenu
            groups={reviewerMenuGroups}
            role="reviewer"
          />
        </div>

        <div className="main-content">
          {children}
        </div>
      </div>
    </div>
  );
};

export default ReviewerLayout;
