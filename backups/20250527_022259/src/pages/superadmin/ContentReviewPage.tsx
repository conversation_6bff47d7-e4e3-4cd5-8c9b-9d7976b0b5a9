import React, { Suspense } from 'react';
import SuperAdminLayout from '@/components/layouts/SuperAdminLayout';
import ContentReviewPanel from '@/components/admin/ContentReviewPanel';
import CommentReviewPanel from '@/components/admin/CommentReviewPanel';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Shield, Settings, MessageSquare, BarChart3, Users, TrendingUp } from 'lucide-react';
import ReviewSettingsPanel from '@/components/admin/ReviewSettingsPanel';
import ModerationHistoryTable from '@/components/admin/ModerationHistoryTable';
import ModerationStatsDashboard from '@/components/admin/ModerationStatsDashboard';
import ReviewerPerformanceDashboard from '@/components/admin/ReviewerPerformanceDashboard';
import ContentQualityTrendsDashboard from '@/components/admin/ContentQualityTrendsDashboard';
import { LoadingSpinner } from '@/components/ui/loading-spinner';
import ErrorBoundary from '@/components/ErrorBoundary';

/**
 * 超级管理员内容审核页面
 * 
 * 提供完整的内容审核功能，包括：
 * - 内容审核面板
 * - 评论审核面板
 * - 审核历史记录
 * - 审核统计分析
 * - 审核员绩效分析
 * - 内容质量趋势分析
 * - 审核设置管理
 */
const SuperAdminContentReviewPage: React.FC = () => {
  return (
    <SuperAdminLayout>
      <div className="container mx-auto py-6">
        <div className="flex justify-between items-center mb-6">
          <div>
            <h1 className="text-2xl font-bold">内容审核管理</h1>
            <p className="text-gray-500 mt-1">管理所有内容审核流程、统计分析和系统设置</p>
          </div>
        </div>

        <Tabs defaultValue="review" className="space-y-6">
          <TabsList className="grid w-full grid-cols-7">
            <TabsTrigger value="review" className="flex items-center gap-2">
              <Shield className="h-4 w-4" />
              内容审核
            </TabsTrigger>
            <TabsTrigger value="comments" className="flex items-center gap-2">
              <MessageSquare className="h-4 w-4" />
              评论审核
            </TabsTrigger>
            <TabsTrigger value="history" className="flex items-center gap-2">
              <BarChart3 className="h-4 w-4" />
              审核历史
            </TabsTrigger>
            <TabsTrigger value="stats" className="flex items-center gap-2">
              <BarChart3 className="h-4 w-4" />
              统计分析
            </TabsTrigger>
            <TabsTrigger value="reviewer" className="flex items-center gap-2">
              <Users className="h-4 w-4" />
              审核员绩效
            </TabsTrigger>
            <TabsTrigger value="trends" className="flex items-center gap-2">
              <TrendingUp className="h-4 w-4" />
              质量趋势
            </TabsTrigger>
            <TabsTrigger value="settings" className="flex items-center gap-2">
              <Settings className="h-4 w-4" />
              审核设置
            </TabsTrigger>
          </TabsList>

          <TabsContent value="review" className="mt-6">
            <Suspense fallback={<div className="flex justify-center py-12"><LoadingSpinner size="lg" text="加载内容审核面板..." /></div>}>
              <ErrorBoundary fallback={<div className="p-4 border border-red-300 bg-red-50 rounded-md text-red-800">内容审核面板加载失败</div>}>
                <ContentReviewPanel />
              </ErrorBoundary>
            </Suspense>
          </TabsContent>

          <TabsContent value="comments" className="mt-6">
            <Suspense fallback={<div className="flex justify-center py-12"><LoadingSpinner size="lg" text="加载评论审核面板..." /></div>}>
              <ErrorBoundary fallback={<div className="p-4 border border-red-300 bg-red-50 rounded-md text-red-800">评论审核面板加载失败</div>}>
                <CommentReviewPanel />
              </ErrorBoundary>
            </Suspense>
          </TabsContent>

          <TabsContent value="history" className="mt-6">
            <Suspense fallback={<div className="flex justify-center py-12"><LoadingSpinner size="lg" text="加载审核历史..." /></div>}>
              <ErrorBoundary fallback={<div className="p-4 border border-red-300 bg-red-50 rounded-md text-red-800">审核历史加载失败</div>}>
                <ModerationHistoryTable />
              </ErrorBoundary>
            </Suspense>
          </TabsContent>

          <TabsContent value="stats" className="mt-6">
            <Suspense fallback={<div className="flex justify-center py-12"><LoadingSpinner size="lg" text="加载统计分析..." /></div>}>
              <ErrorBoundary fallback={<div className="p-4 border border-red-300 bg-red-50 rounded-md text-red-800">统计分析加载失败</div>}>
                <ModerationStatsDashboard />
              </ErrorBoundary>
            </Suspense>
          </TabsContent>

          <TabsContent value="reviewer" className="mt-6">
            <Suspense fallback={<div className="flex justify-center py-12"><LoadingSpinner size="lg" text="加载审核员绩效..." /></div>}>
              <ErrorBoundary fallback={<div className="p-4 border border-red-300 bg-red-50 rounded-md text-red-800">审核员绩效加载失败</div>}>
                <ReviewerPerformanceDashboard />
              </ErrorBoundary>
            </Suspense>
          </TabsContent>

          <TabsContent value="trends" className="mt-6">
            <Suspense fallback={<div className="flex justify-center py-12"><LoadingSpinner size="lg" text="加载质量趋势..." /></div>}>
              <ErrorBoundary fallback={<div className="p-4 border border-red-300 bg-red-50 rounded-md text-red-800">质量趋势加载失败</div>}>
                <ContentQualityTrendsDashboard />
              </ErrorBoundary>
            </Suspense>
          </TabsContent>

          <TabsContent value="settings" className="mt-6">
            <Suspense fallback={<div className="flex justify-center py-12"><LoadingSpinner size="lg" text="加载审核设置..." /></div>}>
              <ErrorBoundary fallback={<div className="p-4 border border-red-300 bg-red-50 rounded-md text-red-800">审核设置加载失败</div>}>
                <ReviewSettingsPanel />
              </ErrorBoundary>
            </Suspense>
          </TabsContent>
        </Tabs>
      </div>
    </SuperAdminLayout>
  );
};

export default SuperAdminContentReviewPage;
