import { useState, useRef, useEffect } from 'react';
import { <PERSON>, <PERSON>Content, <PERSON><PERSON>ooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import {
  ThumbsUp,
  ThumbsDown,
  Calendar,
  User,
  Tag as TagIcon,
  Bookmark,
  MessageSquare,
  Share2,
  Eye,
  Award,
  Clock,
  ChevronDown,
  ChevronUp
} from 'lucide-react';
import { formatDate, truncateText, cn } from '@/lib/utils';
import { useToast } from '@/components/ui/use-toast';
import { voteStory } from '@/lib/api';
import { motion, AnimatePresence } from 'framer-motion';
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip';

// Map tag IDs to labels and colors
const TAG_LABELS: Record<string, { label: string; color?: string; category?: string }> = {
  'career-change': { label: '转行经历', color: 'yellow', category: 'experience' },
  'job-hunting': { label: '求职故事', color: 'blue', category: 'job' },
  'interview': { label: '面试经验', color: 'blue', category: 'job' },
  'salary': { label: '薪资谈判', color: 'blue', category: 'job' },
  'work-life': { label: '工作生活', color: 'yellow', category: 'experience' },
  'advice': { label: '建议分享', color: 'yellow', category: 'experience' },
  'internship': { label: '实习经历', color: 'yellow', category: 'experience' },
  'overseas': { label: '海外就业', color: 'yellow', category: 'experience' },
  'startup': { label: '创业经历', color: 'yellow', category: 'experience' },
  'bachelor': { label: '本科经验', color: 'green', category: 'education' },
  'master': { label: '硕士经验', color: 'green', category: 'education' },
  'phd': { label: '博士经验', color: 'green', category: 'education' },
  'it-industry': { label: 'IT行业', color: 'purple', category: 'industry' },
  'finance': { label: '金融行业', color: 'purple', category: 'industry' },
  'education-industry': { label: '教育行业', color: 'purple', category: 'industry' },
};

// Map category IDs to labels and colors
const CATEGORY_LABELS: Record<string, { label: string; color: string }> = {
  'success': { label: '成功故事', color: 'green' },
  'challenge': { label: '挑战经历', color: 'amber' },
  'advice': { label: '建议分享', color: 'blue' },
  'experience': { label: '经验分享', color: 'indigo' },
};

// Map education level IDs to labels
const EDUCATION_LABELS: Record<string, string> = {
  'high-school': '高中及以下',
  'college': '专科',
  'bachelor': '本科',
  'master': '硕士',
  'phd': '博士',
};

// Map industry IDs to labels
const INDUSTRY_LABELS: Record<string, string> = {
  'it': '互联网/IT',
  'finance': '金融',
  'education': '教育',
  'healthcare': '医疗',
  'manufacturing': '制造业',
  'service': '服务业',
  'government': '政府/公共服务',
  'media': '媒体/文化',
};

interface Story {
  id: number;
  title: string;
  content: string;
  author: string | { id: number; name: string };
  createdAt: string;
  likes: number;
  dislikes?: number;
  tags?: string[];
  category?: string;
  educationLevel?: string;
  industry?: string;
  views?: number;
}

interface ImprovedStoryCardProps {
  story: Story;
  onClick?: () => void;
  searchQuery?: string; // 用于高亮搜索关键词
  compact?: boolean; // 紧凑模式
  featured?: boolean; // 特色故事
  className?: string;
  showAnimation?: boolean; // 是否显示动画
}

export default function ImprovedStoryCard({
  story,
  onClick,
  searchQuery = '',
  compact = false,
  featured = false,
  className,
  showAnimation = true
}: ImprovedStoryCardProps) {
  const { toast } = useToast();
  const [isExpanded, setIsExpanded] = useState(featured);
  const [likes, setLikes] = useState(story.likes || 0);
  const [dislikes, setDislikes] = useState(story.dislikes || 0);
  const [userVote, setUserVote] = useState<'like' | 'dislike' | null>(null);
  const [isVoting, setIsVoting] = useState(false);
  const [isHovered, setIsHovered] = useState(false);
  const contentRef = useRef<HTMLParagraphElement>(null);
  const [isContentOverflowing, setIsContentOverflowing] = useState(false);

  // 计算截断长度
  const truncateLength = compact ? 100 : 200;

  // 检查内容是否溢出
  useEffect(() => {
    if (contentRef.current) {
      const isOverflowing = story.content.length > truncateLength;
      setIsContentOverflowing(isOverflowing);
    }
  }, [story.content, truncateLength]);

  // 处理内容高亮
  const highlightContent = (content: string) => {
    if (!searchQuery) return content;

    try {
      const regex = new RegExp(`(${searchQuery})`, 'gi');
      return content.replace(regex, '<mark class="bg-yellow-100 rounded px-1">$1</mark>');
    } catch (e) {
      // 如果正则表达式无效，返回原始内容
      return content;
    }
  };

  // 处理HTML内容
  const createMarkup = (content: string) => {
    return { __html: highlightContent(content) };
  };

  // Truncate content if not expanded
  const displayContent = isExpanded
    ? story.content
    : truncateText(story.content, truncateLength);

  // Toggle content expansion
  const toggleExpand = (e: React.MouseEvent) => {
    e.stopPropagation();
    setIsExpanded(!isExpanded);
  };

  // Handle card click
  const handleCardClick = () => {
    if (onClick) {
      onClick();
    }
  };

  // Handle mouse enter
  const handleMouseEnter = () => {
    setIsHovered(true);
  };

  // Handle mouse leave
  const handleMouseLeave = () => {
    setIsHovered(false);
  };

  // Handle voting
  const handleVote = async (e: React.MouseEvent, voteType: 'like' | 'dislike') => {
    e.stopPropagation();

    if (isVoting || (userVote === voteType)) {
      return;
    }

    try {
      setIsVoting(true);

      // Optimistic update
      if (userVote === null) {
        // First vote
        if (voteType === 'like') {
          setLikes(likes + 1);
        } else {
          setDislikes(dislikes + 1);
        }
      } else {
        // Changing vote
        if (voteType === 'like') {
          setLikes(likes + 1);
          setDislikes(dislikes - 1);
        } else {
          setLikes(likes - 1);
          setDislikes(dislikes + 1);
        }
      }

      setUserVote(voteType);

      // Call API
      const response = await voteStory({
        storyId: story.id,
        voteType,
      });

      // Update with actual values from server
      if (response.success) {
        setLikes(response.likes);
        setDislikes(response.dislikes);
      } else {
        // Revert optimistic update on error
        setLikes(story.likes || 0);
        setDislikes(story.dislikes || 0);
        setUserVote(null);

        toast({
          title: '投票失败',
          description: '请稍后重试',
          variant: 'destructive',
        });
      }
    } catch (error) {
      console.error('Error voting:', error);

      // Revert optimistic update on error
      setLikes(story.likes || 0);
      setDislikes(story.dislikes || 0);
      setUserVote(null);

      toast({
        title: '投票失败',
        description: '发生错误，请稍后重试',
        variant: 'destructive',
      });
    } finally {
      setIsVoting(false);
    }
  };

  // 渲染标签
  const renderTags = () => {
    // 确保 tags 是一个数组
    const tags = story.tags || [];

    return (
      <div className="flex flex-wrap gap-1.5 mt-3">
        {tags.slice(0, compact ? 2 : undefined).map((tag) => {
          const tagInfo = TAG_LABELS[tag] || { label: tag };
          const colorClass = tagInfo.color ? `bg-${tagInfo.color}-50 text-${tagInfo.color}-700 border-${tagInfo.color}-200` : '';

          return (
            <Badge
              key={tag}
              variant="outline"
              className={cn(
                "text-xs py-0.5 px-2 rounded-full transition-all",
                colorClass,
                "hover:bg-opacity-80"
              )}
            >
              <TagIcon className="h-3 w-3 mr-1" />
              {tagInfo.label}
            </Badge>
          );
        })}

        {compact && tags.length > 2 && (
          <Badge variant="outline" className="text-xs py-0.5 px-2 rounded-full">
            +{tags.length - 2}
          </Badge>
        )}
      </div>
    );
  };

  // 渲染卡片内容
  const cardContent = (
    <>
      <CardHeader className={cn(
        "pb-2 space-y-1",
        compact ? "p-4" : "p-5",
        featured && "bg-gradient-to-r from-blue-50 to-indigo-50"
      )}>
        <div className="flex items-start justify-between gap-2">
          <CardTitle className={cn(
            "text-lg font-bold leading-tight",
            featured && "text-xl",
            "line-clamp-2"
          )}>
            {searchQuery ? (
              <span dangerouslySetInnerHTML={createMarkup(story.title)} />
            ) : (
              story.title
            )}
          </CardTitle>

          {featured && (
            <Badge variant="default" className="ml-auto shrink-0 bg-gradient-to-r from-blue-600 to-indigo-600">
              <Award className="h-3 w-3 mr-1" />
              精选
            </Badge>
          )}
        </div>

        {story.category && (
          <div className="flex items-center">
            <Badge
              variant="outline"
              className={cn(
                "text-xs py-0.5 rounded-full",
                `bg-${CATEGORY_LABELS[story.category]?.color || 'gray'}-50`,
                `text-${CATEGORY_LABELS[story.category]?.color || 'gray'}-700`,
                `border-${CATEGORY_LABELS[story.category]?.color || 'gray'}-200`
              )}
            >
              {CATEGORY_LABELS[story.category]?.label || story.category}
            </Badge>
          </div>
        )}

        {renderTags()}
      </CardHeader>

      <CardContent className={cn(
        "flex-grow relative",
        compact ? "p-4 pt-2" : "p-5 pt-2"
      )}>
        <div
          ref={contentRef}
          className={cn(
            "text-sm text-gray-700 whitespace-pre-line",
            featured && "text-base",
            !isExpanded && isContentOverflowing && "relative"
          )}
        >
          {searchQuery ? (
            <div dangerouslySetInnerHTML={createMarkup(displayContent)} />
          ) : (
            displayContent
          )}

          {!isExpanded && isContentOverflowing && (
            <div className="absolute bottom-0 left-0 right-0 h-8 bg-gradient-to-t from-white to-transparent"></div>
          )}
        </div>

        {isContentOverflowing && (
          <Button
            variant="ghost"
            size="sm"
            className="mt-1 h-8 text-xs font-medium flex items-center gap-1 text-blue-600 hover:text-blue-700 hover:bg-blue-50 transition-colors"
            onClick={toggleExpand}
          >
            {isExpanded ? (
              <>
                <ChevronUp className="h-3 w-3" />
                收起
              </>
            ) : (
              <>
                <ChevronDown className="h-3 w-3" />
                展开全文
              </>
            )}
          </Button>
        )}
      </CardContent>

      <CardFooter className={cn(
        "flex justify-between items-center pt-3 border-t",
        compact ? "p-4 pt-3" : "p-5 pt-3"
      )}>
        <div className="flex items-center text-xs text-gray-500 gap-3">
          <div className="flex items-center">
            <User className="h-3 w-3 mr-1 text-gray-400" />
            <span>{typeof story.author === 'string' ? story.author : story.author.name}</span>
          </div>

          <div className="flex items-center">
            <Clock className="h-3 w-3 mr-1 text-gray-400" />
            <span>{formatDate(new Date(story.createdAt))}</span>
          </div>

          {story.views !== undefined && (
            <div className="flex items-center">
              <Eye className="h-3 w-3 mr-1 text-gray-400" />
              <span>{story.views}</span>
            </div>
          )}
        </div>

        <div className="flex items-center gap-1">
          <TooltipProvider>
            <Tooltip>
              <TooltipTrigger asChild>
                <Button
                  variant="ghost"
                  size="sm"
                  className={cn(
                    "flex items-center gap-1 px-2 h-8 rounded-full",
                    userVote === 'like' ? "bg-green-100 text-green-700" : "hover:bg-gray-100"
                  )}
                  onClick={(e) => handleVote(e, 'like')}
                  disabled={isVoting}
                >
                  <ThumbsUp className="h-3.5 w-3.5" />
                  <span className="text-xs font-medium">{likes}</span>
                </Button>
              </TooltipTrigger>
              <TooltipContent>
                <p>点赞</p>
              </TooltipContent>
            </Tooltip>
          </TooltipProvider>

          <TooltipProvider>
            <Tooltip>
              <TooltipTrigger asChild>
                <Button
                  variant="ghost"
                  size="sm"
                  className={cn(
                    "flex items-center gap-1 px-2 h-8 rounded-full",
                    userVote === 'dislike' ? "bg-red-100 text-red-700" : "hover:bg-gray-100"
                  )}
                  onClick={(e) => handleVote(e, 'dislike')}
                  disabled={isVoting}
                >
                  <ThumbsDown className="h-3.5 w-3.5" />
                  <span className="text-xs font-medium">{dislikes}</span>
                </Button>
              </TooltipTrigger>
              <TooltipContent>
                <p>不喜欢</p>
              </TooltipContent>
            </Tooltip>
          </TooltipProvider>

          {!compact && (
            <TooltipProvider>
              <Tooltip>
                <TooltipTrigger asChild>
                  <Button
                    variant="ghost"
                    size="sm"
                    className="flex items-center gap-1 px-2 h-8 rounded-full hover:bg-gray-100"
                    onClick={(e) => {
                      e.stopPropagation();
                      // 分享功能
                    }}
                  >
                    <Share2 className="h-3.5 w-3.5" />
                  </Button>
                </TooltipTrigger>
                <TooltipContent>
                  <p>分享</p>
                </TooltipContent>
              </Tooltip>
            </TooltipProvider>
          )}
        </div>
      </CardFooter>
    </>
  );

  // 如果不需要动画，直接返回卡片
  if (!showAnimation) {
    return (
      <Card
        className={cn(
          "h-full flex flex-col cursor-pointer transition-all duration-300 overflow-hidden",
          isHovered ? "shadow-lg" : "shadow-sm",
          featured && "border-primary/30 border-2",
          compact && "max-h-[350px]",
          className
        )}
        onClick={handleCardClick}
        onMouseEnter={handleMouseEnter}
        onMouseLeave={handleMouseLeave}
      >
        {cardContent}
      </Card>
    );
  }

  // 带动画的卡片
  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{
        duration: 0.4,
        ease: [0.25, 0.1, 0.25, 1.0]
      }}
      whileHover={{
        scale: featured ? 1.01 : 1.03,
        transition: { duration: 0.2 }
      }}
      className="h-full"
    >
      <Card
        className={cn(
          "h-full flex flex-col cursor-pointer transition-all duration-300 overflow-hidden",
          isHovered ? "shadow-lg" : "shadow-sm",
          featured && "border-primary/30 border-2",
          compact && "max-h-[350px]",
          className
        )}
        onClick={handleCardClick}
        onMouseEnter={handleMouseEnter}
        onMouseLeave={handleMouseLeave}
      >
        {cardContent}
      </Card>
    </motion.div>
  );
}
