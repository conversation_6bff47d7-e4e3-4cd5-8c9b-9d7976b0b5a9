const http = require('http');
const url = require('url');

const PORT = 8788;

// Define admin users with different roles
const adminUsers = [
  {
    username: 'admin1',
    password: 'admin123',
    role: 'admin',
    name: '管理员',
    id: 1,
    permissions: ['content_review', 'user_management', 'data_analysis']
  },
  {
    username: 'reviewer1',
    password: 'admin123',
    role: 'reviewer',
    name: '审核员',
    id: 2,
    permissions: ['content_review']
  },
  {
    username: 'superadmin',
    password: 'admin123',
    role: 'superadmin',
    name: '超级管理员',
    id: 3,
    permissions: ['content_review', 'user_management', 'data_analysis', 'system_config', 'security_management']
  }
];

// Mock reviewer users data
let reviewerUsers = [
  {
    id: 101,
    uuid: 'reviewer-2025-001',
    username: 'reviewer_zhang',
    email: '<EMAIL>',
    name: '张审核员',
    status: 'active',
    role: 'reviewer',
    created_at: '2025-01-01T00:00:00Z',
    lastLoginAt: '2025-05-25T10:30:00Z',
    lastLoginIp: '*************',
    submissionCount: 45,
    specialties: ['故事审核', '问卷审核']
  },
  {
    id: 102,
    uuid: 'reviewer-2025-002',
    username: 'reviewer_li',
    email: '<EMAIL>',
    name: '李审核员',
    status: 'active',
    role: 'reviewer',
    created_at: '2025-01-15T00:00:00Z',
    lastLoginAt: '2025-05-24T16:45:00Z',
    lastLoginIp: '*************',
    submissionCount: 32,
    specialties: ['内容安全', '问卷审核']
  },
  {
    id: 103,
    uuid: 'reviewer-2025-003',
    username: 'reviewer_wang',
    email: '<EMAIL>',
    name: '王审核员',
    status: 'inactive',
    role: 'reviewer',
    created_at: '2025-02-01T00:00:00Z',
    lastLoginAt: '2025-05-20T09:15:00Z',
    lastLoginIp: '*************',
    submissionCount: 18,
    specialties: ['故事审核']
  },
  {
    id: 104,
    uuid: 'reviewer-2025-004',
    username: 'reviewer_chen',
    email: '<EMAIL>',
    name: '陈审核员',
    status: 'active',
    role: 'reviewer',
    created_at: '2025-02-15T00:00:00Z',
    lastLoginAt: '2025-05-25T08:20:00Z',
    lastLoginIp: '*************',
    submissionCount: 67,
    specialties: ['故事审核', '问卷审核', '内容安全']
  },
  {
    id: 105,
    uuid: 'reviewer-2025-005',
    username: 'reviewer_liu',
    email: '<EMAIL>',
    name: '刘审核员',
    status: 'suspended',
    role: 'reviewer',
    created_at: '2025-03-01T00:00:00Z',
    lastLoginAt: '2025-05-18T14:30:00Z',
    lastLoginIp: '*************',
    submissionCount: 23,
    specialties: ['问卷审核']
  }
];

// Mock admin users data (separate from login users)
let adminUsersList = [
  {
    id: 201,
    uuid: 'admin-2025-001',
    username: 'admin_zhang',
    email: '<EMAIL>',
    name: '张管理员',
    status: 'active',
    role: 'admin',
    created_at: '2024-12-01T00:00:00Z',
    lastLoginAt: '2025-05-25T09:15:00Z',
    lastLoginIp: '*************',
    operationCount: 156,
    permissions: ['USER_MANAGEMENT', 'CONTENT_REVIEW', 'DATA_ANALYSIS'],
    lastOperationAt: '2025-05-25T08:45:00Z'
  },
  {
    id: 202,
    uuid: 'admin-2025-002',
    username: 'admin_li',
    email: '<EMAIL>',
    name: '李管理员',
    status: 'active',
    role: 'admin',
    created_at: '2024-12-15T00:00:00Z',
    lastLoginAt: '2025-05-24T14:20:00Z',
    lastLoginIp: '*************',
    operationCount: 89,
    permissions: ['USER_MANAGEMENT', 'CONTENT_REVIEW'],
    lastOperationAt: '2025-05-24T13:30:00Z'
  },
  {
    id: 203,
    uuid: 'superadmin-2025-001',
    username: 'superadmin_wang',
    email: '<EMAIL>',
    name: '王超级管理员',
    status: 'active',
    role: 'superadmin',
    created_at: '2024-11-01T00:00:00Z',
    lastLoginAt: '2025-05-25T11:00:00Z',
    lastLoginIp: '*************',
    operationCount: 234,
    permissions: ['ALL'],
    lastOperationAt: '2025-05-25T10:30:00Z'
  },
  {
    id: 204,
    uuid: 'admin-2025-003',
    username: 'admin_chen',
    email: '<EMAIL>',
    name: '陈管理员',
    status: 'inactive',
    role: 'admin',
    created_at: '2025-01-10T00:00:00Z',
    lastLoginAt: '2025-05-20T16:45:00Z',
    lastLoginIp: '*************',
    operationCount: 45,
    permissions: ['CONTENT_REVIEW', 'DATA_ANALYSIS'],
    lastOperationAt: '2025-05-20T15:20:00Z'
  }
];

// Mock tags data for story wall
let storyTags = [
  {
    id: 'tag-001',
    name: '职场成长',
    description: '关于职业发展和成长的故事',
    color: '#3B82F6',
    category: 'topic',
    usageCount: 45,
    createdAt: '2024-12-01T00:00:00Z',
    isSystem: false
  },
  {
    id: 'tag-002',
    name: '人际关系',
    description: '关于人际交往和关系处理的故事',
    color: '#EF4444',
    category: 'topic',
    usageCount: 32,
    createdAt: '2024-12-02T00:00:00Z',
    isSystem: false
  },
  {
    id: 'tag-003',
    name: '学习经历',
    description: '关于学习和教育的故事',
    color: '#10B981',
    category: 'topic',
    usageCount: 28,
    createdAt: '2024-12-03T00:00:00Z',
    isSystem: false
  },
  {
    id: 'tag-004',
    name: '创业故事',
    description: '关于创业和商业的故事',
    color: '#F59E0B',
    category: 'topic',
    usageCount: 23,
    createdAt: '2024-12-04T00:00:00Z',
    isSystem: false
  },
  {
    id: 'tag-005',
    name: '感动',
    description: '令人感动的故事',
    color: '#8B5CF6',
    category: 'emotion',
    usageCount: 67,
    createdAt: '2024-12-05T00:00:00Z',
    isSystem: false
  },
  {
    id: 'tag-006',
    name: '励志',
    description: '激励人心的故事',
    color: '#EC4899',
    category: 'emotion',
    usageCount: 89,
    createdAt: '2024-12-06T00:00:00Z',
    isSystem: false
  },
  {
    id: 'tag-007',
    name: '温暖',
    description: '温暖人心的故事',
    color: '#06B6D4',
    category: 'emotion',
    usageCount: 54,
    createdAt: '2024-12-07T00:00:00Z',
    isSystem: false
  },
  {
    id: 'tag-008',
    name: '科技',
    description: '科技相关的故事',
    color: '#84CC16',
    category: 'general',
    usageCount: 34,
    createdAt: '2024-12-08T00:00:00Z',
    isSystem: false
  },
  {
    id: 'tag-009',
    name: '推荐',
    description: '系统推荐的优质故事',
    color: '#F97316',
    category: 'quality',
    usageCount: 12,
    createdAt: '2024-12-09T00:00:00Z',
    isSystem: true
  },
  {
    id: 'tag-010',
    name: '热门',
    description: '热门故事标签',
    color: '#DC2626',
    category: 'system',
    usageCount: 156,
    createdAt: '2024-12-10T00:00:00Z',
    isSystem: true
  }
];

// Helper function to parse JSON body
function parseBody(req) {
  return new Promise((resolve, reject) => {
    let body = '';
    req.on('data', chunk => {
      body += chunk.toString();
    });
    req.on('end', () => {
      try {
        resolve(JSON.parse(body));
      } catch (error) {
        reject(error);
      }
    });
  });
}

// Helper function to send JSON response
function sendJSON(res, statusCode, data) {
  res.writeHead(statusCode, {
    'Content-Type': 'application/json',
    'Access-Control-Allow-Origin': '*',
    'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
    'Access-Control-Allow-Headers': 'Content-Type, Authorization',
    'Access-Control-Max-Age': '86400'
  });
  res.end(JSON.stringify(data));
}

// Create HTTP server
const server = http.createServer(async (req, res) => {
  const parsedUrl = url.parse(req.url, true);
  const path = parsedUrl.pathname;
  const method = req.method;

  console.log(`${method} ${path}`);

  // Handle CORS preflight
  if (method === 'OPTIONS') {
    res.writeHead(200, {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type, Authorization',
      'Access-Control-Max-Age': '86400'
    });
    res.end();
    return;
  }

  try {
    // Health check
    if (path === '/health' && method === 'GET') {
      sendJSON(res, 200, {
        status: 'ok',
        environment: 'development',
        timestamp: new Date().toISOString(),
        service: 'simple-http-server'
      });
      return;
    }

    // Admin login
    if (path === '/api/admin/login' && method === 'POST') {
      try {
        const body = await parseBody(req);
        const { username, password } = body;

        console.log(`🔐 管理员登录尝试: ${username}`);

        // Find matching user
        const user = adminUsers.find(u => u.username === username && u.password === password);

        if (user) {
          // Generate simple token
          const token = `token_${user.role}_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;

          console.log(`✅ 登录成功: ${user.name} (${user.role})`);

          sendJSON(res, 200, {
            success: true,
            data: {
              token,
              user: {
                id: user.id,
                username: user.username,
                name: user.name,
                role: user.role,
                permissions: user.permissions
              }
            },
            message: '登录成功'
          });
          return;
        }

        console.log(`❌ 登录失败: 用户名或密码错误`);
        sendJSON(res, 401, {
          success: false,
          error: '用户名或密码错误'
        });
        return;
      } catch (error) {
        console.error('解析请求体失败:', error);
        sendJSON(res, 400, {
          success: false,
          error: '请求格式错误'
        });
        return;
      }
    }

    // Admin dashboard stats
    if (path === '/api/admin/dashboard/stats' && method === 'GET') {
      console.log(`📊 获取仪表盘统计数据`);

      sendJSON(res, 200, {
        success: true,
        data: {
          totalUsers: 22,
          activeUsers: 18,
          totalStories: 40,
          pendingStories: 30,
          totalResponses: 100,
          totalQuestionnaireVoices: 85, // 问卷心声总数
          todayUsers: 5,
          todayStories: 8,
          todayResponses: 15,
          todayQuestionnaireVoices: 12, // 今日新增问卷心声
          securityAlerts: 0,
          testDataStatus: 'loaded'
        }
      });
      return;
    }

    // Test data status
    if (path === '/api/admin/test-data/status' && method === 'GET') {
      console.log(`🧪 检查测试数据状态`);

      sendJSON(res, 200, {
        success: true,
        data: {
          isLoaded: true
        }
      });
      return;
    }

    // Admin roles
    if (path === '/api/admin/roles' && method === 'GET') {
      console.log(`👥 获取角色列表`);

      sendJSON(res, 200, {
        success: true,
        data: [
          {
            id: 'admin',
            name: '管理员',
            description: '系统管理员，拥有大部分管理权限',
            permissions: [
              { id: 'USER_MANAGEMENT', name: '用户管理' },
              { id: 'CONTENT_REVIEW', name: '内容审核' },
              { id: 'DATA_ANALYSIS', name: '数据分析' }
            ]
          },
          {
            id: 'reviewer',
            name: '审核员',
            description: '内容审核员，负责审核用户提交的内容',
            permissions: [
              { id: 'CONTENT_REVIEW', name: '内容审核' },
              { id: 'CONTENT_VIEW', name: '内容查看' }
            ]
          },
          {
            id: 'superadmin',
            name: '超级管理员',
            description: '超级管理员，拥有所有权限',
            permissions: [
              { id: 'USER_MANAGEMENT', name: '用户管理' },
              { id: 'CONTENT_REVIEW', name: '内容审核' },
              { id: 'DATA_ANALYSIS', name: '数据分析' },
              { id: 'SYSTEM_CONFIG', name: '系统配置' },
              { id: 'ROLE_MANAGEMENT', name: '角色管理' }
            ]
          }
        ]
      });
      return;
    }

    // Get users - GET /api/admin/users
    if (path === '/api/admin/users' && method === 'GET') {
      const query = parsedUrl.query;
      const role = query.role || '';
      const page = parseInt(query.page) || 1;
      const limit = parseInt(query.limit) || 10;
      const search = query.search || '';
      const status = query.status || '';

      console.log(`👥 获取用户列表 - 角色: ${role || '全部'}`);

      // 根据角色选择数据源
      let sourceUsers = [];
      if (role === 'reviewer') {
        sourceUsers = [...reviewerUsers];
      } else if (role === 'admin' || role === 'superadmin') {
        sourceUsers = adminUsersList.filter(user => user.role === role);
      } else {
        // 如果没有指定角色，返回所有用户（审核员 + 管理员）
        sourceUsers = [...reviewerUsers, ...adminUsersList];
      }

      // Filter users
      let filteredUsers = sourceUsers;

      if (search) {
        filteredUsers = filteredUsers.filter(user =>
          user.username.toLowerCase().includes(search.toLowerCase()) ||
          user.name.toLowerCase().includes(search.toLowerCase()) ||
          user.email.toLowerCase().includes(search.toLowerCase())
        );
      }

      if (status && status !== 'all') {
        filteredUsers = filteredUsers.filter(user => user.status === status);
      }

      // Pagination
      const total = filteredUsers.length;
      const totalPages = Math.ceil(total / limit);
      const startIndex = (page - 1) * limit;
      const endIndex = startIndex + limit;
      const paginatedUsers = filteredUsers.slice(startIndex, endIndex);

      sendJSON(res, 200, {
        success: true,
        data: {
          users: paginatedUsers,
          pagination: {
            page,
            limit,
            total,
            totalPages
          }
        }
      });
      return;
    }

    // Create user - POST /api/admin/users
    if (path === '/api/admin/users' && method === 'POST') {
      try {
        const body = await parseBody(req);
        const { username, email, name, password, role = 'reviewer' } = body;

        console.log(`➕ 创建新用户 - 角色: ${role}`);

        // 验证必填字段
        if (!username || !email || !name || !password) {
          sendJSON(res, 400, {
            success: false,
            error: '缺少必填字段'
          });
          return;
        }

        // 检查用户名是否已存在（在所有用户中检查）
        const allUsers = [...reviewerUsers, ...adminUsersList];
        const existingUser = allUsers.find(user =>
          user.username === username || user.email === email
        );

        if (existingUser) {
          sendJSON(res, 400, {
            success: false,
            error: '用户名或邮箱已存在'
          });
          return;
        }

        // 根据角色创建用户
        if (role === 'reviewer') {
          // 创建审核员
          const newId = Math.max(...reviewerUsers.map(u => u.id)) + 1;
          const newUuid = `reviewer-2025-${String(newId).padStart(3, '0')}`;

          const newReviewer = {
            id: newId,
            uuid: newUuid,
            username,
            email,
            name,
            status: 'active',
            role: 'reviewer',
            created_at: new Date().toISOString(),
            lastLoginAt: null,
            lastLoginIp: null,
            submissionCount: 0,
            specialties: ['问卷审核', '故事审核']
          };

          reviewerUsers.push(newReviewer);

          sendJSON(res, 201, {
            success: true,
            data: newReviewer,
            message: '审核员创建成功'
          });
        } else if (role === 'admin' || role === 'superadmin') {
          // 创建管理员
          const newId = Math.max(...adminUsersList.map(u => u.id)) + 1;
          const newUuid = `${role}-2025-${String(newId).padStart(3, '0')}`;

          const newAdmin = {
            id: newId,
            uuid: newUuid,
            username,
            email,
            name,
            status: 'active',
            role,
            created_at: new Date().toISOString(),
            lastLoginAt: null,
            lastLoginIp: null,
            operationCount: 0,
            permissions: role === 'superadmin' ? ['ALL'] : ['USER_MANAGEMENT', 'CONTENT_REVIEW'],
            lastOperationAt: new Date().toISOString()
          };

          adminUsersList.push(newAdmin);

          sendJSON(res, 201, {
            success: true,
            data: newAdmin,
            message: `${role === 'superadmin' ? '超级管理员' : '管理员'}创建成功`
          });
        } else {
          sendJSON(res, 400, {
            success: false,
            error: '无效的角色类型'
          });
        }
        return;
      } catch (error) {
        sendJSON(res, 400, {
          success: false,
          error: '请求格式错误'
        });
        return;
      }
    }

    // Update user status - PUT /api/admin/users/:id
    if (path.startsWith('/api/admin/users/') && method === 'PUT') {
      const userId = parseInt(path.split('/')[4]);
      console.log(`🔄 更新用户状态: ${userId}`);

      try {
        const body = await parseBody(req);
        const { status } = body;

        // 先在审核员中查找
        let userIndex = reviewerUsers.findIndex(user => user.id === userId);
        if (userIndex !== -1) {
          reviewerUsers[userIndex].status = status;
          sendJSON(res, 200, {
            success: true,
            data: reviewerUsers[userIndex],
            message: '状态更新成功'
          });
          return;
        }

        // 再在管理员中查找
        userIndex = adminUsersList.findIndex(user => user.id === userId);
        if (userIndex !== -1) {
          adminUsersList[userIndex].status = status;
          sendJSON(res, 200, {
            success: true,
            data: adminUsersList[userIndex],
            message: '状态更新成功'
          });
          return;
        }

        // 用户不存在
        sendJSON(res, 404, {
          success: false,
          error: '用户不存在'
        });
        return;
      } catch (error) {
        sendJSON(res, 400, {
          success: false,
          error: '请求格式错误'
        });
        return;
      }
    }

    // Reset password - POST /api/admin/users/:id/reset-password
    if (path.match(/^\/api\/admin\/users\/\d+\/reset-password$/) && method === 'POST') {
      const userId = parseInt(path.split('/')[4]);
      console.log(`🔑 重置用户密码: ${userId}`);

      // 检查用户是否存在（审核员或管理员）
      const reviewerExists = reviewerUsers.find(user => user.id === userId);
      const adminExists = adminUsersList.find(user => user.id === userId);

      if (!reviewerExists && !adminExists) {
        sendJSON(res, 404, {
          success: false,
          error: '用户不存在'
        });
        return;
      }

      // Generate new password (in real app, this would be sent via email)
      const newPassword = `temp${Math.random().toString(36).substr(2, 8)}`;

      sendJSON(res, 200, {
        success: true,
        data: {
          newPassword, // In real app, don't return password
          message: '密码重置成功，新密码已发送到邮箱'
        }
      });
      return;
    }

    // Delete user - DELETE /api/admin/users/:id
    if (path.startsWith('/api/admin/users/') && method === 'DELETE') {
      const userId = parseInt(path.split('/')[4]);
      console.log(`🗑️ 删除用户: ${userId}`);

      // 先在审核员中查找
      let userIndex = reviewerUsers.findIndex(user => user.id === userId);
      if (userIndex !== -1) {
        const deletedUser = reviewerUsers.splice(userIndex, 1)[0];
        sendJSON(res, 200, {
          success: true,
          data: deletedUser,
          message: '审核员删除成功'
        });
        return;
      }

      // 再在管理员中查找
      userIndex = adminUsersList.findIndex(user => user.id === userId);
      if (userIndex !== -1) {
        const deletedUser = adminUsersList.splice(userIndex, 1)[0];
        sendJSON(res, 200, {
          success: true,
          data: deletedUser,
          message: `${deletedUser.role === 'superadmin' ? '超级管理员' : '管理员'}删除成功`
        });
        return;
      }

      // 用户不存在
      sendJSON(res, 404, {
        success: false,
        error: '用户不存在'
      });
      return;
    }

    // Get tags - GET /api/admin/tags
    if (path === '/api/admin/tags' && method === 'GET') {
      console.log(`🏷️ 获取标签列表`);

      const query = parsedUrl.query;
      const page = parseInt(query.page) || 1;
      const limit = parseInt(query.limit) || 50;
      const search = query.search || '';
      const category = query.category || '';

      // Filter tags
      let filteredTags = [...storyTags];

      if (search) {
        filteredTags = filteredTags.filter(tag =>
          tag.name.toLowerCase().includes(search.toLowerCase()) ||
          tag.description.toLowerCase().includes(search.toLowerCase())
        );
      }

      if (category && category !== 'all') {
        filteredTags = filteredTags.filter(tag => tag.category === category);
      }

      // Pagination
      const total = filteredTags.length;
      const totalPages = Math.ceil(total / limit);
      const startIndex = (page - 1) * limit;
      const endIndex = startIndex + limit;
      const paginatedTags = filteredTags.slice(startIndex, endIndex);

      sendJSON(res, 200, {
        success: true,
        tags: paginatedTags,
        pagination: {
          page,
          limit,
          total,
          totalPages
        }
      });
      return;
    }

    // Create tag - POST /api/admin/tags
    if (path === '/api/admin/tags' && method === 'POST') {
      console.log(`➕ 创建新标签`);

      try {
        const body = await parseBody(req);
        const { name, description, color, category } = body;

        // 验证必填字段
        if (!name || !color || !category) {
          sendJSON(res, 400, {
            success: false,
            error: '缺少必填字段'
          });
          return;
        }

        // 检查标签名是否已存在
        const existingTag = storyTags.find(tag => tag.name === name);
        if (existingTag) {
          sendJSON(res, 400, {
            success: false,
            error: '标签名称已存在'
          });
          return;
        }

        // 生成新的ID
        const newId = `tag-${String(storyTags.length + 1).padStart(3, '0')}`;

        // 创建新标签
        const newTag = {
          id: newId,
          name,
          description: description || '',
          color,
          category,
          usageCount: 0,
          createdAt: new Date().toISOString(),
          isSystem: false
        };

        storyTags.push(newTag);

        sendJSON(res, 201, {
          success: true,
          tag: newTag,
          message: '标签创建成功'
        });
        return;
      } catch (error) {
        sendJSON(res, 400, {
          success: false,
          error: '请求格式错误'
        });
        return;
      }
    }

    // Update tag - PUT /api/admin/tags/:id
    if (path.startsWith('/api/admin/tags/') && method === 'PUT') {
      const tagId = path.split('/')[4];
      console.log(`🔄 更新标签: ${tagId}`);

      try {
        const body = await parseBody(req);
        const { name, description, color, category } = body;

        const tagIndex = storyTags.findIndex(tag => tag.id === tagId);
        if (tagIndex === -1) {
          sendJSON(res, 404, {
            success: false,
            error: '标签不存在'
          });
          return;
        }

        // 检查新名称是否与其他标签冲突
        if (name && name !== storyTags[tagIndex].name) {
          const existingTag = storyTags.find(tag => tag.name === name && tag.id !== tagId);
          if (existingTag) {
            sendJSON(res, 400, {
              success: false,
              error: '标签名称已存在'
            });
            return;
          }
        }

        // 更新标签
        if (name) storyTags[tagIndex].name = name;
        if (description !== undefined) storyTags[tagIndex].description = description;
        if (color) storyTags[tagIndex].color = color;
        if (category) storyTags[tagIndex].category = category;
        storyTags[tagIndex].updatedAt = new Date().toISOString();

        sendJSON(res, 200, {
          success: true,
          tag: storyTags[tagIndex],
          message: '标签更新成功'
        });
        return;
      } catch (error) {
        sendJSON(res, 400, {
          success: false,
          error: '请求格式错误'
        });
        return;
      }
    }

    // Delete tag - DELETE /api/admin/tags/:id
    if (path.startsWith('/api/admin/tags/') && method === 'DELETE') {
      const tagId = path.split('/')[4];
      console.log(`🗑️ 删除标签: ${tagId}`);

      const tagIndex = storyTags.findIndex(tag => tag.id === tagId);
      if (tagIndex === -1) {
        sendJSON(res, 404, {
          success: false,
          error: '标签不存在'
        });
        return;
      }

      // 检查是否为系统标签
      if (storyTags[tagIndex].isSystem) {
        sendJSON(res, 400, {
          success: false,
          error: '系统标签不能删除'
        });
        return;
      }

      const deletedTag = storyTags.splice(tagIndex, 1)[0];

      sendJSON(res, 200, {
        success: true,
        tag: deletedTag,
        message: '标签删除成功'
      });
      return;
    }

    // Get deidentification config - GET /api/admin/deidentification/config
    if (path === '/api/admin/deidentification/config' && method === 'GET') {
      console.log(`🤖 获取脱敏配置`);

      sendJSON(res, 200, {
        success: true,
        config: {
          enabled: false,
          aiProvider: 'grok',
          targetContent: {
            questionnaire: true,
            storyWall: true
          },
          filterOptions: {
            personalInfo: true,
            inappropriateContent: true,
            sensitiveData: true,
            contactInfo: true
          },
          autoReview: false,
          reviewThreshold: 0.8
        },
        providers: [
          {
            id: 'grok',
            name: 'Grok (X.AI)',
            status: 'inactive',
            apiKey: '',
            endpoint: 'https://api.x.ai/v1/chat/completions',
            model: 'grok-3-latest',
            requestCount: 0,
            errorCount: 0
          }
        ],
        stats: {
          totalProcessed: 1234,
          flaggedContent: 89,
          autoApproved: 1145,
          manualReview: 89,
          lastProcessed: new Date().toISOString()
        }
      });
      return;
    }

    // Save deidentification config - POST /api/admin/deidentification/config
    if (path === '/api/admin/deidentification/config' && method === 'POST') {
      console.log(`💾 保存脱敏配置`);

      try {
        const body = await parseBody(req);
        console.log('保存的配置:', body);

        sendJSON(res, 200, {
          success: true,
          message: '脱敏配置保存成功'
        });
        return;
      } catch (error) {
        sendJSON(res, 400, {
          success: false,
          error: '请求格式错误'
        });
        return;
      }
    }

    // Test AI provider - POST /api/admin/deidentification/test-provider
    if (path === '/api/admin/deidentification/test-provider' && method === 'POST') {
      console.log(`🧪 测试AI提供商连接`);

      try {
        const body = await parseBody(req);
        const { providerId, apiKey, endpoint, model } = body;

        if (providerId === 'grok' && apiKey) {
          // 真实Grok API测试
          try {
            const testResponse = await fetch(endpoint, {
              method: 'POST',
              headers: {
                'Content-Type': 'application/json',
                'Authorization': `Bearer ${apiKey}`
              },
              body: JSON.stringify({
                messages: [
                  {
                    role: "system",
                    content: "You are a test assistant."
                  },
                  {
                    role: "user",
                    content: "Testing. Just say hi and hello world and nothing else."
                  }
                ],
                model: model,
                stream: false,
                temperature: 0
              })
            });

            if (testResponse.ok) {
              const result = await testResponse.json();
              console.log('✅ Grok API测试成功:', result.choices?.[0]?.message?.content);

              sendJSON(res, 200, {
                success: true,
                message: 'Grok API连接测试成功',
                response: result.choices?.[0]?.message?.content || 'API响应正常'
              });
            } else {
              const errorText = await testResponse.text();
              console.error('❌ Grok API测试失败:', testResponse.status, errorText);

              sendJSON(res, 400, {
                success: false,
                error: `Grok API连接失败: ${testResponse.status} ${errorText}`
              });
            }
          } catch (apiError) {
            console.error('❌ Grok API请求异常:', apiError);
            sendJSON(res, 500, {
              success: false,
              error: `API请求异常: ${apiError.message}`
            });
          }
        } else {
          sendJSON(res, 400, {
            success: false,
            error: '请提供有效的API密钥'
          });
        }
        return;
      } catch (error) {
        sendJSON(res, 400, {
          success: false,
          error: '请求格式错误'
        });
        return;
      }
    }

    // Test deidentification - POST /api/admin/deidentification/test
    if (path === '/api/admin/deidentification/test' && method === 'POST') {
      console.log(`🔍 测试内容脱敏`);

      try {
        const body = await parseBody(req);
        const { content, provider, filterOptions, apiKey } = body;

        // 尝试使用真实AI API进行脱敏
        if (provider === 'grok' && apiKey) {
          try {
            const prompt = `请对以下文本进行脱敏处理，根据以下规则：
${filterOptions.personalInfo ? '- 隐藏个人信息（姓名、身份证号等）' : ''}
${filterOptions.contactInfo ? '- 隐藏联系方式（手机号、邮箱等）' : ''}
${filterOptions.inappropriateContent ? '- 标记不良信息（违规言论、敏感词汇）' : ''}
${filterOptions.sensitiveData ? '- 隐藏敏感数据（地址、财务信息等）' : ''}

请直接返回脱敏后的文本，不要添加任何解释。如果发现不良信息，请用[已脱敏]替换。

原文本：${content}`;

            const grokResponse = await fetch('https://api.x.ai/v1/chat/completions', {
              method: 'POST',
              headers: {
                'Content-Type': 'application/json',
                'Authorization': `Bearer ${apiKey}`
              },
              body: JSON.stringify({
                messages: [
                  {
                    role: "system",
                    content: "你是一个专业的内容脱敏助手，负责保护用户隐私和过滤不当内容。"
                  },
                  {
                    role: "user",
                    content: prompt
                  }
                ],
                model: "grok-3-latest",
                stream: false,
                temperature: 0.1
              })
            });

            if (grokResponse.ok) {
              const grokResult = await grokResponse.json();
              const aiResult = grokResult.choices?.[0]?.message?.content || content;

              console.log('✅ Grok AI脱敏成功');

              sendJSON(res, 200, {
                success: true,
                result: aiResult,
                flagged: content !== aiResult,
                needsReview: /\[已脱敏\]/.test(aiResult),
                method: 'ai',
                provider: 'grok'
              });
              return;
            } else {
              console.log('⚠️ Grok API失败，使用本地规则');
            }
          } catch (aiError) {
            console.log('⚠️ AI脱敏异常，使用本地规则:', aiError.message);
          }
        }

        // 回退到本地脱敏规则
        let result = content;

        if (filterOptions.contactInfo) {
          result = result.replace(/1[3-9]\d{9}/g, '1****5678');
          result = result.replace(/\b[\w.-]+@[\w.-]+\.\w+\b/g, '***@***.com');
        }

        if (filterOptions.personalInfo) {
          result = result.replace(/张三|李四|王五/g, '***');
          result = result.replace(/\d{17}[\dXx]/g, '******************');
        }

        if (filterOptions.inappropriateContent) {
          result = result.replace(/不良|违规|敏感|抵制|政策有问题/g, '[已脱敏]');
        }

        if (filterOptions.sensitiveData) {
          result = result.replace(/北京市|上海市|广州市/g, '***市');
        }

        sendJSON(res, 200, {
          success: true,
          result: result,
          flagged: content !== result,
          needsReview: /\[已脱敏\]/.test(result),
          method: 'local',
          provider: 'fallback'
        });
        return;
      } catch (error) {
        sendJSON(res, 400, {
          success: false,
          error: '请求格式错误'
        });
        return;
      }
    }

    // Content deidentification API - POST /api/content/deidentify
    if (path === '/api/content/deidentify' && method === 'POST') {
      console.log(`🔒 内容脱敏处理`);

      try {
        const body = await parseBody(req);
        const { content, contentType, userId } = body;

        // 获取当前脱敏配置（这里使用默认配置，实际应从数据库获取）
        const config = {
          enabled: true,
          aiProvider: 'grok',
          apiKey: '************************************************************************************',
          targetContent: {
            questionnaire: true,
            storyWall: true
          },
          filterOptions: {
            personalInfo: true,
            inappropriateContent: true,
            sensitiveData: true,
            contactInfo: true
          },
          autoReview: true
        };

        // 检查是否需要脱敏
        const shouldProcess = (contentType === 'questionnaire' && config.targetContent.questionnaire) ||
                             (contentType === 'story' && config.targetContent.storyWall);

        if (!config.enabled || !shouldProcess) {
          sendJSON(res, 200, {
            success: true,
            processed: false,
            originalContent: content,
            processedContent: content,
            needsReview: false,
            reason: !config.enabled ? '脱敏功能未启用' : '内容类型不在处理范围'
          });
          return;
        }

        // 执行脱敏处理
        let processedContent = content;
        let needsReview = false;
        let method = 'local';

        // 尝试使用AI脱敏
        if (config.aiProvider === 'grok' && config.apiKey) {
          try {
            const prompt = `请对以下${contentType === 'questionnaire' ? '问卷建议' : '故事'}内容进行脱敏处理：
${config.filterOptions.personalInfo ? '- 隐藏个人信息（姓名、身份证号等）' : ''}
${config.filterOptions.contactInfo ? '- 隐藏联系方式（手机号、邮箱等）' : ''}
${config.filterOptions.inappropriateContent ? '- 标记不良信息（违规言论、敏感词汇）' : ''}
${config.filterOptions.sensitiveData ? '- 隐藏敏感数据（地址、财务信息等）' : ''}

请直接返回脱敏后的文本，不要添加任何解释。如果发现不良信息，请用[已脱敏]替换。

原文本：${content}`;

            const grokResponse = await fetch('https://api.x.ai/v1/chat/completions', {
              method: 'POST',
              headers: {
                'Content-Type': 'application/json',
                'Authorization': `Bearer ${config.apiKey}`
              },
              body: JSON.stringify({
                messages: [
                  {
                    role: "system",
                    content: "你是一个专业的内容脱敏助手，负责保护用户隐私和过滤不当内容。"
                  },
                  {
                    role: "user",
                    content: prompt
                  }
                ],
                model: "grok-3-latest",
                stream: false,
                temperature: 0.1
              })
            });

            if (grokResponse.ok) {
              const grokResult = await grokResponse.json();
              processedContent = grokResult.choices?.[0]?.message?.content || content;
              method = 'ai';
              console.log('✅ AI脱敏处理完成');
            } else {
              console.log('⚠️ AI脱敏失败，使用本地规则');
            }
          } catch (aiError) {
            console.log('⚠️ AI脱敏异常，使用本地规则:', aiError.message);
          }
        }

        // 如果AI失败，使用本地规则
        if (method === 'local') {
          if (config.filterOptions.contactInfo) {
            processedContent = processedContent.replace(/1[3-9]\d{9}/g, '1****5678');
            processedContent = processedContent.replace(/\b[\w.-]+@[\w.-]+\.\w+\b/g, '***@***.com');
          }

          if (config.filterOptions.personalInfo) {
            processedContent = processedContent.replace(/张三|李四|王五/g, '***');
            processedContent = processedContent.replace(/\d{17}[\dXx]/g, '******************');
          }

          if (config.filterOptions.inappropriateContent) {
            processedContent = processedContent.replace(/不良|违规|敏感|抵制|政策有问题/g, '[已脱敏]');
          }

          if (config.filterOptions.sensitiveData) {
            processedContent = processedContent.replace(/北京市|上海市|广州市/g, '***市');
          }
        }

        // 检查是否需要人工审核
        needsReview = config.autoReview && /\[已脱敏\]/.test(processedContent);

        // 记录处理日志
        console.log(`📝 内容脱敏完成 - 类型: ${contentType}, 方法: ${method}, 需要审核: ${needsReview}`);

        sendJSON(res, 200, {
          success: true,
          processed: true,
          originalContent: content,
          processedContent: processedContent,
          needsReview: needsReview,
          method: method,
          contentType: contentType,
          flagged: content !== processedContent
        });
        return;
      } catch (error) {
        console.error('❌ 内容脱敏处理失败:', error);
        sendJSON(res, 500, {
          success: false,
          error: '内容脱敏处理失败',
          originalContent: body?.content || '',
          processedContent: body?.content || ''
        });
        return;
      }
    }

    // Create review record - POST /api/admin/review/create
    if (path === '/api/admin/review/create' && method === 'POST') {
      console.log(`📝 创建审核记录`);

      try {
        const body = await parseBody(req);
        const { originalContent, processedContent, contentType, userId, status, flagReason } = body;

        // 生成审核记录ID
        const reviewId = `review_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;

        // 模拟保存审核记录（实际应保存到数据库）
        console.log(`📋 审核记录已创建: ${reviewId}`);
        console.log(`   内容类型: ${contentType}`);
        console.log(`   用户ID: ${userId}`);
        console.log(`   标记原因: ${flagReason}`);
        console.log(`   原始内容长度: ${originalContent?.length || 0}`);
        console.log(`   处理后内容长度: ${processedContent?.length || 0}`);

        sendJSON(res, 201, {
          success: true,
          reviewId: reviewId,
          message: '审核记录创建成功'
        });
        return;
      } catch (error) {
        sendJSON(res, 400, {
          success: false,
          error: '请求格式错误'
        });
        return;
      }
    }

    // Get deidentification stats - GET /api/admin/deidentification/stats
    if (path === '/api/admin/deidentification/stats' && method === 'GET') {
      console.log(`📊 获取脱敏统计`);

      sendJSON(res, 200, {
        success: true,
        stats: {
          totalProcessed: 1234,
          flaggedContent: 89,
          autoApproved: 1145,
          manualReview: 89
        }
      });
      return;
    }

    // Questionnaire stats API
    if (path === '/api/questionnaire/stats' && method === 'GET') {
      console.log(`📊 获取问卷统计数据`);

      // Mock questionnaire statistics data
      const mockStats = {
        totalResponses: 1247,
        educationLevels: {
          '高中及以下': { count: 156, percentage: 12.5 },
          '大专': { count: 249, percentage: 20.0 },
          '本科': { count: 623, percentage: 50.0 },
          '硕士': { count: 187, percentage: 15.0 },
          '博士': { count: 32, percentage: 2.5 }
        },
        regions: {
          '北上广深': { count: 374, percentage: 30.0 },
          '新一线城市': { count: 311, percentage: 25.0 },
          '二线城市': { count: 249, percentage: 20.0 },
          '三线及以下': { count: 187, percentage: 15.0 },
          '海外': { count: 126, percentage: 10.0 }
        },
        industries: {
          '互联网/科技': { count: 312, percentage: 25.0 },
          '金融': { count: 187, percentage: 15.0 },
          '教育': { count: 156, percentage: 12.5 },
          '制造业': { count: 125, percentage: 10.0 },
          '医疗健康': { count: 94, percentage: 7.5 },
          '其他': { count: 373, percentage: 30.0 }
        },
        lastUpdated: new Date().toISOString()
      };

      sendJSON(res, 200, {
        success: true,
        statistics: mockStats
      });
      return;
    }

    // Questionnaire realtime stats API (alias for stats)
    if (path === '/api/questionnaire/realtime-stats' && method === 'GET') {
      console.log(`📊 获取问卷实时统计数据`);

      // Same data as stats API for consistency
      const mockStats = {
        totalResponses: 1247,
        educationLevels: {
          '高中及以下': { count: 156, percentage: 12.5 },
          '大专': { count: 249, percentage: 20.0 },
          '本科': { count: 623, percentage: 50.0 },
          '硕士': { count: 187, percentage: 15.0 },
          '博士': { count: 32, percentage: 2.5 }
        },
        regions: {
          '北上广深': { count: 374, percentage: 30.0 },
          '新一线城市': { count: 311, percentage: 25.0 },
          '二线城市': { count: 249, percentage: 20.0 },
          '三线及以下': { count: 187, percentage: 15.0 },
          '海外': { count: 126, percentage: 10.0 }
        },
        industries: {
          '互联网/科技': { count: 312, percentage: 25.0 },
          '金融': { count: 187, percentage: 15.0 },
          '教育': { count: 156, percentage: 12.5 },
          '制造业': { count: 125, percentage: 10.0 },
          '医疗健康': { count: 94, percentage: 7.5 },
          '其他': { count: 373, percentage: 30.0 }
        },
        lastUpdated: new Date().toISOString()
      };

      sendJSON(res, 200, {
        success: true,
        statistics: mockStats
      });
      return;
    }

    // Questionnaire submit API
    if (path === '/api/questionnaire/submit' && method === 'POST') {
      try {
        const body = await parseBody(req);
        console.log(`📝 收到问卷提交:`, body);

        // Generate response ID
        const responseId = `quest_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;

        sendJSON(res, 200, {
          success: true,
          message: '问卷提交成功',
          responseId: responseId,
          verified: !body.isAnonymous,
          sequenceNumber: `Q${Date.now()}`
        });
        return;
      } catch (error) {
        console.error('问卷提交失败:', error);
        sendJSON(res, 400, {
          success: false,
          error: '请求格式错误'
        });
        return;
      }
    }

    // 404 handler
    sendJSON(res, 404, {
      success: false,
      error: 'API端点未找到',
      path: path,
      method: method,
      availableEndpoints: [
        'GET /health',
        'POST /api/admin/login',
        'GET /api/admin/dashboard/stats',
        'GET /api/admin/test-data/status',
        'GET /api/admin/roles',
        'GET /api/admin/users',
        'POST /api/admin/users',
        'PUT /api/admin/users/:id',
        'POST /api/admin/users/:id/reset-password',
        'DELETE /api/admin/users/:id',
        'GET /api/admin/tags',
        'POST /api/admin/tags',
        'PUT /api/admin/tags/:id',
        'DELETE /api/admin/tags/:id',
        'GET /api/admin/deidentification/config',
        'POST /api/admin/deidentification/config',
        'POST /api/admin/deidentification/test-provider',
        'POST /api/admin/deidentification/test',
        'POST /api/content/deidentify',
        'POST /api/admin/review/create',
        'GET /api/admin/deidentification/stats',
        'GET /api/questionnaire/stats',
        'GET /api/questionnaire/realtime-stats',
        'POST /api/questionnaire/submit'
      ]
    });

  } catch (error) {
    console.error('服务器错误:', error);
    sendJSON(res, 500, {
      success: false,
      error: '服务器内部错误'
    });
  }
});

// Start server
server.listen(PORT, () => {
  console.log(`🚀 简单HTTP服务器运行在 http://localhost:${PORT}`);
  console.log(`📋 可用端点:`);
  console.log(`   GET  /health - 健康检查`);
  console.log(`   POST /api/admin/login - 管理员登录`);
  console.log(`   GET  /api/admin/dashboard/stats - 仪表盘统计`);
  console.log(`   GET  /api/admin/test-data/status - 测试数据状态`);
  console.log(`   GET  /api/admin/roles - 角色列表`);
  console.log(`   GET  /api/admin/users - 获取审核员列表`);
  console.log(`   POST /api/admin/users - 创建新审核员`);
  console.log(`   PUT  /api/admin/users/:id - 更新审核员状态`);
  console.log(`   POST /api/admin/users/:id/reset-password - 重置密码`);
  console.log(`   DELETE /api/admin/users/:id - 删除审核员`);
  console.log(`   GET  /api/admin/tags - 获取标签列表`);
  console.log(`   POST /api/admin/tags - 创建新标签`);
  console.log(`   PUT  /api/admin/tags/:id - 更新标签`);
  console.log(`   DELETE /api/admin/tags/:id - 删除标签`);
  console.log(`   GET  /api/admin/deidentification/config - 获取脱敏配置`);
  console.log(`   POST /api/admin/deidentification/config - 保存脱敏配置`);
  console.log(`   POST /api/admin/deidentification/test-provider - 测试AI提供商`);
  console.log(`   POST /api/admin/deidentification/test - 测试内容脱敏`);
  console.log(`   GET  /api/questionnaire/stats - 获取问卷统计`);
  console.log(`   GET  /api/questionnaire/realtime-stats - 获取问卷实时统计`);
  console.log(`   POST /api/questionnaire/submit - 提交问卷`);
  console.log(`🔐 测试账号:`);
  console.log(`   管理员: admin1/admin123`);
  console.log(`   审核员: reviewer1/admin123`);
  console.log(`   超级管理员: superadmin/admin123`);
});

// Graceful shutdown
process.on('SIGINT', () => {
  console.log('\n正在关闭服务器...');
  server.close(() => {
    console.log('服务器已关闭');
    process.exit(0);
  });
});
