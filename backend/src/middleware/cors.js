/**
 * 🌐 CORS中间件配置
 * 统一的跨域资源共享配置
 */

import { cors } from 'hono/cors';

// 允许的域名列表
const allowedOrigins = [
  // 本地开发环境
  'http://localhost:5173',
  'http://localhost:5174',
  'http://localhost:5175',
  'http://localhost:5176',
  'http://localhost:5177',
  'http://localhost:3000',
  'http://localhost:3001',

  // 生产环境域名
  'https://college-employment-survey.pages.dev',

  // 历史部署域名
  'https://6599d22b.college-employment-survey-realapi.pages.dev',
  'https://beb4f845.college-employment-survey.pages.dev',
  'https://d2292b83.college-employment-survey.pages.dev',
  'https://c4534e21.college-employment-survey.pages.dev',
  'https://2ba531d5.college-employment-survey.pages.dev',
  'https://e4cd94b1.college-employment-survey.pages.dev',
  'https://d68f23d0.college-employment-survey.pages.dev',
  'https://edfb082f.college-employment-survey.pages.dev',
  'https://b104b50c.college-employment-survey.pages.dev',
  'https://de2fcae1.college-employment-survey.pages.dev',
  'https://b95169fc.college-employment-survey.pages.dev',
  'https://0fa31718.college-employment-survey.pages.dev',
  'https://d7d83f81.college-employment-survey.pages.dev',
  'https://3af35328.college-employment-survey.pages.dev',
  'https://39cf363c.college-employment-survey.pages.dev'
];

/**
 * 动态CORS域名检查函数
 * @param {string} origin - 请求来源域名
 * @returns {boolean} 是否允许该域名
 */
function isDomainAllowed(origin) {
  if (!origin) return false;

  // 检查静态允许列表
  if (allowedOrigins.includes(origin)) {
    return true;
  }

  // 检查是否是college-employment-survey.pages.dev的子域名
  if (origin.match(/^https:\/\/[a-f0-9]{8}\.college-employment-survey\.pages\.dev$/)) {
    return true;
  }

  // 检查是否是localhost开发环境
  if (origin.match(/^http:\/\/localhost:\d+$/)) {
    return true;
  }

  return false;
}

/**
 * 创建CORS中间件
 * @param {Object} options - CORS配置选项
 * @returns {Function} CORS中间件函数
 */
export function createCorsMiddleware(options = {}) {
  const defaultOptions = {
    origin: (origin) => {
      // 允许无origin的请求（如直接访问API）
      if (!origin) return true;

      return isDomainAllowed(origin);
    },
    allowMethods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS', 'PATCH'],
    allowHeaders: [
      'Content-Type',
      'Authorization',
      'X-Requested-With',
      'Accept',
      'Origin',
      'Access-Control-Request-Method',
      'Access-Control-Request-Headers',
      'X-Test-Data-Generator' // 支持测试数据生成器标识头
    ],
    exposeHeaders: [
      'Content-Length',
      'Content-Type',
      'X-Total-Count',
      'X-Page-Count'
    ],
    maxAge: 86400, // 24小时
    credentials: true
  };

  const corsOptions = { ...defaultOptions, ...options };

  return cors(corsOptions);
}

/**
 * 开发环境CORS中间件（更宽松的配置）
 */
export function createDevCorsMiddleware() {
  return cors({
    origin: '*',
    allowMethods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS', 'PATCH'],
    allowHeaders: ['*'],
    maxAge: 86400,
    credentials: true
  });
}

/**
 * 生产环境CORS中间件（严格的配置）
 */
export function createProdCorsMiddleware() {
  return createCorsMiddleware({
    origin: allowedOrigins.filter(origin => !origin.includes('localhost')),
    credentials: false // 生产环境可能不需要credentials
  });
}

/**
 * 根据环境自动选择CORS配置
 * @param {string} environment - 环境变量
 * @returns {Function} 适合当前环境的CORS中间件
 */
export function createEnvironmentCorsMiddleware(environment) {
  if (environment === 'development' || environment === 'dev') {
    return createDevCorsMiddleware();
  } else if (environment === 'production' || environment === 'prod') {
    return createProdCorsMiddleware();
  } else {
    // 默认使用标准配置
    return createCorsMiddleware();
  }
}

/**
 * 动态CORS中间件，支持运行时添加域名
 */
export class DynamicCorsMiddleware {
  constructor(initialOrigins = allowedOrigins) {
    this.allowedOrigins = new Set(initialOrigins);
  }

  /**
   * 添加允许的域名
   * @param {string|Array} origins - 要添加的域名
   */
  addOrigins(origins) {
    const originsArray = Array.isArray(origins) ? origins : [origins];
    originsArray.forEach(origin => this.allowedOrigins.add(origin));
  }

  /**
   * 移除允许的域名
   * @param {string|Array} origins - 要移除的域名
   */
  removeOrigins(origins) {
    const originsArray = Array.isArray(origins) ? origins : [origins];
    originsArray.forEach(origin => this.allowedOrigins.delete(origin));
  }

  /**
   * 获取当前允许的域名列表
   * @returns {Array} 域名列表
   */
  getOrigins() {
    return Array.from(this.allowedOrigins);
  }

  /**
   * 创建中间件函数
   * @param {Object} options - 额外的CORS选项
   * @returns {Function} CORS中间件函数
   */
  createMiddleware(options = {}) {
    return createCorsMiddleware({
      ...options,
      origin: this.getOrigins()
    });
  }
}

// 默认导出标准CORS中间件
export default createCorsMiddleware();
