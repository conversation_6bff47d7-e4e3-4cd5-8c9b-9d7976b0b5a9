/**
 * 系统配置服务
 *
 * 提供系统配置相关的 API 调用
 */

import { apiClient } from './apiClient';

// 系统配置类型
export interface SystemConfig {
  // 基本设置
  siteName: string;
  siteDescription: string;
  contactEmail: string;
  maintenanceMode: boolean;
  maintenanceMessage: string;

  // 安全设置
  maxLoginAttempts: number;
  loginLockoutDuration: number; // 分钟
  passwordMinLength: number;
  passwordRequireUppercase: boolean;
  passwordRequireNumbers: boolean;
  passwordRequireSymbols: boolean;
  sessionTimeout: number; // 分钟
  ipBlacklist: string[];

  // 审核设置
  contentReviewEnabled: boolean;
  autoReviewEnabled: boolean;
  reviewSensitivityLevel: 'low' | 'medium' | 'high';
  reviewerNotifications: boolean;
  adminNotifications: boolean;

  // 外部服务
  emailServiceEnabled: boolean;
  emailServiceApiKey: string;
  emailServiceSender: string;
  storageServiceEnabled: boolean;
  storageServiceApiKey: string;
  storageServiceBucket: string;
  aiContentReviewEnabled: boolean;
  aiContentReviewApiKey: string;
  aiContentReviewSensitivity: number; // 0-100
}

// 默认系统配置
const defaultConfig: SystemConfig = {
  // 基本设置
  siteName: '大学就业调查平台',
  siteDescription: '收集和分析大学生就业情况的平台',
  contactEmail: '<EMAIL>',
  maintenanceMode: false,
  maintenanceMessage: '系统正在维护中，请稍后再试。',

  // 安全设置
  maxLoginAttempts: 5,
  loginLockoutDuration: 30,
  passwordMinLength: 8,
  passwordRequireUppercase: true,
  passwordRequireNumbers: true,
  passwordRequireSymbols: false,
  sessionTimeout: 60,
  ipBlacklist: [],

  // 审核设置
  contentReviewEnabled: true,
  autoReviewEnabled: true,
  reviewSensitivityLevel: 'medium',
  reviewerNotifications: true,
  adminNotifications: true,

  // 外部服务
  emailServiceEnabled: true,
  emailServiceApiKey: 'resend_api_key_123456789',
  emailServiceSender: '<EMAIL>',
  storageServiceEnabled: true,
  storageServiceApiKey: 'storage_api_key_123456789',
  storageServiceBucket: 'college-survey-bucket',
  aiContentReviewEnabled: true,
  aiContentReviewApiKey: 'ai_api_key_123456789',
  aiContentReviewSensitivity: 75
};

/**
 * 获取系统配置
 * @returns 系统配置
 */
export async function getSystemConfig() {
  try {
    // 检查是否为开发环境
    if (import.meta.env.DEV && import.meta.env.VITE_USE_MOCK === 'true') {
      console.log('使用模拟数据获取系统配置');

      // 模拟网络延迟
      await new Promise(resolve => setTimeout(resolve, 1000));

      return {
        success: true,
        data: defaultConfig
      };
    }

    // 生产环境使用真实API
    const response = await apiClient.get('/admin/system/config');
    return response.data;
  } catch (error) {
    console.error('获取系统配置失败:', error);
    throw error;
  }
}

/**
 * 更新系统配置
 * @param config 系统配置
 * @returns 更新结果
 */
export async function updateSystemConfig(config: Partial<SystemConfig>) {
  try {
    // 检查是否为开发环境
    if (import.meta.env.DEV && import.meta.env.VITE_USE_MOCK === 'true') {
      console.log('使用模拟数据更新系统配置');

      // 模拟网络延迟
      await new Promise(resolve => setTimeout(resolve, 1500));

      return {
        success: true,
        message: '系统配置已更新',
        data: { ...defaultConfig, ...config }
      };
    }

    // 生产环境使用真实API
    const response = await apiClient.post('/admin/system/config', { config });
    return response.data;
  } catch (error) {
    console.error('更新系统配置失败:', error);
    throw error;
  }
}

/**
 * 测试邮件服务
 * @returns 测试结果
 */
export async function testEmailService(config: {
  emailServiceEnabled: boolean;
  emailServiceApiKey: string;
  emailServiceSender: string;
}) {
  try {
    // 检查是否为开发环境
    if (import.meta.env.DEV && import.meta.env.VITE_USE_MOCK === 'true') {
      console.log('使用模拟数据测试邮件服务');

      // 模拟网络延迟
      await new Promise(resolve => setTimeout(resolve, 1500));

      // 模拟成功率80%
      const isSuccess = Math.random() < 0.8;

      if (isSuccess) {
        return {
          success: true,
          message: '邮件服务连接正常'
        };
      } else {
        return {
          success: false,
          message: '无法连接到邮件服务'
        };
      }
    }

    // 生产环境使用真实API
    const response = await apiClient.post('/admin/system/test-email', config);
    return response.data;
  } catch (error) {
    console.error('测试邮件服务失败:', error);
    throw error;
  }
}

/**
 * 测试存储服务
 * @returns 测试结果
 */
export async function testStorageService(config: {
  storageServiceEnabled: boolean;
  storageServiceApiKey: string;
  storageServiceBucket: string;
}) {
  try {
    // 检查是否为开发环境
    if (import.meta.env.DEV && import.meta.env.VITE_USE_MOCK === 'true') {
      console.log('使用模拟数据测试存储服务');

      // 模拟网络延迟
      await new Promise(resolve => setTimeout(resolve, 1500));

      // 模拟成功率80%
      const isSuccess = Math.random() < 0.8;

      if (isSuccess) {
        return {
          success: true,
          message: '存储服务连接正常'
        };
      } else {
        return {
          success: false,
          message: '无法连接到存储服务'
        };
      }
    }

    // 生产环境使用真实API
    const response = await apiClient.post('/admin/system/test-storage', config);
    return response.data;
  } catch (error) {
    console.error('测试存储服务失败:', error);
    throw error;
  }
}

/**
 * 测试AI内容审查服务
 * @returns 测试结果
 */
export async function testAIContentReview(config: {
  aiContentReviewEnabled: boolean;
  aiContentReviewApiKey: string;
  aiContentReviewSensitivity: number;
}) {
  try {
    // 检查是否为开发环境
    if (import.meta.env.DEV && import.meta.env.VITE_USE_MOCK === 'true') {
      console.log('使用模拟数据测试AI内容审查服务');

      // 模拟网络延迟
      await new Promise(resolve => setTimeout(resolve, 1500));

      // 模拟成功率80%
      const isSuccess = Math.random() < 0.8;

      if (isSuccess) {
        return {
          success: true,
          message: 'AI内容审查服务连接正常'
        };
      } else {
        return {
          success: false,
          message: '无法连接到AI内容审查服务'
        };
      }
    }

    // 生产环境使用真实API
    const response = await apiClient.post('/admin/system/test-ai-review', config);
    return response.data;
  } catch (error) {
    console.error('测试AI内容审查服务失败:', error);
    throw error;
  }
}

/**
 * 重置系统配置
 * @returns 重置结果
 */
export async function resetSystemConfig() {
  try {
    // 检查是否为开发环境
    if (import.meta.env.DEV && import.meta.env.VITE_USE_MOCK === 'true') {
      console.log('使用模拟数据重置系统配置');

      // 模拟网络延迟
      await new Promise(resolve => setTimeout(resolve, 1000));

      return {
        success: true,
        message: '系统配置已重置',
        data: defaultConfig
      };
    }

    // 生产环境使用真实API
    const response = await apiClient.post('/admin/system/reset-config');
    return response.data;
  } catch (error) {
    console.error('重置系统配置失败:', error);
    throw error;
  }
}

/**
 * 导出系统配置
 * @returns 导出结果
 */
export async function exportSystemConfig() {
  try {
    // 检查是否为开发环境
    if (import.meta.env.DEV && import.meta.env.VITE_USE_MOCK === 'true') {
      console.log('使用模拟数据导出系统配置');

      // 模拟网络延迟
      await new Promise(resolve => setTimeout(resolve, 1000));

      // 创建配置数据的 JSON 字符串
      const configData = JSON.stringify(defaultConfig, null, 2);

      // 创建 Blob 对象
      const blob = new Blob([configData], { type: 'application/json' });

      // 创建下载链接
      const downloadUrl = URL.createObjectURL(blob);

      // 创建文件名
      const fileName = `system_config_${new Date().toISOString().split('T')[0]}.json`;

      return {
        success: true,
        message: '配置已成功导出',
        data: {
          downloadUrl,
          fileName
        }
      };
    }

    // 生产环境使用真实API
    const response = await apiClient.get('/admin/system/export-config');
    return response.data;
  } catch (error) {
    console.error('导出系统配置失败:', error);
    throw error;
  }
}

/**
 * 导入系统配置
 * @param file 配置文件
 * @returns 导入结果
 */
export async function importSystemConfig(file: File) {
  try {
    // 检查是否为开发环境
    if (import.meta.env.DEV && import.meta.env.VITE_USE_MOCK === 'true') {
      console.log('使用模拟数据导入系统配置');

      // 模拟网络延迟
      await new Promise(resolve => setTimeout(resolve, 1500));

      // 读取文件内容
      const fileContent = await readFileAsText(file);

      try {
        // 解析 JSON 数据
        const configData = JSON.parse(fileContent);

        // 验证配置数据
        const isValid = validateConfigData(configData);

        if (!isValid) {
          return {
            success: false,
            message: '无效的配置文件格式'
          };
        }

        return {
          success: true,
          message: '配置已成功导入',
          data: configData
        };
      } catch (error) {
        return {
          success: false,
          message: '无效的配置文件格式'
        };
      }
    }

    // 生产环境使用真实API
    const formData = new FormData();
    formData.append('file', file);

    const response = await apiClient.post('/admin/system/import-config', formData, {
      headers: {
        'Content-Type': 'multipart/form-data'
      }
    });

    return response.data;
  } catch (error) {
    console.error('导入系统配置失败:', error);
    throw error;
  }
}

/**
 * 读取文件内容为文本
 * @param file 文件对象
 * @returns 文件内容
 */
function readFileAsText(file: File): Promise<string> {
  return new Promise((resolve, reject) => {
    const reader = new FileReader();
    reader.onload = () => resolve(reader.result as string);
    reader.onerror = reject;
    reader.readAsText(file);
  });
}

/**
 * 验证配置数据
 * @param data 配置数据
 * @returns 是否有效
 */
function validateConfigData(data: any): boolean {
  // 检查必要的字段
  const requiredFields = [
    'siteName',
    'contactEmail',
    'maxLoginAttempts',
    'passwordMinLength',
    'contentReviewEnabled'
  ];

  for (const field of requiredFields) {
    if (data[field] === undefined) {
      return false;
    }
  }

  return true;
}
