import React from 'react';
import ReviewerLayout from '@/components/layouts/ReviewerLayout';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { useNavigate } from 'react-router-dom';

/**
 * 测试批量审核页面
 */
const TestBatchPage: React.FC = () => {
  const navigate = useNavigate();

  return (
    <ReviewerLayout>
      <div className="space-y-6">
        <Card>
          <CardHeader>
            <CardTitle>测试批量审核页面</CardTitle>
          </CardHeader>
          <CardContent>
            <p>这是一个测试页面，用于验证路由是否正常工作。</p>
            <div className="mt-4 space-x-2">
              <Button onClick={() => navigate('/reviewer/dashboard')}>
                返回仪表盘
              </Button>
              <Button onClick={() => navigate('/reviewer/batch/stories')}>
                故事审核
              </Button>
              <Button onClick={() => navigate('/reviewer/batch/voices')}>
                心声审核
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>
    </ReviewerLayout>
  );
};

export default TestBatchPage;
