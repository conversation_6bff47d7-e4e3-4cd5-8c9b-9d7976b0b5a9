import { Context } from 'hono';
import { KVStorageService } from '../../services/kvStorageService';
import { getMockResponses } from '../../services/mockDataService';
import config from '../../config';

// Define environment interface
interface Env {
  SURVEY_KV: KVNamespace;
  DATABASE_URL: string;
}

/**
 * 获取问卷回复列表
 */
export const getResponses = async (c: Context<{ Bindings: Env }>) => {
  try {
    const {
      page = '1',
      pageSize = '10',
      search = '',
      educationLevel = 'all',
      employmentStatus = 'all',
      region = 'all',
      startDate,
      endDate,
      isAnonymous,
      status = 'all',
    } = c.req.query();

    // 解析分页参数
    const currentPage = parseInt(page, 10);
    const limit = parseInt(pageSize, 10);

    // 获取数据
    let responses;
    if (config.mockData.enabled) {
      responses = getMockResponses();
    } else {
      const kvStorage = new KVStorageService(c.env);
      responses = await kvStorage.getAllResponses();
    }

    // 应用筛选条件
    let filteredResponses = responses;

    // 搜索关键词
    if (search) {
      const searchLower = search.toLowerCase();
      filteredResponses = filteredResponses.filter(response =>
        (response.major && response.major.toLowerCase().includes(searchLower)) ||
        (response.region && response.region.toLowerCase().includes(searchLower)) ||
        (response.industry && response.industry.toLowerCase().includes(searchLower)) ||
        (response.position && response.position.toLowerCase().includes(searchLower)) ||
        (response.challenges && response.challenges.toLowerCase().includes(searchLower)) ||
        (response.suggestions && response.suggestions.toLowerCase().includes(searchLower))
      );
    }

    // 学历层次
    if (educationLevel !== 'all') {
      filteredResponses = filteredResponses.filter(response =>
        response.educationLevel === educationLevel
      );
    }

    // 就业状态
    if (employmentStatus !== 'all') {
      filteredResponses = filteredResponses.filter(response =>
        response.employmentStatus === employmentStatus
      );
    }

    // 地区
    if (region !== 'all') {
      filteredResponses = filteredResponses.filter(response =>
        response.region === region
      );
    }

    // 日期范围
    if (startDate) {
      const start = new Date(startDate);
      filteredResponses = filteredResponses.filter(response =>
        new Date(response.createdAt) >= start
      );
    }

    if (endDate) {
      const end = new Date(endDate);
      end.setHours(23, 59, 59, 999); // 设置为当天的最后一毫秒
      filteredResponses = filteredResponses.filter(response =>
        new Date(response.createdAt) <= end
      );
    }

    // 匿名状态
    if (isAnonymous !== undefined) {
      const isAnonymousValue = isAnonymous === 'true';
      filteredResponses = filteredResponses.filter(response =>
        response.isAnonymous === isAnonymousValue
      );
    }

    // 状态筛选
    if (status === 'verified') {
      filteredResponses = filteredResponses.filter(response => !response.isAnonymous);
    } else if (status === 'anonymous') {
      filteredResponses = filteredResponses.filter(response => response.isAnonymous);
    }

    // 计算分页
    const totalItems = filteredResponses.length;
    const totalPages = Math.ceil(totalItems / limit);
    const startIndex = (currentPage - 1) * limit;
    const endIndex = startIndex + limit;
    const paginatedResponses = filteredResponses.slice(startIndex, endIndex);

    // 添加审计日志
    console.log(`管理员获取问卷回复列表，共 ${totalItems} 条记录`, {
      adminId: c.get('user')?.username,
      action: 'getResponses',
      filters: {
        search,
        educationLevel,
        employmentStatus,
        region,
        startDate,
        endDate,
        isAnonymous,
        status,
      },
    });

    return c.json({
      success: true,
      responses: paginatedResponses,
      pagination: {
        currentPage,
        totalPages,
        pageSize: limit,
        totalItems,
      },
    });
  } catch (error) {
    console.error('获取问卷回复列表失败', error);
    return c.json({
      success: false,
      error: '服务器错误，请稍后再试',
    }, 500);
  }
}

/**
 * 获取单个问卷回复
 */
export const getResponse = async (c: Context<{ Bindings: Env }>) => {
  try {
    const { id } = c.req.param();

    // 获取数据
    let response;
    if (config.mockData.enabled) {
      const responses = getMockResponses();
      response = responses.find(r => r.id === parseInt(id, 10));
    } else {
      const kvStorage = new KVStorageService(c.env);
      response = await kvStorage.getResponse(parseInt(id, 10));
    }

    if (!response) {
      return c.json({
        success: false,
        error: '未找到该问卷回复',
      }, 404);
    }

    // 添加审计日志
    console.log(`管理员查看问卷回复详情，ID: ${id}`, {
      adminId: c.get('user')?.username,
      action: 'getResponse',
      responseId: id,
    });

    return c.json({
      success: true,
      response,
    });
  } catch (error) {
    console.error('获取问卷回复详情失败', error);
    return c.json({
      success: false,
      error: '服务器错误，请稍后再试',
    }, 500);
  }
}

/**
 * 更新问卷回复
 */
export const updateResponse = async (c: Context<{ Bindings: Env }>) => {
  try {
    const { id } = c.req.param();
    const updatedData = await c.req.json();

    // 获取原始数据
    let response;
    if (config.mockData.enabled) {
      const responses = getMockResponses();
      response = responses.find(r => r.id === parseInt(id, 10));
    } else {
      const kvStorage = new KVStorageService(c.env);
      response = await kvStorage.getResponse(parseInt(id, 10));
    }

    if (!response) {
      return c.json({
        success: false,
        error: '未找到该问卷回复',
      }, 404);
    }

    // 更新数据
    const updatedResponse = {
      ...response,
      ...updatedData,
      updatedAt: new Date().toISOString(),
    };

    // 保存更新后的数据
    if (!config.mockData.enabled) {
      const kvStorage = new KVStorageService(c.env);
      await kvStorage.updateResponse(parseInt(id, 10), updatedResponse);
    }

    // 添加审计日志
    console.log(`管理员更新问卷回复，ID: ${id}`, {
      adminId: c.get('user')?.username,
      action: 'updateResponse',
      responseId: id,
      changes: updatedData,
    });

    return c.json({
      success: true,
      response: updatedResponse,
    });
  } catch (error) {
    console.error('更新问卷回复失败', error);
    return c.json({
      success: false,
      error: '服务器错误，请稍后再试',
    }, 500);
  }
}

/**
 * 删除问卷回复
 */
export const deleteResponse = async (c: Context<{ Bindings: Env }>) => {
  try {
    const { id } = c.req.param();

    // 获取原始数据
    let response;
    if (config.mockData.enabled) {
      const responses = getMockResponses();
      response = responses.find(r => r.id === parseInt(id, 10));
    } else {
      const kvStorage = new KVStorageService(c.env);
      response = await kvStorage.getResponse(parseInt(id, 10));
    }

    if (!response) {
      return c.json({
        success: false,
        error: '未找到该问卷回复',
      }, 404);
    }

    // 删除数据
    if (!config.mockData.enabled) {
      const kvStorage = new KVStorageService(c.env);
      await kvStorage.deleteResponse(parseInt(id, 10));
    }

    // 添加审计日志
    console.log(`管理员删除问卷回复，ID: ${id}`, {
      adminId: c.get('user')?.username,
      action: 'deleteResponse',
      responseId: id,
    });

    return c.json({
      success: true,
    });
  } catch (error) {
    console.error('删除问卷回复失败', error);
    return c.json({
      success: false,
      error: '服务器错误，请稍后再试',
    }, 500);
  }
}

/**
 * 批量删除问卷回复
 */
export const bulkDeleteResponses = async (c: Context<{ Bindings: Env }>) => {
  try {
    const { ids } = await c.req.json();

    if (!Array.isArray(ids) || ids.length === 0) {
      return c.json({
        success: false,
        error: '请提供有效的ID列表',
      }, 400);
    }

    // 批量删除数据
    if (!config.mockData.enabled) {
      const kvStorage = new KVStorageService(c.env);
      for (const id of ids) {
        await kvStorage.deleteResponse(id);
      }
    }

    // 添加审计日志
    console.log(`管理员批量删除问卷回复，共 ${ids.length} 条记录`, {
      adminId: c.get('user')?.username,
      action: 'bulkDeleteResponses',
      responseIds: ids,
    });

    return c.json({
      success: true,
    });
  } catch (error) {
    console.error('批量删除问卷回复失败', error);
    return c.json({
      success: false,
      error: '服务器错误，请稍后再试',
    }, 500);
  }
}

/**
 * 批量添加标签
 */
export const bulkAddTags = async (c: Context<{ Bindings: Env }>) => {
  try {
    const { ids, tags } = await c.req.json();

    if (!Array.isArray(ids) || ids.length === 0) {
      return c.json({
        success: false,
        error: '请提供有效的ID列表',
      }, 400);
    }

    if (!Array.isArray(tags) || tags.length === 0) {
      return c.json({
        success: false,
        error: '请提供有效的标签列表',
      }, 400);
    }

    // 批量添加标签
    if (!config.mockData.enabled) {
      const kvStorage = new KVStorageService(c.env);

      for (const id of ids) {
        // 获取原始数据
        const response = await kvStorage.getResponse(id);

        if (response) {
          // 更新标签
          const existingTags = response.tags || [];
          const newTags = [...new Set([...existingTags, ...tags])]; // 去重

          // 保存更新后的数据
          await kvStorage.updateResponse(id, {
            ...response,
            tags: newTags,
            updatedAt: new Date().toISOString(),
          });
        }
      }
    }

    // 添加审计日志
    console.log(`管理员批量添加标签，共 ${ids.length} 条记录，标签: ${tags.join(', ')}`, {
      adminId: c.get('user')?.username,
      action: 'bulkAddTags',
      responseIds: ids,
      tags,
    });

    return c.json({
      success: true,
    });
  } catch (error) {
    console.error('批量添加标签失败', error);
    return c.json({
      success: false,
      error: '服务器错误，请稍后再试',
    }, 500);
  }
}

/**
 * 批量编辑问卷回复
 */
export const bulkEditResponses = async (c: Context<{ Bindings: Env }>) => {
  try {
    const { ids, fields } = await c.req.json();

    if (!Array.isArray(ids) || ids.length === 0) {
      return c.json({
        success: false,
        error: '请提供有效的ID列表',
      }, 400);
    }

    if (!fields || typeof fields !== 'object' || Object.keys(fields).length === 0) {
      return c.json({
        success: false,
        error: '请提供有效的字段数据',
      }, 400);
    }

    // 批量编辑数据
    if (!config.mockData.enabled) {
      const kvStorage = new KVStorageService(c.env);

      for (const id of ids) {
        // 获取原始数据
        const response = await kvStorage.getResponse(id);

        if (response) {
          // 更新字段
          const updatedResponse = {
            ...response,
            ...fields,
            updatedAt: new Date().toISOString(),
          };

          // 保存更新后的数据
          await kvStorage.updateResponse(id, updatedResponse);
        }
      }
    }

    // 添加审计日志
    console.log(`管理员批量编辑问卷回复，共 ${ids.length} 条记录，字段: ${Object.keys(fields).join(', ')}`, {
      adminId: c.get('user')?.username,
      action: 'bulkEditResponses',
      responseIds: ids,
      fields,
    });

    return c.json({
      success: true,
    });
  } catch (error) {
    console.error('批量编辑问卷回复失败', error);
    return c.json({
      success: false,
      error: '服务器错误，请稍后再试',
    }, 500);
  }
}

/**
 * 批量验证问卷回复
 */
export const bulkVerifyResponses = async (c: Context<{ Bindings: Env }>) => {
  try {
    const { ids, action, reason } = await c.req.json();

    if (!Array.isArray(ids) || ids.length === 0) {
      return c.json({
        success: false,
        error: '请提供有效的ID列表',
      }, 400);
    }

    if (action !== 'verify' && action !== 'unverify') {
      return c.json({
        success: false,
        error: '请提供有效的操作类型',
      }, 400);
    }

    // 批量验证数据
    if (!config.mockData.enabled) {
      const kvStorage = new KVStorageService(c.env);

      for (const id of ids) {
        // 获取原始数据
        const response = await kvStorage.getResponse(id);

        if (response) {
          // 更新验证状态
          const updatedResponse = {
            ...response,
            isAnonymous: action === 'unverify', // 如果是取消验证，则设为匿名
            verificationStatus: action === 'verify' ? 'verified' : 'unverified',
            verificationReason: reason,
            verifiedAt: action === 'verify' ? new Date().toISOString() : undefined,
            verifiedBy: action === 'verify' ? c.get('user')?.username : undefined,
            updatedAt: new Date().toISOString(),
          };

          // 保存更新后的数据
          await kvStorage.updateResponse(id, updatedResponse);
        }
      }
    }

    // 添加审计日志
    console.log(`管理员批量${action === 'verify' ? '验证' : '取消验证'}问卷回复，共 ${ids.length} 条记录，原因: ${reason}`, {
      adminId: c.get('user')?.username,
      action: `bulk${action === 'verify' ? 'Verify' : 'Unverify'}Responses`,
      responseIds: ids,
      reason,
    });

    return c.json({
      success: true,
    });
  } catch (error) {
    console.error('批量验证问卷回复失败', error);
    return c.json({
      success: false,
      error: '服务器错误，请稍后再试',
    }, 500);
  }
}

