/**
 * 模拟数据
 */

import { Story } from './types';

// 模拟可视化数据
export const mockVisualizationData = {
  success: true,
  stats: {
    totalCount: 1250,
    verifiedCount: 750,
    anonymousCount: 500,
    employedCount: 950,
    unemployedCount: 200,
    averageUnemploymentDuration: "4.8 个月",
    mostCommonEducation: "本科",
    mostCommonIndustry: "互联网/IT",
    educationLevels: [
      { name: "高中/中专", count: 50 },
      { name: "大专", count: 250 },
      { name: "本科", count: 650 },
      { name: "硕士", count: 250 },
      { name: "博士", count: 50 }
    ],
    regions: [
      { name: "北京", count: 200 },
      { name: "上海", count: 180 },
      { name: "广州", count: 150 },
      { name: "深圳", count: 140 },
      { name: "杭州", count: 120 },
      { name: "成都", count: 100 },
      { name: "武汉", count: 80 },
      { name: "南京", count: 70 },
      { name: "西安", count: 60 },
      { name: "其他", count: 150 }
    ],
    industries: [
      { name: "互联网/IT", count: 320 },
      { name: "金融", count: 180 },
      { name: "教育", count: 120 },
      { name: "医疗健康", count: 80 },
      { name: "制造业", count: 100 },
      { name: "服务业", count: 60 },
      { name: "政府/事业单位", count: 50 },
      { name: "其他", count: 40 }
    ],
    expectedSalaries: [
      { range: "5000以下", count: 50 },
      { range: "5000-8000", count: 150 },
      { range: "8000-12000", count: 350 },
      { range: "12000-15000", count: 300 },
      { range: "15000-20000", count: 250 },
      { range: "20000以上", count: 150 }
    ],
    actualSalaries: [
      { range: "5000以下", count: 100 },
      { range: "5000-8000", count: 200 },
      { range: "8000-12000", count: 400 },
      { range: "12000-15000", count: 250 },
      { range: "15000-20000", count: 200 },
      { range: "20000以上", count: 100 }
    ],
    unemploymentDurations: [
      { duration: "3个月以内", count: 80 },
      { duration: "3-6个月", count: 60 },
      { duration: "6-12个月", count: 40 },
      { duration: "1年以上", count: 20 }
    ],
    careerChanges: [
      { group: "高中/中专", count: 50, hasIntention: 30 },
      { group: "大专", count: 250, hasIntention: 150 },
      { group: "本科", count: 650, hasIntention: 300 },
      { group: "硕士", count: 250, hasIntention: 100 },
      { group: "博士", count: 50, hasIntention: 10 }
    ],
    timeSeries: {
      employmentRate: [
        { time: '2023-01', value: 68.5 },
        { time: '2023-02', value: 69.2 },
        { time: '2023-03', value: 70.1 },
        { time: '2023-04', value: 71.5 },
        { time: '2023-05', value: 72.3 },
        { time: '2023-06', value: 73.8 },
        { time: '2023-07', value: 74.2 },
        { time: '2023-08', value: 75.0 },
        { time: '2023-09', value: 76.1 },
        { time: '2023-10', value: 77.5 },
        { time: '2023-11', value: 78.2 },
        { time: '2023-12', value: 79.0 },
      ],
      averageSalary: [
        { time: '2023-01', value: 8500 },
        { time: '2023-02', value: 8600 },
        { time: '2023-03', value: 8700 },
        { time: '2023-04', value: 8900 },
        { time: '2023-05', value: 9100 },
        { time: '2023-06', value: 9300 },
        { time: '2023-07', value: 9500 },
        { time: '2023-08', value: 9700 },
        { time: '2023-09', value: 9900 },
        { time: '2023-10', value: 10100 },
        { time: '2023-11', value: 10300 },
        { time: '2023-12', value: 10500 },
      ],
    },
  }
};

// 模拟故事数据
export const mockStories: Story[] = [
  {
    id: 1,
    title: "从本科到互联网大厂：我的求职之路",
    content: "作为一名普通本科生，我在大四开始找工作时面临了很多挑战。经过三个月的努力，包括刷题、模拟面试和项目准备，我最终收到了一家互联网大厂的offer。这个过程中最重要的是保持积极心态和持续学习的能力。",
    author: "匿名用户",
    createdAt: "2023-05-15T08:30:00Z",
    tags: ["bachelor", "it-industry", "job-hunting", "interview"],
    likes: 156,
    dislikes: 5,
    views: 2345,
    category: "success",
    educationLevel: "bachelor",
    industry: "it",
    status: "approved",
    isAnonymous: true
  },
  {
    id: 2,
    title: "硕士毕业后转行金融的经历分享",
    content: "我本科和硕士都是学习计算机科学的，但毕业后我决定尝试金融行业。这个转变并不容易，我花了很多时间学习金融知识和相关证书。最终通过校友介绍，我进入了一家金融科技公司，将我的技术背景与金融知识结合起来。",
    author: "李明",
    createdAt: "2023-06-20T14:45:00Z",
    tags: ["master", "finance", "career-change", "advice"],
    likes: 89,
    dislikes: 3,
    views: 1256,
    category: "experience",
    educationLevel: "master",
    industry: "finance",
    status: "approved",
    isAnonymous: false
  },
  {
    id: 3,
    title: "海外留学生回国就业指南",
    content: "作为一名从美国留学回来的学生，我发现回国就业有很多需要注意的地方。首先是文化差异的适应，其次是如何让海外经历成为你的优势而不是劣势。我的建议是在回国前就开始准备，了解国内就业市场的最新动态。",
    author: "张晓",
    createdAt: "2023-07-05T10:15:00Z",
    tags: ["overseas-edu", "advice", "job-hunting"],
    likes: 210,
    dislikes: 8,
    views: 3567,
    category: "advice",
    educationLevel: "master",
    industry: "education",
    status: "approved",
    isAnonymous: false
  },
  {
    id: 4,
    title: "从失业到创业：我的自救之路",
    content: "去年因为公司裁员，我突然失业了。在找工作的过程中，我发现了市场上的一个空白点，于是决定自己创业。这一年来，我经历了从0到1的艰辛过程，但也收获了很多。创业不是逃避就业的出路，而是另一种更具挑战的职业选择。",
    author: "匿名用户",
    createdAt: "2023-08-12T16:20:00Z",
    tags: ["startup", "challenge", "advice"],
    likes: 178,
    dislikes: 12,
    views: 2890,
    category: "challenge",
    educationLevel: "bachelor",
    industry: "service",
    status: "approved",
    isAnonymous: true
  },
  {
    id: 5,
    title: "专科生如何在IT行业立足",
    content: "作为一名专科毕业生，我在IT行业求职时遇到了学历歧视。但我通过参与开源项目和不断提升自己的技术能力，最终在一家中型科技公司找到了工作。我想告诉所有专科生，学历只是敲门砖，真正的竞争力在于你的实际能力。",
    author: "王强",
    createdAt: "2023-09-03T09:40:00Z",
    tags: ["college", "it-industry", "advice", "self-taught"],
    likes: 245,
    dislikes: 7,
    views: 4120,
    category: "advice",
    educationLevel: "college",
    industry: "it",
    status: "approved",
    isAnonymous: false
  },
  {
    id: 6,
    title: "教育行业工作五年的心得",
    content: "在教育行业工作了五年后，我想分享一些经验。这个行业需要耐心和热情，但也面临着转型的挑战。如果你想在教育行业发展，建议关注教育科技的最新趋势，并且不断提升自己的专业知识和教学能力。",
    author: "陈静",
    createdAt: "2023-10-18T11:25:00Z",
    tags: ["education-industry", "work-life", "advice"],
    likes: 132,
    dislikes: 4,
    views: 1876,
    category: "experience",
    educationLevel: "master",
    industry: "education",
    status: "approved",
    isAnonymous: false
  },
  {
    id: 7,
    title: "远程工作一年后的感悟",
    content: "疫情后，我的公司转为了完全远程办公。这一年来，我经历了从兴奋到迷茫再到适应的过程。远程工作需要更强的自律能力和沟通技巧，但也带来了更多的自由和可能性。我将分享一些远程工作的实用技巧和注意事项。",
    author: "匿名用户",
    createdAt: "2023-11-07T13:50:00Z",
    tags: ["remote-work", "work-life", "advice"],
    likes: 167,
    dislikes: 6,
    views: 2543,
    category: "experience",
    educationLevel: "bachelor",
    industry: "it",
    status: "approved",
    isAnonymous: true
  },
  {
    id: 8,
    title: "医疗行业实习生存指南",
    content: "作为一名医学院学生，我在一家三甲医院实习的经历既充实又挑战。医疗行业的实习强度大，学习曲线陡峭，但也非常有价值。我想分享一些如何在医疗实习中学习和成长的建议，希望对医学院的学弟学妹们有所帮助。",
    author: "林医生",
    createdAt: "2023-12-01T15:35:00Z",
    tags: ["healthcare", "internship", "advice"],
    likes: 98,
    dislikes: 2,
    views: 1432,
    category: "advice",
    educationLevel: "master",
    industry: "healthcare",
    status: "approved",
    isAnonymous: false
  },
  {
    id: 9,
    title: "博士毕业后的职业选择",
    content: "作为一名刚毕业的博士，我面临着去高校任教还是进入企业研发的选择。经过深思熟虑和多方面考察，我最终选择了企业研发岗位。这个决定让我能够将学术知识应用到实际产品中，同时也有更好的薪资待遇。",
    author: "赵博士",
    createdAt: "2024-01-15T10:05:00Z",
    tags: ["phd", "job-hunting", "advice"],
    likes: 76,
    dislikes: 1,
    views: 1098,
    category: "advice",
    educationLevel: "phd",
    industry: "manufacturing",
    status: "approved",
    isAnonymous: false
  },
  {
    id: 10,
    title: "从制造业到互联网：我的转行故事",
    content: "在传统制造业工作了三年后，我决定转行到互联网行业。这个过程充满挑战，我需要学习新技能，适应新的工作节奏和文化。通过参加培训课程和自学编程，我最终成功转行，现在在一家电商公司担任产品经理。",
    author: "匿名用户",
    createdAt: "2024-02-20T16:55:00Z",
    tags: ["career-change", "it-industry", "manufacturing", "advice"],
    likes: 189,
    dislikes: 9,
    views: 2765,
    category: "success",
    educationLevel: "bachelor",
    industry: "it",
    status: "approved",
    isAnonymous: true
  }
];

// 模拟热门标签
export const mockPopularTags = [
  { tag: "advice", count: 8 },
  { tag: "it-industry", count: 4 },
  { tag: "job-hunting", count: 3 },
  { tag: "career-change", count: 2 },
  { tag: "work-life", count: 2 },
  { tag: "interview", count: 1 },
  { tag: "bachelor", count: 1 },
  { tag: "master", count: 1 },
  { tag: "phd", count: 1 },
  { tag: "remote-work", count: 1 }
];
