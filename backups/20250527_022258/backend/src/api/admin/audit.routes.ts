/**
 * 审计日志路由
 */

import { Hono } from 'hono';
import { Env } from '../../types';
import { adminAuthMiddleware } from '../../middlewares/auth.middleware';
import { superAdminAuthMiddleware } from '../../middlewares/superAdminAuth.middleware';
import * as auditController from './audit.controller';

// 创建路由
const app = new Hono<{ Bindings: Env }>();

// 获取审计日志列表（管理员权限）
app.get('/', adminAuthMiddleware, auditController.getAuditLogs);

// 获取单条审计日志详情（管理员权限）
app.get('/:id', adminAuthMiddleware, auditController.getAuditLog);

// 删除审计日志（超级管理员权限）
app.delete('/:id', superAdminAuthMiddleware, auditController.deleteAuditLog);

// 清理过期审计日志（超级管理员权限）
app.post('/cleanup', superAdminAuthMiddleware, auditController.cleanupAuditLogs);

// 生成审核员操作审计报告（管理员权限）
app.get('/reports/reviewer', adminAuthMiddleware, auditController.generateReviewerAuditReport);

export default app;
