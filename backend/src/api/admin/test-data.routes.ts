/**
 * 测试数据管理路由
 */

import { Hono } from 'hono';
import { Env } from '../../types';
import * as testDataController from './test-data.controller';

// 创建路由
const testDataRouter = new Hono<{ Bindings: Env }>();

// 获取测试数据状态
testDataRouter.get('/status', ...testDataController.getStatus);

// 导入测试数据
testDataRouter.post('/import', ...testDataController.importData);

// 清除测试数据
testDataRouter.post('/clear', ...testDataController.clearData);

export default testDataRouter;
