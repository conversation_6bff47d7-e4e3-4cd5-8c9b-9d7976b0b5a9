/**
 * 标签相关处理程序
 */

import { verifyToken } from '../utils/auth';

/**
 * 处理获取标签列表
 * @param request 请求对象
 * @param env 环境对象
 * @returns 响应对象
 */
export async function handleGetTags(request: Request, env: Env): Promise<Response> {
  try {
    // 获取查询参数
    const url = new URL(request.url);
    const category = url.searchParams.get('category');
    const search = url.searchParams.get('search');
    const sortBy = url.searchParams.get('sortBy') || 'name';
    
    // 构建查询
    let query = `
      SELECT t.*, COUNT(st.story_id) as count
      FROM tags t
      LEFT JOIN story_tags st ON t.id = st.tag_id
    `;
    
    const whereConditions = [];
    const params = [];
    
    // 添加分类过滤
    if (category) {
      whereConditions.push('t.category = ?');
      params.push(category);
    }
    
    // 添加搜索过滤
    if (search) {
      whereConditions.push('t.name LIKE ?');
      params.push(`%${search}%`);
    }
    
    // 添加WHERE子句
    if (whereConditions.length > 0) {
      query += ` WHERE ${whereConditions.join(' AND ')}`;
    }
    
    // 添加分组
    query += ` GROUP BY t.id`;
    
    // 添加排序
    if (sortBy === 'name') {
      query += ` ORDER BY t.name ASC`;
    } else if (sortBy === 'count') {
      query += ` ORDER BY count DESC`;
    } else if (sortBy === 'priority') {
      query += ` ORDER BY t.priority ASC`;
    }
    
    // 执行查询
    const tags = await env.DB.prepare(query).bind(...params).all();
    
    // 组织响应数据
    const formattedTags = tags.results.map(tag => ({
      id: tag.id,
      name: tag.name,
      color: tag.color,
      priority: tag.priority,
      category: tag.category,
      count: tag.count,
      createdAt: tag.created_at,
      updatedAt: tag.updated_at
    }));
    
    // 返回响应
    return new Response(JSON.stringify({
      success: true,
      tags: formattedTags,
      total: formattedTags.length
    }), {
      status: 200,
      headers: { 'Content-Type': 'application/json' }
    });
  } catch (error) {
    console.error('获取标签列表失败:', error);
    
    return new Response(JSON.stringify({
      success: false,
      error: '服务器错误'
    }), {
      status: 500,
      headers: { 'Content-Type': 'application/json' }
    });
  }
}

/**
 * 处理创建标签
 * @param request 请求对象
 * @param env 环境对象
 * @returns 响应对象
 */
export async function handleCreateTag(request: Request, env: Env): Promise<Response> {
  try {
    // 验证管理员权限
    const authHeader = request.headers.get('Authorization');
    
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return new Response(JSON.stringify({
        success: false,
        error: '未授权'
      }), {
        status: 401,
        headers: { 'Content-Type': 'application/json' }
      });
    }
    
    const token = authHeader.substring(7);
    const tokenResult = verifyToken(token, env.JWT_SECRET);
    
    if (!tokenResult.valid || !tokenResult.payload) {
      return new Response(JSON.stringify({
        success: false,
        error: '无效的令牌'
      }), {
        status: 401,
        headers: { 'Content-Type': 'application/json' }
      });
    }
    
    // 验证角色权限
    const role = tokenResult.payload.role;
    
    if (role !== 'admin' && role !== 'superadmin') {
      return new Response(JSON.stringify({
        success: false,
        error: '没有权限'
      }), {
        status: 403,
        headers: { 'Content-Type': 'application/json' }
      });
    }
    
    // 解析请求体
    const { name, color, priority, category } = await request.json() as {
      name: string;
      color: string;
      priority: number;
      category: string;
    };
    
    // 验证参数
    if (!name || !color || !category) {
      return new Response(JSON.stringify({
        success: false,
        error: '缺少必要参数'
      }), {
        status: 400,
        headers: { 'Content-Type': 'application/json' }
      });
    }
    
    // 检查标签是否已存在
    const existingTag = await env.DB.prepare(`
      SELECT id FROM tags WHERE name = ?
    `).bind(name).first();
    
    if (existingTag) {
      return new Response(JSON.stringify({
        success: false,
        error: '标签已存在'
      }), {
        status: 400,
        headers: { 'Content-Type': 'application/json' }
      });
    }
    
    // 创建标签
    const result = await env.DB.prepare(`
      INSERT INTO tags (name, color, priority, category)
      VALUES (?, ?, ?, ?)
    `).bind(
      name,
      color,
      priority || 0,
      category
    ).run();
    
    if (!result.success) {
      throw new Error('Failed to create tag');
    }
    
    // 获取新创建的标签ID
    const tagId = result.meta.last_row_id;
    
    // 返回响应
    return new Response(JSON.stringify({
      success: true,
      tagId,
      message: '标签创建成功'
    }), {
      status: 201,
      headers: { 'Content-Type': 'application/json' }
    });
  } catch (error) {
    console.error('创建标签失败:', error);
    
    return new Response(JSON.stringify({
      success: false,
      error: '服务器错误'
    }), {
      status: 500,
      headers: { 'Content-Type': 'application/json' }
    });
  }
}
