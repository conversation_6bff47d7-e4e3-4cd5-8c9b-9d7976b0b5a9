import { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { z } from 'zod';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from '@/components/ui/form';
import { useToast } from '@/components/ui/use-toast';
import { adminLogin } from '@/services/adminAuthService';
import { clearAuthCache, resetAppState, safeRoleSwitch } from '@/services/authCacheService';

// 定义登录表单验证模式
const loginSchema = z.object({
  username: z.string().min(1, '用户名不能为空'),
  password: z.string().min(1, '密码不能为空'),
});

type LoginFormValues = z.infer<typeof loginSchema>;

export default function AdminLoginPage() {
  const { toast } = useToast();
  const navigate = useNavigate();
  const [isLoading, setIsLoading] = useState(false);

  // 页面加载时完全重置应用状态
  useEffect(() => {
    console.log('AdminLoginPage - 页面加载，重置应用状态');

    // 完全重置应用状态，防止缓存污染
    resetAppState();

    console.log('AdminLoginPage - 应用状态重置完成');
  }, []);

  // 初始化表单
  const form = useForm<LoginFormValues>({
    resolver: zodResolver(loginSchema),
    defaultValues: {
      username: '',
      password: '',
    },
  });

  // 处理表单提交
  const onSubmit = async (values: LoginFormValues) => {
    try {
      setIsLoading(true);
      console.log('提交登录表单:', values);

      // 调用管理员登录服务
      const result = await adminLogin(values.username, values.password);
      console.log('登录结果:', result);

      if (result.success) {
        // 保存token到localStorage
        localStorage.setItem('adminToken', result.data.token);

        // 如果有用户信息，也保存到localStorage
        if (result.data.user) {
          console.log('保存用户信息:', result.data.user);
          localStorage.setItem('adminUser', JSON.stringify(result.data.user));
        }

        // 显示成功消息
        toast({
          title: '登录成功',
          description: `欢迎回来，${result.data.user?.name || '管理员'}！`,
        });

        // 根据角色跳转到不同的仪表盘
        const redirectPath = getRedirectPath(result.data.user?.role || 'admin');
        console.log(`重定向到: ${redirectPath}`);

        // 如果是超级管理员，设置重定向标志
        if (result.data.user?.role === 'superadmin') {
          console.log('设置超级管理员重定向标志');
          sessionStorage.setItem('redirectToRoleDashboard', 'true');
        }

        navigate(redirectPath);
      } else {
        // 显示错误消息
        toast({
          title: '登录失败',
          description: result.error || '用户名或密码错误',
          variant: 'destructive',
        });
      }
    } catch (error) {
      console.error('登录错误:', error);

      toast({
        title: '登录失败',
        description: '服务器错误，请稍后再试',
        variant: 'destructive',
      });
    } finally {
      setIsLoading(false);
    }
  };

  // 根据角色获取重定向路径
  const getRedirectPath = (role: string): string => {
    switch (role) {
      case 'admin':
        return '/admin/dashboard';
      case 'reviewer':
        return '/reviewer/dashboard';
      case 'superadmin':
        return '/superadmin/dashboard';
      default:
        return '/admin/dashboard';
    }
  };

  // 快速登录函数
  const quickLogin = async (role: 'admin' | 'reviewer' | 'superadmin') => {
    setIsLoading(true);

    try {
      let username = '';
      let password = '';

      switch (role) {
        case 'admin':
          username = 'manager';
          password = 'manager123';
          break;
        case 'reviewer':
          username = 'reviewer';
          password = 'reviewer123';
          break;
        case 'superadmin':
          username = 'admin';
          password = 'admin123';
          break;
      }

      // 更新表单值
      form.setValue('username', username);
      form.setValue('password', password);

      // 显示提示信息
      toast({
        title: '测试账号已填充',
        description: `已填充${role === 'admin' ? '管理员' : role === 'reviewer' ? '审核员' : '超级管理员'}账号，点击"登录"按钮继续`,
      });

      // 直接登录
      console.log(`尝试以 ${role} 角色登录，用户名: ${username}`);
      const result = await adminLogin(username, password);
      console.log('登录结果:', result);

      if (result.success) {
        // 保存token到localStorage
        localStorage.setItem('adminToken', result.data.token);

        // 如果有用户信息，也保存到localStorage
        if (result.data.user) {
          console.log('保存用户信息:', result.data.user);
          localStorage.setItem('adminUser', JSON.stringify(result.data.user));
        }

        // 显示成功消息
        toast({
          title: '登录成功',
          description: `欢迎回来，${result.data.user?.name || '管理员'}！`,
        });

        // 根据角色跳转到不同的仪表盘
        const redirectPath = getRedirectPath(result.data.user?.role || 'admin');
        console.log(`重定向到: ${redirectPath}`);

        // 设置重定向标志
        if (role === 'superadmin') {
          sessionStorage.setItem('redirectToRoleDashboard', 'true');
        }

        navigate(redirectPath);
      } else {
        // 显示错误消息
        toast({
          title: '登录失败',
          description: result.error || '用户名或密码错误',
          variant: 'destructive',
        });
      }
    } catch (error) {
      console.error('快速登录错误:', error);

      toast({
        title: '登录失败',
        description: '服务器错误，请稍后再试',
        variant: 'destructive',
      });
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="flex items-center justify-center min-h-screen bg-gray-100">
      <Card className="w-full max-w-md">
        <CardHeader className="space-y-1">
          <CardTitle className="text-2xl font-bold text-center">管理员登录</CardTitle>
          <CardDescription className="text-center">
            请输入您的管理员账号和密码
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="mb-4 bg-blue-50 border border-blue-200 rounded-md p-3">
            <h4 className="text-blue-700 font-medium text-sm mb-1">演示项目</h4>
            <p className="text-blue-600 text-xs">
              这是一个演示项目，使用模拟数据。您可以使用下方的一键登录按钮直接体验不同角色的功能，无需输入用户名和密码。
            </p>
          </div>
          <Form {...form}>
            <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
              <FormField
                control={form.control}
                name="username"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>用户名</FormLabel>
                    <FormControl>
                      <Input placeholder="请输入用户名" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
              <FormField
                control={form.control}
                name="password"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>密码</FormLabel>
                    <FormControl>
                      <Input type="password" placeholder="请输入密码" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
              <Button type="submit" className="w-full" disabled={isLoading}>
                {isLoading ? '登录中...' : '登录'}
              </Button>

              {/* 一键登录按钮 - 在所有环境中显示 */}
              <div className="pt-4 border-t mt-4">
                <h4 className="text-sm font-medium mb-2 text-center">一键登录（演示用）</h4>
                <div className="grid grid-cols-3 gap-2">
                  <Button
                    type="button"
                    variant="default"
                    size="sm"
                    className="w-full bg-green-600 hover:bg-green-700"
                    onClick={async () => {
                      setIsLoading(true);
                      try {
                        // 先清除所有之前的登录信息
                        console.log('管理员登录 - 清除之前的登录信息');
                        clearAuthCache();

                        // 等待一小段时间确保清除完成
                        await new Promise(resolve => setTimeout(resolve, 100));

                        const result = await adminLogin('manager', 'manager123');
                        if (result.success) {
                          // 设置新的登录信息
                          localStorage.setItem('adminToken', result.data.token);
                          if (result.data.user) {
                            localStorage.setItem('adminUser', JSON.stringify({
                              ...result.data.user,
                              role: 'admin', // 确保角色正确
                              loginTime: new Date().toISOString() // 添加登录时间戳
                            }));
                          }

                          console.log('管理员登录成功，用户信息已保存');

                          toast({
                            title: '登录成功',
                            description: `以管理员身份登录成功！`,
                          });

                          // 使用window.location直接跳转，确保页面刷新
                          window.location.href = '/admin/dashboard';
                          console.log('管理员登录成功，正在跳转到 /admin/dashboard');
                        }
                      } catch (error) {
                        console.error('登录错误:', error);
                        toast({
                          title: '登录失败',
                          description: '服务器错误，请稍后再试',
                          variant: 'destructive',
                        });
                      } finally {
                        setIsLoading(false);
                      }
                    }}
                    disabled={isLoading}
                  >
                    管理员登录
                  </Button>
                  <Button
                    type="button"
                    variant="default"
                    size="sm"
                    className="w-full bg-blue-600 hover:bg-blue-700"
                    onClick={async () => {
                      setIsLoading(true);
                      try {
                        // 先清除所有之前的登录信息
                        console.log('审核员登录 - 清除之前的登录信息');
                        clearAuthCache();

                        // 等待一小段时间确保清除完成
                        await new Promise(resolve => setTimeout(resolve, 100));

                        const result = await adminLogin('reviewer', 'reviewer123');
                        if (result.success) {
                          // 设置新的登录信息
                          localStorage.setItem('adminToken', result.data.token);
                          if (result.data.user) {
                            localStorage.setItem('adminUser', JSON.stringify({
                              ...result.data.user,
                              role: 'reviewer', // 确保角色正确
                              loginTime: new Date().toISOString() // 添加登录时间戳
                            }));
                          }

                          console.log('审核员登录成功，用户信息已保存');

                          toast({
                            title: '登录成功',
                            description: `以审核员身份登录成功！`,
                          });

                          // 使用window.location直接跳转，确保页面刷新
                          window.location.href = '/reviewer/dashboard';
                          console.log('审核员登录成功，正在跳转到 /reviewer/dashboard');
                        }
                      } catch (error) {
                        console.error('登录错误:', error);
                        toast({
                          title: '登录失败',
                          description: '服务器错误，请稍后再试',
                          variant: 'destructive',
                        });
                      } finally {
                        setIsLoading(false);
                      }
                    }}
                    disabled={isLoading}
                  >
                    审核员登录
                  </Button>
                  <Button
                    type="button"
                    variant="default"
                    size="sm"
                    className="w-full bg-purple-600 hover:bg-purple-700"
                    onClick={async () => {
                      setIsLoading(true);
                      try {
                        // 先清除所有之前的登录信息
                        console.log('超级管理员登录 - 清除之前的登录信息');
                        clearAuthCache();

                        // 等待一小段时间确保清除完成
                        await new Promise(resolve => setTimeout(resolve, 100));

                        const result = await adminLogin('admin', 'admin123');
                        if (result.success) {
                          // 设置新的登录信息
                          localStorage.setItem('adminToken', result.data.token);
                          if (result.data.user) {
                            localStorage.setItem('adminUser', JSON.stringify({
                              ...result.data.user,
                              role: 'superadmin', // 确保角色正确
                              loginTime: new Date().toISOString() // 添加登录时间戳
                            }));
                          }

                          console.log('超级管理员登录成功，用户信息已保存');

                          toast({
                            title: '登录成功',
                            description: `以超级管理员身份登录成功！`,
                          });

                          // 使用window.location直接跳转，确保页面刷新
                          window.location.href = '/superadmin/dashboard';
                          console.log('超级管理员登录成功，正在跳转到 /superadmin/dashboard');
                        }
                      } catch (error) {
                        console.error('登录错误:', error);
                        toast({
                          title: '登录失败',
                          description: '服务器错误，请稍后再试',
                          variant: 'destructive',
                        });
                      } finally {
                        setIsLoading(false);
                      }
                    }}
                    disabled={isLoading}
                  >
                    超管登录
                  </Button>
                </div>
                <p className="text-xs text-muted-foreground mt-2 text-center">
                  点击按钮直接登录，无需输入用户名和密码
                </p>
              </div>
            </form>
          </Form>
        </CardContent>
        <CardFooter className="flex justify-center">
          <p className="text-sm text-gray-500">
            仅限管理员访问
          </p>
        </CardFooter>
      </Card>
    </div>
  );
}
