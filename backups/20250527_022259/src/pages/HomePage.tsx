import { Link } from 'react-router-dom'
import { Button } from '@/components/ui/button'

export default function HomePage() {
  return (
    <div className="flex flex-col items-center justify-center py-12">
      <div className="text-center max-w-3xl">
        <h1 className="text-4xl font-bold tracking-tight text-gray-900 sm:text-5xl">
          大学生就业现状调研与匿名社交展示平台
        </h1>

        <p className="mt-6 text-lg leading-8 text-gray-600">
          本平台旨在通过结构化问卷收集大学生及各类学历背景毕业生的就业情况、求职经历和期望信息，
          同时搭建一个轻量级匿名社交平台，用于分享故事、观点和经验，实现数据统计、共鸣传播与舆情反馈的结合。
        </p>

        <div className="mt-10 flex items-center justify-center gap-x-6">
          <Link to="/questionnaire">
            <Button size="lg">
              开始问卷调查
            </Button>
          </Link>

          <Link to="/visualization">
            <Button variant="outline" size="lg">
              查看数据可视化
            </Button>
          </Link>

          <Link to="/my-content">
            <Button variant="outline" size="lg">
              管理我的内容
            </Button>
          </Link>
        </div>
      </div>

      <div className="mt-16 grid grid-cols-1 gap-8 sm:grid-cols-2 lg:grid-cols-3">
        <div className="rounded-lg border bg-card p-6 shadow-sm">
          <h3 className="text-lg font-semibold">结构化问卷</h3>
          <p className="mt-2 text-sm text-gray-600">
            六大分类页面，涵盖个人基本信息、就业期望、工作经历、失业状况、转行与反思、建议与反馈等方面。
          </p>
        </div>

        <div className="rounded-lg border bg-card p-6 shadow-sm">
          <h3 className="text-lg font-semibold">数据可视化</h3>
          <p className="mt-2 text-sm text-gray-600">
            展示结果统计：学历、地区、期望薪资等生成图表，支持筛选维度，实时更新。
          </p>
        </div>

        <div className="rounded-lg border bg-card p-6 shadow-sm">
          <h3 className="text-lg font-semibold">故事墙</h3>
          <p className="mt-2 text-sm text-gray-600">
            匿名分享故事与经验，支持点赞/点踩，提供情感共鸣，不开放评论，仅单向互动。
          </p>
        </div>
      </div>
    </div>
  )
}
