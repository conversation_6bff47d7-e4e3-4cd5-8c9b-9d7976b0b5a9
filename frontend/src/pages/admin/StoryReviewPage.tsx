import React from 'react';
import SmartLayout from '@/components/layouts/SmartLayout';
import StoryModerationPanel from '@/components/admin/StoryModerationPanel';

const StoryReviewPage: React.FC = () => {
  return (
    <SmartLayout allowedRoles={['admin', 'superadmin']}>
      <div className="container mx-auto py-6">
        <h1 className="text-2xl font-bold mb-6">故事审核</h1>
        <p className="text-gray-500 mb-6">审核用户提交的故事，只有审核通过的故事才会显示在故事墙上</p>
        <StoryModerationPanel />
      </div>
    </SmartLayout>
  );
};

export default StoryReviewPage;
