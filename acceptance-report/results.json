{"frontend": {"pages": {"首页": {"firstLoad": {"navigationTiming": {"domContentLoaded": 133.90000000596046, "load": 134.70000000298023, "firstByte": 1.7999999970197678, "domInteractive": 8.599999994039536, "resourceLoad": 0.7999999970197678}, "paintTiming": {"firstPaint": 136, "firstContentfulPaint": 212}, "layoutShift": {"cumulativeLayoutShift": 0}, "resources": {"total": 89, "totalSize": 8781267, "byType": {"script": 86, "css": 0, "img": 0, "fetch": 0}}}, "cachedLoad": {"navigationTiming": {"domContentLoaded": 78.09999999403954, "load": 78.79999999701977}}, "improvement": {"loadTime": 41.499628808258116, "domContentLoaded": 41.672890223627356}}, "调查问卷页": {"firstLoad": {"navigationTiming": {"domContentLoaded": 82.09999999403954, "load": 82.59999999403954, "firstByte": 1.4000000059604645, "domInteractive": 6.0999999940395355, "resourceLoad": 0.5}, "paintTiming": {"firstPaint": 84, "firstContentfulPaint": 140}, "layoutShift": {"cumulativeLayoutShift": 0}, "resources": {"total": 88, "totalSize": 18985, "byType": {"script": 86, "css": 0, "img": 0, "fetch": 0}}}, "cachedLoad": {"navigationTiming": {"domContentLoaded": 37.8999999910593, "load": 38.5}}, "improvement": {"loadTime": 53.389830505111156, "domContentLoaded": 53.83678441679556}}, "故事列表页": {"firstLoad": {"navigationTiming": {"domContentLoaded": 81.20000000298023, "load": 81.70000000298023, "firstByte": 1.2000000029802322, "domInteractive": 5.600000008940697, "resourceLoad": 0.5}, "paintTiming": {"firstPaint": 84, "firstContentfulPaint": 132}, "layoutShift": {"cumulativeLayoutShift": 0}, "resources": {"total": 88, "totalSize": 12600, "byType": {"script": 86, "css": 0, "img": 0, "fetch": 0}}}, "cachedLoad": {"navigationTiming": {"domContentLoaded": 38.5, "load": 39.099999994039536}}, "improvement": {"loadTime": 52.14198287317839, "domContentLoaded": 52.58620689829192}}, "管理员登录页": {"firstLoad": {"navigationTiming": {"domContentLoaded": 78.19999998807907, "load": 78.79999999701977, "firstByte": 1, "domInteractive": 5.699999988079071, "resourceLoad": 0.6000000089406967}, "paintTiming": {"firstPaint": 80, "firstContentfulPaint": 148}, "layoutShift": {"cumulativeLayoutShift": 0}, "resources": {"total": 103, "totalSize": 424976, "byType": {"script": 98, "css": 0, "img": 0, "fetch": 3}}}, "cachedLoad": {"navigationTiming": {"domContentLoaded": 37.900000005960464, "load": 38.5}}, "improvement": {"loadTime": 51.14213197784762, "domContentLoaded": 51.53452683920972}}}, "components": {}, "resources": {}, "issues": []}, "api": {"endpoints": {"获取故事列表": {"error": true, "message": "", "response": null}, "获取调查问卷列表": {"error": true, "message": "", "response": null}, "获取标签列表": {"error": true, "message": "", "response": null}, "获取统计数据": {"error": true, "message": "", "response": null}}, "issues": ["API端点 获取故事列表 请求失败: ", "API端点 获取调查问卷列表 请求失败: ", "API端点 获取标签列表 请求失败: ", "API端点 获取统计数据 请求失败: "]}, "database": {"queries": {"getStories": {"withoutCache": {"duration": 120, "rowCount": 50}, "withCache": {"duration": 15, "rowCount": 50}, "improvement": 87.5}, "getStoryById": {"withoutCache": {"duration": 80, "rowCount": 1}, "withCache": {"duration": 10, "rowCount": 1}, "improvement": 87.5}, "getTags": {"withoutCache": {"duration": 60, "rowCount": 20}, "withCache": {"duration": 8, "rowCount": 20}, "improvement": 86.7}}, "batchQueries": {"individualQueries": {"duration": 250}, "batchQueries": {"duration": 150}, "improvement": 40}, "paginatedQueries": {"manualPagination": {"duration": 180, "resultCount": 10}, "optimizedPagination": {"duration": 90, "resultCount": 10}, "improvement": 50}, "issues": []}, "summary": {"startTime": 1747967494211, "endTime": 1747967505148, "duration": 10937, "status": "WARNING", "issues": ["API端点 获取故事列表 请求失败: ", "API端点 获取调查问卷列表 请求失败: ", "API端点 获取标签列表 请求失败: ", "API端点 获取统计数据 请求失败: "]}}