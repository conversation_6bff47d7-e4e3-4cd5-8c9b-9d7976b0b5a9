/**
 * 系统备份服务
 *
 * 提供系统备份和恢复相关的 API 调用
 */

import { apiClient } from './apiClient';

// 备份类型
export interface Backup {
  id: string;
  name: string;
  description: string;
  createdAt: string;
  size: number;
  type: 'full' | 'partial';
  status: 'completed' | 'in_progress' | 'failed';
  createdBy: string;
  metadata?: {
    tables?: string[];
    recordCount?: number;
    version?: string;
    [key: string]: any;
  };
}

// 备份进度
export interface BackupProgress {
  step: string;
  progress: number;
  details: string;
}

// 备份参数
export interface BackupParams {
  name?: string;
  description?: string;
  type?: 'full' | 'partial';
  includeTables?: string[];
  excludeTables?: string[];
}

// 恢复参数
export interface RestoreParams {
  backupId: string;
  options?: {
    overwrite?: boolean;
    skipExisting?: boolean;
    onlyTables?: string[];
  };
}

// 模拟备份数据
const mockBackups: Backup[] = [
  {
    id: 'backup-1',
    name: '每日自动备份',
    description: '系统每日自动创建的完整备份',
    createdAt: new Date(Date.now() - 24 * 60 * 60 * 1000).toISOString(),
    size: 1024 * 1024 * 5, // 5MB
    type: 'full',
    status: 'completed',
    createdBy: 'system',
    metadata: {
      tables: ['users', 'pending_content', 'approved_content', 'reviews', 'settings'],
      recordCount: 1250,
      version: '1.0.0'
    }
  },
  {
    id: 'backup-2',
    name: '手动备份',
    description: '管理员手动创建的备份',
    createdAt: new Date(Date.now() - 2 * 24 * 60 * 60 * 1000).toISOString(),
    size: 1024 * 1024 * 4.5, // 4.5MB
    type: 'full',
    status: 'completed',
    createdBy: 'admin'
  },
  {
    id: 'backup-3',
    name: '更新前备份',
    description: '系统更新前创建的备份',
    createdAt: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000).toISOString(),
    size: 1024 * 1024 * 4, // 4MB
    type: 'full',
    status: 'completed',
    createdBy: 'system'
  }
];

/**
 * 获取备份列表
 * @returns 备份列表
 */
export async function getBackups() {
  try {
    // 检查是否为开发环境
    if (import.meta.env.DEV && import.meta.env.VITE_USE_MOCK === 'true') {
      console.log('使用模拟数据获取备份列表');

      // 模拟网络延迟
      await new Promise(resolve => setTimeout(resolve, 1000));

      return {
        success: true,
        data: mockBackups
      };
    }

    // 生产环境使用真实API
    const response = await apiClient.get('/admin/system/backups');
    return response.data;
  } catch (error) {
    console.error('获取备份列表失败:', error);
    throw error;
  }
}

/**
 * 获取备份详情
 * @param backupId 备份ID
 * @returns 备份详情
 */
export async function getBackupDetails(backupId: string) {
  try {
    // 检查是否为开发环境
    if (import.meta.env.DEV && import.meta.env.VITE_USE_MOCK === 'true') {
      console.log('使用模拟数据获取备份详情');

      // 模拟网络延迟
      await new Promise(resolve => setTimeout(resolve, 800));

      const backup = mockBackups.find(b => b.id === backupId);
      
      if (!backup) {
        return {
          success: false,
          message: '备份不存在'
        };
      }

      return {
        success: true,
        data: backup
      };
    }

    // 生产环境使用真实API
    const response = await apiClient.get(`/admin/system/backups/${backupId}`);
    return response.data;
  } catch (error) {
    console.error('获取备份详情失败:', error);
    throw error;
  }
}

/**
 * 创建备份
 * @param params 备份参数
 * @param progressCallback 进度回调函数
 * @returns 创建结果
 */
export async function createBackup(
  params: BackupParams = {},
  progressCallback?: (progress: BackupProgress) => void
) {
  try {
    // 检查是否为开发环境
    if (import.meta.env.DEV && import.meta.env.VITE_USE_MOCK === 'true') {
      console.log('使用模拟数据创建备份');

      // 模拟备份过程
      const steps = [
        { step: '准备备份', progress: 10, details: '正在准备备份...' },
        { step: '导出用户数据', progress: 30, details: '正在导出用户数据...' },
        { step: '导出内容数据', progress: 50, details: '正在导出内容数据...' },
        { step: '导出系统设置', progress: 70, details: '正在导出系统设置...' },
        { step: '压缩备份文件', progress: 90, details: '正在压缩备份文件...' },
        { step: '完成备份', progress: 100, details: '备份完成' }
      ];

      for (const step of steps) {
        // 模拟网络延迟
        await new Promise(resolve => setTimeout(resolve, 800));
        
        if (progressCallback) {
          progressCallback(step);
        }
      }

      // 创建新备份
      const newBackup: Backup = {
        id: `backup-${Date.now()}`,
        name: params.name || `备份 ${new Date().toLocaleString()}`,
        description: params.description || '手动创建的备份',
        createdAt: new Date().toISOString(),
        size: 1024 * 1024 * (4 + Math.random()), // 4-5MB
        type: params.type || 'full',
        status: 'completed',
        createdBy: 'admin',
        metadata: {
          tables: params.includeTables || ['users', 'pending_content', 'approved_content', 'reviews', 'settings'],
          recordCount: 1250 + Math.floor(Math.random() * 100),
          version: '1.0.0'
        }
      };

      return {
        success: true,
        message: '备份创建成功',
        data: newBackup
      };
    }

    // 生产环境使用真实API
    const response = await apiClient.post('/admin/system/backups', params);
    return response.data;
  } catch (error) {
    console.error('创建备份失败:', error);
    throw error;
  }
}

/**
 * 恢复备份
 * @param params 恢复参数
 * @param progressCallback 进度回调函数
 * @returns 恢复结果
 */
export async function restoreBackup(
  params: RestoreParams,
  progressCallback?: (progress: BackupProgress) => void
) {
  try {
    // 检查是否为开发环境
    if (import.meta.env.DEV && import.meta.env.VITE_USE_MOCK === 'true') {
      console.log('使用模拟数据恢复备份');

      // 模拟恢复过程
      const steps = [
        { step: '准备恢复', progress: 10, details: '正在准备恢复...' },
        { step: '验证备份', progress: 20, details: '正在验证备份文件...' },
        { step: '恢复用户数据', progress: 40, details: '正在恢复用户数据...' },
        { step: '恢复内容数据', progress: 60, details: '正在恢复内容数据...' },
        { step: '恢复系统设置', progress: 80, details: '正在恢复系统设置...' },
        { step: '完成恢复', progress: 100, details: '恢复完成' }
      ];

      for (const step of steps) {
        // 模拟网络延迟
        await new Promise(resolve => setTimeout(resolve, 800));
        
        if (progressCallback) {
          progressCallback(step);
        }
      }

      return {
        success: true,
        message: '备份恢复成功'
      };
    }

    // 生产环境使用真实API
    const response = await apiClient.post(`/admin/system/backups/${params.backupId}/restore`, params.options);
    return response.data;
  } catch (error) {
    console.error('恢复备份失败:', error);
    throw error;
  }
}

/**
 * 删除备份
 * @param backupId 备份ID
 * @returns 删除结果
 */
export async function deleteBackup(backupId: string) {
  try {
    // 检查是否为开发环境
    if (import.meta.env.DEV && import.meta.env.VITE_USE_MOCK === 'true') {
      console.log('使用模拟数据删除备份');

      // 模拟网络延迟
      await new Promise(resolve => setTimeout(resolve, 800));

      return {
        success: true,
        message: '备份删除成功'
      };
    }

    // 生产环境使用真实API
    const response = await apiClient.delete(`/admin/system/backups/${backupId}`);
    return response.data;
  } catch (error) {
    console.error('删除备份失败:', error);
    throw error;
  }
}

/**
 * 下载备份
 * @param backupId 备份ID
 * @returns 下载URL
 */
export async function downloadBackup(backupId: string) {
  try {
    // 检查是否为开发环境
    if (import.meta.env.DEV && import.meta.env.VITE_USE_MOCK === 'true') {
      console.log('使用模拟数据下载备份');

      // 模拟网络延迟
      await new Promise(resolve => setTimeout(resolve, 500));

      // 模拟下载URL
      return {
        success: true,
        data: {
          downloadUrl: '#'
        },
        message: '开发环境无法下载备份'
      };
    }

    // 生产环境使用真实API
    const response = await apiClient.get(`/admin/system/backups/${backupId}/download`);
    return response.data;
  } catch (error) {
    console.error('获取备份下载链接失败:', error);
    throw error;
  }
}
