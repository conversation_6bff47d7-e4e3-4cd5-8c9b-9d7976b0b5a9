/**
 * SSR构建脚本
 * 
 * 用于构建服务端渲染应用
 */

const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

// 项目根目录
const rootDir = path.resolve(__dirname, '..');

// 构建目录
const distDir = path.resolve(rootDir, 'dist');
const clientDistDir = path.resolve(distDir, 'client');
const serverDistDir = path.resolve(distDir, 'server');

// 确保目录存在
if (!fs.existsSync(distDir)) {
  fs.mkdirSync(distDir);
}

// 清理旧的构建文件
if (fs.existsSync(clientDistDir)) {
  console.log('清理客户端构建目录...');
  fs.rmSync(clientDistDir, { recursive: true, force: true });
}

if (fs.existsSync(serverDistDir)) {
  console.log('清理服务端构建目录...');
  fs.rmSync(serverDistDir, { recursive: true, force: true });
}

// 构建客户端
console.log('构建客户端...');
execSync('vite build --outDir dist/client', { stdio: 'inherit', cwd: rootDir });

// 构建服务端
console.log('构建服务端...');
execSync('vite build --config vite.config.ssr.ts', { stdio: 'inherit', cwd: rootDir });

// 复制服务器文件
console.log('复制服务器文件...');
fs.copyFileSync(
  path.resolve(rootDir, 'server.js'),
  path.resolve(distDir, 'server.js')
);

// 复制package.json（仅包含生产依赖）
console.log('创建生产package.json...');
const packageJson = require(path.resolve(rootDir, 'package.json'));
const prodPackageJson = {
  name: packageJson.name,
  version: packageJson.version,
  private: true,
  engines: packageJson.engines || { node: '>=14.0.0' },
  dependencies: {
    express: '^4.18.2',
    compression: '^1.7.4'
  },
  scripts: {
    start: 'NODE_ENV=production node server.js'
  }
};

fs.writeFileSync(
  path.resolve(distDir, 'package.json'),
  JSON.stringify(prodPackageJson, null, 2)
);

// 创建.env文件
console.log('创建生产环境变量文件...');
fs.writeFileSync(
  path.resolve(distDir, '.env'),
  'NODE_ENV=production\nPORT=3000\n'
);

// 创建README.md
console.log('创建部署说明...');
fs.writeFileSync(
  path.resolve(distDir, 'README.md'),
  `# 高校就业调查系统 - 生产部署

## 部署步骤

1. 安装依赖:
   \`\`\`
   npm install
   \`\`\`

2. 启动服务器:
   \`\`\`
   npm start
   \`\`\`

服务器将在 http://localhost:3000 上运行。

## 环境变量

- \`PORT\`: 服务器端口（默认: 3000）
- \`NODE_ENV\`: 环境（默认: production）

## 注意事项

- 此构建包含服务端渲染(SSR)功能
- 确保Node.js版本 >= 14.0.0
`
);

console.log('SSR构建完成!');
console.log(`构建输出目录: ${distDir}`);
console.log('要启动SSR服务器，请运行:');
console.log('  cd dist && npm install && npm start');
