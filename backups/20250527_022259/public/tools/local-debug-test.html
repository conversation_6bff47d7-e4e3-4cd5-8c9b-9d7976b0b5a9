<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>本地开发调试工具</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
            color: white;
        }
        
        .container {
            max-width: 1400px;
            margin: 0 auto;
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            padding: 30px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }
        
        h1 {
            font-size: 2.5rem;
            margin-bottom: 20px;
            text-align: center;
            background: linear-gradient(45deg, #fff, #f0f0f0);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }
        
        .config-section {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 10px;
            padding: 20px;
            margin: 20px 0;
            border-left: 4px solid #4CAF50;
        }
        
        .test-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
            gap: 20px;
            margin: 30px 0;
        }
        
        .test-card {
            background: rgba(255, 255, 255, 0.05);
            border-radius: 15px;
            padding: 20px;
            border: 1px solid rgba(255, 255, 255, 0.1);
        }
        
        .test-item {
            background: rgba(255, 255, 255, 0.05);
            border-radius: 8px;
            padding: 15px;
            margin: 10px 0;
            display: flex;
            align-items: center;
            justify-content: space-between;
        }
        
        .status {
            padding: 5px 15px;
            border-radius: 20px;
            font-size: 0.9rem;
            font-weight: bold;
        }
        
        .status.success { background: #4CAF50; color: white; }
        .status.error { background: #f44336; color: white; }
        .status.testing { background: #ff9800; color: white; }
        .status.pending { background: #9e9e9e; color: white; }
        
        input, select {
            width: 100%;
            padding: 10px;
            border: 1px solid rgba(255, 255, 255, 0.3);
            border-radius: 5px;
            background: rgba(255, 255, 255, 0.1);
            color: white;
            margin: 10px 0;
        }
        
        input::placeholder {
            color: rgba(255, 255, 255, 0.7);
        }
        
        button {
            background: linear-gradient(45deg, #4CAF50, #45a049);
            color: white;
            border: none;
            padding: 12px 25px;
            border-radius: 25px;
            font-size: 1rem;
            cursor: pointer;
            margin: 5px;
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(76, 175, 80, 0.3);
        }
        
        button:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(76, 175, 80, 0.4);
        }
        
        .response-box {
            background: rgba(0, 0, 0, 0.3);
            border-radius: 10px;
            padding: 15px;
            margin: 10px 0;
            font-family: 'Courier New', monospace;
            font-size: 0.9rem;
            max-height: 200px;
            overflow-y: auto;
            white-space: pre-wrap;
        }
        
        .controls {
            text-align: center;
            margin: 30px 0;
        }
        
        .environment-info {
            background: rgba(33, 150, 243, 0.1);
            border: 1px solid rgba(33, 150, 243, 0.3);
            border-radius: 10px;
            padding: 20px;
            margin: 20px 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🛠️ 本地开发调试工具</h1>
        
        <div class="config-section">
            <h2>⚙️ 配置设置</h2>
            <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 20px;">
                <div>
                    <label>前端URL:</label>
                    <input type="text" id="frontend-url" placeholder="http://localhost:5173">
                </div>
                <div>
                    <label>后端API URL:</label>
                    <input type="text" id="backend-url" placeholder="http://localhost:8787">
                </div>
            </div>
            <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 20px;">
                <div>
                    <label>测试环境:</label>
                    <select id="test-env">
                        <option value="local">本地开发</option>
                        <option value="staging">测试环境</option>
                        <option value="production">生产环境</option>
                    </select>
                </div>
                <div>
                    <label>测试模式:</label>
                    <select id="test-mode">
                        <option value="basic">基础测试</option>
                        <option value="comprehensive">完整测试</option>
                        <option value="performance">性能测试</option>
                    </select>
                </div>
            </div>
            <button onclick="updateConfig()">更新配置</button>
        </div>
        
        <div class="test-grid">
            <div class="test-card">
                <h3>🔗 连接测试</h3>
                <div class="test-item">
                    <span>前端服务器</span>
                    <span class="status pending" id="frontend-status">⏳ 待测试</span>
                </div>
                <div class="test-item">
                    <span>后端API</span>
                    <span class="status pending" id="backend-status">⏳ 待测试</span>
                </div>
                <div class="test-item">
                    <span>CORS配置</span>
                    <span class="status pending" id="cors-status">⏳ 待测试</span>
                </div>
                <button onclick="testConnections()">测试连接</button>
                <div class="response-box" id="connection-response"></div>
            </div>
            
            <div class="test-card">
                <h3>🗄️ 数据库测试</h3>
                <div class="test-item">
                    <span>D1连接</span>
                    <span class="status pending" id="db-connection-status">⏳ 待测试</span>
                </div>
                <div class="test-item">
                    <span>查询测试</span>
                    <span class="status pending" id="db-query-status">⏳ 待测试</span>
                </div>
                <div class="test-item">
                    <span>写入测试</span>
                    <span class="status pending" id="db-write-status">⏳ 待测试</span>
                </div>
                <button onclick="testDatabase()">测试数据库</button>
                <div class="response-box" id="database-response"></div>
            </div>
            
            <div class="test-card">
                <h3>🗂️ KV存储测试</h3>
                <div class="test-item">
                    <span>KV连接</span>
                    <span class="status pending" id="kv-connection-status">⏳ 待测试</span>
                </div>
                <div class="test-item">
                    <span>读写测试</span>
                    <span class="status pending" id="kv-rw-status">⏳ 待测试</span>
                </div>
                <div class="test-item">
                    <span>缓存测试</span>
                    <span class="status pending" id="kv-cache-status">⏳ 待测试</span>
                </div>
                <button onclick="testKV()">测试KV存储</button>
                <div class="response-box" id="kv-response"></div>
            </div>
            
            <div class="test-card">
                <h3>📊 API端点测试</h3>
                <div class="test-item">
                    <span>健康检查</span>
                    <span class="status pending" id="health-status">⏳ 待测试</span>
                </div>
                <div class="test-item">
                    <span>业务API</span>
                    <span class="status pending" id="business-api-status">⏳ 待测试</span>
                </div>
                <div class="test-item">
                    <span>错误处理</span>
                    <span class="status pending" id="error-handling-status">⏳ 待测试</span>
                </div>
                <button onclick="testAPIs()">测试API</button>
                <div class="response-box" id="api-response"></div>
            </div>
        </div>
        
        <div class="controls">
            <button onclick="runAllTests()" style="background: linear-gradient(45deg, #2196F3, #1976D2); font-size: 1.2rem; padding: 15px 30px;">
                🚀 运行所有测试
            </button>
            <button onclick="generateReport()" style="background: linear-gradient(45deg, #FF9800, #F57C00);">
                📋 生成报告
            </button>
            <button onclick="resetTests()" style="background: linear-gradient(45deg, #9E9E9E, #757575);">
                🔄 重置测试
            </button>
        </div>
        
        <div class="environment-info">
            <h3>🌍 环境信息</h3>
            <div id="env-info">正在获取环境信息...</div>
        </div>
        
        <div id="test-results" style="display: none;"></div>
    </div>

    <script>
        let config = {
            frontendUrl: 'http://localhost:5173',
            backendUrl: 'http://localhost:8787',
            testEnv: 'local',
            testMode: 'basic'
        };
        
        let testResults = {};
        
        // 初始化
        document.addEventListener('DOMContentLoaded', function() {
            loadConfig();
            updateEnvironmentInfo();
        });
        
        function loadConfig() {
            // 从localStorage加载配置
            const saved = localStorage.getItem('debug-tool-config');
            if (saved) {
                config = { ...config, ...JSON.parse(saved) };
            }
            
            // 更新UI
            document.getElementById('frontend-url').value = config.frontendUrl;
            document.getElementById('backend-url').value = config.backendUrl;
            document.getElementById('test-env').value = config.testEnv;
            document.getElementById('test-mode').value = config.testMode;
        }
        
        function updateConfig() {
            config.frontendUrl = document.getElementById('frontend-url').value;
            config.backendUrl = document.getElementById('backend-url').value;
            config.testEnv = document.getElementById('test-env').value;
            config.testMode = document.getElementById('test-mode').value;
            
            // 保存到localStorage
            localStorage.setItem('debug-tool-config', JSON.stringify(config));
            
            alert('配置已更新！');
        }
        
        async function testConnections() {
            try {
                // 测试前端
                try {
                    const frontendResponse = await fetch(config.frontendUrl);
                    if (frontendResponse.ok) {
                        updateStatus('frontend-status', 'success', '✅ 在线');
                    } else {
                        updateStatus('frontend-status', 'error', '❌ 错误');
                    }
                } catch (error) {
                    updateStatus('frontend-status', 'error', '❌ 离线');
                }
                
                // 测试后端
                try {
                    const backendResponse = await fetch(`${config.backendUrl}/api/health`);
                    const backendData = await backendResponse.json();
                    
                    if (backendResponse.ok && backendData.success) {
                        updateStatus('backend-status', 'success', '✅ 正常');
                        updateStatus('cors-status', 'success', '✅ 正常');
                    } else {
                        updateStatus('backend-status', 'error', '❌ 异常');
                        updateStatus('cors-status', 'error', '❌ 异常');
                    }
                    
                    document.getElementById('connection-response').textContent = 
                        JSON.stringify(backendData, null, 2);
                    
                    testResults.connections = {
                        frontend: frontendResponse?.ok || false,
                        backend: backendResponse.ok,
                        data: backendData
                    };
                    
                } catch (error) {
                    updateStatus('backend-status', 'error', '❌ 连接失败');
                    updateStatus('cors-status', 'error', '❌ CORS错误');
                    
                    document.getElementById('connection-response').textContent = 
                        `错误: ${error.message}`;
                    
                    testResults.connections = {
                        success: false,
                        error: error.message
                    };
                }
                
            } catch (error) {
                console.error('Connection test error:', error);
            }
        }
        
        async function testDatabase() {
            try {
                // 测试数据库连接
                const dbResponse = await fetch(`${config.backendUrl}/api/db/test-connection`);
                const dbData = await dbResponse.json();
                
                if (dbResponse.ok && dbData.success) {
                    updateStatus('db-connection-status', 'success', '✅ 连接成功');
                } else {
                    updateStatus('db-connection-status', 'error', '❌ 连接失败');
                }
                
                // 测试查询
                const queryResponse = await fetch(`${config.backendUrl}/api/questionnaire/stats`);
                const queryData = await queryResponse.json();
                
                if (queryResponse.ok && queryData.success) {
                    updateStatus('db-query-status', 'success', '✅ 查询成功');
                } else {
                    updateStatus('db-query-status', 'error', '❌ 查询失败');
                }
                
                // 模拟写入测试（这里只是检查端点是否存在）
                const writeResponse = await fetch(`${config.backendUrl}/api/questionnaire/submit`, {
                    method: 'OPTIONS'
                });
                
                if (writeResponse.ok) {
                    updateStatus('db-write-status', 'success', '✅ 端点可用');
                } else {
                    updateStatus('db-write-status', 'error', '❌ 端点不可用');
                }
                
                document.getElementById('database-response').textContent = 
                    `连接: ${JSON.stringify(dbData, null, 2)}\n\n` +
                    `查询: ${JSON.stringify(queryData, null, 2)}`;
                
                testResults.database = {
                    connection: dbResponse.ok,
                    query: queryResponse.ok,
                    write: writeResponse.ok
                };
                
            } catch (error) {
                updateStatus('db-connection-status', 'error', '❌ 测试失败');
                updateStatus('db-query-status', 'error', '❌ 测试失败');
                updateStatus('db-write-status', 'error', '❌ 测试失败');
                
                document.getElementById('database-response').textContent = `错误: ${error.message}`;
                testResults.database = { success: false, error: error.message };
            }
        }
        
        async function testKV() {
            try {
                // 如果是本地环境，KV可能不可用
                if (config.testEnv === 'local') {
                    updateStatus('kv-connection-status', 'pending', '⚠️ 本地不可用');
                    updateStatus('kv-rw-status', 'pending', '⚠️ 本地不可用');
                    updateStatus('kv-cache-status', 'pending', '⚠️ 本地不可用');
                    
                    document.getElementById('kv-response').textContent = 
                        '本地开发环境通常不支持KV存储，请在部署环境中测试';
                    
                    testResults.kv = { local: true, message: 'KV not available in local dev' };
                    return;
                }
                
                // 生产环境的KV测试
                const kvResponse = await fetch(`${config.backendUrl}/api/kv/test-connection`);
                const kvData = await kvResponse.json();
                
                if (kvResponse.ok && kvData.success) {
                    updateStatus('kv-connection-status', 'success', '✅ 连接成功');
                    updateStatus('kv-rw-status', 'success', '✅ 读写正常');
                    updateStatus('kv-cache-status', 'success', '✅ 缓存可用');
                } else {
                    updateStatus('kv-connection-status', 'error', '❌ 连接失败');
                    updateStatus('kv-rw-status', 'error', '❌ 读写失败');
                    updateStatus('kv-cache-status', 'error', '❌ 缓存不可用');
                }
                
                document.getElementById('kv-response').textContent = 
                    JSON.stringify(kvData, null, 2);
                
                testResults.kv = {
                    success: kvResponse.ok,
                    data: kvData
                };
                
            } catch (error) {
                updateStatus('kv-connection-status', 'error', '❌ 测试失败');
                updateStatus('kv-rw-status', 'error', '❌ 测试失败');
                updateStatus('kv-cache-status', 'error', '❌ 测试失败');
                
                document.getElementById('kv-response').textContent = `错误: ${error.message}`;
                testResults.kv = { success: false, error: error.message };
            }
        }
        
        async function testAPIs() {
            try {
                // 健康检查
                const healthResponse = await fetch(`${config.backendUrl}/api/health`);
                const healthData = await healthResponse.json();
                
                if (healthResponse.ok && healthData.success) {
                    updateStatus('health-status', 'success', '✅ 健康');
                } else {
                    updateStatus('health-status', 'error', '❌ 异常');
                }
                
                // 业务API测试
                const businessResponse = await fetch(`${config.backendUrl}/api/questionnaire/stats`);
                const businessData = await businessResponse.json();
                
                if (businessResponse.ok && businessData.success) {
                    updateStatus('business-api-status', 'success', '✅ 正常');
                } else {
                    updateStatus('business-api-status', 'error', '❌ 异常');
                }
                
                // 错误处理测试
                const errorResponse = await fetch(`${config.backendUrl}/api/non-existent-endpoint`);
                
                if (errorResponse.status === 404) {
                    updateStatus('error-handling-status', 'success', '✅ 正常');
                } else {
                    updateStatus('error-handling-status', 'error', '❌ 异常');
                }
                
                document.getElementById('api-response').textContent = 
                    `健康检查: ${JSON.stringify(healthData, null, 2)}\n\n` +
                    `业务API: ${JSON.stringify(businessData, null, 2)}`;
                
                testResults.apis = {
                    health: healthResponse.ok,
                    business: businessResponse.ok,
                    errorHandling: errorResponse.status === 404
                };
                
            } catch (error) {
                updateStatus('health-status', 'error', '❌ 测试失败');
                updateStatus('business-api-status', 'error', '❌ 测试失败');
                updateStatus('error-handling-status', 'error', '❌ 测试失败');
                
                document.getElementById('api-response').textContent = `错误: ${error.message}`;
                testResults.apis = { success: false, error: error.message };
            }
        }
        
        function updateStatus(elementId, status, text) {
            const element = document.getElementById(elementId);
            element.className = `status ${status}`;
            element.textContent = text;
        }
        
        async function runAllTests() {
            resetTests();
            
            await testConnections();
            await new Promise(resolve => setTimeout(resolve, 500));
            
            await testDatabase();
            await new Promise(resolve => setTimeout(resolve, 500));
            
            await testKV();
            await new Promise(resolve => setTimeout(resolve, 500));
            
            await testAPIs();
        }
        
        function resetTests() {
            const statuses = document.querySelectorAll('.status');
            statuses.forEach(status => {
                status.className = 'status pending';
                status.textContent = '⏳ 待测试';
            });
            
            const responses = document.querySelectorAll('.response-box');
            responses.forEach(box => box.textContent = '');
            
            testResults = {};
        }
        
        function generateReport() {
            const report = `# 本地开发调试报告
生成时间: ${new Date().toLocaleString()}
测试环境: ${config.testEnv}
测试模式: ${config.testMode}
前端URL: ${config.frontendUrl}
后端URL: ${config.backendUrl}

## 测试结果
${JSON.stringify(testResults, null, 2)}

## 环境信息
${document.getElementById('env-info').textContent}

---
报告生成完成`;
            
            // 复制到剪贴板
            navigator.clipboard.writeText(report).then(() => {
                alert('调试报告已复制到剪贴板！');
            }).catch(() => {
                // 备用方案
                const textarea = document.createElement('textarea');
                textarea.value = report;
                document.body.appendChild(textarea);
                textarea.select();
                document.execCommand('copy');
                document.body.removeChild(textarea);
                alert('调试报告已复制到剪贴板！');
            });
        }
        
        function updateEnvironmentInfo() {
            const info = `
🌐 浏览器: ${navigator.userAgent.split(' ').pop()}
📱 平台: ${navigator.platform}
🌍 语言: ${navigator.language}
📡 网络: ${navigator.onLine ? '在线' : '离线'}
🔗 协议: ${window.location.protocol}
📍 主机: ${window.location.hostname}
⏰ 时间: ${new Date().toLocaleString()}
            `.trim();
            
            document.getElementById('env-info').textContent = info;
        }
    </script>
</body>
</html>
