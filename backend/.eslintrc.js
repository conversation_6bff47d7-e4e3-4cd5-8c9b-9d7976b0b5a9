/**
 * 🔍 ESLint配置文件
 * 统一的代码风格和质量检查规则
 */

module.exports = {
  env: {
    browser: false,
    es2021: true,
    node: true,
    jest: true
  },
  extends: [
    'eslint:recommended'
  ],
  parserOptions: {
    ecmaVersion: 'latest',
    sourceType: 'module'
  },
  rules: {
    // 代码风格规则
    'indent': ['error', 2],
    'linebreak-style': ['error', 'unix'],
    'quotes': ['error', 'single'],
    'semi': ['error', 'always'],

    // 代码质量规则
    'no-unused-vars': ['error', {
      argsIgnorePattern: '^_',
      varsIgnorePattern: '^_'
    }],
    'no-console': ['warn'],
    'no-debugger': ['error'],
    'no-alert': ['error'],

    // 最佳实践
    'eqeqeq': ['error', 'always'],
    'curly': ['error', 'all'],
    'no-eval': ['error'],
    'no-implied-eval': ['error'],
    'no-new-func': ['error'],
    'no-script-url': ['error'],

    // ES6+ 规则
    'prefer-const': ['error'],
    'no-var': ['error'],
    'prefer-arrow-callback': ['error'],
    'arrow-spacing': ['error'],
    'template-curly-spacing': ['error', 'never'],

    // 函数规则
    'func-style': ['error', 'declaration', { allowArrowFunctions: true }],
    'max-params': ['warn', 5],
    'max-lines-per-function': ['warn', { max: 50, skipBlankLines: true }],

    // 复杂度控制
    'complexity': ['warn', 10],
    'max-depth': ['warn', 4],
    'max-nested-callbacks': ['warn', 3],

    // 命名规范
    'camelcase': ['error', { properties: 'never' }],
    'new-cap': ['error'],

    // 空格和格式
    'space-before-blocks': ['error'],
    'space-before-function-paren': ['error', {
      anonymous: 'always',
      named: 'never',
      asyncArrow: 'always'
    }],
    'space-in-parens': ['error', 'never'],
    'space-infix-ops': ['error'],
    'comma-spacing': ['error'],
    'key-spacing': ['error'],
    'object-curly-spacing': ['error', 'always'],
    'array-bracket-spacing': ['error', 'never'],

    // 注释规则
    'spaced-comment': ['error', 'always'],
    'multiline-comment-style': ['error', 'starred-block'],

    // 安全规则（已在最佳实践中定义，这里删除重复）
  },
  overrides: [
    {
      // 测试文件的特殊规则
      files: ['**/*.test.js', '**/*.spec.js', '**/tests/**/*.js'],
      rules: {
        'no-console': 'off',
        'max-lines-per-function': 'off'
      }
    },
    {
      // 配置文件的特殊规则
      files: ['**/*.config.js', '**/wrangler.toml', '**/.eslintrc.js'],
      rules: {
        'no-console': 'off'
      }
    }
  ],
  ignorePatterns: [
    'node_modules/',
    'dist/',
    'build/',
    'coverage/',
    '*.min.js',
    'backups/',
    'archive/'
  ]
};
