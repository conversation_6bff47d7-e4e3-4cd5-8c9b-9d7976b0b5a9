/**
 * 🔧 响应格式化工具
 * 统一的API响应格式，避免重复代码
 */

/**
 * 创建成功响应
 * @param {any} data - 响应数据
 * @param {string} message - 成功消息
 * @param {Object} meta - 元数据
 * @returns {Object} 格式化的成功响应
 */
function createSuccessResponse(data, message = 'Success', meta = {}) {
  return {
    success: true,
    message,
    data,
    meta: {
      timestamp: new Date().toISOString(),
      ...meta
    }
  };
}

/**
 * 创建错误响应
 * @param {string} error - 错误消息
 * @param {number} code - 错误代码
 * @param {Object} details - 错误详情
 * @returns {Object} 格式化的错误响应
 */
export function createErrorResponse(error, code = 500, details = {}) {
  return {
    success: false,
    error,
    code,
    details,
    timestamp: new Date().toISOString()
  };
}

/**
 * 创建分页数据响应
 * @param {Array} items - 数据项
 * @param {Object} pagination - 分页信息
 * @param {string} message - 消息
 * @returns {Object} 格式化的分页响应
 */
export function createPaginatedResponse(items, pagination, message = 'Success') {
  return {
    success: true,
    message,
    data: items,
    pagination,
    meta: {
      timestamp: new Date().toISOString(),
      count: items.length
    }
  };
}

/**
 * 创建统计数据响应
 * @param {Object} statistics - 统计数据
 * @param {string} message - 消息
 * @returns {Object} 格式化的统计响应
 */
export function createStatsResponse(statistics, message = 'Statistics retrieved successfully') {
  return {
    success: true,
    message,
    statistics,
    meta: {
      timestamp: new Date().toISOString(),
      lastUpdated: new Date().toISOString()
    }
  };
}

/**
 * 创建健康检查响应
 * @param {string} status - 状态
 * @param {Object} checks - 检查结果
 * @param {string} version - 版本号
 * @returns {Object} 健康检查响应
 */
export function createHealthResponse(status = 'ok', checks = {}, version = '1.0') {
  return {
    status,
    version,
    timestamp: new Date().toISOString(),
    checks
  };
}

/**
 * 创建验证错误响应
 * @param {Array} errors - 验证错误列表
 * @param {string} message - 错误消息
 * @returns {Object} 验证错误响应
 */
export function createValidationErrorResponse(errors, message = 'Validation failed') {
  return {
    success: false,
    error: message,
    code: 400,
    validationErrors: errors,
    timestamp: new Date().toISOString()
  };
}

/**
 * 创建未找到响应
 * @param {string} resource - 资源名称
 * @param {string} id - 资源ID
 * @returns {Object} 未找到响应
 */
export function createNotFoundResponse(resource = 'Resource', id = null) {
  const message = id ? `${resource} with ID ${id} not found` : `${resource} not found`;
  return createErrorResponse(message, 404);
}

/**
 * 创建未授权响应
 * @param {string} message - 错误消息
 * @returns {Object} 未授权响应
 */
export function createUnauthorizedResponse(message = 'Unauthorized access') {
  return createErrorResponse(message, 401);
}

/**
 * 创建禁止访问响应
 * @param {string} message - 错误消息
 * @returns {Object} 禁止访问响应
 */
export function createForbiddenResponse(message = 'Access forbidden') {
  return createErrorResponse(message, 403);
}

/**
 * 包装异步处理器，自动处理错误
 * @param {Function} handler - 异步处理器函数
 * @returns {Function} 包装后的处理器
 */
export function wrapAsyncHandler(handler) {
  return async (c) => {
    try {
      return await handler(c);
    } catch (error) {
      console.error('API Error:', error);

      // 根据错误类型返回不同的响应
      if (error.name === 'ValidationError') {
        return c.json(createValidationErrorResponse(error.details), 400);
      }

      if (error.name === 'NotFoundError') {
        return c.json(createNotFoundResponse(error.resource, error.id), 404);
      }

      if (error.name === 'UnauthorizedError') {
        return c.json(createUnauthorizedResponse(error.message), 401);
      }

      // 默认服务器错误
      return c.json(createErrorResponse(
        process.env.NODE_ENV === 'production'
          ? 'Internal server error'
          : error.message
      ), 500);
    }
  };
}
