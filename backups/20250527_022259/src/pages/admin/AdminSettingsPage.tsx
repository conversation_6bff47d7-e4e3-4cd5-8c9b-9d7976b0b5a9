import React, { useState } from 'react';
import AdminLayout from '@/components/layouts/AdminLayout';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Switch } from '@/components/ui/switch';
import { Slider } from '@/components/ui/slider';
import { useToast } from '@/components/ui/use-toast';
import { 
  User, Bell, Monitor, Moon, Sun, RotateCw, Smartphone, 
  RefreshCw, Shield, Lock, Key
} from 'lucide-react';

/**
 * 管理员设置页面
 * 
 * 允许管理员修改个人信息和偏好设置
 */
const AdminSettingsPage: React.FC = () => {
  const { toast } = useToast();
  const [isLoading, setIsLoading] = useState(false);
  
  // 模拟保存设置
  const handleSave = () => {
    setIsLoading(true);
    
    // 模拟网络延迟
    setTimeout(() => {
      setIsLoading(false);
      
      toast({
        title: '设置已保存',
        description: '您的设置已成功保存',
      });
    }, 1000);
  };
  
  // 模拟重置设置
  const handleReset = () => {
    toast({
      title: '设置已重置',
      description: '您的设置已恢复默认值',
    });
  };

  return (
    <AdminLayout>
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-2xl font-bold">用户设置</h1>
        <div className="flex space-x-2">
          <Button variant="outline" onClick={handleReset}>
            <RotateCw className="mr-2 h-4 w-4" />
            重置
          </Button>
          <Button onClick={handleSave} disabled={isLoading}>
            {isLoading ? (
              <>
                <RefreshCw className="mr-2 h-4 w-4 animate-spin" />
                保存中...
              </>
            ) : (
              '保存'
            )}
          </Button>
        </div>
      </div>

      <Tabs defaultValue="profile" className="space-y-4">
        <TabsList>
          <TabsTrigger value="profile">
            <User className="mr-2 h-4 w-4" />
            个人资料
          </TabsTrigger>
          <TabsTrigger value="notifications">
            <Bell className="mr-2 h-4 w-4" />
            通知
          </TabsTrigger>
          <TabsTrigger value="appearance">
            <Monitor className="mr-2 h-4 w-4" />
            界面
          </TabsTrigger>
          <TabsTrigger value="security">
            <Shield className="mr-2 h-4 w-4" />
            安全
          </TabsTrigger>
        </TabsList>
        
        {/* 个人资料 */}
        <TabsContent value="profile" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>个人资料</CardTitle>
              <CardDescription>
                管理您的个人信息
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="name">姓名</Label>
                  <Input id="name" placeholder="您的姓名" defaultValue="管理员" />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="email">邮箱</Label>
                  <Input id="email" type="email" placeholder="您的邮箱" defaultValue="<EMAIL>" />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="phone">手机号</Label>
                  <Input id="phone" placeholder="您的手机号" defaultValue="13900139000" />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="department">部门</Label>
                  <Input id="department" placeholder="您的部门" defaultValue="系统管理部" />
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
        
        {/* 通知设置 */}
        <TabsContent value="notifications" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>通知设置</CardTitle>
              <CardDescription>
                管理您接收通知的方式
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <div className="space-y-0.5">
                    <Label className="text-base">新内容通知</Label>
                    <p className="text-sm text-muted-foreground">
                      当有新内容需要审核时通知您
                    </p>
                  </div>
                  <Switch defaultChecked />
                </div>
                
                <div className="flex items-center justify-between">
                  <div className="space-y-0.5">
                    <Label className="text-base">系统警报</Label>
                    <p className="text-sm text-muted-foreground">
                      当系统出现异常时通知您
                    </p>
                  </div>
                  <Switch defaultChecked />
                </div>
                
                <div className="flex items-center justify-between">
                  <div className="space-y-0.5">
                    <Label className="text-base">用户活动</Label>
                    <p className="text-sm text-muted-foreground">
                      接收用户活动和操作通知
                    </p>
                  </div>
                  <Switch defaultChecked />
                </div>
                
                <div className="flex items-center justify-between">
                  <div className="space-y-0.5">
                    <Label className="text-base">邮件通知</Label>
                    <p className="text-sm text-muted-foreground">
                      通过邮件接收通知
                    </p>
                  </div>
                  <Switch />
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
        
        {/* 界面设置 */}
        <TabsContent value="appearance" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>界面设置</CardTitle>
              <CardDescription>
                自定义界面外观和行为
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="space-y-2">
                <Label>主题</Label>
                <div className="grid grid-cols-3 gap-2">
                  <Button variant="outline" className="justify-start">
                    <Sun className="mr-2 h-4 w-4" />
                    浅色
                  </Button>
                  <Button variant="outline" className="justify-start">
                    <Moon className="mr-2 h-4 w-4" />
                    深色
                  </Button>
                  <Button variant="outline" className="justify-start">
                    <Smartphone className="mr-2 h-4 w-4" />
                    系统
                  </Button>
                </div>
              </div>
              
              <div className="space-y-2">
                <div className="flex justify-between">
                  <Label>字体大小</Label>
                  <span className="text-sm">16px</span>
                </div>
                <Slider defaultValue={[16]} min={12} max={24} step={1} />
              </div>
              
              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <div className="space-y-0.5">
                    <Label className="text-base">启用动画</Label>
                    <p className="text-sm text-muted-foreground">
                      控制界面过渡动画效果
                    </p>
                  </div>
                  <Switch defaultChecked />
                </div>
                
                <div className="flex items-center justify-between">
                  <div className="space-y-0.5">
                    <Label className="text-base">高对比度模式</Label>
                    <p className="text-sm text-muted-foreground">
                      增强文本和控件的对比度
                    </p>
                  </div>
                  <Switch />
                </div>
                
                <div className="flex items-center justify-between">
                  <div className="space-y-0.5">
                    <Label className="text-base">紧凑模式</Label>
                    <p className="text-sm text-muted-foreground">
                      减少界面元素间距，显示更多内容
                    </p>
                  </div>
                  <Switch />
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
        
        {/* 安全设置 */}
        <TabsContent value="security" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>安全设置</CardTitle>
              <CardDescription>
                管理您的账号安全选项
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <div className="space-y-0.5">
                    <Label className="text-base">修改密码</Label>
                    <p className="text-sm text-muted-foreground">
                      更新您的登录密码
                    </p>
                  </div>
                  <Button variant="outline" size="sm">
                    <Lock className="mr-2 h-4 w-4" />
                    修改
                  </Button>
                </div>
                
                <div className="flex items-center justify-between">
                  <div className="space-y-0.5">
                    <Label className="text-base">两步验证</Label>
                    <p className="text-sm text-muted-foreground">
                      启用两步验证提高账号安全性
                    </p>
                  </div>
                  <Button variant="outline" size="sm">
                    <Key className="mr-2 h-4 w-4" />
                    设置
                  </Button>
                </div>
                
                <div className="flex items-center justify-between">
                  <div className="space-y-0.5">
                    <Label className="text-base">登录历史</Label>
                    <p className="text-sm text-muted-foreground">
                      查看您的账号登录记录
                    </p>
                  </div>
                  <Button variant="outline" size="sm">
                    查看
                  </Button>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </AdminLayout>
  );
};

export default AdminSettingsPage;
