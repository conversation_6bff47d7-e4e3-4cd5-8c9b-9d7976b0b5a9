/**
 * R2 数据初始化控制器
 *
 * 提供初始化 R2 存储数据的 API
 */

import { Context } from 'hono';
import { R2StorageService } from '../../services/r2StorageService';

// Define environment interface
interface Env {
  R2_BUCKET: R2Bucket;
}

/**
 * 初始化所有数据到 R2
 */
export const initializeR2Data = async (c: Context<{ Bindings: Env }>) => {
  try {
    console.log('Starting R2 data initialization...');
    
    // 创建 R2 存储服务
    const r2StorageService = new R2StorageService(c.env);
    
    // 导出所有数据到 R2
    await r2StorageService.exportAllData();
    
    return c.json({
      success: true,
      message: 'All data exported to R2 successfully',
    });
  } catch (error) {
    console.error('Error initializing R2 data:', error);
    return c.json({
      success: false,
      error: 'Failed to initialize R2 data',
    }, 500);
  }
};

/**
 * 导出故事数据到 R2
 */
export const exportStoriesToR2 = async (c: Context<{ Bindings: Env }>) => {
  try {
    console.log('Starting story data export to R2...');
    
    // 创建 R2 存储服务
    const r2StorageService = new R2StorageService(c.env);
    
    // 导出故事数据到 R2
    await r2StorageService.exportStories();
    
    return c.json({
      success: true,
      message: 'Story data exported to R2 successfully',
    });
  } catch (error) {
    console.error('Error exporting story data to R2:', error);
    return c.json({
      success: false,
      error: 'Failed to export story data to R2',
    }, 500);
  }
};

/**
 * 导出问卷数据到 R2
 */
export const exportQuestionnairesToR2 = async (c: Context<{ Bindings: Env }>) => {
  try {
    console.log('Starting questionnaire data export to R2...');
    
    // 创建 R2 存储服务
    const r2StorageService = new R2StorageService(c.env);
    
    // 导出问卷数据到 R2
    await r2StorageService.exportQuestionnaires();
    
    return c.json({
      success: true,
      message: 'Questionnaire data exported to R2 successfully',
    });
  } catch (error) {
    console.error('Error exporting questionnaire data to R2:', error);
    return c.json({
      success: false,
      error: 'Failed to export questionnaire data to R2',
    }, 500);
  }
};

/**
 * 导出统计数据到 R2
 */
export const exportStatisticsToR2 = async (c: Context<{ Bindings: Env }>) => {
  try {
    console.log('Starting statistics data export to R2...');
    
    // 创建 R2 存储服务
    const r2StorageService = new R2StorageService(c.env);
    
    // 导出统计数据到 R2
    await r2StorageService.exportStatistics();
    
    return c.json({
      success: true,
      message: 'Statistics data exported to R2 successfully',
    });
  } catch (error) {
    console.error('Error exporting statistics data to R2:', error);
    return c.json({
      success: false,
      error: 'Failed to export statistics data to R2',
    }, 500);
  }
};
