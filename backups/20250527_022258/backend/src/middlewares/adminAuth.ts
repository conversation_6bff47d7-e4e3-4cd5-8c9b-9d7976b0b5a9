import { Context, Next } from 'hono';
import { verifyJWT } from '../utils/jwt';

interface Env {
  JWT_SECRET: string;
}

/**
 * 管理员认证中间件
 * 验证请求头中的JWT令牌
 */
export const adminAuth = async (c: Context<{ Bindings: Env }>, next: Next) => {
  return adminAuthMiddleware(c, next);
};

export const adminAuthMiddleware = async (c: Context<{ Bindings: Env }>, next: Next) => {
  // 开发环境下的简化认证
  if (c.env.ENVIRONMENT === 'development' && c.env.USE_MOCK_DATA === 'true') {
    // 使用console.log而不是console.warn，避免在控制台中显示为错误
    console.log('Development mode: Using mock admin authentication');

    // 在开发环境中，我们可以模拟一个管理员用户
    c.set('user', { username: 'admin', role: 'admin' });

    await next();
    return;
  }

  // 获取Authorization头
  const authHeader = c.req.header('Authorization');

  if (!authHeader || !authHeader.startsWith('Bearer ')) {
    return c.json({ success: false, error: 'Unauthorized' }, 401);
  }

  // 提取token
  const token = authHeader.substring(7);

  try {
    // 验证token
    const payload = await verifyJWT(token, c.env.JWT_SECRET);

    if (!payload || payload.role !== 'admin') {
      return c.json({ success: false, error: 'Unauthorized' }, 401);
    }

    // 将用户信息添加到请求上下文
    c.set('user', payload);

    await next();
  } catch (error) {
    console.error('JWT验证错误:', error);

    // 在开发环境中，即使出错也允许访问
    if (c.env.ENVIRONMENT === 'development' && c.env.USE_MOCK_DATA === 'true') {
      console.log('Development mode: JWT verification failed, but allowing access');
      c.set('user', { username: 'admin', role: 'admin' });
      await next();
      return;
    }

    return c.json({ success: false, error: 'Unauthorized' }, 401);
  }
};
