import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Badge } from '@/components/ui/badge';
import { Checkbox } from '@/components/ui/checkbox';
import { Progress } from '@/components/ui/progress';
import { Switch } from '@/components/ui/switch';
import { Separator } from '@/components/ui/separator';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { Calendar } from '@/components/ui/calendar';
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';
import {
  Download,
  FileText,
  Table,
  FileSpreadsheet,
  FileJson,
  Calendar as CalendarIcon,
  Settings,
  Clock,
  CheckCircle,
  AlertCircle,
  Trash2,
  Eye,
  RefreshCw
} from 'lucide-react';
import { useToast } from '@/components/ui/use-toast';
import { format } from 'date-fns';
import { zhCN } from 'date-fns/locale';
import {
  ExportOptions,
  ExportJob,
  createExportJob,
  getExportJobStatus,
  getExportJobs,
  downloadExportFile,
  deleteExportJob,
  getExportTemplates,
  AdvancedSearchFilters
} from '@/services/contentManagementService';

interface ExportPanelProps {
  currentFilters?: AdvancedSearchFilters;
  selectedItems?: any[];
}

/**
 * 导出功能面板组件
 *
 * 提供完整的数据导出功能，包括：
 * - 多种导出格式支持
 * - 自定义导出字段
 * - 导出任务管理
 * - 导出进度监控
 */
const ExportPanel: React.FC<ExportPanelProps> = ({
  currentFilters,
  selectedItems = []
}) => {
  const { toast } = useToast();
  const [isExportDialogOpen, setIsExportDialogOpen] = useState(false);
  const [exportJobs, setExportJobs] = useState<ExportJob[]>([]);
  const [exportTemplates, setExportTemplates] = useState<any[]>([]);
  const [isLoading, setIsLoading] = useState(false);

  // 导出配置
  const [exportConfig, setExportConfig] = useState<{
    type: ExportJob['type'];
    options: ExportOptions;
  }>({
    type: 'contents',
    options: {
      format: 'csv',
      includeMetadata: true,
      includeStatistics: false
    }
  });

  // 导出类型选项
  const exportTypes = [
    { value: 'contents', label: '待审核内容', icon: FileText, description: '导出待审核的内容数据' },
    { value: 'stories', label: '故事数据', icon: FileText, description: '导出故事相关数据' },
    { value: 'comments', label: '评论数据', icon: FileText, description: '导出评论相关数据' },
    { value: 'tags', label: '标签数据', icon: FileText, description: '导出标签和分类数据' },
    { value: 'statistics', label: '统计报告', icon: FileSpreadsheet, description: '导出统计分析报告' },
    { value: 'audit_log', label: '审核日志', icon: FileText, description: '导出审核操作日志' }
  ];

  // 导出格式选项
  const formatOptions = [
    { value: 'csv', label: 'CSV', icon: Table, description: '逗号分隔值文件，适合Excel打开' },
    { value: 'xlsx', label: 'Excel', icon: FileSpreadsheet, description: 'Excel工作簿文件' },
    { value: 'json', label: 'JSON', icon: FileJson, description: 'JSON格式，适合程序处理' },
    { value: 'pdf', label: 'PDF', icon: FileText, description: 'PDF报告文件' }
  ];

  // 可导出字段选项
  const fieldOptions = {
    contents: [
      { value: 'id', label: 'ID' },
      { value: 'type', label: '内容类型' },
      { value: 'status', label: '状态' },
      { value: 'priority', label: '优先级' },
      { value: 'originalContent', label: '原始内容' },
      { value: 'sanitizedContent', label: '清理后内容' },
      { value: 'flags', label: '标记' },
      { value: 'aiSuggestion', label: 'AI建议' },
      { value: 'aiConfidence', label: 'AI置信度' },
      { value: 'reviewerId', label: '审核员' },
      { value: 'reviewNotes', label: '审核备注' },
      { value: 'createdAt', label: '创建时间' },
      { value: 'updatedAt', label: '更新时间' }
    ],
    stories: [
      { value: 'id', label: 'ID' },
      { value: 'title', label: '标题' },
      { value: 'content', label: '内容' },
      { value: 'author', label: '作者' },
      { value: 'isAnonymous', label: '是否匿名' },
      { value: 'likes', label: '点赞数' },
      { value: 'dislikes', label: '点踩数' },
      { value: 'tags', label: '标签' },
      { value: 'category', label: '分类' },
      { value: 'status', label: '状态' },
      { value: 'createdAt', label: '创建时间' }
    ],
    comments: [
      { value: 'id', label: 'ID' },
      { value: 'content', label: '内容' },
      { value: 'userId', label: '用户ID' },
      { value: 'userName', label: '用户名' },
      { value: 'targetType', label: '目标类型' },
      { value: 'targetId', label: '目标ID' },
      { value: 'status', label: '状态' },
      { value: 'createdAt', label: '创建时间' }
    ],
    tags: [
      { value: 'id', label: 'ID' },
      { value: 'name', label: '名称' },
      { value: 'description', label: '描述' },
      { value: 'color', label: '颜色' },
      { value: 'category', label: '分类' },
      { value: 'usageCount', label: '使用次数' },
      { value: 'isSystem', label: '是否系统标签' },
      { value: 'createdAt', label: '创建时间' }
    ],
    statistics: [
      { value: 'totalPending', label: '待审核总数' },
      { value: 'totalApproved', label: '已通过总数' },
      { value: 'totalRejected', label: '已拒绝总数' },
      { value: 'averageProcessingTime', label: '平均处理时间' },
      { value: 'topReviewers', label: '顶级审核员' },
      { value: 'contentTypeStats', label: '内容类型统计' }
    ],
    audit_log: [
      { value: 'id', label: 'ID' },
      { value: 'contentId', label: '内容ID' },
      { value: 'reviewerId', label: '审核员ID' },
      { value: 'action', label: '操作' },
      { value: 'reviewNotes', label: '审核备注' },
      { value: 'ipAddress', label: 'IP地址' },
      { value: 'userAgent', label: '用户代理' },
      { value: 'createdAt', label: '操作时间' }
    ]
  };

  // 加载导出任务和模板
  useEffect(() => {
    const loadData = async () => {
      try {
        const [jobsResponse, templatesResponse] = await Promise.all([
          getExportJobs({ page: 1, pageSize: 10 }),
          getExportTemplates(exportConfig.type)
        ]);

        if (jobsResponse.success) {
          setExportJobs(jobsResponse.data.jobs);
        }

        if (templatesResponse.success) {
          setExportTemplates(templatesResponse.data);
        }
      } catch (error) {
        console.error('加载导出数据失败:', error);
      }
    };

    loadData();
  }, [exportConfig.type]);

  // 监控导出任务进度
  useEffect(() => {
    const interval = setInterval(async () => {
      const processingJobs = exportJobs.filter(job =>
        job.status === 'pending' || job.status === 'processing'
      );

      if (processingJobs.length > 0) {
        try {
          const updatedJobs = await Promise.all(
            processingJobs.map(job => getExportJobStatus(job.id))
          );

          setExportJobs(prevJobs =>
            prevJobs.map(job => {
              const updatedJob = updatedJobs.find(uj => uj.success && uj.data.id === job.id);
              return updatedJob ? updatedJob.data : job;
            })
          );
        } catch (error) {
          console.error('更新导出任务状态失败:', error);
        }
      }
    }, 3000);

    return () => clearInterval(interval);
  }, [exportJobs]);

  // 创建导出任务
  const handleCreateExport = async () => {
    setIsLoading(true);
    try {
      const options: ExportOptions = {
        ...exportConfig.options,
        filters: currentFilters
      };

      const response = await createExportJob(exportConfig.type, options);

      if (response.success) {
        setExportJobs([response.data, ...exportJobs]);
        setIsExportDialogOpen(false);

        toast({
          title: '导出任务已创建',
          description: '导出任务正在后台处理，完成后可下载文件',
        });
      } else {
        throw new Error(response.error || '创建导出任务失败');
      }
    } catch (error) {
      console.error('创建导出任务失败:', error);
      toast({
        title: '创建失败',
        description: error instanceof Error ? error.message : '创建导出任务时发生错误',
        variant: 'destructive',
      });
    } finally {
      setIsLoading(false);
    }
  };

  // 下载导出文件
  const handleDownload = async (job: ExportJob) => {
    try {
      const blob = await downloadExportFile(job.id);
      const url = URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = `export_${job.type}_${job.id}.${job.options.format}`;
      document.body.appendChild(a);
      a.click();
      document.body.removeChild(a);
      URL.revokeObjectURL(url);

      toast({
        title: '下载成功',
        description: '文件已开始下载',
      });
    } catch (error) {
      toast({
        title: '下载失败',
        description: '下载文件时发生错误',
        variant: 'destructive',
      });
    }
  };

  // 删除导出任务
  const handleDeleteJob = async (jobId: string) => {
    try {
      await deleteExportJob(jobId);
      setExportJobs(exportJobs.filter(job => job.id !== jobId));

      toast({
        title: '删除成功',
        description: '导出任务已删除',
      });
    } catch (error) {
      toast({
        title: '删除失败',
        description: '删除导出任务时发生错误',
        variant: 'destructive',
      });
    }
  };

  // 获取状态图标
  const getStatusIcon = (status: ExportJob['status']) => {
    switch (status) {
      case 'pending':
        return <Clock className="h-4 w-4 text-yellow-500" />;
      case 'processing':
        return <RefreshCw className="h-4 w-4 text-blue-500 animate-spin" />;
      case 'completed':
        return <CheckCircle className="h-4 w-4 text-green-500" />;
      case 'failed':
        return <AlertCircle className="h-4 w-4 text-red-500" />;
      default:
        return <Clock className="h-4 w-4 text-gray-500" />;
    }
  };

  // 获取状态文本
  const getStatusText = (status: ExportJob['status']) => {
    switch (status) {
      case 'pending':
        return '等待中';
      case 'processing':
        return '处理中';
      case 'completed':
        return '已完成';
      case 'failed':
        return '失败';
      default:
        return '未知';
    }
  };

  // 格式化文件大小
  const formatFileSize = (bytes: number) => {
    if (bytes === 0) return '0 B';
    const k = 1024;
    const sizes = ['B', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  return (
    <div className="space-y-6">
      {/* 导出控制 */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <div>
              <CardTitle className="flex items-center gap-2">
                <Download className="h-5 w-5" />
                数据导出
              </CardTitle>
              <CardDescription>
                导出各类数据为不同格式的文件
              </CardDescription>
            </div>
            <Dialog open={isExportDialogOpen} onOpenChange={setIsExportDialogOpen}>
              <DialogTrigger asChild>
                <Button>
                  <Download className="h-4 w-4 mr-2" />
                  创建导出任务
                </Button>
              </DialogTrigger>
              <DialogContent className="max-w-2xl">
                <DialogHeader>
                  <DialogTitle>创建导出任务</DialogTitle>
                  <DialogDescription>
                    配置导出选项并创建导出任务
                  </DialogDescription>
                </DialogHeader>

                <div className="space-y-6">
                  {/* 导出类型 */}
                  <div className="space-y-3">
                    <Label>导出类型</Label>
                    <div className="grid grid-cols-2 gap-3">
                      {exportTypes.map((type) => {
                        const Icon = type.icon;
                        return (
                          <div
                            key={type.value}
                            className={`p-3 border rounded-lg cursor-pointer transition-colors ${
                              exportConfig.type === type.value
                                ? 'border-blue-500 bg-blue-50'
                                : 'border-gray-200 hover:border-gray-300'
                            }`}
                            onClick={() => setExportConfig({
                              ...exportConfig,
                              type: type.value
                            })}
                          >
                            <div className="flex items-center gap-2">
                              <Icon className="h-4 w-4" />
                              <span className="font-medium">{type.label}</span>
                            </div>
                            <p className="text-xs text-gray-500 mt-1">{type.description}</p>
                          </div>
                        );
                      })}
                    </div>
                  </div>

                  {/* 导出格式 */}
                  <div className="space-y-3">
                    <Label>导出格式</Label>
                    <div className="grid grid-cols-2 gap-3">
                      {formatOptions.map((format) => {
                        const Icon = format.icon;
                        return (
                          <div
                            key={format.value}
                            className={`p-3 border rounded-lg cursor-pointer transition-colors ${
                              exportConfig.options.format === format.value
                                ? 'border-blue-500 bg-blue-50'
                                : 'border-gray-200 hover:border-gray-300'
                            }`}
                            onClick={() => setExportConfig({
                              ...exportConfig,
                              options: {
                                ...exportConfig.options,
                                format: format.value
                              }
                            })}
                          >
                            <div className="flex items-center gap-2">
                              <Icon className="h-4 w-4" />
                              <span className="font-medium">{format.label}</span>
                            </div>
                            <p className="text-xs text-gray-500 mt-1">{format.description}</p>
                          </div>
                        );
                      })}
                    </div>
                  </div>

                  {/* 导出字段 */}
                  <div className="space-y-3">
                    <Label>导出字段</Label>
                    <div className="max-h-40 overflow-y-auto border rounded-md p-3">
                      <div className="grid grid-cols-2 gap-2">
                        {fieldOptions[exportConfig.type]?.map((field) => (
                          <div key={field.value} className="flex items-center space-x-2">
                            <Checkbox
                              id={`field-${field.value}`}
                              checked={
                                !exportConfig.options.fields ||
                                exportConfig.options.fields.includes(field.value)
                              }
                              onCheckedChange={(checked) => {
                                const currentFields = exportConfig.options.fields ||
                                  fieldOptions[exportConfig.type]?.map(f => f.value) || [];

                                let newFields;
                                if (checked) {
                                  newFields = [...new Set([...currentFields, field.value])];
                                } else {
                                  newFields = currentFields.filter(f => f !== field.value);
                                }

                                setExportConfig({
                                  ...exportConfig,
                                  options: {
                                    ...exportConfig.options,
                                    fields: newFields.length > 0 ? newFields : undefined
                                  }
                                });
                              }}
                            />
                            <Label htmlFor={`field-${field.value}`} className="text-sm">
                              {field.label}
                            </Label>
                          </div>
                        ))}
                      </div>
                    </div>
                  </div>

                  {/* 其他选项 */}
                  <div className="space-y-3">
                    <Label>其他选项</Label>
                    <div className="space-y-2">
                      <div className="flex items-center space-x-2">
                        <Checkbox
                          id="include-metadata"
                          checked={exportConfig.options.includeMetadata || false}
                          onCheckedChange={(checked) => setExportConfig({
                            ...exportConfig,
                            options: {
                              ...exportConfig.options,
                              includeMetadata: checked
                            }
                          })}
                        />
                        <Label htmlFor="include-metadata" className="text-sm">
                          包含元数据
                        </Label>
                      </div>

                      <div className="flex items-center space-x-2">
                        <Checkbox
                          id="include-statistics"
                          checked={exportConfig.options.includeStatistics || false}
                          onCheckedChange={(checked) => setExportConfig({
                            ...exportConfig,
                            options: {
                              ...exportConfig.options,
                              includeStatistics: checked
                            }
                          })}
                        />
                        <Label htmlFor="include-statistics" className="text-sm">
                          包含统计信息
                        </Label>
                      </div>
                    </div>
                  </div>

                  {/* 当前过滤条件提示 */}
                  {currentFilters && Object.keys(currentFilters).length > 0 && (
                    <div className="bg-blue-50 p-3 rounded-md">
                      <p className="text-sm text-blue-800">
                        <Eye className="h-4 w-4 inline mr-1" />
                        将应用当前的搜索过滤条件
                      </p>
                    </div>
                  )}

                  {/* 选中项目提示 */}
                  {selectedItems.length > 0 && (
                    <div className="bg-green-50 p-3 rounded-md">
                      <p className="text-sm text-green-800">
                        <CheckCircle className="h-4 w-4 inline mr-1" />
                        将导出选中的 {selectedItems.length} 个项目
                      </p>
                    </div>
                  )}
                </div>

                <DialogFooter>
                  <Button variant="outline" onClick={() => setIsExportDialogOpen(false)}>
                    取消
                  </Button>
                  <Button onClick={handleCreateExport} disabled={isLoading}>
                    {isLoading ? (
                      <RefreshCw className="h-4 w-4 mr-2 animate-spin" />
                    ) : (
                      <Download className="h-4 w-4 mr-2" />
                    )}
                    创建导出任务
                  </Button>
                </DialogFooter>
              </DialogContent>
            </Dialog>
          </div>
        </CardHeader>
      </Card>

      {/* 导出任务列表 */}
      <Card>
        <CardHeader>
          <CardTitle>导出任务</CardTitle>
          <CardDescription>
            查看和管理导出任务
          </CardDescription>
        </CardHeader>
        <CardContent>
          {exportJobs.length === 0 ? (
            <div className="text-center py-8 text-gray-500">
              <Download className="h-12 w-12 mx-auto mb-4 opacity-50" />
              <p>暂无导出任务</p>
            </div>
          ) : (
            <div className="space-y-4">
              {exportJobs.map((job) => (
                <div key={job.id} className="border rounded-lg p-4">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-3">
                      {getStatusIcon(job.status)}
                      <div>
                        <p className="font-medium">
                          {exportTypes.find(t => t.value === job.type)?.label || job.type}
                        </p>
                        <p className="text-sm text-gray-500">
                          {job.options.format.toUpperCase()} • 创建于 {new Date(job.createdAt).toLocaleString()}
                        </p>
                      </div>
                    </div>

                    <div className="flex items-center gap-2">
                      <Badge variant={
                        job.status === 'completed' ? 'default' :
                        job.status === 'failed' ? 'destructive' :
                        'secondary'
                      }>
                        {getStatusText(job.status)}
                      </Badge>

                      {job.status === 'completed' && job.downloadUrl && (
                        <Button variant="outline" size="sm" onClick={() => handleDownload(job)}>
                          <Download className="h-4 w-4" />
                        </Button>
                      )}

                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => handleDeleteJob(job.id)}
                        className="text-red-600 hover:text-red-700"
                      >
                        <Trash2 className="h-4 w-4" />
                      </Button>
                    </div>
                  </div>

                  {(job.status === 'processing' || job.status === 'completed') && (
                    <div className="mt-3">
                      <div className="flex justify-between text-sm text-gray-500 mb-1">
                        <span>进度: {job.processedItems}/{job.totalItems}</span>
                        {job.fileSize && (
                          <span>文件大小: {formatFileSize(job.fileSize)}</span>
                        )}
                      </div>
                      <Progress value={job.progress} className="w-full" />
                    </div>
                  )}

                  {job.error && (
                    <div className="mt-3 text-sm text-red-600 bg-red-50 p-2 rounded">
                      错误: {job.error}
                    </div>
                  )}
                </div>
              ))}
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
};

export default ExportPanel;
