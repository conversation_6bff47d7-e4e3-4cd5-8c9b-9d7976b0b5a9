import { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { 
  Smartphone, 
  Monitor, 
  Tablet, 
  Info, 
  ArrowLeft,
  Settings,
  Eye
} from 'lucide-react';
import { cn } from '@/lib/utils';
import MobileStoryCard from '@/components/story/MobileStoryCard';
import GestureHint from '@/components/story/GestureHint';
import MobileFilterDrawer from '@/components/story/MobileFilterDrawer';
import { STORY_TAGS, STORY_CATEGORIES, EDUCATION_LEVELS, INDUSTRIES } from '@/constants/storyConstants';

// 模拟故事数据
const mockStories = [
  {
    id: 1,
    title: '从零基础到前端工程师的转行之路',
    content: '我是一名文科生，毕业后在传统行业工作了三年，感觉没有发展前景。去年决定转行学习前端开发，通过自学和参加培训班，现在已经成功入职一家互联网公司。这个过程虽然艰难，但收获很大。想分享一些经验给同样想转行的朋友们...',
    author: '转行小白',
    likes: 156,
    createdAt: '2024-01-15',
    category: '成功故事',
    educationLevel: '本科',
    industry: 'IT行业',
    tags: ['转行经历', 'IT行业', '自学成才']
  },
  {
    id: 2,
    title: '应届生求职面试经验分享',
    content: '作为一名应届毕业生，我在求职过程中经历了很多挫折和成长。从最初的紧张不安到后来的从容应对，我总结了一些面试技巧和心得体会。希望能帮助到正在求职路上的同学们...',
    author: '应届毕业生',
    likes: 89,
    createdAt: '2024-01-20',
    category: '求职经验',
    educationLevel: '本科',
    industry: '金融行业',
    tags: ['面试经验', '求职故事', '应届生']
  },
  {
    id: 3,
    title: '海外留学归国就业心得',
    content: '在海外留学三年，回国后发现就业市场和预期有很大差异。经过半年的求职经历，最终找到了满意的工作。想分享一下海归求职的一些注意事项和建议...',
    author: '海归小伙伴',
    likes: 67,
    createdAt: '2024-01-25',
    category: '求职经验',
    educationLevel: '硕士',
    industry: '教育行业',
    tags: ['海外就业', '硕士经验', '求职故事']
  }
];

export default function MobileStoryWallDemo() {
  const [viewMode, setViewMode] = useState<'mobile' | 'tablet' | 'desktop'>('mobile');
  const [showGestureHint, setShowGestureHint] = useState(true);
  const [showFilterDrawer, setShowFilterDrawer] = useState(false);
  const [selectedTags, setSelectedTags] = useState<string[]>([]);
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedCategory, setSelectedCategory] = useState('all');
  const [selectedEducationLevel, setSelectedEducationLevel] = useState('all');
  const [selectedIndustry, setSelectedIndustry] = useState('all');

  // 获取视口尺寸类名
  const getViewportClass = () => {
    switch (viewMode) {
      case 'mobile':
        return 'max-w-sm mx-auto';
      case 'tablet':
        return 'max-w-2xl mx-auto';
      case 'desktop':
        return 'max-w-6xl mx-auto';
      default:
        return 'max-w-sm mx-auto';
    }
  };

  // 处理故事操作
  const handleStoryShare = (story: any) => {
    alert(`分享故事: ${story.title}`);
  };

  const handleStoryLike = (story: any) => {
    alert(`点赞故事: ${story.title}`);
  };

  const handleStoryBookmark = (story: any) => {
    alert(`收藏故事: ${story.title}`);
  };

  const handleStoryView = (story: any) => {
    alert(`查看故事详情: ${story.title}`);
  };

  const handleClearFilters = () => {
    setSelectedTags([]);
    setSearchQuery('');
    setSelectedCategory('all');
    setSelectedEducationLevel('all');
    setSelectedIndustry('all');
  };

  return (
    <div className="min-h-screen bg-gray-50">
      {/* 顶部控制栏 */}
      <div className="bg-white border-b border-gray-200 sticky top-0 z-40">
        <div className="max-w-6xl mx-auto px-4 py-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-4">
              <Button
                variant="ghost"
                size="sm"
                onClick={() => window.history.back()}
                className="flex items-center gap-2"
              >
                <ArrowLeft className="h-4 w-4" />
                返回
              </Button>
              
              <div className="flex items-center gap-2">
                <Smartphone className="h-5 w-5 text-blue-600" />
                <h1 className="text-lg font-semibold">移动端故事墙演示</h1>
              </div>
            </div>

            {/* 视图模式切换 */}
            <div className="flex items-center gap-2">
              <span className="text-sm text-gray-600">视图模式:</span>
              <div className="flex border border-gray-300 rounded-lg overflow-hidden">
                <Button
                  variant={viewMode === 'mobile' ? 'default' : 'ghost'}
                  size="sm"
                  onClick={() => setViewMode('mobile')}
                  className="rounded-none border-0"
                >
                  <Smartphone className="h-4 w-4" />
                </Button>
                <Button
                  variant={viewMode === 'tablet' ? 'default' : 'ghost'}
                  size="sm"
                  onClick={() => setViewMode('tablet')}
                  className="rounded-none border-0"
                >
                  <Tablet className="h-4 w-4" />
                </Button>
                <Button
                  variant={viewMode === 'desktop' ? 'default' : 'ghost'}
                  size="sm"
                  onClick={() => setViewMode('desktop')}
                  className="rounded-none border-0"
                >
                  <Monitor className="h-4 w-4" />
                </Button>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* 功能说明 */}
      <div className="max-w-6xl mx-auto px-4 py-6">
        <Card className="mb-6">
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Info className="h-5 w-5 text-blue-600" />
              移动端优化功能演示
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div className="text-center p-4 bg-blue-50 rounded-lg">
                <div className="w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-3">
                  <Settings className="h-6 w-6 text-blue-600" />
                </div>
                <h3 className="font-medium mb-2">响应式筛选</h3>
                <p className="text-sm text-gray-600">移动端使用抽屉式筛选，桌面端使用侧边栏筛选</p>
              </div>
              
              <div className="text-center p-4 bg-green-50 rounded-lg">
                <div className="w-12 h-12 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-3">
                  <Eye className="h-6 w-6 text-green-600" />
                </div>
                <h3 className="font-medium mb-2">触摸手势</h3>
                <p className="text-sm text-gray-600">支持点击查看、右滑点赞、左滑分享等手势操作</p>
              </div>
              
              <div className="text-center p-4 bg-purple-50 rounded-lg">
                <div className="w-12 h-12 bg-purple-100 rounded-full flex items-center justify-center mx-auto mb-3">
                  <Smartphone className="h-6 w-6 text-purple-600" />
                </div>
                <h3 className="font-medium mb-2">移动端优化</h3>
                <p className="text-sm text-gray-600">专为移动设备优化的卡片布局和交互体验</p>
              </div>
            </div>
            
            <div className="mt-4 flex gap-2">
              <Button
                variant="outline"
                size="sm"
                onClick={() => setShowGestureHint(true)}
              >
                显示手势提示
              </Button>
              <Button
                variant="outline"
                size="sm"
                onClick={() => setShowFilterDrawer(true)}
              >
                打开筛选抽屉
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* 故事展示区域 */}
      <div className={cn("px-4 pb-8", getViewportClass())}>
        <div className="space-y-4">
          {mockStories.map((story) => (
            <MobileStoryCard
              key={story.id}
              story={story}
              onShare={handleStoryShare}
              onLike={handleStoryLike}
              onBookmark={handleStoryBookmark}
              onViewDetails={handleStoryView}
            />
          ))}
        </div>
      </div>

      {/* 手势提示 */}
      <GestureHint
        show={showGestureHint}
        onDismiss={() => setShowGestureHint(false)}
      />

      {/* 移动端筛选抽屉 */}
      <MobileFilterDrawer
        open={showFilterDrawer}
        onOpenChange={setShowFilterDrawer}
        tags={STORY_TAGS.filter(tag => tag.id !== 'all')}
        selectedTags={selectedTags}
        onTagsChange={setSelectedTags}
        searchQuery={searchQuery}
        onSearchChange={setSearchQuery}
        categories={STORY_CATEGORIES}
        selectedCategory={selectedCategory}
        onCategoryChange={setSelectedCategory}
        educationLevels={EDUCATION_LEVELS}
        selectedEducationLevel={selectedEducationLevel}
        onEducationLevelChange={setSelectedEducationLevel}
        industries={INDUSTRIES}
        selectedIndustry={selectedIndustry}
        onIndustryChange={setSelectedIndustry}
        onClearFilters={handleClearFilters}
        onApplyFilters={() => setShowFilterDrawer(false)}
      />
    </div>
  );
}
