import React, { useState, useEffect } from 'react';
import { <PERSON><PERSON><PERSON>, Pie, Cell, ResponsiveContainer, Legend, Tooltip } from 'recharts';
import { Button } from '@/components/ui/button';
import { Loader2, RefreshCw } from 'lucide-react';

interface UserRoleData {
  role: string;
  count: number;
  percentage: number;
}

interface UserRoleDistributionChartProps {
  height?: number;
  showControls?: boolean;
}

const UserRoleDistributionChart: React.FC<UserRoleDistributionChartProps> = ({ 
  height = 300,
  showControls = true 
}) => {
  const [data, setData] = useState<UserRoleData[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // 颜色配置
  const COLORS = [
    '#8884d8', // 蓝色 - 普通用户
    '#82ca9d', // 绿色 - 审核员
    '#ffc658', // 黄色 - 管理员
    '#ff7c7c', // 红色 - 超级管理员
    '#8dd1e1', // 浅蓝 - 其他
  ];

  // 角色名称映射
  const roleNameMap: { [key: string]: string } = {
    'user': '普通用户',
    'reviewer': '审核员',
    'admin': '管理员',
    'superadmin': '超级管理员',
    'guest': '访客用户'
  };

  // 获取用户角色分布数据
  const fetchUserRoleData = async () => {
    setLoading(true);
    setError(null);
    
    try {
      const response = await fetch('/api/admin/dashboard/user-roles');
      
      if (!response.ok) {
        throw new Error('获取用户角色分布数据失败');
      }
      
      const result = await response.json();
      
      if (result.success) {
        // 转换数据格式
        const formattedData = result.data.map((item: any) => ({
          role: roleNameMap[item.user_type] || item.user_type,
          count: item.count,
          percentage: item.percentage
        }));
        setData(formattedData);
      } else {
        throw new Error(result.message || '数据格式错误');
      }
    } catch (err) {
      console.error('用户角色分布数据获取错误:', err);
      setError(err instanceof Error ? err.message : '未知错误');
      
      // 使用模拟数据作为降级
      generateMockData();
    } finally {
      setLoading(false);
    }
  };

  // 生成模拟数据
  const generateMockData = () => {
    const mockData: UserRoleData[] = [
      { role: '普通用户', count: 1180, percentage: 85.2 },
      { role: '审核员', count: 120, percentage: 8.7 },
      { role: '管理员', count: 65, percentage: 4.7 },
      { role: '超级管理员', count: 15, percentage: 1.1 },
      { role: '访客用户', count: 5, percentage: 0.3 }
    ];
    
    setData(mockData);
  };

  // 初始化数据
  useEffect(() => {
    fetchUserRoleData();
  }, []);

  // 自定义Tooltip
  const CustomTooltip = ({ active, payload }: any) => {
    if (active && payload && payload.length) {
      const data = payload[0].payload;
      return (
        <div className="bg-white p-3 border rounded-lg shadow-lg">
          <p className="font-medium mb-1">{data.role}</p>
          <p className="text-sm text-gray-600">用户数: {data.count.toLocaleString()}</p>
          <p className="text-sm text-gray-600">占比: {data.percentage.toFixed(1)}%</p>
        </div>
      );
    }
    return null;
  };

  // 自定义标签
  const renderCustomLabel = ({ cx, cy, midAngle, innerRadius, outerRadius, percent }: any) => {
    if (percent < 0.05) return null; // 小于5%不显示标签
    
    const RADIAN = Math.PI / 180;
    const radius = innerRadius + (outerRadius - innerRadius) * 0.5;
    const x = cx + radius * Math.cos(-midAngle * RADIAN);
    const y = cy + radius * Math.sin(-midAngle * RADIAN);

    return (
      <text 
        x={x} 
        y={y} 
        fill="white" 
        textAnchor={x > cx ? 'start' : 'end'} 
        dominantBaseline="central"
        fontSize={12}
        fontWeight="bold"
      >
        {`${(percent * 100).toFixed(0)}%`}
      </text>
    );
  };

  // 处理刷新
  const handleRefresh = () => {
    fetchUserRoleData();
  };

  // 渲染加载状态
  if (loading) {
    return (
      <div className="flex items-center justify-center" style={{ height: `${height}px` }}>
        <Loader2 className="h-8 w-8 animate-spin text-muted-foreground" />
        <span className="ml-2 text-muted-foreground">加载用户角色分布数据...</span>
      </div>
    );
  }

  // 渲染错误状态
  if (error) {
    return (
      <div className="flex flex-col items-center justify-center" style={{ height: `${height}px` }}>
        <p className="text-red-500 mb-2">加载失败: {error}</p>
        <Button variant="outline" size="sm" onClick={handleRefresh}>
          <RefreshCw className="h-4 w-4 mr-2" />
          重试
        </Button>
      </div>
    );
  }

  return (
    <div className="w-full">
      {showControls && (
        <div className="flex justify-end items-center mb-4">
          <Button variant="outline" size="sm" onClick={handleRefresh}>
            <RefreshCw className="h-4 w-4 mr-2" />
            刷新
          </Button>
        </div>
      )}
      
      <div style={{ height: `${height}px` }}>
        <ResponsiveContainer width="100%" height="100%">
          <PieChart>
            <Pie
              data={data}
              cx="50%"
              cy="50%"
              labelLine={false}
              label={renderCustomLabel}
              outerRadius={Math.min(height * 0.35, 120)}
              fill="#8884d8"
              dataKey="count"
            >
              {data.map((entry, index) => (
                <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
              ))}
            </Pie>
            <Tooltip content={<CustomTooltip />} />
            <Legend 
              verticalAlign="bottom" 
              height={36}
              formatter={(value, entry: any) => (
                <span style={{ color: entry.color }}>
                  {value} ({entry.payload.count})
                </span>
              )}
            />
          </PieChart>
        </ResponsiveContainer>
      </div>
      
      {data.length === 0 && (
        <div className="text-center text-muted-foreground mt-4">
          暂无用户角色分布数据
        </div>
      )}
      
      {/* 数据摘要 */}
      {data.length > 0 && (
        <div className="mt-4 grid grid-cols-2 gap-4 text-sm">
          <div className="text-center">
            <p className="text-muted-foreground">总用户数</p>
            <p className="font-semibold text-lg">
              {data.reduce((sum, item) => sum + item.count, 0).toLocaleString()}
            </p>
          </div>
          <div className="text-center">
            <p className="text-muted-foreground">角色类型</p>
            <p className="font-semibold text-lg">{data.length}</p>
          </div>
        </div>
      )}
    </div>
  );
};

export default UserRoleDistributionChart;
