import React, { useState, useEffect } from 'react';
import { useToast } from '@/components/ui/use-toast';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Switch } from '@/components/ui/switch';
import { Label } from '@/components/ui/label';
import { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group';
import { Separator } from '@/components/ui/separator';
import { AlertCircle, Save, Settings, Shield, Info, Award, FileText } from 'lucide-react';
import LoadingSpinner from '@/components/ui/loading-spinner';
import ErrorDisplay from '@/components/ui/error-display';
import ReviewerPerformancePanel from './ReviewerPerformancePanel';
import ReviewTemplatesPanel from './ReviewTemplatesPanel';

// 导入数据服务
import { getReviewSettings, updateReviewSettings, getReviewStats } from '@/services/dataService';

// 审核等级枚举
enum ReviewLevel {
  LOW = 'low',
  MEDIUM = 'medium',
  HIGH = 'high'
}

// 审核设置接口
interface ReviewSettings {
  reviewLevel: ReviewLevel;
  autoRejectHighRisk: boolean;
  notifyOnHighPriority: boolean;
  reviewerEmails?: string[];
  sensitiveWordsThreshold?: number;
  [key: string]: any;
}

// 审核统计接口
interface ReviewStats {
  pending: number;
  approved: number;
  rejected: number;
  edited: number;
  total: number;
  byType: { type: string; count: number }[];
  byFlag: { flags: string; count: number }[];
  daily: { date: string; count: number }[];
}

// 审核设置面板组件
export default function ReviewSettingsPanel() {
  const { toast } = useToast();
  const [settings, setSettings] = useState<ReviewSettings>({
    reviewLevel: ReviewLevel.MEDIUM,
    autoRejectHighRisk: false,
    notifyOnHighPriority: true
  });
  const [stats, setStats] = useState<ReviewStats | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [isStatsLoading, setIsStatsLoading] = useState(true);
  const [isSaving, setIsSaving] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [statsError, setStatsError] = useState<string | null>(null);
  const [activeTab, setActiveTab] = useState('settings');

  // 获取审核设置
  const fetchReviewSettings = async () => {
    try {
      setIsLoading(true);
      setError(null);

      const response = await getReviewSettings();

      if (response.success) {
        setSettings(response.settings);
      } else {
        setError(response.error || '获取审核设置失败');
      }
    } catch (error) {
      console.error('获取审核设置错误:', error);
      setError('服务器错误，请稍后再试');
    } finally {
      setIsLoading(false);
    }
  };

  // 获取审核统计信息
  const fetchReviewStats = async () => {
    try {
      setIsStatsLoading(true);
      setStatsError(null);

      const response = await getReviewStats();

      if (response.success) {
        setStats(response.stats);
      } else {
        setStatsError(response.error || '获取审核统计信息失败');
      }
    } catch (error) {
      console.error('获取审核统计信息错误:', error);
      setStatsError('服务器错误，请稍后再试');
    } finally {
      setIsStatsLoading(false);
    }
  };

  // 更新审核设置
  const handleUpdateSettings = async () => {
    try {
      setIsSaving(true);

      const response = await updateReviewSettings(settings);

      if (response.success) {
        toast({
          title: '保存成功',
          description: '审核设置已更新',
        });
      } else {
        toast({
          title: '保存失败',
          description: response.error || '更新审核设置失败',
          variant: 'destructive'
        });
      }
    } catch (error) {
      console.error('更新审核设置错误:', error);
      toast({
        title: '保存失败',
        description: '服务器错误，请稍后再试',
        variant: 'destructive'
      });
    } finally {
      setIsSaving(false);
    }
  };

  // 组件加载时获取审核设置和统计信息
  useEffect(() => {
    fetchReviewSettings();
    fetchReviewStats();
  }, []);

  // 切换标签时刷新数据
  useEffect(() => {
    if (activeTab === 'stats') {
      fetchReviewStats();
    }
  }, [activeTab]);

  // 处理设置变更
  const handleSettingChange = (key: string, value: any) => {
    setSettings(prev => ({
      ...prev,
      [key]: value
    }));
  };

  // 渲染加载状态
  if (isLoading) {
    return (
      <div className="flex justify-center items-center py-12">
        <LoadingSpinner size="lg" text="加载审核设置中..." />
      </div>
    );
  }

  // 渲染错误状态
  if (error) {
    return (
      <ErrorDisplay
        title="加载失败"
        message={error}
        onRetry={fetchReviewSettings}
      />
    );
  }

  return (
    <div className="space-y-4">
      <Tabs value={activeTab} onValueChange={setActiveTab}>
        <TabsList>
          <TabsTrigger value="settings">
            <Settings className="h-4 w-4 mr-2" />
            审核设置
          </TabsTrigger>
          <TabsTrigger value="stats">
            <Info className="h-4 w-4 mr-2" />
            审核统计
          </TabsTrigger>
          <TabsTrigger value="performance">
            <Award className="h-4 w-4 mr-2" />
            绩效统计
          </TabsTrigger>
          <TabsTrigger value="templates">
            <FileText className="h-4 w-4 mr-2" />
            审核模板
          </TabsTrigger>
        </TabsList>

        <TabsContent value="settings" className="space-y-4 mt-4">
          <Card>
            <CardHeader>
              <CardTitle>内容审核策略</CardTitle>
              <CardDescription>
                设置内容审核的严格程度和处理方式
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="space-y-4">
                <h3 className="text-lg font-medium">审核等级</h3>

                <RadioGroup
                  value={settings.reviewLevel}
                  onValueChange={(value) => handleSettingChange('reviewLevel', value)}
                >
                  <div className="flex items-start space-x-2 mb-4">
                    <RadioGroupItem value={ReviewLevel.LOW} id="level-low" />
                    <div className="grid gap-1.5">
                      <Label htmlFor="level-low" className="font-medium">
                        低级别
                      </Label>
                      <p className="text-sm text-muted-foreground">
                        所有内容直接发布，不需要人工审核。适用于内部或低风险场景。
                      </p>
                    </div>
                  </div>

                  <div className="flex items-start space-x-2 mb-4">
                    <RadioGroupItem value={ReviewLevel.MEDIUM} id="level-medium" />
                    <div className="grid gap-1.5">
                      <Label htmlFor="level-medium" className="font-medium">
                        中级别
                      </Label>
                      <p className="text-sm text-muted-foreground">
                        系统判定为敏感或可疑的内容需要人工审核，其他内容直接发布。平衡效率和安全性。
                      </p>
                    </div>
                  </div>

                  <div className="flex items-start space-x-2">
                    <RadioGroupItem value={ReviewLevel.HIGH} id="level-high" />
                    <div className="grid gap-1.5">
                      <Label htmlFor="level-high" className="font-medium">
                        高级别
                      </Label>
                      <p className="text-sm text-muted-foreground">
                        所有内容都需要人工审核后才能发布。适用于高风险或高敏感度场景。
                      </p>
                    </div>
                  </div>
                </RadioGroup>
              </div>

              <Separator />

              <div className="space-y-4">
                <h3 className="text-lg font-medium">高级设置</h3>

                <div className="flex items-center justify-between">
                  <div className="space-y-0.5">
                    <Label htmlFor="auto-reject">自动拒绝高风险内容</Label>
                    <p className="text-sm text-muted-foreground">
                      系统判定为高风险的内容将被自动拒绝，不进入审核队列
                    </p>
                  </div>
                  <Switch
                    id="auto-reject"
                    checked={settings.autoRejectHighRisk}
                    onCheckedChange={(checked) => handleSettingChange('autoRejectHighRisk', checked)}
                  />
                </div>

                <div className="flex items-center justify-between">
                  <div className="space-y-0.5">
                    <Label htmlFor="notify-high-priority">高优先级内容通知</Label>
                    <p className="text-sm text-muted-foreground">
                      当有高优先级内容需要审核时，通过邮件通知审核员
                    </p>
                  </div>
                  <Switch
                    id="notify-high-priority"
                    checked={settings.notifyOnHighPriority}
                    onCheckedChange={(checked) => handleSettingChange('notifyOnHighPriority', checked)}
                  />
                </div>
              </div>
            </CardContent>
            <CardFooter>
              <Button
                onClick={handleUpdateSettings}
                disabled={isSaving}
              >
                {isSaving ? <LoadingSpinner size="sm" /> : <Save className="h-4 w-4 mr-2" />}
                保存设置
              </Button>
            </CardFooter>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>审核提示</CardTitle>
              <CardDescription>
                关于内容审核的重要提示
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="bg-yellow-50 border border-yellow-200 rounded-md p-4 text-yellow-800">
                <div className="flex items-start">
                  <AlertCircle className="h-5 w-5 mr-2 mt-0.5" />
                  <div>
                    <h4 className="font-medium">审核责任提醒</h4>
                    <ul className="mt-2 space-y-1 list-disc list-inside text-sm">
                      <li>所有审核操作都会被记录，包括审核员信息、时间和操作内容</li>
                      <li>请确保审核内容符合平台规范和法律法规</li>
                      <li>对于敏感内容，建议咨询相关专业人员后再做决定</li>
                      <li>如发现违法内容，请立即拒绝并向管理员报告</li>
                    </ul>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="stats" className="space-y-4 mt-4">
          {isStatsLoading ? (
            <div className="flex justify-center items-center py-12">
              <LoadingSpinner size="md" text="加载审核统计中..." />
            </div>
          ) : statsError ? (
            <ErrorDisplay
              title="加载失败"
              message={statsError}
              onRetry={fetchReviewStats}
            />
          ) : stats ? (
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <Card>
                <CardHeader>
                  <CardTitle>审核概览</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="grid grid-cols-2 gap-4">
                    <div className="bg-blue-50 p-4 rounded-md">
                      <p className="text-sm text-blue-600">待审核</p>
                      <p className="text-2xl font-bold">{stats.pending}</p>
                    </div>
                    <div className="bg-green-50 p-4 rounded-md">
                      <p className="text-sm text-green-600">已通过</p>
                      <p className="text-2xl font-bold">{stats.approved}</p>
                    </div>
                    <div className="bg-red-50 p-4 rounded-md">
                      <p className="text-sm text-red-600">已拒绝</p>
                      <p className="text-2xl font-bold">{stats.rejected}</p>
                    </div>
                    <div className="bg-amber-50 p-4 rounded-md">
                      <p className="text-sm text-amber-600">已编辑</p>
                      <p className="text-2xl font-bold">{stats.edited}</p>
                    </div>
                  </div>
                  <div className="mt-4">
                    <p className="text-sm text-muted-foreground">总计: {stats.total} 条内容</p>
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle>内容类型分布</CardTitle>
                </CardHeader>
                <CardContent>
                  {stats.byType.length > 0 ? (
                    <div className="space-y-2">
                      {stats.byType.map((item: any) => (
                        <div key={item.type} className="flex justify-between items-center">
                          <span>{item.type === 'story' ? '故事' : '问卷'}</span>
                          <span className="font-medium">{item.count}</span>
                        </div>
                      ))}
                    </div>
                  ) : (
                    <p className="text-muted-foreground">暂无数据</p>
                  )}
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle>标记分布</CardTitle>
                </CardHeader>
                <CardContent>
                  {stats.byFlag.length > 0 ? (
                    <div className="space-y-2">
                      {stats.byFlag.map((item: any) => (
                        <div key={item.flags} className="flex justify-between items-center">
                          <span>{item.flags || '无标记'}</span>
                          <span className="font-medium">{item.count}</span>
                        </div>
                      ))}
                    </div>
                  ) : (
                    <p className="text-muted-foreground">暂无数据</p>
                  )}
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle>最近7天审核</CardTitle>
                </CardHeader>
                <CardContent>
                  {stats.daily.length > 0 ? (
                    <div className="space-y-2">
                      {stats.daily.map((item: any) => (
                        <div key={item.date} className="flex justify-between items-center">
                          <span>{new Date(item.date).toLocaleDateString()}</span>
                          <span className="font-medium">{item.count}</span>
                        </div>
                      ))}
                    </div>
                  ) : (
                    <p className="text-muted-foreground">暂无数据</p>
                  )}
                </CardContent>
              </Card>
            </div>
          ) : (
            <p className="text-center py-8">暂无统计数据</p>
          )}

          <div className="flex justify-end">
            <Button
              variant="outline"
              onClick={fetchReviewStats}
              disabled={isStatsLoading}
            >
              {isStatsLoading ? <LoadingSpinner size="sm" /> : '刷新数据'}
            </Button>
          </div>
        </TabsContent>

        <TabsContent value="performance" className="mt-4">
          <ReviewerPerformancePanel />
        </TabsContent>

        <TabsContent value="templates" className="mt-4">
          <ReviewTemplatesPanel />
        </TabsContent>
      </Tabs>
    </div>
  );
}
