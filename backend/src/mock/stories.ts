/**
 * 故事模拟数据
 */

export interface MockStory {
  id: number;
  sequenceNumber: string;
  title: string;
  content: string;
  author: string;
  isAnonymous: boolean;
  createdAt: string;
  updatedAt: string;
  status: 'pending' | 'approved' | 'rejected';
  tags?: string[];
  category?: string;
  educationLevel?: string;
  industry?: string;
  likes?: number;
  dislikes?: number;
}

/**
 * 模拟故事数据
 */
export const mockStories: MockStory[] = [
  {
    id: 1,
    sequenceNumber: 'S-00001',
    title: '我的第一份工作经历',
    content: '毕业后，我找到了一份软件开发的工作。虽然薪资不高，但是学到了很多东西。在这家公司，我负责开发和维护一个电子商务网站。工作内容包括前端开发、后端API实现和数据库优化。\n\n最大的挑战是需要快速适应新技术栈，因为公司使用的技术与我在学校学习的有很大不同。但通过自学和向同事请教，我逐渐掌握了这些技术。\n\n这段经历让我明白，实际工作环境与学校教育有很大差异，持续学习的能力比掌握特定技术更重要。',
    author: '张三',
    isAnonymous: false,
    createdAt: new Date(Date.now() - 3 * 86400000).toISOString(),
    updatedAt: new Date(Date.now() - 3 * 86400000).toISOString(),
    status: 'pending',
    tags: ['软件开发', '职场新人', '技术学习'],
    category: '就业经历',
    educationLevel: '本科',
    industry: '互联网',
    likes: 15,
    dislikes: 2
  },
  {
    id: 2,
    sequenceNumber: 'S-00002',
    title: '转行经历分享',
    content: '我从市场营销转到了产品经理，这是一段艰难但值得的旅程。最初的动力是发现自己对产品设计和用户体验更感兴趣，而不是纯粹的市场推广。\n\n转行过程中，我参加了一些产品管理的在线课程，并利用业余时间参与了几个开源项目，积累了一些实际经验。最困难的部分是说服招聘经理给我机会，因为我没有正式的产品经理经验。\n\n最终，我通过展示自己在开源项目中的贡献和对产品的理解，成功获得了一个初级产品经理的职位。虽然薪资有所下降，但我对工作的满意度大大提高了。',
    author: '匿名用户',
    isAnonymous: true,
    createdAt: new Date(Date.now() - 2 * 86400000).toISOString(),
    updatedAt: new Date(Date.now() - 2 * 86400000).toISOString(),
    status: 'pending',
    tags: ['转行', '产品经理', '职业发展'],
    category: '职业转变',
    educationLevel: '硕士',
    industry: '互联网',
    likes: 28,
    dislikes: 3
  },
  {
    id: 3,
    sequenceNumber: 'S-00003',
    title: '实习生活的酸甜苦辣',
    content: '在大公司实习的半年，让我对职场有了全新的认识。作为一名人力资源实习生，我参与了招聘、培训和员工关系等多个方面的工作。\n\n最大的收获是了解了企业的运作方式和职场文化。我发现，学校教授的理论知识在实际工作中往往需要灵活应用，而不是生搬硬套。\n\n实习期间最大的挑战是时间管理。我需要同时处理多个任务，并且保证每个任务的质量。这锻炼了我的优先级管理能力和抗压能力。\n\n这段经历让我确定了自己想要从事人力资源工作，并且明确了未来需要提升的技能方向。',
    author: '李四',
    isAnonymous: false,
    createdAt: new Date(Date.now() - 1 * 86400000).toISOString(),
    updatedAt: new Date(Date.now() - 1 * 86400000).toISOString(),
    status: 'pending',
    tags: ['实习', '人力资源', '职场经验'],
    category: '实习经历',
    educationLevel: '本科',
    industry: '咨询',
    likes: 10,
    dislikes: 1
  },
  {
    id: 4,
    sequenceNumber: 'S-00004',
    title: '研究生求职经历',
    content: '作为一名计算机科学研究生，我的求职经历可能对同专业的学弟学妹有所帮助。我主要投递了互联网公司和金融科技公司的技术岗位。\n\n准备过程中，我发现技术面试和学术研究有很大不同。企业更关注实际解决问题的能力，而不是理论研究。因此，我花了大量时间刷题和参与开源项目，提升自己的编程能力。\n\n面试中最常被问到的是数据结构、算法和系统设计问题。此外，我的研究方向（机器学习）也是面试官关注的重点。\n\n最终，我收到了三家公司的offer，选择了一家提供AI研究岗位的科技公司，能够将我的研究兴趣与实际工作结合起来。',
    author: '王五',
    isAnonymous: false,
    createdAt: new Date(Date.now() - 5 * 86400000).toISOString(),
    updatedAt: new Date(Date.now() - 5 * 86400000).toISOString(),
    status: 'approved',
    tags: ['研究生', '求职', '技术面试', '人工智能'],
    category: '求职经历',
    educationLevel: '硕士',
    industry: '科技',
    likes: 42,
    dislikes: 3
  },
  {
    id: 5,
    sequenceNumber: 'S-00005',
    title: '从校园到创业',
    content: '毕业后直接选择创业是一条充满挑战的道路。我和两位同学一起创办了一家教育科技公司，希望通过技术手段提升教育效率。\n\n创业初期，我们面临的最大问题是资金不足和经验缺乏。我们通过参加创业比赛获得了一些启动资金，并找到了愿意指导我们的导师。\n\n创业两年来，我学到的最重要的一课是：创业不仅仅是关于好点子，更是关于执行力和团队协作。一个普通的点子加上出色的执行，比一个绝妙的点子加上平庸的执行更有可能成功。\n\n虽然我们的公司目前规模还不大，但已经实现了盈利，并帮助了数千名学生提升学习效果。这种成就感是任何工作都无法比拟的。',
    author: '匿名用户',
    isAnonymous: true,
    createdAt: new Date(Date.now() - 10 * 86400000).toISOString(),
    updatedAt: new Date(Date.now() - 10 * 86400000).toISOString(),
    status: 'approved',
    tags: ['创业', '教育科技', '团队协作'],
    category: '创业经历',
    educationLevel: '本科',
    industry: '教育',
    likes: 56,
    dislikes: 4
  }
];
