/**
 * 管理员审计服务
 *
 * 提供管理员审计相关的 API 调用
 */

import { apiClient } from './apiClient';

// 审计日志类型
export interface AuditLog {
  id: string;
  userId: string;
  username: string;
  role: 'admin' | 'superadmin';
  action: string;
  resource: string;
  resourceId?: string;
  details?: string;
  ip?: string;
  userAgent?: string;
  status: 'success' | 'failure';
  timestamp: string;
  riskLevel: 'low' | 'medium' | 'high' | 'critical';
}

// 审计日志搜索参数
export interface AuditLogSearchParams {
  userId?: string;
  username?: string;
  role?: 'admin' | 'superadmin';
  action?: string;
  resource?: string;
  status?: 'success' | 'failure';
  riskLevel?: 'low' | 'medium' | 'high' | 'critical';
  startDate?: string;
  endDate?: string;
  page?: number;
  limit?: number;
}

// 模拟审计日志数据
const mockAuditLogs: AuditLog[] = [
  {
    id: '1',
    userId: '101',
    username: 'admin1',
    role: 'admin',
    action: 'login',
    resource: 'system',
    status: 'success',
    timestamp: '2023-05-20T08:30:45Z',
    ip: '*************',
    userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
    riskLevel: 'low'
  },
  {
    id: '2',
    userId: '102',
    username: 'admin2',
    role: 'admin',
    action: 'update',
    resource: 'user',
    resourceId: '201',
    details: '修改用户角色: user -> reviewer',
    status: 'success',
    timestamp: '2023-05-20T09:15:22Z',
    ip: '*************',
    userAgent: 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
    riskLevel: 'medium'
  },
  {
    id: '3',
    userId: '103',
    username: 'superadmin1',
    role: 'superadmin',
    action: 'delete',
    resource: 'user',
    resourceId: '202',
    details: '删除用户',
    status: 'success',
    timestamp: '2023-05-20T10:05:18Z',
    ip: '*************',
    userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
    riskLevel: 'high'
  },
  {
    id: '4',
    userId: '101',
    username: 'admin1',
    role: 'admin',
    action: 'update',
    resource: 'system_settings',
    details: '修改系统设置: 内容审核阈值',
    status: 'failure',
    timestamp: '2023-05-20T11:30:45Z',
    ip: '*************',
    userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
    riskLevel: 'critical'
  },
  {
    id: '5',
    userId: '103',
    username: 'superadmin1',
    role: 'superadmin',
    action: 'create',
    resource: 'role',
    resourceId: '301',
    details: '创建新角色: 数据分析师',
    status: 'success',
    timestamp: '2023-05-20T13:45:12Z',
    ip: '*************',
    userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
    riskLevel: 'medium'
  },
  {
    id: '6',
    userId: '102',
    username: 'admin2',
    role: 'admin',
    action: 'approve',
    resource: 'story',
    resourceId: '401',
    details: '审核通过故事',
    status: 'success',
    timestamp: '2023-05-20T14:22:33Z',
    ip: '*************',
    userAgent: 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
    riskLevel: 'low'
  },
  {
    id: '7',
    userId: '103',
    username: 'superadmin1',
    role: 'superadmin',
    action: 'update',
    resource: 'security_settings',
    details: '修改安全设置: 密码策略',
    status: 'success',
    timestamp: '2023-05-20T15:10:05Z',
    ip: '*************',
    userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
    riskLevel: 'high'
  },
  {
    id: '8',
    userId: '101',
    username: 'admin1',
    role: 'admin',
    action: 'reject',
    resource: 'questionnaire',
    resourceId: '501',
    details: '拒绝问卷提交',
    status: 'success',
    timestamp: '2023-05-20T16:05:45Z',
    ip: '*************',
    userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
    riskLevel: 'low'
  },
  {
    id: '9',
    userId: '102',
    username: 'admin2',
    role: 'admin',
    action: 'export',
    resource: 'data',
    details: '导出数据分析结果',
    status: 'success',
    timestamp: '2023-05-20T17:30:22Z',
    ip: '*************',
    userAgent: 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
    riskLevel: 'medium'
  },
  {
    id: '10',
    userId: '103',
    username: 'superadmin1',
    role: 'superadmin',
    action: 'backup',
    resource: 'system',
    details: '系统数据备份',
    status: 'success',
    timestamp: '2023-05-20T18:45:10Z',
    ip: '*************',
    userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
    riskLevel: 'medium'
  }
];

/**
 * 获取审计日志
 * @param params 搜索参数
 * @returns 审计日志列表
 */
export async function getAuditLogs(params: AuditLogSearchParams = {}) {
  try {
    console.log('🔍 正在调用审计日志API:', params);

    // 构建查询参数
    const queryParams = new URLSearchParams();
    queryParams.append('page', String(params.page || 1));
    queryParams.append('limit', String(params.limit || 10));

    if (params.username) queryParams.append('username', params.username);
    if (params.role) queryParams.append('role', params.role);
    if (params.action) queryParams.append('action', params.action);
    if (params.resource) queryParams.append('resource', params.resource);
    if (params.status) queryParams.append('status', params.status);
    if (params.riskLevel) queryParams.append('riskLevel', params.riskLevel);
    if (params.startDate) queryParams.append('startDate', params.startDate);
    if (params.endDate) queryParams.append('endDate', params.endDate);

    const apiUrl = `http://localhost:8787/api/admin/audit-logs?${queryParams}`;
    console.log('🔍 正在调用审计日志API:', apiUrl);

    // 调用真实API
    const response = await fetch(apiUrl);
    console.log('📡 审计日志API响应状态:', response.status);

    if (!response.ok) {
      throw new Error(`API调用失败: ${response.status}`);
    }

    const data = await response.json();
    console.log('📊 审计日志API返回数据:', data);

    if (data.success && data.data) {
      return {
        success: true,
        data: {
          logs: data.data.logs || [],
          total: data.data.total || 0,
          page: data.data.page || 1,
          limit: data.data.limit || 10,
          totalPages: data.data.totalPages || 1
        }
      };
    } else {
      console.warn('⚠️ 审计日志API返回数据格式异常:', data);
      throw new Error(data.message || '获取审计日志失败');
    }
  } catch (error) {
    console.error('❌ 获取审计日志失败:', error);
    throw error;
  }
}

/**
 * 获取审计日志详情
 * @param id 审计日志ID
 * @returns 审计日志详情
 */
export async function getAuditLogDetails(id: string) {
  try {
    console.log('🔍 正在调用审计日志详情API:', id);

    const apiUrl = `http://localhost:8787/api/admin/audit-logs/${id}`;
    console.log('🔍 正在调用审计日志详情API:', apiUrl);

    // 调用真实API
    const response = await fetch(apiUrl);
    console.log('📡 审计日志详情API响应状态:', response.status);

    if (!response.ok) {
      throw new Error(`API调用失败: ${response.status}`);
    }

    const data = await response.json();
    console.log('📊 审计日志详情API返回数据:', data);

    if (data.success && data.data) {
      return {
        success: true,
        data: data.data
      };
    } else {
      console.warn('⚠️ 审计日志详情API返回数据格式异常:', data);
      throw new Error(data.message || '获取审计日志详情失败');
    }
  } catch (error) {
    console.error('❌ 获取审计日志详情失败:', error);
    throw error;
  }
}

/**
 * 导出审计日志
 * @param params 搜索参数
 * @returns 导出结果
 */
export async function exportAuditLogs(params: AuditLogSearchParams = {}) {
  try {
    console.log('📤 正在调用导出审计日志API:', params);

    const apiUrl = `http://localhost:8787/api/admin/audit-logs/export`;
    console.log('📤 正在调用导出审计日志API:', apiUrl);

    // 调用真实API
    const response = await fetch(apiUrl, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(params)
    });

    console.log('📡 导出审计日志API响应状态:', response.status);

    if (!response.ok) {
      throw new Error(`API调用失败: ${response.status}`);
    }

    const data = await response.json();
    console.log('📊 导出审计日志API返回数据:', data);

    if (data.success) {
      return {
        success: true,
        message: data.message || '审计日志导出成功',
        data: data.data
      };
    } else {
      console.warn('⚠️ 导出审计日志API返回数据格式异常:', data);
      throw new Error(data.message || '导出审计日志失败');
    }
  } catch (error) {
    console.error('❌ 导出审计日志失败:', error);
    throw error;
  }
}
