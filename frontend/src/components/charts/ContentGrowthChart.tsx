import React, { useState, useEffect } from 'react';
import { <PERSON><PERSON>hart, Line, XAxis, YAxis, CartesianGrid, <PERSON>lt<PERSON>, Responsive<PERSON><PERSON><PERSON>, <PERSON> } from 'recharts';
import { Button } from '@/components/ui/button';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Loader2, RefreshCw } from 'lucide-react';

interface ContentGrowthData {
  date: string;
  totalStories: number;
  newStories: number;
  totalVoices: number;
  newVoices: number;
  totalResponses: number;
  newResponses: number;
}

interface ContentGrowthChartProps {
  height?: number;
  timeRange?: 'week' | 'month' | 'quarter' | 'year';
  showControls?: boolean;
}

const ContentGrowthChart: React.FC<ContentGrowthChartProps> = ({ 
  height = 300, 
  timeRange = 'month',
  showControls = true 
}) => {
  const [data, setData] = useState<ContentGrowthData[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [selectedTimeRange, setSelectedTimeRange] = useState(timeRange);
  const [selectedContent, setSelectedContent] = useState<'all' | 'stories' | 'voices' | 'responses'>('all');

  // 获取内容增长数据
  const fetchContentGrowthData = async () => {
    setLoading(true);
    setError(null);
    
    try {
      const response = await fetch(`/api/admin/dashboard/content-growth?range=${selectedTimeRange}`);
      
      if (!response.ok) {
        throw new Error('获取内容增长数据失败');
      }
      
      const result = await response.json();
      
      if (result.success) {
        setData(result.data);
      } else {
        throw new Error(result.message || '数据格式错误');
      }
    } catch (err) {
      console.error('内容增长数据获取错误:', err);
      setError(err instanceof Error ? err.message : '未知错误');
      
      // 使用模拟数据作为降级
      generateMockData();
    } finally {
      setLoading(false);
    }
  };

  // 生成模拟数据
  const generateMockData = () => {
    const now = new Date();
    const days = selectedTimeRange === 'week' ? 7 : 
                 selectedTimeRange === 'month' ? 30 : 
                 selectedTimeRange === 'quarter' ? 90 : 365;
    
    const mockData: ContentGrowthData[] = [];
    let totalStories = 100;
    let totalVoices = 80;
    let totalResponses = 200;
    
    for (let i = days - 1; i >= 0; i--) {
      const date = new Date(now);
      date.setDate(date.getDate() - i);
      
      const newStories = Math.floor(Math.random() * 8) + 2;
      const newVoices = Math.floor(Math.random() * 6) + 1;
      const newResponses = Math.floor(Math.random() * 15) + 5;
      
      totalStories += newStories;
      totalVoices += newVoices;
      totalResponses += newResponses;
      
      mockData.push({
        date: date.toISOString().split('T')[0],
        totalStories,
        newStories,
        totalVoices,
        newVoices,
        totalResponses,
        newResponses
      });
    }
    
    setData(mockData);
  };

  // 初始化和刷新数据
  useEffect(() => {
    fetchContentGrowthData();
  }, [selectedTimeRange]);

  // 自定义Tooltip
  const CustomTooltip = ({ active, payload, label }: any) => {
    if (active && payload && payload.length) {
      return (
        <div className="bg-white p-3 border rounded-lg shadow-lg">
          <p className="font-medium mb-2">{`日期: ${label}`}</p>
          {payload.map((entry: any, index: number) => (
            <p key={index} style={{ color: entry.color }} className="text-sm">
              {`${entry.name}: ${entry.value.toLocaleString()}`}
            </p>
          ))}
        </div>
      );
    }
    return null;
  };

  // 处理刷新
  const handleRefresh = () => {
    fetchContentGrowthData();
  };

  // 渲染加载状态
  if (loading) {
    return (
      <div className="flex items-center justify-center" style={{ height: `${height}px` }}>
        <Loader2 className="h-8 w-8 animate-spin text-muted-foreground" />
        <span className="ml-2 text-muted-foreground">加载内容增长数据...</span>
      </div>
    );
  }

  // 渲染错误状态
  if (error) {
    return (
      <div className="flex flex-col items-center justify-center" style={{ height: `${height}px` }}>
        <p className="text-red-500 mb-2">加载失败: {error}</p>
        <Button variant="outline" size="sm" onClick={handleRefresh}>
          <RefreshCw className="h-4 w-4 mr-2" />
          重试
        </Button>
      </div>
    );
  }

  return (
    <div className="w-full">
      {showControls && (
        <div className="flex justify-between items-center mb-4">
          <div className="flex space-x-2">
            <Select value={selectedTimeRange} onValueChange={(value: any) => setSelectedTimeRange(value)}>
              <SelectTrigger className="w-32">
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="week">最近一周</SelectItem>
                <SelectItem value="month">最近一月</SelectItem>
                <SelectItem value="quarter">最近三月</SelectItem>
                <SelectItem value="year">最近一年</SelectItem>
              </SelectContent>
            </Select>
            
            <Select value={selectedContent} onValueChange={(value: any) => setSelectedContent(value)}>
              <SelectTrigger className="w-32">
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">全部内容</SelectItem>
                <SelectItem value="stories">故事墙</SelectItem>
                <SelectItem value="voices">问卷心声</SelectItem>
                <SelectItem value="responses">问卷回复</SelectItem>
              </SelectContent>
            </Select>
          </div>
          
          <Button variant="outline" size="sm" onClick={handleRefresh}>
            <RefreshCw className="h-4 w-4 mr-2" />
            刷新
          </Button>
        </div>
      )}
      
      <div style={{ height: `${height}px` }}>
        <ResponsiveContainer width="100%" height="100%">
          <LineChart data={data} margin={{ top: 5, right: 30, left: 20, bottom: 5 }}>
            <CartesianGrid strokeDasharray="3 3" />
            <XAxis 
              dataKey="date" 
              tick={{ fontSize: 12 }}
              tickFormatter={(value) => {
                const date = new Date(value);
                return `${date.getMonth() + 1}/${date.getDate()}`;
              }}
            />
            <YAxis tick={{ fontSize: 12 }} />
            <Tooltip content={<CustomTooltip />} />
            <Legend />
            
            {(selectedContent === 'all' || selectedContent === 'stories') && (
              <Line 
                type="monotone" 
                dataKey="newStories" 
                stroke="#8884d8" 
                strokeWidth={2}
                name="新增故事"
                dot={{ r: 3 }}
              />
            )}
            
            {(selectedContent === 'all' || selectedContent === 'voices') && (
              <Line 
                type="monotone" 
                dataKey="newVoices" 
                stroke="#82ca9d" 
                strokeWidth={2}
                name="新增心声"
                dot={{ r: 3 }}
              />
            )}
            
            {(selectedContent === 'all' || selectedContent === 'responses') && (
              <Line 
                type="monotone" 
                dataKey="newResponses" 
                stroke="#ffc658" 
                strokeWidth={2}
                name="新增问卷"
                dot={{ r: 3 }}
              />
            )}
          </LineChart>
        </ResponsiveContainer>
      </div>
      
      {data.length === 0 && (
        <div className="text-center text-muted-foreground mt-4">
          暂无内容增长数据
        </div>
      )}
    </div>
  );
};

export default ContentGrowthChart;
