<template>
  <div class="error-heatmap-container">
    <div class="heatmap-header">
      <h3>{{ title }}</h3>
      <div class="heatmap-controls">
        <select v-model="timeRange" @change="updateHeatmap">
          <option value="day">24小时</option>
          <option value="week">7天</option>
          <option value="month">30天</option>
        </select>
        <select v-model="groupBy" @change="updateHeatmap">
          <option value="hour">按小时</option>
          <option value="day">按天</option>
          <option value="severity">按严重性</option>
          <option value="source">按来源</option>
          <option value="errorCode">按错误代码</option>
        </select>
      </div>
    </div>
    
    <div class="heatmap-legend">
      <div class="legend-item" v-for="(color, index) in legendColors" :key="index">
        <div class="legend-color" :style="{ backgroundColor: color }"></div>
        <div class="legend-label">{{ legendLabels[index] }}</div>
      </div>
    </div>
    
    <div class="heatmap-grid" ref="heatmapGrid">
      <div v-if="loading" class="heatmap-loading">
        <div class="spinner"></div>
        <p>加载中...</p>
      </div>
      
      <div v-else-if="error" class="heatmap-error">
        <p>{{ error }}</p>
        <button @click="updateHeatmap">重试</button>
      </div>
      
      <div v-else-if="!hasData" class="heatmap-empty">
        <p>没有数据可显示</p>
      </div>
      
      <template v-else>
        <!-- 时间热图 -->
        <div v-if="groupBy === 'hour' || groupBy === 'day'" class="time-heatmap">
          <div 
            v-for="(cell, index) in heatmapData" 
            :key="index" 
            class="heatmap-cell"
            :class="{ 'has-errors': cell.count > 0 }"
            :style="{ backgroundColor: getCellColor(cell.count) }"
            @click="showCellDetails(cell)"
          >
            <div class="cell-label">{{ cell.label }}</div>
            <div class="cell-count">{{ cell.count }}</div>
          </div>
        </div>
        
        <!-- 分类热图 -->
        <div v-else class="category-heatmap">
          <div 
            v-for="(cell, index) in heatmapData" 
            :key="index" 
            class="category-cell"
            :style="{ 
              backgroundColor: getCellColor(cell.count),
              width: getCellWidth(cell.count)
            }"
            @click="showCellDetails(cell)"
          >
            <div class="cell-label">{{ cell.label }}</div>
            <div class="cell-count">{{ cell.count }}</div>
          </div>
        </div>
      </template>
    </div>
    
    <!-- 详情弹窗 -->
    <div v-if="showDetails" class="cell-details-modal">
      <div class="modal-content">
        <div class="modal-header">
          <h4>{{ selectedCell.label }} 详情</h4>
          <button class="close-button" @click="closeDetails">&times;</button>
        </div>
        <div class="modal-body">
          <p><strong>错误数量:</strong> {{ selectedCell.count }}</p>
          <p><strong>时间范围:</strong> {{ selectedCell.timeRange }}</p>
          
          <h5>错误分布</h5>
          <div class="error-distribution">
            <div 
              v-for="(item, index) in selectedCell.distribution" 
              :key="index"
              class="distribution-item"
            >
              <div class="item-label">{{ item.label }}</div>
              <div class="item-bar-container">
                <div 
                  class="item-bar" 
                  :style="{ 
                    width: `${(item.count / selectedCell.count) * 100}%`,
                    backgroundColor: getSeverityColor(item.severity || 'medium')
                  }"
                ></div>
                <div class="item-count">{{ item.count }}</div>
              </div>
            </div>
          </div>
          
          <h5>示例错误</h5>
          <div class="error-samples">
            <div 
              v-for="(sample, index) in selectedCell.samples" 
              :key="index"
              class="error-sample"
            >
              <div class="sample-header">
                <span class="sample-severity" :class="sample.severity">{{ sample.severity }}</span>
                <span class="sample-time">{{ formatTime(sample.timestamp) }}</span>
              </div>
              <div class="sample-message">{{ sample.message }}</div>
              <div v-if="sample.errorCode" class="sample-code">代码: {{ sample.errorCode }}</div>
            </div>
          </div>
        </div>
        <div class="modal-footer">
          <button @click="closeDetails">关闭</button>
          <button @click="viewAllErrors">查看所有错误</button>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'ErrorHeatmap',
  props: {
    title: {
      type: String,
      default: '错误热图'
    },
    dataUrl: {
      type: String,
      required: true
    },
    initialTimeRange: {
      type: String,
      default: 'day'
    },
    initialGroupBy: {
      type: String,
      default: 'hour'
    }
  },
  data() {
    return {
      timeRange: this.initialTimeRange,
      groupBy: this.initialGroupBy,
      heatmapData: [],
      loading: true,
      error: null,
      showDetails: false,
      selectedCell: null,
      legendColors: [
        '#ebedf0', // 0
        '#9be9a8', // 1-3
        '#40c463', // 4-9
        '#30a14e', // 10-19
        '#216e39'  // 20+
      ],
      legendLabels: ['0', '1-3', '4-9', '10-19', '20+']
    };
  },
  computed: {
    hasData() {
      return this.heatmapData && this.heatmapData.length > 0;
    }
  },
  mounted() {
    this.updateHeatmap();
  },
  methods: {
    async updateHeatmap() {
      this.loading = true;
      this.error = null;
      
      try {
        // 构建请求 URL
        const url = `${this.dataUrl}?timeRange=${this.timeRange}&groupBy=${this.groupBy}`;
        
        // 获取数据
        const response = await fetch(url);
        
        if (!response.ok) {
          throw new Error(`HTTP error! status: ${response.status}`);
        }
        
        const data = await response.json();
        
        if (!data.success) {
          throw new Error(data.error || '获取数据失败');
        }
        
        this.heatmapData = data.heatmap || [];
      } catch (error) {
        console.error('Error fetching heatmap data:', error);
        this.error = '获取热图数据失败: ' + error.message;
      } finally {
        this.loading = false;
      }
    },
    
    getCellColor(count) {
      if (count === 0) return this.legendColors[0];
      if (count <= 3) return this.legendColors[1];
      if (count <= 9) return this.legendColors[2];
      if (count <= 19) return this.legendColors[3];
      return this.legendColors[4];
    },
    
    getCellWidth(count) {
      // 根据数量计算宽度，最小 100px，最大 300px
      const minWidth = 100;
      const maxWidth = 300;
      const maxCount = Math.max(...this.heatmapData.map(cell => cell.count));
      
      if (maxCount === 0) return `${minWidth}px`;
      
      const width = minWidth + (maxWidth - minWidth) * (count / maxCount);
      return `${width}px`;
    },
    
    getSeverityColor(severity) {
      switch (severity) {
        case 'critical': return '#e74c3c';
        case 'high': return '#e67e22';
        case 'medium': return '#f1c40f';
        case 'low': return '#3498db';
        default: return '#95a5a6';
      }
    },
    
    showCellDetails(cell) {
      this.selectedCell = cell;
      this.showDetails = true;
    },
    
    closeDetails() {
      this.showDetails = false;
      this.selectedCell = null;
    },
    
    viewAllErrors() {
      // 发出事件，通知父组件查看所有错误
      this.$emit('view-all-errors', this.selectedCell);
    },
    
    formatTime(timestamp) {
      const date = new Date(timestamp);
      return date.toLocaleString();
    }
  }
};
</script>

<style scoped>
.error-heatmap-container {
  background-color: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  padding: 20px;
  margin-bottom: 20px;
}

.heatmap-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
}

.heatmap-header h3 {
  margin: 0;
  font-size: 18px;
  color: #333;
}

.heatmap-controls select {
  margin-left: 10px;
  padding: 5px 10px;
  border: 1px solid #ddd;
  border-radius: 4px;
  background-color: #f9f9f9;
}

.heatmap-legend {
  display: flex;
  margin-bottom: 15px;
}

.legend-item {
  display: flex;
  align-items: center;
  margin-right: 15px;
}

.legend-color {
  width: 15px;
  height: 15px;
  border-radius: 3px;
  margin-right: 5px;
}

.legend-label {
  font-size: 12px;
  color: #666;
}

.heatmap-grid {
  position: relative;
  min-height: 200px;
}

.heatmap-loading, .heatmap-error, .heatmap-empty {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  background-color: rgba(255, 255, 255, 0.8);
}

.spinner {
  border: 3px solid #f3f3f3;
  border-top: 3px solid #3498db;
  border-radius: 50%;
  width: 30px;
  height: 30px;
  animation: spin 1s linear infinite;
  margin-bottom: 10px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.time-heatmap {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(60px, 1fr));
  gap: 5px;
}

.heatmap-cell {
  background-color: #ebedf0;
  border-radius: 4px;
  padding: 8px;
  text-align: center;
  cursor: pointer;
  transition: transform 0.2s;
}

.heatmap-cell:hover {
  transform: scale(1.05);
}

.has-errors {
  color: #fff;
}

.cell-label {
  font-size: 12px;
  margin-bottom: 5px;
}

.cell-count {
  font-size: 16px;
  font-weight: bold;
}

.category-heatmap {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.category-cell {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 10px 15px;
  border-radius: 4px;
  cursor: pointer;
  transition: transform 0.2s;
  color: #fff;
}

.category-cell:hover {
  transform: translateX(5px);
}

.cell-details-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
}

.modal-content {
  background-color: #fff;
  border-radius: 8px;
  width: 80%;
  max-width: 800px;
  max-height: 80vh;
  overflow-y: auto;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15px 20px;
  border-bottom: 1px solid #eee;
}

.modal-header h4 {
  margin: 0;
  font-size: 18px;
}

.close-button {
  background: none;
  border: none;
  font-size: 24px;
  cursor: pointer;
  color: #666;
}

.modal-body {
  padding: 20px;
}

.modal-footer {
  padding: 15px 20px;
  border-top: 1px solid #eee;
  display: flex;
  justify-content: flex-end;
  gap: 10px;
}

.modal-footer button {
  padding: 8px 15px;
  border: none;
  border-radius: 4px;
  cursor: pointer;
}

.modal-footer button:first-child {
  background-color: #f1f1f1;
  color: #333;
}

.modal-footer button:last-child {
  background-color: #3498db;
  color: #fff;
}

.error-distribution {
  margin: 15px 0;
}

.distribution-item {
  margin-bottom: 8px;
}

.item-label {
  font-size: 14px;
  margin-bottom: 3px;
}

.item-bar-container {
  display: flex;
  align-items: center;
  height: 20px;
}

.item-bar {
  height: 100%;
  border-radius: 3px;
  min-width: 5px;
}

.item-count {
  margin-left: 8px;
  font-size: 12px;
  color: #666;
}

.error-samples {
  margin-top: 15px;
}

.error-sample {
  background-color: #f9f9f9;
  border-left: 4px solid #ddd;
  padding: 10px;
  margin-bottom: 10px;
  border-radius: 0 4px 4px 0;
}

.sample-header {
  display: flex;
  justify-content: space-between;
  margin-bottom: 5px;
}

.sample-severity {
  font-weight: bold;
  padding: 2px 6px;
  border-radius: 3px;
  font-size: 12px;
}

.sample-severity.critical {
  background-color: #e74c3c;
  color: #fff;
}

.sample-severity.high {
  background-color: #e67e22;
  color: #fff;
}

.sample-severity.medium {
  background-color: #f1c40f;
}

.sample-severity.low {
  background-color: #3498db;
  color: #fff;
}

.sample-time {
  font-size: 12px;
  color: #666;
}

.sample-message {
  font-size: 14px;
  margin-bottom: 5px;
}

.sample-code {
  font-size: 12px;
  color: #666;
  font-family: monospace;
}
</style>
