import { Pie<PERSON>hart, Pie, Cell, ResponsiveContainer, <PERSON>, Tooltip } from 'recharts';

interface CareerChangeChartProps {
  data: Array<{ group: string; count: number; hasIntention: number }>;
}

export default function CareerChangeChart({ data }: CareerChangeChartProps) {
  // If no data, show placeholder
  if (!data || data.length === 0) {
    return (
      <div className="h-full flex items-center justify-center">
        <p className="text-gray-500">暂无数据</p>
      </div>
    );
  }
  
  // Format data for pie chart
  const chartData = [
    { name: '有转行意向', value: data.reduce((sum, item) => sum + item.hasIntention, 0) },
    { name: '无转行意向', value: data.reduce((sum, item) => sum + (item.count - item.hasIntention), 0) },
  ];
  
  // Colors for pie chart
  const COLORS = ['#FF8042', '#0088FE'];
  
  // Custom tooltip
  const CustomTooltip = ({ active, payload }: any) => {
    if (active && payload && payload.length) {
      const total = chartData.reduce((sum, item) => sum + item.value, 0);
      return (
        <div className="bg-white p-2 border rounded shadow-sm">
          <p className="font-medium">{`${payload[0].name}`}</p>
          <p className="text-sm">{`人数: ${payload[0].value}`}</p>
          <p className="text-sm">{`占比: ${((payload[0].value / total) * 100).toFixed(1)}%`}</p>
        </div>
      );
    }
    return null;
  };
  
  // Additional data for education breakdown
  const educationBreakdown = data.map(item => ({
    name: item.group,
    intentionRate: item.count > 0 ? (item.hasIntention / item.count) * 100 : 0,
  })).sort((a, b) => b.intentionRate - a.intentionRate);
  
  return (
    <div className="h-full flex flex-col">
      <div className="flex-1">
        <ResponsiveContainer width="100%" height="100%">
          <PieChart>
            <Pie
              data={chartData}
              cx="50%"
              cy="50%"
              labelLine={false}
              outerRadius={70}
              fill="#8884d8"
              dataKey="value"
              label={({ name, percent }) => `${name} ${(percent * 100).toFixed(0)}%`}
            >
              {chartData.map((entry, index) => (
                <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
              ))}
            </Pie>
            <Tooltip content={<CustomTooltip />} />
            <Legend />
          </PieChart>
        </ResponsiveContainer>
      </div>
      
      {educationBreakdown.length > 0 && (
        <div className="mt-4">
          <h4 className="text-sm font-medium mb-2">各群体转行意向率</h4>
          <div className="space-y-2">
            {educationBreakdown.map((item, index) => (
              <div key={index} className="flex items-center">
                <div className="w-24 text-xs">{item.name}</div>
                <div className="flex-1 h-2 bg-gray-200 rounded-full overflow-hidden">
                  <div 
                    className="h-full bg-orange-500 rounded-full"
                    style={{ width: `${item.intentionRate}%` }}
                  />
                </div>
                <div className="w-12 text-xs text-right">{item.intentionRate.toFixed(0)}%</div>
              </div>
            ))}
          </div>
        </div>
      )}
    </div>
  );
}
