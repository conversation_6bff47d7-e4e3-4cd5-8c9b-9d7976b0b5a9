import React, { useState } from 'react';
import AdminLayout from '@/components/layouts/AdminLayout';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Tabs, TabsContent, Ta<PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import { Button } from '@/components/ui/button';
import { Textarea } from '@/components/ui/textarea';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Badge } from '@/components/ui/badge';
import { Slider } from '@/components/ui/slider';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { useToast } from '@/components/ui/use-toast';
import { 
  AlertCircle, 
  CheckCircle, 
  Clock, 
  Code, 
  Copy, 
  FileText, 
  Info, 
  RefreshCw, 
  Shield, 
  XCircle 
} from 'lucide-react';
import { useApi } from '@/hooks/useApi';

/**
 * 审核测试页面
 */
const ModerationTestPage: React.FC = () => {
  const { toast } = useToast();
  const api = useApi();
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [activeTab, setActiveTab] = useState<string>('test');
  const [contentType, setContentType] = useState<string>('story');
  const [content, setContent] = useState<string>('');
  const [strictMode, setStrictMode] = useState<boolean>(false);
  const [asyncMode, setAsyncMode] = useState<boolean>(false);
  const [result, setResult] = useState<any>(null);
  const [pendingId, setPendingId] = useState<string>('');
  const [pendingResult, setPendingResult] = useState<any>(null);
  const [pendingLoading, setPendingLoading] = useState<boolean>(false);
  
  // 测试内容审核
  const testModeration = async () => {
    if (!content.trim()) {
      toast({
        title: '内容不能为空',
        description: '请输入需要测试的内容',
        variant: 'destructive',
      });
      return;
    }
    
    setIsLoading(true);
    setResult(null);
    
    try {
      // 构建请求参数
      const params = {
        content,
        contentType,
        strictMode,
        async: asyncMode,
      };
      
      // 发送请求
      const response = await api.post('/admin/content-moderation/test', params);
      
      if (response.success) {
        if (asyncMode) {
          // 异步模式
          setPendingId(response.data.pendingId);
          toast({
            title: '内容已提交审核',
            description: '请点击"获取结果"按钮查看审核结果',
            variant: 'default',
          });
        } else {
          // 同步模式
          setResult(response.data.result);
        }
      } else {
        toast({
          title: '测试失败',
          description: response.error || '无法测试内容审核',
          variant: 'destructive',
        });
      }
    } catch (error) {
      console.error('测试内容审核失败:', error);
      toast({
        title: '测试失败',
        description: '服务器错误，请稍后再试',
        variant: 'destructive',
      });
    } finally {
      setIsLoading(false);
    }
  };
  
  // 获取异步审核结果
  const getPendingResult = async () => {
    if (!pendingId) {
      toast({
        title: '缺少待审核ID',
        description: '请先提交内容进行异步审核',
        variant: 'destructive',
      });
      return;
    }
    
    setPendingLoading(true);
    
    try {
      // 发送请求
      const response = await api.get(`/admin/content-moderation/result/${pendingId}`);
      
      if (response.success) {
        setPendingResult(response.data);
        
        if (response.data.status === 'completed') {
          toast({
            title: '审核完成',
            description: '已获取审核结果',
            variant: 'default',
          });
        } else if (response.data.status === 'pending') {
          toast({
            title: '审核中',
            description: '内容正在审核中，请稍后再试',
            variant: 'default',
          });
        } else {
          toast({
            title: '审核失败',
            description: '内容审核失败，请查看详情',
            variant: 'destructive',
          });
        }
      } else {
        toast({
          title: '获取结果失败',
          description: response.error || '无法获取审核结果',
          variant: 'destructive',
        });
      }
    } catch (error) {
      console.error('获取审核结果失败:', error);
      toast({
        title: '获取结果失败',
        description: '服务器错误，请稍后再试',
        variant: 'destructive',
      });
    } finally {
      setPendingLoading(false);
    }
  };
  
  // 复制JSON结果
  const copyResult = (result: any) => {
    navigator.clipboard.writeText(JSON.stringify(result, null, 2)).then(
      () => {
        toast({
          title: '复制成功',
          description: '结果已复制到剪贴板',
          variant: 'default',
        });
      },
      (err) => {
        console.error('复制失败:', err);
        toast({
          title: '复制失败',
          description: '无法复制结果，请手动选择并复制',
          variant: 'destructive',
        });
      }
    );
  };
  
  // 渲染审核结果
  const renderResult = (result: any) => {
    if (!result) return null;
    
    return (
      <div className="space-y-4 mt-4">
        <div className="flex items-center justify-between">
          <h3 className="text-lg font-medium">审核结果</h3>
          <Button
            variant="ghost"
            size="sm"
            onClick={() => copyResult(result)}
          >
            <Copy className="h-4 w-4 mr-2" />
            复制JSON
          </Button>
        </div>
        
        <div className="flex items-center gap-2">
          <span className="font-medium">操作:</span>
          {result.action === 'approve' && (
            <Badge variant="outline" className="bg-green-50 text-green-700 border-green-200">
              <CheckCircle className="w-3 h-3 mr-1" />
              通过
            </Badge>
          )}
          {result.action === 'reject' && (
            <Badge variant="outline" className="bg-red-50 text-red-700 border-red-200">
              <XCircle className="w-3 h-3 mr-1" />
              拒绝
            </Badge>
          )}
          {result.action === 'review' && (
            <Badge variant="outline" className="bg-yellow-50 text-yellow-700 border-yellow-200">
              <Clock className="w-3 h-3 mr-1" />
              人工审核
            </Badge>
          )}
        </div>
        
        <div className="flex items-center gap-2">
          <span className="font-medium">置信度:</span>
          <div className="w-full max-w-xs">
            <Slider
              value={[result.confidence * 100]}
              max={100}
              step={1}
              disabled
              className={
                result.confidence > 0.8 ? 'bg-green-100' :
                result.confidence > 0.6 ? 'bg-yellow-100' :
                'bg-red-100'
              }
            />
          </div>
          <span className="text-sm">{Math.round(result.confidence * 100)}%</span>
        </div>
        
        {result.severity && (
          <div className="flex items-center gap-2">
            <span className="font-medium">严重程度:</span>
            <Badge variant={
              result.severity === 'high' ? 'destructive' :
              result.severity === 'medium' ? 'default' :
              'outline'
            }>
              {result.severity === 'high' ? '高' :
               result.severity === 'medium' ? '中' : '低'}
            </Badge>
          </div>
        )}
        
        {result.issues && result.issues.length > 0 && (
          <div>
            <span className="font-medium">检测到的问题:</span>
            <ul className="list-disc list-inside mt-1 space-y-1">
              {result.issues.map((issue: string, index: number) => (
                <li key={index} className="text-sm">{issue}</li>
              ))}
            </ul>
          </div>
        )}
        
        {result.explanation && (
          <div>
            <span className="font-medium">解释:</span>
            <p className="text-sm mt-1 whitespace-pre-wrap">{result.explanation}</p>
          </div>
        )}
      </div>
    );
  };
  
  // 渲染异步审核结果
  const renderPendingResult = (result: any) => {
    if (!result) return null;
    
    return (
      <div className="space-y-4 mt-4">
        <div className="flex items-center justify-between">
          <h3 className="text-lg font-medium">异步审核结果</h3>
          <Button
            variant="ghost"
            size="sm"
            onClick={() => copyResult(result)}
          >
            <Copy className="h-4 w-4 mr-2" />
            复制JSON
          </Button>
        </div>
        
        <div className="flex items-center gap-2">
          <span className="font-medium">状态:</span>
          {result.status === 'completed' && (
            <Badge variant="outline" className="bg-green-50 text-green-700 border-green-200">
              <CheckCircle className="w-3 h-3 mr-1" />
              已完成
            </Badge>
          )}
          {result.status === 'pending' && (
            <Badge variant="outline" className="bg-yellow-50 text-yellow-700 border-yellow-200">
              <Clock className="w-3 h-3 mr-1" />
              处理中
            </Badge>
          )}
          {result.status === 'failed' && (
            <Badge variant="outline" className="bg-red-50 text-red-700 border-red-200">
              <XCircle className="w-3 h-3 mr-1" />
              失败
            </Badge>
          )}
        </div>
        
        {result.result && renderResult(result.result)}
      </div>
    );
  };
  
  return (
    <AdminLayout>
      <div className="container mx-auto py-6">
        <div className="flex items-center justify-between mb-6">
          <div>
            <h1 className="text-3xl font-bold">审核测试工具</h1>
            <p className="text-muted-foreground">
              测试内容审核功能
            </p>
          </div>
        </div>
        
        <Alert className="mb-6">
          <Info className="h-4 w-4" />
          <AlertTitle>测试工具说明</AlertTitle>
          <AlertDescription>
            本工具用于测试内容审核功能，可以选择同步或异步方式进行测试。同步方式会立即返回审核结果，异步方式会返回一个待审核ID，需要手动获取审核结果。
          </AlertDescription>
        </Alert>
        
        <Tabs value={activeTab} onValueChange={setActiveTab}>
          <TabsList className="grid w-full grid-cols-2">
            <TabsTrigger value="test">
              <Shield className="w-4 h-4 mr-2" />
              内容测试
            </TabsTrigger>
            <TabsTrigger value="examples">
              <FileText className="w-4 h-4 mr-2" />
              测试样例
            </TabsTrigger>
          </TabsList>
          
          <TabsContent value="test" className="mt-6">
            <Card>
              <CardHeader>
                <CardTitle>内容审核测试</CardTitle>
                <CardDescription>
                  输入内容进行审核测试
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-6">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                      <Label htmlFor="contentType">内容类型</Label>
                      <Select
                        value={contentType}
                        onValueChange={setContentType}
                      >
                        <SelectTrigger id="contentType">
                          <SelectValue placeholder="选择内容类型" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="story">故事</SelectItem>
                          <SelectItem value="questionnaire">问卷</SelectItem>
                          <SelectItem value="comment">评论</SelectItem>
                          <SelectItem value="profile">个人资料</SelectItem>
                          <SelectItem value="feedback">反馈</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                    
                    <div className="flex flex-col space-y-2">
                      <Label>审核选项</Label>
                      <div className="flex items-center space-x-2">
                        <input
                          type="checkbox"
                          id="strictMode"
                          checked={strictMode}
                          onChange={(e) => setStrictMode(e.target.checked)}
                          className="h-4 w-4 rounded border-gray-300"
                        />
                        <Label htmlFor="strictMode" className="text-sm font-normal">
                          严格模式
                        </Label>
                      </div>
                      <div className="flex items-center space-x-2">
                        <input
                          type="checkbox"
                          id="asyncMode"
                          checked={asyncMode}
                          onChange={(e) => setAsyncMode(e.target.checked)}
                          className="h-4 w-4 rounded border-gray-300"
                        />
                        <Label htmlFor="asyncMode" className="text-sm font-normal">
                          异步审核
                        </Label>
                      </div>
                    </div>
                  </div>
                  
                  <div>
                    <Label htmlFor="content">内容</Label>
                    <Textarea
                      id="content"
                      value={content}
                      onChange={(e) => setContent(e.target.value)}
                      placeholder="输入需要测试的内容..."
                      className="min-h-[200px]"
                    />
                  </div>
                  
                  <div className="flex justify-end space-x-2">
                    <Button
                      variant="outline"
                      onClick={() => setContent('')}
                      disabled={isLoading || !content}
                    >
                      清空
                    </Button>
                    <Button
                      onClick={testModeration}
                      disabled={isLoading || !content.trim()}
                    >
                      {isLoading ? (
                        <>
                          <RefreshCw className="w-4 h-4 mr-2 animate-spin" />
                          测试中...
                        </>
                      ) : (
                        <>
                          <Shield className="w-4 h-4 mr-2" />
                          测试审核
                        </>
                      )}
                    </Button>
                  </div>
                  
                  {/* 同步审核结果 */}
                  {!asyncMode && renderResult(result)}
                  
                  {/* 异步审核结果 */}
                  {asyncMode && (
                    <div className="space-y-4 mt-4">
                      {pendingId && (
                        <div className="flex items-center gap-2">
                          <span className="font-medium">待审核ID:</span>
                          <code className="bg-muted px-2 py-1 rounded text-sm">{pendingId}</code>
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={getPendingResult}
                            disabled={pendingLoading}
                          >
                            {pendingLoading ? (
                              <>
                                <RefreshCw className="w-4 h-4 mr-2 animate-spin" />
                                获取中...
                              </>
                            ) : (
                              <>
                                <RefreshCw className="w-4 h-4 mr-2" />
                                获取结果
                              </>
                            )}
                          </Button>
                        </div>
                      )}
                      
                      {renderPendingResult(pendingResult)}
                    </div>
                  )}
                </div>
              </CardContent>
            </Card>
          </TabsContent>
          
          <TabsContent value="examples" className="mt-6">
            <Card>
              <CardHeader>
                <CardTitle>测试样例</CardTitle>
                <CardDescription>
                  常用的测试样例，点击可复制到测试区域
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-6">
                  <div>
                    <h3 className="text-lg font-medium mb-2">安全内容样例</h3>
                    <div className="space-y-2">
                      <div className="p-3 bg-muted rounded-md">
                        <div className="flex justify-between items-start mb-2">
                          <span className="font-medium">普通故事</span>
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => {
                              setContent('我大学毕业后，找工作的过程并不顺利。但经过不断努力，终于在一家科技公司找到了合适的职位。这段经历让我明白，坚持和不断学习的重要性。');
                              setContentType('story');
                              setActiveTab('test');
                            }}
                          >
                            <Copy className="h-4 w-4 mr-2" />
                            复制
                          </Button>
                        </div>
                        <p className="text-sm">我大学毕业后，找工作的过程并不顺利。但经过不断努力，终于在一家科技公司找到了合适的职位。这段经历让我明白，坚持和不断学习的重要性。</p>
                      </div>
                      
                      <div className="p-3 bg-muted rounded-md">
                        <div className="flex justify-between items-start mb-2">
                          <span className="font-medium">普通评论</span>
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => {
                              setContent('感谢分享你的经历，这对我很有启发。我也正在找工作，希望能像你一样坚持下去。');
                              setContentType('comment');
                              setActiveTab('test');
                            }}
                          >
                            <Copy className="h-4 w-4 mr-2" />
                            复制
                          </Button>
                        </div>
                        <p className="text-sm">感谢分享你的经历，这对我很有启发。我也正在找工作，希望能像你一样坚持下去。</p>
                      </div>
                    </div>
                  </div>
                  
                  <div>
                    <h3 className="text-lg font-medium mb-2">需要审核的内容样例</h3>
                    <div className="space-y-2">
                      <div className="p-3 bg-muted rounded-md">
                        <div className="flex justify-between items-start mb-2">
                          <span className="font-medium">含有敏感词的内容</span>
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => {
                              setContent('我毕业后找工作很不顺利，那些招聘人员都是一群白痴，根本不懂得欣赏人才。');
                              setContentType('story');
                              setActiveTab('test');
                            }}
                          >
                            <Copy className="h-4 w-4 mr-2" />
                            复制
                          </Button>
                        </div>
                        <p className="text-sm">我毕业后找工作很不顺利，那些招聘人员都是一群白痴，根本不懂得欣赏人才。</p>
                      </div>
                      
                      <div className="p-3 bg-muted rounded-md">
                        <div className="flex justify-between items-start mb-2">
                          <span className="font-medium">含有联系方式的内容</span>
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => {
                              setContent('我可以帮你介绍工作，请加我微信：job123456，或者打电话给我：13800138000');
                              setContentType('comment');
                              setActiveTab('test');
                            }}
                          >
                            <Copy className="h-4 w-4 mr-2" />
                            复制
                          </Button>
                        </div>
                        <p className="text-sm">我可以帮你介绍工作，请加我微信：job123456，或者打电话给我：13800138000</p>
                      </div>
                    </div>
                  </div>
                  
                  <div>
                    <h3 className="text-lg font-medium mb-2">违规内容样例</h3>
                    <div className="space-y-2">
                      <div className="p-3 bg-muted rounded-md">
                        <div className="flex justify-between items-start mb-2">
                          <span className="font-medium">含有侮辱性内容</span>
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => {
                              setContent('那些XX公司的人都是垃圾，他们根本不配做这行，希望他们公司早日倒闭！');
                              setContentType('story');
                              setActiveTab('test');
                            }}
                          >
                            <Copy className="h-4 w-4 mr-2" />
                            复制
                          </Button>
                        </div>
                        <p className="text-sm">那些XX公司的人都是垃圾，他们根本不配做这行，希望他们公司早日倒闭！</p>
                      </div>
                      
                      <div className="p-3 bg-muted rounded-md">
                        <div className="flex justify-between items-start mb-2">
                          <span className="font-medium">含有广告内容</span>
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => {
                              setContent('想要高薪工作吗？加入我们的培训课程，包教包会，100%推荐就业，详情请访问www.example.com');
                              setContentType('comment');
                              setActiveTab('test');
                            }}
                          >
                            <Copy className="h-4 w-4 mr-2" />
                            复制
                          </Button>
                        </div>
                        <p className="text-sm">想要高薪工作吗？加入我们的培训课程，包教包会，100%推荐就业，详情请访问www.example.com</p>
                      </div>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
      </div>
    </AdminLayout>
  );
};

export default ModerationTestPage;
