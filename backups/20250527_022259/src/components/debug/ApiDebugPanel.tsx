import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Collapsible, CollapsibleContent, CollapsibleTrigger } from '@/components/ui/collapsible';
import {
  Bug,
  CheckCircle,
  XCircle,
  Clock,
  Database,
  Wifi,
  AlertTriangle,
  ChevronDown,
  ChevronRight,
  RefreshCw,
  Eye
} from 'lucide-react';

interface ApiRequest {
  id: string;
  name: string;
  endpoint: string;
  method: string;
  status: 'pending' | 'success' | 'error' | 'idle';
  responseTime?: number;
  data?: any;
  error?: string;
  timestamp?: string;
  expectedStructure?: any;
  dataValidation?: {
    isValid: boolean;
    missingFields: string[];
    extraFields: string[];
    typeErrors: string[];
  };
}

interface ApiDebugPanelProps {
  pageName: string;
  requests: ApiRequest[];
  onTestRequest: (requestId: string) => Promise<void>;
  onTestAll: () => Promise<void>;
}

export const ApiDebugPanel: React.FC<ApiDebugPanelProps> = ({
  pageName,
  requests,
  onTestRequest,
  onTestAll
}) => {
  const [isExpanded, setIsExpanded] = useState(false);
  const [selectedRequest, setSelectedRequest] = useState<string | null>(null);

  // 检查是否应该显示调试面板
  const shouldShowDebugPanel = import.meta.env.DEV || import.meta.env.VITE_SHOW_DEBUG_PANEL === 'true';

  // 如果不应该显示调试面板，返回null
  if (!shouldShowDebugPanel) {
    return null;
  }

  // 统计信息
  const stats = {
    total: requests.length,
    success: requests.filter(r => r.status === 'success').length,
    error: requests.filter(r => r.status === 'error').length,
    pending: requests.filter(r => r.status === 'pending').length,
    avgResponseTime: requests
      .filter(r => r.responseTime)
      .reduce((sum, r) => sum + (r.responseTime || 0), 0) /
      requests.filter(r => r.responseTime).length || 0
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'success': return <CheckCircle className="h-4 w-4 text-green-500" />;
      case 'error': return <XCircle className="h-4 w-4 text-red-500" />;
      case 'pending': return <Clock className="h-4 w-4 text-yellow-500 animate-spin" />;
      default: return <Database className="h-4 w-4 text-gray-400" />;
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'success': return 'bg-green-100 text-green-800';
      case 'error': return 'bg-red-100 text-red-800';
      case 'pending': return 'bg-yellow-100 text-yellow-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  return (
    <Card className="mb-6 border-2 border-dashed border-blue-200 bg-blue-50/50">
      <Collapsible open={isExpanded} onOpenChange={setIsExpanded}>
        <CollapsibleTrigger asChild>
          <CardHeader className="cursor-pointer hover:bg-blue-100/50 transition-colors">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-2">
                <Bug className="h-5 w-5 text-blue-600" />
                <CardTitle className="text-lg">API调试面板 - {pageName}</CardTitle>
                {isExpanded ? <ChevronDown className="h-4 w-4" /> : <ChevronRight className="h-4 w-4" />}
              </div>
              <div className="flex items-center gap-2">
                <Badge variant="outline" className="bg-white">
                  {stats.success}/{stats.total} 成功
                </Badge>
                {stats.error > 0 && (
                  <Badge variant="destructive">
                    {stats.error} 错误
                  </Badge>
                )}
                {stats.pending > 0 && (
                  <Badge variant="secondary">
                    {stats.pending} 进行中
                  </Badge>
                )}
              </div>
            </div>
            <CardDescription>
              点击展开查看详细的API请求状态、数据结构验证和错误信息
            </CardDescription>
          </CardHeader>
        </CollapsibleTrigger>

        <CollapsibleContent>
          <CardContent>
            {/* 统计概览 */}
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mb-6">
              <div className="text-center p-3 bg-white rounded-lg border">
                <div className="text-2xl font-bold text-blue-600">{stats.total}</div>
                <div className="text-sm text-gray-600">总请求数</div>
              </div>
              <div className="text-center p-3 bg-white rounded-lg border">
                <div className="text-2xl font-bold text-green-600">{stats.success}</div>
                <div className="text-sm text-gray-600">成功请求</div>
              </div>
              <div className="text-center p-3 bg-white rounded-lg border">
                <div className="text-2xl font-bold text-red-600">{stats.error}</div>
                <div className="text-sm text-gray-600">失败请求</div>
              </div>
              <div className="text-center p-3 bg-white rounded-lg border">
                <div className="text-2xl font-bold text-purple-600">
                  {stats.avgResponseTime ? Math.round(stats.avgResponseTime) : 0}ms
                </div>
                <div className="text-sm text-gray-600">平均响应时间</div>
              </div>
            </div>

            {/* 操作按钮 */}
            <div className="flex gap-2 mb-6">
              <Button onClick={onTestAll} variant="default" size="sm">
                <RefreshCw className="h-4 w-4 mr-2" />
                测试所有API
              </Button>
              <Button
                onClick={() => window.location.reload()}
                variant="outline"
                size="sm"
              >
                刷新页面
              </Button>
            </div>

            {/* API请求列表 */}
            <Tabs defaultValue="overview" className="w-full">
              <TabsList>
                <TabsTrigger value="overview">概览</TabsTrigger>
                <TabsTrigger value="details">详细信息</TabsTrigger>
                <TabsTrigger value="data">数据预览</TabsTrigger>
              </TabsList>

              <TabsContent value="overview" className="mt-4">
                <div className="space-y-2">
                  {requests.map((request) => (
                    <div
                      key={request.id}
                      className="flex items-center justify-between p-3 bg-white rounded-lg border hover:bg-gray-50 cursor-pointer"
                      onClick={() => setSelectedRequest(selectedRequest === request.id ? null : request.id)}
                    >
                      <div className="flex items-center gap-3">
                        {getStatusIcon(request.status)}
                        <div>
                          <div className="font-medium">{request.name}</div>
                          <div className="text-sm text-gray-500">
                            {request.method} {request.endpoint}
                          </div>
                        </div>
                      </div>
                      <div className="flex items-center gap-2">
                        {request.responseTime && (
                          <Badge variant="outline">{request.responseTime}ms</Badge>
                        )}
                        <Badge className={getStatusColor(request.status)}>
                          {request.status}
                        </Badge>
                        <Button
                          size="sm"
                          variant="outline"
                          onClick={(e) => {
                            e.stopPropagation();
                            onTestRequest(request.id);
                          }}
                        >
                          测试
                        </Button>
                      </div>
                    </div>
                  ))}
                </div>
              </TabsContent>

              <TabsContent value="details" className="mt-4">
                {selectedRequest ? (
                  <RequestDetails request={requests.find(r => r.id === selectedRequest)!} />
                ) : (
                  <div className="text-center py-8 text-gray-500">
                    请在概览标签页中选择一个API请求查看详细信息
                  </div>
                )}
              </TabsContent>

              <TabsContent value="data" className="mt-4">
                {selectedRequest ? (
                  <DataPreview request={requests.find(r => r.id === selectedRequest)!} />
                ) : (
                  <div className="text-center py-8 text-gray-500">
                    请在概览标签页中选择一个API请求查看数据预览
                  </div>
                )}
              </TabsContent>
            </Tabs>
          </CardContent>
        </CollapsibleContent>
      </Collapsible>
    </Card>
  );
};

// 请求详细信息组件
const RequestDetails: React.FC<{ request: ApiRequest }> = ({ request }) => {
  return (
    <div className="space-y-4">
      <div className="grid grid-cols-2 gap-4">
        <div>
          <h4 className="font-medium mb-2">基本信息</h4>
          <div className="space-y-1 text-sm">
            <div><strong>名称:</strong> {request.name}</div>
            <div><strong>端点:</strong> {request.endpoint}</div>
            <div><strong>方法:</strong> {request.method}</div>
            <div><strong>状态:</strong> {request.status}</div>
            {request.timestamp && (
              <div><strong>时间:</strong> {new Date(request.timestamp).toLocaleString()}</div>
            )}
          </div>
        </div>
        <div>
          <h4 className="font-medium mb-2">性能信息</h4>
          <div className="space-y-1 text-sm">
            {request.responseTime && (
              <div><strong>响应时间:</strong> {request.responseTime}ms</div>
            )}
            <div><strong>数据大小:</strong> {request.data ? JSON.stringify(request.data).length : 0} 字符</div>
          </div>
        </div>
      </div>

      {request.error && (
        <div className="p-3 bg-red-50 border border-red-200 rounded-lg">
          <h4 className="font-medium text-red-800 mb-2 flex items-center gap-2">
            <AlertTriangle className="h-4 w-4" />
            错误信息
          </h4>
          <pre className="text-sm text-red-700 whitespace-pre-wrap">{request.error}</pre>
        </div>
      )}

      {request.dataValidation && (
        <div className="p-3 bg-gray-50 border rounded-lg">
          <h4 className="font-medium mb-2">数据验证结果</h4>
          <div className="space-y-2 text-sm">
            <div className="flex items-center gap-2">
              {request.dataValidation.isValid ? (
                <CheckCircle className="h-4 w-4 text-green-500" />
              ) : (
                <XCircle className="h-4 w-4 text-red-500" />
              )}
              <span>{request.dataValidation.isValid ? '数据结构有效' : '数据结构无效'}</span>
            </div>
            {request.dataValidation.missingFields.length > 0 && (
              <div>
                <strong>缺失字段:</strong> {request.dataValidation.missingFields.join(', ')}
              </div>
            )}
            {request.dataValidation.extraFields.length > 0 && (
              <div>
                <strong>额外字段:</strong> {request.dataValidation.extraFields.join(', ')}
              </div>
            )}
            {request.dataValidation.typeErrors.length > 0 && (
              <div>
                <strong>类型错误:</strong> {request.dataValidation.typeErrors.join(', ')}
              </div>
            )}
          </div>
        </div>
      )}
    </div>
  );
};

// 数据预览组件
const DataPreview: React.FC<{ request: ApiRequest }> = ({ request }) => {
  return (
    <div className="space-y-4">
      <div className="flex items-center gap-2">
        <Eye className="h-4 w-4" />
        <h4 className="font-medium">返回数据预览</h4>
      </div>

      {request.data ? (
        <pre className="bg-gray-900 text-green-400 p-4 rounded-lg overflow-auto max-h-96 text-sm">
          {JSON.stringify(request.data, null, 2)}
        </pre>
      ) : (
        <div className="text-center py-8 text-gray-500">
          暂无数据
        </div>
      )}

      {request.expectedStructure && (
        <div>
          <h4 className="font-medium mb-2">期望的数据结构</h4>
          <pre className="bg-blue-50 text-blue-800 p-4 rounded-lg overflow-auto max-h-96 text-sm">
            {JSON.stringify(request.expectedStructure, null, 2)}
          </pre>
        </div>
      )}
    </div>
  );
};

export default ApiDebugPanel;
