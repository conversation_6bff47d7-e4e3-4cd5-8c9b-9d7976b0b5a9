/**
 * 认证缓存服务
 * 
 * 专门处理认证相关的缓存管理，防止不同角色之间的缓存污染
 */

/**
 * 清除所有认证相关的缓存
 */
export function clearAuthCache(): void {
  console.log('开始清除认证缓存...');
  
  // 清除localStorage中的认证信息
  const authKeys = [
    'adminToken',
    'adminUser',
    'reviewerToken', 
    'reviewerUser',
    'superadminToken',
    'superadminUser',
    'userToken',
    'userData',
    'auth_token',
    'user_role',
    'user_data'
  ];
  
  authKeys.forEach(key => {
    if (localStorage.getItem(key)) {
      localStorage.removeItem(key);
      console.log(`已清除 localStorage: ${key}`);
    }
  });
  
  // 清除sessionStorage中的认证信息
  const sessionKeys = [
    'adminSession',
    'reviewerSession',
    'superadminSession',
    'userSession',
    'currentRole',
    'loginTime'
  ];
  
  sessionKeys.forEach(key => {
    if (sessionStorage.getItem(key)) {
      sessionStorage.removeItem(key);
      console.log(`已清除 sessionStorage: ${key}`);
    }
  });
  
  console.log('认证缓存清除完成');
}

/**
 * 清除特定角色的缓存
 */
export function clearRoleCache(role: 'admin' | 'reviewer' | 'superadmin' | 'user'): void {
  console.log(`开始清除 ${role} 角色缓存...`);
  
  const roleKeys = {
    admin: ['adminToken', 'adminUser', 'adminSession'],
    reviewer: ['reviewerToken', 'reviewerUser', 'reviewerSession'],
    superadmin: ['superadminToken', 'superadminUser', 'superadminSession'],
    user: ['userToken', 'userData', 'userSession']
  };
  
  const keys = roleKeys[role] || [];
  
  keys.forEach(key => {
    localStorage.removeItem(key);
    sessionStorage.removeItem(key);
    console.log(`已清除 ${role} 缓存: ${key}`);
  });
  
  console.log(`${role} 角色缓存清除完成`);
}

/**
 * 清除菜单状态缓存
 */
export function clearMenuCache(): void {
  console.log('开始清除菜单状态缓存...');
  
  const menuKeys = [
    'superadmin_menu_initialized',
    'admin_menu_initialized', 
    'reviewer_menu_initialized',
    'user_menu_initialized',
    'menu_state',
    'sidebar_collapsed'
  ];
  
  menuKeys.forEach(key => {
    sessionStorage.removeItem(key);
    localStorage.removeItem(key);
    console.log(`已清除菜单缓存: ${key}`);
  });
  
  console.log('菜单状态缓存清除完成');
}

/**
 * 清除数据缓存
 */
export function clearDataCache(): void {
  console.log('开始清除数据缓存...');
  
  // 清除内存缓存
  if (window.caches) {
    window.caches.keys().then(names => {
      names.forEach(name => {
        if (name.includes('admin') || name.includes('auth') || name.includes('data')) {
          window.caches.delete(name);
          console.log(`已清除缓存: ${name}`);
        }
      });
    });
  }
  
  // 清除localStorage中的数据缓存
  const dataKeys = Object.keys(localStorage).filter(key => 
    key.includes('cache_') || 
    key.includes('data_') ||
    key.includes('stats_') ||
    key.includes('dashboard_')
  );
  
  dataKeys.forEach(key => {
    localStorage.removeItem(key);
    console.log(`已清除数据缓存: ${key}`);
  });
  
  console.log('数据缓存清除完成');
}

/**
 * 完全重置应用状态
 */
export function resetAppState(): void {
  console.log('开始重置应用状态...');
  
  // 清除所有缓存
  clearAuthCache();
  clearMenuCache();
  clearDataCache();
  
  // 清除可能的全局状态
  if (window.__FORCE_REAL_API__) {
    delete window.__FORCE_REAL_API__;
  }
  
  // 清除可能的错误状态
  if (window.__APP_ERROR__) {
    delete window.__APP_ERROR__;
  }
  
  console.log('应用状态重置完成');
}

/**
 * 安全的角色切换
 */
export function safeRoleSwitch(fromRole: string, toRole: string): void {
  console.log(`安全角色切换: ${fromRole} -> ${toRole}`);
  
  // 清除当前角色的所有缓存
  if (['admin', 'reviewer', 'superadmin', 'user'].includes(fromRole)) {
    clearRoleCache(fromRole as any);
  }
  
  // 清除菜单状态
  clearMenuCache();
  
  // 清除可能的路由状态
  sessionStorage.removeItem('lastRoute');
  sessionStorage.removeItem('redirectAfterLogin');
  
  console.log(`角色切换准备完成: ${fromRole} -> ${toRole}`);
}

/**
 * 检查缓存状态
 */
export function checkCacheState(): {
  hasAuthCache: boolean;
  hasMenuCache: boolean;
  hasDataCache: boolean;
  details: string[];
} {
  const details: string[] = [];
  
  // 检查认证缓存
  const authKeys = ['adminToken', 'reviewerToken', 'superadminToken', 'userToken'];
  const hasAuthCache = authKeys.some(key => {
    const exists = !!localStorage.getItem(key);
    if (exists) details.push(`认证缓存: ${key}`);
    return exists;
  });
  
  // 检查菜单缓存
  const menuKeys = ['superadmin_menu_initialized', 'admin_menu_initialized', 'reviewer_menu_initialized'];
  const hasMenuCache = menuKeys.some(key => {
    const exists = !!sessionStorage.getItem(key);
    if (exists) details.push(`菜单缓存: ${key}`);
    return exists;
  });
  
  // 检查数据缓存
  const dataKeys = Object.keys(localStorage).filter(key => 
    key.includes('cache_') || key.includes('data_')
  );
  const hasDataCache = dataKeys.length > 0;
  if (hasDataCache) {
    details.push(`数据缓存: ${dataKeys.length} 项`);
  }
  
  return {
    hasAuthCache,
    hasMenuCache, 
    hasDataCache,
    details
  };
}

/**
 * 获取当前认证状态
 */
export function getCurrentAuthState(): {
  role: string | null;
  token: string | null;
  user: any;
  isAuthenticated: boolean;
} {
  // 检查各种可能的认证状态
  const adminToken = localStorage.getItem('adminToken');
  const reviewerToken = localStorage.getItem('reviewerToken');
  const superadminToken = localStorage.getItem('superadminToken');
  const userToken = localStorage.getItem('userToken');
  
  let role = null;
  let token = null;
  let user = null;
  
  if (superadminToken) {
    role = 'superadmin';
    token = superadminToken;
    user = JSON.parse(localStorage.getItem('superadminUser') || '{}');
  } else if (adminToken) {
    role = 'admin';
    token = adminToken;
    user = JSON.parse(localStorage.getItem('adminUser') || '{}');
  } else if (reviewerToken) {
    role = 'reviewer';
    token = reviewerToken;
    user = JSON.parse(localStorage.getItem('reviewerUser') || '{}');
  } else if (userToken) {
    role = 'user';
    token = userToken;
    user = JSON.parse(localStorage.getItem('userData') || '{}');
  }
  
  return {
    role,
    token,
    user,
    isAuthenticated: !!(role && token)
  };
}
