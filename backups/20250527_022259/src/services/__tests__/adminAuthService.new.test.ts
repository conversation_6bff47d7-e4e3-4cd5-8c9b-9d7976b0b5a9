/**
 * 管理员认证服务测试
 */

import { describe, it, expect, vi, beforeEach } from 'vitest';
import * as unifiedDataService from '../unifiedDataService';

// 模拟unifiedDataService
vi.mock('../unifiedDataService', () => ({
  fetchData: vi.fn()
}));

describe('adminAuthService', () => {
  beforeEach(() => {
    // 重置所有模拟
    vi.resetAllMocks();
  });

  describe('adminLogin', () => {
    it('should call fetchData with correct parameters', async () => {
      // 设置模拟返回值
      (unifiedDataService.fetchData as any).mockResolvedValue({
        token: 'test-token',
        user: {
          id: 1,
          username: 'admin',
          name: '系统管理员',
          role: 'admin',
          permissions: []
        }
      });

      // 导入服务
      const { adminLogin } = await import('../adminAuthService.new');

      // 调用函数
      const result = await adminLogin('admin', 'password');

      // 验证结果
      expect(unifiedDataService.fetchData).toHaveBeenCalledWith(
        '/admin/login',
        'adminLoginMock',
        { username: 'admin', password: 'password' },
        { method: 'POST' }
      );
      expect(result).toEqual({
        token: 'test-token',
        user: {
          id: 1,
          username: 'admin',
          name: '系统管理员',
          role: 'admin',
          permissions: []
        }
      });
    });
  });

  describe('getAdminInfo', () => {
    it('should call fetchData with correct parameters', async () => {
      // 设置模拟返回值
      (unifiedDataService.fetchData as any).mockResolvedValue({
        id: 1,
        username: 'admin',
        name: '系统管理员',
        role: 'admin',
        permissions: []
      });

      // 导入服务
      const { getAdminInfo } = await import('../adminAuthService.new');

      // 调用函数
      const result = await getAdminInfo(1);

      // 验证结果
      expect(unifiedDataService.fetchData).toHaveBeenCalledWith(
        '/admin/1',
        'getAdminInfoMock',
        { id: 1 },
        { method: 'GET' }
      );
      expect(result).toEqual({
        id: 1,
        username: 'admin',
        name: '系统管理员',
        role: 'admin',
        permissions: []
      });
    });
  });

  describe('getAdmins', () => {
    it('should call fetchData with correct parameters', async () => {
      // 设置模拟返回值
      (unifiedDataService.fetchData as any).mockResolvedValue([
        {
          id: 1,
          username: 'admin',
          name: '系统管理员',
          role: 'admin'
        }
      ]);

      // 导入服务
      const { getAdmins } = await import('../adminAuthService.new');

      // 调用函数
      const result = await getAdmins();

      // 验证结果
      expect(unifiedDataService.fetchData).toHaveBeenCalledWith(
        '/admin',
        'getAdminsMock',
        {},
        { method: 'GET' }
      );
      expect(result).toEqual([
        {
          id: 1,
          username: 'admin',
          name: '系统管理员',
          role: 'admin'
        }
      ]);
    });
  });

  describe('addAdmin', () => {
    it('should call fetchData with correct parameters', async () => {
      // 设置模拟返回值
      (unifiedDataService.fetchData as any).mockResolvedValue({
        id: 4,
        username: 'newadmin',
        name: '新管理员',
        role: 'admin'
      });

      // 导入服务
      const { addAdmin } = await import('../adminAuthService.new');

      // 调用函数
      const adminData = {
        username: 'newadmin',
        password: 'password',
        name: '新管理员',
        role: 'admin',
        permissions: ['manage_content']
      };
      const result = await addAdmin(adminData);

      // 验证结果
      expect(unifiedDataService.fetchData).toHaveBeenCalledWith(
        '/admin',
        'addAdminMock',
        adminData,
        { method: 'POST' }
      );
      expect(result).toEqual({
        id: 4,
        username: 'newadmin',
        name: '新管理员',
        role: 'admin'
      });
    });
  });

  describe('updateAdmin', () => {
    it('should call fetchData with correct parameters', async () => {
      // 设置模拟返回值
      (unifiedDataService.fetchData as any).mockResolvedValue({
        id: 1,
        username: 'admin',
        name: '更新的管理员',
        role: 'admin'
      });

      // 导入服务
      const { updateAdmin } = await import('../adminAuthService.new');

      // 调用函数
      const adminData = {
        name: '更新的管理员',
        permissions: ['manage_content', 'view_statistics']
      };
      const result = await updateAdmin(1, adminData);

      // 验证结果
      expect(unifiedDataService.fetchData).toHaveBeenCalledWith(
        '/admin/1',
        'updateAdminMock',
        { id: 1, ...adminData },
        { method: 'PUT' }
      );
      expect(result).toEqual({
        id: 1,
        username: 'admin',
        name: '更新的管理员',
        role: 'admin'
      });
    });
  });

  describe('deleteAdmin', () => {
    it('should call fetchData with correct parameters', async () => {
      // 设置模拟返回值
      (unifiedDataService.fetchData as any).mockResolvedValue({ success: true });

      // 导入服务
      const { deleteAdmin } = await import('../adminAuthService.new');

      // 调用函数
      const result = await deleteAdmin(1);

      // 验证结果
      expect(unifiedDataService.fetchData).toHaveBeenCalledWith(
        '/admin/1',
        'deleteAdminMock',
        { id: 1 },
        { method: 'DELETE' }
      );
      expect(result).toEqual({ success: true });
    });
  });
});
