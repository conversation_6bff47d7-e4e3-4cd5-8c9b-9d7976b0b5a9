/**
 * 错误可视化 API
 * 
 * 提供错误热图、时间序列图表和关联分析数据
 */

import { Hono } from 'hono';
import { SecurityModule } from '../../security';

// 创建路由
const app = new Hono();

/**
 * 获取错误热图数据
 */
app.get('/heatmap', async (c) => {
  try {
    // 获取查询参数
    const timeRange = c.req.query('timeRange') || 'day';
    const groupBy = c.req.query('groupBy') || 'hour';
    
    // 获取日志
    const logs = await SecurityModule.getLogs('all', 1, 1000, c.env);
    
    // 生成热图数据
    const heatmapData = generateHeatmapData(logs.logs, timeRange, groupBy);
    
    return c.json({
      success: true,
      heatmap: heatmapData
    });
  } catch (error) {
    console.error('Error generating heatmap data:', error);
    return c.json({
      success: false,
      error: '生成热图数据失败'
    }, 500);
  }
});

/**
 * 获取错误时间序列图表数据
 */
app.get('/timechart', async (c) => {
  try {
    // 获取查询参数
    const timeRange = c.req.query('timeRange') || 'week';
    const groupBy = c.req.query('groupBy') || 'all';
    
    // 获取日志
    const logs = await SecurityModule.getLogs('all', 1, 1000, c.env);
    
    // 生成图表数据
    const chartData = generateTimeChartData(logs.logs, timeRange, groupBy);
    
    // 生成洞察
    const insights = generateTimeChartInsights(chartData, timeRange, groupBy);
    
    return c.json({
      success: true,
      chart: chartData,
      insights
    });
  } catch (error) {
    console.error('Error generating time chart data:', error);
    return c.json({
      success: false,
      error: '生成时间序列图表数据失败'
    }, 500);
  }
});

/**
 * 获取错误关联分析数据
 */
app.get('/correlation', async (c) => {
  try {
    // 获取查询参数
    const type = c.req.query('type') || 'errorCode';
    
    // 获取日志
    const logs = await SecurityModule.getLogs('all', 1, 1000, c.env);
    
    // 获取错误分析结果
    const analysis = await SecurityModule.getErrorAnalysis();
    
    // 生成关联数据
    const correlationData = generateCorrelationData(logs.logs, analysis, type);
    
    // 生成洞察
    const insights = generateCorrelationInsights(correlationData, type);
    
    return c.json({
      success: true,
      correlation: correlationData,
      insights
    });
  } catch (error) {
    console.error('Error generating correlation data:', error);
    return c.json({
      success: false,
      error: '生成关联分析数据失败'
    }, 500);
  }
});

/**
 * 获取错误仪表板数据
 */
app.get('/dashboard', async (c) => {
  try {
    // 获取日志
    const logs = await SecurityModule.getLogs('all', 1, 1000, c.env);
    
    // 获取错误分析结果
    const analysis = await SecurityModule.getErrorAnalysis();
    
    // 获取性能指标
    const performanceMetrics = await SecurityModule.getPerformanceMetrics();
    
    // 生成仪表板数据
    const dashboardData = {
      summary: generateSummaryData(logs.logs, analysis),
      trends: generateTrendData(logs.logs),
      topIssues: generateTopIssuesData(logs.logs, analysis),
      performance: performanceMetrics
    };
    
    return c.json({
      success: true,
      dashboard: dashboardData
    });
  } catch (error) {
    console.error('Error generating dashboard data:', error);
    return c.json({
      success: false,
      error: '生成仪表板数据失败'
    }, 500);
  }
});

/**
 * 生成热图数据
 * @param logs 日志条目
 * @param timeRange 时间范围
 * @param groupBy 分组方式
 */
function generateHeatmapData(logs: any[], timeRange: string, groupBy: string): any[] {
  // 过滤日志，只保留指定时间范围内的日志
  const filteredLogs = filterLogsByTimeRange(logs, timeRange);
  
  if (groupBy === 'hour') {
    // 按小时分组
    return generateHourlyHeatmap(filteredLogs, timeRange);
  } else if (groupBy === 'day') {
    // 按天分组
    return generateDailyHeatmap(filteredLogs, timeRange);
  } else if (groupBy === 'severity') {
    // 按严重性分组
    return generateSeverityHeatmap(filteredLogs);
  } else if (groupBy === 'source') {
    // 按来源分组
    return generateSourceHeatmap(filteredLogs);
  } else if (groupBy === 'errorCode') {
    // 按错误代码分组
    return generateErrorCodeHeatmap(filteredLogs);
  }
  
  return [];
}

/**
 * 生成时间序列图表数据
 * @param logs 日志条目
 * @param timeRange 时间范围
 * @param groupBy 分组方式
 */
function generateTimeChartData(logs: any[], timeRange: string, groupBy: string): any {
  // 过滤日志，只保留指定时间范围内的日志
  const filteredLogs = filterLogsByTimeRange(logs, timeRange);
  
  // 生成时间标签
  const labels = generateTimeLabels(timeRange);
  
  // 初始化数据集
  let datasets = [];
  
  if (groupBy === 'all') {
    // 所有错误
    datasets = [generateAllErrorsDataset(filteredLogs, labels)];
  } else if (groupBy === 'severity') {
    // 按严重性分组
    datasets = generateSeverityDatasets(filteredLogs, labels);
  } else if (groupBy === 'source') {
    // 按来源分组
    datasets = generateSourceDatasets(filteredLogs, labels);
  } else if (groupBy === 'errorCode') {
    // 按错误代码分组
    datasets = generateErrorCodeDatasets(filteredLogs, labels);
  }
  
  return {
    labels,
    datasets
  };
}

/**
 * 生成关联分析数据
 * @param logs 日志条目
 * @param analysis 错误分析结果
 * @param type 关联类型
 */
function generateCorrelationData(logs: any[], analysis: any, type: string): any {
  // 初始化节点和连接
  const nodes = [];
  const links = [];
  
  if (type === 'errorCode') {
    // 错误代码关联
    return generateErrorCodeCorrelation(logs, analysis);
  } else if (type === 'source') {
    // 错误来源关联
    return generateSourceCorrelation(logs, analysis);
  } else if (type === 'time') {
    // 时间关联
    return generateTimeCorrelation(logs);
  } else if (type === 'severity') {
    // 严重性关联
    return generateSeverityCorrelation(logs);
  }
  
  return { nodes, links };
}

/**
 * 过滤日志，只保留指定时间范围内的日志
 * @param logs 日志条目
 * @param timeRange 时间范围
 */
function filterLogsByTimeRange(logs: any[], timeRange: string): any[] {
  const now = Date.now();
  let cutoff = now;
  
  if (timeRange === 'day') {
    cutoff = now - 24 * 60 * 60 * 1000; // 24小时
  } else if (timeRange === 'week') {
    cutoff = now - 7 * 24 * 60 * 60 * 1000; // 7天
  } else if (timeRange === 'month') {
    cutoff = now - 30 * 24 * 60 * 60 * 1000; // 30天
  }
  
  return logs.filter(log => log.timestamp >= cutoff);
}

/**
 * 生成时间标签
 * @param timeRange 时间范围
 */
function generateTimeLabels(timeRange: string): string[] {
  const labels = [];
  const now = new Date();
  
  if (timeRange === 'day') {
    // 24小时
    for (let i = 23; i >= 0; i--) {
      const date = new Date(now.getTime() - i * 60 * 60 * 1000);
      labels.push(`${date.getHours()}:00`);
    }
  } else if (timeRange === 'week') {
    // 7天
    for (let i = 6; i >= 0; i--) {
      const date = new Date(now.getTime() - i * 24 * 60 * 60 * 1000);
      labels.push(date.toLocaleDateString());
    }
  } else if (timeRange === 'month') {
    // 30天
    for (let i = 29; i >= 0; i--) {
      const date = new Date(now.getTime() - i * 24 * 60 * 60 * 1000);
      labels.push(date.toLocaleDateString());
    }
  }
  
  return labels;
}

/**
 * 生成时间序列图表洞察
 * @param chartData 图表数据
 * @param timeRange 时间范围
 * @param groupBy 分组方式
 */
function generateTimeChartInsights(chartData: any, timeRange: string, groupBy: string): string[] {
  const insights = [];
  
  // 如果没有数据，返回空数组
  if (!chartData.datasets || chartData.datasets.length === 0) {
    return ['没有足够的数据生成洞察'];
  }
  
  // 计算总错误数
  const totalErrors = chartData.datasets.reduce((sum, dataset) => {
    return sum + dataset.data.reduce((s, v) => s + v, 0);
  }, 0);
  
  insights.push(`在过去的${timeRange === 'day' ? '24小时' : timeRange === 'week' ? '7天' : '30天'}中，共记录了 ${totalErrors} 个错误。`);
  
  // 查找峰值
  if (chartData.datasets.length === 1) {
    const data = chartData.datasets[0].data;
    const maxValue = Math.max(...data);
    const maxIndex = data.indexOf(maxValue);
    
    if (maxValue > 0) {
      insights.push(`错误峰值出现在 ${chartData.labels[maxIndex]}，共 ${maxValue} 个错误。`);
    }
  } else {
    // 查找最多错误的数据集
    let maxDataset = null;
    let maxTotal = 0;
    
    for (const dataset of chartData.datasets) {
      const total = dataset.data.reduce((sum, value) => sum + value, 0);
      if (total > maxTotal) {
        maxTotal = total;
        maxDataset = dataset;
      }
    }
    
    if (maxDataset && maxTotal > 0) {
      insights.push(`${maxDataset.label} 是最常见的错误类型，共 ${maxTotal} 个错误。`);
    }
  }
  
  // 分析趋势
  if (chartData.datasets.length === 1) {
    const data = chartData.datasets[0].data;
    const firstHalf = data.slice(0, Math.floor(data.length / 2));
    const secondHalf = data.slice(Math.floor(data.length / 2));
    
    const firstHalfSum = firstHalf.reduce((sum, value) => sum + value, 0);
    const secondHalfSum = secondHalf.reduce((sum, value) => sum + value, 0);
    
    if (secondHalfSum > firstHalfSum * 1.2) {
      insights.push('错误数量呈上升趋势，可能需要关注。');
    } else if (firstHalfSum > secondHalfSum * 1.2) {
      insights.push('错误数量呈下降趋势，系统稳定性可能有所改善。');
    } else {
      insights.push('错误数量相对稳定。');
    }
  }
  
  return insights;
}

/**
 * 生成关联分析洞察
 * @param correlationData 关联数据
 * @param type 关联类型
 */
function generateCorrelationInsights(correlationData: any, type: string): string[] {
  const insights = [];
  
  // 如果没有数据，返回空数组
  if (!correlationData.nodes || correlationData.nodes.length === 0) {
    return ['没有足够的数据生成洞察'];
  }
  
  // 计算节点数量
  const nodeCount = correlationData.nodes.length;
  insights.push(`共发现 ${nodeCount} 个相关的${type === 'errorCode' ? '错误代码' : type === 'source' ? '错误来源' : type === 'time' ? '时间点' : '严重性级别'}。`);
  
  // 查找最大节点
  const maxNode = correlationData.nodes.reduce((max, node) => {
    return (node.count > max.count) ? node : max;
  }, { count: 0 });
  
  if (maxNode.count > 0) {
    insights.push(`${maxNode.label} 是最常见的${type === 'errorCode' ? '错误代码' : type === 'source' ? '错误来源' : type === 'time' ? '时间点' : '严重性级别'}，共 ${maxNode.count} 个错误。`);
  }
  
  // 查找最强关联
  if (correlationData.links && correlationData.links.length > 0) {
    const maxLink = correlationData.links.reduce((max, link) => {
      return (link.value > max.value) ? link : max;
    }, { value: 0 });
    
    if (maxLink.value > 0.5) {
      const sourceNode = correlationData.nodes.find(node => node.id === maxLink.source);
      const targetNode = correlationData.nodes.find(node => node.id === maxLink.target);
      
      if (sourceNode && targetNode) {
        insights.push(`${sourceNode.label} 和 ${targetNode.label} 之间存在强关联，相关性为 ${Math.round(maxLink.value * 100)}%。`);
      }
    }
  }
  
  return insights;
}

/**
 * 生成摘要数据
 * @param logs 日志条目
 * @param analysis 错误分析结果
 */
function generateSummaryData(logs: any[], analysis: any): any {
  return {
    totalErrors: logs.length,
    criticalErrors: logs.filter(log => log.severity === 'critical').length,
    highErrors: logs.filter(log => log.severity === 'high').length,
    mediumErrors: logs.filter(log => log.severity === 'medium').length,
    lowErrors: logs.filter(log => log.severity === 'low').length,
    anomalies: Object.values(analysis.anomalies || {}).filter((a: any) => a.isAnomaly).length,
    clusters: (analysis.clusters || []).length,
    patterns: (analysis.patterns || []).length,
    recentErrors: logs.slice(0, 5).map(log => ({
      id: log.id,
      message: log.message,
      timestamp: log.timestamp,
      severity: log.severity,
      errorCode: log.errorCode
    }))
  };
}

/**
 * 生成趋势数据
 * @param logs 日志条目
 */
function generateTrendData(logs: any[]): any {
  return {
    errorsByDay: calculateErrorsByDay(logs),
    errorsBySeverity: calculateErrorsBySeverity(logs),
    errorsBySource: calculateErrorsBySource(logs)
  };
}

/**
 * 生成热点问题数据
 * @param logs 日志条目
 * @param analysis 错误分析结果
 */
function generateTopIssuesData(logs: any[], analysis: any): any {
  return {
    topErrorCodes: calculateTopErrorCodes(logs),
    topSources: calculateTopSources(logs),
    topPatterns: (analysis.patterns || []).slice(0, 5)
  };
}

/**
 * 计算每天的错误数量
 * @param logs 日志条目
 */
function calculateErrorsByDay(logs: any[]): { date: string; count: number }[] {
  const errorsByDay: { [key: string]: number } = {};
  
  for (const log of logs) {
    const date = new Date(log.timestamp).toISOString().substring(0, 10);
    errorsByDay[date] = (errorsByDay[date] || 0) + 1;
  }
  
  return Object.entries(errorsByDay)
    .map(([date, count]) => ({ date, count }))
    .sort((a, b) => a.date.localeCompare(b.date));
}

/**
 * 计算各严重性级别的错误数量
 * @param logs 日志条目
 */
function calculateErrorsBySeverity(logs: any[]): { severity: string; count: number }[] {
  const errorsBySeverity: { [key: string]: number } = {
    critical: 0,
    high: 0,
    medium: 0,
    low: 0,
    unknown: 0
  };
  
  for (const log of logs) {
    const severity = log.severity || 'unknown';
    errorsBySeverity[severity] = (errorsBySeverity[severity] || 0) + 1;
  }
  
  return Object.entries(errorsBySeverity)
    .map(([severity, count]) => ({ severity, count }));
}

/**
 * 计算各来源的错误数量
 * @param logs 日志条目
 */
function calculateErrorsBySource(logs: any[]): { source: string; count: number }[] {
  const errorsBySource: { [key: string]: number } = {};
  
  for (const log of logs) {
    const source = log.source || 'unknown';
    errorsBySource[source] = (errorsBySource[source] || 0) + 1;
  }
  
  return Object.entries(errorsBySource)
    .map(([source, count]) => ({ source, count }))
    .sort((a, b) => b.count - a.count);
}

/**
 * 计算最常见的错误代码
 * @param logs 日志条目
 */
function calculateTopErrorCodes(logs: any[]): { code: string; count: number }[] {
  const errorsByCode: { [key: string]: number } = {};
  
  for (const log of logs) {
    if (log.errorCode) {
      errorsByCode[log.errorCode] = (errorsByCode[log.errorCode] || 0) + 1;
    }
  }
  
  return Object.entries(errorsByCode)
    .map(([code, count]) => ({ code, count }))
    .sort((a, b) => b.count - a.count)
    .slice(0, 10);
}

/**
 * 计算最常见的错误来源
 * @param logs 日志条目
 */
function calculateTopSources(logs: any[]): { source: string; count: number }[] {
  const errorsBySource: { [key: string]: number } = {};
  
  for (const log of logs) {
    const source = log.source || 'unknown';
    errorsBySource[source] = (errorsBySource[source] || 0) + 1;
  }
  
  return Object.entries(errorsBySource)
    .map(([source, count]) => ({ source, count }))
    .sort((a, b) => b.count - a.count)
    .slice(0, 5);
}

// 导出路由
export default app;
