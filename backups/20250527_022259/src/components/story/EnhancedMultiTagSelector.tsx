import { useState, useEffect, useRef } from 'react';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { ScrollArea } from '@/components/ui/scroll-area';
import { 
  X, 
  Check, 
  ChevronDown, 
  ChevronUp, 
  Search, 
  Tag as TagIcon,
  Plus,
  Sparkles
} from 'lucide-react';
import {
  Collapsible,
  CollapsibleContent,
  CollapsibleTrigger,
} from '@/components/ui/collapsible';
import { cn } from '@/lib/utils';
import { STORY_TAGS, TAG_CATEGORY_LABELS, TAG_CATEGORY_COLORS, type Tag } from '@/constants/storyConstants';

interface EnhancedMultiTagSelectorProps {
  selectedTags: string[];
  onTagsChange: (tags: string[]) => void;
  maxTags?: number;
  className?: string;
  content?: string; // 用于推荐标签的内容
  showRecommendations?: boolean;
}

export default function EnhancedMultiTagSelector({
  selectedTags,
  onTagsChange,
  maxTags = 5,
  className,
  content = '',
  showRecommendations = false,
}: EnhancedMultiTagSelectorProps) {
  const [isOpen, setIsOpen] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');
  const [activeCategory, setActiveCategory] = useState('all');
  const [recommendedTags, setRecommendedTags] = useState<string[]>([]);
  const [isLoadingRecommendations, setIsLoadingRecommendations] = useState(false);
  const searchInputRef = useRef<HTMLInputElement>(null);

  // 将标签按类别分组
  const categorizedTags: Record<string, Tag[]> = {
    all: STORY_TAGS.filter(tag => tag.id !== 'all'),
    job: STORY_TAGS.filter(tag => tag.category === 'job'),
    education: STORY_TAGS.filter(tag => tag.category === 'education'),
    industry: STORY_TAGS.filter(tag => tag.category === 'industry'),
    experience: STORY_TAGS.filter(tag => tag.category === 'experience'),
    other: STORY_TAGS.filter(tag => tag.category === 'other'),
  };

  // 切换标签选择状态
  const toggleTag = (tagId: string) => {
    if (selectedTags.includes(tagId)) {
      // 移除标签
      onTagsChange(selectedTags.filter(id => id !== tagId));
    } else {
      // 添加标签，但要检查是否超过最大数量
      if (selectedTags.length < maxTags) {
        onTagsChange([...selectedTags, tagId]);
      }
    }
  };

  // 清除所有选中的标签
  const clearTags = () => {
    onTagsChange([]);
  };
  
  // 获取标签推荐（模拟API调用）
  const fetchTagRecommendations = async () => {
    if (!content || !showRecommendations) return;
    
    try {
      setIsLoadingRecommendations(true);
      
      // 模拟API调用 - 基于内容关键词推荐标签
      const keywords = content.toLowerCase();
      const recommendations: string[] = [];
      
      // 简单的关键词匹配逻辑
      if (keywords.includes('求职') || keywords.includes('找工作')) {
        recommendations.push('job-hunting', 'job-search');
      }
      if (keywords.includes('面试')) {
        recommendations.push('interview');
      }
      if (keywords.includes('实习')) {
        recommendations.push('internship');
      }
      if (keywords.includes('转行')) {
        recommendations.push('career-change');
      }
      if (keywords.includes('薪资') || keywords.includes('工资')) {
        recommendations.push('salary');
      }
      if (keywords.includes('本科')) {
        recommendations.push('bachelor');
      }
      if (keywords.includes('硕士') || keywords.includes('研究生')) {
        recommendations.push('master');
      }
      if (keywords.includes('IT') || keywords.includes('程序') || keywords.includes('开发')) {
        recommendations.push('it-industry');
      }
      if (keywords.includes('金融')) {
        recommendations.push('finance');
      }
      
      // 模拟网络延迟
      await new Promise(resolve => setTimeout(resolve, 500));
      
      setRecommendedTags(recommendations.slice(0, 3));
    } catch (error) {
      console.error('获取标签推荐失败:', error);
    } finally {
      setIsLoadingRecommendations(false);
    }
  };
  
  // 当内容变化时获取标签推荐
  useEffect(() => {
    if (content && showRecommendations) {
      fetchTagRecommendations();
    }
  }, [content, showRecommendations]);
  
  // 当打开选择器时，聚焦搜索框
  useEffect(() => {
    if (isOpen && searchInputRef.current) {
      setTimeout(() => {
        searchInputRef.current?.focus();
      }, 100);
    }
  }, [isOpen]);
  
  // 筛选标签
  const getFilteredTags = (categoryId: string) => {
    let filteredTags = categorizedTags[categoryId] || [];
    
    if (searchQuery) {
      const query = searchQuery.toLowerCase();
      filteredTags = filteredTags.filter(tag => 
        tag.label.toLowerCase().includes(query) || 
        tag.id.toLowerCase().includes(query)
      );
    }
    
    return filteredTags;
  };
  
  // 渲染标签颜色
  const getTagColor = (tag: Tag) => {
    if (tag.color) return tag.color;
    if (tag.category) return TAG_CATEGORY_COLORS[tag.category] || 'gray';
    return 'gray';
  };
  
  // 渲染标签徽章
  const renderTagBadge = (tag: Tag, isSelected: boolean = false) => {
    const color = getTagColor(tag);
    
    return (
      <Badge
        key={tag.id}
        variant={isSelected ? "default" : "outline"}
        className={cn(
          "cursor-pointer flex items-center gap-1 transition-all",
          {
            "bg-blue-100 text-blue-800 hover:bg-blue-200": color === 'blue' && !isSelected,
            "bg-green-100 text-green-800 hover:bg-green-200": color === 'green' && !isSelected,
            "bg-purple-100 text-purple-800 hover:bg-purple-200": color === 'purple' && !isSelected,
            "bg-yellow-100 text-yellow-800 hover:bg-yellow-200": color === 'yellow' && !isSelected,
            "bg-gray-100 text-gray-800 hover:bg-gray-200": color === 'gray' && !isSelected,
          }
        )}
        onClick={() => toggleTag(tag.id)}
      >
        {tag.label}
        {isSelected ? (
          <X className="h-3 w-3" />
        ) : (
          <Plus className="h-3 w-3" />
        )}
      </Badge>
    );
  };

  return (
    <div className={cn("w-full", className)}>
      {/* 已选标签显示 */}
      <div className="flex flex-wrap gap-2 mb-2">
        {selectedTags.length > 0 ? (
          <>
            {selectedTags.map(tagId => {
              const tag = STORY_TAGS.find(t => t.id === tagId);
              if (!tag) return null;
              return renderTagBadge(tag, true);
            })}
            
            <Button
              variant="ghost"
              size="sm"
              className="h-6 px-2 text-xs"
              onClick={clearTags}
            >
              清除全部
            </Button>
          </>
        ) : (
          <span className="text-sm text-gray-500">未选择标签</span>
        )}
      </div>

      {/* 折叠选择器 */}
      <Collapsible open={isOpen} onOpenChange={setIsOpen}>
        <CollapsibleTrigger asChild>
          <Button
            variant="outline"
            size="sm"
            className="w-full flex justify-between items-center"
          >
            <span>选择标签 ({selectedTags.length}/{maxTags})</span>
            {isOpen ? (
              <ChevronUp className="h-4 w-4" />
            ) : (
              <ChevronDown className="h-4 w-4" />
            )}
          </Button>
        </CollapsibleTrigger>
        <CollapsibleContent>
          <div className="mt-2 border rounded-md p-4 space-y-4">
            {/* 搜索框 */}
            <div className="relative">
              <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
              <Input
                ref={searchInputRef}
                placeholder="搜索标签..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="pl-8"
              />
            </div>
            
            {/* 标签推荐 */}
            {showRecommendations && (
              <div className="space-y-2">
                <div className="flex items-center gap-2">
                  <Sparkles className="h-4 w-4 text-amber-500" />
                  <Label className="text-sm font-medium">推荐标签</Label>
                  {isLoadingRecommendations && (
                    <div className="h-4 w-4 border-2 border-amber-500 border-t-transparent rounded-full animate-spin"></div>
                  )}
                </div>
                <div className="flex flex-wrap gap-2">
                  {recommendedTags.map(tagId => {
                    const tag = STORY_TAGS.find(t => t.id === tagId);
                    if (!tag) return null;
                    return renderTagBadge(tag, selectedTags.includes(tagId));
                  })}
                  {!isLoadingRecommendations && recommendedTags.length === 0 && (
                    <span className="text-sm text-gray-500">暂无推荐标签</span>
                  )}
                </div>
              </div>
            )}
            
            {/* 标签分类选项卡 */}
            <Tabs value={activeCategory} onValueChange={setActiveCategory}>
              <TabsList className="w-full flex overflow-x-auto">
                <TabsTrigger value="all">全部</TabsTrigger>
                <TabsTrigger value="job">{TAG_CATEGORY_LABELS.job}</TabsTrigger>
                <TabsTrigger value="education">{TAG_CATEGORY_LABELS.education}</TabsTrigger>
                <TabsTrigger value="industry">{TAG_CATEGORY_LABELS.industry}</TabsTrigger>
                <TabsTrigger value="experience">{TAG_CATEGORY_LABELS.experience}</TabsTrigger>
                <TabsTrigger value="other">{TAG_CATEGORY_LABELS.other}</TabsTrigger>
              </TabsList>
              
              {Object.keys(categorizedTags).map(category => (
                <TabsContent key={category} value={category} className="mt-2">
                  <ScrollArea className="h-[200px] pr-4">
                    <div className="flex flex-wrap gap-2">
                      {getFilteredTags(category).length > 0 ? (
                        getFilteredTags(category).map(tag => 
                          renderTagBadge(tag, selectedTags.includes(tag.id))
                        )
                      ) : (
                        <div className="w-full flex flex-col items-center justify-center py-8 text-muted-foreground">
                          <TagIcon className="h-8 w-8 mb-2" />
                          <p>未找到匹配的标签</p>
                        </div>
                      )}
                    </div>
                  </ScrollArea>
                </TabsContent>
              ))}
            </Tabs>

            {selectedTags.length >= maxTags && (
              <p className="text-xs text-amber-600">
                已达到最大标签数量 ({maxTags})
              </p>
            )}
          </div>
        </CollapsibleContent>
      </Collapsible>
    </div>
  );
}
