import { useState } from 'react';
import { z } from 'zod';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage
} from '@/components/ui/form';
import { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { useToast } from '@/components/ui/use-toast';
import { sendVerificationEmail, verifyCode } from '@/lib/api';
import { QuestionnaireData } from '@/lib/api';
import { AnonymousIdentityInput } from '@/components/anonymous-auth/AnonymousIdentityInput';

// Define the form schema for submission type
const submissionTypeSchema = z.object({
  submissionType: z.enum(['anonymous', 'verified']),
});

// Define the form schema for email verification
const emailVerificationSchema = z.object({
  email: z.string().email({ message: '请输入有效的邮箱地址' }),
});

// Define the form schema for verification code
const verificationCodeSchema = z.object({
  code: z.string().length(6, { message: '验证码必须是6位数字' }),
});

interface EmailVerificationModuleProps {
  data: Partial<QuestionnaireData>;
  onUpdate: (data: Partial<QuestionnaireData>) => void;
  onSubmit: (verificationId?: string) => void;
  isSubmitting: boolean;
}

export default function EmailVerificationModule({
  data,
  onUpdate,
  onSubmit,
  isSubmitting
}: EmailVerificationModuleProps) {
  const { toast } = useToast();
  const [step, setStep] = useState<'type' | 'email' | 'code'>('type');
  const [submissionType, setSubmissionType] = useState<'anonymous' | 'verified'>('anonymous');
  const [email, setEmail] = useState('');
  const [isVerifying, setIsVerifying] = useState(false);
  const [isSendingCode, setIsSendingCode] = useState(false);
  const [expiryTime, setExpiryTime] = useState<Date | null>(null);

  // 匿名身份验证状态
  const [identityA, setIdentityA] = useState<string>('');
  const [identityB, setIdentityB] = useState<string>('');
  const [identityEnabled, setIdentityEnabled] = useState<boolean>(false);

  // Form for submission type
  const typeForm = useForm<z.infer<typeof submissionTypeSchema>>({
    resolver: zodResolver(submissionTypeSchema),
    defaultValues: {
      submissionType: 'anonymous',
    },
  });

  // Form for email
  const emailForm = useForm<z.infer<typeof emailVerificationSchema>>({
    resolver: zodResolver(emailVerificationSchema),
    defaultValues: {
      email: '',
    },
  });

  // Form for verification code
  const codeForm = useForm<z.infer<typeof verificationCodeSchema>>({
    resolver: zodResolver(verificationCodeSchema),
    defaultValues: {
      code: '',
    },
  });

  // Handle submission type selection
  const onSubmissionTypeSubmit = (values: z.infer<typeof submissionTypeSchema>) => {
    setSubmissionType(values.submissionType);

    if (values.submissionType === 'anonymous') {
      // 更新匿名身份信息
      onUpdate({
        isAnonymous: true,
        identityA: identityEnabled ? identityA : undefined,
        identityB: identityEnabled ? identityB : undefined
      });

      // Submit anonymously
      onSubmit();
    } else {
      // Proceed to email verification
      setStep('email');
    }
  };

  // Handle email submission
  const onEmailSubmit = async (values: z.infer<typeof emailVerificationSchema>) => {
    try {
      setIsSendingCode(true);
      setEmail(values.email);

      const response = await sendVerificationEmail({ email: values.email });

      if (response.success) {
        setExpiryTime(new Date(response.expiresAt));
        setStep('code');
        toast({
          title: '验证码已发送',
          description: `验证码已发送到 ${values.email}，请查收`,
        });
      } else {
        toast({
          title: '发送失败',
          description: '验证码发送失败，请稍后重试',
          variant: 'destructive',
        });
      }
    } catch (error) {
      console.error('Error sending verification code:', error);
      toast({
        title: '发送失败',
        description: '验证码发送失败，请稍后重试',
        variant: 'destructive',
      });
    } finally {
      setIsSendingCode(false);
    }
  };

  // Handle verification code submission
  const onCodeSubmit = async (values: z.infer<typeof verificationCodeSchema>) => {
    try {
      setIsVerifying(true);

      const response = await verifyCode({
        email,
        code: values.code,
      });

      if (response.success) {
        toast({
          title: '验证成功',
          description: '邮箱验证成功，您的提交将被标记为已验证',
        });

        // Submit with verification ID
        onSubmit(response.verificationId);
      } else {
        toast({
          title: '验证失败',
          description: '验证码无效或已过期，请重试',
          variant: 'destructive',
        });
      }
    } catch (error) {
      console.error('Error verifying code:', error);
      toast({
        title: '验证失败',
        description: '验证失败，请稍后重试',
        variant: 'destructive',
      });
    } finally {
      setIsVerifying(false);
    }
  };

  // Handle resend verification code
  const handleResendCode = async () => {
    try {
      setIsSendingCode(true);

      const response = await sendVerificationEmail({ email });

      if (response.success) {
        setExpiryTime(new Date(response.expiresAt));
        toast({
          title: '验证码已重新发送',
          description: `验证码已发送到 ${email}，请查收`,
        });
      } else {
        toast({
          title: '发送失败',
          description: '验证码发送失败，请稍后重试',
          variant: 'destructive',
        });
      }
    } catch (error) {
      console.error('Error resending verification code:', error);
      toast({
        title: '发送失败',
        description: '验证码发送失败，请稍后重试',
        variant: 'destructive',
      });
    } finally {
      setIsSendingCode(false);
    }
  };

  // Calculate time remaining for verification code
  const getTimeRemaining = () => {
    if (!expiryTime) return null;

    const now = new Date();
    const diff = expiryTime.getTime() - now.getTime();

    if (diff <= 0) return null;

    const minutes = Math.floor(diff / (1000 * 60));
    const seconds = Math.floor((diff % (1000 * 60)) / 1000);

    return `${minutes}:${seconds.toString().padStart(2, '0')}`;
  };

  return (
    <div className="max-w-md mx-auto">
      {step === 'type' && (
        <>
          <div className="mb-6 p-4 bg-blue-50 rounded-lg border border-blue-100">
            <h3 className="text-lg font-medium text-blue-800 mb-2">提交方式选择</h3>
            <p className="text-blue-700">
              您可以选择匿名提交或通过邮箱验证提交。验证后的数据将被标记为"已验证"，
              有助于提高数据可信度，但我们不会公开您的邮箱信息。
            </p>
          </div>

          <Form {...typeForm}>
            <form onSubmit={typeForm.handleSubmit(onSubmissionTypeSubmit)} className="space-y-6">
              <FormField
                control={typeForm.control}
                name="submissionType"
                render={({ field }) => (
                  <FormItem>
                    <FormControl>
                      <RadioGroup
                        onValueChange={field.onChange}
                        defaultValue={field.value}
                        className="flex flex-col space-y-4"
                      >
                        <FormItem className="flex items-start space-x-3 space-y-0 rounded-md border p-4">
                          <FormControl>
                            <RadioGroupItem value="anonymous" />
                          </FormControl>
                          <div className="space-y-1 w-full">
                            <FormLabel className="font-medium">匿名提交</FormLabel>
                            <p className="text-sm text-gray-500 mb-4">
                              不需要验证，数据将被标记为"匿名"。适合快速提交，但数据可信度较低。
                            </p>

                            {/* 匿名身份验证 */}
                            <AnonymousIdentityInput
                              onIdentityChange={(a, b, enabled) => {
                                setIdentityA(a);
                                setIdentityB(b);
                                setIdentityEnabled(enabled);
                              }}
                              className="mt-4"
                            />
                          </div>
                        </FormItem>
                        <FormItem className="flex items-start space-x-3 space-y-0 rounded-md border p-4">
                          <FormControl>
                            <RadioGroupItem value="verified" />
                          </FormControl>
                          <div className="space-y-1">
                            <FormLabel className="font-medium">邮箱验证提交</FormLabel>
                            <p className="text-sm text-gray-500">
                              需要通过邮箱验证码验证，数据将被标记为"已验证"。提高数据可信度，邮箱信息不会公开。
                            </p>
                          </div>
                        </FormItem>
                      </RadioGroup>
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <Button type="submit" className="w-full" disabled={isSubmitting}>
                {isSubmitting ? '提交中...' : '继续'}
              </Button>
            </form>
          </Form>
        </>
      )}

      {step === 'email' && (
        <>
          <div className="mb-6 p-4 bg-blue-50 rounded-lg border border-blue-100">
            <h3 className="text-lg font-medium text-blue-800 mb-2">邮箱验证</h3>
            <p className="text-blue-700">
              请输入您的邮箱地址，我们将发送验证码。验证成功后，您的提交将被标记为"已验证"。
              我们不会公开您的邮箱信息，也不会向您发送营销邮件。
            </p>
          </div>

          <Form {...emailForm}>
            <form onSubmit={emailForm.handleSubmit(onEmailSubmit)} className="space-y-6">
              <FormField
                control={emailForm.control}
                name="email"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>邮箱地址</FormLabel>
                    <FormControl>
                      <Input placeholder="请输入您的邮箱地址" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <div className="flex space-x-4">
                <Button
                  type="button"
                  variant="outline"
                  className="flex-1"
                  onClick={() => setStep('type')}
                >
                  返回
                </Button>
                <Button
                  type="submit"
                  className="flex-1"
                  disabled={isSendingCode}
                >
                  {isSendingCode ? '发送中...' : '发送验证码'}
                </Button>
              </div>
            </form>
          </Form>
        </>
      )}

      {step === 'code' && (
        <>
          <div className="mb-6 p-4 bg-blue-50 rounded-lg border border-blue-100">
            <h3 className="text-lg font-medium text-blue-800 mb-2">输入验证码</h3>
            <p className="text-blue-700">
              验证码已发送到 <span className="font-medium">{email}</span>，
              请查收并输入6位数字验证码。
              {expiryTime && getTimeRemaining() && (
                <span className="block mt-1">
                  验证码有效期: <span className="font-medium">{getTimeRemaining()}</span>
                </span>
              )}
            </p>
          </div>

          <Form {...codeForm}>
            <form onSubmit={codeForm.handleSubmit(onCodeSubmit)} className="space-y-6">
              <FormField
                control={codeForm.control}
                name="code"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>验证码</FormLabel>
                    <FormControl>
                      <Input
                        placeholder="请输入6位验证码"
                        maxLength={6}
                        {...field}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <div className="flex space-x-4">
                <Button
                  type="button"
                  variant="outline"
                  className="flex-1"
                  onClick={handleResendCode}
                  disabled={isSendingCode}
                >
                  {isSendingCode ? '发送中...' : '重新发送'}
                </Button>
                <Button
                  type="submit"
                  className="flex-1"
                  disabled={isVerifying || isSubmitting}
                >
                  {isVerifying ? '验证中...' : isSubmitting ? '提交中...' : '验证并提交'}
                </Button>
              </div>

              <Button
                type="button"
                variant="link"
                className="w-full"
                onClick={() => setStep('email')}
              >
                更换邮箱地址
              </Button>
            </form>
          </Form>
        </>
      )}
    </div>
  );
}
