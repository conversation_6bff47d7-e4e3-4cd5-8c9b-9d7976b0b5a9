/**
 * 管理员认证处理程序测试
 */

import { describe, it, expect, beforeEach, vi } from 'vitest';
import { handleAdminLogin } from '../../src/handlers/admin';
import { mockEnv, createMockRequest, parseResponseJson } from '../setup';
import { resetAllMocks } from '../utils';

describe('管理员认证处理程序', () => {
  beforeEach(() => {
    resetAllMocks();
    vi.clearAllMocks();
  });

  describe('handleAdminLogin', () => {
    it('应该在凭据正确时返回成功响应和令牌', async () => {
      // 准备请求
      const request = createMockRequest({
        method: 'POST',
        url: 'http://localhost/api/v1/admin/login',
        body: {
          username: 'admin',
          password: 'admin123'
        }
      });

      // 执行处理程序
      const response = await handleAdminLogin(request, mockEnv);
      
      // 验证响应
      expect(response.status).toBe(200);
      
      const data = await parseResponseJson(response);
      expect(data.success).toBe(true);
      expect(data.token).toBeDefined();
      expect(data.user).toBeDefined();
      expect(data.user.role).toBe('admin');
    });

    it('应该在用户名错误时返回401错误', async () => {
      // 准备请求
      const request = createMockRequest({
        method: 'POST',
        url: 'http://localhost/api/v1/admin/login',
        body: {
          username: 'wrong',
          password: 'admin123'
        }
      });

      // 执行处理程序
      const response = await handleAdminLogin(request, mockEnv);
      
      // 验证响应
      expect(response.status).toBe(401);
      
      const data = await parseResponseJson(response);
      expect(data.success).toBe(false);
      expect(data.error).toBe('无效的用户名或密码');
    });

    it('应该在密码错误时返回401错误', async () => {
      // 准备请求
      const request = createMockRequest({
        method: 'POST',
        url: 'http://localhost/api/v1/admin/login',
        body: {
          username: 'admin',
          password: 'wrong'
        }
      });

      // 执行处理程序
      const response = await handleAdminLogin(request, mockEnv);
      
      // 验证响应
      expect(response.status).toBe(401);
      
      const data = await parseResponseJson(response);
      expect(data.success).toBe(false);
      expect(data.error).toBe('无效的用户名或密码');
    });

    it('应该在请求体缺少必要参数时返回400错误', async () => {
      // 准备请求
      const request = createMockRequest({
        method: 'POST',
        url: 'http://localhost/api/v1/admin/login',
        body: {
          username: 'admin'
          // 缺少密码
        }
      });

      // 执行处理程序
      const response = await handleAdminLogin(request, mockEnv);
      
      // 验证响应
      expect(response.status).toBe(400);
      
      const data = await parseResponseJson(response);
      expect(data.success).toBe(false);
      expect(data.error).toBe('缺少必要参数');
    });

    it('应该识别超级管理员并返回正确的角色', async () => {
      // 准备请求
      const request = createMockRequest({
        method: 'POST',
        url: 'http://localhost/api/v1/admin/login',
        body: {
          username: 'superadmin',
          password: 'superadmin123'
        }
      });

      // 执行处理程序
      const response = await handleAdminLogin(request, mockEnv);
      
      // 验证响应
      expect(response.status).toBe(200);
      
      const data = await parseResponseJson(response);
      expect(data.success).toBe(true);
      expect(data.token).toBeDefined();
      expect(data.user).toBeDefined();
      expect(data.user.role).toBe('superadmin');
    });

    it('应该在发生异常时返回500错误', async () => {
      // 模拟JSON.parse抛出异常
      const originalJsonParse = JSON.parse;
      global.JSON.parse = vi.fn().mockImplementation(() => {
        throw new Error('模拟异常');
      });

      // 准备请求
      const request = createMockRequest({
        method: 'POST',
        url: 'http://localhost/api/v1/admin/login',
        body: 'invalid-json'
      });

      // 执行处理程序
      const response = await handleAdminLogin(request, mockEnv);
      
      // 验证响应
      expect(response.status).toBe(500);
      
      const data = await parseResponseJson(response);
      expect(data.success).toBe(false);
      expect(data.error).toBe('服务器错误');

      // 恢复JSON.parse
      global.JSON.parse = originalJsonParse;
    });
  });
});
