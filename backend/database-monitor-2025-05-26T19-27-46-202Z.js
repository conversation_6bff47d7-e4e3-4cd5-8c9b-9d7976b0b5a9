#!/usr/bin/env node

/**
 * 📊 数据库性能监控脚本
 * 监控关键查询的性能指标
 */

const { performance } = require('perf_hooks');

class DatabasePerformanceMonitor {
  constructor(db) {
    this.db = db;
    this.metrics = [];
  }

  async measureQuery(name, query, params = []) {
    const start = performance.now();
    
    try {
      const result = await this.db.prepare(query).bind(...params).all();
      const duration = performance.now() - start;
      
      this.metrics.push({
        name,
        duration: Math.round(duration * 100) / 100,
        success: true,
        resultCount: result.results?.length || 0,
        timestamp: new Date().toISOString()
      });
      
      return result;
    } catch (error) {
      const duration = performance.now() - start;
      
      this.metrics.push({
        name,
        duration: Math.round(duration * 100) / 100,
        success: false,
        error: error.message,
        timestamp: new Date().toISOString()
      });
      
      throw error;
    }
  }

  async runPerformanceTests() {
    console.log('🚀 开始数据库性能测试...');

    // 测试基础统计查询
    await this.measureQuery(
      'basic_count',
      'SELECT COUNT(*) as total FROM questionnaire_responses_v2'
    );

    // 测试复杂统计查询
    await this.measureQuery(
      'complex_stats',
      `SELECT 
        COUNT(*) as total,
        COUNT(CASE WHEN is_anonymous = 0 THEN 1 END) as verified,
        COUNT(CASE WHEN employment_status_display = '已就业' THEN 1 END) as employed
      FROM questionnaire_responses_v2`
    );

    // 测试分组查询
    await this.measureQuery(
      'major_grouping',
      `SELECT major_display, COUNT(*) as count 
      FROM questionnaire_responses_v2 
      WHERE major_display IS NOT NULL 
      GROUP BY major_display 
      ORDER BY count DESC 
      LIMIT 15`
    );

    // 测试时间范围查询
    await this.measureQuery(
      'time_range',
      `SELECT COUNT(*) as count 
      FROM questionnaire_responses_v2 
      WHERE created_at >= date('now', '-7 days')`
    );

    return this.generateReport();
  }

  generateReport() {
    const report = {
      timestamp: new Date().toISOString(),
      totalQueries: this.metrics.length,
      successfulQueries: this.metrics.filter(m => m.success).length,
      failedQueries: this.metrics.filter(m => !m.success).length,
      averageDuration: this.metrics.reduce((sum, m) => sum + m.duration, 0) / this.metrics.length,
      slowestQuery: this.metrics.reduce((slowest, current) => 
        current.duration > (slowest?.duration || 0) ? current : slowest, null),
      fastestQuery: this.metrics.reduce((fastest, current) => 
        current.duration < (fastest?.duration || Infinity) ? current : fastest, null),
      metrics: this.metrics
    };

    console.log('📊 性能测试报告:');
    console.log(`总查询数: ${report.totalQueries}`);
    console.log(`成功查询: ${report.successfulQueries}`);
    console.log(`失败查询: ${report.failedQueries}`);
    console.log(`平均耗时: ${report.averageDuration.toFixed(2)}ms`);
    
    if (report.slowestQuery) {
      console.log(`最慢查询: ${report.slowestQuery.name} (${report.slowestQuery.duration}ms)`);
    }
    
    if (report.fastestQuery) {
      console.log(`最快查询: ${report.fastestQuery.name} (${report.fastestQuery.duration}ms)`);
    }

    return report;
  }
}

module.exports = DatabasePerformanceMonitor;
