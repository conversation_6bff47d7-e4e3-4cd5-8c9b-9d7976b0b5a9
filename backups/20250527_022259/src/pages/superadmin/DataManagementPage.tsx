import React, { useState, useEffect } from 'react';
import SuperAdminLayout from '@/components/layouts/SuperAdminLayout';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { useToast } from '@/components/ui/use-toast';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Progress } from '@/components/ui/progress';
import {
  Database, Upload, Download, FileText, AlertTriangle, CheckCircle,
  Clock, RefreshCw, Eye, Trash2, Settings, Filter, Search
} from 'lucide-react';

// 数据导入导出任务类型定义
interface DataTask {
  id: string;
  type: 'import' | 'export';
  name: string;
  dataType: string;
  status: 'pending' | 'running' | 'completed' | 'failed' | 'cancelled';
  progress: number;
  createdAt: string;
  completedAt?: string;
  fileSize?: string;
  recordCount?: number;
  errorCount?: number;
  createdBy: string;
}

// 数据类型定义
const DATA_TYPES = [
  { id: 'users', name: '用户数据', description: '用户账户和个人信息' },
  { id: 'questionnaires', name: '问卷数据', description: '问卷内容和回答数据' },
  { id: 'stories', name: '故事数据', description: '用户故事和审核记录' },
  { id: 'audit_logs', name: '审计日志', description: '系统操作审计记录' },
  { id: 'system_config', name: '系统配置', description: '系统设置和配置数据' }
];

// 导出格式选项
const EXPORT_FORMATS = [
  { id: 'csv', name: 'CSV', description: '逗号分隔值文件' },
  { id: 'json', name: 'JSON', description: 'JavaScript对象表示法' },
  { id: 'xlsx', name: 'Excel', description: 'Microsoft Excel文件' },
  { id: 'sql', name: 'SQL', description: 'SQL数据库脚本' }
];

/**
 * 数据管理页面
 */
const DataManagementPage: React.FC = () => {
  const { toast } = useToast();
  const [isLoading, setIsLoading] = useState(false);
  const [tasks, setTasks] = useState<DataTask[]>([]);
  const [selectedDataType, setSelectedDataType] = useState<string>('');
  const [selectedFormat, setSelectedFormat] = useState<string>('csv');
  const [uploadFile, setUploadFile] = useState<File | null>(null);
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState<string>('all');

  // 获取任务数据
  useEffect(() => {
    fetchTasks();
  }, []);

  // 获取任务列表
  const fetchTasks = async () => {
    try {
      setIsLoading(true);
      console.log('📋 调用真实API获取数据任务');

      const response = await fetch('http://localhost:8787/api/admin/data/tasks');

      if (!response.ok) {
        throw new Error(`API调用失败: ${response.status}`);
      }

      const data = await response.json();
      console.log('✅ 数据任务API响应:', data);

      if (data.success) {
        setTasks(data.data.tasks || []);
      } else {
        throw new Error(data.message || '获取数据任务失败');
      }
    } catch (error) {
      console.error('❌ 获取数据任务失败:', error);
      toast({
        variant: 'destructive',
        title: '获取任务失败',
        description: error instanceof Error ? error.message : '发生错误，请稍后重试'
      });
    } finally {
      setIsLoading(false);
    }
  };

  // 获取状态颜色
  const getStatusColor = (status: string) => {
    switch (status) {
      case 'completed': return 'bg-green-100 text-green-800';
      case 'running': return 'bg-blue-100 text-blue-800';
      case 'failed': return 'bg-red-100 text-red-800';
      case 'pending': return 'bg-yellow-100 text-yellow-800';
      case 'cancelled': return 'bg-gray-100 text-gray-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  // 获取状态图标
  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'completed': return <CheckCircle className="h-4 w-4" />;
      case 'running': return <RefreshCw className="h-4 w-4 animate-spin" />;
      case 'failed': return <AlertTriangle className="h-4 w-4" />;
      case 'pending': return <Clock className="h-4 w-4" />;
      default: return <Clock className="h-4 w-4" />;
    }
  };

  // 处理文件上传
  const handleFileUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) {
      setUploadFile(file);
    }
  };

  // 开始导入
  const handleStartImport = async () => {
    if (!uploadFile || !selectedDataType) {
      toast({
        variant: 'destructive',
        title: '参数错误',
        description: '请选择数据类型和上传文件'
      });
      return;
    }

    try {
      setIsLoading(true);
      console.log('📥 调用真实API创建导入任务');

      const response = await fetch('http://localhost:8787/api/admin/data/import', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          dataType: selectedDataType,
          fileName: uploadFile.name,
          fileSize: `${(uploadFile.size / 1024 / 1024).toFixed(2)} MB`
        })
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || `API调用失败: ${response.status}`);
      }

      const data = await response.json();
      console.log('✅ 导入任务创建成功:', data);

      if (data.success) {
        toast({
          title: '导入任务已创建',
          description: data.message || '数据导入任务已开始处理'
        });

        // 重置表单
        setUploadFile(null);
        setSelectedDataType('');

        // 刷新任务列表
        fetchTasks();
      } else {
        throw new Error(data.message || '创建导入任务失败');
      }
    } catch (error) {
      console.error('❌ 导入失败:', error);
      toast({
        variant: 'destructive',
        title: '导入失败',
        description: error instanceof Error ? error.message : '创建导入任务时发生错误'
      });
    } finally {
      setIsLoading(false);
    }
  };

  // 开始导出
  const handleStartExport = async () => {
    if (!selectedDataType) {
      toast({
        variant: 'destructive',
        title: '参数错误',
        description: '请选择要导出的数据类型'
      });
      return;
    }

    try {
      setIsLoading(true);
      console.log('📤 调用真实API创建导出任务');

      const response = await fetch('http://localhost:8787/api/admin/data/export', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          dataType: selectedDataType,
          format: 'csv'
        })
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || `API调用失败: ${response.status}`);
      }

      const data = await response.json();
      console.log('✅ 导出任务创建成功:', data);

      if (data.success) {
        toast({
          title: '导出任务已创建',
          description: data.message || '数据导出任务已开始处理'
        });

        // 重置表单
        setSelectedDataType('');

        // 刷新任务列表
        fetchTasks();
      } else {
        throw new Error(data.message || '创建导出任务失败');
      }
    } catch (error) {
      console.error('❌ 导出失败:', error);
      toast({
        variant: 'destructive',
        title: '导出失败',
        description: error instanceof Error ? error.message : '创建导出任务时发生错误'
      });
    } finally {
      setIsLoading(false);
    }
  };

  // 过滤任务
  const filteredTasks = tasks.filter(task => {
    const matchesSearch = task.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         task.dataType.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesStatus = statusFilter === 'all' || task.status === statusFilter;
    return matchesSearch && matchesStatus;
  });

  return (
    <SuperAdminLayout>
      <div className="container mx-auto py-6">
        <div className="flex justify-between items-center mb-6">
          <div className="flex items-center">
            <Database className="h-6 w-6 mr-2" />
            <h1 className="text-2xl font-bold">数据管理中心</h1>
            <Badge variant="secondary" className="ml-3">
              导入导出
            </Badge>
          </div>
          <div className="flex items-center space-x-2">
            <Button variant="outline">
              <Settings className="mr-2 h-4 w-4" />
              配置
            </Button>
            <Button variant="outline">
              <Eye className="mr-2 h-4 w-4" />
              监控
            </Button>
          </div>
        </div>

        <Tabs defaultValue="import" className="space-y-4">
          <TabsList>
            <TabsTrigger value="import">
              <Upload className="mr-2 h-4 w-4" />
              数据导入
            </TabsTrigger>
            <TabsTrigger value="export">
              <Download className="mr-2 h-4 w-4" />
              数据导出
            </TabsTrigger>
            <TabsTrigger value="tasks">
              <FileText className="mr-2 h-4 w-4" />
              任务管理
            </TabsTrigger>
            <TabsTrigger value="settings">
              <Settings className="mr-2 h-4 w-4" />
              系统设置
            </TabsTrigger>
          </TabsList>

          {/* 数据导入 */}
          <TabsContent value="import">
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              {/* 导入配置 */}
              <Card>
                <CardHeader>
                  <CardTitle>导入配置</CardTitle>
                  <CardDescription>
                    选择数据类型和上传文件
                  </CardDescription>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="space-y-2">
                    <Label htmlFor="dataType">数据类型</Label>
                    <Select value={selectedDataType} onValueChange={setSelectedDataType}>
                      <SelectTrigger>
                        <SelectValue placeholder="选择要导入的数据类型" />
                      </SelectTrigger>
                      <SelectContent>
                        {DATA_TYPES.map(type => (
                          <SelectItem key={type.id} value={type.id}>
                            <div>
                              <div className="font-medium">{type.name}</div>
                              <div className="text-sm text-muted-foreground">{type.description}</div>
                            </div>
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="file">上传文件</Label>
                    <div className="border-2 border-dashed border-gray-300 rounded-lg p-6 text-center">
                      <Upload className="mx-auto h-12 w-12 text-gray-400 mb-4" />
                      <div className="space-y-2">
                        <p className="text-sm text-gray-600">
                          拖拽文件到此处或点击选择文件
                        </p>
                        <p className="text-xs text-gray-500">
                          支持 CSV, JSON, Excel 格式，最大 50MB
                        </p>
                        <Input
                          type="file"
                          accept=".csv,.json,.xlsx,.xls"
                          onChange={handleFileUpload}
                          className="hidden"
                          id="file-upload"
                        />
                        <Label htmlFor="file-upload" className="cursor-pointer">
                          <Button variant="outline" asChild>
                            <span>选择文件</span>
                          </Button>
                        </Label>
                      </div>
                    </div>
                    {uploadFile && (
                      <div className="flex items-center space-x-2 p-2 bg-blue-50 rounded">
                        <FileText className="h-4 w-4 text-blue-600" />
                        <span className="text-sm">{uploadFile.name}</span>
                        <span className="text-xs text-muted-foreground">
                          ({(uploadFile.size / 1024 / 1024).toFixed(2)} MB)
                        </span>
                      </div>
                    )}
                  </div>

                  <Alert>
                    <AlertTriangle className="h-4 w-4" />
                    <AlertTitle>导入须知</AlertTitle>
                    <AlertDescription>
                      • 请确保文件格式正确且数据完整<br/>
                      • 导入过程中请勿关闭页面<br/>
                      • 大文件导入可能需要较长时间
                    </AlertDescription>
                  </Alert>

                  <Button
                    onClick={handleStartImport}
                    disabled={!uploadFile || !selectedDataType || isLoading}
                    className="w-full"
                  >
                    {isLoading ? (
                      <RefreshCw className="mr-2 h-4 w-4 animate-spin" />
                    ) : (
                      <Upload className="mr-2 h-4 w-4" />
                    )}
                    开始导入
                  </Button>
                </CardContent>
              </Card>

              {/* 导入预览 */}
              <Card>
                <CardHeader>
                  <CardTitle>数据预览</CardTitle>
                  <CardDescription>
                    预览将要导入的数据
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  {uploadFile ? (
                    <div className="space-y-4">
                      <div className="grid grid-cols-2 gap-4">
                        <div className="text-center p-3 bg-gray-50 rounded">
                          <p className="text-lg font-semibold">1,250</p>
                          <p className="text-sm text-muted-foreground">预计记录数</p>
                        </div>
                        <div className="text-center p-3 bg-gray-50 rounded">
                          <p className="text-lg font-semibold">12</p>
                          <p className="text-sm text-muted-foreground">数据字段</p>
                        </div>
                      </div>

                      <div className="border rounded-lg p-3">
                        <h4 className="font-medium mb-2">数据样例</h4>
                        <div className="text-sm space-y-1">
                          <div className="grid grid-cols-3 gap-2 font-medium text-muted-foreground">
                            <span>用户ID</span>
                            <span>用户名</span>
                            <span>邮箱</span>
                          </div>
                          <div className="grid grid-cols-3 gap-2">
                            <span>001</span>
                            <span>张三</span>
                            <span><EMAIL></span>
                          </div>
                          <div className="grid grid-cols-3 gap-2">
                            <span>002</span>
                            <span>李四</span>
                            <span><EMAIL></span>
                          </div>
                          <div className="text-center text-muted-foreground">...</div>
                        </div>
                      </div>

                      <Alert>
                        <CheckCircle className="h-4 w-4" />
                        <AlertTitle>验证通过</AlertTitle>
                        <AlertDescription>
                          数据格式正确，可以开始导入
                        </AlertDescription>
                      </Alert>
                    </div>
                  ) : (
                    <div className="text-center py-8 text-muted-foreground">
                      <FileText className="mx-auto h-12 w-12 mb-4" />
                      <p>请先上传文件以预览数据</p>
                    </div>
                  )}
                </CardContent>
              </Card>
            </div>
          </TabsContent>

          {/* 其他标签页内容也将逐步添加 */}
          <TabsContent value="export">
            <Card>
              <CardHeader>
                <CardTitle>数据导出</CardTitle>
                <CardDescription>
                  导出系统数据到文件
                </CardDescription>
              </CardHeader>
              <CardContent>
                <p className="text-muted-foreground">数据导出内容将在下一步添加...</p>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="tasks">
            <Card>
              <CardHeader>
                <CardTitle>任务管理</CardTitle>
                <CardDescription>
                  查看和管理导入导出任务
                </CardDescription>
              </CardHeader>
              <CardContent>
                <p className="text-muted-foreground">任务管理内容将在下一步添加...</p>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="settings">
            <Card>
              <CardHeader>
                <CardTitle>系统设置</CardTitle>
                <CardDescription>
                  配置数据导入导出相关设置
                </CardDescription>
              </CardHeader>
              <CardContent>
                <p className="text-muted-foreground">系统设置内容将在下一步添加...</p>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
      </div>
    </SuperAdminLayout>
  );
};

export default DataManagementPage;
