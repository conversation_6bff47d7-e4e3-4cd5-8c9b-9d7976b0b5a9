import React, { useState, useEffect } from 'react';
import SuperAdminLayout from '@/components/layouts/SuperAdminLayout';
import { Card, CardContent, CardHeader, CardTitle, CardDescription, CardFooter } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { Label } from '@/components/ui/label';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Badge } from '@/components/ui/badge';
import { useToast } from '@/components/ui/use-toast';
import {
  Search, RefreshCw, AlertTriangle, Shield, Lock,
  Eye, Download, Calendar, Clock, Filter, User
} from 'lucide-react';
import {
  Table, TableBody, TableCaption, TableCell, TableHead,
  TableHeader, TableRow
} from '@/components/ui/table';
import {
  Pagination, PaginationContent, PaginationItem,
  PaginationLink, PaginationNext, PaginationPrevious
} from '@/components/ui/pagination-new';
import {
  Dialog, DialogContent, DialogDescription, DialogFooter,
  DialogHeader, DialogTitle, DialogTrigger
} from '@/components/ui/dialog';
import {
  DropdownMenu, DropdownMenuContent, DropdownMenuItem,
  DropdownMenuLabel, DropdownMenuSeparator, DropdownMenuTrigger
} from '@/components/ui/dropdown-menu';
import SecurityLogExportDialog from '@/components/superadmin/SecurityLogExportDialog';

// 日志类型
interface Log {
  id: string;
  type: 'login' | 'action' | 'security';
  severity: 'info' | 'warning' | 'error';
  userId: string;
  username: string;
  userRole: 'user' | 'reviewer' | 'admin' | 'superadmin';
  action: string;
  details: string;
  ip?: string;
  userAgent?: string;
  timestamp: string;
  relatedEntityId?: string;
  relatedEntityType?: string;
}

// 日志搜索参数
interface LogSearchParams {
  type?: string;
  severity?: string;
  username?: string;
  userRole?: string;
  action?: string;
  startDate?: string;
  endDate?: string;
  page?: number;
  limit?: number;
}

/**
 * 超级管理员安全日志页面
 *
 * 查看管理员和审核员的登录历史和操作记录
 */
const SecurityLogsPage: React.FC = () => {
  const { toast } = useToast();
  const [activeTab, setActiveTab] = useState('all');
  const [isLoading, setIsLoading] = useState(false);
  const [searchParams, setSearchParams] = useState<LogSearchParams>({
    page: 1,
    limit: 10
  });
  const [logs, setLogs] = useState<Log[]>([]);
  const [totalLogs, setTotalLogs] = useState(0);
  const [totalPages, setTotalPages] = useState(1);
  const [selectedLog, setSelectedLog] = useState<Log | null>(null);
  const [isLogDetailsOpen, setIsLogDetailsOpen] = useState(false);
  const [isExportDialogOpen, setIsExportDialogOpen] = useState(false);

  // 模拟日志数据
  const mockLogs: Log[] = [
    {
      id: '1',
      type: 'login',
      severity: 'info',
      userId: 'admin-1',
      username: 'admin1',
      userRole: 'admin',
      action: '登录成功',
      details: '管理员登录成功',
      ip: '*************',
      userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
      timestamp: '2023-05-22T08:30:00Z'
    },
    {
      id: '2',
      type: 'login',
      severity: 'error',
      userId: 'admin-1',
      username: 'admin1',
      userRole: 'admin',
      action: '登录失败',
      details: '密码错误',
      ip: '*************',
      userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
      timestamp: '2023-05-22T08:25:00Z'
    },
    {
      id: '3',
      type: 'action',
      severity: 'info',
      userId: 'admin-1',
      username: 'admin1',
      userRole: 'admin',
      action: '修改用户角色',
      details: '将用户 "user123" 的角色从 "user" 修改为 "reviewer"',
      ip: '*************',
      timestamp: '2023-05-22T09:15:00Z',
      relatedEntityId: 'user-123',
      relatedEntityType: 'user'
    },
    {
      id: '4',
      type: 'action',
      severity: 'warning',
      userId: 'reviewer-1',
      username: 'reviewer1',
      userRole: 'reviewer',
      action: '拒绝内容',
      details: '拒绝了用户 "user456" 提交的故事，原因：内容不适合',
      ip: '*************',
      timestamp: '2023-05-22T10:30:00Z',
      relatedEntityId: 'story-789',
      relatedEntityType: 'story'
    },
    {
      id: '5',
      type: 'security',
      severity: 'error',
      userId: 'system',
      username: 'system',
      userRole: 'superadmin',
      action: '安全警报',
      details: '检测到多次登录失败尝试，IP地址已被临时封禁',
      ip: '*************',
      timestamp: '2023-05-22T11:45:00Z'
    },
    {
      id: '6',
      type: 'security',
      severity: 'warning',
      userId: 'system',
      username: 'system',
      userRole: 'superadmin',
      action: '安全警告',
      details: '检测到异常登录位置',
      ip: '*************',
      timestamp: '2023-05-22T12:30:00Z',
      relatedEntityId: 'admin-2',
      relatedEntityType: 'user'
    },
    {
      id: '7',
      type: 'action',
      severity: 'info',
      userId: 'superadmin-1',
      username: 'superadmin',
      userRole: 'superadmin',
      action: '系统配置更新',
      details: '更新了系统安全设置',
      ip: '*************',
      timestamp: '2023-05-22T14:15:00Z'
    }
  ];

  // 加载日志数据
  useEffect(() => {
    fetchLogs();
  }, [searchParams, activeTab]);

  // 获取日志列表
  const fetchLogs = async () => {
    try {
      setIsLoading(true);

      // 模拟网络延迟
      await new Promise(resolve => setTimeout(resolve, 500));

      // 过滤日志
      let filteredLogs = [...mockLogs];

      // 根据标签过滤
      if (activeTab !== 'all') {
        filteredLogs = filteredLogs.filter(log => log.type === activeTab);
      }

      // 根据搜索参数过滤
      if (searchParams.type) {
        filteredLogs = filteredLogs.filter(log => log.type === searchParams.type);
      }

      if (searchParams.severity) {
        filteredLogs = filteredLogs.filter(log => log.severity === searchParams.severity);
      }

      if (searchParams.username) {
        filteredLogs = filteredLogs.filter(log =>
          log.username.toLowerCase().includes(searchParams.username!.toLowerCase())
        );
      }

      if (searchParams.userRole) {
        filteredLogs = filteredLogs.filter(log => log.userRole === searchParams.userRole);
      }

      if (searchParams.action) {
        filteredLogs = filteredLogs.filter(log =>
          log.action.toLowerCase().includes(searchParams.action!.toLowerCase())
        );
      }

      if (searchParams.startDate) {
        const startDate = new Date(searchParams.startDate);
        filteredLogs = filteredLogs.filter(log => new Date(log.timestamp) >= startDate);
      }

      if (searchParams.endDate) {
        const endDate = new Date(searchParams.endDate);
        endDate.setHours(23, 59, 59, 999); // 设置为当天的最后一毫秒
        filteredLogs = filteredLogs.filter(log => new Date(log.timestamp) <= endDate);
      }

      // 分页
      const page = searchParams.page || 1;
      const limit = searchParams.limit || 10;
      const startIndex = (page - 1) * limit;
      const endIndex = startIndex + limit;
      const paginatedLogs = filteredLogs.slice(startIndex, endIndex);

      setLogs(paginatedLogs);
      setTotalLogs(filteredLogs.length);
      setTotalPages(Math.ceil(filteredLogs.length / limit));
      setIsLoading(false);
    } catch (error) {
      console.error('获取日志失败:', error);
      toast({
        title: '获取日志失败',
        description: '发生错误，请稍后重试',
        variant: 'destructive',
      });
      setIsLoading(false);
    }
  };

  // 处理搜索参数变更
  const handleSearchParamChange = (key: keyof LogSearchParams, value: any) => {
    setSearchParams(prev => ({ ...prev, [key]: value, page: 1 }));
  };

  // 处理页码变更
  const handlePageChange = (page: number) => {
    setSearchParams(prev => ({ ...prev, page }));
  };

  // 查看日志详情
  const handleViewLogDetails = (log: Log) => {
    setSelectedLog(log);
    setIsLogDetailsOpen(true);
  };

  // 导出日志
  const handleExportLogs = () => {
    setIsExportDialogOpen(true);
  };

  // 重置搜索
  const resetSearch = () => {
    setSearchParams({
      page: 1,
      limit: 10
    });
  };

  // 获取日志图标
  const getLogIcon = (log: Log) => {
    if (log.type === 'login') {
      return <User className="h-4 w-4" />;
    } else if (log.type === 'action') {
      return <Shield className="h-4 w-4" />;
    } else {
      return <AlertTriangle className="h-4 w-4" />;
    }
  };

  // 获取日志严重性颜色
  const getSeverityColor = (severity: string) => {
    switch (severity) {
      case 'info':
        return 'bg-blue-100 text-blue-800';
      case 'warning':
        return 'bg-yellow-100 text-yellow-800';
      case 'error':
        return 'bg-red-100 text-red-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  return (
    <SuperAdminLayout>
      <div className="container mx-auto py-6">
        <h1 className="text-2xl font-bold mb-6">安全日志</h1>

        <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-4">
          <div className="flex justify-between items-center">
            <TabsList>
              <TabsTrigger value="all">
                <Eye className="mr-2 h-4 w-4" />
                所有日志
              </TabsTrigger>
              <TabsTrigger value="login">
                <User className="mr-2 h-4 w-4" />
                登录日志
              </TabsTrigger>
              <TabsTrigger value="action">
                <Shield className="mr-2 h-4 w-4" />
                操作日志
              </TabsTrigger>
              <TabsTrigger value="security">
                <AlertTriangle className="mr-2 h-4 w-4" />
                安全警报
              </TabsTrigger>
            </TabsList>

            <div className="flex space-x-2">
              <Button variant="outline" onClick={handleExportLogs}>
                <Download className="mr-2 h-4 w-4" />
                导出日志
              </Button>
            </div>
          </div>

          <Card>
            <CardHeader>
              <CardTitle>搜索日志</CardTitle>
              <CardDescription>
                通过用户名、操作类型或时间范围搜索日志
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="username">用户名</Label>
                  <Input
                    id="username"
                    placeholder="输入用户名"
                    value={searchParams.username || ''}
                    onChange={(e) => handleSearchParamChange('username', e.target.value)}
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="action">操作</Label>
                  <Input
                    id="action"
                    placeholder="输入操作类型"
                    value={searchParams.action || ''}
                    onChange={(e) => handleSearchParamChange('action', e.target.value)}
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="severity">严重程度</Label>
                  <Select
                    value={searchParams.severity || ''}
                    onValueChange={(value) => handleSearchParamChange('severity', value)}
                  >
                    <SelectTrigger id="severity">
                      <SelectValue placeholder="选择严重程度" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="">全部</SelectItem>
                      <SelectItem value="info">信息</SelectItem>
                      <SelectItem value="warning">警告</SelectItem>
                      <SelectItem value="error">错误</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="userRole">用户角色</Label>
                  <Select
                    value={searchParams.userRole || ''}
                    onValueChange={(value) => handleSearchParamChange('userRole', value)}
                  >
                    <SelectTrigger id="userRole">
                      <SelectValue placeholder="选择用户角色" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="">全部</SelectItem>
                      <SelectItem value="user">普通用户</SelectItem>
                      <SelectItem value="reviewer">审核员</SelectItem>
                      <SelectItem value="admin">管理员</SelectItem>
                      <SelectItem value="superadmin">超级管理员</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="startDate">开始日期</Label>
                  <Input
                    id="startDate"
                    type="date"
                    value={searchParams.startDate || ''}
                    onChange={(e) => handleSearchParamChange('startDate', e.target.value)}
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="endDate">结束日期</Label>
                  <Input
                    id="endDate"
                    type="date"
                    value={searchParams.endDate || ''}
                    onChange={(e) => handleSearchParamChange('endDate', e.target.value)}
                  />
                </div>
              </div>
            </CardContent>
            <CardFooter className="flex justify-between">
              <Button variant="outline" onClick={resetSearch}>
                <RefreshCw className="mr-2 h-4 w-4" />
                重置
              </Button>
              <Button onClick={() => fetchLogs()}>
                <Search className="mr-2 h-4 w-4" />
                搜索
              </Button>
            </CardFooter>
          </Card>

          <Card>
            <CardHeader>
              <div className="flex justify-between items-center">
                <div>
                  <CardTitle>日志列表</CardTitle>
                  <CardDescription>
                    共找到 {totalLogs} 条日志记录
                  </CardDescription>
                </div>
                <Button variant="outline" size="sm">
                  <Filter className="mr-2 h-4 w-4" />
                  高级筛选
                </Button>
              </div>
            </CardHeader>
            <CardContent>
              <div className="rounded-md border">
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>类型</TableHead>
                      <TableHead>严重程度</TableHead>
                      <TableHead>用户</TableHead>
                      <TableHead>操作</TableHead>
                      <TableHead>IP地址</TableHead>
                      <TableHead>时间</TableHead>
                      <TableHead className="text-right">详情</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {isLoading ? (
                      <TableRow>
                        <TableCell colSpan={7} className="text-center py-8">
                          <div className="flex justify-center items-center">
                            <RefreshCw className="h-6 w-6 animate-spin mr-2" />
                            <span>加载中...</span>
                          </div>
                        </TableCell>
                      </TableRow>
                    ) : logs.length === 0 ? (
                      <TableRow>
                        <TableCell colSpan={7} className="text-center py-8">
                          未找到匹配的日志记录
                        </TableCell>
                      </TableRow>
                    ) : (
                      logs.map((log) => (
                        <TableRow key={log.id}>
                          <TableCell>
                            <div className="flex items-center">
                              {log.type === 'login' ? (
                                <User className="h-4 w-4 mr-2 text-blue-500" />
                              ) : log.type === 'action' ? (
                                <Shield className="h-4 w-4 mr-2 text-green-500" />
                              ) : (
                                <AlertTriangle className="h-4 w-4 mr-2 text-red-500" />
                              )}
                              <span>
                                {log.type === 'login' ? '登录' :
                                 log.type === 'action' ? '操作' : '安全'}
                              </span>
                            </div>
                          </TableCell>
                          <TableCell>
                            <Badge className={`${
                              log.severity === 'info' ? 'bg-blue-100 text-blue-800' :
                              log.severity === 'warning' ? 'bg-yellow-100 text-yellow-800' :
                              'bg-red-100 text-red-800'
                            }`}>
                              {log.severity === 'info' ? '信息' :
                               log.severity === 'warning' ? '警告' : '错误'}
                            </Badge>
                          </TableCell>
                          <TableCell>
                            <div className="flex flex-col">
                              <span className="font-medium">{log.username}</span>
                              <span className="text-xs text-muted-foreground">
                                {log.userRole === 'superadmin' ? '超级管理员' :
                                 log.userRole === 'admin' ? '管理员' :
                                 log.userRole === 'reviewer' ? '审核员' : '普通用户'}
                              </span>
                            </div>
                          </TableCell>
                          <TableCell>
                            <div className="max-w-xs truncate" title={log.action}>
                              {log.action}
                            </div>
                          </TableCell>
                          <TableCell>
                            {log.ip || '-'}
                          </TableCell>
                          <TableCell>
                            {new Date(log.timestamp).toLocaleString()}
                          </TableCell>
                          <TableCell className="text-right">
                            <Button
                              variant="ghost"
                              size="sm"
                              onClick={() => handleViewLogDetails(log)}
                            >
                              <Eye className="h-4 w-4" />
                            </Button>
                          </TableCell>
                        </TableRow>
                      ))
                    )}
                  </TableBody>
                </Table>
              </div>

              {/* 分页 */}
              {totalPages > 1 && (
                <div className="mt-4 flex justify-center">
                  <Pagination>
                    <PaginationContent>
                      <PaginationItem>
                        <PaginationPrevious
                          onClick={() => handlePageChange(Math.max(1, searchParams.page! - 1))}
                          disabled={searchParams.page === 1}
                        />
                      </PaginationItem>

                      {Array.from({ length: totalPages }, (_, i) => i + 1).map((page) => (
                        <PaginationItem key={page}>
                          <PaginationLink
                            onClick={() => handlePageChange(page)}
                            isActive={page === searchParams.page}
                          >
                            {page}
                          </PaginationLink>
                        </PaginationItem>
                      ))}

                      <PaginationItem>
                        <PaginationNext
                          onClick={() => handlePageChange(Math.min(totalPages, searchParams.page! + 1))}
                          disabled={searchParams.page === totalPages}
                        />
                      </PaginationItem>
                    </PaginationContent>
                  </Pagination>
                </div>
              )}
            </CardContent>
          </Card>
        </Tabs>

        {/* 日志详情对话框 */}
        <Dialog open={isLogDetailsOpen} onOpenChange={setIsLogDetailsOpen}>
          <DialogContent>
            {selectedLog && (
              <>
                <DialogHeader>
                  <DialogTitle>日志详情</DialogTitle>
                  <DialogDescription>
                    查看日志的详细信息
                  </DialogDescription>
                </DialogHeader>

                <div className="py-4">
                  <div className="flex items-center mb-4">
                    {selectedLog.type === 'login' ? (
                      <User className="h-5 w-5 mr-2 text-blue-500" />
                    ) : selectedLog.type === 'action' ? (
                      <Shield className="h-5 w-5 mr-2 text-green-500" />
                    ) : (
                      <AlertTriangle className="h-5 w-5 mr-2 text-red-500" />
                    )}
                    <h3 className="text-lg font-medium">
                      {selectedLog.type === 'login' ? '登录日志' :
                       selectedLog.type === 'action' ? '操作日志' : '安全警报'}
                    </h3>
                    <Badge className={`ml-2 ${
                      selectedLog.severity === 'info' ? 'bg-blue-100 text-blue-800' :
                      selectedLog.severity === 'warning' ? 'bg-yellow-100 text-yellow-800' :
                      'bg-red-100 text-red-800'
                    }`}>
                      {selectedLog.severity === 'info' ? '信息' :
                       selectedLog.severity === 'warning' ? '警告' : '错误'}
                    </Badge>
                  </div>

                  <div className="space-y-3">
                    <div className="grid grid-cols-3 gap-2">
                      <span className="font-medium">操作:</span>
                      <span className="col-span-2">{selectedLog.action}</span>
                    </div>

                    <div className="grid grid-cols-3 gap-2">
                      <span className="font-medium">详情:</span>
                      <span className="col-span-2">{selectedLog.details}</span>
                    </div>

                    <div className="grid grid-cols-3 gap-2">
                      <span className="font-medium">用户:</span>
                      <span className="col-span-2">
                        {selectedLog.username}
                        <Badge className="ml-2" variant={
                          selectedLog.userRole === 'superadmin' ? 'destructive' :
                          selectedLog.userRole === 'admin' ? 'default' :
                          selectedLog.userRole === 'reviewer' ? 'secondary' : 'outline'
                        }>
                          {selectedLog.userRole === 'superadmin' ? '超级管理员' :
                           selectedLog.userRole === 'admin' ? '管理员' :
                           selectedLog.userRole === 'reviewer' ? '审核员' : '普通用户'}
                        </Badge>
                      </span>
                    </div>

                    <div className="grid grid-cols-3 gap-2">
                      <span className="font-medium">时间:</span>
                      <span className="col-span-2">{new Date(selectedLog.timestamp).toLocaleString()}</span>
                    </div>

                    <div className="grid grid-cols-3 gap-2">
                      <span className="font-medium">IP地址:</span>
                      <span className="col-span-2">{selectedLog.ip || '-'}</span>
                    </div>

                    {selectedLog.userAgent && (
                      <div className="grid grid-cols-3 gap-2">
                        <span className="font-medium">用户代理:</span>
                        <span className="col-span-2 text-xs break-all">{selectedLog.userAgent}</span>
                      </div>
                    )}

                    {selectedLog.relatedEntityId && (
                      <div className="grid grid-cols-3 gap-2">
                        <span className="font-medium">关联实体:</span>
                        <span className="col-span-2">
                          {selectedLog.relatedEntityType === 'user' ? '用户' :
                           selectedLog.relatedEntityType === 'story' ? '故事' :
                           selectedLog.relatedEntityType === 'questionnaire' ? '问卷' : '其他'}
                          {' ID: '}
                          <span className="font-mono text-xs">{selectedLog.relatedEntityId}</span>
                        </span>
                      </div>
                    )}
                  </div>
                </div>

                <DialogFooter>
                  <Button variant="outline" onClick={() => setIsLogDetailsOpen(false)}>
                    关闭
                  </Button>
                </DialogFooter>
              </>
            )}
          </DialogContent>
        </Dialog>

        {/* 导出日志对话框 */}
        <SecurityLogExportDialog
          open={isExportDialogOpen}
          onOpenChange={setIsExportDialogOpen}
        />
      </div>
    </SuperAdminLayout>
  );
};

export default SecurityLogsPage;
