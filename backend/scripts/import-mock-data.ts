/**
 * 导入模拟数据脚本
 * 
 * 将模拟数据导入到数据库中
 */

import { PrismaClient } from '@prisma/client';
import path from 'path';
import fs from 'fs';

// 初始化Prisma客户端
const prisma = new PrismaClient();

// 模拟数据路径
const MOCK_DATA_DIR = path.join(__dirname, '../../mock-data');

// 模拟数据文件
const USERS_FILE = path.join(MOCK_DATA_DIR, 'users.json');
const QUESTIONNAIRE_FILE = path.join(MOCK_DATA_DIR, 'questionnaire-responses.json');
const STORIES_FILE = path.join(MOCK_DATA_DIR, 'stories.json');
const PENDING_STORIES_FILE = path.join(MOCK_DATA_DIR, 'pending-stories.json');

/**
 * 读取模拟数据文件
 * @param filePath 文件路径
 * @returns 解析后的JSON数据
 */
function readMockDataFile(filePath: string): any {
  try {
    // 检查文件是否存在
    if (!fs.existsSync(filePath)) {
      console.warn(`Mock data file not found: ${filePath}`);
      return null;
    }

    // 读取文件内容
    const fileContent = fs.readFileSync(filePath, 'utf-8');
    return JSON.parse(fileContent);
  } catch (error) {
    console.error(`Error reading mock data file ${filePath}:`, error);
    return null;
  }
}

/**
 * 导入用户数据
 */
async function importUsers() {
  console.log('开始导入用户数据...');
  
  try {
    const data = readMockDataFile(USERS_FILE);
    if (!data || !data.users || !data.users.length) {
      console.warn('No user data found');
      return;
    }
    
    // 清空现有用户表
    await prisma.user.deleteMany();
    
    // 插入新用户数据
    for (const user of data.users) {
      await prisma.user.create({
        data: {
          id: user.id,
          email: user.email,
          isVerified: user.isVerified,
          isAdmin: user.isAdmin || false,
          createdAt: new Date(user.createdAt),
        },
      });
    }
    
    console.log(`成功导入 ${data.users.length} 条用户数据`);
  } catch (error) {
    console.error('导入用户数据失败:', error);
  }
}

/**
 * 导入问卷数据
 */
async function importQuestionnaires() {
  console.log('开始导入问卷数据...');
  
  try {
    const data = readMockDataFile(QUESTIONNAIRE_FILE);
    if (!data || !data.questionnaireResponses || !data.questionnaireResponses.length) {
      console.warn('No questionnaire data found');
      return;
    }
    
    // 清空现有问卷表
    await prisma.questionnaireResponse.deleteMany();
    
    // 插入新问卷数据
    for (const response of data.questionnaireResponses) {
      await prisma.questionnaireResponse.create({
        data: {
          id: response.id,
          userId: response.userId,
          isAnonymous: response.isAnonymous,
          educationLevel: response.educationLevel,
          major: response.major,
          graduationYear: response.graduationYear,
          region: response.region,
          employmentStatus: response.employmentStatus,
          currentIndustry: response.currentIndustry,
          currentPosition: response.currentPosition,
          monthlySalary: response.monthlySalary,
          expectedSalaryRange: response.expectedSalaryRange,
          jobSatisfaction: response.jobSatisfaction,
          unemploymentDuration: response.unemploymentDuration,
          careerChangeIntention: response.careerChangeIntention,
          adviceForStudents: response.adviceForStudents,
          observationOnEmployment: response.observationOnEmployment,
          createdAt: new Date(response.createdAt),
          ipAddress: response.ipAddress,
        },
      });
    }
    
    console.log(`成功导入 ${data.questionnaireResponses.length} 条问卷数据`);
  } catch (error) {
    console.error('导入问卷数据失败:', error);
  }
}

/**
 * 导入故事数据
 */
async function importStories() {
  console.log('开始导入故事数据...');
  
  try {
    // 读取已审核故事
    const storiesData = readMockDataFile(STORIES_FILE);
    if (!storiesData || !storiesData.stories || !storiesData.stories.length) {
      console.warn('No approved stories data found');
      return;
    }
    
    // 读取待审核故事
    const pendingStoriesData = readMockDataFile(PENDING_STORIES_FILE);
    if (!pendingStoriesData || !pendingStoriesData.pendingStories || !pendingStoriesData.pendingStories.length) {
      console.warn('No pending stories data found');
    }
    
    // 合并故事数据
    const allStories = [
      ...storiesData.stories,
      ...(pendingStoriesData?.pendingStories || []),
    ];
    
    // 清空现有故事表
    await prisma.story.deleteMany();
    await prisma.vote.deleteMany();
    
    // 插入故事数据
    for (const story of allStories) {
      // 构建内容，包含元数据
      let contentWithMetadata = story.content;
      
      // 添加标签
      if (story.tags && story.tags.length > 0) {
        contentWithMetadata += `\n\n标签: ${story.tags.join(', ')}`;
      }
      
      // 添加分类
      if (story.category) {
        contentWithMetadata += `\n\n分类: ${story.category}`;
      }
      
      // 添加学历
      if (story.educationLevel) {
        contentWithMetadata += `\n\n学历: ${story.educationLevel}`;
      }
      
      // 添加行业
      if (story.industry) {
        contentWithMetadata += `\n\n行业: ${story.industry}`;
      }
      
      // 创建故事
      await prisma.story.create({
        data: {
          id: story.id,
          userId: story.userId,
          isAnonymous: story.isAnonymous,
          title: story.title,
          content: contentWithMetadata,
          status: story.status,
          likes: story.likes,
          dislikes: story.dislikes,
          createdAt: new Date(story.createdAt),
          ipAddress: story.ipAddress,
        },
      });
    }
    
    console.log(`成功导入 ${allStories.length} 条故事数据`);
  } catch (error) {
    console.error('导入故事数据失败:', error);
  }
}

/**
 * 主函数：执行所有导入操作
 */
async function importAllData() {
  console.log('开始导入所有模拟数据...');
  
  try {
    await importUsers();
    await importQuestionnaires();
    await importStories();
    
    console.log('所有数据导入完成!');
  } catch (error) {
    console.error('导入过程中发生错误:', error);
  } finally {
    await prisma.$disconnect();
  }
}

// 执行导入
importAllData().catch(e => {
  console.error('导入失败:', e);
  process.exit(1);
});
