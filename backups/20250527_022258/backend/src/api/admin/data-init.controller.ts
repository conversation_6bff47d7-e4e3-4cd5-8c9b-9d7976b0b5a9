/**
 * 数据初始化控制器
 *
 * 提供初始化 KV 存储数据的 API
 */

import { Context } from 'hono';
import { DataInitService } from '../../services/dataInitService';

// Define environment interface
interface Env {
  SURVEY_KV: KVNamespace;
}

/**
 * 初始化所有数据
 */
export const initializeAllData = async (c: Context<{ Bindings: Env }>) => {
  try {
    console.log('Starting data initialization...');
    
    // 创建数据初始化服务
    const dataInitService = new DataInitService(c.env);
    
    // 初始化所有数据
    await dataInitService.initializeAllData();
    
    return c.json({
      success: true,
      message: 'All data initialized successfully',
    });
  } catch (error) {
    console.error('Error initializing data:', error);
    return c.json({
      success: false,
      error: 'Failed to initialize data',
    }, 500);
  }
};

/**
 * 初始化用户数据
 */
export const initializeUsers = async (c: Context<{ Bindings: Env }>) => {
  try {
    console.log('Starting user data initialization...');
    
    // 创建数据初始化服务
    const dataInitService = new DataInitService(c.env);
    
    // 初始化用户数据
    await dataInitService.initializeUsers();
    
    return c.json({
      success: true,
      message: 'User data initialized successfully',
    });
  } catch (error) {
    console.error('Error initializing user data:', error);
    return c.json({
      success: false,
      error: 'Failed to initialize user data',
    }, 500);
  }
};

/**
 * 初始化问卷数据
 */
export const initializeQuestionnaires = async (c: Context<{ Bindings: Env }>) => {
  try {
    console.log('Starting questionnaire data initialization...');
    
    // 创建数据初始化服务
    const dataInitService = new DataInitService(c.env);
    
    // 初始化问卷数据
    await dataInitService.initializeQuestionnaires();
    
    return c.json({
      success: true,
      message: 'Questionnaire data initialized successfully',
    });
  } catch (error) {
    console.error('Error initializing questionnaire data:', error);
    return c.json({
      success: false,
      error: 'Failed to initialize questionnaire data',
    }, 500);
  }
};

/**
 * 初始化故事数据
 */
export const initializeStories = async (c: Context<{ Bindings: Env }>) => {
  try {
    console.log('Starting story data initialization...');
    
    // 创建数据初始化服务
    const dataInitService = new DataInitService(c.env);
    
    // 初始化故事数据
    await dataInitService.initializeStories();
    
    return c.json({
      success: true,
      message: 'Story data initialized successfully',
    });
  } catch (error) {
    console.error('Error initializing story data:', error);
    return c.json({
      success: false,
      error: 'Failed to initialize story data',
    }, 500);
  }
};

/**
 * 初始化统计数据
 */
export const initializeStatistics = async (c: Context<{ Bindings: Env }>) => {
  try {
    console.log('Starting statistics data initialization...');
    
    // 创建数据初始化服务
    const dataInitService = new DataInitService(c.env);
    
    // 初始化统计数据
    await dataInitService.initializeStatistics();
    
    return c.json({
      success: true,
      message: 'Statistics data initialized successfully',
    });
  } catch (error) {
    console.error('Error initializing statistics data:', error);
    return c.json({
      success: false,
      error: 'Failed to initialize statistics data',
    }, 500);
  }
};
