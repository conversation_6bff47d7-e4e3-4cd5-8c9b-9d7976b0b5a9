/**
 * 增强版路由配置
 *
 * 实现更智能的代码分割和预加载策略
 */

import React, { lazy, Suspense, useEffect } from 'react';
import { Routes, Route, useLocation, useNavigate } from 'react-router-dom';
import { measureComponentRender } from '../utils/performance';

// 导入布局组件
import Layout from '../components/Layout';
import PermissionGuard from '../components/auth/PermissionGuard';
import RouteLoading from '../components/common/RouteLoading';

// 导入常用组件（不进行代码分割）
import NotFoundPage from '../pages/NotFoundPage';

// 路由分组配置
export const ROUTE_GROUPS = {
  // 公共路由组
  PUBLIC: [
    '/',
    '/questionnaire',
    '/visualization',
    '/story-wall',
    '/story'
  ],
  // 管理员路由组
  ADMIN: [
    '/admin/dashboard',
    '/admin/story-review',
    '/admin/content-review',
    '/admin/comment-review',
    '/admin/user-management',
    '/admin/reviewer-management',
    '/admin/settings'
  ],
  // 审核员路由组
  REVIEWER: [
    '/reviewer/dashboard',
    '/reviewer/story-review',
    '/reviewer/content-review',
    '/reviewer/quick-review',
    '/reviewer/settings'
  ],
  // 超级管理员路由组
  SUPERADMIN: [
    '/superadmin/dashboard',
    '/superadmin/user-management',
    '/superadmin/system-config',
    '/superadmin/security-logs',
    '/superadmin/settings',
    '/superadmin/system-monitor',
    '/superadmin/data-restore',
    '/superadmin/documentation',
    '/superadmin/security'
  ]
};

// 预加载优先级
export const PRELOAD_PRIORITY = {
  HIGH: 1,    // 立即预加载
  MEDIUM: 2,  // 空闲时预加载
  LOW: 3      // 用户悬停或其他触发时预加载
};

// 路由预加载配置
export const PRELOAD_CONFIG = {
  // 首页相关路由
  '/': {
    preloadRoutes: ['/questionnaire', '/story-wall'],
    priority: PRELOAD_PRIORITY.HIGH
  },
  // 问卷页面相关路由
  '/questionnaire': {
    preloadRoutes: ['/submit-success'],
    priority: PRELOAD_PRIORITY.MEDIUM
  },
  // 故事墙相关路由
  '/story-wall': {
    preloadRoutes: ['/story/:storyId'],
    priority: PRELOAD_PRIORITY.MEDIUM
  },
  // 管理员仪表盘相关路由
  '/admin/dashboard': {
    preloadRoutes: ['/admin/story-review', '/admin/content-review', '/admin/user-management'],
    priority: PRELOAD_PRIORITY.HIGH
  },
  // 审核员仪表盘相关路由
  '/reviewer/dashboard': {
    preloadRoutes: ['/reviewer/story-review', '/reviewer/content-review'],
    priority: PRELOAD_PRIORITY.HIGH
  },
  // 超级管理员仪表盘相关路由
  '/superadmin/dashboard': {
    preloadRoutes: ['/superadmin/user-management', '/superadmin/system-config'],
    priority: PRELOAD_PRIORITY.HIGH
  }
};

/**
 * 增强版懒加载组件
 * @param componentName 组件名称
 * @param factory 组件导入函数
 * @param options 额外选项
 */
export const enhancedLazyLoad = (
  componentName: string,
  factory: () => Promise<any>,
  options: {
    fallback?: React.ReactNode;
    errorFallback?: React.ReactNode;
    timeout?: number;
    onLoad?: () => void;
    onError?: (error: Error) => void;
    retry?: boolean;
    maxRetries?: number;
    preload?: boolean;
  } = {}
) => {
  const {
    fallback = <RouteLoading />,
    errorFallback = <div>加载失败，请刷新页面重试</div>,
    timeout = 10000,
    onLoad,
    onError,
    retry = true,
    maxRetries = 3,
    preload = false
  } = options;

  // 创建带有预加载功能的组件
  const LazyComponent = lazy(() => {
    let retryCount = 0;

    const loadComponent = () => {
      return Promise.race([
        measureComponentRender(`LazyLoad_${componentName}`, () => factory())
          .then(module => {
            if (onLoad) onLoad();
            return module;
          }),
        new Promise<never>((_, reject) => {
          setTimeout(() => reject(new Error(`加载超时: ${componentName}`)), timeout);
        })
      ]).catch(error => {
        if (retry && retryCount < maxRetries) {
          retryCount++;
          console.warn(`组件加载失败，正在重试 (${retryCount}/${maxRetries}): ${componentName}`);
          return new Promise(resolve => {
            setTimeout(() => {
              resolve(loadComponent());
            }, 1000 * retryCount); // 递增延迟
          });
        }

        if (onError) onError(error);
        console.error(`组件加载失败: ${componentName}`, error);

        // 返回一个解析为默认导出为错误组件的模块
        return {
          default: () => errorFallback
        };
      });
    };

    return loadComponent();
  });

  // 添加预加载方法的组件
  const Component: React.FC<any> & { preload: () => void } = (props) => (
    <Suspense fallback={fallback}>
      <LazyComponent {...props} />
    </Suspense>
  );

  // 预加载方法
  Component.preload = () => {
    factory().catch(console.error);
  };

  // 如果设置了预加载，立即预加载
  if (preload) {
    Component.preload();
  }

  return Component;
};

// 懒加载路由组件
const HomePage = enhancedLazyLoad('HomePage', () => import('../pages/HomePage'));
const QuestionnairePage = enhancedLazyLoad('QuestionnairePage', () => import('../pages/QuestionnairePage'));
const VisualizationPage = enhancedLazyLoad('VisualizationPage', () => import('../pages/VisualizationPage'));
const AdvancedAnalysisPage = enhancedLazyLoad('AdvancedAnalysisPage', () => import('../pages/AdvancedAnalysisPage'));
const StoryWallPage = enhancedLazyLoad('StoryWallPage', () => import('../pages/StoryWallPage'));
const StoryDetailPage = enhancedLazyLoad('StoryDetailPage', () => import('../pages/StoryDetailPage'));
const SubmitSuccessPage = enhancedLazyLoad('SubmitSuccessPage', () => import('../pages/SubmitSuccessPage'));
const MyContentPage = enhancedLazyLoad('MyContentPage', () => import('../pages/MyContentPage'));

// 管理员页面
const AdminLoginPage = enhancedLazyLoad('AdminLoginPage', () => import('../pages/admin/AdminLoginPage'));
const AdminDashboardHomePage = enhancedLazyLoad('AdminDashboardHomePage', () => import('../pages/admin/AdminDashboardHomePage'));
const StoryReviewPage = enhancedLazyLoad('StoryReviewPage', () => import('../pages/admin/StoryReviewPage'));
const ContentReviewPage = enhancedLazyLoad('ContentReviewPage', () => import('../pages/admin/ContentReviewPage'));
const CommentReviewPage = enhancedLazyLoad('CommentReviewPage', () => import('../pages/admin/CommentReviewPage'));
const QuickReviewPage = enhancedLazyLoad('QuickReviewPage', () => import('../pages/admin/QuickReviewPage'));
const UserManagementPage = enhancedLazyLoad('UserManagementPage', () => import('../pages/admin/UserManagementPage'));
const ReviewerManagementPage = enhancedLazyLoad('ReviewerManagementPage', () => import('../pages/admin/ReviewerManagementPage'));
const AdminSettingsPage = enhancedLazyLoad('AdminSettingsPage', () => import('../pages/admin/AdminSettingsPage'));

// 审核员页面
const ReviewerLoginPage = enhancedLazyLoad('ReviewerLoginPage', () => import('../pages/reviewer/ReviewerLoginPage'));
const ReviewerDashboardPage = enhancedLazyLoad('ReviewerDashboardPage', () => import('../pages/reviewer/ReviewerDashboardPage'));
const ReviewerStoryReviewPage = enhancedLazyLoad('ReviewerStoryReviewPage', () => import('../pages/reviewer/ReviewerStoryReviewPage'));
const ReviewerContentReviewPage = enhancedLazyLoad('ReviewerContentReviewPage', () => import('../pages/reviewer/ReviewerContentReviewPage'));
const ReviewerQuickReviewPage = enhancedLazyLoad('ReviewerQuickReviewPage', () => import('../pages/reviewer/ReviewerQuickReviewPage'));
const ReviewerSettingsPage = enhancedLazyLoad('ReviewerSettingsPage', () => import('../pages/reviewer/ReviewerSettingsPage'));

// 超级管理员页面
const SuperAdminDashboardPage = enhancedLazyLoad('SuperAdminDashboardPage', () => import('../pages/superadmin/SuperAdminDashboardPage'));
const SuperAdminUserManagementPage = enhancedLazyLoad('SuperAdminUserManagementPage', () => import('../pages/superadmin/UserManagement'));
const SystemConfigPage = enhancedLazyLoad('SystemConfigPage', () => import('../pages/superadmin/SystemConfigPage'));
const SecurityLogsPage = enhancedLazyLoad('SecurityLogsPage', () => import('../pages/superadmin/SecurityLogsPage'));
const RoleManagementPage = enhancedLazyLoad('RoleManagementPage', () => import('../pages/superadmin/RoleManagementPage'));
const TestDataManagementPage = enhancedLazyLoad('TestDataManagementPage', () => import('../pages/superadmin/TestDataManagementPage'));
const SuperAdminSettingsPage = enhancedLazyLoad('SuperAdminSettingsPage', () => import('../pages/superadmin/SuperAdminSettingsPage'));
const SystemMonitorPage = enhancedLazyLoad('SystemMonitorPage', () => import('../pages/superadmin/SystemMonitorPage'));
const DataRestorePage = enhancedLazyLoad('DataRestorePage', () => import('../pages/superadmin/DataRestorePage'));
const DocumentationPage = enhancedLazyLoad('DocumentationPage', () => import('../pages/superadmin/DocumentationPage'));
const SecurityPage = enhancedLazyLoad('SecurityPage', () => import('../pages/superadmin/SecurityPage'));

// 临时登录页面
const TempLoginPage = enhancedLazyLoad('TempLoginPage', () => import('../pages/TempLoginPage'));

// 路由组件映射表（用于预加载）
export const routeComponents: Record<string, any> = {
  '/': HomePage,
  '/questionnaire': QuestionnairePage,
  '/visualization': VisualizationPage,
  '/advanced-analysis': AdvancedAnalysisPage,
  '/story-wall': StoryWallPage,
  '/story/:storyId': StoryDetailPage,
  '/submit-success': SubmitSuccessPage,
  '/my-content': MyContentPage,
  '/admin': AdminLoginPage,
  '/admin/login': AdminLoginPage,
  '/temp-login': TempLoginPage,
  '/reviewer/login': ReviewerLoginPage,
  '/reviewer/dashboard': ReviewerDashboardPage,
  '/reviewer/story-review': ReviewerStoryReviewPage,
  '/reviewer/content-review': ReviewerContentReviewPage,
  '/reviewer/quick-review': ReviewerQuickReviewPage,
  '/reviewer/settings': ReviewerSettingsPage,
  '/admin/dashboard': AdminDashboardHomePage,
  '/admin/story-review': StoryReviewPage,
  '/admin/content-review': ContentReviewPage,
  '/admin/comment-review': CommentReviewPage,
  '/admin/quick-review': QuickReviewPage,
  '/admin/user-management': UserManagementPage,
  '/admin/reviewer-management': ReviewerManagementPage,
  '/admin/settings': AdminSettingsPage,
  '/superadmin/dashboard': SuperAdminDashboardPage,
  '/superadmin/user-management': SuperAdminUserManagementPage,
  '/superadmin/system-config': SystemConfigPage,
  '/superadmin/security-logs': SecurityLogsPage,
  '/superadmin/role-management': RoleManagementPage,
  '/superadmin/test-data-management': TestDataManagementPage,
  '/superadmin/settings': SuperAdminSettingsPage,
  '/superadmin/system-monitor': SystemMonitorPage,
  '/superadmin/data-restore': DataRestorePage,
  '/superadmin/documentation': DocumentationPage,
  '/superadmin/security': SecurityPage
};

/**
 * 预加载路由
 * @param path 路由路径
 */
export const preloadRoute = (path: string) => {
  const component = routeComponents[path];
  if (component && typeof component.preload === 'function') {
    console.log(`预加载路由: ${path}`);
    component.preload();
  }
};

/**
 * 获取当前用户角色组的路由
 * @param role 用户角色
 */
export const getRouteGroupByRole = (role: string): string[] => {
  switch (role) {
    case 'admin':
      return ROUTE_GROUPS.ADMIN;
    case 'reviewer':
      return ROUTE_GROUPS.REVIEWER;
    case 'superadmin':
      return ROUTE_GROUPS.SUPERADMIN;
    default:
      return ROUTE_GROUPS.PUBLIC;
  }
};

/**
 * 预加载用户角色相关路由
 * @param role 用户角色
 */
export const preloadRoleRoutes = (role: string) => {
  const routes = getRouteGroupByRole(role);

  // 使用requestIdleCallback在浏览器空闲时预加载
  if ('requestIdleCallback' in window) {
    window.requestIdleCallback(() => {
      routes.forEach(route => {
        preloadRoute(route);
      });
    });
  } else {
    // 降级为setTimeout
    setTimeout(() => {
      routes.forEach(route => {
        preloadRoute(route);
      });
    }, 2000);
  }
};

/**
 * 智能预加载组件
 * 根据当前路由和预加载配置，智能预加载相关路由
 */
export const SmartPreloader: React.FC = () => {
  const location = useLocation();
  const currentPath = location.pathname;

  useEffect(() => {
    // 获取当前路由的预加载配置
    const configEntry = Object.entries(PRELOAD_CONFIG).find(([path]) =>
      currentPath.startsWith(path)
    );

    if (!configEntry) return;

    const [_, config] = configEntry;

    // 根据优先级执行预加载
    if (config.priority === PRELOAD_PRIORITY.HIGH) {
      // 立即预加载
      config.preloadRoutes.forEach(route => {
        preloadRoute(route);
      });
    } else if (config.priority === PRELOAD_PRIORITY.MEDIUM) {
      // 使用requestIdleCallback在浏览器空闲时预加载
      if ('requestIdleCallback' in window) {
        window.requestIdleCallback(() => {
          config.preloadRoutes.forEach(route => {
            preloadRoute(route);
          });
        });
      } else {
        // 降级为setTimeout
        setTimeout(() => {
          config.preloadRoutes.forEach(route => {
            preloadRoute(route);
          });
        }, 2000);
      }
    }
    // LOW优先级预加载由用户交互触发，不在这里处理
  }, [currentPath]);

  return null;
};

// 增强版路由组件
const EnhancedRoutes: React.FC = () => {
  return (
    <Routes>
      {/* 主站路由 */}
      <Route path="/" element={<Layout />}>
        <Route index element={<HomePage />} />
        <Route path="questionnaire" element={<QuestionnairePage />} />
        <Route path="visualization" element={<VisualizationPage />} />
        <Route path="advanced-analysis" element={<AdvancedAnalysisPage />} />
        <Route path="story-wall" element={<StoryWallPage />} />
        <Route path="story/:storyId" element={<StoryDetailPage />} />
        <Route path="submit-success" element={<SubmitSuccessPage />} />
        <Route path="my-content" element={<MyContentPage />} />
        <Route path="*" element={<NotFoundPage />} />
      </Route>

      {/* 登录路由 */}
      <Route path="/admin/login" element={<AdminLoginPage />} />
      <Route path="/reviewer/login" element={<ReviewerLoginPage />} />
      <Route path="/temp-login" element={<TempLoginPage />} />

      {/* 管理员路由 */}
      <Route path="/admin/dashboard" element={
        <PermissionGuard allowedRoles={['admin', 'superadmin']}>
          <AdminDashboardHomePage />
        </PermissionGuard>
      } />
      <Route path="/admin/story-review" element={
        <PermissionGuard allowedRoles={['admin', 'superadmin']}>
          <StoryReviewPage />
        </PermissionGuard>
      } />
      <Route path="/admin/content-review" element={
        <PermissionGuard allowedRoles={['admin', 'superadmin']}>
          <ContentReviewPage />
        </PermissionGuard>
      } />
      <Route path="/admin/comment-review" element={
        <PermissionGuard allowedRoles={['admin', 'superadmin']}>
          <CommentReviewPage />
        </PermissionGuard>
      } />
      <Route path="/admin/quick-review" element={
        <PermissionGuard allowedRoles={['admin', 'superadmin']}>
          <QuickReviewPage />
        </PermissionGuard>
      } />
      <Route path="/admin/user-management" element={
        <PermissionGuard allowedRoles={['admin', 'superadmin']}>
          <UserManagementPage />
        </PermissionGuard>
      } />
      <Route path="/admin/reviewer-management" element={
        <PermissionGuard allowedRoles={['admin', 'superadmin']}>
          <ReviewerManagementPage />
        </PermissionGuard>
      } />
      <Route path="/admin/settings" element={
        <PermissionGuard allowedRoles={['admin', 'superadmin']}>
          <AdminSettingsPage />
        </PermissionGuard>
      } />

      {/* 审核员路由 */}
      <Route path="/reviewer/dashboard" element={
        <PermissionGuard allowedRoles={['reviewer', 'admin', 'superadmin']}>
          <ReviewerDashboardPage />
        </PermissionGuard>
      } />
      <Route path="/reviewer/story-review" element={
        <PermissionGuard allowedRoles={['reviewer', 'admin', 'superadmin']}>
          <ReviewerStoryReviewPage />
        </PermissionGuard>
      } />
      <Route path="/reviewer/content-review" element={
        <PermissionGuard allowedRoles={['reviewer', 'admin', 'superadmin']}>
          <ReviewerContentReviewPage />
        </PermissionGuard>
      } />
      <Route path="/reviewer/quick-review" element={
        <PermissionGuard allowedRoles={['reviewer', 'admin', 'superadmin']}>
          <ReviewerQuickReviewPage />
        </PermissionGuard>
      } />
      <Route path="/reviewer/settings" element={
        <PermissionGuard allowedRoles={['reviewer', 'admin', 'superadmin']}>
          <ReviewerSettingsPage />
        </PermissionGuard>
      } />

      {/* 超级管理员路由 */}
      <Route path="/superadmin/dashboard" element={
        <PermissionGuard allowedRoles={['superadmin']}>
          <SuperAdminDashboardPage />
        </PermissionGuard>
      } />
      <Route path="/superadmin/user-management" element={
        <PermissionGuard allowedRoles={['superadmin']}>
          <SuperAdminUserManagementPage />
        </PermissionGuard>
      } />
      <Route path="/superadmin/system-config" element={
        <PermissionGuard allowedRoles={['superadmin']}>
          <SystemConfigPage />
        </PermissionGuard>
      } />
      <Route path="/superadmin/security-logs" element={
        <PermissionGuard allowedRoles={['superadmin']}>
          <SecurityLogsPage />
        </PermissionGuard>
      } />
      <Route path="/superadmin/role-management" element={
        <PermissionGuard allowedRoles={['superadmin']}>
          <RoleManagementPage />
        </PermissionGuard>
      } />
      <Route path="/superadmin/test-data-management" element={
        <PermissionGuard allowedRoles={['superadmin']}>
          <TestDataManagementPage />
        </PermissionGuard>
      } />
      <Route path="/superadmin/settings" element={
        <PermissionGuard allowedRoles={['superadmin']}>
          <SuperAdminSettingsPage />
        </PermissionGuard>
      } />
      <Route path="/superadmin/system-monitor" element={
        <PermissionGuard allowedRoles={['superadmin']}>
          <SystemMonitorPage />
        </PermissionGuard>
      } />
      <Route path="/superadmin/data-restore" element={
        <PermissionGuard allowedRoles={['superadmin']}>
          <DataRestorePage />
        </PermissionGuard>
      } />
      <Route path="/superadmin/documentation" element={
        <PermissionGuard allowedRoles={['superadmin']}>
          <DocumentationPage />
        </PermissionGuard>
      } />
      <Route path="/superadmin/security" element={
        <PermissionGuard allowedRoles={['superadmin']}>
          <SecurityPage />
        </PermissionGuard>
      } />
    </Routes>
  );
};

export default EnhancedRoutes;
