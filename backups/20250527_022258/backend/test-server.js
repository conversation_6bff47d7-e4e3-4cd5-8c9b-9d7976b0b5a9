/**
 * 简单的测试服务器
 * 用于测试API功能，不依赖Cloudflare Workers
 */

const express = require('express');
const cors = require('cors');
const path = require('path');
const fs = require('fs');

const app = express();
const PORT = 8787;

// 中间件
app.use(cors({
  origin: [
    'http://localhost:5173',
    'http://localhost:5174',
    'http://localhost:5175',
    'http://localhost:5176',
    'http://localhost:5177'
  ],
  credentials: true
}));
app.use(express.json());

// 健康检查
app.get('/health', (req, res) => {
  res.json({ 
    status: 'ok', 
    environment: 'development',
    timestamp: new Date().toISOString()
  });
});

// 数据库健康检查
app.get('/api/health/database', (req, res) => {
  res.json({
    status: 'healthy',
    database: 'connected',
    counts: {
      users: 5,
      questionnaires: 10,
      stories: 8
    },
    timestamp: new Date().toISOString()
  });
});

// 文档API - 获取文档列表
app.get('/api/documentation/documents', (req, res) => {
  const mockDocuments = [
    {
      id: '1',
      title: '项目文档管理规则',
      description: '定义项目文档管理的规则、流程和标准',
      filePath: '/docs/documentation-management-rules.md',
      category: '项目概述',
      type: 'markdown',
      tags: ['文档', '规则', '流程'],
      lastUpdated: '2024-05-23',
      size: '12.8 KB',
      author: '项目团队',
      version: '1.0.0',
      status: 'published',
      accessLevel: 'internal',
      isLocal: true,
      createdBy: 'augment',
      syncStatus: 'synced',
      versions: [{
        version: '1.0.0',
        date: '2024-05-23',
        author: '项目团队',
        changes: '初始版本',
        filePath: '/docs/documentation-management-rules.md',
        size: '12.8 KB'
      }]
    },
    {
      id: '2',
      title: '系统架构概览',
      description: '系统整体架构设计和技术选型说明',
      filePath: '/docs/architecture/overview.md',
      category: '技术文档',
      type: 'markdown',
      tags: ['架构', '设计', '技术选型'],
      lastUpdated: '2024-01-10',
      size: '32.1 KB',
      author: '架构师',
      version: '1.5.0',
      status: 'published',
      accessLevel: 'internal',
      isLocal: true,
      createdBy: 'augment',
      syncStatus: 'synced',
      versions: [{
        version: '1.5.0',
        date: '2024-01-10',
        author: '架构师',
        changes: '更新微服务架构设计',
        filePath: '/docs/architecture/overview.md',
        size: '32.1 KB'
      }]
    },
    {
      id: '3',
      title: 'API 概览文档',
      description: 'REST API 接口的概览和基础说明',
      filePath: '/docs/api/overview.md',
      category: 'API文档',
      type: 'markdown',
      tags: ['API', '概览', 'REST'],
      lastUpdated: '2024-01-13',
      size: '22.8 KB',
      author: '后端团队',
      version: '2.1.0',
      status: 'published',
      accessLevel: 'internal',
      isLocal: true,
      createdBy: 'augment',
      syncStatus: 'synced',
      versions: [{
        version: '2.1.0',
        date: '2024-01-13',
        author: '后端团队',
        changes: '添加新的认证接口',
        filePath: '/docs/api/overview.md',
        size: '22.8 KB'
      }]
    }
  ];

  const syncStatus = {
    lastSync: new Date().toISOString().slice(0, 19).replace('T', ' '),
    status: 'success',
    totalDocs: mockDocuments.length,
    syncedDocs: mockDocuments.length,
    errorDocs: 0
  };

  res.json({
    success: true,
    data: mockDocuments,
    syncStatus,
    fallback: false
  });
});

// 文档API - 同步文档
app.post('/api/documentation/sync', (req, res) => {
  // 模拟同步过程
  setTimeout(() => {
    res.json({
      success: true,
      message: '文档同步完成',
      syncStatus: {
        lastSync: new Date().toISOString().slice(0, 19).replace('T', ' '),
        status: 'success',
        totalDocs: 3,
        syncedDocs: 3,
        errorDocs: 0
      },
      documentsCount: 3
    });
  }, 1000);
});

// 文档API - 获取单个文档
app.get('/api/documentation/documents/:id', (req, res) => {
  const { id } = req.params;
  
  // 模拟文档内容
  const mockDocument = {
    id,
    title: '项目文档管理规则',
    description: '定义项目文档管理的规则、流程和标准',
    filePath: '/docs/documentation-management-rules.md',
    category: '项目概述',
    type: 'markdown',
    tags: ['文档', '规则', '流程'],
    lastUpdated: '2024-05-23',
    size: '12.8 KB',
    author: '项目团队',
    version: '1.0.0',
    status: 'published',
    accessLevel: 'internal',
    isLocal: true,
    createdBy: 'augment',
    syncStatus: 'synced',
    content: '# 项目文档管理规则\n\n这是一个示例文档内容...',
    versions: [{
      version: '1.0.0',
      date: '2024-05-23',
      author: '项目团队',
      changes: '初始版本',
      filePath: '/docs/documentation-management-rules.md',
      size: '12.8 KB'
    }]
  };

  res.json({
    success: true,
    data: mockDocument
  });
});

// 404 处理
app.use('*', (req, res) => {
  res.status(404).json({ error: 'Not found' });
});

// 错误处理
app.use((err, req, res, next) => {
  console.error(`${req.method} ${req.url}`, err);
  res.status(500).json({ error: 'Internal Server Error' });
});

// 启动服务器
app.listen(PORT, () => {
  console.log(`🚀 测试服务器运行在 http://localhost:${PORT}`);
  console.log(`📚 文档API: http://localhost:${PORT}/api/documentation/documents`);
  console.log(`🏥 健康检查: http://localhost:${PORT}/health`);
});

module.exports = app;
