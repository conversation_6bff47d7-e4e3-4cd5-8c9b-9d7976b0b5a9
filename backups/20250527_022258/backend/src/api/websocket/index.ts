import { Hono } from 'hono';
import { Env } from '../../types';
import { WebSocketType } from '../../services/websocket.service';
import { adminAuthMiddleware } from '../../middlewares/auth.middleware';

// 创建WebSocket路由
const app = new Hono<{ Bindings: Env }>();

// 统计数据WebSocket连接
app.get('/statistics', adminAuthMiddleware, (c) => {
  // 获取WebSocket服务
  const webSocketService = c.get('webSocketService');

  // 处理WebSocket连接
  return webSocketService.handleConnection(c.req, WebSocketType.STATISTICS);
});

// 问卷回复WebSocket连接
app.get('/responses', adminAuthMiddleware, (c) => {
  // 获取WebSocket服务
  const webSocketService = c.get('webSocketService');

  // 处理WebSocket连接
  return webSocketService.handleConnection(c.req, WebSocketType.RESPONSES);
});

// 故事墙WebSocket连接
app.get('/story-wall', (c) => {
  // 获取WebSocket服务
  const webSocketService = c.get('webSocketService');

  // 处理WebSocket连接
  return webSocketService.handleConnection(c.req, WebSocketType.STORY_WALL);
});

export default app;
