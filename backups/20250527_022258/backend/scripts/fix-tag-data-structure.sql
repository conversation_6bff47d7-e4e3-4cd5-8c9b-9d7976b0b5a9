-- 标签数据结构修复脚本
-- 解决前端常量与数据库标签不匹配的问题

-- 1. 确保标签表结构正确
DROP TABLE IF EXISTS story_tags;
DROP TABLE IF EXISTS tags;

-- 2. 重新创建标签表 (使用TEXT主键以匹配前端常量)
CREATE TABLE IF NOT EXISTS tags (
  id TEXT PRIMARY KEY,                    -- 使用前端定义的ID
  name TEXT NOT NULL,                     -- 标签显示名称
  color TEXT DEFAULT 'blue',              -- 标签颜色
  priority INTEGER DEFAULT 0,             -- 优先级
  category TEXT DEFAULT 'other',          -- 分类
  parent_id TEXT,                         -- 父标签ID
  count INTEGER DEFAULT 0,                -- 使用次数统计
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  FOREIGN KEY (parent_id) REFERENCES tags(id)
);

-- 3. 创建故事标签关联表
CREATE TABLE IF NOT EXISTS story_tags (
  story_id INTEGER,
  tag_id TEXT,
  PRIMARY KEY (story_id, tag_id),
  FOREIGN KEY (story_id) REFERENCES stories(id) ON DELETE CASCADE,
  FOREIGN KEY (tag_id) REFERENCES tags(id) ON DELETE CASCADE
);

-- 4. 插入前端常量定义的标签数据
INSERT OR REPLACE INTO tags (id, name, color, priority, category, count) VALUES
-- 求职相关标签 (job, blue)
('job-hunting', '求职故事', 'blue', 10, 'job', 156),
('interview', '面试经验', 'blue', 9, 'job', 134),
('resume', '简历技巧', 'blue', 8, 'job', 43),
('job-search', '找工作', 'blue', 7, 'job', 89),
('salary', '薪资谈判', 'blue', 6, 'job', 76),
('offer', 'Offer选择', 'blue', 5, 'job', 32),

-- 学历相关标签 (education, green)
('bachelor', '本科经验', 'green', 10, 'education', 65),
('master', '硕士经验', 'green', 9, 'education', 38),
('phd', '博士经验', 'green', 8, 'education', 12),
('overseas-edu', '海外学历', 'green', 7, 'education', 18),
('continuing-edu', '继续教育', 'green', 6, 'education', 8),
('self-taught', '自学成才', 'green', 5, 'education', 15),

-- 行业相关标签 (industry, purple)
('it-industry', 'IT行业', 'purple', 10, 'industry', 54),
('finance', '金融行业', 'purple', 9, 'industry', 25),
('education-industry', '教育行业', 'purple', 8, 'industry', 16),
('healthcare', '医疗行业', 'purple', 7, 'industry', 14),
('manufacturing', '制造业', 'purple', 6, 'industry', 11),
('service', '服务业', 'purple', 5, 'industry', 13),

-- 经验相关标签 (experience, yellow)
('career-change', '转行经历', 'yellow', 10, 'experience', 87),
('work-life', '工作生活', 'yellow', 9, 'experience', 32),
('advice', '建议分享', 'yellow', 8, 'experience', 29),
('internship', '实习经历', 'yellow', 7, 'experience', 98),
('overseas', '海外就业', 'yellow', 6, 'experience', 22),
('startup', '创业经历', 'yellow', 5, 'experience', 18),
('remote-work', '远程工作', 'yellow', 4, 'experience', 15),
('freelance', '自由职业', 'yellow', 3, 'experience', 8),

-- 其他标签 (other, gray)
('success', '成功故事', 'gray', 10, 'other', 12),
('challenge', '挑战经历', 'gray', 9, 'other', 10),
('inspiration', '励志故事', 'gray', 8, 'other', 9);

-- 5. 更新标签统计计数
UPDATE tags SET updated_at = CURRENT_TIMESTAMP;

-- 6. 为现有故事添加标签关联 (基于内容关键词匹配)
-- 这里需要根据实际故事内容来匹配标签

-- 示例：为包含"求职"关键词的故事添加求职标签
INSERT OR IGNORE INTO story_tags (story_id, tag_id)
SELECT s.id, 'job-hunting'
FROM stories s
WHERE (s.title LIKE '%求职%' OR s.content LIKE '%求职%')
  AND s.status = 'approved';

-- 示例：为包含"面试"关键词的故事添加面试标签
INSERT OR IGNORE INTO story_tags (story_id, tag_id)
SELECT s.id, 'interview'
FROM stories s
WHERE (s.title LIKE '%面试%' OR s.content LIKE '%面试%')
  AND s.status = 'approved';

-- 示例：为包含"实习"关键词的故事添加实习标签
INSERT OR IGNORE INTO story_tags (story_id, tag_id)
SELECT s.id, 'internship'
FROM stories s
WHERE (s.title LIKE '%实习%' OR s.content LIKE '%实习%')
  AND s.status = 'approved';

-- 示例：为包含"转行"关键词的故事添加转行标签
INSERT OR IGNORE INTO story_tags (story_id, tag_id)
SELECT s.id, 'career-change'
FROM stories s
WHERE (s.title LIKE '%转行%' OR s.content LIKE '%转行%')
  AND s.status = 'approved';

-- 示例：为包含"IT"或"程序"关键词的故事添加IT行业标签
INSERT OR IGNORE INTO story_tags (story_id, tag_id)
SELECT s.id, 'it-industry'
FROM stories s
WHERE (s.title LIKE '%IT%' OR s.content LIKE '%IT%' 
       OR s.title LIKE '%程序%' OR s.content LIKE '%程序%'
       OR s.title LIKE '%开发%' OR s.content LIKE '%开发%')
  AND s.status = 'approved';

-- 7. 重新计算标签使用统计
UPDATE tags SET count = (
  SELECT COUNT(*)
  FROM story_tags st
  WHERE st.tag_id = tags.id
);

-- 8. 创建索引以提高查询性能
CREATE INDEX IF NOT EXISTS idx_story_tags_story_id ON story_tags(story_id);
CREATE INDEX IF NOT EXISTS idx_story_tags_tag_id ON story_tags(tag_id);
CREATE INDEX IF NOT EXISTS idx_tags_category ON tags(category);
CREATE INDEX IF NOT EXISTS idx_tags_count ON tags(count DESC);

-- 9. 验证数据
SELECT 
  t.id,
  t.name,
  t.category,
  t.count,
  COUNT(st.story_id) as actual_count
FROM tags t
LEFT JOIN story_tags st ON t.id = st.tag_id
GROUP BY t.id, t.name, t.category, t.count
ORDER BY t.category, t.count DESC;
