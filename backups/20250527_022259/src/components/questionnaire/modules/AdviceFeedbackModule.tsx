import { useEffect } from 'react';
import { z } from 'zod';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { 
  Form,
  FormControl,
  FormField,
  FormItem,
  FormMessage 
} from '@/components/ui/form';
import { Textarea } from '@/components/ui/textarea';
import QuestionItem from '../QuestionItem';
import { QuestionnaireData } from '@/lib/api';

// Define the form schema
const formSchema = z.object({
  adviceForStudents: z.string().min(10, { message: '请至少输入10个字的建议' }).max(500, { message: '字数不能超过500' }),
  observationOnEmployment: z.string().min(10, { message: '请至少输入10个字的观察' }).max(500, { message: '字数不能超过500' }),
});

interface AdviceFeedbackModuleProps {
  data: Partial<QuestionnaireData>;
  onUpdate: (data: Partial<QuestionnaireData>) => void;
}

export default function AdviceFeedbackModule({ 
  data, 
  onUpdate 
}: AdviceFeedbackModuleProps) {
  // Set up form with default values from data
  const form = useForm<z.infer<typeof formSchema>>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      adviceForStudents: data.adviceForStudents || '',
      observationOnEmployment: data.observationOnEmployment || '',
    },
  });
  
  // Update parent component with form values
  useEffect(() => {
    const subscription = form.watch((value) => {
      if (value.adviceForStudents && value.observationOnEmployment) {
        onUpdate({
          adviceForStudents: value.adviceForStudents,
          observationOnEmployment: value.observationOnEmployment,
        });
      }
    });
    
    return () => subscription.unsubscribe();
  }, [form, onUpdate]);
  
  return (
    <div>
      <div className="mb-6 p-4 bg-blue-50 rounded-lg border border-blue-100">
        <h3 className="text-lg font-medium text-blue-800 mb-2">您的建议与观察将帮助更多人</h3>
        <p className="text-blue-700">
          您的真实想法和建议对于高三学子和求职者非常宝贵。这些内容将在"故事墙"中匿名展示，
          帮助更多人了解就业现状和挑战。请分享您的真实经历和感受。
        </p>
      </div>
      
      <Form {...form}>
        <form className="space-y-6">
          <QuestionItem
            number={1}
            title="给高三学子的建议"
            description="如果您能回到高三，您会给自己什么建议？对专业选择、学习方向有什么忠告？"
            required
          >
            <FormField
              control={form.control}
              name="adviceForStudents"
              render={({ field }) => (
                <FormItem>
                  <FormControl>
                    <Textarea
                      placeholder="请输入您给高三学子的建议..."
                      className="min-h-[150px] resize-none"
                      {...field}
                    />
                  </FormControl>
                  <FormMessage />
                  <div className="text-xs text-gray-500 mt-1 flex justify-between">
                    <span>{field.value?.length || 0}/500</span>
                    <span>至少10个字</span>
                  </div>
                </FormItem>
              )}
            />
          </QuestionItem>
          
          <QuestionItem
            number={2}
            title="对当前就业环境的观察"
            description="您认为当前就业市场存在哪些问题和挑战？有什么可能的解决方案？"
            required
          >
            <FormField
              control={form.control}
              name="observationOnEmployment"
              render={({ field }) => (
                <FormItem>
                  <FormControl>
                    <Textarea
                      placeholder="请分享您对就业环境的观察..."
                      className="min-h-[150px] resize-none"
                      {...field}
                    />
                  </FormControl>
                  <FormMessage />
                  <div className="text-xs text-gray-500 mt-1 flex justify-between">
                    <span>{field.value?.length || 0}/500</span>
                    <span>至少10个字</span>
                  </div>
                </FormItem>
              )}
            />
          </QuestionItem>
        </form>
      </Form>
      
      <div className="mt-8 p-4 bg-gray-50 rounded-lg border border-gray-200">
        <h3 className="text-lg font-medium mb-2">提示</h3>
        <ul className="list-disc pl-5 space-y-1 text-gray-700">
          <li>分享您的真实经历和感受，这对他人非常有价值</li>
          <li>具体的例子和建议比泛泛而谈更有帮助</li>
          <li>您的回答将在"故事墙"中匿名展示，帮助更多人</li>
          <li>请避免使用敏感词汇和过于负面的表达</li>
        </ul>
      </div>
    </div>
  );
}
