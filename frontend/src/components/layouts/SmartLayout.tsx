import React from 'react';
import { useAuth } from '@/contexts/AuthContext';
import AdminLayout from './AdminLayout';
import SuperAdminLayout from './SuperAdminLayout';
import ReviewerLayout from './ReviewerLayout';

interface SmartLayoutProps {
  children: React.ReactNode;
  allowedRoles?: ('admin' | 'superadmin' | 'reviewer')[];
  preferredLayout?: 'admin' | 'superadmin' | 'reviewer';
}

/**
 * 智能布局选择器
 * 
 * 根据用户角色自动选择合适的布局组件
 * 防止权限跳跃和组件串动问题
 */
const SmartLayout: React.FC<SmartLayoutProps> = ({ 
  children, 
  allowedRoles = ['admin', 'superadmin', 'reviewer'],
  preferredLayout 
}) => {
  const { user } = useAuth();

  // 获取用户角色
  const userRole = user?.role;

  // 验证用户是否有权限访问
  if (!userRole || !allowedRoles.includes(userRole as any)) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-center">
          <h2 className="text-2xl font-bold text-red-600 mb-4">访问被拒绝</h2>
          <p className="text-gray-600">您没有权限访问此页面</p>
          <p className="text-sm text-gray-500 mt-2">
            需要角色: {allowedRoles.join(', ')} | 当前角色: {userRole || '未知'}
          </p>
        </div>
      </div>
    );
  }

  // 根据优先级选择布局
  const getLayoutComponent = () => {
    // 如果指定了首选布局，且用户有权限，则使用首选布局
    if (preferredLayout && allowedRoles.includes(preferredLayout)) {
      switch (preferredLayout) {
        case 'superadmin':
          return SuperAdminLayout;
        case 'admin':
          return AdminLayout;
        case 'reviewer':
          return ReviewerLayout;
      }
    }

    // 否则根据用户角色选择最合适的布局
    switch (userRole) {
      case 'superadmin':
        return SuperAdminLayout;
      case 'admin':
        return AdminLayout;
      case 'reviewer':
        return ReviewerLayout;
      default:
        // 降级到管理员布局
        return AdminLayout;
    }
  };

  const LayoutComponent = getLayoutComponent();

  return (
    <LayoutComponent>
      {children}
    </LayoutComponent>
  );
};

export default SmartLayout;
