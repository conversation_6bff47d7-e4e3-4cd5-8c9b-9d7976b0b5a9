import React from 'react';
import { Button } from '@/components/ui/button';
import { ChevronLeft, ChevronRight, ChevronsLeft, ChevronsRight } from 'lucide-react';

interface PaginationProps {
  currentPage: number;
  totalPages: number;
  onPageChange: (page: number) => void;
  className?: string;
}

export function Pagination({ currentPage, totalPages, onPageChange, className = '' }: PaginationProps) {
  // 如果只有一页，不显示分页
  if (totalPages <= 1) {
    return null;
  }

  // 计算要显示的页码
  const getPageNumbers = () => {
    const pageNumbers = [];
    const maxPagesToShow = 5; // 最多显示5个页码
    
    if (totalPages <= maxPagesToShow) {
      // 如果总页数小于等于最大显示数，显示所有页码
      for (let i = 1; i <= totalPages; i++) {
        pageNumbers.push(i);
      }
    } else {
      // 否则，显示当前页附近的页码
      let startPage = Math.max(1, currentPage - 2);
      let endPage = Math.min(totalPages, startPage + maxPagesToShow - 1);
      
      // 调整起始页，确保显示maxPagesToShow个页码
      if (endPage - startPage + 1 < maxPagesToShow) {
        startPage = Math.max(1, endPage - maxPagesToShow + 1);
      }
      
      for (let i = startPage; i <= endPage; i++) {
        pageNumbers.push(i);
      }
      
      // 添加省略号
      if (startPage > 1) {
        pageNumbers.unshift(-1); // -1 表示左侧省略号
        pageNumbers.unshift(1); // 始终显示第一页
      }
      
      if (endPage < totalPages) {
        pageNumbers.push(-2); // -2 表示右侧省略号
        pageNumbers.push(totalPages); // 始终显示最后一页
      }
    }
    
    return pageNumbers;
  };

  const pageNumbers = getPageNumbers();

  return (
    <div className={`flex items-center justify-center space-x-1 ${className}`}>
      {/* 首页按钮 */}
      <Button
        variant="outline"
        size="icon"
        onClick={() => onPageChange(1)}
        disabled={currentPage === 1}
        aria-label="首页"
      >
        <ChevronsLeft className="h-4 w-4" />
      </Button>
      
      {/* 上一页按钮 */}
      <Button
        variant="outline"
        size="icon"
        onClick={() => onPageChange(currentPage - 1)}
        disabled={currentPage === 1}
        aria-label="上一页"
      >
        <ChevronLeft className="h-4 w-4" />
      </Button>
      
      {/* 页码按钮 */}
      {pageNumbers.map((pageNumber, index) => {
        if (pageNumber === -1) {
          // 左侧省略号
          return (
            <Button
              key={`ellipsis-left-${index}`}
              variant="outline"
              size="icon"
              disabled
              aria-hidden="true"
            >
              ...
            </Button>
          );
        }
        
        if (pageNumber === -2) {
          // 右侧省略号
          return (
            <Button
              key={`ellipsis-right-${index}`}
              variant="outline"
              size="icon"
              disabled
              aria-hidden="true"
            >
              ...
            </Button>
          );
        }
        
        return (
          <Button
            key={pageNumber}
            variant={pageNumber === currentPage ? "default" : "outline"}
            size="icon"
            onClick={() => onPageChange(pageNumber)}
            aria-label={`第 ${pageNumber} 页`}
            aria-current={pageNumber === currentPage ? "page" : undefined}
          >
            {pageNumber}
          </Button>
        );
      })}
      
      {/* 下一页按钮 */}
      <Button
        variant="outline"
        size="icon"
        onClick={() => onPageChange(currentPage + 1)}
        disabled={currentPage === totalPages}
        aria-label="下一页"
      >
        <ChevronRight className="h-4 w-4" />
      </Button>
      
      {/* 末页按钮 */}
      <Button
        variant="outline"
        size="icon"
        onClick={() => onPageChange(totalPages)}
        disabled={currentPage === totalPages}
        aria-label="末页"
      >
        <ChevronsRight className="h-4 w-4" />
      </Button>
    </div>
  );
}
