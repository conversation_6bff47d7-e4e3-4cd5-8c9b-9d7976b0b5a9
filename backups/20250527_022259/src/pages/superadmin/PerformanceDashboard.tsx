/**
 * 性能监控仪表盘
 * 
 * 提供全面的前端性能监控和分析功能
 */

import React, { useState, useEffect } from 'react';
import {
  Box,
  Typography,
  Paper,
  Grid,
  Card,
  CardContent,
  CardHeader,
  Divider,
  Tabs,
  Tab,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  CircularProgress,
  Chip,
  Button,
  IconButton,
  Tooltip,
  Alert,
  Select,
  MenuItem,
  FormControl,
  InputLabel,
  TextField
} from '@mui/material';
import {
  Refresh as RefreshIcon,
  Download as DownloadIcon,
  Warning as WarningIcon,
  Error as ErrorIcon,
  CheckCircle as CheckCircleIcon,
  Info as InfoIcon,
  Timeline as TimelineIcon,
  Speed as SpeedIcon,
  Memory as MemoryIcon,
  Code as CodeIcon,
  Api as ApiIcon,
  TouchApp as TouchAppIcon,
  Storage as StorageIcon
} from '@mui/icons-material';
import { 
  LineChart, 
  Line, 
  BarChart, 
  Bar, 
  PieChart, 
  Pie, 
  Cell, 
  XAxis, 
  <PERSON><PERSON><PERSON><PERSON>, 
  CartesianGrid, 
  <PERSON><PERSON><PERSON> as Recha<PERSON><PERSON><PERSON><PERSON>, 
  <PERSON>, 
  ResponsiveContainer 
} from 'recharts';

// 导入性能监控服务
import PerformanceMonitoringService, { PerformanceData } from '../../services/monitoring/PerformanceMonitoringService';

// 定义标签页接口
interface TabPanelProps {
  children?: React.ReactNode;
  index: number;
  value: number;
}

// 标签页组件
function TabPanel(props: TabPanelProps) {
  const { children, value, index, ...other } = props;

  return (
    <div
      role="tabpanel"
      hidden={value !== index}
      id={`performance-tabpanel-${index}`}
      aria-labelledby={`performance-tab-${index}`}
      {...other}
    >
      {value === index && (
        <Box sx={{ p: 3 }}>
          {children}
        </Box>
      )}
    </div>
  );
}

// 获取标签属性
function a11yProps(index: number) {
  return {
    id: `performance-tab-${index}`,
    'aria-controls': `performance-tabpanel-${index}`,
  };
}

// 性能指标评分
function getPerformanceScore(data: PerformanceData): number {
  if (!data.webVitals) return 0;
  
  // 基于Web Vitals计算性能评分
  let score = 100;
  
  // LCP (Largest Contentful Paint) - 应小于2.5秒
  if (data.webVitals.LCP) {
    if (data.webVitals.LCP > 4000) {
      score -= 25;
    } else if (data.webVitals.LCP > 2500) {
      score -= 15;
    }
  }
  
  // FID (First Input Delay) - 应小于100毫秒
  if (data.webVitals.FID) {
    if (data.webVitals.FID > 300) {
      score -= 25;
    } else if (data.webVitals.FID > 100) {
      score -= 15;
    }
  }
  
  // CLS (Cumulative Layout Shift) - 应小于0.1
  if (data.webVitals.CLS) {
    if (data.webVitals.CLS > 0.25) {
      score -= 25;
    } else if (data.webVitals.CLS > 0.1) {
      score -= 15;
    }
  }
  
  // 基于JavaScript错误数量
  if (data.javascript.errors > 10) {
    score -= 15;
  } else if (data.javascript.errors > 0) {
    score -= 5;
  }
  
  // 基于长任务数量
  if (data.javascript.longTasks.length > 20) {
    score -= 10;
  } else if (data.javascript.longTasks.length > 5) {
    score -= 5;
  }
  
  return Math.max(0, score);
}

// 获取性能评分等级
function getPerformanceGrade(score: number): { grade: string; color: string } {
  if (score >= 90) {
    return { grade: 'A', color: '#4caf50' };
  } else if (score >= 80) {
    return { grade: 'B', color: '#8bc34a' };
  } else if (score >= 70) {
    return { grade: 'C', color: '#ffeb3b' };
  } else if (score >= 60) {
    return { grade: 'D', color: '#ff9800' };
  } else {
    return { grade: 'F', color: '#f44336' };
  }
}

// 格式化文件大小
function formatFileSize(bytes: number): string {
  if (bytes === 0) return '0 B';
  
  const k = 1024;
  const sizes = ['B', 'KB', 'MB', 'GB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}

// 格式化时间
function formatTime(ms: number): string {
  if (ms < 1) return `${(ms * 1000).toFixed(2)} μs`;
  if (ms < 1000) return `${ms.toFixed(2)} ms`;
  return `${(ms / 1000).toFixed(2)} s`;
}

// 性能仪表盘组件
const PerformanceDashboard: React.FC = () => {
  const [tabValue, setTabValue] = useState(0);
  const [performanceData, setPerformanceData] = useState<PerformanceData | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [refreshInterval, setRefreshInterval] = useState<number | null>(null);
  const [timeRange, setTimeRange] = useState('1h');
  
  // 加载性能数据
  const loadPerformanceData = () => {
    try {
      setLoading(true);
      setError(null);
      
      // 获取性能数据
      const data = PerformanceMonitoringService.getData();
      setPerformanceData(data);
      
      setLoading(false);
    } catch (err) {
      setError('加载性能数据失败');
      setLoading(false);
      console.error('加载性能数据失败:', err);
    }
  };
  
  // 初始加载
  useEffect(() => {
    loadPerformanceData();
  }, []);
  
  // 处理刷新间隔变化
  useEffect(() => {
    if (refreshInterval) {
      const interval = setInterval(() => {
        loadPerformanceData();
      }, refreshInterval * 1000);
      
      return () => clearInterval(interval);
    }
  }, [refreshInterval]);
  
  // 处理标签页变化
  const handleTabChange = (event: React.SyntheticEvent, newValue: number) => {
    setTabValue(newValue);
  };
  
  // 处理刷新
  const handleRefresh = () => {
    loadPerformanceData();
  };
  
  // 处理下载
  const handleDownload = () => {
    if (!performanceData) return;
    
    const dataStr = JSON.stringify(performanceData, null, 2);
    const dataUri = `data:application/json;charset=utf-8,${encodeURIComponent(dataStr)}`;
    
    const linkElement = document.createElement('a');
    linkElement.setAttribute('href', dataUri);
    linkElement.setAttribute('download', `performance-data-${new Date().toISOString()}.json`);
    document.body.appendChild(linkElement);
    linkElement.click();
    document.body.removeChild(linkElement);
  };
  
  // 处理刷新间隔变化
  const handleRefreshIntervalChange = (event: any) => {
    setRefreshInterval(event.target.value);
  };
  
  // 处理时间范围变化
  const handleTimeRangeChange = (event: any) => {
    setTimeRange(event.target.value);
  };
  
  // 计算性能评分
  const performanceScore = performanceData ? getPerformanceScore(performanceData) : 0;
  const { grade, color } = getPerformanceGrade(performanceScore);
  
  // 渲染加载中状态
  if (loading && !performanceData) {
    return (
      <Box display="flex" justifyContent="center" alignItems="center" height="100vh">
        <CircularProgress />
      </Box>
    );
  }
  
  // 渲染错误状态
  if (error && !performanceData) {
    return (
      <Box p={3}>
        <Alert severity="error">{error}</Alert>
      </Box>
    );
  }
  
  return (
    <Box p={3}>
      <Box display="flex" justifyContent="space-between" alignItems="center" mb={3}>
        <Typography variant="h4" component="h1">
          性能监控仪表盘
        </Typography>
        
        <Box display="flex" alignItems="center">
          <FormControl variant="outlined" size="small" sx={{ mr: 2, minWidth: 120 }}>
            <InputLabel id="refresh-interval-label">刷新间隔</InputLabel>
            <Select
              labelId="refresh-interval-label"
              value={refreshInterval || ''}
              onChange={handleRefreshIntervalChange}
              label="刷新间隔"
            >
              <MenuItem value="">手动刷新</MenuItem>
              <MenuItem value={5}>5秒</MenuItem>
              <MenuItem value={10}>10秒</MenuItem>
              <MenuItem value={30}>30秒</MenuItem>
              <MenuItem value={60}>1分钟</MenuItem>
            </Select>
          </FormControl>
          
          <FormControl variant="outlined" size="small" sx={{ mr: 2, minWidth: 120 }}>
            <InputLabel id="time-range-label">时间范围</InputLabel>
            <Select
              labelId="time-range-label"
              value={timeRange}
              onChange={handleTimeRangeChange}
              label="时间范围"
            >
              <MenuItem value="1h">1小时</MenuItem>
              <MenuItem value="6h">6小时</MenuItem>
              <MenuItem value="24h">24小时</MenuItem>
              <MenuItem value="7d">7天</MenuItem>
              <MenuItem value="30d">30天</MenuItem>
            </Select>
          </FormControl>
          
          <Tooltip title="刷新数据">
            <IconButton onClick={handleRefresh}>
              <RefreshIcon />
            </IconButton>
          </Tooltip>
          
          <Tooltip title="下载数据">
            <IconButton onClick={handleDownload} disabled={!performanceData}>
              <DownloadIcon />
            </IconButton>
          </Tooltip>
        </Box>
      </Box>
      
      {performanceData && (
        <>
          {/* 性能概览卡片 */}
          <Grid container spacing={3} mb={3}>
            {/* 性能评分 */}
            <Grid item xs={12} sm={6} md={3}>
              <Card>
                <CardContent>
                  <Typography variant="h6" gutterBottom>
                    性能评分
                  </Typography>
                  <Box display="flex" alignItems="center">
                    <Box
                      sx={{
                        width: 60,
                        height: 60,
                        borderRadius: '50%',
                        bgcolor: color,
                        display: 'flex',
                        justifyContent: 'center',
                        alignItems: 'center',
                        mr: 2
                      }}
                    >
                      <Typography variant="h4" color="white">
                        {grade}
                      </Typography>
                    </Box>
                    <Typography variant="h4">
                      {performanceScore}
                    </Typography>
                  </Box>
                </CardContent>
              </Card>
            </Grid>
            
            {/* Web Vitals */}
            <Grid item xs={12} sm={6} md={3}>
              <Card>
                <CardContent>
                  <Typography variant="h6" gutterBottom>
                    Web Vitals
                  </Typography>
                  <Box>
                    <Typography variant="body2">
                      LCP: {performanceData.webVitals.LCP ? formatTime(performanceData.webVitals.LCP) : 'N/A'}
                    </Typography>
                    <Typography variant="body2">
                      FID: {performanceData.webVitals.FID ? formatTime(performanceData.webVitals.FID) : 'N/A'}
                    </Typography>
                    <Typography variant="body2">
                      CLS: {performanceData.webVitals.CLS?.toFixed(3) || 'N/A'}
                    </Typography>
                  </Box>
                </CardContent>
              </Card>
            </Grid>
            
            {/* 资源统计 */}
            <Grid item xs={12} sm={6} md={3}>
              <Card>
                <CardContent>
                  <Typography variant="h6" gutterBottom>
                    资源统计
                  </Typography>
                  <Box>
                    <Typography variant="body2">
                      总数: {performanceData.resources.totalCount}
                    </Typography>
                    <Typography variant="body2">
                      总大小: {formatFileSize(performanceData.resources.totalSize)}
                    </Typography>
                    <Typography variant="body2">
                      慢资源: {performanceData.resources.slowest.length}
                    </Typography>
                  </Box>
                </CardContent>
              </Card>
            </Grid>
            
            {/* JavaScript统计 */}
            <Grid item xs={12} sm={6} md={3}>
              <Card>
                <CardContent>
                  <Typography variant="h6" gutterBottom>
                    JavaScript统计
                  </Typography>
                  <Box>
                    <Typography variant="body2">
                      错误数: {performanceData.javascript.errors}
                    </Typography>
                    <Typography variant="body2">
                      长任务: {performanceData.javascript.longTasks.length}
                    </Typography>
                    <Typography variant="body2">
                      内存使用: {performanceData.javascript.memoryUsage?.usedJSHeapSize 
                        ? formatFileSize(performanceData.javascript.memoryUsage.usedJSHeapSize) 
                        : 'N/A'}
                    </Typography>
                  </Box>
                </CardContent>
              </Card>
            </Grid>
          </Grid>
          
          {/* 标签页 */}
          <Paper sx={{ mb: 3 }}>
            <Tabs
              value={tabValue}
              onChange={handleTabChange}
              variant="scrollable"
              scrollButtons="auto"
              aria-label="performance dashboard tabs"
            >
              <Tab icon={<SpeedIcon />} label="Web Vitals" {...a11yProps(0)} />
              <Tab icon={<StorageIcon />} label="资源" {...a11yProps(1)} />
              <Tab icon={<CodeIcon />} label="JavaScript" {...a11yProps(2)} />
              <Tab icon={<ApiIcon />} label="API调用" {...a11yProps(3)} />
              <Tab icon={<TouchAppIcon />} label="用户交互" {...a11yProps(4)} />
              <Tab icon={<TimelineIcon />} label="组件性能" {...a11yProps(5)} />
            </Tabs>
            
            {/* Web Vitals标签页 */}
            <TabPanel value={tabValue} index={0}>
              <Typography variant="h6" gutterBottom>
                Web Vitals指标
              </Typography>
              <Typography variant="body2" paragraph>
                Web Vitals是Google提出的一组用于衡量网页用户体验的指标。
              </Typography>
              
              <Grid container spacing={3}>
                {/* LCP卡片 */}
                <Grid item xs={12} md={4}>
                  <Card>
                    <CardHeader
                      title="LCP (Largest Contentful Paint)"
                      subheader="最大内容绘制时间"
                      action={
                        <Tooltip title="最大内容绘制时间，衡量页面加载性能">
                          <InfoIcon />
                        </Tooltip>
                      }
                    />
                    <CardContent>
                      <Typography variant="h4" align="center" gutterBottom>
                        {performanceData.webVitals.LCP ? formatTime(performanceData.webVitals.LCP) : 'N/A'}
                      </Typography>
                      <Box display="flex" justifyContent="center">
                        <Chip
                          label={performanceData.webVitals.LCP && performanceData.webVitals.LCP <= 2500 ? '良好' : 
                                performanceData.webVitals.LCP && performanceData.webVitals.LCP <= 4000 ? '需要改进' : '较差'}
                          color={performanceData.webVitals.LCP && performanceData.webVitals.LCP <= 2500 ? 'success' : 
                                performanceData.webVitals.LCP && performanceData.webVitals.LCP <= 4000 ? 'warning' : 'error'}
                        />
                      </Box>
                    </CardContent>
                  </Card>
                </Grid>
                
                {/* FID卡片 */}
                <Grid item xs={12} md={4}>
                  <Card>
                    <CardHeader
                      title="FID (First Input Delay)"
                      subheader="首次输入延迟"
                      action={
                        <Tooltip title="首次输入延迟，衡量交互性">
                          <InfoIcon />
                        </Tooltip>
                      }
                    />
                    <CardContent>
                      <Typography variant="h4" align="center" gutterBottom>
                        {performanceData.webVitals.FID ? formatTime(performanceData.webVitals.FID) : 'N/A'}
                      </Typography>
                      <Box display="flex" justifyContent="center">
                        <Chip
                          label={performanceData.webVitals.FID && performanceData.webVitals.FID <= 100 ? '良好' : 
                                performanceData.webVitals.FID && performanceData.webVitals.FID <= 300 ? '需要改进' : '较差'}
                          color={performanceData.webVitals.FID && performanceData.webVitals.FID <= 100 ? 'success' : 
                                performanceData.webVitals.FID && performanceData.webVitals.FID <= 300 ? 'warning' : 'error'}
                        />
                      </Box>
                    </CardContent>
                  </Card>
                </Grid>
                
                {/* CLS卡片 */}
                <Grid item xs={12} md={4}>
                  <Card>
                    <CardHeader
                      title="CLS (Cumulative Layout Shift)"
                      subheader="累积布局偏移"
                      action={
                        <Tooltip title="累积布局偏移，衡量视觉稳定性">
                          <InfoIcon />
                        </Tooltip>
                      }
                    />
                    <CardContent>
                      <Typography variant="h4" align="center" gutterBottom>
                        {performanceData.webVitals.CLS?.toFixed(3) || 'N/A'}
                      </Typography>
                      <Box display="flex" justifyContent="center">
                        <Chip
                          label={performanceData.webVitals.CLS && performanceData.webVitals.CLS <= 0.1 ? '良好' : 
                                performanceData.webVitals.CLS && performanceData.webVitals.CLS <= 0.25 ? '需要改进' : '较差'}
                          color={performanceData.webVitals.CLS && performanceData.webVitals.CLS <= 0.1 ? 'success' : 
                                performanceData.webVitals.CLS && performanceData.webVitals.CLS <= 0.25 ? 'warning' : 'error'}
                        />
                      </Box>
                    </CardContent>
                  </Card>
                </Grid>
              </Grid>
            </TabPanel>
            
            {/* 其他标签页内容将在后续实现... */}
          </Paper>
        </>
      )}
    </Box>
  );
};

export default PerformanceDashboard;
