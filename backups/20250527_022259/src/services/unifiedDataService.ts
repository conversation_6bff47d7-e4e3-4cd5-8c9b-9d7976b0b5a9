/**
 * 统一数据服务
 *
 * 提供统一的数据获取接口，支持真实API调用
 */

import { apiService } from './apiService';

// 环境配置
const API_BASE_URL = import.meta.env.VITE_API_BASE_URL || '/api';

/**
 * 统一数据获取函数
 * @param endpoint API端点
 * @param mockKey 模拟数据键（已废弃，保持兼容性）
 * @param data 请求数据
 * @param options 请求选项
 * @returns Promise<any>
 */
export async function fetchData(
  endpoint: string,
  mockKey: string, // 保持兼容性，但不使用
  data: any = {},
  options: RequestInit = {}
): Promise<any> {
  try {
    console.log(`🌐 调用API: ${endpoint}`, data);

    // 构建完整URL
    const url = endpoint.startsWith('http') ? endpoint : `${API_BASE_URL}${endpoint}`;

    // 设置默认请求选项
    const requestOptions: RequestInit = {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
        ...options.headers,
      },
      ...options,
    };

    // 如果是POST/PUT/PATCH请求且有数据，添加到body
    if (['POST', 'PUT', 'PATCH'].includes(requestOptions.method?.toUpperCase() || '') && data) {
      requestOptions.body = JSON.stringify(data);
    }

    // 如果是GET请求且有参数，添加到URL
    if (requestOptions.method?.toUpperCase() === 'GET' && data && Object.keys(data).length > 0) {
      const params = new URLSearchParams();
      Object.entries(data).forEach(([key, value]) => {
        if (value !== undefined && value !== null) {
          params.append(key, String(value));
        }
      });
      const separator = url.includes('?') ? '&' : '?';
      const finalUrl = `${url}${separator}${params.toString()}`;

      const response = await fetch(finalUrl, requestOptions);
      return await handleResponse(response);
    }

    // 发送请求
    const response = await fetch(url, requestOptions);
    return await handleResponse(response);

  } catch (error) {
    // 只在非登录相关的错误时记录详细错误
    if (!endpoint.includes('/admin/login')) {
      console.error(`❌ API调用失败: ${endpoint}`, error);
    } else {
      console.log(`🔐 登录尝试: ${endpoint}`);
    }
    throw error;
  }
}

/**
 * 处理响应
 */
async function handleResponse(response: Response): Promise<any> {
  if (!response.ok) {
    const errorText = await response.text();
    console.error(`HTTP错误 ${response.status}:`, errorText);

    if (response.status === 401) {
      throw new Error('未授权访问，请重新登录');
    } else if (response.status === 403) {
      throw new Error('权限不足');
    } else if (response.status === 404) {
      throw new Error('请求的资源不存在');
    } else if (response.status >= 500) {
      throw new Error('服务器内部错误，请稍后重试');
    } else {
      throw new Error(`请求失败: ${response.status} ${response.statusText}`);
    }
  }

  const contentType = response.headers.get('content-type');
  if (contentType && contentType.includes('application/json')) {
    return await response.json();
  } else {
    return await response.text();
  }
}

/**
 * 管理员登录
 */
export async function adminLogin(username: string, password: string) {
  // 使用环境变量中的API基础URL
  const url = API_BASE_URL ? `${API_BASE_URL}/admin/login` : '/api/admin/login';

  console.log('管理员登录API URL (unified):', url);

  const response = await fetch(url, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    body: JSON.stringify({ username, password }),
  });

  if (!response.ok) {
    const errorText = await response.text();
    console.error(`HTTP错误 ${response.status}:`, errorText);
    throw new Error('登录失败');
  }

  return await response.json();
}

/**
 * 获取待审核内容
 */
export async function getPendingContent() {
  return fetchData('/api/reviewer/pending-content', 'pendingContentMock', {}, { method: 'GET' });
}

/**
 * 审核内容
 */
export async function reviewContent(contentId: string, action: 'approved' | 'rejected', reason?: string, contentType?: string) {
  return fetchData(
    `/api/reviewer/review/${contentId}`,
    'reviewContentMock',
    { action, reason, contentType },
    { method: 'POST' }
  );
}

/**
 * 获取用户列表
 */
export async function getUsers(params: any = {}) {
  return fetchData('/admin/users', 'getUsersMock', params, { method: 'GET' });
}

/**
 * 获取审核员列表
 */
export async function getReviewers(params: any = {}) {
  return fetchData('/admin/reviewers', 'getReviewersMock', params, { method: 'GET' });
}

/**
 * 获取管理员列表
 */
export async function getAdmins(params: any = {}) {
  return fetchData('/admin/admins', 'getAdminsMock', params, { method: 'GET' });
}

// 导出默认对象以保持兼容性
export default {
  fetchData,
  adminLogin,
  getPendingContent,
  reviewContent,
  getUsers,
  getReviewers,
  getAdmins
};
